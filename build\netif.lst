ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 1


   1              		.cpu cortex-m7
   2              		.arch armv7e-m
   3              		.fpu fpv5-d16
   4              		.eabi_attribute 28, 1
   5              		.eabi_attribute 20, 1
   6              		.eabi_attribute 21, 1
   7              		.eabi_attribute 23, 3
   8              		.eabi_attribute 24, 1
   9              		.eabi_attribute 25, 1
  10              		.eabi_attribute 26, 1
  11              		.eabi_attribute 30, 1
  12              		.eabi_attribute 34, 1
  13              		.eabi_attribute 18, 4
  14              		.file	"netif.c"
  15              		.text
  16              	.Ltext0:
  17              		.cfi_sections	.debug_frame
  18              		.file 1 "Middlewares/Third_Party/LwIP/src/core/netif.c"
  19              		.section	.text.netif_do_set_netmask,"ax",%progbits
  20              		.align	1
  21              		.syntax unified
  22              		.thumb
  23              		.thumb_func
  25              	netif_do_set_netmask:
  26              	.LVL0:
  27              	.LFB181:
   1:Middlewares/Third_Party/LwIP/src/core/netif.c **** /**
   2:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @file
   3:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * lwIP network interface abstraction
   4:Middlewares/Third_Party/LwIP/src/core/netif.c ****  *
   5:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @defgroup netif Network interface (NETIF)
   6:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @ingroup callbackstyle_api
   7:Middlewares/Third_Party/LwIP/src/core/netif.c ****  *
   8:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @defgroup netif_ip4 IPv4 address handling
   9:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @ingroup netif
  10:Middlewares/Third_Party/LwIP/src/core/netif.c ****  *
  11:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @defgroup netif_ip6 IPv6 address handling
  12:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @ingroup netif
  13:Middlewares/Third_Party/LwIP/src/core/netif.c ****  *
  14:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @defgroup netif_cd Client data handling
  15:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * Store data (void*) on a netif for application usage.
  16:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @see @ref LWIP_NUM_NETIF_CLIENT_DATA
  17:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @ingroup netif
  18:Middlewares/Third_Party/LwIP/src/core/netif.c ****  */
  19:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
  20:Middlewares/Third_Party/LwIP/src/core/netif.c **** /*
  21:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * Copyright (c) 2001-2004 Swedish Institute of Computer Science.
  22:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * All rights reserved.
  23:Middlewares/Third_Party/LwIP/src/core/netif.c ****  *
  24:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * Redistribution and use in source and binary forms, with or without modification,
  25:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * are permitted provided that the following conditions are met:
  26:Middlewares/Third_Party/LwIP/src/core/netif.c ****  *
  27:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * 1. Redistributions of source code must retain the above copyright notice,
  28:Middlewares/Third_Party/LwIP/src/core/netif.c ****  *    this list of conditions and the following disclaimer.
  29:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * 2. Redistributions in binary form must reproduce the above copyright notice,
  30:Middlewares/Third_Party/LwIP/src/core/netif.c ****  *    this list of conditions and the following disclaimer in the documentation
  31:Middlewares/Third_Party/LwIP/src/core/netif.c ****  *    and/or other materials provided with the distribution.
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 2


  32:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * 3. The name of the author may not be used to endorse or promote products
  33:Middlewares/Third_Party/LwIP/src/core/netif.c ****  *    derived from this software without specific prior written permission.
  34:Middlewares/Third_Party/LwIP/src/core/netif.c ****  *
  35:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR IMPLIED
  36:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
  37:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT
  38:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * SHALL THE AUTHOR BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
  39:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT
  40:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
  41:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
  42:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
  43:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY
  44:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * OF SUCH DAMAGE.
  45:Middlewares/Third_Party/LwIP/src/core/netif.c ****  *
  46:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * This file is part of the lwIP TCP/IP stack.
  47:Middlewares/Third_Party/LwIP/src/core/netif.c ****  *
  48:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * Author: Adam Dunkels <<EMAIL>>
  49:Middlewares/Third_Party/LwIP/src/core/netif.c ****  */
  50:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
  51:Middlewares/Third_Party/LwIP/src/core/netif.c **** #include "lwip/opt.h"
  52:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
  53:Middlewares/Third_Party/LwIP/src/core/netif.c **** #include <string.h> /* memset */
  54:Middlewares/Third_Party/LwIP/src/core/netif.c **** #include <stdlib.h> /* atoi */
  55:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
  56:Middlewares/Third_Party/LwIP/src/core/netif.c **** #include "lwip/def.h"
  57:Middlewares/Third_Party/LwIP/src/core/netif.c **** #include "lwip/ip_addr.h"
  58:Middlewares/Third_Party/LwIP/src/core/netif.c **** #include "lwip/ip6_addr.h"
  59:Middlewares/Third_Party/LwIP/src/core/netif.c **** #include "lwip/netif.h"
  60:Middlewares/Third_Party/LwIP/src/core/netif.c **** #include "lwip/priv/tcp_priv.h"
  61:Middlewares/Third_Party/LwIP/src/core/netif.c **** #include "lwip/udp.h"
  62:Middlewares/Third_Party/LwIP/src/core/netif.c **** #include "lwip/priv/raw_priv.h"
  63:Middlewares/Third_Party/LwIP/src/core/netif.c **** #include "lwip/snmp.h"
  64:Middlewares/Third_Party/LwIP/src/core/netif.c **** #include "lwip/igmp.h"
  65:Middlewares/Third_Party/LwIP/src/core/netif.c **** #include "lwip/etharp.h"
  66:Middlewares/Third_Party/LwIP/src/core/netif.c **** #include "lwip/stats.h"
  67:Middlewares/Third_Party/LwIP/src/core/netif.c **** #include "lwip/sys.h"
  68:Middlewares/Third_Party/LwIP/src/core/netif.c **** #include "lwip/ip.h"
  69:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if ENABLE_LOOPBACK
  70:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_NETIF_LOOPBACK_MULTITHREADING
  71:Middlewares/Third_Party/LwIP/src/core/netif.c **** #include "lwip/tcpip.h"
  72:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_NETIF_LOOPBACK_MULTITHREADING */
  73:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* ENABLE_LOOPBACK */
  74:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
  75:Middlewares/Third_Party/LwIP/src/core/netif.c **** #include "netif/ethernet.h"
  76:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
  77:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_AUTOIP
  78:Middlewares/Third_Party/LwIP/src/core/netif.c **** #include "lwip/autoip.h"
  79:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_AUTOIP */
  80:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_DHCP
  81:Middlewares/Third_Party/LwIP/src/core/netif.c **** #include "lwip/dhcp.h"
  82:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_DHCP */
  83:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_IPV6_DHCP6
  84:Middlewares/Third_Party/LwIP/src/core/netif.c **** #include "lwip/dhcp6.h"
  85:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_IPV6_DHCP6 */
  86:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_IPV6_MLD
  87:Middlewares/Third_Party/LwIP/src/core/netif.c **** #include "lwip/mld6.h"
  88:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_IPV6_MLD */
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 3


  89:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_IPV6
  90:Middlewares/Third_Party/LwIP/src/core/netif.c **** #include "lwip/nd6.h"
  91:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif
  92:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
  93:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_NETIF_STATUS_CALLBACK
  94:Middlewares/Third_Party/LwIP/src/core/netif.c **** #define NETIF_STATUS_CALLBACK(n) do{ if (n->status_callback) { (n->status_callback)(n); }}while(0)
  95:Middlewares/Third_Party/LwIP/src/core/netif.c **** #else
  96:Middlewares/Third_Party/LwIP/src/core/netif.c **** #define NETIF_STATUS_CALLBACK(n)
  97:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_NETIF_STATUS_CALLBACK */
  98:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
  99:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_NETIF_LINK_CALLBACK
 100:Middlewares/Third_Party/LwIP/src/core/netif.c **** #define NETIF_LINK_CALLBACK(n) do{ if (n->link_callback) { (n->link_callback)(n); }}while(0)
 101:Middlewares/Third_Party/LwIP/src/core/netif.c **** #else
 102:Middlewares/Third_Party/LwIP/src/core/netif.c **** #define NETIF_LINK_CALLBACK(n)
 103:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_NETIF_LINK_CALLBACK */
 104:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 105:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_NETIF_EXT_STATUS_CALLBACK
 106:Middlewares/Third_Party/LwIP/src/core/netif.c **** static netif_ext_callback_t *ext_callback;
 107:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif
 108:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 109:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if !LWIP_SINGLE_NETIF
 110:Middlewares/Third_Party/LwIP/src/core/netif.c **** struct netif *netif_list;
 111:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* !LWIP_SINGLE_NETIF */
 112:Middlewares/Third_Party/LwIP/src/core/netif.c **** struct netif *netif_default;
 113:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 114:Middlewares/Third_Party/LwIP/src/core/netif.c **** #define netif_index_to_num(index)   ((index) - 1)
 115:Middlewares/Third_Party/LwIP/src/core/netif.c **** static u8_t netif_num;
 116:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 117:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_NUM_NETIF_CLIENT_DATA > 0
 118:Middlewares/Third_Party/LwIP/src/core/netif.c **** static u8_t netif_client_id;
 119:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif
 120:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 121:Middlewares/Third_Party/LwIP/src/core/netif.c **** #define NETIF_REPORT_TYPE_IPV4  0x01
 122:Middlewares/Third_Party/LwIP/src/core/netif.c **** #define NETIF_REPORT_TYPE_IPV6  0x02
 123:Middlewares/Third_Party/LwIP/src/core/netif.c **** static void netif_issue_reports(struct netif *netif, u8_t report_type);
 124:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 125:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_IPV6
 126:Middlewares/Third_Party/LwIP/src/core/netif.c **** static err_t netif_null_output_ip6(struct netif *netif, struct pbuf *p, const ip6_addr_t *ipaddr);
 127:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_IPV6 */
 128:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_IPV4
 129:Middlewares/Third_Party/LwIP/src/core/netif.c **** static err_t netif_null_output_ip4(struct netif *netif, struct pbuf *p, const ip4_addr_t *ipaddr);
 130:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_IPV4 */
 131:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 132:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_HAVE_LOOPIF
 133:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_IPV4
 134:Middlewares/Third_Party/LwIP/src/core/netif.c **** static err_t netif_loop_output_ipv4(struct netif *netif, struct pbuf *p, const ip4_addr_t *addr);
 135:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif
 136:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_IPV6
 137:Middlewares/Third_Party/LwIP/src/core/netif.c **** static err_t netif_loop_output_ipv6(struct netif *netif, struct pbuf *p, const ip6_addr_t *addr);
 138:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif
 139:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 140:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 141:Middlewares/Third_Party/LwIP/src/core/netif.c **** static struct netif loop_netif;
 142:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 143:Middlewares/Third_Party/LwIP/src/core/netif.c **** /**
 144:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * Initialize a lwip network interface structure for a loopback interface
 145:Middlewares/Third_Party/LwIP/src/core/netif.c ****  *
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 4


 146:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @param netif the lwip network interface structure for this loopif
 147:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @return ERR_OK if the loopif is initialized
 148:Middlewares/Third_Party/LwIP/src/core/netif.c ****  *         ERR_MEM if private data couldn't be allocated
 149:Middlewares/Third_Party/LwIP/src/core/netif.c ****  */
 150:Middlewares/Third_Party/LwIP/src/core/netif.c **** static err_t
 151:Middlewares/Third_Party/LwIP/src/core/netif.c **** netif_loopif_init(struct netif *netif)
 152:Middlewares/Third_Party/LwIP/src/core/netif.c **** {
 153:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT("netif_loopif_init: invalid netif", netif != NULL);
 154:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 155:Middlewares/Third_Party/LwIP/src/core/netif.c ****   /* initialize the snmp variables and counters inside the struct netif
 156:Middlewares/Third_Party/LwIP/src/core/netif.c ****    * ifSpeed: no assumption can be made!
 157:Middlewares/Third_Party/LwIP/src/core/netif.c ****    */
 158:Middlewares/Third_Party/LwIP/src/core/netif.c ****   MIB2_INIT_NETIF(netif, snmp_ifType_softwareLoopback, 0);
 159:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 160:Middlewares/Third_Party/LwIP/src/core/netif.c ****   netif->name[0] = 'l';
 161:Middlewares/Third_Party/LwIP/src/core/netif.c ****   netif->name[1] = 'o';
 162:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_IPV4
 163:Middlewares/Third_Party/LwIP/src/core/netif.c ****   netif->output = netif_loop_output_ipv4;
 164:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif
 165:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_IPV6
 166:Middlewares/Third_Party/LwIP/src/core/netif.c ****   netif->output_ip6 = netif_loop_output_ipv6;
 167:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif
 168:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_LOOPIF_MULTICAST
 169:Middlewares/Third_Party/LwIP/src/core/netif.c ****   netif_set_flags(netif, NETIF_FLAG_IGMP);
 170:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif
 171:Middlewares/Third_Party/LwIP/src/core/netif.c ****   NETIF_SET_CHECKSUM_CTRL(netif, NETIF_CHECKSUM_DISABLE_ALL);
 172:Middlewares/Third_Party/LwIP/src/core/netif.c ****   return ERR_OK;
 173:Middlewares/Third_Party/LwIP/src/core/netif.c **** }
 174:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_HAVE_LOOPIF */
 175:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 176:Middlewares/Third_Party/LwIP/src/core/netif.c **** void
 177:Middlewares/Third_Party/LwIP/src/core/netif.c **** netif_init(void)
 178:Middlewares/Third_Party/LwIP/src/core/netif.c **** {
 179:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_HAVE_LOOPIF
 180:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_IPV4
 181:Middlewares/Third_Party/LwIP/src/core/netif.c **** #define LOOPIF_ADDRINIT &loop_ipaddr, &loop_netmask, &loop_gw,
 182:Middlewares/Third_Party/LwIP/src/core/netif.c ****   ip4_addr_t loop_ipaddr, loop_netmask, loop_gw;
 183:Middlewares/Third_Party/LwIP/src/core/netif.c ****   IP4_ADDR(&loop_gw, 127, 0, 0, 1);
 184:Middlewares/Third_Party/LwIP/src/core/netif.c ****   IP4_ADDR(&loop_ipaddr, 127, 0, 0, 1);
 185:Middlewares/Third_Party/LwIP/src/core/netif.c ****   IP4_ADDR(&loop_netmask, 255, 0, 0, 0);
 186:Middlewares/Third_Party/LwIP/src/core/netif.c **** #else /* LWIP_IPV4 */
 187:Middlewares/Third_Party/LwIP/src/core/netif.c **** #define LOOPIF_ADDRINIT
 188:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_IPV4 */
 189:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 190:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if NO_SYS
 191:Middlewares/Third_Party/LwIP/src/core/netif.c ****   netif_add(&loop_netif, LOOPIF_ADDRINIT NULL, netif_loopif_init, ip_input);
 192:Middlewares/Third_Party/LwIP/src/core/netif.c **** #else  /* NO_SYS */
 193:Middlewares/Third_Party/LwIP/src/core/netif.c ****   netif_add(&loop_netif, LOOPIF_ADDRINIT NULL, netif_loopif_init, tcpip_input);
 194:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* NO_SYS */
 195:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 196:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_IPV6
 197:Middlewares/Third_Party/LwIP/src/core/netif.c ****   IP_ADDR6_HOST(loop_netif.ip6_addr, 0, 0, 0, 0x00000001UL);
 198:Middlewares/Third_Party/LwIP/src/core/netif.c ****   loop_netif.ip6_addr_state[0] = IP6_ADDR_VALID;
 199:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_IPV6 */
 200:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 201:Middlewares/Third_Party/LwIP/src/core/netif.c ****   netif_set_link_up(&loop_netif);
 202:Middlewares/Third_Party/LwIP/src/core/netif.c ****   netif_set_up(&loop_netif);
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 5


 203:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 204:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_HAVE_LOOPIF */
 205:Middlewares/Third_Party/LwIP/src/core/netif.c **** }
 206:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 207:Middlewares/Third_Party/LwIP/src/core/netif.c **** /**
 208:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @ingroup lwip_nosys
 209:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * Forwards a received packet for input processing with
 210:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * ethernet_input() or ip_input() depending on netif flags.
 211:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * Don't call directly, pass to netif_add() and call
 212:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * netif->input().
 213:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * Only works if the netif driver correctly sets
 214:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * NETIF_FLAG_ETHARP and/or NETIF_FLAG_ETHERNET flag!
 215:Middlewares/Third_Party/LwIP/src/core/netif.c ****  */
 216:Middlewares/Third_Party/LwIP/src/core/netif.c **** err_t
 217:Middlewares/Third_Party/LwIP/src/core/netif.c **** netif_input(struct pbuf *p, struct netif *inp)
 218:Middlewares/Third_Party/LwIP/src/core/netif.c **** {
 219:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT_CORE_LOCKED();
 220:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 221:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT("netif_input: invalid pbuf", p != NULL);
 222:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT("netif_input: invalid netif", inp != NULL);
 223:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 224:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_ETHERNET
 225:Middlewares/Third_Party/LwIP/src/core/netif.c ****   if (inp->flags & (NETIF_FLAG_ETHARP | NETIF_FLAG_ETHERNET)) {
 226:Middlewares/Third_Party/LwIP/src/core/netif.c ****     return ethernet_input(p, inp);
 227:Middlewares/Third_Party/LwIP/src/core/netif.c ****   } else
 228:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_ETHERNET */
 229:Middlewares/Third_Party/LwIP/src/core/netif.c ****     return ip_input(p, inp);
 230:Middlewares/Third_Party/LwIP/src/core/netif.c **** }
 231:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 232:Middlewares/Third_Party/LwIP/src/core/netif.c **** /**
 233:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @ingroup netif
 234:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * Add a network interface to the list of lwIP netifs.
 235:Middlewares/Third_Party/LwIP/src/core/netif.c ****  *
 236:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * Same as @ref netif_add but without IPv4 addresses
 237:Middlewares/Third_Party/LwIP/src/core/netif.c ****  */
 238:Middlewares/Third_Party/LwIP/src/core/netif.c **** struct netif *
 239:Middlewares/Third_Party/LwIP/src/core/netif.c **** netif_add_noaddr(struct netif *netif, void *state, netif_init_fn init, netif_input_fn input)
 240:Middlewares/Third_Party/LwIP/src/core/netif.c **** {
 241:Middlewares/Third_Party/LwIP/src/core/netif.c ****   return netif_add(netif,
 242:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_IPV4
 243:Middlewares/Third_Party/LwIP/src/core/netif.c ****                    NULL, NULL, NULL,
 244:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_IPV4*/
 245:Middlewares/Third_Party/LwIP/src/core/netif.c ****                    state, init, input);
 246:Middlewares/Third_Party/LwIP/src/core/netif.c **** }
 247:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 248:Middlewares/Third_Party/LwIP/src/core/netif.c **** /**
 249:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @ingroup netif
 250:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * Add a network interface to the list of lwIP netifs.
 251:Middlewares/Third_Party/LwIP/src/core/netif.c ****  *
 252:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @param netif a pre-allocated netif structure
 253:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @param ipaddr IP address for the new netif
 254:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @param netmask network mask for the new netif
 255:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @param gw default gateway IP address for the new netif
 256:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @param state opaque data passed to the new netif
 257:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @param init callback function that initializes the interface
 258:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @param input callback function that is called to pass
 259:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * ingress packets up in the protocol layer stack.\n
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 6


 260:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * It is recommended to use a function that passes the input directly
 261:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * to the stack (netif_input(), NO_SYS=1 mode) or via sending a
 262:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * message to TCPIP thread (tcpip_input(), NO_SYS=0 mode).\n
 263:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * These functions use netif flags NETIF_FLAG_ETHARP and NETIF_FLAG_ETHERNET
 264:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * to decide whether to forward to ethernet_input() or ip_input().
 265:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * In other words, the functions only work when the netif
 266:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * driver is implemented correctly!\n
 267:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * Most members of struct netif should be be initialized by the
 268:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * netif init function = netif driver (init parameter of this function).\n
 269:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * IPv6: Don't forget to call netif_create_ip6_linklocal_address() after
 270:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * setting the MAC address in struct netif.hwaddr
 271:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * (IPv6 requires a link-local address).
 272:Middlewares/Third_Party/LwIP/src/core/netif.c ****  *
 273:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @return netif, or NULL if failed.
 274:Middlewares/Third_Party/LwIP/src/core/netif.c ****  */
 275:Middlewares/Third_Party/LwIP/src/core/netif.c **** struct netif *
 276:Middlewares/Third_Party/LwIP/src/core/netif.c **** netif_add(struct netif *netif,
 277:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_IPV4
 278:Middlewares/Third_Party/LwIP/src/core/netif.c ****           const ip4_addr_t *ipaddr, const ip4_addr_t *netmask, const ip4_addr_t *gw,
 279:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_IPV4 */
 280:Middlewares/Third_Party/LwIP/src/core/netif.c ****           void *state, netif_init_fn init, netif_input_fn input)
 281:Middlewares/Third_Party/LwIP/src/core/netif.c **** {
 282:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_IPV6
 283:Middlewares/Third_Party/LwIP/src/core/netif.c ****   s8_t i;
 284:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif
 285:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 286:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT_CORE_LOCKED();
 287:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 288:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_SINGLE_NETIF
 289:Middlewares/Third_Party/LwIP/src/core/netif.c ****   if (netif_default != NULL) {
 290:Middlewares/Third_Party/LwIP/src/core/netif.c ****     LWIP_ASSERT("single netif already set", 0);
 291:Middlewares/Third_Party/LwIP/src/core/netif.c ****     return NULL;
 292:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
 293:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif
 294:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 295:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ERROR("netif_add: invalid netif", netif != NULL, return NULL);
 296:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ERROR("netif_add: No init function given", init != NULL, return NULL);
 297:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 298:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_IPV4
 299:Middlewares/Third_Party/LwIP/src/core/netif.c ****   if (ipaddr == NULL) {
 300:Middlewares/Third_Party/LwIP/src/core/netif.c ****     ipaddr = ip_2_ip4(IP4_ADDR_ANY);
 301:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
 302:Middlewares/Third_Party/LwIP/src/core/netif.c ****   if (netmask == NULL) {
 303:Middlewares/Third_Party/LwIP/src/core/netif.c ****     netmask = ip_2_ip4(IP4_ADDR_ANY);
 304:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
 305:Middlewares/Third_Party/LwIP/src/core/netif.c ****   if (gw == NULL) {
 306:Middlewares/Third_Party/LwIP/src/core/netif.c ****     gw = ip_2_ip4(IP4_ADDR_ANY);
 307:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
 308:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 309:Middlewares/Third_Party/LwIP/src/core/netif.c ****   /* reset new interface configuration state */
 310:Middlewares/Third_Party/LwIP/src/core/netif.c ****   ip_addr_set_zero_ip4(&netif->ip_addr);
 311:Middlewares/Third_Party/LwIP/src/core/netif.c ****   ip_addr_set_zero_ip4(&netif->netmask);
 312:Middlewares/Third_Party/LwIP/src/core/netif.c ****   ip_addr_set_zero_ip4(&netif->gw);
 313:Middlewares/Third_Party/LwIP/src/core/netif.c ****   netif->output = netif_null_output_ip4;
 314:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_IPV4 */
 315:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_IPV6
 316:Middlewares/Third_Party/LwIP/src/core/netif.c ****   for (i = 0; i < LWIP_IPV6_NUM_ADDRESSES; i++) {
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 7


 317:Middlewares/Third_Party/LwIP/src/core/netif.c ****     ip_addr_set_zero_ip6(&netif->ip6_addr[i]);
 318:Middlewares/Third_Party/LwIP/src/core/netif.c ****     netif->ip6_addr_state[i] = IP6_ADDR_INVALID;
 319:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_IPV6_ADDRESS_LIFETIMES
 320:Middlewares/Third_Party/LwIP/src/core/netif.c ****     netif->ip6_addr_valid_life[i] = IP6_ADDR_LIFE_STATIC;
 321:Middlewares/Third_Party/LwIP/src/core/netif.c ****     netif->ip6_addr_pref_life[i] = IP6_ADDR_LIFE_STATIC;
 322:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_IPV6_ADDRESS_LIFETIMES */
 323:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
 324:Middlewares/Third_Party/LwIP/src/core/netif.c ****   netif->output_ip6 = netif_null_output_ip6;
 325:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_IPV6 */
 326:Middlewares/Third_Party/LwIP/src/core/netif.c ****   NETIF_SET_CHECKSUM_CTRL(netif, NETIF_CHECKSUM_ENABLE_ALL);
 327:Middlewares/Third_Party/LwIP/src/core/netif.c ****   netif->mtu = 0;
 328:Middlewares/Third_Party/LwIP/src/core/netif.c ****   netif->flags = 0;
 329:Middlewares/Third_Party/LwIP/src/core/netif.c **** #ifdef netif_get_client_data
 330:Middlewares/Third_Party/LwIP/src/core/netif.c ****   memset(netif->client_data, 0, sizeof(netif->client_data));
 331:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_NUM_NETIF_CLIENT_DATA */
 332:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_IPV6
 333:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_IPV6_AUTOCONFIG
 334:Middlewares/Third_Party/LwIP/src/core/netif.c ****   /* IPv6 address autoconfiguration not enabled by default */
 335:Middlewares/Third_Party/LwIP/src/core/netif.c ****   netif->ip6_autoconfig_enabled = 0;
 336:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_IPV6_AUTOCONFIG */
 337:Middlewares/Third_Party/LwIP/src/core/netif.c ****   nd6_restart_netif(netif);
 338:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_IPV6 */
 339:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_NETIF_STATUS_CALLBACK
 340:Middlewares/Third_Party/LwIP/src/core/netif.c ****   netif->status_callback = NULL;
 341:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_NETIF_STATUS_CALLBACK */
 342:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_NETIF_LINK_CALLBACK
 343:Middlewares/Third_Party/LwIP/src/core/netif.c ****   netif->link_callback = NULL;
 344:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_NETIF_LINK_CALLBACK */
 345:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_IGMP
 346:Middlewares/Third_Party/LwIP/src/core/netif.c ****   netif->igmp_mac_filter = NULL;
 347:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_IGMP */
 348:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_IPV6 && LWIP_IPV6_MLD
 349:Middlewares/Third_Party/LwIP/src/core/netif.c ****   netif->mld_mac_filter = NULL;
 350:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_IPV6 && LWIP_IPV6_MLD */
 351:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if ENABLE_LOOPBACK
 352:Middlewares/Third_Party/LwIP/src/core/netif.c ****   netif->loop_first = NULL;
 353:Middlewares/Third_Party/LwIP/src/core/netif.c ****   netif->loop_last = NULL;
 354:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* ENABLE_LOOPBACK */
 355:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 356:Middlewares/Third_Party/LwIP/src/core/netif.c ****   /* remember netif specific state information data */
 357:Middlewares/Third_Party/LwIP/src/core/netif.c ****   netif->state = state;
 358:Middlewares/Third_Party/LwIP/src/core/netif.c ****   netif->num = netif_num;
 359:Middlewares/Third_Party/LwIP/src/core/netif.c ****   netif->input = input;
 360:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 361:Middlewares/Third_Party/LwIP/src/core/netif.c ****   NETIF_RESET_HINTS(netif);
 362:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if ENABLE_LOOPBACK && LWIP_LOOPBACK_MAX_PBUFS
 363:Middlewares/Third_Party/LwIP/src/core/netif.c ****   netif->loop_cnt_current = 0;
 364:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* ENABLE_LOOPBACK && LWIP_LOOPBACK_MAX_PBUFS */
 365:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 366:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_IPV4
 367:Middlewares/Third_Party/LwIP/src/core/netif.c ****   netif_set_addr(netif, ipaddr, netmask, gw);
 368:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_IPV4 */
 369:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 370:Middlewares/Third_Party/LwIP/src/core/netif.c ****   /* call user specified initialization function for netif */
 371:Middlewares/Third_Party/LwIP/src/core/netif.c ****   if (init(netif) != ERR_OK) {
 372:Middlewares/Third_Party/LwIP/src/core/netif.c ****     return NULL;
 373:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 8


 374:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_IPV6 && LWIP_ND6_ALLOW_RA_UPDATES
 375:Middlewares/Third_Party/LwIP/src/core/netif.c ****   /* Initialize the MTU for IPv6 to the one set by the netif driver.
 376:Middlewares/Third_Party/LwIP/src/core/netif.c ****      This can be updated later by RA. */
 377:Middlewares/Third_Party/LwIP/src/core/netif.c ****   netif->mtu6 = netif->mtu;
 378:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_IPV6 && LWIP_ND6_ALLOW_RA_UPDATES */
 379:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 380:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if !LWIP_SINGLE_NETIF
 381:Middlewares/Third_Party/LwIP/src/core/netif.c ****   /* Assign a unique netif number in the range [0..254], so that (num+1) can
 382:Middlewares/Third_Party/LwIP/src/core/netif.c ****      serve as an interface index that fits in a u8_t.
 383:Middlewares/Third_Party/LwIP/src/core/netif.c ****      We assume that the new netif has not yet been added to the list here.
 384:Middlewares/Third_Party/LwIP/src/core/netif.c ****      This algorithm is O(n^2), but that should be OK for lwIP.
 385:Middlewares/Third_Party/LwIP/src/core/netif.c ****      */
 386:Middlewares/Third_Party/LwIP/src/core/netif.c ****   {
 387:Middlewares/Third_Party/LwIP/src/core/netif.c ****     struct netif *netif2;
 388:Middlewares/Third_Party/LwIP/src/core/netif.c ****     int num_netifs;
 389:Middlewares/Third_Party/LwIP/src/core/netif.c ****     do {
 390:Middlewares/Third_Party/LwIP/src/core/netif.c ****       if (netif->num == 255) {
 391:Middlewares/Third_Party/LwIP/src/core/netif.c ****         netif->num = 0;
 392:Middlewares/Third_Party/LwIP/src/core/netif.c ****       }
 393:Middlewares/Third_Party/LwIP/src/core/netif.c ****       num_netifs = 0;
 394:Middlewares/Third_Party/LwIP/src/core/netif.c ****       for (netif2 = netif_list; netif2 != NULL; netif2 = netif2->next) {
 395:Middlewares/Third_Party/LwIP/src/core/netif.c ****         LWIP_ASSERT("netif already added", netif2 != netif);
 396:Middlewares/Third_Party/LwIP/src/core/netif.c ****         num_netifs++;
 397:Middlewares/Third_Party/LwIP/src/core/netif.c ****         LWIP_ASSERT("too many netifs, max. supported number is 255", num_netifs <= 255);
 398:Middlewares/Third_Party/LwIP/src/core/netif.c ****         if (netif2->num == netif->num) {
 399:Middlewares/Third_Party/LwIP/src/core/netif.c ****           netif->num++;
 400:Middlewares/Third_Party/LwIP/src/core/netif.c ****           break;
 401:Middlewares/Third_Party/LwIP/src/core/netif.c ****         }
 402:Middlewares/Third_Party/LwIP/src/core/netif.c ****       }
 403:Middlewares/Third_Party/LwIP/src/core/netif.c ****     } while (netif2 != NULL);
 404:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
 405:Middlewares/Third_Party/LwIP/src/core/netif.c ****   if (netif->num == 254) {
 406:Middlewares/Third_Party/LwIP/src/core/netif.c ****     netif_num = 0;
 407:Middlewares/Third_Party/LwIP/src/core/netif.c ****   } else {
 408:Middlewares/Third_Party/LwIP/src/core/netif.c ****     netif_num = (u8_t)(netif->num + 1);
 409:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
 410:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 411:Middlewares/Third_Party/LwIP/src/core/netif.c ****   /* add this netif to the list */
 412:Middlewares/Third_Party/LwIP/src/core/netif.c ****   netif->next = netif_list;
 413:Middlewares/Third_Party/LwIP/src/core/netif.c ****   netif_list = netif;
 414:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* "LWIP_SINGLE_NETIF */
 415:Middlewares/Third_Party/LwIP/src/core/netif.c ****   mib2_netif_added(netif);
 416:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 417:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_IGMP
 418:Middlewares/Third_Party/LwIP/src/core/netif.c ****   /* start IGMP processing */
 419:Middlewares/Third_Party/LwIP/src/core/netif.c ****   if (netif->flags & NETIF_FLAG_IGMP) {
 420:Middlewares/Third_Party/LwIP/src/core/netif.c ****     igmp_start(netif);
 421:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
 422:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_IGMP */
 423:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 424:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_DEBUGF(NETIF_DEBUG, ("netif: added interface %c%c IP",
 425:Middlewares/Third_Party/LwIP/src/core/netif.c ****                             netif->name[0], netif->name[1]));
 426:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_IPV4
 427:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_DEBUGF(NETIF_DEBUG, (" addr "));
 428:Middlewares/Third_Party/LwIP/src/core/netif.c ****   ip4_addr_debug_print(NETIF_DEBUG, ipaddr);
 429:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_DEBUGF(NETIF_DEBUG, (" netmask "));
 430:Middlewares/Third_Party/LwIP/src/core/netif.c ****   ip4_addr_debug_print(NETIF_DEBUG, netmask);
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 9


 431:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_DEBUGF(NETIF_DEBUG, (" gw "));
 432:Middlewares/Third_Party/LwIP/src/core/netif.c ****   ip4_addr_debug_print(NETIF_DEBUG, gw);
 433:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_IPV4 */
 434:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_DEBUGF(NETIF_DEBUG, ("\n"));
 435:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 436:Middlewares/Third_Party/LwIP/src/core/netif.c ****   netif_invoke_ext_callback(netif, LWIP_NSC_NETIF_ADDED, NULL);
 437:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 438:Middlewares/Third_Party/LwIP/src/core/netif.c ****   return netif;
 439:Middlewares/Third_Party/LwIP/src/core/netif.c **** }
 440:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 441:Middlewares/Third_Party/LwIP/src/core/netif.c **** static void
 442:Middlewares/Third_Party/LwIP/src/core/netif.c **** netif_do_ip_addr_changed(const ip_addr_t *old_addr, const ip_addr_t *new_addr)
 443:Middlewares/Third_Party/LwIP/src/core/netif.c **** {
 444:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_TCP
 445:Middlewares/Third_Party/LwIP/src/core/netif.c ****   tcp_netif_ip_addr_changed(old_addr, new_addr);
 446:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_TCP */
 447:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_UDP
 448:Middlewares/Third_Party/LwIP/src/core/netif.c ****   udp_netif_ip_addr_changed(old_addr, new_addr);
 449:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_UDP */
 450:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_RAW
 451:Middlewares/Third_Party/LwIP/src/core/netif.c ****   raw_netif_ip_addr_changed(old_addr, new_addr);
 452:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_RAW */
 453:Middlewares/Third_Party/LwIP/src/core/netif.c **** }
 454:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 455:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_IPV4
 456:Middlewares/Third_Party/LwIP/src/core/netif.c **** static int
 457:Middlewares/Third_Party/LwIP/src/core/netif.c **** netif_do_set_ipaddr(struct netif *netif, const ip4_addr_t *ipaddr, ip_addr_t *old_addr)
 458:Middlewares/Third_Party/LwIP/src/core/netif.c **** {
 459:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT("invalid pointer", ipaddr != NULL);
 460:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT("invalid pointer", old_addr != NULL);
 461:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 462:Middlewares/Third_Party/LwIP/src/core/netif.c ****   /* address is actually being changed? */
 463:Middlewares/Third_Party/LwIP/src/core/netif.c ****   if (ip4_addr_cmp(ipaddr, netif_ip4_addr(netif)) == 0) {
 464:Middlewares/Third_Party/LwIP/src/core/netif.c ****     ip_addr_t new_addr;
 465:Middlewares/Third_Party/LwIP/src/core/netif.c ****     *ip_2_ip4(&new_addr) = *ipaddr;
 466:Middlewares/Third_Party/LwIP/src/core/netif.c ****     IP_SET_TYPE_VAL(new_addr, IPADDR_TYPE_V4);
 467:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 468:Middlewares/Third_Party/LwIP/src/core/netif.c ****     ip_addr_copy(*old_addr, *netif_ip_addr4(netif));
 469:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 470:Middlewares/Third_Party/LwIP/src/core/netif.c ****     LWIP_DEBUGF(NETIF_DEBUG | LWIP_DBG_STATE, ("netif_set_ipaddr: netif address being changed\n"));
 471:Middlewares/Third_Party/LwIP/src/core/netif.c ****     netif_do_ip_addr_changed(old_addr, &new_addr);
 472:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 473:Middlewares/Third_Party/LwIP/src/core/netif.c ****     mib2_remove_ip4(netif);
 474:Middlewares/Third_Party/LwIP/src/core/netif.c ****     mib2_remove_route_ip4(0, netif);
 475:Middlewares/Third_Party/LwIP/src/core/netif.c ****     /* set new IP address to netif */
 476:Middlewares/Third_Party/LwIP/src/core/netif.c ****     ip4_addr_set(ip_2_ip4(&netif->ip_addr), ipaddr);
 477:Middlewares/Third_Party/LwIP/src/core/netif.c ****     IP_SET_TYPE_VAL(netif->ip_addr, IPADDR_TYPE_V4);
 478:Middlewares/Third_Party/LwIP/src/core/netif.c ****     mib2_add_ip4(netif);
 479:Middlewares/Third_Party/LwIP/src/core/netif.c ****     mib2_add_route_ip4(0, netif);
 480:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 481:Middlewares/Third_Party/LwIP/src/core/netif.c ****     netif_issue_reports(netif, NETIF_REPORT_TYPE_IPV4);
 482:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 483:Middlewares/Third_Party/LwIP/src/core/netif.c ****     NETIF_STATUS_CALLBACK(netif);
 484:Middlewares/Third_Party/LwIP/src/core/netif.c ****     return 1; /* address changed */
 485:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
 486:Middlewares/Third_Party/LwIP/src/core/netif.c ****   return 0; /* address unchanged */
 487:Middlewares/Third_Party/LwIP/src/core/netif.c **** }
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 10


 488:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 489:Middlewares/Third_Party/LwIP/src/core/netif.c **** /**
 490:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @ingroup netif_ip4
 491:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * Change the IP address of a network interface
 492:Middlewares/Third_Party/LwIP/src/core/netif.c ****  *
 493:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @param netif the network interface to change
 494:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @param ipaddr the new IP address
 495:Middlewares/Third_Party/LwIP/src/core/netif.c ****  *
 496:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @note call netif_set_addr() if you also want to change netmask and
 497:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * default gateway
 498:Middlewares/Third_Party/LwIP/src/core/netif.c ****  */
 499:Middlewares/Third_Party/LwIP/src/core/netif.c **** void
 500:Middlewares/Third_Party/LwIP/src/core/netif.c **** netif_set_ipaddr(struct netif *netif, const ip4_addr_t *ipaddr)
 501:Middlewares/Third_Party/LwIP/src/core/netif.c **** {
 502:Middlewares/Third_Party/LwIP/src/core/netif.c ****   ip_addr_t old_addr;
 503:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 504:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ERROR("netif_set_ipaddr: invalid netif", netif != NULL, return);
 505:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 506:Middlewares/Third_Party/LwIP/src/core/netif.c ****   /* Don't propagate NULL pointer (IPv4 ANY) to subsequent functions */
 507:Middlewares/Third_Party/LwIP/src/core/netif.c ****   if (ipaddr == NULL) {
 508:Middlewares/Third_Party/LwIP/src/core/netif.c ****     ipaddr = IP4_ADDR_ANY4;
 509:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
 510:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 511:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT_CORE_LOCKED();
 512:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 513:Middlewares/Third_Party/LwIP/src/core/netif.c ****   if (netif_do_set_ipaddr(netif, ipaddr, &old_addr)) {
 514:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_NETIF_EXT_STATUS_CALLBACK
 515:Middlewares/Third_Party/LwIP/src/core/netif.c ****     netif_ext_callback_args_t args;
 516:Middlewares/Third_Party/LwIP/src/core/netif.c ****     args.ipv4_changed.old_address = &old_addr;
 517:Middlewares/Third_Party/LwIP/src/core/netif.c ****     netif_invoke_ext_callback(netif, LWIP_NSC_IPV4_ADDRESS_CHANGED, &args);
 518:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif
 519:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
 520:Middlewares/Third_Party/LwIP/src/core/netif.c **** }
 521:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 522:Middlewares/Third_Party/LwIP/src/core/netif.c **** static int
 523:Middlewares/Third_Party/LwIP/src/core/netif.c **** netif_do_set_netmask(struct netif *netif, const ip4_addr_t *netmask, ip_addr_t *old_nm)
 524:Middlewares/Third_Party/LwIP/src/core/netif.c **** {
  28              		.loc 1 524 1 view -0
  29              		.cfi_startproc
  30              		@ args = 0, pretend = 0, frame = 0
  31              		@ frame_needed = 0, uses_anonymous_args = 0
  32              		@ link register save eliminated.
 525:Middlewares/Third_Party/LwIP/src/core/netif.c ****   /* address is actually being changed? */
 526:Middlewares/Third_Party/LwIP/src/core/netif.c ****   if (ip4_addr_cmp(netmask, netif_ip4_netmask(netif)) == 0) {
  33              		.loc 1 526 3 view .LVU1
  34              		.loc 1 526 7 is_stmt 0 view .LVU2
  35 0000 0B68     		ldr	r3, [r1]
  36 0002 8268     		ldr	r2, [r0, #8]
  37              	.LVL1:
  38              		.loc 1 526 6 view .LVU3
  39 0004 9342     		cmp	r3, r2
  40 0006 02D0     		beq	.L3
 527:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_NETIF_EXT_STATUS_CALLBACK
 528:Middlewares/Third_Party/LwIP/src/core/netif.c ****     LWIP_ASSERT("invalid pointer", old_nm != NULL);
 529:Middlewares/Third_Party/LwIP/src/core/netif.c ****     ip_addr_copy(*old_nm, *netif_ip_netmask4(netif));
 530:Middlewares/Third_Party/LwIP/src/core/netif.c **** #else
 531:Middlewares/Third_Party/LwIP/src/core/netif.c ****     LWIP_UNUSED_ARG(old_nm);
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 11


  41              		.loc 1 531 5 is_stmt 1 view .LVU4
 532:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif
 533:Middlewares/Third_Party/LwIP/src/core/netif.c ****     mib2_remove_route_ip4(0, netif);
  42              		.loc 1 533 36 view .LVU5
 534:Middlewares/Third_Party/LwIP/src/core/netif.c ****     /* set new netmask to netif */
 535:Middlewares/Third_Party/LwIP/src/core/netif.c ****     ip4_addr_set(ip_2_ip4(&netif->netmask), netmask);
  43              		.loc 1 535 5 view .LVU6
  44              		.loc 1 535 5 is_stmt 0 discriminator 4 view .LVU7
  45 0008 8360     		str	r3, [r0, #8]
 536:Middlewares/Third_Party/LwIP/src/core/netif.c ****     IP_SET_TYPE_VAL(netif->netmask, IPADDR_TYPE_V4);
  46              		.loc 1 536 52 is_stmt 1 view .LVU8
 537:Middlewares/Third_Party/LwIP/src/core/netif.c ****     mib2_add_route_ip4(0, netif);
  47              		.loc 1 537 33 view .LVU9
 538:Middlewares/Third_Party/LwIP/src/core/netif.c ****     LWIP_DEBUGF(NETIF_DEBUG | LWIP_DBG_TRACE | LWIP_DBG_STATE, ("netif: netmask of interface %c%c s
 539:Middlewares/Third_Party/LwIP/src/core/netif.c ****                 netif->name[0], netif->name[1],
 540:Middlewares/Third_Party/LwIP/src/core/netif.c ****                 ip4_addr1_16(netif_ip4_netmask(netif)),
 541:Middlewares/Third_Party/LwIP/src/core/netif.c ****                 ip4_addr2_16(netif_ip4_netmask(netif)),
 542:Middlewares/Third_Party/LwIP/src/core/netif.c ****                 ip4_addr3_16(netif_ip4_netmask(netif)),
 543:Middlewares/Third_Party/LwIP/src/core/netif.c ****                 ip4_addr4_16(netif_ip4_netmask(netif))));
  48              		.loc 1 543 57 view .LVU10
 544:Middlewares/Third_Party/LwIP/src/core/netif.c ****     return 1; /* netmask changed */
  49              		.loc 1 544 5 view .LVU11
  50              		.loc 1 544 12 is_stmt 0 view .LVU12
  51 000a 0120     		movs	r0, #1
  52              	.LVL2:
  53              		.loc 1 544 12 view .LVU13
  54 000c 7047     		bx	lr
  55              	.LVL3:
  56              	.L3:
 545:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
 546:Middlewares/Third_Party/LwIP/src/core/netif.c ****   return 0; /* netmask unchanged */
  57              		.loc 1 546 10 view .LVU14
  58 000e 0020     		movs	r0, #0
  59              	.LVL4:
 547:Middlewares/Third_Party/LwIP/src/core/netif.c **** }
  60              		.loc 1 547 1 view .LVU15
  61 0010 7047     		bx	lr
  62              		.cfi_endproc
  63              	.LFE181:
  65              		.section	.text.netif_do_set_gw,"ax",%progbits
  66              		.align	1
  67              		.syntax unified
  68              		.thumb
  69              		.thumb_func
  71              	netif_do_set_gw:
  72              	.LVL5:
  73              	.LFB183:
 548:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 549:Middlewares/Third_Party/LwIP/src/core/netif.c **** /**
 550:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @ingroup netif_ip4
 551:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * Change the netmask of a network interface
 552:Middlewares/Third_Party/LwIP/src/core/netif.c ****  *
 553:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @param netif the network interface to change
 554:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @param netmask the new netmask
 555:Middlewares/Third_Party/LwIP/src/core/netif.c ****  *
 556:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @note call netif_set_addr() if you also want to change ip address and
 557:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * default gateway
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 12


 558:Middlewares/Third_Party/LwIP/src/core/netif.c ****  */
 559:Middlewares/Third_Party/LwIP/src/core/netif.c **** void
 560:Middlewares/Third_Party/LwIP/src/core/netif.c **** netif_set_netmask(struct netif *netif, const ip4_addr_t *netmask)
 561:Middlewares/Third_Party/LwIP/src/core/netif.c **** {
 562:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_NETIF_EXT_STATUS_CALLBACK
 563:Middlewares/Third_Party/LwIP/src/core/netif.c ****   ip_addr_t old_nm_val;
 564:Middlewares/Third_Party/LwIP/src/core/netif.c ****   ip_addr_t *old_nm = &old_nm_val;
 565:Middlewares/Third_Party/LwIP/src/core/netif.c **** #else
 566:Middlewares/Third_Party/LwIP/src/core/netif.c ****   ip_addr_t *old_nm = NULL;
 567:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif
 568:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT_CORE_LOCKED();
 569:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 570:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ERROR("netif_set_netmask: invalid netif", netif != NULL, return);
 571:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 572:Middlewares/Third_Party/LwIP/src/core/netif.c ****   /* Don't propagate NULL pointer (IPv4 ANY) to subsequent functions */
 573:Middlewares/Third_Party/LwIP/src/core/netif.c ****   if (netmask == NULL) {
 574:Middlewares/Third_Party/LwIP/src/core/netif.c ****     netmask = IP4_ADDR_ANY4;
 575:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
 576:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 577:Middlewares/Third_Party/LwIP/src/core/netif.c ****   if (netif_do_set_netmask(netif, netmask, old_nm)) {
 578:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_NETIF_EXT_STATUS_CALLBACK
 579:Middlewares/Third_Party/LwIP/src/core/netif.c ****     netif_ext_callback_args_t args;
 580:Middlewares/Third_Party/LwIP/src/core/netif.c ****     args.ipv4_changed.old_netmask = old_nm;
 581:Middlewares/Third_Party/LwIP/src/core/netif.c ****     netif_invoke_ext_callback(netif, LWIP_NSC_IPV4_NETMASK_CHANGED, &args);
 582:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif
 583:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
 584:Middlewares/Third_Party/LwIP/src/core/netif.c **** }
 585:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 586:Middlewares/Third_Party/LwIP/src/core/netif.c **** static int
 587:Middlewares/Third_Party/LwIP/src/core/netif.c **** netif_do_set_gw(struct netif *netif, const ip4_addr_t *gw, ip_addr_t *old_gw)
 588:Middlewares/Third_Party/LwIP/src/core/netif.c **** {
  74              		.loc 1 588 1 is_stmt 1 view -0
  75              		.cfi_startproc
  76              		@ args = 0, pretend = 0, frame = 0
  77              		@ frame_needed = 0, uses_anonymous_args = 0
  78              		@ link register save eliminated.
 589:Middlewares/Third_Party/LwIP/src/core/netif.c ****   /* address is actually being changed? */
 590:Middlewares/Third_Party/LwIP/src/core/netif.c ****   if (ip4_addr_cmp(gw, netif_ip4_gw(netif)) == 0) {
  79              		.loc 1 590 3 view .LVU17
  80              		.loc 1 590 7 is_stmt 0 view .LVU18
  81 0000 0B68     		ldr	r3, [r1]
  82 0002 C268     		ldr	r2, [r0, #12]
  83              	.LVL6:
  84              		.loc 1 590 6 view .LVU19
  85 0004 9342     		cmp	r3, r2
  86 0006 02D0     		beq	.L6
 591:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_NETIF_EXT_STATUS_CALLBACK
 592:Middlewares/Third_Party/LwIP/src/core/netif.c ****     LWIP_ASSERT("invalid pointer", old_gw != NULL);
 593:Middlewares/Third_Party/LwIP/src/core/netif.c ****     ip_addr_copy(*old_gw, *netif_ip_gw4(netif));
 594:Middlewares/Third_Party/LwIP/src/core/netif.c **** #else
 595:Middlewares/Third_Party/LwIP/src/core/netif.c ****     LWIP_UNUSED_ARG(old_gw);
  87              		.loc 1 595 5 is_stmt 1 view .LVU20
 596:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif
 597:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 598:Middlewares/Third_Party/LwIP/src/core/netif.c ****     ip4_addr_set(ip_2_ip4(&netif->gw), gw);
  88              		.loc 1 598 5 view .LVU21
  89              		.loc 1 598 5 is_stmt 0 discriminator 4 view .LVU22
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 13


  90 0008 C360     		str	r3, [r0, #12]
 599:Middlewares/Third_Party/LwIP/src/core/netif.c ****     IP_SET_TYPE_VAL(netif->gw, IPADDR_TYPE_V4);
  91              		.loc 1 599 47 is_stmt 1 view .LVU23
 600:Middlewares/Third_Party/LwIP/src/core/netif.c ****     LWIP_DEBUGF(NETIF_DEBUG | LWIP_DBG_TRACE | LWIP_DBG_STATE, ("netif: GW address of interface %c%
 601:Middlewares/Third_Party/LwIP/src/core/netif.c ****                 netif->name[0], netif->name[1],
 602:Middlewares/Third_Party/LwIP/src/core/netif.c ****                 ip4_addr1_16(netif_ip4_gw(netif)),
 603:Middlewares/Third_Party/LwIP/src/core/netif.c ****                 ip4_addr2_16(netif_ip4_gw(netif)),
 604:Middlewares/Third_Party/LwIP/src/core/netif.c ****                 ip4_addr3_16(netif_ip4_gw(netif)),
 605:Middlewares/Third_Party/LwIP/src/core/netif.c ****                 ip4_addr4_16(netif_ip4_gw(netif))));
  92              		.loc 1 605 52 view .LVU24
 606:Middlewares/Third_Party/LwIP/src/core/netif.c ****     return 1; /* gateway changed */
  93              		.loc 1 606 5 view .LVU25
  94              		.loc 1 606 12 is_stmt 0 view .LVU26
  95 000a 0120     		movs	r0, #1
  96              	.LVL7:
  97              		.loc 1 606 12 view .LVU27
  98 000c 7047     		bx	lr
  99              	.LVL8:
 100              	.L6:
 607:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
 608:Middlewares/Third_Party/LwIP/src/core/netif.c ****   return 0; /* gateway unchanged */
 101              		.loc 1 608 10 view .LVU28
 102 000e 0020     		movs	r0, #0
 103              	.LVL9:
 609:Middlewares/Third_Party/LwIP/src/core/netif.c **** }
 104              		.loc 1 609 1 view .LVU29
 105 0010 7047     		bx	lr
 106              		.cfi_endproc
 107              	.LFE183:
 109              		.section	.text.netif_null_output_ip4,"ax",%progbits
 110              		.align	1
 111              		.syntax unified
 112              		.thumb
 113              		.thumb_func
 115              	netif_null_output_ip4:
 116              	.LVL10:
 117              	.LFB194:
 610:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 611:Middlewares/Third_Party/LwIP/src/core/netif.c **** /**
 612:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @ingroup netif_ip4
 613:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * Change the default gateway for a network interface
 614:Middlewares/Third_Party/LwIP/src/core/netif.c ****  *
 615:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @param netif the network interface to change
 616:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @param gw the new default gateway
 617:Middlewares/Third_Party/LwIP/src/core/netif.c ****  *
 618:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @note call netif_set_addr() if you also want to change ip address and netmask
 619:Middlewares/Third_Party/LwIP/src/core/netif.c ****  */
 620:Middlewares/Third_Party/LwIP/src/core/netif.c **** void
 621:Middlewares/Third_Party/LwIP/src/core/netif.c **** netif_set_gw(struct netif *netif, const ip4_addr_t *gw)
 622:Middlewares/Third_Party/LwIP/src/core/netif.c **** {
 623:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_NETIF_EXT_STATUS_CALLBACK
 624:Middlewares/Third_Party/LwIP/src/core/netif.c ****   ip_addr_t old_gw_val;
 625:Middlewares/Third_Party/LwIP/src/core/netif.c ****   ip_addr_t *old_gw = &old_gw_val;
 626:Middlewares/Third_Party/LwIP/src/core/netif.c **** #else
 627:Middlewares/Third_Party/LwIP/src/core/netif.c ****   ip_addr_t *old_gw = NULL;
 628:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif
 629:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT_CORE_LOCKED();
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 14


 630:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 631:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ERROR("netif_set_gw: invalid netif", netif != NULL, return);
 632:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 633:Middlewares/Third_Party/LwIP/src/core/netif.c ****   /* Don't propagate NULL pointer (IPv4 ANY) to subsequent functions */
 634:Middlewares/Third_Party/LwIP/src/core/netif.c ****   if (gw == NULL) {
 635:Middlewares/Third_Party/LwIP/src/core/netif.c ****     gw = IP4_ADDR_ANY4;
 636:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
 637:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 638:Middlewares/Third_Party/LwIP/src/core/netif.c ****   if (netif_do_set_gw(netif, gw, old_gw)) {
 639:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_NETIF_EXT_STATUS_CALLBACK
 640:Middlewares/Third_Party/LwIP/src/core/netif.c ****     netif_ext_callback_args_t args;
 641:Middlewares/Third_Party/LwIP/src/core/netif.c ****     args.ipv4_changed.old_gw = old_gw;
 642:Middlewares/Third_Party/LwIP/src/core/netif.c ****     netif_invoke_ext_callback(netif, LWIP_NSC_IPV4_GATEWAY_CHANGED, &args);
 643:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif
 644:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
 645:Middlewares/Third_Party/LwIP/src/core/netif.c **** }
 646:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 647:Middlewares/Third_Party/LwIP/src/core/netif.c **** /**
 648:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @ingroup netif_ip4
 649:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * Change IP address configuration for a network interface (including netmask
 650:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * and default gateway).
 651:Middlewares/Third_Party/LwIP/src/core/netif.c ****  *
 652:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @param netif the network interface to change
 653:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @param ipaddr the new IP address
 654:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @param netmask the new netmask
 655:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @param gw the new default gateway
 656:Middlewares/Third_Party/LwIP/src/core/netif.c ****  */
 657:Middlewares/Third_Party/LwIP/src/core/netif.c **** void
 658:Middlewares/Third_Party/LwIP/src/core/netif.c **** netif_set_addr(struct netif *netif, const ip4_addr_t *ipaddr, const ip4_addr_t *netmask,
 659:Middlewares/Third_Party/LwIP/src/core/netif.c ****                const ip4_addr_t *gw)
 660:Middlewares/Third_Party/LwIP/src/core/netif.c **** {
 661:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_NETIF_EXT_STATUS_CALLBACK
 662:Middlewares/Third_Party/LwIP/src/core/netif.c ****   netif_nsc_reason_t change_reason = LWIP_NSC_NONE;
 663:Middlewares/Third_Party/LwIP/src/core/netif.c ****   netif_ext_callback_args_t cb_args;
 664:Middlewares/Third_Party/LwIP/src/core/netif.c ****   ip_addr_t old_nm_val;
 665:Middlewares/Third_Party/LwIP/src/core/netif.c ****   ip_addr_t old_gw_val;
 666:Middlewares/Third_Party/LwIP/src/core/netif.c ****   ip_addr_t *old_nm = &old_nm_val;
 667:Middlewares/Third_Party/LwIP/src/core/netif.c ****   ip_addr_t *old_gw = &old_gw_val;
 668:Middlewares/Third_Party/LwIP/src/core/netif.c **** #else
 669:Middlewares/Third_Party/LwIP/src/core/netif.c ****   ip_addr_t *old_nm = NULL;
 670:Middlewares/Third_Party/LwIP/src/core/netif.c ****   ip_addr_t *old_gw = NULL;
 671:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif
 672:Middlewares/Third_Party/LwIP/src/core/netif.c ****   ip_addr_t old_addr;
 673:Middlewares/Third_Party/LwIP/src/core/netif.c ****   int remove;
 674:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 675:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT_CORE_LOCKED();
 676:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 677:Middlewares/Third_Party/LwIP/src/core/netif.c ****   /* Don't propagate NULL pointer (IPv4 ANY) to subsequent functions */
 678:Middlewares/Third_Party/LwIP/src/core/netif.c ****   if (ipaddr == NULL) {
 679:Middlewares/Third_Party/LwIP/src/core/netif.c ****     ipaddr = IP4_ADDR_ANY4;
 680:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
 681:Middlewares/Third_Party/LwIP/src/core/netif.c ****   if (netmask == NULL) {
 682:Middlewares/Third_Party/LwIP/src/core/netif.c ****     netmask = IP4_ADDR_ANY4;
 683:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
 684:Middlewares/Third_Party/LwIP/src/core/netif.c ****   if (gw == NULL) {
 685:Middlewares/Third_Party/LwIP/src/core/netif.c ****     gw = IP4_ADDR_ANY4;
 686:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 15


 687:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 688:Middlewares/Third_Party/LwIP/src/core/netif.c ****   remove = ip4_addr_isany(ipaddr);
 689:Middlewares/Third_Party/LwIP/src/core/netif.c ****   if (remove) {
 690:Middlewares/Third_Party/LwIP/src/core/netif.c ****     /* when removing an address, we have to remove it *before* changing netmask/gw
 691:Middlewares/Third_Party/LwIP/src/core/netif.c ****        to ensure that tcp RST segment can be sent correctly */
 692:Middlewares/Third_Party/LwIP/src/core/netif.c ****     if (netif_do_set_ipaddr(netif, ipaddr, &old_addr)) {
 693:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_NETIF_EXT_STATUS_CALLBACK
 694:Middlewares/Third_Party/LwIP/src/core/netif.c ****       change_reason |= LWIP_NSC_IPV4_ADDRESS_CHANGED;
 695:Middlewares/Third_Party/LwIP/src/core/netif.c ****       cb_args.ipv4_changed.old_address = &old_addr;
 696:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif
 697:Middlewares/Third_Party/LwIP/src/core/netif.c ****     }
 698:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
 699:Middlewares/Third_Party/LwIP/src/core/netif.c ****   if (netif_do_set_netmask(netif, netmask, old_nm)) {
 700:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_NETIF_EXT_STATUS_CALLBACK
 701:Middlewares/Third_Party/LwIP/src/core/netif.c ****     change_reason |= LWIP_NSC_IPV4_NETMASK_CHANGED;
 702:Middlewares/Third_Party/LwIP/src/core/netif.c ****     cb_args.ipv4_changed.old_netmask = old_nm;
 703:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif
 704:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
 705:Middlewares/Third_Party/LwIP/src/core/netif.c ****   if (netif_do_set_gw(netif, gw, old_gw)) {
 706:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_NETIF_EXT_STATUS_CALLBACK
 707:Middlewares/Third_Party/LwIP/src/core/netif.c ****     change_reason |= LWIP_NSC_IPV4_GATEWAY_CHANGED;
 708:Middlewares/Third_Party/LwIP/src/core/netif.c ****     cb_args.ipv4_changed.old_gw = old_gw;
 709:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif
 710:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
 711:Middlewares/Third_Party/LwIP/src/core/netif.c ****   if (!remove) {
 712:Middlewares/Third_Party/LwIP/src/core/netif.c ****     /* set ipaddr last to ensure netmask/gw have been set when status callback is called */
 713:Middlewares/Third_Party/LwIP/src/core/netif.c ****     if (netif_do_set_ipaddr(netif, ipaddr, &old_addr)) {
 714:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_NETIF_EXT_STATUS_CALLBACK
 715:Middlewares/Third_Party/LwIP/src/core/netif.c ****       change_reason |= LWIP_NSC_IPV4_ADDRESS_CHANGED;
 716:Middlewares/Third_Party/LwIP/src/core/netif.c ****       cb_args.ipv4_changed.old_address = &old_addr;
 717:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif
 718:Middlewares/Third_Party/LwIP/src/core/netif.c ****     }
 719:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
 720:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 721:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_NETIF_EXT_STATUS_CALLBACK
 722:Middlewares/Third_Party/LwIP/src/core/netif.c ****   if (change_reason != LWIP_NSC_NONE) {
 723:Middlewares/Third_Party/LwIP/src/core/netif.c ****     change_reason |= LWIP_NSC_IPV4_SETTINGS_CHANGED;
 724:Middlewares/Third_Party/LwIP/src/core/netif.c ****     netif_invoke_ext_callback(netif, change_reason, &cb_args);
 725:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
 726:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif
 727:Middlewares/Third_Party/LwIP/src/core/netif.c **** }
 728:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_IPV4*/
 729:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 730:Middlewares/Third_Party/LwIP/src/core/netif.c **** /**
 731:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @ingroup netif
 732:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * Remove a network interface from the list of lwIP netifs.
 733:Middlewares/Third_Party/LwIP/src/core/netif.c ****  *
 734:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @param netif the network interface to remove
 735:Middlewares/Third_Party/LwIP/src/core/netif.c ****  */
 736:Middlewares/Third_Party/LwIP/src/core/netif.c **** void
 737:Middlewares/Third_Party/LwIP/src/core/netif.c **** netif_remove(struct netif *netif)
 738:Middlewares/Third_Party/LwIP/src/core/netif.c **** {
 739:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_IPV6
 740:Middlewares/Third_Party/LwIP/src/core/netif.c ****   int i;
 741:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif
 742:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 743:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT_CORE_LOCKED();
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 16


 744:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 745:Middlewares/Third_Party/LwIP/src/core/netif.c ****   if (netif == NULL) {
 746:Middlewares/Third_Party/LwIP/src/core/netif.c ****     return;
 747:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
 748:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 749:Middlewares/Third_Party/LwIP/src/core/netif.c ****   netif_invoke_ext_callback(netif, LWIP_NSC_NETIF_REMOVED, NULL);
 750:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 751:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_IPV4
 752:Middlewares/Third_Party/LwIP/src/core/netif.c ****   if (!ip4_addr_isany_val(*netif_ip4_addr(netif))) {
 753:Middlewares/Third_Party/LwIP/src/core/netif.c ****     netif_do_ip_addr_changed(netif_ip_addr4(netif), NULL);
 754:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
 755:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 756:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_IGMP
 757:Middlewares/Third_Party/LwIP/src/core/netif.c ****   /* stop IGMP processing */
 758:Middlewares/Third_Party/LwIP/src/core/netif.c ****   if (netif->flags & NETIF_FLAG_IGMP) {
 759:Middlewares/Third_Party/LwIP/src/core/netif.c ****     igmp_stop(netif);
 760:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
 761:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_IGMP */
 762:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_IPV4*/
 763:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 764:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_IPV6
 765:Middlewares/Third_Party/LwIP/src/core/netif.c ****   for (i = 0; i < LWIP_IPV6_NUM_ADDRESSES; i++) {
 766:Middlewares/Third_Party/LwIP/src/core/netif.c ****     if (ip6_addr_isvalid(netif_ip6_addr_state(netif, i))) {
 767:Middlewares/Third_Party/LwIP/src/core/netif.c ****       netif_do_ip_addr_changed(netif_ip_addr6(netif, i), NULL);
 768:Middlewares/Third_Party/LwIP/src/core/netif.c ****     }
 769:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
 770:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_IPV6_MLD
 771:Middlewares/Third_Party/LwIP/src/core/netif.c ****   /* stop MLD processing */
 772:Middlewares/Third_Party/LwIP/src/core/netif.c ****   mld6_stop(netif);
 773:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_IPV6_MLD */
 774:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_IPV6 */
 775:Middlewares/Third_Party/LwIP/src/core/netif.c ****   if (netif_is_up(netif)) {
 776:Middlewares/Third_Party/LwIP/src/core/netif.c ****     /* set netif down before removing (call callback function) */
 777:Middlewares/Third_Party/LwIP/src/core/netif.c ****     netif_set_down(netif);
 778:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
 779:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 780:Middlewares/Third_Party/LwIP/src/core/netif.c ****   mib2_remove_ip4(netif);
 781:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 782:Middlewares/Third_Party/LwIP/src/core/netif.c ****   /* this netif is default? */
 783:Middlewares/Third_Party/LwIP/src/core/netif.c ****   if (netif_default == netif) {
 784:Middlewares/Third_Party/LwIP/src/core/netif.c ****     /* reset default netif */
 785:Middlewares/Third_Party/LwIP/src/core/netif.c ****     netif_set_default(NULL);
 786:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
 787:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if !LWIP_SINGLE_NETIF
 788:Middlewares/Third_Party/LwIP/src/core/netif.c ****   /*  is it the first netif? */
 789:Middlewares/Third_Party/LwIP/src/core/netif.c ****   if (netif_list == netif) {
 790:Middlewares/Third_Party/LwIP/src/core/netif.c ****     netif_list = netif->next;
 791:Middlewares/Third_Party/LwIP/src/core/netif.c ****   } else {
 792:Middlewares/Third_Party/LwIP/src/core/netif.c ****     /*  look for netif further down the list */
 793:Middlewares/Third_Party/LwIP/src/core/netif.c ****     struct netif *tmp_netif;
 794:Middlewares/Third_Party/LwIP/src/core/netif.c ****     NETIF_FOREACH(tmp_netif) {
 795:Middlewares/Third_Party/LwIP/src/core/netif.c ****       if (tmp_netif->next == netif) {
 796:Middlewares/Third_Party/LwIP/src/core/netif.c ****         tmp_netif->next = netif->next;
 797:Middlewares/Third_Party/LwIP/src/core/netif.c ****         break;
 798:Middlewares/Third_Party/LwIP/src/core/netif.c ****       }
 799:Middlewares/Third_Party/LwIP/src/core/netif.c ****     }
 800:Middlewares/Third_Party/LwIP/src/core/netif.c ****     if (tmp_netif == NULL) {
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 17


 801:Middlewares/Third_Party/LwIP/src/core/netif.c ****       return; /* netif is not on the list */
 802:Middlewares/Third_Party/LwIP/src/core/netif.c ****     }
 803:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
 804:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* !LWIP_SINGLE_NETIF */
 805:Middlewares/Third_Party/LwIP/src/core/netif.c ****   mib2_netif_removed(netif);
 806:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_NETIF_REMOVE_CALLBACK
 807:Middlewares/Third_Party/LwIP/src/core/netif.c ****   if (netif->remove_callback) {
 808:Middlewares/Third_Party/LwIP/src/core/netif.c ****     netif->remove_callback(netif);
 809:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
 810:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_NETIF_REMOVE_CALLBACK */
 811:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_DEBUGF( NETIF_DEBUG, ("netif_remove: removed netif\n") );
 812:Middlewares/Third_Party/LwIP/src/core/netif.c **** }
 813:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 814:Middlewares/Third_Party/LwIP/src/core/netif.c **** /**
 815:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @ingroup netif
 816:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * Set a network interface as the default network interface
 817:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * (used to output all packets for which no specific route is found)
 818:Middlewares/Third_Party/LwIP/src/core/netif.c ****  *
 819:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @param netif the default network interface
 820:Middlewares/Third_Party/LwIP/src/core/netif.c ****  */
 821:Middlewares/Third_Party/LwIP/src/core/netif.c **** void
 822:Middlewares/Third_Party/LwIP/src/core/netif.c **** netif_set_default(struct netif *netif)
 823:Middlewares/Third_Party/LwIP/src/core/netif.c **** {
 824:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT_CORE_LOCKED();
 825:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 826:Middlewares/Third_Party/LwIP/src/core/netif.c ****   if (netif == NULL) {
 827:Middlewares/Third_Party/LwIP/src/core/netif.c ****     /* remove default route */
 828:Middlewares/Third_Party/LwIP/src/core/netif.c ****     mib2_remove_route_ip4(1, netif);
 829:Middlewares/Third_Party/LwIP/src/core/netif.c ****   } else {
 830:Middlewares/Third_Party/LwIP/src/core/netif.c ****     /* install default route */
 831:Middlewares/Third_Party/LwIP/src/core/netif.c ****     mib2_add_route_ip4(1, netif);
 832:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
 833:Middlewares/Third_Party/LwIP/src/core/netif.c ****   netif_default = netif;
 834:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_DEBUGF(NETIF_DEBUG, ("netif: setting default interface %c%c\n",
 835:Middlewares/Third_Party/LwIP/src/core/netif.c ****                             netif ? netif->name[0] : '\'', netif ? netif->name[1] : '\''));
 836:Middlewares/Third_Party/LwIP/src/core/netif.c **** }
 837:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 838:Middlewares/Third_Party/LwIP/src/core/netif.c **** /**
 839:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @ingroup netif
 840:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * Bring an interface up, available for processing
 841:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * traffic.
 842:Middlewares/Third_Party/LwIP/src/core/netif.c ****  */
 843:Middlewares/Third_Party/LwIP/src/core/netif.c **** void
 844:Middlewares/Third_Party/LwIP/src/core/netif.c **** netif_set_up(struct netif *netif)
 845:Middlewares/Third_Party/LwIP/src/core/netif.c **** {
 846:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT_CORE_LOCKED();
 847:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 848:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ERROR("netif_set_up: invalid netif", netif != NULL, return);
 849:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 850:Middlewares/Third_Party/LwIP/src/core/netif.c ****   if (!(netif->flags & NETIF_FLAG_UP)) {
 851:Middlewares/Third_Party/LwIP/src/core/netif.c ****     netif_set_flags(netif, NETIF_FLAG_UP);
 852:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 853:Middlewares/Third_Party/LwIP/src/core/netif.c ****     MIB2_COPY_SYSUPTIME_TO(&netif->ts);
 854:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 855:Middlewares/Third_Party/LwIP/src/core/netif.c ****     NETIF_STATUS_CALLBACK(netif);
 856:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 857:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_NETIF_EXT_STATUS_CALLBACK
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 18


 858:Middlewares/Third_Party/LwIP/src/core/netif.c ****     {
 859:Middlewares/Third_Party/LwIP/src/core/netif.c ****       netif_ext_callback_args_t args;
 860:Middlewares/Third_Party/LwIP/src/core/netif.c ****       args.status_changed.state = 1;
 861:Middlewares/Third_Party/LwIP/src/core/netif.c ****       netif_invoke_ext_callback(netif, LWIP_NSC_STATUS_CHANGED, &args);
 862:Middlewares/Third_Party/LwIP/src/core/netif.c ****     }
 863:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif
 864:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 865:Middlewares/Third_Party/LwIP/src/core/netif.c ****     netif_issue_reports(netif, NETIF_REPORT_TYPE_IPV4 | NETIF_REPORT_TYPE_IPV6);
 866:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_IPV6
 867:Middlewares/Third_Party/LwIP/src/core/netif.c ****     nd6_restart_netif(netif);
 868:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_IPV6 */
 869:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
 870:Middlewares/Third_Party/LwIP/src/core/netif.c **** }
 871:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 872:Middlewares/Third_Party/LwIP/src/core/netif.c **** /** Send ARP/IGMP/MLD/RS events, e.g. on link-up/netif-up or addr-change
 873:Middlewares/Third_Party/LwIP/src/core/netif.c ****  */
 874:Middlewares/Third_Party/LwIP/src/core/netif.c **** static void
 875:Middlewares/Third_Party/LwIP/src/core/netif.c **** netif_issue_reports(struct netif *netif, u8_t report_type)
 876:Middlewares/Third_Party/LwIP/src/core/netif.c **** {
 877:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT("netif_issue_reports: invalid netif", netif != NULL);
 878:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 879:Middlewares/Third_Party/LwIP/src/core/netif.c ****   /* Only send reports when both link and admin states are up */
 880:Middlewares/Third_Party/LwIP/src/core/netif.c ****   if (!(netif->flags & NETIF_FLAG_LINK_UP) ||
 881:Middlewares/Third_Party/LwIP/src/core/netif.c ****       !(netif->flags & NETIF_FLAG_UP)) {
 882:Middlewares/Third_Party/LwIP/src/core/netif.c ****     return;
 883:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
 884:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 885:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_IPV4
 886:Middlewares/Third_Party/LwIP/src/core/netif.c ****   if ((report_type & NETIF_REPORT_TYPE_IPV4) &&
 887:Middlewares/Third_Party/LwIP/src/core/netif.c ****       !ip4_addr_isany_val(*netif_ip4_addr(netif))) {
 888:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_ARP
 889:Middlewares/Third_Party/LwIP/src/core/netif.c ****     /* For Ethernet network interfaces, we would like to send a "gratuitous ARP" */
 890:Middlewares/Third_Party/LwIP/src/core/netif.c ****     if (netif->flags & (NETIF_FLAG_ETHARP)) {
 891:Middlewares/Third_Party/LwIP/src/core/netif.c ****       etharp_gratuitous(netif);
 892:Middlewares/Third_Party/LwIP/src/core/netif.c ****     }
 893:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_ARP */
 894:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 895:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_IGMP
 896:Middlewares/Third_Party/LwIP/src/core/netif.c ****     /* resend IGMP memberships */
 897:Middlewares/Third_Party/LwIP/src/core/netif.c ****     if (netif->flags & NETIF_FLAG_IGMP) {
 898:Middlewares/Third_Party/LwIP/src/core/netif.c ****       igmp_report_groups(netif);
 899:Middlewares/Third_Party/LwIP/src/core/netif.c ****     }
 900:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_IGMP */
 901:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
 902:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_IPV4 */
 903:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 904:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_IPV6
 905:Middlewares/Third_Party/LwIP/src/core/netif.c ****   if (report_type & NETIF_REPORT_TYPE_IPV6) {
 906:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_IPV6_MLD
 907:Middlewares/Third_Party/LwIP/src/core/netif.c ****     /* send mld memberships */
 908:Middlewares/Third_Party/LwIP/src/core/netif.c ****     mld6_report_groups(netif);
 909:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_IPV6_MLD */
 910:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
 911:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_IPV6 */
 912:Middlewares/Third_Party/LwIP/src/core/netif.c **** }
 913:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 914:Middlewares/Third_Party/LwIP/src/core/netif.c **** /**
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 19


 915:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @ingroup netif
 916:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * Bring an interface down, disabling any traffic processing.
 917:Middlewares/Third_Party/LwIP/src/core/netif.c ****  */
 918:Middlewares/Third_Party/LwIP/src/core/netif.c **** void
 919:Middlewares/Third_Party/LwIP/src/core/netif.c **** netif_set_down(struct netif *netif)
 920:Middlewares/Third_Party/LwIP/src/core/netif.c **** {
 921:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT_CORE_LOCKED();
 922:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 923:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ERROR("netif_set_down: invalid netif", netif != NULL, return);
 924:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 925:Middlewares/Third_Party/LwIP/src/core/netif.c ****   if (netif->flags & NETIF_FLAG_UP) {
 926:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_NETIF_EXT_STATUS_CALLBACK
 927:Middlewares/Third_Party/LwIP/src/core/netif.c ****     {
 928:Middlewares/Third_Party/LwIP/src/core/netif.c ****       netif_ext_callback_args_t args;
 929:Middlewares/Third_Party/LwIP/src/core/netif.c ****       args.status_changed.state = 0;
 930:Middlewares/Third_Party/LwIP/src/core/netif.c ****       netif_invoke_ext_callback(netif, LWIP_NSC_STATUS_CHANGED, &args);
 931:Middlewares/Third_Party/LwIP/src/core/netif.c ****     }
 932:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif
 933:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 934:Middlewares/Third_Party/LwIP/src/core/netif.c ****     netif_clear_flags(netif, NETIF_FLAG_UP);
 935:Middlewares/Third_Party/LwIP/src/core/netif.c ****     MIB2_COPY_SYSUPTIME_TO(&netif->ts);
 936:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 937:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_IPV4 && LWIP_ARP
 938:Middlewares/Third_Party/LwIP/src/core/netif.c ****     if (netif->flags & NETIF_FLAG_ETHARP) {
 939:Middlewares/Third_Party/LwIP/src/core/netif.c ****       etharp_cleanup_netif(netif);
 940:Middlewares/Third_Party/LwIP/src/core/netif.c ****     }
 941:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_IPV4 && LWIP_ARP */
 942:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 943:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_IPV6
 944:Middlewares/Third_Party/LwIP/src/core/netif.c ****     nd6_cleanup_netif(netif);
 945:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_IPV6 */
 946:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 947:Middlewares/Third_Party/LwIP/src/core/netif.c ****     NETIF_STATUS_CALLBACK(netif);
 948:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
 949:Middlewares/Third_Party/LwIP/src/core/netif.c **** }
 950:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 951:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_NETIF_STATUS_CALLBACK
 952:Middlewares/Third_Party/LwIP/src/core/netif.c **** /**
 953:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @ingroup netif
 954:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * Set callback to be called when interface is brought up/down or address is changed while up
 955:Middlewares/Third_Party/LwIP/src/core/netif.c ****  */
 956:Middlewares/Third_Party/LwIP/src/core/netif.c **** void
 957:Middlewares/Third_Party/LwIP/src/core/netif.c **** netif_set_status_callback(struct netif *netif, netif_status_callback_fn status_callback)
 958:Middlewares/Third_Party/LwIP/src/core/netif.c **** {
 959:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT_CORE_LOCKED();
 960:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 961:Middlewares/Third_Party/LwIP/src/core/netif.c ****   if (netif) {
 962:Middlewares/Third_Party/LwIP/src/core/netif.c ****     netif->status_callback = status_callback;
 963:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
 964:Middlewares/Third_Party/LwIP/src/core/netif.c **** }
 965:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_NETIF_STATUS_CALLBACK */
 966:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 967:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_NETIF_REMOVE_CALLBACK
 968:Middlewares/Third_Party/LwIP/src/core/netif.c **** /**
 969:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @ingroup netif
 970:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * Set callback to be called when the interface has been removed
 971:Middlewares/Third_Party/LwIP/src/core/netif.c ****  */
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 20


 972:Middlewares/Third_Party/LwIP/src/core/netif.c **** void
 973:Middlewares/Third_Party/LwIP/src/core/netif.c **** netif_set_remove_callback(struct netif *netif, netif_status_callback_fn remove_callback)
 974:Middlewares/Third_Party/LwIP/src/core/netif.c **** {
 975:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT_CORE_LOCKED();
 976:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 977:Middlewares/Third_Party/LwIP/src/core/netif.c ****   if (netif) {
 978:Middlewares/Third_Party/LwIP/src/core/netif.c ****     netif->remove_callback = remove_callback;
 979:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
 980:Middlewares/Third_Party/LwIP/src/core/netif.c **** }
 981:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_NETIF_REMOVE_CALLBACK */
 982:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 983:Middlewares/Third_Party/LwIP/src/core/netif.c **** /**
 984:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @ingroup netif
 985:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * Called by a driver when its link goes up
 986:Middlewares/Third_Party/LwIP/src/core/netif.c ****  */
 987:Middlewares/Third_Party/LwIP/src/core/netif.c **** void
 988:Middlewares/Third_Party/LwIP/src/core/netif.c **** netif_set_link_up(struct netif *netif)
 989:Middlewares/Third_Party/LwIP/src/core/netif.c **** {
 990:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT_CORE_LOCKED();
 991:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 992:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ERROR("netif_set_link_up: invalid netif", netif != NULL, return);
 993:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 994:Middlewares/Third_Party/LwIP/src/core/netif.c ****   if (!(netif->flags & NETIF_FLAG_LINK_UP)) {
 995:Middlewares/Third_Party/LwIP/src/core/netif.c ****     netif_set_flags(netif, NETIF_FLAG_LINK_UP);
 996:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 997:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_DHCP
 998:Middlewares/Third_Party/LwIP/src/core/netif.c ****     dhcp_network_changed(netif);
 999:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_DHCP */
1000:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1001:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_AUTOIP
1002:Middlewares/Third_Party/LwIP/src/core/netif.c ****     autoip_network_changed(netif);
1003:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_AUTOIP */
1004:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1005:Middlewares/Third_Party/LwIP/src/core/netif.c ****     netif_issue_reports(netif, NETIF_REPORT_TYPE_IPV4 | NETIF_REPORT_TYPE_IPV6);
1006:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_IPV6
1007:Middlewares/Third_Party/LwIP/src/core/netif.c ****     nd6_restart_netif(netif);
1008:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_IPV6 */
1009:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1010:Middlewares/Third_Party/LwIP/src/core/netif.c ****     NETIF_LINK_CALLBACK(netif);
1011:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_NETIF_EXT_STATUS_CALLBACK
1012:Middlewares/Third_Party/LwIP/src/core/netif.c ****     {
1013:Middlewares/Third_Party/LwIP/src/core/netif.c ****       netif_ext_callback_args_t args;
1014:Middlewares/Third_Party/LwIP/src/core/netif.c ****       args.link_changed.state = 1;
1015:Middlewares/Third_Party/LwIP/src/core/netif.c ****       netif_invoke_ext_callback(netif, LWIP_NSC_LINK_CHANGED, &args);
1016:Middlewares/Third_Party/LwIP/src/core/netif.c ****     }
1017:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif
1018:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
1019:Middlewares/Third_Party/LwIP/src/core/netif.c **** }
1020:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1021:Middlewares/Third_Party/LwIP/src/core/netif.c **** /**
1022:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @ingroup netif
1023:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * Called by a driver when its link goes down
1024:Middlewares/Third_Party/LwIP/src/core/netif.c ****  */
1025:Middlewares/Third_Party/LwIP/src/core/netif.c **** void
1026:Middlewares/Third_Party/LwIP/src/core/netif.c **** netif_set_link_down(struct netif *netif)
1027:Middlewares/Third_Party/LwIP/src/core/netif.c **** {
1028:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT_CORE_LOCKED();
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 21


1029:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1030:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ERROR("netif_set_link_down: invalid netif", netif != NULL, return);
1031:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1032:Middlewares/Third_Party/LwIP/src/core/netif.c ****   if (netif->flags & NETIF_FLAG_LINK_UP) {
1033:Middlewares/Third_Party/LwIP/src/core/netif.c ****     netif_clear_flags(netif, NETIF_FLAG_LINK_UP);
1034:Middlewares/Third_Party/LwIP/src/core/netif.c ****     NETIF_LINK_CALLBACK(netif);
1035:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_NETIF_EXT_STATUS_CALLBACK
1036:Middlewares/Third_Party/LwIP/src/core/netif.c ****     {
1037:Middlewares/Third_Party/LwIP/src/core/netif.c ****       netif_ext_callback_args_t args;
1038:Middlewares/Third_Party/LwIP/src/core/netif.c ****       args.link_changed.state = 0;
1039:Middlewares/Third_Party/LwIP/src/core/netif.c ****       netif_invoke_ext_callback(netif, LWIP_NSC_LINK_CHANGED, &args);
1040:Middlewares/Third_Party/LwIP/src/core/netif.c ****     }
1041:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif
1042:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
1043:Middlewares/Third_Party/LwIP/src/core/netif.c **** }
1044:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1045:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_NETIF_LINK_CALLBACK
1046:Middlewares/Third_Party/LwIP/src/core/netif.c **** /**
1047:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @ingroup netif
1048:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * Set callback to be called when link is brought up/down
1049:Middlewares/Third_Party/LwIP/src/core/netif.c ****  */
1050:Middlewares/Third_Party/LwIP/src/core/netif.c **** void
1051:Middlewares/Third_Party/LwIP/src/core/netif.c **** netif_set_link_callback(struct netif *netif, netif_status_callback_fn link_callback)
1052:Middlewares/Third_Party/LwIP/src/core/netif.c **** {
1053:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT_CORE_LOCKED();
1054:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1055:Middlewares/Third_Party/LwIP/src/core/netif.c ****   if (netif) {
1056:Middlewares/Third_Party/LwIP/src/core/netif.c ****     netif->link_callback = link_callback;
1057:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
1058:Middlewares/Third_Party/LwIP/src/core/netif.c **** }
1059:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_NETIF_LINK_CALLBACK */
1060:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1061:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if ENABLE_LOOPBACK
1062:Middlewares/Third_Party/LwIP/src/core/netif.c **** /**
1063:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @ingroup netif
1064:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * Send an IP packet to be received on the same netif (loopif-like).
1065:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * The pbuf is simply copied and handed back to netif->input.
1066:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * In multithreaded mode, this is done directly since netif->input must put
1067:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * the packet on a queue.
1068:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * In callback mode, the packet is put on an internal queue and is fed to
1069:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * netif->input by netif_poll().
1070:Middlewares/Third_Party/LwIP/src/core/netif.c ****  *
1071:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @param netif the lwip network interface structure
1072:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @param p the (IP) packet to 'send'
1073:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @return ERR_OK if the packet has been sent
1074:Middlewares/Third_Party/LwIP/src/core/netif.c ****  *         ERR_MEM if the pbuf used to copy the packet couldn't be allocated
1075:Middlewares/Third_Party/LwIP/src/core/netif.c ****  */
1076:Middlewares/Third_Party/LwIP/src/core/netif.c **** err_t
1077:Middlewares/Third_Party/LwIP/src/core/netif.c **** netif_loop_output(struct netif *netif, struct pbuf *p)
1078:Middlewares/Third_Party/LwIP/src/core/netif.c **** {
1079:Middlewares/Third_Party/LwIP/src/core/netif.c ****   struct pbuf *r;
1080:Middlewares/Third_Party/LwIP/src/core/netif.c ****   err_t err;
1081:Middlewares/Third_Party/LwIP/src/core/netif.c ****   struct pbuf *last;
1082:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_LOOPBACK_MAX_PBUFS
1083:Middlewares/Third_Party/LwIP/src/core/netif.c ****   u16_t clen = 0;
1084:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_LOOPBACK_MAX_PBUFS */
1085:Middlewares/Third_Party/LwIP/src/core/netif.c ****   /* If we have a loopif, SNMP counters are adjusted for it,
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 22


1086:Middlewares/Third_Party/LwIP/src/core/netif.c ****    * if not they are adjusted for 'netif'. */
1087:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if MIB2_STATS
1088:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_HAVE_LOOPIF
1089:Middlewares/Third_Party/LwIP/src/core/netif.c ****   struct netif *stats_if = &loop_netif;
1090:Middlewares/Third_Party/LwIP/src/core/netif.c **** #else /* LWIP_HAVE_LOOPIF */
1091:Middlewares/Third_Party/LwIP/src/core/netif.c ****   struct netif *stats_if = netif;
1092:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_HAVE_LOOPIF */
1093:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* MIB2_STATS */
1094:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_NETIF_LOOPBACK_MULTITHREADING
1095:Middlewares/Third_Party/LwIP/src/core/netif.c ****   u8_t schedule_poll = 0;
1096:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_NETIF_LOOPBACK_MULTITHREADING */
1097:Middlewares/Third_Party/LwIP/src/core/netif.c ****   SYS_ARCH_DECL_PROTECT(lev);
1098:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1099:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT("netif_loop_output: invalid netif", netif != NULL);
1100:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT("netif_loop_output: invalid pbuf", p != NULL);
1101:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1102:Middlewares/Third_Party/LwIP/src/core/netif.c ****   /* Allocate a new pbuf */
1103:Middlewares/Third_Party/LwIP/src/core/netif.c ****   r = pbuf_alloc(PBUF_LINK, p->tot_len, PBUF_RAM);
1104:Middlewares/Third_Party/LwIP/src/core/netif.c ****   if (r == NULL) {
1105:Middlewares/Third_Party/LwIP/src/core/netif.c ****     LINK_STATS_INC(link.memerr);
1106:Middlewares/Third_Party/LwIP/src/core/netif.c ****     LINK_STATS_INC(link.drop);
1107:Middlewares/Third_Party/LwIP/src/core/netif.c ****     MIB2_STATS_NETIF_INC(stats_if, ifoutdiscards);
1108:Middlewares/Third_Party/LwIP/src/core/netif.c ****     return ERR_MEM;
1109:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
1110:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_LOOPBACK_MAX_PBUFS
1111:Middlewares/Third_Party/LwIP/src/core/netif.c ****   clen = pbuf_clen(r);
1112:Middlewares/Third_Party/LwIP/src/core/netif.c ****   /* check for overflow or too many pbuf on queue */
1113:Middlewares/Third_Party/LwIP/src/core/netif.c ****   if (((netif->loop_cnt_current + clen) < netif->loop_cnt_current) ||
1114:Middlewares/Third_Party/LwIP/src/core/netif.c ****       ((netif->loop_cnt_current + clen) > LWIP_MIN(LWIP_LOOPBACK_MAX_PBUFS, 0xFFFF))) {
1115:Middlewares/Third_Party/LwIP/src/core/netif.c ****     pbuf_free(r);
1116:Middlewares/Third_Party/LwIP/src/core/netif.c ****     LINK_STATS_INC(link.memerr);
1117:Middlewares/Third_Party/LwIP/src/core/netif.c ****     LINK_STATS_INC(link.drop);
1118:Middlewares/Third_Party/LwIP/src/core/netif.c ****     MIB2_STATS_NETIF_INC(stats_if, ifoutdiscards);
1119:Middlewares/Third_Party/LwIP/src/core/netif.c ****     return ERR_MEM;
1120:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
1121:Middlewares/Third_Party/LwIP/src/core/netif.c ****   netif->loop_cnt_current = (u16_t)(netif->loop_cnt_current + clen);
1122:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_LOOPBACK_MAX_PBUFS */
1123:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1124:Middlewares/Third_Party/LwIP/src/core/netif.c ****   /* Copy the whole pbuf queue p into the single pbuf r */
1125:Middlewares/Third_Party/LwIP/src/core/netif.c ****   if ((err = pbuf_copy(r, p)) != ERR_OK) {
1126:Middlewares/Third_Party/LwIP/src/core/netif.c ****     pbuf_free(r);
1127:Middlewares/Third_Party/LwIP/src/core/netif.c ****     LINK_STATS_INC(link.memerr);
1128:Middlewares/Third_Party/LwIP/src/core/netif.c ****     LINK_STATS_INC(link.drop);
1129:Middlewares/Third_Party/LwIP/src/core/netif.c ****     MIB2_STATS_NETIF_INC(stats_if, ifoutdiscards);
1130:Middlewares/Third_Party/LwIP/src/core/netif.c ****     return err;
1131:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
1132:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1133:Middlewares/Third_Party/LwIP/src/core/netif.c ****   /* Put the packet on a linked list which gets emptied through calling
1134:Middlewares/Third_Party/LwIP/src/core/netif.c ****      netif_poll(). */
1135:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1136:Middlewares/Third_Party/LwIP/src/core/netif.c ****   /* let last point to the last pbuf in chain r */
1137:Middlewares/Third_Party/LwIP/src/core/netif.c ****   for (last = r; last->next != NULL; last = last->next) {
1138:Middlewares/Third_Party/LwIP/src/core/netif.c ****     /* nothing to do here, just get to the last pbuf */
1139:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
1140:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1141:Middlewares/Third_Party/LwIP/src/core/netif.c ****   SYS_ARCH_PROTECT(lev);
1142:Middlewares/Third_Party/LwIP/src/core/netif.c ****   if (netif->loop_first != NULL) {
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 23


1143:Middlewares/Third_Party/LwIP/src/core/netif.c ****     LWIP_ASSERT("if first != NULL, last must also be != NULL", netif->loop_last != NULL);
1144:Middlewares/Third_Party/LwIP/src/core/netif.c ****     netif->loop_last->next = r;
1145:Middlewares/Third_Party/LwIP/src/core/netif.c ****     netif->loop_last = last;
1146:Middlewares/Third_Party/LwIP/src/core/netif.c ****   } else {
1147:Middlewares/Third_Party/LwIP/src/core/netif.c ****     netif->loop_first = r;
1148:Middlewares/Third_Party/LwIP/src/core/netif.c ****     netif->loop_last = last;
1149:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_NETIF_LOOPBACK_MULTITHREADING
1150:Middlewares/Third_Party/LwIP/src/core/netif.c ****     /* No existing packets queued, schedule poll */
1151:Middlewares/Third_Party/LwIP/src/core/netif.c ****     schedule_poll = 1;
1152:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_NETIF_LOOPBACK_MULTITHREADING */
1153:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
1154:Middlewares/Third_Party/LwIP/src/core/netif.c ****   SYS_ARCH_UNPROTECT(lev);
1155:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1156:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LINK_STATS_INC(link.xmit);
1157:Middlewares/Third_Party/LwIP/src/core/netif.c ****   MIB2_STATS_NETIF_ADD(stats_if, ifoutoctets, p->tot_len);
1158:Middlewares/Third_Party/LwIP/src/core/netif.c ****   MIB2_STATS_NETIF_INC(stats_if, ifoutucastpkts);
1159:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1160:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_NETIF_LOOPBACK_MULTITHREADING
1161:Middlewares/Third_Party/LwIP/src/core/netif.c ****   /* For multithreading environment, schedule a call to netif_poll */
1162:Middlewares/Third_Party/LwIP/src/core/netif.c ****   if (schedule_poll) {
1163:Middlewares/Third_Party/LwIP/src/core/netif.c ****     tcpip_try_callback((tcpip_callback_fn)netif_poll, netif);
1164:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
1165:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_NETIF_LOOPBACK_MULTITHREADING */
1166:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1167:Middlewares/Third_Party/LwIP/src/core/netif.c ****   return ERR_OK;
1168:Middlewares/Third_Party/LwIP/src/core/netif.c **** }
1169:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1170:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_HAVE_LOOPIF
1171:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_IPV4
1172:Middlewares/Third_Party/LwIP/src/core/netif.c **** static err_t
1173:Middlewares/Third_Party/LwIP/src/core/netif.c **** netif_loop_output_ipv4(struct netif *netif, struct pbuf *p, const ip4_addr_t *addr)
1174:Middlewares/Third_Party/LwIP/src/core/netif.c **** {
1175:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_UNUSED_ARG(addr);
1176:Middlewares/Third_Party/LwIP/src/core/netif.c ****   return netif_loop_output(netif, p);
1177:Middlewares/Third_Party/LwIP/src/core/netif.c **** }
1178:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_IPV4 */
1179:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1180:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_IPV6
1181:Middlewares/Third_Party/LwIP/src/core/netif.c **** static err_t
1182:Middlewares/Third_Party/LwIP/src/core/netif.c **** netif_loop_output_ipv6(struct netif *netif, struct pbuf *p, const ip6_addr_t *addr)
1183:Middlewares/Third_Party/LwIP/src/core/netif.c **** {
1184:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_UNUSED_ARG(addr);
1185:Middlewares/Third_Party/LwIP/src/core/netif.c ****   return netif_loop_output(netif, p);
1186:Middlewares/Third_Party/LwIP/src/core/netif.c **** }
1187:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_IPV6 */
1188:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_HAVE_LOOPIF */
1189:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1190:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1191:Middlewares/Third_Party/LwIP/src/core/netif.c **** /**
1192:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * Call netif_poll() in the main loop of your application. This is to prevent
1193:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * reentering non-reentrant functions like tcp_input(). Packets passed to
1194:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * netif_loop_output() are put on a list that is passed to netif->input() by
1195:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * netif_poll().
1196:Middlewares/Third_Party/LwIP/src/core/netif.c ****  */
1197:Middlewares/Third_Party/LwIP/src/core/netif.c **** void
1198:Middlewares/Third_Party/LwIP/src/core/netif.c **** netif_poll(struct netif *netif)
1199:Middlewares/Third_Party/LwIP/src/core/netif.c **** {
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 24


1200:Middlewares/Third_Party/LwIP/src/core/netif.c ****   /* If we have a loopif, SNMP counters are adjusted for it,
1201:Middlewares/Third_Party/LwIP/src/core/netif.c ****    * if not they are adjusted for 'netif'. */
1202:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if MIB2_STATS
1203:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_HAVE_LOOPIF
1204:Middlewares/Third_Party/LwIP/src/core/netif.c ****   struct netif *stats_if = &loop_netif;
1205:Middlewares/Third_Party/LwIP/src/core/netif.c **** #else /* LWIP_HAVE_LOOPIF */
1206:Middlewares/Third_Party/LwIP/src/core/netif.c ****   struct netif *stats_if = netif;
1207:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_HAVE_LOOPIF */
1208:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* MIB2_STATS */
1209:Middlewares/Third_Party/LwIP/src/core/netif.c ****   SYS_ARCH_DECL_PROTECT(lev);
1210:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1211:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT("netif_poll: invalid netif", netif != NULL);
1212:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1213:Middlewares/Third_Party/LwIP/src/core/netif.c ****   /* Get a packet from the list. With SYS_LIGHTWEIGHT_PROT=1, this is protected */
1214:Middlewares/Third_Party/LwIP/src/core/netif.c ****   SYS_ARCH_PROTECT(lev);
1215:Middlewares/Third_Party/LwIP/src/core/netif.c ****   while (netif->loop_first != NULL) {
1216:Middlewares/Third_Party/LwIP/src/core/netif.c ****     struct pbuf *in, *in_end;
1217:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_LOOPBACK_MAX_PBUFS
1218:Middlewares/Third_Party/LwIP/src/core/netif.c ****     u8_t clen = 1;
1219:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_LOOPBACK_MAX_PBUFS */
1220:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1221:Middlewares/Third_Party/LwIP/src/core/netif.c ****     in = in_end = netif->loop_first;
1222:Middlewares/Third_Party/LwIP/src/core/netif.c ****     while (in_end->len != in_end->tot_len) {
1223:Middlewares/Third_Party/LwIP/src/core/netif.c ****       LWIP_ASSERT("bogus pbuf: len != tot_len but next == NULL!", in_end->next != NULL);
1224:Middlewares/Third_Party/LwIP/src/core/netif.c ****       in_end = in_end->next;
1225:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_LOOPBACK_MAX_PBUFS
1226:Middlewares/Third_Party/LwIP/src/core/netif.c ****       clen++;
1227:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_LOOPBACK_MAX_PBUFS */
1228:Middlewares/Third_Party/LwIP/src/core/netif.c ****     }
1229:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_LOOPBACK_MAX_PBUFS
1230:Middlewares/Third_Party/LwIP/src/core/netif.c ****     /* adjust the number of pbufs on queue */
1231:Middlewares/Third_Party/LwIP/src/core/netif.c ****     LWIP_ASSERT("netif->loop_cnt_current underflow",
1232:Middlewares/Third_Party/LwIP/src/core/netif.c ****                 ((netif->loop_cnt_current - clen) < netif->loop_cnt_current));
1233:Middlewares/Third_Party/LwIP/src/core/netif.c ****     netif->loop_cnt_current = (u16_t)(netif->loop_cnt_current - clen);
1234:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_LOOPBACK_MAX_PBUFS */
1235:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1236:Middlewares/Third_Party/LwIP/src/core/netif.c ****     /* 'in_end' now points to the last pbuf from 'in' */
1237:Middlewares/Third_Party/LwIP/src/core/netif.c ****     if (in_end == netif->loop_last) {
1238:Middlewares/Third_Party/LwIP/src/core/netif.c ****       /* this was the last pbuf in the list */
1239:Middlewares/Third_Party/LwIP/src/core/netif.c ****       netif->loop_first = netif->loop_last = NULL;
1240:Middlewares/Third_Party/LwIP/src/core/netif.c ****     } else {
1241:Middlewares/Third_Party/LwIP/src/core/netif.c ****       /* pop the pbuf off the list */
1242:Middlewares/Third_Party/LwIP/src/core/netif.c ****       netif->loop_first = in_end->next;
1243:Middlewares/Third_Party/LwIP/src/core/netif.c ****       LWIP_ASSERT("should not be null since first != last!", netif->loop_first != NULL);
1244:Middlewares/Third_Party/LwIP/src/core/netif.c ****     }
1245:Middlewares/Third_Party/LwIP/src/core/netif.c ****     /* De-queue the pbuf from its successors on the 'loop_' list. */
1246:Middlewares/Third_Party/LwIP/src/core/netif.c ****     in_end->next = NULL;
1247:Middlewares/Third_Party/LwIP/src/core/netif.c ****     SYS_ARCH_UNPROTECT(lev);
1248:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1249:Middlewares/Third_Party/LwIP/src/core/netif.c ****     in->if_idx = netif_get_index(netif);
1250:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1251:Middlewares/Third_Party/LwIP/src/core/netif.c ****     LINK_STATS_INC(link.recv);
1252:Middlewares/Third_Party/LwIP/src/core/netif.c ****     MIB2_STATS_NETIF_ADD(stats_if, ifinoctets, in->tot_len);
1253:Middlewares/Third_Party/LwIP/src/core/netif.c ****     MIB2_STATS_NETIF_INC(stats_if, ifinucastpkts);
1254:Middlewares/Third_Party/LwIP/src/core/netif.c ****     /* loopback packets are always IP packets! */
1255:Middlewares/Third_Party/LwIP/src/core/netif.c ****     if (ip_input(in, netif) != ERR_OK) {
1256:Middlewares/Third_Party/LwIP/src/core/netif.c ****       pbuf_free(in);
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 25


1257:Middlewares/Third_Party/LwIP/src/core/netif.c ****     }
1258:Middlewares/Third_Party/LwIP/src/core/netif.c ****     SYS_ARCH_PROTECT(lev);
1259:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
1260:Middlewares/Third_Party/LwIP/src/core/netif.c ****   SYS_ARCH_UNPROTECT(lev);
1261:Middlewares/Third_Party/LwIP/src/core/netif.c **** }
1262:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1263:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if !LWIP_NETIF_LOOPBACK_MULTITHREADING
1264:Middlewares/Third_Party/LwIP/src/core/netif.c **** /**
1265:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * Calls netif_poll() for every netif on the netif_list.
1266:Middlewares/Third_Party/LwIP/src/core/netif.c ****  */
1267:Middlewares/Third_Party/LwIP/src/core/netif.c **** void
1268:Middlewares/Third_Party/LwIP/src/core/netif.c **** netif_poll_all(void)
1269:Middlewares/Third_Party/LwIP/src/core/netif.c **** {
1270:Middlewares/Third_Party/LwIP/src/core/netif.c ****   struct netif *netif;
1271:Middlewares/Third_Party/LwIP/src/core/netif.c ****   /* loop through netifs */
1272:Middlewares/Third_Party/LwIP/src/core/netif.c ****   NETIF_FOREACH(netif) {
1273:Middlewares/Third_Party/LwIP/src/core/netif.c ****     netif_poll(netif);
1274:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
1275:Middlewares/Third_Party/LwIP/src/core/netif.c **** }
1276:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* !LWIP_NETIF_LOOPBACK_MULTITHREADING */
1277:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* ENABLE_LOOPBACK */
1278:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1279:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_NUM_NETIF_CLIENT_DATA > 0
1280:Middlewares/Third_Party/LwIP/src/core/netif.c **** /**
1281:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @ingroup netif_cd
1282:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * Allocate an index to store data in client_data member of struct netif.
1283:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * Returned value is an index in mentioned array.
1284:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @see LWIP_NUM_NETIF_CLIENT_DATA
1285:Middlewares/Third_Party/LwIP/src/core/netif.c ****  */
1286:Middlewares/Third_Party/LwIP/src/core/netif.c **** u8_t
1287:Middlewares/Third_Party/LwIP/src/core/netif.c **** netif_alloc_client_data_id(void)
1288:Middlewares/Third_Party/LwIP/src/core/netif.c **** {
1289:Middlewares/Third_Party/LwIP/src/core/netif.c ****   u8_t result = netif_client_id;
1290:Middlewares/Third_Party/LwIP/src/core/netif.c ****   netif_client_id++;
1291:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1292:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT_CORE_LOCKED();
1293:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1294:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_NUM_NETIF_CLIENT_DATA > 256
1295:Middlewares/Third_Party/LwIP/src/core/netif.c **** #error LWIP_NUM_NETIF_CLIENT_DATA must be <= 256
1296:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif
1297:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT("Increase LWIP_NUM_NETIF_CLIENT_DATA in lwipopts.h", result < LWIP_NUM_NETIF_CLIENT_D
1298:Middlewares/Third_Party/LwIP/src/core/netif.c ****   return (u8_t)(result + LWIP_NETIF_CLIENT_DATA_INDEX_MAX);
1299:Middlewares/Third_Party/LwIP/src/core/netif.c **** }
1300:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif
1301:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1302:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_IPV6
1303:Middlewares/Third_Party/LwIP/src/core/netif.c **** /**
1304:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @ingroup netif_ip6
1305:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * Change an IPv6 address of a network interface
1306:Middlewares/Third_Party/LwIP/src/core/netif.c ****  *
1307:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @param netif the network interface to change
1308:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @param addr_idx index of the IPv6 address
1309:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @param addr6 the new IPv6 address
1310:Middlewares/Third_Party/LwIP/src/core/netif.c ****  *
1311:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @note call netif_ip6_addr_set_state() to set the address valid/temptative
1312:Middlewares/Third_Party/LwIP/src/core/netif.c ****  */
1313:Middlewares/Third_Party/LwIP/src/core/netif.c **** void
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 26


1314:Middlewares/Third_Party/LwIP/src/core/netif.c **** netif_ip6_addr_set(struct netif *netif, s8_t addr_idx, const ip6_addr_t *addr6)
1315:Middlewares/Third_Party/LwIP/src/core/netif.c **** {
1316:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT_CORE_LOCKED();
1317:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1318:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT("netif_ip6_addr_set: invalid netif", netif != NULL);
1319:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT("netif_ip6_addr_set: invalid addr6", addr6 != NULL);
1320:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1321:Middlewares/Third_Party/LwIP/src/core/netif.c ****   netif_ip6_addr_set_parts(netif, addr_idx, addr6->addr[0], addr6->addr[1],
1322:Middlewares/Third_Party/LwIP/src/core/netif.c ****                            addr6->addr[2], addr6->addr[3]);
1323:Middlewares/Third_Party/LwIP/src/core/netif.c **** }
1324:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1325:Middlewares/Third_Party/LwIP/src/core/netif.c **** /*
1326:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * Change an IPv6 address of a network interface (internal version taking 4 * u32_t)
1327:Middlewares/Third_Party/LwIP/src/core/netif.c ****  *
1328:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @param netif the network interface to change
1329:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @param addr_idx index of the IPv6 address
1330:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @param i0 word0 of the new IPv6 address
1331:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @param i1 word1 of the new IPv6 address
1332:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @param i2 word2 of the new IPv6 address
1333:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @param i3 word3 of the new IPv6 address
1334:Middlewares/Third_Party/LwIP/src/core/netif.c ****  */
1335:Middlewares/Third_Party/LwIP/src/core/netif.c **** void
1336:Middlewares/Third_Party/LwIP/src/core/netif.c **** netif_ip6_addr_set_parts(struct netif *netif, s8_t addr_idx, u32_t i0, u32_t i1, u32_t i2, u32_t i3
1337:Middlewares/Third_Party/LwIP/src/core/netif.c **** {
1338:Middlewares/Third_Party/LwIP/src/core/netif.c ****   ip_addr_t old_addr;
1339:Middlewares/Third_Party/LwIP/src/core/netif.c ****   ip_addr_t new_ipaddr;
1340:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT_CORE_LOCKED();
1341:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT("netif != NULL", netif != NULL);
1342:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT("invalid index", addr_idx < LWIP_IPV6_NUM_ADDRESSES);
1343:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1344:Middlewares/Third_Party/LwIP/src/core/netif.c ****   ip6_addr_copy(*ip_2_ip6(&old_addr), *netif_ip6_addr(netif, addr_idx));
1345:Middlewares/Third_Party/LwIP/src/core/netif.c ****   IP_SET_TYPE_VAL(old_addr, IPADDR_TYPE_V6);
1346:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1347:Middlewares/Third_Party/LwIP/src/core/netif.c ****   /* address is actually being changed? */
1348:Middlewares/Third_Party/LwIP/src/core/netif.c ****   if ((ip_2_ip6(&old_addr)->addr[0] != i0) || (ip_2_ip6(&old_addr)->addr[1] != i1) ||
1349:Middlewares/Third_Party/LwIP/src/core/netif.c ****       (ip_2_ip6(&old_addr)->addr[2] != i2) || (ip_2_ip6(&old_addr)->addr[3] != i3)) {
1350:Middlewares/Third_Party/LwIP/src/core/netif.c ****     LWIP_DEBUGF(NETIF_DEBUG | LWIP_DBG_STATE, ("netif_ip6_addr_set: netif address being changed\n")
1351:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1352:Middlewares/Third_Party/LwIP/src/core/netif.c ****     IP_ADDR6(&new_ipaddr, i0, i1, i2, i3);
1353:Middlewares/Third_Party/LwIP/src/core/netif.c ****     ip6_addr_assign_zone(ip_2_ip6(&new_ipaddr), IP6_UNICAST, netif);
1354:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1355:Middlewares/Third_Party/LwIP/src/core/netif.c ****     if (ip6_addr_isvalid(netif_ip6_addr_state(netif, addr_idx))) {
1356:Middlewares/Third_Party/LwIP/src/core/netif.c ****       netif_do_ip_addr_changed(netif_ip_addr6(netif, addr_idx), &new_ipaddr);
1357:Middlewares/Third_Party/LwIP/src/core/netif.c ****     }
1358:Middlewares/Third_Party/LwIP/src/core/netif.c ****     /* @todo: remove/readd mib2 ip6 entries? */
1359:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1360:Middlewares/Third_Party/LwIP/src/core/netif.c ****     ip_addr_copy(netif->ip6_addr[addr_idx], new_ipaddr);
1361:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1362:Middlewares/Third_Party/LwIP/src/core/netif.c ****     if (ip6_addr_isvalid(netif_ip6_addr_state(netif, addr_idx))) {
1363:Middlewares/Third_Party/LwIP/src/core/netif.c ****       netif_issue_reports(netif, NETIF_REPORT_TYPE_IPV6);
1364:Middlewares/Third_Party/LwIP/src/core/netif.c ****       NETIF_STATUS_CALLBACK(netif);
1365:Middlewares/Third_Party/LwIP/src/core/netif.c ****     }
1366:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1367:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_NETIF_EXT_STATUS_CALLBACK
1368:Middlewares/Third_Party/LwIP/src/core/netif.c ****     {
1369:Middlewares/Third_Party/LwIP/src/core/netif.c ****       netif_ext_callback_args_t args;
1370:Middlewares/Third_Party/LwIP/src/core/netif.c ****       args.ipv6_set.addr_index  = addr_idx;
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 27


1371:Middlewares/Third_Party/LwIP/src/core/netif.c ****       args.ipv6_set.old_address = &old_addr;
1372:Middlewares/Third_Party/LwIP/src/core/netif.c ****       netif_invoke_ext_callback(netif, LWIP_NSC_IPV6_SET, &args);
1373:Middlewares/Third_Party/LwIP/src/core/netif.c ****     }
1374:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif
1375:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
1376:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1377:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_DEBUGF(NETIF_DEBUG | LWIP_DBG_TRACE | LWIP_DBG_STATE, ("netif: IPv6 address %d of interface 
1378:Middlewares/Third_Party/LwIP/src/core/netif.c ****               addr_idx, netif->name[0], netif->name[1], ip6addr_ntoa(netif_ip6_addr(netif, addr_idx
1379:Middlewares/Third_Party/LwIP/src/core/netif.c ****               netif_ip6_addr_state(netif, addr_idx)));
1380:Middlewares/Third_Party/LwIP/src/core/netif.c **** }
1381:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1382:Middlewares/Third_Party/LwIP/src/core/netif.c **** /**
1383:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @ingroup netif_ip6
1384:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * Change the state of an IPv6 address of a network interface
1385:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * (INVALID, TEMPTATIVE, PREFERRED, DEPRECATED, where TEMPTATIVE
1386:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * includes the number of checks done, see ip6_addr.h)
1387:Middlewares/Third_Party/LwIP/src/core/netif.c ****  *
1388:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @param netif the network interface to change
1389:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @param addr_idx index of the IPv6 address
1390:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @param state the new IPv6 address state
1391:Middlewares/Third_Party/LwIP/src/core/netif.c ****  */
1392:Middlewares/Third_Party/LwIP/src/core/netif.c **** void
1393:Middlewares/Third_Party/LwIP/src/core/netif.c **** netif_ip6_addr_set_state(struct netif *netif, s8_t addr_idx, u8_t state)
1394:Middlewares/Third_Party/LwIP/src/core/netif.c **** {
1395:Middlewares/Third_Party/LwIP/src/core/netif.c ****   u8_t old_state;
1396:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT_CORE_LOCKED();
1397:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT("netif != NULL", netif != NULL);
1398:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT("invalid index", addr_idx < LWIP_IPV6_NUM_ADDRESSES);
1399:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1400:Middlewares/Third_Party/LwIP/src/core/netif.c ****   old_state = netif_ip6_addr_state(netif, addr_idx);
1401:Middlewares/Third_Party/LwIP/src/core/netif.c ****   /* state is actually being changed? */
1402:Middlewares/Third_Party/LwIP/src/core/netif.c ****   if (old_state != state) {
1403:Middlewares/Third_Party/LwIP/src/core/netif.c ****     u8_t old_valid = old_state & IP6_ADDR_VALID;
1404:Middlewares/Third_Party/LwIP/src/core/netif.c ****     u8_t new_valid = state & IP6_ADDR_VALID;
1405:Middlewares/Third_Party/LwIP/src/core/netif.c ****     LWIP_DEBUGF(NETIF_DEBUG | LWIP_DBG_STATE, ("netif_ip6_addr_set_state: netif address state being
1406:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1407:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_IPV6_MLD
1408:Middlewares/Third_Party/LwIP/src/core/netif.c ****     /* Reevaluate solicited-node multicast group membership. */
1409:Middlewares/Third_Party/LwIP/src/core/netif.c ****     if (netif->flags & NETIF_FLAG_MLD6) {
1410:Middlewares/Third_Party/LwIP/src/core/netif.c ****       nd6_adjust_mld_membership(netif, addr_idx, state);
1411:Middlewares/Third_Party/LwIP/src/core/netif.c ****     }
1412:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_IPV6_MLD */
1413:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1414:Middlewares/Third_Party/LwIP/src/core/netif.c ****     if (old_valid && !new_valid) {
1415:Middlewares/Third_Party/LwIP/src/core/netif.c ****       /* address about to be removed by setting invalid */
1416:Middlewares/Third_Party/LwIP/src/core/netif.c ****       netif_do_ip_addr_changed(netif_ip_addr6(netif, addr_idx), NULL);
1417:Middlewares/Third_Party/LwIP/src/core/netif.c ****       /* @todo: remove mib2 ip6 entries? */
1418:Middlewares/Third_Party/LwIP/src/core/netif.c ****     }
1419:Middlewares/Third_Party/LwIP/src/core/netif.c ****     netif->ip6_addr_state[addr_idx] = state;
1420:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1421:Middlewares/Third_Party/LwIP/src/core/netif.c ****     if (!old_valid && new_valid) {
1422:Middlewares/Third_Party/LwIP/src/core/netif.c ****       /* address added by setting valid */
1423:Middlewares/Third_Party/LwIP/src/core/netif.c ****       /* This is a good moment to check that the address is properly zoned. */
1424:Middlewares/Third_Party/LwIP/src/core/netif.c ****       IP6_ADDR_ZONECHECK_NETIF(netif_ip6_addr(netif, addr_idx), netif);
1425:Middlewares/Third_Party/LwIP/src/core/netif.c ****       /* @todo: add mib2 ip6 entries? */
1426:Middlewares/Third_Party/LwIP/src/core/netif.c ****       netif_issue_reports(netif, NETIF_REPORT_TYPE_IPV6);
1427:Middlewares/Third_Party/LwIP/src/core/netif.c ****     }
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 28


1428:Middlewares/Third_Party/LwIP/src/core/netif.c ****     if ((old_state & ~IP6_ADDR_TENTATIVE_COUNT_MASK) !=
1429:Middlewares/Third_Party/LwIP/src/core/netif.c ****         (state     & ~IP6_ADDR_TENTATIVE_COUNT_MASK)) {
1430:Middlewares/Third_Party/LwIP/src/core/netif.c ****       /* address state has changed -> call the callback function */
1431:Middlewares/Third_Party/LwIP/src/core/netif.c ****       NETIF_STATUS_CALLBACK(netif);
1432:Middlewares/Third_Party/LwIP/src/core/netif.c ****     }
1433:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1434:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_NETIF_EXT_STATUS_CALLBACK
1435:Middlewares/Third_Party/LwIP/src/core/netif.c ****     {
1436:Middlewares/Third_Party/LwIP/src/core/netif.c ****       netif_ext_callback_args_t args;
1437:Middlewares/Third_Party/LwIP/src/core/netif.c ****       args.ipv6_addr_state_changed.addr_index = addr_idx;
1438:Middlewares/Third_Party/LwIP/src/core/netif.c ****       args.ipv6_addr_state_changed.old_state  = old_state;
1439:Middlewares/Third_Party/LwIP/src/core/netif.c ****       args.ipv6_addr_state_changed.address    = netif_ip_addr6(netif, addr_idx);
1440:Middlewares/Third_Party/LwIP/src/core/netif.c ****       netif_invoke_ext_callback(netif, LWIP_NSC_IPV6_ADDR_STATE_CHANGED, &args);
1441:Middlewares/Third_Party/LwIP/src/core/netif.c ****     }
1442:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif
1443:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
1444:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_DEBUGF(NETIF_DEBUG | LWIP_DBG_TRACE | LWIP_DBG_STATE, ("netif: IPv6 address %d of interface 
1445:Middlewares/Third_Party/LwIP/src/core/netif.c ****               addr_idx, netif->name[0], netif->name[1], ip6addr_ntoa(netif_ip6_addr(netif, addr_idx
1446:Middlewares/Third_Party/LwIP/src/core/netif.c ****               netif_ip6_addr_state(netif, addr_idx)));
1447:Middlewares/Third_Party/LwIP/src/core/netif.c **** }
1448:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1449:Middlewares/Third_Party/LwIP/src/core/netif.c **** /**
1450:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * Checks if a specific local address is present on the netif and returns its
1451:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * index. Depending on its state, it may or may not be assigned to the
1452:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * interface (as per RFC terminology).
1453:Middlewares/Third_Party/LwIP/src/core/netif.c ****  *
1454:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * The given address may or may not be zoned (i.e., have a zone index other
1455:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * than IP6_NO_ZONE). If the address is zoned, it must have the correct zone
1456:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * for the given netif, or no match will be found.
1457:Middlewares/Third_Party/LwIP/src/core/netif.c ****  *
1458:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @param netif the netif to check
1459:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @param ip6addr the IPv6 address to find
1460:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @return >= 0: address found, this is its index
1461:Middlewares/Third_Party/LwIP/src/core/netif.c ****  *         -1: address not found on this netif
1462:Middlewares/Third_Party/LwIP/src/core/netif.c ****  */
1463:Middlewares/Third_Party/LwIP/src/core/netif.c **** s8_t
1464:Middlewares/Third_Party/LwIP/src/core/netif.c **** netif_get_ip6_addr_match(struct netif *netif, const ip6_addr_t *ip6addr)
1465:Middlewares/Third_Party/LwIP/src/core/netif.c **** {
1466:Middlewares/Third_Party/LwIP/src/core/netif.c ****   s8_t i;
1467:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1468:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT_CORE_LOCKED();
1469:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1470:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT("netif_get_ip6_addr_match: invalid netif", netif != NULL);
1471:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT("netif_get_ip6_addr_match: invalid ip6addr", ip6addr != NULL);
1472:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1473:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_IPV6_SCOPES
1474:Middlewares/Third_Party/LwIP/src/core/netif.c ****   if (ip6_addr_has_zone(ip6addr) && !ip6_addr_test_zone(ip6addr, netif)) {
1475:Middlewares/Third_Party/LwIP/src/core/netif.c ****     return -1; /* wrong zone, no match */
1476:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
1477:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_IPV6_SCOPES */
1478:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1479:Middlewares/Third_Party/LwIP/src/core/netif.c ****   for (i = 0; i < LWIP_IPV6_NUM_ADDRESSES; i++) {
1480:Middlewares/Third_Party/LwIP/src/core/netif.c ****     if (!ip6_addr_isinvalid(netif_ip6_addr_state(netif, i)) &&
1481:Middlewares/Third_Party/LwIP/src/core/netif.c ****         ip6_addr_cmp_zoneless(netif_ip6_addr(netif, i), ip6addr)) {
1482:Middlewares/Third_Party/LwIP/src/core/netif.c ****       return i;
1483:Middlewares/Third_Party/LwIP/src/core/netif.c ****     }
1484:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 29


1485:Middlewares/Third_Party/LwIP/src/core/netif.c ****   return -1;
1486:Middlewares/Third_Party/LwIP/src/core/netif.c **** }
1487:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1488:Middlewares/Third_Party/LwIP/src/core/netif.c **** /**
1489:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @ingroup netif_ip6
1490:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * Create a link-local IPv6 address on a netif (stored in slot 0)
1491:Middlewares/Third_Party/LwIP/src/core/netif.c ****  *
1492:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @param netif the netif to create the address on
1493:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @param from_mac_48bit if != 0, assume hwadr is a 48-bit MAC address (std conversion)
1494:Middlewares/Third_Party/LwIP/src/core/netif.c ****  *                       if == 0, use hwaddr directly as interface ID
1495:Middlewares/Third_Party/LwIP/src/core/netif.c ****  */
1496:Middlewares/Third_Party/LwIP/src/core/netif.c **** void
1497:Middlewares/Third_Party/LwIP/src/core/netif.c **** netif_create_ip6_linklocal_address(struct netif *netif, u8_t from_mac_48bit)
1498:Middlewares/Third_Party/LwIP/src/core/netif.c **** {
1499:Middlewares/Third_Party/LwIP/src/core/netif.c ****   u8_t i, addr_index;
1500:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1501:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT_CORE_LOCKED();
1502:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1503:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT("netif_create_ip6_linklocal_address: invalid netif", netif != NULL);
1504:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1505:Middlewares/Third_Party/LwIP/src/core/netif.c ****   /* Link-local prefix. */
1506:Middlewares/Third_Party/LwIP/src/core/netif.c ****   ip_2_ip6(&netif->ip6_addr[0])->addr[0] = PP_HTONL(0xfe800000ul);
1507:Middlewares/Third_Party/LwIP/src/core/netif.c ****   ip_2_ip6(&netif->ip6_addr[0])->addr[1] = 0;
1508:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1509:Middlewares/Third_Party/LwIP/src/core/netif.c ****   /* Generate interface ID. */
1510:Middlewares/Third_Party/LwIP/src/core/netif.c ****   if (from_mac_48bit) {
1511:Middlewares/Third_Party/LwIP/src/core/netif.c ****     /* Assume hwaddr is a 48-bit IEEE 802 MAC. Convert to EUI-64 address. Complement Group bit. */
1512:Middlewares/Third_Party/LwIP/src/core/netif.c ****     ip_2_ip6(&netif->ip6_addr[0])->addr[2] = lwip_htonl((((u32_t)(netif->hwaddr[0] ^ 0x02)) << 24) 
1513:Middlewares/Third_Party/LwIP/src/core/netif.c ****         ((u32_t)(netif->hwaddr[1]) << 16) |
1514:Middlewares/Third_Party/LwIP/src/core/netif.c ****         ((u32_t)(netif->hwaddr[2]) << 8) |
1515:Middlewares/Third_Party/LwIP/src/core/netif.c ****         (0xff));
1516:Middlewares/Third_Party/LwIP/src/core/netif.c ****     ip_2_ip6(&netif->ip6_addr[0])->addr[3] = lwip_htonl((u32_t)(0xfeul << 24) |
1517:Middlewares/Third_Party/LwIP/src/core/netif.c ****         ((u32_t)(netif->hwaddr[3]) << 16) |
1518:Middlewares/Third_Party/LwIP/src/core/netif.c ****         ((u32_t)(netif->hwaddr[4]) << 8) |
1519:Middlewares/Third_Party/LwIP/src/core/netif.c ****         (netif->hwaddr[5]));
1520:Middlewares/Third_Party/LwIP/src/core/netif.c ****   } else {
1521:Middlewares/Third_Party/LwIP/src/core/netif.c ****     /* Use hwaddr directly as interface ID. */
1522:Middlewares/Third_Party/LwIP/src/core/netif.c ****     ip_2_ip6(&netif->ip6_addr[0])->addr[2] = 0;
1523:Middlewares/Third_Party/LwIP/src/core/netif.c ****     ip_2_ip6(&netif->ip6_addr[0])->addr[3] = 0;
1524:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1525:Middlewares/Third_Party/LwIP/src/core/netif.c ****     addr_index = 3;
1526:Middlewares/Third_Party/LwIP/src/core/netif.c ****     for (i = 0; (i < 8) && (i < netif->hwaddr_len); i++) {
1527:Middlewares/Third_Party/LwIP/src/core/netif.c ****       if (i == 4) {
1528:Middlewares/Third_Party/LwIP/src/core/netif.c ****         addr_index--;
1529:Middlewares/Third_Party/LwIP/src/core/netif.c ****       }
1530:Middlewares/Third_Party/LwIP/src/core/netif.c ****       ip_2_ip6(&netif->ip6_addr[0])->addr[addr_index] |= lwip_htonl(((u32_t)(netif->hwaddr[netif->h
1531:Middlewares/Third_Party/LwIP/src/core/netif.c ****     }
1532:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
1533:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1534:Middlewares/Third_Party/LwIP/src/core/netif.c ****   /* Set a link-local zone. Even though the zone is implied by the owning
1535:Middlewares/Third_Party/LwIP/src/core/netif.c ****    * netif, setting the zone anyway has two important conceptual advantages:
1536:Middlewares/Third_Party/LwIP/src/core/netif.c ****    * 1) it avoids the need for a ton of exceptions in internal code, allowing
1537:Middlewares/Third_Party/LwIP/src/core/netif.c ****    *    e.g. ip6_addr_cmp() to be used on local addresses;
1538:Middlewares/Third_Party/LwIP/src/core/netif.c ****    * 2) the properly zoned address is visible externally, e.g. when any outside
1539:Middlewares/Third_Party/LwIP/src/core/netif.c ****    *    code enumerates available addresses or uses one to bind a socket.
1540:Middlewares/Third_Party/LwIP/src/core/netif.c ****    * Any external code unaware of address scoping is likely to just ignore the
1541:Middlewares/Third_Party/LwIP/src/core/netif.c ****    * zone field, so this should not create any compatibility problems. */
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 30


1542:Middlewares/Third_Party/LwIP/src/core/netif.c ****   ip6_addr_assign_zone(ip_2_ip6(&netif->ip6_addr[0]), IP6_UNICAST, netif);
1543:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1544:Middlewares/Third_Party/LwIP/src/core/netif.c ****   /* Set address state. */
1545:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_IPV6_DUP_DETECT_ATTEMPTS
1546:Middlewares/Third_Party/LwIP/src/core/netif.c ****   /* Will perform duplicate address detection (DAD). */
1547:Middlewares/Third_Party/LwIP/src/core/netif.c ****   netif_ip6_addr_set_state(netif, 0, IP6_ADDR_TENTATIVE);
1548:Middlewares/Third_Party/LwIP/src/core/netif.c **** #else
1549:Middlewares/Third_Party/LwIP/src/core/netif.c ****   /* Consider address valid. */
1550:Middlewares/Third_Party/LwIP/src/core/netif.c ****   netif_ip6_addr_set_state(netif, 0, IP6_ADDR_PREFERRED);
1551:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_IPV6_AUTOCONFIG */
1552:Middlewares/Third_Party/LwIP/src/core/netif.c **** }
1553:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1554:Middlewares/Third_Party/LwIP/src/core/netif.c **** /**
1555:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @ingroup netif_ip6
1556:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * This function allows for the easy addition of a new IPv6 address to an interface.
1557:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * It takes care of finding an empty slot and then sets the address tentative
1558:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * (to make sure that all the subsequent processing happens).
1559:Middlewares/Third_Party/LwIP/src/core/netif.c ****  *
1560:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @param netif netif to add the address on
1561:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @param ip6addr address to add
1562:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @param chosen_idx if != NULL, the chosen IPv6 address index will be stored here
1563:Middlewares/Third_Party/LwIP/src/core/netif.c ****  */
1564:Middlewares/Third_Party/LwIP/src/core/netif.c **** err_t
1565:Middlewares/Third_Party/LwIP/src/core/netif.c **** netif_add_ip6_address(struct netif *netif, const ip6_addr_t *ip6addr, s8_t *chosen_idx)
1566:Middlewares/Third_Party/LwIP/src/core/netif.c **** {
1567:Middlewares/Third_Party/LwIP/src/core/netif.c ****   s8_t i;
1568:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1569:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT_CORE_LOCKED();
1570:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1571:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT("netif_add_ip6_address: invalid netif", netif != NULL);
1572:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT("netif_add_ip6_address: invalid ip6addr", ip6addr != NULL);
1573:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1574:Middlewares/Third_Party/LwIP/src/core/netif.c ****   i = netif_get_ip6_addr_match(netif, ip6addr);
1575:Middlewares/Third_Party/LwIP/src/core/netif.c ****   if (i >= 0) {
1576:Middlewares/Third_Party/LwIP/src/core/netif.c ****     /* Address already added */
1577:Middlewares/Third_Party/LwIP/src/core/netif.c ****     if (chosen_idx != NULL) {
1578:Middlewares/Third_Party/LwIP/src/core/netif.c ****       *chosen_idx = i;
1579:Middlewares/Third_Party/LwIP/src/core/netif.c ****     }
1580:Middlewares/Third_Party/LwIP/src/core/netif.c ****     return ERR_OK;
1581:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
1582:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1583:Middlewares/Third_Party/LwIP/src/core/netif.c ****   /* Find a free slot. The first one is reserved for link-local addresses. */
1584:Middlewares/Third_Party/LwIP/src/core/netif.c ****   for (i = ip6_addr_islinklocal(ip6addr) ? 0 : 1; i < LWIP_IPV6_NUM_ADDRESSES; i++) {
1585:Middlewares/Third_Party/LwIP/src/core/netif.c ****     if (ip6_addr_isinvalid(netif_ip6_addr_state(netif, i))) {
1586:Middlewares/Third_Party/LwIP/src/core/netif.c ****       ip_addr_copy_from_ip6(netif->ip6_addr[i], *ip6addr);
1587:Middlewares/Third_Party/LwIP/src/core/netif.c ****       ip6_addr_assign_zone(ip_2_ip6(&netif->ip6_addr[i]), IP6_UNICAST, netif);
1588:Middlewares/Third_Party/LwIP/src/core/netif.c ****       netif_ip6_addr_set_state(netif, i, IP6_ADDR_TENTATIVE);
1589:Middlewares/Third_Party/LwIP/src/core/netif.c ****       if (chosen_idx != NULL) {
1590:Middlewares/Third_Party/LwIP/src/core/netif.c ****         *chosen_idx = i;
1591:Middlewares/Third_Party/LwIP/src/core/netif.c ****       }
1592:Middlewares/Third_Party/LwIP/src/core/netif.c ****       return ERR_OK;
1593:Middlewares/Third_Party/LwIP/src/core/netif.c ****     }
1594:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
1595:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1596:Middlewares/Third_Party/LwIP/src/core/netif.c ****   if (chosen_idx != NULL) {
1597:Middlewares/Third_Party/LwIP/src/core/netif.c ****     *chosen_idx = -1;
1598:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 31


1599:Middlewares/Third_Party/LwIP/src/core/netif.c ****   return ERR_VAL;
1600:Middlewares/Third_Party/LwIP/src/core/netif.c **** }
1601:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1602:Middlewares/Third_Party/LwIP/src/core/netif.c **** /** Dummy IPv6 output function for netifs not supporting IPv6
1603:Middlewares/Third_Party/LwIP/src/core/netif.c ****  */
1604:Middlewares/Third_Party/LwIP/src/core/netif.c **** static err_t
1605:Middlewares/Third_Party/LwIP/src/core/netif.c **** netif_null_output_ip6(struct netif *netif, struct pbuf *p, const ip6_addr_t *ipaddr)
1606:Middlewares/Third_Party/LwIP/src/core/netif.c **** {
1607:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_UNUSED_ARG(netif);
1608:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_UNUSED_ARG(p);
1609:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_UNUSED_ARG(ipaddr);
1610:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1611:Middlewares/Third_Party/LwIP/src/core/netif.c ****   return ERR_IF;
1612:Middlewares/Third_Party/LwIP/src/core/netif.c **** }
1613:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_IPV6 */
1614:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1615:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_IPV4
1616:Middlewares/Third_Party/LwIP/src/core/netif.c **** /** Dummy IPv4 output function for netifs not supporting IPv4
1617:Middlewares/Third_Party/LwIP/src/core/netif.c ****  */
1618:Middlewares/Third_Party/LwIP/src/core/netif.c **** static err_t
1619:Middlewares/Third_Party/LwIP/src/core/netif.c **** netif_null_output_ip4(struct netif *netif, struct pbuf *p, const ip4_addr_t *ipaddr)
1620:Middlewares/Third_Party/LwIP/src/core/netif.c **** {
 118              		.loc 1 1620 1 is_stmt 1 view -0
 119              		.cfi_startproc
 120              		@ args = 0, pretend = 0, frame = 0
 121              		@ frame_needed = 0, uses_anonymous_args = 0
 122              		@ link register save eliminated.
1621:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_UNUSED_ARG(netif);
 123              		.loc 1 1621 3 view .LVU31
1622:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_UNUSED_ARG(p);
 124              		.loc 1 1622 3 view .LVU32
1623:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_UNUSED_ARG(ipaddr);
 125              		.loc 1 1623 3 view .LVU33
1624:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1625:Middlewares/Third_Party/LwIP/src/core/netif.c ****   return ERR_IF;
 126              		.loc 1 1625 3 view .LVU34
1626:Middlewares/Third_Party/LwIP/src/core/netif.c **** }
 127              		.loc 1 1626 1 is_stmt 0 view .LVU35
 128 0000 6FF00B00 		mvn	r0, #11
 129              	.LVL11:
 130              		.loc 1 1626 1 view .LVU36
 131 0004 7047     		bx	lr
 132              		.cfi_endproc
 133              	.LFE194:
 135              		.section	.text.netif_do_ip_addr_changed,"ax",%progbits
 136              		.align	1
 137              		.syntax unified
 138              		.thumb
 139              		.thumb_func
 141              	netif_do_ip_addr_changed:
 142              	.LVL12:
 143              	.LFB178:
 443:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_TCP
 144              		.loc 1 443 1 is_stmt 1 view -0
 145              		.cfi_startproc
 146              		@ args = 0, pretend = 0, frame = 0
 147              		@ frame_needed = 0, uses_anonymous_args = 0
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 32


 443:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_TCP
 148              		.loc 1 443 1 is_stmt 0 view .LVU38
 149 0000 38B5     		push	{r3, r4, r5, lr}
 150              	.LCFI0:
 151              		.cfi_def_cfa_offset 16
 152              		.cfi_offset 3, -16
 153              		.cfi_offset 4, -12
 154              		.cfi_offset 5, -8
 155              		.cfi_offset 14, -4
 156 0002 0446     		mov	r4, r0
 157 0004 0D46     		mov	r5, r1
 445:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_TCP */
 158              		.loc 1 445 3 is_stmt 1 view .LVU39
 159 0006 FFF7FEFF 		bl	tcp_netif_ip_addr_changed
 160              	.LVL13:
 448:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_UDP */
 161              		.loc 1 448 3 view .LVU40
 162 000a 2946     		mov	r1, r5
 163 000c 2046     		mov	r0, r4
 164 000e FFF7FEFF 		bl	udp_netif_ip_addr_changed
 165              	.LVL14:
 453:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 166              		.loc 1 453 1 is_stmt 0 view .LVU41
 167 0012 38BD     		pop	{r3, r4, r5, pc}
 453:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 168              		.loc 1 453 1 view .LVU42
 169              		.cfi_endproc
 170              	.LFE178:
 172              		.section	.rodata.netif_issue_reports.str1.4,"aMS",%progbits,1
 173              		.align	2
 174              	.LC0:
 175 0000 4D696464 		.ascii	"Middlewares/Third_Party/LwIP/src/core/netif.c\000"
 175      6C657761 
 175      7265732F 
 175      54686972 
 175      645F5061 
 176 002e 0000     		.align	2
 177              	.LC1:
 178 0030 6E657469 		.ascii	"netif_issue_reports: invalid netif\000"
 178      665F6973 
 178      7375655F 
 178      7265706F 
 178      7274733A 
 179 0053 00       		.align	2
 180              	.LC2:
 181 0054 41737365 		.ascii	"Assertion \"%s\" failed at line %d in %s\012\000"
 181      7274696F 
 181      6E202225 
 181      73222066 
 181      61696C65 
 182              		.section	.text.netif_issue_reports,"ax",%progbits
 183              		.align	1
 184              		.syntax unified
 185              		.thumb
 186              		.thumb_func
 188              	netif_issue_reports:
 189              	.LVL15:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 33


 190              	.LFB189:
 876:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT("netif_issue_reports: invalid netif", netif != NULL);
 191              		.loc 1 876 1 is_stmt 1 view -0
 192              		.cfi_startproc
 193              		@ args = 0, pretend = 0, frame = 0
 194              		@ frame_needed = 0, uses_anonymous_args = 0
 876:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT("netif_issue_reports: invalid netif", netif != NULL);
 195              		.loc 1 876 1 is_stmt 0 view .LVU44
 196 0000 38B5     		push	{r3, r4, r5, lr}
 197              	.LCFI1:
 198              		.cfi_def_cfa_offset 16
 199              		.cfi_offset 3, -16
 200              		.cfi_offset 4, -12
 201              		.cfi_offset 5, -8
 202              		.cfi_offset 14, -4
 203 0002 0C46     		mov	r4, r1
 877:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 204              		.loc 1 877 3 is_stmt 1 view .LVU45
 877:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 205              		.loc 1 877 3 view .LVU46
 206 0004 0546     		mov	r5, r0
 207 0006 70B1     		cbz	r0, .L14
 208              	.LVL16:
 209              	.L11:
 877:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 210              		.loc 1 877 3 discriminator 3 view .LVU47
 877:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 211              		.loc 1 877 3 discriminator 3 view .LVU48
 880:Middlewares/Third_Party/LwIP/src/core/netif.c ****       !(netif->flags & NETIF_FLAG_UP)) {
 212              		.loc 1 880 3 view .LVU49
 880:Middlewares/Third_Party/LwIP/src/core/netif.c ****       !(netif->flags & NETIF_FLAG_UP)) {
 213              		.loc 1 880 14 is_stmt 0 view .LVU50
 214 0008 95F82D30 		ldrb	r3, [r5, #45]	@ zero_extendqisi2
 880:Middlewares/Third_Party/LwIP/src/core/netif.c ****       !(netif->flags & NETIF_FLAG_UP)) {
 215              		.loc 1 880 6 view .LVU51
 216 000c 03F00502 		and	r2, r3, #5
 217 0010 052A     		cmp	r2, #5
 218 0012 07D1     		bne	.L10
 886:Middlewares/Third_Party/LwIP/src/core/netif.c ****       !ip4_addr_isany_val(*netif_ip4_addr(netif))) {
 219              		.loc 1 886 3 is_stmt 1 view .LVU52
 886:Middlewares/Third_Party/LwIP/src/core/netif.c ****       !ip4_addr_isany_val(*netif_ip4_addr(netif))) {
 220              		.loc 1 886 6 is_stmt 0 view .LVU53
 221 0014 14F0010F 		tst	r4, #1
 222 0018 04D0     		beq	.L10
 887:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_ARP
 223              		.loc 1 887 8 view .LVU54
 224 001a 6A68     		ldr	r2, [r5, #4]
 886:Middlewares/Third_Party/LwIP/src/core/netif.c ****       !ip4_addr_isany_val(*netif_ip4_addr(netif))) {
 225              		.loc 1 886 46 discriminator 1 view .LVU55
 226 001c 12B1     		cbz	r2, .L10
 890:Middlewares/Third_Party/LwIP/src/core/netif.c ****       etharp_gratuitous(netif);
 227              		.loc 1 890 5 is_stmt 1 view .LVU56
 890:Middlewares/Third_Party/LwIP/src/core/netif.c ****       etharp_gratuitous(netif);
 228              		.loc 1 890 8 is_stmt 0 view .LVU57
 229 001e 13F0080F 		tst	r3, #8
 230 0022 08D1     		bne	.L15
 231              	.L10:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 34


 912:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 232              		.loc 1 912 1 view .LVU58
 233 0024 38BD     		pop	{r3, r4, r5, pc}
 234              	.LVL17:
 235              	.L14:
 877:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 236              		.loc 1 877 3 is_stmt 1 discriminator 1 view .LVU59
 877:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 237              		.loc 1 877 3 discriminator 1 view .LVU60
 238 0026 064B     		ldr	r3, .L16
 239 0028 40F26D32 		movw	r2, #877
 240 002c 0549     		ldr	r1, .L16+4
 241              	.LVL18:
 877:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 242              		.loc 1 877 3 is_stmt 0 discriminator 1 view .LVU61
 243 002e 0648     		ldr	r0, .L16+8
 244              	.LVL19:
 877:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 245              		.loc 1 877 3 discriminator 1 view .LVU62
 246 0030 FFF7FEFF 		bl	printf
 247              	.LVL20:
 248 0034 E8E7     		b	.L11
 249              	.L15:
 891:Middlewares/Third_Party/LwIP/src/core/netif.c ****     }
 250              		.loc 1 891 7 is_stmt 1 view .LVU63
 251 0036 291D     		adds	r1, r5, #4
 252 0038 2846     		mov	r0, r5
 253 003a FFF7FEFF 		bl	etharp_request
 254              	.LVL21:
 255 003e F1E7     		b	.L10
 256              	.L17:
 257              		.align	2
 258              	.L16:
 259 0040 00000000 		.word	.LC0
 260 0044 30000000 		.word	.LC1
 261 0048 54000000 		.word	.LC2
 262              		.cfi_endproc
 263              	.LFE189:
 265              		.section	.rodata.netif_do_set_ipaddr.str1.4,"aMS",%progbits,1
 266              		.align	2
 267              	.LC3:
 268 0000 696E7661 		.ascii	"invalid pointer\000"
 268      6C696420 
 268      706F696E 
 268      74657200 
 269              		.section	.text.netif_do_set_ipaddr,"ax",%progbits
 270              		.align	1
 271              		.syntax unified
 272              		.thumb
 273              		.thumb_func
 275              	netif_do_set_ipaddr:
 276              	.LVL22:
 277              	.LFB179:
 458:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT("invalid pointer", ipaddr != NULL);
 278              		.loc 1 458 1 view -0
 279              		.cfi_startproc
 280              		@ args = 0, pretend = 0, frame = 8
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 35


 281              		@ frame_needed = 0, uses_anonymous_args = 0
 458:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT("invalid pointer", ipaddr != NULL);
 282              		.loc 1 458 1 is_stmt 0 view .LVU65
 283 0000 70B5     		push	{r4, r5, r6, lr}
 284              	.LCFI2:
 285              		.cfi_def_cfa_offset 16
 286              		.cfi_offset 4, -16
 287              		.cfi_offset 5, -12
 288              		.cfi_offset 6, -8
 289              		.cfi_offset 14, -4
 290 0002 82B0     		sub	sp, sp, #8
 291              	.LCFI3:
 292              		.cfi_def_cfa_offset 24
 293 0004 0446     		mov	r4, r0
 294 0006 1546     		mov	r5, r2
 459:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT("invalid pointer", old_addr != NULL);
 295              		.loc 1 459 3 is_stmt 1 view .LVU66
 459:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT("invalid pointer", old_addr != NULL);
 296              		.loc 1 459 3 view .LVU67
 297 0008 0E46     		mov	r6, r1
 298 000a 39B1     		cbz	r1, .L26
 299              	.LVL23:
 300              	.L19:
 459:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT("invalid pointer", old_addr != NULL);
 301              		.loc 1 459 3 discriminator 3 view .LVU68
 459:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT("invalid pointer", old_addr != NULL);
 302              		.loc 1 459 3 discriminator 3 view .LVU69
 460:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 303              		.loc 1 460 3 view .LVU70
 460:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 304              		.loc 1 460 3 view .LVU71
 305 000c 75B1     		cbz	r5, .L27
 306              	.L20:
 460:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 307              		.loc 1 460 3 discriminator 3 view .LVU72
 460:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 308              		.loc 1 460 3 discriminator 3 view .LVU73
 463:Middlewares/Third_Party/LwIP/src/core/netif.c ****     ip_addr_t new_addr;
 309              		.loc 1 463 3 view .LVU74
 463:Middlewares/Third_Party/LwIP/src/core/netif.c ****     ip_addr_t new_addr;
 310              		.loc 1 463 7 is_stmt 0 view .LVU75
 311 000e 3268     		ldr	r2, [r6]
 312 0010 6368     		ldr	r3, [r4, #4]
 463:Middlewares/Third_Party/LwIP/src/core/netif.c ****     ip_addr_t new_addr;
 313              		.loc 1 463 6 view .LVU76
 314 0012 9A42     		cmp	r2, r3
 315 0014 12D1     		bne	.L28
 486:Middlewares/Third_Party/LwIP/src/core/netif.c **** }
 316              		.loc 1 486 10 view .LVU77
 317 0016 0020     		movs	r0, #0
 318              	.L18:
 487:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 319              		.loc 1 487 1 view .LVU78
 320 0018 02B0     		add	sp, sp, #8
 321              	.LCFI4:
 322              		.cfi_remember_state
 323              		.cfi_def_cfa_offset 16
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 36


 324              		@ sp needed
 325 001a 70BD     		pop	{r4, r5, r6, pc}
 326              	.LVL24:
 327              	.L26:
 328              	.LCFI5:
 329              		.cfi_restore_state
 459:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT("invalid pointer", old_addr != NULL);
 330              		.loc 1 459 3 is_stmt 1 discriminator 1 view .LVU79
 459:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT("invalid pointer", old_addr != NULL);
 331              		.loc 1 459 3 discriminator 1 view .LVU80
 332 001c 104B     		ldr	r3, .L29
 333 001e 40F2CB12 		movw	r2, #459
 334              	.LVL25:
 459:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT("invalid pointer", old_addr != NULL);
 335              		.loc 1 459 3 is_stmt 0 discriminator 1 view .LVU81
 336 0022 1049     		ldr	r1, .L29+4
 337              	.LVL26:
 459:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT("invalid pointer", old_addr != NULL);
 338              		.loc 1 459 3 discriminator 1 view .LVU82
 339 0024 1048     		ldr	r0, .L29+8
 340              	.LVL27:
 459:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT("invalid pointer", old_addr != NULL);
 341              		.loc 1 459 3 discriminator 1 view .LVU83
 342 0026 FFF7FEFF 		bl	printf
 343              	.LVL28:
 344 002a EFE7     		b	.L19
 345              	.L27:
 460:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 346              		.loc 1 460 3 is_stmt 1 discriminator 1 view .LVU84
 460:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 347              		.loc 1 460 3 discriminator 1 view .LVU85
 348 002c 0C4B     		ldr	r3, .L29
 349 002e 4FF4E672 		mov	r2, #460
 350 0032 0C49     		ldr	r1, .L29+4
 351 0034 0C48     		ldr	r0, .L29+8
 352 0036 FFF7FEFF 		bl	printf
 353              	.LVL29:
 354 003a E8E7     		b	.L20
 355              	.L28:
 356              	.LBB2:
 464:Middlewares/Third_Party/LwIP/src/core/netif.c ****     *ip_2_ip4(&new_addr) = *ipaddr;
 357              		.loc 1 464 5 view .LVU86
 465:Middlewares/Third_Party/LwIP/src/core/netif.c ****     IP_SET_TYPE_VAL(new_addr, IPADDR_TYPE_V4);
 358              		.loc 1 465 5 view .LVU87
 465:Middlewares/Third_Party/LwIP/src/core/netif.c ****     IP_SET_TYPE_VAL(new_addr, IPADDR_TYPE_V4);
 359              		.loc 1 465 26 is_stmt 0 view .LVU88
 360 003c 0192     		str	r2, [sp, #4]
 466:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 361              		.loc 1 466 46 is_stmt 1 view .LVU89
 468:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 362              		.loc 1 468 5 view .LVU90
 363 003e 2B60     		str	r3, [r5]
 470:Middlewares/Third_Party/LwIP/src/core/netif.c ****     netif_do_ip_addr_changed(old_addr, &new_addr);
 364              		.loc 1 470 99 view .LVU91
 471:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 365              		.loc 1 471 5 view .LVU92
 366 0040 01A9     		add	r1, sp, #4
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 37


 367 0042 2846     		mov	r0, r5
 368 0044 FFF7FEFF 		bl	netif_do_ip_addr_changed
 369              	.LVL30:
 473:Middlewares/Third_Party/LwIP/src/core/netif.c ****     mib2_remove_route_ip4(0, netif);
 370              		.loc 1 473 27 view .LVU93
 474:Middlewares/Third_Party/LwIP/src/core/netif.c ****     /* set new IP address to netif */
 371              		.loc 1 474 36 view .LVU94
 476:Middlewares/Third_Party/LwIP/src/core/netif.c ****     IP_SET_TYPE_VAL(netif->ip_addr, IPADDR_TYPE_V4);
 372              		.loc 1 476 5 view .LVU95
 373 0048 3EB1     		cbz	r6, .L24
 476:Middlewares/Third_Party/LwIP/src/core/netif.c ****     IP_SET_TYPE_VAL(netif->ip_addr, IPADDR_TYPE_V4);
 374              		.loc 1 476 5 is_stmt 0 discriminator 1 view .LVU96
 375 004a 3368     		ldr	r3, [r6]
 376              	.L22:
 476:Middlewares/Third_Party/LwIP/src/core/netif.c ****     IP_SET_TYPE_VAL(netif->ip_addr, IPADDR_TYPE_V4);
 377              		.loc 1 476 5 discriminator 4 view .LVU97
 378 004c 6360     		str	r3, [r4, #4]
 477:Middlewares/Third_Party/LwIP/src/core/netif.c ****     mib2_add_ip4(netif);
 379              		.loc 1 477 52 is_stmt 1 view .LVU98
 478:Middlewares/Third_Party/LwIP/src/core/netif.c ****     mib2_add_route_ip4(0, netif);
 380              		.loc 1 478 24 view .LVU99
 479:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 381              		.loc 1 479 33 view .LVU100
 481:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 382              		.loc 1 481 5 view .LVU101
 383 004e 0121     		movs	r1, #1
 384 0050 2046     		mov	r0, r4
 385 0052 FFF7FEFF 		bl	netif_issue_reports
 386              	.LVL31:
 483:Middlewares/Third_Party/LwIP/src/core/netif.c ****     return 1; /* address changed */
 387              		.loc 1 483 33 view .LVU102
 484:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
 388              		.loc 1 484 5 view .LVU103
 484:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
 389              		.loc 1 484 12 is_stmt 0 view .LVU104
 390 0056 0120     		movs	r0, #1
 391 0058 DEE7     		b	.L18
 392              	.L24:
 476:Middlewares/Third_Party/LwIP/src/core/netif.c ****     IP_SET_TYPE_VAL(netif->ip_addr, IPADDR_TYPE_V4);
 393              		.loc 1 476 5 discriminator 2 view .LVU105
 394 005a 0023     		movs	r3, #0
 395 005c F6E7     		b	.L22
 396              	.L30:
 397 005e 00BF     		.align	2
 398              	.L29:
 399 0060 00000000 		.word	.LC0
 400 0064 00000000 		.word	.LC3
 401 0068 54000000 		.word	.LC2
 402              	.LBE2:
 403              		.cfi_endproc
 404              	.LFE179:
 406              		.section	.text.netif_init,"ax",%progbits
 407              		.align	1
 408              		.global	netif_init
 409              		.syntax unified
 410              		.thumb
 411              		.thumb_func
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 38


 413              	netif_init:
 414              	.LFB174:
 178:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_HAVE_LOOPIF
 415              		.loc 1 178 1 is_stmt 1 view -0
 416              		.cfi_startproc
 417              		@ args = 0, pretend = 0, frame = 0
 418              		@ frame_needed = 0, uses_anonymous_args = 0
 419              		@ link register save eliminated.
 205:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 420              		.loc 1 205 1 view .LVU107
 421 0000 7047     		bx	lr
 422              		.cfi_endproc
 423              	.LFE174:
 425              		.section	.rodata.netif_input.str1.4,"aMS",%progbits,1
 426              		.align	2
 427              	.LC4:
 428 0000 6E657469 		.ascii	"netif_input: invalid pbuf\000"
 428      665F696E 
 428      7075743A 
 428      20696E76 
 428      616C6964 
 429 001a 0000     		.align	2
 430              	.LC5:
 431 001c 6E657469 		.ascii	"netif_input: invalid netif\000"
 431      665F696E 
 431      7075743A 
 431      20696E76 
 431      616C6964 
 432              		.section	.text.netif_input,"ax",%progbits
 433              		.align	1
 434              		.global	netif_input
 435              		.syntax unified
 436              		.thumb
 437              		.thumb_func
 439              	netif_input:
 440              	.LVL32:
 441              	.LFB175:
 218:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT_CORE_LOCKED();
 442              		.loc 1 218 1 view -0
 443              		.cfi_startproc
 444              		@ args = 0, pretend = 0, frame = 0
 445              		@ frame_needed = 0, uses_anonymous_args = 0
 218:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT_CORE_LOCKED();
 446              		.loc 1 218 1 is_stmt 0 view .LVU109
 447 0000 38B5     		push	{r3, r4, r5, lr}
 448              	.LCFI6:
 449              		.cfi_def_cfa_offset 16
 450              		.cfi_offset 3, -16
 451              		.cfi_offset 4, -12
 452              		.cfi_offset 5, -8
 453              		.cfi_offset 14, -4
 454 0002 0C46     		mov	r4, r1
 219:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 455              		.loc 1 219 28 is_stmt 1 view .LVU110
 221:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT("netif_input: invalid netif", inp != NULL);
 456              		.loc 1 221 3 view .LVU111
 221:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT("netif_input: invalid netif", inp != NULL);
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 39


 457              		.loc 1 221 3 view .LVU112
 458 0004 0546     		mov	r5, r0
 459 0006 50B1     		cbz	r0, .L38
 460              	.LVL33:
 461              	.L33:
 221:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT("netif_input: invalid netif", inp != NULL);
 462              		.loc 1 221 3 discriminator 3 view .LVU113
 221:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT("netif_input: invalid netif", inp != NULL);
 463              		.loc 1 221 3 discriminator 3 view .LVU114
 222:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 464              		.loc 1 222 3 view .LVU115
 222:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 465              		.loc 1 222 3 view .LVU116
 466 0008 84B1     		cbz	r4, .L39
 467              	.L34:
 222:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 468              		.loc 1 222 3 discriminator 3 view .LVU117
 222:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 469              		.loc 1 222 3 discriminator 3 view .LVU118
 225:Middlewares/Third_Party/LwIP/src/core/netif.c ****     return ethernet_input(p, inp);
 470              		.loc 1 225 3 view .LVU119
 225:Middlewares/Third_Party/LwIP/src/core/netif.c ****     return ethernet_input(p, inp);
 471              		.loc 1 225 10 is_stmt 0 view .LVU120
 472 000a 94F82D30 		ldrb	r3, [r4, #45]	@ zero_extendqisi2
 225:Middlewares/Third_Party/LwIP/src/core/netif.c ****     return ethernet_input(p, inp);
 473              		.loc 1 225 6 view .LVU121
 474 000e 13F0180F 		tst	r3, #24
 475 0012 12D0     		beq	.L35
 226:Middlewares/Third_Party/LwIP/src/core/netif.c ****   } else
 476              		.loc 1 226 5 is_stmt 1 view .LVU122
 226:Middlewares/Third_Party/LwIP/src/core/netif.c ****   } else
 477              		.loc 1 226 12 is_stmt 0 view .LVU123
 478 0014 2146     		mov	r1, r4
 479 0016 2846     		mov	r0, r5
 480 0018 FFF7FEFF 		bl	ethernet_input
 481              	.LVL34:
 482              	.L36:
 230:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 483              		.loc 1 230 1 view .LVU124
 484 001c 38BD     		pop	{r3, r4, r5, pc}
 485              	.LVL35:
 486              	.L38:
 221:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT("netif_input: invalid netif", inp != NULL);
 487              		.loc 1 221 3 is_stmt 1 discriminator 1 view .LVU125
 221:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT("netif_input: invalid netif", inp != NULL);
 488              		.loc 1 221 3 discriminator 1 view .LVU126
 489 001e 094B     		ldr	r3, .L40
 490 0020 DD22     		movs	r2, #221
 491 0022 0949     		ldr	r1, .L40+4
 492              	.LVL36:
 221:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT("netif_input: invalid netif", inp != NULL);
 493              		.loc 1 221 3 is_stmt 0 discriminator 1 view .LVU127
 494 0024 0948     		ldr	r0, .L40+8
 495              	.LVL37:
 221:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT("netif_input: invalid netif", inp != NULL);
 496              		.loc 1 221 3 discriminator 1 view .LVU128
 497 0026 FFF7FEFF 		bl	printf
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 40


 498              	.LVL38:
 499 002a EDE7     		b	.L33
 500              	.L39:
 222:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 501              		.loc 1 222 3 is_stmt 1 discriminator 1 view .LVU129
 222:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 502              		.loc 1 222 3 discriminator 1 view .LVU130
 503 002c 054B     		ldr	r3, .L40
 504 002e DE22     		movs	r2, #222
 505 0030 0749     		ldr	r1, .L40+12
 506 0032 0648     		ldr	r0, .L40+8
 507 0034 FFF7FEFF 		bl	printf
 508              	.LVL39:
 509 0038 E7E7     		b	.L34
 510              	.L35:
 229:Middlewares/Third_Party/LwIP/src/core/netif.c **** }
 511              		.loc 1 229 5 view .LVU131
 229:Middlewares/Third_Party/LwIP/src/core/netif.c **** }
 512              		.loc 1 229 12 is_stmt 0 view .LVU132
 513 003a 2146     		mov	r1, r4
 514 003c 2846     		mov	r0, r5
 515 003e FFF7FEFF 		bl	ip4_input
 516              	.LVL40:
 517 0042 EBE7     		b	.L36
 518              	.L41:
 519              		.align	2
 520              	.L40:
 521 0044 00000000 		.word	.LC0
 522 0048 00000000 		.word	.LC4
 523 004c 54000000 		.word	.LC2
 524 0050 1C000000 		.word	.LC5
 525              		.cfi_endproc
 526              	.LFE175:
 528              		.section	.rodata.netif_set_ipaddr.str1.4,"aMS",%progbits,1
 529              		.align	2
 530              	.LC6:
 531 0000 6E657469 		.ascii	"netif_set_ipaddr: invalid netif\000"
 531      665F7365 
 531      745F6970 
 531      61646472 
 531      3A20696E 
 532              		.section	.text.netif_set_ipaddr,"ax",%progbits
 533              		.align	1
 534              		.global	netif_set_ipaddr
 535              		.syntax unified
 536              		.thumb
 537              		.thumb_func
 539              	netif_set_ipaddr:
 540              	.LVL41:
 541              	.LFB180:
 501:Middlewares/Third_Party/LwIP/src/core/netif.c ****   ip_addr_t old_addr;
 542              		.loc 1 501 1 is_stmt 1 view -0
 543              		.cfi_startproc
 544              		@ args = 0, pretend = 0, frame = 8
 545              		@ frame_needed = 0, uses_anonymous_args = 0
 501:Middlewares/Third_Party/LwIP/src/core/netif.c ****   ip_addr_t old_addr;
 546              		.loc 1 501 1 is_stmt 0 view .LVU134
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 41


 547 0000 00B5     		push	{lr}
 548              	.LCFI7:
 549              		.cfi_def_cfa_offset 4
 550              		.cfi_offset 14, -4
 551 0002 83B0     		sub	sp, sp, #12
 552              	.LCFI8:
 553              		.cfi_def_cfa_offset 16
 502:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 554              		.loc 1 502 3 is_stmt 1 view .LVU135
 504:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 555              		.loc 1 504 3 view .LVU136
 504:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 556              		.loc 1 504 3 view .LVU137
 557 0004 40B1     		cbz	r0, .L48
 558 0006 0346     		mov	r3, r0
 504:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 559              		.loc 1 504 3 discriminator 2 view .LVU138
 507:Middlewares/Third_Party/LwIP/src/core/netif.c ****     ipaddr = IP4_ADDR_ANY4;
 560              		.loc 1 507 3 view .LVU139
 507:Middlewares/Third_Party/LwIP/src/core/netif.c ****     ipaddr = IP4_ADDR_ANY4;
 561              		.loc 1 507 6 is_stmt 0 view .LVU140
 562 0008 71B1     		cbz	r1, .L49
 563              	.LVL42:
 564              	.L45:
 511:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 565              		.loc 1 511 28 is_stmt 1 view .LVU141
 513:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_NETIF_EXT_STATUS_CALLBACK
 566              		.loc 1 513 3 view .LVU142
 513:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_NETIF_EXT_STATUS_CALLBACK
 567              		.loc 1 513 7 is_stmt 0 view .LVU143
 568 000a 01AA     		add	r2, sp, #4
 569 000c 1846     		mov	r0, r3
 570              	.LVL43:
 513:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_NETIF_EXT_STATUS_CALLBACK
 571              		.loc 1 513 7 view .LVU144
 572 000e FFF7FEFF 		bl	netif_do_set_ipaddr
 573              	.LVL44:
 519:Middlewares/Third_Party/LwIP/src/core/netif.c **** }
 574              		.loc 1 519 3 is_stmt 1 view .LVU145
 575              	.L42:
 520:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 576              		.loc 1 520 1 is_stmt 0 view .LVU146
 577 0012 03B0     		add	sp, sp, #12
 578              	.LCFI9:
 579              		.cfi_remember_state
 580              		.cfi_def_cfa_offset 4
 581              		@ sp needed
 582 0014 5DF804FB 		ldr	pc, [sp], #4
 583              	.LVL45:
 584              	.L48:
 585              	.LCFI10:
 586              		.cfi_restore_state
 504:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 587              		.loc 1 504 3 is_stmt 1 discriminator 1 view .LVU147
 504:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 588              		.loc 1 504 3 discriminator 1 view .LVU148
 589 0018 044B     		ldr	r3, .L50
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 42


 590 001a 4FF4FC72 		mov	r2, #504
 591 001e 0449     		ldr	r1, .L50+4
 592              	.LVL46:
 504:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 593              		.loc 1 504 3 is_stmt 0 discriminator 1 view .LVU149
 594 0020 0448     		ldr	r0, .L50+8
 595              	.LVL47:
 504:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 596              		.loc 1 504 3 discriminator 1 view .LVU150
 597 0022 FFF7FEFF 		bl	printf
 598              	.LVL48:
 504:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 599              		.loc 1 504 3 is_stmt 1 discriminator 1 view .LVU151
 504:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 600              		.loc 1 504 3 discriminator 1 view .LVU152
 601 0026 F4E7     		b	.L42
 602              	.LVL49:
 603              	.L49:
 508:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
 604              		.loc 1 508 12 is_stmt 0 view .LVU153
 605 0028 0349     		ldr	r1, .L50+12
 606              	.LVL50:
 508:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
 607              		.loc 1 508 12 view .LVU154
 608 002a EEE7     		b	.L45
 609              	.L51:
 610              		.align	2
 611              	.L50:
 612 002c 00000000 		.word	.LC0
 613 0030 00000000 		.word	.LC6
 614 0034 54000000 		.word	.LC2
 615 0038 00000000 		.word	ip_addr_any
 616              		.cfi_endproc
 617              	.LFE180:
 619              		.section	.rodata.netif_set_netmask.str1.4,"aMS",%progbits,1
 620              		.align	2
 621              	.LC7:
 622 0000 6E657469 		.ascii	"netif_set_netmask: invalid netif\000"
 622      665F7365 
 622      745F6E65 
 622      746D6173 
 622      6B3A2069 
 623              		.section	.text.netif_set_netmask,"ax",%progbits
 624              		.align	1
 625              		.global	netif_set_netmask
 626              		.syntax unified
 627              		.thumb
 628              		.thumb_func
 630              	netif_set_netmask:
 631              	.LVL51:
 632              	.LFB182:
 561:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_NETIF_EXT_STATUS_CALLBACK
 633              		.loc 1 561 1 is_stmt 1 view -0
 634              		.cfi_startproc
 635              		@ args = 0, pretend = 0, frame = 0
 636              		@ frame_needed = 0, uses_anonymous_args = 0
 561:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_NETIF_EXT_STATUS_CALLBACK
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 43


 637              		.loc 1 561 1 is_stmt 0 view .LVU156
 638 0000 08B5     		push	{r3, lr}
 639              	.LCFI11:
 640              		.cfi_def_cfa_offset 8
 641              		.cfi_offset 3, -8
 642              		.cfi_offset 14, -4
 566:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif
 643              		.loc 1 566 3 is_stmt 1 view .LVU157
 644              	.LVL52:
 568:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 645              		.loc 1 568 28 view .LVU158
 570:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 646              		.loc 1 570 3 view .LVU159
 570:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 647              		.loc 1 570 3 view .LVU160
 648 0002 30B1     		cbz	r0, .L58
 649 0004 0346     		mov	r3, r0
 570:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 650              		.loc 1 570 3 discriminator 2 view .LVU161
 573:Middlewares/Third_Party/LwIP/src/core/netif.c ****     netmask = IP4_ADDR_ANY4;
 651              		.loc 1 573 3 view .LVU162
 573:Middlewares/Third_Party/LwIP/src/core/netif.c ****     netmask = IP4_ADDR_ANY4;
 652              		.loc 1 573 6 is_stmt 0 view .LVU163
 653 0006 61B1     		cbz	r1, .L59
 654              	.LVL53:
 655              	.L55:
 577:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_NETIF_EXT_STATUS_CALLBACK
 656              		.loc 1 577 3 is_stmt 1 view .LVU164
 577:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_NETIF_EXT_STATUS_CALLBACK
 657              		.loc 1 577 7 is_stmt 0 view .LVU165
 658 0008 0022     		movs	r2, #0
 659 000a 1846     		mov	r0, r3
 660              	.LVL54:
 577:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_NETIF_EXT_STATUS_CALLBACK
 661              		.loc 1 577 7 view .LVU166
 662 000c FFF7FEFF 		bl	netif_do_set_netmask
 663              	.LVL55:
 583:Middlewares/Third_Party/LwIP/src/core/netif.c **** }
 664              		.loc 1 583 3 is_stmt 1 view .LVU167
 665              	.L52:
 584:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 666              		.loc 1 584 1 is_stmt 0 view .LVU168
 667 0010 08BD     		pop	{r3, pc}
 668              	.LVL56:
 669              	.L58:
 570:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 670              		.loc 1 570 3 is_stmt 1 discriminator 1 view .LVU169
 570:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 671              		.loc 1 570 3 discriminator 1 view .LVU170
 672 0012 054B     		ldr	r3, .L60
 673 0014 40F23A22 		movw	r2, #570
 674 0018 0449     		ldr	r1, .L60+4
 675              	.LVL57:
 570:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 676              		.loc 1 570 3 is_stmt 0 discriminator 1 view .LVU171
 677 001a 0548     		ldr	r0, .L60+8
 678              	.LVL58:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 44


 570:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 679              		.loc 1 570 3 discriminator 1 view .LVU172
 680 001c FFF7FEFF 		bl	printf
 681              	.LVL59:
 570:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 682              		.loc 1 570 3 is_stmt 1 discriminator 1 view .LVU173
 570:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 683              		.loc 1 570 3 discriminator 1 view .LVU174
 570:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 684              		.loc 1 570 3 is_stmt 0 view .LVU175
 685 0020 F6E7     		b	.L52
 686              	.LVL60:
 687              	.L59:
 574:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
 688              		.loc 1 574 13 view .LVU176
 689 0022 0449     		ldr	r1, .L60+12
 690              	.LVL61:
 574:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
 691              		.loc 1 574 13 view .LVU177
 692 0024 F0E7     		b	.L55
 693              	.L61:
 694 0026 00BF     		.align	2
 695              	.L60:
 696 0028 00000000 		.word	.LC0
 697 002c 00000000 		.word	.LC7
 698 0030 54000000 		.word	.LC2
 699 0034 00000000 		.word	ip_addr_any
 700              		.cfi_endproc
 701              	.LFE182:
 703              		.section	.rodata.netif_set_gw.str1.4,"aMS",%progbits,1
 704              		.align	2
 705              	.LC8:
 706 0000 6E657469 		.ascii	"netif_set_gw: invalid netif\000"
 706      665F7365 
 706      745F6777 
 706      3A20696E 
 706      76616C69 
 707              		.section	.text.netif_set_gw,"ax",%progbits
 708              		.align	1
 709              		.global	netif_set_gw
 710              		.syntax unified
 711              		.thumb
 712              		.thumb_func
 714              	netif_set_gw:
 715              	.LVL62:
 716              	.LFB184:
 622:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_NETIF_EXT_STATUS_CALLBACK
 717              		.loc 1 622 1 is_stmt 1 view -0
 718              		.cfi_startproc
 719              		@ args = 0, pretend = 0, frame = 0
 720              		@ frame_needed = 0, uses_anonymous_args = 0
 622:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_NETIF_EXT_STATUS_CALLBACK
 721              		.loc 1 622 1 is_stmt 0 view .LVU179
 722 0000 08B5     		push	{r3, lr}
 723              	.LCFI12:
 724              		.cfi_def_cfa_offset 8
 725              		.cfi_offset 3, -8
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 45


 726              		.cfi_offset 14, -4
 627:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif
 727              		.loc 1 627 3 is_stmt 1 view .LVU180
 728              	.LVL63:
 629:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 729              		.loc 1 629 28 view .LVU181
 631:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 730              		.loc 1 631 3 view .LVU182
 631:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 731              		.loc 1 631 3 view .LVU183
 732 0002 30B1     		cbz	r0, .L68
 733 0004 0346     		mov	r3, r0
 631:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 734              		.loc 1 631 3 discriminator 2 view .LVU184
 634:Middlewares/Third_Party/LwIP/src/core/netif.c ****     gw = IP4_ADDR_ANY4;
 735              		.loc 1 634 3 view .LVU185
 634:Middlewares/Third_Party/LwIP/src/core/netif.c ****     gw = IP4_ADDR_ANY4;
 736              		.loc 1 634 6 is_stmt 0 view .LVU186
 737 0006 61B1     		cbz	r1, .L69
 738              	.LVL64:
 739              	.L65:
 638:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_NETIF_EXT_STATUS_CALLBACK
 740              		.loc 1 638 3 is_stmt 1 view .LVU187
 638:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_NETIF_EXT_STATUS_CALLBACK
 741              		.loc 1 638 7 is_stmt 0 view .LVU188
 742 0008 0022     		movs	r2, #0
 743 000a 1846     		mov	r0, r3
 744              	.LVL65:
 638:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_NETIF_EXT_STATUS_CALLBACK
 745              		.loc 1 638 7 view .LVU189
 746 000c FFF7FEFF 		bl	netif_do_set_gw
 747              	.LVL66:
 644:Middlewares/Third_Party/LwIP/src/core/netif.c **** }
 748              		.loc 1 644 3 is_stmt 1 view .LVU190
 749              	.L62:
 645:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 750              		.loc 1 645 1 is_stmt 0 view .LVU191
 751 0010 08BD     		pop	{r3, pc}
 752              	.LVL67:
 753              	.L68:
 631:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 754              		.loc 1 631 3 is_stmt 1 discriminator 1 view .LVU192
 631:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 755              		.loc 1 631 3 discriminator 1 view .LVU193
 756 0012 054B     		ldr	r3, .L70
 757 0014 40F27722 		movw	r2, #631
 758 0018 0449     		ldr	r1, .L70+4
 759              	.LVL68:
 631:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 760              		.loc 1 631 3 is_stmt 0 discriminator 1 view .LVU194
 761 001a 0548     		ldr	r0, .L70+8
 762              	.LVL69:
 631:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 763              		.loc 1 631 3 discriminator 1 view .LVU195
 764 001c FFF7FEFF 		bl	printf
 765              	.LVL70:
 631:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 46


 766              		.loc 1 631 3 is_stmt 1 discriminator 1 view .LVU196
 631:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 767              		.loc 1 631 3 discriminator 1 view .LVU197
 631:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 768              		.loc 1 631 3 is_stmt 0 view .LVU198
 769 0020 F6E7     		b	.L62
 770              	.LVL71:
 771              	.L69:
 635:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
 772              		.loc 1 635 8 view .LVU199
 773 0022 0449     		ldr	r1, .L70+12
 774              	.LVL72:
 635:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
 775              		.loc 1 635 8 view .LVU200
 776 0024 F0E7     		b	.L65
 777              	.L71:
 778 0026 00BF     		.align	2
 779              	.L70:
 780 0028 00000000 		.word	.LC0
 781 002c 00000000 		.word	.LC8
 782 0030 54000000 		.word	.LC2
 783 0034 00000000 		.word	ip_addr_any
 784              		.cfi_endproc
 785              	.LFE184:
 787              		.section	.text.netif_set_addr,"ax",%progbits
 788              		.align	1
 789              		.global	netif_set_addr
 790              		.syntax unified
 791              		.thumb
 792              		.thumb_func
 794              	netif_set_addr:
 795              	.LVL73:
 796              	.LFB185:
 660:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_NETIF_EXT_STATUS_CALLBACK
 797              		.loc 1 660 1 is_stmt 1 view -0
 798              		.cfi_startproc
 799              		@ args = 0, pretend = 0, frame = 8
 800              		@ frame_needed = 0, uses_anonymous_args = 0
 660:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_NETIF_EXT_STATUS_CALLBACK
 801              		.loc 1 660 1 is_stmt 0 view .LVU202
 802 0000 2DE9F041 		push	{r4, r5, r6, r7, r8, lr}
 803              	.LCFI13:
 804              		.cfi_def_cfa_offset 24
 805              		.cfi_offset 4, -24
 806              		.cfi_offset 5, -20
 807              		.cfi_offset 6, -16
 808              		.cfi_offset 7, -12
 809              		.cfi_offset 8, -8
 810              		.cfi_offset 14, -4
 811 0004 82B0     		sub	sp, sp, #8
 812              	.LCFI14:
 813              		.cfi_def_cfa_offset 32
 814 0006 0446     		mov	r4, r0
 815 0008 1646     		mov	r6, r2
 816 000a 1D46     		mov	r5, r3
 669:Middlewares/Third_Party/LwIP/src/core/netif.c ****   ip_addr_t *old_gw = NULL;
 817              		.loc 1 669 3 is_stmt 1 view .LVU203
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 47


 818              	.LVL74:
 670:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif
 819              		.loc 1 670 3 view .LVU204
 672:Middlewares/Third_Party/LwIP/src/core/netif.c ****   int remove;
 820              		.loc 1 672 3 view .LVU205
 673:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 821              		.loc 1 673 3 view .LVU206
 675:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 822              		.loc 1 675 28 view .LVU207
 678:Middlewares/Third_Party/LwIP/src/core/netif.c ****     ipaddr = IP4_ADDR_ANY4;
 823              		.loc 1 678 3 view .LVU208
 678:Middlewares/Third_Party/LwIP/src/core/netif.c ****     ipaddr = IP4_ADDR_ANY4;
 824              		.loc 1 678 6 is_stmt 0 view .LVU209
 825 000c 0F46     		mov	r7, r1
 826 000e D9B1     		cbz	r1, .L84
 827              	.L73:
 828              	.LVL75:
 681:Middlewares/Third_Party/LwIP/src/core/netif.c ****     netmask = IP4_ADDR_ANY4;
 829              		.loc 1 681 3 is_stmt 1 view .LVU210
 681:Middlewares/Third_Party/LwIP/src/core/netif.c ****     netmask = IP4_ADDR_ANY4;
 830              		.loc 1 681 6 is_stmt 0 view .LVU211
 831 0010 E6B1     		cbz	r6, .L85
 832              	.L74:
 833              	.LVL76:
 684:Middlewares/Third_Party/LwIP/src/core/netif.c ****     gw = IP4_ADDR_ANY4;
 834              		.loc 1 684 3 is_stmt 1 view .LVU212
 684:Middlewares/Third_Party/LwIP/src/core/netif.c ****     gw = IP4_ADDR_ANY4;
 835              		.loc 1 684 6 is_stmt 0 view .LVU213
 836 0012 EDB1     		cbz	r5, .L86
 837              	.L75:
 838              	.LVL77:
 688:Middlewares/Third_Party/LwIP/src/core/netif.c ****   if (remove) {
 839              		.loc 1 688 3 is_stmt 1 view .LVU214
 688:Middlewares/Third_Party/LwIP/src/core/netif.c ****   if (remove) {
 840              		.loc 1 688 12 is_stmt 0 view .LVU215
 841 0014 0FB1     		cbz	r7, .L76
 688:Middlewares/Third_Party/LwIP/src/core/netif.c ****   if (remove) {
 842              		.loc 1 688 12 discriminator 2 view .LVU216
 843 0016 3B68     		ldr	r3, [r7]
 844 0018 E3B9     		cbnz	r3, .L82
 845              	.L76:
 692:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_NETIF_EXT_STATUS_CALLBACK
 846              		.loc 1 692 5 is_stmt 1 view .LVU217
 692:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_NETIF_EXT_STATUS_CALLBACK
 847              		.loc 1 692 9 is_stmt 0 view .LVU218
 848 001a 01AA     		add	r2, sp, #4
 849 001c 3946     		mov	r1, r7
 850 001e 2046     		mov	r0, r4
 851              	.LVL78:
 692:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_NETIF_EXT_STATUS_CALLBACK
 852              		.loc 1 692 9 view .LVU219
 853 0020 FFF7FEFF 		bl	netif_do_set_ipaddr
 854              	.LVL79:
 855 0024 4FF00108 		mov	r8, #1
 856              	.L77:
 697:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
 857              		.loc 1 697 5 is_stmt 1 view .LVU220
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 48


 699:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_NETIF_EXT_STATUS_CALLBACK
 858              		.loc 1 699 3 view .LVU221
 699:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_NETIF_EXT_STATUS_CALLBACK
 859              		.loc 1 699 7 is_stmt 0 view .LVU222
 860 0028 0022     		movs	r2, #0
 861 002a 3146     		mov	r1, r6
 862 002c 2046     		mov	r0, r4
 863 002e FFF7FEFF 		bl	netif_do_set_netmask
 864              	.LVL80:
 704:Middlewares/Third_Party/LwIP/src/core/netif.c ****   if (netif_do_set_gw(netif, gw, old_gw)) {
 865              		.loc 1 704 3 is_stmt 1 view .LVU223
 705:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_NETIF_EXT_STATUS_CALLBACK
 866              		.loc 1 705 3 view .LVU224
 705:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_NETIF_EXT_STATUS_CALLBACK
 867              		.loc 1 705 7 is_stmt 0 view .LVU225
 868 0032 0022     		movs	r2, #0
 869 0034 2946     		mov	r1, r5
 870 0036 2046     		mov	r0, r4
 871 0038 FFF7FEFF 		bl	netif_do_set_gw
 872              	.LVL81:
 710:Middlewares/Third_Party/LwIP/src/core/netif.c ****   if (!remove) {
 873              		.loc 1 710 3 is_stmt 1 view .LVU226
 711:Middlewares/Third_Party/LwIP/src/core/netif.c ****     /* set ipaddr last to ensure netmask/gw have been set when status callback is called */
 874              		.loc 1 711 3 view .LVU227
 711:Middlewares/Third_Party/LwIP/src/core/netif.c ****     /* set ipaddr last to ensure netmask/gw have been set when status callback is called */
 875              		.loc 1 711 6 is_stmt 0 view .LVU228
 876 003c B8F1000F 		cmp	r8, #0
 877 0040 0BD0     		beq	.L87
 878              	.L72:
 727:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_IPV4*/
 879              		.loc 1 727 1 view .LVU229
 880 0042 02B0     		add	sp, sp, #8
 881              	.LCFI15:
 882              		.cfi_remember_state
 883              		.cfi_def_cfa_offset 24
 884              		@ sp needed
 885 0044 BDE8F081 		pop	{r4, r5, r6, r7, r8, pc}
 886              	.LVL82:
 887              	.L84:
 888              	.LCFI16:
 889              		.cfi_restore_state
 679:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
 890              		.loc 1 679 12 view .LVU230
 891 0048 074F     		ldr	r7, .L88
 892 004a E1E7     		b	.L73
 893              	.LVL83:
 894              	.L85:
 682:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
 895              		.loc 1 682 13 view .LVU231
 896 004c 064E     		ldr	r6, .L88
 897 004e E0E7     		b	.L74
 898              	.LVL84:
 899              	.L86:
 685:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
 900              		.loc 1 685 8 view .LVU232
 901 0050 054D     		ldr	r5, .L88
 902 0052 DFE7     		b	.L75
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 49


 903              	.LVL85:
 904              	.L82:
 688:Middlewares/Third_Party/LwIP/src/core/netif.c ****   if (remove) {
 905              		.loc 1 688 12 discriminator 4 view .LVU233
 906 0054 4FF00008 		mov	r8, #0
 907 0058 E6E7     		b	.L77
 908              	.LVL86:
 909              	.L87:
 713:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_NETIF_EXT_STATUS_CALLBACK
 910              		.loc 1 713 5 is_stmt 1 view .LVU234
 713:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_NETIF_EXT_STATUS_CALLBACK
 911              		.loc 1 713 9 is_stmt 0 view .LVU235
 912 005a 01AA     		add	r2, sp, #4
 913 005c 3946     		mov	r1, r7
 914 005e 2046     		mov	r0, r4
 915 0060 FFF7FEFF 		bl	netif_do_set_ipaddr
 916              	.LVL87:
 718:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
 917              		.loc 1 718 5 is_stmt 1 view .LVU236
 727:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_IPV4*/
 918              		.loc 1 727 1 is_stmt 0 view .LVU237
 919 0064 EDE7     		b	.L72
 920              	.L89:
 921 0066 00BF     		.align	2
 922              	.L88:
 923 0068 00000000 		.word	ip_addr_any
 924              		.cfi_endproc
 925              	.LFE185:
 927              		.section	.rodata.netif_add.str1.4,"aMS",%progbits,1
 928              		.align	2
 929              	.LC9:
 930 0000 6E657469 		.ascii	"netif_add: invalid netif\000"
 930      665F6164 
 930      643A2069 
 930      6E76616C 
 930      6964206E 
 931 0019 000000   		.align	2
 932              	.LC10:
 933 001c 6E657469 		.ascii	"netif_add: No init function given\000"
 933      665F6164 
 933      643A204E 
 933      6F20696E 
 933      69742066 
 934 003e 0000     		.align	2
 935              	.LC11:
 936 0040 6E657469 		.ascii	"netif already added\000"
 936      6620616C 
 936      72656164 
 936      79206164 
 936      64656400 
 937              		.align	2
 938              	.LC12:
 939 0054 746F6F20 		.ascii	"too many netifs, max. supported number is 255\000"
 939      6D616E79 
 939      206E6574 
 939      6966732C 
 939      206D6178 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 50


 940              		.section	.text.netif_add,"ax",%progbits
 941              		.align	1
 942              		.global	netif_add
 943              		.syntax unified
 944              		.thumb
 945              		.thumb_func
 947              	netif_add:
 948              	.LVL88:
 949              	.LFB177:
 281:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_IPV6
 950              		.loc 1 281 1 is_stmt 1 view -0
 951              		.cfi_startproc
 952              		@ args = 12, pretend = 0, frame = 0
 953              		@ frame_needed = 0, uses_anonymous_args = 0
 281:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_IPV6
 954              		.loc 1 281 1 is_stmt 0 view .LVU239
 955 0000 70B5     		push	{r4, r5, r6, lr}
 956              	.LCFI17:
 957              		.cfi_def_cfa_offset 16
 958              		.cfi_offset 4, -16
 959              		.cfi_offset 5, -12
 960              		.cfi_offset 6, -8
 961              		.cfi_offset 14, -4
 962 0002 059C     		ldr	r4, [sp, #20]
 286:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 963              		.loc 1 286 28 is_stmt 1 view .LVU240
 295:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ERROR("netif_add: No init function given", init != NULL, return NULL);
 964              		.loc 1 295 3 view .LVU241
 295:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ERROR("netif_add: No init function given", init != NULL, return NULL);
 965              		.loc 1 295 3 view .LVU242
 966 0004 0646     		mov	r6, r0
 967 0006 10B3     		cbz	r0, .L112
 295:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ERROR("netif_add: No init function given", init != NULL, return NULL);
 968              		.loc 1 295 3 discriminator 2 view .LVU243
 296:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 969              		.loc 1 296 3 view .LVU244
 296:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 970              		.loc 1 296 3 view .LVU245
 971 0008 002C     		cmp	r4, #0
 972 000a 29D0     		beq	.L113
 296:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 973              		.loc 1 296 3 discriminator 2 view .LVU246
 299:Middlewares/Third_Party/LwIP/src/core/netif.c ****     ipaddr = ip_2_ip4(IP4_ADDR_ANY);
 974              		.loc 1 299 3 view .LVU247
 299:Middlewares/Third_Party/LwIP/src/core/netif.c ****     ipaddr = ip_2_ip4(IP4_ADDR_ANY);
 975              		.loc 1 299 6 is_stmt 0 view .LVU248
 976 000c 0029     		cmp	r1, #0
 977 000e 30D0     		beq	.L114
 978              	.LVL89:
 979              	.L94:
 302:Middlewares/Third_Party/LwIP/src/core/netif.c ****     netmask = ip_2_ip4(IP4_ADDR_ANY);
 980              		.loc 1 302 3 is_stmt 1 view .LVU249
 302:Middlewares/Third_Party/LwIP/src/core/netif.c ****     netmask = ip_2_ip4(IP4_ADDR_ANY);
 981              		.loc 1 302 6 is_stmt 0 view .LVU250
 982 0010 002A     		cmp	r2, #0
 983 0012 30D0     		beq	.L115
 984              	.LVL90:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 51


 985              	.L95:
 305:Middlewares/Third_Party/LwIP/src/core/netif.c ****     gw = ip_2_ip4(IP4_ADDR_ANY);
 986              		.loc 1 305 3 is_stmt 1 view .LVU251
 305:Middlewares/Third_Party/LwIP/src/core/netif.c ****     gw = ip_2_ip4(IP4_ADDR_ANY);
 987              		.loc 1 305 6 is_stmt 0 view .LVU252
 988 0014 002B     		cmp	r3, #0
 989 0016 30D0     		beq	.L116
 990              	.LVL91:
 991              	.L96:
 310:Middlewares/Third_Party/LwIP/src/core/netif.c ****   ip_addr_set_zero_ip4(&netif->netmask);
 992              		.loc 1 310 3 is_stmt 1 view .LVU253
 993 0018 0020     		movs	r0, #0
 994              	.LVL92:
 310:Middlewares/Third_Party/LwIP/src/core/netif.c ****   ip_addr_set_zero_ip4(&netif->netmask);
 995              		.loc 1 310 3 is_stmt 0 view .LVU254
 996 001a 7060     		str	r0, [r6, #4]
 311:Middlewares/Third_Party/LwIP/src/core/netif.c ****   ip_addr_set_zero_ip4(&netif->gw);
 997              		.loc 1 311 3 is_stmt 1 view .LVU255
 998 001c B060     		str	r0, [r6, #8]
 312:Middlewares/Third_Party/LwIP/src/core/netif.c ****   netif->output = netif_null_output_ip4;
 999              		.loc 1 312 3 view .LVU256
 1000 001e F060     		str	r0, [r6, #12]
 313:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_IPV4 */
 1001              		.loc 1 313 3 view .LVU257
 313:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_IPV4 */
 1002              		.loc 1 313 17 is_stmt 0 view .LVU258
 1003 0020 364D     		ldr	r5, .L122
 1004 0022 7561     		str	r5, [r6, #20]
 326:Middlewares/Third_Party/LwIP/src/core/netif.c ****   netif->mtu = 0;
 1005              		.loc 1 326 60 is_stmt 1 view .LVU259
 327:Middlewares/Third_Party/LwIP/src/core/netif.c ****   netif->flags = 0;
 1006              		.loc 1 327 3 view .LVU260
 327:Middlewares/Third_Party/LwIP/src/core/netif.c ****   netif->flags = 0;
 1007              		.loc 1 327 14 is_stmt 0 view .LVU261
 1008 0024 B084     		strh	r0, [r6, #36]	@ movhi
 328:Middlewares/Third_Party/LwIP/src/core/netif.c **** #ifdef netif_get_client_data
 1009              		.loc 1 328 3 is_stmt 1 view .LVU262
 328:Middlewares/Third_Party/LwIP/src/core/netif.c **** #ifdef netif_get_client_data
 1010              		.loc 1 328 16 is_stmt 0 view .LVU263
 1011 0026 86F82D00 		strb	r0, [r6, #45]
 343:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_NETIF_LINK_CALLBACK */
 1012              		.loc 1 343 3 is_stmt 1 view .LVU264
 343:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_NETIF_LINK_CALLBACK */
 1013              		.loc 1 343 24 is_stmt 0 view .LVU265
 1014 002a F061     		str	r0, [r6, #28]
 357:Middlewares/Third_Party/LwIP/src/core/netif.c ****   netif->num = netif_num;
 1015              		.loc 1 357 3 is_stmt 1 view .LVU266
 357:Middlewares/Third_Party/LwIP/src/core/netif.c ****   netif->num = netif_num;
 1016              		.loc 1 357 16 is_stmt 0 view .LVU267
 1017 002c 0498     		ldr	r0, [sp, #16]
 1018 002e 3062     		str	r0, [r6, #32]
 358:Middlewares/Third_Party/LwIP/src/core/netif.c ****   netif->input = input;
 1019              		.loc 1 358 3 is_stmt 1 view .LVU268
 358:Middlewares/Third_Party/LwIP/src/core/netif.c ****   netif->input = input;
 1020              		.loc 1 358 14 is_stmt 0 view .LVU269
 1021 0030 3348     		ldr	r0, .L122+4
 1022 0032 0078     		ldrb	r0, [r0]	@ zero_extendqisi2
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 52


 1023 0034 86F83000 		strb	r0, [r6, #48]
 359:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1024              		.loc 1 359 3 is_stmt 1 view .LVU270
 359:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1025              		.loc 1 359 16 is_stmt 0 view .LVU271
 1026 0038 0698     		ldr	r0, [sp, #24]
 1027 003a 3061     		str	r0, [r6, #16]
 361:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if ENABLE_LOOPBACK && LWIP_LOOPBACK_MAX_PBUFS
 1028              		.loc 1 361 27 is_stmt 1 view .LVU272
 367:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_IPV4 */
 1029              		.loc 1 367 3 view .LVU273
 1030 003c 3046     		mov	r0, r6
 1031 003e FFF7FEFF 		bl	netif_set_addr
 1032              	.LVL93:
 371:Middlewares/Third_Party/LwIP/src/core/netif.c ****     return NULL;
 1033              		.loc 1 371 3 view .LVU274
 371:Middlewares/Third_Party/LwIP/src/core/netif.c ****     return NULL;
 1034              		.loc 1 371 7 is_stmt 0 view .LVU275
 1035 0042 3046     		mov	r0, r6
 1036 0044 A047     		blx	r4
 1037              	.LVL94:
 371:Middlewares/Third_Party/LwIP/src/core/netif.c ****     return NULL;
 1038              		.loc 1 371 6 discriminator 1 view .LVU276
 1039 0046 0028     		cmp	r0, #0
 1040 0048 3ED0     		beq	.L104
 372:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
 1041              		.loc 1 372 12 view .LVU277
 1042 004a 0020     		movs	r0, #0
 1043 004c 50E0     		b	.L90
 1044              	.LVL95:
 1045              	.L112:
 295:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ERROR("netif_add: No init function given", init != NULL, return NULL);
 1046              		.loc 1 295 3 is_stmt 1 discriminator 1 view .LVU278
 295:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ERROR("netif_add: No init function given", init != NULL, return NULL);
 1047              		.loc 1 295 3 discriminator 1 view .LVU279
 1048 004e 2D4B     		ldr	r3, .L122+8
 1049              	.LVL96:
 295:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ERROR("netif_add: No init function given", init != NULL, return NULL);
 1050              		.loc 1 295 3 is_stmt 0 discriminator 1 view .LVU280
 1051 0050 40F22712 		movw	r2, #295
 1052              	.LVL97:
 295:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ERROR("netif_add: No init function given", init != NULL, return NULL);
 1053              		.loc 1 295 3 discriminator 1 view .LVU281
 1054 0054 2C49     		ldr	r1, .L122+12
 1055              	.LVL98:
 295:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ERROR("netif_add: No init function given", init != NULL, return NULL);
 1056              		.loc 1 295 3 discriminator 1 view .LVU282
 1057 0056 2D48     		ldr	r0, .L122+16
 1058              	.LVL99:
 295:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ERROR("netif_add: No init function given", init != NULL, return NULL);
 1059              		.loc 1 295 3 discriminator 1 view .LVU283
 1060 0058 FFF7FEFF 		bl	printf
 1061              	.LVL100:
 295:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ERROR("netif_add: No init function given", init != NULL, return NULL);
 1062              		.loc 1 295 3 is_stmt 1 discriminator 1 view .LVU284
 295:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ERROR("netif_add: No init function given", init != NULL, return NULL);
 1063              		.loc 1 295 3 discriminator 1 view .LVU285
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 53


 1064 005c 3046     		mov	r0, r6
 295:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ERROR("netif_add: No init function given", init != NULL, return NULL);
 1065              		.loc 1 295 3 is_stmt 0 view .LVU286
 1066 005e 47E0     		b	.L90
 1067              	.LVL101:
 1068              	.L113:
 296:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1069              		.loc 1 296 3 is_stmt 1 discriminator 1 view .LVU287
 296:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1070              		.loc 1 296 3 discriminator 1 view .LVU288
 1071 0060 284B     		ldr	r3, .L122+8
 1072              	.LVL102:
 296:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1073              		.loc 1 296 3 is_stmt 0 discriminator 1 view .LVU289
 1074 0062 4FF49472 		mov	r2, #296
 1075              	.LVL103:
 296:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1076              		.loc 1 296 3 discriminator 1 view .LVU290
 1077 0066 2A49     		ldr	r1, .L122+20
 1078              	.LVL104:
 296:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1079              		.loc 1 296 3 discriminator 1 view .LVU291
 1080 0068 2848     		ldr	r0, .L122+16
 1081              	.LVL105:
 296:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1082              		.loc 1 296 3 discriminator 1 view .LVU292
 1083 006a FFF7FEFF 		bl	printf
 1084              	.LVL106:
 296:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1085              		.loc 1 296 3 is_stmt 1 discriminator 1 view .LVU293
 296:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1086              		.loc 1 296 3 discriminator 1 view .LVU294
 1087 006e 0020     		movs	r0, #0
 296:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1088              		.loc 1 296 3 is_stmt 0 view .LVU295
 1089 0070 3EE0     		b	.L90
 1090              	.LVL107:
 1091              	.L114:
 300:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
 1092              		.loc 1 300 12 view .LVU296
 1093 0072 2849     		ldr	r1, .L122+24
 1094              	.LVL108:
 300:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
 1095              		.loc 1 300 12 view .LVU297
 1096 0074 CCE7     		b	.L94
 1097              	.LVL109:
 1098              	.L115:
 303:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
 1099              		.loc 1 303 13 view .LVU298
 1100 0076 274A     		ldr	r2, .L122+24
 1101              	.LVL110:
 303:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
 1102              		.loc 1 303 13 view .LVU299
 1103 0078 CCE7     		b	.L95
 1104              	.LVL111:
 1105              	.L116:
 306:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 54


 1106              		.loc 1 306 8 view .LVU300
 1107 007a 264B     		ldr	r3, .L122+24
 1108              	.LVL112:
 306:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
 1109              		.loc 1 306 8 view .LVU301
 1110 007c CCE7     		b	.L96
 1111              	.LVL113:
 1112              	.L120:
 1113              	.LBB3:
 391:Middlewares/Third_Party/LwIP/src/core/netif.c ****       }
 1114              		.loc 1 391 9 is_stmt 1 view .LVU302
 391:Middlewares/Third_Party/LwIP/src/core/netif.c ****       }
 1115              		.loc 1 391 20 is_stmt 0 view .LVU303
 1116 007e 0023     		movs	r3, #0
 1117 0080 86F83030 		strb	r3, [r6, #48]
 1118 0084 24E0     		b	.L97
 1119              	.LVL114:
 1120              	.L118:
 395:Middlewares/Third_Party/LwIP/src/core/netif.c ****         num_netifs++;
 1121              		.loc 1 395 9 is_stmt 1 discriminator 1 view .LVU304
 395:Middlewares/Third_Party/LwIP/src/core/netif.c ****         num_netifs++;
 1122              		.loc 1 395 9 discriminator 1 view .LVU305
 1123 0086 1F4B     		ldr	r3, .L122+8
 1124 0088 40F28B12 		movw	r2, #395
 1125 008c 2249     		ldr	r1, .L122+28
 1126 008e 1F48     		ldr	r0, .L122+16
 1127 0090 FFF7FEFF 		bl	printf
 1128              	.LVL115:
 1129 0094 09E0     		b	.L99
 1130              	.L100:
 397:Middlewares/Third_Party/LwIP/src/core/netif.c ****         if (netif2->num == netif->num) {
 1131              		.loc 1 397 9 discriminator 3 view .LVU306
 397:Middlewares/Third_Party/LwIP/src/core/netif.c ****         if (netif2->num == netif->num) {
 1132              		.loc 1 397 9 discriminator 3 view .LVU307
 398:Middlewares/Third_Party/LwIP/src/core/netif.c ****           netif->num++;
 1133              		.loc 1 398 9 view .LVU308
 398:Middlewares/Third_Party/LwIP/src/core/netif.c ****           netif->num++;
 1134              		.loc 1 398 19 is_stmt 0 view .LVU309
 1135 0096 94F83020 		ldrb	r2, [r4, #48]	@ zero_extendqisi2
 398:Middlewares/Third_Party/LwIP/src/core/netif.c ****           netif->num++;
 1136              		.loc 1 398 33 view .LVU310
 1137 009a 96F83030 		ldrb	r3, [r6, #48]	@ zero_extendqisi2
 398:Middlewares/Third_Party/LwIP/src/core/netif.c ****           netif->num++;
 1138              		.loc 1 398 12 view .LVU311
 1139 009e 9A42     		cmp	r2, r3
 1140 00a0 0ED0     		beq	.L117
 394:Middlewares/Third_Party/LwIP/src/core/netif.c ****         LWIP_ASSERT("netif already added", netif2 != netif);
 1141              		.loc 1 394 56 is_stmt 1 discriminator 2 view .LVU312
 1142 00a2 2468     		ldr	r4, [r4]
 1143              	.LVL116:
 1144              	.L98:
 394:Middlewares/Third_Party/LwIP/src/core/netif.c ****         LWIP_ASSERT("netif already added", netif2 != netif);
 1145              		.loc 1 394 40 discriminator 1 view .LVU313
 1146 00a4 7CB1     		cbz	r4, .L102
 395:Middlewares/Third_Party/LwIP/src/core/netif.c ****         num_netifs++;
 1147              		.loc 1 395 9 view .LVU314
 395:Middlewares/Third_Party/LwIP/src/core/netif.c ****         num_netifs++;
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 55


 1148              		.loc 1 395 9 view .LVU315
 1149 00a6 B442     		cmp	r4, r6
 1150 00a8 EDD0     		beq	.L118
 1151              	.L99:
 395:Middlewares/Third_Party/LwIP/src/core/netif.c ****         num_netifs++;
 1152              		.loc 1 395 9 discriminator 3 view .LVU316
 395:Middlewares/Third_Party/LwIP/src/core/netif.c ****         num_netifs++;
 1153              		.loc 1 395 9 discriminator 3 view .LVU317
 396:Middlewares/Third_Party/LwIP/src/core/netif.c ****         LWIP_ASSERT("too many netifs, max. supported number is 255", num_netifs <= 255);
 1154              		.loc 1 396 9 view .LVU318
 396:Middlewares/Third_Party/LwIP/src/core/netif.c ****         LWIP_ASSERT("too many netifs, max. supported number is 255", num_netifs <= 255);
 1155              		.loc 1 396 19 is_stmt 0 view .LVU319
 1156 00aa 0135     		adds	r5, r5, #1
 1157              	.LVL117:
 397:Middlewares/Third_Party/LwIP/src/core/netif.c ****         if (netif2->num == netif->num) {
 1158              		.loc 1 397 9 is_stmt 1 view .LVU320
 397:Middlewares/Third_Party/LwIP/src/core/netif.c ****         if (netif2->num == netif->num) {
 1159              		.loc 1 397 9 view .LVU321
 1160 00ac FF2D     		cmp	r5, #255
 1161 00ae F2DD     		ble	.L100
 397:Middlewares/Third_Party/LwIP/src/core/netif.c ****         if (netif2->num == netif->num) {
 1162              		.loc 1 397 9 discriminator 1 view .LVU322
 397:Middlewares/Third_Party/LwIP/src/core/netif.c ****         if (netif2->num == netif->num) {
 1163              		.loc 1 397 9 discriminator 1 view .LVU323
 1164 00b0 144B     		ldr	r3, .L122+8
 1165 00b2 40F28D12 		movw	r2, #397
 1166 00b6 1949     		ldr	r1, .L122+32
 1167 00b8 1448     		ldr	r0, .L122+16
 1168 00ba FFF7FEFF 		bl	printf
 1169              	.LVL118:
 1170 00be EAE7     		b	.L100
 1171              	.L117:
 399:Middlewares/Third_Party/LwIP/src/core/netif.c ****           break;
 1172              		.loc 1 399 11 view .LVU324
 399:Middlewares/Third_Party/LwIP/src/core/netif.c ****           break;
 1173              		.loc 1 399 21 is_stmt 0 view .LVU325
 1174 00c0 0133     		adds	r3, r3, #1
 1175 00c2 86F83030 		strb	r3, [r6, #48]
 400:Middlewares/Third_Party/LwIP/src/core/netif.c ****         }
 1176              		.loc 1 400 11 is_stmt 1 view .LVU326
 1177              	.L102:
 403:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
 1178              		.loc 1 403 21 view .LVU327
 1179 00c6 3CB1     		cbz	r4, .L119
 1180              	.LVL119:
 1181              	.L104:
 387:Middlewares/Third_Party/LwIP/src/core/netif.c ****     int num_netifs;
 1182              		.loc 1 387 5 view .LVU328
 388:Middlewares/Third_Party/LwIP/src/core/netif.c ****     do {
 1183              		.loc 1 388 5 view .LVU329
 389:Middlewares/Third_Party/LwIP/src/core/netif.c ****       if (netif->num == 255) {
 1184              		.loc 1 389 5 view .LVU330
 390:Middlewares/Third_Party/LwIP/src/core/netif.c ****         netif->num = 0;
 1185              		.loc 1 390 7 view .LVU331
 390:Middlewares/Third_Party/LwIP/src/core/netif.c ****         netif->num = 0;
 1186              		.loc 1 390 16 is_stmt 0 view .LVU332
 1187 00c8 96F83030 		ldrb	r3, [r6, #48]	@ zero_extendqisi2
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 56


 390:Middlewares/Third_Party/LwIP/src/core/netif.c ****         netif->num = 0;
 1188              		.loc 1 390 10 view .LVU333
 1189 00cc FF2B     		cmp	r3, #255
 1190 00ce D6D0     		beq	.L120
 1191              	.L97:
 393:Middlewares/Third_Party/LwIP/src/core/netif.c ****       for (netif2 = netif_list; netif2 != NULL; netif2 = netif2->next) {
 1192              		.loc 1 393 7 is_stmt 1 view .LVU334
 1193              	.LVL120:
 394:Middlewares/Third_Party/LwIP/src/core/netif.c ****         LWIP_ASSERT("netif already added", netif2 != netif);
 1194              		.loc 1 394 7 view .LVU335
 394:Middlewares/Third_Party/LwIP/src/core/netif.c ****         LWIP_ASSERT("netif already added", netif2 != netif);
 1195              		.loc 1 394 19 is_stmt 0 view .LVU336
 1196 00d0 134B     		ldr	r3, .L122+36
 1197 00d2 1C68     		ldr	r4, [r3]
 1198              	.LVL121:
 393:Middlewares/Third_Party/LwIP/src/core/netif.c ****       for (netif2 = netif_list; netif2 != NULL; netif2 = netif2->next) {
 1199              		.loc 1 393 18 view .LVU337
 1200 00d4 0025     		movs	r5, #0
 394:Middlewares/Third_Party/LwIP/src/core/netif.c ****         LWIP_ASSERT("netif already added", netif2 != netif);
 1201              		.loc 1 394 7 view .LVU338
 1202 00d6 E5E7     		b	.L98
 1203              	.LVL122:
 1204              	.L119:
 394:Middlewares/Third_Party/LwIP/src/core/netif.c ****         LWIP_ASSERT("netif already added", netif2 != netif);
 1205              		.loc 1 394 7 view .LVU339
 1206              	.LBE3:
 405:Middlewares/Third_Party/LwIP/src/core/netif.c ****     netif_num = 0;
 1207              		.loc 1 405 3 is_stmt 1 view .LVU340
 405:Middlewares/Third_Party/LwIP/src/core/netif.c ****     netif_num = 0;
 1208              		.loc 1 405 12 is_stmt 0 view .LVU341
 1209 00d8 96F83030 		ldrb	r3, [r6, #48]	@ zero_extendqisi2
 405:Middlewares/Third_Party/LwIP/src/core/netif.c ****     netif_num = 0;
 1210              		.loc 1 405 6 view .LVU342
 1211 00dc FE2B     		cmp	r3, #254
 1212 00de 08D0     		beq	.L121
 408:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
 1213              		.loc 1 408 5 is_stmt 1 view .LVU343
 408:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
 1214              		.loc 1 408 17 is_stmt 0 view .LVU344
 1215 00e0 0133     		adds	r3, r3, #1
 408:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
 1216              		.loc 1 408 15 view .LVU345
 1217 00e2 074A     		ldr	r2, .L122+4
 1218 00e4 1370     		strb	r3, [r2]
 1219              	.L106:
 412:Middlewares/Third_Party/LwIP/src/core/netif.c ****   netif_list = netif;
 1220              		.loc 1 412 3 is_stmt 1 view .LVU346
 412:Middlewares/Third_Party/LwIP/src/core/netif.c ****   netif_list = netif;
 1221              		.loc 1 412 15 is_stmt 0 view .LVU347
 1222 00e6 0E4B     		ldr	r3, .L122+36
 1223 00e8 1A68     		ldr	r2, [r3]
 1224 00ea 3260     		str	r2, [r6]
 413:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* "LWIP_SINGLE_NETIF */
 1225              		.loc 1 413 3 is_stmt 1 view .LVU348
 413:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* "LWIP_SINGLE_NETIF */
 1226              		.loc 1 413 14 is_stmt 0 view .LVU349
 1227 00ec 1E60     		str	r6, [r3]
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 57


 415:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1228              		.loc 1 415 26 is_stmt 1 view .LVU350
 425:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_IPV4
 1229              		.loc 1 425 61 view .LVU351
 427:Middlewares/Third_Party/LwIP/src/core/netif.c ****   ip4_addr_debug_print(NETIF_DEBUG, ipaddr);
 1230              		.loc 1 427 39 view .LVU352
 428:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_DEBUGF(NETIF_DEBUG, (" netmask "));
 1231              		.loc 1 428 44 view .LVU353
 429:Middlewares/Third_Party/LwIP/src/core/netif.c ****   ip4_addr_debug_print(NETIF_DEBUG, netmask);
 1232              		.loc 1 429 42 view .LVU354
 430:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_DEBUGF(NETIF_DEBUG, (" gw "));
 1233              		.loc 1 430 45 view .LVU355
 431:Middlewares/Third_Party/LwIP/src/core/netif.c ****   ip4_addr_debug_print(NETIF_DEBUG, gw);
 1234              		.loc 1 431 37 view .LVU356
 432:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_IPV4 */
 1235              		.loc 1 432 40 view .LVU357
 434:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1236              		.loc 1 434 35 view .LVU358
 436:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1237              		.loc 1 436 63 view .LVU359
 438:Middlewares/Third_Party/LwIP/src/core/netif.c **** }
 1238              		.loc 1 438 3 view .LVU360
 438:Middlewares/Third_Party/LwIP/src/core/netif.c **** }
 1239              		.loc 1 438 10 is_stmt 0 view .LVU361
 1240 00ee 3046     		mov	r0, r6
 1241              	.LVL123:
 1242              	.L90:
 439:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1243              		.loc 1 439 1 view .LVU362
 1244 00f0 70BD     		pop	{r4, r5, r6, pc}
 1245              	.LVL124:
 1246              	.L121:
 406:Middlewares/Third_Party/LwIP/src/core/netif.c ****   } else {
 1247              		.loc 1 406 5 is_stmt 1 view .LVU363
 406:Middlewares/Third_Party/LwIP/src/core/netif.c ****   } else {
 1248              		.loc 1 406 15 is_stmt 0 view .LVU364
 1249 00f2 034B     		ldr	r3, .L122+4
 1250 00f4 0022     		movs	r2, #0
 1251 00f6 1A70     		strb	r2, [r3]
 1252 00f8 F5E7     		b	.L106
 1253              	.L123:
 1254 00fa 00BF     		.align	2
 1255              	.L122:
 1256 00fc 00000000 		.word	netif_null_output_ip4
 1257 0100 00000000 		.word	netif_num
 1258 0104 00000000 		.word	.LC0
 1259 0108 00000000 		.word	.LC9
 1260 010c 54000000 		.word	.LC2
 1261 0110 1C000000 		.word	.LC10
 1262 0114 00000000 		.word	ip_addr_any
 1263 0118 40000000 		.word	.LC11
 1264 011c 54000000 		.word	.LC12
 1265 0120 00000000 		.word	netif_list
 1266              		.cfi_endproc
 1267              	.LFE177:
 1269              		.section	.text.netif_add_noaddr,"ax",%progbits
 1270              		.align	1
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 58


 1271              		.global	netif_add_noaddr
 1272              		.syntax unified
 1273              		.thumb
 1274              		.thumb_func
 1276              	netif_add_noaddr:
 1277              	.LVL125:
 1278              	.LFB176:
 240:Middlewares/Third_Party/LwIP/src/core/netif.c ****   return netif_add(netif,
 1279              		.loc 1 240 1 is_stmt 1 view -0
 1280              		.cfi_startproc
 1281              		@ args = 0, pretend = 0, frame = 0
 1282              		@ frame_needed = 0, uses_anonymous_args = 0
 240:Middlewares/Third_Party/LwIP/src/core/netif.c ****   return netif_add(netif,
 1283              		.loc 1 240 1 is_stmt 0 view .LVU366
 1284 0000 00B5     		push	{lr}
 1285              	.LCFI18:
 1286              		.cfi_def_cfa_offset 4
 1287              		.cfi_offset 14, -4
 1288 0002 85B0     		sub	sp, sp, #20
 1289              	.LCFI19:
 1290              		.cfi_def_cfa_offset 24
 241:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_IPV4
 1291              		.loc 1 241 3 is_stmt 1 view .LVU367
 241:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_IPV4
 1292              		.loc 1 241 10 is_stmt 0 view .LVU368
 1293 0004 0293     		str	r3, [sp, #8]
 1294 0006 0192     		str	r2, [sp, #4]
 1295 0008 0091     		str	r1, [sp]
 1296 000a 0023     		movs	r3, #0
 1297              	.LVL126:
 241:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_IPV4
 1298              		.loc 1 241 10 view .LVU369
 1299 000c 1A46     		mov	r2, r3
 1300              	.LVL127:
 241:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_IPV4
 1301              		.loc 1 241 10 view .LVU370
 1302 000e 1946     		mov	r1, r3
 1303              	.LVL128:
 241:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_IPV4
 1304              		.loc 1 241 10 view .LVU371
 1305 0010 FFF7FEFF 		bl	netif_add
 1306              	.LVL129:
 246:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1307              		.loc 1 246 1 view .LVU372
 1308 0014 05B0     		add	sp, sp, #20
 1309              	.LCFI20:
 1310              		.cfi_def_cfa_offset 4
 1311              		@ sp needed
 1312 0016 5DF804FB 		ldr	pc, [sp], #4
 1313              		.cfi_endproc
 1314              	.LFE176:
 1316              		.section	.text.netif_set_default,"ax",%progbits
 1317              		.align	1
 1318              		.global	netif_set_default
 1319              		.syntax unified
 1320              		.thumb
 1321              		.thumb_func
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 59


 1323              	netif_set_default:
 1324              	.LVL130:
 1325              	.LFB187:
 823:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT_CORE_LOCKED();
 1326              		.loc 1 823 1 is_stmt 1 view -0
 1327              		.cfi_startproc
 1328              		@ args = 0, pretend = 0, frame = 0
 1329              		@ frame_needed = 0, uses_anonymous_args = 0
 1330              		@ link register save eliminated.
 824:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1331              		.loc 1 824 28 view .LVU374
 826:Middlewares/Third_Party/LwIP/src/core/netif.c ****     /* remove default route */
 1332              		.loc 1 826 3 view .LVU375
 828:Middlewares/Third_Party/LwIP/src/core/netif.c ****   } else {
 1333              		.loc 1 828 36 view .LVU376
 831:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
 1334              		.loc 1 831 33 view .LVU377
 833:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_DEBUGF(NETIF_DEBUG, ("netif: setting default interface %c%c\n",
 1335              		.loc 1 833 3 view .LVU378
 833:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_DEBUGF(NETIF_DEBUG, ("netif: setting default interface %c%c\n",
 1336              		.loc 1 833 17 is_stmt 0 view .LVU379
 1337 0000 014B     		ldr	r3, .L127
 1338 0002 1860     		str	r0, [r3]
 835:Middlewares/Third_Party/LwIP/src/core/netif.c **** }
 1339              		.loc 1 835 91 is_stmt 1 view .LVU380
 836:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1340              		.loc 1 836 1 is_stmt 0 view .LVU381
 1341 0004 7047     		bx	lr
 1342              	.L128:
 1343 0006 00BF     		.align	2
 1344              	.L127:
 1345 0008 00000000 		.word	netif_default
 1346              		.cfi_endproc
 1347              	.LFE187:
 1349              		.section	.rodata.netif_set_up.str1.4,"aMS",%progbits,1
 1350              		.align	2
 1351              	.LC13:
 1352 0000 6E657469 		.ascii	"netif_set_up: invalid netif\000"
 1352      665F7365 
 1352      745F7570 
 1352      3A20696E 
 1352      76616C69 
 1353              		.section	.text.netif_set_up,"ax",%progbits
 1354              		.align	1
 1355              		.global	netif_set_up
 1356              		.syntax unified
 1357              		.thumb
 1358              		.thumb_func
 1360              	netif_set_up:
 1361              	.LVL131:
 1362              	.LFB188:
 845:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT_CORE_LOCKED();
 1363              		.loc 1 845 1 is_stmt 1 view -0
 1364              		.cfi_startproc
 1365              		@ args = 0, pretend = 0, frame = 0
 1366              		@ frame_needed = 0, uses_anonymous_args = 0
 845:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT_CORE_LOCKED();
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 60


 1367              		.loc 1 845 1 is_stmt 0 view .LVU383
 1368 0000 08B5     		push	{r3, lr}
 1369              	.LCFI21:
 1370              		.cfi_def_cfa_offset 8
 1371              		.cfi_offset 3, -8
 1372              		.cfi_offset 14, -4
 846:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1373              		.loc 1 846 28 is_stmt 1 view .LVU384
 848:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1374              		.loc 1 848 3 view .LVU385
 848:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1375              		.loc 1 848 3 view .LVU386
 1376 0002 28B1     		cbz	r0, .L133
 848:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1377              		.loc 1 848 3 discriminator 2 view .LVU387
 850:Middlewares/Third_Party/LwIP/src/core/netif.c ****     netif_set_flags(netif, NETIF_FLAG_UP);
 1378              		.loc 1 850 3 view .LVU388
 850:Middlewares/Third_Party/LwIP/src/core/netif.c ****     netif_set_flags(netif, NETIF_FLAG_UP);
 1379              		.loc 1 850 14 is_stmt 0 view .LVU389
 1380 0004 90F82D30 		ldrb	r3, [r0, #45]	@ zero_extendqisi2
 850:Middlewares/Third_Party/LwIP/src/core/netif.c ****     netif_set_flags(netif, NETIF_FLAG_UP);
 1381              		.loc 1 850 6 view .LVU390
 1382 0008 13F0010F 		tst	r3, #1
 1383 000c 08D0     		beq	.L134
 1384              	.LVL132:
 1385              	.L129:
 870:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1386              		.loc 1 870 1 view .LVU391
 1387 000e 08BD     		pop	{r3, pc}
 1388              	.LVL133:
 1389              	.L133:
 848:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1390              		.loc 1 848 3 is_stmt 1 discriminator 1 view .LVU392
 848:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1391              		.loc 1 848 3 discriminator 1 view .LVU393
 1392 0010 074B     		ldr	r3, .L135
 1393 0012 4FF45472 		mov	r2, #848
 1394 0016 0749     		ldr	r1, .L135+4
 1395 0018 0748     		ldr	r0, .L135+8
 1396              	.LVL134:
 848:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1397              		.loc 1 848 3 is_stmt 0 discriminator 1 view .LVU394
 1398 001a FFF7FEFF 		bl	printf
 1399              	.LVL135:
 848:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1400              		.loc 1 848 3 is_stmt 1 discriminator 1 view .LVU395
 848:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1401              		.loc 1 848 3 discriminator 1 view .LVU396
 848:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1402              		.loc 1 848 3 is_stmt 0 view .LVU397
 1403 001e F6E7     		b	.L129
 1404              	.LVL136:
 1405              	.L134:
 851:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1406              		.loc 1 851 5 is_stmt 1 view .LVU398
 851:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1407              		.loc 1 851 5 view .LVU399
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 61


 1408 0020 43F00103 		orr	r3, r3, #1
 1409 0024 80F82D30 		strb	r3, [r0, #45]
 851:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1410              		.loc 1 851 5 view .LVU400
 853:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1411              		.loc 1 853 39 view .LVU401
 855:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1412              		.loc 1 855 33 view .LVU402
 865:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_IPV6
 1413              		.loc 1 865 5 view .LVU403
 1414 0028 0321     		movs	r1, #3
 1415 002a FFF7FEFF 		bl	netif_issue_reports
 1416              	.LVL137:
 865:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_IPV6
 1417              		.loc 1 865 5 is_stmt 0 view .LVU404
 1418 002e EEE7     		b	.L129
 1419              	.L136:
 1420              		.align	2
 1421              	.L135:
 1422 0030 00000000 		.word	.LC0
 1423 0034 00000000 		.word	.LC13
 1424 0038 54000000 		.word	.LC2
 1425              		.cfi_endproc
 1426              	.LFE188:
 1428              		.section	.rodata.netif_set_down.str1.4,"aMS",%progbits,1
 1429              		.align	2
 1430              	.LC14:
 1431 0000 6E657469 		.ascii	"netif_set_down: invalid netif\000"
 1431      665F7365 
 1431      745F646F 
 1431      776E3A20 
 1431      696E7661 
 1432              		.section	.text.netif_set_down,"ax",%progbits
 1433              		.align	1
 1434              		.global	netif_set_down
 1435              		.syntax unified
 1436              		.thumb
 1437              		.thumb_func
 1439              	netif_set_down:
 1440              	.LVL138:
 1441              	.LFB190:
 920:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT_CORE_LOCKED();
 1442              		.loc 1 920 1 is_stmt 1 view -0
 1443              		.cfi_startproc
 1444              		@ args = 0, pretend = 0, frame = 0
 1445              		@ frame_needed = 0, uses_anonymous_args = 0
 920:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT_CORE_LOCKED();
 1446              		.loc 1 920 1 is_stmt 0 view .LVU406
 1447 0000 08B5     		push	{r3, lr}
 1448              	.LCFI22:
 1449              		.cfi_def_cfa_offset 8
 1450              		.cfi_offset 3, -8
 1451              		.cfi_offset 14, -4
 921:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1452              		.loc 1 921 28 is_stmt 1 view .LVU407
 923:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1453              		.loc 1 923 3 view .LVU408
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 62


 923:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1454              		.loc 1 923 3 view .LVU409
 1455 0002 60B1     		cbz	r0, .L141
 923:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1456              		.loc 1 923 3 discriminator 2 view .LVU410
 925:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_NETIF_EXT_STATUS_CALLBACK
 1457              		.loc 1 925 3 view .LVU411
 925:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_NETIF_EXT_STATUS_CALLBACK
 1458              		.loc 1 925 12 is_stmt 0 view .LVU412
 1459 0004 90F82D30 		ldrb	r3, [r0, #45]	@ zero_extendqisi2
 925:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_NETIF_EXT_STATUS_CALLBACK
 1460              		.loc 1 925 6 view .LVU413
 1461 0008 13F0010F 		tst	r3, #1
 1462 000c 06D0     		beq	.L137
 934:Middlewares/Third_Party/LwIP/src/core/netif.c ****     MIB2_COPY_SYSUPTIME_TO(&netif->ts);
 1463              		.loc 1 934 5 is_stmt 1 view .LVU414
 934:Middlewares/Third_Party/LwIP/src/core/netif.c ****     MIB2_COPY_SYSUPTIME_TO(&netif->ts);
 1464              		.loc 1 934 5 view .LVU415
 1465 000e 03F0FE01 		and	r1, r3, #254
 1466 0012 80F82D10 		strb	r1, [r0, #45]
 934:Middlewares/Third_Party/LwIP/src/core/netif.c ****     MIB2_COPY_SYSUPTIME_TO(&netif->ts);
 1467              		.loc 1 934 5 view .LVU416
 935:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1468              		.loc 1 935 39 view .LVU417
 938:Middlewares/Third_Party/LwIP/src/core/netif.c ****       etharp_cleanup_netif(netif);
 1469              		.loc 1 938 5 view .LVU418
 938:Middlewares/Third_Party/LwIP/src/core/netif.c ****       etharp_cleanup_netif(netif);
 1470              		.loc 1 938 8 is_stmt 0 view .LVU419
 1471 0016 13F0080F 		tst	r3, #8
 1472 001a 08D1     		bne	.L142
 1473              	.LVL139:
 1474              	.L137:
 949:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1475              		.loc 1 949 1 view .LVU420
 1476 001c 08BD     		pop	{r3, pc}
 1477              	.LVL140:
 1478              	.L141:
 923:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1479              		.loc 1 923 3 is_stmt 1 discriminator 1 view .LVU421
 923:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1480              		.loc 1 923 3 discriminator 1 view .LVU422
 1481 001e 054B     		ldr	r3, .L143
 1482 0020 40F29B32 		movw	r2, #923
 1483 0024 0449     		ldr	r1, .L143+4
 1484 0026 0548     		ldr	r0, .L143+8
 1485              	.LVL141:
 923:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1486              		.loc 1 923 3 is_stmt 0 discriminator 1 view .LVU423
 1487 0028 FFF7FEFF 		bl	printf
 1488              	.LVL142:
 923:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1489              		.loc 1 923 3 is_stmt 1 discriminator 1 view .LVU424
 923:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1490              		.loc 1 923 3 discriminator 1 view .LVU425
 923:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1491              		.loc 1 923 3 is_stmt 0 view .LVU426
 1492 002c F6E7     		b	.L137
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 63


 1493              	.LVL143:
 1494              	.L142:
 939:Middlewares/Third_Party/LwIP/src/core/netif.c ****     }
 1495              		.loc 1 939 7 is_stmt 1 view .LVU427
 1496 002e FFF7FEFF 		bl	etharp_cleanup_netif
 1497              	.LVL144:
 947:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
 1498              		.loc 1 947 33 view .LVU428
 1499 0032 F3E7     		b	.L137
 1500              	.L144:
 1501              		.align	2
 1502              	.L143:
 1503 0034 00000000 		.word	.LC0
 1504 0038 00000000 		.word	.LC14
 1505 003c 54000000 		.word	.LC2
 1506              		.cfi_endproc
 1507              	.LFE190:
 1509              		.section	.text.netif_remove,"ax",%progbits
 1510              		.align	1
 1511              		.global	netif_remove
 1512              		.syntax unified
 1513              		.thumb
 1514              		.thumb_func
 1516              	netif_remove:
 1517              	.LVL145:
 1518              	.LFB186:
 738:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_IPV6
 1519              		.loc 1 738 1 view -0
 1520              		.cfi_startproc
 1521              		@ args = 0, pretend = 0, frame = 0
 1522              		@ frame_needed = 0, uses_anonymous_args = 0
 743:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1523              		.loc 1 743 28 view .LVU430
 745:Middlewares/Third_Party/LwIP/src/core/netif.c ****     return;
 1524              		.loc 1 745 3 view .LVU431
 745:Middlewares/Third_Party/LwIP/src/core/netif.c ****     return;
 1525              		.loc 1 745 6 is_stmt 0 view .LVU432
 1526 0000 50B3     		cbz	r0, .L154
 738:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_IPV6
 1527              		.loc 1 738 1 view .LVU433
 1528 0002 10B5     		push	{r4, lr}
 1529              	.LCFI23:
 1530              		.cfi_def_cfa_offset 8
 1531              		.cfi_offset 4, -8
 1532              		.cfi_offset 14, -4
 1533 0004 0446     		mov	r4, r0
 749:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1534              		.loc 1 749 65 is_stmt 1 view .LVU434
 752:Middlewares/Third_Party/LwIP/src/core/netif.c ****     netif_do_ip_addr_changed(netif_ip_addr4(netif), NULL);
 1535              		.loc 1 752 3 view .LVU435
 752:Middlewares/Third_Party/LwIP/src/core/netif.c ****     netif_do_ip_addr_changed(netif_ip_addr4(netif), NULL);
 1536              		.loc 1 752 8 is_stmt 0 view .LVU436
 1537 0006 4368     		ldr	r3, [r0, #4]
 752:Middlewares/Third_Party/LwIP/src/core/netif.c ****     netif_do_ip_addr_changed(netif_ip_addr4(netif), NULL);
 1538              		.loc 1 752 6 view .LVU437
 1539 0008 93B9     		cbnz	r3, .L157
 1540              	.LVL146:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 64


 1541              	.L147:
 775:Middlewares/Third_Party/LwIP/src/core/netif.c ****     /* set netif down before removing (call callback function) */
 1542              		.loc 1 775 3 is_stmt 1 view .LVU438
 775:Middlewares/Third_Party/LwIP/src/core/netif.c ****     /* set netif down before removing (call callback function) */
 1543              		.loc 1 775 7 is_stmt 0 view .LVU439
 1544 000a 94F82D30 		ldrb	r3, [r4, #45]	@ zero_extendqisi2
 775:Middlewares/Third_Party/LwIP/src/core/netif.c ****     /* set netif down before removing (call callback function) */
 1545              		.loc 1 775 6 view .LVU440
 1546 000e 13F0010F 		tst	r3, #1
 1547 0012 12D1     		bne	.L158
 1548              	.L148:
 780:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1549              		.loc 1 780 25 is_stmt 1 view .LVU441
 783:Middlewares/Third_Party/LwIP/src/core/netif.c ****     /* reset default netif */
 1550              		.loc 1 783 3 view .LVU442
 783:Middlewares/Third_Party/LwIP/src/core/netif.c ****     /* reset default netif */
 1551              		.loc 1 783 21 is_stmt 0 view .LVU443
 1552 0014 114B     		ldr	r3, .L162
 1553 0016 1B68     		ldr	r3, [r3]
 783:Middlewares/Third_Party/LwIP/src/core/netif.c ****     /* reset default netif */
 1554              		.loc 1 783 6 view .LVU444
 1555 0018 A342     		cmp	r3, r4
 1556 001a 12D0     		beq	.L159
 1557              	.L149:
 789:Middlewares/Third_Party/LwIP/src/core/netif.c ****     netif_list = netif->next;
 1558              		.loc 1 789 3 is_stmt 1 view .LVU445
 789:Middlewares/Third_Party/LwIP/src/core/netif.c ****     netif_list = netif->next;
 1559              		.loc 1 789 18 is_stmt 0 view .LVU446
 1560 001c 104B     		ldr	r3, .L162+4
 1561 001e 1B68     		ldr	r3, [r3]
 789:Middlewares/Third_Party/LwIP/src/core/netif.c ****     netif_list = netif->next;
 1562              		.loc 1 789 6 view .LVU447
 1563 0020 A342     		cmp	r3, r4
 1564 0022 12D0     		beq	.L160
 1565              	.L150:
 1566              	.LVL147:
 1567              	.LBB4:
 794:Middlewares/Third_Party/LwIP/src/core/netif.c ****       if (tmp_netif->next == netif) {
 1568              		.loc 1 794 5 is_stmt 1 discriminator 1 view .LVU448
 1569 0024 BBB1     		cbz	r3, .L145
 795:Middlewares/Third_Party/LwIP/src/core/netif.c ****         tmp_netif->next = netif->next;
 1570              		.loc 1 795 7 view .LVU449
 795:Middlewares/Third_Party/LwIP/src/core/netif.c ****         tmp_netif->next = netif->next;
 1571              		.loc 1 795 20 is_stmt 0 view .LVU450
 1572 0026 1A68     		ldr	r2, [r3]
 795:Middlewares/Third_Party/LwIP/src/core/netif.c ****         tmp_netif->next = netif->next;
 1573              		.loc 1 795 10 view .LVU451
 1574 0028 A242     		cmp	r2, r4
 1575 002a 12D0     		beq	.L161
 794:Middlewares/Third_Party/LwIP/src/core/netif.c ****       if (tmp_netif->next == netif) {
 1576              		.loc 1 794 5 discriminator 2 view .LVU452
 1577 002c 1346     		mov	r3, r2
 1578              	.LVL148:
 794:Middlewares/Third_Party/LwIP/src/core/netif.c ****       if (tmp_netif->next == netif) {
 1579              		.loc 1 794 5 discriminator 2 view .LVU453
 1580 002e F9E7     		b	.L150
 1581              	.LVL149:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 65


 1582              	.L157:
 794:Middlewares/Third_Party/LwIP/src/core/netif.c ****       if (tmp_netif->next == netif) {
 1583              		.loc 1 794 5 discriminator 2 view .LVU454
 1584              	.LBE4:
 753:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
 1585              		.loc 1 753 5 is_stmt 1 view .LVU455
 1586 0030 0021     		movs	r1, #0
 1587 0032 0430     		adds	r0, r0, #4
 1588              	.LVL150:
 753:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
 1589              		.loc 1 753 5 is_stmt 0 view .LVU456
 1590 0034 FFF7FEFF 		bl	netif_do_ip_addr_changed
 1591              	.LVL151:
 1592 0038 E7E7     		b	.L147
 1593              	.L158:
 777:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
 1594              		.loc 1 777 5 is_stmt 1 view .LVU457
 1595 003a 2046     		mov	r0, r4
 1596 003c FFF7FEFF 		bl	netif_set_down
 1597              	.LVL152:
 1598 0040 E8E7     		b	.L148
 1599              	.L159:
 785:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
 1600              		.loc 1 785 5 view .LVU458
 1601 0042 0020     		movs	r0, #0
 1602 0044 FFF7FEFF 		bl	netif_set_default
 1603              	.LVL153:
 1604 0048 E8E7     		b	.L149
 1605              	.L160:
 790:Middlewares/Third_Party/LwIP/src/core/netif.c ****   } else {
 1606              		.loc 1 790 5 view .LVU459
 790:Middlewares/Third_Party/LwIP/src/core/netif.c ****   } else {
 1607              		.loc 1 790 23 is_stmt 0 view .LVU460
 1608 004a 2268     		ldr	r2, [r4]
 790:Middlewares/Third_Party/LwIP/src/core/netif.c ****   } else {
 1609              		.loc 1 790 16 view .LVU461
 1610 004c 044B     		ldr	r3, .L162+4
 1611 004e 1A60     		str	r2, [r3]
 1612 0050 01E0     		b	.L145
 1613              	.LVL154:
 1614              	.L161:
 1615              	.LBB5:
 796:Middlewares/Third_Party/LwIP/src/core/netif.c ****         break;
 1616              		.loc 1 796 9 is_stmt 1 view .LVU462
 796:Middlewares/Third_Party/LwIP/src/core/netif.c ****         break;
 1617              		.loc 1 796 32 is_stmt 0 view .LVU463
 1618 0052 2268     		ldr	r2, [r4]
 796:Middlewares/Third_Party/LwIP/src/core/netif.c ****         break;
 1619              		.loc 1 796 25 view .LVU464
 1620 0054 1A60     		str	r2, [r3]
 797:Middlewares/Third_Party/LwIP/src/core/netif.c ****       }
 1621              		.loc 1 797 9 is_stmt 1 view .LVU465
 800:Middlewares/Third_Party/LwIP/src/core/netif.c ****       return; /* netif is not on the list */
 1622              		.loc 1 800 5 view .LVU466
 1623              	.LVL155:
 1624              	.L145:
 800:Middlewares/Third_Party/LwIP/src/core/netif.c ****       return; /* netif is not on the list */
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 66


 1625              		.loc 1 800 5 is_stmt 0 view .LVU467
 1626              	.LBE5:
 812:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1627              		.loc 1 812 1 view .LVU468
 1628 0056 10BD     		pop	{r4, pc}
 1629              	.LVL156:
 1630              	.L154:
 1631              	.LCFI24:
 1632              		.cfi_def_cfa_offset 0
 1633              		.cfi_restore 4
 1634              		.cfi_restore 14
 812:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1635              		.loc 1 812 1 view .LVU469
 1636 0058 7047     		bx	lr
 1637              	.L163:
 1638 005a 00BF     		.align	2
 1639              	.L162:
 1640 005c 00000000 		.word	netif_default
 1641 0060 00000000 		.word	netif_list
 1642              		.cfi_endproc
 1643              	.LFE186:
 1645              		.section	.rodata.netif_set_link_up.str1.4,"aMS",%progbits,1
 1646              		.align	2
 1647              	.LC15:
 1648 0000 6E657469 		.ascii	"netif_set_link_up: invalid netif\000"
 1648      665F7365 
 1648      745F6C69 
 1648      6E6B5F75 
 1648      703A2069 
 1649              		.section	.text.netif_set_link_up,"ax",%progbits
 1650              		.align	1
 1651              		.global	netif_set_link_up
 1652              		.syntax unified
 1653              		.thumb
 1654              		.thumb_func
 1656              	netif_set_link_up:
 1657              	.LVL157:
 1658              	.LFB191:
 989:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT_CORE_LOCKED();
 1659              		.loc 1 989 1 is_stmt 1 view -0
 1660              		.cfi_startproc
 1661              		@ args = 0, pretend = 0, frame = 0
 1662              		@ frame_needed = 0, uses_anonymous_args = 0
 989:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT_CORE_LOCKED();
 1663              		.loc 1 989 1 is_stmt 0 view .LVU471
 1664 0000 10B5     		push	{r4, lr}
 1665              	.LCFI25:
 1666              		.cfi_def_cfa_offset 8
 1667              		.cfi_offset 4, -8
 1668              		.cfi_offset 14, -4
 990:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1669              		.loc 1 990 28 is_stmt 1 view .LVU472
 992:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1670              		.loc 1 992 3 view .LVU473
 992:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1671              		.loc 1 992 3 view .LVU474
 1672 0002 30B1     		cbz	r0, .L168
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 67


 1673 0004 0446     		mov	r4, r0
 992:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1674              		.loc 1 992 3 discriminator 2 view .LVU475
 994:Middlewares/Third_Party/LwIP/src/core/netif.c ****     netif_set_flags(netif, NETIF_FLAG_LINK_UP);
 1675              		.loc 1 994 3 view .LVU476
 994:Middlewares/Third_Party/LwIP/src/core/netif.c ****     netif_set_flags(netif, NETIF_FLAG_LINK_UP);
 1676              		.loc 1 994 14 is_stmt 0 view .LVU477
 1677 0006 90F82D30 		ldrb	r3, [r0, #45]	@ zero_extendqisi2
 994:Middlewares/Third_Party/LwIP/src/core/netif.c ****     netif_set_flags(netif, NETIF_FLAG_LINK_UP);
 1678              		.loc 1 994 6 view .LVU478
 1679 000a 13F0040F 		tst	r3, #4
 1680 000e 08D0     		beq	.L169
 1681              	.LVL158:
 1682              	.L164:
1019:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1683              		.loc 1 1019 1 view .LVU479
 1684 0010 10BD     		pop	{r4, pc}
 1685              	.LVL159:
 1686              	.L168:
 992:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1687              		.loc 1 992 3 is_stmt 1 discriminator 1 view .LVU480
 992:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1688              		.loc 1 992 3 discriminator 1 view .LVU481
 1689 0012 0A4B     		ldr	r3, .L170
 1690 0014 4FF47872 		mov	r2, #992
 1691 0018 0949     		ldr	r1, .L170+4
 1692 001a 0A48     		ldr	r0, .L170+8
 1693              	.LVL160:
 992:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1694              		.loc 1 992 3 is_stmt 0 discriminator 1 view .LVU482
 1695 001c FFF7FEFF 		bl	printf
 1696              	.LVL161:
 992:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1697              		.loc 1 992 3 is_stmt 1 discriminator 1 view .LVU483
 992:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1698              		.loc 1 992 3 discriminator 1 view .LVU484
 992:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1699              		.loc 1 992 3 is_stmt 0 view .LVU485
 1700 0020 F6E7     		b	.L164
 1701              	.LVL162:
 1702              	.L169:
 995:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1703              		.loc 1 995 5 is_stmt 1 view .LVU486
 995:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1704              		.loc 1 995 5 view .LVU487
 1705 0022 43F00403 		orr	r3, r3, #4
 1706 0026 80F82D30 		strb	r3, [r0, #45]
 995:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1707              		.loc 1 995 5 view .LVU488
1005:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_IPV6
 1708              		.loc 1 1005 5 view .LVU489
 1709 002a 0321     		movs	r1, #3
 1710 002c FFF7FEFF 		bl	netif_issue_reports
 1711              	.LVL163:
1010:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_NETIF_EXT_STATUS_CALLBACK
 1712              		.loc 1 1010 5 view .LVU490
1010:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_NETIF_EXT_STATUS_CALLBACK
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 68


 1713              		.loc 1 1010 5 view .LVU491
 1714 0030 E369     		ldr	r3, [r4, #28]
 1715 0032 002B     		cmp	r3, #0
 1716 0034 ECD0     		beq	.L164
1010:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_NETIF_EXT_STATUS_CALLBACK
 1717              		.loc 1 1010 5 discriminator 1 view .LVU492
 1718 0036 2046     		mov	r0, r4
 1719 0038 9847     		blx	r3
 1720              	.LVL164:
1010:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_NETIF_EXT_STATUS_CALLBACK
 1721              		.loc 1 1010 5 discriminator 3 view .LVU493
 1722 003a E9E7     		b	.L164
 1723              	.L171:
 1724              		.align	2
 1725              	.L170:
 1726 003c 00000000 		.word	.LC0
 1727 0040 00000000 		.word	.LC15
 1728 0044 54000000 		.word	.LC2
 1729              		.cfi_endproc
 1730              	.LFE191:
 1732              		.section	.rodata.netif_set_link_down.str1.4,"aMS",%progbits,1
 1733              		.align	2
 1734              	.LC16:
 1735 0000 6E657469 		.ascii	"netif_set_link_down: invalid netif\000"
 1735      665F7365 
 1735      745F6C69 
 1735      6E6B5F64 
 1735      6F776E3A 
 1736              		.section	.text.netif_set_link_down,"ax",%progbits
 1737              		.align	1
 1738              		.global	netif_set_link_down
 1739              		.syntax unified
 1740              		.thumb
 1741              		.thumb_func
 1743              	netif_set_link_down:
 1744              	.LVL165:
 1745              	.LFB192:
1027:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT_CORE_LOCKED();
 1746              		.loc 1 1027 1 view -0
 1747              		.cfi_startproc
 1748              		@ args = 0, pretend = 0, frame = 0
 1749              		@ frame_needed = 0, uses_anonymous_args = 0
1027:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT_CORE_LOCKED();
 1750              		.loc 1 1027 1 is_stmt 0 view .LVU495
 1751 0000 08B5     		push	{r3, lr}
 1752              	.LCFI26:
 1753              		.cfi_def_cfa_offset 8
 1754              		.cfi_offset 3, -8
 1755              		.cfi_offset 14, -4
1028:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1756              		.loc 1 1028 28 is_stmt 1 view .LVU496
1030:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1757              		.loc 1 1030 3 view .LVU497
1030:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1758              		.loc 1 1030 3 view .LVU498
 1759 0002 60B1     		cbz	r0, .L176
1030:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 69


 1760              		.loc 1 1030 3 discriminator 2 view .LVU499
1032:Middlewares/Third_Party/LwIP/src/core/netif.c ****     netif_clear_flags(netif, NETIF_FLAG_LINK_UP);
 1761              		.loc 1 1032 3 view .LVU500
1032:Middlewares/Third_Party/LwIP/src/core/netif.c ****     netif_clear_flags(netif, NETIF_FLAG_LINK_UP);
 1762              		.loc 1 1032 12 is_stmt 0 view .LVU501
 1763 0004 90F82D30 		ldrb	r3, [r0, #45]	@ zero_extendqisi2
1032:Middlewares/Third_Party/LwIP/src/core/netif.c ****     netif_clear_flags(netif, NETIF_FLAG_LINK_UP);
 1764              		.loc 1 1032 6 view .LVU502
 1765 0008 13F0040F 		tst	r3, #4
 1766 000c 06D0     		beq	.L172
1033:Middlewares/Third_Party/LwIP/src/core/netif.c ****     NETIF_LINK_CALLBACK(netif);
 1767              		.loc 1 1033 5 is_stmt 1 view .LVU503
1033:Middlewares/Third_Party/LwIP/src/core/netif.c ****     NETIF_LINK_CALLBACK(netif);
 1768              		.loc 1 1033 5 view .LVU504
 1769 000e 03F0FB03 		and	r3, r3, #251
 1770 0012 80F82D30 		strb	r3, [r0, #45]
1033:Middlewares/Third_Party/LwIP/src/core/netif.c ****     NETIF_LINK_CALLBACK(netif);
 1771              		.loc 1 1033 5 view .LVU505
1034:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_NETIF_EXT_STATUS_CALLBACK
 1772              		.loc 1 1034 5 view .LVU506
1034:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_NETIF_EXT_STATUS_CALLBACK
 1773              		.loc 1 1034 5 view .LVU507
 1774 0016 C369     		ldr	r3, [r0, #28]
 1775 0018 03B1     		cbz	r3, .L172
1034:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_NETIF_EXT_STATUS_CALLBACK
 1776              		.loc 1 1034 5 discriminator 1 view .LVU508
 1777 001a 9847     		blx	r3
 1778              	.LVL166:
1034:Middlewares/Third_Party/LwIP/src/core/netif.c **** #if LWIP_NETIF_EXT_STATUS_CALLBACK
 1779              		.loc 1 1034 5 discriminator 3 view .LVU509
 1780              	.L172:
1043:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1781              		.loc 1 1043 1 is_stmt 0 view .LVU510
 1782 001c 08BD     		pop	{r3, pc}
 1783              	.LVL167:
 1784              	.L176:
1030:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1785              		.loc 1 1030 3 is_stmt 1 discriminator 1 view .LVU511
1030:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1786              		.loc 1 1030 3 discriminator 1 view .LVU512
 1787 001e 044B     		ldr	r3, .L177
 1788 0020 40F20642 		movw	r2, #1030
 1789 0024 0349     		ldr	r1, .L177+4
 1790 0026 0448     		ldr	r0, .L177+8
 1791              	.LVL168:
1030:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1792              		.loc 1 1030 3 is_stmt 0 discriminator 1 view .LVU513
 1793 0028 FFF7FEFF 		bl	printf
 1794              	.LVL169:
1030:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1795              		.loc 1 1030 3 is_stmt 1 discriminator 1 view .LVU514
1030:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1796              		.loc 1 1030 3 discriminator 1 view .LVU515
1030:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1797              		.loc 1 1030 3 is_stmt 0 view .LVU516
 1798 002c F6E7     		b	.L172
 1799              	.L178:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 70


 1800 002e 00BF     		.align	2
 1801              	.L177:
 1802 0030 00000000 		.word	.LC0
 1803 0034 00000000 		.word	.LC16
 1804 0038 54000000 		.word	.LC2
 1805              		.cfi_endproc
 1806              	.LFE192:
 1808              		.section	.text.netif_set_link_callback,"ax",%progbits
 1809              		.align	1
 1810              		.global	netif_set_link_callback
 1811              		.syntax unified
 1812              		.thumb
 1813              		.thumb_func
 1815              	netif_set_link_callback:
 1816              	.LVL170:
 1817              	.LFB193:
1052:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT_CORE_LOCKED();
 1818              		.loc 1 1052 1 is_stmt 1 view -0
 1819              		.cfi_startproc
 1820              		@ args = 0, pretend = 0, frame = 0
 1821              		@ frame_needed = 0, uses_anonymous_args = 0
 1822              		@ link register save eliminated.
1053:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1823              		.loc 1 1053 28 view .LVU518
1055:Middlewares/Third_Party/LwIP/src/core/netif.c ****     netif->link_callback = link_callback;
 1824              		.loc 1 1055 3 view .LVU519
1055:Middlewares/Third_Party/LwIP/src/core/netif.c ****     netif->link_callback = link_callback;
 1825              		.loc 1 1055 6 is_stmt 0 view .LVU520
 1826 0000 00B1     		cbz	r0, .L179
1056:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
 1827              		.loc 1 1056 5 is_stmt 1 view .LVU521
1056:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
 1828              		.loc 1 1056 26 is_stmt 0 view .LVU522
 1829 0002 C161     		str	r1, [r0, #28]
 1830              	.L179:
1058:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_NETIF_LINK_CALLBACK */
 1831              		.loc 1 1058 1 view .LVU523
 1832 0004 7047     		bx	lr
 1833              		.cfi_endproc
 1834              	.LFE193:
 1836              		.section	.text.netif_get_by_index,"ax",%progbits
 1837              		.align	1
 1838              		.global	netif_get_by_index
 1839              		.syntax unified
 1840              		.thumb
 1841              		.thumb_func
 1843              	netif_get_by_index:
 1844              	.LVL171:
 1845              	.LFB197:
1627:Middlewares/Third_Party/LwIP/src/core/netif.c **** #endif /* LWIP_IPV4 */
1628:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1629:Middlewares/Third_Party/LwIP/src/core/netif.c **** /**
1630:Middlewares/Third_Party/LwIP/src/core/netif.c **** * @ingroup netif
1631:Middlewares/Third_Party/LwIP/src/core/netif.c **** * Return the interface index for the netif with name
1632:Middlewares/Third_Party/LwIP/src/core/netif.c **** * or NETIF_NO_INDEX if not found/on error
1633:Middlewares/Third_Party/LwIP/src/core/netif.c **** *
1634:Middlewares/Third_Party/LwIP/src/core/netif.c **** * @param name the name of the netif
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 71


1635:Middlewares/Third_Party/LwIP/src/core/netif.c **** */
1636:Middlewares/Third_Party/LwIP/src/core/netif.c **** u8_t
1637:Middlewares/Third_Party/LwIP/src/core/netif.c **** netif_name_to_index(const char *name)
1638:Middlewares/Third_Party/LwIP/src/core/netif.c **** {
1639:Middlewares/Third_Party/LwIP/src/core/netif.c ****   struct netif *netif = netif_find(name);
1640:Middlewares/Third_Party/LwIP/src/core/netif.c ****   if (netif != NULL) {
1641:Middlewares/Third_Party/LwIP/src/core/netif.c ****     return netif_get_index(netif);
1642:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
1643:Middlewares/Third_Party/LwIP/src/core/netif.c ****   /* No name found, return invalid index */
1644:Middlewares/Third_Party/LwIP/src/core/netif.c ****   return NETIF_NO_INDEX;
1645:Middlewares/Third_Party/LwIP/src/core/netif.c **** }
1646:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1647:Middlewares/Third_Party/LwIP/src/core/netif.c **** /**
1648:Middlewares/Third_Party/LwIP/src/core/netif.c **** * @ingroup netif
1649:Middlewares/Third_Party/LwIP/src/core/netif.c **** * Return the interface name for the netif matching index
1650:Middlewares/Third_Party/LwIP/src/core/netif.c **** * or NULL if not found/on error
1651:Middlewares/Third_Party/LwIP/src/core/netif.c **** *
1652:Middlewares/Third_Party/LwIP/src/core/netif.c **** * @param idx the interface index of the netif
1653:Middlewares/Third_Party/LwIP/src/core/netif.c **** * @param name char buffer of at least NETIF_NAMESIZE bytes
1654:Middlewares/Third_Party/LwIP/src/core/netif.c **** */
1655:Middlewares/Third_Party/LwIP/src/core/netif.c **** char *
1656:Middlewares/Third_Party/LwIP/src/core/netif.c **** netif_index_to_name(u8_t idx, char *name)
1657:Middlewares/Third_Party/LwIP/src/core/netif.c **** {
1658:Middlewares/Third_Party/LwIP/src/core/netif.c ****   struct netif *netif = netif_get_by_index(idx);
1659:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1660:Middlewares/Third_Party/LwIP/src/core/netif.c ****   if (netif != NULL) {
1661:Middlewares/Third_Party/LwIP/src/core/netif.c ****     name[0] = netif->name[0];
1662:Middlewares/Third_Party/LwIP/src/core/netif.c ****     name[1] = netif->name[1];
1663:Middlewares/Third_Party/LwIP/src/core/netif.c ****     lwip_itoa(&name[2], NETIF_NAMESIZE - 2, netif_index_to_num(idx));
1664:Middlewares/Third_Party/LwIP/src/core/netif.c ****     return name;
1665:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
1666:Middlewares/Third_Party/LwIP/src/core/netif.c ****   return NULL;
1667:Middlewares/Third_Party/LwIP/src/core/netif.c **** }
1668:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1669:Middlewares/Third_Party/LwIP/src/core/netif.c **** /**
1670:Middlewares/Third_Party/LwIP/src/core/netif.c **** * @ingroup netif
1671:Middlewares/Third_Party/LwIP/src/core/netif.c **** * Return the interface for the netif index
1672:Middlewares/Third_Party/LwIP/src/core/netif.c **** *
1673:Middlewares/Third_Party/LwIP/src/core/netif.c **** * @param idx index of netif to find
1674:Middlewares/Third_Party/LwIP/src/core/netif.c **** */
1675:Middlewares/Third_Party/LwIP/src/core/netif.c **** struct netif *
1676:Middlewares/Third_Party/LwIP/src/core/netif.c **** netif_get_by_index(u8_t idx)
1677:Middlewares/Third_Party/LwIP/src/core/netif.c **** {
 1846              		.loc 1 1677 1 is_stmt 1 view -0
 1847              		.cfi_startproc
 1848              		@ args = 0, pretend = 0, frame = 0
 1849              		@ frame_needed = 0, uses_anonymous_args = 0
 1850              		@ link register save eliminated.
1678:Middlewares/Third_Party/LwIP/src/core/netif.c ****   struct netif *netif;
 1851              		.loc 1 1678 3 view .LVU525
1679:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1680:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT_CORE_LOCKED();
 1852              		.loc 1 1680 28 view .LVU526
1681:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1682:Middlewares/Third_Party/LwIP/src/core/netif.c ****   if (idx != NETIF_NO_INDEX) {
 1853              		.loc 1 1682 3 view .LVU527
 1854              		.loc 1 1682 6 is_stmt 0 view .LVU528
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 72


 1855 0000 0246     		mov	r2, r0
 1856 0002 58B1     		cbz	r0, .L185
1683:Middlewares/Third_Party/LwIP/src/core/netif.c ****     NETIF_FOREACH(netif) {
 1857              		.loc 1 1683 5 is_stmt 1 view .LVU529
 1858 0004 064B     		ldr	r3, .L187
 1859 0006 1868     		ldr	r0, [r3]
 1860              	.LVL172:
 1861              	.L183:
 1862              		.loc 1 1683 5 discriminator 1 view .LVU530
 1863 0008 38B1     		cbz	r0, .L186
1684:Middlewares/Third_Party/LwIP/src/core/netif.c ****       if (idx == netif_get_index(netif)) {
 1864              		.loc 1 1684 7 view .LVU531
 1865              		.loc 1 1684 18 is_stmt 0 view .LVU532
 1866 000a 90F83030 		ldrb	r3, [r0, #48]	@ zero_extendqisi2
 1867 000e 0133     		adds	r3, r3, #1
 1868 0010 DBB2     		uxtb	r3, r3
 1869              		.loc 1 1684 10 view .LVU533
 1870 0012 9342     		cmp	r3, r2
 1871 0014 03D0     		beq	.L181
1683:Middlewares/Third_Party/LwIP/src/core/netif.c ****     NETIF_FOREACH(netif) {
 1872              		.loc 1 1683 5 is_stmt 1 discriminator 2 view .LVU534
 1873 0016 0068     		ldr	r0, [r0]
 1874              	.LVL173:
1683:Middlewares/Third_Party/LwIP/src/core/netif.c ****     NETIF_FOREACH(netif) {
 1875              		.loc 1 1683 5 is_stmt 0 discriminator 2 view .LVU535
 1876 0018 F6E7     		b	.L183
 1877              	.L186:
1683:Middlewares/Third_Party/LwIP/src/core/netif.c ****     NETIF_FOREACH(netif) {
 1878              		.loc 1 1683 5 discriminator 2 view .LVU536
 1879 001a 7047     		bx	lr
 1880              	.LVL174:
 1881              	.L185:
1685:Middlewares/Third_Party/LwIP/src/core/netif.c ****         return netif; /* found! */
1686:Middlewares/Third_Party/LwIP/src/core/netif.c ****       }
1687:Middlewares/Third_Party/LwIP/src/core/netif.c ****     }
1688:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
1689:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1690:Middlewares/Third_Party/LwIP/src/core/netif.c ****   return NULL;
 1882              		.loc 1 1690 10 view .LVU537
 1883 001c 0020     		movs	r0, #0
 1884              	.LVL175:
 1885              	.L181:
1691:Middlewares/Third_Party/LwIP/src/core/netif.c **** }
 1886              		.loc 1 1691 1 view .LVU538
 1887 001e 7047     		bx	lr
 1888              	.L188:
 1889              		.align	2
 1890              	.L187:
 1891 0020 00000000 		.word	netif_list
 1892              		.cfi_endproc
 1893              	.LFE197:
 1895              		.section	.text.netif_index_to_name,"ax",%progbits
 1896              		.align	1
 1897              		.global	netif_index_to_name
 1898              		.syntax unified
 1899              		.thumb
 1900              		.thumb_func
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 73


 1902              	netif_index_to_name:
 1903              	.LVL176:
 1904              	.LFB196:
1657:Middlewares/Third_Party/LwIP/src/core/netif.c ****   struct netif *netif = netif_get_by_index(idx);
 1905              		.loc 1 1657 1 is_stmt 1 view -0
 1906              		.cfi_startproc
 1907              		@ args = 0, pretend = 0, frame = 0
 1908              		@ frame_needed = 0, uses_anonymous_args = 0
1657:Middlewares/Third_Party/LwIP/src/core/netif.c ****   struct netif *netif = netif_get_by_index(idx);
 1909              		.loc 1 1657 1 is_stmt 0 view .LVU540
 1910 0000 38B5     		push	{r3, r4, r5, lr}
 1911              	.LCFI27:
 1912              		.cfi_def_cfa_offset 16
 1913              		.cfi_offset 3, -16
 1914              		.cfi_offset 4, -12
 1915              		.cfi_offset 5, -8
 1916              		.cfi_offset 14, -4
 1917 0002 0546     		mov	r5, r0
 1918 0004 0C46     		mov	r4, r1
1658:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1919              		.loc 1 1658 3 is_stmt 1 view .LVU541
1658:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1920              		.loc 1 1658 25 is_stmt 0 view .LVU542
 1921 0006 FFF7FEFF 		bl	netif_get_by_index
 1922              	.LVL177:
1660:Middlewares/Third_Party/LwIP/src/core/netif.c ****     name[0] = netif->name[0];
 1923              		.loc 1 1660 3 is_stmt 1 view .LVU543
1660:Middlewares/Third_Party/LwIP/src/core/netif.c ****     name[0] = netif->name[0];
 1924              		.loc 1 1660 6 is_stmt 0 view .LVU544
 1925 000a 0346     		mov	r3, r0
 1926 000c 58B1     		cbz	r0, .L189
1661:Middlewares/Third_Party/LwIP/src/core/netif.c ****     name[1] = netif->name[1];
 1927              		.loc 1 1661 5 is_stmt 1 view .LVU545
1661:Middlewares/Third_Party/LwIP/src/core/netif.c ****     name[1] = netif->name[1];
 1928              		.loc 1 1661 26 is_stmt 0 view .LVU546
 1929 000e 90F82E20 		ldrb	r2, [r0, #46]	@ zero_extendqisi2
1661:Middlewares/Third_Party/LwIP/src/core/netif.c ****     name[1] = netif->name[1];
 1930              		.loc 1 1661 13 view .LVU547
 1931 0012 2270     		strb	r2, [r4]
1662:Middlewares/Third_Party/LwIP/src/core/netif.c ****     lwip_itoa(&name[2], NETIF_NAMESIZE - 2, netif_index_to_num(idx));
 1932              		.loc 1 1662 5 is_stmt 1 view .LVU548
1662:Middlewares/Third_Party/LwIP/src/core/netif.c ****     lwip_itoa(&name[2], NETIF_NAMESIZE - 2, netif_index_to_num(idx));
 1933              		.loc 1 1662 26 is_stmt 0 view .LVU549
 1934 0014 90F82F30 		ldrb	r3, [r0, #47]	@ zero_extendqisi2
1662:Middlewares/Third_Party/LwIP/src/core/netif.c ****     lwip_itoa(&name[2], NETIF_NAMESIZE - 2, netif_index_to_num(idx));
 1935              		.loc 1 1662 13 view .LVU550
 1936 0018 6370     		strb	r3, [r4, #1]
1663:Middlewares/Third_Party/LwIP/src/core/netif.c ****     return name;
 1937              		.loc 1 1663 5 is_stmt 1 view .LVU551
 1938 001a 6A1E     		subs	r2, r5, #1
 1939 001c 0421     		movs	r1, #4
 1940 001e A01C     		adds	r0, r4, #2
 1941              	.LVL178:
1663:Middlewares/Third_Party/LwIP/src/core/netif.c ****     return name;
 1942              		.loc 1 1663 5 is_stmt 0 view .LVU552
 1943 0020 FFF7FEFF 		bl	lwip_itoa
 1944              	.LVL179:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 74


1664:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
 1945              		.loc 1 1664 5 is_stmt 1 view .LVU553
1664:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
 1946              		.loc 1 1664 12 is_stmt 0 view .LVU554
 1947 0024 2346     		mov	r3, r4
 1948              	.L189:
1667:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1949              		.loc 1 1667 1 view .LVU555
 1950 0026 1846     		mov	r0, r3
 1951 0028 38BD     		pop	{r3, r4, r5, pc}
1667:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 1952              		.loc 1 1667 1 view .LVU556
 1953              		.cfi_endproc
 1954              	.LFE196:
 1956              		.section	.text.netif_find,"ax",%progbits
 1957              		.align	1
 1958              		.global	netif_find
 1959              		.syntax unified
 1960              		.thumb
 1961              		.thumb_func
 1963              	netif_find:
 1964              	.LVL180:
 1965              	.LFB198:
1692:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1693:Middlewares/Third_Party/LwIP/src/core/netif.c **** /**
1694:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @ingroup netif
1695:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * Find a network interface by searching for its name
1696:Middlewares/Third_Party/LwIP/src/core/netif.c ****  *
1697:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * @param name the name of the netif (like netif->name) plus concatenated number
1698:Middlewares/Third_Party/LwIP/src/core/netif.c ****  * in ascii representation (e.g. 'en0')
1699:Middlewares/Third_Party/LwIP/src/core/netif.c ****  */
1700:Middlewares/Third_Party/LwIP/src/core/netif.c **** struct netif *
1701:Middlewares/Third_Party/LwIP/src/core/netif.c **** netif_find(const char *name)
1702:Middlewares/Third_Party/LwIP/src/core/netif.c **** {
 1966              		.loc 1 1702 1 is_stmt 1 view -0
 1967              		.cfi_startproc
 1968              		@ args = 0, pretend = 0, frame = 0
 1969              		@ frame_needed = 0, uses_anonymous_args = 0
 1970              		.loc 1 1702 1 is_stmt 0 view .LVU558
 1971 0000 10B5     		push	{r4, lr}
 1972              	.LCFI28:
 1973              		.cfi_def_cfa_offset 8
 1974              		.cfi_offset 4, -8
 1975              		.cfi_offset 14, -4
1703:Middlewares/Third_Party/LwIP/src/core/netif.c ****   struct netif *netif;
 1976              		.loc 1 1703 3 is_stmt 1 view .LVU559
1704:Middlewares/Third_Party/LwIP/src/core/netif.c ****   u8_t num;
 1977              		.loc 1 1704 3 view .LVU560
1705:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1706:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_ASSERT_CORE_LOCKED();
 1978              		.loc 1 1706 28 view .LVU561
1707:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1708:Middlewares/Third_Party/LwIP/src/core/netif.c ****   if (name == NULL) {
 1979              		.loc 1 1708 3 view .LVU562
 1980              		.loc 1 1708 6 is_stmt 0 view .LVU563
 1981 0002 0446     		mov	r4, r0
 1982 0004 B0B1     		cbz	r0, .L192
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 75


1709:Middlewares/Third_Party/LwIP/src/core/netif.c ****     return NULL;
1710:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
1711:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1712:Middlewares/Third_Party/LwIP/src/core/netif.c ****   num = (u8_t)atoi(&name[2]);
 1983              		.loc 1 1712 3 is_stmt 1 view .LVU564
 1984              		.loc 1 1712 15 is_stmt 0 view .LVU565
 1985 0006 0230     		adds	r0, r0, #2
 1986              	.LVL181:
 1987              		.loc 1 1712 15 view .LVU566
 1988 0008 FFF7FEFF 		bl	atoi
 1989              	.LVL182:
 1990              		.loc 1 1712 7 discriminator 1 view .LVU567
 1991 000c C2B2     		uxtb	r2, r0
 1992              	.LVL183:
1713:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
1714:Middlewares/Third_Party/LwIP/src/core/netif.c ****   NETIF_FOREACH(netif) {
 1993              		.loc 1 1714 3 is_stmt 1 view .LVU568
 1994 000e 0A4B     		ldr	r3, .L199
 1995 0010 1868     		ldr	r0, [r3]
 1996              	.LVL184:
 1997              		.loc 1 1714 3 is_stmt 0 view .LVU569
 1998 0012 00E0     		b	.L194
 1999              	.L195:
 2000              		.loc 1 1714 3 is_stmt 1 discriminator 2 view .LVU570
 2001 0014 0068     		ldr	r0, [r0]
 2002              	.LVL185:
 2003              	.L194:
 2004              		.loc 1 1714 3 discriminator 1 view .LVU571
 2005 0016 68B1     		cbz	r0, .L192
1715:Middlewares/Third_Party/LwIP/src/core/netif.c ****     if (num == netif->num &&
 2006              		.loc 1 1715 5 view .LVU572
 2007              		.loc 1 1715 21 is_stmt 0 view .LVU573
 2008 0018 90F83030 		ldrb	r3, [r0, #48]	@ zero_extendqisi2
 2009              		.loc 1 1715 8 view .LVU574
 2010 001c 9342     		cmp	r3, r2
 2011 001e F9D1     		bne	.L195
1716:Middlewares/Third_Party/LwIP/src/core/netif.c ****         name[0] == netif->name[0] &&
 2012              		.loc 1 1716 13 view .LVU575
 2013 0020 2178     		ldrb	r1, [r4]	@ zero_extendqisi2
 2014              		.loc 1 1716 31 view .LVU576
 2015 0022 90F82E30 		ldrb	r3, [r0, #46]	@ zero_extendqisi2
1715:Middlewares/Third_Party/LwIP/src/core/netif.c ****     if (num == netif->num &&
 2016              		.loc 1 1715 27 discriminator 1 view .LVU577
 2017 0026 9942     		cmp	r1, r3
 2018 0028 F4D1     		bne	.L195
1717:Middlewares/Third_Party/LwIP/src/core/netif.c ****         name[1] == netif->name[1]) {
 2019              		.loc 1 1717 13 view .LVU578
 2020 002a 6178     		ldrb	r1, [r4, #1]	@ zero_extendqisi2
 2021              		.loc 1 1717 31 view .LVU579
 2022 002c 90F82F30 		ldrb	r3, [r0, #47]	@ zero_extendqisi2
1716:Middlewares/Third_Party/LwIP/src/core/netif.c ****         name[0] == netif->name[0] &&
 2023              		.loc 1 1716 35 view .LVU580
 2024 0030 9942     		cmp	r1, r3
 2025 0032 EFD1     		bne	.L195
 2026              	.LVL186:
 2027              	.L192:
1718:Middlewares/Third_Party/LwIP/src/core/netif.c ****       LWIP_DEBUGF(NETIF_DEBUG, ("netif_find: found %c%c\n", name[0], name[1]));
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 76


1719:Middlewares/Third_Party/LwIP/src/core/netif.c ****       return netif;
1720:Middlewares/Third_Party/LwIP/src/core/netif.c ****     }
1721:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
1722:Middlewares/Third_Party/LwIP/src/core/netif.c ****   LWIP_DEBUGF(NETIF_DEBUG, ("netif_find: didn't find %c%c\n", name[0], name[1]));
1723:Middlewares/Third_Party/LwIP/src/core/netif.c ****   return NULL;
1724:Middlewares/Third_Party/LwIP/src/core/netif.c **** }
 2028              		.loc 1 1724 1 view .LVU581
 2029 0034 10BD     		pop	{r4, pc}
 2030              	.LVL187:
 2031              	.L200:
 2032              		.loc 1 1724 1 view .LVU582
 2033 0036 00BF     		.align	2
 2034              	.L199:
 2035 0038 00000000 		.word	netif_list
 2036              		.cfi_endproc
 2037              	.LFE198:
 2039              		.section	.text.netif_name_to_index,"ax",%progbits
 2040              		.align	1
 2041              		.global	netif_name_to_index
 2042              		.syntax unified
 2043              		.thumb
 2044              		.thumb_func
 2046              	netif_name_to_index:
 2047              	.LVL188:
 2048              	.LFB195:
1638:Middlewares/Third_Party/LwIP/src/core/netif.c ****   struct netif *netif = netif_find(name);
 2049              		.loc 1 1638 1 is_stmt 1 view -0
 2050              		.cfi_startproc
 2051              		@ args = 0, pretend = 0, frame = 0
 2052              		@ frame_needed = 0, uses_anonymous_args = 0
1638:Middlewares/Third_Party/LwIP/src/core/netif.c ****   struct netif *netif = netif_find(name);
 2053              		.loc 1 1638 1 is_stmt 0 view .LVU584
 2054 0000 08B5     		push	{r3, lr}
 2055              	.LCFI29:
 2056              		.cfi_def_cfa_offset 8
 2057              		.cfi_offset 3, -8
 2058              		.cfi_offset 14, -4
1639:Middlewares/Third_Party/LwIP/src/core/netif.c ****   if (netif != NULL) {
 2059              		.loc 1 1639 3 is_stmt 1 view .LVU585
1639:Middlewares/Third_Party/LwIP/src/core/netif.c ****   if (netif != NULL) {
 2060              		.loc 1 1639 25 is_stmt 0 view .LVU586
 2061 0002 FFF7FEFF 		bl	netif_find
 2062              	.LVL189:
1640:Middlewares/Third_Party/LwIP/src/core/netif.c ****     return netif_get_index(netif);
 2063              		.loc 1 1640 3 is_stmt 1 view .LVU587
1640:Middlewares/Third_Party/LwIP/src/core/netif.c ****     return netif_get_index(netif);
 2064              		.loc 1 1640 6 is_stmt 0 view .LVU588
 2065 0006 20B1     		cbz	r0, .L203
1641:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
 2066              		.loc 1 1641 5 is_stmt 1 view .LVU589
1641:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
 2067              		.loc 1 1641 12 is_stmt 0 view .LVU590
 2068 0008 90F83000 		ldrb	r0, [r0, #48]	@ zero_extendqisi2
 2069              	.LVL190:
1641:Middlewares/Third_Party/LwIP/src/core/netif.c ****   }
 2070              		.loc 1 1641 12 view .LVU591
 2071 000c 0130     		adds	r0, r0, #1
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 77


 2072 000e C0B2     		uxtb	r0, r0
 2073              	.L202:
1645:Middlewares/Third_Party/LwIP/src/core/netif.c **** 
 2074              		.loc 1 1645 1 view .LVU592
 2075 0010 08BD     		pop	{r3, pc}
 2076              	.LVL191:
 2077              	.L203:
1644:Middlewares/Third_Party/LwIP/src/core/netif.c **** }
 2078              		.loc 1 1644 10 view .LVU593
 2079 0012 0020     		movs	r0, #0
 2080              	.LVL192:
1644:Middlewares/Third_Party/LwIP/src/core/netif.c **** }
 2081              		.loc 1 1644 10 view .LVU594
 2082 0014 FCE7     		b	.L202
 2083              		.cfi_endproc
 2084              	.LFE195:
 2086              		.section	.bss.netif_num,"aw",%nobits
 2089              	netif_num:
 2090 0000 00       		.space	1
 2091              		.global	netif_default
 2092              		.section	.bss.netif_default,"aw",%nobits
 2093              		.align	2
 2096              	netif_default:
 2097 0000 00000000 		.space	4
 2098              		.global	netif_list
 2099              		.section	.bss.netif_list,"aw",%nobits
 2100              		.align	2
 2103              	netif_list:
 2104 0000 00000000 		.space	4
 2105              		.text
 2106              	.Letext0:
 2107              		.file 2 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\../lib/gcc/arm-none-eabi/13.3.1/..
 2108              		.file 3 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\../lib/gcc/arm-none-eabi/13.3.1/..
 2109              		.file 4 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\../lib/gcc/arm-none-eabi/13.3.1/in
 2110              		.file 5 "Middlewares/Third_Party/LwIP/src/include/lwip/arch.h"
 2111              		.file 6 "Middlewares/Third_Party/LwIP/src/include/lwip/ip4_addr.h"
 2112              		.file 7 "Middlewares/Third_Party/LwIP/src/include/lwip/ip_addr.h"
 2113              		.file 8 "Middlewares/Third_Party/LwIP/src/include/lwip/err.h"
 2114              		.file 9 "Middlewares/Third_Party/LwIP/src/include/lwip/pbuf.h"
 2115              		.file 10 "Middlewares/Third_Party/LwIP/src/include/lwip/memp.h"
 2116              		.file 11 "Middlewares/Third_Party/LwIP/src/include/lwip/netif.h"
 2117              		.file 12 "Middlewares/Third_Party/LwIP/src/include/lwip/def.h"
 2118              		.file 13 "Middlewares/Third_Party/LwIP/src/include/lwip/etharp.h"
 2119              		.file 14 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\../lib/gcc/arm-none-eabi/13.3.1/.
 2120              		.file 15 "Middlewares/Third_Party/LwIP/src/include/lwip/udp.h"
 2121              		.file 16 "Middlewares/Third_Party/LwIP/src/include/lwip/priv/tcp_priv.h"
 2122              		.file 17 "Middlewares/Third_Party/LwIP/src/include/lwip/ip4.h"
 2123              		.file 18 "Middlewares/Third_Party/LwIP/src/include/netif/ethernet.h"
 2124              		.file 19 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\../lib/gcc/arm-none-eabi/13.3.1/.
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 78


DEFINED SYMBOLS
                            *ABS*:00000000 netif.c
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:20     .text.netif_do_set_netmask:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:25     .text.netif_do_set_netmask:00000000 netif_do_set_netmask
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:66     .text.netif_do_set_gw:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:71     .text.netif_do_set_gw:00000000 netif_do_set_gw
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:110    .text.netif_null_output_ip4:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:115    .text.netif_null_output_ip4:00000000 netif_null_output_ip4
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:136    .text.netif_do_ip_addr_changed:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:141    .text.netif_do_ip_addr_changed:00000000 netif_do_ip_addr_changed
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:173    .rodata.netif_issue_reports.str1.4:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:183    .text.netif_issue_reports:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:188    .text.netif_issue_reports:00000000 netif_issue_reports
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:259    .text.netif_issue_reports:00000040 $d
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:266    .rodata.netif_do_set_ipaddr.str1.4:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:270    .text.netif_do_set_ipaddr:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:275    .text.netif_do_set_ipaddr:00000000 netif_do_set_ipaddr
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:399    .text.netif_do_set_ipaddr:00000060 $d
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:407    .text.netif_init:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:413    .text.netif_init:00000000 netif_init
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:426    .rodata.netif_input.str1.4:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:433    .text.netif_input:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:439    .text.netif_input:00000000 netif_input
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:521    .text.netif_input:00000044 $d
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:529    .rodata.netif_set_ipaddr.str1.4:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:533    .text.netif_set_ipaddr:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:539    .text.netif_set_ipaddr:00000000 netif_set_ipaddr
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:612    .text.netif_set_ipaddr:0000002c $d
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:620    .rodata.netif_set_netmask.str1.4:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:624    .text.netif_set_netmask:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:630    .text.netif_set_netmask:00000000 netif_set_netmask
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:696    .text.netif_set_netmask:00000028 $d
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:704    .rodata.netif_set_gw.str1.4:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:708    .text.netif_set_gw:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:714    .text.netif_set_gw:00000000 netif_set_gw
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:780    .text.netif_set_gw:00000028 $d
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:788    .text.netif_set_addr:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:794    .text.netif_set_addr:00000000 netif_set_addr
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:923    .text.netif_set_addr:00000068 $d
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:928    .rodata.netif_add.str1.4:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:941    .text.netif_add:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:947    .text.netif_add:00000000 netif_add
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:1256   .text.netif_add:000000fc $d
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:2089   .bss.netif_num:00000000 netif_num
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:2103   .bss.netif_list:00000000 netif_list
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:1270   .text.netif_add_noaddr:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:1276   .text.netif_add_noaddr:00000000 netif_add_noaddr
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:1317   .text.netif_set_default:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:1323   .text.netif_set_default:00000000 netif_set_default
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:1345   .text.netif_set_default:00000008 $d
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:2096   .bss.netif_default:00000000 netif_default
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:1350   .rodata.netif_set_up.str1.4:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:1354   .text.netif_set_up:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:1360   .text.netif_set_up:00000000 netif_set_up
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:1422   .text.netif_set_up:00000030 $d
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:1429   .rodata.netif_set_down.str1.4:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:1433   .text.netif_set_down:00000000 $t
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s 			page 79


C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:1439   .text.netif_set_down:00000000 netif_set_down
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:1503   .text.netif_set_down:00000034 $d
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:1510   .text.netif_remove:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:1516   .text.netif_remove:00000000 netif_remove
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:1640   .text.netif_remove:0000005c $d
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:1646   .rodata.netif_set_link_up.str1.4:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:1650   .text.netif_set_link_up:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:1656   .text.netif_set_link_up:00000000 netif_set_link_up
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:1726   .text.netif_set_link_up:0000003c $d
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:1733   .rodata.netif_set_link_down.str1.4:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:1737   .text.netif_set_link_down:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:1743   .text.netif_set_link_down:00000000 netif_set_link_down
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:1802   .text.netif_set_link_down:00000030 $d
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:1809   .text.netif_set_link_callback:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:1815   .text.netif_set_link_callback:00000000 netif_set_link_callback
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:1837   .text.netif_get_by_index:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:1843   .text.netif_get_by_index:00000000 netif_get_by_index
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:1891   .text.netif_get_by_index:00000020 $d
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:1896   .text.netif_index_to_name:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:1902   .text.netif_index_to_name:00000000 netif_index_to_name
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:1957   .text.netif_find:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:1963   .text.netif_find:00000000 netif_find
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:2035   .text.netif_find:00000038 $d
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:2040   .text.netif_name_to_index:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:2046   .text.netif_name_to_index:00000000 netif_name_to_index
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:2090   .bss.netif_num:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:2093   .bss.netif_default:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\ccF8bmpP.s:2100   .bss.netif_list:00000000 $d

UNDEFINED SYMBOLS
tcp_netif_ip_addr_changed
udp_netif_ip_addr_changed
printf
etharp_request
ethernet_input
ip4_input
ip_addr_any
etharp_cleanup_netif
lwip_itoa
atoi
