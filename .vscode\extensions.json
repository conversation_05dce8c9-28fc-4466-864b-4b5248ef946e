{
    "recommendations": [
        "ms-vscode.cpptools",                   // (dependencies to ms-vscode.cpptools-extension-pack)
        "ms-vscode.cpptools-themes",            // (dependencies to ms-vscode.cpptools-extension-pack)
        "ms-vscode.cmake-tools",                // (dependencies to ms-vscode.cpptools-extension-pack)
        "twxs.cmake",				            // (dependencies to ms-vscode.cpptools-extension-pack)
        "ms-vscode.cpptools-extension-pack",    // Provides CMake and C++ file coloring, completion & support
        "dan-c-underwood.arm",		            // Provides syntax highlighting for the Arm Assembly language
        "zixuanwang.linkerscript",	            // Provides syntax highlighting for linker scripts
        "ms-vscode.hexeditor",                  // Provides hex editor fo viewing & anipulating files in their raw hexadecimal representation
        "trond-snekvik.gnu-mapfiles",           // Provides syntax highlighting and symbol listing for GNU linker .map files
        "jeff-hykin.better-cpp-syntax",         // Provides syntax highlighting for C++
        "marus25.cortex-debug",		            // Provides debug support on Arm Cortex-M
        "mcu-debug.debug-tracker-vscode",       // Dependencies to "marus25.cortex-debug"
        "mcu-debug.memory-view",                // Dependencies to "marus25.cortex-debug"
        "mcu-debug.peripheral-viewer",          // Dependencies to "marus25.cortex-debug"
        "mcu-debug.rtos-views"                  // Dependencies to "marus25.cortex-debug"
    ]
}