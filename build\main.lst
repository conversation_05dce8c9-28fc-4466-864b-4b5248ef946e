ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc1qDhUD.s 			page 1


   1              		.cpu cortex-m7
   2              		.arch armv7e-m
   3              		.fpu fpv5-d16
   4              		.eabi_attribute 28, 1
   5              		.eabi_attribute 20, 1
   6              		.eabi_attribute 21, 1
   7              		.eabi_attribute 23, 3
   8              		.eabi_attribute 24, 1
   9              		.eabi_attribute 25, 1
  10              		.eabi_attribute 26, 1
  11              		.eabi_attribute 30, 1
  12              		.eabi_attribute 34, 1
  13              		.eabi_attribute 18, 4
  14              		.file	"main.c"
  15              		.text
  16              	.Ltext0:
  17              		.cfi_sections	.debug_frame
  18              		.file 1 "Core/Src/main.c"
  19              		.section	.text.MPU_Config,"ax",%progbits
  20              		.align	1
  21              		.syntax unified
  22              		.thumb
  23              		.thumb_func
  25              	MPU_Config:
  26              	.LFB179:
   1:Core/Src/main.c **** /* USER CODE BEGIN Header */
   2:Core/Src/main.c **** /**
   3:Core/Src/main.c ****   ******************************************************************************
   4:Core/Src/main.c ****   * @file           : main.c
   5:Core/Src/main.c ****   * @brief          : Main program body
   6:Core/Src/main.c ****   ******************************************************************************
   7:Core/Src/main.c ****   * @attention
   8:Core/Src/main.c ****   *
   9:Core/Src/main.c ****   * Copyright (c) 2025 STMicroelectronics.
  10:Core/Src/main.c ****   * All rights reserved.
  11:Core/Src/main.c ****   *
  12:Core/Src/main.c ****   * This software is licensed under terms that can be found in the LICENSE file
  13:Core/Src/main.c ****   * in the root directory of this software component.
  14:Core/Src/main.c ****   * If no LICENSE file comes with this software, it is provided AS-IS.
  15:Core/Src/main.c ****   *
  16:Core/Src/main.c ****   ******************************************************************************
  17:Core/Src/main.c ****   */
  18:Core/Src/main.c **** /* USER CODE END Header */
  19:Core/Src/main.c **** /* Includes ------------------------------------------------------------------*/
  20:Core/Src/main.c **** #include "main.h"
  21:Core/Src/main.c **** #include "cmsis_os.h"
  22:Core/Src/main.c **** #include "lwip.h"
  23:Core/Src/main.c **** 
  24:Core/Src/main.c **** /* Private includes ----------------------------------------------------------*/
  25:Core/Src/main.c **** /* USER CODE BEGIN Includes */
  26:Core/Src/main.c **** #include <rcl/rcl.h>
  27:Core/Src/main.c **** #include <rcl/error_handling.h>
  28:Core/Src/main.c **** #include <rclc/rclc.h>
  29:Core/Src/main.c **** #include <rclc/executor.h>
  30:Core/Src/main.c **** #include <uxr/client/transport.h>
  31:Core/Src/main.c **** #include <rmw_microxrcedds_c/config.h>
  32:Core/Src/main.c **** #include <rmw_microros/rmw_microros.h>
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc1qDhUD.s 			page 2


  33:Core/Src/main.c **** #include "udp_transport.h"
  34:Core/Src/main.c **** 
  35:Core/Src/main.c **** #include <std_msgs/msg/int32.h>
  36:Core/Src/main.c **** /* USER CODE END Includes */
  37:Core/Src/main.c **** 
  38:Core/Src/main.c **** /* Private typedef -----------------------------------------------------------*/
  39:Core/Src/main.c **** typedef StaticTask_t osStaticThreadDef_t;
  40:Core/Src/main.c **** /* USER CODE BEGIN PTD */
  41:Core/Src/main.c **** 
  42:Core/Src/main.c **** /* USER CODE END PTD */
  43:Core/Src/main.c **** 
  44:Core/Src/main.c **** /* Private function prototypes -----------------------------------------------*/
  45:Core/Src/main.c **** void * microros_allocate(size_t size, void * state);
  46:Core/Src/main.c **** void microros_deallocate(void * pointer, void * state);
  47:Core/Src/main.c **** void * microros_reallocate(void * pointer, size_t size, void * state);
  48:Core/Src/main.c **** void * microros_zero_allocate(size_t number_of_elements, size_t size_of_element, void * state);
  49:Core/Src/main.c **** 
  50:Core/Src/main.c **** /* Private define ------------------------------------------------------------*/
  51:Core/Src/main.c **** /* USER CODE BEGIN PD */
  52:Core/Src/main.c **** 
  53:Core/Src/main.c **** /* USER CODE END PD */
  54:Core/Src/main.c **** 
  55:Core/Src/main.c **** /* Private macro -------------------------------------------------------------*/
  56:Core/Src/main.c **** /* USER CODE BEGIN PM */
  57:Core/Src/main.c **** 
  58:Core/Src/main.c **** /* USER CODE END PM */
  59:Core/Src/main.c **** 
  60:Core/Src/main.c **** /* Private variables ---------------------------------------------------------*/
  61:Core/Src/main.c **** 
  62:Core/Src/main.c **** /* Definitions for defaultTask */
  63:Core/Src/main.c **** osThreadId_t defaultTaskHandle;
  64:Core/Src/main.c **** uint32_t defaultTaskBuffer[ 3000 ];
  65:Core/Src/main.c **** osStaticThreadDef_t defaultTaskControlBlock;
  66:Core/Src/main.c **** const osThreadAttr_t defaultTask_attributes = {
  67:Core/Src/main.c ****   .name = "defaultTask",
  68:Core/Src/main.c ****   .cb_mem = &defaultTaskControlBlock,
  69:Core/Src/main.c ****   .cb_size = sizeof(defaultTaskControlBlock),
  70:Core/Src/main.c ****   .stack_mem = &defaultTaskBuffer[0],
  71:Core/Src/main.c ****   .stack_size = sizeof(defaultTaskBuffer),
  72:Core/Src/main.c ****   .priority = (osPriority_t) osPriorityNormal,
  73:Core/Src/main.c **** };
  74:Core/Src/main.c **** /* USER CODE BEGIN PV */
  75:Core/Src/main.c **** 
  76:Core/Src/main.c **** /* USER CODE END PV */
  77:Core/Src/main.c **** 
  78:Core/Src/main.c **** /* Private function prototypes -----------------------------------------------*/
  79:Core/Src/main.c **** void SystemClock_Config(void);
  80:Core/Src/main.c **** static void MPU_Config(void);
  81:Core/Src/main.c **** static void MX_GPIO_Init(void);
  82:Core/Src/main.c **** void StartDefaultTask(void *argument);
  83:Core/Src/main.c **** 
  84:Core/Src/main.c **** /* USER CODE BEGIN PFP */
  85:Core/Src/main.c **** 
  86:Core/Src/main.c **** /* USER CODE END PFP */
  87:Core/Src/main.c **** 
  88:Core/Src/main.c **** /* Private user code ---------------------------------------------------------*/
  89:Core/Src/main.c **** /* USER CODE BEGIN 0 */
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc1qDhUD.s 			page 3


  90:Core/Src/main.c **** 
  91:Core/Src/main.c **** /* USER CODE END 0 */
  92:Core/Src/main.c **** 
  93:Core/Src/main.c **** /**
  94:Core/Src/main.c ****   * @brief  The application entry point.
  95:Core/Src/main.c ****   * @retval int
  96:Core/Src/main.c ****   */
  97:Core/Src/main.c **** int main(void)
  98:Core/Src/main.c **** {
  99:Core/Src/main.c **** 
 100:Core/Src/main.c ****   /* USER CODE BEGIN 1 */
 101:Core/Src/main.c **** 
 102:Core/Src/main.c ****   /* USER CODE END 1 */
 103:Core/Src/main.c **** 
 104:Core/Src/main.c ****   /* MPU Configuration--------------------------------------------------------*/
 105:Core/Src/main.c ****   MPU_Config();
 106:Core/Src/main.c **** 
 107:Core/Src/main.c ****   /* MCU Configuration--------------------------------------------------------*/
 108:Core/Src/main.c **** 
 109:Core/Src/main.c ****   /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
 110:Core/Src/main.c ****   HAL_Init();
 111:Core/Src/main.c **** 
 112:Core/Src/main.c ****   /* USER CODE BEGIN Init */
 113:Core/Src/main.c **** 
 114:Core/Src/main.c ****   /* USER CODE END Init */
 115:Core/Src/main.c **** 
 116:Core/Src/main.c ****   /* Configure the system clock */
 117:Core/Src/main.c ****   SystemClock_Config();
 118:Core/Src/main.c **** 
 119:Core/Src/main.c ****   /* USER CODE BEGIN SysInit */
 120:Core/Src/main.c **** 
 121:Core/Src/main.c ****   /* USER CODE END SysInit */
 122:Core/Src/main.c **** 
 123:Core/Src/main.c ****   /* Initialize all configured peripherals */
 124:Core/Src/main.c ****   MX_GPIO_Init();
 125:Core/Src/main.c ****   /* USER CODE BEGIN 2 */
 126:Core/Src/main.c **** 
 127:Core/Src/main.c ****   /* USER CODE END 2 */
 128:Core/Src/main.c **** 
 129:Core/Src/main.c ****   /* Init scheduler */
 130:Core/Src/main.c ****   osKernelInitialize();
 131:Core/Src/main.c **** 
 132:Core/Src/main.c ****   /* USER CODE BEGIN RTOS_MUTEX */
 133:Core/Src/main.c ****   /* add mutexes, ... */
 134:Core/Src/main.c ****   /* USER CODE END RTOS_MUTEX */
 135:Core/Src/main.c **** 
 136:Core/Src/main.c ****   /* USER CODE BEGIN RTOS_SEMAPHORES */
 137:Core/Src/main.c ****   /* add semaphores, ... */
 138:Core/Src/main.c ****   /* USER CODE END RTOS_SEMAPHORES */
 139:Core/Src/main.c **** 
 140:Core/Src/main.c ****   /* USER CODE BEGIN RTOS_TIMERS */
 141:Core/Src/main.c ****   /* start timers, add new ones, ... */
 142:Core/Src/main.c ****   /* USER CODE END RTOS_TIMERS */
 143:Core/Src/main.c **** 
 144:Core/Src/main.c ****   /* USER CODE BEGIN RTOS_QUEUES */
 145:Core/Src/main.c ****   /* add queues, ... */
 146:Core/Src/main.c ****   /* USER CODE END RTOS_QUEUES */
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc1qDhUD.s 			page 4


 147:Core/Src/main.c **** 
 148:Core/Src/main.c ****   /* Create the thread(s) */
 149:Core/Src/main.c ****   /* creation of defaultTask */
 150:Core/Src/main.c ****   defaultTaskHandle = osThreadNew(StartDefaultTask, NULL, &defaultTask_attributes);
 151:Core/Src/main.c **** 
 152:Core/Src/main.c ****   /* USER CODE BEGIN RTOS_THREADS */
 153:Core/Src/main.c ****   /* add threads, ... */
 154:Core/Src/main.c ****   /* USER CODE END RTOS_THREADS */
 155:Core/Src/main.c **** 
 156:Core/Src/main.c ****   /* USER CODE BEGIN RTOS_EVENTS */
 157:Core/Src/main.c ****   /* add events, ... */
 158:Core/Src/main.c ****   /* USER CODE END RTOS_EVENTS */
 159:Core/Src/main.c **** 
 160:Core/Src/main.c ****   /* Start scheduler */
 161:Core/Src/main.c ****   osKernelStart();
 162:Core/Src/main.c **** 
 163:Core/Src/main.c ****   /* We should never get here as control is now taken by the scheduler */
 164:Core/Src/main.c **** 
 165:Core/Src/main.c ****   /* Infinite loop */
 166:Core/Src/main.c ****   /* USER CODE BEGIN WHILE */
 167:Core/Src/main.c ****   while (1)
 168:Core/Src/main.c ****   {
 169:Core/Src/main.c ****     /* USER CODE END WHILE */
 170:Core/Src/main.c **** 
 171:Core/Src/main.c ****     /* USER CODE BEGIN 3 */
 172:Core/Src/main.c ****   }
 173:Core/Src/main.c ****   /* USER CODE END 3 */
 174:Core/Src/main.c **** }
 175:Core/Src/main.c **** 
 176:Core/Src/main.c **** /**
 177:Core/Src/main.c ****   * @brief System Clock Configuration
 178:Core/Src/main.c ****   * @retval None
 179:Core/Src/main.c ****   */
 180:Core/Src/main.c **** void SystemClock_Config(void)
 181:Core/Src/main.c **** {
 182:Core/Src/main.c ****   RCC_OscInitTypeDef RCC_OscInitStruct = {0};
 183:Core/Src/main.c ****   RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};
 184:Core/Src/main.c **** 
 185:Core/Src/main.c ****   /** Supply configuration update enable
 186:Core/Src/main.c ****   */
 187:Core/Src/main.c ****   HAL_PWREx_ConfigSupply(PWR_LDO_SUPPLY);
 188:Core/Src/main.c **** 
 189:Core/Src/main.c ****   /** Configure the main internal regulator output voltage
 190:Core/Src/main.c ****   */
 191:Core/Src/main.c ****   __HAL_PWR_VOLTAGESCALING_CONFIG(PWR_REGULATOR_VOLTAGE_SCALE1);
 192:Core/Src/main.c **** 
 193:Core/Src/main.c ****   while(!__HAL_PWR_GET_FLAG(PWR_FLAG_VOSRDY)) {}
 194:Core/Src/main.c **** 
 195:Core/Src/main.c ****   /** Initializes the RCC Oscillators according to the specified parameters
 196:Core/Src/main.c ****   * in the RCC_OscInitTypeDef structure.
 197:Core/Src/main.c ****   */
 198:Core/Src/main.c ****   RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSE;
 199:Core/Src/main.c ****   RCC_OscInitStruct.HSEState = RCC_HSE_ON;
 200:Core/Src/main.c ****   RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
 201:Core/Src/main.c ****   RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSE;
 202:Core/Src/main.c ****   RCC_OscInitStruct.PLL.PLLM = 8;
 203:Core/Src/main.c ****   RCC_OscInitStruct.PLL.PLLN = 360;
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc1qDhUD.s 			page 5


 204:Core/Src/main.c ****   RCC_OscInitStruct.PLL.PLLP = 1;
 205:Core/Src/main.c ****   RCC_OscInitStruct.PLL.PLLQ = 4;
 206:Core/Src/main.c ****   RCC_OscInitStruct.PLL.PLLR = 2;
 207:Core/Src/main.c ****   RCC_OscInitStruct.PLL.PLLRGE = RCC_PLL1VCIRANGE_0;
 208:Core/Src/main.c ****   RCC_OscInitStruct.PLL.PLLVCOSEL = RCC_PLL1VCOMEDIUM;
 209:Core/Src/main.c ****   RCC_OscInitStruct.PLL.PLLFRACN = 0;
 210:Core/Src/main.c ****   if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
 211:Core/Src/main.c ****   {
 212:Core/Src/main.c ****     Error_Handler();
 213:Core/Src/main.c ****   }
 214:Core/Src/main.c **** 
 215:Core/Src/main.c ****   /** Initializes the CPU, AHB and APB buses clocks
 216:Core/Src/main.c ****   */
 217:Core/Src/main.c ****   RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
 218:Core/Src/main.c ****                               |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2
 219:Core/Src/main.c ****                               |RCC_CLOCKTYPE_D3PCLK1|RCC_CLOCKTYPE_D1PCLK1;
 220:Core/Src/main.c ****   RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
 221:Core/Src/main.c ****   RCC_ClkInitStruct.SYSCLKDivider = RCC_SYSCLK_DIV1;
 222:Core/Src/main.c ****   RCC_ClkInitStruct.AHBCLKDivider = RCC_HCLK_DIV2;
 223:Core/Src/main.c ****   RCC_ClkInitStruct.APB3CLKDivider = RCC_APB3_DIV2;
 224:Core/Src/main.c ****   RCC_ClkInitStruct.APB1CLKDivider = RCC_APB1_DIV2;
 225:Core/Src/main.c ****   RCC_ClkInitStruct.APB2CLKDivider = RCC_APB2_DIV2;
 226:Core/Src/main.c ****   RCC_ClkInitStruct.APB4CLKDivider = RCC_APB4_DIV2;
 227:Core/Src/main.c **** 
 228:Core/Src/main.c ****   if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_2) != HAL_OK)
 229:Core/Src/main.c ****   {
 230:Core/Src/main.c ****     Error_Handler();
 231:Core/Src/main.c ****   }
 232:Core/Src/main.c **** }
 233:Core/Src/main.c **** 
 234:Core/Src/main.c **** /**
 235:Core/Src/main.c ****   * @brief GPIO Initialization Function
 236:Core/Src/main.c ****   * @param None
 237:Core/Src/main.c ****   * @retval None
 238:Core/Src/main.c ****   */
 239:Core/Src/main.c **** static void MX_GPIO_Init(void)
 240:Core/Src/main.c **** {
 241:Core/Src/main.c ****   GPIO_InitTypeDef GPIO_InitStruct = {0};
 242:Core/Src/main.c ****   /* USER CODE BEGIN MX_GPIO_Init_1 */
 243:Core/Src/main.c **** 
 244:Core/Src/main.c ****   /* USER CODE END MX_GPIO_Init_1 */
 245:Core/Src/main.c **** 
 246:Core/Src/main.c ****   /* GPIO Ports Clock Enable */
 247:Core/Src/main.c ****   __HAL_RCC_GPIOC_CLK_ENABLE();
 248:Core/Src/main.c ****   __HAL_RCC_GPIOH_CLK_ENABLE();
 249:Core/Src/main.c ****   __HAL_RCC_GPIOA_CLK_ENABLE();
 250:Core/Src/main.c ****   __HAL_RCC_GPIOB_CLK_ENABLE();
 251:Core/Src/main.c ****   __HAL_RCC_GPIOD_CLK_ENABLE();
 252:Core/Src/main.c ****   __HAL_RCC_GPIOE_CLK_ENABLE();
 253:Core/Src/main.c **** 
 254:Core/Src/main.c ****   /*Configure GPIO pin Output Level */
 255:Core/Src/main.c ****   HAL_GPIO_WritePin(LED_RED_GPIO_Port, LED_RED_Pin, GPIO_PIN_RESET);
 256:Core/Src/main.c **** 
 257:Core/Src/main.c ****   /*Configure GPIO pin Output Level */
 258:Core/Src/main.c ****   HAL_GPIO_WritePin(LED_YELLOW_GPIO_Port, LED_YELLOW_Pin, GPIO_PIN_RESET);
 259:Core/Src/main.c **** 
 260:Core/Src/main.c ****   /*Configure GPIO pin : B1_Pin */
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc1qDhUD.s 			page 6


 261:Core/Src/main.c ****   GPIO_InitStruct.Pin = B1_Pin;
 262:Core/Src/main.c ****   GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
 263:Core/Src/main.c ****   GPIO_InitStruct.Pull = GPIO_NOPULL;
 264:Core/Src/main.c ****   HAL_GPIO_Init(B1_GPIO_Port, &GPIO_InitStruct);
 265:Core/Src/main.c **** 
 266:Core/Src/main.c ****   /*Configure GPIO pin : LED_RED_Pin */
 267:Core/Src/main.c ****   GPIO_InitStruct.Pin = LED_RED_Pin;
 268:Core/Src/main.c ****   GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
 269:Core/Src/main.c ****   GPIO_InitStruct.Pull = GPIO_NOPULL;
 270:Core/Src/main.c ****   GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
 271:Core/Src/main.c ****   HAL_GPIO_Init(LED_RED_GPIO_Port, &GPIO_InitStruct);
 272:Core/Src/main.c **** 
 273:Core/Src/main.c ****   /*Configure GPIO pins : STLK_VCP_RX_Pin STLK_VCP_TX_Pin */
 274:Core/Src/main.c ****   GPIO_InitStruct.Pin = STLK_VCP_RX_Pin|STLK_VCP_TX_Pin;
 275:Core/Src/main.c ****   GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
 276:Core/Src/main.c ****   GPIO_InitStruct.Pull = GPIO_NOPULL;
 277:Core/Src/main.c ****   GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
 278:Core/Src/main.c ****   GPIO_InitStruct.Alternate = GPIO_AF7_USART3;
 279:Core/Src/main.c ****   HAL_GPIO_Init(GPIOD, &GPIO_InitStruct);
 280:Core/Src/main.c **** 
 281:Core/Src/main.c ****   /*Configure GPIO pin : LED_YELLOW_Pin */
 282:Core/Src/main.c ****   GPIO_InitStruct.Pin = LED_YELLOW_Pin;
 283:Core/Src/main.c ****   GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
 284:Core/Src/main.c ****   GPIO_InitStruct.Pull = GPIO_NOPULL;
 285:Core/Src/main.c ****   GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
 286:Core/Src/main.c ****   HAL_GPIO_Init(LED_YELLOW_GPIO_Port, &GPIO_InitStruct);
 287:Core/Src/main.c **** 
 288:Core/Src/main.c ****   /* USER CODE BEGIN MX_GPIO_Init_2 */
 289:Core/Src/main.c **** 
 290:Core/Src/main.c ****   /* USER CODE END MX_GPIO_Init_2 */
 291:Core/Src/main.c **** }
 292:Core/Src/main.c **** 
 293:Core/Src/main.c **** /* USER CODE BEGIN 4 */
 294:Core/Src/main.c **** 
 295:Core/Src/main.c **** /* USER CODE END 4 */
 296:Core/Src/main.c **** 
 297:Core/Src/main.c **** /* USER CODE BEGIN Header_StartDefaultTask */
 298:Core/Src/main.c **** /**
 299:Core/Src/main.c ****   * @brief  Function implementing the defaultTask thread.
 300:Core/Src/main.c ****   * @param  argument: Not used
 301:Core/Src/main.c ****   * @retval None
 302:Core/Src/main.c ****   */
 303:Core/Src/main.c **** /* USER CODE END Header_StartDefaultTask */
 304:Core/Src/main.c **** void StartDefaultTask(void *argument)
 305:Core/Src/main.c **** {
 306:Core/Src/main.c ****   /* init code for LWIP */
 307:Core/Src/main.c ****   MX_LWIP_Init();
 308:Core/Src/main.c ****   /* USER CODE BEGIN 5 */
 309:Core/Src/main.c **** 
 310:Core/Src/main.c ****   // micro-ROS configuration
 311:Core/Src/main.c **** 
 312:Core/Src/main.c ****   rmw_uros_set_custom_transport(
 313:Core/Src/main.c ****     false,              //Framing disable here. Udp should Use Packet-oriented mode.
 314:Core/Src/main.c ****     "*************",    //your Agent's ip address
 315:Core/Src/main.c ****     cubemx_transport_open,
 316:Core/Src/main.c ****     cubemx_transport_close,
 317:Core/Src/main.c ****     cubemx_transport_write,
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc1qDhUD.s 			page 7


 318:Core/Src/main.c ****     cubemx_transport_read);
 319:Core/Src/main.c **** 
 320:Core/Src/main.c ****   rcl_allocator_t freeRTOS_allocator = rcutils_get_zero_initialized_allocator();
 321:Core/Src/main.c ****   freeRTOS_allocator.allocate = microros_allocate;
 322:Core/Src/main.c ****   freeRTOS_allocator.deallocate = microros_deallocate;
 323:Core/Src/main.c ****   freeRTOS_allocator.reallocate = microros_reallocate;
 324:Core/Src/main.c ****   freeRTOS_allocator.zero_allocate =  microros_zero_allocate;
 325:Core/Src/main.c **** 
 326:Core/Src/main.c ****   if (!rcutils_set_default_allocator(&freeRTOS_allocator)) {
 327:Core/Src/main.c ****       printf("Error on default allocators (line %d)\n", __LINE__); 
 328:Core/Src/main.c ****   }
 329:Core/Src/main.c **** 
 330:Core/Src/main.c ****   // micro-ROS app
 331:Core/Src/main.c **** 
 332:Core/Src/main.c ****   rcl_publisher_t publisher;
 333:Core/Src/main.c ****   std_msgs__msg__Int32 msg;
 334:Core/Src/main.c ****   rclc_support_t support;
 335:Core/Src/main.c ****   rcl_allocator_t allocator;
 336:Core/Src/main.c ****   rcl_node_t node;
 337:Core/Src/main.c **** 
 338:Core/Src/main.c ****   allocator = rcl_get_default_allocator();
 339:Core/Src/main.c **** 
 340:Core/Src/main.c ****   //create init_options
 341:Core/Src/main.c ****   rclc_support_init(&support, 0, NULL, &allocator);
 342:Core/Src/main.c **** 
 343:Core/Src/main.c ****   // create node
 344:Core/Src/main.c ****   rclc_node_init_default(&node, "cubemx_node", "", &support);
 345:Core/Src/main.c **** 
 346:Core/Src/main.c ****   // create publisher
 347:Core/Src/main.c ****   rclc_publisher_init_default(
 348:Core/Src/main.c ****     &publisher,
 349:Core/Src/main.c ****     &node,
 350:Core/Src/main.c ****     ROSIDL_GET_MSG_TYPE_SUPPORT(std_msgs, msg, Int32),
 351:Core/Src/main.c ****     "cubemx_publisher");
 352:Core/Src/main.c **** 
 353:Core/Src/main.c ****   msg.data = 0;
 354:Core/Src/main.c **** 
 355:Core/Src/main.c ****   for(;;)
 356:Core/Src/main.c ****   {
 357:Core/Src/main.c ****     rcl_ret_t ret = rcl_publish(&publisher, &msg, NULL);
 358:Core/Src/main.c ****     if (ret != RCL_RET_OK)
 359:Core/Src/main.c ****     {
 360:Core/Src/main.c ****       printf("Error publishing (line %d)\n", __LINE__); 
 361:Core/Src/main.c ****     }
 362:Core/Src/main.c ****     
 363:Core/Src/main.c ****     msg.data++;
 364:Core/Src/main.c ****     osDelay(10);
 365:Core/Src/main.c ****   }
 366:Core/Src/main.c ****   /* USER CODE END 5 */
 367:Core/Src/main.c **** }
 368:Core/Src/main.c **** 
 369:Core/Src/main.c ****  /* MPU Configuration */
 370:Core/Src/main.c **** 
 371:Core/Src/main.c **** void MPU_Config(void)
 372:Core/Src/main.c **** {
  27              		.loc 1 372 1 view -0
  28              		.cfi_startproc
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc1qDhUD.s 			page 8


  29              		@ args = 0, pretend = 0, frame = 16
  30              		@ frame_needed = 0, uses_anonymous_args = 0
  31 0000 10B5     		push	{r4, lr}
  32              	.LCFI0:
  33              		.cfi_def_cfa_offset 8
  34              		.cfi_offset 4, -8
  35              		.cfi_offset 14, -4
  36 0002 84B0     		sub	sp, sp, #16
  37              	.LCFI1:
  38              		.cfi_def_cfa_offset 24
 373:Core/Src/main.c ****   MPU_Region_InitTypeDef MPU_InitStruct = {0};
  39              		.loc 1 373 3 view .LVU1
  40              		.loc 1 373 26 is_stmt 0 view .LVU2
  41 0004 0024     		movs	r4, #0
  42 0006 0094     		str	r4, [sp]
  43 0008 0194     		str	r4, [sp, #4]
  44 000a 0294     		str	r4, [sp, #8]
  45 000c 0394     		str	r4, [sp, #12]
 374:Core/Src/main.c **** 
 375:Core/Src/main.c ****   /* Disables the MPU */
 376:Core/Src/main.c ****   HAL_MPU_Disable();
  46              		.loc 1 376 3 is_stmt 1 view .LVU3
  47 000e FFF7FEFF 		bl	HAL_MPU_Disable
  48              	.LVL0:
 377:Core/Src/main.c **** 
 378:Core/Src/main.c ****   /** Initializes and configures the Region and the memory to be protected
 379:Core/Src/main.c ****   */
 380:Core/Src/main.c ****   MPU_InitStruct.Enable = MPU_REGION_ENABLE;
  49              		.loc 1 380 3 view .LVU4
  50              		.loc 1 380 25 is_stmt 0 view .LVU5
  51 0012 0123     		movs	r3, #1
  52 0014 8DF80030 		strb	r3, [sp]
 381:Core/Src/main.c ****   MPU_InitStruct.Number = MPU_REGION_NUMBER0;
  53              		.loc 1 381 3 is_stmt 1 view .LVU6
  54              		.loc 1 381 25 is_stmt 0 view .LVU7
  55 0018 8DF80140 		strb	r4, [sp, #1]
 382:Core/Src/main.c ****   MPU_InitStruct.BaseAddress = 0x0;
  56              		.loc 1 382 3 is_stmt 1 view .LVU8
  57              		.loc 1 382 30 is_stmt 0 view .LVU9
  58 001c 0194     		str	r4, [sp, #4]
 383:Core/Src/main.c ****   MPU_InitStruct.Size = MPU_REGION_SIZE_4GB;
  59              		.loc 1 383 3 is_stmt 1 view .LVU10
  60              		.loc 1 383 23 is_stmt 0 view .LVU11
  61 001e 1F22     		movs	r2, #31
  62 0020 8DF80820 		strb	r2, [sp, #8]
 384:Core/Src/main.c ****   MPU_InitStruct.SubRegionDisable = 0x87;
  63              		.loc 1 384 3 is_stmt 1 view .LVU12
  64              		.loc 1 384 35 is_stmt 0 view .LVU13
  65 0024 8722     		movs	r2, #135
  66 0026 8DF80920 		strb	r2, [sp, #9]
 385:Core/Src/main.c ****   MPU_InitStruct.TypeExtField = MPU_TEX_LEVEL0;
  67              		.loc 1 385 3 is_stmt 1 view .LVU14
  68              		.loc 1 385 31 is_stmt 0 view .LVU15
  69 002a 8DF80A40 		strb	r4, [sp, #10]
 386:Core/Src/main.c ****   MPU_InitStruct.AccessPermission = MPU_REGION_NO_ACCESS;
  70              		.loc 1 386 3 is_stmt 1 view .LVU16
  71              		.loc 1 386 35 is_stmt 0 view .LVU17
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc1qDhUD.s 			page 9


  72 002e 8DF80B40 		strb	r4, [sp, #11]
 387:Core/Src/main.c ****   MPU_InitStruct.DisableExec = MPU_INSTRUCTION_ACCESS_DISABLE;
  73              		.loc 1 387 3 is_stmt 1 view .LVU18
  74              		.loc 1 387 30 is_stmt 0 view .LVU19
  75 0032 8DF80C30 		strb	r3, [sp, #12]
 388:Core/Src/main.c ****   MPU_InitStruct.IsShareable = MPU_ACCESS_SHAREABLE;
  76              		.loc 1 388 3 is_stmt 1 view .LVU20
  77              		.loc 1 388 30 is_stmt 0 view .LVU21
  78 0036 8DF80D30 		strb	r3, [sp, #13]
 389:Core/Src/main.c ****   MPU_InitStruct.IsCacheable = MPU_ACCESS_NOT_CACHEABLE;
  79              		.loc 1 389 3 is_stmt 1 view .LVU22
  80              		.loc 1 389 30 is_stmt 0 view .LVU23
  81 003a 8DF80E40 		strb	r4, [sp, #14]
 390:Core/Src/main.c ****   MPU_InitStruct.IsBufferable = MPU_ACCESS_NOT_BUFFERABLE;
  82              		.loc 1 390 3 is_stmt 1 view .LVU24
  83              		.loc 1 390 31 is_stmt 0 view .LVU25
  84 003e 8DF80F40 		strb	r4, [sp, #15]
 391:Core/Src/main.c **** 
 392:Core/Src/main.c ****   HAL_MPU_ConfigRegion(&MPU_InitStruct);
  85              		.loc 1 392 3 is_stmt 1 view .LVU26
  86 0042 6846     		mov	r0, sp
  87 0044 FFF7FEFF 		bl	HAL_MPU_ConfigRegion
  88              	.LVL1:
 393:Core/Src/main.c ****   /* Enables the MPU */
 394:Core/Src/main.c ****   HAL_MPU_Enable(MPU_PRIVILEGED_DEFAULT);
  89              		.loc 1 394 3 view .LVU27
  90 0048 0420     		movs	r0, #4
  91 004a FFF7FEFF 		bl	HAL_MPU_Enable
  92              	.LVL2:
 395:Core/Src/main.c **** 
 396:Core/Src/main.c **** }
  93              		.loc 1 396 1 is_stmt 0 view .LVU28
  94 004e 04B0     		add	sp, sp, #16
  95              	.LCFI2:
  96              		.cfi_def_cfa_offset 8
  97              		@ sp needed
  98 0050 10BD     		pop	{r4, pc}
  99              		.cfi_endproc
 100              	.LFE179:
 102              		.section	.text.MX_GPIO_Init,"ax",%progbits
 103              		.align	1
 104              		.syntax unified
 105              		.thumb
 106              		.thumb_func
 108              	MX_GPIO_Init:
 109              	.LFB177:
 240:Core/Src/main.c ****   GPIO_InitTypeDef GPIO_InitStruct = {0};
 110              		.loc 1 240 1 is_stmt 1 view -0
 111              		.cfi_startproc
 112              		@ args = 0, pretend = 0, frame = 48
 113              		@ frame_needed = 0, uses_anonymous_args = 0
 114 0000 F0B5     		push	{r4, r5, r6, r7, lr}
 115              	.LCFI3:
 116              		.cfi_def_cfa_offset 20
 117              		.cfi_offset 4, -20
 118              		.cfi_offset 5, -16
 119              		.cfi_offset 6, -12
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc1qDhUD.s 			page 10


 120              		.cfi_offset 7, -8
 121              		.cfi_offset 14, -4
 122 0002 8DB0     		sub	sp, sp, #52
 123              	.LCFI4:
 124              		.cfi_def_cfa_offset 72
 241:Core/Src/main.c ****   /* USER CODE BEGIN MX_GPIO_Init_1 */
 125              		.loc 1 241 3 view .LVU30
 241:Core/Src/main.c ****   /* USER CODE BEGIN MX_GPIO_Init_1 */
 126              		.loc 1 241 20 is_stmt 0 view .LVU31
 127 0004 0024     		movs	r4, #0
 128 0006 0794     		str	r4, [sp, #28]
 129 0008 0894     		str	r4, [sp, #32]
 130 000a 0994     		str	r4, [sp, #36]
 131 000c 0A94     		str	r4, [sp, #40]
 132 000e 0B94     		str	r4, [sp, #44]
 247:Core/Src/main.c ****   __HAL_RCC_GPIOH_CLK_ENABLE();
 133              		.loc 1 247 3 is_stmt 1 view .LVU32
 134              	.LBB4:
 247:Core/Src/main.c ****   __HAL_RCC_GPIOH_CLK_ENABLE();
 135              		.loc 1 247 3 view .LVU33
 247:Core/Src/main.c ****   __HAL_RCC_GPIOH_CLK_ENABLE();
 136              		.loc 1 247 3 view .LVU34
 137 0010 404B     		ldr	r3, .L5
 138 0012 D3F8E020 		ldr	r2, [r3, #224]
 139 0016 42F00402 		orr	r2, r2, #4
 140 001a C3F8E020 		str	r2, [r3, #224]
 247:Core/Src/main.c ****   __HAL_RCC_GPIOH_CLK_ENABLE();
 141              		.loc 1 247 3 view .LVU35
 142 001e D3F8E020 		ldr	r2, [r3, #224]
 143 0022 02F00402 		and	r2, r2, #4
 144 0026 0192     		str	r2, [sp, #4]
 247:Core/Src/main.c ****   __HAL_RCC_GPIOH_CLK_ENABLE();
 145              		.loc 1 247 3 view .LVU36
 146 0028 019A     		ldr	r2, [sp, #4]
 147              	.LBE4:
 247:Core/Src/main.c ****   __HAL_RCC_GPIOH_CLK_ENABLE();
 148              		.loc 1 247 3 view .LVU37
 248:Core/Src/main.c ****   __HAL_RCC_GPIOA_CLK_ENABLE();
 149              		.loc 1 248 3 view .LVU38
 150              	.LBB5:
 248:Core/Src/main.c ****   __HAL_RCC_GPIOA_CLK_ENABLE();
 151              		.loc 1 248 3 view .LVU39
 248:Core/Src/main.c ****   __HAL_RCC_GPIOA_CLK_ENABLE();
 152              		.loc 1 248 3 view .LVU40
 153 002a D3F8E020 		ldr	r2, [r3, #224]
 154 002e 42F08002 		orr	r2, r2, #128
 155 0032 C3F8E020 		str	r2, [r3, #224]
 248:Core/Src/main.c ****   __HAL_RCC_GPIOA_CLK_ENABLE();
 156              		.loc 1 248 3 view .LVU41
 157 0036 D3F8E020 		ldr	r2, [r3, #224]
 158 003a 02F08002 		and	r2, r2, #128
 159 003e 0292     		str	r2, [sp, #8]
 248:Core/Src/main.c ****   __HAL_RCC_GPIOA_CLK_ENABLE();
 160              		.loc 1 248 3 view .LVU42
 161 0040 029A     		ldr	r2, [sp, #8]
 162              	.LBE5:
 248:Core/Src/main.c ****   __HAL_RCC_GPIOA_CLK_ENABLE();
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc1qDhUD.s 			page 11


 163              		.loc 1 248 3 view .LVU43
 249:Core/Src/main.c ****   __HAL_RCC_GPIOB_CLK_ENABLE();
 164              		.loc 1 249 3 view .LVU44
 165              	.LBB6:
 249:Core/Src/main.c ****   __HAL_RCC_GPIOB_CLK_ENABLE();
 166              		.loc 1 249 3 view .LVU45
 249:Core/Src/main.c ****   __HAL_RCC_GPIOB_CLK_ENABLE();
 167              		.loc 1 249 3 view .LVU46
 168 0042 D3F8E020 		ldr	r2, [r3, #224]
 169 0046 42F00102 		orr	r2, r2, #1
 170 004a C3F8E020 		str	r2, [r3, #224]
 249:Core/Src/main.c ****   __HAL_RCC_GPIOB_CLK_ENABLE();
 171              		.loc 1 249 3 view .LVU47
 172 004e D3F8E020 		ldr	r2, [r3, #224]
 173 0052 02F00102 		and	r2, r2, #1
 174 0056 0392     		str	r2, [sp, #12]
 249:Core/Src/main.c ****   __HAL_RCC_GPIOB_CLK_ENABLE();
 175              		.loc 1 249 3 view .LVU48
 176 0058 039A     		ldr	r2, [sp, #12]
 177              	.LBE6:
 249:Core/Src/main.c ****   __HAL_RCC_GPIOB_CLK_ENABLE();
 178              		.loc 1 249 3 view .LVU49
 250:Core/Src/main.c ****   __HAL_RCC_GPIOD_CLK_ENABLE();
 179              		.loc 1 250 3 view .LVU50
 180              	.LBB7:
 250:Core/Src/main.c ****   __HAL_RCC_GPIOD_CLK_ENABLE();
 181              		.loc 1 250 3 view .LVU51
 250:Core/Src/main.c ****   __HAL_RCC_GPIOD_CLK_ENABLE();
 182              		.loc 1 250 3 view .LVU52
 183 005a D3F8E020 		ldr	r2, [r3, #224]
 184 005e 42F00202 		orr	r2, r2, #2
 185 0062 C3F8E020 		str	r2, [r3, #224]
 250:Core/Src/main.c ****   __HAL_RCC_GPIOD_CLK_ENABLE();
 186              		.loc 1 250 3 view .LVU53
 187 0066 D3F8E020 		ldr	r2, [r3, #224]
 188 006a 02F00202 		and	r2, r2, #2
 189 006e 0492     		str	r2, [sp, #16]
 250:Core/Src/main.c ****   __HAL_RCC_GPIOD_CLK_ENABLE();
 190              		.loc 1 250 3 view .LVU54
 191 0070 049A     		ldr	r2, [sp, #16]
 192              	.LBE7:
 250:Core/Src/main.c ****   __HAL_RCC_GPIOD_CLK_ENABLE();
 193              		.loc 1 250 3 view .LVU55
 251:Core/Src/main.c ****   __HAL_RCC_GPIOE_CLK_ENABLE();
 194              		.loc 1 251 3 view .LVU56
 195              	.LBB8:
 251:Core/Src/main.c ****   __HAL_RCC_GPIOE_CLK_ENABLE();
 196              		.loc 1 251 3 view .LVU57
 251:Core/Src/main.c ****   __HAL_RCC_GPIOE_CLK_ENABLE();
 197              		.loc 1 251 3 view .LVU58
 198 0072 D3F8E020 		ldr	r2, [r3, #224]
 199 0076 42F00802 		orr	r2, r2, #8
 200 007a C3F8E020 		str	r2, [r3, #224]
 251:Core/Src/main.c ****   __HAL_RCC_GPIOE_CLK_ENABLE();
 201              		.loc 1 251 3 view .LVU59
 202 007e D3F8E020 		ldr	r2, [r3, #224]
 203 0082 02F00802 		and	r2, r2, #8
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc1qDhUD.s 			page 12


 204 0086 0592     		str	r2, [sp, #20]
 251:Core/Src/main.c ****   __HAL_RCC_GPIOE_CLK_ENABLE();
 205              		.loc 1 251 3 view .LVU60
 206 0088 059A     		ldr	r2, [sp, #20]
 207              	.LBE8:
 251:Core/Src/main.c ****   __HAL_RCC_GPIOE_CLK_ENABLE();
 208              		.loc 1 251 3 view .LVU61
 252:Core/Src/main.c **** 
 209              		.loc 1 252 3 view .LVU62
 210              	.LBB9:
 252:Core/Src/main.c **** 
 211              		.loc 1 252 3 view .LVU63
 252:Core/Src/main.c **** 
 212              		.loc 1 252 3 view .LVU64
 213 008a D3F8E020 		ldr	r2, [r3, #224]
 214 008e 42F01002 		orr	r2, r2, #16
 215 0092 C3F8E020 		str	r2, [r3, #224]
 252:Core/Src/main.c **** 
 216              		.loc 1 252 3 view .LVU65
 217 0096 D3F8E030 		ldr	r3, [r3, #224]
 218 009a 03F01003 		and	r3, r3, #16
 219 009e 0693     		str	r3, [sp, #24]
 252:Core/Src/main.c **** 
 220              		.loc 1 252 3 view .LVU66
 221 00a0 069B     		ldr	r3, [sp, #24]
 222              	.LBE9:
 252:Core/Src/main.c **** 
 223              		.loc 1 252 3 view .LVU67
 255:Core/Src/main.c **** 
 224              		.loc 1 255 3 view .LVU68
 225 00a2 1D4F     		ldr	r7, .L5+4
 226 00a4 2246     		mov	r2, r4
 227 00a6 4FF48041 		mov	r1, #16384
 228 00aa 3846     		mov	r0, r7
 229 00ac FFF7FEFF 		bl	HAL_GPIO_WritePin
 230              	.LVL3:
 258:Core/Src/main.c **** 
 231              		.loc 1 258 3 view .LVU69
 232 00b0 1A4D     		ldr	r5, .L5+8
 233 00b2 2246     		mov	r2, r4
 234 00b4 0221     		movs	r1, #2
 235 00b6 2846     		mov	r0, r5
 236 00b8 FFF7FEFF 		bl	HAL_GPIO_WritePin
 237              	.LVL4:
 261:Core/Src/main.c ****   GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
 238              		.loc 1 261 3 view .LVU70
 261:Core/Src/main.c ****   GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
 239              		.loc 1 261 23 is_stmt 0 view .LVU71
 240 00bc 4FF40053 		mov	r3, #8192
 241 00c0 0793     		str	r3, [sp, #28]
 262:Core/Src/main.c ****   GPIO_InitStruct.Pull = GPIO_NOPULL;
 242              		.loc 1 262 3 is_stmt 1 view .LVU72
 262:Core/Src/main.c ****   GPIO_InitStruct.Pull = GPIO_NOPULL;
 243              		.loc 1 262 24 is_stmt 0 view .LVU73
 244 00c2 0894     		str	r4, [sp, #32]
 263:Core/Src/main.c ****   HAL_GPIO_Init(B1_GPIO_Port, &GPIO_InitStruct);
 245              		.loc 1 263 3 is_stmt 1 view .LVU74
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc1qDhUD.s 			page 13


 263:Core/Src/main.c ****   HAL_GPIO_Init(B1_GPIO_Port, &GPIO_InitStruct);
 246              		.loc 1 263 24 is_stmt 0 view .LVU75
 247 00c4 0994     		str	r4, [sp, #36]
 264:Core/Src/main.c **** 
 248              		.loc 1 264 3 is_stmt 1 view .LVU76
 249 00c6 07A9     		add	r1, sp, #28
 250 00c8 1548     		ldr	r0, .L5+12
 251 00ca FFF7FEFF 		bl	HAL_GPIO_Init
 252              	.LVL5:
 267:Core/Src/main.c ****   GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
 253              		.loc 1 267 3 view .LVU77
 267:Core/Src/main.c ****   GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
 254              		.loc 1 267 23 is_stmt 0 view .LVU78
 255 00ce 4FF48043 		mov	r3, #16384
 256 00d2 0793     		str	r3, [sp, #28]
 268:Core/Src/main.c ****   GPIO_InitStruct.Pull = GPIO_NOPULL;
 257              		.loc 1 268 3 is_stmt 1 view .LVU79
 268:Core/Src/main.c ****   GPIO_InitStruct.Pull = GPIO_NOPULL;
 258              		.loc 1 268 24 is_stmt 0 view .LVU80
 259 00d4 0126     		movs	r6, #1
 260 00d6 0896     		str	r6, [sp, #32]
 269:Core/Src/main.c ****   GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
 261              		.loc 1 269 3 is_stmt 1 view .LVU81
 269:Core/Src/main.c ****   GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
 262              		.loc 1 269 24 is_stmt 0 view .LVU82
 263 00d8 0994     		str	r4, [sp, #36]
 270:Core/Src/main.c ****   HAL_GPIO_Init(LED_RED_GPIO_Port, &GPIO_InitStruct);
 264              		.loc 1 270 3 is_stmt 1 view .LVU83
 270:Core/Src/main.c ****   HAL_GPIO_Init(LED_RED_GPIO_Port, &GPIO_InitStruct);
 265              		.loc 1 270 25 is_stmt 0 view .LVU84
 266 00da 0A94     		str	r4, [sp, #40]
 271:Core/Src/main.c **** 
 267              		.loc 1 271 3 is_stmt 1 view .LVU85
 268 00dc 07A9     		add	r1, sp, #28
 269 00de 3846     		mov	r0, r7
 270 00e0 FFF7FEFF 		bl	HAL_GPIO_Init
 271              	.LVL6:
 274:Core/Src/main.c ****   GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
 272              		.loc 1 274 3 view .LVU86
 274:Core/Src/main.c ****   GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
 273              		.loc 1 274 23 is_stmt 0 view .LVU87
 274 00e4 4FF44073 		mov	r3, #768
 275 00e8 0793     		str	r3, [sp, #28]
 275:Core/Src/main.c ****   GPIO_InitStruct.Pull = GPIO_NOPULL;
 276              		.loc 1 275 3 is_stmt 1 view .LVU88
 275:Core/Src/main.c ****   GPIO_InitStruct.Pull = GPIO_NOPULL;
 277              		.loc 1 275 24 is_stmt 0 view .LVU89
 278 00ea 0227     		movs	r7, #2
 279 00ec 0897     		str	r7, [sp, #32]
 276:Core/Src/main.c ****   GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
 280              		.loc 1 276 3 is_stmt 1 view .LVU90
 276:Core/Src/main.c ****   GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
 281              		.loc 1 276 24 is_stmt 0 view .LVU91
 282 00ee 0994     		str	r4, [sp, #36]
 277:Core/Src/main.c ****   GPIO_InitStruct.Alternate = GPIO_AF7_USART3;
 283              		.loc 1 277 3 is_stmt 1 view .LVU92
 277:Core/Src/main.c ****   GPIO_InitStruct.Alternate = GPIO_AF7_USART3;
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc1qDhUD.s 			page 14


 284              		.loc 1 277 25 is_stmt 0 view .LVU93
 285 00f0 0A94     		str	r4, [sp, #40]
 278:Core/Src/main.c ****   HAL_GPIO_Init(GPIOD, &GPIO_InitStruct);
 286              		.loc 1 278 3 is_stmt 1 view .LVU94
 278:Core/Src/main.c ****   HAL_GPIO_Init(GPIOD, &GPIO_InitStruct);
 287              		.loc 1 278 29 is_stmt 0 view .LVU95
 288 00f2 0723     		movs	r3, #7
 289 00f4 0B93     		str	r3, [sp, #44]
 279:Core/Src/main.c **** 
 290              		.loc 1 279 3 is_stmt 1 view .LVU96
 291 00f6 07A9     		add	r1, sp, #28
 292 00f8 0A48     		ldr	r0, .L5+16
 293 00fa FFF7FEFF 		bl	HAL_GPIO_Init
 294              	.LVL7:
 282:Core/Src/main.c ****   GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
 295              		.loc 1 282 3 view .LVU97
 282:Core/Src/main.c ****   GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
 296              		.loc 1 282 23 is_stmt 0 view .LVU98
 297 00fe 0797     		str	r7, [sp, #28]
 283:Core/Src/main.c ****   GPIO_InitStruct.Pull = GPIO_NOPULL;
 298              		.loc 1 283 3 is_stmt 1 view .LVU99
 283:Core/Src/main.c ****   GPIO_InitStruct.Pull = GPIO_NOPULL;
 299              		.loc 1 283 24 is_stmt 0 view .LVU100
 300 0100 0896     		str	r6, [sp, #32]
 284:Core/Src/main.c ****   GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
 301              		.loc 1 284 3 is_stmt 1 view .LVU101
 284:Core/Src/main.c ****   GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
 302              		.loc 1 284 24 is_stmt 0 view .LVU102
 303 0102 0994     		str	r4, [sp, #36]
 285:Core/Src/main.c ****   HAL_GPIO_Init(LED_YELLOW_GPIO_Port, &GPIO_InitStruct);
 304              		.loc 1 285 3 is_stmt 1 view .LVU103
 285:Core/Src/main.c ****   HAL_GPIO_Init(LED_YELLOW_GPIO_Port, &GPIO_InitStruct);
 305              		.loc 1 285 25 is_stmt 0 view .LVU104
 306 0104 0A94     		str	r4, [sp, #40]
 286:Core/Src/main.c **** 
 307              		.loc 1 286 3 is_stmt 1 view .LVU105
 308 0106 07A9     		add	r1, sp, #28
 309 0108 2846     		mov	r0, r5
 310 010a FFF7FEFF 		bl	HAL_GPIO_Init
 311              	.LVL8:
 291:Core/Src/main.c **** 
 312              		.loc 1 291 1 is_stmt 0 view .LVU106
 313 010e 0DB0     		add	sp, sp, #52
 314              	.LCFI5:
 315              		.cfi_def_cfa_offset 20
 316              		@ sp needed
 317 0110 F0BD     		pop	{r4, r5, r6, r7, pc}
 318              	.L6:
 319 0112 00BF     		.align	2
 320              	.L5:
 321 0114 00440258 		.word	1476543488
 322 0118 00040258 		.word	1476527104
 323 011c 00100258 		.word	1476530176
 324 0120 00080258 		.word	1476528128
 325 0124 000C0258 		.word	1476529152
 326              		.cfi_endproc
 327              	.LFE177:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc1qDhUD.s 			page 15


 329              		.section	.rodata.StartDefaultTask.str1.4,"aMS",%progbits,1
 330              		.align	2
 331              	.LC0:
 332 0000 3139322E 		.ascii	"*************\000"
 332      3136382E 
 332      312E3132 
 332      3100
 333 000e 0000     		.align	2
 334              	.LC1:
 335 0010 4572726F 		.ascii	"Error on default allocators (line %d)\012\000"
 335      72206F6E 
 335      20646566 
 335      61756C74 
 335      20616C6C 
 336 0037 00       		.align	2
 337              	.LC2:
 338 0038 00       		.ascii	"\000"
 339 0039 000000   		.align	2
 340              	.LC3:
 341 003c 63756265 		.ascii	"cubemx_node\000"
 341      6D785F6E 
 341      6F646500 
 342              		.align	2
 343              	.LC4:
 344 0048 63756265 		.ascii	"cubemx_publisher\000"
 344      6D785F70 
 344      75626C69 
 344      73686572 
 344      00
 345 0059 000000   		.align	2
 346              	.LC5:
 347 005c 4572726F 		.ascii	"Error publishing (line %d)\012\000"
 347      72207075 
 347      626C6973 
 347      68696E67 
 347      20286C69 
 348              		.section	.text.StartDefaultTask,"ax",%progbits
 349              		.align	1
 350              		.global	StartDefaultTask
 351              		.syntax unified
 352              		.thumb
 353              		.thumb_func
 355              	StartDefaultTask:
 356              	.LVL9:
 357              	.LFB178:
 305:Core/Src/main.c ****   /* init code for LWIP */
 358              		.loc 1 305 1 is_stmt 1 view -0
 359              		.cfi_startproc
 360              		@ args = 0, pretend = 0, frame = 136
 361              		@ frame_needed = 0, uses_anonymous_args = 0
 305:Core/Src/main.c ****   /* init code for LWIP */
 362              		.loc 1 305 1 is_stmt 0 view .LVU108
 363 0000 00B5     		push	{lr}
 364              	.LCFI6:
 365              		.cfi_def_cfa_offset 4
 366              		.cfi_offset 14, -4
 367 0002 A5B0     		sub	sp, sp, #148
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc1qDhUD.s 			page 16


 368              	.LCFI7:
 369              		.cfi_def_cfa_offset 152
 307:Core/Src/main.c ****   /* USER CODE BEGIN 5 */
 370              		.loc 1 307 3 is_stmt 1 view .LVU109
 371 0004 FFF7FEFF 		bl	MX_LWIP_Init
 372              	.LVL10:
 312:Core/Src/main.c ****     false,              //Framing disable here. Udp should Use Packet-oriented mode.
 373              		.loc 1 312 3 view .LVU110
 374 0008 2B4B     		ldr	r3, .L14
 375 000a 0193     		str	r3, [sp, #4]
 376 000c 2B4B     		ldr	r3, .L14+4
 377 000e 0093     		str	r3, [sp]
 378 0010 2B4B     		ldr	r3, .L14+8
 379 0012 2C4A     		ldr	r2, .L14+12
 380 0014 2C49     		ldr	r1, .L14+16
 381 0016 0020     		movs	r0, #0
 382 0018 FFF7FEFF 		bl	rmw_uros_set_custom_transport
 383              	.LVL11:
 320:Core/Src/main.c ****   freeRTOS_allocator.allocate = microros_allocate;
 384              		.loc 1 320 3 view .LVU111
 320:Core/Src/main.c ****   freeRTOS_allocator.allocate = microros_allocate;
 385              		.loc 1 320 40 is_stmt 0 view .LVU112
 386 001c 1FA8     		add	r0, sp, #124
 387 001e FFF7FEFF 		bl	rcutils_get_zero_initialized_allocator
 388              	.LVL12:
 321:Core/Src/main.c ****   freeRTOS_allocator.deallocate = microros_deallocate;
 389              		.loc 1 321 3 is_stmt 1 view .LVU113
 321:Core/Src/main.c ****   freeRTOS_allocator.deallocate = microros_deallocate;
 390              		.loc 1 321 31 is_stmt 0 view .LVU114
 391 0022 2A4B     		ldr	r3, .L14+20
 392 0024 1F93     		str	r3, [sp, #124]
 322:Core/Src/main.c ****   freeRTOS_allocator.reallocate = microros_reallocate;
 393              		.loc 1 322 3 is_stmt 1 view .LVU115
 322:Core/Src/main.c ****   freeRTOS_allocator.reallocate = microros_reallocate;
 394              		.loc 1 322 33 is_stmt 0 view .LVU116
 395 0026 2A4B     		ldr	r3, .L14+24
 396 0028 2093     		str	r3, [sp, #128]
 323:Core/Src/main.c ****   freeRTOS_allocator.zero_allocate =  microros_zero_allocate;
 397              		.loc 1 323 3 is_stmt 1 view .LVU117
 323:Core/Src/main.c ****   freeRTOS_allocator.zero_allocate =  microros_zero_allocate;
 398              		.loc 1 323 33 is_stmt 0 view .LVU118
 399 002a 2A4B     		ldr	r3, .L14+28
 400 002c 2193     		str	r3, [sp, #132]
 324:Core/Src/main.c **** 
 401              		.loc 1 324 3 is_stmt 1 view .LVU119
 324:Core/Src/main.c **** 
 402              		.loc 1 324 36 is_stmt 0 view .LVU120
 403 002e 2A4B     		ldr	r3, .L14+32
 404 0030 2293     		str	r3, [sp, #136]
 326:Core/Src/main.c ****       printf("Error on default allocators (line %d)\n", __LINE__); 
 405              		.loc 1 326 3 is_stmt 1 view .LVU121
 326:Core/Src/main.c ****       printf("Error on default allocators (line %d)\n", __LINE__); 
 406              		.loc 1 326 8 is_stmt 0 view .LVU122
 407 0032 1FA8     		add	r0, sp, #124
 408 0034 FFF7FEFF 		bl	rcutils_set_default_allocator
 409              	.LVL13:
 326:Core/Src/main.c ****       printf("Error on default allocators (line %d)\n", __LINE__); 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc1qDhUD.s 			page 17


 410              		.loc 1 326 6 discriminator 1 view .LVU123
 411 0038 28B3     		cbz	r0, .L12
 412              	.L8:
 332:Core/Src/main.c ****   std_msgs__msg__Int32 msg;
 413              		.loc 1 332 3 is_stmt 1 view .LVU124
 333:Core/Src/main.c ****   rclc_support_t support;
 414              		.loc 1 333 3 view .LVU125
 334:Core/Src/main.c ****   rcl_allocator_t allocator;
 415              		.loc 1 334 3 view .LVU126
 335:Core/Src/main.c ****   rcl_node_t node;
 416              		.loc 1 335 3 view .LVU127
 336:Core/Src/main.c **** 
 417              		.loc 1 336 3 view .LVU128
 338:Core/Src/main.c **** 
 418              		.loc 1 338 3 view .LVU129
 338:Core/Src/main.c **** 
 419              		.loc 1 338 15 is_stmt 0 view .LVU130
 420 003a 02A8     		add	r0, sp, #8
 421 003c FFF7FEFF 		bl	rcutils_get_default_allocator
 422              	.LVL14:
 423 0040 0DF12C0E 		add	lr, sp, #44
 424 0044 0DF1080C 		add	ip, sp, #8
 425 0048 BCE80F00 		ldmia	ip!, {r0, r1, r2, r3}
 426 004c AEE80F00 		stmia	lr!, {r0, r1, r2, r3}
 427 0050 DCF80030 		ldr	r3, [ip]
 428 0054 CEF80030 		str	r3, [lr]
 341:Core/Src/main.c **** 
 429              		.loc 1 341 3 is_stmt 1 view .LVU131
 430 0058 0BAB     		add	r3, sp, #44
 431 005a 0022     		movs	r2, #0
 432 005c 1146     		mov	r1, r2
 433 005e 10A8     		add	r0, sp, #64
 434 0060 FFF7FEFF 		bl	rclc_support_init
 435              	.LVL15:
 344:Core/Src/main.c **** 
 436              		.loc 1 344 3 view .LVU132
 437 0064 10AB     		add	r3, sp, #64
 438 0066 1D4A     		ldr	r2, .L14+36
 439 0068 1D49     		ldr	r1, .L14+40
 440 006a 09A8     		add	r0, sp, #36
 441 006c FFF7FEFF 		bl	rclc_node_init_default
 442              	.LVL16:
 347:Core/Src/main.c ****     &publisher,
 443              		.loc 1 347 3 view .LVU133
 444 0070 FFF7FEFF 		bl	rosidl_typesupport_c__get_message_type_support_handle__std_msgs__msg__Int32
 445              	.LVL17:
 446 0074 0246     		mov	r2, r0
 347:Core/Src/main.c ****     &publisher,
 447              		.loc 1 347 3 is_stmt 0 discriminator 1 view .LVU134
 448 0076 1B4B     		ldr	r3, .L14+44
 449 0078 09A9     		add	r1, sp, #36
 450 007a 1EA8     		add	r0, sp, #120
 451 007c FFF7FEFF 		bl	rclc_publisher_init_default
 452              	.LVL18:
 353:Core/Src/main.c **** 
 453              		.loc 1 353 3 is_stmt 1 view .LVU135
 353:Core/Src/main.c **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc1qDhUD.s 			page 18


 454              		.loc 1 353 12 is_stmt 0 view .LVU136
 455 0080 0023     		movs	r3, #0
 456 0082 1D93     		str	r3, [sp, #116]
 457 0084 10E0     		b	.L10
 458              	.L12:
 327:Core/Src/main.c ****   }
 459              		.loc 1 327 7 is_stmt 1 view .LVU137
 460 0086 40F24711 		movw	r1, #327
 461 008a 1748     		ldr	r0, .L14+48
 462 008c FFF7FEFF 		bl	printf
 463              	.LVL19:
 464 0090 D3E7     		b	.L8
 465              	.LVL20:
 466              	.L13:
 467              	.LBB10:
 360:Core/Src/main.c ****     }
 468              		.loc 1 360 7 view .LVU138
 469 0092 4FF4B471 		mov	r1, #360
 470 0096 1548     		ldr	r0, .L14+52
 471              	.LVL21:
 360:Core/Src/main.c ****     }
 472              		.loc 1 360 7 is_stmt 0 view .LVU139
 473 0098 FFF7FEFF 		bl	printf
 474              	.LVL22:
 475              	.L9:
 363:Core/Src/main.c ****     osDelay(10);
 476              		.loc 1 363 5 is_stmt 1 view .LVU140
 363:Core/Src/main.c ****     osDelay(10);
 477              		.loc 1 363 8 is_stmt 0 view .LVU141
 478 009c 1D9B     		ldr	r3, [sp, #116]
 363:Core/Src/main.c ****     osDelay(10);
 479              		.loc 1 363 13 view .LVU142
 480 009e 0133     		adds	r3, r3, #1
 481 00a0 1D93     		str	r3, [sp, #116]
 364:Core/Src/main.c ****   }
 482              		.loc 1 364 5 is_stmt 1 view .LVU143
 483 00a2 0A20     		movs	r0, #10
 484 00a4 FFF7FEFF 		bl	osDelay
 485              	.LVL23:
 486              	.LBE10:
 355:Core/Src/main.c ****   {
 487              		.loc 1 355 3 view .LVU144
 488              	.L10:
 355:Core/Src/main.c ****   {
 489              		.loc 1 355 3 view .LVU145
 490              	.LBB11:
 357:Core/Src/main.c ****     if (ret != RCL_RET_OK)
 491              		.loc 1 357 5 view .LVU146
 357:Core/Src/main.c ****     if (ret != RCL_RET_OK)
 492              		.loc 1 357 21 is_stmt 0 view .LVU147
 493 00a8 0022     		movs	r2, #0
 494 00aa 1DA9     		add	r1, sp, #116
 495 00ac 1EA8     		add	r0, sp, #120
 496 00ae FFF7FEFF 		bl	rcl_publish
 497              	.LVL24:
 358:Core/Src/main.c ****     {
 498              		.loc 1 358 5 is_stmt 1 view .LVU148
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc1qDhUD.s 			page 19


 358:Core/Src/main.c ****     {
 499              		.loc 1 358 8 is_stmt 0 view .LVU149
 500 00b2 0028     		cmp	r0, #0
 501 00b4 EDD1     		bne	.L13
 502 00b6 F1E7     		b	.L9
 503              	.L15:
 504              		.align	2
 505              	.L14:
 506 00b8 00000000 		.word	cubemx_transport_read
 507 00bc 00000000 		.word	cubemx_transport_write
 508 00c0 00000000 		.word	cubemx_transport_close
 509 00c4 00000000 		.word	cubemx_transport_open
 510 00c8 00000000 		.word	.LC0
 511 00cc 00000000 		.word	microros_allocate
 512 00d0 00000000 		.word	microros_deallocate
 513 00d4 00000000 		.word	microros_reallocate
 514 00d8 00000000 		.word	microros_zero_allocate
 515 00dc 38000000 		.word	.LC2
 516 00e0 3C000000 		.word	.LC3
 517 00e4 48000000 		.word	.LC4
 518 00e8 10000000 		.word	.LC1
 519 00ec 5C000000 		.word	.LC5
 520              	.LBE11:
 521              		.cfi_endproc
 522              	.LFE178:
 524              		.section	.text.HAL_TIM_PeriodElapsedCallback,"ax",%progbits
 525              		.align	1
 526              		.global	HAL_TIM_PeriodElapsedCallback
 527              		.syntax unified
 528              		.thumb
 529              		.thumb_func
 531              	HAL_TIM_PeriodElapsedCallback:
 532              	.LVL25:
 533              	.LFB180:
 397:Core/Src/main.c **** 
 398:Core/Src/main.c **** /**
 399:Core/Src/main.c ****   * @brief  Period elapsed callback in non blocking mode
 400:Core/Src/main.c ****   * @note   This function is called  when TIM1 interrupt took place, inside
 401:Core/Src/main.c ****   * HAL_TIM_IRQHandler(). It makes a direct call to HAL_IncTick() to increment
 402:Core/Src/main.c ****   * a global variable "uwTick" used as application time base.
 403:Core/Src/main.c ****   * @param  htim : TIM handle
 404:Core/Src/main.c ****   * @retval None
 405:Core/Src/main.c ****   */
 406:Core/Src/main.c **** void HAL_TIM_PeriodElapsedCallback(TIM_HandleTypeDef *htim)
 407:Core/Src/main.c **** {
 534              		.loc 1 407 1 is_stmt 1 view -0
 535              		.cfi_startproc
 536              		@ args = 0, pretend = 0, frame = 0
 537              		@ frame_needed = 0, uses_anonymous_args = 0
 538              		.loc 1 407 1 is_stmt 0 view .LVU151
 539 0000 08B5     		push	{r3, lr}
 540              	.LCFI8:
 541              		.cfi_def_cfa_offset 8
 542              		.cfi_offset 3, -8
 543              		.cfi_offset 14, -4
 408:Core/Src/main.c ****   /* USER CODE BEGIN Callback 0 */
 409:Core/Src/main.c **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc1qDhUD.s 			page 20


 410:Core/Src/main.c ****   /* USER CODE END Callback 0 */
 411:Core/Src/main.c ****   if (htim->Instance == TIM1) {
 544              		.loc 1 411 3 is_stmt 1 view .LVU152
 545              		.loc 1 411 11 is_stmt 0 view .LVU153
 546 0002 0268     		ldr	r2, [r0]
 547              		.loc 1 411 6 view .LVU154
 548 0004 034B     		ldr	r3, .L20
 549 0006 9A42     		cmp	r2, r3
 550 0008 00D0     		beq	.L19
 551              	.LVL26:
 552              	.L16:
 412:Core/Src/main.c ****     HAL_IncTick();
 413:Core/Src/main.c ****   }
 414:Core/Src/main.c ****   /* USER CODE BEGIN Callback 1 */
 415:Core/Src/main.c **** 
 416:Core/Src/main.c ****   /* USER CODE END Callback 1 */
 417:Core/Src/main.c **** }
 553              		.loc 1 417 1 view .LVU155
 554 000a 08BD     		pop	{r3, pc}
 555              	.LVL27:
 556              	.L19:
 412:Core/Src/main.c ****     HAL_IncTick();
 557              		.loc 1 412 5 is_stmt 1 view .LVU156
 558 000c FFF7FEFF 		bl	HAL_IncTick
 559              	.LVL28:
 560              		.loc 1 417 1 is_stmt 0 view .LVU157
 561 0010 FBE7     		b	.L16
 562              	.L21:
 563 0012 00BF     		.align	2
 564              	.L20:
 565 0014 00000140 		.word	1073807360
 566              		.cfi_endproc
 567              	.LFE180:
 569              		.section	.text.Error_Handler,"ax",%progbits
 570              		.align	1
 571              		.global	Error_Handler
 572              		.syntax unified
 573              		.thumb
 574              		.thumb_func
 576              	Error_Handler:
 577              	.LFB181:
 418:Core/Src/main.c **** 
 419:Core/Src/main.c **** /**
 420:Core/Src/main.c ****   * @brief  This function is executed in case of error occurrence.
 421:Core/Src/main.c ****   * @retval None
 422:Core/Src/main.c ****   */
 423:Core/Src/main.c **** void Error_Handler(void)
 424:Core/Src/main.c **** {
 578              		.loc 1 424 1 is_stmt 1 view -0
 579              		.cfi_startproc
 580              		@ Volatile: function does not return.
 581              		@ args = 0, pretend = 0, frame = 0
 582              		@ frame_needed = 0, uses_anonymous_args = 0
 583              		@ link register save eliminated.
 425:Core/Src/main.c ****   /* USER CODE BEGIN Error_Handler_Debug */
 426:Core/Src/main.c ****   /* User can add his own implementation to report the HAL error return state */
 427:Core/Src/main.c ****   __disable_irq();
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc1qDhUD.s 			page 21


 584              		.loc 1 427 3 view .LVU159
 585              	.LBB12:
 586              	.LBI12:
 587              		.file 2 "Drivers/CMSIS/Include/cmsis_gcc.h"
   1:Drivers/CMSIS/Include/cmsis_gcc.h **** /**************************************************************************//**
   2:Drivers/CMSIS/Include/cmsis_gcc.h ****  * @file     cmsis_gcc.h
   3:Drivers/CMSIS/Include/cmsis_gcc.h ****  * @brief    CMSIS compiler GCC header file
   4:Drivers/CMSIS/Include/cmsis_gcc.h ****  * @version  V5.2.0
   5:Drivers/CMSIS/Include/cmsis_gcc.h ****  * @date     08. May 2019
   6:Drivers/CMSIS/Include/cmsis_gcc.h ****  ******************************************************************************/
   7:Drivers/CMSIS/Include/cmsis_gcc.h **** /*
   8:Drivers/CMSIS/Include/cmsis_gcc.h ****  * Copyright (c) 2009-2019 Arm Limited. All rights reserved.
   9:Drivers/CMSIS/Include/cmsis_gcc.h ****  *
  10:Drivers/CMSIS/Include/cmsis_gcc.h ****  * SPDX-License-Identifier: Apache-2.0
  11:Drivers/CMSIS/Include/cmsis_gcc.h ****  *
  12:Drivers/CMSIS/Include/cmsis_gcc.h ****  * Licensed under the Apache License, Version 2.0 (the License); you may
  13:Drivers/CMSIS/Include/cmsis_gcc.h ****  * not use this file except in compliance with the License.
  14:Drivers/CMSIS/Include/cmsis_gcc.h ****  * You may obtain a copy of the License at
  15:Drivers/CMSIS/Include/cmsis_gcc.h ****  *
  16:Drivers/CMSIS/Include/cmsis_gcc.h ****  * www.apache.org/licenses/LICENSE-2.0
  17:Drivers/CMSIS/Include/cmsis_gcc.h ****  *
  18:Drivers/CMSIS/Include/cmsis_gcc.h ****  * Unless required by applicable law or agreed to in writing, software
  19:Drivers/CMSIS/Include/cmsis_gcc.h ****  * distributed under the License is distributed on an AS IS BASIS, WITHOUT
  20:Drivers/CMSIS/Include/cmsis_gcc.h ****  * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  21:Drivers/CMSIS/Include/cmsis_gcc.h ****  * See the License for the specific language governing permissions and
  22:Drivers/CMSIS/Include/cmsis_gcc.h ****  * limitations under the License.
  23:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
  24:Drivers/CMSIS/Include/cmsis_gcc.h **** 
  25:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef __CMSIS_GCC_H
  26:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __CMSIS_GCC_H
  27:Drivers/CMSIS/Include/cmsis_gcc.h **** 
  28:Drivers/CMSIS/Include/cmsis_gcc.h **** /* ignore some GCC warnings */
  29:Drivers/CMSIS/Include/cmsis_gcc.h **** #pragma GCC diagnostic push
  30:Drivers/CMSIS/Include/cmsis_gcc.h **** #pragma GCC diagnostic ignored "-Wsign-conversion"
  31:Drivers/CMSIS/Include/cmsis_gcc.h **** #pragma GCC diagnostic ignored "-Wconversion"
  32:Drivers/CMSIS/Include/cmsis_gcc.h **** #pragma GCC diagnostic ignored "-Wunused-parameter"
  33:Drivers/CMSIS/Include/cmsis_gcc.h **** 
  34:Drivers/CMSIS/Include/cmsis_gcc.h **** /* Fallback for __has_builtin */
  35:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef __has_builtin
  36:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __has_builtin(x) (0)
  37:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  38:Drivers/CMSIS/Include/cmsis_gcc.h **** 
  39:Drivers/CMSIS/Include/cmsis_gcc.h **** /* CMSIS compiler specific defines */
  40:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __ASM
  41:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __ASM                                  __asm
  42:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  43:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __INLINE
  44:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __INLINE                               inline
  45:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  46:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __STATIC_INLINE
  47:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __STATIC_INLINE                        static inline
  48:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  49:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __STATIC_FORCEINLINE                 
  50:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __STATIC_FORCEINLINE                   __attribute__((always_inline)) static inline
  51:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif                                           
  52:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __NO_RETURN
  53:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __NO_RETURN                            __attribute__((__noreturn__))
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc1qDhUD.s 			page 22


  54:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  55:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __USED
  56:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __USED                                 __attribute__((used))
  57:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  58:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __WEAK
  59:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __WEAK                                 __attribute__((weak))
  60:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  61:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __PACKED
  62:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __PACKED                               __attribute__((packed, aligned(1)))
  63:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  64:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __PACKED_STRUCT
  65:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __PACKED_STRUCT                        struct __attribute__((packed, aligned(1)))
  66:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  67:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __PACKED_UNION
  68:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __PACKED_UNION                         union __attribute__((packed, aligned(1)))
  69:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  70:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __UNALIGNED_UINT32        /* deprecated */
  71:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic push
  72:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic ignored "-Wpacked"
  73:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic ignored "-Wattributes"
  74:Drivers/CMSIS/Include/cmsis_gcc.h ****   struct __attribute__((packed)) T_UINT32 { uint32_t v; };
  75:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic pop
  76:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __UNALIGNED_UINT32(x)                  (((struct T_UINT32 *)(x))->v)
  77:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  78:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __UNALIGNED_UINT16_WRITE
  79:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic push
  80:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic ignored "-Wpacked"
  81:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic ignored "-Wattributes"
  82:Drivers/CMSIS/Include/cmsis_gcc.h ****   __PACKED_STRUCT T_UINT16_WRITE { uint16_t v; };
  83:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic pop
  84:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __UNALIGNED_UINT16_WRITE(addr, val)    (void)((((struct T_UINT16_WRITE *)(void *)(addr))-
  85:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  86:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __UNALIGNED_UINT16_READ
  87:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic push
  88:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic ignored "-Wpacked"
  89:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic ignored "-Wattributes"
  90:Drivers/CMSIS/Include/cmsis_gcc.h ****   __PACKED_STRUCT T_UINT16_READ { uint16_t v; };
  91:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic pop
  92:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __UNALIGNED_UINT16_READ(addr)          (((const struct T_UINT16_READ *)(const void *)(add
  93:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  94:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __UNALIGNED_UINT32_WRITE
  95:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic push
  96:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic ignored "-Wpacked"
  97:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic ignored "-Wattributes"
  98:Drivers/CMSIS/Include/cmsis_gcc.h ****   __PACKED_STRUCT T_UINT32_WRITE { uint32_t v; };
  99:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic pop
 100:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __UNALIGNED_UINT32_WRITE(addr, val)    (void)((((struct T_UINT32_WRITE *)(void *)(addr))-
 101:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 102:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __UNALIGNED_UINT32_READ
 103:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic push
 104:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic ignored "-Wpacked"
 105:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic ignored "-Wattributes"
 106:Drivers/CMSIS/Include/cmsis_gcc.h ****   __PACKED_STRUCT T_UINT32_READ { uint32_t v; };
 107:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic pop
 108:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __UNALIGNED_UINT32_READ(addr)          (((const struct T_UINT32_READ *)(const void *)(add
 109:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 110:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __ALIGNED
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc1qDhUD.s 			page 23


 111:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __ALIGNED(x)                           __attribute__((aligned(x)))
 112:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 113:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __RESTRICT
 114:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __RESTRICT                             __restrict
 115:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 116:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __COMPILER_BARRIER
 117:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __COMPILER_BARRIER()                   __ASM volatile("":::"memory")
 118:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 119:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 120:Drivers/CMSIS/Include/cmsis_gcc.h **** /* #########################  Startup and Lowlevel Init  ######################## */
 121:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 122:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef __PROGRAM_START
 123:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 124:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 125:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Initializes data and bss sections
 126:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details This default implementations initialized all data and additional bss
 127:Drivers/CMSIS/Include/cmsis_gcc.h ****            sections relying on .copy.table and .zero.table specified properly
 128:Drivers/CMSIS/Include/cmsis_gcc.h ****            in the used linker script.
 129:Drivers/CMSIS/Include/cmsis_gcc.h ****   
 130:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 131:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE __NO_RETURN void __cmsis_start(void)
 132:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 133:Drivers/CMSIS/Include/cmsis_gcc.h ****   extern void _start(void) __NO_RETURN;
 134:Drivers/CMSIS/Include/cmsis_gcc.h ****   
 135:Drivers/CMSIS/Include/cmsis_gcc.h ****   typedef struct {
 136:Drivers/CMSIS/Include/cmsis_gcc.h ****     uint32_t const* src;
 137:Drivers/CMSIS/Include/cmsis_gcc.h ****     uint32_t* dest;
 138:Drivers/CMSIS/Include/cmsis_gcc.h ****     uint32_t  wlen;
 139:Drivers/CMSIS/Include/cmsis_gcc.h ****   } __copy_table_t;
 140:Drivers/CMSIS/Include/cmsis_gcc.h ****   
 141:Drivers/CMSIS/Include/cmsis_gcc.h ****   typedef struct {
 142:Drivers/CMSIS/Include/cmsis_gcc.h ****     uint32_t* dest;
 143:Drivers/CMSIS/Include/cmsis_gcc.h ****     uint32_t  wlen;
 144:Drivers/CMSIS/Include/cmsis_gcc.h ****   } __zero_table_t;
 145:Drivers/CMSIS/Include/cmsis_gcc.h ****   
 146:Drivers/CMSIS/Include/cmsis_gcc.h ****   extern const __copy_table_t __copy_table_start__;
 147:Drivers/CMSIS/Include/cmsis_gcc.h ****   extern const __copy_table_t __copy_table_end__;
 148:Drivers/CMSIS/Include/cmsis_gcc.h ****   extern const __zero_table_t __zero_table_start__;
 149:Drivers/CMSIS/Include/cmsis_gcc.h ****   extern const __zero_table_t __zero_table_end__;
 150:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 151:Drivers/CMSIS/Include/cmsis_gcc.h ****   for (__copy_table_t const* pTable = &__copy_table_start__; pTable < &__copy_table_end__; ++pTable
 152:Drivers/CMSIS/Include/cmsis_gcc.h ****     for(uint32_t i=0u; i<pTable->wlen; ++i) {
 153:Drivers/CMSIS/Include/cmsis_gcc.h ****       pTable->dest[i] = pTable->src[i];
 154:Drivers/CMSIS/Include/cmsis_gcc.h ****     }
 155:Drivers/CMSIS/Include/cmsis_gcc.h ****   }
 156:Drivers/CMSIS/Include/cmsis_gcc.h ****  
 157:Drivers/CMSIS/Include/cmsis_gcc.h ****   for (__zero_table_t const* pTable = &__zero_table_start__; pTable < &__zero_table_end__; ++pTable
 158:Drivers/CMSIS/Include/cmsis_gcc.h ****     for(uint32_t i=0u; i<pTable->wlen; ++i) {
 159:Drivers/CMSIS/Include/cmsis_gcc.h ****       pTable->dest[i] = 0u;
 160:Drivers/CMSIS/Include/cmsis_gcc.h ****     }
 161:Drivers/CMSIS/Include/cmsis_gcc.h ****   }
 162:Drivers/CMSIS/Include/cmsis_gcc.h ****  
 163:Drivers/CMSIS/Include/cmsis_gcc.h ****   _start();
 164:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 165:Drivers/CMSIS/Include/cmsis_gcc.h ****   
 166:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __PROGRAM_START           __cmsis_start
 167:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc1qDhUD.s 			page 24


 168:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 169:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef __INITIAL_SP
 170:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __INITIAL_SP              __StackTop
 171:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 172:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 173:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef __STACK_LIMIT
 174:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __STACK_LIMIT             __StackLimit
 175:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 176:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 177:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef __VECTOR_TABLE
 178:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __VECTOR_TABLE            __Vectors
 179:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 180:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 181:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef __VECTOR_TABLE_ATTRIBUTE
 182:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __VECTOR_TABLE_ATTRIBUTE  __attribute((used, section(".vectors")))
 183:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 184:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 185:Drivers/CMSIS/Include/cmsis_gcc.h **** /* ###########################  Core Function Access  ########################### */
 186:Drivers/CMSIS/Include/cmsis_gcc.h **** /** \ingroup  CMSIS_Core_FunctionInterface
 187:Drivers/CMSIS/Include/cmsis_gcc.h ****     \defgroup CMSIS_Core_RegAccFunctions CMSIS Core Register Access Functions
 188:Drivers/CMSIS/Include/cmsis_gcc.h ****   @{
 189:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 190:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 191:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 192:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Enable IRQ Interrupts
 193:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Enables IRQ interrupts by clearing the I-bit in the CPSR.
 194:Drivers/CMSIS/Include/cmsis_gcc.h ****            Can only be executed in Privileged modes.
 195:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 196:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __enable_irq(void)
 197:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 198:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("cpsie i" : : : "memory");
 199:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 200:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 201:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 202:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 203:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Disable IRQ Interrupts
 204:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Disables IRQ interrupts by setting the I-bit in the CPSR.
 205:Drivers/CMSIS/Include/cmsis_gcc.h ****            Can only be executed in Privileged modes.
 206:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 207:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __disable_irq(void)
 588              		.loc 2 207 27 view .LVU160
 589              	.LBB13:
 208:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 209:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("cpsid i" : : : "memory");
 590              		.loc 2 209 3 view .LVU161
 591              		.syntax unified
 592              	@ 209 "Drivers/CMSIS/Include/cmsis_gcc.h" 1
 593 0000 72B6     		cpsid i
 594              	@ 0 "" 2
 595              		.thumb
 596              		.syntax unified
 597              	.L23:
 598              	.LBE13:
 599              	.LBE12:
 428:Core/Src/main.c ****   while (1)
 600              		.loc 1 428 3 view .LVU162
 429:Core/Src/main.c ****   {
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc1qDhUD.s 			page 25


 430:Core/Src/main.c ****   }
 601              		.loc 1 430 3 view .LVU163
 428:Core/Src/main.c ****   while (1)
 602              		.loc 1 428 9 view .LVU164
 603 0002 FEE7     		b	.L23
 604              		.cfi_endproc
 605              	.LFE181:
 607              		.section	.text.SystemClock_Config,"ax",%progbits
 608              		.align	1
 609              		.global	SystemClock_Config
 610              		.syntax unified
 611              		.thumb
 612              		.thumb_func
 614              	SystemClock_Config:
 615              	.LFB176:
 181:Core/Src/main.c ****   RCC_OscInitTypeDef RCC_OscInitStruct = {0};
 616              		.loc 1 181 1 view -0
 617              		.cfi_startproc
 618              		@ args = 0, pretend = 0, frame = 112
 619              		@ frame_needed = 0, uses_anonymous_args = 0
 620 0000 00B5     		push	{lr}
 621              	.LCFI9:
 622              		.cfi_def_cfa_offset 4
 623              		.cfi_offset 14, -4
 624 0002 9DB0     		sub	sp, sp, #116
 625              	.LCFI10:
 626              		.cfi_def_cfa_offset 120
 182:Core/Src/main.c ****   RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};
 627              		.loc 1 182 3 view .LVU166
 182:Core/Src/main.c ****   RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};
 628              		.loc 1 182 22 is_stmt 0 view .LVU167
 629 0004 4C22     		movs	r2, #76
 630 0006 0021     		movs	r1, #0
 631 0008 09A8     		add	r0, sp, #36
 632 000a FFF7FEFF 		bl	memset
 633              	.LVL29:
 183:Core/Src/main.c **** 
 634              		.loc 1 183 3 is_stmt 1 view .LVU168
 183:Core/Src/main.c **** 
 635              		.loc 1 183 22 is_stmt 0 view .LVU169
 636 000e 2022     		movs	r2, #32
 637 0010 0021     		movs	r1, #0
 638 0012 01A8     		add	r0, sp, #4
 639 0014 FFF7FEFF 		bl	memset
 640              	.LVL30:
 187:Core/Src/main.c **** 
 641              		.loc 1 187 3 is_stmt 1 view .LVU170
 642 0018 0220     		movs	r0, #2
 643 001a FFF7FEFF 		bl	HAL_PWREx_ConfigSupply
 644              	.LVL31:
 191:Core/Src/main.c **** 
 645              		.loc 1 191 3 view .LVU171
 646              	.LBB14:
 191:Core/Src/main.c **** 
 647              		.loc 1 191 3 view .LVU172
 648 001e 0023     		movs	r3, #0
 649 0020 0093     		str	r3, [sp]
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc1qDhUD.s 			page 26


 191:Core/Src/main.c **** 
 650              		.loc 1 191 3 view .LVU173
 651 0022 214B     		ldr	r3, .L31
 652 0024 9A69     		ldr	r2, [r3, #24]
 653 0026 42F44042 		orr	r2, r2, #49152
 654 002a 9A61     		str	r2, [r3, #24]
 191:Core/Src/main.c **** 
 655              		.loc 1 191 3 view .LVU174
 656 002c 9B69     		ldr	r3, [r3, #24]
 657 002e 03F44043 		and	r3, r3, #49152
 658 0032 0093     		str	r3, [sp]
 191:Core/Src/main.c **** 
 659              		.loc 1 191 3 view .LVU175
 660 0034 009B     		ldr	r3, [sp]
 661              	.LBE14:
 191:Core/Src/main.c **** 
 662              		.loc 1 191 3 view .LVU176
 193:Core/Src/main.c **** 
 663              		.loc 1 193 3 view .LVU177
 664              	.L25:
 193:Core/Src/main.c **** 
 665              		.loc 1 193 48 discriminator 1 view .LVU178
 193:Core/Src/main.c **** 
 666              		.loc 1 193 9 discriminator 1 view .LVU179
 193:Core/Src/main.c **** 
 667              		.loc 1 193 10 is_stmt 0 discriminator 1 view .LVU180
 668 0036 1C4B     		ldr	r3, .L31
 669 0038 9B69     		ldr	r3, [r3, #24]
 193:Core/Src/main.c **** 
 670              		.loc 1 193 9 discriminator 1 view .LVU181
 671 003a 13F4005F 		tst	r3, #8192
 672 003e FAD0     		beq	.L25
 198:Core/Src/main.c ****   RCC_OscInitStruct.HSEState = RCC_HSE_ON;
 673              		.loc 1 198 3 is_stmt 1 view .LVU182
 198:Core/Src/main.c ****   RCC_OscInitStruct.HSEState = RCC_HSE_ON;
 674              		.loc 1 198 36 is_stmt 0 view .LVU183
 675 0040 0122     		movs	r2, #1
 676 0042 0992     		str	r2, [sp, #36]
 199:Core/Src/main.c ****   RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
 677              		.loc 1 199 3 is_stmt 1 view .LVU184
 199:Core/Src/main.c ****   RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
 678              		.loc 1 199 30 is_stmt 0 view .LVU185
 679 0044 4FF48033 		mov	r3, #65536
 680 0048 0A93     		str	r3, [sp, #40]
 200:Core/Src/main.c ****   RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSE;
 681              		.loc 1 200 3 is_stmt 1 view .LVU186
 200:Core/Src/main.c ****   RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSE;
 682              		.loc 1 200 34 is_stmt 0 view .LVU187
 683 004a 0223     		movs	r3, #2
 684 004c 1293     		str	r3, [sp, #72]
 201:Core/Src/main.c ****   RCC_OscInitStruct.PLL.PLLM = 8;
 685              		.loc 1 201 3 is_stmt 1 view .LVU188
 201:Core/Src/main.c ****   RCC_OscInitStruct.PLL.PLLM = 8;
 686              		.loc 1 201 35 is_stmt 0 view .LVU189
 687 004e 1393     		str	r3, [sp, #76]
 202:Core/Src/main.c ****   RCC_OscInitStruct.PLL.PLLN = 360;
 688              		.loc 1 202 3 is_stmt 1 view .LVU190
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc1qDhUD.s 			page 27


 202:Core/Src/main.c ****   RCC_OscInitStruct.PLL.PLLN = 360;
 689              		.loc 1 202 30 is_stmt 0 view .LVU191
 690 0050 0821     		movs	r1, #8
 691 0052 1491     		str	r1, [sp, #80]
 203:Core/Src/main.c ****   RCC_OscInitStruct.PLL.PLLP = 1;
 692              		.loc 1 203 3 is_stmt 1 view .LVU192
 203:Core/Src/main.c ****   RCC_OscInitStruct.PLL.PLLP = 1;
 693              		.loc 1 203 30 is_stmt 0 view .LVU193
 694 0054 4FF4B471 		mov	r1, #360
 695 0058 1591     		str	r1, [sp, #84]
 204:Core/Src/main.c ****   RCC_OscInitStruct.PLL.PLLQ = 4;
 696              		.loc 1 204 3 is_stmt 1 view .LVU194
 204:Core/Src/main.c ****   RCC_OscInitStruct.PLL.PLLQ = 4;
 697              		.loc 1 204 30 is_stmt 0 view .LVU195
 698 005a 1692     		str	r2, [sp, #88]
 205:Core/Src/main.c ****   RCC_OscInitStruct.PLL.PLLR = 2;
 699              		.loc 1 205 3 is_stmt 1 view .LVU196
 205:Core/Src/main.c ****   RCC_OscInitStruct.PLL.PLLR = 2;
 700              		.loc 1 205 30 is_stmt 0 view .LVU197
 701 005c 0422     		movs	r2, #4
 702 005e 1792     		str	r2, [sp, #92]
 206:Core/Src/main.c ****   RCC_OscInitStruct.PLL.PLLRGE = RCC_PLL1VCIRANGE_0;
 703              		.loc 1 206 3 is_stmt 1 view .LVU198
 206:Core/Src/main.c ****   RCC_OscInitStruct.PLL.PLLRGE = RCC_PLL1VCIRANGE_0;
 704              		.loc 1 206 30 is_stmt 0 view .LVU199
 705 0060 1893     		str	r3, [sp, #96]
 207:Core/Src/main.c ****   RCC_OscInitStruct.PLL.PLLVCOSEL = RCC_PLL1VCOMEDIUM;
 706              		.loc 1 207 3 is_stmt 1 view .LVU200
 207:Core/Src/main.c ****   RCC_OscInitStruct.PLL.PLLVCOSEL = RCC_PLL1VCOMEDIUM;
 707              		.loc 1 207 32 is_stmt 0 view .LVU201
 708 0062 0022     		movs	r2, #0
 709 0064 1992     		str	r2, [sp, #100]
 208:Core/Src/main.c ****   RCC_OscInitStruct.PLL.PLLFRACN = 0;
 710              		.loc 1 208 3 is_stmt 1 view .LVU202
 208:Core/Src/main.c ****   RCC_OscInitStruct.PLL.PLLFRACN = 0;
 711              		.loc 1 208 35 is_stmt 0 view .LVU203
 712 0066 1A93     		str	r3, [sp, #104]
 209:Core/Src/main.c ****   if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
 713              		.loc 1 209 3 is_stmt 1 view .LVU204
 209:Core/Src/main.c ****   if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
 714              		.loc 1 209 34 is_stmt 0 view .LVU205
 715 0068 1B92     		str	r2, [sp, #108]
 210:Core/Src/main.c ****   {
 716              		.loc 1 210 3 is_stmt 1 view .LVU206
 210:Core/Src/main.c ****   {
 717              		.loc 1 210 7 is_stmt 0 view .LVU207
 718 006a 09A8     		add	r0, sp, #36
 719 006c FFF7FEFF 		bl	HAL_RCC_OscConfig
 720              	.LVL32:
 210:Core/Src/main.c ****   {
 721              		.loc 1 210 6 discriminator 1 view .LVU208
 722 0070 B0B9     		cbnz	r0, .L29
 217:Core/Src/main.c ****                               |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2
 723              		.loc 1 217 3 is_stmt 1 view .LVU209
 217:Core/Src/main.c ****                               |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2
 724              		.loc 1 217 31 is_stmt 0 view .LVU210
 725 0072 3F23     		movs	r3, #63
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc1qDhUD.s 			page 28


 726 0074 0193     		str	r3, [sp, #4]
 220:Core/Src/main.c ****   RCC_ClkInitStruct.SYSCLKDivider = RCC_SYSCLK_DIV1;
 727              		.loc 1 220 3 is_stmt 1 view .LVU211
 220:Core/Src/main.c ****   RCC_ClkInitStruct.SYSCLKDivider = RCC_SYSCLK_DIV1;
 728              		.loc 1 220 34 is_stmt 0 view .LVU212
 729 0076 0323     		movs	r3, #3
 730 0078 0293     		str	r3, [sp, #8]
 221:Core/Src/main.c ****   RCC_ClkInitStruct.AHBCLKDivider = RCC_HCLK_DIV2;
 731              		.loc 1 221 3 is_stmt 1 view .LVU213
 221:Core/Src/main.c ****   RCC_ClkInitStruct.AHBCLKDivider = RCC_HCLK_DIV2;
 732              		.loc 1 221 35 is_stmt 0 view .LVU214
 733 007a 0023     		movs	r3, #0
 734 007c 0393     		str	r3, [sp, #12]
 222:Core/Src/main.c ****   RCC_ClkInitStruct.APB3CLKDivider = RCC_APB3_DIV2;
 735              		.loc 1 222 3 is_stmt 1 view .LVU215
 222:Core/Src/main.c ****   RCC_ClkInitStruct.APB3CLKDivider = RCC_APB3_DIV2;
 736              		.loc 1 222 35 is_stmt 0 view .LVU216
 737 007e 0823     		movs	r3, #8
 738 0080 0493     		str	r3, [sp, #16]
 223:Core/Src/main.c ****   RCC_ClkInitStruct.APB1CLKDivider = RCC_APB1_DIV2;
 739              		.loc 1 223 3 is_stmt 1 view .LVU217
 223:Core/Src/main.c ****   RCC_ClkInitStruct.APB1CLKDivider = RCC_APB1_DIV2;
 740              		.loc 1 223 36 is_stmt 0 view .LVU218
 741 0082 4023     		movs	r3, #64
 742 0084 0593     		str	r3, [sp, #20]
 224:Core/Src/main.c ****   RCC_ClkInitStruct.APB2CLKDivider = RCC_APB2_DIV2;
 743              		.loc 1 224 3 is_stmt 1 view .LVU219
 224:Core/Src/main.c ****   RCC_ClkInitStruct.APB2CLKDivider = RCC_APB2_DIV2;
 744              		.loc 1 224 36 is_stmt 0 view .LVU220
 745 0086 0693     		str	r3, [sp, #24]
 225:Core/Src/main.c ****   RCC_ClkInitStruct.APB4CLKDivider = RCC_APB4_DIV2;
 746              		.loc 1 225 3 is_stmt 1 view .LVU221
 225:Core/Src/main.c ****   RCC_ClkInitStruct.APB4CLKDivider = RCC_APB4_DIV2;
 747              		.loc 1 225 36 is_stmt 0 view .LVU222
 748 0088 4FF48062 		mov	r2, #1024
 749 008c 0792     		str	r2, [sp, #28]
 226:Core/Src/main.c **** 
 750              		.loc 1 226 3 is_stmt 1 view .LVU223
 226:Core/Src/main.c **** 
 751              		.loc 1 226 36 is_stmt 0 view .LVU224
 752 008e 0893     		str	r3, [sp, #32]
 228:Core/Src/main.c ****   {
 753              		.loc 1 228 3 is_stmt 1 view .LVU225
 228:Core/Src/main.c ****   {
 754              		.loc 1 228 7 is_stmt 0 view .LVU226
 755 0090 0221     		movs	r1, #2
 756 0092 01A8     		add	r0, sp, #4
 757 0094 FFF7FEFF 		bl	HAL_RCC_ClockConfig
 758              	.LVL33:
 228:Core/Src/main.c ****   {
 759              		.loc 1 228 6 discriminator 1 view .LVU227
 760 0098 20B9     		cbnz	r0, .L30
 232:Core/Src/main.c **** 
 761              		.loc 1 232 1 view .LVU228
 762 009a 1DB0     		add	sp, sp, #116
 763              	.LCFI11:
 764              		.cfi_remember_state
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc1qDhUD.s 			page 29


 765              		.cfi_def_cfa_offset 4
 766              		@ sp needed
 767 009c 5DF804FB 		ldr	pc, [sp], #4
 768              	.L29:
 769              	.LCFI12:
 770              		.cfi_restore_state
 212:Core/Src/main.c ****   }
 771              		.loc 1 212 5 is_stmt 1 view .LVU229
 772 00a0 FFF7FEFF 		bl	Error_Handler
 773              	.LVL34:
 774              	.L30:
 230:Core/Src/main.c ****   }
 775              		.loc 1 230 5 view .LVU230
 776 00a4 FFF7FEFF 		bl	Error_Handler
 777              	.LVL35:
 778              	.L32:
 779              		.align	2
 780              	.L31:
 781 00a8 00480258 		.word	1476544512
 782              		.cfi_endproc
 783              	.LFE176:
 785              		.section	.text.main,"ax",%progbits
 786              		.align	1
 787              		.global	main
 788              		.syntax unified
 789              		.thumb
 790              		.thumb_func
 792              	main:
 793              	.LFB175:
  98:Core/Src/main.c **** 
 794              		.loc 1 98 1 view -0
 795              		.cfi_startproc
 796              		@ Volatile: function does not return.
 797              		@ args = 0, pretend = 0, frame = 0
 798              		@ frame_needed = 0, uses_anonymous_args = 0
 799 0000 08B5     		push	{r3, lr}
 800              	.LCFI13:
 801              		.cfi_def_cfa_offset 8
 802              		.cfi_offset 3, -8
 803              		.cfi_offset 14, -4
 105:Core/Src/main.c **** 
 804              		.loc 1 105 3 view .LVU232
 805 0002 FFF7FEFF 		bl	MPU_Config
 806              	.LVL36:
 110:Core/Src/main.c **** 
 807              		.loc 1 110 3 view .LVU233
 808 0006 FFF7FEFF 		bl	HAL_Init
 809              	.LVL37:
 117:Core/Src/main.c **** 
 810              		.loc 1 117 3 view .LVU234
 811 000a FFF7FEFF 		bl	SystemClock_Config
 812              	.LVL38:
 124:Core/Src/main.c ****   /* USER CODE BEGIN 2 */
 813              		.loc 1 124 3 view .LVU235
 814 000e FFF7FEFF 		bl	MX_GPIO_Init
 815              	.LVL39:
 130:Core/Src/main.c **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc1qDhUD.s 			page 30


 816              		.loc 1 130 3 view .LVU236
 817 0012 FFF7FEFF 		bl	osKernelInitialize
 818              	.LVL40:
 150:Core/Src/main.c **** 
 819              		.loc 1 150 3 view .LVU237
 150:Core/Src/main.c **** 
 820              		.loc 1 150 23 is_stmt 0 view .LVU238
 821 0016 054A     		ldr	r2, .L36
 822 0018 0021     		movs	r1, #0
 823 001a 0548     		ldr	r0, .L36+4
 824 001c FFF7FEFF 		bl	osThreadNew
 825              	.LVL41:
 150:Core/Src/main.c **** 
 826              		.loc 1 150 21 discriminator 1 view .LVU239
 827 0020 044B     		ldr	r3, .L36+8
 828 0022 1860     		str	r0, [r3]
 161:Core/Src/main.c **** 
 829              		.loc 1 161 3 is_stmt 1 view .LVU240
 830 0024 FFF7FEFF 		bl	osKernelStart
 831              	.LVL42:
 832              	.L34:
 167:Core/Src/main.c ****   {
 833              		.loc 1 167 3 view .LVU241
 172:Core/Src/main.c ****   /* USER CODE END 3 */
 834              		.loc 1 172 3 view .LVU242
 167:Core/Src/main.c ****   {
 835              		.loc 1 167 9 view .LVU243
 836 0028 FEE7     		b	.L34
 837              	.L37:
 838 002a 00BF     		.align	2
 839              	.L36:
 840 002c 00000000 		.word	defaultTask_attributes
 841 0030 00000000 		.word	StartDefaultTask
 842 0034 00000000 		.word	defaultTaskHandle
 843              		.cfi_endproc
 844              	.LFE175:
 846              		.global	defaultTask_attributes
 847              		.section	.rodata.str1.4,"aMS",%progbits,1
 848              		.align	2
 849              	.LC6:
 850 0000 64656661 		.ascii	"defaultTask\000"
 850      756C7454 
 850      61736B00 
 851              		.section	.rodata.defaultTask_attributes,"a"
 852              		.align	2
 855              	defaultTask_attributes:
 856 0000 00000000 		.word	.LC6
 857 0004 00000000 		.space	4
 858 0008 00000000 		.word	defaultTaskControlBlock
 859 000c 5C000000 		.word	92
 860 0010 00000000 		.word	defaultTaskBuffer
 861 0014 E02E0000 		.word	12000
 862 0018 18000000 		.word	24
 863 001c 00000000 		.space	8
 863      00000000 
 864              		.global	defaultTaskControlBlock
 865              		.section	.bss.defaultTaskControlBlock,"aw",%nobits
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc1qDhUD.s 			page 31


 866              		.align	2
 869              	defaultTaskControlBlock:
 870 0000 00000000 		.space	92
 870      00000000 
 870      00000000 
 870      00000000 
 870      00000000 
 871              		.global	defaultTaskBuffer
 872              		.section	.bss.defaultTaskBuffer,"aw",%nobits
 873              		.align	2
 876              	defaultTaskBuffer:
 877 0000 00000000 		.space	12000
 877      00000000 
 877      00000000 
 877      00000000 
 877      00000000 
 878              		.global	defaultTaskHandle
 879              		.section	.bss.defaultTaskHandle,"aw",%nobits
 880              		.align	2
 883              	defaultTaskHandle:
 884 0000 00000000 		.space	4
 885              		.text
 886              	.Letext0:
 887              		.file 3 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\../lib/gcc/arm-none-eabi/13.3.1/..
 888              		.file 4 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\../lib/gcc/arm-none-eabi/13.3.1/..
 889              		.file 5 "Drivers/CMSIS/Device/ST/STM32H7xx/Include/stm32h723xx.h"
 890              		.file 6 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\../lib/gcc/arm-none-eabi/13.3.1/in
 891              		.file 7 "Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_def.h"
 892              		.file 8 "Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_rcc.h"
 893              		.file 9 "Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_gpio.h"
 894              		.file 10 "Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_dma.h"
 895              		.file 11 "Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_cortex.h"
 896              		.file 12 "Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_tim.h"
 897              		.file 13 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h"
 898              		.file 14 "Middlewares/Third_Party/FreeRTOS/Source/include/FreeRTOS.h"
 899              		.file 15 "Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.h"
 900              		.file 16 "micro_ros_stm32cubemx_utils/microros_static_library/libmicroros/microros_include/rcutils
 901              		.file 17 "micro_ros_stm32cubemx_utils/microros_static_library/libmicroros/microros_include/rcl/rcl
 902              		.file 18 "micro_ros_stm32cubemx_utils/microros_static_library/libmicroros/microros_include/rmw/rmw
 903              		.file 19 "micro_ros_stm32cubemx_utils/microros_static_library/libmicroros/microros_include/rcutils
 904              		.file 20 "micro_ros_stm32cubemx_utils/microros_static_library/libmicroros/microros_include/rmw/rmw
 905              		.file 21 "micro_ros_stm32cubemx_utils/microros_static_library/libmicroros/microros_include/rmw/rmw
 906              		.file 22 "micro_ros_stm32cubemx_utils/microros_static_library/libmicroros/microros_include/rcl/rcl
 907              		.file 23 "micro_ros_stm32cubemx_utils/microros_static_library/libmicroros/microros_include/rcl/rcl
 908              		.file 24 "micro_ros_stm32cubemx_utils/microros_static_library/libmicroros/microros_include/rcl/rcl
 909              		.file 25 "micro_ros_stm32cubemx_utils/microros_static_library/libmicroros/microros_include/rosidl_
 910              		.file 26 "micro_ros_stm32cubemx_utils/microros_static_library/libmicroros/microros_include/rcl/rcl
 911              		.file 27 "micro_ros_stm32cubemx_utils/microros_static_library/libmicroros/microros_include/rcl/rcl
 912              		.file 28 "micro_ros_stm32cubemx_utils/microros_static_library/libmicroros/microros_include/rmw/rmw
 913              		.file 29 "micro_ros_stm32cubemx_utils/microros_static_library/libmicroros/microros_include/rclc/ty
 914              		.file 30 "micro_ros_stm32cubemx_utils/microros_static_library/libmicroros/microros_include/rcl_act
 915              		.file 31 "micro_ros_stm32cubemx_utils/microros_static_library/libmicroros/microros_include/uxr/cli
 916              		.file 32 "micro_ros_stm32cubemx_utils/microros_static_library/libmicroros/microros_include/uxr/cli
 917              		.file 33 "micro_ros_stm32cubemx_utils/microros_static_library/libmicroros/microros_include/uxr/cli
 918              		.file 34 "micro_ros_stm32cubemx_utils/microros_static_library/libmicroros/microros_include/std_msg
 919              		.file 35 "micro_ros_stm32cubemx_utils/microros_static_library/libmicroros/microros_include/rclc/pu
 920              		.file 36 "micro_ros_stm32cubemx_utils/microros_static_library/libmicroros/microros_include/rclc/no
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc1qDhUD.s 			page 32


 921              		.file 37 "micro_ros_stm32cubemx_utils/microros_static_library/libmicroros/microros_include/rclc/in
 922              		.file 38 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\../lib/gcc/arm-none-eabi/13.3.1/.
 923              		.file 39 "micro_ros_stm32cubemx_utils/microros_static_library/libmicroros/microros_include/std_msg
 924              		.file 40 "micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.h"
 925              		.file 41 "micro_ros_stm32cubemx_utils/microros_static_library/libmicroros/microros_include/rmw_mic
 926              		.file 42 "Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal.h"
 927              		.file 43 "LWIP/App/lwip.h"
 928              		.file 44 "Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_pwr_ex.h"
 929              		.file 45 "<built-in>"
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc1qDhUD.s 			page 33


DEFINED SYMBOLS
                            *ABS*:00000000 main.c
C:\Users\<USER>\AppData\Local\Temp\cc1qDhUD.s:20     .text.MPU_Config:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cc1qDhUD.s:25     .text.MPU_Config:00000000 MPU_Config
C:\Users\<USER>\AppData\Local\Temp\cc1qDhUD.s:103    .text.MX_GPIO_Init:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cc1qDhUD.s:108    .text.MX_GPIO_Init:00000000 MX_GPIO_Init
C:\Users\<USER>\AppData\Local\Temp\cc1qDhUD.s:321    .text.MX_GPIO_Init:00000114 $d
C:\Users\<USER>\AppData\Local\Temp\cc1qDhUD.s:330    .rodata.StartDefaultTask.str1.4:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\cc1qDhUD.s:349    .text.StartDefaultTask:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cc1qDhUD.s:355    .text.StartDefaultTask:00000000 StartDefaultTask
C:\Users\<USER>\AppData\Local\Temp\cc1qDhUD.s:506    .text.StartDefaultTask:000000b8 $d
C:\Users\<USER>\AppData\Local\Temp\cc1qDhUD.s:525    .text.HAL_TIM_PeriodElapsedCallback:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cc1qDhUD.s:531    .text.HAL_TIM_PeriodElapsedCallback:00000000 HAL_TIM_PeriodElapsedCallback
C:\Users\<USER>\AppData\Local\Temp\cc1qDhUD.s:565    .text.HAL_TIM_PeriodElapsedCallback:00000014 $d
C:\Users\<USER>\AppData\Local\Temp\cc1qDhUD.s:570    .text.Error_Handler:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cc1qDhUD.s:576    .text.Error_Handler:00000000 Error_Handler
C:\Users\<USER>\AppData\Local\Temp\cc1qDhUD.s:608    .text.SystemClock_Config:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cc1qDhUD.s:614    .text.SystemClock_Config:00000000 SystemClock_Config
C:\Users\<USER>\AppData\Local\Temp\cc1qDhUD.s:781    .text.SystemClock_Config:000000a8 $d
C:\Users\<USER>\AppData\Local\Temp\cc1qDhUD.s:786    .text.main:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cc1qDhUD.s:792    .text.main:00000000 main
C:\Users\<USER>\AppData\Local\Temp\cc1qDhUD.s:840    .text.main:0000002c $d
C:\Users\<USER>\AppData\Local\Temp\cc1qDhUD.s:855    .rodata.defaultTask_attributes:00000000 defaultTask_attributes
C:\Users\<USER>\AppData\Local\Temp\cc1qDhUD.s:883    .bss.defaultTaskHandle:00000000 defaultTaskHandle
C:\Users\<USER>\AppData\Local\Temp\cc1qDhUD.s:848    .rodata.str1.4:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\cc1qDhUD.s:852    .rodata.defaultTask_attributes:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\cc1qDhUD.s:869    .bss.defaultTaskControlBlock:00000000 defaultTaskControlBlock
C:\Users\<USER>\AppData\Local\Temp\cc1qDhUD.s:876    .bss.defaultTaskBuffer:00000000 defaultTaskBuffer
C:\Users\<USER>\AppData\Local\Temp\cc1qDhUD.s:866    .bss.defaultTaskControlBlock:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\cc1qDhUD.s:873    .bss.defaultTaskBuffer:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\cc1qDhUD.s:880    .bss.defaultTaskHandle:00000000 $d

UNDEFINED SYMBOLS
HAL_MPU_Disable
HAL_MPU_ConfigRegion
HAL_MPU_Enable
HAL_GPIO_WritePin
HAL_GPIO_Init
MX_LWIP_Init
rmw_uros_set_custom_transport
rcutils_get_zero_initialized_allocator
rcutils_set_default_allocator
rcutils_get_default_allocator
rclc_support_init
rclc_node_init_default
rosidl_typesupport_c__get_message_type_support_handle__std_msgs__msg__Int32
rclc_publisher_init_default
printf
osDelay
rcl_publish
cubemx_transport_read
cubemx_transport_write
cubemx_transport_close
cubemx_transport_open
microros_allocate
microros_deallocate
microros_reallocate
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc1qDhUD.s 			page 34


microros_zero_allocate
HAL_IncTick
memset
HAL_PWREx_ConfigSupply
HAL_RCC_OscConfig
HAL_RCC_ClockConfig
HAL_Init
osKernelInitialize
osThreadNew
osKernelStart
