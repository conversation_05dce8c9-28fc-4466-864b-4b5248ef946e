ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 1


   1              		.cpu cortex-m7
   2              		.arch armv7e-m
   3              		.fpu fpv5-d16
   4              		.eabi_attribute 28, 1
   5              		.eabi_attribute 20, 1
   6              		.eabi_attribute 21, 1
   7              		.eabi_attribute 23, 3
   8              		.eabi_attribute 24, 1
   9              		.eabi_attribute 25, 1
  10              		.eabi_attribute 26, 1
  11              		.eabi_attribute 30, 1
  12              		.eabi_attribute 34, 1
  13              		.eabi_attribute 18, 4
  14              		.file	"api_lib.c"
  15              		.text
  16              	.Ltext0:
  17              		.cfi_sections	.debug_frame
  18              		.file 1 "Middlewares/Third_Party/LwIP/src/api/api_lib.c"
  19              		.section	.text.netconn_apimsg,"ax",%progbits
  20              		.align	1
  21              		.syntax unified
  22              		.thumb
  23              		.thumb_func
  25              	netconn_apimsg:
  26              	.LVL0:
  27              	.LFB174:
   1:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** /**
   2:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @file
   3:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * Sequential API External module
   4:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  *
   5:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @defgroup netconn Netconn API
   6:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @ingroup sequential_api
   7:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * Thread-safe, to be called from non-TCPIP threads only.
   8:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * TX/RX handling based on @ref netbuf (containing @ref pbuf)
   9:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * to avoid copying data around.
  10:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  *
  11:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @defgroup netconn_common Common functions
  12:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @ingroup netconn
  13:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * For use with TCP and UDP
  14:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  *
  15:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @defgroup netconn_tcp TCP only
  16:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @ingroup netconn
  17:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * TCP only functions
  18:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  *
  19:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @defgroup netconn_udp UDP only
  20:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @ingroup netconn
  21:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * UDP only functions
  22:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  */
  23:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
  24:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** /*
  25:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * Copyright (c) 2001-2004 Swedish Institute of Computer Science.
  26:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * All rights reserved.
  27:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  *
  28:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * Redistribution and use in source and binary forms, with or without modification,
  29:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * are permitted provided that the following conditions are met:
  30:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  *
  31:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * 1. Redistributions of source code must retain the above copyright notice,
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 2


  32:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  *    this list of conditions and the following disclaimer.
  33:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * 2. Redistributions in binary form must reproduce the above copyright notice,
  34:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  *    this list of conditions and the following disclaimer in the documentation
  35:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  *    and/or other materials provided with the distribution.
  36:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * 3. The name of the author may not be used to endorse or promote products
  37:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  *    derived from this software without specific prior written permission.
  38:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  *
  39:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR IMPLIED
  40:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
  41:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT
  42:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * SHALL THE AUTHOR BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
  43:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT
  44:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
  45:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
  46:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
  47:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY
  48:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * OF SUCH DAMAGE.
  49:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  *
  50:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * This file is part of the lwIP TCP/IP stack.
  51:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  *
  52:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * Author: Adam Dunkels <<EMAIL>>
  53:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  */
  54:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
  55:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** /* This is the part of the API that is linked with
  56:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****    the application */
  57:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
  58:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #include "lwip/opt.h"
  59:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
  60:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #if LWIP_NETCONN /* don't build if not configured for use in lwipopts.h */
  61:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
  62:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #include "lwip/api.h"
  63:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #include "lwip/memp.h"
  64:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
  65:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #include "lwip/ip.h"
  66:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #include "lwip/raw.h"
  67:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #include "lwip/udp.h"
  68:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #include "lwip/priv/api_msg.h"
  69:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #include "lwip/priv/tcp_priv.h"
  70:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #include "lwip/priv/tcpip_priv.h"
  71:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
  72:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #ifdef LWIP_HOOK_FILENAME
  73:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #include LWIP_HOOK_FILENAME
  74:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #endif
  75:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
  76:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #include <string.h>
  77:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
  78:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #define API_MSG_VAR_REF(name)               API_VAR_REF(name)
  79:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #define API_MSG_VAR_DECLARE(name)           API_VAR_DECLARE(struct api_msg, name)
  80:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #define API_MSG_VAR_ALLOC(name)             API_VAR_ALLOC(struct api_msg, MEMP_API_MSG, name, ERR_M
  81:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #define API_MSG_VAR_ALLOC_RETURN_NULL(name) API_VAR_ALLOC(struct api_msg, MEMP_API_MSG, name, NULL)
  82:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #define API_MSG_VAR_FREE(name)              API_VAR_FREE(MEMP_API_MSG, name)
  83:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
  84:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #if TCP_LISTEN_BACKLOG
  85:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** /* need to allocate API message for accept so empty message pool does not result in event loss
  86:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * see bug #47512: MPU_COMPATIBLE may fail on empty pool */
  87:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #define API_MSG_VAR_ALLOC_ACCEPT(msg) API_MSG_VAR_ALLOC(msg)
  88:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #define API_MSG_VAR_FREE_ACCEPT(msg) API_MSG_VAR_FREE(msg)
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 3


  89:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #else /* TCP_LISTEN_BACKLOG */
  90:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #define API_MSG_VAR_ALLOC_ACCEPT(msg)
  91:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #define API_MSG_VAR_FREE_ACCEPT(msg)
  92:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #endif /* TCP_LISTEN_BACKLOG */
  93:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
  94:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #if LWIP_NETCONN_FULLDUPLEX
  95:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #define NETCONN_RECVMBOX_WAITABLE(conn) (sys_mbox_valid(&(conn)->recvmbox) && (((conn)->flags & NET
  96:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #define NETCONN_ACCEPTMBOX_WAITABLE(conn) (sys_mbox_valid(&(conn)->acceptmbox) && (((conn)->flags &
  97:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #define NETCONN_MBOX_WAITING_INC(conn) SYS_ARCH_INC(conn->mbox_threads_waiting, 1)
  98:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #define NETCONN_MBOX_WAITING_DEC(conn) SYS_ARCH_DEC(conn->mbox_threads_waiting, 1)
  99:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #else /* LWIP_NETCONN_FULLDUPLEX */
 100:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #define NETCONN_RECVMBOX_WAITABLE(conn)   sys_mbox_valid(&(conn)->recvmbox)
 101:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #define NETCONN_ACCEPTMBOX_WAITABLE(conn) (sys_mbox_valid(&(conn)->acceptmbox) && (((conn)->flags &
 102:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #define NETCONN_MBOX_WAITING_INC(conn)
 103:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #define NETCONN_MBOX_WAITING_DEC(conn)
 104:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #endif /* LWIP_NETCONN_FULLDUPLEX */
 105:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 106:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** static err_t netconn_close_shutdown(struct netconn *conn, u8_t how);
 107:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 108:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** /**
 109:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * Call the lower part of a netconn_* function
 110:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * This function is then running in the thread context
 111:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * of tcpip_thread and has exclusive access to lwIP core code.
 112:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  *
 113:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @param fn function to call
 114:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @param apimsg a struct containing the function to call and its parameters
 115:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @return ERR_OK if the function was called, another err_t if not
 116:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  */
 117:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** static err_t
 118:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** netconn_apimsg(tcpip_callback_fn fn, struct api_msg *apimsg)
 119:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** {
  28              		.loc 1 119 1 view -0
  29              		.cfi_startproc
  30              		@ args = 0, pretend = 0, frame = 0
  31              		@ frame_needed = 0, uses_anonymous_args = 0
  32              		.loc 1 119 1 is_stmt 0 view .LVU1
  33 0000 10B5     		push	{r4, lr}
  34              	.LCFI0:
  35              		.cfi_def_cfa_offset 8
  36              		.cfi_offset 4, -8
  37              		.cfi_offset 14, -4
  38 0002 0C46     		mov	r4, r1
 120:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   err_t err;
  39              		.loc 1 120 3 is_stmt 1 view .LVU2
 121:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 122:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #ifdef LWIP_DEBUG
 123:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   /* catch functions that don't set err */
 124:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   apimsg->err = ERR_VAL;
 125:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #endif /* LWIP_DEBUG */
 126:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 127:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #if LWIP_NETCONN_SEM_PER_THREAD
 128:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   apimsg->op_completed_sem = LWIP_NETCONN_THREAD_SEM_GET();
 129:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #endif /* LWIP_NETCONN_SEM_PER_THREAD */
 130:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 131:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   err = tcpip_send_msg_wait_sem(fn, apimsg, LWIP_API_MSG_SEM(apimsg));
  40              		.loc 1 131 3 view .LVU3
  41              		.loc 1 131 45 is_stmt 0 view .LVU4
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 4


  42 0004 0A68     		ldr	r2, [r1]
  43              		.loc 1 131 9 view .LVU5
  44 0006 0C32     		adds	r2, r2, #12
  45 0008 FFF7FEFF 		bl	tcpip_send_msg_wait_sem
  46              	.LVL1:
 132:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   if (err == ERR_OK) {
  47              		.loc 1 132 3 is_stmt 1 view .LVU6
  48              		.loc 1 132 6 is_stmt 0 view .LVU7
  49 000c 00B1     		cbz	r0, .L4
  50              	.L2:
 133:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     return apimsg->err;
 134:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   }
 135:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   return err;
 136:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** }
  51              		.loc 1 136 1 view .LVU8
  52 000e 10BD     		pop	{r4, pc}
  53              	.LVL2:
  54              	.L4:
 133:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     return apimsg->err;
  55              		.loc 1 133 5 is_stmt 1 view .LVU9
 133:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     return apimsg->err;
  56              		.loc 1 133 18 is_stmt 0 view .LVU10
  57 0010 94F90400 		ldrsb	r0, [r4, #4]
  58 0014 FBE7     		b	.L2
  59              		.cfi_endproc
  60              	.LFE174:
  62              		.section	.rodata.netconn_tcp_recvd_msg.str1.4,"aMS",%progbits,1
  63              		.align	2
  64              	.LC0:
  65 0000 4D696464 		.ascii	"Middlewares/Third_Party/LwIP/src/api/api_lib.c\000"
  65      6C657761 
  65      7265732F 
  65      54686972 
  65      645F5061 
  66 002f 00       		.align	2
  67              	.LC1:
  68 0030 6E657463 		.ascii	"netconn_recv_tcp_pbuf: invalid conn\000"
  68      6F6E6E5F 
  68      72656376 
  68      5F746370 
  68      5F706275 
  69              		.align	2
  70              	.LC2:
  71 0054 41737365 		.ascii	"Assertion \"%s\" failed at line %d in %s\012\000"
  71      7274696F 
  71      6E202225 
  71      73222066 
  71      61696C65 
  72              		.section	.text.netconn_tcp_recvd_msg,"ax",%progbits
  73              		.align	1
  74              		.syntax unified
  75              		.thumb
  76              		.thumb_func
  78              	netconn_tcp_recvd_msg:
  79              	.LVL3:
  80              	.LFB186:
 137:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 5


 138:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** /**
 139:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * Create a new netconn (of a specific type) that has a callback function.
 140:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * The corresponding pcb is also created.
 141:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  *
 142:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @param t the type of 'connection' to create (@see enum netconn_type)
 143:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @param proto the IP protocol for RAW IP pcbs
 144:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @param callback a function to call on status changes (RX available, TX'ed)
 145:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @return a newly allocated struct netconn or
 146:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  *         NULL on memory error
 147:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  */
 148:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** struct netconn *
 149:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** netconn_new_with_proto_and_callback(enum netconn_type t, u8_t proto, netconn_callback callback)
 150:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** {
 151:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   struct netconn *conn;
 152:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_DECLARE(msg);
 153:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_ALLOC_RETURN_NULL(msg);
 154:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 155:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   conn = netconn_alloc(t, callback);
 156:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   if (conn != NULL) {
 157:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     err_t err;
 158:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 159:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     API_MSG_VAR_REF(msg).msg.n.proto = proto;
 160:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     API_MSG_VAR_REF(msg).conn = conn;
 161:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     err = netconn_apimsg(lwip_netconn_do_newconn, &API_MSG_VAR_REF(msg));
 162:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     if (err != ERR_OK) {
 163:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       LWIP_ASSERT("freeing conn without freeing pcb", conn->pcb.tcp == NULL);
 164:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       LWIP_ASSERT("conn has no recvmbox", sys_mbox_valid(&conn->recvmbox));
 165:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #if LWIP_TCP
 166:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       LWIP_ASSERT("conn->acceptmbox shouldn't exist", !sys_mbox_valid(&conn->acceptmbox));
 167:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #endif /* LWIP_TCP */
 168:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #if !LWIP_NETCONN_SEM_PER_THREAD
 169:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       LWIP_ASSERT("conn has no op_completed", sys_sem_valid(&conn->op_completed));
 170:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       sys_sem_free(&conn->op_completed);
 171:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #endif /* !LWIP_NETCONN_SEM_PER_THREAD */
 172:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       sys_mbox_free(&conn->recvmbox);
 173:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       memp_free(MEMP_NETCONN, conn);
 174:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       API_MSG_VAR_FREE(msg);
 175:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       return NULL;
 176:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     }
 177:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   }
 178:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_FREE(msg);
 179:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   return conn;
 180:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** }
 181:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 182:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** /**
 183:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @ingroup netconn_common
 184:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * Close a netconn 'connection' and free all its resources but not the netconn itself.
 185:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * UDP and RAW connection are completely closed, TCP pcbs might still be in a waitstate
 186:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * after this returns.
 187:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  *
 188:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @param conn the netconn to delete
 189:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @return ERR_OK if the connection was deleted
 190:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  */
 191:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** err_t
 192:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** netconn_prepare_delete(struct netconn *conn)
 193:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** {
 194:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   err_t err;
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 6


 195:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_DECLARE(msg);
 196:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 197:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   /* No ASSERT here because possible to get a (conn == NULL) if we got an accept error */
 198:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   if (conn == NULL) {
 199:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     return ERR_OK;
 200:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   }
 201:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 202:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_ALLOC(msg);
 203:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_REF(msg).conn = conn;
 204:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #if LWIP_SO_SNDTIMEO || LWIP_SO_LINGER
 205:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   /* get the time we started, which is later compared to
 206:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****      sys_now() + conn->send_timeout */
 207:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_REF(msg).msg.sd.time_started = sys_now();
 208:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #else /* LWIP_SO_SNDTIMEO || LWIP_SO_LINGER */
 209:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #if LWIP_TCP
 210:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_REF(msg).msg.sd.polls_left =
 211:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     ((LWIP_TCP_CLOSE_TIMEOUT_MS_DEFAULT + TCP_SLOW_INTERVAL - 1) / TCP_SLOW_INTERVAL) + 1;
 212:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #endif /* LWIP_TCP */
 213:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #endif /* LWIP_SO_SNDTIMEO || LWIP_SO_LINGER */
 214:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   err = netconn_apimsg(lwip_netconn_do_delconn, &API_MSG_VAR_REF(msg));
 215:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_FREE(msg);
 216:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 217:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   if (err != ERR_OK) {
 218:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     return err;
 219:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   }
 220:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   return ERR_OK;
 221:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** }
 222:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 223:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** /**
 224:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @ingroup netconn_common
 225:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * Close a netconn 'connection' and free its resources.
 226:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * UDP and RAW connection are completely closed, TCP pcbs might still be in a waitstate
 227:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * after this returns.
 228:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  *
 229:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @param conn the netconn to delete
 230:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @return ERR_OK if the connection was deleted
 231:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  */
 232:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** err_t
 233:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** netconn_delete(struct netconn *conn)
 234:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** {
 235:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   err_t err;
 236:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 237:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   /* No ASSERT here because possible to get a (conn == NULL) if we got an accept error */
 238:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   if (conn == NULL) {
 239:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     return ERR_OK;
 240:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   }
 241:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 242:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #if LWIP_NETCONN_FULLDUPLEX
 243:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   if (conn->flags & NETCONN_FLAG_MBOXINVALID) {
 244:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     /* Already called netconn_prepare_delete() before */
 245:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     err = ERR_OK;
 246:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   } else
 247:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #endif /* LWIP_NETCONN_FULLDUPLEX */
 248:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   {
 249:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     err = netconn_prepare_delete(conn);
 250:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   }
 251:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   if (err == ERR_OK) {
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 7


 252:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     netconn_free(conn);
 253:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   }
 254:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   return err;
 255:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** }
 256:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 257:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** /**
 258:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * Get the local or remote IP address and port of a netconn.
 259:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * For RAW netconns, this returns the protocol instead of a port!
 260:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  *
 261:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @param conn the netconn to query
 262:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @param addr a pointer to which to save the IP address
 263:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @param port a pointer to which to save the port (or protocol for RAW)
 264:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @param local 1 to get the local IP address, 0 to get the remote one
 265:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @return ERR_CONN for invalid connections
 266:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  *         ERR_OK if the information was retrieved
 267:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  */
 268:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** err_t
 269:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** netconn_getaddr(struct netconn *conn, ip_addr_t *addr, u16_t *port, u8_t local)
 270:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** {
 271:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_DECLARE(msg);
 272:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   err_t err;
 273:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 274:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_getaddr: invalid conn", (conn != NULL), return ERR_ARG;);
 275:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_getaddr: invalid addr", (addr != NULL), return ERR_ARG;);
 276:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_getaddr: invalid port", (port != NULL), return ERR_ARG;);
 277:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 278:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_ALLOC(msg);
 279:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_REF(msg).conn = conn;
 280:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_REF(msg).msg.ad.local = local;
 281:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #if LWIP_MPU_COMPATIBLE
 282:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   err = netconn_apimsg(lwip_netconn_do_getaddr, &API_MSG_VAR_REF(msg));
 283:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   *addr = msg->msg.ad.ipaddr;
 284:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   *port = msg->msg.ad.port;
 285:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #else /* LWIP_MPU_COMPATIBLE */
 286:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   msg.msg.ad.ipaddr = addr;
 287:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   msg.msg.ad.port = port;
 288:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   err = netconn_apimsg(lwip_netconn_do_getaddr, &msg);
 289:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #endif /* LWIP_MPU_COMPATIBLE */
 290:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_FREE(msg);
 291:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 292:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   return err;
 293:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** }
 294:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 295:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** /**
 296:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @ingroup netconn_common
 297:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * Bind a netconn to a specific local IP address and port.
 298:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * Binding one netconn twice might not always be checked correctly!
 299:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  *
 300:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @param conn the netconn to bind
 301:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @param addr the local IP address to bind the netconn to
 302:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  *             (use IP4_ADDR_ANY/IP6_ADDR_ANY to bind to all addresses)
 303:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @param port the local port to bind the netconn to (not used for RAW)
 304:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @return ERR_OK if bound, any other err_t on failure
 305:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  */
 306:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** err_t
 307:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** netconn_bind(struct netconn *conn, const ip_addr_t *addr, u16_t port)
 308:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** {
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 8


 309:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_DECLARE(msg);
 310:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   err_t err;
 311:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 312:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_bind: invalid conn", (conn != NULL), return ERR_ARG;);
 313:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 314:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #if LWIP_IPV4
 315:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   /* Don't propagate NULL pointer (IP_ADDR_ANY alias) to subsequent functions */
 316:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   if (addr == NULL) {
 317:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     addr = IP4_ADDR_ANY;
 318:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   }
 319:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #endif /* LWIP_IPV4 */
 320:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 321:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #if LWIP_IPV4 && LWIP_IPV6
 322:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   /* "Socket API like" dual-stack support: If IP to bind to is IP6_ADDR_ANY,
 323:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****    * and NETCONN_FLAG_IPV6_V6ONLY is 0, use IP_ANY_TYPE to bind
 324:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****    */
 325:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   if ((netconn_get_ipv6only(conn) == 0) &&
 326:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       ip_addr_cmp(addr, IP6_ADDR_ANY)) {
 327:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     addr = IP_ANY_TYPE;
 328:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   }
 329:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #endif /* LWIP_IPV4 && LWIP_IPV6 */
 330:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 331:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_ALLOC(msg);
 332:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_REF(msg).conn = conn;
 333:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_REF(msg).msg.bc.ipaddr = API_MSG_VAR_REF(addr);
 334:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_REF(msg).msg.bc.port = port;
 335:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   err = netconn_apimsg(lwip_netconn_do_bind, &API_MSG_VAR_REF(msg));
 336:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_FREE(msg);
 337:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 338:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   return err;
 339:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** }
 340:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 341:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** /**
 342:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @ingroup netconn_common
 343:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * Bind a netconn to a specific interface and port.
 344:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * Binding one netconn twice might not always be checked correctly!
 345:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  *
 346:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @param conn the netconn to bind
 347:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @param if_idx the local interface index to bind the netconn to
 348:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @return ERR_OK if bound, any other err_t on failure
 349:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  */
 350:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** err_t
 351:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** netconn_bind_if(struct netconn *conn, u8_t if_idx)
 352:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** {
 353:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_DECLARE(msg);
 354:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   err_t err;
 355:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 356:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_bind_if: invalid conn", (conn != NULL), return ERR_ARG;);
 357:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 358:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_ALLOC(msg);
 359:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_REF(msg).conn = conn;
 360:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_REF(msg).msg.bc.if_idx = if_idx;
 361:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   err = netconn_apimsg(lwip_netconn_do_bind_if, &API_MSG_VAR_REF(msg));
 362:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_FREE(msg);
 363:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 364:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   return err;
 365:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** }
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 9


 366:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 367:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** /**
 368:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @ingroup netconn_common
 369:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * Connect a netconn to a specific remote IP address and port.
 370:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  *
 371:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @param conn the netconn to connect
 372:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @param addr the remote IP address to connect to
 373:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @param port the remote port to connect to (no used for RAW)
 374:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @return ERR_OK if connected, return value of tcp_/udp_/raw_connect otherwise
 375:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  */
 376:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** err_t
 377:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** netconn_connect(struct netconn *conn, const ip_addr_t *addr, u16_t port)
 378:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** {
 379:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_DECLARE(msg);
 380:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   err_t err;
 381:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 382:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_connect: invalid conn", (conn != NULL), return ERR_ARG;);
 383:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 384:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #if LWIP_IPV4
 385:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   /* Don't propagate NULL pointer (IP_ADDR_ANY alias) to subsequent functions */
 386:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   if (addr == NULL) {
 387:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     addr = IP4_ADDR_ANY;
 388:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   }
 389:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #endif /* LWIP_IPV4 */
 390:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 391:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_ALLOC(msg);
 392:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_REF(msg).conn = conn;
 393:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_REF(msg).msg.bc.ipaddr = API_MSG_VAR_REF(addr);
 394:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_REF(msg).msg.bc.port = port;
 395:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   err = netconn_apimsg(lwip_netconn_do_connect, &API_MSG_VAR_REF(msg));
 396:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_FREE(msg);
 397:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 398:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   return err;
 399:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** }
 400:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 401:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** /**
 402:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @ingroup netconn_udp
 403:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * Disconnect a netconn from its current peer (only valid for UDP netconns).
 404:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  *
 405:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @param conn the netconn to disconnect
 406:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @return See @ref err_t
 407:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  */
 408:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** err_t
 409:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** netconn_disconnect(struct netconn *conn)
 410:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** {
 411:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_DECLARE(msg);
 412:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   err_t err;
 413:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 414:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_disconnect: invalid conn", (conn != NULL), return ERR_ARG;);
 415:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 416:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_ALLOC(msg);
 417:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_REF(msg).conn = conn;
 418:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   err = netconn_apimsg(lwip_netconn_do_disconnect, &API_MSG_VAR_REF(msg));
 419:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_FREE(msg);
 420:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 421:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   return err;
 422:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** }
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 10


 423:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 424:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** /**
 425:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @ingroup netconn_tcp
 426:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * Set a TCP netconn into listen mode
 427:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  *
 428:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @param conn the tcp netconn to set to listen mode
 429:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @param backlog the listen backlog, only used if TCP_LISTEN_BACKLOG==1
 430:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @return ERR_OK if the netconn was set to listen (UDP and RAW netconns
 431:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  *         don't return any error (yet?))
 432:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  */
 433:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** err_t
 434:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** netconn_listen_with_backlog(struct netconn *conn, u8_t backlog)
 435:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** {
 436:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #if LWIP_TCP
 437:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_DECLARE(msg);
 438:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   err_t err;
 439:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 440:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   /* This does no harm. If TCP_LISTEN_BACKLOG is off, backlog is unused. */
 441:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_UNUSED_ARG(backlog);
 442:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 443:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_listen: invalid conn", (conn != NULL), return ERR_ARG;);
 444:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 445:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_ALLOC(msg);
 446:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_REF(msg).conn = conn;
 447:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #if TCP_LISTEN_BACKLOG
 448:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_REF(msg).msg.lb.backlog = backlog;
 449:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #endif /* TCP_LISTEN_BACKLOG */
 450:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   err = netconn_apimsg(lwip_netconn_do_listen, &API_MSG_VAR_REF(msg));
 451:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_FREE(msg);
 452:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 453:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   return err;
 454:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #else /* LWIP_TCP */
 455:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_UNUSED_ARG(conn);
 456:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_UNUSED_ARG(backlog);
 457:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   return ERR_ARG;
 458:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #endif /* LWIP_TCP */
 459:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** }
 460:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 461:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** /**
 462:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @ingroup netconn_tcp
 463:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * Accept a new connection on a TCP listening netconn.
 464:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  *
 465:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @param conn the TCP listen netconn
 466:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @param new_conn pointer where the new connection is stored
 467:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @return ERR_OK if a new connection has been received or an error
 468:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  *                code otherwise
 469:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  */
 470:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** err_t
 471:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** netconn_accept(struct netconn *conn, struct netconn **new_conn)
 472:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** {
 473:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #if LWIP_TCP
 474:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   err_t err;
 475:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   void *accept_ptr;
 476:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   struct netconn *newconn;
 477:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #if TCP_LISTEN_BACKLOG
 478:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_DECLARE(msg);
 479:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #endif /* TCP_LISTEN_BACKLOG */
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 11


 480:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 481:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_accept: invalid pointer",    (new_conn != NULL),                  return ERR_
 482:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   *new_conn = NULL;
 483:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_accept: invalid conn",       (conn != NULL),                      return ERR_
 484:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 485:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   /* NOTE: Although the opengroup spec says a pending error shall be returned to
 486:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****            send/recv/getsockopt(SO_ERROR) only, we return it for listening
 487:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****            connections also, to handle embedded-system errors */
 488:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   err = netconn_err(conn);
 489:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   if (err != ERR_OK) {
 490:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     /* return pending error */
 491:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     return err;
 492:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   }
 493:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   if (!NETCONN_ACCEPTMBOX_WAITABLE(conn)) {
 494:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     /* don't accept if closed: this might block the application task
 495:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****        waiting on acceptmbox forever! */
 496:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     return ERR_CLSD;
 497:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   }
 498:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 499:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_ALLOC_ACCEPT(msg);
 500:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 501:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   NETCONN_MBOX_WAITING_INC(conn);
 502:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   if (netconn_is_nonblocking(conn)) {
 503:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     if (sys_arch_mbox_tryfetch(&conn->acceptmbox, &accept_ptr) == SYS_ARCH_TIMEOUT) {
 504:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       API_MSG_VAR_FREE_ACCEPT(msg);
 505:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       NETCONN_MBOX_WAITING_DEC(conn);
 506:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       return ERR_WOULDBLOCK;
 507:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     }
 508:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   } else {
 509:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #if LWIP_SO_RCVTIMEO
 510:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     if (sys_arch_mbox_fetch(&conn->acceptmbox, &accept_ptr, conn->recv_timeout) == SYS_ARCH_TIMEOUT
 511:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       API_MSG_VAR_FREE_ACCEPT(msg);
 512:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       NETCONN_MBOX_WAITING_DEC(conn);
 513:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       return ERR_TIMEOUT;
 514:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     }
 515:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #else
 516:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     sys_arch_mbox_fetch(&conn->acceptmbox, &accept_ptr, 0);
 517:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #endif /* LWIP_SO_RCVTIMEO*/
 518:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   }
 519:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   NETCONN_MBOX_WAITING_DEC(conn);
 520:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #if LWIP_NETCONN_FULLDUPLEX
 521:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   if (conn->flags & NETCONN_FLAG_MBOXINVALID) {
 522:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     if (lwip_netconn_is_deallocated_msg(accept_ptr)) {
 523:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       /* the netconn has been closed from another thread */
 524:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       API_MSG_VAR_FREE_ACCEPT(msg);
 525:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       return ERR_CONN;
 526:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     }
 527:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   }
 528:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #endif
 529:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 530:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   /* Register event with callback */
 531:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_EVENT(conn, NETCONN_EVT_RCVMINUS, 0);
 532:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 533:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   if (lwip_netconn_is_err_msg(accept_ptr, &err)) {
 534:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     /* a connection has been aborted: e.g. out of pcbs or out of netconns during accept */
 535:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     API_MSG_VAR_FREE_ACCEPT(msg);
 536:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     return err;
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 12


 537:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   }
 538:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   if (accept_ptr == NULL) {
 539:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     /* connection has been aborted */
 540:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     API_MSG_VAR_FREE_ACCEPT(msg);
 541:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     return ERR_CLSD;
 542:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   }
 543:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   newconn = (struct netconn *)accept_ptr;
 544:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #if TCP_LISTEN_BACKLOG
 545:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   /* Let the stack know that we have accepted the connection. */
 546:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_REF(msg).conn = newconn;
 547:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   /* don't care for the return value of lwip_netconn_do_recv */
 548:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   netconn_apimsg(lwip_netconn_do_accepted, &API_MSG_VAR_REF(msg));
 549:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_FREE(msg);
 550:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #endif /* TCP_LISTEN_BACKLOG */
 551:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 552:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   *new_conn = newconn;
 553:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   /* don't set conn->last_err: it's only ERR_OK, anyway */
 554:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   return ERR_OK;
 555:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #else /* LWIP_TCP */
 556:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_UNUSED_ARG(conn);
 557:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_UNUSED_ARG(new_conn);
 558:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   return ERR_ARG;
 559:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #endif /* LWIP_TCP */
 560:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** }
 561:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 562:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** /**
 563:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @ingroup netconn_common
 564:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * Receive data: actual implementation that doesn't care whether pbuf or netbuf
 565:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * is received (this is internal, it's just here for describing common errors)
 566:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  *
 567:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @param conn the netconn from which to receive data
 568:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @param new_buf pointer where a new pbuf/netbuf is stored when received data
 569:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @param apiflags flags that control function behaviour. For now only:
 570:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * - NETCONN_DONTBLOCK: only read data that is available now, don't wait for more data
 571:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @return ERR_OK if data has been received, an error code otherwise (timeout,
 572:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  *                memory error or another error)
 573:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  *         ERR_CONN if not connected
 574:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  *         ERR_CLSD if TCP connection has been closed
 575:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  *         ERR_WOULDBLOCK if the netconn is nonblocking but would block to wait for data
 576:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  *         ERR_TIMEOUT if the netconn has a receive timeout and no data was received
 577:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  */
 578:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** static err_t
 579:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** netconn_recv_data(struct netconn *conn, void **new_buf, u8_t apiflags)
 580:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** {
 581:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   void *buf = NULL;
 582:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   u16_t len;
 583:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 584:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_recv: invalid pointer", (new_buf != NULL), return ERR_ARG;);
 585:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   *new_buf = NULL;
 586:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_recv: invalid conn",    (conn != NULL),    return ERR_ARG;);
 587:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 588:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   if (!NETCONN_RECVMBOX_WAITABLE(conn)) {
 589:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     err_t err = netconn_err(conn);
 590:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     if (err != ERR_OK) {
 591:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       /* return pending error */
 592:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       return err;
 593:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     }
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 13


 594:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     return ERR_CONN;
 595:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   }
 596:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 597:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   NETCONN_MBOX_WAITING_INC(conn);
 598:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   if (netconn_is_nonblocking(conn) || (apiflags & NETCONN_DONTBLOCK) ||
 599:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       (conn->flags & NETCONN_FLAG_MBOXCLOSED) || (conn->pending_err != ERR_OK)) {
 600:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     if (sys_arch_mbox_tryfetch(&conn->recvmbox, &buf) == SYS_ARCH_TIMEOUT) {
 601:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       err_t err;
 602:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       NETCONN_MBOX_WAITING_DEC(conn);
 603:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       err = netconn_err(conn);
 604:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       if (err != ERR_OK) {
 605:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****         /* return pending error */
 606:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****         return err;
 607:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       }
 608:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       if (conn->flags & NETCONN_FLAG_MBOXCLOSED) {
 609:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****         return ERR_CONN;
 610:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       }
 611:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       return ERR_WOULDBLOCK;
 612:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     }
 613:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   } else {
 614:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #if LWIP_SO_RCVTIMEO
 615:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     if (sys_arch_mbox_fetch(&conn->recvmbox, &buf, conn->recv_timeout) == SYS_ARCH_TIMEOUT) {
 616:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       NETCONN_MBOX_WAITING_DEC(conn);
 617:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       return ERR_TIMEOUT;
 618:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     }
 619:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #else
 620:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     sys_arch_mbox_fetch(&conn->recvmbox, &buf, 0);
 621:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #endif /* LWIP_SO_RCVTIMEO*/
 622:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   }
 623:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   NETCONN_MBOX_WAITING_DEC(conn);
 624:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #if LWIP_NETCONN_FULLDUPLEX
 625:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   if (conn->flags & NETCONN_FLAG_MBOXINVALID) {
 626:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     if (lwip_netconn_is_deallocated_msg(buf)) {
 627:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       /* the netconn has been closed from another thread */
 628:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       API_MSG_VAR_FREE_ACCEPT(msg);
 629:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       return ERR_CONN;
 630:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     }
 631:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   }
 632:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #endif
 633:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 634:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #if LWIP_TCP
 635:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #if (LWIP_UDP || LWIP_RAW)
 636:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   if (NETCONNTYPE_GROUP(conn->type) == NETCONN_TCP)
 637:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #endif /* (LWIP_UDP || LWIP_RAW) */
 638:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   {
 639:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     err_t err;
 640:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     /* Check if this is an error message or a pbuf */
 641:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     if (lwip_netconn_is_err_msg(buf, &err)) {
 642:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       /* new_buf has been zeroed above already */
 643:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       if (err == ERR_CLSD) {
 644:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****         /* connection closed translates to ERR_OK with *new_buf == NULL */
 645:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****         return ERR_OK;
 646:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       }
 647:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       return err;
 648:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     }
 649:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     len = ((struct pbuf *)buf)->tot_len;
 650:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   }
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 14


 651:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #endif /* LWIP_TCP */
 652:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #if LWIP_TCP && (LWIP_UDP || LWIP_RAW)
 653:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   else
 654:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #endif /* LWIP_TCP && (LWIP_UDP || LWIP_RAW) */
 655:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #if (LWIP_UDP || LWIP_RAW)
 656:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   {
 657:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     LWIP_ASSERT("buf != NULL", buf != NULL);
 658:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     len = netbuf_len((struct netbuf *)buf);
 659:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   }
 660:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #endif /* (LWIP_UDP || LWIP_RAW) */
 661:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 662:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #if LWIP_SO_RCVBUF
 663:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   SYS_ARCH_DEC(conn->recv_avail, len);
 664:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #endif /* LWIP_SO_RCVBUF */
 665:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   /* Register event with callback */
 666:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_EVENT(conn, NETCONN_EVT_RCVMINUS, len);
 667:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 668:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_DEBUGF(API_LIB_DEBUG, ("netconn_recv_data: received %p, len=%"U16_F"\n", buf, len));
 669:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 670:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   *new_buf = buf;
 671:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   /* don't set conn->last_err: it's only ERR_OK, anyway */
 672:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   return ERR_OK;
 673:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** }
 674:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 675:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #if LWIP_TCP
 676:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** static err_t
 677:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** netconn_tcp_recvd_msg(struct netconn *conn, size_t len, struct api_msg *msg)
 678:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** {
  81              		.loc 1 678 1 is_stmt 1 view -0
  82              		.cfi_startproc
  83              		@ args = 0, pretend = 0, frame = 0
  84              		@ frame_needed = 0, uses_anonymous_args = 0
  85              		.loc 1 678 1 is_stmt 0 view .LVU12
  86 0000 10B5     		push	{r4, lr}
  87              	.LCFI1:
  88              		.cfi_def_cfa_offset 8
  89              		.cfi_offset 4, -8
  90              		.cfi_offset 14, -4
 679:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_recv_tcp_pbuf: invalid conn", (conn != NULL) &&
  91              		.loc 1 679 3 is_stmt 1 view .LVU13
  92              		.loc 1 679 3 view .LVU14
  93 0002 60B1     		cbz	r0, .L6
  94 0004 0C46     		mov	r4, r1
  95 0006 1146     		mov	r1, r2
  96              	.LVL4:
  97              		.loc 1 679 3 is_stmt 0 discriminator 2 view .LVU15
  98 0008 0378     		ldrb	r3, [r0]	@ zero_extendqisi2
  99 000a 03F0F003 		and	r3, r3, #240
 100 000e 102B     		cmp	r3, #16
 101 0010 05D1     		bne	.L6
 102              		.loc 1 679 3 is_stmt 1 discriminator 4 view .LVU16
 103              		.loc 1 679 3 discriminator 4 view .LVU17
 680:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****              NETCONNTYPE_GROUP(netconn_type(conn)) == NETCONN_TCP, return ERR_ARG;);
 681:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 682:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   msg->conn = conn;
 104              		.loc 1 682 3 view .LVU18
 105              		.loc 1 682 13 is_stmt 0 view .LVU19
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 15


 106 0012 1060     		str	r0, [r2]
 683:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   msg->msg.r.len = len;
 107              		.loc 1 683 3 is_stmt 1 view .LVU20
 108              		.loc 1 683 18 is_stmt 0 view .LVU21
 109 0014 9460     		str	r4, [r2, #8]
 684:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 685:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   return netconn_apimsg(lwip_netconn_do_recv, msg);
 110              		.loc 1 685 3 is_stmt 1 view .LVU22
 111              		.loc 1 685 10 is_stmt 0 view .LVU23
 112 0016 0748     		ldr	r0, .L10
 113              	.LVL5:
 114              		.loc 1 685 10 view .LVU24
 115 0018 FFF7FEFF 		bl	netconn_apimsg
 116              	.LVL6:
 117              	.L8:
 686:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** }
 118              		.loc 1 686 1 view .LVU25
 119 001c 10BD     		pop	{r4, pc}
 120              	.LVL7:
 121              	.L6:
 679:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****              NETCONNTYPE_GROUP(netconn_type(conn)) == NETCONN_TCP, return ERR_ARG;);
 122              		.loc 1 679 3 is_stmt 1 discriminator 3 view .LVU26
 679:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****              NETCONNTYPE_GROUP(netconn_type(conn)) == NETCONN_TCP, return ERR_ARG;);
 123              		.loc 1 679 3 discriminator 3 view .LVU27
 124 001e 064B     		ldr	r3, .L10+4
 125 0020 40F2A722 		movw	r2, #679
 126              	.LVL8:
 679:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****              NETCONNTYPE_GROUP(netconn_type(conn)) == NETCONN_TCP, return ERR_ARG;);
 127              		.loc 1 679 3 is_stmt 0 discriminator 3 view .LVU28
 128 0024 0549     		ldr	r1, .L10+8
 129 0026 0648     		ldr	r0, .L10+12
 130              	.LVL9:
 679:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****              NETCONNTYPE_GROUP(netconn_type(conn)) == NETCONN_TCP, return ERR_ARG;);
 131              		.loc 1 679 3 discriminator 3 view .LVU29
 132 0028 FFF7FEFF 		bl	printf
 133              	.LVL10:
 679:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****              NETCONNTYPE_GROUP(netconn_type(conn)) == NETCONN_TCP, return ERR_ARG;);
 134              		.loc 1 679 3 is_stmt 1 discriminator 1 view .LVU30
 679:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****              NETCONNTYPE_GROUP(netconn_type(conn)) == NETCONN_TCP, return ERR_ARG;);
 135              		.loc 1 679 3 discriminator 1 view .LVU31
 136 002c 6FF00F00 		mvn	r0, #15
 679:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****              NETCONNTYPE_GROUP(netconn_type(conn)) == NETCONN_TCP, return ERR_ARG;);
 137              		.loc 1 679 3 is_stmt 0 view .LVU32
 138 0030 F4E7     		b	.L8
 139              	.L11:
 140 0032 00BF     		.align	2
 141              	.L10:
 142 0034 00000000 		.word	lwip_netconn_do_recv
 143 0038 00000000 		.word	.LC0
 144 003c 30000000 		.word	.LC1
 145 0040 54000000 		.word	.LC2
 146              		.cfi_endproc
 147              	.LFE186:
 149              		.section	.rodata.netconn_close_shutdown.str1.4,"aMS",%progbits,1
 150              		.align	2
 151              	.LC3:
 152 0000 6E657463 		.ascii	"netconn_close: invalid conn\000"
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 16


 152      6F6E6E5F 
 152      636C6F73 
 152      653A2069 
 152      6E76616C 
 153              		.section	.text.netconn_close_shutdown,"ax",%progbits
 154              		.align	1
 155              		.syntax unified
 156              		.thumb
 157              		.thumb_func
 159              	netconn_close_shutdown:
 160              	.LVL11:
 161              	.LFB198:
 687:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 688:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** err_t
 689:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** netconn_tcp_recvd(struct netconn *conn, size_t len)
 690:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** {
 691:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   err_t err;
 692:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_DECLARE(msg);
 693:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_recv_tcp_pbuf: invalid conn", (conn != NULL) &&
 694:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****              NETCONNTYPE_GROUP(netconn_type(conn)) == NETCONN_TCP, return ERR_ARG;);
 695:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 696:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_ALLOC(msg);
 697:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   err = netconn_tcp_recvd_msg(conn, len, &API_VAR_REF(msg));
 698:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_FREE(msg);
 699:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   return err;
 700:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** }
 701:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 702:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** static err_t
 703:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** netconn_recv_data_tcp(struct netconn *conn, struct pbuf **new_buf, u8_t apiflags)
 704:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** {
 705:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   err_t err;
 706:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   struct pbuf *buf;
 707:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_DECLARE(msg);
 708:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #if LWIP_MPU_COMPATIBLE
 709:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   msg = NULL;
 710:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #endif
 711:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 712:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   if (!NETCONN_RECVMBOX_WAITABLE(conn)) {
 713:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     /* This only happens when calling this function more than once *after* receiving FIN */
 714:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     return ERR_CONN;
 715:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   }
 716:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   if (netconn_is_flag_set(conn, NETCONN_FIN_RX_PENDING)) {
 717:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     netconn_clear_flags(conn, NETCONN_FIN_RX_PENDING);
 718:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     goto handle_fin;
 719:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   }
 720:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 721:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   if (!(apiflags & NETCONN_NOAUTORCVD)) {
 722:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     /* need to allocate API message here so empty message pool does not result in event loss
 723:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       * see bug #47512: MPU_COMPATIBLE may fail on empty pool */
 724:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     API_MSG_VAR_ALLOC(msg);
 725:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   }
 726:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 727:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   err = netconn_recv_data(conn, (void **)new_buf, apiflags);
 728:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   if (err != ERR_OK) {
 729:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     if (!(apiflags & NETCONN_NOAUTORCVD)) {
 730:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       API_MSG_VAR_FREE(msg);
 731:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     }
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 17


 732:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     return err;
 733:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   }
 734:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   buf = *new_buf;
 735:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   if (!(apiflags & NETCONN_NOAUTORCVD)) {
 736:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     /* Let the stack know that we have taken the data. */
 737:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     u16_t len = buf ? buf->tot_len : 1;
 738:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     /* don't care for the return value of lwip_netconn_do_recv */
 739:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     /* @todo: this should really be fixed, e.g. by retrying in poll on error */
 740:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     netconn_tcp_recvd_msg(conn, len,  &API_VAR_REF(msg));
 741:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     API_MSG_VAR_FREE(msg);
 742:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   }
 743:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 744:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   /* If we are closed, we indicate that we no longer wish to use the socket */
 745:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   if (buf == NULL) {
 746:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     if (apiflags & NETCONN_NOFIN) {
 747:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       /* received a FIN but the caller cannot handle it right now:
 748:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****          re-enqueue it and return "no data" */
 749:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       netconn_set_flags(conn, NETCONN_FIN_RX_PENDING);
 750:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       return ERR_WOULDBLOCK;
 751:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     } else {
 752:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** handle_fin:
 753:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       API_EVENT(conn, NETCONN_EVT_RCVMINUS, 0);
 754:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       if (conn->pcb.ip == NULL) {
 755:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****         /* race condition: RST during recv */
 756:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****         err = netconn_err(conn);
 757:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****         if (err != ERR_OK) {
 758:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****           return err;
 759:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****         }
 760:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****         return ERR_RST;
 761:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       }
 762:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       /* RX side is closed, so deallocate the recvmbox */
 763:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       netconn_close_shutdown(conn, NETCONN_SHUT_RD);
 764:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       /* Don' store ERR_CLSD as conn->err since we are only half-closed */
 765:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       return ERR_CLSD;
 766:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     }
 767:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   }
 768:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   return err;
 769:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** }
 770:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 771:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** /**
 772:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @ingroup netconn_tcp
 773:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * Receive data (in form of a pbuf) from a TCP netconn
 774:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  *
 775:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @param conn the netconn from which to receive data
 776:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @param new_buf pointer where a new pbuf is stored when received data
 777:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @return ERR_OK if data has been received, an error code otherwise (timeout,
 778:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  *                memory error or another error, @see netconn_recv_data)
 779:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  *         ERR_ARG if conn is not a TCP netconn
 780:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  */
 781:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** err_t
 782:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** netconn_recv_tcp_pbuf(struct netconn *conn, struct pbuf **new_buf)
 783:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** {
 784:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_recv_tcp_pbuf: invalid conn", (conn != NULL) &&
 785:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****              NETCONNTYPE_GROUP(netconn_type(conn)) == NETCONN_TCP, return ERR_ARG;);
 786:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 787:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   return netconn_recv_data_tcp(conn, new_buf, 0);
 788:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** }
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 18


 789:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 790:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** /**
 791:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @ingroup netconn_tcp
 792:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * Receive data (in form of a pbuf) from a TCP netconn
 793:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  *
 794:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @param conn the netconn from which to receive data
 795:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @param new_buf pointer where a new pbuf is stored when received data
 796:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @param apiflags flags that control function behaviour. For now only:
 797:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * - NETCONN_DONTBLOCK: only read data that is available now, don't wait for more data
 798:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @return ERR_OK if data has been received, an error code otherwise (timeout,
 799:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  *                memory error or another error, @see netconn_recv_data)
 800:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  *         ERR_ARG if conn is not a TCP netconn
 801:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  */
 802:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** err_t
 803:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** netconn_recv_tcp_pbuf_flags(struct netconn *conn, struct pbuf **new_buf, u8_t apiflags)
 804:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** {
 805:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_recv_tcp_pbuf: invalid conn", (conn != NULL) &&
 806:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****              NETCONNTYPE_GROUP(netconn_type(conn)) == NETCONN_TCP, return ERR_ARG;);
 807:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 808:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   return netconn_recv_data_tcp(conn, new_buf, apiflags);
 809:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** }
 810:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #endif /* LWIP_TCP */
 811:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 812:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** /**
 813:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * Receive data (in form of a netbuf) from a UDP or RAW netconn
 814:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  *
 815:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @param conn the netconn from which to receive data
 816:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @param new_buf pointer where a new netbuf is stored when received data
 817:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @return ERR_OK if data has been received, an error code otherwise (timeout,
 818:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  *                memory error or another error)
 819:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  *         ERR_ARG if conn is not a UDP/RAW netconn
 820:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  */
 821:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** err_t
 822:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** netconn_recv_udp_raw_netbuf(struct netconn *conn, struct netbuf **new_buf)
 823:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** {
 824:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_recv_udp_raw_netbuf: invalid conn", (conn != NULL) &&
 825:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****              NETCONNTYPE_GROUP(netconn_type(conn)) != NETCONN_TCP, return ERR_ARG;);
 826:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 827:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   return netconn_recv_data(conn, (void **)new_buf, 0);
 828:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** }
 829:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 830:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** /**
 831:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * Receive data (in form of a netbuf) from a UDP or RAW netconn
 832:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  *
 833:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @param conn the netconn from which to receive data
 834:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @param new_buf pointer where a new netbuf is stored when received data
 835:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @param apiflags flags that control function behaviour. For now only:
 836:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * - NETCONN_DONTBLOCK: only read data that is available now, don't wait for more data
 837:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @return ERR_OK if data has been received, an error code otherwise (timeout,
 838:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  *                memory error or another error)
 839:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  *         ERR_ARG if conn is not a UDP/RAW netconn
 840:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  */
 841:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** err_t
 842:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** netconn_recv_udp_raw_netbuf_flags(struct netconn *conn, struct netbuf **new_buf, u8_t apiflags)
 843:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** {
 844:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_recv_udp_raw_netbuf: invalid conn", (conn != NULL) &&
 845:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****              NETCONNTYPE_GROUP(netconn_type(conn)) != NETCONN_TCP, return ERR_ARG;);
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 19


 846:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 847:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   return netconn_recv_data(conn, (void **)new_buf, apiflags);
 848:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** }
 849:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 850:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** /**
 851:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @ingroup netconn_common
 852:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * Receive data (in form of a netbuf containing a packet buffer) from a netconn
 853:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  *
 854:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @param conn the netconn from which to receive data
 855:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @param new_buf pointer where a new netbuf is stored when received data
 856:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @return ERR_OK if data has been received, an error code otherwise (timeout,
 857:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  *                memory error or another error)
 858:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  */
 859:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** err_t
 860:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** netconn_recv(struct netconn *conn, struct netbuf **new_buf)
 861:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** {
 862:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #if LWIP_TCP
 863:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   struct netbuf *buf = NULL;
 864:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   err_t err;
 865:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #endif /* LWIP_TCP */
 866:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 867:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_recv: invalid pointer", (new_buf != NULL), return ERR_ARG;);
 868:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   *new_buf = NULL;
 869:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_recv: invalid conn",    (conn != NULL),    return ERR_ARG;);
 870:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 871:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #if LWIP_TCP
 872:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #if (LWIP_UDP || LWIP_RAW)
 873:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   if (NETCONNTYPE_GROUP(conn->type) == NETCONN_TCP)
 874:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #endif /* (LWIP_UDP || LWIP_RAW) */
 875:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   {
 876:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     struct pbuf *p = NULL;
 877:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     /* This is not a listening netconn, since recvmbox is set */
 878:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 879:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     buf = (struct netbuf *)memp_malloc(MEMP_NETBUF);
 880:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     if (buf == NULL) {
 881:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       return ERR_MEM;
 882:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     }
 883:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 884:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     err = netconn_recv_data_tcp(conn, &p, 0);
 885:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     if (err != ERR_OK) {
 886:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       memp_free(MEMP_NETBUF, buf);
 887:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       return err;
 888:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     }
 889:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     LWIP_ASSERT("p != NULL", p != NULL);
 890:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 891:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     buf->p = p;
 892:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     buf->ptr = p;
 893:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     buf->port = 0;
 894:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     ip_addr_set_zero(&buf->addr);
 895:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     *new_buf = buf;
 896:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     /* don't set conn->last_err: it's only ERR_OK, anyway */
 897:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     return ERR_OK;
 898:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   }
 899:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #endif /* LWIP_TCP */
 900:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #if LWIP_TCP && (LWIP_UDP || LWIP_RAW)
 901:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   else
 902:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #endif /* LWIP_TCP && (LWIP_UDP || LWIP_RAW) */
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 20


 903:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   {
 904:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #if (LWIP_UDP || LWIP_RAW)
 905:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     return netconn_recv_data(conn, (void **)new_buf, 0);
 906:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #endif /* (LWIP_UDP || LWIP_RAW) */
 907:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   }
 908:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** }
 909:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 910:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** /**
 911:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @ingroup netconn_udp
 912:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * Send data (in form of a netbuf) to a specific remote IP address and port.
 913:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * Only to be used for UDP and RAW netconns (not TCP).
 914:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  *
 915:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @param conn the netconn over which to send data
 916:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @param buf a netbuf containing the data to send
 917:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @param addr the remote IP address to which to send the data
 918:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @param port the remote port to which to send the data
 919:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @return ERR_OK if data was sent, any other err_t on error
 920:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  */
 921:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** err_t
 922:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** netconn_sendto(struct netconn *conn, struct netbuf *buf, const ip_addr_t *addr, u16_t port)
 923:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** {
 924:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   if (buf != NULL) {
 925:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     ip_addr_set(&buf->addr, addr);
 926:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     buf->port = port;
 927:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     return netconn_send(conn, buf);
 928:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   }
 929:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   return ERR_VAL;
 930:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** }
 931:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 932:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** /**
 933:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @ingroup netconn_udp
 934:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * Send data over a UDP or RAW netconn (that is already connected).
 935:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  *
 936:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @param conn the UDP or RAW netconn over which to send data
 937:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @param buf a netbuf containing the data to send
 938:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @return ERR_OK if data was sent, any other err_t on error
 939:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  */
 940:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** err_t
 941:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** netconn_send(struct netconn *conn, struct netbuf *buf)
 942:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** {
 943:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_DECLARE(msg);
 944:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   err_t err;
 945:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 946:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_send: invalid conn",  (conn != NULL), return ERR_ARG;);
 947:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 948:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_DEBUGF(API_LIB_DEBUG, ("netconn_send: sending %"U16_F" bytes\n", buf->p->tot_len));
 949:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 950:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_ALLOC(msg);
 951:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_REF(msg).conn = conn;
 952:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_REF(msg).msg.b = buf;
 953:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   err = netconn_apimsg(lwip_netconn_do_send, &API_MSG_VAR_REF(msg));
 954:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_FREE(msg);
 955:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 956:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   return err;
 957:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** }
 958:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 959:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** /**
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 21


 960:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @ingroup netconn_tcp
 961:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * Send data over a TCP netconn.
 962:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  *
 963:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @param conn the TCP netconn over which to send data
 964:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @param dataptr pointer to the application buffer that contains the data to send
 965:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @param size size of the application data to send
 966:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @param apiflags combination of following flags :
 967:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * - NETCONN_COPY: data will be copied into memory belonging to the stack
 968:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * - NETCONN_MORE: for TCP connection, PSH flag will be set on last segment sent
 969:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * - NETCONN_DONTBLOCK: only write the data if all data can be written at once
 970:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @param bytes_written pointer to a location that receives the number of written bytes
 971:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @return ERR_OK if data was sent, any other err_t on error
 972:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  */
 973:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** err_t
 974:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** netconn_write_partly(struct netconn *conn, const void *dataptr, size_t size,
 975:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****                      u8_t apiflags, size_t *bytes_written)
 976:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** {
 977:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   struct netvector vector;
 978:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   vector.ptr = dataptr;
 979:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   vector.len = size;
 980:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   return netconn_write_vectors_partly(conn, &vector, 1, apiflags, bytes_written);
 981:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** }
 982:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 983:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** /**
 984:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * Send vectorized data atomically over a TCP netconn.
 985:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  *
 986:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @param conn the TCP netconn over which to send data
 987:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @param vectors array of vectors containing data to send
 988:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @param vectorcnt number of vectors in the array
 989:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @param apiflags combination of following flags :
 990:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * - NETCONN_COPY: data will be copied into memory belonging to the stack
 991:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * - NETCONN_MORE: for TCP connection, PSH flag will be set on last segment sent
 992:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * - NETCONN_DONTBLOCK: only write the data if all data can be written at once
 993:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @param bytes_written pointer to a location that receives the number of written bytes
 994:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @return ERR_OK if data was sent, any other err_t on error
 995:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  */
 996:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** err_t
 997:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** netconn_write_vectors_partly(struct netconn *conn, struct netvector *vectors, u16_t vectorcnt,
 998:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****                              u8_t apiflags, size_t *bytes_written)
 999:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** {
1000:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_DECLARE(msg);
1001:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   err_t err;
1002:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   u8_t dontblock;
1003:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   size_t size;
1004:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   int i;
1005:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
1006:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_write: invalid conn",  (conn != NULL), return ERR_ARG;);
1007:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_write: invalid conn->type",  (NETCONNTYPE_GROUP(conn->type) == NETCONN_TCP), 
1008:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   dontblock = netconn_is_nonblocking(conn) || (apiflags & NETCONN_DONTBLOCK);
1009:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #if LWIP_SO_SNDTIMEO
1010:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   if (conn->send_timeout != 0) {
1011:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     dontblock = 1;
1012:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   }
1013:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #endif /* LWIP_SO_SNDTIMEO */
1014:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   if (dontblock && !bytes_written) {
1015:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     /* This implies netconn_write() cannot be used for non-blocking send, since
1016:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****        it has no way to return the number of bytes written. */
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 22


1017:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     return ERR_VAL;
1018:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   }
1019:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
1020:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   /* sum up the total size */
1021:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   size = 0;
1022:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   for (i = 0; i < vectorcnt; i++) {
1023:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     size += vectors[i].len;
1024:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     if (size < vectors[i].len) {
1025:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       /* overflow */
1026:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       return ERR_VAL;
1027:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     }
1028:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   }
1029:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   if (size == 0) {
1030:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     return ERR_OK;
1031:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   } else if (size > SSIZE_MAX) {
1032:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     ssize_t limited;
1033:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     /* this is required by the socket layer (cannot send full size_t range) */
1034:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     if (!bytes_written) {
1035:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       return ERR_VAL;
1036:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     }
1037:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     /* limit the amount of data to send */
1038:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     limited = SSIZE_MAX;
1039:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     size = (size_t)limited;
1040:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   }
1041:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
1042:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_ALLOC(msg);
1043:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   /* non-blocking write sends as much  */
1044:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_REF(msg).conn = conn;
1045:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_REF(msg).msg.w.vector = vectors;
1046:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_REF(msg).msg.w.vector_cnt = vectorcnt;
1047:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_REF(msg).msg.w.vector_off = 0;
1048:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_REF(msg).msg.w.apiflags = apiflags;
1049:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_REF(msg).msg.w.len = size;
1050:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_REF(msg).msg.w.offset = 0;
1051:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #if LWIP_SO_SNDTIMEO
1052:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   if (conn->send_timeout != 0) {
1053:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     /* get the time we started, which is later compared to
1054:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****         sys_now() + conn->send_timeout */
1055:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     API_MSG_VAR_REF(msg).msg.w.time_started = sys_now();
1056:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   } else {
1057:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     API_MSG_VAR_REF(msg).msg.w.time_started = 0;
1058:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   }
1059:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #endif /* LWIP_SO_SNDTIMEO */
1060:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
1061:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   /* For locking the core: this _can_ be delayed on low memory/low send buffer,
1062:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****      but if it is, this is done inside api_msg.c:do_write(), so we can use the
1063:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****      non-blocking version here. */
1064:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   err = netconn_apimsg(lwip_netconn_do_write, &API_MSG_VAR_REF(msg));
1065:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   if (err == ERR_OK) {
1066:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     if (bytes_written != NULL) {
1067:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       *bytes_written = API_MSG_VAR_REF(msg).msg.w.offset;
1068:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     }
1069:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     /* for blocking, check all requested bytes were written, NOTE: send_timeout is
1070:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****        treated as dontblock (see dontblock assignment above) */
1071:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     if (!dontblock) {
1072:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       LWIP_ASSERT("do_write failed to write all bytes", API_MSG_VAR_REF(msg).msg.w.offset == size);
1073:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     }
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 23


1074:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   }
1075:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_FREE(msg);
1076:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
1077:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   return err;
1078:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** }
1079:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
1080:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** /**
1081:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @ingroup netconn_tcp
1082:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * Close or shutdown a TCP netconn (doesn't delete it).
1083:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  *
1084:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @param conn the TCP netconn to close or shutdown
1085:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @param how fully close or only shutdown one side?
1086:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @return ERR_OK if the netconn was closed, any other err_t on error
1087:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  */
1088:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** static err_t
1089:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** netconn_close_shutdown(struct netconn *conn, u8_t how)
1090:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** {
 162              		.loc 1 1090 1 is_stmt 1 view -0
 163              		.cfi_startproc
 164              		@ args = 0, pretend = 0, frame = 32
 165              		@ frame_needed = 0, uses_anonymous_args = 0
 166              		.loc 1 1090 1 is_stmt 0 view .LVU34
 167 0000 00B5     		push	{lr}
 168              	.LCFI2:
 169              		.cfi_def_cfa_offset 4
 170              		.cfi_offset 14, -4
 171 0002 89B0     		sub	sp, sp, #36
 172              	.LCFI3:
 173              		.cfi_def_cfa_offset 40
1091:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_DECLARE(msg);
 174              		.loc 1 1091 3 is_stmt 1 view .LVU35
1092:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   err_t err;
 175              		.loc 1 1092 3 view .LVU36
1093:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_UNUSED_ARG(how);
 176              		.loc 1 1093 3 view .LVU37
1094:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
1095:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_close: invalid conn",  (conn != NULL), return ERR_ARG;);
 177              		.loc 1 1095 3 view .LVU38
 178              		.loc 1 1095 3 view .LVU39
 179 0004 60B1     		cbz	r0, .L16
 180              		.loc 1 1095 3 discriminator 2 view .LVU40
 181              		.loc 1 1095 3 discriminator 2 view .LVU41
1096:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
1097:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_ALLOC(msg);
 182              		.loc 1 1097 25 view .LVU42
1098:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_REF(msg).conn = conn;
 183              		.loc 1 1098 3 view .LVU43
 184              		.loc 1 1098 29 is_stmt 0 view .LVU44
 185 0006 0090     		str	r0, [sp]
1099:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #if LWIP_TCP
1100:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   /* shutting down both ends is the same as closing */
1101:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_REF(msg).msg.sd.shut = how;
 186              		.loc 1 1101 3 is_stmt 1 view .LVU45
 187              		.loc 1 1101 36 is_stmt 0 view .LVU46
 188 0008 8DF80810 		strb	r1, [sp, #8]
1102:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #if LWIP_SO_SNDTIMEO || LWIP_SO_LINGER
1103:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   /* get the time we started, which is later compared to
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 24


1104:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****      sys_now() + conn->send_timeout */
1105:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_REF(msg).msg.sd.time_started = sys_now();
1106:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #else /* LWIP_SO_SNDTIMEO || LWIP_SO_LINGER */
1107:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_REF(msg).msg.sd.polls_left =
 189              		.loc 1 1107 3 is_stmt 1 view .LVU47
 190              		.loc 1 1107 42 is_stmt 0 view .LVU48
 191 000c 2923     		movs	r3, #41
 192 000e 8DF80930 		strb	r3, [sp, #9]
1108:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     ((LWIP_TCP_CLOSE_TIMEOUT_MS_DEFAULT + TCP_SLOW_INTERVAL - 1) / TCP_SLOW_INTERVAL) + 1;
1109:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #endif /* LWIP_SO_SNDTIMEO || LWIP_SO_LINGER */
1110:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #endif /* LWIP_TCP */
1111:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   err = netconn_apimsg(lwip_netconn_do_close, &API_MSG_VAR_REF(msg));
 193              		.loc 1 1111 3 is_stmt 1 view .LVU49
 194              		.loc 1 1111 9 is_stmt 0 view .LVU50
 195 0012 6946     		mov	r1, sp
 196              	.LVL12:
 197              		.loc 1 1111 9 view .LVU51
 198 0014 0748     		ldr	r0, .L17
 199              	.LVL13:
 200              		.loc 1 1111 9 view .LVU52
 201 0016 FFF7FEFF 		bl	netconn_apimsg
 202              	.LVL14:
1112:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_FREE(msg);
 203              		.loc 1 1112 24 is_stmt 1 view .LVU53
1113:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
1114:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   return err;
 204              		.loc 1 1114 3 view .LVU54
 205              	.L14:
1115:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** }
 206              		.loc 1 1115 1 is_stmt 0 view .LVU55
 207 001a 09B0     		add	sp, sp, #36
 208              	.LCFI4:
 209              		.cfi_remember_state
 210              		.cfi_def_cfa_offset 4
 211              		@ sp needed
 212 001c 5DF804FB 		ldr	pc, [sp], #4
 213              	.LVL15:
 214              	.L16:
 215              	.LCFI5:
 216              		.cfi_restore_state
1095:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 217              		.loc 1 1095 3 is_stmt 1 discriminator 1 view .LVU56
1095:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 218              		.loc 1 1095 3 discriminator 1 view .LVU57
 219 0020 054B     		ldr	r3, .L17+4
 220 0022 40F24742 		movw	r2, #1095
 221 0026 0549     		ldr	r1, .L17+8
 222              	.LVL16:
1095:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 223              		.loc 1 1095 3 is_stmt 0 discriminator 1 view .LVU58
 224 0028 0548     		ldr	r0, .L17+12
 225              	.LVL17:
1095:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 226              		.loc 1 1095 3 discriminator 1 view .LVU59
 227 002a FFF7FEFF 		bl	printf
 228              	.LVL18:
1095:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 25


 229              		.loc 1 1095 3 is_stmt 1 discriminator 1 view .LVU60
1095:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 230              		.loc 1 1095 3 discriminator 1 view .LVU61
 231 002e 6FF00F00 		mvn	r0, #15
1095:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 232              		.loc 1 1095 3 is_stmt 0 view .LVU62
 233 0032 F2E7     		b	.L14
 234              	.L18:
 235              		.align	2
 236              	.L17:
 237 0034 00000000 		.word	lwip_netconn_do_close
 238 0038 00000000 		.word	.LC0
 239 003c 00000000 		.word	.LC3
 240 0040 54000000 		.word	.LC2
 241              		.cfi_endproc
 242              	.LFE198:
 244              		.section	.rodata.netconn_new_with_proto_and_callback.str1.4,"aMS",%progbits,1
 245              		.align	2
 246              	.LC4:
 247 0000 66726565 		.ascii	"freeing conn without freeing pcb\000"
 247      696E6720 
 247      636F6E6E 
 247      20776974 
 247      686F7574 
 248 0021 000000   		.align	2
 249              	.LC5:
 250 0024 636F6E6E 		.ascii	"conn has no recvmbox\000"
 250      20686173 
 250      206E6F20 
 250      72656376 
 250      6D626F78 
 251 0039 000000   		.align	2
 252              	.LC6:
 253 003c 636F6E6E 		.ascii	"conn->acceptmbox shouldn't exist\000"
 253      2D3E6163 
 253      63657074 
 253      6D626F78 
 253      2073686F 
 254 005d 000000   		.align	2
 255              	.LC7:
 256 0060 636F6E6E 		.ascii	"conn has no op_completed\000"
 256      20686173 
 256      206E6F20 
 256      6F705F63 
 256      6F6D706C 
 257              		.section	.text.netconn_new_with_proto_and_callback,"ax",%progbits
 258              		.align	1
 259              		.global	netconn_new_with_proto_and_callback
 260              		.syntax unified
 261              		.thumb
 262              		.thumb_func
 264              	netconn_new_with_proto_and_callback:
 265              	.LVL19:
 266              	.LFB175:
 150:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   struct netconn *conn;
 267              		.loc 1 150 1 is_stmt 1 view -0
 268              		.cfi_startproc
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 26


 269              		@ args = 0, pretend = 0, frame = 32
 270              		@ frame_needed = 0, uses_anonymous_args = 0
 150:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   struct netconn *conn;
 271              		.loc 1 150 1 is_stmt 0 view .LVU64
 272 0000 70B5     		push	{r4, r5, r6, lr}
 273              	.LCFI6:
 274              		.cfi_def_cfa_offset 16
 275              		.cfi_offset 4, -16
 276              		.cfi_offset 5, -12
 277              		.cfi_offset 6, -8
 278              		.cfi_offset 14, -4
 279 0002 88B0     		sub	sp, sp, #32
 280              	.LCFI7:
 281              		.cfi_def_cfa_offset 48
 282 0004 0C46     		mov	r4, r1
 151:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_DECLARE(msg);
 283              		.loc 1 151 3 is_stmt 1 view .LVU65
 152:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_ALLOC_RETURN_NULL(msg);
 284              		.loc 1 152 3 view .LVU66
 153:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 285              		.loc 1 153 37 view .LVU67
 155:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   if (conn != NULL) {
 286              		.loc 1 155 3 view .LVU68
 155:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   if (conn != NULL) {
 287              		.loc 1 155 10 is_stmt 0 view .LVU69
 288 0006 1146     		mov	r1, r2
 289              	.LVL20:
 155:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   if (conn != NULL) {
 290              		.loc 1 155 10 view .LVU70
 291 0008 FFF7FEFF 		bl	netconn_alloc
 292              	.LVL21:
 156:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     err_t err;
 293              		.loc 1 156 3 is_stmt 1 view .LVU71
 156:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     err_t err;
 294              		.loc 1 156 6 is_stmt 0 view .LVU72
 295 000c 0546     		mov	r5, r0
 296 000e 38B1     		cbz	r0, .L19
 297              	.LBB2:
 157:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 298              		.loc 1 157 5 is_stmt 1 view .LVU73
 159:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     API_MSG_VAR_REF(msg).conn = conn;
 299              		.loc 1 159 5 view .LVU74
 159:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     API_MSG_VAR_REF(msg).conn = conn;
 300              		.loc 1 159 38 is_stmt 0 view .LVU75
 301 0010 8DF80840 		strb	r4, [sp, #8]
 160:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     err = netconn_apimsg(lwip_netconn_do_newconn, &API_MSG_VAR_REF(msg));
 302              		.loc 1 160 5 is_stmt 1 view .LVU76
 160:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     err = netconn_apimsg(lwip_netconn_do_newconn, &API_MSG_VAR_REF(msg));
 303              		.loc 1 160 31 is_stmt 0 view .LVU77
 304 0014 0090     		str	r0, [sp]
 161:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     if (err != ERR_OK) {
 305              		.loc 1 161 5 is_stmt 1 view .LVU78
 161:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     if (err != ERR_OK) {
 306              		.loc 1 161 11 is_stmt 0 view .LVU79
 307 0016 6946     		mov	r1, sp
 308 0018 2048     		ldr	r0, .L30
 309              	.LVL22:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 27


 161:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     if (err != ERR_OK) {
 310              		.loc 1 161 11 view .LVU80
 311 001a FFF7FEFF 		bl	netconn_apimsg
 312              	.LVL23:
 162:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       LWIP_ASSERT("freeing conn without freeing pcb", conn->pcb.tcp == NULL);
 313              		.loc 1 162 5 is_stmt 1 view .LVU81
 162:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       LWIP_ASSERT("freeing conn without freeing pcb", conn->pcb.tcp == NULL);
 314              		.loc 1 162 8 is_stmt 0 view .LVU82
 315 001e 10B9     		cbnz	r0, .L26
 316              	.LVL24:
 317              	.L19:
 162:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       LWIP_ASSERT("freeing conn without freeing pcb", conn->pcb.tcp == NULL);
 318              		.loc 1 162 8 view .LVU83
 319              	.LBE2:
 180:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 320              		.loc 1 180 1 view .LVU84
 321 0020 2846     		mov	r0, r5
 322 0022 08B0     		add	sp, sp, #32
 323              	.LCFI8:
 324              		.cfi_remember_state
 325              		.cfi_def_cfa_offset 16
 326              		@ sp needed
 327 0024 70BD     		pop	{r4, r5, r6, pc}
 328              	.LVL25:
 329              	.L26:
 330              	.LCFI9:
 331              		.cfi_restore_state
 332              	.LBB3:
 163:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       LWIP_ASSERT("conn has no recvmbox", sys_mbox_valid(&conn->recvmbox));
 333              		.loc 1 163 7 is_stmt 1 view .LVU85
 163:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       LWIP_ASSERT("conn has no recvmbox", sys_mbox_valid(&conn->recvmbox));
 334              		.loc 1 163 7 view .LVU86
 335 0026 6B68     		ldr	r3, [r5, #4]
 336 0028 2BB1     		cbz	r3, .L21
 163:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       LWIP_ASSERT("conn has no recvmbox", sys_mbox_valid(&conn->recvmbox));
 337              		.loc 1 163 7 discriminator 1 view .LVU87
 163:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       LWIP_ASSERT("conn has no recvmbox", sys_mbox_valid(&conn->recvmbox));
 338              		.loc 1 163 7 discriminator 1 view .LVU88
 339 002a 1D4B     		ldr	r3, .L30+4
 340 002c A322     		movs	r2, #163
 341 002e 1D49     		ldr	r1, .L30+8
 342 0030 1D48     		ldr	r0, .L30+12
 343              	.LVL26:
 163:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       LWIP_ASSERT("conn has no recvmbox", sys_mbox_valid(&conn->recvmbox));
 344              		.loc 1 163 7 is_stmt 0 discriminator 1 view .LVU89
 345 0032 FFF7FEFF 		bl	printf
 346              	.LVL27:
 347              	.L21:
 163:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       LWIP_ASSERT("conn has no recvmbox", sys_mbox_valid(&conn->recvmbox));
 348              		.loc 1 163 7 is_stmt 1 discriminator 3 view .LVU90
 163:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       LWIP_ASSERT("conn has no recvmbox", sys_mbox_valid(&conn->recvmbox));
 349              		.loc 1 163 7 discriminator 3 view .LVU91
 164:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #if LWIP_TCP
 350              		.loc 1 164 7 view .LVU92
 164:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #if LWIP_TCP
 351              		.loc 1 164 7 view .LVU93
 352 0036 05F11004 		add	r4, r5, #16
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 28


 353 003a 2046     		mov	r0, r4
 354 003c FFF7FEFF 		bl	sys_mbox_valid
 355              	.LVL28:
 164:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #if LWIP_TCP
 356              		.loc 1 164 7 is_stmt 0 discriminator 1 view .LVU94
 357 0040 B0B1     		cbz	r0, .L27
 358              	.L22:
 164:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #if LWIP_TCP
 359              		.loc 1 164 7 is_stmt 1 discriminator 3 view .LVU95
 164:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #if LWIP_TCP
 360              		.loc 1 164 7 discriminator 3 view .LVU96
 166:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #endif /* LWIP_TCP */
 361              		.loc 1 166 7 view .LVU97
 166:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #endif /* LWIP_TCP */
 362              		.loc 1 166 7 view .LVU98
 363 0042 05F11400 		add	r0, r5, #20
 364 0046 FFF7FEFF 		bl	sys_mbox_valid
 365              	.LVL29:
 166:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #endif /* LWIP_TCP */
 366              		.loc 1 166 7 is_stmt 0 discriminator 1 view .LVU99
 367 004a C0B9     		cbnz	r0, .L28
 368              	.L23:
 166:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #endif /* LWIP_TCP */
 369              		.loc 1 166 7 is_stmt 1 discriminator 3 view .LVU100
 166:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #endif /* LWIP_TCP */
 370              		.loc 1 166 7 discriminator 3 view .LVU101
 169:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       sys_sem_free(&conn->op_completed);
 371              		.loc 1 169 7 view .LVU102
 169:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       sys_sem_free(&conn->op_completed);
 372              		.loc 1 169 7 view .LVU103
 373 004c 05F10C06 		add	r6, r5, #12
 374 0050 3046     		mov	r0, r6
 375 0052 FFF7FEFF 		bl	sys_sem_valid
 376              	.LVL30:
 169:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       sys_sem_free(&conn->op_completed);
 377              		.loc 1 169 7 is_stmt 0 discriminator 1 view .LVU104
 378 0056 C8B1     		cbz	r0, .L29
 379              	.L24:
 169:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       sys_sem_free(&conn->op_completed);
 380              		.loc 1 169 7 is_stmt 1 discriminator 3 view .LVU105
 169:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       sys_sem_free(&conn->op_completed);
 381              		.loc 1 169 7 discriminator 3 view .LVU106
 170:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #endif /* !LWIP_NETCONN_SEM_PER_THREAD */
 382              		.loc 1 170 7 view .LVU107
 383 0058 3046     		mov	r0, r6
 384 005a FFF7FEFF 		bl	sys_sem_free
 385              	.LVL31:
 172:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       memp_free(MEMP_NETCONN, conn);
 386              		.loc 1 172 7 view .LVU108
 387 005e 2046     		mov	r0, r4
 388 0060 FFF7FEFF 		bl	sys_mbox_free
 389              	.LVL32:
 173:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       API_MSG_VAR_FREE(msg);
 390              		.loc 1 173 7 view .LVU109
 391 0064 2946     		mov	r1, r5
 392 0066 0720     		movs	r0, #7
 393 0068 FFF7FEFF 		bl	memp_free
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 29


 394              	.LVL33:
 174:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       return NULL;
 395              		.loc 1 174 28 view .LVU110
 175:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     }
 396              		.loc 1 175 7 view .LVU111
 175:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     }
 397              		.loc 1 175 14 is_stmt 0 view .LVU112
 398 006c 0025     		movs	r5, #0
 399              	.LVL34:
 175:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     }
 400              		.loc 1 175 14 view .LVU113
 401 006e D7E7     		b	.L19
 402              	.LVL35:
 403              	.L27:
 164:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #if LWIP_TCP
 404              		.loc 1 164 7 is_stmt 1 discriminator 1 view .LVU114
 164:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #if LWIP_TCP
 405              		.loc 1 164 7 discriminator 1 view .LVU115
 406 0070 0B4B     		ldr	r3, .L30+4
 407 0072 A422     		movs	r2, #164
 408 0074 0D49     		ldr	r1, .L30+16
 409 0076 0C48     		ldr	r0, .L30+12
 410 0078 FFF7FEFF 		bl	printf
 411              	.LVL36:
 412 007c E1E7     		b	.L22
 413              	.L28:
 166:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #endif /* LWIP_TCP */
 414              		.loc 1 166 7 discriminator 1 view .LVU116
 166:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #endif /* LWIP_TCP */
 415              		.loc 1 166 7 discriminator 1 view .LVU117
 416 007e 084B     		ldr	r3, .L30+4
 417 0080 A622     		movs	r2, #166
 418 0082 0B49     		ldr	r1, .L30+20
 419 0084 0848     		ldr	r0, .L30+12
 420 0086 FFF7FEFF 		bl	printf
 421              	.LVL37:
 422 008a DFE7     		b	.L23
 423              	.L29:
 169:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       sys_sem_free(&conn->op_completed);
 424              		.loc 1 169 7 discriminator 1 view .LVU118
 169:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       sys_sem_free(&conn->op_completed);
 425              		.loc 1 169 7 discriminator 1 view .LVU119
 426 008c 044B     		ldr	r3, .L30+4
 427 008e A922     		movs	r2, #169
 428 0090 0849     		ldr	r1, .L30+24
 429 0092 0548     		ldr	r0, .L30+12
 430 0094 FFF7FEFF 		bl	printf
 431              	.LVL38:
 432 0098 DEE7     		b	.L24
 433              	.L31:
 434 009a 00BF     		.align	2
 435              	.L30:
 436 009c 00000000 		.word	lwip_netconn_do_newconn
 437 00a0 00000000 		.word	.LC0
 438 00a4 00000000 		.word	.LC4
 439 00a8 54000000 		.word	.LC2
 440 00ac 24000000 		.word	.LC5
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 30


 441 00b0 3C000000 		.word	.LC6
 442 00b4 60000000 		.word	.LC7
 443              	.LBE3:
 444              		.cfi_endproc
 445              	.LFE175:
 447              		.section	.text.netconn_prepare_delete,"ax",%progbits
 448              		.align	1
 449              		.global	netconn_prepare_delete
 450              		.syntax unified
 451              		.thumb
 452              		.thumb_func
 454              	netconn_prepare_delete:
 455              	.LVL39:
 456              	.LFB176:
 193:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   err_t err;
 457              		.loc 1 193 1 view -0
 458              		.cfi_startproc
 459              		@ args = 0, pretend = 0, frame = 32
 460              		@ frame_needed = 0, uses_anonymous_args = 0
 194:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_DECLARE(msg);
 461              		.loc 1 194 3 view .LVU121
 195:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 462              		.loc 1 195 3 view .LVU122
 198:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     return ERR_OK;
 463              		.loc 1 198 3 view .LVU123
 198:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     return ERR_OK;
 464              		.loc 1 198 6 is_stmt 0 view .LVU124
 465 0000 60B1     		cbz	r0, .L34
 193:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   err_t err;
 466              		.loc 1 193 1 view .LVU125
 467 0002 00B5     		push	{lr}
 468              	.LCFI10:
 469              		.cfi_def_cfa_offset 4
 470              		.cfi_offset 14, -4
 471 0004 89B0     		sub	sp, sp, #36
 472              	.LCFI11:
 473              		.cfi_def_cfa_offset 40
 202:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_REF(msg).conn = conn;
 474              		.loc 1 202 25 is_stmt 1 view .LVU126
 203:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #if LWIP_SO_SNDTIMEO || LWIP_SO_LINGER
 475              		.loc 1 203 3 view .LVU127
 203:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #if LWIP_SO_SNDTIMEO || LWIP_SO_LINGER
 476              		.loc 1 203 29 is_stmt 0 view .LVU128
 477 0006 0090     		str	r0, [sp]
 210:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     ((LWIP_TCP_CLOSE_TIMEOUT_MS_DEFAULT + TCP_SLOW_INTERVAL - 1) / TCP_SLOW_INTERVAL) + 1;
 478              		.loc 1 210 3 is_stmt 1 view .LVU129
 210:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     ((LWIP_TCP_CLOSE_TIMEOUT_MS_DEFAULT + TCP_SLOW_INTERVAL - 1) / TCP_SLOW_INTERVAL) + 1;
 479              		.loc 1 210 42 is_stmt 0 view .LVU130
 480 0008 2923     		movs	r3, #41
 481 000a 8DF80930 		strb	r3, [sp, #9]
 214:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_FREE(msg);
 482              		.loc 1 214 3 is_stmt 1 view .LVU131
 214:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_FREE(msg);
 483              		.loc 1 214 9 is_stmt 0 view .LVU132
 484 000e 6946     		mov	r1, sp
 485 0010 0348     		ldr	r0, .L39
 486              	.LVL40:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 31


 214:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_FREE(msg);
 487              		.loc 1 214 9 view .LVU133
 488 0012 FFF7FEFF 		bl	netconn_apimsg
 489              	.LVL41:
 215:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 490              		.loc 1 215 24 is_stmt 1 view .LVU134
 217:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     return err;
 491              		.loc 1 217 3 view .LVU135
 221:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 492              		.loc 1 221 1 is_stmt 0 view .LVU136
 493 0016 09B0     		add	sp, sp, #36
 494              	.LCFI12:
 495              		.cfi_def_cfa_offset 4
 496              		@ sp needed
 497 0018 5DF804FB 		ldr	pc, [sp], #4
 498              	.LVL42:
 499              	.L34:
 500              	.LCFI13:
 501              		.cfi_def_cfa_offset 0
 502              		.cfi_restore 14
 199:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   }
 503              		.loc 1 199 12 view .LVU137
 504 001c 0020     		movs	r0, #0
 505              	.LVL43:
 221:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 506              		.loc 1 221 1 view .LVU138
 507 001e 7047     		bx	lr
 508              	.L40:
 509              		.align	2
 510              	.L39:
 511 0020 00000000 		.word	lwip_netconn_do_delconn
 512              		.cfi_endproc
 513              	.LFE176:
 515              		.section	.text.netconn_delete,"ax",%progbits
 516              		.align	1
 517              		.global	netconn_delete
 518              		.syntax unified
 519              		.thumb
 520              		.thumb_func
 522              	netconn_delete:
 523              	.LVL44:
 524              	.LFB177:
 234:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   err_t err;
 525              		.loc 1 234 1 is_stmt 1 view -0
 526              		.cfi_startproc
 527              		@ args = 0, pretend = 0, frame = 0
 528              		@ frame_needed = 0, uses_anonymous_args = 0
 234:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   err_t err;
 529              		.loc 1 234 1 is_stmt 0 view .LVU140
 530 0000 38B5     		push	{r3, r4, r5, lr}
 531              	.LCFI14:
 532              		.cfi_def_cfa_offset 16
 533              		.cfi_offset 3, -16
 534              		.cfi_offset 4, -12
 535              		.cfi_offset 5, -8
 536              		.cfi_offset 14, -4
 235:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 32


 537              		.loc 1 235 3 is_stmt 1 view .LVU141
 238:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     return ERR_OK;
 538              		.loc 1 238 3 view .LVU142
 238:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     return ERR_OK;
 539              		.loc 1 238 6 is_stmt 0 view .LVU143
 540 0002 50B1     		cbz	r0, .L43
 541 0004 0546     		mov	r5, r0
 249:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   }
 542              		.loc 1 249 5 is_stmt 1 view .LVU144
 249:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   }
 543              		.loc 1 249 11 is_stmt 0 view .LVU145
 544 0006 FFF7FEFF 		bl	netconn_prepare_delete
 545              	.LVL45:
 251:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     netconn_free(conn);
 546              		.loc 1 251 3 is_stmt 1 view .LVU146
 251:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     netconn_free(conn);
 547              		.loc 1 251 6 is_stmt 0 view .LVU147
 548 000a 0446     		mov	r4, r0
 549 000c 08B1     		cbz	r0, .L45
 550              	.LVL46:
 551              	.L42:
 255:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 552              		.loc 1 255 1 view .LVU148
 553 000e 2046     		mov	r0, r4
 554 0010 38BD     		pop	{r3, r4, r5, pc}
 555              	.LVL47:
 556              	.L45:
 252:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   }
 557              		.loc 1 252 5 is_stmt 1 view .LVU149
 558 0012 2846     		mov	r0, r5
 559              	.LVL48:
 252:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   }
 560              		.loc 1 252 5 is_stmt 0 view .LVU150
 561 0014 FFF7FEFF 		bl	netconn_free
 562              	.LVL49:
 563 0018 F9E7     		b	.L42
 564              	.LVL50:
 565              	.L43:
 239:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   }
 566              		.loc 1 239 12 view .LVU151
 567 001a 0024     		movs	r4, #0
 568 001c F7E7     		b	.L42
 569              		.cfi_endproc
 570              	.LFE177:
 572              		.section	.rodata.netconn_getaddr.str1.4,"aMS",%progbits,1
 573              		.align	2
 574              	.LC8:
 575 0000 6E657463 		.ascii	"netconn_getaddr: invalid conn\000"
 575      6F6E6E5F 
 575      67657461 
 575      6464723A 
 575      20696E76 
 576 001e 0000     		.align	2
 577              	.LC9:
 578 0020 6E657463 		.ascii	"netconn_getaddr: invalid addr\000"
 578      6F6E6E5F 
 578      67657461 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 33


 578      6464723A 
 578      20696E76 
 579 003e 0000     		.align	2
 580              	.LC10:
 581 0040 6E657463 		.ascii	"netconn_getaddr: invalid port\000"
 581      6F6E6E5F 
 581      67657461 
 581      6464723A 
 581      20696E76 
 582              		.section	.text.netconn_getaddr,"ax",%progbits
 583              		.align	1
 584              		.global	netconn_getaddr
 585              		.syntax unified
 586              		.thumb
 587              		.thumb_func
 589              	netconn_getaddr:
 590              	.LVL51:
 591              	.LFB178:
 270:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_DECLARE(msg);
 592              		.loc 1 270 1 is_stmt 1 view -0
 593              		.cfi_startproc
 594              		@ args = 0, pretend = 0, frame = 32
 595              		@ frame_needed = 0, uses_anonymous_args = 0
 270:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_DECLARE(msg);
 596              		.loc 1 270 1 is_stmt 0 view .LVU153
 597 0000 10B5     		push	{r4, lr}
 598              	.LCFI15:
 599              		.cfi_def_cfa_offset 8
 600              		.cfi_offset 4, -8
 601              		.cfi_offset 14, -4
 602 0002 88B0     		sub	sp, sp, #32
 603              	.LCFI16:
 604              		.cfi_def_cfa_offset 40
 271:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   err_t err;
 605              		.loc 1 271 3 is_stmt 1 view .LVU154
 272:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 606              		.loc 1 272 3 view .LVU155
 274:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_getaddr: invalid addr", (addr != NULL), return ERR_ARG;);
 607              		.loc 1 274 3 view .LVU156
 274:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_getaddr: invalid addr", (addr != NULL), return ERR_ARG;);
 608              		.loc 1 274 3 view .LVU157
 609 0004 60B1     		cbz	r0, .L52
 274:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_getaddr: invalid addr", (addr != NULL), return ERR_ARG;);
 610              		.loc 1 274 3 discriminator 2 view .LVU158
 274:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_getaddr: invalid addr", (addr != NULL), return ERR_ARG;);
 611              		.loc 1 274 3 discriminator 2 view .LVU159
 275:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_getaddr: invalid port", (port != NULL), return ERR_ARG;);
 612              		.loc 1 275 3 view .LVU160
 275:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_getaddr: invalid port", (port != NULL), return ERR_ARG;);
 613              		.loc 1 275 3 view .LVU161
 614 0006 A9B1     		cbz	r1, .L53
 275:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_getaddr: invalid port", (port != NULL), return ERR_ARG;);
 615              		.loc 1 275 3 discriminator 2 view .LVU162
 275:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_getaddr: invalid port", (port != NULL), return ERR_ARG;);
 616              		.loc 1 275 3 discriminator 2 view .LVU163
 276:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 617              		.loc 1 276 3 view .LVU164
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 34


 276:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 618              		.loc 1 276 3 view .LVU165
 619 0008 F2B1     		cbz	r2, .L54
 276:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 620              		.loc 1 276 3 discriminator 2 view .LVU166
 276:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 621              		.loc 1 276 3 discriminator 2 view .LVU167
 278:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_REF(msg).conn = conn;
 622              		.loc 1 278 25 view .LVU168
 279:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_REF(msg).msg.ad.local = local;
 623              		.loc 1 279 3 view .LVU169
 279:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_REF(msg).msg.ad.local = local;
 624              		.loc 1 279 29 is_stmt 0 view .LVU170
 625 000a 0090     		str	r0, [sp]
 280:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #if LWIP_MPU_COMPATIBLE
 626              		.loc 1 280 3 is_stmt 1 view .LVU171
 280:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #if LWIP_MPU_COMPATIBLE
 627              		.loc 1 280 37 is_stmt 0 view .LVU172
 628 000c 8DF81030 		strb	r3, [sp, #16]
 286:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   msg.msg.ad.port = port;
 629              		.loc 1 286 3 is_stmt 1 view .LVU173
 286:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   msg.msg.ad.port = port;
 630              		.loc 1 286 21 is_stmt 0 view .LVU174
 631 0010 0291     		str	r1, [sp, #8]
 287:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   err = netconn_apimsg(lwip_netconn_do_getaddr, &msg);
 632              		.loc 1 287 3 is_stmt 1 view .LVU175
 287:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   err = netconn_apimsg(lwip_netconn_do_getaddr, &msg);
 633              		.loc 1 287 19 is_stmt 0 view .LVU176
 634 0012 0392     		str	r2, [sp, #12]
 288:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #endif /* LWIP_MPU_COMPATIBLE */
 635              		.loc 1 288 3 is_stmt 1 view .LVU177
 288:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #endif /* LWIP_MPU_COMPATIBLE */
 636              		.loc 1 288 9 is_stmt 0 view .LVU178
 637 0014 6946     		mov	r1, sp
 638              	.LVL52:
 288:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #endif /* LWIP_MPU_COMPATIBLE */
 639              		.loc 1 288 9 view .LVU179
 640 0016 1148     		ldr	r0, .L55
 641              	.LVL53:
 288:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #endif /* LWIP_MPU_COMPATIBLE */
 642              		.loc 1 288 9 view .LVU180
 643 0018 FFF7FEFF 		bl	netconn_apimsg
 644              	.LVL54:
 290:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 645              		.loc 1 290 24 is_stmt 1 view .LVU181
 292:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** }
 646              		.loc 1 292 3 view .LVU182
 647              	.L48:
 293:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 648              		.loc 1 293 1 is_stmt 0 view .LVU183
 649 001c 08B0     		add	sp, sp, #32
 650              	.LCFI17:
 651              		.cfi_remember_state
 652              		.cfi_def_cfa_offset 8
 653              		@ sp needed
 654 001e 10BD     		pop	{r4, pc}
 655              	.LVL55:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 35


 656              	.L52:
 657              	.LCFI18:
 658              		.cfi_restore_state
 274:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_getaddr: invalid addr", (addr != NULL), return ERR_ARG;);
 659              		.loc 1 274 3 is_stmt 1 discriminator 1 view .LVU184
 274:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_getaddr: invalid addr", (addr != NULL), return ERR_ARG;);
 660              		.loc 1 274 3 discriminator 1 view .LVU185
 661 0020 0F4B     		ldr	r3, .L55+4
 662              	.LVL56:
 274:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_getaddr: invalid addr", (addr != NULL), return ERR_ARG;);
 663              		.loc 1 274 3 is_stmt 0 discriminator 1 view .LVU186
 664 0022 4FF48972 		mov	r2, #274
 665              	.LVL57:
 274:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_getaddr: invalid addr", (addr != NULL), return ERR_ARG;);
 666              		.loc 1 274 3 discriminator 1 view .LVU187
 667 0026 0F49     		ldr	r1, .L55+8
 668              	.LVL58:
 274:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_getaddr: invalid addr", (addr != NULL), return ERR_ARG;);
 669              		.loc 1 274 3 discriminator 1 view .LVU188
 670 0028 0F48     		ldr	r0, .L55+12
 671              	.LVL59:
 274:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_getaddr: invalid addr", (addr != NULL), return ERR_ARG;);
 672              		.loc 1 274 3 discriminator 1 view .LVU189
 673 002a FFF7FEFF 		bl	printf
 674              	.LVL60:
 274:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_getaddr: invalid addr", (addr != NULL), return ERR_ARG;);
 675              		.loc 1 274 3 is_stmt 1 discriminator 1 view .LVU190
 274:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_getaddr: invalid addr", (addr != NULL), return ERR_ARG;);
 676              		.loc 1 274 3 discriminator 1 view .LVU191
 677 002e 6FF00F00 		mvn	r0, #15
 274:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_getaddr: invalid addr", (addr != NULL), return ERR_ARG;);
 678              		.loc 1 274 3 is_stmt 0 view .LVU192
 679 0032 F3E7     		b	.L48
 680              	.LVL61:
 681              	.L53:
 275:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_getaddr: invalid port", (port != NULL), return ERR_ARG;);
 682              		.loc 1 275 3 is_stmt 1 discriminator 1 view .LVU193
 275:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_getaddr: invalid port", (port != NULL), return ERR_ARG;);
 683              		.loc 1 275 3 discriminator 1 view .LVU194
 684 0034 0A4B     		ldr	r3, .L55+4
 685              	.LVL62:
 275:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_getaddr: invalid port", (port != NULL), return ERR_ARG;);
 686              		.loc 1 275 3 is_stmt 0 discriminator 1 view .LVU195
 687 0036 40F21312 		movw	r2, #275
 688              	.LVL63:
 275:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_getaddr: invalid port", (port != NULL), return ERR_ARG;);
 689              		.loc 1 275 3 discriminator 1 view .LVU196
 690 003a 0C49     		ldr	r1, .L55+16
 691              	.LVL64:
 275:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_getaddr: invalid port", (port != NULL), return ERR_ARG;);
 692              		.loc 1 275 3 discriminator 1 view .LVU197
 693 003c 0A48     		ldr	r0, .L55+12
 694              	.LVL65:
 275:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_getaddr: invalid port", (port != NULL), return ERR_ARG;);
 695              		.loc 1 275 3 discriminator 1 view .LVU198
 696 003e FFF7FEFF 		bl	printf
 697              	.LVL66:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 36


 275:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_getaddr: invalid port", (port != NULL), return ERR_ARG;);
 698              		.loc 1 275 3 is_stmt 1 discriminator 1 view .LVU199
 275:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_getaddr: invalid port", (port != NULL), return ERR_ARG;);
 699              		.loc 1 275 3 discriminator 1 view .LVU200
 700 0042 6FF00F00 		mvn	r0, #15
 275:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_getaddr: invalid port", (port != NULL), return ERR_ARG;);
 701              		.loc 1 275 3 is_stmt 0 view .LVU201
 702 0046 E9E7     		b	.L48
 703              	.LVL67:
 704              	.L54:
 276:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 705              		.loc 1 276 3 is_stmt 1 discriminator 1 view .LVU202
 276:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 706              		.loc 1 276 3 discriminator 1 view .LVU203
 707 0048 054B     		ldr	r3, .L55+4
 708              	.LVL68:
 276:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 709              		.loc 1 276 3 is_stmt 0 discriminator 1 view .LVU204
 710 004a 4FF48A72 		mov	r2, #276
 711              	.LVL69:
 276:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 712              		.loc 1 276 3 discriminator 1 view .LVU205
 713 004e 0849     		ldr	r1, .L55+20
 714              	.LVL70:
 276:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 715              		.loc 1 276 3 discriminator 1 view .LVU206
 716 0050 0548     		ldr	r0, .L55+12
 717              	.LVL71:
 276:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 718              		.loc 1 276 3 discriminator 1 view .LVU207
 719 0052 FFF7FEFF 		bl	printf
 720              	.LVL72:
 276:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 721              		.loc 1 276 3 is_stmt 1 discriminator 1 view .LVU208
 276:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 722              		.loc 1 276 3 discriminator 1 view .LVU209
 723 0056 6FF00F00 		mvn	r0, #15
 276:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 724              		.loc 1 276 3 is_stmt 0 view .LVU210
 725 005a DFE7     		b	.L48
 726              	.L56:
 727              		.align	2
 728              	.L55:
 729 005c 00000000 		.word	lwip_netconn_do_getaddr
 730 0060 00000000 		.word	.LC0
 731 0064 00000000 		.word	.LC8
 732 0068 54000000 		.word	.LC2
 733 006c 20000000 		.word	.LC9
 734 0070 40000000 		.word	.LC10
 735              		.cfi_endproc
 736              	.LFE178:
 738              		.section	.rodata.netconn_bind.str1.4,"aMS",%progbits,1
 739              		.align	2
 740              	.LC11:
 741 0000 6E657463 		.ascii	"netconn_bind: invalid conn\000"
 741      6F6E6E5F 
 741      62696E64 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 37


 741      3A20696E 
 741      76616C69 
 742              		.section	.text.netconn_bind,"ax",%progbits
 743              		.align	1
 744              		.global	netconn_bind
 745              		.syntax unified
 746              		.thumb
 747              		.thumb_func
 749              	netconn_bind:
 750              	.LVL73:
 751              	.LFB179:
 308:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_DECLARE(msg);
 752              		.loc 1 308 1 is_stmt 1 view -0
 753              		.cfi_startproc
 754              		@ args = 0, pretend = 0, frame = 32
 755              		@ frame_needed = 0, uses_anonymous_args = 0
 308:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_DECLARE(msg);
 756              		.loc 1 308 1 is_stmt 0 view .LVU212
 757 0000 00B5     		push	{lr}
 758              	.LCFI19:
 759              		.cfi_def_cfa_offset 4
 760              		.cfi_offset 14, -4
 761 0002 89B0     		sub	sp, sp, #36
 762              	.LCFI20:
 763              		.cfi_def_cfa_offset 40
 309:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   err_t err;
 764              		.loc 1 309 3 is_stmt 1 view .LVU213
 310:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 765              		.loc 1 310 3 view .LVU214
 312:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 766              		.loc 1 312 3 view .LVU215
 312:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 767              		.loc 1 312 3 view .LVU216
 768 0004 60B1     		cbz	r0, .L63
 769 0006 0346     		mov	r3, r0
 312:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 770              		.loc 1 312 3 discriminator 2 view .LVU217
 312:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 771              		.loc 1 312 3 discriminator 2 view .LVU218
 316:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     addr = IP4_ADDR_ANY;
 772              		.loc 1 316 3 view .LVU219
 316:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     addr = IP4_ADDR_ANY;
 773              		.loc 1 316 6 is_stmt 0 view .LVU220
 774 0008 A1B1     		cbz	r1, .L64
 775              	.LVL74:
 776              	.L60:
 331:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_REF(msg).conn = conn;
 777              		.loc 1 331 25 is_stmt 1 view .LVU221
 332:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_REF(msg).msg.bc.ipaddr = API_MSG_VAR_REF(addr);
 778              		.loc 1 332 3 view .LVU222
 332:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_REF(msg).msg.bc.ipaddr = API_MSG_VAR_REF(addr);
 779              		.loc 1 332 29 is_stmt 0 view .LVU223
 780 000a 0093     		str	r3, [sp]
 333:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_REF(msg).msg.bc.port = port;
 781              		.loc 1 333 3 is_stmt 1 view .LVU224
 333:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_REF(msg).msg.bc.port = port;
 782              		.loc 1 333 38 is_stmt 0 view .LVU225
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 38


 783 000c 0291     		str	r1, [sp, #8]
 334:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   err = netconn_apimsg(lwip_netconn_do_bind, &API_MSG_VAR_REF(msg));
 784              		.loc 1 334 3 is_stmt 1 view .LVU226
 334:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   err = netconn_apimsg(lwip_netconn_do_bind, &API_MSG_VAR_REF(msg));
 785              		.loc 1 334 36 is_stmt 0 view .LVU227
 786 000e ADF80C20 		strh	r2, [sp, #12]	@ movhi
 335:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_FREE(msg);
 787              		.loc 1 335 3 is_stmt 1 view .LVU228
 335:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_FREE(msg);
 788              		.loc 1 335 9 is_stmt 0 view .LVU229
 789 0012 6946     		mov	r1, sp
 790              	.LVL75:
 335:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_FREE(msg);
 791              		.loc 1 335 9 view .LVU230
 792 0014 0848     		ldr	r0, .L65
 793              	.LVL76:
 335:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_FREE(msg);
 794              		.loc 1 335 9 view .LVU231
 795 0016 FFF7FEFF 		bl	netconn_apimsg
 796              	.LVL77:
 336:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 797              		.loc 1 336 24 is_stmt 1 view .LVU232
 338:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** }
 798              		.loc 1 338 3 view .LVU233
 799              	.L59:
 339:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 800              		.loc 1 339 1 is_stmt 0 view .LVU234
 801 001a 09B0     		add	sp, sp, #36
 802              	.LCFI21:
 803              		.cfi_remember_state
 804              		.cfi_def_cfa_offset 4
 805              		@ sp needed
 806 001c 5DF804FB 		ldr	pc, [sp], #4
 807              	.LVL78:
 808              	.L63:
 809              	.LCFI22:
 810              		.cfi_restore_state
 312:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 811              		.loc 1 312 3 is_stmt 1 discriminator 1 view .LVU235
 312:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 812              		.loc 1 312 3 discriminator 1 view .LVU236
 813 0020 064B     		ldr	r3, .L65+4
 814 0022 4FF49C72 		mov	r2, #312
 815              	.LVL79:
 312:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 816              		.loc 1 312 3 is_stmt 0 discriminator 1 view .LVU237
 817 0026 0649     		ldr	r1, .L65+8
 818              	.LVL80:
 312:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 819              		.loc 1 312 3 discriminator 1 view .LVU238
 820 0028 0648     		ldr	r0, .L65+12
 821              	.LVL81:
 312:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 822              		.loc 1 312 3 discriminator 1 view .LVU239
 823 002a FFF7FEFF 		bl	printf
 824              	.LVL82:
 312:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 39


 825              		.loc 1 312 3 is_stmt 1 discriminator 1 view .LVU240
 312:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 826              		.loc 1 312 3 discriminator 1 view .LVU241
 827 002e 6FF00F00 		mvn	r0, #15
 312:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 828              		.loc 1 312 3 is_stmt 0 view .LVU242
 829 0032 F2E7     		b	.L59
 830              	.LVL83:
 831              	.L64:
 317:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   }
 832              		.loc 1 317 10 view .LVU243
 833 0034 0449     		ldr	r1, .L65+16
 834              	.LVL84:
 317:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   }
 835              		.loc 1 317 10 view .LVU244
 836 0036 E8E7     		b	.L60
 837              	.L66:
 838              		.align	2
 839              	.L65:
 840 0038 00000000 		.word	lwip_netconn_do_bind
 841 003c 00000000 		.word	.LC0
 842 0040 00000000 		.word	.LC11
 843 0044 54000000 		.word	.LC2
 844 0048 00000000 		.word	ip_addr_any
 845              		.cfi_endproc
 846              	.LFE179:
 848              		.section	.rodata.netconn_bind_if.str1.4,"aMS",%progbits,1
 849              		.align	2
 850              	.LC12:
 851 0000 6E657463 		.ascii	"netconn_bind_if: invalid conn\000"
 851      6F6E6E5F 
 851      62696E64 
 851      5F69663A 
 851      20696E76 
 852              		.section	.text.netconn_bind_if,"ax",%progbits
 853              		.align	1
 854              		.global	netconn_bind_if
 855              		.syntax unified
 856              		.thumb
 857              		.thumb_func
 859              	netconn_bind_if:
 860              	.LVL85:
 861              	.LFB180:
 352:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_DECLARE(msg);
 862              		.loc 1 352 1 is_stmt 1 view -0
 863              		.cfi_startproc
 864              		@ args = 0, pretend = 0, frame = 32
 865              		@ frame_needed = 0, uses_anonymous_args = 0
 352:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_DECLARE(msg);
 866              		.loc 1 352 1 is_stmt 0 view .LVU246
 867 0000 00B5     		push	{lr}
 868              	.LCFI23:
 869              		.cfi_def_cfa_offset 4
 870              		.cfi_offset 14, -4
 871 0002 89B0     		sub	sp, sp, #36
 872              	.LCFI24:
 873              		.cfi_def_cfa_offset 40
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 40


 353:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   err_t err;
 874              		.loc 1 353 3 is_stmt 1 view .LVU247
 354:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 875              		.loc 1 354 3 view .LVU248
 356:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 876              		.loc 1 356 3 view .LVU249
 356:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 877              		.loc 1 356 3 view .LVU250
 878 0004 48B1     		cbz	r0, .L71
 356:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 879              		.loc 1 356 3 discriminator 2 view .LVU251
 356:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 880              		.loc 1 356 3 discriminator 2 view .LVU252
 358:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_REF(msg).conn = conn;
 881              		.loc 1 358 25 view .LVU253
 359:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_REF(msg).msg.bc.if_idx = if_idx;
 882              		.loc 1 359 3 view .LVU254
 359:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_REF(msg).msg.bc.if_idx = if_idx;
 883              		.loc 1 359 29 is_stmt 0 view .LVU255
 884 0006 0090     		str	r0, [sp]
 360:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   err = netconn_apimsg(lwip_netconn_do_bind_if, &API_MSG_VAR_REF(msg));
 885              		.loc 1 360 3 is_stmt 1 view .LVU256
 360:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   err = netconn_apimsg(lwip_netconn_do_bind_if, &API_MSG_VAR_REF(msg));
 886              		.loc 1 360 38 is_stmt 0 view .LVU257
 887 0008 8DF80E10 		strb	r1, [sp, #14]
 361:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_FREE(msg);
 888              		.loc 1 361 3 is_stmt 1 view .LVU258
 361:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_FREE(msg);
 889              		.loc 1 361 9 is_stmt 0 view .LVU259
 890 000c 6946     		mov	r1, sp
 891              	.LVL86:
 361:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_FREE(msg);
 892              		.loc 1 361 9 view .LVU260
 893 000e 0848     		ldr	r0, .L72
 894              	.LVL87:
 361:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_FREE(msg);
 895              		.loc 1 361 9 view .LVU261
 896 0010 FFF7FEFF 		bl	netconn_apimsg
 897              	.LVL88:
 362:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 898              		.loc 1 362 24 is_stmt 1 view .LVU262
 364:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** }
 899              		.loc 1 364 3 view .LVU263
 900              	.L69:
 365:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 901              		.loc 1 365 1 is_stmt 0 view .LVU264
 902 0014 09B0     		add	sp, sp, #36
 903              	.LCFI25:
 904              		.cfi_remember_state
 905              		.cfi_def_cfa_offset 4
 906              		@ sp needed
 907 0016 5DF804FB 		ldr	pc, [sp], #4
 908              	.LVL89:
 909              	.L71:
 910              	.LCFI26:
 911              		.cfi_restore_state
 356:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 41


 912              		.loc 1 356 3 is_stmt 1 discriminator 1 view .LVU265
 356:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 913              		.loc 1 356 3 discriminator 1 view .LVU266
 914 001a 064B     		ldr	r3, .L72+4
 915 001c 4FF4B272 		mov	r2, #356
 916 0020 0549     		ldr	r1, .L72+8
 917              	.LVL90:
 356:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 918              		.loc 1 356 3 is_stmt 0 discriminator 1 view .LVU267
 919 0022 0648     		ldr	r0, .L72+12
 920              	.LVL91:
 356:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 921              		.loc 1 356 3 discriminator 1 view .LVU268
 922 0024 FFF7FEFF 		bl	printf
 923              	.LVL92:
 356:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 924              		.loc 1 356 3 is_stmt 1 discriminator 1 view .LVU269
 356:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 925              		.loc 1 356 3 discriminator 1 view .LVU270
 926 0028 6FF00F00 		mvn	r0, #15
 356:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 927              		.loc 1 356 3 is_stmt 0 view .LVU271
 928 002c F2E7     		b	.L69
 929              	.L73:
 930 002e 00BF     		.align	2
 931              	.L72:
 932 0030 00000000 		.word	lwip_netconn_do_bind_if
 933 0034 00000000 		.word	.LC0
 934 0038 00000000 		.word	.LC12
 935 003c 54000000 		.word	.LC2
 936              		.cfi_endproc
 937              	.LFE180:
 939              		.section	.rodata.netconn_connect.str1.4,"aMS",%progbits,1
 940              		.align	2
 941              	.LC13:
 942 0000 6E657463 		.ascii	"netconn_connect: invalid conn\000"
 942      6F6E6E5F 
 942      636F6E6E 
 942      6563743A 
 942      20696E76 
 943              		.section	.text.netconn_connect,"ax",%progbits
 944              		.align	1
 945              		.global	netconn_connect
 946              		.syntax unified
 947              		.thumb
 948              		.thumb_func
 950              	netconn_connect:
 951              	.LVL93:
 952              	.LFB181:
 378:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_DECLARE(msg);
 953              		.loc 1 378 1 is_stmt 1 view -0
 954              		.cfi_startproc
 955              		@ args = 0, pretend = 0, frame = 32
 956              		@ frame_needed = 0, uses_anonymous_args = 0
 378:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_DECLARE(msg);
 957              		.loc 1 378 1 is_stmt 0 view .LVU273
 958 0000 00B5     		push	{lr}
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 42


 959              	.LCFI27:
 960              		.cfi_def_cfa_offset 4
 961              		.cfi_offset 14, -4
 962 0002 89B0     		sub	sp, sp, #36
 963              	.LCFI28:
 964              		.cfi_def_cfa_offset 40
 379:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   err_t err;
 965              		.loc 1 379 3 is_stmt 1 view .LVU274
 380:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 966              		.loc 1 380 3 view .LVU275
 382:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 967              		.loc 1 382 3 view .LVU276
 382:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 968              		.loc 1 382 3 view .LVU277
 969 0004 60B1     		cbz	r0, .L80
 970 0006 0346     		mov	r3, r0
 382:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 971              		.loc 1 382 3 discriminator 2 view .LVU278
 382:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 972              		.loc 1 382 3 discriminator 2 view .LVU279
 386:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     addr = IP4_ADDR_ANY;
 973              		.loc 1 386 3 view .LVU280
 386:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     addr = IP4_ADDR_ANY;
 974              		.loc 1 386 6 is_stmt 0 view .LVU281
 975 0008 A1B1     		cbz	r1, .L81
 976              	.LVL94:
 977              	.L77:
 391:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_REF(msg).conn = conn;
 978              		.loc 1 391 25 is_stmt 1 view .LVU282
 392:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_REF(msg).msg.bc.ipaddr = API_MSG_VAR_REF(addr);
 979              		.loc 1 392 3 view .LVU283
 392:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_REF(msg).msg.bc.ipaddr = API_MSG_VAR_REF(addr);
 980              		.loc 1 392 29 is_stmt 0 view .LVU284
 981 000a 0093     		str	r3, [sp]
 393:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_REF(msg).msg.bc.port = port;
 982              		.loc 1 393 3 is_stmt 1 view .LVU285
 393:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_REF(msg).msg.bc.port = port;
 983              		.loc 1 393 38 is_stmt 0 view .LVU286
 984 000c 0291     		str	r1, [sp, #8]
 394:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   err = netconn_apimsg(lwip_netconn_do_connect, &API_MSG_VAR_REF(msg));
 985              		.loc 1 394 3 is_stmt 1 view .LVU287
 394:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   err = netconn_apimsg(lwip_netconn_do_connect, &API_MSG_VAR_REF(msg));
 986              		.loc 1 394 36 is_stmt 0 view .LVU288
 987 000e ADF80C20 		strh	r2, [sp, #12]	@ movhi
 395:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_FREE(msg);
 988              		.loc 1 395 3 is_stmt 1 view .LVU289
 395:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_FREE(msg);
 989              		.loc 1 395 9 is_stmt 0 view .LVU290
 990 0012 6946     		mov	r1, sp
 991              	.LVL95:
 395:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_FREE(msg);
 992              		.loc 1 395 9 view .LVU291
 993 0014 0848     		ldr	r0, .L82
 994              	.LVL96:
 395:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_FREE(msg);
 995              		.loc 1 395 9 view .LVU292
 996 0016 FFF7FEFF 		bl	netconn_apimsg
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 43


 997              	.LVL97:
 396:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 998              		.loc 1 396 24 is_stmt 1 view .LVU293
 398:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** }
 999              		.loc 1 398 3 view .LVU294
 1000              	.L76:
 399:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 1001              		.loc 1 399 1 is_stmt 0 view .LVU295
 1002 001a 09B0     		add	sp, sp, #36
 1003              	.LCFI29:
 1004              		.cfi_remember_state
 1005              		.cfi_def_cfa_offset 4
 1006              		@ sp needed
 1007 001c 5DF804FB 		ldr	pc, [sp], #4
 1008              	.LVL98:
 1009              	.L80:
 1010              	.LCFI30:
 1011              		.cfi_restore_state
 382:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 1012              		.loc 1 382 3 is_stmt 1 discriminator 1 view .LVU296
 382:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 1013              		.loc 1 382 3 discriminator 1 view .LVU297
 1014 0020 064B     		ldr	r3, .L82+4
 1015 0022 4FF4BF72 		mov	r2, #382
 1016              	.LVL99:
 382:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 1017              		.loc 1 382 3 is_stmt 0 discriminator 1 view .LVU298
 1018 0026 0649     		ldr	r1, .L82+8
 1019              	.LVL100:
 382:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 1020              		.loc 1 382 3 discriminator 1 view .LVU299
 1021 0028 0648     		ldr	r0, .L82+12
 1022              	.LVL101:
 382:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 1023              		.loc 1 382 3 discriminator 1 view .LVU300
 1024 002a FFF7FEFF 		bl	printf
 1025              	.LVL102:
 382:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 1026              		.loc 1 382 3 is_stmt 1 discriminator 1 view .LVU301
 382:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 1027              		.loc 1 382 3 discriminator 1 view .LVU302
 1028 002e 6FF00F00 		mvn	r0, #15
 382:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 1029              		.loc 1 382 3 is_stmt 0 view .LVU303
 1030 0032 F2E7     		b	.L76
 1031              	.LVL103:
 1032              	.L81:
 387:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   }
 1033              		.loc 1 387 10 view .LVU304
 1034 0034 0449     		ldr	r1, .L82+16
 1035              	.LVL104:
 387:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   }
 1036              		.loc 1 387 10 view .LVU305
 1037 0036 E8E7     		b	.L77
 1038              	.L83:
 1039              		.align	2
 1040              	.L82:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 44


 1041 0038 00000000 		.word	lwip_netconn_do_connect
 1042 003c 00000000 		.word	.LC0
 1043 0040 00000000 		.word	.LC13
 1044 0044 54000000 		.word	.LC2
 1045 0048 00000000 		.word	ip_addr_any
 1046              		.cfi_endproc
 1047              	.LFE181:
 1049              		.section	.rodata.netconn_disconnect.str1.4,"aMS",%progbits,1
 1050              		.align	2
 1051              	.LC14:
 1052 0000 6E657463 		.ascii	"netconn_disconnect: invalid conn\000"
 1052      6F6E6E5F 
 1052      64697363 
 1052      6F6E6E65 
 1052      63743A20 
 1053              		.section	.text.netconn_disconnect,"ax",%progbits
 1054              		.align	1
 1055              		.global	netconn_disconnect
 1056              		.syntax unified
 1057              		.thumb
 1058              		.thumb_func
 1060              	netconn_disconnect:
 1061              	.LVL105:
 1062              	.LFB182:
 410:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_DECLARE(msg);
 1063              		.loc 1 410 1 is_stmt 1 view -0
 1064              		.cfi_startproc
 1065              		@ args = 0, pretend = 0, frame = 32
 1066              		@ frame_needed = 0, uses_anonymous_args = 0
 410:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_DECLARE(msg);
 1067              		.loc 1 410 1 is_stmt 0 view .LVU307
 1068 0000 00B5     		push	{lr}
 1069              	.LCFI31:
 1070              		.cfi_def_cfa_offset 4
 1071              		.cfi_offset 14, -4
 1072 0002 89B0     		sub	sp, sp, #36
 1073              	.LCFI32:
 1074              		.cfi_def_cfa_offset 40
 411:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   err_t err;
 1075              		.loc 1 411 3 is_stmt 1 view .LVU308
 412:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 1076              		.loc 1 412 3 view .LVU309
 414:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 1077              		.loc 1 414 3 view .LVU310
 414:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 1078              		.loc 1 414 3 view .LVU311
 1079 0004 38B1     		cbz	r0, .L88
 414:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 1080              		.loc 1 414 3 discriminator 2 view .LVU312
 414:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 1081              		.loc 1 414 3 discriminator 2 view .LVU313
 416:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_REF(msg).conn = conn;
 1082              		.loc 1 416 25 view .LVU314
 417:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   err = netconn_apimsg(lwip_netconn_do_disconnect, &API_MSG_VAR_REF(msg));
 1083              		.loc 1 417 3 view .LVU315
 417:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   err = netconn_apimsg(lwip_netconn_do_disconnect, &API_MSG_VAR_REF(msg));
 1084              		.loc 1 417 29 is_stmt 0 view .LVU316
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 45


 1085 0006 0090     		str	r0, [sp]
 418:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_FREE(msg);
 1086              		.loc 1 418 3 is_stmt 1 view .LVU317
 418:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_FREE(msg);
 1087              		.loc 1 418 9 is_stmt 0 view .LVU318
 1088 0008 6946     		mov	r1, sp
 1089 000a 0848     		ldr	r0, .L89
 1090              	.LVL106:
 418:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_FREE(msg);
 1091              		.loc 1 418 9 view .LVU319
 1092 000c FFF7FEFF 		bl	netconn_apimsg
 1093              	.LVL107:
 419:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 1094              		.loc 1 419 24 is_stmt 1 view .LVU320
 421:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** }
 1095              		.loc 1 421 3 view .LVU321
 1096              	.L86:
 422:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 1097              		.loc 1 422 1 is_stmt 0 view .LVU322
 1098 0010 09B0     		add	sp, sp, #36
 1099              	.LCFI33:
 1100              		.cfi_remember_state
 1101              		.cfi_def_cfa_offset 4
 1102              		@ sp needed
 1103 0012 5DF804FB 		ldr	pc, [sp], #4
 1104              	.LVL108:
 1105              	.L88:
 1106              	.LCFI34:
 1107              		.cfi_restore_state
 414:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 1108              		.loc 1 414 3 is_stmt 1 discriminator 1 view .LVU323
 414:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 1109              		.loc 1 414 3 discriminator 1 view .LVU324
 1110 0016 064B     		ldr	r3, .L89+4
 1111 0018 4FF4CF72 		mov	r2, #414
 1112 001c 0549     		ldr	r1, .L89+8
 1113 001e 0648     		ldr	r0, .L89+12
 1114              	.LVL109:
 414:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 1115              		.loc 1 414 3 is_stmt 0 discriminator 1 view .LVU325
 1116 0020 FFF7FEFF 		bl	printf
 1117              	.LVL110:
 414:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 1118              		.loc 1 414 3 is_stmt 1 discriminator 1 view .LVU326
 414:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 1119              		.loc 1 414 3 discriminator 1 view .LVU327
 1120 0024 6FF00F00 		mvn	r0, #15
 414:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 1121              		.loc 1 414 3 is_stmt 0 view .LVU328
 1122 0028 F2E7     		b	.L86
 1123              	.L90:
 1124 002a 00BF     		.align	2
 1125              	.L89:
 1126 002c 00000000 		.word	lwip_netconn_do_disconnect
 1127 0030 00000000 		.word	.LC0
 1128 0034 00000000 		.word	.LC14
 1129 0038 54000000 		.word	.LC2
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 46


 1130              		.cfi_endproc
 1131              	.LFE182:
 1133              		.section	.rodata.netconn_listen_with_backlog.str1.4,"aMS",%progbits,1
 1134              		.align	2
 1135              	.LC15:
 1136 0000 6E657463 		.ascii	"netconn_listen: invalid conn\000"
 1136      6F6E6E5F 
 1136      6C697374 
 1136      656E3A20 
 1136      696E7661 
 1137              		.section	.text.netconn_listen_with_backlog,"ax",%progbits
 1138              		.align	1
 1139              		.global	netconn_listen_with_backlog
 1140              		.syntax unified
 1141              		.thumb
 1142              		.thumb_func
 1144              	netconn_listen_with_backlog:
 1145              	.LVL111:
 1146              	.LFB183:
 435:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #if LWIP_TCP
 1147              		.loc 1 435 1 is_stmt 1 view -0
 1148              		.cfi_startproc
 1149              		@ args = 0, pretend = 0, frame = 32
 1150              		@ frame_needed = 0, uses_anonymous_args = 0
 435:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #if LWIP_TCP
 1151              		.loc 1 435 1 is_stmt 0 view .LVU330
 1152 0000 00B5     		push	{lr}
 1153              	.LCFI35:
 1154              		.cfi_def_cfa_offset 4
 1155              		.cfi_offset 14, -4
 1156 0002 89B0     		sub	sp, sp, #36
 1157              	.LCFI36:
 1158              		.cfi_def_cfa_offset 40
 437:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   err_t err;
 1159              		.loc 1 437 3 is_stmt 1 view .LVU331
 438:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 1160              		.loc 1 438 3 view .LVU332
 441:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 1161              		.loc 1 441 3 view .LVU333
 443:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 1162              		.loc 1 443 3 view .LVU334
 443:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 1163              		.loc 1 443 3 view .LVU335
 1164 0004 38B1     		cbz	r0, .L95
 443:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 1165              		.loc 1 443 3 discriminator 2 view .LVU336
 443:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 1166              		.loc 1 443 3 discriminator 2 view .LVU337
 445:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_REF(msg).conn = conn;
 1167              		.loc 1 445 25 view .LVU338
 446:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #if TCP_LISTEN_BACKLOG
 1168              		.loc 1 446 3 view .LVU339
 446:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #if TCP_LISTEN_BACKLOG
 1169              		.loc 1 446 29 is_stmt 0 view .LVU340
 1170 0006 0090     		str	r0, [sp]
 450:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_FREE(msg);
 1171              		.loc 1 450 3 is_stmt 1 view .LVU341
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 47


 450:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_FREE(msg);
 1172              		.loc 1 450 9 is_stmt 0 view .LVU342
 1173 0008 6946     		mov	r1, sp
 1174              	.LVL112:
 450:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_FREE(msg);
 1175              		.loc 1 450 9 view .LVU343
 1176 000a 0848     		ldr	r0, .L96
 1177              	.LVL113:
 450:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_FREE(msg);
 1178              		.loc 1 450 9 view .LVU344
 1179 000c FFF7FEFF 		bl	netconn_apimsg
 1180              	.LVL114:
 451:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 1181              		.loc 1 451 24 is_stmt 1 view .LVU345
 453:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #else /* LWIP_TCP */
 1182              		.loc 1 453 3 view .LVU346
 1183              	.L93:
 459:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 1184              		.loc 1 459 1 is_stmt 0 view .LVU347
 1185 0010 09B0     		add	sp, sp, #36
 1186              	.LCFI37:
 1187              		.cfi_remember_state
 1188              		.cfi_def_cfa_offset 4
 1189              		@ sp needed
 1190 0012 5DF804FB 		ldr	pc, [sp], #4
 1191              	.LVL115:
 1192              	.L95:
 1193              	.LCFI38:
 1194              		.cfi_restore_state
 443:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 1195              		.loc 1 443 3 is_stmt 1 discriminator 1 view .LVU348
 443:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 1196              		.loc 1 443 3 discriminator 1 view .LVU349
 1197 0016 064B     		ldr	r3, .L96+4
 1198 0018 40F2BB12 		movw	r2, #443
 1199 001c 0549     		ldr	r1, .L96+8
 1200              	.LVL116:
 443:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 1201              		.loc 1 443 3 is_stmt 0 discriminator 1 view .LVU350
 1202 001e 0648     		ldr	r0, .L96+12
 1203              	.LVL117:
 443:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 1204              		.loc 1 443 3 discriminator 1 view .LVU351
 1205 0020 FFF7FEFF 		bl	printf
 1206              	.LVL118:
 443:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 1207              		.loc 1 443 3 is_stmt 1 discriminator 1 view .LVU352
 443:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 1208              		.loc 1 443 3 discriminator 1 view .LVU353
 1209 0024 6FF00F00 		mvn	r0, #15
 443:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 1210              		.loc 1 443 3 is_stmt 0 view .LVU354
 1211 0028 F2E7     		b	.L93
 1212              	.L97:
 1213 002a 00BF     		.align	2
 1214              	.L96:
 1215 002c 00000000 		.word	lwip_netconn_do_listen
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 48


 1216 0030 00000000 		.word	.LC0
 1217 0034 00000000 		.word	.LC15
 1218 0038 54000000 		.word	.LC2
 1219              		.cfi_endproc
 1220              	.LFE183:
 1222              		.section	.text.netconn_tcp_recvd,"ax",%progbits
 1223              		.align	1
 1224              		.global	netconn_tcp_recvd
 1225              		.syntax unified
 1226              		.thumb
 1227              		.thumb_func
 1229              	netconn_tcp_recvd:
 1230              	.LVL119:
 1231              	.LFB187:
 690:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   err_t err;
 1232              		.loc 1 690 1 is_stmt 1 view -0
 1233              		.cfi_startproc
 1234              		@ args = 0, pretend = 0, frame = 32
 1235              		@ frame_needed = 0, uses_anonymous_args = 0
 690:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   err_t err;
 1236              		.loc 1 690 1 is_stmt 0 view .LVU356
 1237 0000 00B5     		push	{lr}
 1238              	.LCFI39:
 1239              		.cfi_def_cfa_offset 4
 1240              		.cfi_offset 14, -4
 1241 0002 89B0     		sub	sp, sp, #36
 1242              	.LCFI40:
 1243              		.cfi_def_cfa_offset 40
 691:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_DECLARE(msg);
 1244              		.loc 1 691 3 is_stmt 1 view .LVU357
 692:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_recv_tcp_pbuf: invalid conn", (conn != NULL) &&
 1245              		.loc 1 692 3 view .LVU358
 693:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****              NETCONNTYPE_GROUP(netconn_type(conn)) == NETCONN_TCP, return ERR_ARG;);
 1246              		.loc 1 693 3 view .LVU359
 693:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****              NETCONNTYPE_GROUP(netconn_type(conn)) == NETCONN_TCP, return ERR_ARG;);
 1247              		.loc 1 693 3 view .LVU360
 1248 0004 50B1     		cbz	r0, .L99
 693:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****              NETCONNTYPE_GROUP(netconn_type(conn)) == NETCONN_TCP, return ERR_ARG;);
 1249              		.loc 1 693 3 is_stmt 0 discriminator 2 view .LVU361
 1250 0006 0378     		ldrb	r3, [r0]	@ zero_extendqisi2
 1251 0008 03F0F003 		and	r3, r3, #240
 1252 000c 102B     		cmp	r3, #16
 1253 000e 05D1     		bne	.L99
 693:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****              NETCONNTYPE_GROUP(netconn_type(conn)) == NETCONN_TCP, return ERR_ARG;);
 1254              		.loc 1 693 3 is_stmt 1 discriminator 4 view .LVU362
 693:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****              NETCONNTYPE_GROUP(netconn_type(conn)) == NETCONN_TCP, return ERR_ARG;);
 1255              		.loc 1 693 3 discriminator 4 view .LVU363
 696:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   err = netconn_tcp_recvd_msg(conn, len, &API_VAR_REF(msg));
 1256              		.loc 1 696 25 view .LVU364
 697:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_FREE(msg);
 1257              		.loc 1 697 3 view .LVU365
 697:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_FREE(msg);
 1258              		.loc 1 697 9 is_stmt 0 view .LVU366
 1259 0010 6A46     		mov	r2, sp
 1260 0012 FFF7FEFF 		bl	netconn_tcp_recvd_msg
 1261              	.LVL120:
 698:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   return err;
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 49


 1262              		.loc 1 698 24 is_stmt 1 view .LVU367
 699:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** }
 1263              		.loc 1 699 3 view .LVU368
 1264              	.L101:
 700:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 1265              		.loc 1 700 1 is_stmt 0 view .LVU369
 1266 0016 09B0     		add	sp, sp, #36
 1267              	.LCFI41:
 1268              		.cfi_remember_state
 1269              		.cfi_def_cfa_offset 4
 1270              		@ sp needed
 1271 0018 5DF804FB 		ldr	pc, [sp], #4
 1272              	.LVL121:
 1273              	.L99:
 1274              	.LCFI42:
 1275              		.cfi_restore_state
 693:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****              NETCONNTYPE_GROUP(netconn_type(conn)) == NETCONN_TCP, return ERR_ARG;);
 1276              		.loc 1 693 3 is_stmt 1 discriminator 3 view .LVU370
 693:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****              NETCONNTYPE_GROUP(netconn_type(conn)) == NETCONN_TCP, return ERR_ARG;);
 1277              		.loc 1 693 3 discriminator 3 view .LVU371
 1278 001c 044B     		ldr	r3, .L103
 1279 001e 40F2B522 		movw	r2, #693
 1280 0022 0449     		ldr	r1, .L103+4
 1281              	.LVL122:
 693:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****              NETCONNTYPE_GROUP(netconn_type(conn)) == NETCONN_TCP, return ERR_ARG;);
 1282              		.loc 1 693 3 is_stmt 0 discriminator 3 view .LVU372
 1283 0024 0448     		ldr	r0, .L103+8
 1284              	.LVL123:
 693:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****              NETCONNTYPE_GROUP(netconn_type(conn)) == NETCONN_TCP, return ERR_ARG;);
 1285              		.loc 1 693 3 discriminator 3 view .LVU373
 1286 0026 FFF7FEFF 		bl	printf
 1287              	.LVL124:
 693:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****              NETCONNTYPE_GROUP(netconn_type(conn)) == NETCONN_TCP, return ERR_ARG;);
 1288              		.loc 1 693 3 is_stmt 1 discriminator 1 view .LVU374
 693:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****              NETCONNTYPE_GROUP(netconn_type(conn)) == NETCONN_TCP, return ERR_ARG;);
 1289              		.loc 1 693 3 discriminator 1 view .LVU375
 1290 002a 6FF00F00 		mvn	r0, #15
 693:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****              NETCONNTYPE_GROUP(netconn_type(conn)) == NETCONN_TCP, return ERR_ARG;);
 1291              		.loc 1 693 3 is_stmt 0 view .LVU376
 1292 002e F2E7     		b	.L101
 1293              	.L104:
 1294              		.align	2
 1295              	.L103:
 1296 0030 00000000 		.word	.LC0
 1297 0034 30000000 		.word	.LC1
 1298 0038 54000000 		.word	.LC2
 1299              		.cfi_endproc
 1300              	.LFE187:
 1302              		.section	.rodata.netconn_send.str1.4,"aMS",%progbits,1
 1303              		.align	2
 1304              	.LC16:
 1305 0000 6E657463 		.ascii	"netconn_send: invalid conn\000"
 1305      6F6E6E5F 
 1305      73656E64 
 1305      3A20696E 
 1305      76616C69 
 1306              		.section	.text.netconn_send,"ax",%progbits
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 50


 1307              		.align	1
 1308              		.global	netconn_send
 1309              		.syntax unified
 1310              		.thumb
 1311              		.thumb_func
 1313              	netconn_send:
 1314              	.LVL125:
 1315              	.LFB195:
 942:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_DECLARE(msg);
 1316              		.loc 1 942 1 is_stmt 1 view -0
 1317              		.cfi_startproc
 1318              		@ args = 0, pretend = 0, frame = 32
 1319              		@ frame_needed = 0, uses_anonymous_args = 0
 942:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_DECLARE(msg);
 1320              		.loc 1 942 1 is_stmt 0 view .LVU378
 1321 0000 00B5     		push	{lr}
 1322              	.LCFI43:
 1323              		.cfi_def_cfa_offset 4
 1324              		.cfi_offset 14, -4
 1325 0002 89B0     		sub	sp, sp, #36
 1326              	.LCFI44:
 1327              		.cfi_def_cfa_offset 40
 943:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   err_t err;
 1328              		.loc 1 943 3 is_stmt 1 view .LVU379
 944:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 1329              		.loc 1 944 3 view .LVU380
 946:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 1330              		.loc 1 946 3 view .LVU381
 946:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 1331              		.loc 1 946 3 view .LVU382
 1332 0004 40B1     		cbz	r0, .L109
 946:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 1333              		.loc 1 946 3 discriminator 2 view .LVU383
 946:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 1334              		.loc 1 946 3 discriminator 2 view .LVU384
 948:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 1335              		.loc 1 948 90 view .LVU385
 950:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_REF(msg).conn = conn;
 1336              		.loc 1 950 25 view .LVU386
 951:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_REF(msg).msg.b = buf;
 1337              		.loc 1 951 3 view .LVU387
 951:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_REF(msg).msg.b = buf;
 1338              		.loc 1 951 29 is_stmt 0 view .LVU388
 1339 0006 0090     		str	r0, [sp]
 952:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   err = netconn_apimsg(lwip_netconn_do_send, &API_MSG_VAR_REF(msg));
 1340              		.loc 1 952 3 is_stmt 1 view .LVU389
 952:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   err = netconn_apimsg(lwip_netconn_do_send, &API_MSG_VAR_REF(msg));
 1341              		.loc 1 952 30 is_stmt 0 view .LVU390
 1342 0008 0291     		str	r1, [sp, #8]
 953:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_FREE(msg);
 1343              		.loc 1 953 3 is_stmt 1 view .LVU391
 953:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_FREE(msg);
 1344              		.loc 1 953 9 is_stmt 0 view .LVU392
 1345 000a 6946     		mov	r1, sp
 1346              	.LVL126:
 953:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_FREE(msg);
 1347              		.loc 1 953 9 view .LVU393
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 51


 1348 000c 0748     		ldr	r0, .L110
 1349              	.LVL127:
 953:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_FREE(msg);
 1350              		.loc 1 953 9 view .LVU394
 1351 000e FFF7FEFF 		bl	netconn_apimsg
 1352              	.LVL128:
 954:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 1353              		.loc 1 954 24 is_stmt 1 view .LVU395
 956:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** }
 1354              		.loc 1 956 3 view .LVU396
 1355              	.L107:
 957:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 1356              		.loc 1 957 1 is_stmt 0 view .LVU397
 1357 0012 09B0     		add	sp, sp, #36
 1358              	.LCFI45:
 1359              		.cfi_remember_state
 1360              		.cfi_def_cfa_offset 4
 1361              		@ sp needed
 1362 0014 5DF804FB 		ldr	pc, [sp], #4
 1363              	.LVL129:
 1364              	.L109:
 1365              	.LCFI46:
 1366              		.cfi_restore_state
 946:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 1367              		.loc 1 946 3 is_stmt 1 discriminator 1 view .LVU398
 946:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 1368              		.loc 1 946 3 discriminator 1 view .LVU399
 1369 0018 054B     		ldr	r3, .L110+4
 1370 001a 40F2B232 		movw	r2, #946
 1371 001e 0549     		ldr	r1, .L110+8
 1372              	.LVL130:
 946:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 1373              		.loc 1 946 3 is_stmt 0 discriminator 1 view .LVU400
 1374 0020 0548     		ldr	r0, .L110+12
 1375              	.LVL131:
 946:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 1376              		.loc 1 946 3 discriminator 1 view .LVU401
 1377 0022 FFF7FEFF 		bl	printf
 1378              	.LVL132:
 946:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 1379              		.loc 1 946 3 is_stmt 1 discriminator 1 view .LVU402
 946:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 1380              		.loc 1 946 3 discriminator 1 view .LVU403
 1381 0026 6FF00F00 		mvn	r0, #15
 946:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 1382              		.loc 1 946 3 is_stmt 0 view .LVU404
 1383 002a F2E7     		b	.L107
 1384              	.L111:
 1385              		.align	2
 1386              	.L110:
 1387 002c 00000000 		.word	lwip_netconn_do_send
 1388 0030 00000000 		.word	.LC0
 1389 0034 00000000 		.word	.LC16
 1390 0038 54000000 		.word	.LC2
 1391              		.cfi_endproc
 1392              	.LFE195:
 1394              		.section	.text.netconn_sendto,"ax",%progbits
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 52


 1395              		.align	1
 1396              		.global	netconn_sendto
 1397              		.syntax unified
 1398              		.thumb
 1399              		.thumb_func
 1401              	netconn_sendto:
 1402              	.LVL133:
 1403              	.LFB194:
 923:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   if (buf != NULL) {
 1404              		.loc 1 923 1 is_stmt 1 view -0
 1405              		.cfi_startproc
 1406              		@ args = 0, pretend = 0, frame = 0
 1407              		@ frame_needed = 0, uses_anonymous_args = 0
 924:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     ip_addr_set(&buf->addr, addr);
 1408              		.loc 1 924 3 view .LVU406
 924:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     ip_addr_set(&buf->addr, addr);
 1409              		.loc 1 924 6 is_stmt 0 view .LVU407
 1410 0000 69B1     		cbz	r1, .L115
 923:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   if (buf != NULL) {
 1411              		.loc 1 923 1 view .LVU408
 1412 0002 08B5     		push	{r3, lr}
 1413              	.LCFI47:
 1414              		.cfi_def_cfa_offset 8
 1415              		.cfi_offset 3, -8
 1416              		.cfi_offset 14, -4
 1417 0004 8C46     		mov	ip, r1
 925:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     buf->port = port;
 1418              		.loc 1 925 5 is_stmt 1 view .LVU409
 1419 0006 42B1     		cbz	r2, .L116
 925:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     buf->port = port;
 1420              		.loc 1 925 5 is_stmt 0 discriminator 1 view .LVU410
 1421 0008 1268     		ldr	r2, [r2]
 1422              	.LVL134:
 1423              	.L114:
 925:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     buf->port = port;
 1424              		.loc 1 925 5 discriminator 4 view .LVU411
 1425 000a CCF80820 		str	r2, [ip, #8]
 926:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     return netconn_send(conn, buf);
 1426              		.loc 1 926 5 is_stmt 1 view .LVU412
 926:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     return netconn_send(conn, buf);
 1427              		.loc 1 926 15 is_stmt 0 view .LVU413
 1428 000e ACF80C30 		strh	r3, [ip, #12]	@ movhi
 927:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   }
 1429              		.loc 1 927 5 is_stmt 1 view .LVU414
 927:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   }
 1430              		.loc 1 927 12 is_stmt 0 view .LVU415
 1431 0012 6146     		mov	r1, ip
 1432              	.LVL135:
 927:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   }
 1433              		.loc 1 927 12 view .LVU416
 1434 0014 FFF7FEFF 		bl	netconn_send
 1435              	.LVL136:
 930:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 1436              		.loc 1 930 1 view .LVU417
 1437 0018 08BD     		pop	{r3, pc}
 1438              	.LVL137:
 1439              	.L116:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 53


 925:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     buf->port = port;
 1440              		.loc 1 925 5 discriminator 2 view .LVU418
 1441 001a 0022     		movs	r2, #0
 1442              	.LVL138:
 925:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     buf->port = port;
 1443              		.loc 1 925 5 discriminator 2 view .LVU419
 1444 001c F5E7     		b	.L114
 1445              	.LVL139:
 1446              	.L115:
 1447              	.LCFI48:
 1448              		.cfi_def_cfa_offset 0
 1449              		.cfi_restore 3
 1450              		.cfi_restore 14
 929:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** }
 1451              		.loc 1 929 10 view .LVU420
 1452 001e 6FF00500 		mvn	r0, #5
 1453              	.LVL140:
 930:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 1454              		.loc 1 930 1 view .LVU421
 1455 0022 7047     		bx	lr
 1456              		.cfi_endproc
 1457              	.LFE194:
 1459              		.section	.rodata.netconn_write_vectors_partly.str1.4,"aMS",%progbits,1
 1460              		.align	2
 1461              	.LC17:
 1462 0000 6E657463 		.ascii	"netconn_write: invalid conn\000"
 1462      6F6E6E5F 
 1462      77726974 
 1462      653A2069 
 1462      6E76616C 
 1463              		.align	2
 1464              	.LC18:
 1465 001c 6E657463 		.ascii	"netconn_write: invalid conn->type\000"
 1465      6F6E6E5F 
 1465      77726974 
 1465      653A2069 
 1465      6E76616C 
 1466 003e 0000     		.align	2
 1467              	.LC19:
 1468 0040 646F5F77 		.ascii	"do_write failed to write all bytes\000"
 1468      72697465 
 1468      20666169 
 1468      6C656420 
 1468      746F2077 
 1469              		.section	.text.netconn_write_vectors_partly,"ax",%progbits
 1470              		.align	1
 1471              		.global	netconn_write_vectors_partly
 1472              		.syntax unified
 1473              		.thumb
 1474              		.thumb_func
 1476              	netconn_write_vectors_partly:
 1477              	.LVL141:
 1478              	.LFB197:
 999:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_DECLARE(msg);
 1479              		.loc 1 999 1 is_stmt 1 view -0
 1480              		.cfi_startproc
 1481              		@ args = 4, pretend = 0, frame = 32
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 54


 1482              		@ frame_needed = 0, uses_anonymous_args = 0
 999:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_DECLARE(msg);
 1483              		.loc 1 999 1 is_stmt 0 view .LVU423
 1484 0000 F0B5     		push	{r4, r5, r6, r7, lr}
 1485              	.LCFI49:
 1486              		.cfi_def_cfa_offset 20
 1487              		.cfi_offset 4, -20
 1488              		.cfi_offset 5, -16
 1489              		.cfi_offset 6, -12
 1490              		.cfi_offset 7, -8
 1491              		.cfi_offset 14, -4
 1492 0002 89B0     		sub	sp, sp, #36
 1493              	.LCFI50:
 1494              		.cfi_def_cfa_offset 56
 1495 0004 0E9E     		ldr	r6, [sp, #56]
1000:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   err_t err;
 1496              		.loc 1 1000 3 is_stmt 1 view .LVU424
1001:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   u8_t dontblock;
 1497              		.loc 1 1001 3 view .LVU425
1002:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   size_t size;
 1498              		.loc 1 1002 3 view .LVU426
1003:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   int i;
 1499              		.loc 1 1003 3 view .LVU427
1004:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 1500              		.loc 1 1004 3 view .LVU428
1006:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_write: invalid conn->type",  (NETCONNTYPE_GROUP(conn->type) == NETCONN_TCP), 
 1501              		.loc 1 1006 3 view .LVU429
1006:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_write: invalid conn->type",  (NETCONNTYPE_GROUP(conn->type) == NETCONN_TCP), 
 1502              		.loc 1 1006 3 view .LVU430
 1503 0006 78B1     		cbz	r0, .L137
 1504 0008 8446     		mov	ip, r0
1006:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_write: invalid conn->type",  (NETCONNTYPE_GROUP(conn->type) == NETCONN_TCP), 
 1505              		.loc 1 1006 3 discriminator 2 view .LVU431
1006:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_write: invalid conn->type",  (NETCONNTYPE_GROUP(conn->type) == NETCONN_TCP), 
 1506              		.loc 1 1006 3 discriminator 2 view .LVU432
1007:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   dontblock = netconn_is_nonblocking(conn) || (apiflags & NETCONN_DONTBLOCK);
 1507              		.loc 1 1007 3 view .LVU433
1007:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   dontblock = netconn_is_nonblocking(conn) || (apiflags & NETCONN_DONTBLOCK);
 1508              		.loc 1 1007 3 view .LVU434
 1509 000a 0078     		ldrb	r0, [r0]	@ zero_extendqisi2
 1510              	.LVL142:
1007:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   dontblock = netconn_is_nonblocking(conn) || (apiflags & NETCONN_DONTBLOCK);
 1511              		.loc 1 1007 3 is_stmt 0 view .LVU435
 1512 000c 00F0F000 		and	r0, r0, #240
 1513 0010 1028     		cmp	r0, #16
 1514 0012 13D1     		bne	.L138
1007:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   dontblock = netconn_is_nonblocking(conn) || (apiflags & NETCONN_DONTBLOCK);
 1515              		.loc 1 1007 3 is_stmt 1 discriminator 2 view .LVU436
1007:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   dontblock = netconn_is_nonblocking(conn) || (apiflags & NETCONN_DONTBLOCK);
 1516              		.loc 1 1007 3 discriminator 2 view .LVU437
1008:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #if LWIP_SO_SNDTIMEO
 1517              		.loc 1 1008 3 view .LVU438
1008:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #if LWIP_SO_SNDTIMEO
 1518              		.loc 1 1008 15 is_stmt 0 view .LVU439
 1519 0014 9CF82000 		ldrb	r0, [ip, #32]	@ zero_extendqisi2
1008:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #if LWIP_SO_SNDTIMEO
 1520              		.loc 1 1008 44 view .LVU440
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 55


 1521 0018 10F0020F 		tst	r0, #2
 1522 001c 18D1     		bne	.L130
1008:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #if LWIP_SO_SNDTIMEO
 1523              		.loc 1 1008 44 discriminator 2 view .LVU441
 1524 001e 13F0040F 		tst	r3, #4
 1525 0022 28D0     		beq	.L131
1008:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #if LWIP_SO_SNDTIMEO
 1526              		.loc 1 1008 44 discriminator 3 view .LVU442
 1527 0024 0120     		movs	r0, #1
 1528 0026 14E0     		b	.L125
 1529              	.LVL143:
 1530              	.L137:
1006:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_write: invalid conn->type",  (NETCONNTYPE_GROUP(conn->type) == NETCONN_TCP), 
 1531              		.loc 1 1006 3 is_stmt 1 discriminator 1 view .LVU443
1006:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_write: invalid conn->type",  (NETCONNTYPE_GROUP(conn->type) == NETCONN_TCP), 
 1532              		.loc 1 1006 3 discriminator 1 view .LVU444
 1533 0028 2D4B     		ldr	r3, .L140
 1534              	.LVL144:
1006:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_write: invalid conn->type",  (NETCONNTYPE_GROUP(conn->type) == NETCONN_TCP), 
 1535              		.loc 1 1006 3 is_stmt 0 discriminator 1 view .LVU445
 1536 002a 40F2EE32 		movw	r2, #1006
 1537              	.LVL145:
1006:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_write: invalid conn->type",  (NETCONNTYPE_GROUP(conn->type) == NETCONN_TCP), 
 1538              		.loc 1 1006 3 discriminator 1 view .LVU446
 1539 002e 2D49     		ldr	r1, .L140+4
 1540              	.LVL146:
1006:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_write: invalid conn->type",  (NETCONNTYPE_GROUP(conn->type) == NETCONN_TCP), 
 1541              		.loc 1 1006 3 discriminator 1 view .LVU447
 1542 0030 2D48     		ldr	r0, .L140+8
 1543              	.LVL147:
1006:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_write: invalid conn->type",  (NETCONNTYPE_GROUP(conn->type) == NETCONN_TCP), 
 1544              		.loc 1 1006 3 discriminator 1 view .LVU448
 1545 0032 FFF7FEFF 		bl	printf
 1546              	.LVL148:
1006:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_write: invalid conn->type",  (NETCONNTYPE_GROUP(conn->type) == NETCONN_TCP), 
 1547              		.loc 1 1006 3 is_stmt 1 discriminator 1 view .LVU449
1006:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_write: invalid conn->type",  (NETCONNTYPE_GROUP(conn->type) == NETCONN_TCP), 
 1548              		.loc 1 1006 3 discriminator 1 view .LVU450
 1549 0036 6FF00F05 		mvn	r5, #15
1006:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_write: invalid conn->type",  (NETCONNTYPE_GROUP(conn->type) == NETCONN_TCP), 
 1550              		.loc 1 1006 3 is_stmt 0 view .LVU451
 1551 003a 49E0     		b	.L123
 1552              	.LVL149:
 1553              	.L138:
1007:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   dontblock = netconn_is_nonblocking(conn) || (apiflags & NETCONN_DONTBLOCK);
 1554              		.loc 1 1007 3 is_stmt 1 discriminator 1 view .LVU452
1007:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   dontblock = netconn_is_nonblocking(conn) || (apiflags & NETCONN_DONTBLOCK);
 1555              		.loc 1 1007 3 discriminator 1 view .LVU453
 1556 003c 284B     		ldr	r3, .L140
 1557              	.LVL150:
1007:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   dontblock = netconn_is_nonblocking(conn) || (apiflags & NETCONN_DONTBLOCK);
 1558              		.loc 1 1007 3 is_stmt 0 discriminator 1 view .LVU454
 1559 003e 40F2EF32 		movw	r2, #1007
 1560              	.LVL151:
1007:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   dontblock = netconn_is_nonblocking(conn) || (apiflags & NETCONN_DONTBLOCK);
 1561              		.loc 1 1007 3 discriminator 1 view .LVU455
 1562 0042 2A49     		ldr	r1, .L140+12
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 56


 1563              	.LVL152:
1007:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   dontblock = netconn_is_nonblocking(conn) || (apiflags & NETCONN_DONTBLOCK);
 1564              		.loc 1 1007 3 discriminator 1 view .LVU456
 1565 0044 2848     		ldr	r0, .L140+8
 1566 0046 FFF7FEFF 		bl	printf
 1567              	.LVL153:
1007:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   dontblock = netconn_is_nonblocking(conn) || (apiflags & NETCONN_DONTBLOCK);
 1568              		.loc 1 1007 3 is_stmt 1 discriminator 1 view .LVU457
1007:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   dontblock = netconn_is_nonblocking(conn) || (apiflags & NETCONN_DONTBLOCK);
 1569              		.loc 1 1007 3 discriminator 1 view .LVU458
 1570 004a 6FF00505 		mvn	r5, #5
1007:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   dontblock = netconn_is_nonblocking(conn) || (apiflags & NETCONN_DONTBLOCK);
 1571              		.loc 1 1007 3 is_stmt 0 view .LVU459
 1572 004e 3FE0     		b	.L123
 1573              	.LVL154:
 1574              	.L130:
1008:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #if LWIP_SO_SNDTIMEO
 1575              		.loc 1 1008 44 discriminator 3 view .LVU460
 1576 0050 0120     		movs	r0, #1
 1577              	.L125:
 1578              	.LVL155:
1014:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     /* This implies netconn_write() cannot be used for non-blocking send, since
 1579              		.loc 1 1014 3 is_stmt 1 view .LVU461
1014:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     /* This implies netconn_write() cannot be used for non-blocking send, since
 1580              		.loc 1 1014 7 is_stmt 0 view .LVU462
 1581 0052 00F0FF07 		and	r7, r0, #255
1014:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     /* This implies netconn_write() cannot be used for non-blocking send, since
 1582              		.loc 1 1014 17 view .LVU463
 1583 0056 002E     		cmp	r6, #0
 1584 0058 14BF     		ite	ne
 1585 005a 0020     		movne	r0, #0
 1586 005c 00F00100 		andeq	r0, r0, #1
 1587              	.LVL156:
1014:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     /* This implies netconn_write() cannot be used for non-blocking send, since
 1588              		.loc 1 1014 6 view .LVU464
 1589 0060 88BB     		cbnz	r0, .L132
1021:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   for (i = 0; i < vectorcnt; i++) {
 1590              		.loc 1 1021 8 view .LVU465
 1591 0062 0446     		mov	r4, r0
 1592              	.L126:
 1593              	.LVL157:
1022:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     size += vectors[i].len;
 1594              		.loc 1 1022 17 is_stmt 1 discriminator 1 view .LVU466
 1595 0064 8242     		cmp	r2, r0
 1596 0066 08DD     		ble	.L139
1023:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     if (size < vectors[i].len) {
 1597              		.loc 1 1023 5 view .LVU467
1023:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     if (size < vectors[i].len) {
 1598              		.loc 1 1023 20 is_stmt 0 view .LVU468
 1599 0068 01EBC005 		add	r5, r1, r0, lsl #3
1023:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     if (size < vectors[i].len) {
 1600              		.loc 1 1023 23 view .LVU469
 1601 006c 6D68     		ldr	r5, [r5, #4]
 1602              	.LVL158:
1024:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       /* overflow */
 1603              		.loc 1 1024 5 is_stmt 1 view .LVU470
1024:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       /* overflow */
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 57


 1604              		.loc 1 1024 8 is_stmt 0 view .LVU471
 1605 006e 6419     		adds	r4, r4, r5
 1606              	.LVL159:
1024:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       /* overflow */
 1607              		.loc 1 1024 8 view .LVU472
 1608 0070 2CD2     		bcs	.L133
1022:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     size += vectors[i].len;
 1609              		.loc 1 1022 31 is_stmt 1 discriminator 2 view .LVU473
 1610 0072 0130     		adds	r0, r0, #1
 1611              	.LVL160:
1022:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     size += vectors[i].len;
 1612              		.loc 1 1022 31 is_stmt 0 discriminator 2 view .LVU474
 1613 0074 F6E7     		b	.L126
 1614              	.LVL161:
 1615              	.L131:
1008:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #if LWIP_SO_SNDTIMEO
 1616              		.loc 1 1008 44 discriminator 4 view .LVU475
 1617 0076 0020     		movs	r0, #0
 1618 0078 EBE7     		b	.L125
 1619              	.LVL162:
 1620              	.L139:
1029:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     return ERR_OK;
 1621              		.loc 1 1029 3 is_stmt 1 view .LVU476
1029:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     return ERR_OK;
 1622              		.loc 1 1029 6 is_stmt 0 view .LVU477
 1623 007a 64B3     		cbz	r4, .L134
1031:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     ssize_t limited;
 1624              		.loc 1 1031 10 is_stmt 1 view .LVU478
1031:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     ssize_t limited;
 1625              		.loc 1 1031 13 is_stmt 0 view .LVU479
 1626 007c 002C     		cmp	r4, #0
 1627 007e 02DA     		bge	.L128
 1628              	.LBB4:
1032:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     /* this is required by the socket layer (cannot send full size_t range) */
 1629              		.loc 1 1032 5 is_stmt 1 view .LVU480
1034:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       return ERR_VAL;
 1630              		.loc 1 1034 5 view .LVU481
1034:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       return ERR_VAL;
 1631              		.loc 1 1034 8 is_stmt 0 view .LVU482
 1632 0080 5EB3     		cbz	r6, .L135
1039:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   }
 1633              		.loc 1 1039 10 view .LVU483
 1634 0082 6FF00044 		mvn	r4, #-2147483648
 1635              	.LVL163:
 1636              	.L128:
1039:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   }
 1637              		.loc 1 1039 10 view .LVU484
 1638              	.LBE4:
1042:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   /* non-blocking write sends as much  */
 1639              		.loc 1 1042 25 is_stmt 1 view .LVU485
1044:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_REF(msg).msg.w.vector = vectors;
 1640              		.loc 1 1044 3 view .LVU486
1044:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_REF(msg).msg.w.vector = vectors;
 1641              		.loc 1 1044 29 is_stmt 0 view .LVU487
 1642 0086 CDF800C0 		str	ip, [sp]
1045:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_REF(msg).msg.w.vector_cnt = vectorcnt;
 1643              		.loc 1 1045 3 is_stmt 1 view .LVU488
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 58


1045:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_REF(msg).msg.w.vector_cnt = vectorcnt;
 1644              		.loc 1 1045 37 is_stmt 0 view .LVU489
 1645 008a 0291     		str	r1, [sp, #8]
1046:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_REF(msg).msg.w.vector_off = 0;
 1646              		.loc 1 1046 3 is_stmt 1 view .LVU490
1046:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_REF(msg).msg.w.vector_off = 0;
 1647              		.loc 1 1046 41 is_stmt 0 view .LVU491
 1648 008c ADF80C20 		strh	r2, [sp, #12]	@ movhi
1047:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_REF(msg).msg.w.apiflags = apiflags;
 1649              		.loc 1 1047 3 is_stmt 1 view .LVU492
1047:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_REF(msg).msg.w.apiflags = apiflags;
 1650              		.loc 1 1047 41 is_stmt 0 view .LVU493
 1651 0090 0022     		movs	r2, #0
 1652              	.LVL164:
1047:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_REF(msg).msg.w.apiflags = apiflags;
 1653              		.loc 1 1047 41 view .LVU494
 1654 0092 0492     		str	r2, [sp, #16]
1048:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_REF(msg).msg.w.len = size;
 1655              		.loc 1 1048 3 is_stmt 1 view .LVU495
1048:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_REF(msg).msg.w.len = size;
 1656              		.loc 1 1048 39 is_stmt 0 view .LVU496
 1657 0094 8DF81C30 		strb	r3, [sp, #28]
1049:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_REF(msg).msg.w.offset = 0;
 1658              		.loc 1 1049 3 is_stmt 1 view .LVU497
1049:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_REF(msg).msg.w.offset = 0;
 1659              		.loc 1 1049 34 is_stmt 0 view .LVU498
 1660 0098 0594     		str	r4, [sp, #20]
1050:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #if LWIP_SO_SNDTIMEO
 1661              		.loc 1 1050 3 is_stmt 1 view .LVU499
1050:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #if LWIP_SO_SNDTIMEO
 1662              		.loc 1 1050 37 is_stmt 0 view .LVU500
 1663 009a 0692     		str	r2, [sp, #24]
1064:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   if (err == ERR_OK) {
 1664              		.loc 1 1064 3 is_stmt 1 view .LVU501
1064:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   if (err == ERR_OK) {
 1665              		.loc 1 1064 9 is_stmt 0 view .LVU502
 1666 009c 6946     		mov	r1, sp
 1667              	.LVL165:
1064:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   if (err == ERR_OK) {
 1668              		.loc 1 1064 9 view .LVU503
 1669 009e 1448     		ldr	r0, .L140+16
 1670              	.LVL166:
1064:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   if (err == ERR_OK) {
 1671              		.loc 1 1064 9 view .LVU504
 1672 00a0 FFF7FEFF 		bl	netconn_apimsg
 1673              	.LVL167:
1065:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     if (bytes_written != NULL) {
 1674              		.loc 1 1065 3 is_stmt 1 view .LVU505
1065:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     if (bytes_written != NULL) {
 1675              		.loc 1 1065 6 is_stmt 0 view .LVU506
 1676 00a4 0546     		mov	r5, r0
 1677 00a6 98B9     		cbnz	r0, .L123
1066:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       *bytes_written = API_MSG_VAR_REF(msg).msg.w.offset;
 1678              		.loc 1 1066 5 is_stmt 1 view .LVU507
1066:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       *bytes_written = API_MSG_VAR_REF(msg).msg.w.offset;
 1679              		.loc 1 1066 8 is_stmt 0 view .LVU508
 1680 00a8 0EB1     		cbz	r6, .L129
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 59


1067:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     }
 1681              		.loc 1 1067 7 is_stmt 1 view .LVU509
1067:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     }
 1682              		.loc 1 1067 50 is_stmt 0 view .LVU510
 1683 00aa 069B     		ldr	r3, [sp, #24]
1067:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     }
 1684              		.loc 1 1067 22 view .LVU511
 1685 00ac 3360     		str	r3, [r6]
 1686              	.L129:
1071:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       LWIP_ASSERT("do_write failed to write all bytes", API_MSG_VAR_REF(msg).msg.w.offset == size);
 1687              		.loc 1 1071 5 is_stmt 1 view .LVU512
1071:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       LWIP_ASSERT("do_write failed to write all bytes", API_MSG_VAR_REF(msg).msg.w.offset == size);
 1688              		.loc 1 1071 8 is_stmt 0 view .LVU513
 1689 00ae 7FB9     		cbnz	r7, .L123
1072:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     }
 1690              		.loc 1 1072 7 is_stmt 1 view .LVU514
1072:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     }
 1691              		.loc 1 1072 7 view .LVU515
 1692 00b0 069B     		ldr	r3, [sp, #24]
 1693 00b2 A342     		cmp	r3, r4
 1694 00b4 0CD0     		beq	.L123
1072:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     }
 1695              		.loc 1 1072 7 discriminator 1 view .LVU516
1072:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     }
 1696              		.loc 1 1072 7 discriminator 1 view .LVU517
 1697 00b6 0A4B     		ldr	r3, .L140
 1698 00b8 4FF48662 		mov	r2, #1072
 1699 00bc 0D49     		ldr	r1, .L140+20
 1700 00be 0A48     		ldr	r0, .L140+8
 1701              	.LVL168:
1072:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     }
 1702              		.loc 1 1072 7 is_stmt 0 discriminator 1 view .LVU518
 1703 00c0 FFF7FEFF 		bl	printf
 1704              	.LVL169:
 1705 00c4 04E0     		b	.L123
 1706              	.LVL170:
 1707              	.L132:
1017:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   }
 1708              		.loc 1 1017 12 view .LVU519
 1709 00c6 6FF00505 		mvn	r5, #5
 1710 00ca 01E0     		b	.L123
 1711              	.LVL171:
 1712              	.L133:
1026:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     }
 1713              		.loc 1 1026 14 view .LVU520
 1714 00cc 6FF00505 		mvn	r5, #5
 1715              	.LVL172:
 1716              	.L123:
1078:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 1717              		.loc 1 1078 1 view .LVU521
 1718 00d0 2846     		mov	r0, r5
 1719 00d2 09B0     		add	sp, sp, #36
 1720              	.LCFI51:
 1721              		.cfi_remember_state
 1722              		.cfi_def_cfa_offset 20
 1723              		@ sp needed
 1724 00d4 F0BD     		pop	{r4, r5, r6, r7, pc}
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 60


 1725              	.LVL173:
 1726              	.L134:
 1727              	.LCFI52:
 1728              		.cfi_restore_state
1030:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   } else if (size > SSIZE_MAX) {
 1729              		.loc 1 1030 12 view .LVU522
 1730 00d6 0025     		movs	r5, #0
 1731 00d8 FAE7     		b	.L123
 1732              	.L135:
 1733              	.LBB5:
1035:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     }
 1734              		.loc 1 1035 14 view .LVU523
 1735 00da 6FF00505 		mvn	r5, #5
 1736 00de F7E7     		b	.L123
 1737              	.L141:
 1738              		.align	2
 1739              	.L140:
 1740 00e0 00000000 		.word	.LC0
 1741 00e4 00000000 		.word	.LC17
 1742 00e8 54000000 		.word	.LC2
 1743 00ec 1C000000 		.word	.LC18
 1744 00f0 00000000 		.word	lwip_netconn_do_write
 1745 00f4 40000000 		.word	.LC19
 1746              	.LBE5:
 1747              		.cfi_endproc
 1748              	.LFE197:
 1750              		.section	.text.netconn_write_partly,"ax",%progbits
 1751              		.align	1
 1752              		.global	netconn_write_partly
 1753              		.syntax unified
 1754              		.thumb
 1755              		.thumb_func
 1757              	netconn_write_partly:
 1758              	.LVL174:
 1759              	.LFB196:
 976:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   struct netvector vector;
 1760              		.loc 1 976 1 is_stmt 1 view -0
 1761              		.cfi_startproc
 1762              		@ args = 4, pretend = 0, frame = 8
 1763              		@ frame_needed = 0, uses_anonymous_args = 0
 976:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   struct netvector vector;
 1764              		.loc 1 976 1 is_stmt 0 view .LVU525
 1765 0000 00B5     		push	{lr}
 1766              	.LCFI53:
 1767              		.cfi_def_cfa_offset 4
 1768              		.cfi_offset 14, -4
 1769 0002 85B0     		sub	sp, sp, #20
 1770              	.LCFI54:
 1771              		.cfi_def_cfa_offset 24
 977:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   vector.ptr = dataptr;
 1772              		.loc 1 977 3 is_stmt 1 view .LVU526
 978:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   vector.len = size;
 1773              		.loc 1 978 3 view .LVU527
 978:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   vector.len = size;
 1774              		.loc 1 978 14 is_stmt 0 view .LVU528
 1775 0004 0291     		str	r1, [sp, #8]
 979:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   return netconn_write_vectors_partly(conn, &vector, 1, apiflags, bytes_written);
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 61


 1776              		.loc 1 979 3 is_stmt 1 view .LVU529
 979:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   return netconn_write_vectors_partly(conn, &vector, 1, apiflags, bytes_written);
 1777              		.loc 1 979 14 is_stmt 0 view .LVU530
 1778 0006 0392     		str	r2, [sp, #12]
 980:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** }
 1779              		.loc 1 980 3 is_stmt 1 view .LVU531
 980:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** }
 1780              		.loc 1 980 10 is_stmt 0 view .LVU532
 1781 0008 069A     		ldr	r2, [sp, #24]
 1782              	.LVL175:
 980:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** }
 1783              		.loc 1 980 10 view .LVU533
 1784 000a 0092     		str	r2, [sp]
 1785 000c 0122     		movs	r2, #1
 1786 000e 02A9     		add	r1, sp, #8
 1787              	.LVL176:
 980:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** }
 1788              		.loc 1 980 10 view .LVU534
 1789 0010 FFF7FEFF 		bl	netconn_write_vectors_partly
 1790              	.LVL177:
 981:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 1791              		.loc 1 981 1 view .LVU535
 1792 0014 05B0     		add	sp, sp, #20
 1793              	.LCFI55:
 1794              		.cfi_def_cfa_offset 4
 1795              		@ sp needed
 1796 0016 5DF804FB 		ldr	pc, [sp], #4
 981:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 1797              		.loc 1 981 1 view .LVU536
 1798              		.cfi_endproc
 1799              	.LFE196:
 1801              		.section	.text.netconn_close,"ax",%progbits
 1802              		.align	1
 1803              		.global	netconn_close
 1804              		.syntax unified
 1805              		.thumb
 1806              		.thumb_func
 1808              	netconn_close:
 1809              	.LVL178:
 1810              	.LFB199:
1116:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
1117:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** /**
1118:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @ingroup netconn_tcp
1119:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * Close a TCP netconn (doesn't delete it).
1120:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  *
1121:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @param conn the TCP netconn to close
1122:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @return ERR_OK if the netconn was closed, any other err_t on error
1123:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  */
1124:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** err_t
1125:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** netconn_close(struct netconn *conn)
1126:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** {
 1811              		.loc 1 1126 1 is_stmt 1 view -0
 1812              		.cfi_startproc
 1813              		@ args = 0, pretend = 0, frame = 0
 1814              		@ frame_needed = 0, uses_anonymous_args = 0
 1815              		.loc 1 1126 1 is_stmt 0 view .LVU538
 1816 0000 08B5     		push	{r3, lr}
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 62


 1817              	.LCFI56:
 1818              		.cfi_def_cfa_offset 8
 1819              		.cfi_offset 3, -8
 1820              		.cfi_offset 14, -4
1127:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   /* shutting down both ends is the same as closing */
1128:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   return netconn_close_shutdown(conn, NETCONN_SHUT_RDWR);
 1821              		.loc 1 1128 3 is_stmt 1 view .LVU539
 1822              		.loc 1 1128 10 is_stmt 0 view .LVU540
 1823 0002 0321     		movs	r1, #3
 1824 0004 FFF7FEFF 		bl	netconn_close_shutdown
 1825              	.LVL179:
1129:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** }
 1826              		.loc 1 1129 1 view .LVU541
 1827 0008 08BD     		pop	{r3, pc}
 1828              		.cfi_endproc
 1829              	.LFE199:
 1831              		.section	.text.netconn_err,"ax",%progbits
 1832              		.align	1
 1833              		.global	netconn_err
 1834              		.syntax unified
 1835              		.thumb
 1836              		.thumb_func
 1838              	netconn_err:
 1839              	.LVL180:
 1840              	.LFB200:
1130:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
1131:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** /**
1132:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @ingroup netconn_common
1133:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * Get and reset pending error on a netconn
1134:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  *
1135:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @param conn the netconn to get the error from
1136:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @return and pending error or ERR_OK if no error was pending
1137:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  */
1138:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** err_t
1139:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** netconn_err(struct netconn *conn)
1140:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** {
 1841              		.loc 1 1140 1 is_stmt 1 view -0
 1842              		.cfi_startproc
 1843              		@ args = 0, pretend = 0, frame = 0
 1844              		@ frame_needed = 0, uses_anonymous_args = 0
 1845              		.loc 1 1140 1 is_stmt 0 view .LVU543
 1846 0000 38B5     		push	{r3, r4, r5, lr}
 1847              	.LCFI57:
 1848              		.cfi_def_cfa_offset 16
 1849              		.cfi_offset 3, -16
 1850              		.cfi_offset 4, -12
 1851              		.cfi_offset 5, -8
 1852              		.cfi_offset 14, -4
1141:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   err_t err;
 1853              		.loc 1 1141 3 is_stmt 1 view .LVU544
1142:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   SYS_ARCH_DECL_PROTECT(lev);
 1854              		.loc 1 1142 3 view .LVU545
1143:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   if (conn == NULL) {
 1855              		.loc 1 1143 3 view .LVU546
 1856              		.loc 1 1143 6 is_stmt 0 view .LVU547
 1857 0002 50B1     		cbz	r0, .L148
 1858 0004 0446     		mov	r4, r0
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 63


1144:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     return ERR_OK;
1145:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   }
1146:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   SYS_ARCH_PROTECT(lev);
 1859              		.loc 1 1146 3 is_stmt 1 view .LVU548
 1860 0006 FFF7FEFF 		bl	sys_arch_protect
 1861              	.LVL181:
1147:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   err = conn->pending_err;
 1862              		.loc 1 1147 3 view .LVU549
 1863              		.loc 1 1147 7 is_stmt 0 view .LVU550
 1864 000a 94F90850 		ldrsb	r5, [r4, #8]
 1865              	.LVL182:
1148:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   conn->pending_err = ERR_OK;
 1866              		.loc 1 1148 3 is_stmt 1 view .LVU551
 1867              		.loc 1 1148 21 is_stmt 0 view .LVU552
 1868 000e 0023     		movs	r3, #0
 1869 0010 2372     		strb	r3, [r4, #8]
1149:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   SYS_ARCH_UNPROTECT(lev);
 1870              		.loc 1 1149 3 is_stmt 1 view .LVU553
 1871 0012 FFF7FEFF 		bl	sys_arch_unprotect
 1872              	.LVL183:
1150:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   return err;
 1873              		.loc 1 1150 3 view .LVU554
 1874              	.L147:
1151:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** }
 1875              		.loc 1 1151 1 is_stmt 0 view .LVU555
 1876 0016 2846     		mov	r0, r5
 1877 0018 38BD     		pop	{r3, r4, r5, pc}
 1878              	.LVL184:
 1879              	.L148:
1144:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   }
 1880              		.loc 1 1144 12 view .LVU556
 1881 001a 0025     		movs	r5, #0
 1882 001c FBE7     		b	.L147
 1883              		.cfi_endproc
 1884              	.LFE200:
 1886              		.section	.rodata.netconn_accept.str1.4,"aMS",%progbits,1
 1887              		.align	2
 1888              	.LC20:
 1889 0000 6E657463 		.ascii	"netconn_accept: invalid pointer\000"
 1889      6F6E6E5F 
 1889      61636365 
 1889      70743A20 
 1889      696E7661 
 1890              		.align	2
 1891              	.LC21:
 1892 0020 6E657463 		.ascii	"netconn_accept: invalid conn\000"
 1892      6F6E6E5F 
 1892      61636365 
 1892      70743A20 
 1892      696E7661 
 1893              		.section	.text.netconn_accept,"ax",%progbits
 1894              		.align	1
 1895              		.global	netconn_accept
 1896              		.syntax unified
 1897              		.thumb
 1898              		.thumb_func
 1900              	netconn_accept:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 64


 1901              	.LVL185:
 1902              	.LFB184:
 472:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #if LWIP_TCP
 1903              		.loc 1 472 1 is_stmt 1 view -0
 1904              		.cfi_startproc
 1905              		@ args = 0, pretend = 0, frame = 8
 1906              		@ frame_needed = 0, uses_anonymous_args = 0
 472:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #if LWIP_TCP
 1907              		.loc 1 472 1 is_stmt 0 view .LVU558
 1908 0000 F0B5     		push	{r4, r5, r6, r7, lr}
 1909              	.LCFI58:
 1910              		.cfi_def_cfa_offset 20
 1911              		.cfi_offset 4, -20
 1912              		.cfi_offset 5, -16
 1913              		.cfi_offset 6, -12
 1914              		.cfi_offset 7, -8
 1915              		.cfi_offset 14, -4
 1916 0002 83B0     		sub	sp, sp, #12
 1917              	.LCFI59:
 1918              		.cfi_def_cfa_offset 32
 474:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   void *accept_ptr;
 1919              		.loc 1 474 3 is_stmt 1 view .LVU559
 475:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   struct netconn *newconn;
 1920              		.loc 1 475 3 view .LVU560
 476:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #if TCP_LISTEN_BACKLOG
 1921              		.loc 1 476 3 view .LVU561
 481:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   *new_conn = NULL;
 1922              		.loc 1 481 3 view .LVU562
 481:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   *new_conn = NULL;
 1923              		.loc 1 481 3 view .LVU563
 1924 0004 69B1     		cbz	r1, .L164
 1925 0006 0446     		mov	r4, r0
 1926 0008 0E46     		mov	r6, r1
 481:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   *new_conn = NULL;
 1927              		.loc 1 481 3 discriminator 2 view .LVU564
 481:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   *new_conn = NULL;
 1928              		.loc 1 481 3 discriminator 2 view .LVU565
 482:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_accept: invalid conn",       (conn != NULL),                      return ERR_
 1929              		.loc 1 482 3 view .LVU566
 482:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_accept: invalid conn",       (conn != NULL),                      return ERR_
 1930              		.loc 1 482 13 is_stmt 0 view .LVU567
 1931 000a 0023     		movs	r3, #0
 1932 000c 0B60     		str	r3, [r1]
 483:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 1933              		.loc 1 483 3 is_stmt 1 view .LVU568
 483:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 1934              		.loc 1 483 3 view .LVU569
 1935 000e 90B1     		cbz	r0, .L165
 483:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 1936              		.loc 1 483 3 discriminator 2 view .LVU570
 483:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 1937              		.loc 1 483 3 discriminator 2 view .LVU571
 488:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   if (err != ERR_OK) {
 1938              		.loc 1 488 3 view .LVU572
 488:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   if (err != ERR_OK) {
 1939              		.loc 1 488 9 is_stmt 0 view .LVU573
 1940 0010 FFF7FEFF 		bl	netconn_err
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 65


 1941              	.LVL186:
 488:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   if (err != ERR_OK) {
 1942              		.loc 1 488 9 view .LVU574
 1943 0014 0546     		mov	r5, r0
 488:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   if (err != ERR_OK) {
 1944              		.loc 1 488 7 discriminator 1 view .LVU575
 1945 0016 8DF80700 		strb	r0, [sp, #7]
 489:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     /* return pending error */
 1946              		.loc 1 489 3 is_stmt 1 view .LVU576
 489:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     /* return pending error */
 1947              		.loc 1 489 6 is_stmt 0 view .LVU577
 1948 001a B0B1     		cbz	r0, .L166
 1949              	.LVL187:
 1950              	.L152:
 560:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 1951              		.loc 1 560 1 view .LVU578
 1952 001c 2846     		mov	r0, r5
 1953 001e 03B0     		add	sp, sp, #12
 1954              	.LCFI60:
 1955              		.cfi_remember_state
 1956              		.cfi_def_cfa_offset 20
 1957              		@ sp needed
 1958 0020 F0BD     		pop	{r4, r5, r6, r7, pc}
 1959              	.LVL188:
 1960              	.L164:
 1961              	.LCFI61:
 1962              		.cfi_restore_state
 481:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   *new_conn = NULL;
 1963              		.loc 1 481 3 is_stmt 1 discriminator 1 view .LVU579
 481:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   *new_conn = NULL;
 1964              		.loc 1 481 3 discriminator 1 view .LVU580
 1965 0022 294B     		ldr	r3, .L169
 1966 0024 40F2E112 		movw	r2, #481
 1967 0028 2849     		ldr	r1, .L169+4
 1968              	.LVL189:
 481:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   *new_conn = NULL;
 1969              		.loc 1 481 3 is_stmt 0 discriminator 1 view .LVU581
 1970 002a 2948     		ldr	r0, .L169+8
 1971              	.LVL190:
 481:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   *new_conn = NULL;
 1972              		.loc 1 481 3 discriminator 1 view .LVU582
 1973 002c FFF7FEFF 		bl	printf
 1974              	.LVL191:
 481:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   *new_conn = NULL;
 1975              		.loc 1 481 3 is_stmt 1 discriminator 1 view .LVU583
 481:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   *new_conn = NULL;
 1976              		.loc 1 481 3 discriminator 1 view .LVU584
 1977 0030 6FF00F05 		mvn	r5, #15
 481:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   *new_conn = NULL;
 1978              		.loc 1 481 3 is_stmt 0 view .LVU585
 1979 0034 F2E7     		b	.L152
 1980              	.LVL192:
 1981              	.L165:
 483:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 1982              		.loc 1 483 3 is_stmt 1 discriminator 1 view .LVU586
 483:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 1983              		.loc 1 483 3 discriminator 1 view .LVU587
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 66


 1984 0036 244B     		ldr	r3, .L169
 1985 0038 40F2E312 		movw	r2, #483
 1986 003c 2549     		ldr	r1, .L169+12
 1987              	.LVL193:
 483:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 1988              		.loc 1 483 3 is_stmt 0 discriminator 1 view .LVU588
 1989 003e 2448     		ldr	r0, .L169+8
 1990              	.LVL194:
 483:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 1991              		.loc 1 483 3 discriminator 1 view .LVU589
 1992 0040 FFF7FEFF 		bl	printf
 1993              	.LVL195:
 483:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 1994              		.loc 1 483 3 is_stmt 1 discriminator 1 view .LVU590
 483:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 1995              		.loc 1 483 3 discriminator 1 view .LVU591
 1996 0044 6FF00F05 		mvn	r5, #15
 483:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 1997              		.loc 1 483 3 is_stmt 0 view .LVU592
 1998 0048 E8E7     		b	.L152
 1999              	.L166:
 493:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     /* don't accept if closed: this might block the application task
 2000              		.loc 1 493 3 is_stmt 1 view .LVU593
 493:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     /* don't accept if closed: this might block the application task
 2001              		.loc 1 493 8 is_stmt 0 view .LVU594
 2002 004a 04F11407 		add	r7, r4, #20
 2003 004e 3846     		mov	r0, r7
 2004 0050 FFF7FEFF 		bl	sys_mbox_valid
 2005              	.LVL196:
 493:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     /* don't accept if closed: this might block the application task
 2006              		.loc 1 493 6 discriminator 1 view .LVU595
 2007 0054 60B3     		cbz	r0, .L158
 493:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     /* don't accept if closed: this might block the application task
 2008              		.loc 1 493 8 discriminator 1 view .LVU596
 2009 0056 94F82030 		ldrb	r3, [r4, #32]	@ zero_extendqisi2
 493:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     /* don't accept if closed: this might block the application task
 2010              		.loc 1 493 7 discriminator 1 view .LVU597
 2011 005a 13F0010F 		tst	r3, #1
 2012 005e 2AD1     		bne	.L159
 499:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 2013              		.loc 1 499 32 is_stmt 1 view .LVU598
 501:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   if (netconn_is_nonblocking(conn)) {
 2014              		.loc 1 501 33 view .LVU599
 502:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     if (sys_arch_mbox_tryfetch(&conn->acceptmbox, &accept_ptr) == SYS_ARCH_TIMEOUT) {
 2015              		.loc 1 502 3 view .LVU600
 502:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     if (sys_arch_mbox_tryfetch(&conn->acceptmbox, &accept_ptr) == SYS_ARCH_TIMEOUT) {
 2016              		.loc 1 502 6 is_stmt 0 view .LVU601
 2017 0060 13F0020F 		tst	r3, #2
 2018 0064 16D0     		beq	.L154
 503:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       API_MSG_VAR_FREE_ACCEPT(msg);
 2019              		.loc 1 503 5 is_stmt 1 view .LVU602
 503:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       API_MSG_VAR_FREE_ACCEPT(msg);
 2020              		.loc 1 503 9 is_stmt 0 view .LVU603
 2021 0066 6946     		mov	r1, sp
 2022 0068 3846     		mov	r0, r7
 2023 006a FFF7FEFF 		bl	sys_arch_mbox_tryfetch
 2024              	.LVL197:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 67


 503:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       API_MSG_VAR_FREE_ACCEPT(msg);
 2025              		.loc 1 503 8 discriminator 1 view .LVU604
 2026 006e B0F1FF3F 		cmp	r0, #-1
 2027 0072 23D0     		beq	.L167
 2028              	.L155:
 519:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #if LWIP_NETCONN_FULLDUPLEX
 2029              		.loc 1 519 33 is_stmt 1 view .LVU605
 531:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 2030              		.loc 1 531 3 view .LVU606
 2031 0074 A36A     		ldr	r3, [r4, #40]
 2032 0076 1BB1     		cbz	r3, .L156
 531:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 2033              		.loc 1 531 3 discriminator 1 view .LVU607
 2034 0078 0022     		movs	r2, #0
 2035 007a 0121     		movs	r1, #1
 2036 007c 2046     		mov	r0, r4
 2037 007e 9847     		blx	r3
 2038              	.LVL198:
 2039              	.L156:
 531:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 2040              		.loc 1 531 43 discriminator 3 view .LVU608
 533:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     /* a connection has been aborted: e.g. out of pcbs or out of netconns during accept */
 2041              		.loc 1 533 3 view .LVU609
 533:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     /* a connection has been aborted: e.g. out of pcbs or out of netconns during accept */
 2042              		.loc 1 533 7 is_stmt 0 view .LVU610
 2043 0080 0DF10701 		add	r1, sp, #7
 2044 0084 0098     		ldr	r0, [sp]
 2045 0086 FFF7FEFF 		bl	lwip_netconn_is_err_msg
 2046              	.LVL199:
 533:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     /* a connection has been aborted: e.g. out of pcbs or out of netconns during accept */
 2047              		.loc 1 533 6 discriminator 1 view .LVU611
 2048 008a 70B9     		cbnz	r0, .L168
 538:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     /* connection has been aborted */
 2049              		.loc 1 538 3 is_stmt 1 view .LVU612
 538:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     /* connection has been aborted */
 2050              		.loc 1 538 18 is_stmt 0 view .LVU613
 2051 008c 009B     		ldr	r3, [sp]
 538:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     /* connection has been aborted */
 2052              		.loc 1 538 6 view .LVU614
 2053 008e C3B1     		cbz	r3, .L162
 543:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #if TCP_LISTEN_BACKLOG
 2054              		.loc 1 543 3 is_stmt 1 view .LVU615
 2055              	.LVL200:
 552:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   /* don't set conn->last_err: it's only ERR_OK, anyway */
 2056              		.loc 1 552 3 view .LVU616
 552:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   /* don't set conn->last_err: it's only ERR_OK, anyway */
 2057              		.loc 1 552 13 is_stmt 0 view .LVU617
 2058 0090 3360     		str	r3, [r6]
 554:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #else /* LWIP_TCP */
 2059              		.loc 1 554 3 is_stmt 1 view .LVU618
 554:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #else /* LWIP_TCP */
 2060              		.loc 1 554 10 is_stmt 0 view .LVU619
 2061 0092 C3E7     		b	.L152
 2062              	.LVL201:
 2063              	.L154:
 510:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       API_MSG_VAR_FREE_ACCEPT(msg);
 2064              		.loc 1 510 5 is_stmt 1 view .LVU620
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 68


 510:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       API_MSG_VAR_FREE_ACCEPT(msg);
 2065              		.loc 1 510 9 is_stmt 0 view .LVU621
 2066 0094 E269     		ldr	r2, [r4, #28]
 2067 0096 6946     		mov	r1, sp
 2068 0098 3846     		mov	r0, r7
 2069 009a FFF7FEFF 		bl	sys_arch_mbox_fetch
 2070              	.LVL202:
 510:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       API_MSG_VAR_FREE_ACCEPT(msg);
 2071              		.loc 1 510 8 discriminator 1 view .LVU622
 2072 009e B0F1FF3F 		cmp	r0, #-1
 2073 00a2 E7D1     		bne	.L155
 513:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     }
 2074              		.loc 1 513 14 view .LVU623
 2075 00a4 6FF00205 		mvn	r5, #2
 2076 00a8 B8E7     		b	.L152
 2077              	.L168:
 535:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     return err;
 2078              		.loc 1 535 33 is_stmt 1 view .LVU624
 536:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   }
 2079              		.loc 1 536 5 view .LVU625
 536:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   }
 2080              		.loc 1 536 12 is_stmt 0 view .LVU626
 2081 00aa 9DF90750 		ldrsb	r5, [sp, #7]
 2082 00ae B5E7     		b	.L152
 2083              	.L158:
 496:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   }
 2084              		.loc 1 496 12 view .LVU627
 2085 00b0 6FF00E05 		mvn	r5, #14
 2086 00b4 B2E7     		b	.L152
 2087              	.L159:
 2088 00b6 6FF00E05 		mvn	r5, #14
 2089 00ba AFE7     		b	.L152
 2090              	.L167:
 506:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     }
 2091              		.loc 1 506 14 view .LVU628
 2092 00bc 6FF00605 		mvn	r5, #6
 2093 00c0 ACE7     		b	.L152
 2094              	.L162:
 541:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   }
 2095              		.loc 1 541 12 view .LVU629
 2096 00c2 6FF00E05 		mvn	r5, #14
 2097 00c6 A9E7     		b	.L152
 2098              	.L170:
 2099              		.align	2
 2100              	.L169:
 2101 00c8 00000000 		.word	.LC0
 2102 00cc 00000000 		.word	.LC20
 2103 00d0 54000000 		.word	.LC2
 2104 00d4 20000000 		.word	.LC21
 2105              		.cfi_endproc
 2106              	.LFE184:
 2108              		.section	.rodata.netconn_recv_data.str1.4,"aMS",%progbits,1
 2109              		.align	2
 2110              	.LC22:
 2111 0000 6E657463 		.ascii	"netconn_recv: invalid pointer\000"
 2111      6F6E6E5F 
 2111      72656376 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 69


 2111      3A20696E 
 2111      76616C69 
 2112 001e 0000     		.align	2
 2113              	.LC23:
 2114 0020 6E657463 		.ascii	"netconn_recv: invalid conn\000"
 2114      6F6E6E5F 
 2114      72656376 
 2114      3A20696E 
 2114      76616C69 
 2115 003b 00       		.align	2
 2116              	.LC24:
 2117 003c 62756620 		.ascii	"buf != NULL\000"
 2117      213D204E 
 2117      554C4C00 
 2118              		.section	.text.netconn_recv_data,"ax",%progbits
 2119              		.align	1
 2120              		.syntax unified
 2121              		.thumb
 2122              		.thumb_func
 2124              	netconn_recv_data:
 2125              	.LVL203:
 2126              	.LFB185:
 580:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   void *buf = NULL;
 2127              		.loc 1 580 1 is_stmt 1 view -0
 2128              		.cfi_startproc
 2129              		@ args = 0, pretend = 0, frame = 8
 2130              		@ frame_needed = 0, uses_anonymous_args = 0
 580:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   void *buf = NULL;
 2131              		.loc 1 580 1 is_stmt 0 view .LVU631
 2132 0000 F0B5     		push	{r4, r5, r6, r7, lr}
 2133              	.LCFI62:
 2134              		.cfi_def_cfa_offset 20
 2135              		.cfi_offset 4, -20
 2136              		.cfi_offset 5, -16
 2137              		.cfi_offset 6, -12
 2138              		.cfi_offset 7, -8
 2139              		.cfi_offset 14, -4
 2140 0002 83B0     		sub	sp, sp, #12
 2141              	.LCFI63:
 2142              		.cfi_def_cfa_offset 32
 581:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   u16_t len;
 2143              		.loc 1 581 3 is_stmt 1 view .LVU632
 581:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   u16_t len;
 2144              		.loc 1 581 9 is_stmt 0 view .LVU633
 2145 0004 0023     		movs	r3, #0
 2146 0006 0193     		str	r3, [sp, #4]
 582:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 2147              		.loc 1 582 3 is_stmt 1 view .LVU634
 584:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   *new_buf = NULL;
 2148              		.loc 1 584 3 view .LVU635
 584:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   *new_buf = NULL;
 2149              		.loc 1 584 3 view .LVU636
 2150 0008 0029     		cmp	r1, #0
 2151 000a 38D0     		beq	.L190
 2152 000c 0446     		mov	r4, r0
 2153 000e 1646     		mov	r6, r2
 2154 0010 0F46     		mov	r7, r1
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 70


 584:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   *new_buf = NULL;
 2155              		.loc 1 584 3 discriminator 2 view .LVU637
 584:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   *new_buf = NULL;
 2156              		.loc 1 584 3 discriminator 2 view .LVU638
 585:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_recv: invalid conn",    (conn != NULL),    return ERR_ARG;);
 2157              		.loc 1 585 3 view .LVU639
 585:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_recv: invalid conn",    (conn != NULL),    return ERR_ARG;);
 2158              		.loc 1 585 12 is_stmt 0 view .LVU640
 2159 0012 0023     		movs	r3, #0
 2160 0014 0B60     		str	r3, [r1]
 586:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 2161              		.loc 1 586 3 is_stmt 1 view .LVU641
 586:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 2162              		.loc 1 586 3 view .LVU642
 2163 0016 0028     		cmp	r0, #0
 2164 0018 3BD0     		beq	.L191
 586:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 2165              		.loc 1 586 3 discriminator 2 view .LVU643
 586:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 2166              		.loc 1 586 3 discriminator 2 view .LVU644
 588:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     err_t err = netconn_err(conn);
 2167              		.loc 1 588 3 view .LVU645
 588:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     err_t err = netconn_err(conn);
 2168              		.loc 1 588 8 is_stmt 0 view .LVU646
 2169 001a 00F11005 		add	r5, r0, #16
 2170 001e 2846     		mov	r0, r5
 2171              	.LVL204:
 588:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     err_t err = netconn_err(conn);
 2172              		.loc 1 588 8 view .LVU647
 2173 0020 FFF7FEFF 		bl	sys_mbox_valid
 2174              	.LVL205:
 588:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     err_t err = netconn_err(conn);
 2175              		.loc 1 588 6 discriminator 1 view .LVU648
 2176 0024 0028     		cmp	r0, #0
 2177 0026 3ED0     		beq	.L192
 597:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   if (netconn_is_nonblocking(conn) || (apiflags & NETCONN_DONTBLOCK) ||
 2178              		.loc 1 597 33 is_stmt 1 view .LVU649
 598:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       (conn->flags & NETCONN_FLAG_MBOXCLOSED) || (conn->pending_err != ERR_OK)) {
 2179              		.loc 1 598 3 view .LVU650
 598:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       (conn->flags & NETCONN_FLAG_MBOXCLOSED) || (conn->pending_err != ERR_OK)) {
 2180              		.loc 1 598 7 is_stmt 0 view .LVU651
 2181 0028 94F82030 		ldrb	r3, [r4, #32]	@ zero_extendqisi2
 598:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       (conn->flags & NETCONN_FLAG_MBOXCLOSED) || (conn->pending_err != ERR_OK)) {
 2182              		.loc 1 598 6 view .LVU652
 2183 002c 13F0020F 		tst	r3, #2
 2184 0030 09D1     		bne	.L176
 598:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       (conn->flags & NETCONN_FLAG_MBOXCLOSED) || (conn->pending_err != ERR_OK)) {
 2185              		.loc 1 598 36 discriminator 1 view .LVU653
 2186 0032 16F0040F 		tst	r6, #4
 2187 0036 06D1     		bne	.L176
 598:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       (conn->flags & NETCONN_FLAG_MBOXCLOSED) || (conn->pending_err != ERR_OK)) {
 2188              		.loc 1 598 70 discriminator 2 view .LVU654
 2189 0038 13F0010F 		tst	r3, #1
 2190 003c 03D1     		bne	.L176
 599:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     if (sys_arch_mbox_tryfetch(&conn->recvmbox, &buf) == SYS_ARCH_TIMEOUT) {
 2191              		.loc 1 599 55 view .LVU655
 2192 003e 94F90830 		ldrsb	r3, [r4, #8]
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 71


 599:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     if (sys_arch_mbox_tryfetch(&conn->recvmbox, &buf) == SYS_ARCH_TIMEOUT) {
 2193              		.loc 1 599 47 view .LVU656
 2194 0042 002B     		cmp	r3, #0
 2195 0044 44D0     		beq	.L177
 2196              	.L176:
 600:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       err_t err;
 2197              		.loc 1 600 5 is_stmt 1 view .LVU657
 600:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       err_t err;
 2198              		.loc 1 600 9 is_stmt 0 view .LVU658
 2199 0046 01A9     		add	r1, sp, #4
 2200 0048 2846     		mov	r0, r5
 2201 004a FFF7FEFF 		bl	sys_arch_mbox_tryfetch
 2202              	.LVL206:
 600:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       err_t err;
 2203              		.loc 1 600 8 discriminator 1 view .LVU659
 2204 004e B0F1FF3F 		cmp	r0, #-1
 2205 0052 30D0     		beq	.L193
 2206              	.L178:
 623:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #if LWIP_NETCONN_FULLDUPLEX
 2207              		.loc 1 623 33 is_stmt 1 view .LVU660
 636:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #endif /* (LWIP_UDP || LWIP_RAW) */
 2208              		.loc 1 636 3 view .LVU661
 636:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #endif /* (LWIP_UDP || LWIP_RAW) */
 2209              		.loc 1 636 7 is_stmt 0 view .LVU662
 2210 0054 2378     		ldrb	r3, [r4]	@ zero_extendqisi2
 636:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #endif /* (LWIP_UDP || LWIP_RAW) */
 2211              		.loc 1 636 6 view .LVU663
 2212 0056 03F0F003 		and	r3, r3, #240
 2213 005a 102B     		cmp	r3, #16
 2214 005c 43D0     		beq	.L194
 657:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     len = netbuf_len((struct netbuf *)buf);
 2215              		.loc 1 657 5 is_stmt 1 view .LVU664
 657:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     len = netbuf_len((struct netbuf *)buf);
 2216              		.loc 1 657 5 view .LVU665
 2217 005e 019B     		ldr	r3, [sp, #4]
 2218 0060 002B     		cmp	r3, #0
 2219 0062 50D0     		beq	.L195
 2220              	.L184:
 657:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     len = netbuf_len((struct netbuf *)buf);
 2221              		.loc 1 657 5 discriminator 3 view .LVU666
 657:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     len = netbuf_len((struct netbuf *)buf);
 2222              		.loc 1 657 5 discriminator 3 view .LVU667
 658:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   }
 2223              		.loc 1 658 5 view .LVU668
 658:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   }
 2224              		.loc 1 658 11 is_stmt 0 view .LVU669
 2225 0064 019B     		ldr	r3, [sp, #4]
 2226 0066 1B68     		ldr	r3, [r3]
 658:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   }
 2227              		.loc 1 658 9 view .LVU670
 2228 0068 1A89     		ldrh	r2, [r3, #8]
 2229              	.LVL207:
 2230              	.L183:
 666:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 2231              		.loc 1 666 3 is_stmt 1 view .LVU671
 2232 006a A36A     		ldr	r3, [r4, #40]
 2233 006c 13B1     		cbz	r3, .L185
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 72


 666:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 2234              		.loc 1 666 3 discriminator 1 view .LVU672
 2235 006e 0121     		movs	r1, #1
 2236 0070 2046     		mov	r0, r4
 2237 0072 9847     		blx	r3
 2238              	.LVL208:
 2239              	.L185:
 666:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 2240              		.loc 1 666 45 discriminator 3 view .LVU673
 668:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 2241              		.loc 1 668 91 view .LVU674
 670:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   /* don't set conn->last_err: it's only ERR_OK, anyway */
 2242              		.loc 1 670 3 view .LVU675
 670:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   /* don't set conn->last_err: it's only ERR_OK, anyway */
 2243              		.loc 1 670 12 is_stmt 0 view .LVU676
 2244 0074 019B     		ldr	r3, [sp, #4]
 2245 0076 3B60     		str	r3, [r7]
 672:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** }
 2246              		.loc 1 672 3 is_stmt 1 view .LVU677
 672:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** }
 2247              		.loc 1 672 10 is_stmt 0 view .LVU678
 2248 0078 0020     		movs	r0, #0
 2249              	.LVL209:
 2250              	.L173:
 673:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 2251              		.loc 1 673 1 view .LVU679
 2252 007a 03B0     		add	sp, sp, #12
 2253              	.LCFI64:
 2254              		.cfi_remember_state
 2255              		.cfi_def_cfa_offset 20
 2256              		@ sp needed
 2257 007c F0BD     		pop	{r4, r5, r6, r7, pc}
 2258              	.LVL210:
 2259              	.L190:
 2260              	.LCFI65:
 2261              		.cfi_restore_state
 584:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   *new_buf = NULL;
 2262              		.loc 1 584 3 is_stmt 1 discriminator 1 view .LVU680
 584:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   *new_buf = NULL;
 2263              		.loc 1 584 3 discriminator 1 view .LVU681
 2264 007e 274B     		ldr	r3, .L196
 2265 0080 4FF41272 		mov	r2, #584
 2266              	.LVL211:
 584:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   *new_buf = NULL;
 2267              		.loc 1 584 3 is_stmt 0 discriminator 1 view .LVU682
 2268 0084 2649     		ldr	r1, .L196+4
 2269              	.LVL212:
 584:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   *new_buf = NULL;
 2270              		.loc 1 584 3 discriminator 1 view .LVU683
 2271 0086 2748     		ldr	r0, .L196+8
 2272              	.LVL213:
 584:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   *new_buf = NULL;
 2273              		.loc 1 584 3 discriminator 1 view .LVU684
 2274 0088 FFF7FEFF 		bl	printf
 2275              	.LVL214:
 584:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   *new_buf = NULL;
 2276              		.loc 1 584 3 is_stmt 1 discriminator 1 view .LVU685
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 73


 584:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   *new_buf = NULL;
 2277              		.loc 1 584 3 discriminator 1 view .LVU686
 2278 008c 6FF00F00 		mvn	r0, #15
 584:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   *new_buf = NULL;
 2279              		.loc 1 584 3 is_stmt 0 view .LVU687
 2280 0090 F3E7     		b	.L173
 2281              	.LVL215:
 2282              	.L191:
 586:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 2283              		.loc 1 586 3 is_stmt 1 discriminator 1 view .LVU688
 586:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 2284              		.loc 1 586 3 discriminator 1 view .LVU689
 2285 0092 224B     		ldr	r3, .L196
 2286 0094 40F24A22 		movw	r2, #586
 2287              	.LVL216:
 586:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 2288              		.loc 1 586 3 is_stmt 0 discriminator 1 view .LVU690
 2289 0098 2349     		ldr	r1, .L196+12
 2290              	.LVL217:
 586:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 2291              		.loc 1 586 3 discriminator 1 view .LVU691
 2292 009a 2248     		ldr	r0, .L196+8
 2293              	.LVL218:
 586:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 2294              		.loc 1 586 3 discriminator 1 view .LVU692
 2295 009c FFF7FEFF 		bl	printf
 2296              	.LVL219:
 586:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 2297              		.loc 1 586 3 is_stmt 1 discriminator 1 view .LVU693
 586:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 2298              		.loc 1 586 3 discriminator 1 view .LVU694
 2299 00a0 6FF00F00 		mvn	r0, #15
 586:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 2300              		.loc 1 586 3 is_stmt 0 view .LVU695
 2301 00a4 E9E7     		b	.L173
 2302              	.L192:
 2303              	.LBB6:
 589:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     if (err != ERR_OK) {
 2304              		.loc 1 589 5 is_stmt 1 view .LVU696
 589:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     if (err != ERR_OK) {
 2305              		.loc 1 589 17 is_stmt 0 view .LVU697
 2306 00a6 2046     		mov	r0, r4
 2307 00a8 FFF7FEFF 		bl	netconn_err
 2308              	.LVL220:
 590:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       /* return pending error */
 2309              		.loc 1 590 5 is_stmt 1 view .LVU698
 590:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       /* return pending error */
 2310              		.loc 1 590 8 is_stmt 0 view .LVU699
 2311 00ac 0028     		cmp	r0, #0
 2312 00ae E4D1     		bne	.L173
 594:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   }
 2313              		.loc 1 594 12 view .LVU700
 2314 00b0 6FF00A00 		mvn	r0, #10
 2315              	.LVL221:
 594:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   }
 2316              		.loc 1 594 12 view .LVU701
 2317 00b4 E1E7     		b	.L173
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 74


 2318              	.LVL222:
 2319              	.L193:
 594:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   }
 2320              		.loc 1 594 12 view .LVU702
 2321              	.LBE6:
 2322              	.LBB7:
 601:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       NETCONN_MBOX_WAITING_DEC(conn);
 2323              		.loc 1 601 7 is_stmt 1 view .LVU703
 602:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       err = netconn_err(conn);
 2324              		.loc 1 602 37 view .LVU704
 603:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       if (err != ERR_OK) {
 2325              		.loc 1 603 7 view .LVU705
 603:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       if (err != ERR_OK) {
 2326              		.loc 1 603 13 is_stmt 0 view .LVU706
 2327 00b6 2046     		mov	r0, r4
 2328 00b8 FFF7FEFF 		bl	netconn_err
 2329              	.LVL223:
 604:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****         /* return pending error */
 2330              		.loc 1 604 7 is_stmt 1 view .LVU707
 604:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****         /* return pending error */
 2331              		.loc 1 604 10 is_stmt 0 view .LVU708
 2332 00bc 0028     		cmp	r0, #0
 2333 00be DCD1     		bne	.L173
 608:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****         return ERR_CONN;
 2334              		.loc 1 608 7 is_stmt 1 view .LVU709
 608:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****         return ERR_CONN;
 2335              		.loc 1 608 15 is_stmt 0 view .LVU710
 2336 00c0 94F82030 		ldrb	r3, [r4, #32]	@ zero_extendqisi2
 608:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****         return ERR_CONN;
 2337              		.loc 1 608 10 view .LVU711
 2338 00c4 13F0010F 		tst	r3, #1
 2339 00c8 25D0     		beq	.L187
 609:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       }
 2340              		.loc 1 609 16 view .LVU712
 2341 00ca 6FF00A00 		mvn	r0, #10
 2342              	.LVL224:
 609:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       }
 2343              		.loc 1 609 16 view .LVU713
 2344 00ce D4E7     		b	.L173
 2345              	.LVL225:
 2346              	.L177:
 609:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       }
 2347              		.loc 1 609 16 view .LVU714
 2348              	.LBE7:
 615:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       NETCONN_MBOX_WAITING_DEC(conn);
 2349              		.loc 1 615 5 is_stmt 1 view .LVU715
 615:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       NETCONN_MBOX_WAITING_DEC(conn);
 2350              		.loc 1 615 9 is_stmt 0 view .LVU716
 2351 00d0 E269     		ldr	r2, [r4, #28]
 2352 00d2 01A9     		add	r1, sp, #4
 2353 00d4 2846     		mov	r0, r5
 2354 00d6 FFF7FEFF 		bl	sys_arch_mbox_fetch
 2355              	.LVL226:
 615:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       NETCONN_MBOX_WAITING_DEC(conn);
 2356              		.loc 1 615 8 discriminator 1 view .LVU717
 2357 00da B0F1FF3F 		cmp	r0, #-1
 2358 00de B9D1     		bne	.L178
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 75


 617:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     }
 2359              		.loc 1 617 14 view .LVU718
 2360 00e0 6FF00200 		mvn	r0, #2
 2361 00e4 C9E7     		b	.L173
 2362              	.L194:
 2363              	.LBB8:
 639:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     /* Check if this is an error message or a pbuf */
 2364              		.loc 1 639 5 is_stmt 1 view .LVU719
 641:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       /* new_buf has been zeroed above already */
 2365              		.loc 1 641 5 view .LVU720
 641:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       /* new_buf has been zeroed above already */
 2366              		.loc 1 641 9 is_stmt 0 view .LVU721
 2367 00e6 0DF10301 		add	r1, sp, #3
 2368 00ea 0198     		ldr	r0, [sp, #4]
 2369 00ec FFF7FEFF 		bl	lwip_netconn_is_err_msg
 2370              	.LVL227:
 641:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       /* new_buf has been zeroed above already */
 2371              		.loc 1 641 8 discriminator 1 view .LVU722
 2372 00f0 30B1     		cbz	r0, .L180
 643:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****         /* connection closed translates to ERR_OK with *new_buf == NULL */
 2373              		.loc 1 643 7 is_stmt 1 view .LVU723
 643:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****         /* connection closed translates to ERR_OK with *new_buf == NULL */
 2374              		.loc 1 643 15 is_stmt 0 view .LVU724
 2375 00f2 9DF90300 		ldrsb	r0, [sp, #3]
 643:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****         /* connection closed translates to ERR_OK with *new_buf == NULL */
 2376              		.loc 1 643 10 view .LVU725
 2377 00f6 10F10F0F 		cmn	r0, #15
 2378 00fa BED1     		bne	.L173
 645:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       }
 2379              		.loc 1 645 16 view .LVU726
 2380 00fc 0020     		movs	r0, #0
 2381 00fe BCE7     		b	.L173
 2382              	.L180:
 649:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   }
 2383              		.loc 1 649 5 is_stmt 1 view .LVU727
 649:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   }
 2384              		.loc 1 649 12 is_stmt 0 view .LVU728
 2385 0100 019B     		ldr	r3, [sp, #4]
 649:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   }
 2386              		.loc 1 649 9 view .LVU729
 2387 0102 1A89     		ldrh	r2, [r3, #8]
 2388              	.LVL228:
 649:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   }
 2389              		.loc 1 649 9 view .LVU730
 2390              	.LBE8:
 2391 0104 B1E7     		b	.L183
 2392              	.LVL229:
 2393              	.L195:
 657:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     len = netbuf_len((struct netbuf *)buf);
 2394              		.loc 1 657 5 is_stmt 1 discriminator 1 view .LVU731
 657:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     len = netbuf_len((struct netbuf *)buf);
 2395              		.loc 1 657 5 discriminator 1 view .LVU732
 2396 0106 054B     		ldr	r3, .L196
 2397 0108 40F29122 		movw	r2, #657
 2398 010c 0749     		ldr	r1, .L196+16
 2399 010e 0548     		ldr	r0, .L196+8
 2400 0110 FFF7FEFF 		bl	printf
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 76


 2401              	.LVL230:
 2402 0114 A6E7     		b	.L184
 2403              	.LVL231:
 2404              	.L187:
 2405              	.LBB9:
 611:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     }
 2406              		.loc 1 611 14 is_stmt 0 view .LVU733
 2407 0116 6FF00600 		mvn	r0, #6
 2408              	.LVL232:
 611:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     }
 2409              		.loc 1 611 14 view .LVU734
 2410 011a AEE7     		b	.L173
 2411              	.L197:
 2412              		.align	2
 2413              	.L196:
 2414 011c 00000000 		.word	.LC0
 2415 0120 00000000 		.word	.LC22
 2416 0124 54000000 		.word	.LC2
 2417 0128 20000000 		.word	.LC23
 2418 012c 3C000000 		.word	.LC24
 2419              	.LBE9:
 2420              		.cfi_endproc
 2421              	.LFE185:
 2423              		.section	.rodata.netconn_recv_udp_raw_netbuf.str1.4,"aMS",%progbits,1
 2424              		.align	2
 2425              	.LC25:
 2426 0000 6E657463 		.ascii	"netconn_recv_udp_raw_netbuf: invalid conn\000"
 2426      6F6E6E5F 
 2426      72656376 
 2426      5F756470 
 2426      5F726177 
 2427              		.section	.text.netconn_recv_udp_raw_netbuf,"ax",%progbits
 2428              		.align	1
 2429              		.global	netconn_recv_udp_raw_netbuf
 2430              		.syntax unified
 2431              		.thumb
 2432              		.thumb_func
 2434              	netconn_recv_udp_raw_netbuf:
 2435              	.LVL233:
 2436              	.LFB191:
 823:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_recv_udp_raw_netbuf: invalid conn", (conn != NULL) &&
 2437              		.loc 1 823 1 is_stmt 1 view -0
 2438              		.cfi_startproc
 2439              		@ args = 0, pretend = 0, frame = 0
 2440              		@ frame_needed = 0, uses_anonymous_args = 0
 823:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_recv_udp_raw_netbuf: invalid conn", (conn != NULL) &&
 2441              		.loc 1 823 1 is_stmt 0 view .LVU736
 2442 0000 08B5     		push	{r3, lr}
 2443              	.LCFI66:
 2444              		.cfi_def_cfa_offset 8
 2445              		.cfi_offset 3, -8
 2446              		.cfi_offset 14, -4
 824:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****              NETCONNTYPE_GROUP(netconn_type(conn)) != NETCONN_TCP, return ERR_ARG;);
 2447              		.loc 1 824 3 is_stmt 1 view .LVU737
 824:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****              NETCONNTYPE_GROUP(netconn_type(conn)) != NETCONN_TCP, return ERR_ARG;);
 2448              		.loc 1 824 3 view .LVU738
 2449 0002 40B1     		cbz	r0, .L199
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 77


 824:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****              NETCONNTYPE_GROUP(netconn_type(conn)) != NETCONN_TCP, return ERR_ARG;);
 2450              		.loc 1 824 3 is_stmt 0 discriminator 2 view .LVU739
 2451 0004 0378     		ldrb	r3, [r0]	@ zero_extendqisi2
 2452 0006 03F0F003 		and	r3, r3, #240
 2453 000a 102B     		cmp	r3, #16
 2454 000c 03D0     		beq	.L199
 824:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****              NETCONNTYPE_GROUP(netconn_type(conn)) != NETCONN_TCP, return ERR_ARG;);
 2455              		.loc 1 824 3 is_stmt 1 discriminator 4 view .LVU740
 824:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****              NETCONNTYPE_GROUP(netconn_type(conn)) != NETCONN_TCP, return ERR_ARG;);
 2456              		.loc 1 824 3 discriminator 4 view .LVU741
 827:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** }
 2457              		.loc 1 827 3 view .LVU742
 827:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** }
 2458              		.loc 1 827 10 is_stmt 0 view .LVU743
 2459 000e 0022     		movs	r2, #0
 2460 0010 FFF7FEFF 		bl	netconn_recv_data
 2461              	.LVL234:
 2462              	.L201:
 828:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 2463              		.loc 1 828 1 view .LVU744
 2464 0014 08BD     		pop	{r3, pc}
 2465              	.LVL235:
 2466              	.L199:
 824:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****              NETCONNTYPE_GROUP(netconn_type(conn)) != NETCONN_TCP, return ERR_ARG;);
 2467              		.loc 1 824 3 is_stmt 1 discriminator 3 view .LVU745
 824:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****              NETCONNTYPE_GROUP(netconn_type(conn)) != NETCONN_TCP, return ERR_ARG;);
 2468              		.loc 1 824 3 discriminator 3 view .LVU746
 2469 0016 054B     		ldr	r3, .L203
 2470 0018 4FF44E72 		mov	r2, #824
 2471 001c 0449     		ldr	r1, .L203+4
 2472              	.LVL236:
 824:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****              NETCONNTYPE_GROUP(netconn_type(conn)) != NETCONN_TCP, return ERR_ARG;);
 2473              		.loc 1 824 3 is_stmt 0 discriminator 3 view .LVU747
 2474 001e 0548     		ldr	r0, .L203+8
 2475              	.LVL237:
 824:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****              NETCONNTYPE_GROUP(netconn_type(conn)) != NETCONN_TCP, return ERR_ARG;);
 2476              		.loc 1 824 3 discriminator 3 view .LVU748
 2477 0020 FFF7FEFF 		bl	printf
 2478              	.LVL238:
 824:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****              NETCONNTYPE_GROUP(netconn_type(conn)) != NETCONN_TCP, return ERR_ARG;);
 2479              		.loc 1 824 3 is_stmt 1 discriminator 1 view .LVU749
 824:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****              NETCONNTYPE_GROUP(netconn_type(conn)) != NETCONN_TCP, return ERR_ARG;);
 2480              		.loc 1 824 3 discriminator 1 view .LVU750
 2481 0024 6FF00F00 		mvn	r0, #15
 824:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****              NETCONNTYPE_GROUP(netconn_type(conn)) != NETCONN_TCP, return ERR_ARG;);
 2482              		.loc 1 824 3 is_stmt 0 view .LVU751
 2483 0028 F4E7     		b	.L201
 2484              	.L204:
 2485 002a 00BF     		.align	2
 2486              	.L203:
 2487 002c 00000000 		.word	.LC0
 2488 0030 00000000 		.word	.LC25
 2489 0034 54000000 		.word	.LC2
 2490              		.cfi_endproc
 2491              	.LFE191:
 2493              		.section	.text.netconn_recv_udp_raw_netbuf_flags,"ax",%progbits
 2494              		.align	1
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 78


 2495              		.global	netconn_recv_udp_raw_netbuf_flags
 2496              		.syntax unified
 2497              		.thumb
 2498              		.thumb_func
 2500              	netconn_recv_udp_raw_netbuf_flags:
 2501              	.LVL239:
 2502              	.LFB192:
 843:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_recv_udp_raw_netbuf: invalid conn", (conn != NULL) &&
 2503              		.loc 1 843 1 is_stmt 1 view -0
 2504              		.cfi_startproc
 2505              		@ args = 0, pretend = 0, frame = 0
 2506              		@ frame_needed = 0, uses_anonymous_args = 0
 843:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_recv_udp_raw_netbuf: invalid conn", (conn != NULL) &&
 2507              		.loc 1 843 1 is_stmt 0 view .LVU753
 2508 0000 08B5     		push	{r3, lr}
 2509              	.LCFI67:
 2510              		.cfi_def_cfa_offset 8
 2511              		.cfi_offset 3, -8
 2512              		.cfi_offset 14, -4
 844:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****              NETCONNTYPE_GROUP(netconn_type(conn)) != NETCONN_TCP, return ERR_ARG;);
 2513              		.loc 1 844 3 is_stmt 1 view .LVU754
 844:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****              NETCONNTYPE_GROUP(netconn_type(conn)) != NETCONN_TCP, return ERR_ARG;);
 2514              		.loc 1 844 3 view .LVU755
 2515 0002 38B1     		cbz	r0, .L206
 844:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****              NETCONNTYPE_GROUP(netconn_type(conn)) != NETCONN_TCP, return ERR_ARG;);
 2516              		.loc 1 844 3 is_stmt 0 discriminator 2 view .LVU756
 2517 0004 0378     		ldrb	r3, [r0]	@ zero_extendqisi2
 2518 0006 03F0F003 		and	r3, r3, #240
 2519 000a 102B     		cmp	r3, #16
 2520 000c 02D0     		beq	.L206
 844:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****              NETCONNTYPE_GROUP(netconn_type(conn)) != NETCONN_TCP, return ERR_ARG;);
 2521              		.loc 1 844 3 is_stmt 1 discriminator 4 view .LVU757
 844:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****              NETCONNTYPE_GROUP(netconn_type(conn)) != NETCONN_TCP, return ERR_ARG;);
 2522              		.loc 1 844 3 discriminator 4 view .LVU758
 847:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** }
 2523              		.loc 1 847 3 view .LVU759
 847:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** }
 2524              		.loc 1 847 10 is_stmt 0 view .LVU760
 2525 000e FFF7FEFF 		bl	netconn_recv_data
 2526              	.LVL240:
 2527              	.L208:
 848:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 2528              		.loc 1 848 1 view .LVU761
 2529 0012 08BD     		pop	{r3, pc}
 2530              	.LVL241:
 2531              	.L206:
 844:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****              NETCONNTYPE_GROUP(netconn_type(conn)) != NETCONN_TCP, return ERR_ARG;);
 2532              		.loc 1 844 3 is_stmt 1 discriminator 3 view .LVU762
 844:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****              NETCONNTYPE_GROUP(netconn_type(conn)) != NETCONN_TCP, return ERR_ARG;);
 2533              		.loc 1 844 3 discriminator 3 view .LVU763
 2534 0014 044B     		ldr	r3, .L210
 2535 0016 4FF45372 		mov	r2, #844
 2536              	.LVL242:
 844:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****              NETCONNTYPE_GROUP(netconn_type(conn)) != NETCONN_TCP, return ERR_ARG;);
 2537              		.loc 1 844 3 is_stmt 0 discriminator 3 view .LVU764
 2538 001a 0449     		ldr	r1, .L210+4
 2539              	.LVL243:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 79


 844:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****              NETCONNTYPE_GROUP(netconn_type(conn)) != NETCONN_TCP, return ERR_ARG;);
 2540              		.loc 1 844 3 discriminator 3 view .LVU765
 2541 001c 0448     		ldr	r0, .L210+8
 2542              	.LVL244:
 844:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****              NETCONNTYPE_GROUP(netconn_type(conn)) != NETCONN_TCP, return ERR_ARG;);
 2543              		.loc 1 844 3 discriminator 3 view .LVU766
 2544 001e FFF7FEFF 		bl	printf
 2545              	.LVL245:
 844:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****              NETCONNTYPE_GROUP(netconn_type(conn)) != NETCONN_TCP, return ERR_ARG;);
 2546              		.loc 1 844 3 is_stmt 1 discriminator 1 view .LVU767
 844:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****              NETCONNTYPE_GROUP(netconn_type(conn)) != NETCONN_TCP, return ERR_ARG;);
 2547              		.loc 1 844 3 discriminator 1 view .LVU768
 2548 0022 6FF00F00 		mvn	r0, #15
 844:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****              NETCONNTYPE_GROUP(netconn_type(conn)) != NETCONN_TCP, return ERR_ARG;);
 2549              		.loc 1 844 3 is_stmt 0 view .LVU769
 2550 0026 F4E7     		b	.L208
 2551              	.L211:
 2552              		.align	2
 2553              	.L210:
 2554 0028 00000000 		.word	.LC0
 2555 002c 00000000 		.word	.LC25
 2556 0030 54000000 		.word	.LC2
 2557              		.cfi_endproc
 2558              	.LFE192:
 2560              		.section	.text.netconn_recv_data_tcp,"ax",%progbits
 2561              		.align	1
 2562              		.syntax unified
 2563              		.thumb
 2564              		.thumb_func
 2566              	netconn_recv_data_tcp:
 2567              	.LVL246:
 2568              	.LFB188:
 704:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   err_t err;
 2569              		.loc 1 704 1 is_stmt 1 view -0
 2570              		.cfi_startproc
 2571              		@ args = 0, pretend = 0, frame = 32
 2572              		@ frame_needed = 0, uses_anonymous_args = 0
 704:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   err_t err;
 2573              		.loc 1 704 1 is_stmt 0 view .LVU771
 2574 0000 2DE9F041 		push	{r4, r5, r6, r7, r8, lr}
 2575              	.LCFI68:
 2576              		.cfi_def_cfa_offset 24
 2577              		.cfi_offset 4, -24
 2578              		.cfi_offset 5, -20
 2579              		.cfi_offset 6, -16
 2580              		.cfi_offset 7, -12
 2581              		.cfi_offset 8, -8
 2582              		.cfi_offset 14, -4
 2583 0004 88B0     		sub	sp, sp, #32
 2584              	.LCFI69:
 2585              		.cfi_def_cfa_offset 56
 2586 0006 0446     		mov	r4, r0
 2587 0008 0F46     		mov	r7, r1
 2588 000a 1546     		mov	r5, r2
 705:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   struct pbuf *buf;
 2589              		.loc 1 705 3 is_stmt 1 view .LVU772
 706:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   API_MSG_VAR_DECLARE(msg);
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 80


 2590              		.loc 1 706 3 view .LVU773
 707:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #if LWIP_MPU_COMPATIBLE
 2591              		.loc 1 707 3 view .LVU774
 712:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     /* This only happens when calling this function more than once *after* receiving FIN */
 2592              		.loc 1 712 3 view .LVU775
 712:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     /* This only happens when calling this function more than once *after* receiving FIN */
 2593              		.loc 1 712 8 is_stmt 0 view .LVU776
 2594 000c 1030     		adds	r0, r0, #16
 2595              	.LVL247:
 712:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     /* This only happens when calling this function more than once *after* receiving FIN */
 2596              		.loc 1 712 8 view .LVU777
 2597 000e FFF7FEFF 		bl	sys_mbox_valid
 2598              	.LVL248:
 712:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     /* This only happens when calling this function more than once *after* receiving FIN */
 2599              		.loc 1 712 6 discriminator 1 view .LVU778
 2600 0012 0028     		cmp	r0, #0
 2601 0014 43D0     		beq	.L220
 716:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     netconn_clear_flags(conn, NETCONN_FIN_RX_PENDING);
 2602              		.loc 1 716 3 is_stmt 1 view .LVU779
 716:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     netconn_clear_flags(conn, NETCONN_FIN_RX_PENDING);
 2603              		.loc 1 716 7 is_stmt 0 view .LVU780
 2604 0016 94F82030 		ldrb	r3, [r4, #32]	@ zero_extendqisi2
 2605 001a 94F92020 		ldrsb	r2, [r4, #32]
 716:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     netconn_clear_flags(conn, NETCONN_FIN_RX_PENDING);
 2606              		.loc 1 716 6 view .LVU781
 2607 001e 002A     		cmp	r2, #0
 2608 0020 1FDB     		blt	.L224
 721:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     /* need to allocate API message here so empty message pool does not result in event loss
 2609              		.loc 1 721 3 is_stmt 1 view .LVU782
 2610 0022 05F00808 		and	r8, r5, #8
 724:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   }
 2611              		.loc 1 724 27 view .LVU783
 727:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   if (err != ERR_OK) {
 2612              		.loc 1 727 3 view .LVU784
 727:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   if (err != ERR_OK) {
 2613              		.loc 1 727 9 is_stmt 0 view .LVU785
 2614 0026 2A46     		mov	r2, r5
 2615 0028 3946     		mov	r1, r7
 2616 002a 2046     		mov	r0, r4
 2617 002c FFF7FEFF 		bl	netconn_recv_data
 2618              	.LVL249:
 728:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     if (!(apiflags & NETCONN_NOAUTORCVD)) {
 2619              		.loc 1 728 3 is_stmt 1 view .LVU786
 728:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     if (!(apiflags & NETCONN_NOAUTORCVD)) {
 2620              		.loc 1 728 6 is_stmt 0 view .LVU787
 2621 0030 0646     		mov	r6, r0
 2622 0032 38BB     		cbnz	r0, .L213
 734:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   if (!(apiflags & NETCONN_NOAUTORCVD)) {
 2623              		.loc 1 734 3 is_stmt 1 view .LVU788
 734:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   if (!(apiflags & NETCONN_NOAUTORCVD)) {
 2624              		.loc 1 734 7 is_stmt 0 view .LVU789
 2625 0034 3F68     		ldr	r7, [r7]
 2626              	.LVL250:
 735:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     /* Let the stack know that we have taken the data. */
 2627              		.loc 1 735 3 is_stmt 1 view .LVU790
 735:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     /* Let the stack know that we have taken the data. */
 2628              		.loc 1 735 6 is_stmt 0 view .LVU791
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 81


 2629 0036 B8F1000F 		cmp	r8, #0
 2630 003a 05D1     		bne	.L216
 2631              	.LBB10:
 737:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     /* don't care for the return value of lwip_netconn_do_recv */
 2632              		.loc 1 737 5 is_stmt 1 view .LVU792
 737:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     /* don't care for the return value of lwip_netconn_do_recv */
 2633              		.loc 1 737 11 is_stmt 0 view .LVU793
 2634 003c 37B3     		cbz	r7, .L221
 737:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     /* don't care for the return value of lwip_netconn_do_recv */
 2635              		.loc 1 737 11 discriminator 1 view .LVU794
 2636 003e 3989     		ldrh	r1, [r7, #8]
 2637              	.L217:
 2638              	.LVL251:
 740:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     API_MSG_VAR_FREE(msg);
 2639              		.loc 1 740 5 is_stmt 1 view .LVU795
 2640 0040 6A46     		mov	r2, sp
 2641 0042 2046     		mov	r0, r4
 2642              	.LVL252:
 740:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     API_MSG_VAR_FREE(msg);
 2643              		.loc 1 740 5 is_stmt 0 view .LVU796
 2644 0044 FFF7FEFF 		bl	netconn_tcp_recvd_msg
 2645              	.LVL253:
 2646              	.L216:
 741:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   }
 2647              		.loc 1 741 26 is_stmt 1 view .LVU797
 2648              	.LBE10:
 745:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     if (apiflags & NETCONN_NOFIN) {
 2649              		.loc 1 745 3 view .LVU798
 745:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     if (apiflags & NETCONN_NOFIN) {
 2650              		.loc 1 745 6 is_stmt 0 view .LVU799
 2651 0048 E7B9     		cbnz	r7, .L213
 746:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       /* received a FIN but the caller cannot handle it right now:
 2652              		.loc 1 746 5 is_stmt 1 view .LVU800
 746:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       /* received a FIN but the caller cannot handle it right now:
 2653              		.loc 1 746 8 is_stmt 0 view .LVU801
 2654 004a 15F0100F 		tst	r5, #16
 2655 004e 0CD0     		beq	.L215
 749:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       return ERR_WOULDBLOCK;
 2656              		.loc 1 749 7 is_stmt 1 view .LVU802
 749:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       return ERR_WOULDBLOCK;
 2657              		.loc 1 749 7 view .LVU803
 2658 0050 94F82030 		ldrb	r3, [r4, #32]	@ zero_extendqisi2
 2659 0054 43F08003 		orr	r3, r3, #128
 2660 0058 84F82030 		strb	r3, [r4, #32]
 749:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       return ERR_WOULDBLOCK;
 2661              		.loc 1 749 7 view .LVU804
 750:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     } else {
 2662              		.loc 1 750 7 view .LVU805
 750:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     } else {
 2663              		.loc 1 750 14 is_stmt 0 view .LVU806
 2664 005c 6FF00606 		mvn	r6, #6
 2665              	.LVL254:
 750:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     } else {
 2666              		.loc 1 750 14 view .LVU807
 2667 0060 10E0     		b	.L213
 2668              	.LVL255:
 2669              	.L224:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 82


 717:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     goto handle_fin;
 2670              		.loc 1 717 5 is_stmt 1 view .LVU808
 717:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     goto handle_fin;
 2671              		.loc 1 717 5 view .LVU809
 2672 0062 03F07F03 		and	r3, r3, #127
 2673 0066 84F82030 		strb	r3, [r4, #32]
 717:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     goto handle_fin;
 2674              		.loc 1 717 5 view .LVU810
 718:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   }
 2675              		.loc 1 718 5 view .LVU811
 2676              	.LVL256:
 2677              	.L215:
 753:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       if (conn->pcb.ip == NULL) {
 2678              		.loc 1 753 7 view .LVU812
 2679 006a A36A     		ldr	r3, [r4, #40]
 2680 006c 1BB1     		cbz	r3, .L218
 753:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       if (conn->pcb.ip == NULL) {
 2681              		.loc 1 753 7 discriminator 1 view .LVU813
 2682 006e 0022     		movs	r2, #0
 2683 0070 0121     		movs	r1, #1
 2684 0072 2046     		mov	r0, r4
 2685 0074 9847     		blx	r3
 2686              	.LVL257:
 2687              	.L218:
 753:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       if (conn->pcb.ip == NULL) {
 2688              		.loc 1 753 47 discriminator 3 view .LVU814
 754:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****         /* race condition: RST during recv */
 2689              		.loc 1 754 7 view .LVU815
 754:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****         /* race condition: RST during recv */
 2690              		.loc 1 754 20 is_stmt 0 view .LVU816
 2691 0076 6368     		ldr	r3, [r4, #4]
 754:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****         /* race condition: RST during recv */
 2692              		.loc 1 754 10 view .LVU817
 2693 0078 53B9     		cbnz	r3, .L219
 756:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****         if (err != ERR_OK) {
 2694              		.loc 1 756 9 is_stmt 1 view .LVU818
 756:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****         if (err != ERR_OK) {
 2695              		.loc 1 756 15 is_stmt 0 view .LVU819
 2696 007a 2046     		mov	r0, r4
 2697 007c FFF7FEFF 		bl	netconn_err
 2698              	.LVL258:
 757:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****           return err;
 2699              		.loc 1 757 9 is_stmt 1 view .LVU820
 757:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****           return err;
 2700              		.loc 1 757 12 is_stmt 0 view .LVU821
 2701 0080 0646     		mov	r6, r0
 2702 0082 78B1     		cbz	r0, .L225
 2703              	.LVL259:
 2704              	.L213:
 769:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 2705              		.loc 1 769 1 view .LVU822
 2706 0084 3046     		mov	r0, r6
 2707 0086 08B0     		add	sp, sp, #32
 2708              	.LCFI70:
 2709              		.cfi_remember_state
 2710              		.cfi_def_cfa_offset 24
 2711              		@ sp needed
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 83


 2712 0088 BDE8F081 		pop	{r4, r5, r6, r7, r8, pc}
 2713              	.LVL260:
 2714              	.L221:
 2715              	.LCFI71:
 2716              		.cfi_restore_state
 2717              	.LBB11:
 737:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     /* don't care for the return value of lwip_netconn_do_recv */
 2718              		.loc 1 737 11 discriminator 2 view .LVU823
 2719 008c 0121     		movs	r1, #1
 2720 008e D7E7     		b	.L217
 2721              	.LVL261:
 2722              	.L219:
 737:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     /* don't care for the return value of lwip_netconn_do_recv */
 2723              		.loc 1 737 11 discriminator 2 view .LVU824
 2724              	.LBE11:
 763:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       /* Don' store ERR_CLSD as conn->err since we are only half-closed */
 2725              		.loc 1 763 7 is_stmt 1 view .LVU825
 2726 0090 0121     		movs	r1, #1
 2727 0092 2046     		mov	r0, r4
 2728 0094 FFF7FEFF 		bl	netconn_close_shutdown
 2729              	.LVL262:
 765:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     }
 2730              		.loc 1 765 7 view .LVU826
 765:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     }
 2731              		.loc 1 765 14 is_stmt 0 view .LVU827
 2732 0098 6FF00E06 		mvn	r6, #14
 2733 009c F2E7     		b	.L213
 2734              	.LVL263:
 2735              	.L220:
 714:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   }
 2736              		.loc 1 714 12 view .LVU828
 2737 009e 6FF00A06 		mvn	r6, #10
 2738 00a2 EFE7     		b	.L213
 2739              	.LVL264:
 2740              	.L225:
 760:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       }
 2741              		.loc 1 760 16 view .LVU829
 2742 00a4 6FF00D06 		mvn	r6, #13
 2743 00a8 ECE7     		b	.L213
 2744              		.cfi_endproc
 2745              	.LFE188:
 2747              		.section	.text.netconn_recv_tcp_pbuf,"ax",%progbits
 2748              		.align	1
 2749              		.global	netconn_recv_tcp_pbuf
 2750              		.syntax unified
 2751              		.thumb
 2752              		.thumb_func
 2754              	netconn_recv_tcp_pbuf:
 2755              	.LVL265:
 2756              	.LFB189:
 783:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_recv_tcp_pbuf: invalid conn", (conn != NULL) &&
 2757              		.loc 1 783 1 is_stmt 1 view -0
 2758              		.cfi_startproc
 2759              		@ args = 0, pretend = 0, frame = 0
 2760              		@ frame_needed = 0, uses_anonymous_args = 0
 783:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_recv_tcp_pbuf: invalid conn", (conn != NULL) &&
 2761              		.loc 1 783 1 is_stmt 0 view .LVU831
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 84


 2762 0000 08B5     		push	{r3, lr}
 2763              	.LCFI72:
 2764              		.cfi_def_cfa_offset 8
 2765              		.cfi_offset 3, -8
 2766              		.cfi_offset 14, -4
 784:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****              NETCONNTYPE_GROUP(netconn_type(conn)) == NETCONN_TCP, return ERR_ARG;);
 2767              		.loc 1 784 3 is_stmt 1 view .LVU832
 784:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****              NETCONNTYPE_GROUP(netconn_type(conn)) == NETCONN_TCP, return ERR_ARG;);
 2768              		.loc 1 784 3 view .LVU833
 2769 0002 40B1     		cbz	r0, .L227
 784:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****              NETCONNTYPE_GROUP(netconn_type(conn)) == NETCONN_TCP, return ERR_ARG;);
 2770              		.loc 1 784 3 is_stmt 0 discriminator 2 view .LVU834
 2771 0004 0378     		ldrb	r3, [r0]	@ zero_extendqisi2
 2772 0006 03F0F003 		and	r3, r3, #240
 2773 000a 102B     		cmp	r3, #16
 2774 000c 03D1     		bne	.L227
 784:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****              NETCONNTYPE_GROUP(netconn_type(conn)) == NETCONN_TCP, return ERR_ARG;);
 2775              		.loc 1 784 3 is_stmt 1 discriminator 4 view .LVU835
 784:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****              NETCONNTYPE_GROUP(netconn_type(conn)) == NETCONN_TCP, return ERR_ARG;);
 2776              		.loc 1 784 3 discriminator 4 view .LVU836
 787:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** }
 2777              		.loc 1 787 3 view .LVU837
 787:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** }
 2778              		.loc 1 787 10 is_stmt 0 view .LVU838
 2779 000e 0022     		movs	r2, #0
 2780 0010 FFF7FEFF 		bl	netconn_recv_data_tcp
 2781              	.LVL266:
 2782              	.L229:
 788:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 2783              		.loc 1 788 1 view .LVU839
 2784 0014 08BD     		pop	{r3, pc}
 2785              	.LVL267:
 2786              	.L227:
 784:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****              NETCONNTYPE_GROUP(netconn_type(conn)) == NETCONN_TCP, return ERR_ARG;);
 2787              		.loc 1 784 3 is_stmt 1 discriminator 3 view .LVU840
 784:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****              NETCONNTYPE_GROUP(netconn_type(conn)) == NETCONN_TCP, return ERR_ARG;);
 2788              		.loc 1 784 3 discriminator 3 view .LVU841
 2789 0016 054B     		ldr	r3, .L231
 2790 0018 4FF44472 		mov	r2, #784
 2791 001c 0449     		ldr	r1, .L231+4
 2792              	.LVL268:
 784:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****              NETCONNTYPE_GROUP(netconn_type(conn)) == NETCONN_TCP, return ERR_ARG;);
 2793              		.loc 1 784 3 is_stmt 0 discriminator 3 view .LVU842
 2794 001e 0548     		ldr	r0, .L231+8
 2795              	.LVL269:
 784:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****              NETCONNTYPE_GROUP(netconn_type(conn)) == NETCONN_TCP, return ERR_ARG;);
 2796              		.loc 1 784 3 discriminator 3 view .LVU843
 2797 0020 FFF7FEFF 		bl	printf
 2798              	.LVL270:
 784:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****              NETCONNTYPE_GROUP(netconn_type(conn)) == NETCONN_TCP, return ERR_ARG;);
 2799              		.loc 1 784 3 is_stmt 1 discriminator 1 view .LVU844
 784:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****              NETCONNTYPE_GROUP(netconn_type(conn)) == NETCONN_TCP, return ERR_ARG;);
 2800              		.loc 1 784 3 discriminator 1 view .LVU845
 2801 0024 6FF00F00 		mvn	r0, #15
 784:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****              NETCONNTYPE_GROUP(netconn_type(conn)) == NETCONN_TCP, return ERR_ARG;);
 2802              		.loc 1 784 3 is_stmt 0 view .LVU846
 2803 0028 F4E7     		b	.L229
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 85


 2804              	.L232:
 2805 002a 00BF     		.align	2
 2806              	.L231:
 2807 002c 00000000 		.word	.LC0
 2808 0030 30000000 		.word	.LC1
 2809 0034 54000000 		.word	.LC2
 2810              		.cfi_endproc
 2811              	.LFE189:
 2813              		.section	.text.netconn_recv_tcp_pbuf_flags,"ax",%progbits
 2814              		.align	1
 2815              		.global	netconn_recv_tcp_pbuf_flags
 2816              		.syntax unified
 2817              		.thumb
 2818              		.thumb_func
 2820              	netconn_recv_tcp_pbuf_flags:
 2821              	.LVL271:
 2822              	.LFB190:
 804:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_recv_tcp_pbuf: invalid conn", (conn != NULL) &&
 2823              		.loc 1 804 1 is_stmt 1 view -0
 2824              		.cfi_startproc
 2825              		@ args = 0, pretend = 0, frame = 0
 2826              		@ frame_needed = 0, uses_anonymous_args = 0
 804:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_recv_tcp_pbuf: invalid conn", (conn != NULL) &&
 2827              		.loc 1 804 1 is_stmt 0 view .LVU848
 2828 0000 08B5     		push	{r3, lr}
 2829              	.LCFI73:
 2830              		.cfi_def_cfa_offset 8
 2831              		.cfi_offset 3, -8
 2832              		.cfi_offset 14, -4
 805:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****              NETCONNTYPE_GROUP(netconn_type(conn)) == NETCONN_TCP, return ERR_ARG;);
 2833              		.loc 1 805 3 is_stmt 1 view .LVU849
 805:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****              NETCONNTYPE_GROUP(netconn_type(conn)) == NETCONN_TCP, return ERR_ARG;);
 2834              		.loc 1 805 3 view .LVU850
 2835 0002 38B1     		cbz	r0, .L234
 805:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****              NETCONNTYPE_GROUP(netconn_type(conn)) == NETCONN_TCP, return ERR_ARG;);
 2836              		.loc 1 805 3 is_stmt 0 discriminator 2 view .LVU851
 2837 0004 0378     		ldrb	r3, [r0]	@ zero_extendqisi2
 2838 0006 03F0F003 		and	r3, r3, #240
 2839 000a 102B     		cmp	r3, #16
 2840 000c 02D1     		bne	.L234
 805:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****              NETCONNTYPE_GROUP(netconn_type(conn)) == NETCONN_TCP, return ERR_ARG;);
 2841              		.loc 1 805 3 is_stmt 1 discriminator 4 view .LVU852
 805:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****              NETCONNTYPE_GROUP(netconn_type(conn)) == NETCONN_TCP, return ERR_ARG;);
 2842              		.loc 1 805 3 discriminator 4 view .LVU853
 808:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** }
 2843              		.loc 1 808 3 view .LVU854
 808:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** }
 2844              		.loc 1 808 10 is_stmt 0 view .LVU855
 2845 000e FFF7FEFF 		bl	netconn_recv_data_tcp
 2846              	.LVL272:
 2847              	.L236:
 809:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #endif /* LWIP_TCP */
 2848              		.loc 1 809 1 view .LVU856
 2849 0012 08BD     		pop	{r3, pc}
 2850              	.LVL273:
 2851              	.L234:
 805:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****              NETCONNTYPE_GROUP(netconn_type(conn)) == NETCONN_TCP, return ERR_ARG;);
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 86


 2852              		.loc 1 805 3 is_stmt 1 discriminator 3 view .LVU857
 805:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****              NETCONNTYPE_GROUP(netconn_type(conn)) == NETCONN_TCP, return ERR_ARG;);
 2853              		.loc 1 805 3 discriminator 3 view .LVU858
 2854 0014 044B     		ldr	r3, .L238
 2855 0016 40F22532 		movw	r2, #805
 2856              	.LVL274:
 805:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****              NETCONNTYPE_GROUP(netconn_type(conn)) == NETCONN_TCP, return ERR_ARG;);
 2857              		.loc 1 805 3 is_stmt 0 discriminator 3 view .LVU859
 2858 001a 0449     		ldr	r1, .L238+4
 2859              	.LVL275:
 805:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****              NETCONNTYPE_GROUP(netconn_type(conn)) == NETCONN_TCP, return ERR_ARG;);
 2860              		.loc 1 805 3 discriminator 3 view .LVU860
 2861 001c 0448     		ldr	r0, .L238+8
 2862              	.LVL276:
 805:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****              NETCONNTYPE_GROUP(netconn_type(conn)) == NETCONN_TCP, return ERR_ARG;);
 2863              		.loc 1 805 3 discriminator 3 view .LVU861
 2864 001e FFF7FEFF 		bl	printf
 2865              	.LVL277:
 805:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****              NETCONNTYPE_GROUP(netconn_type(conn)) == NETCONN_TCP, return ERR_ARG;);
 2866              		.loc 1 805 3 is_stmt 1 discriminator 1 view .LVU862
 805:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****              NETCONNTYPE_GROUP(netconn_type(conn)) == NETCONN_TCP, return ERR_ARG;);
 2867              		.loc 1 805 3 discriminator 1 view .LVU863
 2868 0022 6FF00F00 		mvn	r0, #15
 805:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****              NETCONNTYPE_GROUP(netconn_type(conn)) == NETCONN_TCP, return ERR_ARG;);
 2869              		.loc 1 805 3 is_stmt 0 view .LVU864
 2870 0026 F4E7     		b	.L236
 2871              	.L239:
 2872              		.align	2
 2873              	.L238:
 2874 0028 00000000 		.word	.LC0
 2875 002c 30000000 		.word	.LC1
 2876 0030 54000000 		.word	.LC2
 2877              		.cfi_endproc
 2878              	.LFE190:
 2880              		.section	.rodata.netconn_recv.str1.4,"aMS",%progbits,1
 2881              		.align	2
 2882              	.LC26:
 2883 0000 7020213D 		.ascii	"p != NULL\000"
 2883      204E554C 
 2883      4C00
 2884              		.section	.text.netconn_recv,"ax",%progbits
 2885              		.align	1
 2886              		.global	netconn_recv
 2887              		.syntax unified
 2888              		.thumb
 2889              		.thumb_func
 2891              	netconn_recv:
 2892              	.LVL278:
 2893              	.LFB193:
 861:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #if LWIP_TCP
 2894              		.loc 1 861 1 is_stmt 1 view -0
 2895              		.cfi_startproc
 2896              		@ args = 0, pretend = 0, frame = 8
 2897              		@ frame_needed = 0, uses_anonymous_args = 0
 861:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #if LWIP_TCP
 2898              		.loc 1 861 1 is_stmt 0 view .LVU866
 2899 0000 70B5     		push	{r4, r5, r6, lr}
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 87


 2900              	.LCFI74:
 2901              		.cfi_def_cfa_offset 16
 2902              		.cfi_offset 4, -16
 2903              		.cfi_offset 5, -12
 2904              		.cfi_offset 6, -8
 2905              		.cfi_offset 14, -4
 2906 0002 82B0     		sub	sp, sp, #8
 2907              	.LCFI75:
 2908              		.cfi_def_cfa_offset 24
 863:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   err_t err;
 2909              		.loc 1 863 3 is_stmt 1 view .LVU867
 2910              	.LVL279:
 864:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #endif /* LWIP_TCP */
 2911              		.loc 1 864 3 view .LVU868
 867:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   *new_buf = NULL;
 2912              		.loc 1 867 3 view .LVU869
 867:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   *new_buf = NULL;
 2913              		.loc 1 867 3 view .LVU870
 2914 0004 11B3     		cbz	r1, .L250
 2915 0006 0446     		mov	r4, r0
 2916 0008 0D46     		mov	r5, r1
 867:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   *new_buf = NULL;
 2917              		.loc 1 867 3 discriminator 2 view .LVU871
 867:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   *new_buf = NULL;
 2918              		.loc 1 867 3 discriminator 2 view .LVU872
 868:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_recv: invalid conn",    (conn != NULL),    return ERR_ARG;);
 2919              		.loc 1 868 3 view .LVU873
 868:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   LWIP_ERROR("netconn_recv: invalid conn",    (conn != NULL),    return ERR_ARG;);
 2920              		.loc 1 868 12 is_stmt 0 view .LVU874
 2921 000a 0023     		movs	r3, #0
 2922 000c 0B60     		str	r3, [r1]
 869:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 2923              		.loc 1 869 3 is_stmt 1 view .LVU875
 869:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 2924              		.loc 1 869 3 view .LVU876
 2925 000e 38B3     		cbz	r0, .L251
 869:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 2926              		.loc 1 869 3 discriminator 2 view .LVU877
 869:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 2927              		.loc 1 869 3 discriminator 2 view .LVU878
 873:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #endif /* (LWIP_UDP || LWIP_RAW) */
 2928              		.loc 1 873 3 view .LVU879
 873:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #endif /* (LWIP_UDP || LWIP_RAW) */
 2929              		.loc 1 873 7 is_stmt 0 view .LVU880
 2930 0010 0378     		ldrb	r3, [r0]	@ zero_extendqisi2
 873:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #endif /* (LWIP_UDP || LWIP_RAW) */
 2931              		.loc 1 873 6 view .LVU881
 2932 0012 03F0F003 		and	r3, r3, #240
 2933 0016 102B     		cmp	r3, #16
 2934 0018 3CD1     		bne	.L244
 2935              	.LBB12:
 876:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     /* This is not a listening netconn, since recvmbox is set */
 2936              		.loc 1 876 5 is_stmt 1 view .LVU882
 876:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     /* This is not a listening netconn, since recvmbox is set */
 2937              		.loc 1 876 18 is_stmt 0 view .LVU883
 2938 001a 0023     		movs	r3, #0
 2939 001c 0193     		str	r3, [sp, #4]
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 88


 879:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     if (buf == NULL) {
 2940              		.loc 1 879 5 is_stmt 1 view .LVU884
 879:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     if (buf == NULL) {
 2941              		.loc 1 879 28 is_stmt 0 view .LVU885
 2942 001e 0620     		movs	r0, #6
 2943              	.LVL280:
 879:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     if (buf == NULL) {
 2944              		.loc 1 879 28 view .LVU886
 2945 0020 FFF7FEFF 		bl	memp_malloc
 2946              	.LVL281:
 880:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       return ERR_MEM;
 2947              		.loc 1 880 5 is_stmt 1 view .LVU887
 880:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       return ERR_MEM;
 2948              		.loc 1 880 8 is_stmt 0 view .LVU888
 2949 0024 0646     		mov	r6, r0
 2950 0026 0028     		cmp	r0, #0
 2951 0028 31D0     		beq	.L248
 884:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     if (err != ERR_OK) {
 2952              		.loc 1 884 5 is_stmt 1 view .LVU889
 884:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     if (err != ERR_OK) {
 2953              		.loc 1 884 11 is_stmt 0 view .LVU890
 2954 002a 0022     		movs	r2, #0
 2955 002c 01A9     		add	r1, sp, #4
 2956 002e 2046     		mov	r0, r4
 2957              	.LVL282:
 884:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     if (err != ERR_OK) {
 2958              		.loc 1 884 11 view .LVU891
 2959 0030 FFF7FEFF 		bl	netconn_recv_data_tcp
 2960              	.LVL283:
 885:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       memp_free(MEMP_NETBUF, buf);
 2961              		.loc 1 885 5 is_stmt 1 view .LVU892
 885:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       memp_free(MEMP_NETBUF, buf);
 2962              		.loc 1 885 8 is_stmt 0 view .LVU893
 2963 0034 0446     		mov	r4, r0
 2964              	.LVL284:
 885:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       memp_free(MEMP_NETBUF, buf);
 2965              		.loc 1 885 8 view .LVU894
 2966 0036 E8B9     		cbnz	r0, .L252
 889:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 2967              		.loc 1 889 5 is_stmt 1 view .LVU895
 889:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 2968              		.loc 1 889 5 view .LVU896
 2969 0038 019B     		ldr	r3, [sp, #4]
 2970 003a 03B3     		cbz	r3, .L253
 2971              	.LVL285:
 2972              	.L247:
 889:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 2973              		.loc 1 889 5 discriminator 3 view .LVU897
 889:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 2974              		.loc 1 889 5 discriminator 3 view .LVU898
 891:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     buf->ptr = p;
 2975              		.loc 1 891 5 view .LVU899
 891:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     buf->ptr = p;
 2976              		.loc 1 891 12 is_stmt 0 view .LVU900
 2977 003c 019B     		ldr	r3, [sp, #4]
 2978 003e 3360     		str	r3, [r6]
 892:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     buf->port = 0;
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 89


 2979              		.loc 1 892 5 is_stmt 1 view .LVU901
 892:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     buf->port = 0;
 2980              		.loc 1 892 14 is_stmt 0 view .LVU902
 2981 0040 7360     		str	r3, [r6, #4]
 893:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     ip_addr_set_zero(&buf->addr);
 2982              		.loc 1 893 5 is_stmt 1 view .LVU903
 893:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     ip_addr_set_zero(&buf->addr);
 2983              		.loc 1 893 15 is_stmt 0 view .LVU904
 2984 0042 0023     		movs	r3, #0
 2985 0044 B381     		strh	r3, [r6, #12]	@ movhi
 894:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     *new_buf = buf;
 2986              		.loc 1 894 5 is_stmt 1 view .LVU905
 2987 0046 B360     		str	r3, [r6, #8]
 895:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     /* don't set conn->last_err: it's only ERR_OK, anyway */
 2988              		.loc 1 895 5 view .LVU906
 895:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     /* don't set conn->last_err: it's only ERR_OK, anyway */
 2989              		.loc 1 895 14 is_stmt 0 view .LVU907
 2990 0048 2E60     		str	r6, [r5]
 897:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   }
 2991              		.loc 1 897 5 is_stmt 1 view .LVU908
 897:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   }
 2992              		.loc 1 897 12 is_stmt 0 view .LVU909
 2993 004a 27E0     		b	.L242
 2994              	.LVL286:
 2995              	.L250:
 897:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   }
 2996              		.loc 1 897 12 view .LVU910
 2997              	.LBE12:
 867:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   *new_buf = NULL;
 2998              		.loc 1 867 3 is_stmt 1 discriminator 1 view .LVU911
 867:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   *new_buf = NULL;
 2999              		.loc 1 867 3 discriminator 1 view .LVU912
 3000 004c 154B     		ldr	r3, .L254
 3001 004e 40F26332 		movw	r2, #867
 3002 0052 1549     		ldr	r1, .L254+4
 3003              	.LVL287:
 867:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   *new_buf = NULL;
 3004              		.loc 1 867 3 is_stmt 0 discriminator 1 view .LVU913
 3005 0054 1548     		ldr	r0, .L254+8
 3006              	.LVL288:
 867:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   *new_buf = NULL;
 3007              		.loc 1 867 3 discriminator 1 view .LVU914
 3008 0056 FFF7FEFF 		bl	printf
 3009              	.LVL289:
 867:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   *new_buf = NULL;
 3010              		.loc 1 867 3 is_stmt 1 discriminator 1 view .LVU915
 867:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   *new_buf = NULL;
 3011              		.loc 1 867 3 discriminator 1 view .LVU916
 3012 005a 6FF00F04 		mvn	r4, #15
 867:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   *new_buf = NULL;
 3013              		.loc 1 867 3 is_stmt 0 view .LVU917
 3014 005e 1DE0     		b	.L242
 3015              	.LVL290:
 3016              	.L251:
 869:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 3017              		.loc 1 869 3 is_stmt 1 discriminator 1 view .LVU918
 869:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 90


 3018              		.loc 1 869 3 discriminator 1 view .LVU919
 3019 0060 104B     		ldr	r3, .L254
 3020 0062 40F26532 		movw	r2, #869
 3021 0066 1249     		ldr	r1, .L254+12
 3022              	.LVL291:
 869:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 3023              		.loc 1 869 3 is_stmt 0 discriminator 1 view .LVU920
 3024 0068 1048     		ldr	r0, .L254+8
 3025              	.LVL292:
 869:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 3026              		.loc 1 869 3 discriminator 1 view .LVU921
 3027 006a FFF7FEFF 		bl	printf
 3028              	.LVL293:
 869:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 3029              		.loc 1 869 3 is_stmt 1 discriminator 1 view .LVU922
 869:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 3030              		.loc 1 869 3 discriminator 1 view .LVU923
 3031 006e 6FF00F04 		mvn	r4, #15
 3032              	.LVL294:
 869:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 3033              		.loc 1 869 3 is_stmt 0 view .LVU924
 3034 0072 13E0     		b	.L242
 3035              	.LVL295:
 3036              	.L252:
 3037              	.LBB13:
 886:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       return err;
 3038              		.loc 1 886 7 is_stmt 1 view .LVU925
 3039 0074 3146     		mov	r1, r6
 3040 0076 0620     		movs	r0, #6
 3041              	.LVL296:
 886:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****       return err;
 3042              		.loc 1 886 7 is_stmt 0 view .LVU926
 3043 0078 FFF7FEFF 		bl	memp_free
 3044              	.LVL297:
 887:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     }
 3045              		.loc 1 887 7 is_stmt 1 view .LVU927
 887:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     }
 3046              		.loc 1 887 14 is_stmt 0 view .LVU928
 3047 007c 0EE0     		b	.L242
 3048              	.LVL298:
 3049              	.L253:
 889:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 3050              		.loc 1 889 5 is_stmt 1 discriminator 1 view .LVU929
 889:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 3051              		.loc 1 889 5 discriminator 1 view .LVU930
 3052 007e 094B     		ldr	r3, .L254
 3053 0080 40F27932 		movw	r2, #889
 3054 0084 0B49     		ldr	r1, .L254+16
 3055 0086 0948     		ldr	r0, .L254+8
 3056              	.LVL299:
 889:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 3057              		.loc 1 889 5 is_stmt 0 discriminator 1 view .LVU931
 3058 0088 FFF7FEFF 		bl	printf
 3059              	.LVL300:
 3060 008c D6E7     		b	.L247
 3061              	.LVL301:
 3062              	.L248:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 91


 881:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     }
 3063              		.loc 1 881 14 view .LVU932
 3064 008e 4FF0FF34 		mov	r4, #-1
 3065              	.LVL302:
 881:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     }
 3066              		.loc 1 881 14 view .LVU933
 3067 0092 03E0     		b	.L242
 3068              	.LVL303:
 3069              	.L244:
 881:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****     }
 3070              		.loc 1 881 14 view .LVU934
 3071              	.LBE13:
 905:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #endif /* (LWIP_UDP || LWIP_RAW) */
 3072              		.loc 1 905 5 is_stmt 1 view .LVU935
 905:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #endif /* (LWIP_UDP || LWIP_RAW) */
 3073              		.loc 1 905 12 is_stmt 0 view .LVU936
 3074 0094 0022     		movs	r2, #0
 3075 0096 FFF7FEFF 		bl	netconn_recv_data
 3076              	.LVL304:
 905:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** #endif /* (LWIP_UDP || LWIP_RAW) */
 3077              		.loc 1 905 12 view .LVU937
 3078 009a 0446     		mov	r4, r0
 3079              	.LVL305:
 3080              	.L242:
 908:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
 3081              		.loc 1 908 1 view .LVU938
 3082 009c 2046     		mov	r0, r4
 3083 009e 02B0     		add	sp, sp, #8
 3084              	.LCFI76:
 3085              		.cfi_def_cfa_offset 16
 3086              		@ sp needed
 3087 00a0 70BD     		pop	{r4, r5, r6, pc}
 3088              	.L255:
 3089 00a2 00BF     		.align	2
 3090              	.L254:
 3091 00a4 00000000 		.word	.LC0
 3092 00a8 00000000 		.word	.LC22
 3093 00ac 54000000 		.word	.LC2
 3094 00b0 20000000 		.word	.LC23
 3095 00b4 00000000 		.word	.LC26
 3096              		.cfi_endproc
 3097              	.LFE193:
 3099              		.section	.text.netconn_shutdown,"ax",%progbits
 3100              		.align	1
 3101              		.global	netconn_shutdown
 3102              		.syntax unified
 3103              		.thumb
 3104              		.thumb_func
 3106              	netconn_shutdown:
 3107              	.LVL306:
 3108              	.LFB201:
1152:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** 
1153:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** /**
1154:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @ingroup netconn_tcp
1155:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * Shut down one or both sides of a TCP netconn (doesn't delete it).
1156:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  *
1157:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @param conn the TCP netconn to shut down
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 92


1158:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @param shut_rx shut down the RX side (no more read possible after this)
1159:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @param shut_tx shut down the TX side (no more write possible after this)
1160:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  * @return ERR_OK if the netconn was closed, any other err_t on error
1161:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****  */
1162:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** err_t
1163:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** netconn_shutdown(struct netconn *conn, u8_t shut_rx, u8_t shut_tx)
1164:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** {
 3109              		.loc 1 1164 1 is_stmt 1 view -0
 3110              		.cfi_startproc
 3111              		@ args = 0, pretend = 0, frame = 0
 3112              		@ frame_needed = 0, uses_anonymous_args = 0
 3113              		.loc 1 1164 1 is_stmt 0 view .LVU940
 3114 0000 08B5     		push	{r3, lr}
 3115              	.LCFI77:
 3116              		.cfi_def_cfa_offset 8
 3117              		.cfi_offset 3, -8
 3118              		.cfi_offset 14, -4
1165:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   return netconn_close_shutdown(conn, (u8_t)((shut_rx ? NETCONN_SHUT_RD : 0) | (shut_tx ? NETCONN_S
 3119              		.loc 1 1165 3 is_stmt 1 view .LVU941
 3120              		.loc 1 1165 78 is_stmt 0 view .LVU942
 3121 0002 31B1     		cbz	r1, .L259
 3122              		.loc 1 1165 78 discriminator 1 view .LVU943
 3123 0004 0121     		movs	r1, #1
 3124              	.LVL307:
 3125              	.L257:
 3126              		.loc 1 1165 78 discriminator 4 view .LVU944
 3127 0006 32B1     		cbz	r2, .L260
 3128              		.loc 1 1165 78 discriminator 5 view .LVU945
 3129 0008 0223     		movs	r3, #2
 3130              	.L258:
 3131              		.loc 1 1165 10 discriminator 8 view .LVU946
 3132 000a 1943     		orrs	r1, r1, r3
 3133 000c FFF7FEFF 		bl	netconn_close_shutdown
 3134              	.LVL308:
1166:Middlewares/Third_Party/LwIP/src/api/api_lib.c **** }
 3135              		.loc 1 1166 1 view .LVU947
 3136 0010 08BD     		pop	{r3, pc}
 3137              	.LVL309:
 3138              	.L259:
1165:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   return netconn_close_shutdown(conn, (u8_t)((shut_rx ? NETCONN_SHUT_RD : 0) | (shut_tx ? NETCONN_S
 3139              		.loc 1 1165 78 discriminator 2 view .LVU948
 3140 0012 0021     		movs	r1, #0
 3141              	.LVL310:
1165:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   return netconn_close_shutdown(conn, (u8_t)((shut_rx ? NETCONN_SHUT_RD : 0) | (shut_tx ? NETCONN_S
 3142              		.loc 1 1165 78 discriminator 2 view .LVU949
 3143 0014 F7E7     		b	.L257
 3144              	.L260:
1165:Middlewares/Third_Party/LwIP/src/api/api_lib.c ****   return netconn_close_shutdown(conn, (u8_t)((shut_rx ? NETCONN_SHUT_RD : 0) | (shut_tx ? NETCONN_S
 3145              		.loc 1 1165 78 discriminator 6 view .LVU950
 3146 0016 0023     		movs	r3, #0
 3147 0018 F7E7     		b	.L258
 3148              		.cfi_endproc
 3149              	.LFE201:
 3151              		.text
 3152              	.Letext0:
 3153              		.file 2 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin/../lib/gcc/arm-none-eabi/13.3.1/../
 3154              		.file 3 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin/../lib/gcc/arm-none-eabi/13.3.1/../
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 93


 3155              		.file 4 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin/../lib/gcc/arm-none-eabi/13.3.1/inc
 3156              		.file 5 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin/../lib/gcc/arm-none-eabi/13.3.1/../
 3157              		.file 6 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin/../lib/gcc/arm-none-eabi/13.3.1/../
 3158              		.file 7 "Middlewares/Third_Party/LwIP/system/arch/cc.h"
 3159              		.file 8 "Middlewares/Third_Party/LwIP/src/include/lwip/arch.h"
 3160              		.file 9 "Middlewares/Third_Party/LwIP/src/include/lwip/err.h"
 3161              		.file 10 "Middlewares/Third_Party/LwIP/src/include/lwip/pbuf.h"
 3162              		.file 11 "Middlewares/Third_Party/LwIP/src/include/lwip/ip4_addr.h"
 3163              		.file 12 "Middlewares/Third_Party/LwIP/src/include/lwip/ip_addr.h"
 3164              		.file 13 "Middlewares/Third_Party/LwIP/src/include/lwip/netbuf.h"
 3165              		.file 14 "Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.h"
 3166              		.file 15 "Middlewares/Third_Party/LwIP/system/arch/sys_arch.h"
 3167              		.file 16 "Middlewares/Third_Party/LwIP/src/include/lwip/api.h"
 3168              		.file 17 "Middlewares/Third_Party/LwIP/src/include/lwip/ip.h"
 3169              		.file 18 "Middlewares/Third_Party/LwIP/src/include/lwip/tcp.h"
 3170              		.file 19 "Middlewares/Third_Party/LwIP/src/include/lwip/udp.h"
 3171              		.file 20 "Middlewares/Third_Party/LwIP/src/include/lwip/priv/api_msg.h"
 3172              		.file 21 "Middlewares/Third_Party/LwIP/src/include/lwip/memp.h"
 3173              		.file 22 "Middlewares/Third_Party/LwIP/src/include/lwip/tcpip.h"
 3174              		.file 23 "Middlewares/Third_Party/LwIP/src/include/lwip/tcpbase.h"
 3175              		.file 24 "Middlewares/Third_Party/LwIP/src/include/lwip/priv/tcp_priv.h"
 3176              		.file 25 "Middlewares/Third_Party/LwIP/src/include/lwip/prot/tcp.h"
 3177              		.file 26 "Middlewares/Third_Party/LwIP/src/include/lwip/sys.h"
 3178              		.file 27 "Middlewares/Third_Party/LwIP/src/include/lwip/priv/tcpip_priv.h"
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 94


DEFINED SYMBOLS
                            *ABS*:00000000 api_lib.c
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:20     .text.netconn_apimsg:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:25     .text.netconn_apimsg:00000000 netconn_apimsg
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:63     .rodata.netconn_tcp_recvd_msg.str1.4:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:73     .text.netconn_tcp_recvd_msg:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:78     .text.netconn_tcp_recvd_msg:00000000 netconn_tcp_recvd_msg
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:142    .text.netconn_tcp_recvd_msg:00000034 $d
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:150    .rodata.netconn_close_shutdown.str1.4:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:154    .text.netconn_close_shutdown:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:159    .text.netconn_close_shutdown:00000000 netconn_close_shutdown
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:237    .text.netconn_close_shutdown:00000034 $d
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:245    .rodata.netconn_new_with_proto_and_callback.str1.4:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:258    .text.netconn_new_with_proto_and_callback:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:264    .text.netconn_new_with_proto_and_callback:00000000 netconn_new_with_proto_and_callback
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:436    .text.netconn_new_with_proto_and_callback:0000009c $d
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:448    .text.netconn_prepare_delete:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:454    .text.netconn_prepare_delete:00000000 netconn_prepare_delete
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:511    .text.netconn_prepare_delete:00000020 $d
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:516    .text.netconn_delete:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:522    .text.netconn_delete:00000000 netconn_delete
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:573    .rodata.netconn_getaddr.str1.4:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:583    .text.netconn_getaddr:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:589    .text.netconn_getaddr:00000000 netconn_getaddr
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:729    .text.netconn_getaddr:0000005c $d
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:739    .rodata.netconn_bind.str1.4:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:743    .text.netconn_bind:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:749    .text.netconn_bind:00000000 netconn_bind
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:840    .text.netconn_bind:00000038 $d
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:849    .rodata.netconn_bind_if.str1.4:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:853    .text.netconn_bind_if:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:859    .text.netconn_bind_if:00000000 netconn_bind_if
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:932    .text.netconn_bind_if:00000030 $d
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:940    .rodata.netconn_connect.str1.4:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:944    .text.netconn_connect:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:950    .text.netconn_connect:00000000 netconn_connect
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:1041   .text.netconn_connect:00000038 $d
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:1050   .rodata.netconn_disconnect.str1.4:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:1054   .text.netconn_disconnect:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:1060   .text.netconn_disconnect:00000000 netconn_disconnect
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:1126   .text.netconn_disconnect:0000002c $d
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:1134   .rodata.netconn_listen_with_backlog.str1.4:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:1138   .text.netconn_listen_with_backlog:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:1144   .text.netconn_listen_with_backlog:00000000 netconn_listen_with_backlog
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:1215   .text.netconn_listen_with_backlog:0000002c $d
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:1223   .text.netconn_tcp_recvd:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:1229   .text.netconn_tcp_recvd:00000000 netconn_tcp_recvd
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:1296   .text.netconn_tcp_recvd:00000030 $d
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:1303   .rodata.netconn_send.str1.4:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:1307   .text.netconn_send:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:1313   .text.netconn_send:00000000 netconn_send
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:1387   .text.netconn_send:0000002c $d
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:1395   .text.netconn_sendto:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:1401   .text.netconn_sendto:00000000 netconn_sendto
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:1460   .rodata.netconn_write_vectors_partly.str1.4:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:1470   .text.netconn_write_vectors_partly:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:1476   .text.netconn_write_vectors_partly:00000000 netconn_write_vectors_partly
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 95


C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:1740   .text.netconn_write_vectors_partly:000000e0 $d
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:1751   .text.netconn_write_partly:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:1757   .text.netconn_write_partly:00000000 netconn_write_partly
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:1802   .text.netconn_close:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:1808   .text.netconn_close:00000000 netconn_close
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:1832   .text.netconn_err:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:1838   .text.netconn_err:00000000 netconn_err
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:1887   .rodata.netconn_accept.str1.4:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:1894   .text.netconn_accept:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:1900   .text.netconn_accept:00000000 netconn_accept
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:2101   .text.netconn_accept:000000c8 $d
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:2109   .rodata.netconn_recv_data.str1.4:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:2119   .text.netconn_recv_data:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:2124   .text.netconn_recv_data:00000000 netconn_recv_data
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:2414   .text.netconn_recv_data:0000011c $d
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:2424   .rodata.netconn_recv_udp_raw_netbuf.str1.4:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:2428   .text.netconn_recv_udp_raw_netbuf:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:2434   .text.netconn_recv_udp_raw_netbuf:00000000 netconn_recv_udp_raw_netbuf
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:2487   .text.netconn_recv_udp_raw_netbuf:0000002c $d
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:2494   .text.netconn_recv_udp_raw_netbuf_flags:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:2500   .text.netconn_recv_udp_raw_netbuf_flags:00000000 netconn_recv_udp_raw_netbuf_flags
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:2554   .text.netconn_recv_udp_raw_netbuf_flags:00000028 $d
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:2561   .text.netconn_recv_data_tcp:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:2566   .text.netconn_recv_data_tcp:00000000 netconn_recv_data_tcp
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:2748   .text.netconn_recv_tcp_pbuf:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:2754   .text.netconn_recv_tcp_pbuf:00000000 netconn_recv_tcp_pbuf
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:2807   .text.netconn_recv_tcp_pbuf:0000002c $d
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:2814   .text.netconn_recv_tcp_pbuf_flags:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:2820   .text.netconn_recv_tcp_pbuf_flags:00000000 netconn_recv_tcp_pbuf_flags
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:2874   .text.netconn_recv_tcp_pbuf_flags:00000028 $d
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:2881   .rodata.netconn_recv.str1.4:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:2885   .text.netconn_recv:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:2891   .text.netconn_recv:00000000 netconn_recv
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:3091   .text.netconn_recv:000000a4 $d
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:3100   .text.netconn_shutdown:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s:3106   .text.netconn_shutdown:00000000 netconn_shutdown

UNDEFINED SYMBOLS
tcpip_send_msg_wait_sem
printf
lwip_netconn_do_recv
lwip_netconn_do_close
netconn_alloc
sys_mbox_valid
sys_sem_valid
sys_sem_free
sys_mbox_free
memp_free
lwip_netconn_do_newconn
lwip_netconn_do_delconn
netconn_free
lwip_netconn_do_getaddr
lwip_netconn_do_bind
ip_addr_any
lwip_netconn_do_bind_if
lwip_netconn_do_connect
lwip_netconn_do_disconnect
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5OKERH.s 			page 96


lwip_netconn_do_listen
lwip_netconn_do_send
lwip_netconn_do_write
sys_arch_protect
sys_arch_unprotect
sys_arch_mbox_tryfetch
lwip_netconn_is_err_msg
sys_arch_mbox_fetch
memp_malloc
