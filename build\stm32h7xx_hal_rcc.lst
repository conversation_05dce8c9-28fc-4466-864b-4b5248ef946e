ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 1


   1              		.cpu cortex-m7
   2              		.arch armv7e-m
   3              		.fpu fpv5-d16
   4              		.eabi_attribute 28, 1
   5              		.eabi_attribute 20, 1
   6              		.eabi_attribute 21, 1
   7              		.eabi_attribute 23, 3
   8              		.eabi_attribute 24, 1
   9              		.eabi_attribute 25, 1
  10              		.eabi_attribute 26, 1
  11              		.eabi_attribute 30, 1
  12              		.eabi_attribute 34, 1
  13              		.eabi_attribute 18, 4
  14              		.file	"stm32h7xx_hal_rcc.c"
  15              		.text
  16              	.Ltext0:
  17              		.cfi_sections	.debug_frame
  18              		.file 1 "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c"
  19              		.section	.text.HAL_RCC_DeInit,"ax",%progbits
  20              		.align	1
  21              		.global	HAL_RCC_DeInit
  22              		.syntax unified
  23              		.thumb
  24              		.thumb_func
  26              	HAL_RCC_DeInit:
  27              	.LFB144:
   1:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** /**
   2:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   ******************************************************************************
   3:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @file    stm32h7xx_hal_rcc.c
   4:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * <AUTHOR> Application Team
   5:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @brief   RCC HAL module driver.
   6:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *          This file provides firmware functions to manage the following
   7:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *          functionalities of the Reset and Clock Control (RCC) peripheral:
   8:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *           + Initialization and de-initialization functions
   9:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *           + Peripheral Control functions
  10:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *
  11:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   @verbatim
  12:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   ==============================================================================
  13:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****                       ##### RCC specific features #####
  14:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   ==============================================================================
  15:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     [..]
  16:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       After reset the device is running from Internal High Speed oscillator
  17:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       (HSI 64MHz) with Flash 0 wait state,and all peripherals are off except
  18:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       internal SRAM, Flash, JTAG and PWR
  19:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       (+) There is no pre-scaler on High speed (AHB) and Low speed (APB) buses;
  20:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           all peripherals mapped on these buses are running at HSI speed.
  21:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       (+) The clock for all peripherals is switched off, except the SRAM and FLASH.
  22:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       (+) All GPIOs are in analogue mode , except the JTAG pins which
  23:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           are assigned to be used for debug purpose.
  24:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
  25:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     [..]
  26:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       Once the device started from reset, the user application has to:
  27:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       (+) Configure the clock source to be used to drive the System clock
  28:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           (if the application needs higher frequency/performance)
  29:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       (+) Configure the System clock frequency and Flash settings
  30:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       (+) Configure the AHB and APB buses pre-scalers
  31:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       (+) Enable the clock for the peripheral(s) to be used
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 2


  32:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       (+) Configure the clock kernel source(s) for peripherals which clocks are not
  33:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           derived from the System clock through :RCC_D1CCIPR,RCC_D2CCIP1R,RCC_D2CCIP2R
  34:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           and RCC_D3CCIPR registers
  35:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
  36:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****                       ##### RCC Limitations #####
  37:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   ==============================================================================
  38:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     [..]
  39:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       A delay between an RCC peripheral clock enable and the effective peripheral
  40:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       enabling should be taken into account in order to manage the peripheral read/write
  41:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       from/to registers.
  42:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       (+) This delay depends on the peripheral mapping.
  43:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       (+) If peripheral is mapped on AHB: the delay is 2 AHB clock cycle
  44:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           after the clock enable bit is set on the hardware register
  45:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       (+) If peripheral is mapped on APB: the delay is 2 APB clock cycle
  46:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           after the clock enable bit is set on the hardware register
  47:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
  48:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     [..]
  49:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       Implemented Workaround:
  50:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       (+) For AHB & APB peripherals, a dummy read to the peripheral register has been
  51:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           inserted in each __HAL_RCC_PPP_CLK_ENABLE() macro.
  52:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
  53:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   @endverbatim
  54:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****  ******************************************************************************
  55:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @attention
  56:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *
  57:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * Copyright (c) 2017 STMicroelectronics.
  58:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * All rights reserved.
  59:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *
  60:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * This software is licensed under terms that can be found in the LICENSE file in
  61:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * the root directory of this software component.
  62:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * If no LICENSE file comes with this software, it is provided AS-IS.
  63:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   ******************************************************************************
  64:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   */
  65:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
  66:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** /* Includes ------------------------------------------------------------------*/
  67:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #include "stm32h7xx_hal.h"
  68:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
  69:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** /** @addtogroup STM32H7xx_HAL_Driver
  70:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @{
  71:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   */
  72:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
  73:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** /** @defgroup RCC  RCC
  74:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @brief RCC HAL module driver
  75:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @{
  76:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   */
  77:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
  78:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #ifdef HAL_RCC_MODULE_ENABLED
  79:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
  80:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** /* Private typedef -----------------------------------------------------------*/
  81:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** /* Private define ------------------------------------------------------------*/
  82:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** /* Private macro -------------------------------------------------------------*/
  83:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** /** @defgroup RCC_Private_Macros RCC Private Macros
  84:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @{
  85:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   */
  86:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #define MCO1_CLK_ENABLE()     __HAL_RCC_GPIOA_CLK_ENABLE()
  87:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #define MCO1_GPIO_PORT        GPIOA
  88:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #define MCO1_PIN              GPIO_PIN_8
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 3


  89:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
  90:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #define MCO2_CLK_ENABLE()      __HAL_RCC_GPIOC_CLK_ENABLE()
  91:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #define MCO2_GPIO_PORT         GPIOC
  92:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #define MCO2_PIN               GPIO_PIN_9
  93:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
  94:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** /**
  95:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @}
  96:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   */
  97:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** /* Private variables ---------------------------------------------------------*/
  98:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** /** @defgroup RCC_Private_Variables RCC Private Variables
  99:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @{
 100:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   */
 101:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 102:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** /**
 103:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @}
 104:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   */
 105:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** /* Private function prototypes -----------------------------------------------*/
 106:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** /* Exported functions --------------------------------------------------------*/
 107:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 108:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** /** @defgroup RCC_Exported_Functions RCC Exported Functions
 109:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @{
 110:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   */
 111:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 112:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** /** @defgroup RCC_Exported_Functions_Group1 Initialization and de-initialization functions
 113:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****  *  @brief    Initialization and Configuration functions
 114:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****  *
 115:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** @verbatim
 116:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****  ===============================================================================
 117:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****            ##### Initialization and de-initialization functions #####
 118:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****  ===============================================================================
 119:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     [..]
 120:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       This section provides functions allowing to configure the internal/external oscillators
 121:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       (HSE, HSI, LSE,CSI, LSI,HSI48, PLL, CSS and MCO) and the System buses clocks (SYSCLK, AHB3, A
 122:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****        AHB2,AHB4,APB3, APB1L, APB1H, APB2, and APB4).
 123:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 124:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     [..] Internal/external clock and PLL configuration
 125:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****          (#) HSI (high-speed internal), 64 MHz factory-trimmed RC used directly or through
 126:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****              the PLL as System clock source.
 127:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****          (#) CSI is a low-power RC oscillator which can be used directly as system clock, periphera
 128:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****              clock, or PLL input.But even with frequency calibration, is less accurate than an
 129:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****              external crystal oscillator or ceramic resonator.
 130:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****          (#) LSI (low-speed internal), 32 KHz low consumption RC used as IWDG and/or RTC
 131:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****              clock source.
 132:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 133:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****          (#) HSE (high-speed external), 4 to 48 MHz crystal oscillator used directly or
 134:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****              through the PLL as System clock source. Can be used also as RTC clock source.
 135:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 136:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****          (#) LSE (low-speed external), 32 KHz oscillator used as RTC clock source.
 137:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 138:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****          (#) PLL , The RCC features three independent PLLs (clocked by HSI , HSE or CSI),
 139:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****              featuring three different output clocks and able  to work either in integer or Fractio
 140:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****            (++) A main PLL, PLL1, which is generally used to provide clocks to the CPU
 141:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****                 and to some peripherals.
 142:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****            (++) Two dedicated PLLs, PLL2 and PLL3, which are used to generate the kernel clock for 
 143:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 144:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 145:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****          (#) CSS (Clock security system), once enabled and if a HSE clock failure occurs
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 4


 146:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****             (HSE used directly or through PLL as System clock source), the System clock
 147:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****              is automatically switched to HSI and an interrupt is generated if enabled.
 148:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****              The interrupt is linked to the Cortex-M NMI (Non-Mask-able Interrupt)
 149:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****              exception vector.
 150:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 151:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****          (#) MCO1 (micro controller clock output), used to output HSI, LSE, HSE, PLL1(PLL1_Q)
 152:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****              or HSI48 clock (through a configurable pre-scaler) on PA8 pin.
 153:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 154:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****          (#) MCO2 (micro controller clock output), used to output HSE, PLL2(PLL2_P), SYSCLK,
 155:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****              LSI, CSI, or PLL1(PLL1_P) clock (through a configurable pre-scaler) on PC9 pin.
 156:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 157:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     [..] System, AHB and APB buses clocks configuration
 158:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****          (#) Several clock sources can be used to drive the System clock (SYSCLK): CSI,HSI,
 159:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****              HSE and PLL.
 160:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****              The AHB clock (HCLK) is derived from System core clock through configurable
 161:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****              pre-scaler and used to clock the CPU, memory and peripherals mapped
 162:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****              on AHB and APB bus of the 3 Domains (D1, D2, D3)* through configurable pre-scalers
 163:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****              and used to clock the peripherals mapped on these buses. You can use
 164:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****              "HAL_RCC_GetSysClockFreq()" function to retrieve system clock frequency.
 165:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 166:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****          -@- All the peripheral clocks are derived from the System clock (SYSCLK) except those
 167:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****              with dual clock domain where kernel source clock could be selected through
 168:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****              RCC_D1CCIPR,RCC_D2CCIP1R,RCC_D2CCIP2R and RCC_D3CCIPR registers.
 169:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 170:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****      (*) : 2 Domains (CD and SRD) for stm32h7a3xx and stm32h7b3xx family lines.
 171:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** @endverbatim
 172:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @{
 173:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   */
 174:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 175:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** /**
 176:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @brief  Resets the RCC clock configuration to the default reset state.
 177:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @note   The default reset state of the clock configuration is given below:
 178:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *            - HSI ON and used as system clock source
 179:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *            - HSE, PLL1, PLL2 and PLL3 OFF
 180:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *            - AHB, APB Bus pre-scaler set to 1.
 181:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *            - CSS, MCO1 and MCO2 OFF
 182:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *            - All interrupts disabled
 183:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @note   This function doesn't modify the configuration of the
 184:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *            - Peripheral clocks
 185:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *            - LSI, LSE and RTC clocks
 186:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @retval HAL status
 187:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   */
 188:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** HAL_StatusTypeDef HAL_RCC_DeInit(void)
 189:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** {
  28              		.loc 1 189 1 view -0
  29              		.cfi_startproc
  30              		@ args = 0, pretend = 0, frame = 0
  31              		@ frame_needed = 0, uses_anonymous_args = 0
  32 0000 38B5     		push	{r3, r4, r5, lr}
  33              	.LCFI0:
  34              		.cfi_def_cfa_offset 16
  35              		.cfi_offset 3, -16
  36              		.cfi_offset 4, -12
  37              		.cfi_offset 5, -8
  38              		.cfi_offset 14, -4
 190:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   uint32_t tickstart;
  39              		.loc 1 190 3 view .LVU1
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 5


 191:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 192:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Increasing the CPU frequency */
 193:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   if (FLASH_LATENCY_DEFAULT  > __HAL_FLASH_GET_LATENCY())
  40              		.loc 1 193 3 view .LVU2
  41              		.loc 1 193 32 is_stmt 0 view .LVU3
  42 0002 684B     		ldr	r3, .L33
  43 0004 1B68     		ldr	r3, [r3]
  44 0006 03F00F03 		and	r3, r3, #15
  45              		.loc 1 193 6 view .LVU4
  46 000a 062B     		cmp	r3, #6
  47 000c 0CD8     		bhi	.L2
 194:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
 195:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     /* Program the new number of wait states to the LATENCY bits in the FLASH_ACR register */
 196:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     __HAL_FLASH_SET_LATENCY(FLASH_LATENCY_DEFAULT);
  48              		.loc 1 196 5 is_stmt 1 view .LVU5
  49 000e 654A     		ldr	r2, .L33
  50 0010 1368     		ldr	r3, [r2]
  51 0012 23F00F03 		bic	r3, r3, #15
  52 0016 43F00703 		orr	r3, r3, #7
  53 001a 1360     		str	r3, [r2]
 197:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 198:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     /* Check that the new number of wait states is taken into account to access the Flash
 199:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     memory by reading the FLASH_ACR register */
 200:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     if (__HAL_FLASH_GET_LATENCY() != FLASH_LATENCY_DEFAULT)
  54              		.loc 1 200 5 view .LVU6
  55              		.loc 1 200 9 is_stmt 0 view .LVU7
  56 001c 1368     		ldr	r3, [r2]
  57 001e 03F00F03 		and	r3, r3, #15
  58              		.loc 1 200 8 view .LVU8
  59 0022 072B     		cmp	r3, #7
  60 0024 40F0BA80 		bne	.L16
  61              	.L2:
 201:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 202:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       return HAL_ERROR;
 203:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     }
 204:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 205:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
 206:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 207:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 208:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Get Start Tick */
 209:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   tickstart = HAL_GetTick();
  62              		.loc 1 209 3 is_stmt 1 view .LVU9
  63              		.loc 1 209 15 is_stmt 0 view .LVU10
  64 0028 FFF7FEFF 		bl	HAL_GetTick
  65              	.LVL0:
  66 002c 0446     		mov	r4, r0
  67              	.LVL1:
 210:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 211:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Set HSION bit */
 212:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   SET_BIT(RCC->CR, RCC_CR_HSION);
  68              		.loc 1 212 3 is_stmt 1 view .LVU11
  69 002e 5E4A     		ldr	r2, .L33+4
  70 0030 1368     		ldr	r3, [r2]
  71 0032 43F00103 		orr	r3, r3, #1
  72 0036 1360     		str	r3, [r2]
 213:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 214:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Wait till HSI is ready */
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 6


 215:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   while (READ_BIT(RCC->CR, RCC_CR_HSIRDY) == 0U)
  73              		.loc 1 215 3 view .LVU12
  74              	.LVL2:
  75              	.L4:
  76              		.loc 1 215 43 view .LVU13
  77              		.loc 1 215 10 is_stmt 0 view .LVU14
  78 0038 5B4B     		ldr	r3, .L33+4
  79 003a 1B68     		ldr	r3, [r3]
  80              		.loc 1 215 43 view .LVU15
  81 003c 13F0040F 		tst	r3, #4
  82 0040 06D1     		bne	.L26
 216:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
 217:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     if ((HAL_GetTick() - tickstart) > HSI_TIMEOUT_VALUE)
  83              		.loc 1 217 5 is_stmt 1 view .LVU16
  84              		.loc 1 217 10 is_stmt 0 view .LVU17
  85 0042 FFF7FEFF 		bl	HAL_GetTick
  86              	.LVL3:
  87              		.loc 1 217 24 discriminator 1 view .LVU18
  88 0046 001B     		subs	r0, r0, r4
  89              		.loc 1 217 8 discriminator 1 view .LVU19
  90 0048 0228     		cmp	r0, #2
  91 004a F5D9     		bls	.L4
 218:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 219:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       return HAL_TIMEOUT;
  92              		.loc 1 219 14 view .LVU20
  93 004c 0324     		movs	r4, #3
  94              	.LVL4:
  95              		.loc 1 219 14 view .LVU21
  96 004e A6E0     		b	.L3
  97              	.LVL5:
  98              	.L26:
 220:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     }
 221:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
 222:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 223:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Set HSITRIM[6:0] bits to the reset value */
 224:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   SET_BIT(RCC->HSICFGR, RCC_HSICFGR_HSITRIM_6);
  99              		.loc 1 224 3 is_stmt 1 view .LVU22
 100 0050 554B     		ldr	r3, .L33+4
 101 0052 5A68     		ldr	r2, [r3, #4]
 102 0054 42F08042 		orr	r2, r2, #1073741824
 103 0058 5A60     		str	r2, [r3, #4]
 225:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 226:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Reset CFGR register */
 227:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   CLEAR_REG(RCC->CFGR);
 104              		.loc 1 227 3 view .LVU23
 105 005a 0022     		movs	r2, #0
 106 005c 1A61     		str	r2, [r3, #16]
 228:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 229:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Update the SystemCoreClock and SystemD2Clock global variables */
 230:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   SystemCoreClock = HSI_VALUE;
 107              		.loc 1 230 3 view .LVU24
 108              		.loc 1 230 19 is_stmt 0 view .LVU25
 109 005e 534B     		ldr	r3, .L33+8
 110 0060 534A     		ldr	r2, .L33+12
 111 0062 1360     		str	r3, [r2]
 231:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   SystemD2Clock = HSI_VALUE;
 112              		.loc 1 231 3 is_stmt 1 view .LVU26
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 7


 113              		.loc 1 231 17 is_stmt 0 view .LVU27
 114 0064 534A     		ldr	r2, .L33+16
 115 0066 1360     		str	r3, [r2]
 232:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 233:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Adapt Systick interrupt period */
 234:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   if (HAL_InitTick(uwTickPrio) != HAL_OK)
 116              		.loc 1 234 3 is_stmt 1 view .LVU28
 117              		.loc 1 234 7 is_stmt 0 view .LVU29
 118 0068 534B     		ldr	r3, .L33+20
 119 006a 1868     		ldr	r0, [r3]
 120 006c FFF7FEFF 		bl	HAL_InitTick
 121              	.LVL6:
 122              		.loc 1 234 6 discriminator 1 view .LVU30
 123 0070 0446     		mov	r4, r0
 124              	.LVL7:
 125              		.loc 1 234 6 discriminator 1 view .LVU31
 126 0072 08B1     		cbz	r0, .L27
 235:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
 236:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     return HAL_ERROR;
 127              		.loc 1 236 12 view .LVU32
 128 0074 0124     		movs	r4, #1
 129 0076 92E0     		b	.L3
 130              	.L27:
 237:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
 238:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 239:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Get Start Tick */
 240:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   tickstart = HAL_GetTick();
 131              		.loc 1 240 3 is_stmt 1 view .LVU33
 132              		.loc 1 240 15 is_stmt 0 view .LVU34
 133 0078 FFF7FEFF 		bl	HAL_GetTick
 134              	.LVL8:
 135 007c 0546     		mov	r5, r0
 136              	.LVL9:
 241:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 242:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Wait till clock switch is ready */
 243:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   while (READ_BIT(RCC->CFGR, RCC_CFGR_SWS) != 0U)
 137              		.loc 1 243 3 is_stmt 1 view .LVU35
 138              	.L6:
 139              		.loc 1 243 44 view .LVU36
 140              		.loc 1 243 10 is_stmt 0 view .LVU37
 141 007e 4A4B     		ldr	r3, .L33+4
 142 0080 1B69     		ldr	r3, [r3, #16]
 143              		.loc 1 243 44 view .LVU38
 144 0082 13F0380F 		tst	r3, #56
 145 0086 08D0     		beq	.L28
 244:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
 245:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     if ((HAL_GetTick() - tickstart) > CLOCKSWITCH_TIMEOUT_VALUE)
 146              		.loc 1 245 5 is_stmt 1 view .LVU39
 147              		.loc 1 245 10 is_stmt 0 view .LVU40
 148 0088 FFF7FEFF 		bl	HAL_GetTick
 149              	.LVL10:
 150              		.loc 1 245 24 discriminator 1 view .LVU41
 151 008c 401B     		subs	r0, r0, r5
 152              		.loc 1 245 8 discriminator 1 view .LVU42
 153 008e 41F28833 		movw	r3, #5000
 154 0092 9842     		cmp	r0, r3
 155 0094 F3D9     		bls	.L6
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 8


 246:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 247:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       return HAL_TIMEOUT;
 156              		.loc 1 247 14 view .LVU43
 157 0096 0324     		movs	r4, #3
 158 0098 81E0     		b	.L3
 159              	.L28:
 248:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     }
 249:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
 250:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 251:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Get Start Tick */
 252:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   tickstart = HAL_GetTick();
 160              		.loc 1 252 3 is_stmt 1 view .LVU44
 161              		.loc 1 252 15 is_stmt 0 view .LVU45
 162 009a FFF7FEFF 		bl	HAL_GetTick
 163              	.LVL11:
 164 009e 0546     		mov	r5, r0
 165              	.LVL12:
 253:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 254:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Reset CSION, CSIKERON, HSEON, HSI48ON, HSECSSON, HSIDIV bits */
 255:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   CLEAR_BIT(RCC->CR, RCC_CR_HSEON | RCC_CR_HSIKERON | RCC_CR_HSIDIV | RCC_CR_HSIDIVF | RCC_CR_CSION
 166              		.loc 1 255 3 is_stmt 1 view .LVU46
 167 00a0 414A     		ldr	r2, .L33+4
 168 00a2 1168     		ldr	r1, [r2]
 169 00a4 454B     		ldr	r3, .L33+24
 170 00a6 0B40     		ands	r3, r3, r1
 171 00a8 1360     		str	r3, [r2]
 256:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****             | RCC_CR_HSI48ON | RCC_CR_CSSHSEON);
 257:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 258:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Wait till HSE is disabled */
 259:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   while (READ_BIT(RCC->CR, RCC_CR_HSERDY) != 0U)
 172              		.loc 1 259 3 view .LVU47
 173              	.LVL13:
 174              	.L8:
 175              		.loc 1 259 43 view .LVU48
 176              		.loc 1 259 10 is_stmt 0 view .LVU49
 177 00aa 3F4B     		ldr	r3, .L33+4
 178 00ac 1B68     		ldr	r3, [r3]
 179              		.loc 1 259 43 view .LVU50
 180 00ae 13F4003F 		tst	r3, #131072
 181 00b2 06D0     		beq	.L29
 260:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
 261:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     if ((HAL_GetTick() - tickstart) > HSE_TIMEOUT_VALUE)
 182              		.loc 1 261 5 is_stmt 1 view .LVU51
 183              		.loc 1 261 10 is_stmt 0 view .LVU52
 184 00b4 FFF7FEFF 		bl	HAL_GetTick
 185              	.LVL14:
 186              		.loc 1 261 24 discriminator 1 view .LVU53
 187 00b8 401B     		subs	r0, r0, r5
 188              		.loc 1 261 8 discriminator 1 view .LVU54
 189 00ba 6428     		cmp	r0, #100
 190 00bc F5D9     		bls	.L8
 262:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 263:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       return HAL_TIMEOUT;
 191              		.loc 1 263 14 view .LVU55
 192 00be 0324     		movs	r4, #3
 193 00c0 6DE0     		b	.L3
 194              	.L29:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 9


 264:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     }
 265:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
 266:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 267:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Get Start Tick */
 268:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   tickstart = HAL_GetTick();
 195              		.loc 1 268 3 is_stmt 1 view .LVU56
 196              		.loc 1 268 15 is_stmt 0 view .LVU57
 197 00c2 FFF7FEFF 		bl	HAL_GetTick
 198              	.LVL15:
 199 00c6 0546     		mov	r5, r0
 200              	.LVL16:
 269:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 270:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Clear PLLON bit */
 271:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   CLEAR_BIT(RCC->CR, RCC_CR_PLL1ON);
 201              		.loc 1 271 3 is_stmt 1 view .LVU58
 202 00c8 374A     		ldr	r2, .L33+4
 203 00ca 1368     		ldr	r3, [r2]
 204 00cc 23F08073 		bic	r3, r3, #16777216
 205 00d0 1360     		str	r3, [r2]
 272:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 273:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Wait till PLL is disabled */
 274:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   while (READ_BIT(RCC->CR, RCC_CR_PLL1RDY) != 0U)
 206              		.loc 1 274 3 view .LVU59
 207              	.LVL17:
 208              	.L10:
 209              		.loc 1 274 44 view .LVU60
 210              		.loc 1 274 10 is_stmt 0 view .LVU61
 211 00d2 354B     		ldr	r3, .L33+4
 212 00d4 1B68     		ldr	r3, [r3]
 213              		.loc 1 274 44 view .LVU62
 214 00d6 13F0007F 		tst	r3, #33554432
 215 00da 06D0     		beq	.L30
 275:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
 276:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     if ((HAL_GetTick() - tickstart) > PLL_TIMEOUT_VALUE)
 216              		.loc 1 276 5 is_stmt 1 view .LVU63
 217              		.loc 1 276 10 is_stmt 0 view .LVU64
 218 00dc FFF7FEFF 		bl	HAL_GetTick
 219              	.LVL18:
 220              		.loc 1 276 24 discriminator 1 view .LVU65
 221 00e0 401B     		subs	r0, r0, r5
 222              		.loc 1 276 8 discriminator 1 view .LVU66
 223 00e2 0228     		cmp	r0, #2
 224 00e4 F5D9     		bls	.L10
 277:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 278:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       return HAL_TIMEOUT;
 225              		.loc 1 278 14 view .LVU67
 226 00e6 0324     		movs	r4, #3
 227 00e8 59E0     		b	.L3
 228              	.L30:
 279:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     }
 280:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
 281:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 282:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Get Start Tick */
 283:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   tickstart = HAL_GetTick();
 229              		.loc 1 283 3 is_stmt 1 view .LVU68
 230              		.loc 1 283 15 is_stmt 0 view .LVU69
 231 00ea FFF7FEFF 		bl	HAL_GetTick
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 10


 232              	.LVL19:
 233 00ee 0546     		mov	r5, r0
 234              	.LVL20:
 284:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 285:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Reset PLL2ON bit */
 286:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   CLEAR_BIT(RCC->CR, RCC_CR_PLL2ON);
 235              		.loc 1 286 3 is_stmt 1 view .LVU70
 236 00f0 2D4A     		ldr	r2, .L33+4
 237 00f2 1368     		ldr	r3, [r2]
 238 00f4 23F08063 		bic	r3, r3, #67108864
 239 00f8 1360     		str	r3, [r2]
 287:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 288:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Wait till PLL2 is disabled */
 289:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   while (READ_BIT(RCC->CR, RCC_CR_PLL2RDY) != 0U)
 240              		.loc 1 289 3 view .LVU71
 241              	.LVL21:
 242              	.L12:
 243              		.loc 1 289 44 view .LVU72
 244              		.loc 1 289 10 is_stmt 0 view .LVU73
 245 00fa 2B4B     		ldr	r3, .L33+4
 246 00fc 1B68     		ldr	r3, [r3]
 247              		.loc 1 289 44 view .LVU74
 248 00fe 13F0006F 		tst	r3, #134217728
 249 0102 06D0     		beq	.L31
 290:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
 291:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     if ((HAL_GetTick() - tickstart) > PLL_TIMEOUT_VALUE)
 250              		.loc 1 291 5 is_stmt 1 view .LVU75
 251              		.loc 1 291 10 is_stmt 0 view .LVU76
 252 0104 FFF7FEFF 		bl	HAL_GetTick
 253              	.LVL22:
 254              		.loc 1 291 24 discriminator 1 view .LVU77
 255 0108 401B     		subs	r0, r0, r5
 256              		.loc 1 291 8 discriminator 1 view .LVU78
 257 010a 0228     		cmp	r0, #2
 258 010c F5D9     		bls	.L12
 292:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 293:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       return HAL_TIMEOUT;
 259              		.loc 1 293 14 view .LVU79
 260 010e 0324     		movs	r4, #3
 261 0110 45E0     		b	.L3
 262              	.L31:
 294:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     }
 295:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
 296:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 297:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Get Start Tick */
 298:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   tickstart = HAL_GetTick();
 263              		.loc 1 298 3 is_stmt 1 view .LVU80
 264              		.loc 1 298 15 is_stmt 0 view .LVU81
 265 0112 FFF7FEFF 		bl	HAL_GetTick
 266              	.LVL23:
 267 0116 0546     		mov	r5, r0
 268              	.LVL24:
 299:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 300:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Reset PLL3 bit */
 301:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   CLEAR_BIT(RCC->CR, RCC_CR_PLL3ON);
 269              		.loc 1 301 3 is_stmt 1 view .LVU82
 270 0118 234A     		ldr	r2, .L33+4
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 11


 271 011a 1368     		ldr	r3, [r2]
 272 011c 23F08053 		bic	r3, r3, #268435456
 273 0120 1360     		str	r3, [r2]
 302:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 303:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Wait till PLL3 is disabled */
 304:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   while (READ_BIT(RCC->CR, RCC_CR_PLL3RDY) != 0U)
 274              		.loc 1 304 3 view .LVU83
 275              	.LVL25:
 276              	.L14:
 277              		.loc 1 304 44 view .LVU84
 278              		.loc 1 304 10 is_stmt 0 view .LVU85
 279 0122 214B     		ldr	r3, .L33+4
 280 0124 1B68     		ldr	r3, [r3]
 281              		.loc 1 304 44 view .LVU86
 282 0126 13F0005F 		tst	r3, #536870912
 283 012a 06D0     		beq	.L32
 305:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
 306:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     if ((HAL_GetTick() - tickstart) > PLL_TIMEOUT_VALUE)
 284              		.loc 1 306 5 is_stmt 1 view .LVU87
 285              		.loc 1 306 10 is_stmt 0 view .LVU88
 286 012c FFF7FEFF 		bl	HAL_GetTick
 287              	.LVL26:
 288              		.loc 1 306 24 discriminator 1 view .LVU89
 289 0130 401B     		subs	r0, r0, r5
 290              		.loc 1 306 8 discriminator 1 view .LVU90
 291 0132 0228     		cmp	r0, #2
 292 0134 F5D9     		bls	.L14
 307:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 308:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       return HAL_TIMEOUT;
 293              		.loc 1 308 14 view .LVU91
 294 0136 0324     		movs	r4, #3
 295 0138 31E0     		b	.L3
 296              	.L32:
 309:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     }
 310:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
 311:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 312:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #if defined(RCC_D1CFGR_HPRE)
 313:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Reset D1CFGR register */
 314:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   CLEAR_REG(RCC->D1CFGR);
 297              		.loc 1 314 3 is_stmt 1 view .LVU92
 298 013a 1B4B     		ldr	r3, .L33+4
 299 013c 0022     		movs	r2, #0
 300 013e 9A61     		str	r2, [r3, #24]
 315:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 316:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Reset D2CFGR register */
 317:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   CLEAR_REG(RCC->D2CFGR);
 301              		.loc 1 317 3 view .LVU93
 302 0140 DA61     		str	r2, [r3, #28]
 318:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 319:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Reset D3CFGR register */
 320:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   CLEAR_REG(RCC->D3CFGR);
 303              		.loc 1 320 3 view .LVU94
 304 0142 1A62     		str	r2, [r3, #32]
 321:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #else
 322:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Reset CDCFGR1 register */
 323:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   CLEAR_REG(RCC->CDCFGR1);
 324:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 12


 325:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Reset CDCFGR2 register */
 326:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   CLEAR_REG(RCC->CDCFGR2);
 327:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 328:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Reset SRDCFGR register */
 329:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   CLEAR_REG(RCC->SRDCFGR);
 330:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #endif
 331:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 332:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Reset PLLCKSELR register to default value */
 333:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   RCC->PLLCKSELR = RCC_PLLCKSELR_DIVM1_5 | RCC_PLLCKSELR_DIVM2_5 | RCC_PLLCKSELR_DIVM3_5;
 305              		.loc 1 333 3 view .LVU95
 306              		.loc 1 333 18 is_stmt 0 view .LVU96
 307 0144 1E49     		ldr	r1, .L33+28
 308 0146 9962     		str	r1, [r3, #40]
 334:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 335:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Reset PLLCFGR register to default value */
 336:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   WRITE_REG(RCC->PLLCFGR, 0x01FF0000U);
 309              		.loc 1 336 3 is_stmt 1 view .LVU97
 310 0148 1E49     		ldr	r1, .L33+32
 311 014a D962     		str	r1, [r3, #44]
 337:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 338:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Reset PLL1DIVR register to default value */
 339:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   WRITE_REG(RCC->PLL1DIVR, 0x01010280U);
 312              		.loc 1 339 3 view .LVU98
 313 014c 1E49     		ldr	r1, .L33+36
 314 014e 1963     		str	r1, [r3, #48]
 340:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 341:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Reset PLL1FRACR register */
 342:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   CLEAR_REG(RCC->PLL1FRACR);
 315              		.loc 1 342 3 view .LVU99
 316 0150 5A63     		str	r2, [r3, #52]
 343:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 344:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Reset PLL2DIVR register to default value */
 345:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   WRITE_REG(RCC->PLL2DIVR, 0x01010280U);
 317              		.loc 1 345 3 view .LVU100
 318 0152 9963     		str	r1, [r3, #56]
 346:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 347:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Reset PLL2FRACR register */
 348:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   CLEAR_REG(RCC->PLL2FRACR);
 319              		.loc 1 348 3 view .LVU101
 320 0154 DA63     		str	r2, [r3, #60]
 349:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 350:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Reset PLL3DIVR register to default value */
 351:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   WRITE_REG(RCC->PLL3DIVR, 0x01010280U);
 321              		.loc 1 351 3 view .LVU102
 322 0156 1964     		str	r1, [r3, #64]
 352:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 353:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Reset PLL3FRACR register */
 354:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   CLEAR_REG(RCC->PLL3FRACR);
 323              		.loc 1 354 3 view .LVU103
 324 0158 5A64     		str	r2, [r3, #68]
 355:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 356:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #if defined(RCC_CR_HSEEXT)
 357:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Reset HSEEXT  */
 358:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   CLEAR_BIT(RCC->CR, RCC_CR_HSEEXT);
 359:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #endif /* RCC_CR_HSEEXT */
 360:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 361:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Reset HSEBYP bit */
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 13


 362:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   CLEAR_BIT(RCC->CR, RCC_CR_HSEBYP);
 325              		.loc 1 362 3 view .LVU104
 326 015a 1968     		ldr	r1, [r3]
 327 015c 21F48021 		bic	r1, r1, #262144
 328 0160 1960     		str	r1, [r3]
 363:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 364:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Disable all interrupts */
 365:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   CLEAR_REG(RCC->CIER);
 329              		.loc 1 365 3 view .LVU105
 330 0162 1A66     		str	r2, [r3, #96]
 366:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 367:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Clear all interrupts flags */
 368:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   WRITE_REG(RCC->CICR, 0xFFFFFFFFU);
 331              		.loc 1 368 3 view .LVU106
 332 0164 4FF0FF32 		mov	r2, #-1
 333 0168 9A66     		str	r2, [r3, #104]
 369:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 370:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Reset all RSR flags */
 371:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   SET_BIT(RCC->RSR, RCC_RSR_RMVF);
 334              		.loc 1 371 3 view .LVU107
 335 016a D3F8D020 		ldr	r2, [r3, #208]
 336 016e 42F48032 		orr	r2, r2, #65536
 337 0172 C3F8D020 		str	r2, [r3, #208]
 372:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 373:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Decreasing the number of wait states because of lower CPU frequency */
 374:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   if (FLASH_LATENCY_DEFAULT  < __HAL_FLASH_GET_LATENCY())
 338              		.loc 1 374 3 view .LVU108
 339              		.loc 1 374 32 is_stmt 0 view .LVU109
 340 0176 0B4B     		ldr	r3, .L33
 341 0178 1B68     		ldr	r3, [r3]
 342              		.loc 1 374 6 view .LVU110
 343 017a 13F0080F 		tst	r3, #8
 344 017e 0ED0     		beq	.L3
 375:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
 376:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     /* Program the new number of wait states to the LATENCY bits in the FLASH_ACR register */
 377:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     __HAL_FLASH_SET_LATENCY(FLASH_LATENCY_DEFAULT);
 345              		.loc 1 377 5 is_stmt 1 view .LVU111
 346 0180 084A     		ldr	r2, .L33
 347 0182 1368     		ldr	r3, [r2]
 348 0184 23F00F03 		bic	r3, r3, #15
 349 0188 43F00703 		orr	r3, r3, #7
 350 018c 1360     		str	r3, [r2]
 378:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 379:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     /* Check that the new number of wait states is taken into account to access the Flash
 380:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     memory by reading the FLASH_ACR register */
 381:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     if (__HAL_FLASH_GET_LATENCY() != FLASH_LATENCY_DEFAULT)
 351              		.loc 1 381 5 view .LVU112
 352              		.loc 1 381 9 is_stmt 0 view .LVU113
 353 018e 1368     		ldr	r3, [r2]
 354 0190 03F00F03 		and	r3, r3, #15
 355              		.loc 1 381 8 view .LVU114
 356 0194 072B     		cmp	r3, #7
 357 0196 02D0     		beq	.L3
 382:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 383:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       return HAL_ERROR;
 358              		.loc 1 383 14 view .LVU115
 359 0198 0124     		movs	r4, #1
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 14


 360 019a 00E0     		b	.L3
 361              	.LVL27:
 362              	.L16:
 202:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     }
 363              		.loc 1 202 14 view .LVU116
 364 019c 0124     		movs	r4, #1
 365              	.L3:
 384:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     }
 385:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 386:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
 387:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 388:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   return HAL_OK;
 389:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** }
 366              		.loc 1 389 1 view .LVU117
 367 019e 2046     		mov	r0, r4
 368 01a0 38BD     		pop	{r3, r4, r5, pc}
 369              	.L34:
 370 01a2 00BF     		.align	2
 371              	.L33:
 372 01a4 00200052 		.word	1375739904
 373 01a8 00440258 		.word	1*********
 374 01ac 0090D003 		.word	64000000
 375 01b0 00000000 		.word	SystemCoreClock
 376 01b4 00000000 		.word	SystemD2Clock
 377 01b8 00000000 		.word	uwTickPrio
 378 01bc 45EDF6FF 		.word	-594619
 379 01c0 00020202 		.word	33686016
 380 01c4 0000FF01 		.word	33488896
 381 01c8 80020101 		.word	16843392
 382              		.cfi_endproc
 383              	.LFE144:
 385              		.section	.text.HAL_RCC_OscConfig,"ax",%progbits
 386              		.align	1
 387              		.weak	HAL_RCC_OscConfig
 388              		.syntax unified
 389              		.thumb
 390              		.thumb_func
 392              	HAL_RCC_OscConfig:
 393              	.LVL28:
 394              	.LFB145:
 390:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 391:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** /**
 392:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @brief  Initializes the RCC Oscillators according to the specified parameters in the
 393:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *         RCC_OscInitTypeDef.
 394:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @param  RCC_OscInitStruct: pointer to an RCC_OscInitTypeDef structure that
 395:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *         contains the configuration information for the RCC Oscillators.
 396:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @note   The PLL is not disabled when used as system clock.
 397:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @note   Transitions LSE Bypass to LSE On and LSE On to LSE Bypass are not
 398:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *         supported by this function. User should request a transition to LSE Off
 399:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *         first and then LSE On or LSE Bypass.
 400:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @note   Transition HSE Bypass to HSE On and HSE On to HSE Bypass are not
 401:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *         supported by this function. User should request a transition to HSE Off
 402:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *         first and then HSE On or HSE Bypass.
 403:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @retval HAL status
 404:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   */
 405:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** __weak HAL_StatusTypeDef HAL_RCC_OscConfig(RCC_OscInitTypeDef  *RCC_OscInitStruct)
 406:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** {
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 15


 395              		.loc 1 406 1 is_stmt 1 view -0
 396              		.cfi_startproc
 397              		@ args = 0, pretend = 0, frame = 0
 398              		@ frame_needed = 0, uses_anonymous_args = 0
 407:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   uint32_t tickstart;
 399              		.loc 1 407 3 view .LVU119
 408:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   uint32_t temp1_pllckcfg, temp2_pllckcfg;
 400              		.loc 1 408 3 view .LVU120
 409:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 410:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Check Null pointer */
 411:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   if (RCC_OscInitStruct == NULL)
 401              		.loc 1 411 3 view .LVU121
 402              		.loc 1 411 6 is_stmt 0 view .LVU122
 403 0000 0028     		cmp	r0, #0
 404 0002 00F0D582 		beq	.L102
 406:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   uint32_t tickstart;
 405              		.loc 1 406 1 view .LVU123
 406 0006 38B5     		push	{r3, r4, r5, lr}
 407              	.LCFI1:
 408              		.cfi_def_cfa_offset 16
 409              		.cfi_offset 3, -16
 410              		.cfi_offset 4, -12
 411              		.cfi_offset 5, -8
 412              		.cfi_offset 14, -4
 413 0008 0446     		mov	r4, r0
 412:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
 413:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     return HAL_ERROR;
 414:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
 415:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 416:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Check the parameters */
 417:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   assert_param(IS_RCC_OSCILLATORTYPE(RCC_OscInitStruct->OscillatorType));
 414              		.loc 1 417 3 is_stmt 1 view .LVU124
 418:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /*------------------------------- HSE Configuration ------------------------*/
 419:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   if (((RCC_OscInitStruct->OscillatorType) & RCC_OSCILLATORTYPE_HSE) == RCC_OSCILLATORTYPE_HSE)
 415              		.loc 1 419 3 view .LVU125
 416              		.loc 1 419 26 is_stmt 0 view .LVU126
 417 000a 0368     		ldr	r3, [r0]
 418              		.loc 1 419 6 view .LVU127
 419 000c 13F0010F 		tst	r3, #1
 420 0010 25D0     		beq	.L37
 421              	.LBB2:
 420:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
 421:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     /* Check the parameters */
 422:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     assert_param(IS_RCC_HSE(RCC_OscInitStruct->HSEState));
 422              		.loc 1 422 5 is_stmt 1 view .LVU128
 423:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 424:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     const uint32_t temp_sysclksrc = __HAL_RCC_GET_SYSCLK_SOURCE();
 423              		.loc 1 424 5 view .LVU129
 424              		.loc 1 424 37 is_stmt 0 view .LVU130
 425 0012 A04A     		ldr	r2, .L154
 426 0014 1369     		ldr	r3, [r2, #16]
 427              		.loc 1 424 20 view .LVU131
 428 0016 03F03803 		and	r3, r3, #56
 429              	.LVL29:
 425:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     const uint32_t temp_pllckselr = RCC->PLLCKSELR;
 430              		.loc 1 425 5 is_stmt 1 view .LVU132
 431              		.loc 1 425 20 is_stmt 0 view .LVU133
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 16


 432 001a 926A     		ldr	r2, [r2, #40]
 433              	.LVL30:
 426:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     /* When the HSE is used as system clock or clock source for PLL in these cases HSE will not dis
 427:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     if ((temp_sysclksrc == RCC_CFGR_SWS_HSE) || ((temp_sysclksrc == RCC_CFGR_SWS_PLL1) && ((temp_pl
 434              		.loc 1 427 5 is_stmt 1 view .LVU134
 435              		.loc 1 427 8 is_stmt 0 view .LVU135
 436 001c 102B     		cmp	r3, #16
 437 001e 15D0     		beq	.L38
 438              		.loc 1 427 46 discriminator 1 view .LVU136
 439 0020 182B     		cmp	r3, #24
 440 0022 0FD0     		beq	.L136
 441              	.LVL31:
 442              	.L39:
 428:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 429:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       if ((__HAL_RCC_GET_FLAG(RCC_FLAG_HSERDY) != 0U) && (RCC_OscInitStruct->HSEState == RCC_HSE_OF
 430:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
 431:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         return HAL_ERROR;
 432:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       }
 433:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     }
 434:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     else
 435:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 436:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       /* Set the new HSE configuration ---------------------------------------*/
 437:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       __HAL_RCC_HSE_CONFIG(RCC_OscInitStruct->HSEState);
 443              		.loc 1 437 7 is_stmt 1 view .LVU137
 444              		.loc 1 437 7 view .LVU138
 445 0024 6368     		ldr	r3, [r4, #4]
 446              	.LVL32:
 447              		.loc 1 437 7 is_stmt 0 view .LVU139
 448 0026 B3F5803F 		cmp	r3, #65536
 449 002a 3FD0     		beq	.L137
 450              		.loc 1 437 7 is_stmt 1 discriminator 2 view .LVU140
 451 002c 002B     		cmp	r3, #0
 452 002e 53D1     		bne	.L42
 453              		.loc 1 437 7 discriminator 4 view .LVU141
 454 0030 984B     		ldr	r3, .L154
 455 0032 1A68     		ldr	r2, [r3]
 456 0034 22F48032 		bic	r2, r2, #65536
 457 0038 1A60     		str	r2, [r3]
 458              		.loc 1 437 7 discriminator 4 view .LVU142
 459 003a 1A68     		ldr	r2, [r3]
 460 003c 22F48022 		bic	r2, r2, #262144
 461 0040 1A60     		str	r2, [r3]
 462 0042 38E0     		b	.L41
 463              	.LVL33:
 464              	.L136:
 427:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 465              		.loc 1 427 108 is_stmt 0 discriminator 2 view .LVU143
 466 0044 02F00302 		and	r2, r2, #3
 467              	.LVL34:
 427:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 468              		.loc 1 427 88 discriminator 2 view .LVU144
 469 0048 022A     		cmp	r2, #2
 470 004a EBD1     		bne	.L39
 471              	.L38:
 429:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
 472              		.loc 1 429 7 is_stmt 1 view .LVU145
 429:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 17


 473              		.loc 1 429 12 is_stmt 0 view .LVU146
 474 004c 914B     		ldr	r3, .L154
 475              	.LVL35:
 429:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
 476              		.loc 1 429 12 view .LVU147
 477 004e 1B68     		ldr	r3, [r3]
 429:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
 478              		.loc 1 429 10 view .LVU148
 479 0050 13F4003F 		tst	r3, #131072
 480 0054 03D0     		beq	.L37
 429:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
 481              		.loc 1 429 76 discriminator 1 view .LVU149
 482 0056 6368     		ldr	r3, [r4, #4]
 429:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
 483              		.loc 1 429 55 discriminator 1 view .LVU150
 484 0058 002B     		cmp	r3, #0
 485 005a 00F0AB82 		beq	.L138
 486              	.LVL36:
 487              	.L37:
 429:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
 488              		.loc 1 429 55 discriminator 1 view .LVU151
 489              	.LBE2:
 438:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 439:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       /* Check the HSE State */
 440:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       if (RCC_OscInitStruct->HSEState != RCC_HSE_OFF)
 441:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
 442:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         /* Get Start Tick*/
 443:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         tickstart = HAL_GetTick();
 444:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 445:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         /* Wait till HSE is ready */
 446:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         while (__HAL_RCC_GET_FLAG(RCC_FLAG_HSERDY) == 0U)
 447:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         {
 448:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           if ((uint32_t)(HAL_GetTick() - tickstart) > HSE_TIMEOUT_VALUE)
 449:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           {
 450:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****             return HAL_TIMEOUT;
 451:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           }
 452:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         }
 453:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       }
 454:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       else
 455:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
 456:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         /* Get Start Tick*/
 457:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         tickstart = HAL_GetTick();
 458:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 459:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         /* Wait till HSE is disabled */
 460:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         while (__HAL_RCC_GET_FLAG(RCC_FLAG_HSERDY) != 0U)
 461:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         {
 462:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           if ((uint32_t)(HAL_GetTick() - tickstart) > HSE_TIMEOUT_VALUE)
 463:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           {
 464:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****             return HAL_TIMEOUT;
 465:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           }
 466:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         }
 467:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       }
 468:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     }
 469:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
 470:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /*----------------------------- HSI Configuration --------------------------*/
 471:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   if (((RCC_OscInitStruct->OscillatorType) & RCC_OSCILLATORTYPE_HSI) == RCC_OSCILLATORTYPE_HSI)
 490              		.loc 1 471 3 is_stmt 1 view .LVU152
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 18


 491              		.loc 1 471 26 is_stmt 0 view .LVU153
 492 005e 2368     		ldr	r3, [r4]
 493              		.loc 1 471 6 view .LVU154
 494 0060 13F0020F 		tst	r3, #2
 495 0064 00F08880 		beq	.L49
 496              	.LBB3:
 472:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
 473:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     /* Check the parameters */
 474:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     assert_param(IS_RCC_HSI(RCC_OscInitStruct->HSIState));
 497              		.loc 1 474 5 is_stmt 1 view .LVU155
 475:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     assert_param(IS_RCC_HSICALIBRATION_VALUE(RCC_OscInitStruct->HSICalibrationValue));
 498              		.loc 1 475 5 view .LVU156
 476:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 477:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     /* When the HSI is used as system clock it will not be disabled */
 478:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     const uint32_t temp_sysclksrc = __HAL_RCC_GET_SYSCLK_SOURCE();
 499              		.loc 1 478 5 view .LVU157
 500              		.loc 1 478 37 is_stmt 0 view .LVU158
 501 0068 8A4A     		ldr	r2, .L154
 502 006a 1369     		ldr	r3, [r2, #16]
 503              	.LVL37:
 479:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     const uint32_t temp_pllckselr = RCC->PLLCKSELR;
 504              		.loc 1 479 5 is_stmt 1 view .LVU159
 505              		.loc 1 479 20 is_stmt 0 view .LVU160
 506 006c 926A     		ldr	r2, [r2, #40]
 507              	.LVL38:
 480:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     if ((temp_sysclksrc == RCC_CFGR_SWS_HSI) || ((temp_sysclksrc == RCC_CFGR_SWS_PLL1) && ((temp_pl
 508              		.loc 1 480 5 is_stmt 1 view .LVU161
 509              		.loc 1 480 8 is_stmt 0 view .LVU162
 510 006e 13F03803 		ands	r3, r3, #56
 511              	.LVL39:
 512              		.loc 1 480 8 view .LVU163
 513 0072 5AD0     		beq	.L50
 514              		.loc 1 480 46 discriminator 1 view .LVU164
 515 0074 182B     		cmp	r3, #24
 516 0076 55D0     		beq	.L139
 517              	.L51:
 481:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 482:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       /* When HSI is used as system clock it will not be disabled */
 483:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       if ((__HAL_RCC_GET_FLAG(RCC_FLAG_HSIRDY) != 0U) && (RCC_OscInitStruct->HSIState == RCC_HSI_OF
 484:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
 485:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         return HAL_ERROR;
 486:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       }
 487:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       /* Otherwise, only HSI division and calibration are allowed */
 488:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       else
 489:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
 490:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         /* Enable the Internal High Speed oscillator (HSI, HSIDIV2, HSIDIV4, or HSIDIV8) */
 491:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         __HAL_RCC_HSI_CONFIG(RCC_OscInitStruct->HSIState);
 492:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 493:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         /* Get Start Tick*/
 494:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         tickstart = HAL_GetTick();
 495:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 496:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         /* Wait till HSI is ready */
 497:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         while (__HAL_RCC_GET_FLAG(RCC_FLAG_HSIRDY) == 0U)
 498:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         {
 499:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           if ((uint32_t)(HAL_GetTick() - tickstart) > HSI_TIMEOUT_VALUE)
 500:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           {
 501:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****             return HAL_TIMEOUT;
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 19


 502:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           }
 503:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         }
 504:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         /* Adjusts the Internal High Speed oscillator (HSI) calibration value.*/
 505:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         __HAL_RCC_HSI_CALIBRATIONVALUE_ADJUST(RCC_OscInitStruct->HSICalibrationValue);
 506:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       }
 507:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     }
 508:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 509:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     else
 510:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 511:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       /* Check the HSI State */
 512:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       if ((RCC_OscInitStruct->HSIState) != RCC_HSI_OFF)
 518              		.loc 1 512 7 is_stmt 1 view .LVU165
 519              		.loc 1 512 29 is_stmt 0 view .LVU166
 520 0078 E368     		ldr	r3, [r4, #12]
 521              	.LVL40:
 522              		.loc 1 512 10 view .LVU167
 523 007a 002B     		cmp	r3, #0
 524 007c 00F0A980 		beq	.L55
 513:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
 514:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         /* Enable the Internal High Speed oscillator (HSI, HSIDIV2,HSIDIV4, or HSIDIV8) */
 515:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         __HAL_RCC_HSI_CONFIG(RCC_OscInitStruct->HSIState);
 525              		.loc 1 515 9 is_stmt 1 view .LVU168
 526 0080 8449     		ldr	r1, .L154
 527 0082 0A68     		ldr	r2, [r1]
 528              	.LVL41:
 529              		.loc 1 515 9 is_stmt 0 view .LVU169
 530 0084 22F01902 		bic	r2, r2, #25
 531 0088 1343     		orrs	r3, r3, r2
 532 008a 0B60     		str	r3, [r1]
 516:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 517:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         /* Get Start Tick*/
 518:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         tickstart = HAL_GetTick();
 533              		.loc 1 518 9 is_stmt 1 view .LVU170
 534              		.loc 1 518 21 is_stmt 0 view .LVU171
 535 008c FFF7FEFF 		bl	HAL_GetTick
 536              	.LVL42:
 537 0090 0546     		mov	r5, r0
 538              	.LVL43:
 519:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 520:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         /* Wait till HSI is ready */
 521:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         while (__HAL_RCC_GET_FLAG(RCC_FLAG_HSIRDY) == 0U)
 539              		.loc 1 521 9 is_stmt 1 view .LVU172
 540              	.L56:
 541              		.loc 1 521 52 view .LVU173
 542              		.loc 1 521 16 is_stmt 0 view .LVU174
 543 0092 804B     		ldr	r3, .L154
 544 0094 1B68     		ldr	r3, [r3]
 545              		.loc 1 521 52 view .LVU175
 546 0096 13F0040F 		tst	r3, #4
 547 009a 40F09180 		bne	.L140
 522:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         {
 523:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           if ((HAL_GetTick() - tickstart) > HSI_TIMEOUT_VALUE)
 548              		.loc 1 523 11 is_stmt 1 view .LVU176
 549              		.loc 1 523 16 is_stmt 0 view .LVU177
 550 009e FFF7FEFF 		bl	HAL_GetTick
 551              	.LVL44:
 552              		.loc 1 523 30 discriminator 1 view .LVU178
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 20


 553 00a2 401B     		subs	r0, r0, r5
 554              		.loc 1 523 14 discriminator 1 view .LVU179
 555 00a4 0228     		cmp	r0, #2
 556 00a6 F4D9     		bls	.L56
 524:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           {
 525:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****             return HAL_TIMEOUT;
 557              		.loc 1 525 20 view .LVU180
 558 00a8 0320     		movs	r0, #3
 559 00aa 8AE2     		b	.L36
 560              	.LVL45:
 561              	.L137:
 562              		.loc 1 525 20 view .LVU181
 563              	.LBE3:
 564              	.LBB4:
 437:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 565              		.loc 1 437 7 is_stmt 1 discriminator 1 view .LVU182
 566 00ac 794A     		ldr	r2, .L154
 567 00ae 1368     		ldr	r3, [r2]
 568 00b0 43F48033 		orr	r3, r3, #65536
 569 00b4 1360     		str	r3, [r2]
 570              	.L41:
 437:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 571              		.loc 1 437 7 discriminator 10 view .LVU183
 440:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
 572              		.loc 1 440 7 view .LVU184
 440:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
 573              		.loc 1 440 28 is_stmt 0 view .LVU185
 574 00b6 6368     		ldr	r3, [r4, #4]
 440:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
 575              		.loc 1 440 10 view .LVU186
 576 00b8 2BB3     		cbz	r3, .L44
 443:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 577              		.loc 1 443 9 is_stmt 1 view .LVU187
 443:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 578              		.loc 1 443 21 is_stmt 0 view .LVU188
 579 00ba FFF7FEFF 		bl	HAL_GetTick
 580              	.LVL46:
 443:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 581              		.loc 1 443 21 view .LVU189
 582 00be 0546     		mov	r5, r0
 583              	.LVL47:
 446:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         {
 584              		.loc 1 446 9 is_stmt 1 view .LVU190
 585              	.L45:
 446:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         {
 586              		.loc 1 446 52 view .LVU191
 446:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         {
 587              		.loc 1 446 16 is_stmt 0 view .LVU192
 588 00c0 744B     		ldr	r3, .L154
 589 00c2 1B68     		ldr	r3, [r3]
 446:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         {
 590              		.loc 1 446 52 view .LVU193
 591 00c4 13F4003F 		tst	r3, #131072
 592 00c8 C9D1     		bne	.L37
 448:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           {
 593              		.loc 1 448 11 is_stmt 1 view .LVU194
 448:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           {
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 21


 594              		.loc 1 448 26 is_stmt 0 view .LVU195
 595 00ca FFF7FEFF 		bl	HAL_GetTick
 596              	.LVL48:
 448:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           {
 597              		.loc 1 448 40 discriminator 1 view .LVU196
 598 00ce 401B     		subs	r0, r0, r5
 448:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           {
 599              		.loc 1 448 14 discriminator 1 view .LVU197
 600 00d0 6428     		cmp	r0, #100
 601 00d2 F5D9     		bls	.L45
 450:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           }
 602              		.loc 1 450 20 view .LVU198
 603 00d4 0320     		movs	r0, #3
 604 00d6 74E2     		b	.L36
 605              	.LVL49:
 606              	.L42:
 437:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 607              		.loc 1 437 7 is_stmt 1 discriminator 5 view .LVU199
 608 00d8 B3F5A02F 		cmp	r3, #327680
 609 00dc 09D0     		beq	.L141
 437:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 610              		.loc 1 437 7 discriminator 8 view .LVU200
 611 00de 6D4B     		ldr	r3, .L154
 612 00e0 1A68     		ldr	r2, [r3]
 613 00e2 22F48032 		bic	r2, r2, #65536
 614 00e6 1A60     		str	r2, [r3]
 437:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 615              		.loc 1 437 7 discriminator 8 view .LVU201
 616 00e8 1A68     		ldr	r2, [r3]
 617 00ea 22F48022 		bic	r2, r2, #262144
 618 00ee 1A60     		str	r2, [r3]
 619 00f0 E1E7     		b	.L41
 620              	.L141:
 437:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 621              		.loc 1 437 7 discriminator 7 view .LVU202
 622 00f2 684B     		ldr	r3, .L154
 623 00f4 1A68     		ldr	r2, [r3]
 624 00f6 42F48022 		orr	r2, r2, #262144
 625 00fa 1A60     		str	r2, [r3]
 437:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 626              		.loc 1 437 7 discriminator 7 view .LVU203
 627 00fc 1A68     		ldr	r2, [r3]
 628 00fe 42F48032 		orr	r2, r2, #65536
 629 0102 1A60     		str	r2, [r3]
 630 0104 D7E7     		b	.L41
 631              	.L44:
 457:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 632              		.loc 1 457 9 view .LVU204
 457:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 633              		.loc 1 457 21 is_stmt 0 view .LVU205
 634 0106 FFF7FEFF 		bl	HAL_GetTick
 635              	.LVL50:
 457:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 636              		.loc 1 457 21 view .LVU206
 637 010a 0546     		mov	r5, r0
 638              	.LVL51:
 460:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         {
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 22


 639              		.loc 1 460 9 is_stmt 1 view .LVU207
 640              	.L47:
 460:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         {
 641              		.loc 1 460 52 view .LVU208
 460:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         {
 642              		.loc 1 460 16 is_stmt 0 view .LVU209
 643 010c 614B     		ldr	r3, .L154
 644 010e 1B68     		ldr	r3, [r3]
 460:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         {
 645              		.loc 1 460 52 view .LVU210
 646 0110 13F4003F 		tst	r3, #131072
 647 0114 A3D0     		beq	.L37
 462:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           {
 648              		.loc 1 462 11 is_stmt 1 view .LVU211
 462:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           {
 649              		.loc 1 462 26 is_stmt 0 view .LVU212
 650 0116 FFF7FEFF 		bl	HAL_GetTick
 651              	.LVL52:
 462:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           {
 652              		.loc 1 462 40 discriminator 1 view .LVU213
 653 011a 401B     		subs	r0, r0, r5
 462:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           {
 654              		.loc 1 462 14 discriminator 1 view .LVU214
 655 011c 6428     		cmp	r0, #100
 656 011e F5D9     		bls	.L47
 464:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           }
 657              		.loc 1 464 20 view .LVU215
 658 0120 0320     		movs	r0, #3
 659 0122 4EE2     		b	.L36
 660              	.LVL53:
 661              	.L139:
 464:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           }
 662              		.loc 1 464 20 view .LVU216
 663              	.LBE4:
 664              	.LBB5:
 480:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 665              		.loc 1 480 88 discriminator 2 view .LVU217
 666 0124 12F0030F 		tst	r2, #3
 667 0128 A6D1     		bne	.L51
 668              	.L50:
 483:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
 669              		.loc 1 483 7 is_stmt 1 view .LVU218
 483:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
 670              		.loc 1 483 12 is_stmt 0 view .LVU219
 671 012a 5A4B     		ldr	r3, .L154
 672              	.LVL54:
 483:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
 673              		.loc 1 483 12 view .LVU220
 674 012c 1B68     		ldr	r3, [r3]
 483:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
 675              		.loc 1 483 10 view .LVU221
 676 012e 13F0040F 		tst	r3, #4
 677 0132 03D0     		beq	.L52
 483:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
 678              		.loc 1 483 76 discriminator 1 view .LVU222
 679 0134 E368     		ldr	r3, [r4, #12]
 483:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 23


 680              		.loc 1 483 55 discriminator 1 view .LVU223
 681 0136 002B     		cmp	r3, #0
 682 0138 00F03E82 		beq	.L106
 683              	.L52:
 491:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 684              		.loc 1 491 9 is_stmt 1 view .LVU224
 685 013c 554A     		ldr	r2, .L154
 686              	.LVL55:
 491:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 687              		.loc 1 491 9 is_stmt 0 view .LVU225
 688 013e 1368     		ldr	r3, [r2]
 689 0140 23F01903 		bic	r3, r3, #25
 690 0144 E168     		ldr	r1, [r4, #12]
 691 0146 0B43     		orrs	r3, r3, r1
 692 0148 1360     		str	r3, [r2]
 494:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 693              		.loc 1 494 9 is_stmt 1 view .LVU226
 494:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 694              		.loc 1 494 21 is_stmt 0 view .LVU227
 695 014a FFF7FEFF 		bl	HAL_GetTick
 696              	.LVL56:
 697 014e 0546     		mov	r5, r0
 698              	.LVL57:
 497:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         {
 699              		.loc 1 497 9 is_stmt 1 view .LVU228
 700              	.L53:
 497:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         {
 701              		.loc 1 497 52 view .LVU229
 497:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         {
 702              		.loc 1 497 16 is_stmt 0 view .LVU230
 703 0150 504B     		ldr	r3, .L154
 704 0152 1B68     		ldr	r3, [r3]
 497:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         {
 705              		.loc 1 497 52 view .LVU231
 706 0154 13F0040F 		tst	r3, #4
 707 0158 06D1     		bne	.L142
 499:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           {
 708              		.loc 1 499 11 is_stmt 1 view .LVU232
 499:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           {
 709              		.loc 1 499 26 is_stmt 0 view .LVU233
 710 015a FFF7FEFF 		bl	HAL_GetTick
 711              	.LVL58:
 499:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           {
 712              		.loc 1 499 40 discriminator 1 view .LVU234
 713 015e 401B     		subs	r0, r0, r5
 499:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           {
 714              		.loc 1 499 14 discriminator 1 view .LVU235
 715 0160 0228     		cmp	r0, #2
 716 0162 F5D9     		bls	.L53
 501:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           }
 717              		.loc 1 501 20 view .LVU236
 718 0164 0320     		movs	r0, #3
 719 0166 2CE2     		b	.L36
 720              	.L142:
 505:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       }
 721              		.loc 1 505 9 is_stmt 1 view .LVU237
 722 0168 4A4A     		ldr	r2, .L154
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 24


 723 016a 5368     		ldr	r3, [r2, #4]
 724 016c 23F0FE43 		bic	r3, r3, #2130706432
 725 0170 2169     		ldr	r1, [r4, #16]
 726 0172 43EA0163 		orr	r3, r3, r1, lsl #24
 727 0176 5360     		str	r3, [r2, #4]
 505:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       }
 728              		.loc 1 505 86 view .LVU238
 729              	.LVL59:
 730              	.L49:
 505:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       }
 731              		.loc 1 505 86 is_stmt 0 view .LVU239
 732              	.LBE5:
 526:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           }
 527:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         }
 528:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 529:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         /* Adjusts the Internal High Speed oscillator (HSI) calibration value.*/
 530:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         __HAL_RCC_HSI_CALIBRATIONVALUE_ADJUST(RCC_OscInitStruct->HSICalibrationValue);
 531:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       }
 532:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       else
 533:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
 534:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         /* Disable the Internal High Speed oscillator (HSI). */
 535:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         __HAL_RCC_HSI_DISABLE();
 536:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 537:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         /* Get Start Tick*/
 538:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         tickstart = HAL_GetTick();
 539:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 540:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         /* Wait till HSI is disabled */
 541:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         while (__HAL_RCC_GET_FLAG(RCC_FLAG_HSIRDY) != 0U)
 542:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         {
 543:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           if ((HAL_GetTick() - tickstart) > HSI_TIMEOUT_VALUE)
 544:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           {
 545:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****             return HAL_TIMEOUT;
 546:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           }
 547:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         }
 548:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       }
 549:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     }
 550:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
 551:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /*----------------------------- CSI Configuration --------------------------*/
 552:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   if (((RCC_OscInitStruct->OscillatorType) & RCC_OSCILLATORTYPE_CSI) == RCC_OSCILLATORTYPE_CSI)
 733              		.loc 1 552 3 is_stmt 1 view .LVU240
 734              		.loc 1 552 26 is_stmt 0 view .LVU241
 735 0178 2368     		ldr	r3, [r4]
 736              		.loc 1 552 6 view .LVU242
 737 017a 13F0100F 		tst	r3, #16
 738 017e 51D0     		beq	.L60
 739              	.LBB6:
 553:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
 554:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     /* Check the parameters */
 555:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     assert_param(IS_RCC_CSI(RCC_OscInitStruct->CSIState));
 740              		.loc 1 555 5 is_stmt 1 view .LVU243
 556:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     assert_param(IS_RCC_CSICALIBRATION_VALUE(RCC_OscInitStruct->CSICalibrationValue));
 741              		.loc 1 556 5 view .LVU244
 557:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 558:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     /* When the CSI is used as system clock it will not disabled */
 559:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     const uint32_t temp_sysclksrc = __HAL_RCC_GET_SYSCLK_SOURCE();
 742              		.loc 1 559 5 view .LVU245
 743              		.loc 1 559 37 is_stmt 0 view .LVU246
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 25


 744 0180 444A     		ldr	r2, .L154
 745 0182 1369     		ldr	r3, [r2, #16]
 746              		.loc 1 559 20 view .LVU247
 747 0184 03F03803 		and	r3, r3, #56
 748              	.LVL60:
 560:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     const uint32_t temp_pllckselr = RCC->PLLCKSELR;
 749              		.loc 1 560 5 is_stmt 1 view .LVU248
 750              		.loc 1 560 20 is_stmt 0 view .LVU249
 751 0188 926A     		ldr	r2, [r2, #40]
 752              	.LVL61:
 561:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     if ((temp_sysclksrc == RCC_CFGR_SWS_CSI) || ((temp_sysclksrc == RCC_CFGR_SWS_PLL1) && ((temp_pl
 753              		.loc 1 561 5 is_stmt 1 view .LVU250
 754              		.loc 1 561 8 is_stmt 0 view .LVU251
 755 018a 082B     		cmp	r3, #8
 756 018c 39D0     		beq	.L61
 757              		.loc 1 561 46 discriminator 1 view .LVU252
 758 018e 182B     		cmp	r3, #24
 759 0190 33D0     		beq	.L143
 760              	.LVL62:
 761              	.L62:
 562:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 563:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       /* When CSI is used as system clock it will not disabled */
 564:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       if ((__HAL_RCC_GET_FLAG(RCC_FLAG_CSIRDY) != 0U) && (RCC_OscInitStruct->CSIState != RCC_CSI_ON
 565:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
 566:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         return HAL_ERROR;
 567:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       }
 568:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       /* Otherwise, just the calibration is allowed */
 569:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       else
 570:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
 571:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         /* Adjusts the Internal High Speed oscillator (CSI) calibration value.*/
 572:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         __HAL_RCC_CSI_CALIBRATIONVALUE_ADJUST(RCC_OscInitStruct->CSICalibrationValue);
 573:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       }
 574:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     }
 575:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     else
 576:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 577:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       /* Check the CSI State */
 578:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       if ((RCC_OscInitStruct->CSIState) != RCC_CSI_OFF)
 762              		.loc 1 578 7 is_stmt 1 view .LVU253
 763              		.loc 1 578 29 is_stmt 0 view .LVU254
 764 0192 E369     		ldr	r3, [r4, #28]
 765              	.LVL63:
 766              		.loc 1 578 10 view .LVU255
 767 0194 002B     		cmp	r3, #0
 768 0196 69D0     		beq	.L64
 579:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
 580:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         /* Enable the Internal High Speed oscillator (CSI). */
 581:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         __HAL_RCC_CSI_ENABLE();
 769              		.loc 1 581 9 is_stmt 1 view .LVU256
 770 0198 3E4A     		ldr	r2, .L154
 771 019a 1368     		ldr	r3, [r2]
 772 019c 43F08003 		orr	r3, r3, #128
 773 01a0 1360     		str	r3, [r2]
 582:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 583:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         /* Get Start Tick*/
 584:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         tickstart = HAL_GetTick();
 774              		.loc 1 584 9 view .LVU257
 775              		.loc 1 584 21 is_stmt 0 view .LVU258
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 26


 776 01a2 FFF7FEFF 		bl	HAL_GetTick
 777              	.LVL64:
 778 01a6 0546     		mov	r5, r0
 779              	.LVL65:
 585:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 586:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         /* Wait till CSI is ready */
 587:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         while (__HAL_RCC_GET_FLAG(RCC_FLAG_CSIRDY) == 0U)
 780              		.loc 1 587 9 is_stmt 1 view .LVU259
 781              	.L65:
 782              		.loc 1 587 52 view .LVU260
 783              		.loc 1 587 16 is_stmt 0 view .LVU261
 784 01a8 3A4B     		ldr	r3, .L154
 785 01aa 1B68     		ldr	r3, [r3]
 786              		.loc 1 587 52 view .LVU262
 787 01ac 13F4807F 		tst	r3, #256
 788 01b0 53D1     		bne	.L144
 588:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         {
 589:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           if ((HAL_GetTick() - tickstart) > CSI_TIMEOUT_VALUE)
 789              		.loc 1 589 11 is_stmt 1 view .LVU263
 790              		.loc 1 589 16 is_stmt 0 view .LVU264
 791 01b2 FFF7FEFF 		bl	HAL_GetTick
 792              	.LVL66:
 793              		.loc 1 589 30 discriminator 1 view .LVU265
 794 01b6 401B     		subs	r0, r0, r5
 795              		.loc 1 589 14 discriminator 1 view .LVU266
 796 01b8 0228     		cmp	r0, #2
 797 01ba F5D9     		bls	.L65
 590:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           {
 591:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****             return HAL_TIMEOUT;
 798              		.loc 1 591 20 view .LVU267
 799 01bc 0320     		movs	r0, #3
 800 01be 00E2     		b	.L36
 801              	.LVL67:
 802              	.L140:
 803              		.loc 1 591 20 view .LVU268
 804              	.LBE6:
 805              	.LBB7:
 530:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       }
 806              		.loc 1 530 9 is_stmt 1 view .LVU269
 807 01c0 344A     		ldr	r2, .L154
 808 01c2 5368     		ldr	r3, [r2, #4]
 809 01c4 23F0FE43 		bic	r3, r3, #2130706432
 810 01c8 2169     		ldr	r1, [r4, #16]
 811 01ca 43EA0163 		orr	r3, r3, r1, lsl #24
 812 01ce 5360     		str	r3, [r2, #4]
 530:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       }
 813              		.loc 1 530 86 view .LVU270
 814 01d0 D2E7     		b	.L49
 815              	.LVL68:
 816              	.L55:
 535:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 817              		.loc 1 535 9 view .LVU271
 818 01d2 304A     		ldr	r2, .L154
 819              	.LVL69:
 535:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 820              		.loc 1 535 9 is_stmt 0 view .LVU272
 821 01d4 1368     		ldr	r3, [r2]
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 27


 822 01d6 23F00103 		bic	r3, r3, #1
 823 01da 1360     		str	r3, [r2]
 538:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 824              		.loc 1 538 9 is_stmt 1 view .LVU273
 538:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 825              		.loc 1 538 21 is_stmt 0 view .LVU274
 826 01dc FFF7FEFF 		bl	HAL_GetTick
 827              	.LVL70:
 828 01e0 0546     		mov	r5, r0
 829              	.LVL71:
 541:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         {
 830              		.loc 1 541 9 is_stmt 1 view .LVU275
 831              	.L58:
 541:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         {
 832              		.loc 1 541 52 view .LVU276
 541:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         {
 833              		.loc 1 541 16 is_stmt 0 view .LVU277
 834 01e2 2C4B     		ldr	r3, .L154
 835 01e4 1B68     		ldr	r3, [r3]
 541:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         {
 836              		.loc 1 541 52 view .LVU278
 837 01e6 13F0040F 		tst	r3, #4
 838 01ea C5D0     		beq	.L49
 543:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           {
 839              		.loc 1 543 11 is_stmt 1 view .LVU279
 543:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           {
 840              		.loc 1 543 16 is_stmt 0 view .LVU280
 841 01ec FFF7FEFF 		bl	HAL_GetTick
 842              	.LVL72:
 543:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           {
 843              		.loc 1 543 30 discriminator 1 view .LVU281
 844 01f0 401B     		subs	r0, r0, r5
 543:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           {
 845              		.loc 1 543 14 discriminator 1 view .LVU282
 846 01f2 0228     		cmp	r0, #2
 847 01f4 F5D9     		bls	.L58
 545:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           }
 848              		.loc 1 545 20 view .LVU283
 849 01f6 0320     		movs	r0, #3
 850 01f8 E3E1     		b	.L36
 851              	.LVL73:
 852              	.L143:
 545:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           }
 853              		.loc 1 545 20 view .LVU284
 854              	.LBE7:
 855              	.LBB8:
 561:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 856              		.loc 1 561 108 discriminator 2 view .LVU285
 857 01fa 02F00302 		and	r2, r2, #3
 858              	.LVL74:
 561:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 859              		.loc 1 561 88 discriminator 2 view .LVU286
 860 01fe 012A     		cmp	r2, #1
 861 0200 C7D1     		bne	.L62
 862              	.L61:
 564:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
 863              		.loc 1 564 7 is_stmt 1 view .LVU287
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 28


 564:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
 864              		.loc 1 564 12 is_stmt 0 view .LVU288
 865 0202 244B     		ldr	r3, .L154
 866              	.LVL75:
 564:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
 867              		.loc 1 564 12 view .LVU289
 868 0204 1B68     		ldr	r3, [r3]
 564:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
 869              		.loc 1 564 10 view .LVU290
 870 0206 13F4807F 		tst	r3, #256
 871 020a 03D0     		beq	.L63
 564:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
 872              		.loc 1 564 76 discriminator 1 view .LVU291
 873 020c E369     		ldr	r3, [r4, #28]
 564:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
 874              		.loc 1 564 55 discriminator 1 view .LVU292
 875 020e 802B     		cmp	r3, #128
 876 0210 40F0D481 		bne	.L110
 877              	.L63:
 572:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       }
 878              		.loc 1 572 9 is_stmt 1 view .LVU293
 572:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       }
 879              		.loc 1 572 9 view .LVU294
 880 0214 1F4A     		ldr	r2, .L154
 881 0216 D368     		ldr	r3, [r2, #12]
 882 0218 23F07C53 		bic	r3, r3, #1056964608
 883 021c 216A     		ldr	r1, [r4, #32]
 884 021e 43EA0163 		orr	r3, r3, r1, lsl #24
 885 0222 D360     		str	r3, [r2, #12]
 572:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       }
 886              		.loc 1 572 9 view .LVU295
 887              	.LVL76:
 888              	.L60:
 572:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       }
 889              		.loc 1 572 9 is_stmt 0 view .LVU296
 890              	.LBE8:
 592:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           }
 593:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         }
 594:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 595:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         /* Adjusts the Internal High Speed oscillator (CSI) calibration value.*/
 596:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         __HAL_RCC_CSI_CALIBRATIONVALUE_ADJUST(RCC_OscInitStruct->CSICalibrationValue);
 597:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       }
 598:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       else
 599:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
 600:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         /* Disable the Internal High Speed oscillator (CSI). */
 601:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         __HAL_RCC_CSI_DISABLE();
 602:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 603:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         /* Get Start Tick*/
 604:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         tickstart = HAL_GetTick();
 605:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 606:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         /* Wait till CSI is disabled */
 607:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         while (__HAL_RCC_GET_FLAG(RCC_FLAG_CSIRDY) != 0U)
 608:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         {
 609:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           if ((HAL_GetTick() - tickstart) > CSI_TIMEOUT_VALUE)
 610:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           {
 611:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****             return HAL_TIMEOUT;
 612:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           }
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 29


 613:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         }
 614:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       }
 615:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     }
 616:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
 617:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /*------------------------------ LSI Configuration -------------------------*/
 618:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   if (((RCC_OscInitStruct->OscillatorType) & RCC_OSCILLATORTYPE_LSI) == RCC_OSCILLATORTYPE_LSI)
 891              		.loc 1 618 3 is_stmt 1 view .LVU297
 892              		.loc 1 618 26 is_stmt 0 view .LVU298
 893 0224 2368     		ldr	r3, [r4]
 894              		.loc 1 618 6 view .LVU299
 895 0226 13F0080F 		tst	r3, #8
 896 022a 49D0     		beq	.L69
 619:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
 620:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     /* Check the parameters */
 621:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     assert_param(IS_RCC_LSI(RCC_OscInitStruct->LSIState));
 897              		.loc 1 621 5 is_stmt 1 view .LVU300
 622:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 623:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     /* Check the LSI State */
 624:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     if ((RCC_OscInitStruct->LSIState) != RCC_LSI_OFF)
 898              		.loc 1 624 5 view .LVU301
 899              		.loc 1 624 27 is_stmt 0 view .LVU302
 900 022c 6369     		ldr	r3, [r4, #20]
 901              		.loc 1 624 8 view .LVU303
 902 022e 002B     		cmp	r3, #0
 903 0230 32D0     		beq	.L70
 625:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 626:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       /* Enable the Internal Low Speed oscillator (LSI). */
 627:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       __HAL_RCC_LSI_ENABLE();
 904              		.loc 1 627 7 is_stmt 1 view .LVU304
 905 0232 184A     		ldr	r2, .L154
 906 0234 536F     		ldr	r3, [r2, #116]
 907 0236 43F00103 		orr	r3, r3, #1
 908 023a 5367     		str	r3, [r2, #116]
 628:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 629:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       /* Get Start Tick*/
 630:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       tickstart = HAL_GetTick();
 909              		.loc 1 630 7 view .LVU305
 910              		.loc 1 630 19 is_stmt 0 view .LVU306
 911 023c FFF7FEFF 		bl	HAL_GetTick
 912              	.LVL77:
 913 0240 0546     		mov	r5, r0
 914              	.LVL78:
 631:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 632:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       /* Wait till LSI is ready */
 633:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       while (__HAL_RCC_GET_FLAG(RCC_FLAG_LSIRDY) == 0U)
 915              		.loc 1 633 7 is_stmt 1 view .LVU307
 916              	.L71:
 917              		.loc 1 633 50 view .LVU308
 918              		.loc 1 633 14 is_stmt 0 view .LVU309
 919 0242 144B     		ldr	r3, .L154
 920 0244 5B6F     		ldr	r3, [r3, #116]
 921              		.loc 1 633 50 view .LVU310
 922 0246 13F0020F 		tst	r3, #2
 923 024a 39D1     		bne	.L69
 634:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
 635:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         if ((HAL_GetTick() - tickstart) > LSI_TIMEOUT_VALUE)
 924              		.loc 1 635 9 is_stmt 1 view .LVU311
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 30


 925              		.loc 1 635 14 is_stmt 0 view .LVU312
 926 024c FFF7FEFF 		bl	HAL_GetTick
 927              	.LVL79:
 928              		.loc 1 635 28 discriminator 1 view .LVU313
 929 0250 401B     		subs	r0, r0, r5
 930              		.loc 1 635 12 discriminator 1 view .LVU314
 931 0252 0228     		cmp	r0, #2
 932 0254 F5D9     		bls	.L71
 636:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         {
 637:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           return HAL_TIMEOUT;
 933              		.loc 1 637 18 view .LVU315
 934 0256 0320     		movs	r0, #3
 935 0258 B3E1     		b	.L36
 936              	.LVL80:
 937              	.L144:
 938              	.LBB9:
 596:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       }
 939              		.loc 1 596 9 is_stmt 1 view .LVU316
 596:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       }
 940              		.loc 1 596 9 view .LVU317
 941 025a 0E4A     		ldr	r2, .L154
 942 025c D368     		ldr	r3, [r2, #12]
 943 025e 23F07C53 		bic	r3, r3, #1056964608
 944 0262 216A     		ldr	r1, [r4, #32]
 945 0264 43EA0163 		orr	r3, r3, r1, lsl #24
 946 0268 D360     		str	r3, [r2, #12]
 596:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       }
 947              		.loc 1 596 9 view .LVU318
 948 026a DBE7     		b	.L60
 949              	.LVL81:
 950              	.L64:
 601:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 951              		.loc 1 601 9 view .LVU319
 952 026c 094A     		ldr	r2, .L154
 953 026e 1368     		ldr	r3, [r2]
 954 0270 23F08003 		bic	r3, r3, #128
 955 0274 1360     		str	r3, [r2]
 604:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 956              		.loc 1 604 9 view .LVU320
 604:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 957              		.loc 1 604 21 is_stmt 0 view .LVU321
 958 0276 FFF7FEFF 		bl	HAL_GetTick
 959              	.LVL82:
 960 027a 0546     		mov	r5, r0
 961              	.LVL83:
 607:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         {
 962              		.loc 1 607 9 is_stmt 1 view .LVU322
 963              	.L67:
 607:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         {
 964              		.loc 1 607 52 view .LVU323
 607:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         {
 965              		.loc 1 607 16 is_stmt 0 view .LVU324
 966 027c 054B     		ldr	r3, .L154
 967 027e 1B68     		ldr	r3, [r3]
 607:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         {
 968              		.loc 1 607 52 view .LVU325
 969 0280 13F4807F 		tst	r3, #256
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 31


 970 0284 CED0     		beq	.L60
 609:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           {
 971              		.loc 1 609 11 is_stmt 1 view .LVU326
 609:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           {
 972              		.loc 1 609 16 is_stmt 0 view .LVU327
 973 0286 FFF7FEFF 		bl	HAL_GetTick
 974              	.LVL84:
 609:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           {
 975              		.loc 1 609 30 discriminator 1 view .LVU328
 976 028a 401B     		subs	r0, r0, r5
 609:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           {
 977              		.loc 1 609 14 discriminator 1 view .LVU329
 978 028c 0228     		cmp	r0, #2
 979 028e F5D9     		bls	.L67
 611:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           }
 980              		.loc 1 611 20 view .LVU330
 981 0290 0320     		movs	r0, #3
 982 0292 96E1     		b	.L36
 983              	.L155:
 984              		.align	2
 985              	.L154:
 986 0294 00440258 		.word	1*********
 987              	.LVL85:
 988              	.L70:
 611:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           }
 989              		.loc 1 611 20 view .LVU331
 990              	.LBE9:
 638:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         }
 639:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       }
 640:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     }
 641:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     else
 642:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 643:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       /* Disable the Internal Low Speed oscillator (LSI). */
 644:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       __HAL_RCC_LSI_DISABLE();
 991              		.loc 1 644 7 is_stmt 1 view .LVU332
 992 0298 9D4A     		ldr	r2, .L156
 993 029a 536F     		ldr	r3, [r2, #116]
 994 029c 23F00103 		bic	r3, r3, #1
 995 02a0 5367     		str	r3, [r2, #116]
 645:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 646:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       /* Get Start Tick*/
 647:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       tickstart = HAL_GetTick();
 996              		.loc 1 647 7 view .LVU333
 997              		.loc 1 647 19 is_stmt 0 view .LVU334
 998 02a2 FFF7FEFF 		bl	HAL_GetTick
 999              	.LVL86:
 1000 02a6 0546     		mov	r5, r0
 1001              	.LVL87:
 648:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 649:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       /* Wait till LSI is ready */
 650:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       while (__HAL_RCC_GET_FLAG(RCC_FLAG_LSIRDY) != 0U)
 1002              		.loc 1 650 7 is_stmt 1 view .LVU335
 1003              	.L73:
 1004              		.loc 1 650 50 view .LVU336
 1005              		.loc 1 650 14 is_stmt 0 view .LVU337
 1006 02a8 994B     		ldr	r3, .L156
 1007 02aa 5B6F     		ldr	r3, [r3, #116]
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 32


 1008              		.loc 1 650 50 view .LVU338
 1009 02ac 13F0020F 		tst	r3, #2
 1010 02b0 06D0     		beq	.L69
 651:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
 652:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         if ((HAL_GetTick() - tickstart) > LSI_TIMEOUT_VALUE)
 1011              		.loc 1 652 9 is_stmt 1 view .LVU339
 1012              		.loc 1 652 14 is_stmt 0 view .LVU340
 1013 02b2 FFF7FEFF 		bl	HAL_GetTick
 1014              	.LVL88:
 1015              		.loc 1 652 28 discriminator 1 view .LVU341
 1016 02b6 401B     		subs	r0, r0, r5
 1017              		.loc 1 652 12 discriminator 1 view .LVU342
 1018 02b8 0228     		cmp	r0, #2
 1019 02ba F5D9     		bls	.L73
 653:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         {
 654:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           return HAL_TIMEOUT;
 1020              		.loc 1 654 18 view .LVU343
 1021 02bc 0320     		movs	r0, #3
 1022 02be 80E1     		b	.L36
 1023              	.LVL89:
 1024              	.L69:
 655:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         }
 656:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       }
 657:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     }
 658:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
 659:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 660:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /*------------------------------ HSI48 Configuration -------------------------*/
 661:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   if (((RCC_OscInitStruct->OscillatorType) & RCC_OSCILLATORTYPE_HSI48) == RCC_OSCILLATORTYPE_HSI48)
 1025              		.loc 1 661 3 is_stmt 1 view .LVU344
 1026              		.loc 1 661 26 is_stmt 0 view .LVU345
 1027 02c0 2368     		ldr	r3, [r4]
 1028              		.loc 1 661 6 view .LVU346
 1029 02c2 13F0200F 		tst	r3, #32
 1030 02c6 29D0     		beq	.L75
 662:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
 663:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     /* Check the parameters */
 664:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     assert_param(IS_RCC_HSI48(RCC_OscInitStruct->HSI48State));
 1031              		.loc 1 664 5 is_stmt 1 view .LVU347
 665:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 666:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     /* Check the HSI48 State */
 667:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     if ((RCC_OscInitStruct->HSI48State) != RCC_HSI48_OFF)
 1032              		.loc 1 667 5 view .LVU348
 1033              		.loc 1 667 27 is_stmt 0 view .LVU349
 1034 02c8 A369     		ldr	r3, [r4, #24]
 1035              		.loc 1 667 8 view .LVU350
 1036 02ca 9BB1     		cbz	r3, .L76
 668:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 669:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       /* Enable the Internal Low Speed oscillator (HSI48). */
 670:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       __HAL_RCC_HSI48_ENABLE();
 1037              		.loc 1 670 7 is_stmt 1 view .LVU351
 1038 02cc 904A     		ldr	r2, .L156
 1039 02ce 1368     		ldr	r3, [r2]
 1040 02d0 43F48053 		orr	r3, r3, #4096
 1041 02d4 1360     		str	r3, [r2]
 1042              		.loc 1 670 31 view .LVU352
 671:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 672:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       /* Get time-out */
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 33


 673:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       tickstart = HAL_GetTick();
 1043              		.loc 1 673 7 view .LVU353
 1044              		.loc 1 673 19 is_stmt 0 view .LVU354
 1045 02d6 FFF7FEFF 		bl	HAL_GetTick
 1046              	.LVL90:
 1047 02da 0546     		mov	r5, r0
 1048              	.LVL91:
 674:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 675:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       /* Wait till HSI48 is ready */
 676:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       while (__HAL_RCC_GET_FLAG(RCC_FLAG_HSI48RDY) == 0U)
 1049              		.loc 1 676 7 is_stmt 1 view .LVU355
 1050              	.L77:
 1051              		.loc 1 676 52 view .LVU356
 1052              		.loc 1 676 14 is_stmt 0 view .LVU357
 1053 02dc 8C4B     		ldr	r3, .L156
 1054 02de 1B68     		ldr	r3, [r3]
 1055              		.loc 1 676 52 view .LVU358
 1056 02e0 13F4005F 		tst	r3, #8192
 1057 02e4 1AD1     		bne	.L75
 677:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
 678:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         if ((HAL_GetTick() - tickstart) > HSI48_TIMEOUT_VALUE)
 1058              		.loc 1 678 9 is_stmt 1 view .LVU359
 1059              		.loc 1 678 14 is_stmt 0 view .LVU360
 1060 02e6 FFF7FEFF 		bl	HAL_GetTick
 1061              	.LVL92:
 1062              		.loc 1 678 28 discriminator 1 view .LVU361
 1063 02ea 401B     		subs	r0, r0, r5
 1064              		.loc 1 678 12 discriminator 1 view .LVU362
 1065 02ec 0228     		cmp	r0, #2
 1066 02ee F5D9     		bls	.L77
 679:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         {
 680:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           return HAL_TIMEOUT;
 1067              		.loc 1 680 18 view .LVU363
 1068 02f0 0320     		movs	r0, #3
 1069 02f2 66E1     		b	.L36
 1070              	.LVL93:
 1071              	.L76:
 681:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         }
 682:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       }
 683:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     }
 684:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     else
 685:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 686:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       /* Disable the Internal Low Speed oscillator (HSI48). */
 687:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       __HAL_RCC_HSI48_DISABLE();
 1072              		.loc 1 687 7 is_stmt 1 view .LVU364
 1073 02f4 864A     		ldr	r2, .L156
 1074 02f6 1368     		ldr	r3, [r2]
 1075 02f8 23F48053 		bic	r3, r3, #4096
 1076 02fc 1360     		str	r3, [r2]
 1077              		.loc 1 687 32 view .LVU365
 688:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 689:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       /* Get time-out */
 690:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       tickstart = HAL_GetTick();
 1078              		.loc 1 690 7 view .LVU366
 1079              		.loc 1 690 19 is_stmt 0 view .LVU367
 1080 02fe FFF7FEFF 		bl	HAL_GetTick
 1081              	.LVL94:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 34


 1082 0302 0546     		mov	r5, r0
 1083              	.LVL95:
 691:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 692:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       /* Wait till HSI48 is ready */
 693:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       while (__HAL_RCC_GET_FLAG(RCC_FLAG_HSI48RDY) != 0U)
 1084              		.loc 1 693 7 is_stmt 1 view .LVU368
 1085              	.L79:
 1086              		.loc 1 693 52 view .LVU369
 1087              		.loc 1 693 14 is_stmt 0 view .LVU370
 1088 0304 824B     		ldr	r3, .L156
 1089 0306 1B68     		ldr	r3, [r3]
 1090              		.loc 1 693 52 view .LVU371
 1091 0308 13F4005F 		tst	r3, #8192
 1092 030c 06D0     		beq	.L75
 694:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
 695:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         if ((HAL_GetTick() - tickstart) > HSI48_TIMEOUT_VALUE)
 1093              		.loc 1 695 9 is_stmt 1 view .LVU372
 1094              		.loc 1 695 14 is_stmt 0 view .LVU373
 1095 030e FFF7FEFF 		bl	HAL_GetTick
 1096              	.LVL96:
 1097              		.loc 1 695 28 discriminator 1 view .LVU374
 1098 0312 401B     		subs	r0, r0, r5
 1099              		.loc 1 695 12 discriminator 1 view .LVU375
 1100 0314 0228     		cmp	r0, #2
 1101 0316 F5D9     		bls	.L79
 696:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         {
 697:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           return HAL_TIMEOUT;
 1102              		.loc 1 697 18 view .LVU376
 1103 0318 0320     		movs	r0, #3
 1104 031a 52E1     		b	.L36
 1105              	.LVL97:
 1106              	.L75:
 698:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         }
 699:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       }
 700:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     }
 701:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
 702:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /*------------------------------ LSE Configuration -------------------------*/
 703:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   if (((RCC_OscInitStruct->OscillatorType) & RCC_OSCILLATORTYPE_LSE) == RCC_OSCILLATORTYPE_LSE)
 1107              		.loc 1 703 3 is_stmt 1 view .LVU377
 1108              		.loc 1 703 26 is_stmt 0 view .LVU378
 1109 031c 2368     		ldr	r3, [r4]
 1110              		.loc 1 703 6 view .LVU379
 1111 031e 13F0040F 		tst	r3, #4
 1112 0322 21D1     		bne	.L145
 1113              	.L81:
 704:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
 705:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     /* Check the parameters */
 706:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     assert_param(IS_RCC_LSE(RCC_OscInitStruct->LSEState));
 707:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 708:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     /* Enable write access to Backup domain */
 709:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     PWR->CR1 |= PWR_CR1_DBP;
 710:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 711:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     /* Wait for Backup domain Write protection disable */
 712:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     tickstart = HAL_GetTick();
 713:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 714:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     while ((PWR->CR1 & PWR_CR1_DBP) == 0U)
 715:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 35


 716:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       if ((HAL_GetTick() - tickstart) > RCC_DBP_TIMEOUT_VALUE)
 717:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
 718:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         return HAL_TIMEOUT;
 719:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       }
 720:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     }
 721:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 722:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     /* Set the new LSE configuration -----------------------------------------*/
 723:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     __HAL_RCC_LSE_CONFIG(RCC_OscInitStruct->LSEState);
 724:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     /* Check the LSE State */
 725:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     if ((RCC_OscInitStruct->LSEState) != RCC_LSE_OFF)
 726:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 727:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       /* Get Start Tick*/
 728:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       tickstart = HAL_GetTick();
 729:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 730:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       /* Wait till LSE is ready */
 731:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       while (__HAL_RCC_GET_FLAG(RCC_FLAG_LSERDY) == 0U)
 732:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
 733:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         if ((HAL_GetTick() - tickstart) > RCC_LSE_TIMEOUT_VALUE)
 734:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         {
 735:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           return HAL_TIMEOUT;
 736:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         }
 737:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       }
 738:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     }
 739:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     else
 740:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 741:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       /* Get Start Tick*/
 742:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       tickstart = HAL_GetTick();
 743:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 744:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       /* Wait till LSE is disabled */
 745:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       while (__HAL_RCC_GET_FLAG(RCC_FLAG_LSERDY) != 0U)
 746:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
 747:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         if ((HAL_GetTick() - tickstart) > RCC_LSE_TIMEOUT_VALUE)
 748:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         {
 749:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           return HAL_TIMEOUT;
 750:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         }
 751:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       }
 752:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     }
 753:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
 754:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /*-------------------------------- PLL Configuration -----------------------*/
 755:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Check the parameters */
 756:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   assert_param(IS_RCC_PLL(RCC_OscInitStruct->PLL.PLLState));
 1114              		.loc 1 756 3 is_stmt 1 view .LVU380
 757:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   if ((RCC_OscInitStruct->PLL.PLLState) != RCC_PLL_NONE)
 1115              		.loc 1 757 3 view .LVU381
 1116              		.loc 1 757 30 is_stmt 0 view .LVU382
 1117 0324 636A     		ldr	r3, [r4, #36]
 1118              		.loc 1 757 6 view .LVU383
 1119 0326 002B     		cmp	r3, #0
 1120 0328 00F04A81 		beq	.L120
 758:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
 759:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     /* Check if the PLL is used as system clock or not */
 760:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     if (__HAL_RCC_GET_SYSCLK_SOURCE() != RCC_CFGR_SWS_PLL1)
 1121              		.loc 1 760 5 is_stmt 1 view .LVU384
 1122              		.loc 1 760 9 is_stmt 0 view .LVU385
 1123 032c 784A     		ldr	r2, .L156
 1124 032e 1269     		ldr	r2, [r2, #16]
 1125 0330 02F03802 		and	r2, r2, #56
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 36


 1126              		.loc 1 760 8 view .LVU386
 1127 0334 182A     		cmp	r2, #24
 1128 0336 00F0F180 		beq	.L93
 761:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 762:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       if ((RCC_OscInitStruct->PLL.PLLState) == RCC_PLL_ON)
 1129              		.loc 1 762 7 is_stmt 1 view .LVU387
 1130              		.loc 1 762 10 is_stmt 0 view .LVU388
 1131 033a 022B     		cmp	r3, #2
 1132 033c 75D0     		beq	.L146
 763:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
 764:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         /* Check the parameters */
 765:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         assert_param(IS_RCC_PLLSOURCE(RCC_OscInitStruct->PLL.PLLSource));
 766:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         assert_param(IS_RCC_PLLRGE_VALUE(RCC_OscInitStruct->PLL.PLLRGE));
 767:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         assert_param(IS_RCC_PLLVCO_VALUE(RCC_OscInitStruct->PLL.PLLVCOSEL));
 768:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         assert_param(IS_RCC_PLLM_VALUE(RCC_OscInitStruct->PLL.PLLM));
 769:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         assert_param(IS_RCC_PLLN_VALUE(RCC_OscInitStruct->PLL.PLLN));
 770:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         assert_param(IS_RCC_PLLP_VALUE(RCC_OscInitStruct->PLL.PLLP));
 771:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         assert_param(IS_RCC_PLLQ_VALUE(RCC_OscInitStruct->PLL.PLLQ));
 772:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         assert_param(IS_RCC_PLLR_VALUE(RCC_OscInitStruct->PLL.PLLR));
 773:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         assert_param(IS_RCC_PLLFRACN_VALUE(RCC_OscInitStruct->PLL.PLLFRACN));
 774:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 775:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         /* Disable the main PLL. */
 776:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         __HAL_RCC_PLL_DISABLE();
 777:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 778:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         /* Get Start Tick*/
 779:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         tickstart = HAL_GetTick();
 780:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 781:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         /* Wait till PLL is disabled */
 782:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         while (__HAL_RCC_GET_FLAG(RCC_FLAG_PLLRDY) != 0U)
 783:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         {
 784:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           if ((HAL_GetTick() - tickstart) > PLL_TIMEOUT_VALUE)
 785:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           {
 786:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****             return HAL_TIMEOUT;
 787:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           }
 788:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         }
 789:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 790:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         /* Configure the main PLL clock source, multiplication and division factors. */
 791:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         __HAL_RCC_PLL_CONFIG(RCC_OscInitStruct->PLL.PLLSource,
 792:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****                              RCC_OscInitStruct->PLL.PLLM,
 793:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****                              RCC_OscInitStruct->PLL.PLLN,
 794:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****                              RCC_OscInitStruct->PLL.PLLP,
 795:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****                              RCC_OscInitStruct->PLL.PLLQ,
 796:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****                              RCC_OscInitStruct->PLL.PLLR);
 797:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 798:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         /* Disable PLLFRACN . */
 799:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         __HAL_RCC_PLLFRACN_DISABLE();
 800:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 801:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         /* Configure PLL PLL1FRACN */
 802:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         __HAL_RCC_PLLFRACN_CONFIG(RCC_OscInitStruct->PLL.PLLFRACN);
 803:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 804:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         /* Select PLL1 input reference frequency range: VCI */
 805:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         __HAL_RCC_PLL_VCIRANGE(RCC_OscInitStruct->PLL.PLLRGE) ;
 806:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 807:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         /* Select PLL1 output frequency range : VCO */
 808:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         __HAL_RCC_PLL_VCORANGE(RCC_OscInitStruct->PLL.PLLVCOSEL) ;
 809:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 810:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         /* Enable PLL System Clock output. */
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 37


 811:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         __HAL_RCC_PLLCLKOUT_ENABLE(RCC_PLL1_DIVP);
 812:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 813:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         /* Enable PLL1Q Clock output. */
 814:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         __HAL_RCC_PLLCLKOUT_ENABLE(RCC_PLL1_DIVQ);
 815:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 816:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         /* Enable PLL1R  Clock output. */
 817:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         __HAL_RCC_PLLCLKOUT_ENABLE(RCC_PLL1_DIVR);
 818:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 819:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         /* Enable PLL1FRACN . */
 820:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         __HAL_RCC_PLLFRACN_ENABLE();
 821:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 822:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         /* Enable the main PLL. */
 823:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         __HAL_RCC_PLL_ENABLE();
 824:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 825:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         /* Get Start Tick*/
 826:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         tickstart = HAL_GetTick();
 827:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 828:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         /* Wait till PLL is ready */
 829:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         while (__HAL_RCC_GET_FLAG(RCC_FLAG_PLLRDY) == 0U)
 830:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         {
 831:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           if ((HAL_GetTick() - tickstart) > PLL_TIMEOUT_VALUE)
 832:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           {
 833:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****             return HAL_TIMEOUT;
 834:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           }
 835:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         }
 836:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       }
 837:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       else
 838:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
 839:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         /* Disable the main PLL. */
 840:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         __HAL_RCC_PLL_DISABLE();
 1133              		.loc 1 840 9 is_stmt 1 view .LVU389
 1134 033e 744A     		ldr	r2, .L156
 1135 0340 1368     		ldr	r3, [r2]
 1136 0342 23F08073 		bic	r3, r3, #16777216
 1137 0346 1360     		str	r3, [r2]
 841:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 842:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         /* Get Start Tick*/
 843:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         tickstart = HAL_GetTick();
 1138              		.loc 1 843 9 view .LVU390
 1139              		.loc 1 843 21 is_stmt 0 view .LVU391
 1140 0348 FFF7FEFF 		bl	HAL_GetTick
 1141              	.LVL98:
 1142 034c 0446     		mov	r4, r0
 1143              	.LVL99:
 844:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 845:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         /* Wait till PLL is disabled */
 846:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         while (__HAL_RCC_GET_FLAG(RCC_FLAG_PLLRDY) != 0U)
 1144              		.loc 1 846 9 is_stmt 1 view .LVU392
 1145              	.L99:
 1146              		.loc 1 846 52 view .LVU393
 1147              		.loc 1 846 16 is_stmt 0 view .LVU394
 1148 034e 704B     		ldr	r3, .L156
 1149 0350 1B68     		ldr	r3, [r3]
 1150              		.loc 1 846 52 view .LVU395
 1151 0352 13F0007F 		tst	r3, #33554432
 1152 0356 00F0D880 		beq	.L147
 847:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         {
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 38


 848:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           if ((HAL_GetTick() - tickstart) > PLL_TIMEOUT_VALUE)
 1153              		.loc 1 848 11 is_stmt 1 view .LVU396
 1154              		.loc 1 848 16 is_stmt 0 view .LVU397
 1155 035a FFF7FEFF 		bl	HAL_GetTick
 1156              	.LVL100:
 1157              		.loc 1 848 30 discriminator 1 view .LVU398
 1158 035e 001B     		subs	r0, r0, r4
 1159              		.loc 1 848 14 discriminator 1 view .LVU399
 1160 0360 0228     		cmp	r0, #2
 1161 0362 F4D9     		bls	.L99
 849:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           {
 850:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****             return HAL_TIMEOUT;
 1162              		.loc 1 850 20 view .LVU400
 1163 0364 0320     		movs	r0, #3
 1164 0366 2CE1     		b	.L36
 1165              	.LVL101:
 1166              	.L145:
 706:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 1167              		.loc 1 706 5 is_stmt 1 view .LVU401
 709:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 1168              		.loc 1 709 5 view .LVU402
 709:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 1169              		.loc 1 709 8 is_stmt 0 view .LVU403
 1170 0368 6A4A     		ldr	r2, .L156+4
 1171 036a 1368     		ldr	r3, [r2]
 709:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 1172              		.loc 1 709 14 view .LVU404
 1173 036c 43F48073 		orr	r3, r3, #256
 1174 0370 1360     		str	r3, [r2]
 712:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 1175              		.loc 1 712 5 is_stmt 1 view .LVU405
 712:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 1176              		.loc 1 712 17 is_stmt 0 view .LVU406
 1177 0372 FFF7FEFF 		bl	HAL_GetTick
 1178              	.LVL102:
 1179 0376 0546     		mov	r5, r0
 1180              	.LVL103:
 714:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 1181              		.loc 1 714 5 is_stmt 1 view .LVU407
 1182              	.L82:
 714:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 1183              		.loc 1 714 37 view .LVU408
 714:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 1184              		.loc 1 714 16 is_stmt 0 view .LVU409
 1185 0378 664B     		ldr	r3, .L156+4
 1186 037a 1B68     		ldr	r3, [r3]
 714:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 1187              		.loc 1 714 37 view .LVU410
 1188 037c 13F4807F 		tst	r3, #256
 1189 0380 06D1     		bne	.L148
 716:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
 1190              		.loc 1 716 7 is_stmt 1 view .LVU411
 716:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
 1191              		.loc 1 716 12 is_stmt 0 view .LVU412
 1192 0382 FFF7FEFF 		bl	HAL_GetTick
 1193              	.LVL104:
 716:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 39


 1194              		.loc 1 716 26 discriminator 1 view .LVU413
 1195 0386 401B     		subs	r0, r0, r5
 716:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
 1196              		.loc 1 716 10 discriminator 1 view .LVU414
 1197 0388 6428     		cmp	r0, #100
 1198 038a F5D9     		bls	.L82
 718:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       }
 1199              		.loc 1 718 16 view .LVU415
 1200 038c 0320     		movs	r0, #3
 1201 038e 18E1     		b	.L36
 1202              	.L148:
 723:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     /* Check the LSE State */
 1203              		.loc 1 723 5 is_stmt 1 view .LVU416
 723:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     /* Check the LSE State */
 1204              		.loc 1 723 5 view .LVU417
 1205 0390 A368     		ldr	r3, [r4, #8]
 1206 0392 012B     		cmp	r3, #1
 1207 0394 0AD0     		beq	.L149
 723:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     /* Check the LSE State */
 1208              		.loc 1 723 5 discriminator 2 view .LVU418
 1209 0396 0BBB     		cbnz	r3, .L86
 723:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     /* Check the LSE State */
 1210              		.loc 1 723 5 discriminator 4 view .LVU419
 1211 0398 5D4B     		ldr	r3, .L156
 1212 039a 1A6F     		ldr	r2, [r3, #112]
 1213 039c 22F00102 		bic	r2, r2, #1
 1214 03a0 1A67     		str	r2, [r3, #112]
 723:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     /* Check the LSE State */
 1215              		.loc 1 723 5 discriminator 4 view .LVU420
 1216 03a2 1A6F     		ldr	r2, [r3, #112]
 1217 03a4 22F00402 		bic	r2, r2, #4
 1218 03a8 1A67     		str	r2, [r3, #112]
 1219 03aa 04E0     		b	.L85
 1220              	.L149:
 723:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     /* Check the LSE State */
 1221              		.loc 1 723 5 discriminator 1 view .LVU421
 1222 03ac 584A     		ldr	r2, .L156
 1223 03ae 136F     		ldr	r3, [r2, #112]
 1224 03b0 43F00103 		orr	r3, r3, #1
 1225 03b4 1367     		str	r3, [r2, #112]
 1226              	.L85:
 723:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     /* Check the LSE State */
 1227              		.loc 1 723 5 discriminator 10 view .LVU422
 725:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 1228              		.loc 1 725 5 view .LVU423
 725:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 1229              		.loc 1 725 27 is_stmt 0 view .LVU424
 1230 03b6 A368     		ldr	r3, [r4, #8]
 725:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 1231              		.loc 1 725 8 view .LVU425
 1232 03b8 33B3     		cbz	r3, .L88
 728:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 1233              		.loc 1 728 7 is_stmt 1 view .LVU426
 728:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 1234              		.loc 1 728 19 is_stmt 0 view .LVU427
 1235 03ba FFF7FEFF 		bl	HAL_GetTick
 1236              	.LVL105:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 40


 1237 03be 0546     		mov	r5, r0
 1238              	.LVL106:
 731:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
 1239              		.loc 1 731 7 is_stmt 1 view .LVU428
 1240              	.L89:
 731:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
 1241              		.loc 1 731 50 view .LVU429
 731:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
 1242              		.loc 1 731 14 is_stmt 0 view .LVU430
 1243 03c0 534B     		ldr	r3, .L156
 1244 03c2 1B6F     		ldr	r3, [r3, #112]
 731:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
 1245              		.loc 1 731 50 view .LVU431
 1246 03c4 13F0020F 		tst	r3, #2
 1247 03c8 ACD1     		bne	.L81
 733:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         {
 1248              		.loc 1 733 9 is_stmt 1 view .LVU432
 733:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         {
 1249              		.loc 1 733 14 is_stmt 0 view .LVU433
 1250 03ca FFF7FEFF 		bl	HAL_GetTick
 1251              	.LVL107:
 733:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         {
 1252              		.loc 1 733 28 discriminator 1 view .LVU434
 1253 03ce 401B     		subs	r0, r0, r5
 733:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         {
 1254              		.loc 1 733 12 discriminator 1 view .LVU435
 1255 03d0 41F28833 		movw	r3, #5000
 1256 03d4 9842     		cmp	r0, r3
 1257 03d6 F3D9     		bls	.L89
 735:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         }
 1258              		.loc 1 735 18 view .LVU436
 1259 03d8 0320     		movs	r0, #3
 1260 03da F2E0     		b	.L36
 1261              	.L86:
 723:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     /* Check the LSE State */
 1262              		.loc 1 723 5 is_stmt 1 discriminator 5 view .LVU437
 1263 03dc 052B     		cmp	r3, #5
 1264 03de 09D0     		beq	.L150
 723:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     /* Check the LSE State */
 1265              		.loc 1 723 5 discriminator 8 view .LVU438
 1266 03e0 4B4B     		ldr	r3, .L156
 1267 03e2 1A6F     		ldr	r2, [r3, #112]
 1268 03e4 22F00102 		bic	r2, r2, #1
 1269 03e8 1A67     		str	r2, [r3, #112]
 723:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     /* Check the LSE State */
 1270              		.loc 1 723 5 discriminator 8 view .LVU439
 1271 03ea 1A6F     		ldr	r2, [r3, #112]
 1272 03ec 22F00402 		bic	r2, r2, #4
 1273 03f0 1A67     		str	r2, [r3, #112]
 1274 03f2 E0E7     		b	.L85
 1275              	.L150:
 723:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     /* Check the LSE State */
 1276              		.loc 1 723 5 discriminator 7 view .LVU440
 1277 03f4 464B     		ldr	r3, .L156
 1278 03f6 1A6F     		ldr	r2, [r3, #112]
 1279 03f8 42F00402 		orr	r2, r2, #4
 1280 03fc 1A67     		str	r2, [r3, #112]
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 41


 723:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     /* Check the LSE State */
 1281              		.loc 1 723 5 discriminator 7 view .LVU441
 1282 03fe 1A6F     		ldr	r2, [r3, #112]
 1283 0400 42F00102 		orr	r2, r2, #1
 1284 0404 1A67     		str	r2, [r3, #112]
 1285 0406 D6E7     		b	.L85
 1286              	.L88:
 742:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 1287              		.loc 1 742 7 view .LVU442
 742:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 1288              		.loc 1 742 19 is_stmt 0 view .LVU443
 1289 0408 FFF7FEFF 		bl	HAL_GetTick
 1290              	.LVL108:
 1291 040c 0546     		mov	r5, r0
 1292              	.LVL109:
 745:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
 1293              		.loc 1 745 7 is_stmt 1 view .LVU444
 1294              	.L91:
 745:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
 1295              		.loc 1 745 50 view .LVU445
 745:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
 1296              		.loc 1 745 14 is_stmt 0 view .LVU446
 1297 040e 404B     		ldr	r3, .L156
 1298 0410 1B6F     		ldr	r3, [r3, #112]
 745:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
 1299              		.loc 1 745 50 view .LVU447
 1300 0412 13F0020F 		tst	r3, #2
 1301 0416 85D0     		beq	.L81
 747:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         {
 1302              		.loc 1 747 9 is_stmt 1 view .LVU448
 747:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         {
 1303              		.loc 1 747 14 is_stmt 0 view .LVU449
 1304 0418 FFF7FEFF 		bl	HAL_GetTick
 1305              	.LVL110:
 747:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         {
 1306              		.loc 1 747 28 discriminator 1 view .LVU450
 1307 041c 401B     		subs	r0, r0, r5
 747:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         {
 1308              		.loc 1 747 12 discriminator 1 view .LVU451
 1309 041e 41F28833 		movw	r3, #5000
 1310 0422 9842     		cmp	r0, r3
 1311 0424 F3D9     		bls	.L91
 749:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         }
 1312              		.loc 1 749 18 view .LVU452
 1313 0426 0320     		movs	r0, #3
 1314 0428 CBE0     		b	.L36
 1315              	.LVL111:
 1316              	.L146:
 765:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         assert_param(IS_RCC_PLLRGE_VALUE(RCC_OscInitStruct->PLL.PLLRGE));
 1317              		.loc 1 765 9 is_stmt 1 view .LVU453
 766:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         assert_param(IS_RCC_PLLVCO_VALUE(RCC_OscInitStruct->PLL.PLLVCOSEL));
 1318              		.loc 1 766 9 view .LVU454
 767:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         assert_param(IS_RCC_PLLM_VALUE(RCC_OscInitStruct->PLL.PLLM));
 1319              		.loc 1 767 9 view .LVU455
 768:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         assert_param(IS_RCC_PLLN_VALUE(RCC_OscInitStruct->PLL.PLLN));
 1320              		.loc 1 768 9 view .LVU456
 769:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         assert_param(IS_RCC_PLLP_VALUE(RCC_OscInitStruct->PLL.PLLP));
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 42


 1321              		.loc 1 769 9 view .LVU457
 770:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         assert_param(IS_RCC_PLLQ_VALUE(RCC_OscInitStruct->PLL.PLLQ));
 1322              		.loc 1 770 9 view .LVU458
 771:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         assert_param(IS_RCC_PLLR_VALUE(RCC_OscInitStruct->PLL.PLLR));
 1323              		.loc 1 771 9 view .LVU459
 772:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         assert_param(IS_RCC_PLLFRACN_VALUE(RCC_OscInitStruct->PLL.PLLFRACN));
 1324              		.loc 1 772 9 view .LVU460
 773:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 1325              		.loc 1 773 9 view .LVU461
 776:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 1326              		.loc 1 776 9 view .LVU462
 1327 042a 394A     		ldr	r2, .L156
 1328 042c 1368     		ldr	r3, [r2]
 1329 042e 23F08073 		bic	r3, r3, #16777216
 1330 0432 1360     		str	r3, [r2]
 779:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 1331              		.loc 1 779 9 view .LVU463
 779:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 1332              		.loc 1 779 21 is_stmt 0 view .LVU464
 1333 0434 FFF7FEFF 		bl	HAL_GetTick
 1334              	.LVL112:
 1335 0438 0546     		mov	r5, r0
 1336              	.LVL113:
 782:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         {
 1337              		.loc 1 782 9 is_stmt 1 view .LVU465
 1338              	.L95:
 782:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         {
 1339              		.loc 1 782 52 view .LVU466
 782:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         {
 1340              		.loc 1 782 16 is_stmt 0 view .LVU467
 1341 043a 354B     		ldr	r3, .L156
 1342 043c 1B68     		ldr	r3, [r3]
 782:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         {
 1343              		.loc 1 782 52 view .LVU468
 1344 043e 13F0007F 		tst	r3, #33554432
 1345 0442 06D0     		beq	.L151
 784:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           {
 1346              		.loc 1 784 11 is_stmt 1 view .LVU469
 784:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           {
 1347              		.loc 1 784 16 is_stmt 0 view .LVU470
 1348 0444 FFF7FEFF 		bl	HAL_GetTick
 1349              	.LVL114:
 784:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           {
 1350              		.loc 1 784 30 discriminator 1 view .LVU471
 1351 0448 401B     		subs	r0, r0, r5
 784:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           {
 1352              		.loc 1 784 14 discriminator 1 view .LVU472
 1353 044a 0228     		cmp	r0, #2
 1354 044c F5D9     		bls	.L95
 786:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           }
 1355              		.loc 1 786 20 view .LVU473
 1356 044e 0320     		movs	r0, #3
 1357 0450 B7E0     		b	.L36
 1358              	.L151:
 791:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****                              RCC_OscInitStruct->PLL.PLLM,
 1359              		.loc 1 791 9 is_stmt 1 view .LVU474
 791:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****                              RCC_OscInitStruct->PLL.PLLM,
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 43


 1360              		.loc 1 791 9 view .LVU475
 1361 0452 2F4B     		ldr	r3, .L156
 1362 0454 996A     		ldr	r1, [r3, #40]
 1363 0456 304A     		ldr	r2, .L156+8
 1364 0458 0A40     		ands	r2, r2, r1
 1365 045a A16A     		ldr	r1, [r4, #40]
 1366 045c E06A     		ldr	r0, [r4, #44]
 1367 045e 41EA0011 		orr	r1, r1, r0, lsl #4
 1368 0462 0A43     		orrs	r2, r2, r1
 1369 0464 9A62     		str	r2, [r3, #40]
 791:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****                              RCC_OscInitStruct->PLL.PLLM,
 1370              		.loc 1 791 9 view .LVU476
 1371 0466 226B     		ldr	r2, [r4, #48]
 1372 0468 013A     		subs	r2, r2, #1
 1373 046a C2F30802 		ubfx	r2, r2, #0, #9
 1374 046e 616B     		ldr	r1, [r4, #52]
 1375 0470 0139     		subs	r1, r1, #1
 1376 0472 4902     		lsls	r1, r1, #9
 1377 0474 89B2     		uxth	r1, r1
 1378 0476 0A43     		orrs	r2, r2, r1
 1379 0478 A16B     		ldr	r1, [r4, #56]
 1380 047a 0139     		subs	r1, r1, #1
 1381 047c 0904     		lsls	r1, r1, #16
 1382 047e 01F4FE01 		and	r1, r1, #8323072
 1383 0482 0A43     		orrs	r2, r2, r1
 1384 0484 E16B     		ldr	r1, [r4, #60]
 1385 0486 0139     		subs	r1, r1, #1
 1386 0488 0906     		lsls	r1, r1, #24
 1387 048a 01F0FE41 		and	r1, r1, #2130706432
 1388 048e 0A43     		orrs	r2, r2, r1
 1389 0490 1A63     		str	r2, [r3, #48]
 791:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****                              RCC_OscInitStruct->PLL.PLLM,
 1390              		.loc 1 791 9 view .LVU477
 799:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 1391              		.loc 1 799 9 view .LVU478
 1392 0492 DA6A     		ldr	r2, [r3, #44]
 1393 0494 22F00102 		bic	r2, r2, #1
 1394 0498 DA62     		str	r2, [r3, #44]
 802:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 1395              		.loc 1 802 9 view .LVU479
 1396 049a 5A6B     		ldr	r2, [r3, #52]
 1397 049c 6FF3CF02 		bfc	r2, #3, #13
 1398 04a0 A16C     		ldr	r1, [r4, #72]
 1399 04a2 42EAC102 		orr	r2, r2, r1, lsl #3
 1400 04a6 5A63     		str	r2, [r3, #52]
 805:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 1401              		.loc 1 805 9 view .LVU480
 1402 04a8 DA6A     		ldr	r2, [r3, #44]
 1403 04aa 22F00C02 		bic	r2, r2, #12
 1404 04ae 216C     		ldr	r1, [r4, #64]
 1405 04b0 0A43     		orrs	r2, r2, r1
 1406 04b2 DA62     		str	r2, [r3, #44]
 808:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 1407              		.loc 1 808 9 view .LVU481
 1408 04b4 DA6A     		ldr	r2, [r3, #44]
 1409 04b6 22F00202 		bic	r2, r2, #2
 1410 04ba 616C     		ldr	r1, [r4, #68]
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 44


 1411 04bc 0A43     		orrs	r2, r2, r1
 1412 04be DA62     		str	r2, [r3, #44]
 811:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 1413              		.loc 1 811 9 view .LVU482
 1414 04c0 DA6A     		ldr	r2, [r3, #44]
 1415 04c2 42F48032 		orr	r2, r2, #65536
 1416 04c6 DA62     		str	r2, [r3, #44]
 814:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 1417              		.loc 1 814 9 view .LVU483
 1418 04c8 DA6A     		ldr	r2, [r3, #44]
 1419 04ca 42F40032 		orr	r2, r2, #131072
 1420 04ce DA62     		str	r2, [r3, #44]
 817:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 1421              		.loc 1 817 9 view .LVU484
 1422 04d0 DA6A     		ldr	r2, [r3, #44]
 1423 04d2 42F48022 		orr	r2, r2, #262144
 1424 04d6 DA62     		str	r2, [r3, #44]
 820:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 1425              		.loc 1 820 9 view .LVU485
 1426 04d8 DA6A     		ldr	r2, [r3, #44]
 1427 04da 42F00102 		orr	r2, r2, #1
 1428 04de DA62     		str	r2, [r3, #44]
 823:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 1429              		.loc 1 823 9 view .LVU486
 1430 04e0 1A68     		ldr	r2, [r3]
 1431 04e2 42F08072 		orr	r2, r2, #16777216
 1432 04e6 1A60     		str	r2, [r3]
 826:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 1433              		.loc 1 826 9 view .LVU487
 826:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 1434              		.loc 1 826 21 is_stmt 0 view .LVU488
 1435 04e8 FFF7FEFF 		bl	HAL_GetTick
 1436              	.LVL115:
 1437 04ec 0446     		mov	r4, r0
 1438              	.LVL116:
 829:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         {
 1439              		.loc 1 829 9 is_stmt 1 view .LVU489
 1440              	.L97:
 829:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         {
 1441              		.loc 1 829 52 view .LVU490
 829:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         {
 1442              		.loc 1 829 16 is_stmt 0 view .LVU491
 1443 04ee 084B     		ldr	r3, .L156
 1444 04f0 1B68     		ldr	r3, [r3]
 829:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         {
 1445              		.loc 1 829 52 view .LVU492
 1446 04f2 13F0007F 		tst	r3, #33554432
 1447 04f6 06D1     		bne	.L152
 831:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           {
 1448              		.loc 1 831 11 is_stmt 1 view .LVU493
 831:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           {
 1449              		.loc 1 831 16 is_stmt 0 view .LVU494
 1450 04f8 FFF7FEFF 		bl	HAL_GetTick
 1451              	.LVL117:
 831:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           {
 1452              		.loc 1 831 30 discriminator 1 view .LVU495
 1453 04fc 001B     		subs	r0, r0, r4
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 45


 831:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           {
 1454              		.loc 1 831 14 discriminator 1 view .LVU496
 1455 04fe 0228     		cmp	r0, #2
 1456 0500 F5D9     		bls	.L97
 833:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           }
 1457              		.loc 1 833 20 view .LVU497
 1458 0502 0320     		movs	r0, #3
 1459 0504 5DE0     		b	.L36
 1460              	.L152:
 851:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           }
 852:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         }
 853:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       }
 854:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     }
 855:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     else
 856:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 857:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       /* Do not return HAL_ERROR if request repeats the current configuration */
 858:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       temp1_pllckcfg = RCC->PLLCKSELR;
 859:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       temp2_pllckcfg = RCC->PLL1DIVR;
 860:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       if (((RCC_OscInitStruct->PLL.PLLState) == RCC_PLL_OFF) ||
 861:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           (READ_BIT(temp1_pllckcfg, RCC_PLLCKSELR_PLLSRC) != RCC_OscInitStruct->PLL.PLLSource) ||
 862:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           ((READ_BIT(temp1_pllckcfg, RCC_PLLCKSELR_DIVM1) >> RCC_PLLCKSELR_DIVM1_Pos) != RCC_OscIni
 863:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           (READ_BIT(temp2_pllckcfg, RCC_PLL1DIVR_N1) != (RCC_OscInitStruct->PLL.PLLN - 1U)) ||
 864:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           ((READ_BIT(temp2_pllckcfg, RCC_PLL1DIVR_P1) >> RCC_PLL1DIVR_P1_Pos) != (RCC_OscInitStruct
 865:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           ((READ_BIT(temp2_pllckcfg, RCC_PLL1DIVR_Q1) >> RCC_PLL1DIVR_Q1_Pos) != (RCC_OscInitStruct
 866:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           ((READ_BIT(temp2_pllckcfg, RCC_PLL1DIVR_R1) >> RCC_PLL1DIVR_R1_Pos) != (RCC_OscInitStruct
 867:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
 868:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         return HAL_ERROR;
 869:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       }
 870:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       else
 871:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
 872:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         /* Check if only fractional part needs to be updated  */
 873:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         temp1_pllckcfg = ((RCC->PLL1FRACR & RCC_PLL1FRACR_FRACN1) >> RCC_PLL1FRACR_FRACN1_Pos);
 874:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         if (RCC_OscInitStruct->PLL.PLLFRACN != temp1_pllckcfg)
 875:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         {
 876:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           assert_param(IS_RCC_PLLFRACN_VALUE(RCC_OscInitStruct->PLL.PLLFRACN));
 877:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           /* Disable PLL1FRACEN */
 878:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           __HAL_RCC_PLLFRACN_DISABLE();
 879:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           /* Get Start Tick*/
 880:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           tickstart = HAL_GetTick();
 881:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           /* Wait at least 2 CK_REF (PLL input source divided by M) period to make sure next latche
 882:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           while ((HAL_GetTick() - tickstart) < PLL_FRAC_TIMEOUT_VALUE)
 883:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           {
 884:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           }
 885:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           /* Configure PLL1 PLL1FRACN */
 886:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           __HAL_RCC_PLLFRACN_CONFIG(RCC_OscInitStruct->PLL.PLLFRACN);
 887:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           /* Enable PLL1FRACEN to latch new value. */
 888:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           __HAL_RCC_PLLFRACN_ENABLE();
 889:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         }
 890:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       }
 891:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     }
 892:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
 893:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   return HAL_OK;
 1461              		.loc 1 893 10 view .LVU498
 1462 0506 0020     		movs	r0, #0
 1463 0508 5BE0     		b	.L36
 1464              	.L147:
 1465              		.loc 1 893 10 view .LVU499
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 46


 1466 050a 0020     		movs	r0, #0
 1467 050c 59E0     		b	.L36
 1468              	.L157:
 1469 050e 00BF     		.align	2
 1470              	.L156:
 1471 0510 00440258 		.word	1*********
 1472 0514 00480258 		.word	1476544512
 1473 0518 0CFCFFFF 		.word	-1012
 1474              	.LVL118:
 1475              	.L93:
 858:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       temp2_pllckcfg = RCC->PLL1DIVR;
 1476              		.loc 1 858 7 is_stmt 1 view .LVU500
 858:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       temp2_pllckcfg = RCC->PLL1DIVR;
 1477              		.loc 1 858 22 is_stmt 0 view .LVU501
 1478 051c 3049     		ldr	r1, .L158
 1479 051e 8A6A     		ldr	r2, [r1, #40]
 1480              	.LVL119:
 859:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       if (((RCC_OscInitStruct->PLL.PLLState) == RCC_PLL_OFF) ||
 1481              		.loc 1 859 7 is_stmt 1 view .LVU502
 859:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       if (((RCC_OscInitStruct->PLL.PLLState) == RCC_PLL_OFF) ||
 1482              		.loc 1 859 22 is_stmt 0 view .LVU503
 1483 0520 086B     		ldr	r0, [r1, #48]
 1484              	.LVL120:
 860:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           (READ_BIT(temp1_pllckcfg, RCC_PLLCKSELR_PLLSRC) != RCC_OscInitStruct->PLL.PLLSource) ||
 1485              		.loc 1 860 7 is_stmt 1 view .LVU504
 860:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           (READ_BIT(temp1_pllckcfg, RCC_PLLCKSELR_PLLSRC) != RCC_OscInitStruct->PLL.PLLSource) ||
 1486              		.loc 1 860 10 is_stmt 0 view .LVU505
 1487 0522 012B     		cmp	r3, #1
 1488 0524 4ED0     		beq	.L124
 861:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           ((READ_BIT(temp1_pllckcfg, RCC_PLLCKSELR_DIVM1) >> RCC_PLLCKSELR_DIVM1_Pos) != RCC_OscIni
 1489              		.loc 1 861 12 view .LVU506
 1490 0526 02F00303 		and	r3, r2, #3
 861:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           ((READ_BIT(temp1_pllckcfg, RCC_PLLCKSELR_DIVM1) >> RCC_PLLCKSELR_DIVM1_Pos) != RCC_OscIni
 1491              		.loc 1 861 84 view .LVU507
 1492 052a A16A     		ldr	r1, [r4, #40]
 860:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           (READ_BIT(temp1_pllckcfg, RCC_PLLCKSELR_PLLSRC) != RCC_OscInitStruct->PLL.PLLSource) ||
 1493              		.loc 1 860 62 discriminator 1 view .LVU508
 1494 052c 8B42     		cmp	r3, r1
 1495 052e 4BD1     		bne	.L125
 862:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           (READ_BIT(temp2_pllckcfg, RCC_PLL1DIVR_N1) != (RCC_OscInitStruct->PLL.PLLN - 1U)) ||
 1496              		.loc 1 862 59 view .LVU509
 1497 0530 C2F30512 		ubfx	r2, r2, #4, #6
 1498              	.LVL121:
 862:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           (READ_BIT(temp2_pllckcfg, RCC_PLL1DIVR_N1) != (RCC_OscInitStruct->PLL.PLLN - 1U)) ||
 1499              		.loc 1 862 112 view .LVU510
 1500 0534 E36A     		ldr	r3, [r4, #44]
 861:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           ((READ_BIT(temp1_pllckcfg, RCC_PLLCKSELR_DIVM1) >> RCC_PLLCKSELR_DIVM1_Pos) != RCC_OscIni
 1501              		.loc 1 861 96 view .LVU511
 1502 0536 9A42     		cmp	r2, r3
 1503 0538 48D1     		bne	.L126
 863:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           ((READ_BIT(temp2_pllckcfg, RCC_PLL1DIVR_P1) >> RCC_PLL1DIVR_P1_Pos) != (RCC_OscInitStruct
 1504              		.loc 1 863 12 view .LVU512
 1505 053a C0F30802 		ubfx	r2, r0, #0, #9
 863:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           ((READ_BIT(temp2_pllckcfg, RCC_PLL1DIVR_P1) >> RCC_PLL1DIVR_P1_Pos) != (RCC_OscInitStruct
 1506              		.loc 1 863 80 view .LVU513
 1507 053e 236B     		ldr	r3, [r4, #48]
 863:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           ((READ_BIT(temp2_pllckcfg, RCC_PLL1DIVR_P1) >> RCC_PLL1DIVR_P1_Pos) != (RCC_OscInitStruct
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 47


 1508              		.loc 1 863 86 view .LVU514
 1509 0540 013B     		subs	r3, r3, #1
 862:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           (READ_BIT(temp2_pllckcfg, RCC_PLL1DIVR_N1) != (RCC_OscInitStruct->PLL.PLLN - 1U)) ||
 1510              		.loc 1 862 119 view .LVU515
 1511 0542 9A42     		cmp	r2, r3
 1512 0544 44D1     		bne	.L127
 864:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           ((READ_BIT(temp2_pllckcfg, RCC_PLL1DIVR_Q1) >> RCC_PLL1DIVR_Q1_Pos) != (RCC_OscInitStruct
 1513              		.loc 1 864 55 view .LVU516
 1514 0546 C0F34622 		ubfx	r2, r0, #9, #7
 864:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           ((READ_BIT(temp2_pllckcfg, RCC_PLL1DIVR_Q1) >> RCC_PLL1DIVR_Q1_Pos) != (RCC_OscInitStruct
 1515              		.loc 1 864 105 view .LVU517
 1516 054a 636B     		ldr	r3, [r4, #52]
 864:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           ((READ_BIT(temp2_pllckcfg, RCC_PLL1DIVR_Q1) >> RCC_PLL1DIVR_Q1_Pos) != (RCC_OscInitStruct
 1517              		.loc 1 864 111 view .LVU518
 1518 054c 013B     		subs	r3, r3, #1
 863:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           ((READ_BIT(temp2_pllckcfg, RCC_PLL1DIVR_P1) >> RCC_PLL1DIVR_P1_Pos) != (RCC_OscInitStruct
 1519              		.loc 1 863 93 view .LVU519
 1520 054e 9A42     		cmp	r2, r3
 1521 0550 40D1     		bne	.L128
 865:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           ((READ_BIT(temp2_pllckcfg, RCC_PLL1DIVR_R1) >> RCC_PLL1DIVR_R1_Pos) != (RCC_OscInitStruct
 1522              		.loc 1 865 55 view .LVU520
 1523 0552 C0F30642 		ubfx	r2, r0, #16, #7
 865:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           ((READ_BIT(temp2_pllckcfg, RCC_PLL1DIVR_R1) >> RCC_PLL1DIVR_R1_Pos) != (RCC_OscInitStruct
 1524              		.loc 1 865 105 view .LVU521
 1525 0556 A36B     		ldr	r3, [r4, #56]
 865:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           ((READ_BIT(temp2_pllckcfg, RCC_PLL1DIVR_R1) >> RCC_PLL1DIVR_R1_Pos) != (RCC_OscInitStruct
 1526              		.loc 1 865 111 view .LVU522
 1527 0558 013B     		subs	r3, r3, #1
 864:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           ((READ_BIT(temp2_pllckcfg, RCC_PLL1DIVR_Q1) >> RCC_PLL1DIVR_Q1_Pos) != (RCC_OscInitStruct
 1528              		.loc 1 864 118 view .LVU523
 1529 055a 9A42     		cmp	r2, r3
 1530 055c 3CD1     		bne	.L129
 866:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
 1531              		.loc 1 866 55 view .LVU524
 1532 055e C0F30660 		ubfx	r0, r0, #24, #7
 1533              	.LVL122:
 866:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
 1534              		.loc 1 866 105 view .LVU525
 1535 0562 E36B     		ldr	r3, [r4, #60]
 866:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
 1536              		.loc 1 866 111 view .LVU526
 1537 0564 013B     		subs	r3, r3, #1
 865:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           ((READ_BIT(temp2_pllckcfg, RCC_PLL1DIVR_R1) >> RCC_PLL1DIVR_R1_Pos) != (RCC_OscInitStruct
 1538              		.loc 1 865 118 view .LVU527
 1539 0566 9842     		cmp	r0, r3
 1540 0568 38D1     		bne	.L130
 873:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         if (RCC_OscInitStruct->PLL.PLLFRACN != temp1_pllckcfg)
 1541              		.loc 1 873 9 is_stmt 1 view .LVU528
 873:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         if (RCC_OscInitStruct->PLL.PLLFRACN != temp1_pllckcfg)
 1542              		.loc 1 873 31 is_stmt 0 view .LVU529
 1543 056a 1D4B     		ldr	r3, .L158
 1544 056c 5B6B     		ldr	r3, [r3, #52]
 873:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         if (RCC_OscInitStruct->PLL.PLLFRACN != temp1_pllckcfg)
 1545              		.loc 1 873 24 view .LVU530
 1546 056e C3F3CC03 		ubfx	r3, r3, #3, #13
 1547              	.LVL123:
 874:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         {
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 48


 1548              		.loc 1 874 9 is_stmt 1 view .LVU531
 874:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         {
 1549              		.loc 1 874 35 is_stmt 0 view .LVU532
 1550 0572 A26C     		ldr	r2, [r4, #72]
 874:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         {
 1551              		.loc 1 874 12 view .LVU533
 1552 0574 9A42     		cmp	r2, r3
 1553 0576 01D1     		bne	.L153
 1554              		.loc 1 893 10 view .LVU534
 1555 0578 0020     		movs	r0, #0
 1556 057a 22E0     		b	.L36
 1557              	.L153:
 876:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           /* Disable PLL1FRACEN */
 1558              		.loc 1 876 11 is_stmt 1 view .LVU535
 878:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           /* Get Start Tick*/
 1559              		.loc 1 878 11 view .LVU536
 1560 057c 184A     		ldr	r2, .L158
 1561 057e D36A     		ldr	r3, [r2, #44]
 1562              	.LVL124:
 878:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           /* Get Start Tick*/
 1563              		.loc 1 878 11 is_stmt 0 view .LVU537
 1564 0580 23F00103 		bic	r3, r3, #1
 1565 0584 D362     		str	r3, [r2, #44]
 880:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           /* Wait at least 2 CK_REF (PLL input source divided by M) period to make sure next latche
 1566              		.loc 1 880 11 is_stmt 1 view .LVU538
 880:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           /* Wait at least 2 CK_REF (PLL input source divided by M) period to make sure next latche
 1567              		.loc 1 880 23 is_stmt 0 view .LVU539
 1568 0586 FFF7FEFF 		bl	HAL_GetTick
 1569              	.LVL125:
 1570 058a 0546     		mov	r5, r0
 1571              	.LVL126:
 882:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           {
 1572              		.loc 1 882 11 is_stmt 1 view .LVU540
 1573              	.L101:
 884:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           /* Configure PLL1 PLL1FRACN */
 1574              		.loc 1 884 11 view .LVU541
 882:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           {
 1575              		.loc 1 882 46 discriminator 1 view .LVU542
 882:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           {
 1576              		.loc 1 882 19 is_stmt 0 discriminator 1 view .LVU543
 1577 058c FFF7FEFF 		bl	HAL_GetTick
 1578              	.LVL127:
 882:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           {
 1579              		.loc 1 882 46 discriminator 1 view .LVU544
 1580 0590 A842     		cmp	r0, r5
 1581 0592 FBD0     		beq	.L101
 886:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           /* Enable PLL1FRACEN to latch new value. */
 1582              		.loc 1 886 11 is_stmt 1 view .LVU545
 1583 0594 124A     		ldr	r2, .L158
 1584 0596 536B     		ldr	r3, [r2, #52]
 1585 0598 6FF3CF03 		bfc	r3, #3, #13
 1586 059c A16C     		ldr	r1, [r4, #72]
 1587 059e 43EAC103 		orr	r3, r3, r1, lsl #3
 1588 05a2 5363     		str	r3, [r2, #52]
 888:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         }
 1589              		.loc 1 888 11 view .LVU546
 1590 05a4 D36A     		ldr	r3, [r2, #44]
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 49


 1591 05a6 43F00103 		orr	r3, r3, #1
 1592 05aa D362     		str	r3, [r2, #44]
 1593              		.loc 1 893 10 is_stmt 0 view .LVU547
 1594 05ac 0020     		movs	r0, #0
 1595 05ae 08E0     		b	.L36
 1596              	.LVL128:
 1597              	.L102:
 1598              	.LCFI2:
 1599              		.cfi_def_cfa_offset 0
 1600              		.cfi_restore 3
 1601              		.cfi_restore 4
 1602              		.cfi_restore 5
 1603              		.cfi_restore 14
 413:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
 1604              		.loc 1 413 12 view .LVU548
 1605 05b0 0120     		movs	r0, #1
 1606              	.LVL129:
 894:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** }
 1607              		.loc 1 894 1 view .LVU549
 1608 05b2 7047     		bx	lr
 1609              	.LVL130:
 1610              	.L138:
 1611              	.LCFI3:
 1612              		.cfi_def_cfa_offset 16
 1613              		.cfi_offset 3, -16
 1614              		.cfi_offset 4, -12
 1615              		.cfi_offset 5, -8
 1616              		.cfi_offset 14, -4
 1617              	.LBB10:
 431:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       }
 1618              		.loc 1 431 16 view .LVU550
 1619 05b4 0120     		movs	r0, #1
 1620              	.LVL131:
 431:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       }
 1621              		.loc 1 431 16 view .LVU551
 1622 05b6 04E0     		b	.L36
 1623              	.LVL132:
 1624              	.L106:
 431:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       }
 1625              		.loc 1 431 16 view .LVU552
 1626              	.LBE10:
 1627              	.LBB11:
 485:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       }
 1628              		.loc 1 485 16 view .LVU553
 1629 05b8 0120     		movs	r0, #1
 1630 05ba 02E0     		b	.L36
 1631              	.LVL133:
 1632              	.L110:
 485:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       }
 1633              		.loc 1 485 16 view .LVU554
 1634              	.LBE11:
 1635              	.LBB12:
 566:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       }
 1636              		.loc 1 566 16 view .LVU555
 1637 05bc 0120     		movs	r0, #1
 1638 05be 00E0     		b	.L36
 1639              	.LVL134:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 50


 1640              	.L120:
 566:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       }
 1641              		.loc 1 566 16 view .LVU556
 1642              	.LBE12:
 893:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** }
 1643              		.loc 1 893 10 view .LVU557
 1644 05c0 0020     		movs	r0, #0
 1645              	.LVL135:
 1646              	.L36:
 1647              		.loc 1 894 1 view .LVU558
 1648 05c2 38BD     		pop	{r3, r4, r5, pc}
 1649              	.LVL136:
 1650              	.L124:
 868:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       }
 1651              		.loc 1 868 16 view .LVU559
 1652 05c4 0120     		movs	r0, #1
 1653              	.LVL137:
 868:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       }
 1654              		.loc 1 868 16 view .LVU560
 1655 05c6 FCE7     		b	.L36
 1656              	.LVL138:
 1657              	.L125:
 868:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       }
 1658              		.loc 1 868 16 view .LVU561
 1659 05c8 0120     		movs	r0, #1
 1660              	.LVL139:
 868:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       }
 1661              		.loc 1 868 16 view .LVU562
 1662 05ca FAE7     		b	.L36
 1663              	.LVL140:
 1664              	.L126:
 868:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       }
 1665              		.loc 1 868 16 view .LVU563
 1666 05cc 0120     		movs	r0, #1
 1667              	.LVL141:
 868:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       }
 1668              		.loc 1 868 16 view .LVU564
 1669 05ce F8E7     		b	.L36
 1670              	.LVL142:
 1671              	.L127:
 868:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       }
 1672              		.loc 1 868 16 view .LVU565
 1673 05d0 0120     		movs	r0, #1
 1674              	.LVL143:
 868:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       }
 1675              		.loc 1 868 16 view .LVU566
 1676 05d2 F6E7     		b	.L36
 1677              	.LVL144:
 1678              	.L128:
 868:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       }
 1679              		.loc 1 868 16 view .LVU567
 1680 05d4 0120     		movs	r0, #1
 1681              	.LVL145:
 868:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       }
 1682              		.loc 1 868 16 view .LVU568
 1683 05d6 F4E7     		b	.L36
 1684              	.LVL146:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 51


 1685              	.L129:
 868:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       }
 1686              		.loc 1 868 16 view .LVU569
 1687 05d8 0120     		movs	r0, #1
 1688              	.LVL147:
 868:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       }
 1689              		.loc 1 868 16 view .LVU570
 1690 05da F2E7     		b	.L36
 1691              	.L130:
 1692 05dc 0120     		movs	r0, #1
 1693 05de F0E7     		b	.L36
 1694              	.L159:
 1695              		.align	2
 1696              	.L158:
 1697 05e0 00440258 		.word	1*********
 1698              		.cfi_endproc
 1699              	.LFE145:
 1701              		.section	.text.HAL_RCC_MCOConfig,"ax",%progbits
 1702              		.align	1
 1703              		.global	HAL_RCC_MCOConfig
 1704              		.syntax unified
 1705              		.thumb
 1706              		.thumb_func
 1708              	HAL_RCC_MCOConfig:
 1709              	.LVL148:
 1710              	.LFB147:
 895:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 896:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** /**
 897:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @brief  Initializes the CPU, AHB and APB buses clocks according to the specified
 898:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *         parameters in the RCC_ClkInitStruct.
 899:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @param  RCC_ClkInitStruct: pointer to an RCC_OscInitTypeDef structure that
 900:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *         contains the configuration information for the RCC peripheral.
 901:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @param  FLatency: FLASH Latency, this parameter depend on device selected
 902:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *
 903:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @note   The SystemCoreClock CMSIS variable is used to store System Core Clock Frequency
 904:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *         and updated by HAL_InitTick() function called within this function
 905:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *
 906:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @note   The HSI is used (enabled by hardware) as system clock source after
 907:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *         start-up from Reset, wake-up from STOP and STANDBY mode, or in case
 908:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *         of failure of the HSE used directly or indirectly as system clock
 909:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *         (if the Clock Security System CSS is enabled).
 910:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *
 911:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @note   A switch from one clock source to another occurs only if the target
 912:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *         clock source is ready (clock stable after start-up delay or PLL locked).
 913:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *         If a clock source which is not yet ready is selected, the switch will
 914:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *         occur when the clock source will be ready.
 915:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *         You can use HAL_RCC_GetClockConfig() function to know which clock is
 916:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *         currently used as system clock source.
 917:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @note   Depending on the device voltage range, the software has to set correctly
 918:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *         D1CPRE[3:0] bits to ensure that  Domain1 core clock not exceed the maximum allowed freq
 919:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *         (for more details refer to section above "Initialization/de-initialization functions")
 920:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @retval None
 921:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   */
 922:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** HAL_StatusTypeDef HAL_RCC_ClockConfig(const RCC_ClkInitTypeDef  *RCC_ClkInitStruct, uint32_t FLaten
 923:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** {
 924:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   HAL_StatusTypeDef halstatus;
 925:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   uint32_t tickstart;
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 52


 926:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   uint32_t common_system_clock;
 927:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 928:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Check Null pointer */
 929:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   if (RCC_ClkInitStruct == NULL)
 930:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
 931:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     return HAL_ERROR;
 932:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
 933:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 934:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Check the parameters */
 935:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   assert_param(IS_RCC_CLOCKTYPE(RCC_ClkInitStruct->ClockType));
 936:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   assert_param(IS_FLASH_LATENCY(FLatency));
 937:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 938:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* To correctly read data from FLASH memory, the number of wait states (LATENCY)
 939:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     must be correctly programmed according to the frequency of the CPU clock
 940:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     (HCLK) and the supply voltage of the device. */
 941:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 942:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Increasing the CPU frequency */
 943:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   if (FLatency > __HAL_FLASH_GET_LATENCY())
 944:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
 945:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     /* Program the new number of wait states to the LATENCY bits in the FLASH_ACR register */
 946:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     __HAL_FLASH_SET_LATENCY(FLatency);
 947:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 948:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     /* Check that the new number of wait states is taken into account to access the Flash
 949:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     memory by reading the FLASH_ACR register */
 950:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     if (__HAL_FLASH_GET_LATENCY() != FLatency)
 951:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 952:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       return HAL_ERROR;
 953:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     }
 954:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 955:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
 956:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 957:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Increasing the BUS frequency divider */
 958:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /*-------------------------- D1PCLK1/CDPCLK1 Configuration ---------------------------*/
 959:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   if (((RCC_ClkInitStruct->ClockType) & RCC_CLOCKTYPE_D1PCLK1) == RCC_CLOCKTYPE_D1PCLK1)
 960:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
 961:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #if defined (RCC_D1CFGR_D1PPRE)
 962:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     if ((RCC_ClkInitStruct->APB3CLKDivider) > (RCC->D1CFGR & RCC_D1CFGR_D1PPRE))
 963:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 964:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       assert_param(IS_RCC_D1PCLK1(RCC_ClkInitStruct->APB3CLKDivider));
 965:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       MODIFY_REG(RCC->D1CFGR, RCC_D1CFGR_D1PPRE, RCC_ClkInitStruct->APB3CLKDivider);
 966:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     }
 967:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #else
 968:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     if ((RCC_ClkInitStruct->APB3CLKDivider) > (RCC->CDCFGR1 & RCC_CDCFGR1_CDPPRE))
 969:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 970:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       assert_param(IS_RCC_CDPCLK1(RCC_ClkInitStruct->APB3CLKDivider));
 971:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       MODIFY_REG(RCC->CDCFGR1, RCC_CDCFGR1_CDPPRE, RCC_ClkInitStruct->APB3CLKDivider);
 972:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     }
 973:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #endif
 974:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
 975:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 976:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /*-------------------------- PCLK1 Configuration ---------------------------*/
 977:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   if (((RCC_ClkInitStruct->ClockType) & RCC_CLOCKTYPE_PCLK1) == RCC_CLOCKTYPE_PCLK1)
 978:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
 979:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #if defined (RCC_D2CFGR_D2PPRE1)
 980:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     if ((RCC_ClkInitStruct->APB1CLKDivider) > (RCC->D2CFGR & RCC_D2CFGR_D2PPRE1))
 981:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 982:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       assert_param(IS_RCC_PCLK1(RCC_ClkInitStruct->APB1CLKDivider));
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 53


 983:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       MODIFY_REG(RCC->D2CFGR, RCC_D2CFGR_D2PPRE1, (RCC_ClkInitStruct->APB1CLKDivider));
 984:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     }
 985:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #else
 986:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     if ((RCC_ClkInitStruct->APB1CLKDivider) > (RCC->CDCFGR2 & RCC_CDCFGR2_CDPPRE1))
 987:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 988:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       assert_param(IS_RCC_PCLK1(RCC_ClkInitStruct->APB1CLKDivider));
 989:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       MODIFY_REG(RCC->CDCFGR2, RCC_CDCFGR2_CDPPRE1, (RCC_ClkInitStruct->APB1CLKDivider));
 990:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     }
 991:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #endif
 992:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
 993:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /*-------------------------- PCLK2 Configuration ---------------------------*/
 994:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   if (((RCC_ClkInitStruct->ClockType) & RCC_CLOCKTYPE_PCLK2) == RCC_CLOCKTYPE_PCLK2)
 995:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
 996:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #if defined(RCC_D2CFGR_D2PPRE2)
 997:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     if ((RCC_ClkInitStruct->APB2CLKDivider) > (RCC->D2CFGR & RCC_D2CFGR_D2PPRE2))
 998:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 999:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       assert_param(IS_RCC_PCLK2(RCC_ClkInitStruct->APB2CLKDivider));
1000:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       MODIFY_REG(RCC->D2CFGR, RCC_D2CFGR_D2PPRE2, (RCC_ClkInitStruct->APB2CLKDivider));
1001:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     }
1002:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #else
1003:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     if ((RCC_ClkInitStruct->APB2CLKDivider) > (RCC->CDCFGR2 & RCC_CDCFGR2_CDPPRE2))
1004:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
1005:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       assert_param(IS_RCC_PCLK2(RCC_ClkInitStruct->APB2CLKDivider));
1006:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       MODIFY_REG(RCC->CDCFGR2, RCC_CDCFGR2_CDPPRE2, (RCC_ClkInitStruct->APB2CLKDivider));
1007:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     }
1008:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #endif
1009:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
1010:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1011:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /*-------------------------- D3PCLK1 Configuration ---------------------------*/
1012:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   if (((RCC_ClkInitStruct->ClockType) & RCC_CLOCKTYPE_D3PCLK1) == RCC_CLOCKTYPE_D3PCLK1)
1013:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
1014:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #if defined(RCC_D3CFGR_D3PPRE)
1015:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     if ((RCC_ClkInitStruct->APB4CLKDivider) > (RCC->D3CFGR & RCC_D3CFGR_D3PPRE))
1016:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
1017:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       assert_param(IS_RCC_D3PCLK1(RCC_ClkInitStruct->APB4CLKDivider));
1018:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       MODIFY_REG(RCC->D3CFGR, RCC_D3CFGR_D3PPRE, (RCC_ClkInitStruct->APB4CLKDivider));
1019:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     }
1020:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #else
1021:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     if ((RCC_ClkInitStruct->APB4CLKDivider) > (RCC->SRDCFGR & RCC_SRDCFGR_SRDPPRE))
1022:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
1023:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       assert_param(IS_RCC_D3PCLK1(RCC_ClkInitStruct->APB4CLKDivider));
1024:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       MODIFY_REG(RCC->SRDCFGR, RCC_SRDCFGR_SRDPPRE, (RCC_ClkInitStruct->APB4CLKDivider));
1025:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     }
1026:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #endif
1027:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
1028:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1029:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /*-------------------------- HCLK Configuration --------------------------*/
1030:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   if (((RCC_ClkInitStruct->ClockType) & RCC_CLOCKTYPE_HCLK) == RCC_CLOCKTYPE_HCLK)
1031:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
1032:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #if defined (RCC_D1CFGR_HPRE)
1033:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     if ((RCC_ClkInitStruct->AHBCLKDivider) > (RCC->D1CFGR & RCC_D1CFGR_HPRE))
1034:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
1035:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       /* Set the new HCLK clock divider */
1036:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       assert_param(IS_RCC_HCLK(RCC_ClkInitStruct->AHBCLKDivider));
1037:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       MODIFY_REG(RCC->D1CFGR, RCC_D1CFGR_HPRE, RCC_ClkInitStruct->AHBCLKDivider);
1038:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     }
1039:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #else
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 54


1040:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     if ((RCC_ClkInitStruct->AHBCLKDivider) > (RCC->CDCFGR1 & RCC_CDCFGR1_HPRE))
1041:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
1042:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       /* Set the new HCLK clock divider */
1043:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       assert_param(IS_RCC_HCLK(RCC_ClkInitStruct->AHBCLKDivider));
1044:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       MODIFY_REG(RCC->CDCFGR1, RCC_CDCFGR1_HPRE, RCC_ClkInitStruct->AHBCLKDivider);
1045:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     }
1046:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #endif
1047:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
1048:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1049:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /*------------------------- SYSCLK Configuration -------------------------*/
1050:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   if (((RCC_ClkInitStruct->ClockType) & RCC_CLOCKTYPE_SYSCLK) == RCC_CLOCKTYPE_SYSCLK)
1051:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
1052:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     assert_param(IS_RCC_SYSCLK(RCC_ClkInitStruct->SYSCLKDivider));
1053:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     assert_param(IS_RCC_SYSCLKSOURCE(RCC_ClkInitStruct->SYSCLKSource));
1054:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #if defined(RCC_D1CFGR_D1CPRE)
1055:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     MODIFY_REG(RCC->D1CFGR, RCC_D1CFGR_D1CPRE, RCC_ClkInitStruct->SYSCLKDivider);
1056:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #else
1057:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     MODIFY_REG(RCC->CDCFGR1, RCC_CDCFGR1_CDCPRE, RCC_ClkInitStruct->SYSCLKDivider);
1058:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #endif
1059:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     /* HSE is selected as System Clock Source */
1060:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     if (RCC_ClkInitStruct->SYSCLKSource == RCC_SYSCLKSOURCE_HSE)
1061:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
1062:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       /* Check the HSE ready flag */
1063:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       if (__HAL_RCC_GET_FLAG(RCC_FLAG_HSERDY) == 0U)
1064:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
1065:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         return HAL_ERROR;
1066:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       }
1067:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     }
1068:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     /* PLL is selected as System Clock Source */
1069:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     else if (RCC_ClkInitStruct->SYSCLKSource == RCC_SYSCLKSOURCE_PLLCLK)
1070:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
1071:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       /* Check the PLL ready flag */
1072:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       if (__HAL_RCC_GET_FLAG(RCC_FLAG_PLLRDY) == 0U)
1073:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
1074:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         return HAL_ERROR;
1075:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       }
1076:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     }
1077:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     /* CSI is selected as System Clock Source */
1078:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     else if (RCC_ClkInitStruct->SYSCLKSource == RCC_SYSCLKSOURCE_CSI)
1079:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
1080:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       /* Check the PLL ready flag */
1081:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       if (__HAL_RCC_GET_FLAG(RCC_FLAG_CSIRDY) == 0U)
1082:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
1083:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         return HAL_ERROR;
1084:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       }
1085:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     }
1086:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     /* HSI is selected as System Clock Source */
1087:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     else
1088:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
1089:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       /* Check the HSI ready flag */
1090:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       if (__HAL_RCC_GET_FLAG(RCC_FLAG_HSIRDY) == 0U)
1091:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
1092:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         return HAL_ERROR;
1093:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       }
1094:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     }
1095:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     MODIFY_REG(RCC->CFGR, RCC_CFGR_SW, RCC_ClkInitStruct->SYSCLKSource);
1096:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 55


1097:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     /* Get Start Tick*/
1098:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     tickstart = HAL_GetTick();
1099:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1100:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     while (__HAL_RCC_GET_SYSCLK_SOURCE() != (RCC_ClkInitStruct->SYSCLKSource << RCC_CFGR_SWS_Pos))
1101:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
1102:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       if ((HAL_GetTick() - tickstart) > CLOCKSWITCH_TIMEOUT_VALUE)
1103:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
1104:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         return HAL_TIMEOUT;
1105:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       }
1106:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     }
1107:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1108:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
1109:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1110:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Decreasing the BUS frequency divider */
1111:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /*-------------------------- HCLK Configuration --------------------------*/
1112:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   if (((RCC_ClkInitStruct->ClockType) & RCC_CLOCKTYPE_HCLK) == RCC_CLOCKTYPE_HCLK)
1113:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
1114:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #if defined(RCC_D1CFGR_HPRE)
1115:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     if ((RCC_ClkInitStruct->AHBCLKDivider) < (RCC->D1CFGR & RCC_D1CFGR_HPRE))
1116:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
1117:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       /* Set the new HCLK clock divider */
1118:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       assert_param(IS_RCC_HCLK(RCC_ClkInitStruct->AHBCLKDivider));
1119:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       MODIFY_REG(RCC->D1CFGR, RCC_D1CFGR_HPRE, RCC_ClkInitStruct->AHBCLKDivider);
1120:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     }
1121:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #else
1122:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     if ((RCC_ClkInitStruct->AHBCLKDivider) < (RCC->CDCFGR1 & RCC_CDCFGR1_HPRE))
1123:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
1124:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       /* Set the new HCLK clock divider */
1125:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       assert_param(IS_RCC_HCLK(RCC_ClkInitStruct->AHBCLKDivider));
1126:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       MODIFY_REG(RCC->CDCFGR1, RCC_CDCFGR1_HPRE, RCC_ClkInitStruct->AHBCLKDivider);
1127:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     }
1128:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #endif
1129:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
1130:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1131:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Decreasing the number of wait states because of lower CPU frequency */
1132:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   if (FLatency < __HAL_FLASH_GET_LATENCY())
1133:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
1134:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     /* Program the new number of wait states to the LATENCY bits in the FLASH_ACR register */
1135:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     __HAL_FLASH_SET_LATENCY(FLatency);
1136:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1137:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     /* Check that the new number of wait states is taken into account to access the Flash
1138:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     memory by reading the FLASH_ACR register */
1139:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     if (__HAL_FLASH_GET_LATENCY() != FLatency)
1140:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
1141:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       return HAL_ERROR;
1142:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     }
1143:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
1144:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1145:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /*-------------------------- D1PCLK1/CDPCLK Configuration ---------------------------*/
1146:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   if (((RCC_ClkInitStruct->ClockType) & RCC_CLOCKTYPE_D1PCLK1) == RCC_CLOCKTYPE_D1PCLK1)
1147:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
1148:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #if defined(RCC_D1CFGR_D1PPRE)
1149:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     if ((RCC_ClkInitStruct->APB3CLKDivider) < (RCC->D1CFGR & RCC_D1CFGR_D1PPRE))
1150:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
1151:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       assert_param(IS_RCC_D1PCLK1(RCC_ClkInitStruct->APB3CLKDivider));
1152:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       MODIFY_REG(RCC->D1CFGR, RCC_D1CFGR_D1PPRE, RCC_ClkInitStruct->APB3CLKDivider);
1153:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     }
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 56


1154:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #else
1155:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     if ((RCC_ClkInitStruct->APB3CLKDivider) < (RCC->CDCFGR1 & RCC_CDCFGR1_CDPPRE))
1156:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
1157:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       assert_param(IS_RCC_CDPCLK1(RCC_ClkInitStruct->APB3CLKDivider));
1158:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       MODIFY_REG(RCC->CDCFGR1, RCC_CDCFGR1_CDPPRE, RCC_ClkInitStruct->APB3CLKDivider);
1159:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     }
1160:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #endif
1161:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
1162:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1163:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /*-------------------------- PCLK1 Configuration ---------------------------*/
1164:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   if (((RCC_ClkInitStruct->ClockType) & RCC_CLOCKTYPE_PCLK1) == RCC_CLOCKTYPE_PCLK1)
1165:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
1166:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #if defined(RCC_D2CFGR_D2PPRE1)
1167:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     if ((RCC_ClkInitStruct->APB1CLKDivider) < (RCC->D2CFGR & RCC_D2CFGR_D2PPRE1))
1168:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
1169:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       assert_param(IS_RCC_PCLK1(RCC_ClkInitStruct->APB1CLKDivider));
1170:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       MODIFY_REG(RCC->D2CFGR, RCC_D2CFGR_D2PPRE1, (RCC_ClkInitStruct->APB1CLKDivider));
1171:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     }
1172:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #else
1173:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     if ((RCC_ClkInitStruct->APB1CLKDivider) < (RCC->CDCFGR2 & RCC_CDCFGR2_CDPPRE1))
1174:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
1175:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       assert_param(IS_RCC_PCLK1(RCC_ClkInitStruct->APB1CLKDivider));
1176:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       MODIFY_REG(RCC->CDCFGR2, RCC_CDCFGR2_CDPPRE1, (RCC_ClkInitStruct->APB1CLKDivider));
1177:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     }
1178:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #endif
1179:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
1180:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1181:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /*-------------------------- PCLK2 Configuration ---------------------------*/
1182:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   if (((RCC_ClkInitStruct->ClockType) & RCC_CLOCKTYPE_PCLK2) == RCC_CLOCKTYPE_PCLK2)
1183:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
1184:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #if defined (RCC_D2CFGR_D2PPRE2)
1185:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     if ((RCC_ClkInitStruct->APB2CLKDivider) < (RCC->D2CFGR & RCC_D2CFGR_D2PPRE2))
1186:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
1187:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       assert_param(IS_RCC_PCLK2(RCC_ClkInitStruct->APB2CLKDivider));
1188:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       MODIFY_REG(RCC->D2CFGR, RCC_D2CFGR_D2PPRE2, (RCC_ClkInitStruct->APB2CLKDivider));
1189:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     }
1190:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #else
1191:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     if ((RCC_ClkInitStruct->APB2CLKDivider) < (RCC->CDCFGR2 & RCC_CDCFGR2_CDPPRE2))
1192:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
1193:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       assert_param(IS_RCC_PCLK2(RCC_ClkInitStruct->APB2CLKDivider));
1194:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       MODIFY_REG(RCC->CDCFGR2, RCC_CDCFGR2_CDPPRE2, (RCC_ClkInitStruct->APB2CLKDivider));
1195:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     }
1196:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #endif
1197:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
1198:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1199:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /*-------------------------- D3PCLK1/SRDPCLK1 Configuration ---------------------------*/
1200:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   if (((RCC_ClkInitStruct->ClockType) & RCC_CLOCKTYPE_D3PCLK1) == RCC_CLOCKTYPE_D3PCLK1)
1201:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
1202:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #if defined(RCC_D3CFGR_D3PPRE)
1203:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     if ((RCC_ClkInitStruct->APB4CLKDivider) < (RCC->D3CFGR & RCC_D3CFGR_D3PPRE))
1204:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
1205:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       assert_param(IS_RCC_D3PCLK1(RCC_ClkInitStruct->APB4CLKDivider));
1206:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       MODIFY_REG(RCC->D3CFGR, RCC_D3CFGR_D3PPRE, (RCC_ClkInitStruct->APB4CLKDivider));
1207:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     }
1208:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #else
1209:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     if ((RCC_ClkInitStruct->APB4CLKDivider) < (RCC->SRDCFGR & RCC_SRDCFGR_SRDPPRE))
1210:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 57


1211:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       assert_param(IS_RCC_SRDPCLK1(RCC_ClkInitStruct->APB4CLKDivider));
1212:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       MODIFY_REG(RCC->SRDCFGR, RCC_SRDCFGR_SRDPPRE, (RCC_ClkInitStruct->APB4CLKDivider));
1213:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     }
1214:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #endif
1215:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
1216:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1217:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Update the SystemCoreClock global variable */
1218:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #if defined(RCC_D1CFGR_D1CPRE)
1219:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   common_system_clock = HAL_RCC_GetSysClockFreq() >> ((D1CorePrescTable[(RCC->D1CFGR & RCC_D1CFGR_D
1220:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #else
1221:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   common_system_clock = HAL_RCC_GetSysClockFreq() >> ((D1CorePrescTable[(RCC->CDCFGR1 & RCC_CDCFGR1
1222:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #endif
1223:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1224:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #if defined(RCC_D1CFGR_HPRE)
1225:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   SystemD2Clock = (common_system_clock >> ((D1CorePrescTable[(RCC->D1CFGR & RCC_D1CFGR_HPRE) >> RCC
1226:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #else
1227:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   SystemD2Clock = (common_system_clock >> ((D1CorePrescTable[(RCC->CDCFGR1 & RCC_CDCFGR1_HPRE) >> R
1228:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #endif
1229:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1230:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #if defined(DUAL_CORE) && defined(CORE_CM4)
1231:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   SystemCoreClock = SystemD2Clock;
1232:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #else
1233:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   SystemCoreClock = common_system_clock;
1234:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #endif /* DUAL_CORE && CORE_CM4 */
1235:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1236:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Configure the source of time base considering new system clocks settings*/
1237:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   halstatus = HAL_InitTick(uwTickPrio);
1238:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1239:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   return halstatus;
1240:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** }
1241:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1242:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** /**
1243:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @}
1244:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   */
1245:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1246:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** /** @defgroup RCC_Exported_Functions_Group2 Peripheral Control functions
1247:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****  *  @brief   RCC clocks control functions
1248:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****  *
1249:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** @verbatim
1250:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****  ===============================================================================
1251:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****                       ##### Peripheral Control functions #####
1252:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****  ===============================================================================
1253:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     [..]
1254:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     This subsection provides a set of functions allowing to control the RCC Clocks
1255:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     frequencies.
1256:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1257:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** @endverbatim
1258:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @{
1259:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   */
1260:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1261:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** /**
1262:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @brief  Selects the clock source to output on MCO1 pin(PA8) or on MCO2 pin(PC9).
1263:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @note   PA8/PC9 should be configured in alternate function mode.
1264:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @param  RCC_MCOx: specifies the output direction for the clock source.
1265:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *          This parameter can be one of the following values:
1266:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *            @arg RCC_MCO1: Clock source to output on MCO1 pin(PA8).
1267:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *            @arg RCC_MCO2: Clock source to output on MCO2 pin(PC9).
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 58


1268:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @param  RCC_MCOSource: specifies the clock source to output.
1269:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *          This parameter can be one of the following values:
1270:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *            @arg RCC_MCO1SOURCE_HSI: HSI clock selected as MCO1 source
1271:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *            @arg RCC_MCO1SOURCE_LSE: LSE clock selected as MCO1 source
1272:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *            @arg RCC_MCO1SOURCE_HSE: HSE clock selected as MCO1 source
1273:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *            @arg RCC_MCO1SOURCE_PLL1QCLK:  PLL1Q clock selected as MCO1 source
1274:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *            @arg RCC_MCO1SOURCE_HSI48: HSI48 (48MHZ) selected as MCO1 source
1275:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *            @arg RCC_MCO2SOURCE_SYSCLK: System clock (SYSCLK) selected as MCO2 source
1276:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *            @arg RCC_MCO2SOURCE_PLL2PCLK: PLL2P clock selected as MCO2 source
1277:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *            @arg RCC_MCO2SOURCE_HSE: HSE clock selected as MCO2 source
1278:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *            @arg RCC_MCO2SOURCE_PLLCLK:  PLL1P clock selected as MCO2 source
1279:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *            @arg RCC_MCO2SOURCE_CSICLK:  CSI clock selected as MCO2 source
1280:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *            @arg RCC_MCO2SOURCE_LSICLK:  LSI clock selected as MCO2 source
1281:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @param  RCC_MCODiv: specifies the MCOx pre-scaler.
1282:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *          This parameter can be one of the following values:
1283:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *            @arg RCC_MCODIV_1 up to RCC_MCODIV_15  : divider applied to MCOx clock
1284:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @retval None
1285:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   */
1286:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** void HAL_RCC_MCOConfig(uint32_t RCC_MCOx, uint32_t RCC_MCOSource, uint32_t RCC_MCODiv)
1287:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** {
 1711              		.loc 1 1287 1 is_stmt 1 view -0
 1712              		.cfi_startproc
 1713              		@ args = 0, pretend = 0, frame = 32
 1714              		@ frame_needed = 0, uses_anonymous_args = 0
 1715              		.loc 1 1287 1 is_stmt 0 view .LVU572
 1716 0000 70B5     		push	{r4, r5, r6, lr}
 1717              	.LCFI4:
 1718              		.cfi_def_cfa_offset 16
 1719              		.cfi_offset 4, -16
 1720              		.cfi_offset 5, -12
 1721              		.cfi_offset 6, -8
 1722              		.cfi_offset 14, -4
 1723 0002 88B0     		sub	sp, sp, #32
 1724              	.LCFI5:
 1725              		.cfi_def_cfa_offset 48
 1726 0004 0C46     		mov	r4, r1
 1727 0006 1546     		mov	r5, r2
1288:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   GPIO_InitTypeDef GPIO_InitStruct;
 1728              		.loc 1 1288 3 is_stmt 1 view .LVU573
1289:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Check the parameters */
1290:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   assert_param(IS_RCC_MCO(RCC_MCOx));
 1729              		.loc 1 1290 3 view .LVU574
1291:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   assert_param(IS_RCC_MCODIV(RCC_MCODiv));
 1730              		.loc 1 1291 3 view .LVU575
1292:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* RCC_MCO1 */
1293:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   if (RCC_MCOx == RCC_MCO1)
 1731              		.loc 1 1293 3 view .LVU576
 1732              		.loc 1 1293 6 is_stmt 0 view .LVU577
 1733 0008 10BB     		cbnz	r0, .L161
1294:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
1295:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     assert_param(IS_RCC_MCO1SOURCE(RCC_MCOSource));
 1734              		.loc 1 1295 5 is_stmt 1 view .LVU578
1296:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1297:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     /* MCO1 Clock Enable */
1298:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     MCO1_CLK_ENABLE();
 1735              		.loc 1 1298 5 view .LVU579
 1736              	.LBB13:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 59


 1737              		.loc 1 1298 5 view .LVU580
 1738              		.loc 1 1298 5 view .LVU581
 1739 000a 234E     		ldr	r6, .L164
 1740 000c D6F8E030 		ldr	r3, [r6, #224]
 1741 0010 43F00103 		orr	r3, r3, #1
 1742 0014 C6F8E030 		str	r3, [r6, #224]
 1743              		.loc 1 1298 5 view .LVU582
 1744 0018 D6F8E030 		ldr	r3, [r6, #224]
 1745 001c 03F00103 		and	r3, r3, #1
 1746 0020 0193     		str	r3, [sp, #4]
 1747              		.loc 1 1298 5 view .LVU583
 1748 0022 019B     		ldr	r3, [sp, #4]
 1749              	.LBE13:
 1750              		.loc 1 1298 5 view .LVU584
1299:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1300:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     /* Configure the MCO1 pin in alternate function mode */
1301:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     GPIO_InitStruct.Pin = MCO1_PIN;
 1751              		.loc 1 1301 5 view .LVU585
 1752              		.loc 1 1301 25 is_stmt 0 view .LVU586
 1753 0024 4FF48073 		mov	r3, #256
 1754 0028 0393     		str	r3, [sp, #12]
1302:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
 1755              		.loc 1 1302 5 is_stmt 1 view .LVU587
 1756              		.loc 1 1302 26 is_stmt 0 view .LVU588
 1757 002a 0223     		movs	r3, #2
 1758 002c 0493     		str	r3, [sp, #16]
1303:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;
 1759              		.loc 1 1303 5 is_stmt 1 view .LVU589
 1760              		.loc 1 1303 27 is_stmt 0 view .LVU590
 1761 002e 0323     		movs	r3, #3
 1762 0030 0693     		str	r3, [sp, #24]
1304:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     GPIO_InitStruct.Pull = GPIO_NOPULL;
 1763              		.loc 1 1304 5 is_stmt 1 view .LVU591
 1764              		.loc 1 1304 26 is_stmt 0 view .LVU592
 1765 0032 0023     		movs	r3, #0
 1766 0034 0593     		str	r3, [sp, #20]
1305:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     GPIO_InitStruct.Alternate = GPIO_AF0_MCO;
 1767              		.loc 1 1305 5 is_stmt 1 view .LVU593
 1768              		.loc 1 1305 31 is_stmt 0 view .LVU594
 1769 0036 0793     		str	r3, [sp, #28]
1306:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     HAL_GPIO_Init(MCO1_GPIO_PORT, &GPIO_InitStruct);
 1770              		.loc 1 1306 5 is_stmt 1 view .LVU595
 1771 0038 03A9     		add	r1, sp, #12
 1772              	.LVL149:
 1773              		.loc 1 1306 5 is_stmt 0 view .LVU596
 1774 003a 1848     		ldr	r0, .L164+4
 1775              	.LVL150:
 1776              		.loc 1 1306 5 view .LVU597
 1777 003c FFF7FEFF 		bl	HAL_GPIO_Init
 1778              	.LVL151:
1307:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1308:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     /* Mask MCO1 and MCO1PRE[3:0] bits then Select MCO1 clock source and pre-scaler */
1309:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     MODIFY_REG(RCC->CFGR, (RCC_CFGR_MCO1 | RCC_CFGR_MCO1PRE), (RCC_MCOSource | RCC_MCODiv));
 1779              		.loc 1 1309 5 is_stmt 1 view .LVU598
 1780 0040 3369     		ldr	r3, [r6, #16]
 1781 0042 23F0FE73 		bic	r3, r3, #33292288
 1782 0046 2543     		orrs	r5, r5, r4
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 60


 1783              	.LVL152:
 1784              		.loc 1 1309 5 is_stmt 0 view .LVU599
 1785 0048 1D43     		orrs	r5, r5, r3
 1786 004a 3561     		str	r5, [r6, #16]
 1787              	.LVL153:
 1788              	.L160:
1310:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
1311:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   else
1312:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
1313:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     assert_param(IS_RCC_MCO2SOURCE(RCC_MCOSource));
1314:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1315:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     /* MCO2 Clock Enable */
1316:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     MCO2_CLK_ENABLE();
1317:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1318:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     /* Configure the MCO2 pin in alternate function mode */
1319:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     GPIO_InitStruct.Pin = MCO2_PIN;
1320:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
1321:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;
1322:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     GPIO_InitStruct.Pull = GPIO_NOPULL;
1323:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     GPIO_InitStruct.Alternate = GPIO_AF0_MCO;
1324:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     HAL_GPIO_Init(MCO2_GPIO_PORT, &GPIO_InitStruct);
1325:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1326:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     /* Mask MCO2 and MCO2PRE[3:0] bits then Select MCO2 clock source and pre-scaler */
1327:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     MODIFY_REG(RCC->CFGR, (RCC_CFGR_MCO2 | RCC_CFGR_MCO2PRE), (RCC_MCOSource | (RCC_MCODiv << 7U)))
1328:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
1329:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** }
 1789              		.loc 1 1329 1 view .LVU600
 1790 004c 08B0     		add	sp, sp, #32
 1791              	.LCFI6:
 1792              		.cfi_remember_state
 1793              		.cfi_def_cfa_offset 16
 1794              		@ sp needed
 1795 004e 70BD     		pop	{r4, r5, r6, pc}
 1796              	.LVL154:
 1797              	.L161:
 1798              	.LCFI7:
 1799              		.cfi_restore_state
1313:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 1800              		.loc 1 1313 5 is_stmt 1 view .LVU601
1316:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 1801              		.loc 1 1316 5 view .LVU602
 1802              	.LBB14:
1316:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 1803              		.loc 1 1316 5 view .LVU603
1316:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 1804              		.loc 1 1316 5 view .LVU604
 1805 0050 114E     		ldr	r6, .L164
 1806 0052 D6F8E030 		ldr	r3, [r6, #224]
 1807 0056 43F00403 		orr	r3, r3, #4
 1808 005a C6F8E030 		str	r3, [r6, #224]
1316:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 1809              		.loc 1 1316 5 view .LVU605
 1810 005e D6F8E030 		ldr	r3, [r6, #224]
 1811 0062 03F00403 		and	r3, r3, #4
 1812 0066 0293     		str	r3, [sp, #8]
1316:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 1813              		.loc 1 1316 5 view .LVU606
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 61


 1814 0068 029B     		ldr	r3, [sp, #8]
 1815              	.LBE14:
1316:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 1816              		.loc 1 1316 5 view .LVU607
1319:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
 1817              		.loc 1 1319 5 view .LVU608
1319:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
 1818              		.loc 1 1319 25 is_stmt 0 view .LVU609
 1819 006a 4FF40073 		mov	r3, #512
 1820 006e 0393     		str	r3, [sp, #12]
1320:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;
 1821              		.loc 1 1320 5 is_stmt 1 view .LVU610
1320:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;
 1822              		.loc 1 1320 26 is_stmt 0 view .LVU611
 1823 0070 0223     		movs	r3, #2
 1824 0072 0493     		str	r3, [sp, #16]
1321:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     GPIO_InitStruct.Pull = GPIO_NOPULL;
 1825              		.loc 1 1321 5 is_stmt 1 view .LVU612
1321:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     GPIO_InitStruct.Pull = GPIO_NOPULL;
 1826              		.loc 1 1321 27 is_stmt 0 view .LVU613
 1827 0074 0323     		movs	r3, #3
 1828 0076 0693     		str	r3, [sp, #24]
1322:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     GPIO_InitStruct.Alternate = GPIO_AF0_MCO;
 1829              		.loc 1 1322 5 is_stmt 1 view .LVU614
1322:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     GPIO_InitStruct.Alternate = GPIO_AF0_MCO;
 1830              		.loc 1 1322 26 is_stmt 0 view .LVU615
 1831 0078 0023     		movs	r3, #0
 1832 007a 0593     		str	r3, [sp, #20]
1323:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     HAL_GPIO_Init(MCO2_GPIO_PORT, &GPIO_InitStruct);
 1833              		.loc 1 1323 5 is_stmt 1 view .LVU616
1323:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     HAL_GPIO_Init(MCO2_GPIO_PORT, &GPIO_InitStruct);
 1834              		.loc 1 1323 31 is_stmt 0 view .LVU617
 1835 007c 0793     		str	r3, [sp, #28]
1324:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 1836              		.loc 1 1324 5 is_stmt 1 view .LVU618
 1837 007e 03A9     		add	r1, sp, #12
 1838              	.LVL155:
1324:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 1839              		.loc 1 1324 5 is_stmt 0 view .LVU619
 1840 0080 0748     		ldr	r0, .L164+8
 1841              	.LVL156:
1324:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 1842              		.loc 1 1324 5 view .LVU620
 1843 0082 FFF7FEFF 		bl	HAL_GPIO_Init
 1844              	.LVL157:
1327:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
 1845              		.loc 1 1327 5 is_stmt 1 view .LVU621
 1846 0086 3369     		ldr	r3, [r6, #16]
 1847 0088 23F07E43 		bic	r3, r3, #-33554432
 1848 008c 44EAC514 		orr	r4, r4, r5, lsl #7
 1849              	.LVL158:
1327:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
 1850              		.loc 1 1327 5 is_stmt 0 view .LVU622
 1851 0090 1C43     		orrs	r4, r4, r3
 1852 0092 3461     		str	r4, [r6, #16]
 1853              		.loc 1 1329 1 view .LVU623
 1854 0094 DAE7     		b	.L160
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 62


 1855              	.L165:
 1856 0096 00BF     		.align	2
 1857              	.L164:
 1858 0098 00440258 		.word	1*********
 1859 009c 00000258 		.word	1476526080
 1860 00a0 00080258 		.word	1476528128
 1861              		.cfi_endproc
 1862              	.LFE147:
 1864              		.section	.text.HAL_RCC_EnableCSS,"ax",%progbits
 1865              		.align	1
 1866              		.global	HAL_RCC_EnableCSS
 1867              		.syntax unified
 1868              		.thumb
 1869              		.thumb_func
 1871              	HAL_RCC_EnableCSS:
 1872              	.LFB148:
1330:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1331:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** /**
1332:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @brief  Enables the Clock Security System.
1333:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @note   If a failure is detected on the HSE oscillator clock, this oscillator
1334:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *         is automatically disabled and an interrupt is generated to inform the
1335:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *         software about the failure (Clock Security System Interrupt, CSSI),
1336:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *         allowing the MCU to perform rescue operations. The CSSI is linked to
1337:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *         the Cortex-M NMI (Non-Mask-able Interrupt) exception vector.
1338:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @retval None
1339:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   */
1340:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** void HAL_RCC_EnableCSS(void)
1341:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** {
 1873              		.loc 1 1341 1 is_stmt 1 view -0
 1874              		.cfi_startproc
 1875              		@ args = 0, pretend = 0, frame = 0
 1876              		@ frame_needed = 0, uses_anonymous_args = 0
 1877              		@ link register save eliminated.
1342:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   SET_BIT(RCC->CR, RCC_CR_CSSHSEON) ;
 1878              		.loc 1 1342 3 view .LVU625
 1879 0000 024A     		ldr	r2, .L167
 1880 0002 1368     		ldr	r3, [r2]
 1881 0004 43F40023 		orr	r3, r3, #524288
 1882 0008 1360     		str	r3, [r2]
1343:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** }
 1883              		.loc 1 1343 1 is_stmt 0 view .LVU626
 1884 000a 7047     		bx	lr
 1885              	.L168:
 1886              		.align	2
 1887              	.L167:
 1888 000c 00440258 		.word	1*********
 1889              		.cfi_endproc
 1890              	.LFE148:
 1892              		.section	.text.HAL_RCC_DisableCSS,"ax",%progbits
 1893              		.align	1
 1894              		.global	HAL_RCC_DisableCSS
 1895              		.syntax unified
 1896              		.thumb
 1897              		.thumb_func
 1899              	HAL_RCC_DisableCSS:
 1900              	.LFB149:
1344:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 63


1345:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** /**
1346:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @brief  Disables the Clock Security System.
1347:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @retval None
1348:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   */
1349:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** void HAL_RCC_DisableCSS(void)
1350:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** {
 1901              		.loc 1 1350 1 is_stmt 1 view -0
 1902              		.cfi_startproc
 1903              		@ args = 0, pretend = 0, frame = 0
 1904              		@ frame_needed = 0, uses_anonymous_args = 0
 1905              		@ link register save eliminated.
1351:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   CLEAR_BIT(RCC->CR, RCC_CR_CSSHSEON);
 1906              		.loc 1 1351 3 view .LVU628
 1907 0000 024A     		ldr	r2, .L170
 1908 0002 1368     		ldr	r3, [r2]
 1909 0004 23F40023 		bic	r3, r3, #524288
 1910 0008 1360     		str	r3, [r2]
1352:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** }
 1911              		.loc 1 1352 1 is_stmt 0 view .LVU629
 1912 000a 7047     		bx	lr
 1913              	.L171:
 1914              		.align	2
 1915              	.L170:
 1916 000c 00440258 		.word	1*********
 1917              		.cfi_endproc
 1918              	.LFE149:
 1920              		.section	.text.HAL_RCC_GetSysClockFreq,"ax",%progbits
 1921              		.align	1
 1922              		.global	HAL_RCC_GetSysClockFreq
 1923              		.syntax unified
 1924              		.thumb
 1925              		.thumb_func
 1927              	HAL_RCC_GetSysClockFreq:
 1928              	.LFB150:
1353:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1354:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** /**
1355:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @brief  Returns the SYSCLK frequency
1356:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *
1357:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @note   The system frequency computed by this function is not the real
1358:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *         frequency in the chip. It is calculated based on the predefined
1359:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *         constant and the selected clock source:
1360:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @note     If SYSCLK source is CSI, function returns values based on CSI_VALUE(*)
1361:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @note     If SYSCLK source is HSI, function returns values based on HSI_VALUE(**)
1362:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @note     If SYSCLK source is HSE, function returns values based on HSE_VALUE(***)
1363:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @note     If SYSCLK source is PLL, function returns values based on CSI_VALUE(*),
1364:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *           HSI_VALUE(**) or HSE_VALUE(***) multiplied/divided by the PLL factors.
1365:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @note     (*) CSI_VALUE is a constant defined in stm32h7xx_hal_conf.h file (default value
1366:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *               4 MHz) but the real value may vary depending on the variations
1367:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *               in voltage and temperature.
1368:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @note     (**) HSI_VALUE is a constant defined in stm32h7xx_hal_conf.h file (default value
1369:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *               64 MHz) but the real value may vary depending on the variations
1370:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *               in voltage and temperature.
1371:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @note     (***) HSE_VALUE is a constant defined in stm32h7xx_hal_conf.h file (default value
1372:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *                25 MHz), user has to ensure that HSE_VALUE is same as the real
1373:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *                frequency of the crystal used. Otherwise, this function may
1374:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *                have wrong result.
1375:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 64


1376:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @note   The result of this function could be not correct when using fractional
1377:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *         value for HSE crystal.
1378:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *
1379:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @note   This function can be used by the user application to compute the
1380:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *         baud rate for the communication peripherals or configure other parameters.
1381:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *
1382:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @note   Each time SYSCLK changes, this function must be called to update the
1383:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *         right SYSCLK value. Otherwise, any configuration based on this function will be incorre
1384:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *
1385:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *
1386:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @retval SYSCLK frequency
1387:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   */
1388:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** uint32_t HAL_RCC_GetSysClockFreq(void)
1389:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** {
 1929              		.loc 1 1389 1 is_stmt 1 view -0
 1930              		.cfi_startproc
 1931              		@ args = 0, pretend = 0, frame = 0
 1932              		@ frame_needed = 0, uses_anonymous_args = 0
 1933              		@ link register save eliminated.
1390:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   uint32_t pllp, pllsource, pllm, pllfracen, hsivalue;
 1934              		.loc 1 1390 3 view .LVU631
1391:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   float_t fracn1, pllvco;
 1935              		.loc 1 1391 3 view .LVU632
1392:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   uint32_t sysclockfreq;
 1936              		.loc 1 1392 3 view .LVU633
1393:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1394:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Get SYSCLK source -------------------------------------------------------*/
1395:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1396:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   switch (RCC->CFGR & RCC_CFGR_SWS)
 1937              		.loc 1 1396 3 view .LVU634
 1938              		.loc 1 1396 14 is_stmt 0 view .LVU635
 1939 0000 754B     		ldr	r3, .L186
 1940 0002 1B69     		ldr	r3, [r3, #16]
 1941              		.loc 1 1396 21 view .LVU636
 1942 0004 03F03803 		and	r3, r3, #56
 1943              		.loc 1 1396 3 view .LVU637
 1944 0008 102B     		cmp	r3, #16
 1945 000a 00F0DE80 		beq	.L180
 1946 000e 182B     		cmp	r3, #24
 1947 0010 0FD0     		beq	.L174
 1948 0012 002B     		cmp	r3, #0
 1949 0014 40F0DB80 		bne	.L181
1397:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
1398:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     case RCC_CFGR_SWS_HSI:  /* HSI used as system clock source */
1399:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1400:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       if (__HAL_RCC_GET_FLAG(RCC_FLAG_HSIDIV) != 0U)
 1950              		.loc 1 1400 7 is_stmt 1 view .LVU638
 1951              		.loc 1 1400 11 is_stmt 0 view .LVU639
 1952 0018 6F4B     		ldr	r3, .L186
 1953 001a 1B68     		ldr	r3, [r3]
 1954              		.loc 1 1400 10 view .LVU640
 1955 001c 13F0200F 		tst	r3, #32
 1956 0020 00F0D780 		beq	.L182
1401:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
1402:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         sysclockfreq = (uint32_t)(HSI_VALUE >> (__HAL_RCC_GET_HSI_DIVIDER() >> 3));
 1957              		.loc 1 1402 9 is_stmt 1 view .LVU641
 1958              		.loc 1 1402 49 is_stmt 0 view .LVU642
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 65


 1959 0024 6C4B     		ldr	r3, .L186
 1960 0026 1B68     		ldr	r3, [r3]
 1961              		.loc 1 1402 77 view .LVU643
 1962 0028 C3F3C103 		ubfx	r3, r3, #3, #2
 1963              		.loc 1 1402 22 view .LVU644
 1964 002c 6B48     		ldr	r0, .L186+4
 1965 002e D840     		lsrs	r0, r0, r3
 1966              	.LVL159:
 1967              		.loc 1 1402 22 view .LVU645
 1968 0030 7047     		bx	lr
 1969              	.LVL160:
 1970              	.L174:
1389:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   uint32_t pllp, pllsource, pllm, pllfracen, hsivalue;
 1971              		.loc 1 1389 1 view .LVU646
 1972 0032 10B4     		push	{r4}
 1973              	.LCFI8:
 1974              		.cfi_def_cfa_offset 4
 1975              		.cfi_offset 4, -4
1403:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       }
1404:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       else
1405:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
1406:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         sysclockfreq = (uint32_t) HSI_VALUE;
1407:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       }
1408:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1409:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       break;
1410:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1411:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     case RCC_CFGR_SWS_CSI:  /* CSI used as system clock  source */
1412:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       sysclockfreq = CSI_VALUE;
1413:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       break;
1414:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1415:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     case RCC_CFGR_SWS_HSE:  /* HSE used as system clock  source */
1416:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       sysclockfreq = HSE_VALUE;
1417:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       break;
1418:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1419:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     case RCC_CFGR_SWS_PLL1:  /* PLL1 used as system clock  source */
1420:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1421:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       /* PLL_VCO = (HSE_VALUE or HSI_VALUE or CSI_VALUE/ PLLM) * PLLN
1422:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       SYSCLK = PLL_VCO / PLLR
1423:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       */
1424:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       pllsource = (RCC->PLLCKSELR & RCC_PLLCKSELR_PLLSRC);
 1976              		.loc 1 1424 7 is_stmt 1 view .LVU647
 1977              		.loc 1 1424 23 is_stmt 0 view .LVU648
 1978 0034 684B     		ldr	r3, .L186
 1979 0036 9A6A     		ldr	r2, [r3, #40]
 1980              		.loc 1 1424 17 view .LVU649
 1981 0038 02F00302 		and	r2, r2, #3
 1982              	.LVL161:
1425:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       pllm = ((RCC->PLLCKSELR & RCC_PLLCKSELR_DIVM1) >> 4)  ;
 1983              		.loc 1 1425 7 is_stmt 1 view .LVU650
 1984              		.loc 1 1425 19 is_stmt 0 view .LVU651
 1985 003c 9C6A     		ldr	r4, [r3, #40]
 1986              		.loc 1 1425 12 view .LVU652
 1987 003e C4F30510 		ubfx	r0, r4, #4, #6
 1988              	.LVL162:
1426:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       pllfracen = ((RCC-> PLLCFGR & RCC_PLLCFGR_PLL1FRACEN) >> RCC_PLLCFGR_PLL1FRACEN_Pos);
 1989              		.loc 1 1426 7 is_stmt 1 view .LVU653
 1990              		.loc 1 1426 24 is_stmt 0 view .LVU654
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 66


 1991 0042 D96A     		ldr	r1, [r3, #44]
 1992              		.loc 1 1426 17 view .LVU655
 1993 0044 01F00101 		and	r1, r1, #1
 1994              	.LVL163:
1427:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       fracn1 = (float_t)(uint32_t)(pllfracen * ((RCC->PLL1FRACR & RCC_PLL1FRACR_FRACN1) >> 3));
 1995              		.loc 1 1427 7 is_stmt 1 view .LVU656
 1996              		.loc 1 1427 53 is_stmt 0 view .LVU657
 1997 0048 5B6B     		ldr	r3, [r3, #52]
 1998              		.loc 1 1427 89 view .LVU658
 1999 004a C3F3CC03 		ubfx	r3, r3, #3, #13
 2000              		.loc 1 1427 25 view .LVU659
 2001 004e 01FB03F3 		mul	r3, r1, r3
 2002              		.loc 1 1427 14 view .LVU660
 2003 0052 07EE903A 		vmov	s15, r3	@ int
 2004 0056 F8EE677A 		vcvt.f32.u32	s15, s15
 2005              	.LVL164:
1428:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1429:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       if (pllm != 0U)
 2006              		.loc 1 1429 7 is_stmt 1 view .LVU661
 2007              		.loc 1 1429 10 is_stmt 0 view .LVU662
 2008 005a 14F47C7F 		tst	r4, #1008
 2009 005e 77D0     		beq	.L172
1430:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
1431:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         switch (pllsource)
 2010              		.loc 1 1431 9 is_stmt 1 view .LVU663
 2011 0060 012A     		cmp	r2, #1
 2012 0062 4AD0     		beq	.L175
 2013 0064 022A     		cmp	r2, #2
 2014 0066 76D0     		beq	.L176
 2015 0068 002A     		cmp	r2, #0
 2016 006a 40F09180 		bne	.L177
1432:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         {
1433:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           case RCC_PLLSOURCE_HSI:  /* HSI used as PLL clock source */
1434:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1435:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****             if (__HAL_RCC_GET_FLAG(RCC_FLAG_HSIDIV) != 0U)
 2017              		.loc 1 1435 13 view .LVU664
 2018              		.loc 1 1435 17 is_stmt 0 view .LVU665
 2019 006e 5A4B     		ldr	r3, .L186
 2020 0070 1B68     		ldr	r3, [r3]
 2021              		.loc 1 1435 16 view .LVU666
 2022 0072 13F0200F 		tst	r3, #32
 2023 0076 23D0     		beq	.L178
1436:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****             {
1437:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****               hsivalue = (HSI_VALUE >> (__HAL_RCC_GET_HSI_DIVIDER() >> 3));
 2024              		.loc 1 1437 15 is_stmt 1 view .LVU667
 2025              		.loc 1 1437 41 is_stmt 0 view .LVU668
 2026 0078 5749     		ldr	r1, .L186
 2027              	.LVL165:
 2028              		.loc 1 1437 41 view .LVU669
 2029 007a 0A68     		ldr	r2, [r1]
 2030              	.LVL166:
 2031              		.loc 1 1437 69 view .LVU670
 2032 007c C2F3C102 		ubfx	r2, r2, #3, #2
 2033              		.loc 1 1437 24 view .LVU671
 2034 0080 564B     		ldr	r3, .L186+4
 2035 0082 D340     		lsrs	r3, r3, r2
 2036              	.LVL167:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 67


1438:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****               pllvco = ((float_t)hsivalue / (float_t)pllm) * ((float_t)(uint32_t)(RCC->PLL1DIVR & R
 2037              		.loc 1 1438 15 is_stmt 1 view .LVU672
 2038              		.loc 1 1438 25 is_stmt 0 view .LVU673
 2039 0084 07EE103A 		vmov	s14, r3	@ int
 2040 0088 F8EE476A 		vcvt.f32.u32	s13, s14
 2041              		.loc 1 1438 45 view .LVU674
 2042 008c 07EE100A 		vmov	s14, r0	@ int
 2043 0090 B8EE476A 		vcvt.f32.u32	s12, s14
 2044              		.loc 1 1438 43 view .LVU675
 2045 0094 86EE867A 		vdiv.f32	s14, s13, s12
 2046              		.loc 1 1438 86 view .LVU676
 2047 0098 0B6B     		ldr	r3, [r1, #48]
 2048              	.LVL168:
 2049              		.loc 1 1438 72 view .LVU677
 2050 009a C3F30803 		ubfx	r3, r3, #0, #9
 2051              		.loc 1 1438 63 view .LVU678
 2052 009e 06EE903A 		vmov	s13, r3	@ int
 2053 00a2 F8EE666A 		vcvt.f32.u32	s13, s13
 2054              		.loc 1 1438 126 view .LVU679
 2055 00a6 9FED4E6A 		vldr.32	s12, .L186+8
 2056 00aa 67EE867A 		vmul.f32	s15, s15, s12
 2057              	.LVL169:
 2058              		.loc 1 1438 116 view .LVU680
 2059 00ae 76EEA77A 		vadd.f32	s15, s13, s15
 2060              		.loc 1 1438 145 view .LVU681
 2061 00b2 F7EE006A 		vmov.f32	s13, #1.0e+0
 2062 00b6 77EEA67A 		vadd.f32	s15, s15, s13
 2063              		.loc 1 1438 22 view .LVU682
 2064 00ba 27EE277A 		vmul.f32	s14, s14, s15
 2065              	.LVL170:
 2066              		.loc 1 1438 22 view .LVU683
 2067 00be 38E0     		b	.L179
 2068              	.LVL171:
 2069              	.L178:
1439:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****             }
1440:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****             else
1441:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****             {
1442:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****               pllvco = ((float_t)HSI_VALUE / (float_t)pllm) * ((float_t)(uint32_t)(RCC->PLL1DIVR & 
 2070              		.loc 1 1442 15 is_stmt 1 view .LVU684
 2071              		.loc 1 1442 46 is_stmt 0 view .LVU685
 2072 00c0 07EE100A 		vmov	s14, r0	@ int
 2073 00c4 F8EE476A 		vcvt.f32.u32	s13, s14
 2074              		.loc 1 1442 44 view .LVU686
 2075 00c8 9FED466A 		vldr.32	s12, .L186+12
 2076 00cc 86EE267A 		vdiv.f32	s14, s12, s13
 2077              		.loc 1 1442 87 view .LVU687
 2078 00d0 414B     		ldr	r3, .L186
 2079 00d2 1B6B     		ldr	r3, [r3, #48]
 2080              		.loc 1 1442 73 view .LVU688
 2081 00d4 C3F30803 		ubfx	r3, r3, #0, #9
 2082              		.loc 1 1442 64 view .LVU689
 2083 00d8 06EE903A 		vmov	s13, r3	@ int
 2084 00dc F8EE666A 		vcvt.f32.u32	s13, s13
 2085              		.loc 1 1442 127 view .LVU690
 2086 00e0 9FED3F6A 		vldr.32	s12, .L186+8
 2087 00e4 67EE867A 		vmul.f32	s15, s15, s12
 2088              	.LVL172:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 68


 2089              		.loc 1 1442 117 view .LVU691
 2090 00e8 76EEA77A 		vadd.f32	s15, s13, s15
 2091              		.loc 1 1442 146 view .LVU692
 2092 00ec F7EE006A 		vmov.f32	s13, #1.0e+0
 2093 00f0 77EEA67A 		vadd.f32	s15, s15, s13
 2094              		.loc 1 1442 22 view .LVU693
 2095 00f4 27EE277A 		vmul.f32	s14, s14, s15
 2096              	.LVL173:
 2097              		.loc 1 1442 22 view .LVU694
 2098 00f8 1BE0     		b	.L179
 2099              	.LVL174:
 2100              	.L175:
1443:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****             }
1444:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****             break;
1445:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1446:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           case RCC_PLLSOURCE_CSI:  /* CSI used as PLL clock source */
1447:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****             pllvco = ((float_t)CSI_VALUE / (float_t)pllm) * ((float_t)(uint32_t)(RCC->PLL1DIVR & RC
 2101              		.loc 1 1447 13 is_stmt 1 view .LVU695
 2102              		.loc 1 1447 44 is_stmt 0 view .LVU696
 2103 00fa 07EE100A 		vmov	s14, r0	@ int
 2104 00fe F8EE476A 		vcvt.f32.u32	s13, s14
 2105              		.loc 1 1447 42 view .LVU697
 2106 0102 9FED396A 		vldr.32	s12, .L186+16
 2107 0106 86EE267A 		vdiv.f32	s14, s12, s13
 2108              		.loc 1 1447 85 view .LVU698
 2109 010a 334B     		ldr	r3, .L186
 2110 010c 1B6B     		ldr	r3, [r3, #48]
 2111              		.loc 1 1447 71 view .LVU699
 2112 010e C3F30803 		ubfx	r3, r3, #0, #9
 2113              		.loc 1 1447 62 view .LVU700
 2114 0112 06EE903A 		vmov	s13, r3	@ int
 2115 0116 F8EE666A 		vcvt.f32.u32	s13, s13
 2116              		.loc 1 1447 125 view .LVU701
 2117 011a 9FED316A 		vldr.32	s12, .L186+8
 2118 011e 67EE867A 		vmul.f32	s15, s15, s12
 2119              	.LVL175:
 2120              		.loc 1 1447 115 view .LVU702
 2121 0122 76EEA77A 		vadd.f32	s15, s13, s15
 2122              		.loc 1 1447 144 view .LVU703
 2123 0126 F7EE006A 		vmov.f32	s13, #1.0e+0
 2124 012a 77EEA67A 		vadd.f32	s15, s15, s13
 2125              		.loc 1 1447 20 view .LVU704
 2126 012e 27EE277A 		vmul.f32	s14, s14, s15
 2127              	.LVL176:
1448:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****             break;
 2128              		.loc 1 1448 13 is_stmt 1 view .LVU705
 2129              	.L179:
1449:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1450:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           case RCC_PLLSOURCE_HSE:  /* HSE used as PLL clock source */
1451:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****             pllvco = ((float_t)HSE_VALUE / (float_t)pllm) * ((float_t)(uint32_t)(RCC->PLL1DIVR & RC
1452:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****             break;
1453:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1454:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****           default:
1455:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****             pllvco = ((float_t)CSI_VALUE / (float_t)pllm) * ((float_t)(uint32_t)(RCC->PLL1DIVR & RC
1456:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****             break;
1457:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         }
1458:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         pllp = (((RCC->PLL1DIVR & RCC_PLL1DIVR_P1) >> 9) + 1U) ;
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 69


 2130              		.loc 1 1458 9 view .LVU706
 2131              		.loc 1 1458 22 is_stmt 0 view .LVU707
 2132 0132 294B     		ldr	r3, .L186
 2133 0134 1B6B     		ldr	r3, [r3, #48]
 2134              		.loc 1 1458 52 view .LVU708
 2135 0136 C3F34623 		ubfx	r3, r3, #9, #7
 2136              		.loc 1 1458 14 view .LVU709
 2137 013a 0133     		adds	r3, r3, #1
 2138              	.LVL177:
1459:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         sysclockfreq = (uint32_t)(float_t)(pllvco / (float_t)pllp);
 2139              		.loc 1 1459 9 is_stmt 1 view .LVU710
 2140              		.loc 1 1459 53 is_stmt 0 view .LVU711
 2141 013c 07EE903A 		vmov	s15, r3	@ int
 2142 0140 F8EE677A 		vcvt.f32.u32	s15, s15
 2143              		.loc 1 1459 34 view .LVU712
 2144 0144 C7EE276A 		vdiv.f32	s13, s14, s15
 2145              		.loc 1 1459 22 view .LVU713
 2146 0148 FCEEE67A 		vcvt.u32.f32	s15, s13
 2147 014c 17EE900A 		vmov	r0, s15	@ int
 2148              	.LVL178:
 2149              	.L172:
1460:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       }
1461:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       else
1462:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
1463:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         sysclockfreq = 0U;
1464:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       }
1465:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       break;
1466:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1467:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     default:
1468:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       sysclockfreq = CSI_VALUE;
1469:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       break;
1470:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
1471:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1472:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   return sysclockfreq;
1473:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** }
 2150              		.loc 1 1473 1 view .LVU714
 2151 0150 5DF8044B 		ldr	r4, [sp], #4
 2152              	.LCFI9:
 2153              		.cfi_remember_state
 2154              		.cfi_restore 4
 2155              		.cfi_def_cfa_offset 0
 2156              	.LVL179:
 2157              		.loc 1 1473 1 view .LVU715
 2158 0154 7047     		bx	lr
 2159              	.LVL180:
 2160              	.L176:
 2161              	.LCFI10:
 2162              		.cfi_restore_state
1451:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****             break;
 2163              		.loc 1 1451 13 is_stmt 1 view .LVU716
1451:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****             break;
 2164              		.loc 1 1451 44 is_stmt 0 view .LVU717
 2165 0156 07EE100A 		vmov	s14, r0	@ int
 2166 015a F8EE476A 		vcvt.f32.u32	s13, s14
1451:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****             break;
 2167              		.loc 1 1451 42 view .LVU718
 2168 015e 9FED236A 		vldr.32	s12, .L186+20
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 70


 2169 0162 86EE267A 		vdiv.f32	s14, s12, s13
1451:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****             break;
 2170              		.loc 1 1451 85 view .LVU719
 2171 0166 1C4B     		ldr	r3, .L186
 2172 0168 1B6B     		ldr	r3, [r3, #48]
1451:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****             break;
 2173              		.loc 1 1451 71 view .LVU720
 2174 016a C3F30803 		ubfx	r3, r3, #0, #9
1451:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****             break;
 2175              		.loc 1 1451 62 view .LVU721
 2176 016e 06EE903A 		vmov	s13, r3	@ int
 2177 0172 F8EE666A 		vcvt.f32.u32	s13, s13
1451:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****             break;
 2178              		.loc 1 1451 125 view .LVU722
 2179 0176 9FED1A6A 		vldr.32	s12, .L186+8
 2180 017a 67EE867A 		vmul.f32	s15, s15, s12
 2181              	.LVL181:
1451:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****             break;
 2182              		.loc 1 1451 115 view .LVU723
 2183 017e 76EEA77A 		vadd.f32	s15, s13, s15
1451:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****             break;
 2184              		.loc 1 1451 144 view .LVU724
 2185 0182 F7EE006A 		vmov.f32	s13, #1.0e+0
 2186 0186 77EEA67A 		vadd.f32	s15, s15, s13
1451:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****             break;
 2187              		.loc 1 1451 20 view .LVU725
 2188 018a 27EE277A 		vmul.f32	s14, s14, s15
 2189              	.LVL182:
1452:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 2190              		.loc 1 1452 13 is_stmt 1 view .LVU726
 2191 018e D0E7     		b	.L179
 2192              	.LVL183:
 2193              	.L177:
1455:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****             break;
 2194              		.loc 1 1455 13 view .LVU727
1455:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****             break;
 2195              		.loc 1 1455 44 is_stmt 0 view .LVU728
 2196 0190 07EE100A 		vmov	s14, r0	@ int
 2197 0194 F8EE476A 		vcvt.f32.u32	s13, s14
1455:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****             break;
 2198              		.loc 1 1455 42 view .LVU729
 2199 0198 9FED136A 		vldr.32	s12, .L186+16
 2200 019c 86EE267A 		vdiv.f32	s14, s12, s13
1455:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****             break;
 2201              		.loc 1 1455 85 view .LVU730
 2202 01a0 0D4B     		ldr	r3, .L186
 2203 01a2 1B6B     		ldr	r3, [r3, #48]
1455:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****             break;
 2204              		.loc 1 1455 71 view .LVU731
 2205 01a4 C3F30803 		ubfx	r3, r3, #0, #9
1455:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****             break;
 2206              		.loc 1 1455 62 view .LVU732
 2207 01a8 06EE903A 		vmov	s13, r3	@ int
 2208 01ac F8EE666A 		vcvt.f32.u32	s13, s13
1455:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****             break;
 2209              		.loc 1 1455 125 view .LVU733
 2210 01b0 9FED0B6A 		vldr.32	s12, .L186+8
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 71


 2211 01b4 67EE867A 		vmul.f32	s15, s15, s12
 2212              	.LVL184:
1455:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****             break;
 2213              		.loc 1 1455 115 view .LVU734
 2214 01b8 76EEA77A 		vadd.f32	s15, s13, s15
1455:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****             break;
 2215              		.loc 1 1455 144 view .LVU735
 2216 01bc F7EE006A 		vmov.f32	s13, #1.0e+0
 2217 01c0 77EEA67A 		vadd.f32	s15, s15, s13
1455:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****             break;
 2218              		.loc 1 1455 20 view .LVU736
 2219 01c4 27EE277A 		vmul.f32	s14, s14, s15
 2220              	.LVL185:
1456:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****         }
 2221              		.loc 1 1456 13 is_stmt 1 view .LVU737
 2222 01c8 B3E7     		b	.L179
 2223              	.LVL186:
 2224              	.L180:
 2225              	.LCFI11:
 2226              		.cfi_def_cfa_offset 0
 2227              		.cfi_restore 4
1416:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       break;
 2228              		.loc 1 1416 20 is_stmt 0 view .LVU738
 2229 01ca 0948     		ldr	r0, .L186+24
 2230 01cc 7047     		bx	lr
 2231              	.L181:
1396:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
 2232              		.loc 1 1396 3 view .LVU739
 2233 01ce 0948     		ldr	r0, .L186+28
 2234 01d0 7047     		bx	lr
 2235              	.L182:
1406:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       }
 2236              		.loc 1 1406 22 view .LVU740
 2237 01d2 0248     		ldr	r0, .L186+4
 2238              	.LVL187:
1472:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** }
 2239              		.loc 1 1472 3 is_stmt 1 view .LVU741
 2240              		.loc 1 1473 1 is_stmt 0 view .LVU742
 2241 01d4 7047     		bx	lr
 2242              	.L187:
 2243 01d6 00BF     		.align	2
 2244              	.L186:
 2245 01d8 00440258 		.word	1*********
 2246 01dc 0090D003 		.word	64000000
 2247 01e0 00000039 		.word	*********
 2248 01e4 0024744C 		.word	1282679808
 2249 01e8 0024744A 		.word	1249125376
 2250 01ec 0024F44A 		.word	1257513984
 2251 01f0 00127A00 		.word	8000000
 2252 01f4 00093D00 		.word	4000000
 2253              		.cfi_endproc
 2254              	.LFE150:
 2256              		.section	.text.HAL_RCC_ClockConfig,"ax",%progbits
 2257              		.align	1
 2258              		.global	HAL_RCC_ClockConfig
 2259              		.syntax unified
 2260              		.thumb
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 72


 2261              		.thumb_func
 2263              	HAL_RCC_ClockConfig:
 2264              	.LVL188:
 2265              	.LFB146:
 923:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   HAL_StatusTypeDef halstatus;
 2266              		.loc 1 923 1 is_stmt 1 view -0
 2267              		.cfi_startproc
 2268              		@ args = 0, pretend = 0, frame = 0
 2269              		@ frame_needed = 0, uses_anonymous_args = 0
 924:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   uint32_t tickstart;
 2270              		.loc 1 924 3 view .LVU744
 925:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   uint32_t common_system_clock;
 2271              		.loc 1 925 3 view .LVU745
 926:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 2272              		.loc 1 926 3 view .LVU746
 929:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
 2273              		.loc 1 929 3 view .LVU747
 929:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
 2274              		.loc 1 929 6 is_stmt 0 view .LVU748
 2275 0000 0028     		cmp	r0, #0
 2276 0002 00F03281 		beq	.L209
 923:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   HAL_StatusTypeDef halstatus;
 2277              		.loc 1 923 1 view .LVU749
 2278 0006 70B5     		push	{r4, r5, r6, lr}
 2279              	.LCFI12:
 2280              		.cfi_def_cfa_offset 16
 2281              		.cfi_offset 4, -16
 2282              		.cfi_offset 5, -12
 2283              		.cfi_offset 6, -8
 2284              		.cfi_offset 14, -4
 2285 0008 0D46     		mov	r5, r1
 2286 000a 0446     		mov	r4, r0
 935:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   assert_param(IS_FLASH_LATENCY(FLatency));
 2287              		.loc 1 935 3 is_stmt 1 view .LVU750
 936:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 2288              		.loc 1 936 3 view .LVU751
 943:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
 2289              		.loc 1 943 3 view .LVU752
 943:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
 2290              		.loc 1 943 18 is_stmt 0 view .LVU753
 2291 000c 9B4B     		ldr	r3, .L225
 2292 000e 1B68     		ldr	r3, [r3]
 2293 0010 03F00F03 		and	r3, r3, #15
 943:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
 2294              		.loc 1 943 6 view .LVU754
 2295 0014 8B42     		cmp	r3, r1
 2296 0016 0BD2     		bcs	.L190
 946:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 2297              		.loc 1 946 5 is_stmt 1 view .LVU755
 2298 0018 984A     		ldr	r2, .L225
 2299 001a 1368     		ldr	r3, [r2]
 2300 001c 23F00F03 		bic	r3, r3, #15
 2301 0020 0B43     		orrs	r3, r3, r1
 2302 0022 1360     		str	r3, [r2]
 950:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 2303              		.loc 1 950 5 view .LVU756
 950:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 73


 2304              		.loc 1 950 9 is_stmt 0 view .LVU757
 2305 0024 1368     		ldr	r3, [r2]
 2306 0026 03F00F03 		and	r3, r3, #15
 950:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 2307              		.loc 1 950 8 view .LVU758
 2308 002a 8B42     		cmp	r3, r1
 2309 002c 40F01F81 		bne	.L210
 2310              	.L190:
 959:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
 2311              		.loc 1 959 3 is_stmt 1 view .LVU759
 959:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
 2312              		.loc 1 959 26 is_stmt 0 view .LVU760
 2313 0030 2368     		ldr	r3, [r4]
 959:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
 2314              		.loc 1 959 6 view .LVU761
 2315 0032 13F0040F 		tst	r3, #4
 2316 0036 0CD0     		beq	.L191
 962:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 2317              		.loc 1 962 5 is_stmt 1 view .LVU762
 962:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 2318              		.loc 1 962 27 is_stmt 0 view .LVU763
 2319 0038 2269     		ldr	r2, [r4, #16]
 962:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 2320              		.loc 1 962 51 view .LVU764
 2321 003a 914B     		ldr	r3, .L225+4
 2322 003c 9B69     		ldr	r3, [r3, #24]
 962:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 2323              		.loc 1 962 60 view .LVU765
 2324 003e 03F07003 		and	r3, r3, #112
 962:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 2325              		.loc 1 962 8 view .LVU766
 2326 0042 9A42     		cmp	r2, r3
 2327 0044 05D9     		bls	.L191
 964:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       MODIFY_REG(RCC->D1CFGR, RCC_D1CFGR_D1PPRE, RCC_ClkInitStruct->APB3CLKDivider);
 2328              		.loc 1 964 7 is_stmt 1 view .LVU767
 965:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     }
 2329              		.loc 1 965 7 view .LVU768
 2330 0046 8E49     		ldr	r1, .L225+4
 2331              	.LVL189:
 965:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     }
 2332              		.loc 1 965 7 is_stmt 0 view .LVU769
 2333 0048 8B69     		ldr	r3, [r1, #24]
 2334 004a 23F07003 		bic	r3, r3, #112
 2335 004e 1A43     		orrs	r2, r2, r3
 2336 0050 8A61     		str	r2, [r1, #24]
 2337              	.L191:
 977:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
 2338              		.loc 1 977 3 is_stmt 1 view .LVU770
 977:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
 2339              		.loc 1 977 26 is_stmt 0 view .LVU771
 2340 0052 2368     		ldr	r3, [r4]
 977:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
 2341              		.loc 1 977 6 view .LVU772
 2342 0054 13F0080F 		tst	r3, #8
 2343 0058 0CD0     		beq	.L192
 980:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 2344              		.loc 1 980 5 is_stmt 1 view .LVU773
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 74


 980:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 2345              		.loc 1 980 27 is_stmt 0 view .LVU774
 2346 005a 6269     		ldr	r2, [r4, #20]
 980:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 2347              		.loc 1 980 51 view .LVU775
 2348 005c 884B     		ldr	r3, .L225+4
 2349 005e DB69     		ldr	r3, [r3, #28]
 980:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 2350              		.loc 1 980 60 view .LVU776
 2351 0060 03F07003 		and	r3, r3, #112
 980:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 2352              		.loc 1 980 8 view .LVU777
 2353 0064 9A42     		cmp	r2, r3
 2354 0066 05D9     		bls	.L192
 982:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       MODIFY_REG(RCC->D2CFGR, RCC_D2CFGR_D2PPRE1, (RCC_ClkInitStruct->APB1CLKDivider));
 2355              		.loc 1 982 7 is_stmt 1 view .LVU778
 983:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     }
 2356              		.loc 1 983 7 view .LVU779
 2357 0068 8549     		ldr	r1, .L225+4
 2358 006a CB69     		ldr	r3, [r1, #28]
 2359 006c 23F07003 		bic	r3, r3, #112
 2360 0070 1A43     		orrs	r2, r2, r3
 2361 0072 CA61     		str	r2, [r1, #28]
 2362              	.L192:
 994:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
 2363              		.loc 1 994 3 view .LVU780
 994:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
 2364              		.loc 1 994 26 is_stmt 0 view .LVU781
 2365 0074 2368     		ldr	r3, [r4]
 994:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
 2366              		.loc 1 994 6 view .LVU782
 2367 0076 13F0100F 		tst	r3, #16
 2368 007a 0CD0     		beq	.L193
 997:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 2369              		.loc 1 997 5 is_stmt 1 view .LVU783
 997:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 2370              		.loc 1 997 27 is_stmt 0 view .LVU784
 2371 007c A269     		ldr	r2, [r4, #24]
 997:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 2372              		.loc 1 997 51 view .LVU785
 2373 007e 804B     		ldr	r3, .L225+4
 2374 0080 DB69     		ldr	r3, [r3, #28]
 997:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 2375              		.loc 1 997 60 view .LVU786
 2376 0082 03F4E063 		and	r3, r3, #1792
 997:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 2377              		.loc 1 997 8 view .LVU787
 2378 0086 9A42     		cmp	r2, r3
 2379 0088 05D9     		bls	.L193
 999:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       MODIFY_REG(RCC->D2CFGR, RCC_D2CFGR_D2PPRE2, (RCC_ClkInitStruct->APB2CLKDivider));
 2380              		.loc 1 999 7 is_stmt 1 view .LVU788
1000:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     }
 2381              		.loc 1 1000 7 view .LVU789
 2382 008a 7D49     		ldr	r1, .L225+4
 2383 008c CB69     		ldr	r3, [r1, #28]
 2384 008e 23F4E063 		bic	r3, r3, #1792
 2385 0092 1A43     		orrs	r2, r2, r3
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 75


 2386 0094 CA61     		str	r2, [r1, #28]
 2387              	.L193:
1012:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
 2388              		.loc 1 1012 3 view .LVU790
1012:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
 2389              		.loc 1 1012 26 is_stmt 0 view .LVU791
 2390 0096 2368     		ldr	r3, [r4]
1012:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
 2391              		.loc 1 1012 6 view .LVU792
 2392 0098 13F0200F 		tst	r3, #32
 2393 009c 0CD0     		beq	.L194
1015:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 2394              		.loc 1 1015 5 is_stmt 1 view .LVU793
1015:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 2395              		.loc 1 1015 27 is_stmt 0 view .LVU794
 2396 009e E269     		ldr	r2, [r4, #28]
1015:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 2397              		.loc 1 1015 51 view .LVU795
 2398 00a0 774B     		ldr	r3, .L225+4
 2399 00a2 1B6A     		ldr	r3, [r3, #32]
1015:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 2400              		.loc 1 1015 60 view .LVU796
 2401 00a4 03F07003 		and	r3, r3, #112
1015:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 2402              		.loc 1 1015 8 view .LVU797
 2403 00a8 9A42     		cmp	r2, r3
 2404 00aa 05D9     		bls	.L194
1017:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       MODIFY_REG(RCC->D3CFGR, RCC_D3CFGR_D3PPRE, (RCC_ClkInitStruct->APB4CLKDivider));
 2405              		.loc 1 1017 7 is_stmt 1 view .LVU798
1018:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     }
 2406              		.loc 1 1018 7 view .LVU799
 2407 00ac 7449     		ldr	r1, .L225+4
 2408 00ae 0B6A     		ldr	r3, [r1, #32]
 2409 00b0 23F07003 		bic	r3, r3, #112
 2410 00b4 1A43     		orrs	r2, r2, r3
 2411 00b6 0A62     		str	r2, [r1, #32]
 2412              	.L194:
1030:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
 2413              		.loc 1 1030 3 view .LVU800
1030:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
 2414              		.loc 1 1030 26 is_stmt 0 view .LVU801
 2415 00b8 2368     		ldr	r3, [r4]
1030:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
 2416              		.loc 1 1030 6 view .LVU802
 2417 00ba 13F0020F 		tst	r3, #2
 2418 00be 0CD0     		beq	.L195
1033:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 2419              		.loc 1 1033 5 is_stmt 1 view .LVU803
1033:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 2420              		.loc 1 1033 27 is_stmt 0 view .LVU804
 2421 00c0 E268     		ldr	r2, [r4, #12]
1033:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 2422              		.loc 1 1033 50 view .LVU805
 2423 00c2 6F4B     		ldr	r3, .L225+4
 2424 00c4 9B69     		ldr	r3, [r3, #24]
1033:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 2425              		.loc 1 1033 59 view .LVU806
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 76


 2426 00c6 03F00F03 		and	r3, r3, #15
1033:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 2427              		.loc 1 1033 8 view .LVU807
 2428 00ca 9A42     		cmp	r2, r3
 2429 00cc 05D9     		bls	.L195
1036:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       MODIFY_REG(RCC->D1CFGR, RCC_D1CFGR_HPRE, RCC_ClkInitStruct->AHBCLKDivider);
 2430              		.loc 1 1036 7 is_stmt 1 view .LVU808
1037:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     }
 2431              		.loc 1 1037 7 view .LVU809
 2432 00ce 6C49     		ldr	r1, .L225+4
 2433 00d0 8B69     		ldr	r3, [r1, #24]
 2434 00d2 23F00F03 		bic	r3, r3, #15
 2435 00d6 1A43     		orrs	r2, r2, r3
 2436 00d8 8A61     		str	r2, [r1, #24]
 2437              	.L195:
1050:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
 2438              		.loc 1 1050 3 view .LVU810
1050:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
 2439              		.loc 1 1050 26 is_stmt 0 view .LVU811
 2440 00da 2368     		ldr	r3, [r4]
1050:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
 2441              		.loc 1 1050 6 view .LVU812
 2442 00dc 13F0010F 		tst	r3, #1
 2443 00e0 41D0     		beq	.L196
1052:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     assert_param(IS_RCC_SYSCLKSOURCE(RCC_ClkInitStruct->SYSCLKSource));
 2444              		.loc 1 1052 5 is_stmt 1 view .LVU813
1053:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #if defined(RCC_D1CFGR_D1CPRE)
 2445              		.loc 1 1053 5 view .LVU814
1055:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #else
 2446              		.loc 1 1055 5 view .LVU815
 2447 00e2 674A     		ldr	r2, .L225+4
 2448 00e4 9369     		ldr	r3, [r2, #24]
 2449 00e6 23F47063 		bic	r3, r3, #3840
 2450 00ea A168     		ldr	r1, [r4, #8]
 2451 00ec 0B43     		orrs	r3, r3, r1
 2452 00ee 9361     		str	r3, [r2, #24]
1060:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 2453              		.loc 1 1060 5 view .LVU816
1060:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 2454              		.loc 1 1060 26 is_stmt 0 view .LVU817
 2455 00f0 6368     		ldr	r3, [r4, #4]
1060:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 2456              		.loc 1 1060 8 view .LVU818
 2457 00f2 022B     		cmp	r3, #2
 2458 00f4 0AD0     		beq	.L221
1069:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 2459              		.loc 1 1069 10 is_stmt 1 view .LVU819
1069:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 2460              		.loc 1 1069 13 is_stmt 0 view .LVU820
 2461 00f6 032B     		cmp	r3, #3
 2462 00f8 27D0     		beq	.L222
1078:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 2463              		.loc 1 1078 10 is_stmt 1 view .LVU821
1078:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 2464              		.loc 1 1078 13 is_stmt 0 view .LVU822
 2465 00fa 012B     		cmp	r3, #1
 2466 00fc 2CD0     		beq	.L223
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 77


1090:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
 2467              		.loc 1 1090 7 is_stmt 1 view .LVU823
1090:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
 2468              		.loc 1 1090 11 is_stmt 0 view .LVU824
 2469 00fe 604A     		ldr	r2, .L225+4
 2470 0100 1268     		ldr	r2, [r2]
1090:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
 2471              		.loc 1 1090 10 view .LVU825
 2472 0102 12F0040F 		tst	r2, #4
 2473 0106 06D1     		bne	.L198
1092:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       }
 2474              		.loc 1 1092 16 view .LVU826
 2475 0108 0120     		movs	r0, #1
 2476              	.LVL190:
1092:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       }
 2477              		.loc 1 1092 16 view .LVU827
 2478 010a ADE0     		b	.L189
 2479              	.LVL191:
 2480              	.L221:
1063:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
 2481              		.loc 1 1063 7 is_stmt 1 view .LVU828
1063:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
 2482              		.loc 1 1063 11 is_stmt 0 view .LVU829
 2483 010c 1268     		ldr	r2, [r2]
1063:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
 2484              		.loc 1 1063 10 view .LVU830
 2485 010e 12F4003F 		tst	r2, #131072
 2486 0112 00F0AE80 		beq	.L224
 2487              	.L198:
1095:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 2488              		.loc 1 1095 5 is_stmt 1 view .LVU831
 2489 0116 5A49     		ldr	r1, .L225+4
 2490 0118 0A69     		ldr	r2, [r1, #16]
 2491 011a 22F00702 		bic	r2, r2, #7
 2492 011e 1343     		orrs	r3, r3, r2
 2493 0120 0B61     		str	r3, [r1, #16]
1098:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 2494              		.loc 1 1098 5 view .LVU832
1098:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 2495              		.loc 1 1098 17 is_stmt 0 view .LVU833
 2496 0122 FFF7FEFF 		bl	HAL_GetTick
 2497              	.LVL192:
1098:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 2498              		.loc 1 1098 17 view .LVU834
 2499 0126 0646     		mov	r6, r0
 2500              	.LVL193:
1100:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 2501              		.loc 1 1100 5 is_stmt 1 view .LVU835
 2502              	.L201:
1100:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 2503              		.loc 1 1100 42 view .LVU836
1100:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 2504              		.loc 1 1100 12 is_stmt 0 view .LVU837
 2505 0128 554B     		ldr	r3, .L225+4
 2506 012a 1B69     		ldr	r3, [r3, #16]
 2507 012c 03F03803 		and	r3, r3, #56
1100:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 78


 2508              		.loc 1 1100 63 view .LVU838
 2509 0130 6268     		ldr	r2, [r4, #4]
1100:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 2510              		.loc 1 1100 42 view .LVU839
 2511 0132 B3EBC20F 		cmp	r3, r2, lsl #3
 2512 0136 16D0     		beq	.L196
1102:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
 2513              		.loc 1 1102 7 is_stmt 1 view .LVU840
1102:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
 2514              		.loc 1 1102 12 is_stmt 0 view .LVU841
 2515 0138 FFF7FEFF 		bl	HAL_GetTick
 2516              	.LVL194:
1102:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
 2517              		.loc 1 1102 26 discriminator 1 view .LVU842
 2518 013c 801B     		subs	r0, r0, r6
1102:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
 2519              		.loc 1 1102 10 discriminator 1 view .LVU843
 2520 013e 41F28833 		movw	r3, #5000
 2521 0142 9842     		cmp	r0, r3
 2522 0144 F0D9     		bls	.L201
1104:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       }
 2523              		.loc 1 1104 16 view .LVU844
 2524 0146 0320     		movs	r0, #3
 2525 0148 8EE0     		b	.L189
 2526              	.LVL195:
 2527              	.L222:
1072:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
 2528              		.loc 1 1072 7 is_stmt 1 view .LVU845
1072:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
 2529              		.loc 1 1072 11 is_stmt 0 view .LVU846
 2530 014a 4D4A     		ldr	r2, .L225+4
 2531 014c 1268     		ldr	r2, [r2]
1072:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
 2532              		.loc 1 1072 10 view .LVU847
 2533 014e 12F0007F 		tst	r2, #33554432
 2534 0152 E0D1     		bne	.L198
1074:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       }
 2535              		.loc 1 1074 16 view .LVU848
 2536 0154 0120     		movs	r0, #1
 2537              	.LVL196:
1074:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       }
 2538              		.loc 1 1074 16 view .LVU849
 2539 0156 87E0     		b	.L189
 2540              	.LVL197:
 2541              	.L223:
1081:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
 2542              		.loc 1 1081 7 is_stmt 1 view .LVU850
1081:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
 2543              		.loc 1 1081 11 is_stmt 0 view .LVU851
 2544 0158 494A     		ldr	r2, .L225+4
 2545 015a 1268     		ldr	r2, [r2]
1081:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       {
 2546              		.loc 1 1081 10 view .LVU852
 2547 015c 12F4807F 		tst	r2, #256
 2548 0160 D9D1     		bne	.L198
1083:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       }
 2549              		.loc 1 1083 16 view .LVU853
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 79


 2550 0162 0120     		movs	r0, #1
 2551              	.LVL198:
1083:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       }
 2552              		.loc 1 1083 16 view .LVU854
 2553 0164 80E0     		b	.L189
 2554              	.L196:
1112:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
 2555              		.loc 1 1112 3 is_stmt 1 view .LVU855
1112:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
 2556              		.loc 1 1112 26 is_stmt 0 view .LVU856
 2557 0166 2368     		ldr	r3, [r4]
1112:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
 2558              		.loc 1 1112 6 view .LVU857
 2559 0168 13F0020F 		tst	r3, #2
 2560 016c 0CD0     		beq	.L203
1115:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 2561              		.loc 1 1115 5 is_stmt 1 view .LVU858
1115:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 2562              		.loc 1 1115 27 is_stmt 0 view .LVU859
 2563 016e E268     		ldr	r2, [r4, #12]
1115:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 2564              		.loc 1 1115 50 view .LVU860
 2565 0170 434B     		ldr	r3, .L225+4
 2566 0172 9B69     		ldr	r3, [r3, #24]
1115:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 2567              		.loc 1 1115 59 view .LVU861
 2568 0174 03F00F03 		and	r3, r3, #15
1115:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 2569              		.loc 1 1115 8 view .LVU862
 2570 0178 9A42     		cmp	r2, r3
 2571 017a 05D2     		bcs	.L203
1118:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       MODIFY_REG(RCC->D1CFGR, RCC_D1CFGR_HPRE, RCC_ClkInitStruct->AHBCLKDivider);
 2572              		.loc 1 1118 7 is_stmt 1 view .LVU863
1119:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     }
 2573              		.loc 1 1119 7 view .LVU864
 2574 017c 4049     		ldr	r1, .L225+4
 2575 017e 8B69     		ldr	r3, [r1, #24]
 2576 0180 23F00F03 		bic	r3, r3, #15
 2577 0184 1A43     		orrs	r2, r2, r3
 2578 0186 8A61     		str	r2, [r1, #24]
 2579              	.L203:
1132:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
 2580              		.loc 1 1132 3 view .LVU865
1132:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
 2581              		.loc 1 1132 18 is_stmt 0 view .LVU866
 2582 0188 3C4B     		ldr	r3, .L225
 2583 018a 1B68     		ldr	r3, [r3]
 2584 018c 03F00F03 		and	r3, r3, #15
1132:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
 2585              		.loc 1 1132 6 view .LVU867
 2586 0190 AB42     		cmp	r3, r5
 2587 0192 0AD9     		bls	.L204
1135:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 2588              		.loc 1 1135 5 is_stmt 1 view .LVU868
 2589 0194 394A     		ldr	r2, .L225
 2590 0196 1368     		ldr	r3, [r2]
 2591 0198 23F00F03 		bic	r3, r3, #15
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 80


 2592 019c 2B43     		orrs	r3, r3, r5
 2593 019e 1360     		str	r3, [r2]
1139:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 2594              		.loc 1 1139 5 view .LVU869
1139:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 2595              		.loc 1 1139 9 is_stmt 0 view .LVU870
 2596 01a0 1368     		ldr	r3, [r2]
 2597 01a2 03F00F03 		and	r3, r3, #15
1139:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 2598              		.loc 1 1139 8 view .LVU871
 2599 01a6 AB42     		cmp	r3, r5
 2600 01a8 65D1     		bne	.L216
 2601              	.L204:
1146:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
 2602              		.loc 1 1146 3 is_stmt 1 view .LVU872
1146:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
 2603              		.loc 1 1146 26 is_stmt 0 view .LVU873
 2604 01aa 2368     		ldr	r3, [r4]
1146:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
 2605              		.loc 1 1146 6 view .LVU874
 2606 01ac 13F0040F 		tst	r3, #4
 2607 01b0 0CD0     		beq	.L205
1149:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 2608              		.loc 1 1149 5 is_stmt 1 view .LVU875
1149:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 2609              		.loc 1 1149 27 is_stmt 0 view .LVU876
 2610 01b2 2269     		ldr	r2, [r4, #16]
1149:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 2611              		.loc 1 1149 51 view .LVU877
 2612 01b4 324B     		ldr	r3, .L225+4
 2613 01b6 9B69     		ldr	r3, [r3, #24]
1149:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 2614              		.loc 1 1149 60 view .LVU878
 2615 01b8 03F07003 		and	r3, r3, #112
1149:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 2616              		.loc 1 1149 8 view .LVU879
 2617 01bc 9A42     		cmp	r2, r3
 2618 01be 05D2     		bcs	.L205
1151:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       MODIFY_REG(RCC->D1CFGR, RCC_D1CFGR_D1PPRE, RCC_ClkInitStruct->APB3CLKDivider);
 2619              		.loc 1 1151 7 is_stmt 1 view .LVU880
1152:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     }
 2620              		.loc 1 1152 7 view .LVU881
 2621 01c0 2F49     		ldr	r1, .L225+4
 2622 01c2 8B69     		ldr	r3, [r1, #24]
 2623 01c4 23F07003 		bic	r3, r3, #112
 2624 01c8 1A43     		orrs	r2, r2, r3
 2625 01ca 8A61     		str	r2, [r1, #24]
 2626              	.L205:
1164:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
 2627              		.loc 1 1164 3 view .LVU882
1164:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
 2628              		.loc 1 1164 26 is_stmt 0 view .LVU883
 2629 01cc 2368     		ldr	r3, [r4]
1164:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
 2630              		.loc 1 1164 6 view .LVU884
 2631 01ce 13F0080F 		tst	r3, #8
 2632 01d2 0CD0     		beq	.L206
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 81


1167:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 2633              		.loc 1 1167 5 is_stmt 1 view .LVU885
1167:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 2634              		.loc 1 1167 27 is_stmt 0 view .LVU886
 2635 01d4 6269     		ldr	r2, [r4, #20]
1167:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 2636              		.loc 1 1167 51 view .LVU887
 2637 01d6 2A4B     		ldr	r3, .L225+4
 2638 01d8 DB69     		ldr	r3, [r3, #28]
1167:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 2639              		.loc 1 1167 60 view .LVU888
 2640 01da 03F07003 		and	r3, r3, #112
1167:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 2641              		.loc 1 1167 8 view .LVU889
 2642 01de 9A42     		cmp	r2, r3
 2643 01e0 05D2     		bcs	.L206
1169:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       MODIFY_REG(RCC->D2CFGR, RCC_D2CFGR_D2PPRE1, (RCC_ClkInitStruct->APB1CLKDivider));
 2644              		.loc 1 1169 7 is_stmt 1 view .LVU890
1170:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     }
 2645              		.loc 1 1170 7 view .LVU891
 2646 01e2 2749     		ldr	r1, .L225+4
 2647 01e4 CB69     		ldr	r3, [r1, #28]
 2648 01e6 23F07003 		bic	r3, r3, #112
 2649 01ea 1A43     		orrs	r2, r2, r3
 2650 01ec CA61     		str	r2, [r1, #28]
 2651              	.L206:
1182:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
 2652              		.loc 1 1182 3 view .LVU892
1182:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
 2653              		.loc 1 1182 26 is_stmt 0 view .LVU893
 2654 01ee 2368     		ldr	r3, [r4]
1182:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
 2655              		.loc 1 1182 6 view .LVU894
 2656 01f0 13F0100F 		tst	r3, #16
 2657 01f4 0CD0     		beq	.L207
1185:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 2658              		.loc 1 1185 5 is_stmt 1 view .LVU895
1185:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 2659              		.loc 1 1185 27 is_stmt 0 view .LVU896
 2660 01f6 A269     		ldr	r2, [r4, #24]
1185:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 2661              		.loc 1 1185 51 view .LVU897
 2662 01f8 214B     		ldr	r3, .L225+4
 2663 01fa DB69     		ldr	r3, [r3, #28]
1185:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 2664              		.loc 1 1185 60 view .LVU898
 2665 01fc 03F4E063 		and	r3, r3, #1792
1185:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 2666              		.loc 1 1185 8 view .LVU899
 2667 0200 9A42     		cmp	r2, r3
 2668 0202 05D2     		bcs	.L207
1187:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       MODIFY_REG(RCC->D2CFGR, RCC_D2CFGR_D2PPRE2, (RCC_ClkInitStruct->APB2CLKDivider));
 2669              		.loc 1 1187 7 is_stmt 1 view .LVU900
1188:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     }
 2670              		.loc 1 1188 7 view .LVU901
 2671 0204 1E49     		ldr	r1, .L225+4
 2672 0206 CB69     		ldr	r3, [r1, #28]
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 82


 2673 0208 23F4E063 		bic	r3, r3, #1792
 2674 020c 1A43     		orrs	r2, r2, r3
 2675 020e CA61     		str	r2, [r1, #28]
 2676              	.L207:
1200:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
 2677              		.loc 1 1200 3 view .LVU902
1200:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
 2678              		.loc 1 1200 26 is_stmt 0 view .LVU903
 2679 0210 2368     		ldr	r3, [r4]
1200:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
 2680              		.loc 1 1200 6 view .LVU904
 2681 0212 13F0200F 		tst	r3, #32
 2682 0216 0CD0     		beq	.L208
1203:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 2683              		.loc 1 1203 5 is_stmt 1 view .LVU905
1203:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 2684              		.loc 1 1203 27 is_stmt 0 view .LVU906
 2685 0218 E269     		ldr	r2, [r4, #28]
1203:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 2686              		.loc 1 1203 51 view .LVU907
 2687 021a 194B     		ldr	r3, .L225+4
 2688 021c 1B6A     		ldr	r3, [r3, #32]
1203:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 2689              		.loc 1 1203 60 view .LVU908
 2690 021e 03F07003 		and	r3, r3, #112
1203:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     {
 2691              		.loc 1 1203 8 view .LVU909
 2692 0222 9A42     		cmp	r2, r3
 2693 0224 05D2     		bcs	.L208
1205:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       MODIFY_REG(RCC->D3CFGR, RCC_D3CFGR_D3PPRE, (RCC_ClkInitStruct->APB4CLKDivider));
 2694              		.loc 1 1205 7 is_stmt 1 view .LVU910
1206:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     }
 2695              		.loc 1 1206 7 view .LVU911
 2696 0226 1649     		ldr	r1, .L225+4
 2697 0228 0B6A     		ldr	r3, [r1, #32]
 2698 022a 23F07003 		bic	r3, r3, #112
 2699 022e 1A43     		orrs	r2, r2, r3
 2700 0230 0A62     		str	r2, [r1, #32]
 2701              	.L208:
1219:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #else
 2702              		.loc 1 1219 3 view .LVU912
1219:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #else
 2703              		.loc 1 1219 25 is_stmt 0 view .LVU913
 2704 0232 FFF7FEFF 		bl	HAL_RCC_GetSysClockFreq
 2705              	.LVL199:
1219:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #else
 2706              		.loc 1 1219 77 discriminator 1 view .LVU914
 2707 0236 1249     		ldr	r1, .L225+4
 2708 0238 8B69     		ldr	r3, [r1, #24]
1219:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #else
 2709              		.loc 1 1219 107 discriminator 1 view .LVU915
 2710 023a C3F30323 		ubfx	r3, r3, #8, #4
1219:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #else
 2711              		.loc 1 1219 72 discriminator 1 view .LVU916
 2712 023e 114A     		ldr	r2, .L225+8
 2713 0240 D35C     		ldrb	r3, [r2, r3]	@ zero_extendqisi2
1219:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #else
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 83


 2714              		.loc 1 1219 134 discriminator 1 view .LVU917
 2715 0242 03F01F03 		and	r3, r3, #31
1219:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #else
 2716              		.loc 1 1219 23 discriminator 1 view .LVU918
 2717 0246 D840     		lsrs	r0, r0, r3
 2718              	.LVL200:
1225:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #else
 2719              		.loc 1 1225 3 is_stmt 1 view .LVU919
1225:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #else
 2720              		.loc 1 1225 66 is_stmt 0 view .LVU920
 2721 0248 8B69     		ldr	r3, [r1, #24]
1225:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #else
 2722              		.loc 1 1225 94 view .LVU921
 2723 024a 03F00F03 		and	r3, r3, #15
1225:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #else
 2724              		.loc 1 1225 61 view .LVU922
 2725 024e D35C     		ldrb	r3, [r2, r3]	@ zero_extendqisi2
1225:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #else
 2726              		.loc 1 1225 119 view .LVU923
 2727 0250 03F01F03 		and	r3, r3, #31
1225:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #else
 2728              		.loc 1 1225 40 view .LVU924
 2729 0254 20FA03F3 		lsr	r3, r0, r3
1225:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #else
 2730              		.loc 1 1225 17 view .LVU925
 2731 0258 0B4A     		ldr	r2, .L225+12
 2732 025a 1360     		str	r3, [r2]
1233:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #endif /* DUAL_CORE && CORE_CM4 */
 2733              		.loc 1 1233 3 is_stmt 1 view .LVU926
1233:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #endif /* DUAL_CORE && CORE_CM4 */
 2734              		.loc 1 1233 19 is_stmt 0 view .LVU927
 2735 025c 0B4B     		ldr	r3, .L225+16
 2736 025e 1860     		str	r0, [r3]
1237:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 2737              		.loc 1 1237 3 is_stmt 1 view .LVU928
1237:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 2738              		.loc 1 1237 15 is_stmt 0 view .LVU929
 2739 0260 0B4B     		ldr	r3, .L225+20
 2740 0262 1868     		ldr	r0, [r3]
 2741              	.LVL201:
1237:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 2742              		.loc 1 1237 15 view .LVU930
 2743 0264 FFF7FEFF 		bl	HAL_InitTick
 2744              	.LVL202:
1239:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** }
 2745              		.loc 1 1239 3 is_stmt 1 view .LVU931
 2746              	.L189:
1240:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 2747              		.loc 1 1240 1 is_stmt 0 view .LVU932
 2748 0268 70BD     		pop	{r4, r5, r6, pc}
 2749              	.LVL203:
 2750              	.L209:
 2751              	.LCFI13:
 2752              		.cfi_def_cfa_offset 0
 2753              		.cfi_restore 4
 2754              		.cfi_restore 5
 2755              		.cfi_restore 6
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 84


 2756              		.cfi_restore 14
 931:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
 2757              		.loc 1 931 12 view .LVU933
 2758 026a 0120     		movs	r0, #1
 2759              	.LVL204:
1240:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 2760              		.loc 1 1240 1 view .LVU934
 2761 026c 7047     		bx	lr
 2762              	.LVL205:
 2763              	.L210:
 2764              	.LCFI14:
 2765              		.cfi_def_cfa_offset 16
 2766              		.cfi_offset 4, -16
 2767              		.cfi_offset 5, -12
 2768              		.cfi_offset 6, -8
 2769              		.cfi_offset 14, -4
 952:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     }
 2770              		.loc 1 952 14 view .LVU935
 2771 026e 0120     		movs	r0, #1
 2772              	.LVL206:
 952:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     }
 2773              		.loc 1 952 14 view .LVU936
 2774 0270 FAE7     		b	.L189
 2775              	.LVL207:
 2776              	.L224:
1065:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       }
 2777              		.loc 1 1065 16 view .LVU937
 2778 0272 0120     		movs	r0, #1
 2779              	.LVL208:
1065:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****       }
 2780              		.loc 1 1065 16 view .LVU938
 2781 0274 F8E7     		b	.L189
 2782              	.L216:
1141:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     }
 2783              		.loc 1 1141 14 view .LVU939
 2784 0276 0120     		movs	r0, #1
 2785 0278 F6E7     		b	.L189
 2786              	.L226:
 2787 027a 00BF     		.align	2
 2788              	.L225:
 2789 027c 00200052 		.word	1375739904
 2790 0280 00440258 		.word	1*********
 2791 0284 00000000 		.word	D1CorePrescTable
 2792 0288 00000000 		.word	SystemD2Clock
 2793 028c 00000000 		.word	SystemCoreClock
 2794 0290 00000000 		.word	uwTickPrio
 2795              		.cfi_endproc
 2796              	.LFE146:
 2798              		.section	.text.HAL_RCC_GetHCLKFreq,"ax",%progbits
 2799              		.align	1
 2800              		.global	HAL_RCC_GetHCLKFreq
 2801              		.syntax unified
 2802              		.thumb
 2803              		.thumb_func
 2805              	HAL_RCC_GetHCLKFreq:
 2806              	.LFB151:
1474:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 85


1475:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1476:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** /**
1477:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @brief  Returns the HCLK frequency
1478:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @note   Each time HCLK changes, this function must be called to update the
1479:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *         right HCLK value. Otherwise, any configuration based on this function will be incorrect
1480:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *
1481:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @note   The SystemD2Clock CMSIS variable is used to store System domain2 Clock Frequency
1482:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *         and updated within this function
1483:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @retval HCLK frequency
1484:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   */
1485:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** uint32_t HAL_RCC_GetHCLKFreq(void)
1486:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** {
 2807              		.loc 1 1486 1 is_stmt 1 view -0
 2808              		.cfi_startproc
 2809              		@ args = 0, pretend = 0, frame = 0
 2810              		@ frame_needed = 0, uses_anonymous_args = 0
 2811 0000 08B5     		push	{r3, lr}
 2812              	.LCFI15:
 2813              		.cfi_def_cfa_offset 8
 2814              		.cfi_offset 3, -8
 2815              		.cfi_offset 14, -4
1487:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   uint32_t common_system_clock;
 2816              		.loc 1 1487 3 view .LVU941
1488:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1489:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #if defined(RCC_D1CFGR_D1CPRE)
1490:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   common_system_clock = HAL_RCC_GetSysClockFreq() >> (D1CorePrescTable[(RCC->D1CFGR & RCC_D1CFGR_D1
 2817              		.loc 1 1490 3 view .LVU942
 2818              		.loc 1 1490 25 is_stmt 0 view .LVU943
 2819 0002 FFF7FEFF 		bl	HAL_RCC_GetSysClockFreq
 2820              	.LVL209:
 2821              		.loc 1 1490 76 discriminator 1 view .LVU944
 2822 0006 0B4A     		ldr	r2, .L229
 2823 0008 9369     		ldr	r3, [r2, #24]
 2824              		.loc 1 1490 106 discriminator 1 view .LVU945
 2825 000a C3F30323 		ubfx	r3, r3, #8, #4
 2826              		.loc 1 1490 71 discriminator 1 view .LVU946
 2827 000e 0A49     		ldr	r1, .L229+4
 2828 0010 CB5C     		ldrb	r3, [r1, r3]	@ zero_extendqisi2
 2829              		.loc 1 1490 132 discriminator 1 view .LVU947
 2830 0012 03F01F03 		and	r3, r3, #31
 2831              		.loc 1 1490 23 discriminator 1 view .LVU948
 2832 0016 20FA03F3 		lsr	r3, r0, r3
 2833              	.LVL210:
1491:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #else
1492:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   common_system_clock = HAL_RCC_GetSysClockFreq() >> (D1CorePrescTable[(RCC->CDCFGR1 & RCC_CDCFGR1_
1493:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #endif
1494:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1495:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #if defined(RCC_D1CFGR_HPRE)
1496:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   SystemD2Clock = (common_system_clock >> ((D1CorePrescTable[(RCC->D1CFGR & RCC_D1CFGR_HPRE) >> RCC
 2834              		.loc 1 1496 3 is_stmt 1 view .LVU949
 2835              		.loc 1 1496 66 is_stmt 0 view .LVU950
 2836 001a 9269     		ldr	r2, [r2, #24]
 2837              		.loc 1 1496 94 view .LVU951
 2838 001c 02F00F02 		and	r2, r2, #15
 2839              		.loc 1 1496 61 view .LVU952
 2840 0020 885C     		ldrb	r0, [r1, r2]	@ zero_extendqisi2
 2841              		.loc 1 1496 119 view .LVU953
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 86


 2842 0022 00F01F00 		and	r0, r0, #31
 2843              		.loc 1 1496 40 view .LVU954
 2844 0026 23FA00F0 		lsr	r0, r3, r0
 2845              		.loc 1 1496 17 view .LVU955
 2846 002a 044A     		ldr	r2, .L229+8
 2847 002c 1060     		str	r0, [r2]
1497:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #else
1498:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   SystemD2Clock = (common_system_clock >> ((D1CorePrescTable[(RCC->CDCFGR1 & RCC_CDCFGR1_HPRE) >> R
1499:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #endif
1500:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1501:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #if defined(DUAL_CORE) && defined(CORE_CM4)
1502:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   SystemCoreClock = SystemD2Clock;
1503:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #else
1504:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   SystemCoreClock = common_system_clock;
 2848              		.loc 1 1504 3 is_stmt 1 view .LVU956
 2849              		.loc 1 1504 19 is_stmt 0 view .LVU957
 2850 002e 044A     		ldr	r2, .L229+12
 2851 0030 1360     		str	r3, [r2]
1505:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #endif /* DUAL_CORE && CORE_CM4 */
1506:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1507:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   return SystemD2Clock;
 2852              		.loc 1 1507 3 is_stmt 1 view .LVU958
1508:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** }
 2853              		.loc 1 1508 1 is_stmt 0 view .LVU959
 2854 0032 08BD     		pop	{r3, pc}
 2855              	.LVL211:
 2856              	.L230:
 2857              		.loc 1 1508 1 view .LVU960
 2858              		.align	2
 2859              	.L229:
 2860 0034 00440258 		.word	1*********
 2861 0038 00000000 		.word	D1CorePrescTable
 2862 003c 00000000 		.word	SystemD2Clock
 2863 0040 00000000 		.word	SystemCoreClock
 2864              		.cfi_endproc
 2865              	.LFE151:
 2867              		.section	.text.HAL_RCC_GetPCLK1Freq,"ax",%progbits
 2868              		.align	1
 2869              		.global	HAL_RCC_GetPCLK1Freq
 2870              		.syntax unified
 2871              		.thumb
 2872              		.thumb_func
 2874              	HAL_RCC_GetPCLK1Freq:
 2875              	.LFB152:
1509:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1510:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1511:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** /**
1512:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @brief  Returns the PCLK1 frequency
1513:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @note   Each time PCLK1 changes, this function must be called to update the
1514:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *         right PCLK1 value. Otherwise, any configuration based on this function will be incorrec
1515:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @retval PCLK1 frequency
1516:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   */
1517:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** uint32_t HAL_RCC_GetPCLK1Freq(void)
1518:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** {
 2876              		.loc 1 1518 1 is_stmt 1 view -0
 2877              		.cfi_startproc
 2878              		@ args = 0, pretend = 0, frame = 0
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 87


 2879              		@ frame_needed = 0, uses_anonymous_args = 0
 2880 0000 08B5     		push	{r3, lr}
 2881              	.LCFI16:
 2882              		.cfi_def_cfa_offset 8
 2883              		.cfi_offset 3, -8
 2884              		.cfi_offset 14, -4
1519:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #if defined (RCC_D2CFGR_D2PPRE1)
1520:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Get HCLK source and Compute PCLK1 frequency ---------------------------*/
1521:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   return (HAL_RCC_GetHCLKFreq() >> ((D1CorePrescTable[(RCC->D2CFGR & RCC_D2CFGR_D2PPRE1) >> RCC_D2C
 2885              		.loc 1 1521 3 view .LVU962
 2886              		.loc 1 1521 11 is_stmt 0 view .LVU963
 2887 0002 FFF7FEFF 		bl	HAL_RCC_GetHCLKFreq
 2888              	.LVL212:
 2889              		.loc 1 1521 59 discriminator 1 view .LVU964
 2890 0006 054B     		ldr	r3, .L233
 2891 0008 DB69     		ldr	r3, [r3, #28]
 2892              		.loc 1 1521 90 discriminator 1 view .LVU965
 2893 000a C3F30213 		ubfx	r3, r3, #4, #3
 2894              		.loc 1 1521 54 discriminator 1 view .LVU966
 2895 000e 044A     		ldr	r2, .L233+4
 2896 0010 D35C     		ldrb	r3, [r2, r3]	@ zero_extendqisi2
 2897              		.loc 1 1521 118 discriminator 1 view .LVU967
 2898 0012 03F01F03 		and	r3, r3, #31
1522:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #else
1523:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Get HCLK source and Compute PCLK1 frequency ---------------------------*/
1524:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   return (HAL_RCC_GetHCLKFreq() >> ((D1CorePrescTable[(RCC->CDCFGR2 & RCC_CDCFGR2_CDPPRE1) >> RCC_C
1525:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #endif
1526:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** }
 2899              		.loc 1 1526 1 view .LVU968
 2900 0016 D840     		lsrs	r0, r0, r3
 2901 0018 08BD     		pop	{r3, pc}
 2902              	.L234:
 2903 001a 00BF     		.align	2
 2904              	.L233:
 2905 001c 00440258 		.word	1*********
 2906 0020 00000000 		.word	D1CorePrescTable
 2907              		.cfi_endproc
 2908              	.LFE152:
 2910              		.section	.text.HAL_RCC_GetPCLK2Freq,"ax",%progbits
 2911              		.align	1
 2912              		.global	HAL_RCC_GetPCLK2Freq
 2913              		.syntax unified
 2914              		.thumb
 2915              		.thumb_func
 2917              	HAL_RCC_GetPCLK2Freq:
 2918              	.LFB153:
1527:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1528:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1529:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** /**
1530:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @brief  Returns the D2 PCLK2 frequency
1531:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @note   Each time PCLK2 changes, this function must be called to update the
1532:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *         right PCLK2 value. Otherwise, any configuration based on this function will be incorrec
1533:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @retval PCLK1 frequency
1534:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   */
1535:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** uint32_t HAL_RCC_GetPCLK2Freq(void)
1536:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** {
 2919              		.loc 1 1536 1 is_stmt 1 view -0
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 88


 2920              		.cfi_startproc
 2921              		@ args = 0, pretend = 0, frame = 0
 2922              		@ frame_needed = 0, uses_anonymous_args = 0
 2923 0000 08B5     		push	{r3, lr}
 2924              	.LCFI17:
 2925              		.cfi_def_cfa_offset 8
 2926              		.cfi_offset 3, -8
 2927              		.cfi_offset 14, -4
1537:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Get HCLK source and Compute PCLK1 frequency ---------------------------*/
1538:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #if defined(RCC_D2CFGR_D2PPRE2)
1539:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   return (HAL_RCC_GetHCLKFreq() >> ((D1CorePrescTable[(RCC->D2CFGR & RCC_D2CFGR_D2PPRE2) >> RCC_D2C
 2928              		.loc 1 1539 3 view .LVU970
 2929              		.loc 1 1539 11 is_stmt 0 view .LVU971
 2930 0002 FFF7FEFF 		bl	HAL_RCC_GetHCLKFreq
 2931              	.LVL213:
 2932              		.loc 1 1539 59 discriminator 1 view .LVU972
 2933 0006 054B     		ldr	r3, .L237
 2934 0008 DB69     		ldr	r3, [r3, #28]
 2935              		.loc 1 1539 90 discriminator 1 view .LVU973
 2936 000a C3F30223 		ubfx	r3, r3, #8, #3
 2937              		.loc 1 1539 54 discriminator 1 view .LVU974
 2938 000e 044A     		ldr	r2, .L237+4
 2939 0010 D35C     		ldrb	r3, [r2, r3]	@ zero_extendqisi2
 2940              		.loc 1 1539 118 discriminator 1 view .LVU975
 2941 0012 03F01F03 		and	r3, r3, #31
1540:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #else
1541:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   return (HAL_RCC_GetHCLKFreq() >> ((D1CorePrescTable[(RCC->CDCFGR2 & RCC_CDCFGR2_CDPPRE2) >> RCC_C
1542:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #endif
1543:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** }
 2942              		.loc 1 1543 1 view .LVU976
 2943 0016 D840     		lsrs	r0, r0, r3
 2944 0018 08BD     		pop	{r3, pc}
 2945              	.L238:
 2946 001a 00BF     		.align	2
 2947              	.L237:
 2948 001c 00440258 		.word	1*********
 2949 0020 00000000 		.word	D1CorePrescTable
 2950              		.cfi_endproc
 2951              	.LFE153:
 2953              		.section	.text.HAL_RCC_GetOscConfig,"ax",%progbits
 2954              		.align	1
 2955              		.global	HAL_RCC_GetOscConfig
 2956              		.syntax unified
 2957              		.thumb
 2958              		.thumb_func
 2960              	HAL_RCC_GetOscConfig:
 2961              	.LVL214:
 2962              	.LFB154:
1544:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1545:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** /**
1546:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @brief  Configures the RCC_OscInitStruct according to the internal
1547:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * RCC configuration registers.
1548:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @param  RCC_OscInitStruct: pointer to an RCC_OscInitTypeDef structure that
1549:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * will be configured.
1550:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @retval None
1551:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   */
1552:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** void HAL_RCC_GetOscConfig(RCC_OscInitTypeDef  *RCC_OscInitStruct)
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 89


1553:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** {
 2963              		.loc 1 1553 1 is_stmt 1 view -0
 2964              		.cfi_startproc
 2965              		@ args = 0, pretend = 0, frame = 0
 2966              		@ frame_needed = 0, uses_anonymous_args = 0
 2967              		@ link register save eliminated.
1554:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Set all possible values for the Oscillator type parameter ---------------*/
1555:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   RCC_OscInitStruct->OscillatorType = RCC_OSCILLATORTYPE_HSE | RCC_OSCILLATORTYPE_HSI | RCC_OSCILLA
 2968              		.loc 1 1555 3 view .LVU978
 2969              		.loc 1 1555 37 is_stmt 0 view .LVU979
 2970 0000 3F23     		movs	r3, #63
 2971 0002 0360     		str	r3, [r0]
1556:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****                                       RCC_OSCILLATORTYPE_LSE | RCC_OSCILLATORTYPE_LSI | RCC_OSCILLA
1557:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1558:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Get the HSE configuration -----------------------------------------------*/
1559:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #if defined(RCC_CR_HSEEXT)
1560:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   if ((RCC->CR & (RCC_CR_HSEBYP | RCC_CR_HSEEXT)) == RCC_CR_HSEBYP)
1561:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
1562:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     RCC_OscInitStruct->HSEState = RCC_HSE_BYPASS;
1563:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
1564:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   else if ((RCC->CR & (RCC_CR_HSEBYP | RCC_CR_HSEEXT)) == (RCC_CR_HSEBYP | RCC_CR_HSEEXT))
1565:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
1566:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     RCC_OscInitStruct->HSEState = RCC_HSE_BYPASS_DIGITAL;
1567:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
1568:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   else if ((RCC->CR & RCC_CR_HSEON) == RCC_CR_HSEON)
1569:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
1570:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     RCC_OscInitStruct->HSEState = RCC_HSE_ON;
1571:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
1572:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   else
1573:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
1574:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     RCC_OscInitStruct->HSEState = RCC_HSE_OFF;
1575:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
1576:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #else
1577:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   if ((RCC->CR & RCC_CR_HSEBYP) == RCC_CR_HSEBYP)
 2972              		.loc 1 1577 3 is_stmt 1 view .LVU980
 2973              		.loc 1 1577 11 is_stmt 0 view .LVU981
 2974 0004 444B     		ldr	r3, .L256
 2975 0006 1B68     		ldr	r3, [r3]
 2976              		.loc 1 1577 6 view .LVU982
 2977 0008 13F4802F 		tst	r3, #262144
 2978 000c 5ED0     		beq	.L240
1578:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
1579:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     RCC_OscInitStruct->HSEState = RCC_HSE_BYPASS;
 2979              		.loc 1 1579 5 is_stmt 1 view .LVU983
 2980              		.loc 1 1579 33 is_stmt 0 view .LVU984
 2981 000e 4FF4A023 		mov	r3, #327680
 2982 0012 4360     		str	r3, [r0, #4]
 2983              	.L241:
1580:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
1581:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   else if ((RCC->CR & RCC_CR_HSEON) == RCC_CR_HSEON)
1582:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
1583:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     RCC_OscInitStruct->HSEState = RCC_HSE_ON;
1584:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
1585:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   else
1586:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
1587:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     RCC_OscInitStruct->HSEState = RCC_HSE_OFF;
1588:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 90


1589:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #endif /* RCC_CR_HSEEXT */
1590:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1591:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Get the CSI configuration -----------------------------------------------*/
1592:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   if ((RCC->CR & RCC_CR_CSION) == RCC_CR_CSION)
 2984              		.loc 1 1592 3 is_stmt 1 view .LVU985
 2985              		.loc 1 1592 11 is_stmt 0 view .LVU986
 2986 0014 404B     		ldr	r3, .L256
 2987 0016 1B68     		ldr	r3, [r3]
 2988              		.loc 1 1592 6 view .LVU987
 2989 0018 13F0800F 		tst	r3, #128
 2990 001c 62D0     		beq	.L243
1593:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
1594:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     RCC_OscInitStruct->CSIState = RCC_CSI_ON;
 2991              		.loc 1 1594 5 is_stmt 1 view .LVU988
 2992              		.loc 1 1594 33 is_stmt 0 view .LVU989
 2993 001e 8023     		movs	r3, #128
 2994 0020 C361     		str	r3, [r0, #28]
 2995              	.L244:
1595:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
1596:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   else
1597:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
1598:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     RCC_OscInitStruct->CSIState = RCC_CSI_OFF;
1599:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
1600:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1601:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #if defined(RCC_VER_X)
1602:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   if (HAL_GetREVID() <= REV_ID_Y)
1603:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
1604:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     RCC_OscInitStruct->CSICalibrationValue = (uint32_t)(READ_BIT(RCC->HSICFGR, HAL_RCC_REV_Y_CSITRI
1605:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
1606:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   else
1607:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
1608:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     RCC_OscInitStruct->CSICalibrationValue = (uint32_t)(READ_BIT(RCC->CSICFGR, RCC_CSICFGR_CSITRIM)
1609:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
1610:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #else
1611:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   RCC_OscInitStruct->CSICalibrationValue = (uint32_t)(READ_BIT(RCC->CSICFGR, RCC_CSICFGR_CSITRIM) >
 2996              		.loc 1 1611 3 is_stmt 1 view .LVU990
 2997              		.loc 1 1611 55 is_stmt 0 view .LVU991
 2998 0022 3D4A     		ldr	r2, .L256
 2999 0024 D368     		ldr	r3, [r2, #12]
 3000              		.loc 1 1611 44 view .LVU992
 3001 0026 C3F30563 		ubfx	r3, r3, #24, #6
 3002              		.loc 1 1611 42 view .LVU993
 3003 002a 0362     		str	r3, [r0, #32]
1612:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #endif /*RCC_VER_X*/
1613:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1614:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Get the HSI configuration -----------------------------------------------*/
1615:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   if ((RCC->CR & RCC_CR_HSION) == RCC_CR_HSION)
 3004              		.loc 1 1615 3 is_stmt 1 view .LVU994
 3005              		.loc 1 1615 11 is_stmt 0 view .LVU995
 3006 002c 1368     		ldr	r3, [r2]
 3007              		.loc 1 1615 6 view .LVU996
 3008 002e 13F0010F 		tst	r3, #1
 3009 0032 5AD0     		beq	.L245
1616:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
1617:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     RCC_OscInitStruct->HSIState = RCC_HSI_ON;
 3010              		.loc 1 1617 5 is_stmt 1 view .LVU997
 3011              		.loc 1 1617 33 is_stmt 0 view .LVU998
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 91


 3012 0034 0123     		movs	r3, #1
 3013 0036 C360     		str	r3, [r0, #12]
 3014              	.L246:
1618:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
1619:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   else
1620:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
1621:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     RCC_OscInitStruct->HSIState = RCC_HSI_OFF;
1622:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
1623:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1624:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #if defined(RCC_VER_X)
1625:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   if (HAL_GetREVID() <= REV_ID_Y)
1626:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
1627:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     RCC_OscInitStruct->HSICalibrationValue = (uint32_t)(READ_BIT(RCC->HSICFGR, HAL_RCC_REV_Y_HSITRI
1628:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
1629:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   else
1630:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
1631:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     RCC_OscInitStruct->HSICalibrationValue = (uint32_t)(READ_BIT(RCC->HSICFGR, RCC_HSICFGR_HSITRIM)
1632:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
1633:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #else
1634:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   RCC_OscInitStruct->HSICalibrationValue = (uint32_t)(READ_BIT(RCC->HSICFGR, RCC_HSICFGR_HSITRIM) >
 3015              		.loc 1 1634 3 is_stmt 1 view .LVU999
 3016              		.loc 1 1634 55 is_stmt 0 view .LVU1000
 3017 0038 374A     		ldr	r2, .L256
 3018 003a 5368     		ldr	r3, [r2, #4]
 3019              		.loc 1 1634 44 view .LVU1001
 3020 003c C3F30663 		ubfx	r3, r3, #24, #7
 3021              		.loc 1 1634 42 view .LVU1002
 3022 0040 0361     		str	r3, [r0, #16]
1635:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #endif /*RCC_VER_X*/
1636:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1637:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Get the LSE configuration -----------------------------------------------*/
1638:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #if defined(RCC_BDCR_LSEEXT)
1639:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   if ((RCC->BDCR & (RCC_BDCR_LSEBYP | RCC_BDCR_LSEEXT)) == RCC_BDCR_LSEBYP)
1640:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
1641:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     RCC_OscInitStruct->LSEState = RCC_LSE_BYPASS;
1642:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
1643:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   else if ((RCC->BDCR & (RCC_BDCR_LSEBYP | RCC_BDCR_LSEEXT)) == (RCC_BDCR_LSEBYP | RCC_BDCR_LSEEXT)
1644:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
1645:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     RCC_OscInitStruct->LSEState = RCC_LSE_BYPASS_DIGITAL;
1646:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
1647:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   else if ((RCC->BDCR & RCC_BDCR_LSEON) == RCC_BDCR_LSEON)
1648:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
1649:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     RCC_OscInitStruct->LSEState = RCC_LSE_ON;
1650:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
1651:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   else
1652:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
1653:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     RCC_OscInitStruct->LSEState = RCC_LSE_OFF;
1654:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
1655:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #else
1656:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   if ((RCC->BDCR & RCC_BDCR_LSEBYP) == RCC_BDCR_LSEBYP)
 3023              		.loc 1 1656 3 is_stmt 1 view .LVU1003
 3024              		.loc 1 1656 11 is_stmt 0 view .LVU1004
 3025 0042 136F     		ldr	r3, [r2, #112]
 3026              		.loc 1 1656 6 view .LVU1005
 3027 0044 13F0040F 		tst	r3, #4
 3028 0048 52D0     		beq	.L247
1657:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 92


1658:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     RCC_OscInitStruct->LSEState = RCC_LSE_BYPASS;
 3029              		.loc 1 1658 5 is_stmt 1 view .LVU1006
 3030              		.loc 1 1658 33 is_stmt 0 view .LVU1007
 3031 004a 0523     		movs	r3, #5
 3032 004c 8360     		str	r3, [r0, #8]
 3033              	.L248:
1659:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
1660:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   else if ((RCC->BDCR & RCC_BDCR_LSEON) == RCC_BDCR_LSEON)
1661:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
1662:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     RCC_OscInitStruct->LSEState = RCC_LSE_ON;
1663:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
1664:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   else
1665:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
1666:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     RCC_OscInitStruct->LSEState = RCC_LSE_OFF;
1667:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
1668:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #endif /* RCC_BDCR_LSEEXT */
1669:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1670:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Get the LSI configuration -----------------------------------------------*/
1671:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   if ((RCC->CSR & RCC_CSR_LSION) == RCC_CSR_LSION)
 3034              		.loc 1 1671 3 is_stmt 1 view .LVU1008
 3035              		.loc 1 1671 11 is_stmt 0 view .LVU1009
 3036 004e 324B     		ldr	r3, .L256
 3037 0050 5B6F     		ldr	r3, [r3, #116]
 3038              		.loc 1 1671 6 view .LVU1010
 3039 0052 13F0010F 		tst	r3, #1
 3040 0056 56D0     		beq	.L250
1672:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
1673:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     RCC_OscInitStruct->LSIState = RCC_LSI_ON;
 3041              		.loc 1 1673 5 is_stmt 1 view .LVU1011
 3042              		.loc 1 1673 33 is_stmt 0 view .LVU1012
 3043 0058 0123     		movs	r3, #1
 3044 005a 4361     		str	r3, [r0, #20]
 3045              	.L251:
1674:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
1675:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   else
1676:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
1677:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     RCC_OscInitStruct->LSIState = RCC_LSI_OFF;
1678:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
1679:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1680:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Get the HSI48 configuration ---------------------------------------------*/
1681:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   if ((RCC->CR & RCC_CR_HSI48ON) == RCC_CR_HSI48ON)
 3046              		.loc 1 1681 3 is_stmt 1 view .LVU1013
 3047              		.loc 1 1681 11 is_stmt 0 view .LVU1014
 3048 005c 2E4B     		ldr	r3, .L256
 3049 005e 1B68     		ldr	r3, [r3]
 3050              		.loc 1 1681 6 view .LVU1015
 3051 0060 13F4805F 		tst	r3, #4096
 3052 0064 52D0     		beq	.L252
1682:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
1683:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     RCC_OscInitStruct->HSI48State = RCC_HSI48_ON;
 3053              		.loc 1 1683 5 is_stmt 1 view .LVU1016
 3054              		.loc 1 1683 35 is_stmt 0 view .LVU1017
 3055 0066 0123     		movs	r3, #1
 3056 0068 8361     		str	r3, [r0, #24]
 3057              	.L253:
1684:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
1685:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   else
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 93


1686:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
1687:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     RCC_OscInitStruct->HSI48State = RCC_HSI48_OFF;
1688:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
1689:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1690:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Get the PLL configuration -----------------------------------------------*/
1691:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   if ((RCC->CR & RCC_CR_PLLON) == RCC_CR_PLLON)
 3058              		.loc 1 1691 3 is_stmt 1 view .LVU1018
 3059              		.loc 1 1691 11 is_stmt 0 view .LVU1019
 3060 006a 2B4B     		ldr	r3, .L256
 3061 006c 1B68     		ldr	r3, [r3]
 3062              		.loc 1 1691 6 view .LVU1020
 3063 006e 13F0807F 		tst	r3, #16777216
 3064 0072 4ED0     		beq	.L254
1692:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
1693:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     RCC_OscInitStruct->PLL.PLLState = RCC_PLL_ON;
 3065              		.loc 1 1693 5 is_stmt 1 view .LVU1021
 3066              		.loc 1 1693 37 is_stmt 0 view .LVU1022
 3067 0074 0223     		movs	r3, #2
 3068 0076 4362     		str	r3, [r0, #36]
 3069              	.L255:
1694:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
1695:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   else
1696:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
1697:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     RCC_OscInitStruct->PLL.PLLState = RCC_PLL_OFF;
1698:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
1699:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   RCC_OscInitStruct->PLL.PLLSource = (uint32_t)(RCC->PLLCKSELR & RCC_PLLCKSELR_PLLSRC);
 3070              		.loc 1 1699 3 is_stmt 1 view .LVU1023
 3071              		.loc 1 1699 52 is_stmt 0 view .LVU1024
 3072 0078 274B     		ldr	r3, .L256
 3073 007a 9A6A     		ldr	r2, [r3, #40]
 3074              		.loc 1 1699 38 view .LVU1025
 3075 007c 02F00302 		and	r2, r2, #3
 3076              		.loc 1 1699 36 view .LVU1026
 3077 0080 8262     		str	r2, [r0, #40]
1700:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   RCC_OscInitStruct->PLL.PLLM = (uint32_t)((RCC->PLLCKSELR & RCC_PLLCKSELR_DIVM1) >> RCC_PLLCKSELR_
 3078              		.loc 1 1700 3 is_stmt 1 view .LVU1027
 3079              		.loc 1 1700 48 is_stmt 0 view .LVU1028
 3080 0082 9A6A     		ldr	r2, [r3, #40]
 3081              		.loc 1 1700 33 view .LVU1029
 3082 0084 C2F30512 		ubfx	r2, r2, #4, #6
 3083              		.loc 1 1700 31 view .LVU1030
 3084 0088 C262     		str	r2, [r0, #44]
1701:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   RCC_OscInitStruct->PLL.PLLN = (uint32_t)((RCC->PLL1DIVR & RCC_PLL1DIVR_N1) >> RCC_PLL1DIVR_N1_Pos
 3085              		.loc 1 1701 3 is_stmt 1 view .LVU1031
 3086              		.loc 1 1701 48 is_stmt 0 view .LVU1032
 3087 008a 1A6B     		ldr	r2, [r3, #48]
 3088              		.loc 1 1701 33 view .LVU1033
 3089 008c C2F30802 		ubfx	r2, r2, #0, #9
 3090              		.loc 1 1701 102 view .LVU1034
 3091 0090 0132     		adds	r2, r2, #1
 3092              		.loc 1 1701 31 view .LVU1035
 3093 0092 0263     		str	r2, [r0, #48]
1702:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   RCC_OscInitStruct->PLL.PLLR = (uint32_t)((RCC->PLL1DIVR & RCC_PLL1DIVR_R1) >> RCC_PLL1DIVR_R1_Pos
 3094              		.loc 1 1702 3 is_stmt 1 view .LVU1036
 3095              		.loc 1 1702 48 is_stmt 0 view .LVU1037
 3096 0094 1A6B     		ldr	r2, [r3, #48]
 3097              		.loc 1 1702 33 view .LVU1038
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 94


 3098 0096 C2F30662 		ubfx	r2, r2, #24, #7
 3099              		.loc 1 1702 102 view .LVU1039
 3100 009a 0132     		adds	r2, r2, #1
 3101              		.loc 1 1702 31 view .LVU1040
 3102 009c C263     		str	r2, [r0, #60]
1703:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   RCC_OscInitStruct->PLL.PLLP = (uint32_t)((RCC->PLL1DIVR & RCC_PLL1DIVR_P1) >> RCC_PLL1DIVR_P1_Pos
 3103              		.loc 1 1703 3 is_stmt 1 view .LVU1041
 3104              		.loc 1 1703 48 is_stmt 0 view .LVU1042
 3105 009e 1A6B     		ldr	r2, [r3, #48]
 3106              		.loc 1 1703 33 view .LVU1043
 3107 00a0 C2F34622 		ubfx	r2, r2, #9, #7
 3108              		.loc 1 1703 102 view .LVU1044
 3109 00a4 0132     		adds	r2, r2, #1
 3110              		.loc 1 1703 31 view .LVU1045
 3111 00a6 4263     		str	r2, [r0, #52]
1704:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   RCC_OscInitStruct->PLL.PLLQ = (uint32_t)((RCC->PLL1DIVR & RCC_PLL1DIVR_Q1) >> RCC_PLL1DIVR_Q1_Pos
 3112              		.loc 1 1704 3 is_stmt 1 view .LVU1046
 3113              		.loc 1 1704 48 is_stmt 0 view .LVU1047
 3114 00a8 1A6B     		ldr	r2, [r3, #48]
 3115              		.loc 1 1704 33 view .LVU1048
 3116 00aa C2F30642 		ubfx	r2, r2, #16, #7
 3117              		.loc 1 1704 102 view .LVU1049
 3118 00ae 0132     		adds	r2, r2, #1
 3119              		.loc 1 1704 31 view .LVU1050
 3120 00b0 8263     		str	r2, [r0, #56]
1705:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   RCC_OscInitStruct->PLL.PLLRGE = (uint32_t)((RCC->PLLCFGR & RCC_PLLCFGR_PLL1RGE));
 3121              		.loc 1 1705 3 is_stmt 1 view .LVU1051
 3122              		.loc 1 1705 50 is_stmt 0 view .LVU1052
 3123 00b2 DA6A     		ldr	r2, [r3, #44]
 3124              		.loc 1 1705 35 view .LVU1053
 3125 00b4 02F00C02 		and	r2, r2, #12
 3126              		.loc 1 1705 33 view .LVU1054
 3127 00b8 0264     		str	r2, [r0, #64]
1706:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   RCC_OscInitStruct->PLL.PLLVCOSEL = (uint32_t)((RCC->PLLCFGR & RCC_PLLCFGR_PLL1VCOSEL) >> RCC_PLLC
 3128              		.loc 1 1706 3 is_stmt 1 view .LVU1055
 3129              		.loc 1 1706 53 is_stmt 0 view .LVU1056
 3130 00ba DA6A     		ldr	r2, [r3, #44]
 3131              		.loc 1 1706 38 view .LVU1057
 3132 00bc C2F34002 		ubfx	r2, r2, #1, #1
 3133              		.loc 1 1706 36 view .LVU1058
 3134 00c0 4264     		str	r2, [r0, #68]
1707:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   RCC_OscInitStruct->PLL.PLLFRACN = (uint32_t)(((RCC->PLL1FRACR & RCC_PLL1FRACR_FRACN1) >> RCC_PLL1
 3135              		.loc 1 1707 3 is_stmt 1 view .LVU1059
 3136              		.loc 1 1707 53 is_stmt 0 view .LVU1060
 3137 00c2 5B6B     		ldr	r3, [r3, #52]
 3138              		.loc 1 1707 37 view .LVU1061
 3139 00c4 C3F3CC03 		ubfx	r3, r3, #3, #13
 3140              		.loc 1 1707 35 view .LVU1062
 3141 00c8 8364     		str	r3, [r0, #72]
1708:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** }
 3142              		.loc 1 1708 1 view .LVU1063
 3143 00ca 7047     		bx	lr
 3144              	.L240:
1581:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
 3145              		.loc 1 1581 8 is_stmt 1 view .LVU1064
1581:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
 3146              		.loc 1 1581 16 is_stmt 0 view .LVU1065
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 95


 3147 00cc 124B     		ldr	r3, .L256
 3148 00ce 1B68     		ldr	r3, [r3]
1581:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
 3149              		.loc 1 1581 11 view .LVU1066
 3150 00d0 13F4803F 		tst	r3, #65536
 3151 00d4 03D0     		beq	.L242
1583:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
 3152              		.loc 1 1583 5 is_stmt 1 view .LVU1067
1583:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
 3153              		.loc 1 1583 33 is_stmt 0 view .LVU1068
 3154 00d6 4FF48033 		mov	r3, #65536
 3155 00da 4360     		str	r3, [r0, #4]
 3156 00dc 9AE7     		b	.L241
 3157              	.L242:
1587:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
 3158              		.loc 1 1587 5 is_stmt 1 view .LVU1069
1587:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
 3159              		.loc 1 1587 33 is_stmt 0 view .LVU1070
 3160 00de 0023     		movs	r3, #0
 3161 00e0 4360     		str	r3, [r0, #4]
 3162 00e2 97E7     		b	.L241
 3163              	.L243:
1598:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
 3164              		.loc 1 1598 5 is_stmt 1 view .LVU1071
1598:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
 3165              		.loc 1 1598 33 is_stmt 0 view .LVU1072
 3166 00e4 0023     		movs	r3, #0
 3167 00e6 C361     		str	r3, [r0, #28]
 3168 00e8 9BE7     		b	.L244
 3169              	.L245:
1621:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
 3170              		.loc 1 1621 5 is_stmt 1 view .LVU1073
1621:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
 3171              		.loc 1 1621 33 is_stmt 0 view .LVU1074
 3172 00ea 0023     		movs	r3, #0
 3173 00ec C360     		str	r3, [r0, #12]
 3174 00ee A3E7     		b	.L246
 3175              	.L247:
1660:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
 3176              		.loc 1 1660 8 is_stmt 1 view .LVU1075
1660:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
 3177              		.loc 1 1660 16 is_stmt 0 view .LVU1076
 3178 00f0 094B     		ldr	r3, .L256
 3179 00f2 1B6F     		ldr	r3, [r3, #112]
1660:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
 3180              		.loc 1 1660 11 view .LVU1077
 3181 00f4 13F0010F 		tst	r3, #1
 3182 00f8 02D0     		beq	.L249
1662:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
 3183              		.loc 1 1662 5 is_stmt 1 view .LVU1078
1662:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
 3184              		.loc 1 1662 33 is_stmt 0 view .LVU1079
 3185 00fa 0123     		movs	r3, #1
 3186 00fc 8360     		str	r3, [r0, #8]
 3187 00fe A6E7     		b	.L248
 3188              	.L249:
1666:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 96


 3189              		.loc 1 1666 5 is_stmt 1 view .LVU1080
1666:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
 3190              		.loc 1 1666 33 is_stmt 0 view .LVU1081
 3191 0100 0023     		movs	r3, #0
 3192 0102 8360     		str	r3, [r0, #8]
 3193 0104 A3E7     		b	.L248
 3194              	.L250:
1677:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
 3195              		.loc 1 1677 5 is_stmt 1 view .LVU1082
1677:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
 3196              		.loc 1 1677 33 is_stmt 0 view .LVU1083
 3197 0106 0023     		movs	r3, #0
 3198 0108 4361     		str	r3, [r0, #20]
 3199 010a A7E7     		b	.L251
 3200              	.L252:
1687:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
 3201              		.loc 1 1687 5 is_stmt 1 view .LVU1084
1687:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
 3202              		.loc 1 1687 35 is_stmt 0 view .LVU1085
 3203 010c 0023     		movs	r3, #0
 3204 010e 8361     		str	r3, [r0, #24]
 3205 0110 ABE7     		b	.L253
 3206              	.L254:
1697:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
 3207              		.loc 1 1697 5 is_stmt 1 view .LVU1086
1697:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
 3208              		.loc 1 1697 37 is_stmt 0 view .LVU1087
 3209 0112 0123     		movs	r3, #1
 3210 0114 4362     		str	r3, [r0, #36]
 3211 0116 AFE7     		b	.L255
 3212              	.L257:
 3213              		.align	2
 3214              	.L256:
 3215 0118 00440258 		.word	1*********
 3216              		.cfi_endproc
 3217              	.LFE154:
 3219              		.section	.text.HAL_RCC_GetClockConfig,"ax",%progbits
 3220              		.align	1
 3221              		.global	HAL_RCC_GetClockConfig
 3222              		.syntax unified
 3223              		.thumb
 3224              		.thumb_func
 3226              	HAL_RCC_GetClockConfig:
 3227              	.LVL215:
 3228              	.LFB155:
1709:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1710:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** /**
1711:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @brief  Configures the RCC_ClkInitStruct according to the internal
1712:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * RCC configuration registers.
1713:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @param  RCC_ClkInitStruct: pointer to an RCC_ClkInitTypeDef structure that
1714:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * will be configured.
1715:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @param  pFLatency: Pointer on the Flash Latency.
1716:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @retval None
1717:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   */
1718:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** void HAL_RCC_GetClockConfig(RCC_ClkInitTypeDef  *RCC_ClkInitStruct, uint32_t *pFLatency)
1719:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** {
 3229              		.loc 1 1719 1 is_stmt 1 view -0
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 97


 3230              		.cfi_startproc
 3231              		@ args = 0, pretend = 0, frame = 0
 3232              		@ frame_needed = 0, uses_anonymous_args = 0
 3233              		@ link register save eliminated.
1720:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Set all possible values for the Clock type parameter --------------------*/
1721:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   RCC_ClkInitStruct->ClockType = RCC_CLOCKTYPE_SYSCLK | RCC_CLOCKTYPE_HCLK | RCC_CLOCKTYPE_D1PCLK1 
 3234              		.loc 1 1721 3 view .LVU1089
 3235              		.loc 1 1721 32 is_stmt 0 view .LVU1090
 3236 0000 3F23     		movs	r3, #63
 3237 0002 0360     		str	r3, [r0]
1722:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****                                  RCC_CLOCKTYPE_PCLK2 |  RCC_CLOCKTYPE_D3PCLK1  ;
1723:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1724:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Get the SYSCLK configuration --------------------------------------------*/
1725:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   RCC_ClkInitStruct->SYSCLKSource = (uint32_t)(RCC->CFGR & RCC_CFGR_SW);
 3238              		.loc 1 1725 3 is_stmt 1 view .LVU1091
 3239              		.loc 1 1725 51 is_stmt 0 view .LVU1092
 3240 0004 114B     		ldr	r3, .L259
 3241 0006 1A69     		ldr	r2, [r3, #16]
 3242              		.loc 1 1725 37 view .LVU1093
 3243 0008 02F00702 		and	r2, r2, #7
 3244              		.loc 1 1725 35 view .LVU1094
 3245 000c 4260     		str	r2, [r0, #4]
1726:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1727:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #if defined(RCC_D1CFGR_D1CPRE)
1728:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Get the SYSCLK configuration ----------------------------------------------*/
1729:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   RCC_ClkInitStruct->SYSCLKDivider = (uint32_t)(RCC->D1CFGR & RCC_D1CFGR_D1CPRE);
 3246              		.loc 1 1729 3 is_stmt 1 view .LVU1095
 3247              		.loc 1 1729 52 is_stmt 0 view .LVU1096
 3248 000e 9A69     		ldr	r2, [r3, #24]
 3249              		.loc 1 1729 38 view .LVU1097
 3250 0010 02F47062 		and	r2, r2, #3840
 3251              		.loc 1 1729 36 view .LVU1098
 3252 0014 8260     		str	r2, [r0, #8]
1730:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1731:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Get the D1HCLK configuration ----------------------------------------------*/
1732:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   RCC_ClkInitStruct->AHBCLKDivider = (uint32_t)(RCC->D1CFGR & RCC_D1CFGR_HPRE);
 3253              		.loc 1 1732 3 is_stmt 1 view .LVU1099
 3254              		.loc 1 1732 52 is_stmt 0 view .LVU1100
 3255 0016 9A69     		ldr	r2, [r3, #24]
 3256              		.loc 1 1732 38 view .LVU1101
 3257 0018 02F00F02 		and	r2, r2, #15
 3258              		.loc 1 1732 36 view .LVU1102
 3259 001c C260     		str	r2, [r0, #12]
1733:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1734:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Get the APB3 configuration ----------------------------------------------*/
1735:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   RCC_ClkInitStruct->APB3CLKDivider = (uint32_t)(RCC->D1CFGR & RCC_D1CFGR_D1PPRE);
 3260              		.loc 1 1735 3 is_stmt 1 view .LVU1103
 3261              		.loc 1 1735 53 is_stmt 0 view .LVU1104
 3262 001e 9A69     		ldr	r2, [r3, #24]
 3263              		.loc 1 1735 39 view .LVU1105
 3264 0020 02F07002 		and	r2, r2, #112
 3265              		.loc 1 1735 37 view .LVU1106
 3266 0024 0261     		str	r2, [r0, #16]
1736:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1737:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Get the APB1 configuration ----------------------------------------------*/
1738:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   RCC_ClkInitStruct->APB1CLKDivider = (uint32_t)(RCC->D2CFGR & RCC_D2CFGR_D2PPRE1);
 3267              		.loc 1 1738 3 is_stmt 1 view .LVU1107
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 98


 3268              		.loc 1 1738 53 is_stmt 0 view .LVU1108
 3269 0026 DA69     		ldr	r2, [r3, #28]
 3270              		.loc 1 1738 39 view .LVU1109
 3271 0028 02F07002 		and	r2, r2, #112
 3272              		.loc 1 1738 37 view .LVU1110
 3273 002c 4261     		str	r2, [r0, #20]
1739:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1740:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Get the APB2 configuration ----------------------------------------------*/
1741:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   RCC_ClkInitStruct->APB2CLKDivider = (uint32_t)(RCC->D2CFGR & RCC_D2CFGR_D2PPRE2);
 3274              		.loc 1 1741 3 is_stmt 1 view .LVU1111
 3275              		.loc 1 1741 53 is_stmt 0 view .LVU1112
 3276 002e DA69     		ldr	r2, [r3, #28]
 3277              		.loc 1 1741 39 view .LVU1113
 3278 0030 02F4E062 		and	r2, r2, #1792
 3279              		.loc 1 1741 37 view .LVU1114
 3280 0034 8261     		str	r2, [r0, #24]
1742:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1743:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Get the APB4 configuration ----------------------------------------------*/
1744:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   RCC_ClkInitStruct->APB4CLKDivider = (uint32_t)(RCC->D3CFGR & RCC_D3CFGR_D3PPRE);
 3281              		.loc 1 1744 3 is_stmt 1 view .LVU1115
 3282              		.loc 1 1744 53 is_stmt 0 view .LVU1116
 3283 0036 1B6A     		ldr	r3, [r3, #32]
 3284              		.loc 1 1744 39 view .LVU1117
 3285 0038 03F07003 		and	r3, r3, #112
 3286              		.loc 1 1744 37 view .LVU1118
 3287 003c C361     		str	r3, [r0, #28]
1745:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #else
1746:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Get the SYSCLK configuration ----------------------------------------------*/
1747:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   RCC_ClkInitStruct->SYSCLKDivider = (uint32_t)(RCC->CDCFGR1 & RCC_CDCFGR1_CDCPRE);
1748:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1749:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Get the D1HCLK configuration ----------------------------------------------*/
1750:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   RCC_ClkInitStruct->AHBCLKDivider = (uint32_t)(RCC->CDCFGR1 & RCC_CDCFGR1_HPRE);
1751:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1752:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Get the APB3 configuration ----------------------------------------------*/
1753:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   RCC_ClkInitStruct->APB3CLKDivider = (uint32_t)(RCC->CDCFGR1 & RCC_CDCFGR1_CDPPRE);
1754:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1755:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Get the APB1 configuration ----------------------------------------------*/
1756:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   RCC_ClkInitStruct->APB1CLKDivider = (uint32_t)(RCC->CDCFGR2 & RCC_CDCFGR2_CDPPRE1);
1757:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1758:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Get the APB2 configuration ----------------------------------------------*/
1759:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   RCC_ClkInitStruct->APB2CLKDivider = (uint32_t)(RCC->CDCFGR2 & RCC_CDCFGR2_CDPPRE2);
1760:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1761:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Get the APB4 configuration ----------------------------------------------*/
1762:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   RCC_ClkInitStruct->APB4CLKDivider = (uint32_t)(RCC->SRDCFGR & RCC_SRDCFGR_SRDPPRE);
1763:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** #endif
1764:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1765:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Get the Flash Wait State (Latency) configuration ------------------------*/
1766:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   *pFLatency = (uint32_t)(FLASH->ACR & FLASH_ACR_LATENCY);
 3288              		.loc 1 1766 3 is_stmt 1 view .LVU1119
 3289              		.loc 1 1766 32 is_stmt 0 view .LVU1120
 3290 003e 044B     		ldr	r3, .L259+4
 3291 0040 1B68     		ldr	r3, [r3]
 3292              		.loc 1 1766 16 view .LVU1121
 3293 0042 03F00F03 		and	r3, r3, #15
 3294              		.loc 1 1766 14 view .LVU1122
 3295 0046 0B60     		str	r3, [r1]
1767:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** }
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 99


 3296              		.loc 1 1767 1 view .LVU1123
 3297 0048 7047     		bx	lr
 3298              	.L260:
 3299 004a 00BF     		.align	2
 3300              	.L259:
 3301 004c 00440258 		.word	1*********
 3302 0050 00200052 		.word	1375739904
 3303              		.cfi_endproc
 3304              	.LFE155:
 3306              		.section	.text.HAL_RCC_CSSCallback,"ax",%progbits
 3307              		.align	1
 3308              		.weak	HAL_RCC_CSSCallback
 3309              		.syntax unified
 3310              		.thumb
 3311              		.thumb_func
 3313              	HAL_RCC_CSSCallback:
 3314              	.LFB157:
1768:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1769:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** /**
1770:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @brief This function handles the RCC CSS interrupt request.
1771:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @note This API should be called under the NMI_Handler().
1772:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @retval None
1773:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   */
1774:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** void HAL_RCC_NMI_IRQHandler(void)
1775:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** {
1776:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Check RCC CSSF flag  */
1777:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   if (__HAL_RCC_GET_IT(RCC_IT_CSS))
1778:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
1779:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     /* RCC Clock Security System interrupt user callback */
1780:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     HAL_RCC_CSSCallback();
1781:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1782:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     /* Clear RCC CSS pending bit */
1783:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****     __HAL_RCC_CLEAR_IT(RCC_IT_CSS);
1784:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
1785:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** }
1786:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
1787:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** /**
1788:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @brief  RCC Clock Security System interrupt callback
1789:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   * @retval none
1790:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   */
1791:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** __weak void HAL_RCC_CSSCallback(void)
1792:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** {
 3315              		.loc 1 1792 1 is_stmt 1 view -0
 3316              		.cfi_startproc
 3317              		@ args = 0, pretend = 0, frame = 0
 3318              		@ frame_needed = 0, uses_anonymous_args = 0
 3319              		@ link register save eliminated.
1793:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* NOTE : This function Should not be modified, when the callback is needed,
1794:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****             the HAL_RCC_CSSCallback could be implemented in the user file
1795:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****    */
1796:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** }
 3320              		.loc 1 1796 1 view .LVU1125
 3321 0000 7047     		bx	lr
 3322              		.cfi_endproc
 3323              	.LFE157:
 3325              		.section	.text.HAL_RCC_NMI_IRQHandler,"ax",%progbits
 3326              		.align	1
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 100


 3327              		.global	HAL_RCC_NMI_IRQHandler
 3328              		.syntax unified
 3329              		.thumb
 3330              		.thumb_func
 3332              	HAL_RCC_NMI_IRQHandler:
 3333              	.LFB156:
1775:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   /* Check RCC CSSF flag  */
 3334              		.loc 1 1775 1 view -0
 3335              		.cfi_startproc
 3336              		@ args = 0, pretend = 0, frame = 0
 3337              		@ frame_needed = 0, uses_anonymous_args = 0
 3338 0000 08B5     		push	{r3, lr}
 3339              	.LCFI18:
 3340              		.cfi_def_cfa_offset 8
 3341              		.cfi_offset 3, -8
 3342              		.cfi_offset 14, -4
1777:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
 3343              		.loc 1 1777 3 view .LVU1127
1777:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
 3344              		.loc 1 1777 7 is_stmt 0 view .LVU1128
 3345 0002 064B     		ldr	r3, .L266
 3346 0004 5B6E     		ldr	r3, [r3, #100]
1777:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   {
 3347              		.loc 1 1777 6 view .LVU1129
 3348 0006 13F4806F 		tst	r3, #1024
 3349 000a 00D1     		bne	.L265
 3350              	.L262:
1785:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 3351              		.loc 1 1785 1 view .LVU1130
 3352 000c 08BD     		pop	{r3, pc}
 3353              	.L265:
1780:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 3354              		.loc 1 1780 5 is_stmt 1 view .LVU1131
 3355 000e FFF7FEFF 		bl	HAL_RCC_CSSCallback
 3356              	.LVL216:
1783:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c ****   }
 3357              		.loc 1 1783 5 view .LVU1132
 3358 0012 024B     		ldr	r3, .L266
 3359 0014 4FF48062 		mov	r2, #1024
 3360 0018 9A66     		str	r2, [r3, #104]
1785:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c **** 
 3361              		.loc 1 1785 1 is_stmt 0 view .LVU1133
 3362 001a F7E7     		b	.L262
 3363              	.L267:
 3364              		.align	2
 3365              	.L266:
 3366 001c 00440258 		.word	1*********
 3367              		.cfi_endproc
 3368              	.LFE156:
 3370              		.text
 3371              	.Letext0:
 3372              		.file 2 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\../lib/gcc/arm-none-eabi/13.3.1/..
 3373              		.file 3 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\../lib/gcc/arm-none-eabi/13.3.1/..
 3374              		.file 4 "Drivers/CMSIS/Device/ST/STM32H7xx/Include/system_stm32h7xx.h"
 3375              		.file 5 "Drivers/CMSIS/Device/ST/STM32H7xx/Include/stm32h723xx.h"
 3376              		.file 6 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\../lib/gcc/arm-none-eabi/13.3.1/..
 3377              		.file 7 "Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_def.h"
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 101


 3378              		.file 8 "Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_rcc.h"
 3379              		.file 9 "Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_gpio.h"
 3380              		.file 10 "Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal.h"
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s 			page 102


DEFINED SYMBOLS
                            *ABS*:00000000 stm32h7xx_hal_rcc.c
C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s:20     .text.HAL_RCC_DeInit:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s:26     .text.HAL_RCC_DeInit:00000000 HAL_RCC_DeInit
C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s:372    .text.HAL_RCC_DeInit:000001a4 $d
C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s:386    .text.HAL_RCC_OscConfig:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s:392    .text.HAL_RCC_OscConfig:00000000 HAL_RCC_OscConfig
C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s:986    .text.HAL_RCC_OscConfig:00000294 $d
C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s:992    .text.HAL_RCC_OscConfig:00000298 $t
C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s:1471   .text.HAL_RCC_OscConfig:00000510 $d
C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s:1478   .text.HAL_RCC_OscConfig:0000051c $t
C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s:1697   .text.HAL_RCC_OscConfig:000005e0 $d
C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s:1702   .text.HAL_RCC_MCOConfig:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s:1708   .text.HAL_RCC_MCOConfig:00000000 HAL_RCC_MCOConfig
C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s:1858   .text.HAL_RCC_MCOConfig:00000098 $d
C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s:1865   .text.HAL_RCC_EnableCSS:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s:1871   .text.HAL_RCC_EnableCSS:00000000 HAL_RCC_EnableCSS
C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s:1888   .text.HAL_RCC_EnableCSS:0000000c $d
C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s:1893   .text.HAL_RCC_DisableCSS:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s:1899   .text.HAL_RCC_DisableCSS:00000000 HAL_RCC_DisableCSS
C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s:1916   .text.HAL_RCC_DisableCSS:0000000c $d
C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s:1921   .text.HAL_RCC_GetSysClockFreq:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s:1927   .text.HAL_RCC_GetSysClockFreq:00000000 HAL_RCC_GetSysClockFreq
C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s:2245   .text.HAL_RCC_GetSysClockFreq:000001d8 $d
C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s:2257   .text.HAL_RCC_ClockConfig:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s:2263   .text.HAL_RCC_ClockConfig:00000000 HAL_RCC_ClockConfig
C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s:2789   .text.HAL_RCC_ClockConfig:0000027c $d
C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s:2799   .text.HAL_RCC_GetHCLKFreq:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s:2805   .text.HAL_RCC_GetHCLKFreq:00000000 HAL_RCC_GetHCLKFreq
C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s:2860   .text.HAL_RCC_GetHCLKFreq:00000034 $d
C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s:2868   .text.HAL_RCC_GetPCLK1Freq:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s:2874   .text.HAL_RCC_GetPCLK1Freq:00000000 HAL_RCC_GetPCLK1Freq
C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s:2905   .text.HAL_RCC_GetPCLK1Freq:0000001c $d
C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s:2911   .text.HAL_RCC_GetPCLK2Freq:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s:2917   .text.HAL_RCC_GetPCLK2Freq:00000000 HAL_RCC_GetPCLK2Freq
C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s:2948   .text.HAL_RCC_GetPCLK2Freq:0000001c $d
C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s:2954   .text.HAL_RCC_GetOscConfig:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s:2960   .text.HAL_RCC_GetOscConfig:00000000 HAL_RCC_GetOscConfig
C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s:3215   .text.HAL_RCC_GetOscConfig:00000118 $d
C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s:3220   .text.HAL_RCC_GetClockConfig:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s:3226   .text.HAL_RCC_GetClockConfig:00000000 HAL_RCC_GetClockConfig
C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s:3301   .text.HAL_RCC_GetClockConfig:0000004c $d
C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s:3307   .text.HAL_RCC_CSSCallback:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s:3313   .text.HAL_RCC_CSSCallback:00000000 HAL_RCC_CSSCallback
C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s:3326   .text.HAL_RCC_NMI_IRQHandler:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s:3332   .text.HAL_RCC_NMI_IRQHandler:00000000 HAL_RCC_NMI_IRQHandler
C:\Users\<USER>\AppData\Local\Temp\ccVT5Yz7.s:3366   .text.HAL_RCC_NMI_IRQHandler:0000001c $d

UNDEFINED SYMBOLS
HAL_GetTick
HAL_InitTick
SystemCoreClock
SystemD2Clock
uwTickPrio
HAL_GPIO_Init
D1CorePrescTable
