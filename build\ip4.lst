ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccEukS4o.s 			page 1


   1              		.cpu cortex-m7
   2              		.arch armv7e-m
   3              		.fpu fpv5-d16
   4              		.eabi_attribute 28, 1
   5              		.eabi_attribute 20, 1
   6              		.eabi_attribute 21, 1
   7              		.eabi_attribute 23, 3
   8              		.eabi_attribute 24, 1
   9              		.eabi_attribute 25, 1
  10              		.eabi_attribute 26, 1
  11              		.eabi_attribute 30, 1
  12              		.eabi_attribute 34, 1
  13              		.eabi_attribute 18, 4
  14              		.file	"ip4.c"
  15              		.text
  16              	.Ltext0:
  17              		.cfi_sections	.debug_frame
  18              		.file 1 "Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c"
  19              		.section	.text.ip4_input_accept,"ax",%progbits
  20              		.align	1
  21              		.syntax unified
  22              		.thumb
  23              		.thumb_func
  25              	ip4_input_accept:
  26              	.LVL0:
  27              	.LFB171:
   1:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** /**
   2:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * @file
   3:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * This is the IPv4 layer implementation for incoming and outgoing IP traffic.
   4:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  *
   5:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * @see ip_frag.c
   6:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  *
   7:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  */
   8:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
   9:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** /*
  10:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * Copyright (c) 2001-2004 Swedish Institute of Computer Science.
  11:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * All rights reserved.
  12:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  *
  13:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * Redistribution and use in source and binary forms, with or without modification,
  14:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * are permitted provided that the following conditions are met:
  15:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  *
  16:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * 1. Redistributions of source code must retain the above copyright notice,
  17:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  *    this list of conditions and the following disclaimer.
  18:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * 2. Redistributions in binary form must reproduce the above copyright notice,
  19:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  *    this list of conditions and the following disclaimer in the documentation
  20:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  *    and/or other materials provided with the distribution.
  21:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * 3. The name of the author may not be used to endorse or promote products
  22:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  *    derived from this software without specific prior written permission.
  23:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  *
  24:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR IMPLIED
  25:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
  26:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT
  27:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * SHALL THE AUTHOR BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
  28:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT
  29:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
  30:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
  31:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccEukS4o.s 			page 2


  32:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY
  33:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * OF SUCH DAMAGE.
  34:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  *
  35:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * This file is part of the lwIP TCP/IP stack.
  36:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  *
  37:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * Author: Adam Dunkels <<EMAIL>>
  38:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  *
  39:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  */
  40:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
  41:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #include "lwip/opt.h"
  42:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
  43:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #if LWIP_IPV4
  44:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
  45:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #include "lwip/ip.h"
  46:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #include "lwip/def.h"
  47:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #include "lwip/mem.h"
  48:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #include "lwip/ip4_frag.h"
  49:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #include "lwip/inet_chksum.h"
  50:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #include "lwip/netif.h"
  51:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #include "lwip/icmp.h"
  52:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #include "lwip/igmp.h"
  53:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #include "lwip/priv/raw_priv.h"
  54:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #include "lwip/udp.h"
  55:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #include "lwip/priv/tcp_priv.h"
  56:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #include "lwip/autoip.h"
  57:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #include "lwip/stats.h"
  58:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #include "lwip/prot/iana.h"
  59:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
  60:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #include <string.h>
  61:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
  62:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #ifdef LWIP_HOOK_FILENAME
  63:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #include LWIP_HOOK_FILENAME
  64:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #endif
  65:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
  66:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** /** Set this to 0 in the rare case of wanting to call an extra function to
  67:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * generate the IP checksum (in contrast to calculating it on-the-fly). */
  68:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #ifndef LWIP_INLINE_IP_CHKSUM
  69:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #if LWIP_CHECKSUM_CTRL_PER_NETIF
  70:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #define LWIP_INLINE_IP_CHKSUM   0
  71:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #else /* LWIP_CHECKSUM_CTRL_PER_NETIF */
  72:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #define LWIP_INLINE_IP_CHKSUM   1
  73:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #endif /* LWIP_CHECKSUM_CTRL_PER_NETIF */
  74:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #endif
  75:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
  76:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #if LWIP_INLINE_IP_CHKSUM && CHECKSUM_GEN_IP
  77:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #define CHECKSUM_GEN_IP_INLINE  1
  78:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #else
  79:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #define CHECKSUM_GEN_IP_INLINE  0
  80:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #endif
  81:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
  82:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #if LWIP_DHCP || defined(LWIP_IP_ACCEPT_UDP_PORT)
  83:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #define IP_ACCEPT_LINK_LAYER_ADDRESSING 1
  84:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
  85:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** /** Some defines for DHCP to let link-layer-addressed packets through while the
  86:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * netif is down.
  87:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * To use this in your own application/protocol, define LWIP_IP_ACCEPT_UDP_PORT(port)
  88:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * to return 1 if the port is accepted and 0 if the port is not accepted.
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccEukS4o.s 			page 3


  89:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  */
  90:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #if LWIP_DHCP && defined(LWIP_IP_ACCEPT_UDP_PORT)
  91:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** /* accept DHCP client port and custom port */
  92:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #define IP_ACCEPT_LINK_LAYER_ADDRESSED_PORT(port) (((port) == PP_NTOHS(LWIP_IANA_PORT_DHCP_CLIENT))
  93:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****          || (LWIP_IP_ACCEPT_UDP_PORT(port)))
  94:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #elif defined(LWIP_IP_ACCEPT_UDP_PORT) /* LWIP_DHCP && defined(LWIP_IP_ACCEPT_UDP_PORT) */
  95:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** /* accept custom port only */
  96:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #define IP_ACCEPT_LINK_LAYER_ADDRESSED_PORT(port) (LWIP_IP_ACCEPT_UDP_PORT(port))
  97:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #else /* LWIP_DHCP && defined(LWIP_IP_ACCEPT_UDP_PORT) */
  98:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** /* accept DHCP client port only */
  99:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #define IP_ACCEPT_LINK_LAYER_ADDRESSED_PORT(port) ((port) == PP_NTOHS(LWIP_IANA_PORT_DHCP_CLIENT))
 100:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #endif /* LWIP_DHCP && defined(LWIP_IP_ACCEPT_UDP_PORT) */
 101:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 102:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #else /* LWIP_DHCP */
 103:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #define IP_ACCEPT_LINK_LAYER_ADDRESSING 0
 104:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #endif /* LWIP_DHCP */
 105:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 106:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** /** The IP header ID of the next outgoing IP packet */
 107:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** static u16_t ip_id;
 108:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 109:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #if LWIP_MULTICAST_TX_OPTIONS
 110:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** /** The default netif used for multicast */
 111:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** static struct netif *ip4_default_multicast_netif;
 112:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 113:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** /**
 114:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * @ingroup ip4
 115:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * Set a default netif for IPv4 multicast. */
 116:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** void
 117:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** ip4_set_default_multicast_netif(struct netif *default_multicast_netif)
 118:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** {
 119:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   ip4_default_multicast_netif = default_multicast_netif;
 120:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** }
 121:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #endif /* LWIP_MULTICAST_TX_OPTIONS */
 122:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 123:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #ifdef LWIP_HOOK_IP4_ROUTE_SRC
 124:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** /**
 125:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * Source based IPv4 routing must be fully implemented in
 126:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * LWIP_HOOK_IP4_ROUTE_SRC(). This function only provides the parameters.
 127:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  */
 128:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** struct netif *
 129:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** ip4_route_src(const ip4_addr_t *src, const ip4_addr_t *dest)
 130:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** {
 131:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   if (src != NULL) {
 132:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     /* when src==NULL, the hook is called from ip4_route(dest) */
 133:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     struct netif *netif = LWIP_HOOK_IP4_ROUTE_SRC(src, dest);
 134:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     if (netif != NULL) {
 135:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       return netif;
 136:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     }
 137:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   }
 138:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   return ip4_route(dest);
 139:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** }
 140:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #endif /* LWIP_HOOK_IP4_ROUTE_SRC */
 141:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 142:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** /**
 143:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * Finds the appropriate network interface for a given IP address. It
 144:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * searches the list of network interfaces linearly. A match is found
 145:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * if the masked IP address of the network interface equals the masked
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccEukS4o.s 			page 4


 146:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * IP address given to the function.
 147:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  *
 148:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * @param dest the destination IP address for which to find the route
 149:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * @return the netif on which to send to reach dest
 150:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  */
 151:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** struct netif *
 152:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** ip4_route(const ip4_addr_t *dest)
 153:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** {
 154:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #if !LWIP_SINGLE_NETIF
 155:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   struct netif *netif;
 156:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 157:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   LWIP_ASSERT_CORE_LOCKED();
 158:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 159:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #if LWIP_MULTICAST_TX_OPTIONS
 160:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   /* Use administratively selected interface for multicast by default */
 161:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   if (ip4_addr_ismulticast(dest) && ip4_default_multicast_netif) {
 162:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     return ip4_default_multicast_netif;
 163:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   }
 164:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #endif /* LWIP_MULTICAST_TX_OPTIONS */
 165:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 166:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   /* bug #54569: in case LWIP_SINGLE_NETIF=1 and LWIP_DEBUGF() disabled, the following loop is opti
 167:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   LWIP_UNUSED_ARG(dest);
 168:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 169:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   /* iterate through netifs */
 170:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   NETIF_FOREACH(netif) {
 171:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     /* is the netif up, does it have a link and a valid address? */
 172:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     if (netif_is_up(netif) && netif_is_link_up(netif) && !ip4_addr_isany_val(*netif_ip4_addr(netif)
 173:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       /* network mask matches? */
 174:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       if (ip4_addr_netcmp(dest, netif_ip4_addr(netif), netif_ip4_netmask(netif))) {
 175:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****         /* return netif on which to forward IP packet */
 176:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****         return netif;
 177:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       }
 178:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       /* gateway matches on a non broadcast interface? (i.e. peer in a point to point interface) */
 179:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       if (((netif->flags & NETIF_FLAG_BROADCAST) == 0) && ip4_addr_cmp(dest, netif_ip4_gw(netif))) 
 180:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****         /* return netif on which to forward IP packet */
 181:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****         return netif;
 182:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       }
 183:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     }
 184:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   }
 185:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 186:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #if LWIP_NETIF_LOOPBACK && !LWIP_HAVE_LOOPIF
 187:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   /* loopif is disabled, looopback traffic is passed through any netif */
 188:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   if (ip4_addr_isloopback(dest)) {
 189:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     /* don't check for link on loopback traffic */
 190:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     if (netif_default != NULL && netif_is_up(netif_default)) {
 191:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       return netif_default;
 192:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     }
 193:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     /* default netif is not up, just use any netif for loopback traffic */
 194:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     NETIF_FOREACH(netif) {
 195:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       if (netif_is_up(netif)) {
 196:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****         return netif;
 197:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       }
 198:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     }
 199:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     return NULL;
 200:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   }
 201:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #endif /* LWIP_NETIF_LOOPBACK && !LWIP_HAVE_LOOPIF */
 202:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccEukS4o.s 			page 5


 203:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #ifdef LWIP_HOOK_IP4_ROUTE_SRC
 204:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   netif = LWIP_HOOK_IP4_ROUTE_SRC(NULL, dest);
 205:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   if (netif != NULL) {
 206:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     return netif;
 207:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   }
 208:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #elif defined(LWIP_HOOK_IP4_ROUTE)
 209:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   netif = LWIP_HOOK_IP4_ROUTE(dest);
 210:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   if (netif != NULL) {
 211:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     return netif;
 212:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   }
 213:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #endif
 214:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #endif /* !LWIP_SINGLE_NETIF */
 215:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 216:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   if ((netif_default == NULL) || !netif_is_up(netif_default) || !netif_is_link_up(netif_default) ||
 217:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       ip4_addr_isany_val(*netif_ip4_addr(netif_default)) || ip4_addr_isloopback(dest)) {
 218:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     /* No matching netif found and default netif is not usable.
 219:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****        If this is not good enough for you, use LWIP_HOOK_IP4_ROUTE() */
 220:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     LWIP_DEBUGF(IP_DEBUG | LWIP_DBG_LEVEL_SERIOUS, ("ip4_route: No route to %"U16_F".%"U16_F".%"U16
 221:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****                 ip4_addr1_16(dest), ip4_addr2_16(dest), ip4_addr3_16(dest), ip4_addr4_16(dest)));
 222:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     IP_STATS_INC(ip.rterr);
 223:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     MIB2_STATS_INC(mib2.ipoutnoroutes);
 224:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     return NULL;
 225:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   }
 226:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 227:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   return netif_default;
 228:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** }
 229:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 230:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #if IP_FORWARD
 231:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** /**
 232:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * Determine whether an IP address is in a reserved set of addresses
 233:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * that may not be forwarded, or whether datagrams to that destination
 234:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * may be forwarded.
 235:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * @param p the packet to forward
 236:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * @return 1: can forward 0: discard
 237:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  */
 238:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** static int
 239:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** ip4_canforward(struct pbuf *p)
 240:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** {
 241:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   u32_t addr = lwip_htonl(ip4_addr_get_u32(ip4_current_dest_addr()));
 242:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 243:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #ifdef LWIP_HOOK_IP4_CANFORWARD
 244:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   int ret = LWIP_HOOK_IP4_CANFORWARD(p, addr);
 245:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   if (ret >= 0) {
 246:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     return ret;
 247:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   }
 248:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #endif /* LWIP_HOOK_IP4_CANFORWARD */
 249:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 250:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   if (p->flags & PBUF_FLAG_LLBCAST) {
 251:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     /* don't route link-layer broadcasts */
 252:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     return 0;
 253:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   }
 254:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   if ((p->flags & PBUF_FLAG_LLMCAST) || IP_MULTICAST(addr)) {
 255:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     /* don't route link-layer multicasts (use LWIP_HOOK_IP4_CANFORWARD instead) */
 256:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     return 0;
 257:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   }
 258:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   if (IP_EXPERIMENTAL(addr)) {
 259:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     return 0;
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccEukS4o.s 			page 6


 260:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   }
 261:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   if (IP_CLASSA(addr)) {
 262:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     u32_t net = addr & IP_CLASSA_NET;
 263:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     if ((net == 0) || (net == ((u32_t)IP_LOOPBACKNET << IP_CLASSA_NSHIFT))) {
 264:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       /* don't route loopback packets */
 265:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       return 0;
 266:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     }
 267:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   }
 268:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   return 1;
 269:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** }
 270:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 271:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** /**
 272:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * Forwards an IP packet. It finds an appropriate route for the
 273:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * packet, decrements the TTL value of the packet, adjusts the
 274:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * checksum and outputs the packet on the appropriate interface.
 275:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  *
 276:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * @param p the packet to forward (p->payload points to IP header)
 277:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * @param iphdr the IP header of the input packet
 278:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * @param inp the netif on which this packet was received
 279:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  */
 280:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** static void
 281:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** ip4_forward(struct pbuf *p, struct ip_hdr *iphdr, struct netif *inp)
 282:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** {
 283:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   struct netif *netif;
 284:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 285:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   PERF_START;
 286:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   LWIP_UNUSED_ARG(inp);
 287:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 288:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   if (!ip4_canforward(p)) {
 289:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     goto return_noroute;
 290:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   }
 291:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 292:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   /* RFC3927 2.7: do not forward link-local addresses */
 293:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   if (ip4_addr_islinklocal(ip4_current_dest_addr())) {
 294:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     LWIP_DEBUGF(IP_DEBUG, ("ip4_forward: not forwarding LLA %"U16_F".%"U16_F".%"U16_F".%"U16_F"\n",
 295:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****                            ip4_addr1_16(ip4_current_dest_addr()), ip4_addr2_16(ip4_current_dest_add
 296:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****                            ip4_addr3_16(ip4_current_dest_addr()), ip4_addr4_16(ip4_current_dest_add
 297:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     goto return_noroute;
 298:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   }
 299:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 300:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   /* Find network interface where to forward this IP packet to. */
 301:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   netif = ip4_route_src(ip4_current_src_addr(), ip4_current_dest_addr());
 302:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   if (netif == NULL) {
 303:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     LWIP_DEBUGF(IP_DEBUG, ("ip4_forward: no forwarding route for %"U16_F".%"U16_F".%"U16_F".%"U16_F
 304:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****                            ip4_addr1_16(ip4_current_dest_addr()), ip4_addr2_16(ip4_current_dest_add
 305:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****                            ip4_addr3_16(ip4_current_dest_addr()), ip4_addr4_16(ip4_current_dest_add
 306:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     /* @todo: send ICMP_DUR_NET? */
 307:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     goto return_noroute;
 308:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   }
 309:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #if !IP_FORWARD_ALLOW_TX_ON_RX_NETIF
 310:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   /* Do not forward packets onto the same network interface on which
 311:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****    * they arrived. */
 312:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   if (netif == inp) {
 313:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     LWIP_DEBUGF(IP_DEBUG, ("ip4_forward: not bouncing packets back on incoming interface.\n"));
 314:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     goto return_noroute;
 315:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   }
 316:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #endif /* IP_FORWARD_ALLOW_TX_ON_RX_NETIF */
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccEukS4o.s 			page 7


 317:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 318:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   /* decrement TTL */
 319:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   IPH_TTL_SET(iphdr, IPH_TTL(iphdr) - 1);
 320:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   /* send ICMP if TTL == 0 */
 321:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   if (IPH_TTL(iphdr) == 0) {
 322:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     MIB2_STATS_INC(mib2.ipinhdrerrors);
 323:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #if LWIP_ICMP
 324:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     /* Don't send ICMP messages in response to ICMP messages */
 325:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     if (IPH_PROTO(iphdr) != IP_PROTO_ICMP) {
 326:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       icmp_time_exceeded(p, ICMP_TE_TTL);
 327:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     }
 328:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #endif /* LWIP_ICMP */
 329:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     return;
 330:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   }
 331:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 332:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   /* Incrementally update the IP checksum. */
 333:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   if (IPH_CHKSUM(iphdr) >= PP_HTONS(0xffffU - 0x100)) {
 334:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     IPH_CHKSUM_SET(iphdr, (u16_t)(IPH_CHKSUM(iphdr) + PP_HTONS(0x100) + 1));
 335:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   } else {
 336:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     IPH_CHKSUM_SET(iphdr, (u16_t)(IPH_CHKSUM(iphdr) + PP_HTONS(0x100)));
 337:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   }
 338:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 339:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   LWIP_DEBUGF(IP_DEBUG, ("ip4_forward: forwarding packet to %"U16_F".%"U16_F".%"U16_F".%"U16_F"\n",
 340:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****                          ip4_addr1_16(ip4_current_dest_addr()), ip4_addr2_16(ip4_current_dest_addr(
 341:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****                          ip4_addr3_16(ip4_current_dest_addr()), ip4_addr4_16(ip4_current_dest_addr(
 342:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 343:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   IP_STATS_INC(ip.fw);
 344:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   MIB2_STATS_INC(mib2.ipforwdatagrams);
 345:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   IP_STATS_INC(ip.xmit);
 346:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 347:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   PERF_STOP("ip4_forward");
 348:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   /* don't fragment if interface has mtu set to 0 [loopif] */
 349:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   if (netif->mtu && (p->tot_len > netif->mtu)) {
 350:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     if ((IPH_OFFSET(iphdr) & PP_NTOHS(IP_DF)) == 0) {
 351:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #if IP_FRAG
 352:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       ip4_frag(p, netif, ip4_current_dest_addr());
 353:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #else /* IP_FRAG */
 354:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       /* @todo: send ICMP Destination Unreachable code 13 "Communication administratively prohibite
 355:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #endif /* IP_FRAG */
 356:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     } else {
 357:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #if LWIP_ICMP
 358:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       /* send ICMP Destination Unreachable code 4: "Fragmentation Needed and DF Set" */
 359:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       icmp_dest_unreach(p, ICMP_DUR_FRAG);
 360:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #endif /* LWIP_ICMP */
 361:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     }
 362:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     return;
 363:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   }
 364:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   /* transmit pbuf on chosen interface */
 365:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   netif->output(netif, p, ip4_current_dest_addr());
 366:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   return;
 367:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** return_noroute:
 368:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   MIB2_STATS_INC(mib2.ipoutnoroutes);
 369:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** }
 370:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #endif /* IP_FORWARD */
 371:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 372:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** /** Return true if the current input packet should be accepted on this netif */
 373:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** static int
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccEukS4o.s 			page 8


 374:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** ip4_input_accept(struct netif *netif)
 375:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** {
  28              		.loc 1 375 1 view -0
  29              		.cfi_startproc
  30              		@ args = 0, pretend = 0, frame = 0
  31              		@ frame_needed = 0, uses_anonymous_args = 0
  32              		.loc 1 375 1 is_stmt 0 view .LVU1
  33 0000 08B5     		push	{r3, lr}
  34              	.LCFI0:
  35              		.cfi_def_cfa_offset 8
  36              		.cfi_offset 3, -8
  37              		.cfi_offset 14, -4
 376:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   LWIP_DEBUGF(IP_DEBUG, ("ip_input: iphdr->dest 0x%"X32_F" netif->ip_addr 0x%"X32_F" (0x%"X32_F", 0
 377:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****                          ip4_addr_get_u32(ip4_current_dest_addr()), ip4_addr_get_u32(netif_ip4_addr
 378:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****                          ip4_addr_get_u32(ip4_current_dest_addr()) & ip4_addr_get_u32(netif_ip4_net
 379:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****                          ip4_addr_get_u32(netif_ip4_addr(netif)) & ip4_addr_get_u32(netif_ip4_netma
 380:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****                          ip4_addr_get_u32(ip4_current_dest_addr()) & ~ip4_addr_get_u32(netif_ip4_ne
  38              		.loc 1 380 115 is_stmt 1 view .LVU2
 381:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 382:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   /* interface is up and configured? */
 383:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   if ((netif_is_up(netif)) && (!ip4_addr_isany_val(*netif_ip4_addr(netif)))) {
  39              		.loc 1 383 3 view .LVU3
  40              		.loc 1 383 8 is_stmt 0 view .LVU4
  41 0002 90F82D30 		ldrb	r3, [r0, #45]	@ zero_extendqisi2
  42              		.loc 1 383 6 view .LVU5
  43 0006 13F0010F 		tst	r3, #1
  44 000a 0DD0     		beq	.L4
  45 000c 0146     		mov	r1, r0
  46              		.loc 1 383 33 discriminator 1 view .LVU6
  47 000e 4368     		ldr	r3, [r0, #4]
  48              		.loc 1 383 28 discriminator 1 view .LVU7
  49 0010 63B1     		cbz	r3, .L5
 384:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     /* unicast to this interface address? */
 385:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     if (ip4_addr_cmp(ip4_current_dest_addr(), netif_ip4_addr(netif)) ||
  50              		.loc 1 385 5 is_stmt 1 view .LVU8
  51              		.loc 1 385 9 is_stmt 0 view .LVU9
  52 0012 074A     		ldr	r2, .L8
  53 0014 5069     		ldr	r0, [r2, #20]
  54              	.LVL1:
  55              		.loc 1 385 8 view .LVU10
  56 0016 8342     		cmp	r3, r0
  57 0018 04D0     		beq	.L3
 386:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****         /* or broadcast on this interface network address? */
 387:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****         ip4_addr_isbroadcast(ip4_current_dest_addr(), netif)
  58              		.loc 1 387 9 view .LVU11
  59 001a FFF7FEFF 		bl	ip4_addr_isbroadcast_u32
  60              	.LVL2:
 385:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****         /* or broadcast on this interface network address? */
  61              		.loc 1 385 70 discriminator 1 view .LVU12
  62 001e 08B9     		cbnz	r0, .L3
 388:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #if LWIP_NETIF_LOOPBACK && !LWIP_HAVE_LOOPIF
 389:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****         || (ip4_addr_get_u32(ip4_current_dest_addr()) == PP_HTONL(IPADDR_LOOPBACK))
 390:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #endif /* LWIP_NETIF_LOOPBACK && !LWIP_HAVE_LOOPIF */
 391:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****        ) {
 392:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       LWIP_DEBUGF(IP_DEBUG, ("ip4_input: packet accepted on interface %c%c\n",
 393:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****                              netif->name[0], netif->name[1]));
 394:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       /* accept on this netif */
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccEukS4o.s 			page 9


 395:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       return 1;
 396:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     }
 397:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #if LWIP_AUTOIP
 398:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     /* connections to link-local addresses must persist after changing
 399:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****         the netif's address (RFC3927 ch. 1.9) */
 400:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     if (autoip_accept_packet(netif, ip4_current_dest_addr())) {
 401:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       LWIP_DEBUGF(IP_DEBUG, ("ip4_input: LLA packet accepted on interface %c%c\n",
 402:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****                              netif->name[0], netif->name[1]));
 403:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       /* accept on this netif */
 404:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       return 1;
 405:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     }
 406:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #endif /* LWIP_AUTOIP */
 407:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   }
 408:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   return 0;
  63              		.loc 1 408 10 view .LVU13
  64 0020 0020     		movs	r0, #0
  65 0022 02E0     		b	.L1
  66              	.L3:
 393:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       /* accept on this netif */
  67              		.loc 1 393 62 is_stmt 1 view .LVU14
 395:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     }
  68              		.loc 1 395 7 view .LVU15
 395:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     }
  69              		.loc 1 395 14 is_stmt 0 view .LVU16
  70 0024 0120     		movs	r0, #1
  71 0026 00E0     		b	.L1
  72              	.LVL3:
  73              	.L4:
  74              		.loc 1 408 10 view .LVU17
  75 0028 0020     		movs	r0, #0
  76              	.LVL4:
  77              	.L1:
 409:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** }
  78              		.loc 1 409 1 view .LVU18
  79 002a 08BD     		pop	{r3, pc}
  80              	.LVL5:
  81              	.L5:
 408:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** }
  82              		.loc 1 408 10 view .LVU19
  83 002c 0020     		movs	r0, #0
  84              	.LVL6:
 408:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** }
  85              		.loc 1 408 10 view .LVU20
  86 002e FCE7     		b	.L1
  87              	.L9:
  88              		.align	2
  89              	.L8:
  90 0030 00000000 		.word	ip_data
  91              		.cfi_endproc
  92              	.LFE171:
  94              		.section	.text.ip4_route,"ax",%progbits
  95              		.align	1
  96              		.global	ip4_route
  97              		.syntax unified
  98              		.thumb
  99              		.thumb_func
 101              	ip4_route:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccEukS4o.s 			page 10


 102              	.LVL7:
 103              	.LFB170:
 153:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #if !LWIP_SINGLE_NETIF
 104              		.loc 1 153 1 is_stmt 1 view -0
 105              		.cfi_startproc
 106              		@ args = 0, pretend = 0, frame = 0
 107              		@ frame_needed = 0, uses_anonymous_args = 0
 108              		@ link register save eliminated.
 153:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #if !LWIP_SINGLE_NETIF
 109              		.loc 1 153 1 is_stmt 0 view .LVU22
 110 0000 8446     		mov	ip, r0
 155:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 111              		.loc 1 155 3 is_stmt 1 view .LVU23
 157:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 112              		.loc 1 157 28 view .LVU24
 167:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 113              		.loc 1 167 3 view .LVU25
 170:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     /* is the netif up, does it have a link and a valid address? */
 114              		.loc 1 170 3 view .LVU26
 115 0002 284B     		ldr	r3, .L31
 116 0004 1868     		ldr	r0, [r3]
 117              	.LVL8:
 170:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     /* is the netif up, does it have a link and a valid address? */
 118              		.loc 1 170 3 is_stmt 0 view .LVU27
 119 0006 2DE0     		b	.L16
 120              	.L12:
 121              	.LCFI1:
 122              		.cfi_def_cfa_offset 4
 123              		.cfi_offset 4, -4
 170:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     /* is the netif up, does it have a link and a valid address? */
 124              		.loc 1 170 3 is_stmt 1 discriminator 2 view .LVU28
 125 0008 0068     		ldr	r0, [r0]
 126              	.LVL9:
 170:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     /* is the netif up, does it have a link and a valid address? */
 127              		.loc 1 170 3 discriminator 1 view .LVU29
 128 000a B8B1     		cbz	r0, .L29
 172:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       /* network mask matches? */
 129              		.loc 1 172 5 view .LVU30
 172:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       /* network mask matches? */
 130              		.loc 1 172 9 is_stmt 0 view .LVU31
 131 000c 90F82D30 		ldrb	r3, [r0, #45]	@ zero_extendqisi2
 172:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       /* network mask matches? */
 132              		.loc 1 172 8 view .LVU32
 133 0010 13F0010F 		tst	r3, #1
 134 0014 F8D0     		beq	.L12
 172:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       /* network mask matches? */
 135              		.loc 1 172 28 discriminator 1 view .LVU33
 136 0016 13F0040F 		tst	r3, #4
 137 001a F5D0     		beq	.L12
 172:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       /* network mask matches? */
 138              		.loc 1 172 59 discriminator 2 view .LVU34
 139 001c 4268     		ldr	r2, [r0, #4]
 172:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       /* network mask matches? */
 140              		.loc 1 172 55 discriminator 2 view .LVU35
 141 001e 002A     		cmp	r2, #0
 142 0020 F2D0     		beq	.L12
 143              	.L20:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccEukS4o.s 			page 11


 174:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****         /* return netif on which to forward IP packet */
 144              		.loc 1 174 7 is_stmt 1 view .LVU36
 174:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****         /* return netif on which to forward IP packet */
 145              		.loc 1 174 11 is_stmt 0 view .LVU37
 146 0022 DCF80010 		ldr	r1, [ip]
 147 0026 4A40     		eors	r2, r2, r1
 174:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****         /* return netif on which to forward IP packet */
 148              		.loc 1 174 10 view .LVU38
 149 0028 8468     		ldr	r4, [r0, #8]
 150 002a 2242     		tst	r2, r4
 151 002c 16D0     		beq	.L10
 179:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****         /* return netif on which to forward IP packet */
 152              		.loc 1 179 7 is_stmt 1 view .LVU39
 179:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****         /* return netif on which to forward IP packet */
 153              		.loc 1 179 10 is_stmt 0 view .LVU40
 154 002e 13F0020F 		tst	r3, #2
 155 0032 E9D1     		bne	.L12
 179:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****         /* return netif on which to forward IP packet */
 156              		.loc 1 179 59 discriminator 1 view .LVU41
 157 0034 C368     		ldr	r3, [r0, #12]
 179:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****         /* return netif on which to forward IP packet */
 158              		.loc 1 179 56 discriminator 1 view .LVU42
 159 0036 9942     		cmp	r1, r3
 160 0038 E6D1     		bne	.L12
 161 003a 0FE0     		b	.L10
 162              	.L29:
 216:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       ip4_addr_isany_val(*netif_ip4_addr(netif_default)) || ip4_addr_isloopback(dest)) {
 163              		.loc 1 216 3 is_stmt 1 view .LVU43
 216:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       ip4_addr_isany_val(*netif_ip4_addr(netif_default)) || ip4_addr_isloopback(dest)) {
 164              		.loc 1 216 22 is_stmt 0 view .LVU44
 165 003c 1A4B     		ldr	r3, .L31+4
 166 003e 1B68     		ldr	r3, [r3]
 216:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       ip4_addr_isany_val(*netif_ip4_addr(netif_default)) || ip4_addr_isloopback(dest)) {
 167              		.loc 1 216 6 view .LVU45
 168 0040 63B1     		cbz	r3, .L10
 216:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       ip4_addr_isany_val(*netif_ip4_addr(netif_default)) || ip4_addr_isloopback(dest)) {
 169              		.loc 1 216 35 discriminator 1 view .LVU46
 170 0042 93F82D20 		ldrb	r2, [r3, #45]	@ zero_extendqisi2
 216:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       ip4_addr_isany_val(*netif_ip4_addr(netif_default)) || ip4_addr_isloopback(dest)) {
 171              		.loc 1 216 62 discriminator 1 view .LVU47
 172 0046 02F00502 		and	r2, r2, #5
 173 004a 052A     		cmp	r2, #5
 174 004c 06D1     		bne	.L10
 217:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     /* No matching netif found and default netif is not usable.
 175              		.loc 1 217 7 view .LVU48
 176 004e 5A68     		ldr	r2, [r3, #4]
 216:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       ip4_addr_isany_val(*netif_ip4_addr(netif_default)) || ip4_addr_isloopback(dest)) {
 177              		.loc 1 216 98 discriminator 2 view .LVU49
 178 0050 22B1     		cbz	r2, .L10
 217:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     /* No matching netif found and default netif is not usable.
 179              		.loc 1 217 61 view .LVU50
 180 0052 9CF80020 		ldrb	r2, [ip]	@ zero_extendqisi2
 217:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     /* No matching netif found and default netif is not usable.
 181              		.loc 1 217 58 view .LVU51
 182 0056 7F2A     		cmp	r2, #127
 183 0058 00D0     		beq	.L10
 227:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** }
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccEukS4o.s 			page 12


 184              		.loc 1 227 10 view .LVU52
 185 005a 1846     		mov	r0, r3
 186              	.LVL10:
 187              	.L10:
 228:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 188              		.loc 1 228 1 view .LVU53
 189 005c 5DF8044B 		ldr	r4, [sp], #4
 190              	.LCFI2:
 191              		.cfi_restore 4
 192              		.cfi_def_cfa_offset 0
 193 0060 7047     		bx	lr
 194              	.LVL11:
 195              	.L18:
 170:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     /* is the netif up, does it have a link and a valid address? */
 196              		.loc 1 170 3 is_stmt 1 discriminator 2 view .LVU54
 197 0062 0068     		ldr	r0, [r0]
 198              	.LVL12:
 199              	.L16:
 170:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     /* is the netif up, does it have a link and a valid address? */
 200              		.loc 1 170 3 discriminator 1 view .LVU55
 201 0064 60B1     		cbz	r0, .L30
 172:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       /* network mask matches? */
 202              		.loc 1 172 5 view .LVU56
 172:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       /* network mask matches? */
 203              		.loc 1 172 9 is_stmt 0 view .LVU57
 204 0066 90F82D30 		ldrb	r3, [r0, #45]	@ zero_extendqisi2
 172:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       /* network mask matches? */
 205              		.loc 1 172 8 view .LVU58
 206 006a 13F0010F 		tst	r3, #1
 207 006e F8D0     		beq	.L18
 172:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       /* network mask matches? */
 208              		.loc 1 172 28 discriminator 1 view .LVU59
 209 0070 13F0040F 		tst	r3, #4
 210 0074 F5D0     		beq	.L18
 172:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       /* network mask matches? */
 211              		.loc 1 172 59 discriminator 2 view .LVU60
 212 0076 4268     		ldr	r2, [r0, #4]
 172:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       /* network mask matches? */
 213              		.loc 1 172 55 discriminator 2 view .LVU61
 214 0078 002A     		cmp	r2, #0
 215 007a F2D0     		beq	.L18
 153:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #if !LWIP_SINGLE_NETIF
 216              		.loc 1 153 1 view .LVU62
 217 007c 10B4     		push	{r4}
 218              	.LCFI3:
 219              		.cfi_def_cfa_offset 4
 220              		.cfi_offset 4, -4
 221 007e D0E7     		b	.L20
 222              	.L30:
 223              	.LCFI4:
 224              		.cfi_def_cfa_offset 0
 225              		.cfi_restore 4
 216:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       ip4_addr_isany_val(*netif_ip4_addr(netif_default)) || ip4_addr_isloopback(dest)) {
 226              		.loc 1 216 3 is_stmt 1 view .LVU63
 216:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       ip4_addr_isany_val(*netif_ip4_addr(netif_default)) || ip4_addr_isloopback(dest)) {
 227              		.loc 1 216 22 is_stmt 0 view .LVU64
 228 0080 094B     		ldr	r3, .L31+4
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccEukS4o.s 			page 13


 229 0082 1B68     		ldr	r3, [r3]
 216:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       ip4_addr_isany_val(*netif_ip4_addr(netif_default)) || ip4_addr_isloopback(dest)) {
 230              		.loc 1 216 6 view .LVU65
 231 0084 6BB1     		cbz	r3, .L23
 216:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       ip4_addr_isany_val(*netif_ip4_addr(netif_default)) || ip4_addr_isloopback(dest)) {
 232              		.loc 1 216 35 discriminator 1 view .LVU66
 233 0086 93F82D20 		ldrb	r2, [r3, #45]	@ zero_extendqisi2
 216:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       ip4_addr_isany_val(*netif_ip4_addr(netif_default)) || ip4_addr_isloopback(dest)) {
 234              		.loc 1 216 62 discriminator 1 view .LVU67
 235 008a 02F00502 		and	r2, r2, #5
 236 008e 052A     		cmp	r2, #5
 237 0090 07D1     		bne	.L23
 217:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     /* No matching netif found and default netif is not usable.
 238              		.loc 1 217 7 view .LVU68
 239 0092 5A68     		ldr	r2, [r3, #4]
 216:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       ip4_addr_isany_val(*netif_ip4_addr(netif_default)) || ip4_addr_isloopback(dest)) {
 240              		.loc 1 216 98 discriminator 2 view .LVU69
 241 0094 2AB1     		cbz	r2, .L23
 217:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     /* No matching netif found and default netif is not usable.
 242              		.loc 1 217 61 view .LVU70
 243 0096 9CF80020 		ldrb	r2, [ip]	@ zero_extendqisi2
 217:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     /* No matching netif found and default netif is not usable.
 244              		.loc 1 217 58 view .LVU71
 245 009a 7F2A     		cmp	r2, #127
 246 009c 01D0     		beq	.L23
 227:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** }
 247              		.loc 1 227 10 view .LVU72
 248 009e 1846     		mov	r0, r3
 249              	.LVL13:
 227:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** }
 250              		.loc 1 227 10 view .LVU73
 251 00a0 7047     		bx	lr
 252              	.LVL14:
 253              	.L23:
 228:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 254              		.loc 1 228 1 view .LVU74
 255 00a2 7047     		bx	lr
 256              	.L32:
 257              		.align	2
 258              	.L31:
 259 00a4 00000000 		.word	netif_list
 260 00a8 00000000 		.word	netif_default
 261              		.cfi_endproc
 262              	.LFE170:
 264              		.section	.text.ip4_input,"ax",%progbits
 265              		.align	1
 266              		.global	ip4_input
 267              		.syntax unified
 268              		.thumb
 269              		.thumb_func
 271              	ip4_input:
 272              	.LVL15:
 273              	.LFB172:
 410:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 411:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** /**
 412:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * This function is called by the network interface device driver when
 413:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * an IP packet is received. The function does the basic checks of the
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccEukS4o.s 			page 14


 414:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * IP header such as packet size being at least larger than the header
 415:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * size etc. If the packet was not destined for us, the packet is
 416:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * forwarded (using ip_forward). The IP checksum is always checked.
 417:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  *
 418:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * Finally, the packet is sent to the upper layer protocol input function.
 419:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  *
 420:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * @param p the received IP packet (p->payload points to IP header)
 421:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * @param inp the netif on which this packet was received
 422:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * @return ERR_OK if the packet was processed (could return ERR_* if it wasn't
 423:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  *         processed, but currently always returns ERR_OK)
 424:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  */
 425:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** err_t
 426:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** ip4_input(struct pbuf *p, struct netif *inp)
 427:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** {
 274              		.loc 1 427 1 is_stmt 1 view -0
 275              		.cfi_startproc
 276              		@ args = 0, pretend = 0, frame = 0
 277              		@ frame_needed = 0, uses_anonymous_args = 0
 278              		.loc 1 427 1 is_stmt 0 view .LVU76
 279 0000 2DE9F041 		push	{r4, r5, r6, r7, r8, lr}
 280              	.LCFI5:
 281              		.cfi_def_cfa_offset 24
 282              		.cfi_offset 4, -24
 283              		.cfi_offset 5, -20
 284              		.cfi_offset 6, -16
 285              		.cfi_offset 7, -12
 286              		.cfi_offset 8, -8
 287              		.cfi_offset 14, -4
 288 0004 0446     		mov	r4, r0
 428:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   const struct ip_hdr *iphdr;
 289              		.loc 1 428 3 is_stmt 1 view .LVU77
 429:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   struct netif *netif;
 290              		.loc 1 429 3 view .LVU78
 430:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   u16_t iphdr_hlen;
 291              		.loc 1 430 3 view .LVU79
 431:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   u16_t iphdr_len;
 292              		.loc 1 431 3 view .LVU80
 432:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #if IP_ACCEPT_LINK_LAYER_ADDRESSING || LWIP_IGMP
 433:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   int check_ip_src = 1;
 434:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #endif /* IP_ACCEPT_LINK_LAYER_ADDRESSING || LWIP_IGMP */
 435:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #if LWIP_RAW
 436:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   raw_input_state_t raw_status;
 437:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #endif /* LWIP_RAW */
 438:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 439:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   LWIP_ASSERT_CORE_LOCKED();
 293              		.loc 1 439 28 view .LVU81
 440:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 441:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   IP_STATS_INC(ip.recv);
 294              		.loc 1 441 24 view .LVU82
 442:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   MIB2_STATS_INC(mib2.ipinreceives);
 295              		.loc 1 442 36 view .LVU83
 443:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 444:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   /* identify the IP header */
 445:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   iphdr = (struct ip_hdr *)p->payload;
 296              		.loc 1 445 3 view .LVU84
 297              		.loc 1 445 9 is_stmt 0 view .LVU85
 298 0006 4768     		ldr	r7, [r0, #4]
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccEukS4o.s 			page 15


 299              	.LVL16:
 446:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   if (IPH_V(iphdr) != 4) {
 300              		.loc 1 446 3 is_stmt 1 view .LVU86
 301              		.loc 1 446 7 is_stmt 0 view .LVU87
 302 0008 3B78     		ldrb	r3, [r7]	@ zero_extendqisi2
 303              		.loc 1 446 20 view .LVU88
 304 000a 1A09     		lsrs	r2, r3, #4
 305              		.loc 1 446 6 view .LVU89
 306 000c 042A     		cmp	r2, #4
 307 000e 04D0     		beq	.L34
 447:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     LWIP_DEBUGF(IP_DEBUG | LWIP_DBG_LEVEL_WARNING, ("IP packet dropped due to bad version number %"
 308              		.loc 1 447 132 is_stmt 1 view .LVU90
 448:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     ip4_debug_print(p);
 309              		.loc 1 448 23 view .LVU91
 449:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     pbuf_free(p);
 310              		.loc 1 449 5 view .LVU92
 311 0010 FFF7FEFF 		bl	pbuf_free
 312              	.LVL17:
 450:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     IP_STATS_INC(ip.err);
 313              		.loc 1 450 25 view .LVU93
 451:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     IP_STATS_INC(ip.drop);
 314              		.loc 1 451 26 view .LVU94
 452:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     MIB2_STATS_INC(mib2.ipinhdrerrors);
 315              		.loc 1 452 39 view .LVU95
 453:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     return ERR_OK;
 316              		.loc 1 453 5 view .LVU96
 317              	.L35:
 454:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   }
 455:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 456:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #ifdef LWIP_HOOK_IP4_INPUT
 457:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   if (LWIP_HOOK_IP4_INPUT(p, inp)) {
 458:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     /* the packet has been eaten */
 459:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     return ERR_OK;
 460:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   }
 461:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #endif
 462:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 463:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   /* obtain IP header length in bytes */
 464:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   iphdr_hlen = IPH_HL_BYTES(iphdr);
 465:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   /* obtain ip length in bytes */
 466:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   iphdr_len = lwip_ntohs(IPH_LEN(iphdr));
 467:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 468:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   /* Trim pbuf. This is especially required for packets < 60 bytes. */
 469:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   if (iphdr_len < p->tot_len) {
 470:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     pbuf_realloc(p, iphdr_len);
 471:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   }
 472:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 473:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   /* header length exceeds first pbuf length, or ip length exceeds total pbuf length? */
 474:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   if ((iphdr_hlen > p->len) || (iphdr_len > p->tot_len) || (iphdr_hlen < IP_HLEN)) {
 475:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     if (iphdr_hlen < IP_HLEN) {
 476:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       LWIP_DEBUGF(IP_DEBUG | LWIP_DBG_LEVEL_SERIOUS,
 477:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****                   ("ip4_input: short IP header (%"U16_F" bytes) received, IP packet dropped\n", iph
 478:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     }
 479:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     if (iphdr_hlen > p->len) {
 480:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       LWIP_DEBUGF(IP_DEBUG | LWIP_DBG_LEVEL_SERIOUS,
 481:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****                   ("IP header (len %"U16_F") does not fit in first pbuf (len %"U16_F"), IP packet d
 482:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****                    iphdr_hlen, p->len));
 483:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     }
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccEukS4o.s 			page 16


 484:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     if (iphdr_len > p->tot_len) {
 485:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       LWIP_DEBUGF(IP_DEBUG | LWIP_DBG_LEVEL_SERIOUS,
 486:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****                   ("IP (len %"U16_F") is longer than pbuf (len %"U16_F"), IP packet dropped.\n",
 487:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****                    iphdr_len, p->tot_len));
 488:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     }
 489:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     /* free (drop) packet pbufs */
 490:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     pbuf_free(p);
 491:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     IP_STATS_INC(ip.lenerr);
 492:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     IP_STATS_INC(ip.drop);
 493:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     MIB2_STATS_INC(mib2.ipindiscards);
 494:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     return ERR_OK;
 495:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   }
 496:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 497:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   /* verify checksum */
 498:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #if CHECKSUM_CHECK_IP
 499:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   IF__NETIF_CHECKSUM_ENABLED(inp, NETIF_CHECKSUM_CHECK_IP) {
 500:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     if (inet_chksum(iphdr, iphdr_hlen) != 0) {
 501:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 502:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       LWIP_DEBUGF(IP_DEBUG | LWIP_DBG_LEVEL_SERIOUS,
 503:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****                   ("Checksum (0x%"X16_F") failed, IP packet dropped.\n", inet_chksum(iphdr, iphdr_h
 504:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       ip4_debug_print(p);
 505:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       pbuf_free(p);
 506:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       IP_STATS_INC(ip.chkerr);
 507:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       IP_STATS_INC(ip.drop);
 508:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       MIB2_STATS_INC(mib2.ipinhdrerrors);
 509:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       return ERR_OK;
 510:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     }
 511:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   }
 512:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #endif
 513:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 514:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   /* copy IP addresses to aligned ip_addr_t */
 515:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   ip_addr_copy_from_ip4(ip_data.current_iphdr_dest, iphdr->dest);
 516:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   ip_addr_copy_from_ip4(ip_data.current_iphdr_src, iphdr->src);
 517:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 518:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   /* match packet against an interface, i.e. is this packet for us? */
 519:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   if (ip4_addr_ismulticast(ip4_current_dest_addr())) {
 520:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #if LWIP_IGMP
 521:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     if ((inp->flags & NETIF_FLAG_IGMP) && (igmp_lookfor_group(inp, ip4_current_dest_addr()))) {
 522:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       /* IGMP snooping switches need 0.0.0.0 to be allowed as source address (RFC 4541) */
 523:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       ip4_addr_t allsystems;
 524:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       IP4_ADDR(&allsystems, 224, 0, 0, 1);
 525:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       if (ip4_addr_cmp(ip4_current_dest_addr(), &allsystems) &&
 526:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****           ip4_addr_isany(ip4_current_src_addr())) {
 527:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****         check_ip_src = 0;
 528:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       }
 529:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       netif = inp;
 530:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     } else {
 531:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       netif = NULL;
 532:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     }
 533:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #else /* LWIP_IGMP */
 534:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     if ((netif_is_up(inp)) && (!ip4_addr_isany_val(*netif_ip4_addr(inp)))) {
 535:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       netif = inp;
 536:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     } else {
 537:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       netif = NULL;
 538:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     }
 539:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #endif /* LWIP_IGMP */
 540:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   } else {
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccEukS4o.s 			page 17


 541:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     /* start trying with inp. if that's not acceptable, start walking the
 542:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****        list of configured netifs. */
 543:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     if (ip4_input_accept(inp)) {
 544:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       netif = inp;
 545:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     } else {
 546:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       netif = NULL;
 547:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #if !LWIP_NETIF_LOOPBACK || LWIP_HAVE_LOOPIF
 548:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       /* Packets sent to the loopback address must not be accepted on an
 549:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****        * interface that does not have the loopback address assigned to it,
 550:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****        * unless a non-loopback interface is used for loopback traffic. */
 551:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       if (!ip4_addr_isloopback(ip4_current_dest_addr()))
 552:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #endif /* !LWIP_NETIF_LOOPBACK || LWIP_HAVE_LOOPIF */
 553:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       {
 554:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #if !LWIP_SINGLE_NETIF
 555:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****         NETIF_FOREACH(netif) {
 556:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****           if (netif == inp) {
 557:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****             /* we checked that before already */
 558:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****             continue;
 559:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****           }
 560:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****           if (ip4_input_accept(netif)) {
 561:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****             break;
 562:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****           }
 563:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****         }
 564:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #endif /* !LWIP_SINGLE_NETIF */
 565:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       }
 566:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     }
 567:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   }
 568:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 569:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #if IP_ACCEPT_LINK_LAYER_ADDRESSING
 570:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   /* Pass DHCP messages regardless of destination address. DHCP traffic is addressed
 571:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****    * using link layer addressing (such as Ethernet MAC) so we must not filter on IP.
 572:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****    * According to RFC 1542 section 3.1.1, referred by RFC 2131).
 573:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****    *
 574:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****    * If you want to accept private broadcast communication while a netif is down,
 575:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****    * define LWIP_IP_ACCEPT_UDP_PORT(dst_port), e.g.:
 576:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****    *
 577:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****    * #define LWIP_IP_ACCEPT_UDP_PORT(dst_port) ((dst_port) == PP_NTOHS(12345))
 578:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****    */
 579:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   if (netif == NULL) {
 580:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     /* remote port is DHCP server? */
 581:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     if (IPH_PROTO(iphdr) == IP_PROTO_UDP) {
 582:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       const struct udp_hdr *udphdr = (const struct udp_hdr *)((const u8_t *)iphdr + iphdr_hlen);
 583:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       LWIP_DEBUGF(IP_DEBUG | LWIP_DBG_TRACE, ("ip4_input: UDP packet to DHCP client port %"U16_F"\n
 584:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****                                               lwip_ntohs(udphdr->dest)));
 585:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       if (IP_ACCEPT_LINK_LAYER_ADDRESSED_PORT(udphdr->dest)) {
 586:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****         LWIP_DEBUGF(IP_DEBUG | LWIP_DBG_TRACE, ("ip4_input: DHCP packet accepted.\n"));
 587:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****         netif = inp;
 588:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****         check_ip_src = 0;
 589:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       }
 590:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     }
 591:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   }
 592:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #endif /* IP_ACCEPT_LINK_LAYER_ADDRESSING */
 593:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 594:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   /* broadcast or multicast packet source address? Compliant with RFC 1122: ******* */
 595:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #if LWIP_IGMP || IP_ACCEPT_LINK_LAYER_ADDRESSING
 596:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   if (check_ip_src
 597:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #if IP_ACCEPT_LINK_LAYER_ADDRESSING
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccEukS4o.s 			page 18


 598:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       /* DHCP servers need 0.0.0.0 to be allowed as source address (RFC *******: *******/a) */
 599:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       && !ip4_addr_isany_val(*ip4_current_src_addr())
 600:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #endif /* IP_ACCEPT_LINK_LAYER_ADDRESSING */
 601:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****      )
 602:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #endif /* LWIP_IGMP || IP_ACCEPT_LINK_LAYER_ADDRESSING */
 603:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   {
 604:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     if ((ip4_addr_isbroadcast(ip4_current_src_addr(), inp)) ||
 605:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****         (ip4_addr_ismulticast(ip4_current_src_addr()))) {
 606:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       /* packet source is not valid */
 607:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       LWIP_DEBUGF(IP_DEBUG | LWIP_DBG_TRACE | LWIP_DBG_LEVEL_WARNING, ("ip4_input: packet source is
 608:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       /* free (drop) packet pbufs */
 609:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       pbuf_free(p);
 610:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       IP_STATS_INC(ip.drop);
 611:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       MIB2_STATS_INC(mib2.ipinaddrerrors);
 612:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       MIB2_STATS_INC(mib2.ipindiscards);
 613:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       return ERR_OK;
 614:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     }
 615:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   }
 616:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 617:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   /* packet not for us? */
 618:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   if (netif == NULL) {
 619:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     /* packet not for us, route or discard */
 620:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     LWIP_DEBUGF(IP_DEBUG | LWIP_DBG_TRACE, ("ip4_input: packet not for us.\n"));
 621:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #if IP_FORWARD
 622:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     /* non-broadcast packet? */
 623:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     if (!ip4_addr_isbroadcast(ip4_current_dest_addr(), inp)) {
 624:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       /* try to forward IP packet on (other) interfaces */
 625:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       ip4_forward(p, (struct ip_hdr *)p->payload, inp);
 626:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     } else
 627:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #endif /* IP_FORWARD */
 628:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     {
 629:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       IP_STATS_INC(ip.drop);
 630:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       MIB2_STATS_INC(mib2.ipinaddrerrors);
 631:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       MIB2_STATS_INC(mib2.ipindiscards);
 632:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     }
 633:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     pbuf_free(p);
 634:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     return ERR_OK;
 635:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   }
 636:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   /* packet consists of multiple fragments? */
 637:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   if ((IPH_OFFSET(iphdr) & PP_HTONS(IP_OFFMASK | IP_MF)) != 0) {
 638:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #if IP_REASSEMBLY /* packet fragment reassembly code present? */
 639:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     LWIP_DEBUGF(IP_DEBUG, ("IP packet is a fragment (id=0x%04"X16_F" tot_len=%"U16_F" len=%"U16_F" 
 640:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****                            lwip_ntohs(IPH_ID(iphdr)), p->tot_len, lwip_ntohs(IPH_LEN(iphdr)), (u16_
 641:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     /* reassemble the packet*/
 642:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     p = ip4_reass(p);
 643:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     /* packet not fully reassembled yet? */
 644:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     if (p == NULL) {
 645:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       return ERR_OK;
 646:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     }
 647:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     iphdr = (const struct ip_hdr *)p->payload;
 648:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #else /* IP_REASSEMBLY == 0, no packet fragment reassembly code present */
 649:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     pbuf_free(p);
 650:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     LWIP_DEBUGF(IP_DEBUG | LWIP_DBG_LEVEL_SERIOUS, ("IP packet dropped since it was fragmented (0x%
 651:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****                 lwip_ntohs(IPH_OFFSET(iphdr))));
 652:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     IP_STATS_INC(ip.opterr);
 653:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     IP_STATS_INC(ip.drop);
 654:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     /* unsupported protocol feature */
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccEukS4o.s 			page 19


 655:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     MIB2_STATS_INC(mib2.ipinunknownprotos);
 656:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     return ERR_OK;
 657:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #endif /* IP_REASSEMBLY */
 658:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   }
 659:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 660:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #if IP_OPTIONS_ALLOWED == 0 /* no support for IP options in the IP header? */
 661:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 662:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #if LWIP_IGMP
 663:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   /* there is an extra "router alert" option in IGMP messages which we allow for but do not police 
 664:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   if ((iphdr_hlen > IP_HLEN) &&  (IPH_PROTO(iphdr) != IP_PROTO_IGMP)) {
 665:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #else
 666:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   if (iphdr_hlen > IP_HLEN) {
 667:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #endif /* LWIP_IGMP */
 668:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     LWIP_DEBUGF(IP_DEBUG | LWIP_DBG_LEVEL_SERIOUS, ("IP packet dropped since there were IP options 
 669:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     pbuf_free(p);
 670:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     IP_STATS_INC(ip.opterr);
 671:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     IP_STATS_INC(ip.drop);
 672:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     /* unsupported protocol feature */
 673:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     MIB2_STATS_INC(mib2.ipinunknownprotos);
 674:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     return ERR_OK;
 675:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   }
 676:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #endif /* IP_OPTIONS_ALLOWED == 0 */
 677:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 678:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   /* send to upper layers */
 679:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   LWIP_DEBUGF(IP_DEBUG, ("ip4_input: \n"));
 680:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   ip4_debug_print(p);
 681:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   LWIP_DEBUGF(IP_DEBUG, ("ip4_input: p->len %"U16_F" p->tot_len %"U16_F"\n", p->len, p->tot_len));
 682:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 683:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   ip_data.current_netif = netif;
 684:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   ip_data.current_input_netif = inp;
 685:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   ip_data.current_ip4_header = iphdr;
 686:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   ip_data.current_ip_header_tot_len = IPH_HL_BYTES(iphdr);
 687:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 688:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #if LWIP_RAW
 689:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   /* raw input did not eat the packet? */
 690:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   raw_status = raw_input(p, inp);
 691:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   if (raw_status != RAW_INPUT_EATEN)
 692:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #endif /* LWIP_RAW */
 693:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   {
 694:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     pbuf_remove_header(p, iphdr_hlen); /* Move to payload, no check necessary. */
 695:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 696:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     switch (IPH_PROTO(iphdr)) {
 697:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #if LWIP_UDP
 698:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       case IP_PROTO_UDP:
 699:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #if LWIP_UDPLITE
 700:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       case IP_PROTO_UDPLITE:
 701:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #endif /* LWIP_UDPLITE */
 702:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****         MIB2_STATS_INC(mib2.ipindelivers);
 703:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****         udp_input(p, inp);
 704:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****         break;
 705:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #endif /* LWIP_UDP */
 706:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #if LWIP_TCP
 707:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       case IP_PROTO_TCP:
 708:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****         MIB2_STATS_INC(mib2.ipindelivers);
 709:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****         tcp_input(p, inp);
 710:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****         break;
 711:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #endif /* LWIP_TCP */
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccEukS4o.s 			page 20


 712:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #if LWIP_ICMP
 713:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       case IP_PROTO_ICMP:
 714:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****         MIB2_STATS_INC(mib2.ipindelivers);
 715:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****         icmp_input(p, inp);
 716:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****         break;
 717:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #endif /* LWIP_ICMP */
 718:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #if LWIP_IGMP
 719:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       case IP_PROTO_IGMP:
 720:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****         igmp_input(p, inp, ip4_current_dest_addr());
 721:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****         break;
 722:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #endif /* LWIP_IGMP */
 723:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       default:
 724:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #if LWIP_RAW
 725:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****         if (raw_status == RAW_INPUT_DELIVERED) {
 726:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****           MIB2_STATS_INC(mib2.ipindelivers);
 727:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****         } else
 728:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #endif /* LWIP_RAW */
 729:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****         {
 730:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #if LWIP_ICMP
 731:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****           /* send ICMP destination protocol unreachable unless is was a broadcast */
 732:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****           if (!ip4_addr_isbroadcast(ip4_current_dest_addr(), netif) &&
 733:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****               !ip4_addr_ismulticast(ip4_current_dest_addr())) {
 734:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****             pbuf_header_force(p, (s16_t)iphdr_hlen); /* Move to ip header, no check necessary. */
 735:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****             icmp_dest_unreach(p, ICMP_DUR_PROTO);
 736:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****           }
 737:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #endif /* LWIP_ICMP */
 738:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 739:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****           LWIP_DEBUGF(IP_DEBUG | LWIP_DBG_LEVEL_SERIOUS, ("Unsupported transport protocol %"U16_F"\
 740:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 741:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****           IP_STATS_INC(ip.proterr);
 742:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****           IP_STATS_INC(ip.drop);
 743:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****           MIB2_STATS_INC(mib2.ipinunknownprotos);
 744:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****         }
 745:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****         pbuf_free(p);
 746:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****         break;
 747:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     }
 748:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   }
 749:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 750:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   /* @todo: this is not really necessary... */
 751:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   ip_data.current_netif = NULL;
 752:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   ip_data.current_input_netif = NULL;
 753:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   ip_data.current_ip4_header = NULL;
 754:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   ip_data.current_ip_header_tot_len = 0;
 755:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   ip4_addr_set_any(ip4_current_src_addr());
 756:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   ip4_addr_set_any(ip4_current_dest_addr());
 757:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 758:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   return ERR_OK;
 759:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** }
 318              		.loc 1 759 1 is_stmt 0 view .LVU97
 319 0014 0020     		movs	r0, #0
 320 0016 BDE8F081 		pop	{r4, r5, r6, r7, r8, pc}
 321              	.LVL18:
 322              	.L34:
 323              		.loc 1 759 1 view .LVU98
 324 001a 0E46     		mov	r6, r1
 464:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   /* obtain ip length in bytes */
 325              		.loc 1 464 3 is_stmt 1 view .LVU99
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccEukS4o.s 			page 21


 464:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   /* obtain ip length in bytes */
 326              		.loc 1 464 16 is_stmt 0 view .LVU100
 327 001c 03F00F03 		and	r3, r3, #15
 328 0020 9D00     		lsls	r5, r3, #2
 329              	.LVL19:
 466:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 330              		.loc 1 466 3 is_stmt 1 view .LVU101
 466:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 331              		.loc 1 466 15 is_stmt 0 view .LVU102
 332 0022 7888     		ldrh	r0, [r7, #2]	@ unaligned
 333              	.LVL20:
 466:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 334              		.loc 1 466 15 view .LVU103
 335 0024 FFF7FEFF 		bl	lwip_htons
 336              	.LVL21:
 466:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 337              		.loc 1 466 15 view .LVU104
 338 0028 8046     		mov	r8, r0
 339              	.LVL22:
 469:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     pbuf_realloc(p, iphdr_len);
 340              		.loc 1 469 3 is_stmt 1 view .LVU105
 469:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     pbuf_realloc(p, iphdr_len);
 341              		.loc 1 469 20 is_stmt 0 view .LVU106
 342 002a 2389     		ldrh	r3, [r4, #8]
 469:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     pbuf_realloc(p, iphdr_len);
 343              		.loc 1 469 6 view .LVU107
 344 002c 8342     		cmp	r3, r0
 345 002e 1BD8     		bhi	.L60
 346              	.LVL23:
 347              	.L36:
 474:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     if (iphdr_hlen < IP_HLEN) {
 348              		.loc 1 474 3 is_stmt 1 view .LVU108
 474:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     if (iphdr_hlen < IP_HLEN) {
 349              		.loc 1 474 22 is_stmt 0 view .LVU109
 350 0030 6389     		ldrh	r3, [r4, #10]
 474:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     if (iphdr_hlen < IP_HLEN) {
 351              		.loc 1 474 6 view .LVU110
 352 0032 AB42     		cmp	r3, r5
 353 0034 1DD3     		bcc	.L37
 474:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     if (iphdr_hlen < IP_HLEN) {
 354              		.loc 1 474 46 discriminator 1 view .LVU111
 355 0036 2389     		ldrh	r3, [r4, #8]
 474:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     if (iphdr_hlen < IP_HLEN) {
 356              		.loc 1 474 29 discriminator 1 view .LVU112
 357 0038 4345     		cmp	r3, r8
 358 003a 1AD3     		bcc	.L37
 474:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     if (iphdr_hlen < IP_HLEN) {
 359              		.loc 1 474 57 discriminator 2 view .LVU113
 360 003c 132D     		cmp	r5, #19
 361 003e 18D9     		bls	.L37
 515:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   ip_addr_copy_from_ip4(ip_data.current_iphdr_src, iphdr->src);
 362              		.loc 1 515 3 is_stmt 1 view .LVU114
 363 0040 3B69     		ldr	r3, [r7, #16]	@ unaligned
 364 0042 544A     		ldr	r2, .L63
 365 0044 5361     		str	r3, [r2, #20]
 516:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 366              		.loc 1 516 3 view .LVU115
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccEukS4o.s 			page 22


 367 0046 F968     		ldr	r1, [r7, #12]	@ unaligned
 368 0048 1161     		str	r1, [r2, #16]
 519:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #if LWIP_IGMP
 369              		.loc 1 519 3 view .LVU116
 519:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #if LWIP_IGMP
 370              		.loc 1 519 7 is_stmt 0 view .LVU117
 371 004a 03F0F003 		and	r3, r3, #240
 519:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #if LWIP_IGMP
 372              		.loc 1 519 6 view .LVU118
 373 004e E02B     		cmp	r3, #224
 374 0050 13D1     		bne	.L39
 534:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       netif = inp;
 375              		.loc 1 534 5 is_stmt 1 view .LVU119
 534:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       netif = inp;
 376              		.loc 1 534 10 is_stmt 0 view .LVU120
 377 0052 96F82D30 		ldrb	r3, [r6, #45]	@ zero_extendqisi2
 534:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       netif = inp;
 378              		.loc 1 534 8 view .LVU121
 379 0056 13F0010F 		tst	r3, #1
 380 005a 28D0     		beq	.L54
 534:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       netif = inp;
 381              		.loc 1 534 33 discriminator 1 view .LVU122
 382 005c 7368     		ldr	r3, [r6, #4]
 534:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       netif = inp;
 383              		.loc 1 534 28 discriminator 1 view .LVU123
 384 005e 002B     		cmp	r3, #0
 385 0060 68D1     		bne	.L55
 537:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     }
 386              		.loc 1 537 13 view .LVU124
 387 0062 4FF00008 		mov	r8, #0
 388 0066 24E0     		b	.L40
 389              	.LVL24:
 390              	.L60:
 470:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   }
 391              		.loc 1 470 5 is_stmt 1 view .LVU125
 392 0068 0146     		mov	r1, r0
 393 006a 2046     		mov	r0, r4
 394              	.LVL25:
 470:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   }
 395              		.loc 1 470 5 is_stmt 0 view .LVU126
 396 006c FFF7FEFF 		bl	pbuf_realloc
 397              	.LVL26:
 398 0070 DEE7     		b	.L36
 399              	.L37:
 475:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       LWIP_DEBUGF(IP_DEBUG | LWIP_DBG_LEVEL_SERIOUS,
 400              		.loc 1 475 5 is_stmt 1 view .LVU127
 477:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     }
 401              		.loc 1 477 109 view .LVU128
 479:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       LWIP_DEBUGF(IP_DEBUG | LWIP_DBG_LEVEL_SERIOUS,
 402              		.loc 1 479 5 view .LVU129
 482:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     }
 403              		.loc 1 482 40 view .LVU130
 484:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       LWIP_DEBUGF(IP_DEBUG | LWIP_DBG_LEVEL_SERIOUS,
 404              		.loc 1 484 5 view .LVU131
 487:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     }
 405              		.loc 1 487 43 view .LVU132
 490:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     IP_STATS_INC(ip.lenerr);
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccEukS4o.s 			page 23


 406              		.loc 1 490 5 view .LVU133
 407 0072 2046     		mov	r0, r4
 408 0074 FFF7FEFF 		bl	pbuf_free
 409              	.LVL27:
 491:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     IP_STATS_INC(ip.drop);
 410              		.loc 1 491 28 view .LVU134
 492:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     MIB2_STATS_INC(mib2.ipindiscards);
 411              		.loc 1 492 26 view .LVU135
 493:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     return ERR_OK;
 412              		.loc 1 493 38 view .LVU136
 494:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   }
 413              		.loc 1 494 5 view .LVU137
 494:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   }
 414              		.loc 1 494 12 is_stmt 0 view .LVU138
 415 0078 CCE7     		b	.L35
 416              	.L39:
 543:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       netif = inp;
 417              		.loc 1 543 5 is_stmt 1 view .LVU139
 543:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       netif = inp;
 418              		.loc 1 543 9 is_stmt 0 view .LVU140
 419 007a 3046     		mov	r0, r6
 420 007c FFF7FEFF 		bl	ip4_input_accept
 421              	.LVL28:
 543:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       netif = inp;
 422              		.loc 1 543 8 discriminator 1 view .LVU141
 423 0080 0028     		cmp	r0, #0
 424 0082 59D1     		bne	.L56
 546:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #if !LWIP_NETIF_LOOPBACK || LWIP_HAVE_LOOPIF
 425              		.loc 1 546 7 is_stmt 1 view .LVU142
 426              	.LVL29:
 551:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #endif /* !LWIP_NETIF_LOOPBACK || LWIP_HAVE_LOOPIF */
 427              		.loc 1 551 7 view .LVU143
 551:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #endif /* !LWIP_NETIF_LOOPBACK || LWIP_HAVE_LOOPIF */
 428              		.loc 1 551 12 is_stmt 0 view .LVU144
 429 0084 434B     		ldr	r3, .L63
 430 0086 1B7D     		ldrb	r3, [r3, #20]	@ zero_extendqisi2
 551:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #endif /* !LWIP_NETIF_LOOPBACK || LWIP_HAVE_LOOPIF */
 431              		.loc 1 551 10 view .LVU145
 432 0088 7F2B     		cmp	r3, #127
 433 008a 57D0     		beq	.L57
 555:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****           if (netif == inp) {
 434              		.loc 1 555 9 is_stmt 1 view .LVU146
 435 008c 424B     		ldr	r3, .L63+4
 436 008e D3F80080 		ldr	r8, [r3]
 437              	.LVL30:
 555:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****           if (netif == inp) {
 438              		.loc 1 555 9 is_stmt 0 view .LVU147
 439 0092 01E0     		b	.L41
 440              	.L42:
 555:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****           if (netif == inp) {
 441              		.loc 1 555 9 is_stmt 1 discriminator 2 view .LVU148
 442 0094 D8F80080 		ldr	r8, [r8]
 443              	.LVL31:
 444              	.L41:
 555:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****           if (netif == inp) {
 445              		.loc 1 555 9 discriminator 1 view .LVU149
 446 0098 B8F1000F 		cmp	r8, #0
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccEukS4o.s 			page 24


 447 009c 09D0     		beq	.L40
 556:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****             /* we checked that before already */
 448              		.loc 1 556 11 view .LVU150
 556:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****             /* we checked that before already */
 449              		.loc 1 556 14 is_stmt 0 view .LVU151
 450 009e B045     		cmp	r8, r6
 451 00a0 F8D0     		beq	.L42
 560:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****             break;
 452              		.loc 1 560 11 is_stmt 1 view .LVU152
 560:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****             break;
 453              		.loc 1 560 15 is_stmt 0 view .LVU153
 454 00a2 4046     		mov	r0, r8
 455 00a4 FFF7FEFF 		bl	ip4_input_accept
 456              	.LVL32:
 560:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****             break;
 457              		.loc 1 560 14 discriminator 1 view .LVU154
 458 00a8 0028     		cmp	r0, #0
 459 00aa F3D0     		beq	.L42
 460 00ac 01E0     		b	.L40
 461              	.LVL33:
 462              	.L54:
 537:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     }
 463              		.loc 1 537 13 view .LVU155
 464 00ae 4FF00008 		mov	r8, #0
 465              	.L40:
 466              	.LVL34:
 604:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****         (ip4_addr_ismulticast(ip4_current_src_addr()))) {
 467              		.loc 1 604 5 is_stmt 1 view .LVU156
 604:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****         (ip4_addr_ismulticast(ip4_current_src_addr()))) {
 468              		.loc 1 604 10 is_stmt 0 view .LVU157
 469 00b2 3146     		mov	r1, r6
 470 00b4 374B     		ldr	r3, .L63
 471 00b6 1869     		ldr	r0, [r3, #16]
 472 00b8 FFF7FEFF 		bl	ip4_addr_isbroadcast_u32
 473              	.LVL35:
 604:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****         (ip4_addr_ismulticast(ip4_current_src_addr()))) {
 474              		.loc 1 604 8 discriminator 1 view .LVU158
 475 00bc 0028     		cmp	r0, #0
 476 00be 40D1     		bne	.L44
 605:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       /* packet source is not valid */
 477              		.loc 1 605 10 view .LVU159
 478 00c0 344B     		ldr	r3, .L63
 479 00c2 1B69     		ldr	r3, [r3, #16]
 480 00c4 03F0F003 		and	r3, r3, #240
 604:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****         (ip4_addr_ismulticast(ip4_current_src_addr()))) {
 481              		.loc 1 604 61 discriminator 1 view .LVU160
 482 00c8 E02B     		cmp	r3, #224
 483 00ca 3AD0     		beq	.L44
 618:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     /* packet not for us, route or discard */
 484              		.loc 1 618 3 is_stmt 1 view .LVU161
 618:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     /* packet not for us, route or discard */
 485              		.loc 1 618 6 is_stmt 0 view .LVU162
 486 00cc B8F1000F 		cmp	r8, #0
 487 00d0 3BD0     		beq	.L61
 637:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #if IP_REASSEMBLY /* packet fragment reassembly code present? */
 488              		.loc 1 637 3 is_stmt 1 view .LVU163
 637:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #if IP_REASSEMBLY /* packet fragment reassembly code present? */
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccEukS4o.s 			page 25


 489              		.loc 1 637 8 is_stmt 0 view .LVU164
 490 00d2 FB88     		ldrh	r3, [r7, #6]	@ unaligned
 637:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #if IP_REASSEMBLY /* packet fragment reassembly code present? */
 491              		.loc 1 637 6 view .LVU165
 492 00d4 23F0C003 		bic	r3, r3, #192
 493 00d8 9BB2     		uxth	r3, r3
 494 00da 33B1     		cbz	r3, .L47
 640:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     /* reassemble the packet*/
 495              		.loc 1 640 202 is_stmt 1 view .LVU166
 642:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     /* packet not fully reassembled yet? */
 496              		.loc 1 642 5 view .LVU167
 642:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     /* packet not fully reassembled yet? */
 497              		.loc 1 642 9 is_stmt 0 view .LVU168
 498 00dc 2046     		mov	r0, r4
 499 00de FFF7FEFF 		bl	ip4_reass
 500              	.LVL36:
 644:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       return ERR_OK;
 501              		.loc 1 644 5 is_stmt 1 view .LVU169
 644:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       return ERR_OK;
 502              		.loc 1 644 8 is_stmt 0 view .LVU170
 503 00e2 0446     		mov	r4, r0
 504 00e4 0028     		cmp	r0, #0
 505 00e6 95D0     		beq	.L35
 647:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #else /* IP_REASSEMBLY == 0, no packet fragment reassembly code present */
 506              		.loc 1 647 5 is_stmt 1 view .LVU171
 647:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #else /* IP_REASSEMBLY == 0, no packet fragment reassembly code present */
 507              		.loc 1 647 11 is_stmt 0 view .LVU172
 508 00e8 4768     		ldr	r7, [r0, #4]
 509              	.LVL37:
 510              	.L47:
 679:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   ip4_debug_print(p);
 511              		.loc 1 679 43 is_stmt 1 view .LVU173
 680:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   LWIP_DEBUGF(IP_DEBUG, ("ip4_input: p->len %"U16_F" p->tot_len %"U16_F"\n", p->len, p->tot_len));
 512              		.loc 1 680 21 view .LVU174
 681:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 513              		.loc 1 681 98 view .LVU175
 683:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   ip_data.current_input_netif = inp;
 514              		.loc 1 683 3 view .LVU176
 683:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   ip_data.current_input_netif = inp;
 515              		.loc 1 683 25 is_stmt 0 view .LVU177
 516 00ea 2A4A     		ldr	r2, .L63
 517 00ec C2F80080 		str	r8, [r2]
 684:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   ip_data.current_ip4_header = iphdr;
 518              		.loc 1 684 3 is_stmt 1 view .LVU178
 684:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   ip_data.current_ip4_header = iphdr;
 519              		.loc 1 684 31 is_stmt 0 view .LVU179
 520 00f0 5660     		str	r6, [r2, #4]
 685:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   ip_data.current_ip_header_tot_len = IPH_HL_BYTES(iphdr);
 521              		.loc 1 685 3 is_stmt 1 view .LVU180
 685:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   ip_data.current_ip_header_tot_len = IPH_HL_BYTES(iphdr);
 522              		.loc 1 685 30 is_stmt 0 view .LVU181
 523 00f2 9760     		str	r7, [r2, #8]
 686:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 524              		.loc 1 686 3 is_stmt 1 view .LVU182
 686:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 525              		.loc 1 686 39 is_stmt 0 view .LVU183
 526 00f4 3B78     		ldrb	r3, [r7]	@ zero_extendqisi2
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccEukS4o.s 			page 26


 527 00f6 03F00F03 		and	r3, r3, #15
 528 00fa 9B00     		lsls	r3, r3, #2
 686:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 529              		.loc 1 686 37 view .LVU184
 530 00fc 9381     		strh	r3, [r2, #12]	@ movhi
 694:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 531              		.loc 1 694 5 is_stmt 1 view .LVU185
 532 00fe 2946     		mov	r1, r5
 533 0100 2046     		mov	r0, r4
 534 0102 FFF7FEFF 		bl	pbuf_remove_header
 535              	.LVL38:
 696:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #if LWIP_UDP
 536              		.loc 1 696 5 view .LVU186
 696:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #if LWIP_UDP
 537              		.loc 1 696 13 is_stmt 0 view .LVU187
 538 0106 7B7A     		ldrb	r3, [r7, #9]	@ zero_extendqisi2
 696:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #if LWIP_UDP
 539              		.loc 1 696 5 view .LVU188
 540 0108 062B     		cmp	r3, #6
 541 010a 2FD0     		beq	.L48
 696:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #if LWIP_UDP
 542              		.loc 1 696 5 view .LVU189
 543 010c 112B     		cmp	r3, #17
 544 010e 20D0     		beq	.L49
 545 0110 012B     		cmp	r3, #1
 546 0112 30D0     		beq	.L50
 732:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****               !ip4_addr_ismulticast(ip4_current_dest_addr())) {
 547              		.loc 1 732 11 is_stmt 1 view .LVU190
 732:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****               !ip4_addr_ismulticast(ip4_current_dest_addr())) {
 548              		.loc 1 732 16 is_stmt 0 view .LVU191
 549 0114 4146     		mov	r1, r8
 550 0116 1F4B     		ldr	r3, .L63
 551 0118 5869     		ldr	r0, [r3, #20]
 552 011a FFF7FEFF 		bl	ip4_addr_isbroadcast_u32
 553              	.LVL39:
 732:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****               !ip4_addr_ismulticast(ip4_current_dest_addr())) {
 554              		.loc 1 732 14 discriminator 1 view .LVU192
 555 011e 28B9     		cbnz	r0, .L53
 733:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****             pbuf_header_force(p, (s16_t)iphdr_hlen); /* Move to ip header, no check necessary. */
 556              		.loc 1 733 16 view .LVU193
 557 0120 1C4B     		ldr	r3, .L63
 558 0122 5B69     		ldr	r3, [r3, #20]
 559 0124 03F0F003 		and	r3, r3, #240
 732:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****               !ip4_addr_ismulticast(ip4_current_dest_addr())) {
 560              		.loc 1 732 69 discriminator 1 view .LVU194
 561 0128 E02B     		cmp	r3, #224
 562 012a 29D1     		bne	.L62
 563              	.L53:
 739:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 564              		.loc 1 739 129 is_stmt 1 view .LVU195
 741:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****           IP_STATS_INC(ip.drop);
 565              		.loc 1 741 35 view .LVU196
 742:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****           MIB2_STATS_INC(mib2.ipinunknownprotos);
 566              		.loc 1 742 32 view .LVU197
 743:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****         }
 567              		.loc 1 743 49 view .LVU198
 745:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****         break;
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccEukS4o.s 			page 27


 568              		.loc 1 745 9 view .LVU199
 569 012c 2046     		mov	r0, r4
 570 012e FFF7FEFF 		bl	pbuf_free
 571              	.LVL40:
 746:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     }
 572              		.loc 1 746 9 view .LVU200
 573 0132 12E0     		b	.L52
 574              	.LVL41:
 575              	.L55:
 535:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     } else {
 576              		.loc 1 535 13 is_stmt 0 view .LVU201
 577 0134 B046     		mov	r8, r6
 578 0136 BCE7     		b	.L40
 579              	.L56:
 544:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     } else {
 580              		.loc 1 544 13 view .LVU202
 581 0138 B046     		mov	r8, r6
 582 013a BAE7     		b	.L40
 583              	.LVL42:
 584              	.L57:
 546:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #if !LWIP_NETIF_LOOPBACK || LWIP_HAVE_LOOPIF
 585              		.loc 1 546 13 view .LVU203
 586 013c 4FF00008 		mov	r8, #0
 587 0140 B7E7     		b	.L40
 588              	.LVL43:
 589              	.L44:
 607:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       /* free (drop) packet pbufs */
 590              		.loc 1 607 116 is_stmt 1 view .LVU204
 609:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       IP_STATS_INC(ip.drop);
 591              		.loc 1 609 7 view .LVU205
 592 0142 2046     		mov	r0, r4
 593 0144 FFF7FEFF 		bl	pbuf_free
 594              	.LVL44:
 610:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       MIB2_STATS_INC(mib2.ipinaddrerrors);
 595              		.loc 1 610 28 view .LVU206
 611:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       MIB2_STATS_INC(mib2.ipindiscards);
 596              		.loc 1 611 42 view .LVU207
 612:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       return ERR_OK;
 597              		.loc 1 612 40 view .LVU208
 613:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     }
 598              		.loc 1 613 7 view .LVU209
 613:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     }
 599              		.loc 1 613 14 is_stmt 0 view .LVU210
 600 0148 64E7     		b	.L35
 601              	.L61:
 620:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #if IP_FORWARD
 602              		.loc 1 620 80 is_stmt 1 view .LVU211
 629:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       MIB2_STATS_INC(mib2.ipinaddrerrors);
 603              		.loc 1 629 28 view .LVU212
 630:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       MIB2_STATS_INC(mib2.ipindiscards);
 604              		.loc 1 630 42 view .LVU213
 631:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     }
 605              		.loc 1 631 40 view .LVU214
 633:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     return ERR_OK;
 606              		.loc 1 633 5 view .LVU215
 607 014a 2046     		mov	r0, r4
 608 014c FFF7FEFF 		bl	pbuf_free
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccEukS4o.s 			page 28


 609              	.LVL45:
 634:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   }
 610              		.loc 1 634 5 view .LVU216
 634:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   }
 611              		.loc 1 634 12 is_stmt 0 view .LVU217
 612 0150 60E7     		b	.L35
 613              	.L49:
 702:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****         udp_input(p, inp);
 614              		.loc 1 702 42 is_stmt 1 view .LVU218
 703:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****         break;
 615              		.loc 1 703 9 view .LVU219
 616 0152 3146     		mov	r1, r6
 617 0154 2046     		mov	r0, r4
 618 0156 FFF7FEFF 		bl	udp_input
 619              	.LVL46:
 704:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #endif /* LWIP_UDP */
 620              		.loc 1 704 9 view .LVU220
 621              	.L52:
 751:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   ip_data.current_input_netif = NULL;
 622              		.loc 1 751 3 view .LVU221
 751:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   ip_data.current_input_netif = NULL;
 623              		.loc 1 751 25 is_stmt 0 view .LVU222
 624 015a 0E4B     		ldr	r3, .L63
 625 015c 0022     		movs	r2, #0
 626 015e 1A60     		str	r2, [r3]
 752:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   ip_data.current_ip4_header = NULL;
 627              		.loc 1 752 3 is_stmt 1 view .LVU223
 752:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   ip_data.current_ip4_header = NULL;
 628              		.loc 1 752 31 is_stmt 0 view .LVU224
 629 0160 5A60     		str	r2, [r3, #4]
 753:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   ip_data.current_ip_header_tot_len = 0;
 630              		.loc 1 753 3 is_stmt 1 view .LVU225
 753:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   ip_data.current_ip_header_tot_len = 0;
 631              		.loc 1 753 30 is_stmt 0 view .LVU226
 632 0162 9A60     		str	r2, [r3, #8]
 754:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   ip4_addr_set_any(ip4_current_src_addr());
 633              		.loc 1 754 3 is_stmt 1 view .LVU227
 754:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   ip4_addr_set_any(ip4_current_src_addr());
 634              		.loc 1 754 37 is_stmt 0 view .LVU228
 635 0164 9A81     		strh	r2, [r3, #12]	@ movhi
 755:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   ip4_addr_set_any(ip4_current_dest_addr());
 636              		.loc 1 755 3 is_stmt 1 view .LVU229
 637 0166 1A61     		str	r2, [r3, #16]
 756:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 638              		.loc 1 756 3 view .LVU230
 639 0168 5A61     		str	r2, [r3, #20]
 758:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** }
 640              		.loc 1 758 3 view .LVU231
 758:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** }
 641              		.loc 1 758 10 is_stmt 0 view .LVU232
 642 016a 53E7     		b	.L35
 643              	.L48:
 708:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****         tcp_input(p, inp);
 644              		.loc 1 708 42 is_stmt 1 view .LVU233
 709:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****         break;
 645              		.loc 1 709 9 view .LVU234
 646 016c 3146     		mov	r1, r6
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccEukS4o.s 			page 29


 647 016e 2046     		mov	r0, r4
 648 0170 FFF7FEFF 		bl	tcp_input
 649              	.LVL47:
 710:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #endif /* LWIP_TCP */
 650              		.loc 1 710 9 view .LVU235
 651 0174 F1E7     		b	.L52
 652              	.L50:
 714:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****         icmp_input(p, inp);
 653              		.loc 1 714 42 view .LVU236
 715:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****         break;
 654              		.loc 1 715 9 view .LVU237
 655 0176 3146     		mov	r1, r6
 656 0178 2046     		mov	r0, r4
 657 017a FFF7FEFF 		bl	icmp_input
 658              	.LVL48:
 716:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #endif /* LWIP_ICMP */
 659              		.loc 1 716 9 view .LVU238
 660 017e ECE7     		b	.L52
 661              	.L62:
 734:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****             icmp_dest_unreach(p, ICMP_DUR_PROTO);
 662              		.loc 1 734 13 view .LVU239
 663 0180 2946     		mov	r1, r5
 664 0182 2046     		mov	r0, r4
 665 0184 FFF7FEFF 		bl	pbuf_header_force
 666              	.LVL49:
 735:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****           }
 667              		.loc 1 735 13 view .LVU240
 668 0188 0221     		movs	r1, #2
 669 018a 2046     		mov	r0, r4
 670 018c FFF7FEFF 		bl	icmp_dest_unreach
 671              	.LVL50:
 672 0190 CCE7     		b	.L53
 673              	.L64:
 674 0192 00BF     		.align	2
 675              	.L63:
 676 0194 00000000 		.word	ip_data
 677 0198 00000000 		.word	netif_list
 678              		.cfi_endproc
 679              	.LFE172:
 681              		.section	.rodata.ip4_output_if_src.str1.4,"aMS",%progbits,1
 682              		.align	2
 683              	.LC0:
 684 0000 4D696464 		.ascii	"Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c\000"
 684      6C657761 
 684      7265732F 
 684      54686972 
 684      645F5061 
 685 0031 000000   		.align	2
 686              	.LC1:
 687 0034 702D3E72 		.ascii	"p->ref == 1\000"
 687      6566203D 
 687      3D203100 
 688              		.align	2
 689              	.LC2:
 690 0040 41737365 		.ascii	"Assertion \"%s\" failed at line %d in %s\012\000"
 690      7274696F 
 690      6E202225 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccEukS4o.s 			page 30


 690      73222066 
 690      61696C65 
 691              		.align	2
 692              	.LC3:
 693 0068 63686563 		.ascii	"check that first pbuf can hold struct ip_hdr\000"
 693      6B207468 
 693      61742066 
 693      69727374 
 693      20706275 
 694              		.section	.text.ip4_output_if_src,"ax",%progbits
 695              		.align	1
 696              		.global	ip4_output_if_src
 697              		.syntax unified
 698              		.thumb
 699              		.thumb_func
 701              	ip4_output_if_src:
 702              	.LVL51:
 703              	.LFB174:
 760:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 761:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** /**
 762:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * Sends an IP packet on a network interface. This function constructs
 763:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * the IP header and calculates the IP header checksum. If the source
 764:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * IP address is NULL, the IP address of the outgoing network
 765:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * interface is filled in as source address.
 766:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * If the destination IP address is LWIP_IP_HDRINCL, p is assumed to already
 767:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * include an IP header and p->payload points to it instead of the data.
 768:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  *
 769:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * @param p the packet to send (p->payload points to the data, e.g. next
 770:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****             protocol header; if dest == LWIP_IP_HDRINCL, p already includes an
 771:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****             IP header and p->payload points to that IP header)
 772:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * @param src the source IP address to send from (if src == IP4_ADDR_ANY, the
 773:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  *         IP  address of the netif used to send is used as source address)
 774:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * @param dest the destination IP address to send the packet to
 775:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * @param ttl the TTL value to be set in the IP header
 776:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * @param tos the TOS value to be set in the IP header
 777:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * @param proto the PROTOCOL to be set in the IP header
 778:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * @param netif the netif on which to send this packet
 779:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * @return ERR_OK if the packet was sent OK
 780:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  *         ERR_BUF if p doesn't have enough space for IP/LINK headers
 781:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  *         returns errors returned by netif->output
 782:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  *
 783:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * @note ip_id: RFC791 "some host may be able to simply use
 784:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  *  unique identifiers independent of destination"
 785:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  */
 786:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** err_t
 787:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** ip4_output_if(struct pbuf *p, const ip4_addr_t *src, const ip4_addr_t *dest,
 788:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****               u8_t ttl, u8_t tos,
 789:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****               u8_t proto, struct netif *netif)
 790:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** {
 791:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #if IP_OPTIONS_SEND
 792:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   return ip4_output_if_opt(p, src, dest, ttl, tos, proto, netif, NULL, 0);
 793:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** }
 794:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 795:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** /**
 796:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * Same as ip_output_if() but with the possibility to include IP options:
 797:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  *
 798:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * @ param ip_options pointer to the IP options, copied into the IP header
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccEukS4o.s 			page 31


 799:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * @ param optlen length of ip_options
 800:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  */
 801:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** err_t
 802:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** ip4_output_if_opt(struct pbuf *p, const ip4_addr_t *src, const ip4_addr_t *dest,
 803:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****                   u8_t ttl, u8_t tos, u8_t proto, struct netif *netif, void *ip_options,
 804:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****                   u16_t optlen)
 805:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** {
 806:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #endif /* IP_OPTIONS_SEND */
 807:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   const ip4_addr_t *src_used = src;
 808:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   if (dest != LWIP_IP_HDRINCL) {
 809:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     if (ip4_addr_isany(src)) {
 810:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       src_used = netif_ip4_addr(netif);
 811:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     }
 812:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   }
 813:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 814:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #if IP_OPTIONS_SEND
 815:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   return ip4_output_if_opt_src(p, src_used, dest, ttl, tos, proto, netif,
 816:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****                                ip_options, optlen);
 817:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #else /* IP_OPTIONS_SEND */
 818:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   return ip4_output_if_src(p, src_used, dest, ttl, tos, proto, netif);
 819:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #endif /* IP_OPTIONS_SEND */
 820:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** }
 821:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 822:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** /**
 823:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * Same as ip_output_if() but 'src' address is not replaced by netif address
 824:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * when it is 'any'.
 825:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  */
 826:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** err_t
 827:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** ip4_output_if_src(struct pbuf *p, const ip4_addr_t *src, const ip4_addr_t *dest,
 828:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****                   u8_t ttl, u8_t tos,
 829:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****                   u8_t proto, struct netif *netif)
 830:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** {
 704              		.loc 1 830 1 view -0
 705              		.cfi_startproc
 706              		@ args = 12, pretend = 0, frame = 8
 707              		@ frame_needed = 0, uses_anonymous_args = 0
 708              		.loc 1 830 1 is_stmt 0 view .LVU242
 709 0000 2DE9F043 		push	{r4, r5, r6, r7, r8, r9, lr}
 710              	.LCFI6:
 711              		.cfi_def_cfa_offset 28
 712              		.cfi_offset 4, -28
 713              		.cfi_offset 5, -24
 714              		.cfi_offset 6, -20
 715              		.cfi_offset 7, -16
 716              		.cfi_offset 8, -12
 717              		.cfi_offset 9, -8
 718              		.cfi_offset 14, -4
 719 0004 83B0     		sub	sp, sp, #12
 720              	.LCFI7:
 721              		.cfi_def_cfa_offset 40
 722 0006 0446     		mov	r4, r0
 723 0008 0F46     		mov	r7, r1
 724 000a 1646     		mov	r6, r2
 725 000c 9946     		mov	r9, r3
 726 000e DDF83080 		ldr	r8, [sp, #48]
 831:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #if IP_OPTIONS_SEND
 832:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   return ip4_output_if_opt_src(p, src, dest, ttl, tos, proto, netif, NULL, 0);
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccEukS4o.s 			page 32


 833:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** }
 834:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 835:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** /**
 836:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * Same as ip_output_if_opt() but 'src' address is not replaced by netif address
 837:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * when it is 'any'.
 838:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  */
 839:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** err_t
 840:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** ip4_output_if_opt_src(struct pbuf *p, const ip4_addr_t *src, const ip4_addr_t *dest,
 841:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****                       u8_t ttl, u8_t tos, u8_t proto, struct netif *netif, void *ip_options,
 842:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****                       u16_t optlen)
 843:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** {
 844:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #endif /* IP_OPTIONS_SEND */
 845:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   struct ip_hdr *iphdr;
 727              		.loc 1 845 3 is_stmt 1 view .LVU243
 846:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   ip4_addr_t dest_addr;
 728              		.loc 1 846 3 view .LVU244
 847:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #if CHECKSUM_GEN_IP_INLINE
 848:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   u32_t chk_sum = 0;
 849:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #endif /* CHECKSUM_GEN_IP_INLINE */
 850:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 851:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   LWIP_ASSERT_CORE_LOCKED();
 729              		.loc 1 851 28 view .LVU245
 852:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   LWIP_IP_CHECK_PBUF_REF_COUNT_FOR_TX(p);
 730              		.loc 1 852 3 view .LVU246
 731              		.loc 1 852 3 view .LVU247
 732 0012 827B     		ldrb	r2, [r0, #14]	@ zero_extendqisi2
 733              	.LVL52:
 734              		.loc 1 852 3 is_stmt 0 view .LVU248
 735 0014 012A     		cmp	r2, #1
 736 0016 3FD1     		bne	.L77
 737              	.LVL53:
 738              	.L66:
 739              		.loc 1 852 3 is_stmt 1 discriminator 3 view .LVU249
 740              		.loc 1 852 3 discriminator 3 view .LVU250
 853:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 854:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   MIB2_STATS_INC(mib2.ipoutrequests);
 741              		.loc 1 854 37 view .LVU251
 855:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 856:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   /* Should the IP header be generated or is it already included in p? */
 857:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   if (dest != LWIP_IP_HDRINCL) {
 742              		.loc 1 857 3 view .LVU252
 743              		.loc 1 857 6 is_stmt 0 view .LVU253
 744 0018 002E     		cmp	r6, #0
 745 001a 51D0     		beq	.L67
 746              	.LBB2:
 858:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     u16_t ip_hlen = IP_HLEN;
 747              		.loc 1 858 5 is_stmt 1 view .LVU254
 748              	.LVL54:
 859:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #if IP_OPTIONS_SEND
 860:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     u16_t optlen_aligned = 0;
 861:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     if (optlen != 0) {
 862:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #if CHECKSUM_GEN_IP_INLINE
 863:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       int i;
 864:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #endif /* CHECKSUM_GEN_IP_INLINE */
 865:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       if (optlen > (IP_HLEN_MAX - IP_HLEN)) {
 866:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****         /* optlen too long */
 867:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****         LWIP_DEBUGF(IP_DEBUG | LWIP_DBG_LEVEL_SERIOUS, ("ip4_output_if_opt: optlen too long\n"));
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccEukS4o.s 			page 33


 868:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****         IP_STATS_INC(ip.err);
 869:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****         MIB2_STATS_INC(mib2.ipoutdiscards);
 870:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****         return ERR_VAL;
 871:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       }
 872:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       /* round up to a multiple of 4 */
 873:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       optlen_aligned = (u16_t)((optlen + 3) & ~3);
 874:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       ip_hlen = (u16_t)(ip_hlen + optlen_aligned);
 875:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       /* First write in the IP options */
 876:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       if (pbuf_add_header(p, optlen_aligned)) {
 877:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****         LWIP_DEBUGF(IP_DEBUG | LWIP_DBG_LEVEL_SERIOUS, ("ip4_output_if_opt: not enough room for IP 
 878:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****         IP_STATS_INC(ip.err);
 879:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****         MIB2_STATS_INC(mib2.ipoutdiscards);
 880:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****         return ERR_BUF;
 881:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       }
 882:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       MEMCPY(p->payload, ip_options, optlen);
 883:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       if (optlen < optlen_aligned) {
 884:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****         /* zero the remaining bytes */
 885:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****         memset(((char *)p->payload) + optlen, 0, (size_t)(optlen_aligned - optlen));
 886:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       }
 887:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #if CHECKSUM_GEN_IP_INLINE
 888:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       for (i = 0; i < optlen_aligned / 2; i++) {
 889:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****         chk_sum += ((u16_t *)p->payload)[i];
 890:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       }
 891:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #endif /* CHECKSUM_GEN_IP_INLINE */
 892:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     }
 893:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #endif /* IP_OPTIONS_SEND */
 894:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     /* generate IP header */
 895:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     if (pbuf_add_header(p, IP_HLEN)) {
 749              		.loc 1 895 5 view .LVU255
 750              		.loc 1 895 9 is_stmt 0 view .LVU256
 751 001c 1421     		movs	r1, #20
 752 001e 2046     		mov	r0, r4
 753 0020 FFF7FEFF 		bl	pbuf_add_header
 754              	.LVL55:
 755              		.loc 1 895 8 discriminator 1 view .LVU257
 756 0024 0028     		cmp	r0, #0
 757 0026 59D1     		bne	.L74
 896:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       LWIP_DEBUGF(IP_DEBUG | LWIP_DBG_LEVEL_SERIOUS, ("ip4_output: not enough room for IP header in
 897:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 898:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       IP_STATS_INC(ip.err);
 899:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       MIB2_STATS_INC(mib2.ipoutdiscards);
 900:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       return ERR_BUF;
 901:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     }
 902:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 903:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     iphdr = (struct ip_hdr *)p->payload;
 758              		.loc 1 903 5 is_stmt 1 view .LVU258
 759              		.loc 1 903 11 is_stmt 0 view .LVU259
 760 0028 6568     		ldr	r5, [r4, #4]
 761              	.LVL56:
 904:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     LWIP_ASSERT("check that first pbuf can hold struct ip_hdr",
 762              		.loc 1 904 5 is_stmt 1 view .LVU260
 763              		.loc 1 904 5 view .LVU261
 764 002a 6389     		ldrh	r3, [r4, #10]
 765 002c 132B     		cmp	r3, #19
 766 002e 3BD9     		bls	.L78
 767              	.L69:
 768              		.loc 1 904 5 discriminator 3 view .LVU262
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccEukS4o.s 			page 34


 769              		.loc 1 904 5 discriminator 3 view .LVU263
 905:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****                 (p->len >= sizeof(struct ip_hdr)));
 906:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 907:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     IPH_TTL_SET(iphdr, ttl);
 770              		.loc 1 907 5 view .LVU264
 771 0030 85F80890 		strb	r9, [r5, #8]
 908:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     IPH_PROTO_SET(iphdr, proto);
 772              		.loc 1 908 5 view .LVU265
 773 0034 9DF82C30 		ldrb	r3, [sp, #44]	@ zero_extendqisi2
 774 0038 6B72     		strb	r3, [r5, #9]
 909:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #if CHECKSUM_GEN_IP_INLINE
 910:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     chk_sum += PP_NTOHS(proto | (ttl << 8));
 911:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #endif /* CHECKSUM_GEN_IP_INLINE */
 912:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 913:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     /* dest cannot be NULL here */
 914:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     ip4_addr_copy(iphdr->dest, *dest);
 775              		.loc 1 914 5 view .LVU266
 776 003a 3368     		ldr	r3, [r6]
 777 003c 2B61     		str	r3, [r5, #16]	@ unaligned
 915:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #if CHECKSUM_GEN_IP_INLINE
 916:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     chk_sum += ip4_addr_get_u32(&iphdr->dest) & 0xFFFF;
 917:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     chk_sum += ip4_addr_get_u32(&iphdr->dest) >> 16;
 918:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #endif /* CHECKSUM_GEN_IP_INLINE */
 919:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 920:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     IPH_VHL_SET(iphdr, 4, ip_hlen / 4);
 778              		.loc 1 920 5 view .LVU267
 779 003e 4523     		movs	r3, #69
 780 0040 2B70     		strb	r3, [r5]
 921:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     IPH_TOS_SET(iphdr, tos);
 781              		.loc 1 921 5 view .LVU268
 782 0042 9DF82830 		ldrb	r3, [sp, #40]	@ zero_extendqisi2
 783 0046 6B70     		strb	r3, [r5, #1]
 922:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #if CHECKSUM_GEN_IP_INLINE
 923:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     chk_sum += PP_NTOHS(tos | (iphdr->_v_hl << 8));
 924:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #endif /* CHECKSUM_GEN_IP_INLINE */
 925:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     IPH_LEN_SET(iphdr, lwip_htons(p->tot_len));
 784              		.loc 1 925 5 view .LVU269
 785 0048 2089     		ldrh	r0, [r4, #8]
 786 004a FFF7FEFF 		bl	lwip_htons
 787              	.LVL57:
 788              		.loc 1 925 5 is_stmt 0 discriminator 1 view .LVU270
 789 004e 6880     		strh	r0, [r5, #2]	@ unaligned
 926:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #if CHECKSUM_GEN_IP_INLINE
 927:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     chk_sum += iphdr->_len;
 928:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #endif /* CHECKSUM_GEN_IP_INLINE */
 929:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     IPH_OFFSET_SET(iphdr, 0);
 790              		.loc 1 929 5 is_stmt 1 view .LVU271
 791 0050 0023     		movs	r3, #0
 792 0052 AB71     		strb	r3, [r5, #6]
 793 0054 EB71     		strb	r3, [r5, #7]
 930:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     IPH_ID_SET(iphdr, lwip_htons(ip_id));
 794              		.loc 1 930 5 view .LVU272
 795 0056 DFF8A490 		ldr	r9, .L81+20
 796              	.LVL58:
 797              		.loc 1 930 5 is_stmt 0 view .LVU273
 798 005a B9F80000 		ldrh	r0, [r9]
 799 005e FFF7FEFF 		bl	lwip_htons
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccEukS4o.s 			page 35


 800              	.LVL59:
 801              		.loc 1 930 5 discriminator 1 view .LVU274
 802 0062 A880     		strh	r0, [r5, #4]	@ unaligned
 931:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #if CHECKSUM_GEN_IP_INLINE
 932:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     chk_sum += iphdr->_id;
 933:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #endif /* CHECKSUM_GEN_IP_INLINE */
 934:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     ++ip_id;
 803              		.loc 1 934 5 is_stmt 1 view .LVU275
 804 0064 B9F80030 		ldrh	r3, [r9]
 805 0068 0133     		adds	r3, r3, #1
 806 006a A9F80030 		strh	r3, [r9]	@ movhi
 935:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 936:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     if (src == NULL) {
 807              		.loc 1 936 5 view .LVU276
 808              		.loc 1 936 8 is_stmt 0 view .LVU277
 809 006e 1FB3     		cbz	r7, .L79
 937:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       ip4_addr_copy(iphdr->src, *IP4_ADDR_ANY4);
 938:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     } else {
 939:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       /* src cannot be NULL here */
 940:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       ip4_addr_copy(iphdr->src, *src);
 810              		.loc 1 940 7 is_stmt 1 view .LVU278
 811 0070 3B68     		ldr	r3, [r7]
 812 0072 EB60     		str	r3, [r5, #12]	@ unaligned
 813              	.L71:
 941:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     }
 942:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 943:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #if CHECKSUM_GEN_IP_INLINE
 944:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     chk_sum += ip4_addr_get_u32(&iphdr->src) & 0xFFFF;
 945:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     chk_sum += ip4_addr_get_u32(&iphdr->src) >> 16;
 946:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     chk_sum = (chk_sum >> 16) + (chk_sum & 0xFFFF);
 947:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     chk_sum = (chk_sum >> 16) + chk_sum;
 948:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     chk_sum = ~chk_sum;
 949:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     IF__NETIF_CHECKSUM_ENABLED(netif, NETIF_CHECKSUM_GEN_IP) {
 950:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       iphdr->_chksum = (u16_t)chk_sum; /* network order */
 951:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     }
 952:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #if LWIP_CHECKSUM_CTRL_PER_NETIF
 953:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     else {
 954:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       IPH_CHKSUM_SET(iphdr, 0);
 955:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     }
 956:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #endif /* LWIP_CHECKSUM_CTRL_PER_NETIF*/
 957:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #else /* CHECKSUM_GEN_IP_INLINE */
 958:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     IPH_CHKSUM_SET(iphdr, 0);
 814              		.loc 1 958 5 view .LVU279
 815 0074 0023     		movs	r3, #0
 816 0076 AB72     		strb	r3, [r5, #10]
 817 0078 EB72     		strb	r3, [r5, #11]
 818              	.LVL60:
 819              	.L72:
 820              		.loc 1 958 5 is_stmt 0 view .LVU280
 821              	.LBE2:
 959:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #if CHECKSUM_GEN_IP
 960:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     IF__NETIF_CHECKSUM_ENABLED(netif, NETIF_CHECKSUM_GEN_IP) {
 961:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       IPH_CHKSUM_SET(iphdr, inet_chksum(iphdr, ip_hlen));
 962:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     }
 963:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #endif /* CHECKSUM_GEN_IP */
 964:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #endif /* CHECKSUM_GEN_IP_INLINE */
 965:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   } else {
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccEukS4o.s 			page 36


 966:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     /* IP header already included in p */
 967:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     if (p->len < IP_HLEN) {
 968:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       LWIP_DEBUGF(IP_DEBUG | LWIP_DBG_LEVEL_SERIOUS, ("ip4_output: LWIP_IP_HDRINCL but pbuf is too 
 969:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       IP_STATS_INC(ip.err);
 970:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       MIB2_STATS_INC(mib2.ipoutdiscards);
 971:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       return ERR_BUF;
 972:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     }
 973:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     iphdr = (struct ip_hdr *)p->payload;
 974:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     ip4_addr_copy(dest_addr, iphdr->dest);
 975:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     dest = &dest_addr;
 976:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   }
 977:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 978:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   IP_STATS_INC(ip.xmit);
 822              		.loc 1 978 24 is_stmt 1 view .LVU281
 979:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 980:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   LWIP_DEBUGF(IP_DEBUG, ("ip4_output_if: %c%c%"U16_F"\n", netif->name[0], netif->name[1], (u16_t)ne
 823              		.loc 1 980 110 view .LVU282
 981:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   ip4_debug_print(p);
 824              		.loc 1 981 21 view .LVU283
 982:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 983:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #if ENABLE_LOOPBACK
 984:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   if (ip4_addr_cmp(dest, netif_ip4_addr(netif))
 985:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #if !LWIP_HAVE_LOOPIF
 986:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       || ip4_addr_isloopback(dest)
 987:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #endif /* !LWIP_HAVE_LOOPIF */
 988:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****      ) {
 989:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     /* Packet to self, enqueue it for loopback */
 990:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     LWIP_DEBUGF(IP_DEBUG, ("netif_loop_output()"));
 991:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     return netif_loop_output(netif, p);
 992:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   }
 993:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #if LWIP_MULTICAST_TX_OPTIONS
 994:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   if ((p->flags & PBUF_FLAG_MCASTLOOP) != 0) {
 995:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     netif_loop_output(netif, p);
 996:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   }
 997:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #endif /* LWIP_MULTICAST_TX_OPTIONS */
 998:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #endif /* ENABLE_LOOPBACK */
 999:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #if IP_FRAG
1000:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   /* don't fragment if interface has mtu set to 0 [loopif] */
1001:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   if (netif->mtu && (p->tot_len > netif->mtu)) {
 825              		.loc 1 1001 3 view .LVU284
 826              		.loc 1 1001 12 is_stmt 0 view .LVU285
 827 007a B8F82430 		ldrh	r3, [r8, #36]
 828              		.loc 1 1001 6 view .LVU286
 829 007e 13B1     		cbz	r3, .L73
 830              		.loc 1 1001 23 discriminator 1 view .LVU287
 831 0080 2289     		ldrh	r2, [r4, #8]
 832              		.loc 1 1001 18 discriminator 1 view .LVU288
 833 0082 9342     		cmp	r3, r2
 834 0084 24D3     		bcc	.L80
 835              	.L73:
1002:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     return ip4_frag(p, netif, dest);
1003:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   }
1004:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #endif /* IP_FRAG */
1005:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
1006:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   LWIP_DEBUGF(IP_DEBUG, ("ip4_output_if: call netif->output()\n"));
 836              		.loc 1 1006 67 is_stmt 1 view .LVU289
1007:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   return netif->output(netif, p, dest);
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccEukS4o.s 			page 37


 837              		.loc 1 1007 3 view .LVU290
 838              		.loc 1 1007 15 is_stmt 0 view .LVU291
 839 0086 D8F81430 		ldr	r3, [r8, #20]
 840              		.loc 1 1007 10 view .LVU292
 841 008a 3246     		mov	r2, r6
 842 008c 2146     		mov	r1, r4
 843 008e 4046     		mov	r0, r8
 844 0090 9847     		blx	r3
 845              	.LVL61:
 846              	.L68:
1008:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** }
 847              		.loc 1 1008 1 view .LVU293
 848 0092 03B0     		add	sp, sp, #12
 849              	.LCFI8:
 850              		.cfi_remember_state
 851              		.cfi_def_cfa_offset 28
 852              		@ sp needed
 853 0094 BDE8F083 		pop	{r4, r5, r6, r7, r8, r9, pc}
 854              	.LVL62:
 855              	.L77:
 856              	.LCFI9:
 857              		.cfi_restore_state
 852:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 858              		.loc 1 852 3 is_stmt 1 discriminator 1 view .LVU294
 852:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 859              		.loc 1 852 3 discriminator 1 view .LVU295
 860 0098 134B     		ldr	r3, .L81
 861              	.LVL63:
 852:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 862              		.loc 1 852 3 is_stmt 0 discriminator 1 view .LVU296
 863 009a 4FF45572 		mov	r2, #852
 864 009e 1349     		ldr	r1, .L81+4
 865              	.LVL64:
 852:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 866              		.loc 1 852 3 discriminator 1 view .LVU297
 867 00a0 1348     		ldr	r0, .L81+8
 868              	.LVL65:
 852:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 869              		.loc 1 852 3 discriminator 1 view .LVU298
 870 00a2 FFF7FEFF 		bl	printf
 871              	.LVL66:
 872 00a6 B7E7     		b	.L66
 873              	.LVL67:
 874              	.L78:
 875              	.LBB3:
 904:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****                 (p->len >= sizeof(struct ip_hdr)));
 876              		.loc 1 904 5 is_stmt 1 discriminator 1 view .LVU299
 904:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****                 (p->len >= sizeof(struct ip_hdr)));
 877              		.loc 1 904 5 discriminator 1 view .LVU300
 878 00a8 0F4B     		ldr	r3, .L81
 879 00aa 4FF46272 		mov	r2, #904
 880 00ae 1149     		ldr	r1, .L81+12
 881 00b0 0F48     		ldr	r0, .L81+8
 882 00b2 FFF7FEFF 		bl	printf
 883              	.LVL68:
 884 00b6 BBE7     		b	.L69
 885              	.LVL69:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccEukS4o.s 			page 38


 886              	.L79:
 937:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     } else {
 887              		.loc 1 937 7 view .LVU301
 888 00b8 0F4B     		ldr	r3, .L81+16
 889 00ba 1B68     		ldr	r3, [r3]
 890 00bc EB60     		str	r3, [r5, #12]	@ unaligned
 891 00be D9E7     		b	.L71
 892              	.LVL70:
 893              	.L67:
 937:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     } else {
 894              		.loc 1 937 7 is_stmt 0 view .LVU302
 895              	.LBE3:
 967:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       LWIP_DEBUGF(IP_DEBUG | LWIP_DBG_LEVEL_SERIOUS, ("ip4_output: LWIP_IP_HDRINCL but pbuf is too 
 896              		.loc 1 967 5 is_stmt 1 view .LVU303
 967:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       LWIP_DEBUGF(IP_DEBUG | LWIP_DBG_LEVEL_SERIOUS, ("ip4_output: LWIP_IP_HDRINCL but pbuf is too 
 897              		.loc 1 967 10 is_stmt 0 view .LVU304
 898 00c0 6389     		ldrh	r3, [r4, #10]
 967:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       LWIP_DEBUGF(IP_DEBUG | LWIP_DBG_LEVEL_SERIOUS, ("ip4_output: LWIP_IP_HDRINCL but pbuf is too 
 899              		.loc 1 967 8 view .LVU305
 900 00c2 132B     		cmp	r3, #19
 901 00c4 0DD9     		bls	.L75
 973:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     ip4_addr_copy(dest_addr, iphdr->dest);
 902              		.loc 1 973 5 is_stmt 1 view .LVU306
 973:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     ip4_addr_copy(dest_addr, iphdr->dest);
 903              		.loc 1 973 11 is_stmt 0 view .LVU307
 904 00c6 6368     		ldr	r3, [r4, #4]
 905              	.LVL71:
 974:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     dest = &dest_addr;
 906              		.loc 1 974 5 is_stmt 1 view .LVU308
 907 00c8 1B69     		ldr	r3, [r3, #16]	@ unaligned
 908              	.LVL72:
 974:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     dest = &dest_addr;
 909              		.loc 1 974 5 is_stmt 0 view .LVU309
 910 00ca 0193     		str	r3, [sp, #4]
 975:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   }
 911              		.loc 1 975 5 is_stmt 1 view .LVU310
 912              	.LVL73:
 975:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   }
 913              		.loc 1 975 10 is_stmt 0 view .LVU311
 914 00cc 01AE     		add	r6, sp, #4
 915              	.LVL74:
 975:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   }
 916              		.loc 1 975 10 view .LVU312
 917 00ce D4E7     		b	.L72
 918              	.LVL75:
 919              	.L80:
1002:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   }
 920              		.loc 1 1002 5 is_stmt 1 view .LVU313
1002:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   }
 921              		.loc 1 1002 12 is_stmt 0 view .LVU314
 922 00d0 3246     		mov	r2, r6
 923 00d2 4146     		mov	r1, r8
 924 00d4 2046     		mov	r0, r4
 925 00d6 FFF7FEFF 		bl	ip4_frag
 926              	.LVL76:
 927 00da DAE7     		b	.L68
 928              	.LVL77:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccEukS4o.s 			page 39


 929              	.L74:
 930              	.LBB4:
 900:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     }
 931              		.loc 1 900 14 view .LVU315
 932 00dc 6FF00100 		mvn	r0, #1
 933 00e0 D7E7     		b	.L68
 934              	.LVL78:
 935              	.L75:
 900:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     }
 936              		.loc 1 900 14 view .LVU316
 937              	.LBE4:
 971:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     }
 938              		.loc 1 971 14 view .LVU317
 939 00e2 6FF00100 		mvn	r0, #1
 940 00e6 D4E7     		b	.L68
 941              	.L82:
 942              		.align	2
 943              	.L81:
 944 00e8 00000000 		.word	.LC0
 945 00ec 34000000 		.word	.LC1
 946 00f0 40000000 		.word	.LC2
 947 00f4 68000000 		.word	.LC3
 948 00f8 00000000 		.word	ip_addr_any
 949 00fc 00000000 		.word	ip_id
 950              		.cfi_endproc
 951              	.LFE174:
 953              		.section	.text.ip4_output_if,"ax",%progbits
 954              		.align	1
 955              		.global	ip4_output_if
 956              		.syntax unified
 957              		.thumb
 958              		.thumb_func
 960              	ip4_output_if:
 961              	.LVL79:
 962              	.LFB173:
 790:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #if IP_OPTIONS_SEND
 963              		.loc 1 790 1 is_stmt 1 view -0
 964              		.cfi_startproc
 965              		@ args = 12, pretend = 0, frame = 0
 966              		@ frame_needed = 0, uses_anonymous_args = 0
 790:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #if IP_OPTIONS_SEND
 967              		.loc 1 790 1 is_stmt 0 view .LVU319
 968 0000 10B5     		push	{r4, lr}
 969              	.LCFI10:
 970              		.cfi_def_cfa_offset 8
 971              		.cfi_offset 4, -8
 972              		.cfi_offset 14, -4
 973 0002 84B0     		sub	sp, sp, #16
 974              	.LCFI11:
 975              		.cfi_def_cfa_offset 24
 976 0004 089C     		ldr	r4, [sp, #32]
 807:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   if (dest != LWIP_IP_HDRINCL) {
 977              		.loc 1 807 3 is_stmt 1 view .LVU320
 978              	.LVL80:
 808:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     if (ip4_addr_isany(src)) {
 979              		.loc 1 808 3 view .LVU321
 808:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     if (ip4_addr_isany(src)) {
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccEukS4o.s 			page 40


 980              		.loc 1 808 6 is_stmt 0 view .LVU322
 981 0006 9446     		mov	ip, r2
 982 0008 1AB1     		cbz	r2, .L84
 809:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       src_used = netif_ip4_addr(netif);
 983              		.loc 1 809 5 is_stmt 1 view .LVU323
 809:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       src_used = netif_ip4_addr(netif);
 984              		.loc 1 809 8 is_stmt 0 view .LVU324
 985 000a 09B1     		cbz	r1, .L85
 809:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       src_used = netif_ip4_addr(netif);
 986              		.loc 1 809 9 discriminator 1 view .LVU325
 987 000c 0A68     		ldr	r2, [r1]
 988              	.LVL81:
 809:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****       src_used = netif_ip4_addr(netif);
 989              		.loc 1 809 9 discriminator 1 view .LVU326
 990 000e 02B9     		cbnz	r2, .L84
 991              	.L85:
 810:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     }
 992              		.loc 1 810 7 is_stmt 1 view .LVU327
 810:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     }
 993              		.loc 1 810 16 is_stmt 0 view .LVU328
 994 0010 211D     		adds	r1, r4, #4
 995              	.LVL82:
 996              	.L84:
 818:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #endif /* IP_OPTIONS_SEND */
 997              		.loc 1 818 3 is_stmt 1 view .LVU329
 818:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** #endif /* IP_OPTIONS_SEND */
 998              		.loc 1 818 10 is_stmt 0 view .LVU330
 999 0012 0294     		str	r4, [sp, #8]
 1000 0014 9DF81C20 		ldrb	r2, [sp, #28]	@ zero_extendqisi2
 1001 0018 0192     		str	r2, [sp, #4]
 1002 001a 9DF81820 		ldrb	r2, [sp, #24]	@ zero_extendqisi2
 1003 001e 0092     		str	r2, [sp]
 1004 0020 6246     		mov	r2, ip
 1005 0022 FFF7FEFF 		bl	ip4_output_if_src
 1006              	.LVL83:
 820:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 1007              		.loc 1 820 1 view .LVU331
 1008 0026 04B0     		add	sp, sp, #16
 1009              	.LCFI12:
 1010              		.cfi_def_cfa_offset 8
 1011              		@ sp needed
 1012 0028 10BD     		pop	{r4, pc}
 820:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 1013              		.loc 1 820 1 view .LVU332
 1014              		.cfi_endproc
 1015              	.LFE173:
 1017              		.section	.text.ip4_output,"ax",%progbits
 1018              		.align	1
 1019              		.global	ip4_output
 1020              		.syntax unified
 1021              		.thumb
 1022              		.thumb_func
 1024              	ip4_output:
 1025              	.LVL84:
 1026              	.LFB175:
1009:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
1010:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** /**
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccEukS4o.s 			page 41


1011:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * Simple interface to ip_output_if. It finds the outgoing network
1012:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * interface and calls upon ip_output_if to do the actual work.
1013:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  *
1014:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * @param p the packet to send (p->payload points to the data, e.g. next
1015:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****             protocol header; if dest == LWIP_IP_HDRINCL, p already includes an
1016:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****             IP header and p->payload points to that IP header)
1017:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * @param src the source IP address to send from (if src == IP4_ADDR_ANY, the
1018:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  *         IP  address of the netif used to send is used as source address)
1019:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * @param dest the destination IP address to send the packet to
1020:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * @param ttl the TTL value to be set in the IP header
1021:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * @param tos the TOS value to be set in the IP header
1022:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * @param proto the PROTOCOL to be set in the IP header
1023:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  *
1024:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  * @return ERR_RTE if no route is found
1025:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  *         see ip_output_if() for more return values
1026:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****  */
1027:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** err_t
1028:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** ip4_output(struct pbuf *p, const ip4_addr_t *src, const ip4_addr_t *dest,
1029:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****            u8_t ttl, u8_t tos, u8_t proto)
1030:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** {
 1027              		.loc 1 1030 1 is_stmt 1 view -0
 1028              		.cfi_startproc
 1029              		@ args = 8, pretend = 0, frame = 0
 1030              		@ frame_needed = 0, uses_anonymous_args = 0
 1031              		.loc 1 1030 1 is_stmt 0 view .LVU334
 1032 0000 F0B5     		push	{r4, r5, r6, r7, lr}
 1033              	.LCFI13:
 1034              		.cfi_def_cfa_offset 20
 1035              		.cfi_offset 4, -20
 1036              		.cfi_offset 5, -16
 1037              		.cfi_offset 6, -12
 1038              		.cfi_offset 7, -8
 1039              		.cfi_offset 14, -4
 1040 0002 85B0     		sub	sp, sp, #20
 1041              	.LCFI14:
 1042              		.cfi_def_cfa_offset 40
 1043 0004 0446     		mov	r4, r0
 1044 0006 0F46     		mov	r7, r1
 1045 0008 1546     		mov	r5, r2
 1046 000a 1E46     		mov	r6, r3
1031:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   struct netif *netif;
 1047              		.loc 1 1031 3 is_stmt 1 view .LVU335
1032:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
1033:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   LWIP_IP_CHECK_PBUF_REF_COUNT_FOR_TX(p);
 1048              		.loc 1 1033 3 view .LVU336
 1049              		.loc 1 1033 3 view .LVU337
 1050 000c 827B     		ldrb	r2, [r0, #14]	@ zero_extendqisi2
 1051              	.LVL85:
 1052              		.loc 1 1033 3 is_stmt 0 view .LVU338
 1053 000e 012A     		cmp	r2, #1
 1054 0010 12D1     		bne	.L92
 1055              	.LVL86:
 1056              	.L88:
 1057              		.loc 1 1033 3 is_stmt 1 discriminator 3 view .LVU339
 1058              		.loc 1 1033 3 discriminator 3 view .LVU340
1034:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
1035:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   if ((netif = ip4_route_src(src, dest)) == NULL) {
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccEukS4o.s 			page 42


 1059              		.loc 1 1035 3 view .LVU341
 1060              		.loc 1 1035 16 is_stmt 0 view .LVU342
 1061 0012 2846     		mov	r0, r5
 1062 0014 FFF7FEFF 		bl	ip4_route
 1063              	.LVL87:
 1064              		.loc 1 1035 6 discriminator 1 view .LVU343
 1065 0018 B0B1     		cbz	r0, .L90
1036:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     LWIP_DEBUGF(IP_DEBUG, ("ip4_output: No route to %"U16_F".%"U16_F".%"U16_F".%"U16_F"\n",
1037:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****                            ip4_addr1_16(dest), ip4_addr2_16(dest), ip4_addr3_16(dest), ip4_addr4_16
1038:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     IP_STATS_INC(ip.rterr);
1039:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****     return ERR_RTE;
1040:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   }
1041:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
1042:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   return ip4_output_if(p, src, dest, ttl, tos, proto, netif);
 1066              		.loc 1 1042 3 is_stmt 1 view .LVU344
 1067              		.loc 1 1042 10 is_stmt 0 view .LVU345
 1068 001a 0290     		str	r0, [sp, #8]
 1069 001c 9DF82C30 		ldrb	r3, [sp, #44]	@ zero_extendqisi2
 1070 0020 0193     		str	r3, [sp, #4]
 1071 0022 9DF82830 		ldrb	r3, [sp, #40]	@ zero_extendqisi2
 1072 0026 0093     		str	r3, [sp]
 1073 0028 3346     		mov	r3, r6
 1074 002a 2A46     		mov	r2, r5
 1075 002c 3946     		mov	r1, r7
 1076 002e 2046     		mov	r0, r4
 1077              	.LVL88:
 1078              		.loc 1 1042 10 view .LVU346
 1079 0030 FFF7FEFF 		bl	ip4_output_if
 1080              	.LVL89:
 1081              	.L89:
1043:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** }
 1082              		.loc 1 1043 1 view .LVU347
 1083 0034 05B0     		add	sp, sp, #20
 1084              	.LCFI15:
 1085              		.cfi_remember_state
 1086              		.cfi_def_cfa_offset 20
 1087              		@ sp needed
 1088 0036 F0BD     		pop	{r4, r5, r6, r7, pc}
 1089              	.LVL90:
 1090              	.L92:
 1091              	.LCFI16:
 1092              		.cfi_restore_state
1033:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 1093              		.loc 1 1033 3 is_stmt 1 discriminator 1 view .LVU348
1033:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 1094              		.loc 1 1033 3 discriminator 1 view .LVU349
 1095 0038 054B     		ldr	r3, .L93
 1096              	.LVL91:
1033:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 1097              		.loc 1 1033 3 is_stmt 0 discriminator 1 view .LVU350
 1098 003a 40F20942 		movw	r2, #1033
 1099 003e 0549     		ldr	r1, .L93+4
 1100              	.LVL92:
1033:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 1101              		.loc 1 1033 3 discriminator 1 view .LVU351
 1102 0040 0548     		ldr	r0, .L93+8
 1103              	.LVL93:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccEukS4o.s 			page 43


1033:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c **** 
 1104              		.loc 1 1033 3 discriminator 1 view .LVU352
 1105 0042 FFF7FEFF 		bl	printf
 1106              	.LVL94:
 1107 0046 E4E7     		b	.L88
 1108              	.LVL95:
 1109              	.L90:
1039:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   }
 1110              		.loc 1 1039 12 view .LVU353
 1111 0048 6FF00300 		mvn	r0, #3
 1112              	.LVL96:
1039:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4.c ****   }
 1113              		.loc 1 1039 12 view .LVU354
 1114 004c F2E7     		b	.L89
 1115              	.L94:
 1116 004e 00BF     		.align	2
 1117              	.L93:
 1118 0050 00000000 		.word	.LC0
 1119 0054 34000000 		.word	.LC1
 1120 0058 40000000 		.word	.LC2
 1121              		.cfi_endproc
 1122              	.LFE175:
 1124              		.section	.bss.ip_id,"aw",%nobits
 1125              		.align	1
 1128              	ip_id:
 1129 0000 0000     		.space	2
 1130              		.text
 1131              	.Letext0:
 1132              		.file 2 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\../lib/gcc/arm-none-eabi/13.3.1/..
 1133              		.file 3 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\../lib/gcc/arm-none-eabi/13.3.1/..
 1134              		.file 4 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\../lib/gcc/arm-none-eabi/13.3.1/in
 1135              		.file 5 "Middlewares/Third_Party/LwIP/src/include/lwip/arch.h"
 1136              		.file 6 "Middlewares/Third_Party/LwIP/src/include/lwip/err.h"
 1137              		.file 7 "Middlewares/Third_Party/LwIP/src/include/lwip/pbuf.h"
 1138              		.file 8 "Middlewares/Third_Party/LwIP/src/include/lwip/ip4_addr.h"
 1139              		.file 9 "Middlewares/Third_Party/LwIP/src/include/lwip/ip_addr.h"
 1140              		.file 10 "Middlewares/Third_Party/LwIP/src/include/lwip/memp.h"
 1141              		.file 11 "Middlewares/Third_Party/LwIP/src/include/lwip/netif.h"
 1142              		.file 12 "Middlewares/Third_Party/LwIP/src/include/lwip/prot/ip4.h"
 1143              		.file 13 "Middlewares/Third_Party/LwIP/src/include/lwip/ip.h"
 1144              		.file 14 "Middlewares/Third_Party/LwIP/src/include/lwip/ip4_frag.h"
 1145              		.file 15 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\../lib/gcc/arm-none-eabi/13.3.1/.
 1146              		.file 16 "Middlewares/Third_Party/LwIP/src/include/lwip/icmp.h"
 1147              		.file 17 "Middlewares/Third_Party/LwIP/src/include/lwip/priv/tcp_priv.h"
 1148              		.file 18 "Middlewares/Third_Party/LwIP/src/include/lwip/udp.h"
 1149              		.file 19 "Middlewares/Third_Party/LwIP/src/include/lwip/def.h"
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccEukS4o.s 			page 44


DEFINED SYMBOLS
                            *ABS*:00000000 ip4.c
C:\Users\<USER>\AppData\Local\Temp\ccEukS4o.s:20     .text.ip4_input_accept:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccEukS4o.s:25     .text.ip4_input_accept:00000000 ip4_input_accept
C:\Users\<USER>\AppData\Local\Temp\ccEukS4o.s:90     .text.ip4_input_accept:00000030 $d
C:\Users\<USER>\AppData\Local\Temp\ccEukS4o.s:95     .text.ip4_route:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccEukS4o.s:101    .text.ip4_route:00000000 ip4_route
C:\Users\<USER>\AppData\Local\Temp\ccEukS4o.s:259    .text.ip4_route:000000a4 $d
C:\Users\<USER>\AppData\Local\Temp\ccEukS4o.s:265    .text.ip4_input:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccEukS4o.s:271    .text.ip4_input:00000000 ip4_input
C:\Users\<USER>\AppData\Local\Temp\ccEukS4o.s:676    .text.ip4_input:00000194 $d
C:\Users\<USER>\AppData\Local\Temp\ccEukS4o.s:682    .rodata.ip4_output_if_src.str1.4:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\ccEukS4o.s:695    .text.ip4_output_if_src:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccEukS4o.s:701    .text.ip4_output_if_src:00000000 ip4_output_if_src
C:\Users\<USER>\AppData\Local\Temp\ccEukS4o.s:944    .text.ip4_output_if_src:000000e8 $d
C:\Users\<USER>\AppData\Local\Temp\ccEukS4o.s:1128   .bss.ip_id:00000000 ip_id
C:\Users\<USER>\AppData\Local\Temp\ccEukS4o.s:954    .text.ip4_output_if:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccEukS4o.s:960    .text.ip4_output_if:00000000 ip4_output_if
C:\Users\<USER>\AppData\Local\Temp\ccEukS4o.s:1018   .text.ip4_output:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccEukS4o.s:1024   .text.ip4_output:00000000 ip4_output
C:\Users\<USER>\AppData\Local\Temp\ccEukS4o.s:1118   .text.ip4_output:00000050 $d
C:\Users\<USER>\AppData\Local\Temp\ccEukS4o.s:1125   .bss.ip_id:00000000 $d

UNDEFINED SYMBOLS
ip4_addr_isbroadcast_u32
ip_data
netif_list
netif_default
pbuf_free
lwip_htons
pbuf_realloc
ip4_reass
pbuf_remove_header
udp_input
tcp_input
icmp_input
pbuf_header_force
icmp_dest_unreach
pbuf_add_header
printf
ip4_frag
ip_addr_any
