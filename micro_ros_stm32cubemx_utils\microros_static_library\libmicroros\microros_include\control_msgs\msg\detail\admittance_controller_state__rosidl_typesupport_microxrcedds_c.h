// generated from rosidl_typesupport_microxrcedds_c/resource/idl__rosidl_typesupport_c.h.em
// with input from control_msgs:msg/AdmittanceControllerState.idl
// generated code does not contain a copyright notice
#ifndef CONTROL_MSGS__MSG__ADMITTANCE_CONTROLLER_STATE__ROSIDL_TYPESUPPORT_MICROXRCEDDS_C_H_
#define CONTROL_MSGS__MSG__ADMITTANCE_CONTROLLER_STATE__ROSIDL_TYPESUPPORT_MICROXRCEDDS_C_H_


#include <stddef.h>
#include <stdbool.h>
#include <stdint.h>
#include "rosidl_runtime_c/message_type_support_struct.h"
#include "rosidl_typesupport_interface/macros.h"
#include "control_msgs/msg/rosidl_typesupport_microxrcedds_c__visibility_control.h"

#ifdef __cplusplus
extern "C"
{
#endif

ROSIDL_TYPESUPPORT_MICROXRCEDDS_C_PUBLIC_control_msgs
size_t get_serialized_size_control_msgs__msg__AdmittanceControllerState(
  const void * untyped_ros_message,
  size_t current_alignment);

ROSIDL_TYPESUPPORT_MICROXRCEDDS_C_PUBLIC_control_msgs
size_t max_serialized_size_control_msgs__msg__AdmittanceControllerState(
  bool * full_bounded,
  size_t current_alignment);

ROSIDL_TYPESUPPORT_MICROXRCEDDS_C_PUBLIC_control_msgs
const rosidl_message_type_support_t *
  ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_microxrcedds_c, control_msgs, msg, AdmittanceControllerState)();

#ifdef __cplusplus
}
#endif


#endif  // CONTROL_MSGS__MSG__ADMITTANCE_CONTROLLER_STATE__ROSIDL_TYPESUPPORT_MICROXRCEDDS_C_H_
