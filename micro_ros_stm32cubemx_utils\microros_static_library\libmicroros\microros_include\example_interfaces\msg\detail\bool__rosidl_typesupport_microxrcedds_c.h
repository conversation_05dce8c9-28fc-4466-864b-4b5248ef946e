// generated from rosidl_typesupport_microxrcedds_c/resource/idl__rosidl_typesupport_c.h.em
// with input from example_interfaces:msg/Bool.idl
// generated code does not contain a copyright notice
#ifndef EXAMPLE_INTERFACES__MSG__BOOL__ROSIDL_TYPESUPPORT_MICROXRCEDDS_C_H_
#define EXAMPLE_INTERFACES__MSG__BOOL__ROSIDL_TYPESUPPORT_MICROXRCEDDS_C_H_


#include <stddef.h>
#include <stdbool.h>
#include <stdint.h>
#include "rosidl_runtime_c/message_type_support_struct.h"
#include "rosidl_typesupport_interface/macros.h"
#include "example_interfaces/msg/rosidl_typesupport_microxrcedds_c__visibility_control.h"

#ifdef __cplusplus
extern "C"
{
#endif

ROSIDL_TYPESUPPORT_MICROXRCEDDS_C_PUBLIC_example_interfaces
size_t get_serialized_size_example_interfaces__msg__Bool(
  const void * untyped_ros_message,
  size_t current_alignment);

R<PERSON>IDL_TYPESUPPORT_MICROXRCEDDS_C_PUBLIC_example_interfaces
size_t max_serialized_size_example_interfaces__msg__Bool(
  bool * full_bounded,
  size_t current_alignment);

ROSIDL_TYPESUPPORT_MICROXRCEDDS_C_PUBLIC_example_interfaces
const rosidl_message_type_support_t *
  ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_microxrcedds_c, example_interfaces, msg, Bool)();

#ifdef __cplusplus
}
#endif


#endif  // EXAMPLE_INTERFACES__MSG__BOOL__ROSIDL_TYPESUPPORT_MICROXRCEDDS_C_H_
