ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 1


   1              		.cpu cortex-m7
   2              		.arch armv7e-m
   3              		.fpu fpv5-d16
   4              		.eabi_attribute 28, 1
   5              		.eabi_attribute 20, 1
   6              		.eabi_attribute 21, 1
   7              		.eabi_attribute 23, 3
   8              		.eabi_attribute 24, 1
   9              		.eabi_attribute 25, 1
  10              		.eabi_attribute 26, 1
  11              		.eabi_attribute 30, 1
  12              		.eabi_attribute 34, 1
  13              		.eabi_attribute 18, 4
  14              		.file	"stm32h7xx_hal_cortex.c"
  15              		.text
  16              	.Ltext0:
  17              		.cfi_sections	.debug_frame
  18              		.file 1 "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c"
  19              		.section	.text.__NVIC_EnableIRQ,"ax",%progbits
  20              		.align	1
  21              		.syntax unified
  22              		.thumb
  23              		.thumb_func
  25              	__NVIC_EnableIRQ:
  26              	.LVL0:
  27              	.LFB106:
  28              		.file 2 "Drivers/CMSIS/Include/core_cm7.h"
   1:Drivers/CMSIS/Include/core_cm7.h **** /**************************************************************************//**
   2:Drivers/CMSIS/Include/core_cm7.h ****  * @file     core_cm7.h
   3:Drivers/CMSIS/Include/core_cm7.h ****  * @brief    CMSIS Cortex-M7 Core Peripheral Access Layer Header File
   4:Drivers/CMSIS/Include/core_cm7.h ****  * @version  V5.1.1
   5:Drivers/CMSIS/Include/core_cm7.h ****  * @date     28. March 2019
   6:Drivers/CMSIS/Include/core_cm7.h ****  ******************************************************************************/
   7:Drivers/CMSIS/Include/core_cm7.h **** /*
   8:Drivers/CMSIS/Include/core_cm7.h ****  * Copyright (c) 2009-2019 Arm Limited. All rights reserved.
   9:Drivers/CMSIS/Include/core_cm7.h ****  *
  10:Drivers/CMSIS/Include/core_cm7.h ****  * SPDX-License-Identifier: Apache-2.0
  11:Drivers/CMSIS/Include/core_cm7.h ****  *
  12:Drivers/CMSIS/Include/core_cm7.h ****  * Licensed under the Apache License, Version 2.0 (the License); you may
  13:Drivers/CMSIS/Include/core_cm7.h ****  * not use this file except in compliance with the License.
  14:Drivers/CMSIS/Include/core_cm7.h ****  * You may obtain a copy of the License at
  15:Drivers/CMSIS/Include/core_cm7.h ****  *
  16:Drivers/CMSIS/Include/core_cm7.h ****  * www.apache.org/licenses/LICENSE-2.0
  17:Drivers/CMSIS/Include/core_cm7.h ****  *
  18:Drivers/CMSIS/Include/core_cm7.h ****  * Unless required by applicable law or agreed to in writing, software
  19:Drivers/CMSIS/Include/core_cm7.h ****  * distributed under the License is distributed on an AS IS BASIS, WITHOUT
  20:Drivers/CMSIS/Include/core_cm7.h ****  * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  21:Drivers/CMSIS/Include/core_cm7.h ****  * See the License for the specific language governing permissions and
  22:Drivers/CMSIS/Include/core_cm7.h ****  * limitations under the License.
  23:Drivers/CMSIS/Include/core_cm7.h ****  */
  24:Drivers/CMSIS/Include/core_cm7.h **** 
  25:Drivers/CMSIS/Include/core_cm7.h **** #if   defined ( __ICCARM__ )
  26:Drivers/CMSIS/Include/core_cm7.h ****   #pragma system_include         /* treat file as system include file for MISRA check */
  27:Drivers/CMSIS/Include/core_cm7.h **** #elif defined (__clang__)
  28:Drivers/CMSIS/Include/core_cm7.h ****   #pragma clang system_header   /* treat file as system include file */
  29:Drivers/CMSIS/Include/core_cm7.h **** #endif
  30:Drivers/CMSIS/Include/core_cm7.h **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 2


  31:Drivers/CMSIS/Include/core_cm7.h **** #ifndef __CORE_CM7_H_GENERIC
  32:Drivers/CMSIS/Include/core_cm7.h **** #define __CORE_CM7_H_GENERIC
  33:Drivers/CMSIS/Include/core_cm7.h **** 
  34:Drivers/CMSIS/Include/core_cm7.h **** #include <stdint.h>
  35:Drivers/CMSIS/Include/core_cm7.h **** 
  36:Drivers/CMSIS/Include/core_cm7.h **** #ifdef __cplusplus
  37:Drivers/CMSIS/Include/core_cm7.h ****  extern "C" {
  38:Drivers/CMSIS/Include/core_cm7.h **** #endif
  39:Drivers/CMSIS/Include/core_cm7.h **** 
  40:Drivers/CMSIS/Include/core_cm7.h **** /**
  41:Drivers/CMSIS/Include/core_cm7.h ****   \page CMSIS_MISRA_Exceptions  MISRA-C:2004 Compliance Exceptions
  42:Drivers/CMSIS/Include/core_cm7.h ****   CMSIS violates the following MISRA-C:2004 rules:
  43:Drivers/CMSIS/Include/core_cm7.h **** 
  44:Drivers/CMSIS/Include/core_cm7.h ****    \li Required Rule 8.5, object/function definition in header file.<br>
  45:Drivers/CMSIS/Include/core_cm7.h ****      Function definitions in header files are used to allow 'inlining'.
  46:Drivers/CMSIS/Include/core_cm7.h **** 
  47:Drivers/CMSIS/Include/core_cm7.h ****    \li Required Rule 18.4, declaration of union type or object of union type: '{...}'.<br>
  48:Drivers/CMSIS/Include/core_cm7.h ****      Unions are used for effective representation of core registers.
  49:Drivers/CMSIS/Include/core_cm7.h **** 
  50:Drivers/CMSIS/Include/core_cm7.h ****    \li Advisory Rule 19.7, Function-like macro defined.<br>
  51:Drivers/CMSIS/Include/core_cm7.h ****      Function-like macros are used to allow more efficient code.
  52:Drivers/CMSIS/Include/core_cm7.h ****  */
  53:Drivers/CMSIS/Include/core_cm7.h **** 
  54:Drivers/CMSIS/Include/core_cm7.h **** 
  55:Drivers/CMSIS/Include/core_cm7.h **** /*******************************************************************************
  56:Drivers/CMSIS/Include/core_cm7.h ****  *                 CMSIS definitions
  57:Drivers/CMSIS/Include/core_cm7.h ****  ******************************************************************************/
  58:Drivers/CMSIS/Include/core_cm7.h **** /**
  59:Drivers/CMSIS/Include/core_cm7.h ****   \ingroup Cortex_M7
  60:Drivers/CMSIS/Include/core_cm7.h ****   @{
  61:Drivers/CMSIS/Include/core_cm7.h ****  */
  62:Drivers/CMSIS/Include/core_cm7.h **** 
  63:Drivers/CMSIS/Include/core_cm7.h **** #include "cmsis_version.h"
  64:Drivers/CMSIS/Include/core_cm7.h **** 
  65:Drivers/CMSIS/Include/core_cm7.h **** /* CMSIS CM7 definitions */
  66:Drivers/CMSIS/Include/core_cm7.h **** #define __CM7_CMSIS_VERSION_MAIN  (__CM_CMSIS_VERSION_MAIN)                  /*!< \deprecated [31:1
  67:Drivers/CMSIS/Include/core_cm7.h **** #define __CM7_CMSIS_VERSION_SUB   ( __CM_CMSIS_VERSION_SUB)                  /*!< \deprecated [15:0
  68:Drivers/CMSIS/Include/core_cm7.h **** #define __CM7_CMSIS_VERSION       ((__CM7_CMSIS_VERSION_MAIN << 16U) | \
  69:Drivers/CMSIS/Include/core_cm7.h ****                                     __CM7_CMSIS_VERSION_SUB           )      /*!< \deprecated CMSIS
  70:Drivers/CMSIS/Include/core_cm7.h **** 
  71:Drivers/CMSIS/Include/core_cm7.h **** #define __CORTEX_M                (7U)                                       /*!< Cortex-M Core */
  72:Drivers/CMSIS/Include/core_cm7.h **** 
  73:Drivers/CMSIS/Include/core_cm7.h **** /** __FPU_USED indicates whether an FPU is used or not.
  74:Drivers/CMSIS/Include/core_cm7.h ****     For this, __FPU_PRESENT has to be checked prior to making use of FPU specific registers and fun
  75:Drivers/CMSIS/Include/core_cm7.h **** */
  76:Drivers/CMSIS/Include/core_cm7.h **** #if defined ( __CC_ARM )
  77:Drivers/CMSIS/Include/core_cm7.h ****   #if defined __TARGET_FPU_VFP
  78:Drivers/CMSIS/Include/core_cm7.h ****     #if defined (__FPU_PRESENT) && (__FPU_PRESENT == 1U)
  79:Drivers/CMSIS/Include/core_cm7.h ****       #define __FPU_USED       1U
  80:Drivers/CMSIS/Include/core_cm7.h ****     #else
  81:Drivers/CMSIS/Include/core_cm7.h ****       #error "Compiler generates FPU instructions for a device without an FPU (check __FPU_PRESENT)
  82:Drivers/CMSIS/Include/core_cm7.h ****       #define __FPU_USED       0U
  83:Drivers/CMSIS/Include/core_cm7.h ****     #endif
  84:Drivers/CMSIS/Include/core_cm7.h ****   #else
  85:Drivers/CMSIS/Include/core_cm7.h ****     #define __FPU_USED         0U
  86:Drivers/CMSIS/Include/core_cm7.h ****   #endif
  87:Drivers/CMSIS/Include/core_cm7.h **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 3


  88:Drivers/CMSIS/Include/core_cm7.h **** #elif defined (__ARMCC_VERSION) && (__ARMCC_VERSION >= 6010050)
  89:Drivers/CMSIS/Include/core_cm7.h ****   #if defined __ARM_FP
  90:Drivers/CMSIS/Include/core_cm7.h ****     #if defined (__FPU_PRESENT) && (__FPU_PRESENT == 1U)
  91:Drivers/CMSIS/Include/core_cm7.h ****       #define __FPU_USED       1U
  92:Drivers/CMSIS/Include/core_cm7.h ****     #else
  93:Drivers/CMSIS/Include/core_cm7.h ****       #warning "Compiler generates FPU instructions for a device without an FPU (check __FPU_PRESEN
  94:Drivers/CMSIS/Include/core_cm7.h ****       #define __FPU_USED       0U
  95:Drivers/CMSIS/Include/core_cm7.h ****     #endif
  96:Drivers/CMSIS/Include/core_cm7.h ****   #else
  97:Drivers/CMSIS/Include/core_cm7.h ****     #define __FPU_USED         0U
  98:Drivers/CMSIS/Include/core_cm7.h ****   #endif
  99:Drivers/CMSIS/Include/core_cm7.h **** 
 100:Drivers/CMSIS/Include/core_cm7.h **** #elif defined ( __GNUC__ )
 101:Drivers/CMSIS/Include/core_cm7.h ****   #if defined (__VFP_FP__) && !defined(__SOFTFP__)
 102:Drivers/CMSIS/Include/core_cm7.h ****     #if defined (__FPU_PRESENT) && (__FPU_PRESENT == 1U)
 103:Drivers/CMSIS/Include/core_cm7.h ****       #define __FPU_USED       1U
 104:Drivers/CMSIS/Include/core_cm7.h ****     #else
 105:Drivers/CMSIS/Include/core_cm7.h ****       #error "Compiler generates FPU instructions for a device without an FPU (check __FPU_PRESENT)
 106:Drivers/CMSIS/Include/core_cm7.h ****       #define __FPU_USED       0U
 107:Drivers/CMSIS/Include/core_cm7.h ****     #endif
 108:Drivers/CMSIS/Include/core_cm7.h ****   #else
 109:Drivers/CMSIS/Include/core_cm7.h ****     #define __FPU_USED         0U
 110:Drivers/CMSIS/Include/core_cm7.h ****   #endif
 111:Drivers/CMSIS/Include/core_cm7.h **** 
 112:Drivers/CMSIS/Include/core_cm7.h **** #elif defined ( __ICCARM__ )
 113:Drivers/CMSIS/Include/core_cm7.h ****   #if defined __ARMVFP__
 114:Drivers/CMSIS/Include/core_cm7.h ****     #if defined (__FPU_PRESENT) && (__FPU_PRESENT == 1U)
 115:Drivers/CMSIS/Include/core_cm7.h ****       #define __FPU_USED       1U
 116:Drivers/CMSIS/Include/core_cm7.h ****     #else
 117:Drivers/CMSIS/Include/core_cm7.h ****       #error "Compiler generates FPU instructions for a device without an FPU (check __FPU_PRESENT)
 118:Drivers/CMSIS/Include/core_cm7.h ****       #define __FPU_USED       0U
 119:Drivers/CMSIS/Include/core_cm7.h ****     #endif
 120:Drivers/CMSIS/Include/core_cm7.h ****   #else
 121:Drivers/CMSIS/Include/core_cm7.h ****     #define __FPU_USED         0U
 122:Drivers/CMSIS/Include/core_cm7.h ****   #endif
 123:Drivers/CMSIS/Include/core_cm7.h **** 
 124:Drivers/CMSIS/Include/core_cm7.h **** #elif defined ( __TI_ARM__ )
 125:Drivers/CMSIS/Include/core_cm7.h ****   #if defined __TI_VFP_SUPPORT__
 126:Drivers/CMSIS/Include/core_cm7.h ****     #if defined (__FPU_PRESENT) && (__FPU_PRESENT == 1U)
 127:Drivers/CMSIS/Include/core_cm7.h ****       #define __FPU_USED       1U
 128:Drivers/CMSIS/Include/core_cm7.h ****     #else
 129:Drivers/CMSIS/Include/core_cm7.h ****       #error "Compiler generates FPU instructions for a device without an FPU (check __FPU_PRESENT)
 130:Drivers/CMSIS/Include/core_cm7.h ****       #define __FPU_USED       0U
 131:Drivers/CMSIS/Include/core_cm7.h ****     #endif
 132:Drivers/CMSIS/Include/core_cm7.h ****   #else
 133:Drivers/CMSIS/Include/core_cm7.h ****     #define __FPU_USED         0U
 134:Drivers/CMSIS/Include/core_cm7.h ****   #endif
 135:Drivers/CMSIS/Include/core_cm7.h **** 
 136:Drivers/CMSIS/Include/core_cm7.h **** #elif defined ( __TASKING__ )
 137:Drivers/CMSIS/Include/core_cm7.h ****   #if defined __FPU_VFP__
 138:Drivers/CMSIS/Include/core_cm7.h ****     #if defined (__FPU_PRESENT) && (__FPU_PRESENT == 1U)
 139:Drivers/CMSIS/Include/core_cm7.h ****       #define __FPU_USED       1U
 140:Drivers/CMSIS/Include/core_cm7.h ****     #else
 141:Drivers/CMSIS/Include/core_cm7.h ****       #error "Compiler generates FPU instructions for a device without an FPU (check __FPU_PRESENT)
 142:Drivers/CMSIS/Include/core_cm7.h ****       #define __FPU_USED       0U
 143:Drivers/CMSIS/Include/core_cm7.h ****     #endif
 144:Drivers/CMSIS/Include/core_cm7.h ****   #else
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 4


 145:Drivers/CMSIS/Include/core_cm7.h ****     #define __FPU_USED         0U
 146:Drivers/CMSIS/Include/core_cm7.h ****   #endif
 147:Drivers/CMSIS/Include/core_cm7.h **** 
 148:Drivers/CMSIS/Include/core_cm7.h **** #elif defined ( __CSMC__ )
 149:Drivers/CMSIS/Include/core_cm7.h ****   #if ( __CSMC__ & 0x400U)
 150:Drivers/CMSIS/Include/core_cm7.h ****     #if defined (__FPU_PRESENT) && (__FPU_PRESENT == 1U)
 151:Drivers/CMSIS/Include/core_cm7.h ****       #define __FPU_USED       1U
 152:Drivers/CMSIS/Include/core_cm7.h ****     #else
 153:Drivers/CMSIS/Include/core_cm7.h ****       #error "Compiler generates FPU instructions for a device without an FPU (check __FPU_PRESENT)
 154:Drivers/CMSIS/Include/core_cm7.h ****       #define __FPU_USED       0U
 155:Drivers/CMSIS/Include/core_cm7.h ****     #endif
 156:Drivers/CMSIS/Include/core_cm7.h ****   #else
 157:Drivers/CMSIS/Include/core_cm7.h ****     #define __FPU_USED         0U
 158:Drivers/CMSIS/Include/core_cm7.h ****   #endif
 159:Drivers/CMSIS/Include/core_cm7.h **** 
 160:Drivers/CMSIS/Include/core_cm7.h **** #endif
 161:Drivers/CMSIS/Include/core_cm7.h **** 
 162:Drivers/CMSIS/Include/core_cm7.h **** #include "cmsis_compiler.h"               /* CMSIS compiler specific defines */
 163:Drivers/CMSIS/Include/core_cm7.h **** 
 164:Drivers/CMSIS/Include/core_cm7.h **** 
 165:Drivers/CMSIS/Include/core_cm7.h **** #ifdef __cplusplus
 166:Drivers/CMSIS/Include/core_cm7.h **** }
 167:Drivers/CMSIS/Include/core_cm7.h **** #endif
 168:Drivers/CMSIS/Include/core_cm7.h **** 
 169:Drivers/CMSIS/Include/core_cm7.h **** #endif /* __CORE_CM7_H_GENERIC */
 170:Drivers/CMSIS/Include/core_cm7.h **** 
 171:Drivers/CMSIS/Include/core_cm7.h **** #ifndef __CMSIS_GENERIC
 172:Drivers/CMSIS/Include/core_cm7.h **** 
 173:Drivers/CMSIS/Include/core_cm7.h **** #ifndef __CORE_CM7_H_DEPENDANT
 174:Drivers/CMSIS/Include/core_cm7.h **** #define __CORE_CM7_H_DEPENDANT
 175:Drivers/CMSIS/Include/core_cm7.h **** 
 176:Drivers/CMSIS/Include/core_cm7.h **** #ifdef __cplusplus
 177:Drivers/CMSIS/Include/core_cm7.h ****  extern "C" {
 178:Drivers/CMSIS/Include/core_cm7.h **** #endif
 179:Drivers/CMSIS/Include/core_cm7.h **** 
 180:Drivers/CMSIS/Include/core_cm7.h **** /* check device defines and use defaults */
 181:Drivers/CMSIS/Include/core_cm7.h **** #if defined __CHECK_DEVICE_DEFINES
 182:Drivers/CMSIS/Include/core_cm7.h ****   #ifndef __CM7_REV
 183:Drivers/CMSIS/Include/core_cm7.h ****     #define __CM7_REV               0x0000U
 184:Drivers/CMSIS/Include/core_cm7.h ****     #warning "__CM7_REV not defined in device header file; using default!"
 185:Drivers/CMSIS/Include/core_cm7.h ****   #endif
 186:Drivers/CMSIS/Include/core_cm7.h **** 
 187:Drivers/CMSIS/Include/core_cm7.h ****   #ifndef __FPU_PRESENT
 188:Drivers/CMSIS/Include/core_cm7.h ****     #define __FPU_PRESENT             0U
 189:Drivers/CMSIS/Include/core_cm7.h ****     #warning "__FPU_PRESENT not defined in device header file; using default!"
 190:Drivers/CMSIS/Include/core_cm7.h ****   #endif
 191:Drivers/CMSIS/Include/core_cm7.h **** 
 192:Drivers/CMSIS/Include/core_cm7.h ****   #ifndef __MPU_PRESENT
 193:Drivers/CMSIS/Include/core_cm7.h ****     #define __MPU_PRESENT             0U
 194:Drivers/CMSIS/Include/core_cm7.h ****     #warning "__MPU_PRESENT not defined in device header file; using default!"
 195:Drivers/CMSIS/Include/core_cm7.h ****   #endif
 196:Drivers/CMSIS/Include/core_cm7.h **** 
 197:Drivers/CMSIS/Include/core_cm7.h ****   #ifndef __ICACHE_PRESENT
 198:Drivers/CMSIS/Include/core_cm7.h ****     #define __ICACHE_PRESENT          0U
 199:Drivers/CMSIS/Include/core_cm7.h ****     #warning "__ICACHE_PRESENT not defined in device header file; using default!"
 200:Drivers/CMSIS/Include/core_cm7.h ****   #endif
 201:Drivers/CMSIS/Include/core_cm7.h **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 5


 202:Drivers/CMSIS/Include/core_cm7.h ****   #ifndef __DCACHE_PRESENT
 203:Drivers/CMSIS/Include/core_cm7.h ****     #define __DCACHE_PRESENT          0U
 204:Drivers/CMSIS/Include/core_cm7.h ****     #warning "__DCACHE_PRESENT not defined in device header file; using default!"
 205:Drivers/CMSIS/Include/core_cm7.h ****   #endif
 206:Drivers/CMSIS/Include/core_cm7.h **** 
 207:Drivers/CMSIS/Include/core_cm7.h ****   #ifndef __DTCM_PRESENT
 208:Drivers/CMSIS/Include/core_cm7.h ****     #define __DTCM_PRESENT            0U
 209:Drivers/CMSIS/Include/core_cm7.h ****     #warning "__DTCM_PRESENT        not defined in device header file; using default!"
 210:Drivers/CMSIS/Include/core_cm7.h ****   #endif
 211:Drivers/CMSIS/Include/core_cm7.h **** 
 212:Drivers/CMSIS/Include/core_cm7.h ****   #ifndef __NVIC_PRIO_BITS
 213:Drivers/CMSIS/Include/core_cm7.h ****     #define __NVIC_PRIO_BITS          3U
 214:Drivers/CMSIS/Include/core_cm7.h ****     #warning "__NVIC_PRIO_BITS not defined in device header file; using default!"
 215:Drivers/CMSIS/Include/core_cm7.h ****   #endif
 216:Drivers/CMSIS/Include/core_cm7.h **** 
 217:Drivers/CMSIS/Include/core_cm7.h ****   #ifndef __Vendor_SysTickConfig
 218:Drivers/CMSIS/Include/core_cm7.h ****     #define __Vendor_SysTickConfig    0U
 219:Drivers/CMSIS/Include/core_cm7.h ****     #warning "__Vendor_SysTickConfig not defined in device header file; using default!"
 220:Drivers/CMSIS/Include/core_cm7.h ****   #endif
 221:Drivers/CMSIS/Include/core_cm7.h **** #endif
 222:Drivers/CMSIS/Include/core_cm7.h **** 
 223:Drivers/CMSIS/Include/core_cm7.h **** /* IO definitions (access restrictions to peripheral registers) */
 224:Drivers/CMSIS/Include/core_cm7.h **** /**
 225:Drivers/CMSIS/Include/core_cm7.h ****     \defgroup CMSIS_glob_defs CMSIS Global Defines
 226:Drivers/CMSIS/Include/core_cm7.h **** 
 227:Drivers/CMSIS/Include/core_cm7.h ****     <strong>IO Type Qualifiers</strong> are used
 228:Drivers/CMSIS/Include/core_cm7.h ****     \li to specify the access to peripheral variables.
 229:Drivers/CMSIS/Include/core_cm7.h ****     \li for automatic generation of peripheral register debug information.
 230:Drivers/CMSIS/Include/core_cm7.h **** */
 231:Drivers/CMSIS/Include/core_cm7.h **** #ifdef __cplusplus
 232:Drivers/CMSIS/Include/core_cm7.h ****   #define   __I     volatile             /*!< Defines 'read only' permissions */
 233:Drivers/CMSIS/Include/core_cm7.h **** #else
 234:Drivers/CMSIS/Include/core_cm7.h ****   #define   __I     volatile const       /*!< Defines 'read only' permissions */
 235:Drivers/CMSIS/Include/core_cm7.h **** #endif
 236:Drivers/CMSIS/Include/core_cm7.h **** #define     __O     volatile             /*!< Defines 'write only' permissions */
 237:Drivers/CMSIS/Include/core_cm7.h **** #define     __IO    volatile             /*!< Defines 'read / write' permissions */
 238:Drivers/CMSIS/Include/core_cm7.h **** 
 239:Drivers/CMSIS/Include/core_cm7.h **** /* following defines should be used for structure members */
 240:Drivers/CMSIS/Include/core_cm7.h **** #define     __IM     volatile const      /*! Defines 'read only' structure member permissions */
 241:Drivers/CMSIS/Include/core_cm7.h **** #define     __OM     volatile            /*! Defines 'write only' structure member permissions */
 242:Drivers/CMSIS/Include/core_cm7.h **** #define     __IOM    volatile            /*! Defines 'read / write' structure member permissions */
 243:Drivers/CMSIS/Include/core_cm7.h **** 
 244:Drivers/CMSIS/Include/core_cm7.h **** /*@} end of group Cortex_M7 */
 245:Drivers/CMSIS/Include/core_cm7.h **** 
 246:Drivers/CMSIS/Include/core_cm7.h **** 
 247:Drivers/CMSIS/Include/core_cm7.h **** 
 248:Drivers/CMSIS/Include/core_cm7.h **** /*******************************************************************************
 249:Drivers/CMSIS/Include/core_cm7.h ****  *                 Register Abstraction
 250:Drivers/CMSIS/Include/core_cm7.h ****   Core Register contain:
 251:Drivers/CMSIS/Include/core_cm7.h ****   - Core Register
 252:Drivers/CMSIS/Include/core_cm7.h ****   - Core NVIC Register
 253:Drivers/CMSIS/Include/core_cm7.h ****   - Core SCB Register
 254:Drivers/CMSIS/Include/core_cm7.h ****   - Core SysTick Register
 255:Drivers/CMSIS/Include/core_cm7.h ****   - Core Debug Register
 256:Drivers/CMSIS/Include/core_cm7.h ****   - Core MPU Register
 257:Drivers/CMSIS/Include/core_cm7.h ****   - Core FPU Register
 258:Drivers/CMSIS/Include/core_cm7.h ****  ******************************************************************************/
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 6


 259:Drivers/CMSIS/Include/core_cm7.h **** /**
 260:Drivers/CMSIS/Include/core_cm7.h ****   \defgroup CMSIS_core_register Defines and Type Definitions
 261:Drivers/CMSIS/Include/core_cm7.h ****   \brief Type definitions and defines for Cortex-M processor based devices.
 262:Drivers/CMSIS/Include/core_cm7.h **** */
 263:Drivers/CMSIS/Include/core_cm7.h **** 
 264:Drivers/CMSIS/Include/core_cm7.h **** /**
 265:Drivers/CMSIS/Include/core_cm7.h ****   \ingroup    CMSIS_core_register
 266:Drivers/CMSIS/Include/core_cm7.h ****   \defgroup   CMSIS_CORE  Status and Control Registers
 267:Drivers/CMSIS/Include/core_cm7.h ****   \brief      Core Register type definitions.
 268:Drivers/CMSIS/Include/core_cm7.h ****   @{
 269:Drivers/CMSIS/Include/core_cm7.h ****  */
 270:Drivers/CMSIS/Include/core_cm7.h **** 
 271:Drivers/CMSIS/Include/core_cm7.h **** /**
 272:Drivers/CMSIS/Include/core_cm7.h ****   \brief  Union type to access the Application Program Status Register (APSR).
 273:Drivers/CMSIS/Include/core_cm7.h ****  */
 274:Drivers/CMSIS/Include/core_cm7.h **** typedef union
 275:Drivers/CMSIS/Include/core_cm7.h **** {
 276:Drivers/CMSIS/Include/core_cm7.h ****   struct
 277:Drivers/CMSIS/Include/core_cm7.h ****   {
 278:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t _reserved0:16;              /*!< bit:  0..15  Reserved */
 279:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t GE:4;                       /*!< bit: 16..19  Greater than or Equal flags */
 280:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t _reserved1:7;               /*!< bit: 20..26  Reserved */
 281:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t Q:1;                        /*!< bit:     27  Saturation condition flag */
 282:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t V:1;                        /*!< bit:     28  Overflow condition code flag */
 283:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t C:1;                        /*!< bit:     29  Carry condition code flag */
 284:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t Z:1;                        /*!< bit:     30  Zero condition code flag */
 285:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t N:1;                        /*!< bit:     31  Negative condition code flag */
 286:Drivers/CMSIS/Include/core_cm7.h ****   } b;                                   /*!< Structure used for bit  access */
 287:Drivers/CMSIS/Include/core_cm7.h ****   uint32_t w;                            /*!< Type      used for word access */
 288:Drivers/CMSIS/Include/core_cm7.h **** } APSR_Type;
 289:Drivers/CMSIS/Include/core_cm7.h **** 
 290:Drivers/CMSIS/Include/core_cm7.h **** /* APSR Register Definitions */
 291:Drivers/CMSIS/Include/core_cm7.h **** #define APSR_N_Pos                         31U                                            /*!< APSR
 292:Drivers/CMSIS/Include/core_cm7.h **** #define APSR_N_Msk                         (1UL << APSR_N_Pos)                            /*!< APSR
 293:Drivers/CMSIS/Include/core_cm7.h **** 
 294:Drivers/CMSIS/Include/core_cm7.h **** #define APSR_Z_Pos                         30U                                            /*!< APSR
 295:Drivers/CMSIS/Include/core_cm7.h **** #define APSR_Z_Msk                         (1UL << APSR_Z_Pos)                            /*!< APSR
 296:Drivers/CMSIS/Include/core_cm7.h **** 
 297:Drivers/CMSIS/Include/core_cm7.h **** #define APSR_C_Pos                         29U                                            /*!< APSR
 298:Drivers/CMSIS/Include/core_cm7.h **** #define APSR_C_Msk                         (1UL << APSR_C_Pos)                            /*!< APSR
 299:Drivers/CMSIS/Include/core_cm7.h **** 
 300:Drivers/CMSIS/Include/core_cm7.h **** #define APSR_V_Pos                         28U                                            /*!< APSR
 301:Drivers/CMSIS/Include/core_cm7.h **** #define APSR_V_Msk                         (1UL << APSR_V_Pos)                            /*!< APSR
 302:Drivers/CMSIS/Include/core_cm7.h **** 
 303:Drivers/CMSIS/Include/core_cm7.h **** #define APSR_Q_Pos                         27U                                            /*!< APSR
 304:Drivers/CMSIS/Include/core_cm7.h **** #define APSR_Q_Msk                         (1UL << APSR_Q_Pos)                            /*!< APSR
 305:Drivers/CMSIS/Include/core_cm7.h **** 
 306:Drivers/CMSIS/Include/core_cm7.h **** #define APSR_GE_Pos                        16U                                            /*!< APSR
 307:Drivers/CMSIS/Include/core_cm7.h **** #define APSR_GE_Msk                        (0xFUL << APSR_GE_Pos)                         /*!< APSR
 308:Drivers/CMSIS/Include/core_cm7.h **** 
 309:Drivers/CMSIS/Include/core_cm7.h **** 
 310:Drivers/CMSIS/Include/core_cm7.h **** /**
 311:Drivers/CMSIS/Include/core_cm7.h ****   \brief  Union type to access the Interrupt Program Status Register (IPSR).
 312:Drivers/CMSIS/Include/core_cm7.h ****  */
 313:Drivers/CMSIS/Include/core_cm7.h **** typedef union
 314:Drivers/CMSIS/Include/core_cm7.h **** {
 315:Drivers/CMSIS/Include/core_cm7.h ****   struct
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 7


 316:Drivers/CMSIS/Include/core_cm7.h ****   {
 317:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t ISR:9;                      /*!< bit:  0.. 8  Exception number */
 318:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t _reserved0:23;              /*!< bit:  9..31  Reserved */
 319:Drivers/CMSIS/Include/core_cm7.h ****   } b;                                   /*!< Structure used for bit  access */
 320:Drivers/CMSIS/Include/core_cm7.h ****   uint32_t w;                            /*!< Type      used for word access */
 321:Drivers/CMSIS/Include/core_cm7.h **** } IPSR_Type;
 322:Drivers/CMSIS/Include/core_cm7.h **** 
 323:Drivers/CMSIS/Include/core_cm7.h **** /* IPSR Register Definitions */
 324:Drivers/CMSIS/Include/core_cm7.h **** #define IPSR_ISR_Pos                        0U                                            /*!< IPSR
 325:Drivers/CMSIS/Include/core_cm7.h **** #define IPSR_ISR_Msk                       (0x1FFUL /*<< IPSR_ISR_Pos*/)                  /*!< IPSR
 326:Drivers/CMSIS/Include/core_cm7.h **** 
 327:Drivers/CMSIS/Include/core_cm7.h **** 
 328:Drivers/CMSIS/Include/core_cm7.h **** /**
 329:Drivers/CMSIS/Include/core_cm7.h ****   \brief  Union type to access the Special-Purpose Program Status Registers (xPSR).
 330:Drivers/CMSIS/Include/core_cm7.h ****  */
 331:Drivers/CMSIS/Include/core_cm7.h **** typedef union
 332:Drivers/CMSIS/Include/core_cm7.h **** {
 333:Drivers/CMSIS/Include/core_cm7.h ****   struct
 334:Drivers/CMSIS/Include/core_cm7.h ****   {
 335:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t ISR:9;                      /*!< bit:  0.. 8  Exception number */
 336:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t _reserved0:1;               /*!< bit:      9  Reserved */
 337:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t ICI_IT_1:6;                 /*!< bit: 10..15  ICI/IT part 1 */
 338:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t GE:4;                       /*!< bit: 16..19  Greater than or Equal flags */
 339:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t _reserved1:4;               /*!< bit: 20..23  Reserved */
 340:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t T:1;                        /*!< bit:     24  Thumb bit */
 341:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t ICI_IT_2:2;                 /*!< bit: 25..26  ICI/IT part 2 */
 342:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t Q:1;                        /*!< bit:     27  Saturation condition flag */
 343:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t V:1;                        /*!< bit:     28  Overflow condition code flag */
 344:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t C:1;                        /*!< bit:     29  Carry condition code flag */
 345:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t Z:1;                        /*!< bit:     30  Zero condition code flag */
 346:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t N:1;                        /*!< bit:     31  Negative condition code flag */
 347:Drivers/CMSIS/Include/core_cm7.h ****   } b;                                   /*!< Structure used for bit  access */
 348:Drivers/CMSIS/Include/core_cm7.h ****   uint32_t w;                            /*!< Type      used for word access */
 349:Drivers/CMSIS/Include/core_cm7.h **** } xPSR_Type;
 350:Drivers/CMSIS/Include/core_cm7.h **** 
 351:Drivers/CMSIS/Include/core_cm7.h **** /* xPSR Register Definitions */
 352:Drivers/CMSIS/Include/core_cm7.h **** #define xPSR_N_Pos                         31U                                            /*!< xPSR
 353:Drivers/CMSIS/Include/core_cm7.h **** #define xPSR_N_Msk                         (1UL << xPSR_N_Pos)                            /*!< xPSR
 354:Drivers/CMSIS/Include/core_cm7.h **** 
 355:Drivers/CMSIS/Include/core_cm7.h **** #define xPSR_Z_Pos                         30U                                            /*!< xPSR
 356:Drivers/CMSIS/Include/core_cm7.h **** #define xPSR_Z_Msk                         (1UL << xPSR_Z_Pos)                            /*!< xPSR
 357:Drivers/CMSIS/Include/core_cm7.h **** 
 358:Drivers/CMSIS/Include/core_cm7.h **** #define xPSR_C_Pos                         29U                                            /*!< xPSR
 359:Drivers/CMSIS/Include/core_cm7.h **** #define xPSR_C_Msk                         (1UL << xPSR_C_Pos)                            /*!< xPSR
 360:Drivers/CMSIS/Include/core_cm7.h **** 
 361:Drivers/CMSIS/Include/core_cm7.h **** #define xPSR_V_Pos                         28U                                            /*!< xPSR
 362:Drivers/CMSIS/Include/core_cm7.h **** #define xPSR_V_Msk                         (1UL << xPSR_V_Pos)                            /*!< xPSR
 363:Drivers/CMSIS/Include/core_cm7.h **** 
 364:Drivers/CMSIS/Include/core_cm7.h **** #define xPSR_Q_Pos                         27U                                            /*!< xPSR
 365:Drivers/CMSIS/Include/core_cm7.h **** #define xPSR_Q_Msk                         (1UL << xPSR_Q_Pos)                            /*!< xPSR
 366:Drivers/CMSIS/Include/core_cm7.h **** 
 367:Drivers/CMSIS/Include/core_cm7.h **** #define xPSR_ICI_IT_2_Pos                  25U                                            /*!< xPSR
 368:Drivers/CMSIS/Include/core_cm7.h **** #define xPSR_ICI_IT_2_Msk                  (3UL << xPSR_ICI_IT_2_Pos)                     /*!< xPSR
 369:Drivers/CMSIS/Include/core_cm7.h **** 
 370:Drivers/CMSIS/Include/core_cm7.h **** #define xPSR_T_Pos                         24U                                            /*!< xPSR
 371:Drivers/CMSIS/Include/core_cm7.h **** #define xPSR_T_Msk                         (1UL << xPSR_T_Pos)                            /*!< xPSR
 372:Drivers/CMSIS/Include/core_cm7.h **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 8


 373:Drivers/CMSIS/Include/core_cm7.h **** #define xPSR_GE_Pos                        16U                                            /*!< xPSR
 374:Drivers/CMSIS/Include/core_cm7.h **** #define xPSR_GE_Msk                        (0xFUL << xPSR_GE_Pos)                         /*!< xPSR
 375:Drivers/CMSIS/Include/core_cm7.h **** 
 376:Drivers/CMSIS/Include/core_cm7.h **** #define xPSR_ICI_IT_1_Pos                  10U                                            /*!< xPSR
 377:Drivers/CMSIS/Include/core_cm7.h **** #define xPSR_ICI_IT_1_Msk                  (0x3FUL << xPSR_ICI_IT_1_Pos)                  /*!< xPSR
 378:Drivers/CMSIS/Include/core_cm7.h **** 
 379:Drivers/CMSIS/Include/core_cm7.h **** #define xPSR_ISR_Pos                        0U                                            /*!< xPSR
 380:Drivers/CMSIS/Include/core_cm7.h **** #define xPSR_ISR_Msk                       (0x1FFUL /*<< xPSR_ISR_Pos*/)                  /*!< xPSR
 381:Drivers/CMSIS/Include/core_cm7.h **** 
 382:Drivers/CMSIS/Include/core_cm7.h **** 
 383:Drivers/CMSIS/Include/core_cm7.h **** /**
 384:Drivers/CMSIS/Include/core_cm7.h ****   \brief  Union type to access the Control Registers (CONTROL).
 385:Drivers/CMSIS/Include/core_cm7.h ****  */
 386:Drivers/CMSIS/Include/core_cm7.h **** typedef union
 387:Drivers/CMSIS/Include/core_cm7.h **** {
 388:Drivers/CMSIS/Include/core_cm7.h ****   struct
 389:Drivers/CMSIS/Include/core_cm7.h ****   {
 390:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t nPRIV:1;                    /*!< bit:      0  Execution privilege in Thread mode */
 391:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t SPSEL:1;                    /*!< bit:      1  Stack to be used */
 392:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t FPCA:1;                     /*!< bit:      2  FP extension active flag */
 393:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t _reserved0:29;              /*!< bit:  3..31  Reserved */
 394:Drivers/CMSIS/Include/core_cm7.h ****   } b;                                   /*!< Structure used for bit  access */
 395:Drivers/CMSIS/Include/core_cm7.h ****   uint32_t w;                            /*!< Type      used for word access */
 396:Drivers/CMSIS/Include/core_cm7.h **** } CONTROL_Type;
 397:Drivers/CMSIS/Include/core_cm7.h **** 
 398:Drivers/CMSIS/Include/core_cm7.h **** /* CONTROL Register Definitions */
 399:Drivers/CMSIS/Include/core_cm7.h **** #define CONTROL_FPCA_Pos                    2U                                            /*!< CONT
 400:Drivers/CMSIS/Include/core_cm7.h **** #define CONTROL_FPCA_Msk                   (1UL << CONTROL_FPCA_Pos)                      /*!< CONT
 401:Drivers/CMSIS/Include/core_cm7.h **** 
 402:Drivers/CMSIS/Include/core_cm7.h **** #define CONTROL_SPSEL_Pos                   1U                                            /*!< CONT
 403:Drivers/CMSIS/Include/core_cm7.h **** #define CONTROL_SPSEL_Msk                  (1UL << CONTROL_SPSEL_Pos)                     /*!< CONT
 404:Drivers/CMSIS/Include/core_cm7.h **** 
 405:Drivers/CMSIS/Include/core_cm7.h **** #define CONTROL_nPRIV_Pos                   0U                                            /*!< CONT
 406:Drivers/CMSIS/Include/core_cm7.h **** #define CONTROL_nPRIV_Msk                  (1UL /*<< CONTROL_nPRIV_Pos*/)                 /*!< CONT
 407:Drivers/CMSIS/Include/core_cm7.h **** 
 408:Drivers/CMSIS/Include/core_cm7.h **** /*@} end of group CMSIS_CORE */
 409:Drivers/CMSIS/Include/core_cm7.h **** 
 410:Drivers/CMSIS/Include/core_cm7.h **** 
 411:Drivers/CMSIS/Include/core_cm7.h **** /**
 412:Drivers/CMSIS/Include/core_cm7.h ****   \ingroup    CMSIS_core_register
 413:Drivers/CMSIS/Include/core_cm7.h ****   \defgroup   CMSIS_NVIC  Nested Vectored Interrupt Controller (NVIC)
 414:Drivers/CMSIS/Include/core_cm7.h ****   \brief      Type definitions for the NVIC Registers
 415:Drivers/CMSIS/Include/core_cm7.h ****   @{
 416:Drivers/CMSIS/Include/core_cm7.h ****  */
 417:Drivers/CMSIS/Include/core_cm7.h **** 
 418:Drivers/CMSIS/Include/core_cm7.h **** /**
 419:Drivers/CMSIS/Include/core_cm7.h ****   \brief  Structure type to access the Nested Vectored Interrupt Controller (NVIC).
 420:Drivers/CMSIS/Include/core_cm7.h ****  */
 421:Drivers/CMSIS/Include/core_cm7.h **** typedef struct
 422:Drivers/CMSIS/Include/core_cm7.h **** {
 423:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t ISER[8U];               /*!< Offset: 0x000 (R/W)  Interrupt Set Enable Register */
 424:Drivers/CMSIS/Include/core_cm7.h ****         uint32_t RESERVED0[24U];
 425:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t ICER[8U];               /*!< Offset: 0x080 (R/W)  Interrupt Clear Enable Register 
 426:Drivers/CMSIS/Include/core_cm7.h ****         uint32_t RESERVED1[24U];
 427:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t ISPR[8U];               /*!< Offset: 0x100 (R/W)  Interrupt Set Pending Register *
 428:Drivers/CMSIS/Include/core_cm7.h ****         uint32_t RESERVED2[24U];
 429:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t ICPR[8U];               /*!< Offset: 0x180 (R/W)  Interrupt Clear Pending Register
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 9


 430:Drivers/CMSIS/Include/core_cm7.h ****         uint32_t RESERVED3[24U];
 431:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t IABR[8U];               /*!< Offset: 0x200 (R/W)  Interrupt Active bit Register */
 432:Drivers/CMSIS/Include/core_cm7.h ****         uint32_t RESERVED4[56U];
 433:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint8_t  IP[240U];               /*!< Offset: 0x300 (R/W)  Interrupt Priority Register (8Bi
 434:Drivers/CMSIS/Include/core_cm7.h ****         uint32_t RESERVED5[644U];
 435:Drivers/CMSIS/Include/core_cm7.h ****   __OM  uint32_t STIR;                   /*!< Offset: 0xE00 ( /W)  Software Trigger Interrupt Regis
 436:Drivers/CMSIS/Include/core_cm7.h **** }  NVIC_Type;
 437:Drivers/CMSIS/Include/core_cm7.h **** 
 438:Drivers/CMSIS/Include/core_cm7.h **** /* Software Triggered Interrupt Register Definitions */
 439:Drivers/CMSIS/Include/core_cm7.h **** #define NVIC_STIR_INTID_Pos                 0U                                         /*!< STIR: I
 440:Drivers/CMSIS/Include/core_cm7.h **** #define NVIC_STIR_INTID_Msk                (0x1FFUL /*<< NVIC_STIR_INTID_Pos*/)        /*!< STIR: I
 441:Drivers/CMSIS/Include/core_cm7.h **** 
 442:Drivers/CMSIS/Include/core_cm7.h **** /*@} end of group CMSIS_NVIC */
 443:Drivers/CMSIS/Include/core_cm7.h **** 
 444:Drivers/CMSIS/Include/core_cm7.h **** 
 445:Drivers/CMSIS/Include/core_cm7.h **** /**
 446:Drivers/CMSIS/Include/core_cm7.h ****   \ingroup  CMSIS_core_register
 447:Drivers/CMSIS/Include/core_cm7.h ****   \defgroup CMSIS_SCB     System Control Block (SCB)
 448:Drivers/CMSIS/Include/core_cm7.h ****   \brief    Type definitions for the System Control Block Registers
 449:Drivers/CMSIS/Include/core_cm7.h ****   @{
 450:Drivers/CMSIS/Include/core_cm7.h ****  */
 451:Drivers/CMSIS/Include/core_cm7.h **** 
 452:Drivers/CMSIS/Include/core_cm7.h **** /**
 453:Drivers/CMSIS/Include/core_cm7.h ****   \brief  Structure type to access the System Control Block (SCB).
 454:Drivers/CMSIS/Include/core_cm7.h ****  */
 455:Drivers/CMSIS/Include/core_cm7.h **** typedef struct
 456:Drivers/CMSIS/Include/core_cm7.h **** {
 457:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t CPUID;                  /*!< Offset: 0x000 (R/ )  CPUID Base Register */
 458:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t ICSR;                   /*!< Offset: 0x004 (R/W)  Interrupt Control and State Regi
 459:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t VTOR;                   /*!< Offset: 0x008 (R/W)  Vector Table Offset Register */
 460:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t AIRCR;                  /*!< Offset: 0x00C (R/W)  Application Interrupt and Reset 
 461:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t SCR;                    /*!< Offset: 0x010 (R/W)  System Control Register */
 462:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t CCR;                    /*!< Offset: 0x014 (R/W)  Configuration Control Register *
 463:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint8_t  SHPR[12U];              /*!< Offset: 0x018 (R/W)  System Handlers Priority Registe
 464:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t SHCSR;                  /*!< Offset: 0x024 (R/W)  System Handler Control and State
 465:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t CFSR;                   /*!< Offset: 0x028 (R/W)  Configurable Fault Status Regist
 466:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t HFSR;                   /*!< Offset: 0x02C (R/W)  HardFault Status Register */
 467:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t DFSR;                   /*!< Offset: 0x030 (R/W)  Debug Fault Status Register */
 468:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t MMFAR;                  /*!< Offset: 0x034 (R/W)  MemManage Fault Address Register
 469:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t BFAR;                   /*!< Offset: 0x038 (R/W)  BusFault Address Register */
 470:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t AFSR;                   /*!< Offset: 0x03C (R/W)  Auxiliary Fault Status Register 
 471:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t ID_PFR[2U];             /*!< Offset: 0x040 (R/ )  Processor Feature Register */
 472:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t ID_DFR;                 /*!< Offset: 0x048 (R/ )  Debug Feature Register */
 473:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t ID_AFR;                 /*!< Offset: 0x04C (R/ )  Auxiliary Feature Register */
 474:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t ID_MFR[4U];             /*!< Offset: 0x050 (R/ )  Memory Model Feature Register */
 475:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t ID_ISAR[5U];            /*!< Offset: 0x060 (R/ )  Instruction Set Attributes Regis
 476:Drivers/CMSIS/Include/core_cm7.h ****         uint32_t RESERVED0[1U];
 477:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t CLIDR;                  /*!< Offset: 0x078 (R/ )  Cache Level ID register */
 478:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t CTR;                    /*!< Offset: 0x07C (R/ )  Cache Type register */
 479:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t CCSIDR;                 /*!< Offset: 0x080 (R/ )  Cache Size ID Register */
 480:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t CSSELR;                 /*!< Offset: 0x084 (R/W)  Cache Size Selection Register */
 481:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t CPACR;                  /*!< Offset: 0x088 (R/W)  Coprocessor Access Control Regis
 482:Drivers/CMSIS/Include/core_cm7.h ****         uint32_t RESERVED3[93U];
 483:Drivers/CMSIS/Include/core_cm7.h ****   __OM  uint32_t STIR;                   /*!< Offset: 0x200 ( /W)  Software Triggered Interrupt Reg
 484:Drivers/CMSIS/Include/core_cm7.h ****         uint32_t RESERVED4[15U];
 485:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t MVFR0;                  /*!< Offset: 0x240 (R/ )  Media and VFP Feature Register 0
 486:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t MVFR1;                  /*!< Offset: 0x244 (R/ )  Media and VFP Feature Register 1
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 10


 487:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t MVFR2;                  /*!< Offset: 0x248 (R/ )  Media and VFP Feature Register 2
 488:Drivers/CMSIS/Include/core_cm7.h ****         uint32_t RESERVED5[1U];
 489:Drivers/CMSIS/Include/core_cm7.h ****   __OM  uint32_t ICIALLU;                /*!< Offset: 0x250 ( /W)  I-Cache Invalidate All to PoU */
 490:Drivers/CMSIS/Include/core_cm7.h ****         uint32_t RESERVED6[1U];
 491:Drivers/CMSIS/Include/core_cm7.h ****   __OM  uint32_t ICIMVAU;                /*!< Offset: 0x258 ( /W)  I-Cache Invalidate by MVA to PoU
 492:Drivers/CMSIS/Include/core_cm7.h ****   __OM  uint32_t DCIMVAC;                /*!< Offset: 0x25C ( /W)  D-Cache Invalidate by MVA to PoC
 493:Drivers/CMSIS/Include/core_cm7.h ****   __OM  uint32_t DCISW;                  /*!< Offset: 0x260 ( /W)  D-Cache Invalidate by Set-way */
 494:Drivers/CMSIS/Include/core_cm7.h ****   __OM  uint32_t DCCMVAU;                /*!< Offset: 0x264 ( /W)  D-Cache Clean by MVA to PoU */
 495:Drivers/CMSIS/Include/core_cm7.h ****   __OM  uint32_t DCCMVAC;                /*!< Offset: 0x268 ( /W)  D-Cache Clean by MVA to PoC */
 496:Drivers/CMSIS/Include/core_cm7.h ****   __OM  uint32_t DCCSW;                  /*!< Offset: 0x26C ( /W)  D-Cache Clean by Set-way */
 497:Drivers/CMSIS/Include/core_cm7.h ****   __OM  uint32_t DCCIMVAC;               /*!< Offset: 0x270 ( /W)  D-Cache Clean and Invalidate by 
 498:Drivers/CMSIS/Include/core_cm7.h ****   __OM  uint32_t DCCISW;                 /*!< Offset: 0x274 ( /W)  D-Cache Clean and Invalidate by 
 499:Drivers/CMSIS/Include/core_cm7.h ****         uint32_t RESERVED7[6U];
 500:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t ITCMCR;                 /*!< Offset: 0x290 (R/W)  Instruction Tightly-Coupled Memo
 501:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t DTCMCR;                 /*!< Offset: 0x294 (R/W)  Data Tightly-Coupled Memory Cont
 502:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t AHBPCR;                 /*!< Offset: 0x298 (R/W)  AHBP Control Register */
 503:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t CACR;                   /*!< Offset: 0x29C (R/W)  L1 Cache Control Register */
 504:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t AHBSCR;                 /*!< Offset: 0x2A0 (R/W)  AHB Slave Control Register */
 505:Drivers/CMSIS/Include/core_cm7.h ****         uint32_t RESERVED8[1U];
 506:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t ABFSR;                  /*!< Offset: 0x2A8 (R/W)  Auxiliary Bus Fault Status Regis
 507:Drivers/CMSIS/Include/core_cm7.h **** } SCB_Type;
 508:Drivers/CMSIS/Include/core_cm7.h **** 
 509:Drivers/CMSIS/Include/core_cm7.h **** /* SCB CPUID Register Definitions */
 510:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CPUID_IMPLEMENTER_Pos          24U                                            /*!< SCB 
 511:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CPUID_IMPLEMENTER_Msk          (0xFFUL << SCB_CPUID_IMPLEMENTER_Pos)          /*!< SCB 
 512:Drivers/CMSIS/Include/core_cm7.h **** 
 513:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CPUID_VARIANT_Pos              20U                                            /*!< SCB 
 514:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CPUID_VARIANT_Msk              (0xFUL << SCB_CPUID_VARIANT_Pos)               /*!< SCB 
 515:Drivers/CMSIS/Include/core_cm7.h **** 
 516:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CPUID_ARCHITECTURE_Pos         16U                                            /*!< SCB 
 517:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CPUID_ARCHITECTURE_Msk         (0xFUL << SCB_CPUID_ARCHITECTURE_Pos)          /*!< SCB 
 518:Drivers/CMSIS/Include/core_cm7.h **** 
 519:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CPUID_PARTNO_Pos                4U                                            /*!< SCB 
 520:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CPUID_PARTNO_Msk               (0xFFFUL << SCB_CPUID_PARTNO_Pos)              /*!< SCB 
 521:Drivers/CMSIS/Include/core_cm7.h **** 
 522:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CPUID_REVISION_Pos              0U                                            /*!< SCB 
 523:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CPUID_REVISION_Msk             (0xFUL /*<< SCB_CPUID_REVISION_Pos*/)          /*!< SCB 
 524:Drivers/CMSIS/Include/core_cm7.h **** 
 525:Drivers/CMSIS/Include/core_cm7.h **** /* SCB Interrupt Control State Register Definitions */
 526:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ICSR_NMIPENDSET_Pos            31U                                            /*!< SCB 
 527:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ICSR_NMIPENDSET_Msk            (1UL << SCB_ICSR_NMIPENDSET_Pos)               /*!< SCB 
 528:Drivers/CMSIS/Include/core_cm7.h **** 
 529:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ICSR_PENDSVSET_Pos             28U                                            /*!< SCB 
 530:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ICSR_PENDSVSET_Msk             (1UL << SCB_ICSR_PENDSVSET_Pos)                /*!< SCB 
 531:Drivers/CMSIS/Include/core_cm7.h **** 
 532:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ICSR_PENDSVCLR_Pos             27U                                            /*!< SCB 
 533:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ICSR_PENDSVCLR_Msk             (1UL << SCB_ICSR_PENDSVCLR_Pos)                /*!< SCB 
 534:Drivers/CMSIS/Include/core_cm7.h **** 
 535:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ICSR_PENDSTSET_Pos             26U                                            /*!< SCB 
 536:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ICSR_PENDSTSET_Msk             (1UL << SCB_ICSR_PENDSTSET_Pos)                /*!< SCB 
 537:Drivers/CMSIS/Include/core_cm7.h **** 
 538:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ICSR_PENDSTCLR_Pos             25U                                            /*!< SCB 
 539:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ICSR_PENDSTCLR_Msk             (1UL << SCB_ICSR_PENDSTCLR_Pos)                /*!< SCB 
 540:Drivers/CMSIS/Include/core_cm7.h **** 
 541:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ICSR_ISRPREEMPT_Pos            23U                                            /*!< SCB 
 542:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ICSR_ISRPREEMPT_Msk            (1UL << SCB_ICSR_ISRPREEMPT_Pos)               /*!< SCB 
 543:Drivers/CMSIS/Include/core_cm7.h **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 11


 544:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ICSR_ISRPENDING_Pos            22U                                            /*!< SCB 
 545:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ICSR_ISRPENDING_Msk            (1UL << SCB_ICSR_ISRPENDING_Pos)               /*!< SCB 
 546:Drivers/CMSIS/Include/core_cm7.h **** 
 547:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ICSR_VECTPENDING_Pos           12U                                            /*!< SCB 
 548:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ICSR_VECTPENDING_Msk           (0x1FFUL << SCB_ICSR_VECTPENDING_Pos)          /*!< SCB 
 549:Drivers/CMSIS/Include/core_cm7.h **** 
 550:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ICSR_RETTOBASE_Pos             11U                                            /*!< SCB 
 551:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ICSR_RETTOBASE_Msk             (1UL << SCB_ICSR_RETTOBASE_Pos)                /*!< SCB 
 552:Drivers/CMSIS/Include/core_cm7.h **** 
 553:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ICSR_VECTACTIVE_Pos             0U                                            /*!< SCB 
 554:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ICSR_VECTACTIVE_Msk            (0x1FFUL /*<< SCB_ICSR_VECTACTIVE_Pos*/)       /*!< SCB 
 555:Drivers/CMSIS/Include/core_cm7.h **** 
 556:Drivers/CMSIS/Include/core_cm7.h **** /* SCB Vector Table Offset Register Definitions */
 557:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_VTOR_TBLOFF_Pos                 7U                                            /*!< SCB 
 558:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_VTOR_TBLOFF_Msk                (0x1FFFFFFUL << SCB_VTOR_TBLOFF_Pos)           /*!< SCB 
 559:Drivers/CMSIS/Include/core_cm7.h **** 
 560:Drivers/CMSIS/Include/core_cm7.h **** /* SCB Application Interrupt and Reset Control Register Definitions */
 561:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_AIRCR_VECTKEY_Pos              16U                                            /*!< SCB 
 562:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_AIRCR_VECTKEY_Msk              (0xFFFFUL << SCB_AIRCR_VECTKEY_Pos)            /*!< SCB 
 563:Drivers/CMSIS/Include/core_cm7.h **** 
 564:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_AIRCR_VECTKEYSTAT_Pos          16U                                            /*!< SCB 
 565:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_AIRCR_VECTKEYSTAT_Msk          (0xFFFFUL << SCB_AIRCR_VECTKEYSTAT_Pos)        /*!< SCB 
 566:Drivers/CMSIS/Include/core_cm7.h **** 
 567:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_AIRCR_ENDIANESS_Pos            15U                                            /*!< SCB 
 568:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_AIRCR_ENDIANESS_Msk            (1UL << SCB_AIRCR_ENDIANESS_Pos)               /*!< SCB 
 569:Drivers/CMSIS/Include/core_cm7.h **** 
 570:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_AIRCR_PRIGROUP_Pos              8U                                            /*!< SCB 
 571:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_AIRCR_PRIGROUP_Msk             (7UL << SCB_AIRCR_PRIGROUP_Pos)                /*!< SCB 
 572:Drivers/CMSIS/Include/core_cm7.h **** 
 573:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_AIRCR_SYSRESETREQ_Pos           2U                                            /*!< SCB 
 574:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_AIRCR_SYSRESETREQ_Msk          (1UL << SCB_AIRCR_SYSRESETREQ_Pos)             /*!< SCB 
 575:Drivers/CMSIS/Include/core_cm7.h **** 
 576:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_AIRCR_VECTCLRACTIVE_Pos         1U                                            /*!< SCB 
 577:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_AIRCR_VECTCLRACTIVE_Msk        (1UL << SCB_AIRCR_VECTCLRACTIVE_Pos)           /*!< SCB 
 578:Drivers/CMSIS/Include/core_cm7.h **** 
 579:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_AIRCR_VECTRESET_Pos             0U                                            /*!< SCB 
 580:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_AIRCR_VECTRESET_Msk            (1UL /*<< SCB_AIRCR_VECTRESET_Pos*/)           /*!< SCB 
 581:Drivers/CMSIS/Include/core_cm7.h **** 
 582:Drivers/CMSIS/Include/core_cm7.h **** /* SCB System Control Register Definitions */
 583:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_SCR_SEVONPEND_Pos               4U                                            /*!< SCB 
 584:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_SCR_SEVONPEND_Msk              (1UL << SCB_SCR_SEVONPEND_Pos)                 /*!< SCB 
 585:Drivers/CMSIS/Include/core_cm7.h **** 
 586:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_SCR_SLEEPDEEP_Pos               2U                                            /*!< SCB 
 587:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_SCR_SLEEPDEEP_Msk              (1UL << SCB_SCR_SLEEPDEEP_Pos)                 /*!< SCB 
 588:Drivers/CMSIS/Include/core_cm7.h **** 
 589:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_SCR_SLEEPONEXIT_Pos             1U                                            /*!< SCB 
 590:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_SCR_SLEEPONEXIT_Msk            (1UL << SCB_SCR_SLEEPONEXIT_Pos)               /*!< SCB 
 591:Drivers/CMSIS/Include/core_cm7.h **** 
 592:Drivers/CMSIS/Include/core_cm7.h **** /* SCB Configuration Control Register Definitions */
 593:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CCR_BP_Pos                      18U                                           /*!< SCB 
 594:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CCR_BP_Msk                     (1UL << SCB_CCR_BP_Pos)                        /*!< SCB 
 595:Drivers/CMSIS/Include/core_cm7.h **** 
 596:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CCR_IC_Pos                      17U                                           /*!< SCB 
 597:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CCR_IC_Msk                     (1UL << SCB_CCR_IC_Pos)                        /*!< SCB 
 598:Drivers/CMSIS/Include/core_cm7.h **** 
 599:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CCR_DC_Pos                      16U                                           /*!< SCB 
 600:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CCR_DC_Msk                     (1UL << SCB_CCR_DC_Pos)                        /*!< SCB 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 12


 601:Drivers/CMSIS/Include/core_cm7.h **** 
 602:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CCR_STKALIGN_Pos                9U                                            /*!< SCB 
 603:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CCR_STKALIGN_Msk               (1UL << SCB_CCR_STKALIGN_Pos)                  /*!< SCB 
 604:Drivers/CMSIS/Include/core_cm7.h **** 
 605:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CCR_BFHFNMIGN_Pos               8U                                            /*!< SCB 
 606:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CCR_BFHFNMIGN_Msk              (1UL << SCB_CCR_BFHFNMIGN_Pos)                 /*!< SCB 
 607:Drivers/CMSIS/Include/core_cm7.h **** 
 608:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CCR_DIV_0_TRP_Pos               4U                                            /*!< SCB 
 609:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CCR_DIV_0_TRP_Msk              (1UL << SCB_CCR_DIV_0_TRP_Pos)                 /*!< SCB 
 610:Drivers/CMSIS/Include/core_cm7.h **** 
 611:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CCR_UNALIGN_TRP_Pos             3U                                            /*!< SCB 
 612:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CCR_UNALIGN_TRP_Msk            (1UL << SCB_CCR_UNALIGN_TRP_Pos)               /*!< SCB 
 613:Drivers/CMSIS/Include/core_cm7.h **** 
 614:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CCR_USERSETMPEND_Pos            1U                                            /*!< SCB 
 615:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CCR_USERSETMPEND_Msk           (1UL << SCB_CCR_USERSETMPEND_Pos)              /*!< SCB 
 616:Drivers/CMSIS/Include/core_cm7.h **** 
 617:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CCR_NONBASETHRDENA_Pos          0U                                            /*!< SCB 
 618:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CCR_NONBASETHRDENA_Msk         (1UL /*<< SCB_CCR_NONBASETHRDENA_Pos*/)        /*!< SCB 
 619:Drivers/CMSIS/Include/core_cm7.h **** 
 620:Drivers/CMSIS/Include/core_cm7.h **** /* SCB System Handler Control and State Register Definitions */
 621:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_SHCSR_USGFAULTENA_Pos          18U                                            /*!< SCB 
 622:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_SHCSR_USGFAULTENA_Msk          (1UL << SCB_SHCSR_USGFAULTENA_Pos)             /*!< SCB 
 623:Drivers/CMSIS/Include/core_cm7.h **** 
 624:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_SHCSR_BUSFAULTENA_Pos          17U                                            /*!< SCB 
 625:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_SHCSR_BUSFAULTENA_Msk          (1UL << SCB_SHCSR_BUSFAULTENA_Pos)             /*!< SCB 
 626:Drivers/CMSIS/Include/core_cm7.h **** 
 627:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_SHCSR_MEMFAULTENA_Pos          16U                                            /*!< SCB 
 628:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_SHCSR_MEMFAULTENA_Msk          (1UL << SCB_SHCSR_MEMFAULTENA_Pos)             /*!< SCB 
 629:Drivers/CMSIS/Include/core_cm7.h **** 
 630:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_SHCSR_SVCALLPENDED_Pos         15U                                            /*!< SCB 
 631:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_SHCSR_SVCALLPENDED_Msk         (1UL << SCB_SHCSR_SVCALLPENDED_Pos)            /*!< SCB 
 632:Drivers/CMSIS/Include/core_cm7.h **** 
 633:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_SHCSR_BUSFAULTPENDED_Pos       14U                                            /*!< SCB 
 634:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_SHCSR_BUSFAULTPENDED_Msk       (1UL << SCB_SHCSR_BUSFAULTPENDED_Pos)          /*!< SCB 
 635:Drivers/CMSIS/Include/core_cm7.h **** 
 636:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_SHCSR_MEMFAULTPENDED_Pos       13U                                            /*!< SCB 
 637:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_SHCSR_MEMFAULTPENDED_Msk       (1UL << SCB_SHCSR_MEMFAULTPENDED_Pos)          /*!< SCB 
 638:Drivers/CMSIS/Include/core_cm7.h **** 
 639:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_SHCSR_USGFAULTPENDED_Pos       12U                                            /*!< SCB 
 640:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_SHCSR_USGFAULTPENDED_Msk       (1UL << SCB_SHCSR_USGFAULTPENDED_Pos)          /*!< SCB 
 641:Drivers/CMSIS/Include/core_cm7.h **** 
 642:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_SHCSR_SYSTICKACT_Pos           11U                                            /*!< SCB 
 643:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_SHCSR_SYSTICKACT_Msk           (1UL << SCB_SHCSR_SYSTICKACT_Pos)              /*!< SCB 
 644:Drivers/CMSIS/Include/core_cm7.h **** 
 645:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_SHCSR_PENDSVACT_Pos            10U                                            /*!< SCB 
 646:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_SHCSR_PENDSVACT_Msk            (1UL << SCB_SHCSR_PENDSVACT_Pos)               /*!< SCB 
 647:Drivers/CMSIS/Include/core_cm7.h **** 
 648:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_SHCSR_MONITORACT_Pos            8U                                            /*!< SCB 
 649:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_SHCSR_MONITORACT_Msk           (1UL << SCB_SHCSR_MONITORACT_Pos)              /*!< SCB 
 650:Drivers/CMSIS/Include/core_cm7.h **** 
 651:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_SHCSR_SVCALLACT_Pos             7U                                            /*!< SCB 
 652:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_SHCSR_SVCALLACT_Msk            (1UL << SCB_SHCSR_SVCALLACT_Pos)               /*!< SCB 
 653:Drivers/CMSIS/Include/core_cm7.h **** 
 654:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_SHCSR_USGFAULTACT_Pos           3U                                            /*!< SCB 
 655:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_SHCSR_USGFAULTACT_Msk          (1UL << SCB_SHCSR_USGFAULTACT_Pos)             /*!< SCB 
 656:Drivers/CMSIS/Include/core_cm7.h **** 
 657:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_SHCSR_BUSFAULTACT_Pos           1U                                            /*!< SCB 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 13


 658:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_SHCSR_BUSFAULTACT_Msk          (1UL << SCB_SHCSR_BUSFAULTACT_Pos)             /*!< SCB 
 659:Drivers/CMSIS/Include/core_cm7.h **** 
 660:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_SHCSR_MEMFAULTACT_Pos           0U                                            /*!< SCB 
 661:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_SHCSR_MEMFAULTACT_Msk          (1UL /*<< SCB_SHCSR_MEMFAULTACT_Pos*/)         /*!< SCB 
 662:Drivers/CMSIS/Include/core_cm7.h **** 
 663:Drivers/CMSIS/Include/core_cm7.h **** /* SCB Configurable Fault Status Register Definitions */
 664:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_USGFAULTSR_Pos            16U                                            /*!< SCB 
 665:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_USGFAULTSR_Msk            (0xFFFFUL << SCB_CFSR_USGFAULTSR_Pos)          /*!< SCB 
 666:Drivers/CMSIS/Include/core_cm7.h **** 
 667:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_BUSFAULTSR_Pos             8U                                            /*!< SCB 
 668:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_BUSFAULTSR_Msk            (0xFFUL << SCB_CFSR_BUSFAULTSR_Pos)            /*!< SCB 
 669:Drivers/CMSIS/Include/core_cm7.h **** 
 670:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_MEMFAULTSR_Pos             0U                                            /*!< SCB 
 671:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_MEMFAULTSR_Msk            (0xFFUL /*<< SCB_CFSR_MEMFAULTSR_Pos*/)        /*!< SCB 
 672:Drivers/CMSIS/Include/core_cm7.h **** 
 673:Drivers/CMSIS/Include/core_cm7.h **** /* MemManage Fault Status Register (part of SCB Configurable Fault Status Register) */
 674:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_MMARVALID_Pos             (SCB_SHCSR_MEMFAULTACT_Pos + 7U)               /*!< SCB 
 675:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_MMARVALID_Msk             (1UL << SCB_CFSR_MMARVALID_Pos)                /*!< SCB 
 676:Drivers/CMSIS/Include/core_cm7.h **** 
 677:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_MLSPERR_Pos               (SCB_SHCSR_MEMFAULTACT_Pos + 5U)               /*!< SCB 
 678:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_MLSPERR_Msk               (1UL << SCB_CFSR_MLSPERR_Pos)                  /*!< SCB 
 679:Drivers/CMSIS/Include/core_cm7.h **** 
 680:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_MSTKERR_Pos               (SCB_SHCSR_MEMFAULTACT_Pos + 4U)               /*!< SCB 
 681:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_MSTKERR_Msk               (1UL << SCB_CFSR_MSTKERR_Pos)                  /*!< SCB 
 682:Drivers/CMSIS/Include/core_cm7.h **** 
 683:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_MUNSTKERR_Pos             (SCB_SHCSR_MEMFAULTACT_Pos + 3U)               /*!< SCB 
 684:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_MUNSTKERR_Msk             (1UL << SCB_CFSR_MUNSTKERR_Pos)                /*!< SCB 
 685:Drivers/CMSIS/Include/core_cm7.h **** 
 686:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_DACCVIOL_Pos              (SCB_SHCSR_MEMFAULTACT_Pos + 1U)               /*!< SCB 
 687:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_DACCVIOL_Msk              (1UL << SCB_CFSR_DACCVIOL_Pos)                 /*!< SCB 
 688:Drivers/CMSIS/Include/core_cm7.h **** 
 689:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_IACCVIOL_Pos              (SCB_SHCSR_MEMFAULTACT_Pos + 0U)               /*!< SCB 
 690:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_IACCVIOL_Msk              (1UL /*<< SCB_CFSR_IACCVIOL_Pos*/)             /*!< SCB 
 691:Drivers/CMSIS/Include/core_cm7.h **** 
 692:Drivers/CMSIS/Include/core_cm7.h **** /* BusFault Status Register (part of SCB Configurable Fault Status Register) */
 693:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_BFARVALID_Pos            (SCB_CFSR_BUSFAULTSR_Pos + 7U)                  /*!< SCB 
 694:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_BFARVALID_Msk            (1UL << SCB_CFSR_BFARVALID_Pos)                 /*!< SCB 
 695:Drivers/CMSIS/Include/core_cm7.h **** 
 696:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_LSPERR_Pos               (SCB_CFSR_BUSFAULTSR_Pos + 5U)                  /*!< SCB 
 697:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_LSPERR_Msk               (1UL << SCB_CFSR_LSPERR_Pos)                    /*!< SCB 
 698:Drivers/CMSIS/Include/core_cm7.h **** 
 699:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_STKERR_Pos               (SCB_CFSR_BUSFAULTSR_Pos + 4U)                  /*!< SCB 
 700:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_STKERR_Msk               (1UL << SCB_CFSR_STKERR_Pos)                    /*!< SCB 
 701:Drivers/CMSIS/Include/core_cm7.h **** 
 702:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_UNSTKERR_Pos             (SCB_CFSR_BUSFAULTSR_Pos + 3U)                  /*!< SCB 
 703:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_UNSTKERR_Msk             (1UL << SCB_CFSR_UNSTKERR_Pos)                  /*!< SCB 
 704:Drivers/CMSIS/Include/core_cm7.h **** 
 705:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_IMPRECISERR_Pos          (SCB_CFSR_BUSFAULTSR_Pos + 2U)                  /*!< SCB 
 706:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_IMPRECISERR_Msk          (1UL << SCB_CFSR_IMPRECISERR_Pos)               /*!< SCB 
 707:Drivers/CMSIS/Include/core_cm7.h **** 
 708:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_PRECISERR_Pos            (SCB_CFSR_BUSFAULTSR_Pos + 1U)                  /*!< SCB 
 709:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_PRECISERR_Msk            (1UL << SCB_CFSR_PRECISERR_Pos)                 /*!< SCB 
 710:Drivers/CMSIS/Include/core_cm7.h **** 
 711:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_IBUSERR_Pos              (SCB_CFSR_BUSFAULTSR_Pos + 0U)                  /*!< SCB 
 712:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_IBUSERR_Msk              (1UL << SCB_CFSR_IBUSERR_Pos)                   /*!< SCB 
 713:Drivers/CMSIS/Include/core_cm7.h **** 
 714:Drivers/CMSIS/Include/core_cm7.h **** /* UsageFault Status Register (part of SCB Configurable Fault Status Register) */
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 14


 715:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_DIVBYZERO_Pos            (SCB_CFSR_USGFAULTSR_Pos + 9U)                  /*!< SCB 
 716:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_DIVBYZERO_Msk            (1UL << SCB_CFSR_DIVBYZERO_Pos)                 /*!< SCB 
 717:Drivers/CMSIS/Include/core_cm7.h **** 
 718:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_UNALIGNED_Pos            (SCB_CFSR_USGFAULTSR_Pos + 8U)                  /*!< SCB 
 719:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_UNALIGNED_Msk            (1UL << SCB_CFSR_UNALIGNED_Pos)                 /*!< SCB 
 720:Drivers/CMSIS/Include/core_cm7.h **** 
 721:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_NOCP_Pos                 (SCB_CFSR_USGFAULTSR_Pos + 3U)                  /*!< SCB 
 722:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_NOCP_Msk                 (1UL << SCB_CFSR_NOCP_Pos)                      /*!< SCB 
 723:Drivers/CMSIS/Include/core_cm7.h **** 
 724:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_INVPC_Pos                (SCB_CFSR_USGFAULTSR_Pos + 2U)                  /*!< SCB 
 725:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_INVPC_Msk                (1UL << SCB_CFSR_INVPC_Pos)                     /*!< SCB 
 726:Drivers/CMSIS/Include/core_cm7.h **** 
 727:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_INVSTATE_Pos             (SCB_CFSR_USGFAULTSR_Pos + 1U)                  /*!< SCB 
 728:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_INVSTATE_Msk             (1UL << SCB_CFSR_INVSTATE_Pos)                  /*!< SCB 
 729:Drivers/CMSIS/Include/core_cm7.h **** 
 730:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_UNDEFINSTR_Pos           (SCB_CFSR_USGFAULTSR_Pos + 0U)                  /*!< SCB 
 731:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_UNDEFINSTR_Msk           (1UL << SCB_CFSR_UNDEFINSTR_Pos)                /*!< SCB 
 732:Drivers/CMSIS/Include/core_cm7.h **** 
 733:Drivers/CMSIS/Include/core_cm7.h **** /* SCB Hard Fault Status Register Definitions */
 734:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_HFSR_DEBUGEVT_Pos              31U                                            /*!< SCB 
 735:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_HFSR_DEBUGEVT_Msk              (1UL << SCB_HFSR_DEBUGEVT_Pos)                 /*!< SCB 
 736:Drivers/CMSIS/Include/core_cm7.h **** 
 737:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_HFSR_FORCED_Pos                30U                                            /*!< SCB 
 738:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_HFSR_FORCED_Msk                (1UL << SCB_HFSR_FORCED_Pos)                   /*!< SCB 
 739:Drivers/CMSIS/Include/core_cm7.h **** 
 740:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_HFSR_VECTTBL_Pos                1U                                            /*!< SCB 
 741:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_HFSR_VECTTBL_Msk               (1UL << SCB_HFSR_VECTTBL_Pos)                  /*!< SCB 
 742:Drivers/CMSIS/Include/core_cm7.h **** 
 743:Drivers/CMSIS/Include/core_cm7.h **** /* SCB Debug Fault Status Register Definitions */
 744:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_DFSR_EXTERNAL_Pos               4U                                            /*!< SCB 
 745:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_DFSR_EXTERNAL_Msk              (1UL << SCB_DFSR_EXTERNAL_Pos)                 /*!< SCB 
 746:Drivers/CMSIS/Include/core_cm7.h **** 
 747:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_DFSR_VCATCH_Pos                 3U                                            /*!< SCB 
 748:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_DFSR_VCATCH_Msk                (1UL << SCB_DFSR_VCATCH_Pos)                   /*!< SCB 
 749:Drivers/CMSIS/Include/core_cm7.h **** 
 750:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_DFSR_DWTTRAP_Pos                2U                                            /*!< SCB 
 751:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_DFSR_DWTTRAP_Msk               (1UL << SCB_DFSR_DWTTRAP_Pos)                  /*!< SCB 
 752:Drivers/CMSIS/Include/core_cm7.h **** 
 753:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_DFSR_BKPT_Pos                   1U                                            /*!< SCB 
 754:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_DFSR_BKPT_Msk                  (1UL << SCB_DFSR_BKPT_Pos)                     /*!< SCB 
 755:Drivers/CMSIS/Include/core_cm7.h **** 
 756:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_DFSR_HALTED_Pos                 0U                                            /*!< SCB 
 757:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_DFSR_HALTED_Msk                (1UL /*<< SCB_DFSR_HALTED_Pos*/)               /*!< SCB 
 758:Drivers/CMSIS/Include/core_cm7.h **** 
 759:Drivers/CMSIS/Include/core_cm7.h **** /* SCB Cache Level ID Register Definitions */
 760:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CLIDR_LOUU_Pos                 27U                                            /*!< SCB 
 761:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CLIDR_LOUU_Msk                 (7UL << SCB_CLIDR_LOUU_Pos)                    /*!< SCB 
 762:Drivers/CMSIS/Include/core_cm7.h **** 
 763:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CLIDR_LOC_Pos                  24U                                            /*!< SCB 
 764:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CLIDR_LOC_Msk                  (7UL << SCB_CLIDR_LOC_Pos)                     /*!< SCB 
 765:Drivers/CMSIS/Include/core_cm7.h **** 
 766:Drivers/CMSIS/Include/core_cm7.h **** /* SCB Cache Type Register Definitions */
 767:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CTR_FORMAT_Pos                 29U                                            /*!< SCB 
 768:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CTR_FORMAT_Msk                 (7UL << SCB_CTR_FORMAT_Pos)                    /*!< SCB 
 769:Drivers/CMSIS/Include/core_cm7.h **** 
 770:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CTR_CWG_Pos                    24U                                            /*!< SCB 
 771:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CTR_CWG_Msk                    (0xFUL << SCB_CTR_CWG_Pos)                     /*!< SCB 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 15


 772:Drivers/CMSIS/Include/core_cm7.h **** 
 773:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CTR_ERG_Pos                    20U                                            /*!< SCB 
 774:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CTR_ERG_Msk                    (0xFUL << SCB_CTR_ERG_Pos)                     /*!< SCB 
 775:Drivers/CMSIS/Include/core_cm7.h **** 
 776:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CTR_DMINLINE_Pos               16U                                            /*!< SCB 
 777:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CTR_DMINLINE_Msk               (0xFUL << SCB_CTR_DMINLINE_Pos)                /*!< SCB 
 778:Drivers/CMSIS/Include/core_cm7.h **** 
 779:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CTR_IMINLINE_Pos                0U                                            /*!< SCB 
 780:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CTR_IMINLINE_Msk               (0xFUL /*<< SCB_CTR_IMINLINE_Pos*/)            /*!< SCB 
 781:Drivers/CMSIS/Include/core_cm7.h **** 
 782:Drivers/CMSIS/Include/core_cm7.h **** /* SCB Cache Size ID Register Definitions */
 783:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CCSIDR_WT_Pos                  31U                                            /*!< SCB 
 784:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CCSIDR_WT_Msk                  (1UL << SCB_CCSIDR_WT_Pos)                     /*!< SCB 
 785:Drivers/CMSIS/Include/core_cm7.h **** 
 786:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CCSIDR_WB_Pos                  30U                                            /*!< SCB 
 787:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CCSIDR_WB_Msk                  (1UL << SCB_CCSIDR_WB_Pos)                     /*!< SCB 
 788:Drivers/CMSIS/Include/core_cm7.h **** 
 789:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CCSIDR_RA_Pos                  29U                                            /*!< SCB 
 790:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CCSIDR_RA_Msk                  (1UL << SCB_CCSIDR_RA_Pos)                     /*!< SCB 
 791:Drivers/CMSIS/Include/core_cm7.h **** 
 792:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CCSIDR_WA_Pos                  28U                                            /*!< SCB 
 793:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CCSIDR_WA_Msk                  (1UL << SCB_CCSIDR_WA_Pos)                     /*!< SCB 
 794:Drivers/CMSIS/Include/core_cm7.h **** 
 795:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CCSIDR_NUMSETS_Pos             13U                                            /*!< SCB 
 796:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CCSIDR_NUMSETS_Msk             (0x7FFFUL << SCB_CCSIDR_NUMSETS_Pos)           /*!< SCB 
 797:Drivers/CMSIS/Include/core_cm7.h **** 
 798:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CCSIDR_ASSOCIATIVITY_Pos        3U                                            /*!< SCB 
 799:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CCSIDR_ASSOCIATIVITY_Msk       (0x3FFUL << SCB_CCSIDR_ASSOCIATIVITY_Pos)      /*!< SCB 
 800:Drivers/CMSIS/Include/core_cm7.h **** 
 801:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CCSIDR_LINESIZE_Pos             0U                                            /*!< SCB 
 802:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CCSIDR_LINESIZE_Msk            (7UL /*<< SCB_CCSIDR_LINESIZE_Pos*/)           /*!< SCB 
 803:Drivers/CMSIS/Include/core_cm7.h **** 
 804:Drivers/CMSIS/Include/core_cm7.h **** /* SCB Cache Size Selection Register Definitions */
 805:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CSSELR_LEVEL_Pos                1U                                            /*!< SCB 
 806:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CSSELR_LEVEL_Msk               (7UL << SCB_CSSELR_LEVEL_Pos)                  /*!< SCB 
 807:Drivers/CMSIS/Include/core_cm7.h **** 
 808:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CSSELR_IND_Pos                  0U                                            /*!< SCB 
 809:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CSSELR_IND_Msk                 (1UL /*<< SCB_CSSELR_IND_Pos*/)                /*!< SCB 
 810:Drivers/CMSIS/Include/core_cm7.h **** 
 811:Drivers/CMSIS/Include/core_cm7.h **** /* SCB Software Triggered Interrupt Register Definitions */
 812:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_STIR_INTID_Pos                  0U                                            /*!< SCB 
 813:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_STIR_INTID_Msk                 (0x1FFUL /*<< SCB_STIR_INTID_Pos*/)            /*!< SCB 
 814:Drivers/CMSIS/Include/core_cm7.h **** 
 815:Drivers/CMSIS/Include/core_cm7.h **** /* SCB D-Cache Invalidate by Set-way Register Definitions */
 816:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_DCISW_WAY_Pos                  30U                                            /*!< SCB 
 817:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_DCISW_WAY_Msk                  (3UL << SCB_DCISW_WAY_Pos)                     /*!< SCB 
 818:Drivers/CMSIS/Include/core_cm7.h **** 
 819:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_DCISW_SET_Pos                   5U                                            /*!< SCB 
 820:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_DCISW_SET_Msk                  (0x1FFUL << SCB_DCISW_SET_Pos)                 /*!< SCB 
 821:Drivers/CMSIS/Include/core_cm7.h **** 
 822:Drivers/CMSIS/Include/core_cm7.h **** /* SCB D-Cache Clean by Set-way Register Definitions */
 823:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_DCCSW_WAY_Pos                  30U                                            /*!< SCB 
 824:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_DCCSW_WAY_Msk                  (3UL << SCB_DCCSW_WAY_Pos)                     /*!< SCB 
 825:Drivers/CMSIS/Include/core_cm7.h **** 
 826:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_DCCSW_SET_Pos                   5U                                            /*!< SCB 
 827:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_DCCSW_SET_Msk                  (0x1FFUL << SCB_DCCSW_SET_Pos)                 /*!< SCB 
 828:Drivers/CMSIS/Include/core_cm7.h **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 16


 829:Drivers/CMSIS/Include/core_cm7.h **** /* SCB D-Cache Clean and Invalidate by Set-way Register Definitions */
 830:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_DCCISW_WAY_Pos                 30U                                            /*!< SCB 
 831:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_DCCISW_WAY_Msk                 (3UL << SCB_DCCISW_WAY_Pos)                    /*!< SCB 
 832:Drivers/CMSIS/Include/core_cm7.h **** 
 833:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_DCCISW_SET_Pos                  5U                                            /*!< SCB 
 834:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_DCCISW_SET_Msk                 (0x1FFUL << SCB_DCCISW_SET_Pos)                /*!< SCB 
 835:Drivers/CMSIS/Include/core_cm7.h **** 
 836:Drivers/CMSIS/Include/core_cm7.h **** /* Instruction Tightly-Coupled Memory Control Register Definitions */
 837:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ITCMCR_SZ_Pos                   3U                                            /*!< SCB 
 838:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ITCMCR_SZ_Msk                  (0xFUL << SCB_ITCMCR_SZ_Pos)                   /*!< SCB 
 839:Drivers/CMSIS/Include/core_cm7.h **** 
 840:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ITCMCR_RETEN_Pos                2U                                            /*!< SCB 
 841:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ITCMCR_RETEN_Msk               (1UL << SCB_ITCMCR_RETEN_Pos)                  /*!< SCB 
 842:Drivers/CMSIS/Include/core_cm7.h **** 
 843:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ITCMCR_RMW_Pos                  1U                                            /*!< SCB 
 844:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ITCMCR_RMW_Msk                 (1UL << SCB_ITCMCR_RMW_Pos)                    /*!< SCB 
 845:Drivers/CMSIS/Include/core_cm7.h **** 
 846:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ITCMCR_EN_Pos                   0U                                            /*!< SCB 
 847:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ITCMCR_EN_Msk                  (1UL /*<< SCB_ITCMCR_EN_Pos*/)                 /*!< SCB 
 848:Drivers/CMSIS/Include/core_cm7.h **** 
 849:Drivers/CMSIS/Include/core_cm7.h **** /* Data Tightly-Coupled Memory Control Register Definitions */
 850:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_DTCMCR_SZ_Pos                   3U                                            /*!< SCB 
 851:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_DTCMCR_SZ_Msk                  (0xFUL << SCB_DTCMCR_SZ_Pos)                   /*!< SCB 
 852:Drivers/CMSIS/Include/core_cm7.h **** 
 853:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_DTCMCR_RETEN_Pos                2U                                            /*!< SCB 
 854:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_DTCMCR_RETEN_Msk               (1UL << SCB_DTCMCR_RETEN_Pos)                   /*!< SCB
 855:Drivers/CMSIS/Include/core_cm7.h **** 
 856:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_DTCMCR_RMW_Pos                  1U                                            /*!< SCB 
 857:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_DTCMCR_RMW_Msk                 (1UL << SCB_DTCMCR_RMW_Pos)                    /*!< SCB 
 858:Drivers/CMSIS/Include/core_cm7.h **** 
 859:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_DTCMCR_EN_Pos                   0U                                            /*!< SCB 
 860:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_DTCMCR_EN_Msk                  (1UL /*<< SCB_DTCMCR_EN_Pos*/)                 /*!< SCB 
 861:Drivers/CMSIS/Include/core_cm7.h **** 
 862:Drivers/CMSIS/Include/core_cm7.h **** /* AHBP Control Register Definitions */
 863:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_AHBPCR_SZ_Pos                   1U                                            /*!< SCB 
 864:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_AHBPCR_SZ_Msk                  (7UL << SCB_AHBPCR_SZ_Pos)                     /*!< SCB 
 865:Drivers/CMSIS/Include/core_cm7.h **** 
 866:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_AHBPCR_EN_Pos                   0U                                            /*!< SCB 
 867:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_AHBPCR_EN_Msk                  (1UL /*<< SCB_AHBPCR_EN_Pos*/)                 /*!< SCB 
 868:Drivers/CMSIS/Include/core_cm7.h **** 
 869:Drivers/CMSIS/Include/core_cm7.h **** /* L1 Cache Control Register Definitions */
 870:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CACR_FORCEWT_Pos                2U                                            /*!< SCB 
 871:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CACR_FORCEWT_Msk               (1UL << SCB_CACR_FORCEWT_Pos)                  /*!< SCB 
 872:Drivers/CMSIS/Include/core_cm7.h **** 
 873:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CACR_ECCEN_Pos                  1U                                            /*!< SCB 
 874:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CACR_ECCEN_Msk                 (1UL << SCB_CACR_ECCEN_Pos)                    /*!< SCB 
 875:Drivers/CMSIS/Include/core_cm7.h **** 
 876:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CACR_SIWT_Pos                   0U                                            /*!< SCB 
 877:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CACR_SIWT_Msk                  (1UL /*<< SCB_CACR_SIWT_Pos*/)                 /*!< SCB 
 878:Drivers/CMSIS/Include/core_cm7.h **** 
 879:Drivers/CMSIS/Include/core_cm7.h **** /* AHBS Control Register Definitions */
 880:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_AHBSCR_INITCOUNT_Pos           11U                                            /*!< SCB 
 881:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_AHBSCR_INITCOUNT_Msk           (0x1FUL << SCB_AHBPCR_INITCOUNT_Pos)           /*!< SCB 
 882:Drivers/CMSIS/Include/core_cm7.h **** 
 883:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_AHBSCR_TPRI_Pos                 2U                                            /*!< SCB 
 884:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_AHBSCR_TPRI_Msk                (0x1FFUL << SCB_AHBPCR_TPRI_Pos)               /*!< SCB 
 885:Drivers/CMSIS/Include/core_cm7.h **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 17


 886:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_AHBSCR_CTL_Pos                  0U                                            /*!< SCB 
 887:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_AHBSCR_CTL_Msk                 (3UL /*<< SCB_AHBPCR_CTL_Pos*/)                /*!< SCB 
 888:Drivers/CMSIS/Include/core_cm7.h **** 
 889:Drivers/CMSIS/Include/core_cm7.h **** /* Auxiliary Bus Fault Status Register Definitions */
 890:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ABFSR_AXIMTYPE_Pos              8U                                            /*!< SCB 
 891:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ABFSR_AXIMTYPE_Msk             (3UL << SCB_ABFSR_AXIMTYPE_Pos)                /*!< SCB 
 892:Drivers/CMSIS/Include/core_cm7.h **** 
 893:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ABFSR_EPPB_Pos                  4U                                            /*!< SCB 
 894:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ABFSR_EPPB_Msk                 (1UL << SCB_ABFSR_EPPB_Pos)                    /*!< SCB 
 895:Drivers/CMSIS/Include/core_cm7.h **** 
 896:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ABFSR_AXIM_Pos                  3U                                            /*!< SCB 
 897:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ABFSR_AXIM_Msk                 (1UL << SCB_ABFSR_AXIM_Pos)                    /*!< SCB 
 898:Drivers/CMSIS/Include/core_cm7.h **** 
 899:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ABFSR_AHBP_Pos                  2U                                            /*!< SCB 
 900:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ABFSR_AHBP_Msk                 (1UL << SCB_ABFSR_AHBP_Pos)                    /*!< SCB 
 901:Drivers/CMSIS/Include/core_cm7.h **** 
 902:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ABFSR_DTCM_Pos                  1U                                            /*!< SCB 
 903:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ABFSR_DTCM_Msk                 (1UL << SCB_ABFSR_DTCM_Pos)                    /*!< SCB 
 904:Drivers/CMSIS/Include/core_cm7.h **** 
 905:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ABFSR_ITCM_Pos                  0U                                            /*!< SCB 
 906:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ABFSR_ITCM_Msk                 (1UL /*<< SCB_ABFSR_ITCM_Pos*/)                /*!< SCB 
 907:Drivers/CMSIS/Include/core_cm7.h **** 
 908:Drivers/CMSIS/Include/core_cm7.h **** /*@} end of group CMSIS_SCB */
 909:Drivers/CMSIS/Include/core_cm7.h **** 
 910:Drivers/CMSIS/Include/core_cm7.h **** 
 911:Drivers/CMSIS/Include/core_cm7.h **** /**
 912:Drivers/CMSIS/Include/core_cm7.h ****   \ingroup  CMSIS_core_register
 913:Drivers/CMSIS/Include/core_cm7.h ****   \defgroup CMSIS_SCnSCB System Controls not in SCB (SCnSCB)
 914:Drivers/CMSIS/Include/core_cm7.h ****   \brief    Type definitions for the System Control and ID Register not in the SCB
 915:Drivers/CMSIS/Include/core_cm7.h ****   @{
 916:Drivers/CMSIS/Include/core_cm7.h ****  */
 917:Drivers/CMSIS/Include/core_cm7.h **** 
 918:Drivers/CMSIS/Include/core_cm7.h **** /**
 919:Drivers/CMSIS/Include/core_cm7.h ****   \brief  Structure type to access the System Control and ID Register not in the SCB.
 920:Drivers/CMSIS/Include/core_cm7.h ****  */
 921:Drivers/CMSIS/Include/core_cm7.h **** typedef struct
 922:Drivers/CMSIS/Include/core_cm7.h **** {
 923:Drivers/CMSIS/Include/core_cm7.h ****         uint32_t RESERVED0[1U];
 924:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t ICTR;                   /*!< Offset: 0x004 (R/ )  Interrupt Controller Type Regist
 925:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t ACTLR;                  /*!< Offset: 0x008 (R/W)  Auxiliary Control Register */
 926:Drivers/CMSIS/Include/core_cm7.h **** } SCnSCB_Type;
 927:Drivers/CMSIS/Include/core_cm7.h **** 
 928:Drivers/CMSIS/Include/core_cm7.h **** /* Interrupt Controller Type Register Definitions */
 929:Drivers/CMSIS/Include/core_cm7.h **** #define SCnSCB_ICTR_INTLINESNUM_Pos         0U                                         /*!< ICTR: I
 930:Drivers/CMSIS/Include/core_cm7.h **** #define SCnSCB_ICTR_INTLINESNUM_Msk        (0xFUL /*<< SCnSCB_ICTR_INTLINESNUM_Pos*/)  /*!< ICTR: I
 931:Drivers/CMSIS/Include/core_cm7.h **** 
 932:Drivers/CMSIS/Include/core_cm7.h **** /* Auxiliary Control Register Definitions */
 933:Drivers/CMSIS/Include/core_cm7.h **** #define SCnSCB_ACTLR_DISDYNADD_Pos         26U                                         /*!< ACTLR: 
 934:Drivers/CMSIS/Include/core_cm7.h **** #define SCnSCB_ACTLR_DISDYNADD_Msk         (1UL << SCnSCB_ACTLR_DISDYNADD_Pos)         /*!< ACTLR: 
 935:Drivers/CMSIS/Include/core_cm7.h **** 
 936:Drivers/CMSIS/Include/core_cm7.h **** #define SCnSCB_ACTLR_DISISSCH1_Pos         21U                                         /*!< ACTLR: 
 937:Drivers/CMSIS/Include/core_cm7.h **** #define SCnSCB_ACTLR_DISISSCH1_Msk         (0x1FUL << SCnSCB_ACTLR_DISISSCH1_Pos)      /*!< ACTLR: 
 938:Drivers/CMSIS/Include/core_cm7.h **** 
 939:Drivers/CMSIS/Include/core_cm7.h **** #define SCnSCB_ACTLR_DISDI_Pos             16U                                         /*!< ACTLR: 
 940:Drivers/CMSIS/Include/core_cm7.h **** #define SCnSCB_ACTLR_DISDI_Msk             (0x1FUL << SCnSCB_ACTLR_DISDI_Pos)          /*!< ACTLR: 
 941:Drivers/CMSIS/Include/core_cm7.h **** 
 942:Drivers/CMSIS/Include/core_cm7.h **** #define SCnSCB_ACTLR_DISCRITAXIRUR_Pos     15U                                         /*!< ACTLR: 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 18


 943:Drivers/CMSIS/Include/core_cm7.h **** #define SCnSCB_ACTLR_DISCRITAXIRUR_Msk     (1UL << SCnSCB_ACTLR_DISCRITAXIRUR_Pos)     /*!< ACTLR: 
 944:Drivers/CMSIS/Include/core_cm7.h **** 
 945:Drivers/CMSIS/Include/core_cm7.h **** #define SCnSCB_ACTLR_DISBTACALLOC_Pos      14U                                         /*!< ACTLR: 
 946:Drivers/CMSIS/Include/core_cm7.h **** #define SCnSCB_ACTLR_DISBTACALLOC_Msk      (1UL << SCnSCB_ACTLR_DISBTACALLOC_Pos)      /*!< ACTLR: 
 947:Drivers/CMSIS/Include/core_cm7.h **** 
 948:Drivers/CMSIS/Include/core_cm7.h **** #define SCnSCB_ACTLR_DISBTACREAD_Pos       13U                                         /*!< ACTLR: 
 949:Drivers/CMSIS/Include/core_cm7.h **** #define SCnSCB_ACTLR_DISBTACREAD_Msk       (1UL << SCnSCB_ACTLR_DISBTACREAD_Pos)       /*!< ACTLR: 
 950:Drivers/CMSIS/Include/core_cm7.h **** 
 951:Drivers/CMSIS/Include/core_cm7.h **** #define SCnSCB_ACTLR_DISITMATBFLUSH_Pos    12U                                         /*!< ACTLR: 
 952:Drivers/CMSIS/Include/core_cm7.h **** #define SCnSCB_ACTLR_DISITMATBFLUSH_Msk    (1UL << SCnSCB_ACTLR_DISITMATBFLUSH_Pos)    /*!< ACTLR: 
 953:Drivers/CMSIS/Include/core_cm7.h **** 
 954:Drivers/CMSIS/Include/core_cm7.h **** #define SCnSCB_ACTLR_DISRAMODE_Pos         11U                                         /*!< ACTLR: 
 955:Drivers/CMSIS/Include/core_cm7.h **** #define SCnSCB_ACTLR_DISRAMODE_Msk         (1UL << SCnSCB_ACTLR_DISRAMODE_Pos)         /*!< ACTLR: 
 956:Drivers/CMSIS/Include/core_cm7.h **** 
 957:Drivers/CMSIS/Include/core_cm7.h **** #define SCnSCB_ACTLR_FPEXCODIS_Pos         10U                                         /*!< ACTLR: 
 958:Drivers/CMSIS/Include/core_cm7.h **** #define SCnSCB_ACTLR_FPEXCODIS_Msk         (1UL << SCnSCB_ACTLR_FPEXCODIS_Pos)         /*!< ACTLR: 
 959:Drivers/CMSIS/Include/core_cm7.h **** 
 960:Drivers/CMSIS/Include/core_cm7.h **** #define SCnSCB_ACTLR_DISFOLD_Pos            2U                                         /*!< ACTLR: 
 961:Drivers/CMSIS/Include/core_cm7.h **** #define SCnSCB_ACTLR_DISFOLD_Msk           (1UL << SCnSCB_ACTLR_DISFOLD_Pos)           /*!< ACTLR: 
 962:Drivers/CMSIS/Include/core_cm7.h **** 
 963:Drivers/CMSIS/Include/core_cm7.h **** #define SCnSCB_ACTLR_DISMCYCINT_Pos         0U                                         /*!< ACTLR: 
 964:Drivers/CMSIS/Include/core_cm7.h **** #define SCnSCB_ACTLR_DISMCYCINT_Msk        (1UL /*<< SCnSCB_ACTLR_DISMCYCINT_Pos*/)    /*!< ACTLR: 
 965:Drivers/CMSIS/Include/core_cm7.h **** 
 966:Drivers/CMSIS/Include/core_cm7.h **** /*@} end of group CMSIS_SCnotSCB */
 967:Drivers/CMSIS/Include/core_cm7.h **** 
 968:Drivers/CMSIS/Include/core_cm7.h **** 
 969:Drivers/CMSIS/Include/core_cm7.h **** /**
 970:Drivers/CMSIS/Include/core_cm7.h ****   \ingroup  CMSIS_core_register
 971:Drivers/CMSIS/Include/core_cm7.h ****   \defgroup CMSIS_SysTick     System Tick Timer (SysTick)
 972:Drivers/CMSIS/Include/core_cm7.h ****   \brief    Type definitions for the System Timer Registers.
 973:Drivers/CMSIS/Include/core_cm7.h ****   @{
 974:Drivers/CMSIS/Include/core_cm7.h ****  */
 975:Drivers/CMSIS/Include/core_cm7.h **** 
 976:Drivers/CMSIS/Include/core_cm7.h **** /**
 977:Drivers/CMSIS/Include/core_cm7.h ****   \brief  Structure type to access the System Timer (SysTick).
 978:Drivers/CMSIS/Include/core_cm7.h ****  */
 979:Drivers/CMSIS/Include/core_cm7.h **** typedef struct
 980:Drivers/CMSIS/Include/core_cm7.h **** {
 981:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t CTRL;                   /*!< Offset: 0x000 (R/W)  SysTick Control and Status Regis
 982:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t LOAD;                   /*!< Offset: 0x004 (R/W)  SysTick Reload Value Register */
 983:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t VAL;                    /*!< Offset: 0x008 (R/W)  SysTick Current Value Register *
 984:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t CALIB;                  /*!< Offset: 0x00C (R/ )  SysTick Calibration Register */
 985:Drivers/CMSIS/Include/core_cm7.h **** } SysTick_Type;
 986:Drivers/CMSIS/Include/core_cm7.h **** 
 987:Drivers/CMSIS/Include/core_cm7.h **** /* SysTick Control / Status Register Definitions */
 988:Drivers/CMSIS/Include/core_cm7.h **** #define SysTick_CTRL_COUNTFLAG_Pos         16U                                            /*!< SysT
 989:Drivers/CMSIS/Include/core_cm7.h **** #define SysTick_CTRL_COUNTFLAG_Msk         (1UL << SysTick_CTRL_COUNTFLAG_Pos)            /*!< SysT
 990:Drivers/CMSIS/Include/core_cm7.h **** 
 991:Drivers/CMSIS/Include/core_cm7.h **** #define SysTick_CTRL_CLKSOURCE_Pos          2U                                            /*!< SysT
 992:Drivers/CMSIS/Include/core_cm7.h **** #define SysTick_CTRL_CLKSOURCE_Msk         (1UL << SysTick_CTRL_CLKSOURCE_Pos)            /*!< SysT
 993:Drivers/CMSIS/Include/core_cm7.h **** 
 994:Drivers/CMSIS/Include/core_cm7.h **** #define SysTick_CTRL_TICKINT_Pos            1U                                            /*!< SysT
 995:Drivers/CMSIS/Include/core_cm7.h **** #define SysTick_CTRL_TICKINT_Msk           (1UL << SysTick_CTRL_TICKINT_Pos)              /*!< SysT
 996:Drivers/CMSIS/Include/core_cm7.h **** 
 997:Drivers/CMSIS/Include/core_cm7.h **** #define SysTick_CTRL_ENABLE_Pos             0U                                            /*!< SysT
 998:Drivers/CMSIS/Include/core_cm7.h **** #define SysTick_CTRL_ENABLE_Msk            (1UL /*<< SysTick_CTRL_ENABLE_Pos*/)           /*!< SysT
 999:Drivers/CMSIS/Include/core_cm7.h **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 19


1000:Drivers/CMSIS/Include/core_cm7.h **** /* SysTick Reload Register Definitions */
1001:Drivers/CMSIS/Include/core_cm7.h **** #define SysTick_LOAD_RELOAD_Pos             0U                                            /*!< SysT
1002:Drivers/CMSIS/Include/core_cm7.h **** #define SysTick_LOAD_RELOAD_Msk            (0xFFFFFFUL /*<< SysTick_LOAD_RELOAD_Pos*/)    /*!< SysT
1003:Drivers/CMSIS/Include/core_cm7.h **** 
1004:Drivers/CMSIS/Include/core_cm7.h **** /* SysTick Current Register Definitions */
1005:Drivers/CMSIS/Include/core_cm7.h **** #define SysTick_VAL_CURRENT_Pos             0U                                            /*!< SysT
1006:Drivers/CMSIS/Include/core_cm7.h **** #define SysTick_VAL_CURRENT_Msk            (0xFFFFFFUL /*<< SysTick_VAL_CURRENT_Pos*/)    /*!< SysT
1007:Drivers/CMSIS/Include/core_cm7.h **** 
1008:Drivers/CMSIS/Include/core_cm7.h **** /* SysTick Calibration Register Definitions */
1009:Drivers/CMSIS/Include/core_cm7.h **** #define SysTick_CALIB_NOREF_Pos            31U                                            /*!< SysT
1010:Drivers/CMSIS/Include/core_cm7.h **** #define SysTick_CALIB_NOREF_Msk            (1UL << SysTick_CALIB_NOREF_Pos)               /*!< SysT
1011:Drivers/CMSIS/Include/core_cm7.h **** 
1012:Drivers/CMSIS/Include/core_cm7.h **** #define SysTick_CALIB_SKEW_Pos             30U                                            /*!< SysT
1013:Drivers/CMSIS/Include/core_cm7.h **** #define SysTick_CALIB_SKEW_Msk             (1UL << SysTick_CALIB_SKEW_Pos)                /*!< SysT
1014:Drivers/CMSIS/Include/core_cm7.h **** 
1015:Drivers/CMSIS/Include/core_cm7.h **** #define SysTick_CALIB_TENMS_Pos             0U                                            /*!< SysT
1016:Drivers/CMSIS/Include/core_cm7.h **** #define SysTick_CALIB_TENMS_Msk            (0xFFFFFFUL /*<< SysTick_CALIB_TENMS_Pos*/)    /*!< SysT
1017:Drivers/CMSIS/Include/core_cm7.h **** 
1018:Drivers/CMSIS/Include/core_cm7.h **** /*@} end of group CMSIS_SysTick */
1019:Drivers/CMSIS/Include/core_cm7.h **** 
1020:Drivers/CMSIS/Include/core_cm7.h **** 
1021:Drivers/CMSIS/Include/core_cm7.h **** /**
1022:Drivers/CMSIS/Include/core_cm7.h ****   \ingroup  CMSIS_core_register
1023:Drivers/CMSIS/Include/core_cm7.h ****   \defgroup CMSIS_ITM     Instrumentation Trace Macrocell (ITM)
1024:Drivers/CMSIS/Include/core_cm7.h ****   \brief    Type definitions for the Instrumentation Trace Macrocell (ITM)
1025:Drivers/CMSIS/Include/core_cm7.h ****   @{
1026:Drivers/CMSIS/Include/core_cm7.h ****  */
1027:Drivers/CMSIS/Include/core_cm7.h **** 
1028:Drivers/CMSIS/Include/core_cm7.h **** /**
1029:Drivers/CMSIS/Include/core_cm7.h ****   \brief  Structure type to access the Instrumentation Trace Macrocell Register (ITM).
1030:Drivers/CMSIS/Include/core_cm7.h ****  */
1031:Drivers/CMSIS/Include/core_cm7.h **** typedef struct
1032:Drivers/CMSIS/Include/core_cm7.h **** {
1033:Drivers/CMSIS/Include/core_cm7.h ****   __OM  union
1034:Drivers/CMSIS/Include/core_cm7.h ****   {
1035:Drivers/CMSIS/Include/core_cm7.h ****     __OM  uint8_t    u8;                 /*!< Offset: 0x000 ( /W)  ITM Stimulus Port 8-bit */
1036:Drivers/CMSIS/Include/core_cm7.h ****     __OM  uint16_t   u16;                /*!< Offset: 0x000 ( /W)  ITM Stimulus Port 16-bit */
1037:Drivers/CMSIS/Include/core_cm7.h ****     __OM  uint32_t   u32;                /*!< Offset: 0x000 ( /W)  ITM Stimulus Port 32-bit */
1038:Drivers/CMSIS/Include/core_cm7.h ****   }  PORT [32U];                         /*!< Offset: 0x000 ( /W)  ITM Stimulus Port Registers */
1039:Drivers/CMSIS/Include/core_cm7.h ****         uint32_t RESERVED0[864U];
1040:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t TER;                    /*!< Offset: 0xE00 (R/W)  ITM Trace Enable Register */
1041:Drivers/CMSIS/Include/core_cm7.h ****         uint32_t RESERVED1[15U];
1042:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t TPR;                    /*!< Offset: 0xE40 (R/W)  ITM Trace Privilege Register */
1043:Drivers/CMSIS/Include/core_cm7.h ****         uint32_t RESERVED2[15U];
1044:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t TCR;                    /*!< Offset: 0xE80 (R/W)  ITM Trace Control Register */
1045:Drivers/CMSIS/Include/core_cm7.h ****         uint32_t RESERVED3[32U];
1046:Drivers/CMSIS/Include/core_cm7.h ****         uint32_t RESERVED4[43U];
1047:Drivers/CMSIS/Include/core_cm7.h ****   __OM  uint32_t LAR;                    /*!< Offset: 0xFB0 ( /W)  ITM Lock Access Register */
1048:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t LSR;                    /*!< Offset: 0xFB4 (R/ )  ITM Lock Status Register */
1049:Drivers/CMSIS/Include/core_cm7.h ****         uint32_t RESERVED5[6U];
1050:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t PID4;                   /*!< Offset: 0xFD0 (R/ )  ITM Peripheral Identification Re
1051:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t PID5;                   /*!< Offset: 0xFD4 (R/ )  ITM Peripheral Identification Re
1052:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t PID6;                   /*!< Offset: 0xFD8 (R/ )  ITM Peripheral Identification Re
1053:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t PID7;                   /*!< Offset: 0xFDC (R/ )  ITM Peripheral Identification Re
1054:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t PID0;                   /*!< Offset: 0xFE0 (R/ )  ITM Peripheral Identification Re
1055:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t PID1;                   /*!< Offset: 0xFE4 (R/ )  ITM Peripheral Identification Re
1056:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t PID2;                   /*!< Offset: 0xFE8 (R/ )  ITM Peripheral Identification Re
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 20


1057:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t PID3;                   /*!< Offset: 0xFEC (R/ )  ITM Peripheral Identification Re
1058:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t CID0;                   /*!< Offset: 0xFF0 (R/ )  ITM Component  Identification Re
1059:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t CID1;                   /*!< Offset: 0xFF4 (R/ )  ITM Component  Identification Re
1060:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t CID2;                   /*!< Offset: 0xFF8 (R/ )  ITM Component  Identification Re
1061:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t CID3;                   /*!< Offset: 0xFFC (R/ )  ITM Component  Identification Re
1062:Drivers/CMSIS/Include/core_cm7.h **** } ITM_Type;
1063:Drivers/CMSIS/Include/core_cm7.h **** 
1064:Drivers/CMSIS/Include/core_cm7.h **** /* ITM Trace Privilege Register Definitions */
1065:Drivers/CMSIS/Include/core_cm7.h **** #define ITM_TPR_PRIVMASK_Pos                0U                                            /*!< ITM 
1066:Drivers/CMSIS/Include/core_cm7.h **** #define ITM_TPR_PRIVMASK_Msk               (0xFFFFFFFFUL /*<< ITM_TPR_PRIVMASK_Pos*/)     /*!< ITM 
1067:Drivers/CMSIS/Include/core_cm7.h **** 
1068:Drivers/CMSIS/Include/core_cm7.h **** /* ITM Trace Control Register Definitions */
1069:Drivers/CMSIS/Include/core_cm7.h **** #define ITM_TCR_BUSY_Pos                   23U                                            /*!< ITM 
1070:Drivers/CMSIS/Include/core_cm7.h **** #define ITM_TCR_BUSY_Msk                   (1UL << ITM_TCR_BUSY_Pos)                      /*!< ITM 
1071:Drivers/CMSIS/Include/core_cm7.h **** 
1072:Drivers/CMSIS/Include/core_cm7.h **** #define ITM_TCR_TraceBusID_Pos             16U                                            /*!< ITM 
1073:Drivers/CMSIS/Include/core_cm7.h **** #define ITM_TCR_TraceBusID_Msk             (0x7FUL << ITM_TCR_TraceBusID_Pos)             /*!< ITM 
1074:Drivers/CMSIS/Include/core_cm7.h **** 
1075:Drivers/CMSIS/Include/core_cm7.h **** #define ITM_TCR_GTSFREQ_Pos                10U                                            /*!< ITM 
1076:Drivers/CMSIS/Include/core_cm7.h **** #define ITM_TCR_GTSFREQ_Msk                (3UL << ITM_TCR_GTSFREQ_Pos)                   /*!< ITM 
1077:Drivers/CMSIS/Include/core_cm7.h **** 
1078:Drivers/CMSIS/Include/core_cm7.h **** #define ITM_TCR_TSPrescale_Pos              8U                                            /*!< ITM 
1079:Drivers/CMSIS/Include/core_cm7.h **** #define ITM_TCR_TSPrescale_Msk             (3UL << ITM_TCR_TSPrescale_Pos)                /*!< ITM 
1080:Drivers/CMSIS/Include/core_cm7.h **** 
1081:Drivers/CMSIS/Include/core_cm7.h **** #define ITM_TCR_SWOENA_Pos                  4U                                            /*!< ITM 
1082:Drivers/CMSIS/Include/core_cm7.h **** #define ITM_TCR_SWOENA_Msk                 (1UL << ITM_TCR_SWOENA_Pos)                    /*!< ITM 
1083:Drivers/CMSIS/Include/core_cm7.h **** 
1084:Drivers/CMSIS/Include/core_cm7.h **** #define ITM_TCR_DWTENA_Pos                  3U                                            /*!< ITM 
1085:Drivers/CMSIS/Include/core_cm7.h **** #define ITM_TCR_DWTENA_Msk                 (1UL << ITM_TCR_DWTENA_Pos)                    /*!< ITM 
1086:Drivers/CMSIS/Include/core_cm7.h **** 
1087:Drivers/CMSIS/Include/core_cm7.h **** #define ITM_TCR_SYNCENA_Pos                 2U                                            /*!< ITM 
1088:Drivers/CMSIS/Include/core_cm7.h **** #define ITM_TCR_SYNCENA_Msk                (1UL << ITM_TCR_SYNCENA_Pos)                   /*!< ITM 
1089:Drivers/CMSIS/Include/core_cm7.h **** 
1090:Drivers/CMSIS/Include/core_cm7.h **** #define ITM_TCR_TSENA_Pos                   1U                                            /*!< ITM 
1091:Drivers/CMSIS/Include/core_cm7.h **** #define ITM_TCR_TSENA_Msk                  (1UL << ITM_TCR_TSENA_Pos)                     /*!< ITM 
1092:Drivers/CMSIS/Include/core_cm7.h **** 
1093:Drivers/CMSIS/Include/core_cm7.h **** #define ITM_TCR_ITMENA_Pos                  0U                                            /*!< ITM 
1094:Drivers/CMSIS/Include/core_cm7.h **** #define ITM_TCR_ITMENA_Msk                 (1UL /*<< ITM_TCR_ITMENA_Pos*/)                /*!< ITM 
1095:Drivers/CMSIS/Include/core_cm7.h **** 
1096:Drivers/CMSIS/Include/core_cm7.h **** /* ITM Lock Status Register Definitions */
1097:Drivers/CMSIS/Include/core_cm7.h **** #define ITM_LSR_ByteAcc_Pos                 2U                                            /*!< ITM 
1098:Drivers/CMSIS/Include/core_cm7.h **** #define ITM_LSR_ByteAcc_Msk                (1UL << ITM_LSR_ByteAcc_Pos)                   /*!< ITM 
1099:Drivers/CMSIS/Include/core_cm7.h **** 
1100:Drivers/CMSIS/Include/core_cm7.h **** #define ITM_LSR_Access_Pos                  1U                                            /*!< ITM 
1101:Drivers/CMSIS/Include/core_cm7.h **** #define ITM_LSR_Access_Msk                 (1UL << ITM_LSR_Access_Pos)                    /*!< ITM 
1102:Drivers/CMSIS/Include/core_cm7.h **** 
1103:Drivers/CMSIS/Include/core_cm7.h **** #define ITM_LSR_Present_Pos                 0U                                            /*!< ITM 
1104:Drivers/CMSIS/Include/core_cm7.h **** #define ITM_LSR_Present_Msk                (1UL /*<< ITM_LSR_Present_Pos*/)               /*!< ITM 
1105:Drivers/CMSIS/Include/core_cm7.h **** 
1106:Drivers/CMSIS/Include/core_cm7.h **** /*@}*/ /* end of group CMSIS_ITM */
1107:Drivers/CMSIS/Include/core_cm7.h **** 
1108:Drivers/CMSIS/Include/core_cm7.h **** 
1109:Drivers/CMSIS/Include/core_cm7.h **** /**
1110:Drivers/CMSIS/Include/core_cm7.h ****   \ingroup  CMSIS_core_register
1111:Drivers/CMSIS/Include/core_cm7.h ****   \defgroup CMSIS_DWT     Data Watchpoint and Trace (DWT)
1112:Drivers/CMSIS/Include/core_cm7.h ****   \brief    Type definitions for the Data Watchpoint and Trace (DWT)
1113:Drivers/CMSIS/Include/core_cm7.h ****   @{
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 21


1114:Drivers/CMSIS/Include/core_cm7.h ****  */
1115:Drivers/CMSIS/Include/core_cm7.h **** 
1116:Drivers/CMSIS/Include/core_cm7.h **** /**
1117:Drivers/CMSIS/Include/core_cm7.h ****   \brief  Structure type to access the Data Watchpoint and Trace Register (DWT).
1118:Drivers/CMSIS/Include/core_cm7.h ****  */
1119:Drivers/CMSIS/Include/core_cm7.h **** typedef struct
1120:Drivers/CMSIS/Include/core_cm7.h **** {
1121:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t CTRL;                   /*!< Offset: 0x000 (R/W)  Control Register */
1122:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t CYCCNT;                 /*!< Offset: 0x004 (R/W)  Cycle Count Register */
1123:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t CPICNT;                 /*!< Offset: 0x008 (R/W)  CPI Count Register */
1124:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t EXCCNT;                 /*!< Offset: 0x00C (R/W)  Exception Overhead Count Registe
1125:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t SLEEPCNT;               /*!< Offset: 0x010 (R/W)  Sleep Count Register */
1126:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t LSUCNT;                 /*!< Offset: 0x014 (R/W)  LSU Count Register */
1127:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t FOLDCNT;                /*!< Offset: 0x018 (R/W)  Folded-instruction Count Registe
1128:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t PCSR;                   /*!< Offset: 0x01C (R/ )  Program Counter Sample Register 
1129:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t COMP0;                  /*!< Offset: 0x020 (R/W)  Comparator Register 0 */
1130:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t MASK0;                  /*!< Offset: 0x024 (R/W)  Mask Register 0 */
1131:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t FUNCTION0;              /*!< Offset: 0x028 (R/W)  Function Register 0 */
1132:Drivers/CMSIS/Include/core_cm7.h ****         uint32_t RESERVED0[1U];
1133:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t COMP1;                  /*!< Offset: 0x030 (R/W)  Comparator Register 1 */
1134:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t MASK1;                  /*!< Offset: 0x034 (R/W)  Mask Register 1 */
1135:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t FUNCTION1;              /*!< Offset: 0x038 (R/W)  Function Register 1 */
1136:Drivers/CMSIS/Include/core_cm7.h ****         uint32_t RESERVED1[1U];
1137:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t COMP2;                  /*!< Offset: 0x040 (R/W)  Comparator Register 2 */
1138:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t MASK2;                  /*!< Offset: 0x044 (R/W)  Mask Register 2 */
1139:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t FUNCTION2;              /*!< Offset: 0x048 (R/W)  Function Register 2 */
1140:Drivers/CMSIS/Include/core_cm7.h ****         uint32_t RESERVED2[1U];
1141:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t COMP3;                  /*!< Offset: 0x050 (R/W)  Comparator Register 3 */
1142:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t MASK3;                  /*!< Offset: 0x054 (R/W)  Mask Register 3 */
1143:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t FUNCTION3;              /*!< Offset: 0x058 (R/W)  Function Register 3 */
1144:Drivers/CMSIS/Include/core_cm7.h ****         uint32_t RESERVED3[981U];
1145:Drivers/CMSIS/Include/core_cm7.h ****   __OM  uint32_t LAR;                    /*!< Offset: 0xFB0 (  W)  Lock Access Register */
1146:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t LSR;                    /*!< Offset: 0xFB4 (R  )  Lock Status Register */
1147:Drivers/CMSIS/Include/core_cm7.h **** } DWT_Type;
1148:Drivers/CMSIS/Include/core_cm7.h **** 
1149:Drivers/CMSIS/Include/core_cm7.h **** /* DWT Control Register Definitions */
1150:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_CTRL_NUMCOMP_Pos               28U                                         /*!< DWT CTR
1151:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_CTRL_NUMCOMP_Msk               (0xFUL << DWT_CTRL_NUMCOMP_Pos)             /*!< DWT CTR
1152:Drivers/CMSIS/Include/core_cm7.h **** 
1153:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_CTRL_NOTRCPKT_Pos              27U                                         /*!< DWT CTR
1154:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_CTRL_NOTRCPKT_Msk              (0x1UL << DWT_CTRL_NOTRCPKT_Pos)            /*!< DWT CTR
1155:Drivers/CMSIS/Include/core_cm7.h **** 
1156:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_CTRL_NOEXTTRIG_Pos             26U                                         /*!< DWT CTR
1157:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_CTRL_NOEXTTRIG_Msk             (0x1UL << DWT_CTRL_NOEXTTRIG_Pos)           /*!< DWT CTR
1158:Drivers/CMSIS/Include/core_cm7.h **** 
1159:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_CTRL_NOCYCCNT_Pos              25U                                         /*!< DWT CTR
1160:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_CTRL_NOCYCCNT_Msk              (0x1UL << DWT_CTRL_NOCYCCNT_Pos)            /*!< DWT CTR
1161:Drivers/CMSIS/Include/core_cm7.h **** 
1162:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_CTRL_NOPRFCNT_Pos              24U                                         /*!< DWT CTR
1163:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_CTRL_NOPRFCNT_Msk              (0x1UL << DWT_CTRL_NOPRFCNT_Pos)            /*!< DWT CTR
1164:Drivers/CMSIS/Include/core_cm7.h **** 
1165:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_CTRL_CYCEVTENA_Pos             22U                                         /*!< DWT CTR
1166:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_CTRL_CYCEVTENA_Msk             (0x1UL << DWT_CTRL_CYCEVTENA_Pos)           /*!< DWT CTR
1167:Drivers/CMSIS/Include/core_cm7.h **** 
1168:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_CTRL_FOLDEVTENA_Pos            21U                                         /*!< DWT CTR
1169:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_CTRL_FOLDEVTENA_Msk            (0x1UL << DWT_CTRL_FOLDEVTENA_Pos)          /*!< DWT CTR
1170:Drivers/CMSIS/Include/core_cm7.h **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 22


1171:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_CTRL_LSUEVTENA_Pos             20U                                         /*!< DWT CTR
1172:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_CTRL_LSUEVTENA_Msk             (0x1UL << DWT_CTRL_LSUEVTENA_Pos)           /*!< DWT CTR
1173:Drivers/CMSIS/Include/core_cm7.h **** 
1174:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_CTRL_SLEEPEVTENA_Pos           19U                                         /*!< DWT CTR
1175:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_CTRL_SLEEPEVTENA_Msk           (0x1UL << DWT_CTRL_SLEEPEVTENA_Pos)         /*!< DWT CTR
1176:Drivers/CMSIS/Include/core_cm7.h **** 
1177:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_CTRL_EXCEVTENA_Pos             18U                                         /*!< DWT CTR
1178:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_CTRL_EXCEVTENA_Msk             (0x1UL << DWT_CTRL_EXCEVTENA_Pos)           /*!< DWT CTR
1179:Drivers/CMSIS/Include/core_cm7.h **** 
1180:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_CTRL_CPIEVTENA_Pos             17U                                         /*!< DWT CTR
1181:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_CTRL_CPIEVTENA_Msk             (0x1UL << DWT_CTRL_CPIEVTENA_Pos)           /*!< DWT CTR
1182:Drivers/CMSIS/Include/core_cm7.h **** 
1183:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_CTRL_EXCTRCENA_Pos             16U                                         /*!< DWT CTR
1184:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_CTRL_EXCTRCENA_Msk             (0x1UL << DWT_CTRL_EXCTRCENA_Pos)           /*!< DWT CTR
1185:Drivers/CMSIS/Include/core_cm7.h **** 
1186:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_CTRL_PCSAMPLENA_Pos            12U                                         /*!< DWT CTR
1187:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_CTRL_PCSAMPLENA_Msk            (0x1UL << DWT_CTRL_PCSAMPLENA_Pos)          /*!< DWT CTR
1188:Drivers/CMSIS/Include/core_cm7.h **** 
1189:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_CTRL_SYNCTAP_Pos               10U                                         /*!< DWT CTR
1190:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_CTRL_SYNCTAP_Msk               (0x3UL << DWT_CTRL_SYNCTAP_Pos)             /*!< DWT CTR
1191:Drivers/CMSIS/Include/core_cm7.h **** 
1192:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_CTRL_CYCTAP_Pos                 9U                                         /*!< DWT CTR
1193:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_CTRL_CYCTAP_Msk                (0x1UL << DWT_CTRL_CYCTAP_Pos)              /*!< DWT CTR
1194:Drivers/CMSIS/Include/core_cm7.h **** 
1195:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_CTRL_POSTINIT_Pos               5U                                         /*!< DWT CTR
1196:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_CTRL_POSTINIT_Msk              (0xFUL << DWT_CTRL_POSTINIT_Pos)            /*!< DWT CTR
1197:Drivers/CMSIS/Include/core_cm7.h **** 
1198:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_CTRL_POSTPRESET_Pos             1U                                         /*!< DWT CTR
1199:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_CTRL_POSTPRESET_Msk            (0xFUL << DWT_CTRL_POSTPRESET_Pos)          /*!< DWT CTR
1200:Drivers/CMSIS/Include/core_cm7.h **** 
1201:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_CTRL_CYCCNTENA_Pos              0U                                         /*!< DWT CTR
1202:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_CTRL_CYCCNTENA_Msk             (0x1UL /*<< DWT_CTRL_CYCCNTENA_Pos*/)       /*!< DWT CTR
1203:Drivers/CMSIS/Include/core_cm7.h **** 
1204:Drivers/CMSIS/Include/core_cm7.h **** /* DWT CPI Count Register Definitions */
1205:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_CPICNT_CPICNT_Pos               0U                                         /*!< DWT CPI
1206:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_CPICNT_CPICNT_Msk              (0xFFUL /*<< DWT_CPICNT_CPICNT_Pos*/)       /*!< DWT CPI
1207:Drivers/CMSIS/Include/core_cm7.h **** 
1208:Drivers/CMSIS/Include/core_cm7.h **** /* DWT Exception Overhead Count Register Definitions */
1209:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_EXCCNT_EXCCNT_Pos               0U                                         /*!< DWT EXC
1210:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_EXCCNT_EXCCNT_Msk              (0xFFUL /*<< DWT_EXCCNT_EXCCNT_Pos*/)       /*!< DWT EXC
1211:Drivers/CMSIS/Include/core_cm7.h **** 
1212:Drivers/CMSIS/Include/core_cm7.h **** /* DWT Sleep Count Register Definitions */
1213:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_SLEEPCNT_SLEEPCNT_Pos           0U                                         /*!< DWT SLE
1214:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_SLEEPCNT_SLEEPCNT_Msk          (0xFFUL /*<< DWT_SLEEPCNT_SLEEPCNT_Pos*/)   /*!< DWT SLE
1215:Drivers/CMSIS/Include/core_cm7.h **** 
1216:Drivers/CMSIS/Include/core_cm7.h **** /* DWT LSU Count Register Definitions */
1217:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_LSUCNT_LSUCNT_Pos               0U                                         /*!< DWT LSU
1218:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_LSUCNT_LSUCNT_Msk              (0xFFUL /*<< DWT_LSUCNT_LSUCNT_Pos*/)       /*!< DWT LSU
1219:Drivers/CMSIS/Include/core_cm7.h **** 
1220:Drivers/CMSIS/Include/core_cm7.h **** /* DWT Folded-instruction Count Register Definitions */
1221:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_FOLDCNT_FOLDCNT_Pos             0U                                         /*!< DWT FOL
1222:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_FOLDCNT_FOLDCNT_Msk            (0xFFUL /*<< DWT_FOLDCNT_FOLDCNT_Pos*/)     /*!< DWT FOL
1223:Drivers/CMSIS/Include/core_cm7.h **** 
1224:Drivers/CMSIS/Include/core_cm7.h **** /* DWT Comparator Mask Register Definitions */
1225:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_MASK_MASK_Pos                   0U                                         /*!< DWT MAS
1226:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_MASK_MASK_Msk                  (0x1FUL /*<< DWT_MASK_MASK_Pos*/)           /*!< DWT MAS
1227:Drivers/CMSIS/Include/core_cm7.h **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 23


1228:Drivers/CMSIS/Include/core_cm7.h **** /* DWT Comparator Function Register Definitions */
1229:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_FUNCTION_MATCHED_Pos           24U                                         /*!< DWT FUN
1230:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_FUNCTION_MATCHED_Msk           (0x1UL << DWT_FUNCTION_MATCHED_Pos)         /*!< DWT FUN
1231:Drivers/CMSIS/Include/core_cm7.h **** 
1232:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_FUNCTION_DATAVADDR1_Pos        16U                                         /*!< DWT FUN
1233:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_FUNCTION_DATAVADDR1_Msk        (0xFUL << DWT_FUNCTION_DATAVADDR1_Pos)      /*!< DWT FUN
1234:Drivers/CMSIS/Include/core_cm7.h **** 
1235:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_FUNCTION_DATAVADDR0_Pos        12U                                         /*!< DWT FUN
1236:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_FUNCTION_DATAVADDR0_Msk        (0xFUL << DWT_FUNCTION_DATAVADDR0_Pos)      /*!< DWT FUN
1237:Drivers/CMSIS/Include/core_cm7.h **** 
1238:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_FUNCTION_DATAVSIZE_Pos         10U                                         /*!< DWT FUN
1239:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_FUNCTION_DATAVSIZE_Msk         (0x3UL << DWT_FUNCTION_DATAVSIZE_Pos)       /*!< DWT FUN
1240:Drivers/CMSIS/Include/core_cm7.h **** 
1241:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_FUNCTION_LNK1ENA_Pos            9U                                         /*!< DWT FUN
1242:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_FUNCTION_LNK1ENA_Msk           (0x1UL << DWT_FUNCTION_LNK1ENA_Pos)         /*!< DWT FUN
1243:Drivers/CMSIS/Include/core_cm7.h **** 
1244:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_FUNCTION_DATAVMATCH_Pos         8U                                         /*!< DWT FUN
1245:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_FUNCTION_DATAVMATCH_Msk        (0x1UL << DWT_FUNCTION_DATAVMATCH_Pos)      /*!< DWT FUN
1246:Drivers/CMSIS/Include/core_cm7.h **** 
1247:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_FUNCTION_CYCMATCH_Pos           7U                                         /*!< DWT FUN
1248:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_FUNCTION_CYCMATCH_Msk          (0x1UL << DWT_FUNCTION_CYCMATCH_Pos)        /*!< DWT FUN
1249:Drivers/CMSIS/Include/core_cm7.h **** 
1250:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_FUNCTION_EMITRANGE_Pos          5U                                         /*!< DWT FUN
1251:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_FUNCTION_EMITRANGE_Msk         (0x1UL << DWT_FUNCTION_EMITRANGE_Pos)       /*!< DWT FUN
1252:Drivers/CMSIS/Include/core_cm7.h **** 
1253:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_FUNCTION_FUNCTION_Pos           0U                                         /*!< DWT FUN
1254:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_FUNCTION_FUNCTION_Msk          (0xFUL /*<< DWT_FUNCTION_FUNCTION_Pos*/)    /*!< DWT FUN
1255:Drivers/CMSIS/Include/core_cm7.h **** 
1256:Drivers/CMSIS/Include/core_cm7.h **** /*@}*/ /* end of group CMSIS_DWT */
1257:Drivers/CMSIS/Include/core_cm7.h **** 
1258:Drivers/CMSIS/Include/core_cm7.h **** 
1259:Drivers/CMSIS/Include/core_cm7.h **** /**
1260:Drivers/CMSIS/Include/core_cm7.h ****   \ingroup  CMSIS_core_register
1261:Drivers/CMSIS/Include/core_cm7.h ****   \defgroup CMSIS_TPI     Trace Port Interface (TPI)
1262:Drivers/CMSIS/Include/core_cm7.h ****   \brief    Type definitions for the Trace Port Interface (TPI)
1263:Drivers/CMSIS/Include/core_cm7.h ****   @{
1264:Drivers/CMSIS/Include/core_cm7.h ****  */
1265:Drivers/CMSIS/Include/core_cm7.h **** 
1266:Drivers/CMSIS/Include/core_cm7.h **** /**
1267:Drivers/CMSIS/Include/core_cm7.h ****   \brief  Structure type to access the Trace Port Interface Register (TPI).
1268:Drivers/CMSIS/Include/core_cm7.h ****  */
1269:Drivers/CMSIS/Include/core_cm7.h **** typedef struct
1270:Drivers/CMSIS/Include/core_cm7.h **** {
1271:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t SSPSR;                  /*!< Offset: 0x000 (R/ )  Supported Parallel Port Size Reg
1272:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t CSPSR;                  /*!< Offset: 0x004 (R/W)  Current Parallel Port Size Regis
1273:Drivers/CMSIS/Include/core_cm7.h ****         uint32_t RESERVED0[2U];
1274:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t ACPR;                   /*!< Offset: 0x010 (R/W)  Asynchronous Clock Prescaler Reg
1275:Drivers/CMSIS/Include/core_cm7.h ****         uint32_t RESERVED1[55U];
1276:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t SPPR;                   /*!< Offset: 0x0F0 (R/W)  Selected Pin Protocol Register *
1277:Drivers/CMSIS/Include/core_cm7.h ****         uint32_t RESERVED2[131U];
1278:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t FFSR;                   /*!< Offset: 0x300 (R/ )  Formatter and Flush Status Regis
1279:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t FFCR;                   /*!< Offset: 0x304 (R/W)  Formatter and Flush Control Regi
1280:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t FSCR;                   /*!< Offset: 0x308 (R/ )  Formatter Synchronization Counte
1281:Drivers/CMSIS/Include/core_cm7.h ****         uint32_t RESERVED3[759U];
1282:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t TRIGGER;                /*!< Offset: 0xEE8 (R/ )  TRIGGER Register */
1283:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t FIFO0;                  /*!< Offset: 0xEEC (R/ )  Integration ETM Data */
1284:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t ITATBCTR2;              /*!< Offset: 0xEF0 (R/ )  ITATBCTR2 */
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 24


1285:Drivers/CMSIS/Include/core_cm7.h ****         uint32_t RESERVED4[1U];
1286:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t ITATBCTR0;              /*!< Offset: 0xEF8 (R/ )  ITATBCTR0 */
1287:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t FIFO1;                  /*!< Offset: 0xEFC (R/ )  Integration ITM Data */
1288:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t ITCTRL;                 /*!< Offset: 0xF00 (R/W)  Integration Mode Control */
1289:Drivers/CMSIS/Include/core_cm7.h ****         uint32_t RESERVED5[39U];
1290:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t CLAIMSET;               /*!< Offset: 0xFA0 (R/W)  Claim tag set */
1291:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t CLAIMCLR;               /*!< Offset: 0xFA4 (R/W)  Claim tag clear */
1292:Drivers/CMSIS/Include/core_cm7.h ****         uint32_t RESERVED7[8U];
1293:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t DEVID;                  /*!< Offset: 0xFC8 (R/ )  TPIU_DEVID */
1294:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t DEVTYPE;                /*!< Offset: 0xFCC (R/ )  TPIU_DEVTYPE */
1295:Drivers/CMSIS/Include/core_cm7.h **** } TPI_Type;
1296:Drivers/CMSIS/Include/core_cm7.h **** 
1297:Drivers/CMSIS/Include/core_cm7.h **** /* TPI Asynchronous Clock Prescaler Register Definitions */
1298:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_ACPR_PRESCALER_Pos              0U                                         /*!< TPI ACP
1299:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_ACPR_PRESCALER_Msk             (0x1FFFUL /*<< TPI_ACPR_PRESCALER_Pos*/)    /*!< TPI ACP
1300:Drivers/CMSIS/Include/core_cm7.h **** 
1301:Drivers/CMSIS/Include/core_cm7.h **** /* TPI Selected Pin Protocol Register Definitions */
1302:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_SPPR_TXMODE_Pos                 0U                                         /*!< TPI SPP
1303:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_SPPR_TXMODE_Msk                (0x3UL /*<< TPI_SPPR_TXMODE_Pos*/)          /*!< TPI SPP
1304:Drivers/CMSIS/Include/core_cm7.h **** 
1305:Drivers/CMSIS/Include/core_cm7.h **** /* TPI Formatter and Flush Status Register Definitions */
1306:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FFSR_FtNonStop_Pos              3U                                         /*!< TPI FFS
1307:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FFSR_FtNonStop_Msk             (0x1UL << TPI_FFSR_FtNonStop_Pos)           /*!< TPI FFS
1308:Drivers/CMSIS/Include/core_cm7.h **** 
1309:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FFSR_TCPresent_Pos              2U                                         /*!< TPI FFS
1310:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FFSR_TCPresent_Msk             (0x1UL << TPI_FFSR_TCPresent_Pos)           /*!< TPI FFS
1311:Drivers/CMSIS/Include/core_cm7.h **** 
1312:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FFSR_FtStopped_Pos              1U                                         /*!< TPI FFS
1313:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FFSR_FtStopped_Msk             (0x1UL << TPI_FFSR_FtStopped_Pos)           /*!< TPI FFS
1314:Drivers/CMSIS/Include/core_cm7.h **** 
1315:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FFSR_FlInProg_Pos               0U                                         /*!< TPI FFS
1316:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FFSR_FlInProg_Msk              (0x1UL /*<< TPI_FFSR_FlInProg_Pos*/)        /*!< TPI FFS
1317:Drivers/CMSIS/Include/core_cm7.h **** 
1318:Drivers/CMSIS/Include/core_cm7.h **** /* TPI Formatter and Flush Control Register Definitions */
1319:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FFCR_TrigIn_Pos                 8U                                         /*!< TPI FFC
1320:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FFCR_TrigIn_Msk                (0x1UL << TPI_FFCR_TrigIn_Pos)              /*!< TPI FFC
1321:Drivers/CMSIS/Include/core_cm7.h **** 
1322:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FFCR_EnFCont_Pos                1U                                         /*!< TPI FFC
1323:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FFCR_EnFCont_Msk               (0x1UL << TPI_FFCR_EnFCont_Pos)             /*!< TPI FFC
1324:Drivers/CMSIS/Include/core_cm7.h **** 
1325:Drivers/CMSIS/Include/core_cm7.h **** /* TPI TRIGGER Register Definitions */
1326:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_TRIGGER_TRIGGER_Pos             0U                                         /*!< TPI TRI
1327:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_TRIGGER_TRIGGER_Msk            (0x1UL /*<< TPI_TRIGGER_TRIGGER_Pos*/)      /*!< TPI TRI
1328:Drivers/CMSIS/Include/core_cm7.h **** 
1329:Drivers/CMSIS/Include/core_cm7.h **** /* TPI Integration ETM Data Register Definitions (FIFO0) */
1330:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FIFO0_ITM_ATVALID_Pos          29U                                         /*!< TPI FIF
1331:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FIFO0_ITM_ATVALID_Msk          (0x1UL << TPI_FIFO0_ITM_ATVALID_Pos)        /*!< TPI FIF
1332:Drivers/CMSIS/Include/core_cm7.h **** 
1333:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FIFO0_ITM_bytecount_Pos        27U                                         /*!< TPI FIF
1334:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FIFO0_ITM_bytecount_Msk        (0x3UL << TPI_FIFO0_ITM_bytecount_Pos)      /*!< TPI FIF
1335:Drivers/CMSIS/Include/core_cm7.h **** 
1336:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FIFO0_ETM_ATVALID_Pos          26U                                         /*!< TPI FIF
1337:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FIFO0_ETM_ATVALID_Msk          (0x1UL << TPI_FIFO0_ETM_ATVALID_Pos)        /*!< TPI FIF
1338:Drivers/CMSIS/Include/core_cm7.h **** 
1339:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FIFO0_ETM_bytecount_Pos        24U                                         /*!< TPI FIF
1340:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FIFO0_ETM_bytecount_Msk        (0x3UL << TPI_FIFO0_ETM_bytecount_Pos)      /*!< TPI FIF
1341:Drivers/CMSIS/Include/core_cm7.h **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 25


1342:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FIFO0_ETM2_Pos                 16U                                         /*!< TPI FIF
1343:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FIFO0_ETM2_Msk                 (0xFFUL << TPI_FIFO0_ETM2_Pos)              /*!< TPI FIF
1344:Drivers/CMSIS/Include/core_cm7.h **** 
1345:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FIFO0_ETM1_Pos                  8U                                         /*!< TPI FIF
1346:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FIFO0_ETM1_Msk                 (0xFFUL << TPI_FIFO0_ETM1_Pos)              /*!< TPI FIF
1347:Drivers/CMSIS/Include/core_cm7.h **** 
1348:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FIFO0_ETM0_Pos                  0U                                         /*!< TPI FIF
1349:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FIFO0_ETM0_Msk                 (0xFFUL /*<< TPI_FIFO0_ETM0_Pos*/)          /*!< TPI FIF
1350:Drivers/CMSIS/Include/core_cm7.h **** 
1351:Drivers/CMSIS/Include/core_cm7.h **** /* TPI ITATBCTR2 Register Definitions */
1352:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_ITATBCTR2_ATREADY2_Pos          0U                                         /*!< TPI ITA
1353:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_ITATBCTR2_ATREADY2_Msk         (0x1UL /*<< TPI_ITATBCTR2_ATREADY2_Pos*/)   /*!< TPI ITA
1354:Drivers/CMSIS/Include/core_cm7.h **** 
1355:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_ITATBCTR2_ATREADY1_Pos          0U                                         /*!< TPI ITA
1356:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_ITATBCTR2_ATREADY1_Msk         (0x1UL /*<< TPI_ITATBCTR2_ATREADY1_Pos*/)   /*!< TPI ITA
1357:Drivers/CMSIS/Include/core_cm7.h **** 
1358:Drivers/CMSIS/Include/core_cm7.h **** /* TPI Integration ITM Data Register Definitions (FIFO1) */
1359:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FIFO1_ITM_ATVALID_Pos          29U                                         /*!< TPI FIF
1360:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FIFO1_ITM_ATVALID_Msk          (0x1UL << TPI_FIFO1_ITM_ATVALID_Pos)        /*!< TPI FIF
1361:Drivers/CMSIS/Include/core_cm7.h **** 
1362:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FIFO1_ITM_bytecount_Pos        27U                                         /*!< TPI FIF
1363:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FIFO1_ITM_bytecount_Msk        (0x3UL << TPI_FIFO1_ITM_bytecount_Pos)      /*!< TPI FIF
1364:Drivers/CMSIS/Include/core_cm7.h **** 
1365:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FIFO1_ETM_ATVALID_Pos          26U                                         /*!< TPI FIF
1366:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FIFO1_ETM_ATVALID_Msk          (0x1UL << TPI_FIFO1_ETM_ATVALID_Pos)        /*!< TPI FIF
1367:Drivers/CMSIS/Include/core_cm7.h **** 
1368:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FIFO1_ETM_bytecount_Pos        24U                                         /*!< TPI FIF
1369:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FIFO1_ETM_bytecount_Msk        (0x3UL << TPI_FIFO1_ETM_bytecount_Pos)      /*!< TPI FIF
1370:Drivers/CMSIS/Include/core_cm7.h **** 
1371:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FIFO1_ITM2_Pos                 16U                                         /*!< TPI FIF
1372:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FIFO1_ITM2_Msk                 (0xFFUL << TPI_FIFO1_ITM2_Pos)              /*!< TPI FIF
1373:Drivers/CMSIS/Include/core_cm7.h **** 
1374:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FIFO1_ITM1_Pos                  8U                                         /*!< TPI FIF
1375:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FIFO1_ITM1_Msk                 (0xFFUL << TPI_FIFO1_ITM1_Pos)              /*!< TPI FIF
1376:Drivers/CMSIS/Include/core_cm7.h **** 
1377:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FIFO1_ITM0_Pos                  0U                                         /*!< TPI FIF
1378:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FIFO1_ITM0_Msk                 (0xFFUL /*<< TPI_FIFO1_ITM0_Pos*/)          /*!< TPI FIF
1379:Drivers/CMSIS/Include/core_cm7.h **** 
1380:Drivers/CMSIS/Include/core_cm7.h **** /* TPI ITATBCTR0 Register Definitions */
1381:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_ITATBCTR0_ATREADY2_Pos          0U                                         /*!< TPI ITA
1382:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_ITATBCTR0_ATREADY2_Msk         (0x1UL /*<< TPI_ITATBCTR0_ATREADY2_Pos*/)   /*!< TPI ITA
1383:Drivers/CMSIS/Include/core_cm7.h **** 
1384:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_ITATBCTR0_ATREADY1_Pos          0U                                         /*!< TPI ITA
1385:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_ITATBCTR0_ATREADY1_Msk         (0x1UL /*<< TPI_ITATBCTR0_ATREADY1_Pos*/)   /*!< TPI ITA
1386:Drivers/CMSIS/Include/core_cm7.h **** 
1387:Drivers/CMSIS/Include/core_cm7.h **** /* TPI Integration Mode Control Register Definitions */
1388:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_ITCTRL_Mode_Pos                 0U                                         /*!< TPI ITC
1389:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_ITCTRL_Mode_Msk                (0x3UL /*<< TPI_ITCTRL_Mode_Pos*/)          /*!< TPI ITC
1390:Drivers/CMSIS/Include/core_cm7.h **** 
1391:Drivers/CMSIS/Include/core_cm7.h **** /* TPI DEVID Register Definitions */
1392:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_DEVID_NRZVALID_Pos             11U                                         /*!< TPI DEV
1393:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_DEVID_NRZVALID_Msk             (0x1UL << TPI_DEVID_NRZVALID_Pos)           /*!< TPI DEV
1394:Drivers/CMSIS/Include/core_cm7.h **** 
1395:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_DEVID_MANCVALID_Pos            10U                                         /*!< TPI DEV
1396:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_DEVID_MANCVALID_Msk            (0x1UL << TPI_DEVID_MANCVALID_Pos)          /*!< TPI DEV
1397:Drivers/CMSIS/Include/core_cm7.h **** 
1398:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_DEVID_PTINVALID_Pos             9U                                         /*!< TPI DEV
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 26


1399:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_DEVID_PTINVALID_Msk            (0x1UL << TPI_DEVID_PTINVALID_Pos)          /*!< TPI DEV
1400:Drivers/CMSIS/Include/core_cm7.h **** 
1401:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_DEVID_MinBufSz_Pos              6U                                         /*!< TPI DEV
1402:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_DEVID_MinBufSz_Msk             (0x7UL << TPI_DEVID_MinBufSz_Pos)           /*!< TPI DEV
1403:Drivers/CMSIS/Include/core_cm7.h **** 
1404:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_DEVID_AsynClkIn_Pos             5U                                         /*!< TPI DEV
1405:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_DEVID_AsynClkIn_Msk            (0x1UL << TPI_DEVID_AsynClkIn_Pos)          /*!< TPI DEV
1406:Drivers/CMSIS/Include/core_cm7.h **** 
1407:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_DEVID_NrTraceInput_Pos          0U                                         /*!< TPI DEV
1408:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_DEVID_NrTraceInput_Msk         (0x1FUL /*<< TPI_DEVID_NrTraceInput_Pos*/)  /*!< TPI DEV
1409:Drivers/CMSIS/Include/core_cm7.h **** 
1410:Drivers/CMSIS/Include/core_cm7.h **** /* TPI DEVTYPE Register Definitions */
1411:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_DEVTYPE_SubType_Pos             4U                                         /*!< TPI DEV
1412:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_DEVTYPE_SubType_Msk            (0xFUL /*<< TPI_DEVTYPE_SubType_Pos*/)      /*!< TPI DEV
1413:Drivers/CMSIS/Include/core_cm7.h **** 
1414:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_DEVTYPE_MajorType_Pos           0U                                         /*!< TPI DEV
1415:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_DEVTYPE_MajorType_Msk          (0xFUL << TPI_DEVTYPE_MajorType_Pos)        /*!< TPI DEV
1416:Drivers/CMSIS/Include/core_cm7.h **** 
1417:Drivers/CMSIS/Include/core_cm7.h **** /*@}*/ /* end of group CMSIS_TPI */
1418:Drivers/CMSIS/Include/core_cm7.h **** 
1419:Drivers/CMSIS/Include/core_cm7.h **** 
1420:Drivers/CMSIS/Include/core_cm7.h **** #if defined (__MPU_PRESENT) && (__MPU_PRESENT == 1U)
1421:Drivers/CMSIS/Include/core_cm7.h **** /**
1422:Drivers/CMSIS/Include/core_cm7.h ****   \ingroup  CMSIS_core_register
1423:Drivers/CMSIS/Include/core_cm7.h ****   \defgroup CMSIS_MPU     Memory Protection Unit (MPU)
1424:Drivers/CMSIS/Include/core_cm7.h ****   \brief    Type definitions for the Memory Protection Unit (MPU)
1425:Drivers/CMSIS/Include/core_cm7.h ****   @{
1426:Drivers/CMSIS/Include/core_cm7.h ****  */
1427:Drivers/CMSIS/Include/core_cm7.h **** 
1428:Drivers/CMSIS/Include/core_cm7.h **** /**
1429:Drivers/CMSIS/Include/core_cm7.h ****   \brief  Structure type to access the Memory Protection Unit (MPU).
1430:Drivers/CMSIS/Include/core_cm7.h ****  */
1431:Drivers/CMSIS/Include/core_cm7.h **** typedef struct
1432:Drivers/CMSIS/Include/core_cm7.h **** {
1433:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t TYPE;                   /*!< Offset: 0x000 (R/ )  MPU Type Register */
1434:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t CTRL;                   /*!< Offset: 0x004 (R/W)  MPU Control Register */
1435:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t RNR;                    /*!< Offset: 0x008 (R/W)  MPU Region RNRber Register */
1436:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t RBAR;                   /*!< Offset: 0x00C (R/W)  MPU Region Base Address Register
1437:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t RASR;                   /*!< Offset: 0x010 (R/W)  MPU Region Attribute and Size Re
1438:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t RBAR_A1;                /*!< Offset: 0x014 (R/W)  MPU Alias 1 Region Base Address 
1439:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t RASR_A1;                /*!< Offset: 0x018 (R/W)  MPU Alias 1 Region Attribute and
1440:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t RBAR_A2;                /*!< Offset: 0x01C (R/W)  MPU Alias 2 Region Base Address 
1441:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t RASR_A2;                /*!< Offset: 0x020 (R/W)  MPU Alias 2 Region Attribute and
1442:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t RBAR_A3;                /*!< Offset: 0x024 (R/W)  MPU Alias 3 Region Base Address 
1443:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t RASR_A3;                /*!< Offset: 0x028 (R/W)  MPU Alias 3 Region Attribute and
1444:Drivers/CMSIS/Include/core_cm7.h **** } MPU_Type;
1445:Drivers/CMSIS/Include/core_cm7.h **** 
1446:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_TYPE_RALIASES                  4U
1447:Drivers/CMSIS/Include/core_cm7.h **** 
1448:Drivers/CMSIS/Include/core_cm7.h **** /* MPU Type Register Definitions */
1449:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_TYPE_IREGION_Pos               16U                                            /*!< MPU 
1450:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_TYPE_IREGION_Msk               (0xFFUL << MPU_TYPE_IREGION_Pos)               /*!< MPU 
1451:Drivers/CMSIS/Include/core_cm7.h **** 
1452:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_TYPE_DREGION_Pos                8U                                            /*!< MPU 
1453:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_TYPE_DREGION_Msk               (0xFFUL << MPU_TYPE_DREGION_Pos)               /*!< MPU 
1454:Drivers/CMSIS/Include/core_cm7.h **** 
1455:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_TYPE_SEPARATE_Pos               0U                                            /*!< MPU 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 27


1456:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_TYPE_SEPARATE_Msk              (1UL /*<< MPU_TYPE_SEPARATE_Pos*/)             /*!< MPU 
1457:Drivers/CMSIS/Include/core_cm7.h **** 
1458:Drivers/CMSIS/Include/core_cm7.h **** /* MPU Control Register Definitions */
1459:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_CTRL_PRIVDEFENA_Pos             2U                                            /*!< MPU 
1460:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_CTRL_PRIVDEFENA_Msk            (1UL << MPU_CTRL_PRIVDEFENA_Pos)               /*!< MPU 
1461:Drivers/CMSIS/Include/core_cm7.h **** 
1462:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_CTRL_HFNMIENA_Pos               1U                                            /*!< MPU 
1463:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_CTRL_HFNMIENA_Msk              (1UL << MPU_CTRL_HFNMIENA_Pos)                 /*!< MPU 
1464:Drivers/CMSIS/Include/core_cm7.h **** 
1465:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_CTRL_ENABLE_Pos                 0U                                            /*!< MPU 
1466:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_CTRL_ENABLE_Msk                (1UL /*<< MPU_CTRL_ENABLE_Pos*/)               /*!< MPU 
1467:Drivers/CMSIS/Include/core_cm7.h **** 
1468:Drivers/CMSIS/Include/core_cm7.h **** /* MPU Region Number Register Definitions */
1469:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_RNR_REGION_Pos                  0U                                            /*!< MPU 
1470:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_RNR_REGION_Msk                 (0xFFUL /*<< MPU_RNR_REGION_Pos*/)             /*!< MPU 
1471:Drivers/CMSIS/Include/core_cm7.h **** 
1472:Drivers/CMSIS/Include/core_cm7.h **** /* MPU Region Base Address Register Definitions */
1473:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_RBAR_ADDR_Pos                   5U                                            /*!< MPU 
1474:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_RBAR_ADDR_Msk                  (0x7FFFFFFUL << MPU_RBAR_ADDR_Pos)             /*!< MPU 
1475:Drivers/CMSIS/Include/core_cm7.h **** 
1476:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_RBAR_VALID_Pos                  4U                                            /*!< MPU 
1477:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_RBAR_VALID_Msk                 (1UL << MPU_RBAR_VALID_Pos)                    /*!< MPU 
1478:Drivers/CMSIS/Include/core_cm7.h **** 
1479:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_RBAR_REGION_Pos                 0U                                            /*!< MPU 
1480:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_RBAR_REGION_Msk                (0xFUL /*<< MPU_RBAR_REGION_Pos*/)             /*!< MPU 
1481:Drivers/CMSIS/Include/core_cm7.h **** 
1482:Drivers/CMSIS/Include/core_cm7.h **** /* MPU Region Attribute and Size Register Definitions */
1483:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_RASR_ATTRS_Pos                 16U                                            /*!< MPU 
1484:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_RASR_ATTRS_Msk                 (0xFFFFUL << MPU_RASR_ATTRS_Pos)               /*!< MPU 
1485:Drivers/CMSIS/Include/core_cm7.h **** 
1486:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_RASR_XN_Pos                    28U                                            /*!< MPU 
1487:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_RASR_XN_Msk                    (1UL << MPU_RASR_XN_Pos)                       /*!< MPU 
1488:Drivers/CMSIS/Include/core_cm7.h **** 
1489:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_RASR_AP_Pos                    24U                                            /*!< MPU 
1490:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_RASR_AP_Msk                    (0x7UL << MPU_RASR_AP_Pos)                     /*!< MPU 
1491:Drivers/CMSIS/Include/core_cm7.h **** 
1492:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_RASR_TEX_Pos                   19U                                            /*!< MPU 
1493:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_RASR_TEX_Msk                   (0x7UL << MPU_RASR_TEX_Pos)                    /*!< MPU 
1494:Drivers/CMSIS/Include/core_cm7.h **** 
1495:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_RASR_S_Pos                     18U                                            /*!< MPU 
1496:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_RASR_S_Msk                     (1UL << MPU_RASR_S_Pos)                        /*!< MPU 
1497:Drivers/CMSIS/Include/core_cm7.h **** 
1498:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_RASR_C_Pos                     17U                                            /*!< MPU 
1499:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_RASR_C_Msk                     (1UL << MPU_RASR_C_Pos)                        /*!< MPU 
1500:Drivers/CMSIS/Include/core_cm7.h **** 
1501:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_RASR_B_Pos                     16U                                            /*!< MPU 
1502:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_RASR_B_Msk                     (1UL << MPU_RASR_B_Pos)                        /*!< MPU 
1503:Drivers/CMSIS/Include/core_cm7.h **** 
1504:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_RASR_SRD_Pos                    8U                                            /*!< MPU 
1505:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_RASR_SRD_Msk                   (0xFFUL << MPU_RASR_SRD_Pos)                   /*!< MPU 
1506:Drivers/CMSIS/Include/core_cm7.h **** 
1507:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_RASR_SIZE_Pos                   1U                                            /*!< MPU 
1508:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_RASR_SIZE_Msk                  (0x1FUL << MPU_RASR_SIZE_Pos)                  /*!< MPU 
1509:Drivers/CMSIS/Include/core_cm7.h **** 
1510:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_RASR_ENABLE_Pos                 0U                                            /*!< MPU 
1511:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_RASR_ENABLE_Msk                (1UL /*<< MPU_RASR_ENABLE_Pos*/)               /*!< MPU 
1512:Drivers/CMSIS/Include/core_cm7.h **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 28


1513:Drivers/CMSIS/Include/core_cm7.h **** /*@} end of group CMSIS_MPU */
1514:Drivers/CMSIS/Include/core_cm7.h **** #endif /* defined (__MPU_PRESENT) && (__MPU_PRESENT == 1U) */
1515:Drivers/CMSIS/Include/core_cm7.h **** 
1516:Drivers/CMSIS/Include/core_cm7.h **** 
1517:Drivers/CMSIS/Include/core_cm7.h **** /**
1518:Drivers/CMSIS/Include/core_cm7.h ****   \ingroup  CMSIS_core_register
1519:Drivers/CMSIS/Include/core_cm7.h ****   \defgroup CMSIS_FPU     Floating Point Unit (FPU)
1520:Drivers/CMSIS/Include/core_cm7.h ****   \brief    Type definitions for the Floating Point Unit (FPU)
1521:Drivers/CMSIS/Include/core_cm7.h ****   @{
1522:Drivers/CMSIS/Include/core_cm7.h ****  */
1523:Drivers/CMSIS/Include/core_cm7.h **** 
1524:Drivers/CMSIS/Include/core_cm7.h **** /**
1525:Drivers/CMSIS/Include/core_cm7.h ****   \brief  Structure type to access the Floating Point Unit (FPU).
1526:Drivers/CMSIS/Include/core_cm7.h ****  */
1527:Drivers/CMSIS/Include/core_cm7.h **** typedef struct
1528:Drivers/CMSIS/Include/core_cm7.h **** {
1529:Drivers/CMSIS/Include/core_cm7.h ****         uint32_t RESERVED0[1U];
1530:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t FPCCR;                  /*!< Offset: 0x004 (R/W)  Floating-Point Context Control R
1531:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t FPCAR;                  /*!< Offset: 0x008 (R/W)  Floating-Point Context Address R
1532:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t FPDSCR;                 /*!< Offset: 0x00C (R/W)  Floating-Point Default Status Co
1533:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t MVFR0;                  /*!< Offset: 0x010 (R/ )  Media and FP Feature Register 0 
1534:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t MVFR1;                  /*!< Offset: 0x014 (R/ )  Media and FP Feature Register 1 
1535:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t MVFR2;                  /*!< Offset: 0x018 (R/ )  Media and FP Feature Register 2 
1536:Drivers/CMSIS/Include/core_cm7.h **** } FPU_Type;
1537:Drivers/CMSIS/Include/core_cm7.h **** 
1538:Drivers/CMSIS/Include/core_cm7.h **** /* Floating-Point Context Control Register Definitions */
1539:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_FPCCR_ASPEN_Pos                31U                                            /*!< FPCC
1540:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_FPCCR_ASPEN_Msk                (1UL << FPU_FPCCR_ASPEN_Pos)                   /*!< FPCC
1541:Drivers/CMSIS/Include/core_cm7.h **** 
1542:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_FPCCR_LSPEN_Pos                30U                                            /*!< FPCC
1543:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_FPCCR_LSPEN_Msk                (1UL << FPU_FPCCR_LSPEN_Pos)                   /*!< FPCC
1544:Drivers/CMSIS/Include/core_cm7.h **** 
1545:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_FPCCR_MONRDY_Pos                8U                                            /*!< FPCC
1546:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_FPCCR_MONRDY_Msk               (1UL << FPU_FPCCR_MONRDY_Pos)                  /*!< FPCC
1547:Drivers/CMSIS/Include/core_cm7.h **** 
1548:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_FPCCR_BFRDY_Pos                 6U                                            /*!< FPCC
1549:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_FPCCR_BFRDY_Msk                (1UL << FPU_FPCCR_BFRDY_Pos)                   /*!< FPCC
1550:Drivers/CMSIS/Include/core_cm7.h **** 
1551:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_FPCCR_MMRDY_Pos                 5U                                            /*!< FPCC
1552:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_FPCCR_MMRDY_Msk                (1UL << FPU_FPCCR_MMRDY_Pos)                   /*!< FPCC
1553:Drivers/CMSIS/Include/core_cm7.h **** 
1554:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_FPCCR_HFRDY_Pos                 4U                                            /*!< FPCC
1555:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_FPCCR_HFRDY_Msk                (1UL << FPU_FPCCR_HFRDY_Pos)                   /*!< FPCC
1556:Drivers/CMSIS/Include/core_cm7.h **** 
1557:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_FPCCR_THREAD_Pos                3U                                            /*!< FPCC
1558:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_FPCCR_THREAD_Msk               (1UL << FPU_FPCCR_THREAD_Pos)                  /*!< FPCC
1559:Drivers/CMSIS/Include/core_cm7.h **** 
1560:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_FPCCR_USER_Pos                  1U                                            /*!< FPCC
1561:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_FPCCR_USER_Msk                 (1UL << FPU_FPCCR_USER_Pos)                    /*!< FPCC
1562:Drivers/CMSIS/Include/core_cm7.h **** 
1563:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_FPCCR_LSPACT_Pos                0U                                            /*!< FPCC
1564:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_FPCCR_LSPACT_Msk               (1UL /*<< FPU_FPCCR_LSPACT_Pos*/)              /*!< FPCC
1565:Drivers/CMSIS/Include/core_cm7.h **** 
1566:Drivers/CMSIS/Include/core_cm7.h **** /* Floating-Point Context Address Register Definitions */
1567:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_FPCAR_ADDRESS_Pos               3U                                            /*!< FPCA
1568:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_FPCAR_ADDRESS_Msk              (0x1FFFFFFFUL << FPU_FPCAR_ADDRESS_Pos)        /*!< FPCA
1569:Drivers/CMSIS/Include/core_cm7.h **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 29


1570:Drivers/CMSIS/Include/core_cm7.h **** /* Floating-Point Default Status Control Register Definitions */
1571:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_FPDSCR_AHP_Pos                 26U                                            /*!< FPDS
1572:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_FPDSCR_AHP_Msk                 (1UL << FPU_FPDSCR_AHP_Pos)                    /*!< FPDS
1573:Drivers/CMSIS/Include/core_cm7.h **** 
1574:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_FPDSCR_DN_Pos                  25U                                            /*!< FPDS
1575:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_FPDSCR_DN_Msk                  (1UL << FPU_FPDSCR_DN_Pos)                     /*!< FPDS
1576:Drivers/CMSIS/Include/core_cm7.h **** 
1577:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_FPDSCR_FZ_Pos                  24U                                            /*!< FPDS
1578:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_FPDSCR_FZ_Msk                  (1UL << FPU_FPDSCR_FZ_Pos)                     /*!< FPDS
1579:Drivers/CMSIS/Include/core_cm7.h **** 
1580:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_FPDSCR_RMode_Pos               22U                                            /*!< FPDS
1581:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_FPDSCR_RMode_Msk               (3UL << FPU_FPDSCR_RMode_Pos)                  /*!< FPDS
1582:Drivers/CMSIS/Include/core_cm7.h **** 
1583:Drivers/CMSIS/Include/core_cm7.h **** /* Media and FP Feature Register 0 Definitions */
1584:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_MVFR0_FP_rounding_modes_Pos    28U                                            /*!< MVFR
1585:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_MVFR0_FP_rounding_modes_Msk    (0xFUL << FPU_MVFR0_FP_rounding_modes_Pos)     /*!< MVFR
1586:Drivers/CMSIS/Include/core_cm7.h **** 
1587:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_MVFR0_Short_vectors_Pos        24U                                            /*!< MVFR
1588:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_MVFR0_Short_vectors_Msk        (0xFUL << FPU_MVFR0_Short_vectors_Pos)         /*!< MVFR
1589:Drivers/CMSIS/Include/core_cm7.h **** 
1590:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_MVFR0_Square_root_Pos          20U                                            /*!< MVFR
1591:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_MVFR0_Square_root_Msk          (0xFUL << FPU_MVFR0_Square_root_Pos)           /*!< MVFR
1592:Drivers/CMSIS/Include/core_cm7.h **** 
1593:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_MVFR0_Divide_Pos               16U                                            /*!< MVFR
1594:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_MVFR0_Divide_Msk               (0xFUL << FPU_MVFR0_Divide_Pos)                /*!< MVFR
1595:Drivers/CMSIS/Include/core_cm7.h **** 
1596:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_MVFR0_FP_excep_trapping_Pos    12U                                            /*!< MVFR
1597:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_MVFR0_FP_excep_trapping_Msk    (0xFUL << FPU_MVFR0_FP_excep_trapping_Pos)     /*!< MVFR
1598:Drivers/CMSIS/Include/core_cm7.h **** 
1599:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_MVFR0_Double_precision_Pos      8U                                            /*!< MVFR
1600:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_MVFR0_Double_precision_Msk     (0xFUL << FPU_MVFR0_Double_precision_Pos)      /*!< MVFR
1601:Drivers/CMSIS/Include/core_cm7.h **** 
1602:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_MVFR0_Single_precision_Pos      4U                                            /*!< MVFR
1603:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_MVFR0_Single_precision_Msk     (0xFUL << FPU_MVFR0_Single_precision_Pos)      /*!< MVFR
1604:Drivers/CMSIS/Include/core_cm7.h **** 
1605:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_MVFR0_A_SIMD_registers_Pos      0U                                            /*!< MVFR
1606:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_MVFR0_A_SIMD_registers_Msk     (0xFUL /*<< FPU_MVFR0_A_SIMD_registers_Pos*/)  /*!< MVFR
1607:Drivers/CMSIS/Include/core_cm7.h **** 
1608:Drivers/CMSIS/Include/core_cm7.h **** /* Media and FP Feature Register 1 Definitions */
1609:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_MVFR1_FP_fused_MAC_Pos         28U                                            /*!< MVFR
1610:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_MVFR1_FP_fused_MAC_Msk         (0xFUL << FPU_MVFR1_FP_fused_MAC_Pos)          /*!< MVFR
1611:Drivers/CMSIS/Include/core_cm7.h **** 
1612:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_MVFR1_FP_HPFP_Pos              24U                                            /*!< MVFR
1613:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_MVFR1_FP_HPFP_Msk              (0xFUL << FPU_MVFR1_FP_HPFP_Pos)               /*!< MVFR
1614:Drivers/CMSIS/Include/core_cm7.h **** 
1615:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_MVFR1_D_NaN_mode_Pos            4U                                            /*!< MVFR
1616:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_MVFR1_D_NaN_mode_Msk           (0xFUL << FPU_MVFR1_D_NaN_mode_Pos)            /*!< MVFR
1617:Drivers/CMSIS/Include/core_cm7.h **** 
1618:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_MVFR1_FtZ_mode_Pos              0U                                            /*!< MVFR
1619:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_MVFR1_FtZ_mode_Msk             (0xFUL /*<< FPU_MVFR1_FtZ_mode_Pos*/)          /*!< MVFR
1620:Drivers/CMSIS/Include/core_cm7.h **** 
1621:Drivers/CMSIS/Include/core_cm7.h **** /* Media and FP Feature Register 2 Definitions */
1622:Drivers/CMSIS/Include/core_cm7.h **** 
1623:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_MVFR2_VFP_Misc_Pos              4U                                            /*!< MVFR
1624:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_MVFR2_VFP_Misc_Msk             (0xFUL << FPU_MVFR2_VFP_Misc_Pos)              /*!< MVFR
1625:Drivers/CMSIS/Include/core_cm7.h **** 
1626:Drivers/CMSIS/Include/core_cm7.h **** /*@} end of group CMSIS_FPU */
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 30


1627:Drivers/CMSIS/Include/core_cm7.h **** 
1628:Drivers/CMSIS/Include/core_cm7.h **** 
1629:Drivers/CMSIS/Include/core_cm7.h **** /**
1630:Drivers/CMSIS/Include/core_cm7.h ****   \ingroup  CMSIS_core_register
1631:Drivers/CMSIS/Include/core_cm7.h ****   \defgroup CMSIS_CoreDebug       Core Debug Registers (CoreDebug)
1632:Drivers/CMSIS/Include/core_cm7.h ****   \brief    Type definitions for the Core Debug Registers
1633:Drivers/CMSIS/Include/core_cm7.h ****   @{
1634:Drivers/CMSIS/Include/core_cm7.h ****  */
1635:Drivers/CMSIS/Include/core_cm7.h **** 
1636:Drivers/CMSIS/Include/core_cm7.h **** /**
1637:Drivers/CMSIS/Include/core_cm7.h ****   \brief  Structure type to access the Core Debug Register (CoreDebug).
1638:Drivers/CMSIS/Include/core_cm7.h ****  */
1639:Drivers/CMSIS/Include/core_cm7.h **** typedef struct
1640:Drivers/CMSIS/Include/core_cm7.h **** {
1641:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t DHCSR;                  /*!< Offset: 0x000 (R/W)  Debug Halting Control and Status
1642:Drivers/CMSIS/Include/core_cm7.h ****   __OM  uint32_t DCRSR;                  /*!< Offset: 0x004 ( /W)  Debug Core Register Selector Reg
1643:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t DCRDR;                  /*!< Offset: 0x008 (R/W)  Debug Core Register Data Registe
1644:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t DEMCR;                  /*!< Offset: 0x00C (R/W)  Debug Exception and Monitor Cont
1645:Drivers/CMSIS/Include/core_cm7.h **** } CoreDebug_Type;
1646:Drivers/CMSIS/Include/core_cm7.h **** 
1647:Drivers/CMSIS/Include/core_cm7.h **** /* Debug Halting Control and Status Register Definitions */
1648:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DHCSR_DBGKEY_Pos         16U                                            /*!< Core
1649:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DHCSR_DBGKEY_Msk         (0xFFFFUL << CoreDebug_DHCSR_DBGKEY_Pos)       /*!< Core
1650:Drivers/CMSIS/Include/core_cm7.h **** 
1651:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DHCSR_S_RESET_ST_Pos     25U                                            /*!< Core
1652:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DHCSR_S_RESET_ST_Msk     (1UL << CoreDebug_DHCSR_S_RESET_ST_Pos)        /*!< Core
1653:Drivers/CMSIS/Include/core_cm7.h **** 
1654:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DHCSR_S_RETIRE_ST_Pos    24U                                            /*!< Core
1655:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DHCSR_S_RETIRE_ST_Msk    (1UL << CoreDebug_DHCSR_S_RETIRE_ST_Pos)       /*!< Core
1656:Drivers/CMSIS/Include/core_cm7.h **** 
1657:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DHCSR_S_LOCKUP_Pos       19U                                            /*!< Core
1658:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DHCSR_S_LOCKUP_Msk       (1UL << CoreDebug_DHCSR_S_LOCKUP_Pos)          /*!< Core
1659:Drivers/CMSIS/Include/core_cm7.h **** 
1660:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DHCSR_S_SLEEP_Pos        18U                                            /*!< Core
1661:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DHCSR_S_SLEEP_Msk        (1UL << CoreDebug_DHCSR_S_SLEEP_Pos)           /*!< Core
1662:Drivers/CMSIS/Include/core_cm7.h **** 
1663:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DHCSR_S_HALT_Pos         17U                                            /*!< Core
1664:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DHCSR_S_HALT_Msk         (1UL << CoreDebug_DHCSR_S_HALT_Pos)            /*!< Core
1665:Drivers/CMSIS/Include/core_cm7.h **** 
1666:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DHCSR_S_REGRDY_Pos       16U                                            /*!< Core
1667:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DHCSR_S_REGRDY_Msk       (1UL << CoreDebug_DHCSR_S_REGRDY_Pos)          /*!< Core
1668:Drivers/CMSIS/Include/core_cm7.h **** 
1669:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DHCSR_C_SNAPSTALL_Pos     5U                                            /*!< Core
1670:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DHCSR_C_SNAPSTALL_Msk    (1UL << CoreDebug_DHCSR_C_SNAPSTALL_Pos)       /*!< Core
1671:Drivers/CMSIS/Include/core_cm7.h **** 
1672:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DHCSR_C_MASKINTS_Pos      3U                                            /*!< Core
1673:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DHCSR_C_MASKINTS_Msk     (1UL << CoreDebug_DHCSR_C_MASKINTS_Pos)        /*!< Core
1674:Drivers/CMSIS/Include/core_cm7.h **** 
1675:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DHCSR_C_STEP_Pos          2U                                            /*!< Core
1676:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DHCSR_C_STEP_Msk         (1UL << CoreDebug_DHCSR_C_STEP_Pos)            /*!< Core
1677:Drivers/CMSIS/Include/core_cm7.h **** 
1678:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DHCSR_C_HALT_Pos          1U                                            /*!< Core
1679:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DHCSR_C_HALT_Msk         (1UL << CoreDebug_DHCSR_C_HALT_Pos)            /*!< Core
1680:Drivers/CMSIS/Include/core_cm7.h **** 
1681:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DHCSR_C_DEBUGEN_Pos       0U                                            /*!< Core
1682:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DHCSR_C_DEBUGEN_Msk      (1UL /*<< CoreDebug_DHCSR_C_DEBUGEN_Pos*/)     /*!< Core
1683:Drivers/CMSIS/Include/core_cm7.h **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 31


1684:Drivers/CMSIS/Include/core_cm7.h **** /* Debug Core Register Selector Register Definitions */
1685:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DCRSR_REGWnR_Pos         16U                                            /*!< Core
1686:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DCRSR_REGWnR_Msk         (1UL << CoreDebug_DCRSR_REGWnR_Pos)            /*!< Core
1687:Drivers/CMSIS/Include/core_cm7.h **** 
1688:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DCRSR_REGSEL_Pos          0U                                            /*!< Core
1689:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DCRSR_REGSEL_Msk         (0x1FUL /*<< CoreDebug_DCRSR_REGSEL_Pos*/)     /*!< Core
1690:Drivers/CMSIS/Include/core_cm7.h **** 
1691:Drivers/CMSIS/Include/core_cm7.h **** /* Debug Exception and Monitor Control Register Definitions */
1692:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DEMCR_TRCENA_Pos         24U                                            /*!< Core
1693:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DEMCR_TRCENA_Msk         (1UL << CoreDebug_DEMCR_TRCENA_Pos)            /*!< Core
1694:Drivers/CMSIS/Include/core_cm7.h **** 
1695:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DEMCR_MON_REQ_Pos        19U                                            /*!< Core
1696:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DEMCR_MON_REQ_Msk        (1UL << CoreDebug_DEMCR_MON_REQ_Pos)           /*!< Core
1697:Drivers/CMSIS/Include/core_cm7.h **** 
1698:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DEMCR_MON_STEP_Pos       18U                                            /*!< Core
1699:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DEMCR_MON_STEP_Msk       (1UL << CoreDebug_DEMCR_MON_STEP_Pos)          /*!< Core
1700:Drivers/CMSIS/Include/core_cm7.h **** 
1701:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DEMCR_MON_PEND_Pos       17U                                            /*!< Core
1702:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DEMCR_MON_PEND_Msk       (1UL << CoreDebug_DEMCR_MON_PEND_Pos)          /*!< Core
1703:Drivers/CMSIS/Include/core_cm7.h **** 
1704:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DEMCR_MON_EN_Pos         16U                                            /*!< Core
1705:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DEMCR_MON_EN_Msk         (1UL << CoreDebug_DEMCR_MON_EN_Pos)            /*!< Core
1706:Drivers/CMSIS/Include/core_cm7.h **** 
1707:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DEMCR_VC_HARDERR_Pos     10U                                            /*!< Core
1708:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DEMCR_VC_HARDERR_Msk     (1UL << CoreDebug_DEMCR_VC_HARDERR_Pos)        /*!< Core
1709:Drivers/CMSIS/Include/core_cm7.h **** 
1710:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DEMCR_VC_INTERR_Pos       9U                                            /*!< Core
1711:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DEMCR_VC_INTERR_Msk      (1UL << CoreDebug_DEMCR_VC_INTERR_Pos)         /*!< Core
1712:Drivers/CMSIS/Include/core_cm7.h **** 
1713:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DEMCR_VC_BUSERR_Pos       8U                                            /*!< Core
1714:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DEMCR_VC_BUSERR_Msk      (1UL << CoreDebug_DEMCR_VC_BUSERR_Pos)         /*!< Core
1715:Drivers/CMSIS/Include/core_cm7.h **** 
1716:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DEMCR_VC_STATERR_Pos      7U                                            /*!< Core
1717:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DEMCR_VC_STATERR_Msk     (1UL << CoreDebug_DEMCR_VC_STATERR_Pos)        /*!< Core
1718:Drivers/CMSIS/Include/core_cm7.h **** 
1719:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DEMCR_VC_CHKERR_Pos       6U                                            /*!< Core
1720:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DEMCR_VC_CHKERR_Msk      (1UL << CoreDebug_DEMCR_VC_CHKERR_Pos)         /*!< Core
1721:Drivers/CMSIS/Include/core_cm7.h **** 
1722:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DEMCR_VC_NOCPERR_Pos      5U                                            /*!< Core
1723:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DEMCR_VC_NOCPERR_Msk     (1UL << CoreDebug_DEMCR_VC_NOCPERR_Pos)        /*!< Core
1724:Drivers/CMSIS/Include/core_cm7.h **** 
1725:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DEMCR_VC_MMERR_Pos        4U                                            /*!< Core
1726:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DEMCR_VC_MMERR_Msk       (1UL << CoreDebug_DEMCR_VC_MMERR_Pos)          /*!< Core
1727:Drivers/CMSIS/Include/core_cm7.h **** 
1728:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DEMCR_VC_CORERESET_Pos    0U                                            /*!< Core
1729:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DEMCR_VC_CORERESET_Msk   (1UL /*<< CoreDebug_DEMCR_VC_CORERESET_Pos*/)  /*!< Core
1730:Drivers/CMSIS/Include/core_cm7.h **** 
1731:Drivers/CMSIS/Include/core_cm7.h **** /*@} end of group CMSIS_CoreDebug */
1732:Drivers/CMSIS/Include/core_cm7.h **** 
1733:Drivers/CMSIS/Include/core_cm7.h **** 
1734:Drivers/CMSIS/Include/core_cm7.h **** /**
1735:Drivers/CMSIS/Include/core_cm7.h ****   \ingroup    CMSIS_core_register
1736:Drivers/CMSIS/Include/core_cm7.h ****   \defgroup   CMSIS_core_bitfield     Core register bit field macros
1737:Drivers/CMSIS/Include/core_cm7.h ****   \brief      Macros for use with bit field definitions (xxx_Pos, xxx_Msk).
1738:Drivers/CMSIS/Include/core_cm7.h ****   @{
1739:Drivers/CMSIS/Include/core_cm7.h ****  */
1740:Drivers/CMSIS/Include/core_cm7.h **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 32


1741:Drivers/CMSIS/Include/core_cm7.h **** /**
1742:Drivers/CMSIS/Include/core_cm7.h ****   \brief   Mask and shift a bit field value for use in a register bit range.
1743:Drivers/CMSIS/Include/core_cm7.h ****   \param[in] field  Name of the register bit field.
1744:Drivers/CMSIS/Include/core_cm7.h ****   \param[in] value  Value of the bit field. This parameter is interpreted as an uint32_t type.
1745:Drivers/CMSIS/Include/core_cm7.h ****   \return           Masked and shifted value.
1746:Drivers/CMSIS/Include/core_cm7.h **** */
1747:Drivers/CMSIS/Include/core_cm7.h **** #define _VAL2FLD(field, value)    (((uint32_t)(value) << field ## _Pos) & field ## _Msk)
1748:Drivers/CMSIS/Include/core_cm7.h **** 
1749:Drivers/CMSIS/Include/core_cm7.h **** /**
1750:Drivers/CMSIS/Include/core_cm7.h ****   \brief     Mask and shift a register value to extract a bit filed value.
1751:Drivers/CMSIS/Include/core_cm7.h ****   \param[in] field  Name of the register bit field.
1752:Drivers/CMSIS/Include/core_cm7.h ****   \param[in] value  Value of register. This parameter is interpreted as an uint32_t type.
1753:Drivers/CMSIS/Include/core_cm7.h ****   \return           Masked and shifted bit field value.
1754:Drivers/CMSIS/Include/core_cm7.h **** */
1755:Drivers/CMSIS/Include/core_cm7.h **** #define _FLD2VAL(field, value)    (((uint32_t)(value) & field ## _Msk) >> field ## _Pos)
1756:Drivers/CMSIS/Include/core_cm7.h **** 
1757:Drivers/CMSIS/Include/core_cm7.h **** /*@} end of group CMSIS_core_bitfield */
1758:Drivers/CMSIS/Include/core_cm7.h **** 
1759:Drivers/CMSIS/Include/core_cm7.h **** 
1760:Drivers/CMSIS/Include/core_cm7.h **** /**
1761:Drivers/CMSIS/Include/core_cm7.h ****   \ingroup    CMSIS_core_register
1762:Drivers/CMSIS/Include/core_cm7.h ****   \defgroup   CMSIS_core_base     Core Definitions
1763:Drivers/CMSIS/Include/core_cm7.h ****   \brief      Definitions for base addresses, unions, and structures.
1764:Drivers/CMSIS/Include/core_cm7.h ****   @{
1765:Drivers/CMSIS/Include/core_cm7.h ****  */
1766:Drivers/CMSIS/Include/core_cm7.h **** 
1767:Drivers/CMSIS/Include/core_cm7.h **** /* Memory mapping of Core Hardware */
1768:Drivers/CMSIS/Include/core_cm7.h **** #define SCS_BASE            (0xE000E000UL)                            /*!< System Control Space Bas
1769:Drivers/CMSIS/Include/core_cm7.h **** #define ITM_BASE            (0xE0000000UL)                            /*!< ITM Base Address */
1770:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_BASE            (0xE0001000UL)                            /*!< DWT Base Address */
1771:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_BASE            (0xE0040000UL)                            /*!< TPI Base Address */
1772:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_BASE      (0xE000EDF0UL)                            /*!< Core Debug Base Address 
1773:Drivers/CMSIS/Include/core_cm7.h **** #define SysTick_BASE        (SCS_BASE +  0x0010UL)                    /*!< SysTick Base Address */
1774:Drivers/CMSIS/Include/core_cm7.h **** #define NVIC_BASE           (SCS_BASE +  0x0100UL)                    /*!< NVIC Base Address */
1775:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_BASE            (SCS_BASE +  0x0D00UL)                    /*!< System Control Block Bas
1776:Drivers/CMSIS/Include/core_cm7.h **** 
1777:Drivers/CMSIS/Include/core_cm7.h **** #define SCnSCB              ((SCnSCB_Type    *)     SCS_BASE      )   /*!< System control Register 
1778:Drivers/CMSIS/Include/core_cm7.h **** #define SCB                 ((SCB_Type       *)     SCB_BASE      )   /*!< SCB configuration struct
1779:Drivers/CMSIS/Include/core_cm7.h **** #define SysTick             ((SysTick_Type   *)     SysTick_BASE  )   /*!< SysTick configuration st
1780:Drivers/CMSIS/Include/core_cm7.h **** #define NVIC                ((NVIC_Type      *)     NVIC_BASE     )   /*!< NVIC configuration struc
1781:Drivers/CMSIS/Include/core_cm7.h **** #define ITM                 ((ITM_Type       *)     ITM_BASE      )   /*!< ITM configuration struct
1782:Drivers/CMSIS/Include/core_cm7.h **** #define DWT                 ((DWT_Type       *)     DWT_BASE      )   /*!< DWT configuration struct
1783:Drivers/CMSIS/Include/core_cm7.h **** #define TPI                 ((TPI_Type       *)     TPI_BASE      )   /*!< TPI configuration struct
1784:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug           ((CoreDebug_Type *)     CoreDebug_BASE)   /*!< Core Debug configuration
1785:Drivers/CMSIS/Include/core_cm7.h **** 
1786:Drivers/CMSIS/Include/core_cm7.h **** #if defined (__MPU_PRESENT) && (__MPU_PRESENT == 1U)
1787:Drivers/CMSIS/Include/core_cm7.h ****   #define MPU_BASE          (SCS_BASE +  0x0D90UL)                    /*!< Memory Protection Unit *
1788:Drivers/CMSIS/Include/core_cm7.h ****   #define MPU               ((MPU_Type       *)     MPU_BASE      )   /*!< Memory Protection Unit *
1789:Drivers/CMSIS/Include/core_cm7.h **** #endif
1790:Drivers/CMSIS/Include/core_cm7.h **** 
1791:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_BASE            (SCS_BASE +  0x0F30UL)                    /*!< Floating Point Unit */
1792:Drivers/CMSIS/Include/core_cm7.h **** #define FPU                 ((FPU_Type       *)     FPU_BASE      )   /*!< Floating Point Unit */
1793:Drivers/CMSIS/Include/core_cm7.h **** 
1794:Drivers/CMSIS/Include/core_cm7.h **** /*@} */
1795:Drivers/CMSIS/Include/core_cm7.h **** 
1796:Drivers/CMSIS/Include/core_cm7.h **** 
1797:Drivers/CMSIS/Include/core_cm7.h **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 33


1798:Drivers/CMSIS/Include/core_cm7.h **** /*******************************************************************************
1799:Drivers/CMSIS/Include/core_cm7.h ****  *                Hardware Abstraction Layer
1800:Drivers/CMSIS/Include/core_cm7.h ****   Core Function Interface contains:
1801:Drivers/CMSIS/Include/core_cm7.h ****   - Core NVIC Functions
1802:Drivers/CMSIS/Include/core_cm7.h ****   - Core SysTick Functions
1803:Drivers/CMSIS/Include/core_cm7.h ****   - Core Debug Functions
1804:Drivers/CMSIS/Include/core_cm7.h ****   - Core Register Access Functions
1805:Drivers/CMSIS/Include/core_cm7.h ****  ******************************************************************************/
1806:Drivers/CMSIS/Include/core_cm7.h **** /**
1807:Drivers/CMSIS/Include/core_cm7.h ****   \defgroup CMSIS_Core_FunctionInterface Functions and Instructions Reference
1808:Drivers/CMSIS/Include/core_cm7.h **** */
1809:Drivers/CMSIS/Include/core_cm7.h **** 
1810:Drivers/CMSIS/Include/core_cm7.h **** 
1811:Drivers/CMSIS/Include/core_cm7.h **** 
1812:Drivers/CMSIS/Include/core_cm7.h **** /* ##########################   NVIC functions  #################################### */
1813:Drivers/CMSIS/Include/core_cm7.h **** /**
1814:Drivers/CMSIS/Include/core_cm7.h ****   \ingroup  CMSIS_Core_FunctionInterface
1815:Drivers/CMSIS/Include/core_cm7.h ****   \defgroup CMSIS_Core_NVICFunctions NVIC Functions
1816:Drivers/CMSIS/Include/core_cm7.h ****   \brief    Functions that manage interrupts and exceptions via the NVIC.
1817:Drivers/CMSIS/Include/core_cm7.h ****   @{
1818:Drivers/CMSIS/Include/core_cm7.h ****  */
1819:Drivers/CMSIS/Include/core_cm7.h **** 
1820:Drivers/CMSIS/Include/core_cm7.h **** #ifdef CMSIS_NVIC_VIRTUAL
1821:Drivers/CMSIS/Include/core_cm7.h ****   #ifndef CMSIS_NVIC_VIRTUAL_HEADER_FILE
1822:Drivers/CMSIS/Include/core_cm7.h ****     #define CMSIS_NVIC_VIRTUAL_HEADER_FILE "cmsis_nvic_virtual.h"
1823:Drivers/CMSIS/Include/core_cm7.h ****   #endif
1824:Drivers/CMSIS/Include/core_cm7.h ****   #include CMSIS_NVIC_VIRTUAL_HEADER_FILE
1825:Drivers/CMSIS/Include/core_cm7.h **** #else
1826:Drivers/CMSIS/Include/core_cm7.h ****   #define NVIC_SetPriorityGrouping    __NVIC_SetPriorityGrouping
1827:Drivers/CMSIS/Include/core_cm7.h ****   #define NVIC_GetPriorityGrouping    __NVIC_GetPriorityGrouping
1828:Drivers/CMSIS/Include/core_cm7.h ****   #define NVIC_EnableIRQ              __NVIC_EnableIRQ
1829:Drivers/CMSIS/Include/core_cm7.h ****   #define NVIC_GetEnableIRQ           __NVIC_GetEnableIRQ
1830:Drivers/CMSIS/Include/core_cm7.h ****   #define NVIC_DisableIRQ             __NVIC_DisableIRQ
1831:Drivers/CMSIS/Include/core_cm7.h ****   #define NVIC_GetPendingIRQ          __NVIC_GetPendingIRQ
1832:Drivers/CMSIS/Include/core_cm7.h ****   #define NVIC_SetPendingIRQ          __NVIC_SetPendingIRQ
1833:Drivers/CMSIS/Include/core_cm7.h ****   #define NVIC_ClearPendingIRQ        __NVIC_ClearPendingIRQ
1834:Drivers/CMSIS/Include/core_cm7.h ****   #define NVIC_GetActive              __NVIC_GetActive
1835:Drivers/CMSIS/Include/core_cm7.h ****   #define NVIC_SetPriority            __NVIC_SetPriority
1836:Drivers/CMSIS/Include/core_cm7.h ****   #define NVIC_GetPriority            __NVIC_GetPriority
1837:Drivers/CMSIS/Include/core_cm7.h ****   #define NVIC_SystemReset            __NVIC_SystemReset
1838:Drivers/CMSIS/Include/core_cm7.h **** #endif /* CMSIS_NVIC_VIRTUAL */
1839:Drivers/CMSIS/Include/core_cm7.h **** 
1840:Drivers/CMSIS/Include/core_cm7.h **** #ifdef CMSIS_VECTAB_VIRTUAL
1841:Drivers/CMSIS/Include/core_cm7.h ****   #ifndef CMSIS_VECTAB_VIRTUAL_HEADER_FILE
1842:Drivers/CMSIS/Include/core_cm7.h ****     #define CMSIS_VECTAB_VIRTUAL_HEADER_FILE "cmsis_vectab_virtual.h"
1843:Drivers/CMSIS/Include/core_cm7.h ****   #endif
1844:Drivers/CMSIS/Include/core_cm7.h ****   #include CMSIS_VECTAB_VIRTUAL_HEADER_FILE
1845:Drivers/CMSIS/Include/core_cm7.h **** #else
1846:Drivers/CMSIS/Include/core_cm7.h ****   #define NVIC_SetVector              __NVIC_SetVector
1847:Drivers/CMSIS/Include/core_cm7.h ****   #define NVIC_GetVector              __NVIC_GetVector
1848:Drivers/CMSIS/Include/core_cm7.h **** #endif  /* (CMSIS_VECTAB_VIRTUAL) */
1849:Drivers/CMSIS/Include/core_cm7.h **** 
1850:Drivers/CMSIS/Include/core_cm7.h **** #define NVIC_USER_IRQ_OFFSET          16
1851:Drivers/CMSIS/Include/core_cm7.h **** 
1852:Drivers/CMSIS/Include/core_cm7.h **** 
1853:Drivers/CMSIS/Include/core_cm7.h **** /* The following EXC_RETURN values are saved the LR on exception entry */
1854:Drivers/CMSIS/Include/core_cm7.h **** #define EXC_RETURN_HANDLER         (0xFFFFFFF1UL)     /* return to Handler mode, uses MSP after ret
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 34


1855:Drivers/CMSIS/Include/core_cm7.h **** #define EXC_RETURN_THREAD_MSP      (0xFFFFFFF9UL)     /* return to Thread mode, uses MSP after retu
1856:Drivers/CMSIS/Include/core_cm7.h **** #define EXC_RETURN_THREAD_PSP      (0xFFFFFFFDUL)     /* return to Thread mode, uses PSP after retu
1857:Drivers/CMSIS/Include/core_cm7.h **** #define EXC_RETURN_HANDLER_FPU     (0xFFFFFFE1UL)     /* return to Handler mode, uses MSP after ret
1858:Drivers/CMSIS/Include/core_cm7.h **** #define EXC_RETURN_THREAD_MSP_FPU  (0xFFFFFFE9UL)     /* return to Thread mode, uses MSP after retu
1859:Drivers/CMSIS/Include/core_cm7.h **** #define EXC_RETURN_THREAD_PSP_FPU  (0xFFFFFFEDUL)     /* return to Thread mode, uses PSP after retu
1860:Drivers/CMSIS/Include/core_cm7.h **** 
1861:Drivers/CMSIS/Include/core_cm7.h **** 
1862:Drivers/CMSIS/Include/core_cm7.h **** /**
1863:Drivers/CMSIS/Include/core_cm7.h ****   \brief   Set Priority Grouping
1864:Drivers/CMSIS/Include/core_cm7.h ****   \details Sets the priority grouping field using the required unlock sequence.
1865:Drivers/CMSIS/Include/core_cm7.h ****            The parameter PriorityGroup is assigned to the field SCB->AIRCR [10:8] PRIGROUP field.
1866:Drivers/CMSIS/Include/core_cm7.h ****            Only values from 0..7 are used.
1867:Drivers/CMSIS/Include/core_cm7.h ****            In case of a conflict between priority grouping and available
1868:Drivers/CMSIS/Include/core_cm7.h ****            priority bits (__NVIC_PRIO_BITS), the smallest possible priority group is set.
1869:Drivers/CMSIS/Include/core_cm7.h ****   \param [in]      PriorityGroup  Priority grouping field.
1870:Drivers/CMSIS/Include/core_cm7.h ****  */
1871:Drivers/CMSIS/Include/core_cm7.h **** __STATIC_INLINE void __NVIC_SetPriorityGrouping(uint32_t PriorityGroup)
1872:Drivers/CMSIS/Include/core_cm7.h **** {
1873:Drivers/CMSIS/Include/core_cm7.h ****   uint32_t reg_value;
1874:Drivers/CMSIS/Include/core_cm7.h ****   uint32_t PriorityGroupTmp = (PriorityGroup & (uint32_t)0x07UL);             /* only values 0..7 a
1875:Drivers/CMSIS/Include/core_cm7.h **** 
1876:Drivers/CMSIS/Include/core_cm7.h ****   reg_value  =  SCB->AIRCR;                                                   /* read old register 
1877:Drivers/CMSIS/Include/core_cm7.h ****   reg_value &= ~((uint32_t)(SCB_AIRCR_VECTKEY_Msk | SCB_AIRCR_PRIGROUP_Msk)); /* clear bits to chan
1878:Drivers/CMSIS/Include/core_cm7.h ****   reg_value  =  (reg_value                                   |
1879:Drivers/CMSIS/Include/core_cm7.h ****                 ((uint32_t)0x5FAUL << SCB_AIRCR_VECTKEY_Pos) |
1880:Drivers/CMSIS/Include/core_cm7.h ****                 (PriorityGroupTmp << SCB_AIRCR_PRIGROUP_Pos)  );              /* Insert write key a
1881:Drivers/CMSIS/Include/core_cm7.h ****   SCB->AIRCR =  reg_value;
1882:Drivers/CMSIS/Include/core_cm7.h **** }
1883:Drivers/CMSIS/Include/core_cm7.h **** 
1884:Drivers/CMSIS/Include/core_cm7.h **** 
1885:Drivers/CMSIS/Include/core_cm7.h **** /**
1886:Drivers/CMSIS/Include/core_cm7.h ****   \brief   Get Priority Grouping
1887:Drivers/CMSIS/Include/core_cm7.h ****   \details Reads the priority grouping field from the NVIC Interrupt Controller.
1888:Drivers/CMSIS/Include/core_cm7.h ****   \return                Priority grouping field (SCB->AIRCR [10:8] PRIGROUP field).
1889:Drivers/CMSIS/Include/core_cm7.h ****  */
1890:Drivers/CMSIS/Include/core_cm7.h **** __STATIC_INLINE uint32_t __NVIC_GetPriorityGrouping(void)
1891:Drivers/CMSIS/Include/core_cm7.h **** {
1892:Drivers/CMSIS/Include/core_cm7.h ****   return ((uint32_t)((SCB->AIRCR & SCB_AIRCR_PRIGROUP_Msk) >> SCB_AIRCR_PRIGROUP_Pos));
1893:Drivers/CMSIS/Include/core_cm7.h **** }
1894:Drivers/CMSIS/Include/core_cm7.h **** 
1895:Drivers/CMSIS/Include/core_cm7.h **** 
1896:Drivers/CMSIS/Include/core_cm7.h **** /**
1897:Drivers/CMSIS/Include/core_cm7.h ****   \brief   Enable Interrupt
1898:Drivers/CMSIS/Include/core_cm7.h ****   \details Enables a device specific interrupt in the NVIC interrupt controller.
1899:Drivers/CMSIS/Include/core_cm7.h ****   \param [in]      IRQn  Device specific interrupt number.
1900:Drivers/CMSIS/Include/core_cm7.h ****   \note    IRQn must not be negative.
1901:Drivers/CMSIS/Include/core_cm7.h ****  */
1902:Drivers/CMSIS/Include/core_cm7.h **** __STATIC_INLINE void __NVIC_EnableIRQ(IRQn_Type IRQn)
1903:Drivers/CMSIS/Include/core_cm7.h **** {
  29              		.loc 2 1903 1 view -0
  30              		.cfi_startproc
  31              		@ args = 0, pretend = 0, frame = 0
  32              		@ frame_needed = 0, uses_anonymous_args = 0
  33              		@ link register save eliminated.
1904:Drivers/CMSIS/Include/core_cm7.h ****   if ((int32_t)(IRQn) >= 0)
  34              		.loc 2 1904 3 view .LVU1
  35              		.loc 2 1904 6 is_stmt 0 view .LVU2
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 35


  36 0000 0028     		cmp	r0, #0
  37              		.loc 2 1904 6 view .LVU3
  38 0002 07DB     		blt	.L1
1905:Drivers/CMSIS/Include/core_cm7.h ****   {
1906:Drivers/CMSIS/Include/core_cm7.h ****     __COMPILER_BARRIER();
  39              		.loc 2 1906 5 is_stmt 1 view .LVU4
1907:Drivers/CMSIS/Include/core_cm7.h ****     NVIC->ISER[(((uint32_t)IRQn) >> 5UL)] = (uint32_t)(1UL << (((uint32_t)IRQn) & 0x1FUL));
  40              		.loc 2 1907 5 view .LVU5
  41              		.loc 2 1907 81 is_stmt 0 view .LVU6
  42 0004 00F01F02 		and	r2, r0, #31
  43              		.loc 2 1907 34 view .LVU7
  44 0008 4009     		lsrs	r0, r0, #5
  45              	.LVL1:
  46              		.loc 2 1907 45 view .LVU8
  47 000a 0123     		movs	r3, #1
  48 000c 9340     		lsls	r3, r3, r2
  49              		.loc 2 1907 43 view .LVU9
  50 000e 024A     		ldr	r2, .L3
  51 0010 42F82030 		str	r3, [r2, r0, lsl #2]
1908:Drivers/CMSIS/Include/core_cm7.h ****     __COMPILER_BARRIER();
  52              		.loc 2 1908 5 is_stmt 1 view .LVU10
  53              	.L1:
1909:Drivers/CMSIS/Include/core_cm7.h ****   }
1910:Drivers/CMSIS/Include/core_cm7.h **** }
  54              		.loc 2 1910 1 is_stmt 0 view .LVU11
  55 0014 7047     		bx	lr
  56              	.L4:
  57 0016 00BF     		.align	2
  58              	.L3:
  59 0018 00E100E0 		.word	-*********
  60              		.cfi_endproc
  61              	.LFE106:
  63              		.section	.text.__NVIC_DisableIRQ,"ax",%progbits
  64              		.align	1
  65              		.syntax unified
  66              		.thumb
  67              		.thumb_func
  69              	__NVIC_DisableIRQ:
  70              	.LVL2:
  71              	.LFB108:
1911:Drivers/CMSIS/Include/core_cm7.h **** 
1912:Drivers/CMSIS/Include/core_cm7.h **** 
1913:Drivers/CMSIS/Include/core_cm7.h **** /**
1914:Drivers/CMSIS/Include/core_cm7.h ****   \brief   Get Interrupt Enable status
1915:Drivers/CMSIS/Include/core_cm7.h ****   \details Returns a device specific interrupt enable status from the NVIC interrupt controller.
1916:Drivers/CMSIS/Include/core_cm7.h ****   \param [in]      IRQn  Device specific interrupt number.
1917:Drivers/CMSIS/Include/core_cm7.h ****   \return             0  Interrupt is not enabled.
1918:Drivers/CMSIS/Include/core_cm7.h ****   \return             1  Interrupt is enabled.
1919:Drivers/CMSIS/Include/core_cm7.h ****   \note    IRQn must not be negative.
1920:Drivers/CMSIS/Include/core_cm7.h ****  */
1921:Drivers/CMSIS/Include/core_cm7.h **** __STATIC_INLINE uint32_t __NVIC_GetEnableIRQ(IRQn_Type IRQn)
1922:Drivers/CMSIS/Include/core_cm7.h **** {
1923:Drivers/CMSIS/Include/core_cm7.h ****   if ((int32_t)(IRQn) >= 0)
1924:Drivers/CMSIS/Include/core_cm7.h ****   {
1925:Drivers/CMSIS/Include/core_cm7.h ****     return((uint32_t)(((NVIC->ISER[(((uint32_t)IRQn) >> 5UL)] & (1UL << (((uint32_t)IRQn) & 0x1FUL)
1926:Drivers/CMSIS/Include/core_cm7.h ****   }
1927:Drivers/CMSIS/Include/core_cm7.h ****   else
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 36


1928:Drivers/CMSIS/Include/core_cm7.h ****   {
1929:Drivers/CMSIS/Include/core_cm7.h ****     return(0U);
1930:Drivers/CMSIS/Include/core_cm7.h ****   }
1931:Drivers/CMSIS/Include/core_cm7.h **** }
1932:Drivers/CMSIS/Include/core_cm7.h **** 
1933:Drivers/CMSIS/Include/core_cm7.h **** 
1934:Drivers/CMSIS/Include/core_cm7.h **** /**
1935:Drivers/CMSIS/Include/core_cm7.h ****   \brief   Disable Interrupt
1936:Drivers/CMSIS/Include/core_cm7.h ****   \details Disables a device specific interrupt in the NVIC interrupt controller.
1937:Drivers/CMSIS/Include/core_cm7.h ****   \param [in]      IRQn  Device specific interrupt number.
1938:Drivers/CMSIS/Include/core_cm7.h ****   \note    IRQn must not be negative.
1939:Drivers/CMSIS/Include/core_cm7.h ****  */
1940:Drivers/CMSIS/Include/core_cm7.h **** __STATIC_INLINE void __NVIC_DisableIRQ(IRQn_Type IRQn)
1941:Drivers/CMSIS/Include/core_cm7.h **** {
  72              		.loc 2 1941 1 is_stmt 1 view -0
  73              		.cfi_startproc
  74              		@ args = 0, pretend = 0, frame = 0
  75              		@ frame_needed = 0, uses_anonymous_args = 0
  76              		@ link register save eliminated.
1942:Drivers/CMSIS/Include/core_cm7.h ****   if ((int32_t)(IRQn) >= 0)
  77              		.loc 2 1942 3 view .LVU13
  78              		.loc 2 1942 6 is_stmt 0 view .LVU14
  79 0000 0028     		cmp	r0, #0
  80              		.loc 2 1942 6 view .LVU15
  81 0002 0CDB     		blt	.L5
1943:Drivers/CMSIS/Include/core_cm7.h ****   {
1944:Drivers/CMSIS/Include/core_cm7.h ****     NVIC->ICER[(((uint32_t)IRQn) >> 5UL)] = (uint32_t)(1UL << (((uint32_t)IRQn) & 0x1FUL));
  82              		.loc 2 1944 5 is_stmt 1 view .LVU16
  83              		.loc 2 1944 81 is_stmt 0 view .LVU17
  84 0004 00F01F02 		and	r2, r0, #31
  85              		.loc 2 1944 34 view .LVU18
  86 0008 4009     		lsrs	r0, r0, #5
  87              	.LVL3:
  88              		.loc 2 1944 45 view .LVU19
  89 000a 0123     		movs	r3, #1
  90 000c 9340     		lsls	r3, r3, r2
  91              		.loc 2 1944 43 view .LVU20
  92 000e 2030     		adds	r0, r0, #32
  93 0010 034A     		ldr	r2, .L7
  94 0012 42F82030 		str	r3, [r2, r0, lsl #2]
1945:Drivers/CMSIS/Include/core_cm7.h ****     __DSB();
  95              		.loc 2 1945 5 is_stmt 1 view .LVU21
  96              	.LBB36:
  97              	.LBI36:
  98              		.file 3 "Drivers/CMSIS/Include/cmsis_gcc.h"
   1:Drivers/CMSIS/Include/cmsis_gcc.h **** /**************************************************************************//**
   2:Drivers/CMSIS/Include/cmsis_gcc.h ****  * @file     cmsis_gcc.h
   3:Drivers/CMSIS/Include/cmsis_gcc.h ****  * @brief    CMSIS compiler GCC header file
   4:Drivers/CMSIS/Include/cmsis_gcc.h ****  * @version  V5.2.0
   5:Drivers/CMSIS/Include/cmsis_gcc.h ****  * @date     08. May 2019
   6:Drivers/CMSIS/Include/cmsis_gcc.h ****  ******************************************************************************/
   7:Drivers/CMSIS/Include/cmsis_gcc.h **** /*
   8:Drivers/CMSIS/Include/cmsis_gcc.h ****  * Copyright (c) 2009-2019 Arm Limited. All rights reserved.
   9:Drivers/CMSIS/Include/cmsis_gcc.h ****  *
  10:Drivers/CMSIS/Include/cmsis_gcc.h ****  * SPDX-License-Identifier: Apache-2.0
  11:Drivers/CMSIS/Include/cmsis_gcc.h ****  *
  12:Drivers/CMSIS/Include/cmsis_gcc.h ****  * Licensed under the Apache License, Version 2.0 (the License); you may
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 37


  13:Drivers/CMSIS/Include/cmsis_gcc.h ****  * not use this file except in compliance with the License.
  14:Drivers/CMSIS/Include/cmsis_gcc.h ****  * You may obtain a copy of the License at
  15:Drivers/CMSIS/Include/cmsis_gcc.h ****  *
  16:Drivers/CMSIS/Include/cmsis_gcc.h ****  * www.apache.org/licenses/LICENSE-2.0
  17:Drivers/CMSIS/Include/cmsis_gcc.h ****  *
  18:Drivers/CMSIS/Include/cmsis_gcc.h ****  * Unless required by applicable law or agreed to in writing, software
  19:Drivers/CMSIS/Include/cmsis_gcc.h ****  * distributed under the License is distributed on an AS IS BASIS, WITHOUT
  20:Drivers/CMSIS/Include/cmsis_gcc.h ****  * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  21:Drivers/CMSIS/Include/cmsis_gcc.h ****  * See the License for the specific language governing permissions and
  22:Drivers/CMSIS/Include/cmsis_gcc.h ****  * limitations under the License.
  23:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
  24:Drivers/CMSIS/Include/cmsis_gcc.h **** 
  25:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef __CMSIS_GCC_H
  26:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __CMSIS_GCC_H
  27:Drivers/CMSIS/Include/cmsis_gcc.h **** 
  28:Drivers/CMSIS/Include/cmsis_gcc.h **** /* ignore some GCC warnings */
  29:Drivers/CMSIS/Include/cmsis_gcc.h **** #pragma GCC diagnostic push
  30:Drivers/CMSIS/Include/cmsis_gcc.h **** #pragma GCC diagnostic ignored "-Wsign-conversion"
  31:Drivers/CMSIS/Include/cmsis_gcc.h **** #pragma GCC diagnostic ignored "-Wconversion"
  32:Drivers/CMSIS/Include/cmsis_gcc.h **** #pragma GCC diagnostic ignored "-Wunused-parameter"
  33:Drivers/CMSIS/Include/cmsis_gcc.h **** 
  34:Drivers/CMSIS/Include/cmsis_gcc.h **** /* Fallback for __has_builtin */
  35:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef __has_builtin
  36:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __has_builtin(x) (0)
  37:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  38:Drivers/CMSIS/Include/cmsis_gcc.h **** 
  39:Drivers/CMSIS/Include/cmsis_gcc.h **** /* CMSIS compiler specific defines */
  40:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __ASM
  41:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __ASM                                  __asm
  42:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  43:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __INLINE
  44:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __INLINE                               inline
  45:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  46:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __STATIC_INLINE
  47:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __STATIC_INLINE                        static inline
  48:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  49:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __STATIC_FORCEINLINE                 
  50:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __STATIC_FORCEINLINE                   __attribute__((always_inline)) static inline
  51:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif                                           
  52:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __NO_RETURN
  53:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __NO_RETURN                            __attribute__((__noreturn__))
  54:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  55:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __USED
  56:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __USED                                 __attribute__((used))
  57:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  58:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __WEAK
  59:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __WEAK                                 __attribute__((weak))
  60:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  61:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __PACKED
  62:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __PACKED                               __attribute__((packed, aligned(1)))
  63:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  64:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __PACKED_STRUCT
  65:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __PACKED_STRUCT                        struct __attribute__((packed, aligned(1)))
  66:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  67:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __PACKED_UNION
  68:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __PACKED_UNION                         union __attribute__((packed, aligned(1)))
  69:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 38


  70:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __UNALIGNED_UINT32        /* deprecated */
  71:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic push
  72:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic ignored "-Wpacked"
  73:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic ignored "-Wattributes"
  74:Drivers/CMSIS/Include/cmsis_gcc.h ****   struct __attribute__((packed)) T_UINT32 { uint32_t v; };
  75:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic pop
  76:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __UNALIGNED_UINT32(x)                  (((struct T_UINT32 *)(x))->v)
  77:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  78:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __UNALIGNED_UINT16_WRITE
  79:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic push
  80:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic ignored "-Wpacked"
  81:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic ignored "-Wattributes"
  82:Drivers/CMSIS/Include/cmsis_gcc.h ****   __PACKED_STRUCT T_UINT16_WRITE { uint16_t v; };
  83:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic pop
  84:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __UNALIGNED_UINT16_WRITE(addr, val)    (void)((((struct T_UINT16_WRITE *)(void *)(addr))-
  85:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  86:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __UNALIGNED_UINT16_READ
  87:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic push
  88:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic ignored "-Wpacked"
  89:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic ignored "-Wattributes"
  90:Drivers/CMSIS/Include/cmsis_gcc.h ****   __PACKED_STRUCT T_UINT16_READ { uint16_t v; };
  91:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic pop
  92:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __UNALIGNED_UINT16_READ(addr)          (((const struct T_UINT16_READ *)(const void *)(add
  93:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  94:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __UNALIGNED_UINT32_WRITE
  95:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic push
  96:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic ignored "-Wpacked"
  97:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic ignored "-Wattributes"
  98:Drivers/CMSIS/Include/cmsis_gcc.h ****   __PACKED_STRUCT T_UINT32_WRITE { uint32_t v; };
  99:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic pop
 100:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __UNALIGNED_UINT32_WRITE(addr, val)    (void)((((struct T_UINT32_WRITE *)(void *)(addr))-
 101:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 102:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __UNALIGNED_UINT32_READ
 103:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic push
 104:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic ignored "-Wpacked"
 105:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic ignored "-Wattributes"
 106:Drivers/CMSIS/Include/cmsis_gcc.h ****   __PACKED_STRUCT T_UINT32_READ { uint32_t v; };
 107:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic pop
 108:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __UNALIGNED_UINT32_READ(addr)          (((const struct T_UINT32_READ *)(const void *)(add
 109:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 110:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __ALIGNED
 111:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __ALIGNED(x)                           __attribute__((aligned(x)))
 112:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 113:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __RESTRICT
 114:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __RESTRICT                             __restrict
 115:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 116:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __COMPILER_BARRIER
 117:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __COMPILER_BARRIER()                   __ASM volatile("":::"memory")
 118:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 119:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 120:Drivers/CMSIS/Include/cmsis_gcc.h **** /* #########################  Startup and Lowlevel Init  ######################## */
 121:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 122:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef __PROGRAM_START
 123:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 124:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 125:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Initializes data and bss sections
 126:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details This default implementations initialized all data and additional bss
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 39


 127:Drivers/CMSIS/Include/cmsis_gcc.h ****            sections relying on .copy.table and .zero.table specified properly
 128:Drivers/CMSIS/Include/cmsis_gcc.h ****            in the used linker script.
 129:Drivers/CMSIS/Include/cmsis_gcc.h ****   
 130:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 131:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE __NO_RETURN void __cmsis_start(void)
 132:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 133:Drivers/CMSIS/Include/cmsis_gcc.h ****   extern void _start(void) __NO_RETURN;
 134:Drivers/CMSIS/Include/cmsis_gcc.h ****   
 135:Drivers/CMSIS/Include/cmsis_gcc.h ****   typedef struct {
 136:Drivers/CMSIS/Include/cmsis_gcc.h ****     uint32_t const* src;
 137:Drivers/CMSIS/Include/cmsis_gcc.h ****     uint32_t* dest;
 138:Drivers/CMSIS/Include/cmsis_gcc.h ****     uint32_t  wlen;
 139:Drivers/CMSIS/Include/cmsis_gcc.h ****   } __copy_table_t;
 140:Drivers/CMSIS/Include/cmsis_gcc.h ****   
 141:Drivers/CMSIS/Include/cmsis_gcc.h ****   typedef struct {
 142:Drivers/CMSIS/Include/cmsis_gcc.h ****     uint32_t* dest;
 143:Drivers/CMSIS/Include/cmsis_gcc.h ****     uint32_t  wlen;
 144:Drivers/CMSIS/Include/cmsis_gcc.h ****   } __zero_table_t;
 145:Drivers/CMSIS/Include/cmsis_gcc.h ****   
 146:Drivers/CMSIS/Include/cmsis_gcc.h ****   extern const __copy_table_t __copy_table_start__;
 147:Drivers/CMSIS/Include/cmsis_gcc.h ****   extern const __copy_table_t __copy_table_end__;
 148:Drivers/CMSIS/Include/cmsis_gcc.h ****   extern const __zero_table_t __zero_table_start__;
 149:Drivers/CMSIS/Include/cmsis_gcc.h ****   extern const __zero_table_t __zero_table_end__;
 150:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 151:Drivers/CMSIS/Include/cmsis_gcc.h ****   for (__copy_table_t const* pTable = &__copy_table_start__; pTable < &__copy_table_end__; ++pTable
 152:Drivers/CMSIS/Include/cmsis_gcc.h ****     for(uint32_t i=0u; i<pTable->wlen; ++i) {
 153:Drivers/CMSIS/Include/cmsis_gcc.h ****       pTable->dest[i] = pTable->src[i];
 154:Drivers/CMSIS/Include/cmsis_gcc.h ****     }
 155:Drivers/CMSIS/Include/cmsis_gcc.h ****   }
 156:Drivers/CMSIS/Include/cmsis_gcc.h ****  
 157:Drivers/CMSIS/Include/cmsis_gcc.h ****   for (__zero_table_t const* pTable = &__zero_table_start__; pTable < &__zero_table_end__; ++pTable
 158:Drivers/CMSIS/Include/cmsis_gcc.h ****     for(uint32_t i=0u; i<pTable->wlen; ++i) {
 159:Drivers/CMSIS/Include/cmsis_gcc.h ****       pTable->dest[i] = 0u;
 160:Drivers/CMSIS/Include/cmsis_gcc.h ****     }
 161:Drivers/CMSIS/Include/cmsis_gcc.h ****   }
 162:Drivers/CMSIS/Include/cmsis_gcc.h ****  
 163:Drivers/CMSIS/Include/cmsis_gcc.h ****   _start();
 164:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 165:Drivers/CMSIS/Include/cmsis_gcc.h ****   
 166:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __PROGRAM_START           __cmsis_start
 167:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 168:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 169:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef __INITIAL_SP
 170:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __INITIAL_SP              __StackTop
 171:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 172:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 173:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef __STACK_LIMIT
 174:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __STACK_LIMIT             __StackLimit
 175:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 176:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 177:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef __VECTOR_TABLE
 178:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __VECTOR_TABLE            __Vectors
 179:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 180:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 181:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef __VECTOR_TABLE_ATTRIBUTE
 182:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __VECTOR_TABLE_ATTRIBUTE  __attribute((used, section(".vectors")))
 183:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 40


 184:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 185:Drivers/CMSIS/Include/cmsis_gcc.h **** /* ###########################  Core Function Access  ########################### */
 186:Drivers/CMSIS/Include/cmsis_gcc.h **** /** \ingroup  CMSIS_Core_FunctionInterface
 187:Drivers/CMSIS/Include/cmsis_gcc.h ****     \defgroup CMSIS_Core_RegAccFunctions CMSIS Core Register Access Functions
 188:Drivers/CMSIS/Include/cmsis_gcc.h ****   @{
 189:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 190:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 191:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 192:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Enable IRQ Interrupts
 193:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Enables IRQ interrupts by clearing the I-bit in the CPSR.
 194:Drivers/CMSIS/Include/cmsis_gcc.h ****            Can only be executed in Privileged modes.
 195:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 196:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __enable_irq(void)
 197:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 198:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("cpsie i" : : : "memory");
 199:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 200:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 201:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 202:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 203:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Disable IRQ Interrupts
 204:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Disables IRQ interrupts by setting the I-bit in the CPSR.
 205:Drivers/CMSIS/Include/cmsis_gcc.h ****            Can only be executed in Privileged modes.
 206:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 207:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __disable_irq(void)
 208:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 209:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("cpsid i" : : : "memory");
 210:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 211:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 212:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 213:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 214:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get Control Register
 215:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the content of the Control Register.
 216:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               Control Register value
 217:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 218:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __get_CONTROL(void)
 219:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 220:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 221:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 222:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, control" : "=r" (result) );
 223:Drivers/CMSIS/Include/cmsis_gcc.h ****   return(result);
 224:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 225:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 226:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 227:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (defined (__ARM_FEATURE_CMSE ) && (__ARM_FEATURE_CMSE == 3))
 228:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 229:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get Control Register (non-secure)
 230:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the content of the non-secure Control Register when in secure mode.
 231:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               non-secure Control Register value
 232:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 233:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __TZ_get_CONTROL_NS(void)
 234:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 235:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 236:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 237:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, control_ns" : "=r" (result) );
 238:Drivers/CMSIS/Include/cmsis_gcc.h ****   return(result);
 239:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 240:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 41


 241:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 242:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 243:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 244:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Set Control Register
 245:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Writes the given value to the Control Register.
 246:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    control  Control Register value to set
 247:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 248:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __set_CONTROL(uint32_t control)
 249:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 250:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MSR control, %0" : : "r" (control) : "memory");
 251:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 252:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 253:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 254:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (defined (__ARM_FEATURE_CMSE ) && (__ARM_FEATURE_CMSE == 3))
 255:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 256:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Set Control Register (non-secure)
 257:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Writes the given value to the non-secure Control Register when in secure state.
 258:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    control  Control Register value to set
 259:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 260:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __TZ_set_CONTROL_NS(uint32_t control)
 261:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 262:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MSR control_ns, %0" : : "r" (control) : "memory");
 263:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 264:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 265:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 266:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 267:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 268:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get IPSR Register
 269:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the content of the IPSR Register.
 270:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               IPSR Register value
 271:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 272:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __get_IPSR(void)
 273:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 274:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 275:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 276:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, ipsr" : "=r" (result) );
 277:Drivers/CMSIS/Include/cmsis_gcc.h ****   return(result);
 278:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 279:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 280:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 281:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 282:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get APSR Register
 283:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the content of the APSR Register.
 284:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               APSR Register value
 285:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 286:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __get_APSR(void)
 287:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 288:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 289:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 290:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, apsr" : "=r" (result) );
 291:Drivers/CMSIS/Include/cmsis_gcc.h ****   return(result);
 292:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 293:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 294:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 295:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 296:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get xPSR Register
 297:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the content of the xPSR Register.
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 42


 298:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               xPSR Register value
 299:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 300:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __get_xPSR(void)
 301:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 302:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 303:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 304:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, xpsr" : "=r" (result) );
 305:Drivers/CMSIS/Include/cmsis_gcc.h ****   return(result);
 306:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 307:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 308:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 309:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 310:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get Process Stack Pointer
 311:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the current value of the Process Stack Pointer (PSP).
 312:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               PSP Register value
 313:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 314:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __get_PSP(void)
 315:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 316:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 317:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 318:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, psp"  : "=r" (result) );
 319:Drivers/CMSIS/Include/cmsis_gcc.h ****   return(result);
 320:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 321:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 322:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 323:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (defined (__ARM_FEATURE_CMSE ) && (__ARM_FEATURE_CMSE == 3))
 324:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 325:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get Process Stack Pointer (non-secure)
 326:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the current value of the non-secure Process Stack Pointer (PSP) when in secure s
 327:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               PSP Register value
 328:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 329:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __TZ_get_PSP_NS(void)
 330:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 331:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 332:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 333:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, psp_ns"  : "=r" (result) );
 334:Drivers/CMSIS/Include/cmsis_gcc.h ****   return(result);
 335:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 336:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 337:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 338:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 339:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 340:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Set Process Stack Pointer
 341:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Assigns the given value to the Process Stack Pointer (PSP).
 342:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    topOfProcStack  Process Stack Pointer value to set
 343:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 344:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __set_PSP(uint32_t topOfProcStack)
 345:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 346:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MSR psp, %0" : : "r" (topOfProcStack) : );
 347:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 348:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 349:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 350:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (defined (__ARM_FEATURE_CMSE ) && (__ARM_FEATURE_CMSE == 3))
 351:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 352:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Set Process Stack Pointer (non-secure)
 353:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Assigns the given value to the non-secure Process Stack Pointer (PSP) when in secure sta
 354:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    topOfProcStack  Process Stack Pointer value to set
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 43


 355:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 356:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __TZ_set_PSP_NS(uint32_t topOfProcStack)
 357:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 358:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MSR psp_ns, %0" : : "r" (topOfProcStack) : );
 359:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 360:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 361:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 362:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 363:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 364:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get Main Stack Pointer
 365:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the current value of the Main Stack Pointer (MSP).
 366:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               MSP Register value
 367:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 368:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __get_MSP(void)
 369:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 370:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 371:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 372:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, msp" : "=r" (result) );
 373:Drivers/CMSIS/Include/cmsis_gcc.h ****   return(result);
 374:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 375:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 376:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 377:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (defined (__ARM_FEATURE_CMSE ) && (__ARM_FEATURE_CMSE == 3))
 378:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 379:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get Main Stack Pointer (non-secure)
 380:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the current value of the non-secure Main Stack Pointer (MSP) when in secure stat
 381:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               MSP Register value
 382:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 383:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __TZ_get_MSP_NS(void)
 384:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 385:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 386:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 387:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, msp_ns" : "=r" (result) );
 388:Drivers/CMSIS/Include/cmsis_gcc.h ****   return(result);
 389:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 390:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 391:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 392:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 393:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 394:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Set Main Stack Pointer
 395:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Assigns the given value to the Main Stack Pointer (MSP).
 396:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    topOfMainStack  Main Stack Pointer value to set
 397:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 398:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __set_MSP(uint32_t topOfMainStack)
 399:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 400:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MSR msp, %0" : : "r" (topOfMainStack) : );
 401:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 402:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 403:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 404:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (defined (__ARM_FEATURE_CMSE ) && (__ARM_FEATURE_CMSE == 3))
 405:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 406:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Set Main Stack Pointer (non-secure)
 407:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Assigns the given value to the non-secure Main Stack Pointer (MSP) when in secure state.
 408:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    topOfMainStack  Main Stack Pointer value to set
 409:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 410:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __TZ_set_MSP_NS(uint32_t topOfMainStack)
 411:Drivers/CMSIS/Include/cmsis_gcc.h **** {
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 44


 412:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MSR msp_ns, %0" : : "r" (topOfMainStack) : );
 413:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 414:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 415:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 416:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 417:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (defined (__ARM_FEATURE_CMSE ) && (__ARM_FEATURE_CMSE == 3))
 418:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 419:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get Stack Pointer (non-secure)
 420:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the current value of the non-secure Stack Pointer (SP) when in secure state.
 421:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               SP Register value
 422:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 423:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __TZ_get_SP_NS(void)
 424:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 425:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 426:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 427:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, sp_ns" : "=r" (result) );
 428:Drivers/CMSIS/Include/cmsis_gcc.h ****   return(result);
 429:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 430:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 431:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 432:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 433:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Set Stack Pointer (non-secure)
 434:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Assigns the given value to the non-secure Stack Pointer (SP) when in secure state.
 435:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    topOfStack  Stack Pointer value to set
 436:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 437:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __TZ_set_SP_NS(uint32_t topOfStack)
 438:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 439:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MSR sp_ns, %0" : : "r" (topOfStack) : );
 440:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 441:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 442:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 443:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 444:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 445:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get Priority Mask
 446:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the current state of the priority mask bit from the Priority Mask Register.
 447:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               Priority Mask value
 448:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 449:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __get_PRIMASK(void)
 450:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 451:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 452:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 453:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, primask" : "=r" (result) :: "memory");
 454:Drivers/CMSIS/Include/cmsis_gcc.h ****   return(result);
 455:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 456:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 457:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 458:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (defined (__ARM_FEATURE_CMSE ) && (__ARM_FEATURE_CMSE == 3))
 459:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 460:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get Priority Mask (non-secure)
 461:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the current state of the non-secure priority mask bit from the Priority Mask Reg
 462:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               Priority Mask value
 463:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 464:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __TZ_get_PRIMASK_NS(void)
 465:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 466:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 467:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 468:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, primask_ns" : "=r" (result) :: "memory");
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 45


 469:Drivers/CMSIS/Include/cmsis_gcc.h ****   return(result);
 470:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 471:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 472:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 473:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 474:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 475:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Set Priority Mask
 476:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Assigns the given value to the Priority Mask Register.
 477:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    priMask  Priority Mask
 478:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 479:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __set_PRIMASK(uint32_t priMask)
 480:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 481:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MSR primask, %0" : : "r" (priMask) : "memory");
 482:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 483:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 484:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 485:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (defined (__ARM_FEATURE_CMSE ) && (__ARM_FEATURE_CMSE == 3))
 486:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 487:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Set Priority Mask (non-secure)
 488:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Assigns the given value to the non-secure Priority Mask Register when in secure state.
 489:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    priMask  Priority Mask
 490:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 491:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __TZ_set_PRIMASK_NS(uint32_t priMask)
 492:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 493:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MSR primask_ns, %0" : : "r" (priMask) : "memory");
 494:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 495:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 496:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 497:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 498:Drivers/CMSIS/Include/cmsis_gcc.h **** #if ((defined (__ARM_ARCH_7M__      ) && (__ARM_ARCH_7M__      == 1)) || \
 499:Drivers/CMSIS/Include/cmsis_gcc.h ****      (defined (__ARM_ARCH_7EM__     ) && (__ARM_ARCH_7EM__     == 1)) || \
 500:Drivers/CMSIS/Include/cmsis_gcc.h ****      (defined (__ARM_ARCH_8M_MAIN__ ) && (__ARM_ARCH_8M_MAIN__ == 1))    )
 501:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 502:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Enable FIQ
 503:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Enables FIQ interrupts by clearing the F-bit in the CPSR.
 504:Drivers/CMSIS/Include/cmsis_gcc.h ****            Can only be executed in Privileged modes.
 505:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 506:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __enable_fault_irq(void)
 507:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 508:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("cpsie f" : : : "memory");
 509:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 510:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 511:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 512:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 513:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Disable FIQ
 514:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Disables FIQ interrupts by setting the F-bit in the CPSR.
 515:Drivers/CMSIS/Include/cmsis_gcc.h ****            Can only be executed in Privileged modes.
 516:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 517:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __disable_fault_irq(void)
 518:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 519:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("cpsid f" : : : "memory");
 520:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 521:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 522:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 523:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 524:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get Base Priority
 525:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the current value of the Base Priority register.
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 46


 526:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               Base Priority register value
 527:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 528:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __get_BASEPRI(void)
 529:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 530:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 531:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 532:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, basepri" : "=r" (result) );
 533:Drivers/CMSIS/Include/cmsis_gcc.h ****   return(result);
 534:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 535:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 536:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 537:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (defined (__ARM_FEATURE_CMSE ) && (__ARM_FEATURE_CMSE == 3))
 538:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 539:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get Base Priority (non-secure)
 540:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the current value of the non-secure Base Priority register when in secure state.
 541:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               Base Priority register value
 542:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 543:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __TZ_get_BASEPRI_NS(void)
 544:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 545:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 546:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 547:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, basepri_ns" : "=r" (result) );
 548:Drivers/CMSIS/Include/cmsis_gcc.h ****   return(result);
 549:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 550:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 551:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 552:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 553:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 554:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Set Base Priority
 555:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Assigns the given value to the Base Priority register.
 556:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    basePri  Base Priority value to set
 557:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 558:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __set_BASEPRI(uint32_t basePri)
 559:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 560:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MSR basepri, %0" : : "r" (basePri) : "memory");
 561:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 562:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 563:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 564:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (defined (__ARM_FEATURE_CMSE ) && (__ARM_FEATURE_CMSE == 3))
 565:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 566:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Set Base Priority (non-secure)
 567:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Assigns the given value to the non-secure Base Priority register when in secure state.
 568:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    basePri  Base Priority value to set
 569:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 570:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __TZ_set_BASEPRI_NS(uint32_t basePri)
 571:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 572:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MSR basepri_ns, %0" : : "r" (basePri) : "memory");
 573:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 574:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 575:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 576:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 577:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 578:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Set Base Priority with condition
 579:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Assigns the given value to the Base Priority register only if BASEPRI masking is disable
 580:Drivers/CMSIS/Include/cmsis_gcc.h ****            or the new value increases the BASEPRI priority level.
 581:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    basePri  Base Priority value to set
 582:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 47


 583:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __set_BASEPRI_MAX(uint32_t basePri)
 584:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 585:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MSR basepri_max, %0" : : "r" (basePri) : "memory");
 586:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 587:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 588:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 589:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 590:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get Fault Mask
 591:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the current value of the Fault Mask register.
 592:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               Fault Mask register value
 593:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 594:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __get_FAULTMASK(void)
 595:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 596:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 597:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 598:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, faultmask" : "=r" (result) );
 599:Drivers/CMSIS/Include/cmsis_gcc.h ****   return(result);
 600:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 601:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 602:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 603:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (defined (__ARM_FEATURE_CMSE ) && (__ARM_FEATURE_CMSE == 3))
 604:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 605:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get Fault Mask (non-secure)
 606:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the current value of the non-secure Fault Mask register when in secure state.
 607:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               Fault Mask register value
 608:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 609:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __TZ_get_FAULTMASK_NS(void)
 610:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 611:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 612:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 613:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, faultmask_ns" : "=r" (result) );
 614:Drivers/CMSIS/Include/cmsis_gcc.h ****   return(result);
 615:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 616:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 617:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 618:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 619:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 620:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Set Fault Mask
 621:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Assigns the given value to the Fault Mask register.
 622:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    faultMask  Fault Mask value to set
 623:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 624:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __set_FAULTMASK(uint32_t faultMask)
 625:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 626:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MSR faultmask, %0" : : "r" (faultMask) : "memory");
 627:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 628:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 629:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 630:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (defined (__ARM_FEATURE_CMSE ) && (__ARM_FEATURE_CMSE == 3))
 631:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 632:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Set Fault Mask (non-secure)
 633:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Assigns the given value to the non-secure Fault Mask register when in secure state.
 634:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    faultMask  Fault Mask value to set
 635:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 636:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __TZ_set_FAULTMASK_NS(uint32_t faultMask)
 637:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 638:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MSR faultmask_ns, %0" : : "r" (faultMask) : "memory");
 639:Drivers/CMSIS/Include/cmsis_gcc.h **** }
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 48


 640:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 641:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 642:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif /* ((defined (__ARM_ARCH_7M__      ) && (__ARM_ARCH_7M__      == 1)) || \
 643:Drivers/CMSIS/Include/cmsis_gcc.h ****            (defined (__ARM_ARCH_7EM__     ) && (__ARM_ARCH_7EM__     == 1)) || \
 644:Drivers/CMSIS/Include/cmsis_gcc.h ****            (defined (__ARM_ARCH_8M_MAIN__ ) && (__ARM_ARCH_8M_MAIN__ == 1))    ) */
 645:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 646:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 647:Drivers/CMSIS/Include/cmsis_gcc.h **** #if ((defined (__ARM_ARCH_8M_MAIN__ ) && (__ARM_ARCH_8M_MAIN__ == 1)) || \
 648:Drivers/CMSIS/Include/cmsis_gcc.h ****      (defined (__ARM_ARCH_8M_BASE__ ) && (__ARM_ARCH_8M_BASE__ == 1))    )
 649:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 650:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 651:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get Process Stack Pointer Limit
 652:Drivers/CMSIS/Include/cmsis_gcc.h ****   Devices without ARMv8-M Main Extensions (i.e. Cortex-M23) lack the non-secure
 653:Drivers/CMSIS/Include/cmsis_gcc.h ****   Stack Pointer Limit register hence zero is returned always in non-secure
 654:Drivers/CMSIS/Include/cmsis_gcc.h ****   mode.
 655:Drivers/CMSIS/Include/cmsis_gcc.h ****   
 656:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the current value of the Process Stack Pointer Limit (PSPLIM).
 657:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               PSPLIM Register value
 658:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 659:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __get_PSPLIM(void)
 660:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 661:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (!(defined (__ARM_ARCH_8M_MAIN__ ) && (__ARM_ARCH_8M_MAIN__ == 1)) && \
 662:Drivers/CMSIS/Include/cmsis_gcc.h ****     (!defined (__ARM_FEATURE_CMSE) || (__ARM_FEATURE_CMSE < 3)))
 663:Drivers/CMSIS/Include/cmsis_gcc.h ****     // without main extensions, the non-secure PSPLIM is RAZ/WI
 664:Drivers/CMSIS/Include/cmsis_gcc.h ****   return 0U;
 665:Drivers/CMSIS/Include/cmsis_gcc.h **** #else
 666:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 667:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, psplim"  : "=r" (result) );
 668:Drivers/CMSIS/Include/cmsis_gcc.h ****   return result;
 669:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 670:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 671:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 672:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (defined (__ARM_FEATURE_CMSE) && (__ARM_FEATURE_CMSE == 3))
 673:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 674:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get Process Stack Pointer Limit (non-secure)
 675:Drivers/CMSIS/Include/cmsis_gcc.h ****   Devices without ARMv8-M Main Extensions (i.e. Cortex-M23) lack the non-secure
 676:Drivers/CMSIS/Include/cmsis_gcc.h ****   Stack Pointer Limit register hence zero is returned always.
 677:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 678:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the current value of the non-secure Process Stack Pointer Limit (PSPLIM) when in
 679:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               PSPLIM Register value
 680:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 681:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __TZ_get_PSPLIM_NS(void)
 682:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 683:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (!(defined (__ARM_ARCH_8M_MAIN__ ) && (__ARM_ARCH_8M_MAIN__ == 1)))
 684:Drivers/CMSIS/Include/cmsis_gcc.h ****   // without main extensions, the non-secure PSPLIM is RAZ/WI
 685:Drivers/CMSIS/Include/cmsis_gcc.h ****   return 0U;
 686:Drivers/CMSIS/Include/cmsis_gcc.h **** #else
 687:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 688:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, psplim_ns"  : "=r" (result) );
 689:Drivers/CMSIS/Include/cmsis_gcc.h ****   return result;
 690:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 691:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 692:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 693:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 694:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 695:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 696:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Set Process Stack Pointer Limit
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 49


 697:Drivers/CMSIS/Include/cmsis_gcc.h ****   Devices without ARMv8-M Main Extensions (i.e. Cortex-M23) lack the non-secure
 698:Drivers/CMSIS/Include/cmsis_gcc.h ****   Stack Pointer Limit register hence the write is silently ignored in non-secure
 699:Drivers/CMSIS/Include/cmsis_gcc.h ****   mode.
 700:Drivers/CMSIS/Include/cmsis_gcc.h ****   
 701:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Assigns the given value to the Process Stack Pointer Limit (PSPLIM).
 702:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    ProcStackPtrLimit  Process Stack Pointer Limit value to set
 703:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 704:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __set_PSPLIM(uint32_t ProcStackPtrLimit)
 705:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 706:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (!(defined (__ARM_ARCH_8M_MAIN__ ) && (__ARM_ARCH_8M_MAIN__ == 1)) && \
 707:Drivers/CMSIS/Include/cmsis_gcc.h ****     (!defined (__ARM_FEATURE_CMSE) || (__ARM_FEATURE_CMSE < 3)))
 708:Drivers/CMSIS/Include/cmsis_gcc.h ****   // without main extensions, the non-secure PSPLIM is RAZ/WI
 709:Drivers/CMSIS/Include/cmsis_gcc.h ****   (void)ProcStackPtrLimit;
 710:Drivers/CMSIS/Include/cmsis_gcc.h **** #else
 711:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MSR psplim, %0" : : "r" (ProcStackPtrLimit));
 712:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 713:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 714:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 715:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 716:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (defined (__ARM_FEATURE_CMSE  ) && (__ARM_FEATURE_CMSE   == 3))
 717:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 718:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Set Process Stack Pointer (non-secure)
 719:Drivers/CMSIS/Include/cmsis_gcc.h ****   Devices without ARMv8-M Main Extensions (i.e. Cortex-M23) lack the non-secure
 720:Drivers/CMSIS/Include/cmsis_gcc.h ****   Stack Pointer Limit register hence the write is silently ignored.
 721:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 722:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Assigns the given value to the non-secure Process Stack Pointer Limit (PSPLIM) when in s
 723:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    ProcStackPtrLimit  Process Stack Pointer Limit value to set
 724:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 725:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __TZ_set_PSPLIM_NS(uint32_t ProcStackPtrLimit)
 726:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 727:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (!(defined (__ARM_ARCH_8M_MAIN__ ) && (__ARM_ARCH_8M_MAIN__ == 1)))
 728:Drivers/CMSIS/Include/cmsis_gcc.h ****   // without main extensions, the non-secure PSPLIM is RAZ/WI
 729:Drivers/CMSIS/Include/cmsis_gcc.h ****   (void)ProcStackPtrLimit;
 730:Drivers/CMSIS/Include/cmsis_gcc.h **** #else
 731:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MSR psplim_ns, %0\n" : : "r" (ProcStackPtrLimit));
 732:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 733:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 734:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 735:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 736:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 737:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 738:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get Main Stack Pointer Limit
 739:Drivers/CMSIS/Include/cmsis_gcc.h ****   Devices without ARMv8-M Main Extensions (i.e. Cortex-M23) lack the non-secure
 740:Drivers/CMSIS/Include/cmsis_gcc.h ****   Stack Pointer Limit register hence zero is returned always in non-secure
 741:Drivers/CMSIS/Include/cmsis_gcc.h ****   mode.
 742:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 743:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the current value of the Main Stack Pointer Limit (MSPLIM).
 744:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               MSPLIM Register value
 745:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 746:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __get_MSPLIM(void)
 747:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 748:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (!(defined (__ARM_ARCH_8M_MAIN__ ) && (__ARM_ARCH_8M_MAIN__ == 1)) && \
 749:Drivers/CMSIS/Include/cmsis_gcc.h ****     (!defined (__ARM_FEATURE_CMSE) || (__ARM_FEATURE_CMSE < 3)))
 750:Drivers/CMSIS/Include/cmsis_gcc.h ****   // without main extensions, the non-secure MSPLIM is RAZ/WI
 751:Drivers/CMSIS/Include/cmsis_gcc.h ****   return 0U;
 752:Drivers/CMSIS/Include/cmsis_gcc.h **** #else
 753:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 50


 754:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, msplim" : "=r" (result) );
 755:Drivers/CMSIS/Include/cmsis_gcc.h ****   return result;
 756:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 757:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 758:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 759:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 760:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (defined (__ARM_FEATURE_CMSE  ) && (__ARM_FEATURE_CMSE   == 3))
 761:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 762:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get Main Stack Pointer Limit (non-secure)
 763:Drivers/CMSIS/Include/cmsis_gcc.h ****   Devices without ARMv8-M Main Extensions (i.e. Cortex-M23) lack the non-secure
 764:Drivers/CMSIS/Include/cmsis_gcc.h ****   Stack Pointer Limit register hence zero is returned always.
 765:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 766:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the current value of the non-secure Main Stack Pointer Limit(MSPLIM) when in sec
 767:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               MSPLIM Register value
 768:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 769:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __TZ_get_MSPLIM_NS(void)
 770:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 771:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (!(defined (__ARM_ARCH_8M_MAIN__ ) && (__ARM_ARCH_8M_MAIN__ == 1)))
 772:Drivers/CMSIS/Include/cmsis_gcc.h ****   // without main extensions, the non-secure MSPLIM is RAZ/WI
 773:Drivers/CMSIS/Include/cmsis_gcc.h ****   return 0U;
 774:Drivers/CMSIS/Include/cmsis_gcc.h **** #else
 775:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 776:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, msplim_ns" : "=r" (result) );
 777:Drivers/CMSIS/Include/cmsis_gcc.h ****   return result;
 778:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 779:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 780:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 781:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 782:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 783:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 784:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Set Main Stack Pointer Limit
 785:Drivers/CMSIS/Include/cmsis_gcc.h ****   Devices without ARMv8-M Main Extensions (i.e. Cortex-M23) lack the non-secure
 786:Drivers/CMSIS/Include/cmsis_gcc.h ****   Stack Pointer Limit register hence the write is silently ignored in non-secure
 787:Drivers/CMSIS/Include/cmsis_gcc.h ****   mode.
 788:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 789:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Assigns the given value to the Main Stack Pointer Limit (MSPLIM).
 790:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    MainStackPtrLimit  Main Stack Pointer Limit value to set
 791:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 792:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __set_MSPLIM(uint32_t MainStackPtrLimit)
 793:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 794:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (!(defined (__ARM_ARCH_8M_MAIN__ ) && (__ARM_ARCH_8M_MAIN__ == 1)) && \
 795:Drivers/CMSIS/Include/cmsis_gcc.h ****     (!defined (__ARM_FEATURE_CMSE) || (__ARM_FEATURE_CMSE < 3)))
 796:Drivers/CMSIS/Include/cmsis_gcc.h ****   // without main extensions, the non-secure MSPLIM is RAZ/WI
 797:Drivers/CMSIS/Include/cmsis_gcc.h ****   (void)MainStackPtrLimit;
 798:Drivers/CMSIS/Include/cmsis_gcc.h **** #else
 799:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MSR msplim, %0" : : "r" (MainStackPtrLimit));
 800:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 801:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 802:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 803:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 804:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (defined (__ARM_FEATURE_CMSE  ) && (__ARM_FEATURE_CMSE   == 3))
 805:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 806:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Set Main Stack Pointer Limit (non-secure)
 807:Drivers/CMSIS/Include/cmsis_gcc.h ****   Devices without ARMv8-M Main Extensions (i.e. Cortex-M23) lack the non-secure
 808:Drivers/CMSIS/Include/cmsis_gcc.h ****   Stack Pointer Limit register hence the write is silently ignored.
 809:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 810:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Assigns the given value to the non-secure Main Stack Pointer Limit (MSPLIM) when in secu
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 51


 811:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    MainStackPtrLimit  Main Stack Pointer value to set
 812:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 813:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __TZ_set_MSPLIM_NS(uint32_t MainStackPtrLimit)
 814:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 815:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (!(defined (__ARM_ARCH_8M_MAIN__ ) && (__ARM_ARCH_8M_MAIN__ == 1)))
 816:Drivers/CMSIS/Include/cmsis_gcc.h ****   // without main extensions, the non-secure MSPLIM is RAZ/WI
 817:Drivers/CMSIS/Include/cmsis_gcc.h ****   (void)MainStackPtrLimit;
 818:Drivers/CMSIS/Include/cmsis_gcc.h **** #else
 819:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MSR msplim_ns, %0" : : "r" (MainStackPtrLimit));
 820:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 821:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 822:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 823:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 824:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif /* ((defined (__ARM_ARCH_8M_MAIN__ ) && (__ARM_ARCH_8M_MAIN__ == 1)) || \
 825:Drivers/CMSIS/Include/cmsis_gcc.h ****            (defined (__ARM_ARCH_8M_BASE__ ) && (__ARM_ARCH_8M_BASE__ == 1))    ) */
 826:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 827:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 828:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 829:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get FPSCR
 830:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the current value of the Floating Point Status/Control register.
 831:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               Floating Point Status/Control register value
 832:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 833:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __get_FPSCR(void)
 834:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 835:Drivers/CMSIS/Include/cmsis_gcc.h **** #if ((defined (__FPU_PRESENT) && (__FPU_PRESENT == 1U)) && \
 836:Drivers/CMSIS/Include/cmsis_gcc.h ****      (defined (__FPU_USED   ) && (__FPU_USED    == 1U))     )
 837:Drivers/CMSIS/Include/cmsis_gcc.h **** #if __has_builtin(__builtin_arm_get_fpscr) 
 838:Drivers/CMSIS/Include/cmsis_gcc.h **** // Re-enable using built-in when GCC has been fixed
 839:Drivers/CMSIS/Include/cmsis_gcc.h **** // || (__GNUC__ > 7) || (__GNUC__ == 7 && __GNUC_MINOR__ >= 2)
 840:Drivers/CMSIS/Include/cmsis_gcc.h ****   /* see https://gcc.gnu.org/ml/gcc-patches/2017-04/msg00443.html */
 841:Drivers/CMSIS/Include/cmsis_gcc.h ****   return __builtin_arm_get_fpscr();
 842:Drivers/CMSIS/Include/cmsis_gcc.h **** #else
 843:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 844:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 845:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("VMRS %0, fpscr" : "=r" (result) );
 846:Drivers/CMSIS/Include/cmsis_gcc.h ****   return(result);
 847:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 848:Drivers/CMSIS/Include/cmsis_gcc.h **** #else
 849:Drivers/CMSIS/Include/cmsis_gcc.h ****   return(0U);
 850:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 851:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 852:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 853:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 854:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 855:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Set FPSCR
 856:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Assigns the given value to the Floating Point Status/Control register.
 857:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    fpscr  Floating Point Status/Control value to set
 858:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 859:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __set_FPSCR(uint32_t fpscr)
 860:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 861:Drivers/CMSIS/Include/cmsis_gcc.h **** #if ((defined (__FPU_PRESENT) && (__FPU_PRESENT == 1U)) && \
 862:Drivers/CMSIS/Include/cmsis_gcc.h ****      (defined (__FPU_USED   ) && (__FPU_USED    == 1U))     )
 863:Drivers/CMSIS/Include/cmsis_gcc.h **** #if __has_builtin(__builtin_arm_set_fpscr)
 864:Drivers/CMSIS/Include/cmsis_gcc.h **** // Re-enable using built-in when GCC has been fixed
 865:Drivers/CMSIS/Include/cmsis_gcc.h **** // || (__GNUC__ > 7) || (__GNUC__ == 7 && __GNUC_MINOR__ >= 2)
 866:Drivers/CMSIS/Include/cmsis_gcc.h ****   /* see https://gcc.gnu.org/ml/gcc-patches/2017-04/msg00443.html */
 867:Drivers/CMSIS/Include/cmsis_gcc.h ****   __builtin_arm_set_fpscr(fpscr);
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 52


 868:Drivers/CMSIS/Include/cmsis_gcc.h **** #else
 869:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("VMSR fpscr, %0" : : "r" (fpscr) : "vfpcc", "memory");
 870:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 871:Drivers/CMSIS/Include/cmsis_gcc.h **** #else
 872:Drivers/CMSIS/Include/cmsis_gcc.h ****   (void)fpscr;
 873:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 874:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 875:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 876:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 877:Drivers/CMSIS/Include/cmsis_gcc.h **** /*@} end of CMSIS_Core_RegAccFunctions */
 878:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 879:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 880:Drivers/CMSIS/Include/cmsis_gcc.h **** /* ##########################  Core Instruction Access  ######################### */
 881:Drivers/CMSIS/Include/cmsis_gcc.h **** /** \defgroup CMSIS_Core_InstructionInterface CMSIS Core Instruction Interface
 882:Drivers/CMSIS/Include/cmsis_gcc.h ****   Access to dedicated instructions
 883:Drivers/CMSIS/Include/cmsis_gcc.h ****   @{
 884:Drivers/CMSIS/Include/cmsis_gcc.h **** */
 885:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 886:Drivers/CMSIS/Include/cmsis_gcc.h **** /* Define macros for porting to both thumb1 and thumb2.
 887:Drivers/CMSIS/Include/cmsis_gcc.h ****  * For thumb1, use low register (r0-r7), specified by constraint "l"
 888:Drivers/CMSIS/Include/cmsis_gcc.h ****  * Otherwise, use general registers, specified by constraint "r" */
 889:Drivers/CMSIS/Include/cmsis_gcc.h **** #if defined (__thumb__) && !defined (__thumb2__)
 890:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __CMSIS_GCC_OUT_REG(r) "=l" (r)
 891:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __CMSIS_GCC_RW_REG(r) "+l" (r)
 892:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __CMSIS_GCC_USE_REG(r) "l" (r)
 893:Drivers/CMSIS/Include/cmsis_gcc.h **** #else
 894:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __CMSIS_GCC_OUT_REG(r) "=r" (r)
 895:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __CMSIS_GCC_RW_REG(r) "+r" (r)
 896:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __CMSIS_GCC_USE_REG(r) "r" (r)
 897:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 898:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 899:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 900:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   No Operation
 901:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details No Operation does nothing. This instruction can be used for code alignment purposes.
 902:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 903:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __NOP()                             __ASM volatile ("nop")
 904:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 905:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 906:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Wait For Interrupt
 907:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Wait For Interrupt is a hint instruction that suspends execution until one of a number o
 908:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 909:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __WFI()                             __ASM volatile ("wfi")
 910:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 911:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 912:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 913:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Wait For Event
 914:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Wait For Event is a hint instruction that permits the processor to enter
 915:Drivers/CMSIS/Include/cmsis_gcc.h ****            a low-power state until one of a number of events occurs.
 916:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 917:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __WFE()                             __ASM volatile ("wfe")
 918:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 919:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 920:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 921:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Send Event
 922:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Send Event is a hint instruction. It causes an event to be signaled to the CPU.
 923:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 924:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __SEV()                             __ASM volatile ("sev")
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 53


 925:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 926:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 927:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 928:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Instruction Synchronization Barrier
 929:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Instruction Synchronization Barrier flushes the pipeline in the processor,
 930:Drivers/CMSIS/Include/cmsis_gcc.h ****            so that all instructions following the ISB are fetched from cache or memory,
 931:Drivers/CMSIS/Include/cmsis_gcc.h ****            after the instruction has been completed.
 932:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 933:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __ISB(void)
 934:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 935:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("isb 0xF":::"memory");
 936:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 937:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 938:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 939:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 940:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Data Synchronization Barrier
 941:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Acts as a special kind of Data Memory Barrier.
 942:Drivers/CMSIS/Include/cmsis_gcc.h ****            It completes when all explicit memory accesses before this instruction complete.
 943:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 944:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __DSB(void)
  99              		.loc 3 944 27 view .LVU22
 100              	.LBB37:
 945:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 946:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("dsb 0xF":::"memory");
 101              		.loc 3 946 3 view .LVU23
 102              		.syntax unified
 103              	@ 946 "Drivers/CMSIS/Include/cmsis_gcc.h" 1
 104 0016 BFF34F8F 		dsb 0xF
 105              	@ 0 "" 2
 106              		.thumb
 107              		.syntax unified
 108              	.LBE37:
 109              	.LBE36:
1946:Drivers/CMSIS/Include/core_cm7.h ****     __ISB();
 110              		.loc 2 1946 5 view .LVU24
 111              	.LBB38:
 112              	.LBI38:
 933:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 113              		.loc 3 933 27 view .LVU25
 114              	.LBB39:
 935:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 115              		.loc 3 935 3 view .LVU26
 116              		.syntax unified
 117              	@ 935 "Drivers/CMSIS/Include/cmsis_gcc.h" 1
 118 001a BFF36F8F 		isb 0xF
 119              	@ 0 "" 2
 120              		.thumb
 121              		.syntax unified
 122              	.L5:
 123              	.LBE39:
 124              	.LBE38:
1947:Drivers/CMSIS/Include/core_cm7.h ****   }
1948:Drivers/CMSIS/Include/core_cm7.h **** }
 125              		.loc 2 1948 1 is_stmt 0 view .LVU27
 126 001e 7047     		bx	lr
 127              	.L8:
 128              		.align	2
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 54


 129              	.L7:
 130 0020 00E100E0 		.word	-*********
 131              		.cfi_endproc
 132              	.LFE108:
 134              		.section	.text.__NVIC_SetPriority,"ax",%progbits
 135              		.align	1
 136              		.syntax unified
 137              		.thumb
 138              		.thumb_func
 140              	__NVIC_SetPriority:
 141              	.LVL4:
 142              	.LFB113:
1949:Drivers/CMSIS/Include/core_cm7.h **** 
1950:Drivers/CMSIS/Include/core_cm7.h **** 
1951:Drivers/CMSIS/Include/core_cm7.h **** /**
1952:Drivers/CMSIS/Include/core_cm7.h ****   \brief   Get Pending Interrupt
1953:Drivers/CMSIS/Include/core_cm7.h ****   \details Reads the NVIC pending register and returns the pending bit for the specified device spe
1954:Drivers/CMSIS/Include/core_cm7.h ****   \param [in]      IRQn  Device specific interrupt number.
1955:Drivers/CMSIS/Include/core_cm7.h ****   \return             0  Interrupt status is not pending.
1956:Drivers/CMSIS/Include/core_cm7.h ****   \return             1  Interrupt status is pending.
1957:Drivers/CMSIS/Include/core_cm7.h ****   \note    IRQn must not be negative.
1958:Drivers/CMSIS/Include/core_cm7.h ****  */
1959:Drivers/CMSIS/Include/core_cm7.h **** __STATIC_INLINE uint32_t __NVIC_GetPendingIRQ(IRQn_Type IRQn)
1960:Drivers/CMSIS/Include/core_cm7.h **** {
1961:Drivers/CMSIS/Include/core_cm7.h ****   if ((int32_t)(IRQn) >= 0)
1962:Drivers/CMSIS/Include/core_cm7.h ****   {
1963:Drivers/CMSIS/Include/core_cm7.h ****     return((uint32_t)(((NVIC->ISPR[(((uint32_t)IRQn) >> 5UL)] & (1UL << (((uint32_t)IRQn) & 0x1FUL)
1964:Drivers/CMSIS/Include/core_cm7.h ****   }
1965:Drivers/CMSIS/Include/core_cm7.h ****   else
1966:Drivers/CMSIS/Include/core_cm7.h ****   {
1967:Drivers/CMSIS/Include/core_cm7.h ****     return(0U);
1968:Drivers/CMSIS/Include/core_cm7.h ****   }
1969:Drivers/CMSIS/Include/core_cm7.h **** }
1970:Drivers/CMSIS/Include/core_cm7.h **** 
1971:Drivers/CMSIS/Include/core_cm7.h **** 
1972:Drivers/CMSIS/Include/core_cm7.h **** /**
1973:Drivers/CMSIS/Include/core_cm7.h ****   \brief   Set Pending Interrupt
1974:Drivers/CMSIS/Include/core_cm7.h ****   \details Sets the pending bit of a device specific interrupt in the NVIC pending register.
1975:Drivers/CMSIS/Include/core_cm7.h ****   \param [in]      IRQn  Device specific interrupt number.
1976:Drivers/CMSIS/Include/core_cm7.h ****   \note    IRQn must not be negative.
1977:Drivers/CMSIS/Include/core_cm7.h ****  */
1978:Drivers/CMSIS/Include/core_cm7.h **** __STATIC_INLINE void __NVIC_SetPendingIRQ(IRQn_Type IRQn)
1979:Drivers/CMSIS/Include/core_cm7.h **** {
1980:Drivers/CMSIS/Include/core_cm7.h ****   if ((int32_t)(IRQn) >= 0)
1981:Drivers/CMSIS/Include/core_cm7.h ****   {
1982:Drivers/CMSIS/Include/core_cm7.h ****     NVIC->ISPR[(((uint32_t)IRQn) >> 5UL)] = (uint32_t)(1UL << (((uint32_t)IRQn) & 0x1FUL));
1983:Drivers/CMSIS/Include/core_cm7.h ****   }
1984:Drivers/CMSIS/Include/core_cm7.h **** }
1985:Drivers/CMSIS/Include/core_cm7.h **** 
1986:Drivers/CMSIS/Include/core_cm7.h **** 
1987:Drivers/CMSIS/Include/core_cm7.h **** /**
1988:Drivers/CMSIS/Include/core_cm7.h ****   \brief   Clear Pending Interrupt
1989:Drivers/CMSIS/Include/core_cm7.h ****   \details Clears the pending bit of a device specific interrupt in the NVIC pending register.
1990:Drivers/CMSIS/Include/core_cm7.h ****   \param [in]      IRQn  Device specific interrupt number.
1991:Drivers/CMSIS/Include/core_cm7.h ****   \note    IRQn must not be negative.
1992:Drivers/CMSIS/Include/core_cm7.h ****  */
1993:Drivers/CMSIS/Include/core_cm7.h **** __STATIC_INLINE void __NVIC_ClearPendingIRQ(IRQn_Type IRQn)
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 55


1994:Drivers/CMSIS/Include/core_cm7.h **** {
1995:Drivers/CMSIS/Include/core_cm7.h ****   if ((int32_t)(IRQn) >= 0)
1996:Drivers/CMSIS/Include/core_cm7.h ****   {
1997:Drivers/CMSIS/Include/core_cm7.h ****     NVIC->ICPR[(((uint32_t)IRQn) >> 5UL)] = (uint32_t)(1UL << (((uint32_t)IRQn) & 0x1FUL));
1998:Drivers/CMSIS/Include/core_cm7.h ****   }
1999:Drivers/CMSIS/Include/core_cm7.h **** }
2000:Drivers/CMSIS/Include/core_cm7.h **** 
2001:Drivers/CMSIS/Include/core_cm7.h **** 
2002:Drivers/CMSIS/Include/core_cm7.h **** /**
2003:Drivers/CMSIS/Include/core_cm7.h ****   \brief   Get Active Interrupt
2004:Drivers/CMSIS/Include/core_cm7.h ****   \details Reads the active register in the NVIC and returns the active bit for the device specific
2005:Drivers/CMSIS/Include/core_cm7.h ****   \param [in]      IRQn  Device specific interrupt number.
2006:Drivers/CMSIS/Include/core_cm7.h ****   \return             0  Interrupt status is not active.
2007:Drivers/CMSIS/Include/core_cm7.h ****   \return             1  Interrupt status is active.
2008:Drivers/CMSIS/Include/core_cm7.h ****   \note    IRQn must not be negative.
2009:Drivers/CMSIS/Include/core_cm7.h ****  */
2010:Drivers/CMSIS/Include/core_cm7.h **** __STATIC_INLINE uint32_t __NVIC_GetActive(IRQn_Type IRQn)
2011:Drivers/CMSIS/Include/core_cm7.h **** {
2012:Drivers/CMSIS/Include/core_cm7.h ****   if ((int32_t)(IRQn) >= 0)
2013:Drivers/CMSIS/Include/core_cm7.h ****   {
2014:Drivers/CMSIS/Include/core_cm7.h ****     return((uint32_t)(((NVIC->IABR[(((uint32_t)IRQn) >> 5UL)] & (1UL << (((uint32_t)IRQn) & 0x1FUL)
2015:Drivers/CMSIS/Include/core_cm7.h ****   }
2016:Drivers/CMSIS/Include/core_cm7.h ****   else
2017:Drivers/CMSIS/Include/core_cm7.h ****   {
2018:Drivers/CMSIS/Include/core_cm7.h ****     return(0U);
2019:Drivers/CMSIS/Include/core_cm7.h ****   }
2020:Drivers/CMSIS/Include/core_cm7.h **** }
2021:Drivers/CMSIS/Include/core_cm7.h **** 
2022:Drivers/CMSIS/Include/core_cm7.h **** 
2023:Drivers/CMSIS/Include/core_cm7.h **** /**
2024:Drivers/CMSIS/Include/core_cm7.h ****   \brief   Set Interrupt Priority
2025:Drivers/CMSIS/Include/core_cm7.h ****   \details Sets the priority of a device specific interrupt or a processor exception.
2026:Drivers/CMSIS/Include/core_cm7.h ****            The interrupt number can be positive to specify a device specific interrupt,
2027:Drivers/CMSIS/Include/core_cm7.h ****            or negative to specify a processor exception.
2028:Drivers/CMSIS/Include/core_cm7.h ****   \param [in]      IRQn  Interrupt number.
2029:Drivers/CMSIS/Include/core_cm7.h ****   \param [in]  priority  Priority to set.
2030:Drivers/CMSIS/Include/core_cm7.h ****   \note    The priority cannot be set for every processor exception.
2031:Drivers/CMSIS/Include/core_cm7.h ****  */
2032:Drivers/CMSIS/Include/core_cm7.h **** __STATIC_INLINE void __NVIC_SetPriority(IRQn_Type IRQn, uint32_t priority)
2033:Drivers/CMSIS/Include/core_cm7.h **** {
 143              		.loc 2 2033 1 is_stmt 1 view -0
 144              		.cfi_startproc
 145              		@ args = 0, pretend = 0, frame = 0
 146              		@ frame_needed = 0, uses_anonymous_args = 0
 147              		@ link register save eliminated.
2034:Drivers/CMSIS/Include/core_cm7.h ****   if ((int32_t)(IRQn) >= 0)
 148              		.loc 2 2034 3 view .LVU29
 149              		.loc 2 2034 6 is_stmt 0 view .LVU30
 150 0000 0028     		cmp	r0, #0
 151              		.loc 2 2034 6 view .LVU31
 152 0002 04DB     		blt	.L10
2035:Drivers/CMSIS/Include/core_cm7.h ****   {
2036:Drivers/CMSIS/Include/core_cm7.h ****     NVIC->IP[((uint32_t)IRQn)]                = (uint8_t)((priority << (8U - __NVIC_PRIO_BITS)) & (
 153              		.loc 2 2036 5 is_stmt 1 view .LVU32
 154              		.loc 2 2036 49 is_stmt 0 view .LVU33
 155 0004 0901     		lsls	r1, r1, #4
 156              	.LVL5:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 56


 157              		.loc 2 2036 49 view .LVU34
 158 0006 C9B2     		uxtb	r1, r1
 159              		.loc 2 2036 47 view .LVU35
 160 0008 044B     		ldr	r3, .L12
 161 000a 1954     		strb	r1, [r3, r0]
 162 000c 7047     		bx	lr
 163              	.LVL6:
 164              	.L10:
2037:Drivers/CMSIS/Include/core_cm7.h ****   }
2038:Drivers/CMSIS/Include/core_cm7.h ****   else
2039:Drivers/CMSIS/Include/core_cm7.h ****   {
2040:Drivers/CMSIS/Include/core_cm7.h ****     SCB->SHPR[(((uint32_t)IRQn) & 0xFUL)-4UL] = (uint8_t)((priority << (8U - __NVIC_PRIO_BITS)) & (
 165              		.loc 2 2040 5 is_stmt 1 view .LVU36
 166              		.loc 2 2040 33 is_stmt 0 view .LVU37
 167 000e 00F00F00 		and	r0, r0, #15
 168              	.LVL7:
 169              		.loc 2 2040 49 view .LVU38
 170 0012 0901     		lsls	r1, r1, #4
 171              	.LVL8:
 172              		.loc 2 2040 49 view .LVU39
 173 0014 C9B2     		uxtb	r1, r1
 174              		.loc 2 2040 47 view .LVU40
 175 0016 024B     		ldr	r3, .L12+4
 176 0018 1954     		strb	r1, [r3, r0]
2041:Drivers/CMSIS/Include/core_cm7.h ****   }
2042:Drivers/CMSIS/Include/core_cm7.h **** }
 177              		.loc 2 2042 1 view .LVU41
 178 001a 7047     		bx	lr
 179              	.L13:
 180              		.align	2
 181              	.L12:
 182 001c 00E400E0 		.word	-*********
 183 0020 14ED00E0 		.word	-*********
 184              		.cfi_endproc
 185              	.LFE113:
 187              		.section	.text.__NVIC_GetPriority,"ax",%progbits
 188              		.align	1
 189              		.syntax unified
 190              		.thumb
 191              		.thumb_func
 193              	__NVIC_GetPriority:
 194              	.LVL9:
 195              	.LFB114:
2043:Drivers/CMSIS/Include/core_cm7.h **** 
2044:Drivers/CMSIS/Include/core_cm7.h **** 
2045:Drivers/CMSIS/Include/core_cm7.h **** /**
2046:Drivers/CMSIS/Include/core_cm7.h ****   \brief   Get Interrupt Priority
2047:Drivers/CMSIS/Include/core_cm7.h ****   \details Reads the priority of a device specific interrupt or a processor exception.
2048:Drivers/CMSIS/Include/core_cm7.h ****            The interrupt number can be positive to specify a device specific interrupt,
2049:Drivers/CMSIS/Include/core_cm7.h ****            or negative to specify a processor exception.
2050:Drivers/CMSIS/Include/core_cm7.h ****   \param [in]   IRQn  Interrupt number.
2051:Drivers/CMSIS/Include/core_cm7.h ****   \return             Interrupt Priority.
2052:Drivers/CMSIS/Include/core_cm7.h ****                       Value is aligned automatically to the implemented priority bits of the microc
2053:Drivers/CMSIS/Include/core_cm7.h ****  */
2054:Drivers/CMSIS/Include/core_cm7.h **** __STATIC_INLINE uint32_t __NVIC_GetPriority(IRQn_Type IRQn)
2055:Drivers/CMSIS/Include/core_cm7.h **** {
 196              		.loc 2 2055 1 is_stmt 1 view -0
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 57


 197              		.cfi_startproc
 198              		@ args = 0, pretend = 0, frame = 0
 199              		@ frame_needed = 0, uses_anonymous_args = 0
 200              		@ link register save eliminated.
2056:Drivers/CMSIS/Include/core_cm7.h **** 
2057:Drivers/CMSIS/Include/core_cm7.h ****   if ((int32_t)(IRQn) >= 0)
 201              		.loc 2 2057 3 view .LVU43
 202              		.loc 2 2057 6 is_stmt 0 view .LVU44
 203 0000 0028     		cmp	r0, #0
 204              		.loc 2 2057 6 view .LVU45
 205 0002 03DB     		blt	.L15
2058:Drivers/CMSIS/Include/core_cm7.h ****   {
2059:Drivers/CMSIS/Include/core_cm7.h ****     return(((uint32_t)NVIC->IP[((uint32_t)IRQn)]                >> (8U - __NVIC_PRIO_BITS)));
 206              		.loc 2 2059 5 is_stmt 1 view .LVU46
 207              		.loc 2 2059 31 is_stmt 0 view .LVU47
 208 0004 044B     		ldr	r3, .L17
 209 0006 185C     		ldrb	r0, [r3, r0]	@ zero_extendqisi2
 210              	.LVL10:
 211              		.loc 2 2059 65 view .LVU48
 212 0008 0009     		lsrs	r0, r0, #4
 213 000a 7047     		bx	lr
 214              	.L15:
2060:Drivers/CMSIS/Include/core_cm7.h ****   }
2061:Drivers/CMSIS/Include/core_cm7.h ****   else
2062:Drivers/CMSIS/Include/core_cm7.h ****   {
2063:Drivers/CMSIS/Include/core_cm7.h ****     return(((uint32_t)SCB->SHPR[(((uint32_t)IRQn) & 0xFUL)-4UL] >> (8U - __NVIC_PRIO_BITS)));
 215              		.loc 2 2063 5 is_stmt 1 view .LVU49
 216              		.loc 2 2063 51 is_stmt 0 view .LVU50
 217 000c 00F00F00 		and	r0, r0, #15
 218              		.loc 2 2063 32 view .LVU51
 219 0010 024B     		ldr	r3, .L17+4
 220 0012 185C     		ldrb	r0, [r3, r0]	@ zero_extendqisi2
 221              		.loc 2 2063 65 view .LVU52
 222 0014 0009     		lsrs	r0, r0, #4
2064:Drivers/CMSIS/Include/core_cm7.h ****   }
2065:Drivers/CMSIS/Include/core_cm7.h **** }
 223              		.loc 2 2065 1 view .LVU53
 224 0016 7047     		bx	lr
 225              	.L18:
 226              		.align	2
 227              	.L17:
 228 0018 00E400E0 		.word	-*********
 229 001c 14ED00E0 		.word	-*********
 230              		.cfi_endproc
 231              	.LFE114:
 233              		.section	.text.NVIC_EncodePriority,"ax",%progbits
 234              		.align	1
 235              		.syntax unified
 236              		.thumb
 237              		.thumb_func
 239              	NVIC_EncodePriority:
 240              	.LVL11:
 241              	.LFB115:
2066:Drivers/CMSIS/Include/core_cm7.h **** 
2067:Drivers/CMSIS/Include/core_cm7.h **** 
2068:Drivers/CMSIS/Include/core_cm7.h **** /**
2069:Drivers/CMSIS/Include/core_cm7.h ****   \brief   Encode Priority
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 58


2070:Drivers/CMSIS/Include/core_cm7.h ****   \details Encodes the priority for an interrupt with the given priority group,
2071:Drivers/CMSIS/Include/core_cm7.h ****            preemptive priority value, and subpriority value.
2072:Drivers/CMSIS/Include/core_cm7.h ****            In case of a conflict between priority grouping and available
2073:Drivers/CMSIS/Include/core_cm7.h ****            priority bits (__NVIC_PRIO_BITS), the smallest possible priority group is set.
2074:Drivers/CMSIS/Include/core_cm7.h ****   \param [in]     PriorityGroup  Used priority group.
2075:Drivers/CMSIS/Include/core_cm7.h ****   \param [in]   PreemptPriority  Preemptive priority value (starting from 0).
2076:Drivers/CMSIS/Include/core_cm7.h ****   \param [in]       SubPriority  Subpriority value (starting from 0).
2077:Drivers/CMSIS/Include/core_cm7.h ****   \return                        Encoded priority. Value can be used in the function \ref NVIC_SetP
2078:Drivers/CMSIS/Include/core_cm7.h ****  */
2079:Drivers/CMSIS/Include/core_cm7.h **** __STATIC_INLINE uint32_t NVIC_EncodePriority (uint32_t PriorityGroup, uint32_t PreemptPriority, uin
2080:Drivers/CMSIS/Include/core_cm7.h **** {
 242              		.loc 2 2080 1 is_stmt 1 view -0
 243              		.cfi_startproc
 244              		@ args = 0, pretend = 0, frame = 0
 245              		@ frame_needed = 0, uses_anonymous_args = 0
 246              		.loc 2 2080 1 is_stmt 0 view .LVU55
 247 0000 00B5     		push	{lr}
 248              	.LCFI0:
 249              		.cfi_def_cfa_offset 4
 250              		.cfi_offset 14, -4
2081:Drivers/CMSIS/Include/core_cm7.h ****   uint32_t PriorityGroupTmp = (PriorityGroup & (uint32_t)0x07UL);   /* only values 0..7 are used   
 251              		.loc 2 2081 3 is_stmt 1 view .LVU56
 252              		.loc 2 2081 12 is_stmt 0 view .LVU57
 253 0002 00F00700 		and	r0, r0, #7
 254              	.LVL12:
2082:Drivers/CMSIS/Include/core_cm7.h ****   uint32_t PreemptPriorityBits;
 255              		.loc 2 2082 3 is_stmt 1 view .LVU58
2083:Drivers/CMSIS/Include/core_cm7.h ****   uint32_t SubPriorityBits;
 256              		.loc 2 2083 3 view .LVU59
2084:Drivers/CMSIS/Include/core_cm7.h **** 
2085:Drivers/CMSIS/Include/core_cm7.h ****   PreemptPriorityBits = ((7UL - PriorityGroupTmp) > (uint32_t)(__NVIC_PRIO_BITS)) ? (uint32_t)(__NV
 257              		.loc 2 2085 3 view .LVU60
 258              		.loc 2 2085 31 is_stmt 0 view .LVU61
 259 0006 C0F1070C 		rsb	ip, r0, #7
 260              		.loc 2 2085 23 view .LVU62
 261 000a BCF1040F 		cmp	ip, #4
 262 000e 28BF     		it	cs
 263 0010 4FF0040C 		movcs	ip, #4
 264              	.LVL13:
2086:Drivers/CMSIS/Include/core_cm7.h ****   SubPriorityBits     = ((PriorityGroupTmp + (uint32_t)(__NVIC_PRIO_BITS)) < (uint32_t)7UL) ? (uint
 265              		.loc 2 2086 3 is_stmt 1 view .LVU63
 266              		.loc 2 2086 44 is_stmt 0 view .LVU64
 267 0014 031D     		adds	r3, r0, #4
 268              		.loc 2 2086 109 view .LVU65
 269 0016 062B     		cmp	r3, #6
 270 0018 0FD9     		bls	.L21
 271              		.loc 2 2086 109 discriminator 1 view .LVU66
 272 001a C31E     		subs	r3, r0, #3
 273              	.L20:
 274              	.LVL14:
2087:Drivers/CMSIS/Include/core_cm7.h **** 
2088:Drivers/CMSIS/Include/core_cm7.h ****   return (
 275              		.loc 2 2088 3 is_stmt 1 view .LVU67
2089:Drivers/CMSIS/Include/core_cm7.h ****            ((PreemptPriority & (uint32_t)((1UL << (PreemptPriorityBits)) - 1UL)) << SubPriorityBits
 276              		.loc 2 2089 30 is_stmt 0 view .LVU68
 277 001c 4FF0FF3E 		mov	lr, #-1
 278 0020 0EFA0CF0 		lsl	r0, lr, ip
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 59


 279              	.LVL15:
 280              		.loc 2 2089 30 view .LVU69
 281 0024 21EA0001 		bic	r1, r1, r0
 282              	.LVL16:
 283              		.loc 2 2089 82 view .LVU70
 284 0028 9940     		lsls	r1, r1, r3
2090:Drivers/CMSIS/Include/core_cm7.h ****            ((SubPriority     & (uint32_t)((1UL << (SubPriorityBits    )) - 1UL)))
 285              		.loc 2 2090 30 view .LVU71
 286 002a 0EFA03FE 		lsl	lr, lr, r3
 287 002e 22EA0E02 		bic	r2, r2, lr
 288              	.LVL17:
2091:Drivers/CMSIS/Include/core_cm7.h ****          );
2092:Drivers/CMSIS/Include/core_cm7.h **** }
 289              		.loc 2 2092 1 view .LVU72
 290 0032 41EA0200 		orr	r0, r1, r2
 291 0036 5DF804FB 		ldr	pc, [sp], #4
 292              	.LVL18:
 293              	.L21:
2086:Drivers/CMSIS/Include/core_cm7.h **** 
 294              		.loc 2 2086 109 discriminator 2 view .LVU73
 295 003a 0023     		movs	r3, #0
 296 003c EEE7     		b	.L20
 297              		.cfi_endproc
 298              	.LFE115:
 300              		.section	.text.NVIC_DecodePriority,"ax",%progbits
 301              		.align	1
 302              		.syntax unified
 303              		.thumb
 304              		.thumb_func
 306              	NVIC_DecodePriority:
 307              	.LVL19:
 308              	.LFB116:
2093:Drivers/CMSIS/Include/core_cm7.h **** 
2094:Drivers/CMSIS/Include/core_cm7.h **** 
2095:Drivers/CMSIS/Include/core_cm7.h **** /**
2096:Drivers/CMSIS/Include/core_cm7.h ****   \brief   Decode Priority
2097:Drivers/CMSIS/Include/core_cm7.h ****   \details Decodes an interrupt priority value with a given priority group to
2098:Drivers/CMSIS/Include/core_cm7.h ****            preemptive priority value and subpriority value.
2099:Drivers/CMSIS/Include/core_cm7.h ****            In case of a conflict between priority grouping and available
2100:Drivers/CMSIS/Include/core_cm7.h ****            priority bits (__NVIC_PRIO_BITS) the smallest possible priority group is set.
2101:Drivers/CMSIS/Include/core_cm7.h ****   \param [in]         Priority   Priority value, which can be retrieved with the function \ref NVIC
2102:Drivers/CMSIS/Include/core_cm7.h ****   \param [in]     PriorityGroup  Used priority group.
2103:Drivers/CMSIS/Include/core_cm7.h ****   \param [out] pPreemptPriority  Preemptive priority value (starting from 0).
2104:Drivers/CMSIS/Include/core_cm7.h ****   \param [out]     pSubPriority  Subpriority value (starting from 0).
2105:Drivers/CMSIS/Include/core_cm7.h ****  */
2106:Drivers/CMSIS/Include/core_cm7.h **** __STATIC_INLINE void NVIC_DecodePriority (uint32_t Priority, uint32_t PriorityGroup, uint32_t* cons
2107:Drivers/CMSIS/Include/core_cm7.h **** {
 309              		.loc 2 2107 1 is_stmt 1 view -0
 310              		.cfi_startproc
 311              		@ args = 0, pretend = 0, frame = 0
 312              		@ frame_needed = 0, uses_anonymous_args = 0
 313              		.loc 2 2107 1 is_stmt 0 view .LVU75
 314 0000 10B5     		push	{r4, lr}
 315              	.LCFI1:
 316              		.cfi_def_cfa_offset 8
 317              		.cfi_offset 4, -8
 318              		.cfi_offset 14, -4
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 60


2108:Drivers/CMSIS/Include/core_cm7.h ****   uint32_t PriorityGroupTmp = (PriorityGroup & (uint32_t)0x07UL);   /* only values 0..7 are used   
 319              		.loc 2 2108 3 is_stmt 1 view .LVU76
 320              		.loc 2 2108 12 is_stmt 0 view .LVU77
 321 0002 01F00701 		and	r1, r1, #7
 322              	.LVL20:
2109:Drivers/CMSIS/Include/core_cm7.h ****   uint32_t PreemptPriorityBits;
 323              		.loc 2 2109 3 is_stmt 1 view .LVU78
2110:Drivers/CMSIS/Include/core_cm7.h ****   uint32_t SubPriorityBits;
 324              		.loc 2 2110 3 view .LVU79
2111:Drivers/CMSIS/Include/core_cm7.h **** 
2112:Drivers/CMSIS/Include/core_cm7.h ****   PreemptPriorityBits = ((7UL - PriorityGroupTmp) > (uint32_t)(__NVIC_PRIO_BITS)) ? (uint32_t)(__NV
 325              		.loc 2 2112 3 view .LVU80
 326              		.loc 2 2112 31 is_stmt 0 view .LVU81
 327 0006 C1F1070C 		rsb	ip, r1, #7
 328              		.loc 2 2112 23 view .LVU82
 329 000a BCF1040F 		cmp	ip, #4
 330 000e 28BF     		it	cs
 331 0010 4FF0040C 		movcs	ip, #4
 332              	.LVL21:
2113:Drivers/CMSIS/Include/core_cm7.h ****   SubPriorityBits     = ((PriorityGroupTmp + (uint32_t)(__NVIC_PRIO_BITS)) < (uint32_t)7UL) ? (uint
 333              		.loc 2 2113 3 is_stmt 1 view .LVU83
 334              		.loc 2 2113 44 is_stmt 0 view .LVU84
 335 0014 0C1D     		adds	r4, r1, #4
 336              		.loc 2 2113 109 view .LVU85
 337 0016 062C     		cmp	r4, #6
 338 0018 0FD9     		bls	.L25
 339              		.loc 2 2113 109 discriminator 1 view .LVU86
 340 001a 0339     		subs	r1, r1, #3
 341              	.LVL22:
 342              	.L24:
2114:Drivers/CMSIS/Include/core_cm7.h **** 
2115:Drivers/CMSIS/Include/core_cm7.h ****   *pPreemptPriority = (Priority >> SubPriorityBits) & (uint32_t)((1UL << (PreemptPriorityBits)) - 1
 343              		.loc 2 2115 3 is_stmt 1 view .LVU87
 344              		.loc 2 2115 33 is_stmt 0 view .LVU88
 345 001c 20FA01F4 		lsr	r4, r0, r1
 346              	.LVL23:
 347              		.loc 2 2115 53 view .LVU89
 348 0020 4FF0FF3E 		mov	lr, #-1
 349 0024 0EFA0CFC 		lsl	ip, lr, ip
 350              	.LVL24:
 351              		.loc 2 2115 53 view .LVU90
 352 0028 24EA0C04 		bic	r4, r4, ip
 353              		.loc 2 2115 21 view .LVU91
 354 002c 1460     		str	r4, [r2]
2116:Drivers/CMSIS/Include/core_cm7.h ****   *pSubPriority     = (Priority                   ) & (uint32_t)((1UL << (SubPriorityBits    )) - 1
 355              		.loc 2 2116 3 is_stmt 1 view .LVU92
 356              		.loc 2 2116 53 is_stmt 0 view .LVU93
 357 002e 0EFA01FE 		lsl	lr, lr, r1
 358 0032 20EA0E00 		bic	r0, r0, lr
 359              	.LVL25:
 360              		.loc 2 2116 21 view .LVU94
 361 0036 1860     		str	r0, [r3]
2117:Drivers/CMSIS/Include/core_cm7.h **** }
 362              		.loc 2 2117 1 view .LVU95
 363 0038 10BD     		pop	{r4, pc}
 364              	.LVL26:
 365              	.L25:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 61


2113:Drivers/CMSIS/Include/core_cm7.h **** 
 366              		.loc 2 2113 109 discriminator 2 view .LVU96
 367 003a 0021     		movs	r1, #0
 368              	.LVL27:
2113:Drivers/CMSIS/Include/core_cm7.h **** 
 369              		.loc 2 2113 109 discriminator 2 view .LVU97
 370 003c EEE7     		b	.L24
 371              		.cfi_endproc
 372              	.LFE116:
 374              		.section	.text.__NVIC_SystemReset,"ax",%progbits
 375              		.align	1
 376              		.syntax unified
 377              		.thumb
 378              		.thumb_func
 380              	__NVIC_SystemReset:
 381              	.LFB119:
2118:Drivers/CMSIS/Include/core_cm7.h **** 
2119:Drivers/CMSIS/Include/core_cm7.h **** 
2120:Drivers/CMSIS/Include/core_cm7.h **** /**
2121:Drivers/CMSIS/Include/core_cm7.h ****   \brief   Set Interrupt Vector
2122:Drivers/CMSIS/Include/core_cm7.h ****   \details Sets an interrupt vector in SRAM based interrupt vector table.
2123:Drivers/CMSIS/Include/core_cm7.h ****            The interrupt number can be positive to specify a device specific interrupt,
2124:Drivers/CMSIS/Include/core_cm7.h ****            or negative to specify a processor exception.
2125:Drivers/CMSIS/Include/core_cm7.h ****            VTOR must been relocated to SRAM before.
2126:Drivers/CMSIS/Include/core_cm7.h ****   \param [in]   IRQn      Interrupt number
2127:Drivers/CMSIS/Include/core_cm7.h ****   \param [in]   vector    Address of interrupt handler function
2128:Drivers/CMSIS/Include/core_cm7.h ****  */
2129:Drivers/CMSIS/Include/core_cm7.h **** __STATIC_INLINE void __NVIC_SetVector(IRQn_Type IRQn, uint32_t vector)
2130:Drivers/CMSIS/Include/core_cm7.h **** {
2131:Drivers/CMSIS/Include/core_cm7.h ****   uint32_t vectors = (uint32_t )SCB->VTOR;
2132:Drivers/CMSIS/Include/core_cm7.h ****   (* (int *) (vectors + ((int32_t)IRQn + NVIC_USER_IRQ_OFFSET) * 4)) = vector;
2133:Drivers/CMSIS/Include/core_cm7.h ****   __DSB();
2134:Drivers/CMSIS/Include/core_cm7.h **** }
2135:Drivers/CMSIS/Include/core_cm7.h **** 
2136:Drivers/CMSIS/Include/core_cm7.h **** 
2137:Drivers/CMSIS/Include/core_cm7.h **** /**
2138:Drivers/CMSIS/Include/core_cm7.h ****   \brief   Get Interrupt Vector
2139:Drivers/CMSIS/Include/core_cm7.h ****   \details Reads an interrupt vector from interrupt vector table.
2140:Drivers/CMSIS/Include/core_cm7.h ****            The interrupt number can be positive to specify a device specific interrupt,
2141:Drivers/CMSIS/Include/core_cm7.h ****            or negative to specify a processor exception.
2142:Drivers/CMSIS/Include/core_cm7.h ****   \param [in]   IRQn      Interrupt number.
2143:Drivers/CMSIS/Include/core_cm7.h ****   \return                 Address of interrupt handler function
2144:Drivers/CMSIS/Include/core_cm7.h ****  */
2145:Drivers/CMSIS/Include/core_cm7.h **** __STATIC_INLINE uint32_t __NVIC_GetVector(IRQn_Type IRQn)
2146:Drivers/CMSIS/Include/core_cm7.h **** {
2147:Drivers/CMSIS/Include/core_cm7.h ****   uint32_t vectors = (uint32_t )SCB->VTOR;
2148:Drivers/CMSIS/Include/core_cm7.h ****   return (uint32_t)(* (int *) (vectors + ((int32_t)IRQn + NVIC_USER_IRQ_OFFSET) * 4));
2149:Drivers/CMSIS/Include/core_cm7.h **** }
2150:Drivers/CMSIS/Include/core_cm7.h **** 
2151:Drivers/CMSIS/Include/core_cm7.h **** 
2152:Drivers/CMSIS/Include/core_cm7.h **** /**
2153:Drivers/CMSIS/Include/core_cm7.h ****   \brief   System Reset
2154:Drivers/CMSIS/Include/core_cm7.h ****   \details Initiates a system reset request to reset the MCU.
2155:Drivers/CMSIS/Include/core_cm7.h ****  */
2156:Drivers/CMSIS/Include/core_cm7.h **** __NO_RETURN __STATIC_INLINE void __NVIC_SystemReset(void)
2157:Drivers/CMSIS/Include/core_cm7.h **** {
 382              		.loc 2 2157 1 is_stmt 1 view -0
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 62


 383              		.cfi_startproc
 384              		@ Volatile: function does not return.
 385              		@ args = 0, pretend = 0, frame = 0
 386              		@ frame_needed = 0, uses_anonymous_args = 0
 387              		@ link register save eliminated.
2158:Drivers/CMSIS/Include/core_cm7.h ****   __DSB();                                                          /* Ensure all outstanding memor
 388              		.loc 2 2158 3 view .LVU99
 389              	.LBB40:
 390              	.LBI40:
 944:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 391              		.loc 3 944 27 view .LVU100
 392              	.LBB41:
 393              		.loc 3 946 3 view .LVU101
 394              		.syntax unified
 395              	@ 946 "Drivers/CMSIS/Include/cmsis_gcc.h" 1
 396 0000 BFF34F8F 		dsb 0xF
 397              	@ 0 "" 2
 398              		.thumb
 399              		.syntax unified
 400              	.LBE41:
 401              	.LBE40:
2159:Drivers/CMSIS/Include/core_cm7.h ****                                                                        buffered write are completed
2160:Drivers/CMSIS/Include/core_cm7.h ****   SCB->AIRCR  = (uint32_t)((0x5FAUL << SCB_AIRCR_VECTKEY_Pos)    |
 402              		.loc 2 2160 3 view .LVU102
2161:Drivers/CMSIS/Include/core_cm7.h ****                            (SCB->AIRCR & SCB_AIRCR_PRIGROUP_Msk) |
 403              		.loc 2 2161 32 is_stmt 0 view .LVU103
 404 0004 0549     		ldr	r1, .L29
 405 0006 CA68     		ldr	r2, [r1, #12]
 406              		.loc 2 2161 40 view .LVU104
 407 0008 02F4E062 		and	r2, r2, #1792
2160:Drivers/CMSIS/Include/core_cm7.h ****                            (SCB->AIRCR & SCB_AIRCR_PRIGROUP_Msk) |
 408              		.loc 2 2160 17 view .LVU105
 409 000c 044B     		ldr	r3, .L29+4
 410 000e 1343     		orrs	r3, r3, r2
2160:Drivers/CMSIS/Include/core_cm7.h ****                            (SCB->AIRCR & SCB_AIRCR_PRIGROUP_Msk) |
 411              		.loc 2 2160 15 view .LVU106
 412 0010 CB60     		str	r3, [r1, #12]
2162:Drivers/CMSIS/Include/core_cm7.h ****                             SCB_AIRCR_SYSRESETREQ_Msk    );         /* Keep priority group unchange
2163:Drivers/CMSIS/Include/core_cm7.h ****   __DSB();                                                          /* Ensure completion of memory 
 413              		.loc 2 2163 3 is_stmt 1 view .LVU107
 414              	.LBB42:
 415              	.LBI42:
 944:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 416              		.loc 3 944 27 view .LVU108
 417              	.LBB43:
 418              		.loc 3 946 3 view .LVU109
 419              		.syntax unified
 420              	@ 946 "Drivers/CMSIS/Include/cmsis_gcc.h" 1
 421 0012 BFF34F8F 		dsb 0xF
 422              	@ 0 "" 2
 423              		.thumb
 424              		.syntax unified
 425              	.L28:
 426              	.LBE43:
 427              	.LBE42:
2164:Drivers/CMSIS/Include/core_cm7.h **** 
2165:Drivers/CMSIS/Include/core_cm7.h ****   for(;;)                                                           /* wait until reset */
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 63


 428              		.loc 2 2165 3 view .LVU110
2166:Drivers/CMSIS/Include/core_cm7.h ****   {
2167:Drivers/CMSIS/Include/core_cm7.h ****     __NOP();
 429              		.loc 2 2167 5 discriminator 1 view .LVU111
 430              		.syntax unified
 431              	@ 2167 "Drivers/CMSIS/Include/core_cm7.h" 1
 432 0016 00BF     		nop
 433              	@ 0 "" 2
2165:Drivers/CMSIS/Include/core_cm7.h ****   {
 434              		.loc 2 2165 3 view .LVU112
 435              		.thumb
 436              		.syntax unified
 437 0018 FDE7     		b	.L28
 438              	.L30:
 439 001a 00BF     		.align	2
 440              	.L29:
 441 001c 00ED00E0 		.word	-*********
 442 0020 0400FA05 		.word	*********
 443              		.cfi_endproc
 444              	.LFE119:
 446              		.section	.text.HAL_NVIC_SetPriorityGrouping,"ax",%progbits
 447              		.align	1
 448              		.global	HAL_NVIC_SetPriorityGrouping
 449              		.syntax unified
 450              		.thumb
 451              		.thumb_func
 453              	HAL_NVIC_SetPriorityGrouping:
 454              	.LVL28:
 455              	.LFB144:
   1:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** /**
   2:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   ******************************************************************************
   3:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * @file    stm32h7xx_hal_cortex.c
   4:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * <AUTHOR> Application Team
   5:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * @brief   CORTEX HAL module driver.
   6:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   *          This file provides firmware functions to manage the following
   7:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   *          functionalities of the CORTEX:
   8:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   *           + Initialization and de-initialization functions
   9:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   *           + Peripheral Control functions
  10:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   *
  11:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   @verbatim
  12:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   ==============================================================================
  13:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****                         ##### How to use this driver #####
  14:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   ==============================================================================
  15:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** 
  16:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****     [..]
  17:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****     *** How to configure Interrupts using CORTEX HAL driver ***
  18:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****     ===========================================================
  19:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****     [..]
  20:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****     This section provides functions allowing to configure the NVIC interrupts (IRQ).
  21:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****     The Cortex-M exceptions are managed by CMSIS functions.
  22:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** 
  23:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****     (#) Configure the NVIC Priority Grouping using HAL_NVIC_SetPriorityGrouping()
  24:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****         function according to the following table.
  25:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****     (#) Configure the priority of the selected IRQ Channels using HAL_NVIC_SetPriority().
  26:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****     (#) Enable the selected IRQ Channels using HAL_NVIC_EnableIRQ().
  27:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****     (#) please refer to programming manual for details in how to configure priority.
  28:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 64


  29:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****      -@- When the NVIC_PRIORITYGROUP_0 is selected, IRQ preemption is no more possible.
  30:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****          The pending IRQ priority will be managed only by the sub priority.
  31:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** 
  32:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****      -@- IRQ priority order (sorted by highest to lowest priority):
  33:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****         (+@) Lowest preemption priority
  34:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****         (+@) Lowest sub priority
  35:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****         (+@) Lowest hardware priority (IRQ number)
  36:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** 
  37:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****     [..]
  38:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****     *** How to configure Systick using CORTEX HAL driver ***
  39:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****     ========================================================
  40:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****     [..]
  41:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****     Setup SysTick Timer for time base.
  42:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** 
  43:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****    (+) The HAL_SYSTICK_Config() function calls the SysTick_Config() function which
  44:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****        is a CMSIS function that:
  45:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****         (++) Configures the SysTick Reload register with value passed as function parameter.
  46:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****         (++) Configures the SysTick IRQ priority to the lowest value (0x0F).
  47:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****         (++) Resets the SysTick Counter register.
  48:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****         (++) Configures the SysTick Counter clock source to be Core Clock Source (HCLK).
  49:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****         (++) Enables the SysTick Interrupt.
  50:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****         (++) Starts the SysTick Counter.
  51:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** 
  52:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****    (+) You can change the SysTick Clock source to be HCLK_Div8 by calling the macro
  53:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****        HAL_SYSTICK_CLKSourceConfig(SYSTICK_CLKSOURCE_HCLK_DIV8) just after the
  54:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****        HAL_SYSTICK_Config() function call. The HAL_SYSTICK_CLKSourceConfig() macro is defined
  55:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****        inside the stm32h7xx_hal_cortex.h file.
  56:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** 
  57:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****    (+) You can change the SysTick IRQ priority by calling the
  58:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****        HAL_NVIC_SetPriority(SysTick_IRQn,...) function just after the HAL_SYSTICK_Config() function
  59:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****        call. The HAL_NVIC_SetPriority() call the NVIC_SetPriority() function which is a CMSIS funct
  60:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** 
  61:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****    (+) To adjust the SysTick time base, use the following formula:
  62:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** 
  63:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****        Reload Value = SysTick Counter Clock (Hz) x  Desired Time base (s)
  64:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****        (++) Reload Value is the parameter to be passed for HAL_SYSTICK_Config() function
  65:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****        (++) Reload Value should not exceed 0xFFFFFF
  66:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** 
  67:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   @endverbatim
  68:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   ******************************************************************************
  69:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * @attention
  70:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   *
  71:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * Copyright (c) 2017 STMicroelectronics.
  72:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * All rights reserved.
  73:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   *
  74:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * This software is licensed under terms that can be found in the LICENSE file in
  75:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * the root directory of this software component.
  76:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * If no LICENSE file comes with this software, it is provided AS-IS.
  77:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   *
  78:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   ******************************************************************************
  79:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   */
  80:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** 
  81:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** /* Includes ------------------------------------------------------------------*/
  82:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** #include "stm32h7xx_hal.h"
  83:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** 
  84:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** /** @addtogroup STM32H7xx_HAL_Driver
  85:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * @{
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 65


  86:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   */
  87:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** 
  88:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** /** @defgroup CORTEX CORTEX
  89:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * @brief CORTEX HAL module driver
  90:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * @{
  91:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   */
  92:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** 
  93:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** #ifdef HAL_CORTEX_MODULE_ENABLED
  94:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** 
  95:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** /* Private types -------------------------------------------------------------*/
  96:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** /* Private variables ---------------------------------------------------------*/
  97:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** /* Private constants ---------------------------------------------------------*/
  98:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** /* Private macros ------------------------------------------------------------*/
  99:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** /* Private functions ---------------------------------------------------------*/
 100:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** /* Exported functions --------------------------------------------------------*/
 101:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** 
 102:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** /** @defgroup CORTEX_Exported_Functions CORTEX Exported Functions
 103:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * @{
 104:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   */
 105:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** 
 106:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** 
 107:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** /** @defgroup CORTEX_Exported_Functions_Group1 Initialization and de-initialization functions
 108:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****  *  @brief    Initialization and Configuration functions
 109:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****  *
 110:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** @verbatim
 111:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   ==============================================================================
 112:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****               ##### Initialization and de-initialization functions #####
 113:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   ==============================================================================
 114:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****     [..]
 115:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****       This section provides the CORTEX HAL driver functions allowing to configure Interrupts
 116:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****       Systick functionalities
 117:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** 
 118:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** @endverbatim
 119:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * @{
 120:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   */
 121:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** 
 122:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** 
 123:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** /**
 124:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * @brief  Sets the priority grouping field (preemption priority and subpriority)
 125:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   *         using the required unlock sequence.
 126:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * @param  PriorityGroup The priority grouping bits length.
 127:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   *         This parameter can be one of the following values:
 128:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   *         @arg NVIC_PRIORITYGROUP_0: 0 bits for preemption priority
 129:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   *                                    4 bits for subpriority
 130:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   *         @arg NVIC_PRIORITYGROUP_1: 1 bits for preemption priority
 131:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   *                                    3 bits for subpriority
 132:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   *         @arg NVIC_PRIORITYGROUP_2: 2 bits for preemption priority
 133:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   *                                    2 bits for subpriority
 134:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   *         @arg NVIC_PRIORITYGROUP_3: 3 bits for preemption priority
 135:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   *                                    1 bits for subpriority
 136:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   *         @arg NVIC_PRIORITYGROUP_4: 4 bits for preemption priority
 137:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   *                                    0 bits for subpriority
 138:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * @note   When the NVIC_PriorityGroup_0 is selected, IRQ preemption is no more possible.
 139:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   *         The pending IRQ priority will be managed only by the subpriority.
 140:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * @retval None
 141:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   */
 142:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** void HAL_NVIC_SetPriorityGrouping(uint32_t PriorityGroup)
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 66


 143:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** {
 456              		.loc 1 143 1 view -0
 457              		.cfi_startproc
 458              		@ args = 0, pretend = 0, frame = 0
 459              		@ frame_needed = 0, uses_anonymous_args = 0
 460              		@ link register save eliminated.
 144:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   /* Check the parameters */
 145:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   assert_param(IS_NVIC_PRIORITY_GROUP(PriorityGroup));
 461              		.loc 1 145 3 view .LVU114
 146:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** 
 147:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   /* Set the PRIGROUP[10:8] bits according to the PriorityGroup parameter value */
 148:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   NVIC_SetPriorityGrouping(PriorityGroup);
 462              		.loc 1 148 3 view .LVU115
 463              	.LBB44:
 464              	.LBI44:
1871:Drivers/CMSIS/Include/core_cm7.h **** {
 465              		.loc 2 1871 22 view .LVU116
 466              	.LBB45:
1873:Drivers/CMSIS/Include/core_cm7.h ****   uint32_t PriorityGroupTmp = (PriorityGroup & (uint32_t)0x07UL);             /* only values 0..7 a
 467              		.loc 2 1873 3 view .LVU117
1874:Drivers/CMSIS/Include/core_cm7.h **** 
 468              		.loc 2 1874 3 view .LVU118
1876:Drivers/CMSIS/Include/core_cm7.h ****   reg_value &= ~((uint32_t)(SCB_AIRCR_VECTKEY_Msk | SCB_AIRCR_PRIGROUP_Msk)); /* clear bits to chan
 469              		.loc 2 1876 3 view .LVU119
1876:Drivers/CMSIS/Include/core_cm7.h ****   reg_value &= ~((uint32_t)(SCB_AIRCR_VECTKEY_Msk | SCB_AIRCR_PRIGROUP_Msk)); /* clear bits to chan
 470              		.loc 2 1876 14 is_stmt 0 view .LVU120
 471 0000 0649     		ldr	r1, .L32
 472 0002 CB68     		ldr	r3, [r1, #12]
 473              	.LVL29:
1877:Drivers/CMSIS/Include/core_cm7.h ****   reg_value  =  (reg_value                                   |
 474              		.loc 2 1877 3 is_stmt 1 view .LVU121
1877:Drivers/CMSIS/Include/core_cm7.h ****   reg_value  =  (reg_value                                   |
 475              		.loc 2 1877 13 is_stmt 0 view .LVU122
 476 0004 23F4E063 		bic	r3, r3, #1792
 477              	.LVL30:
1877:Drivers/CMSIS/Include/core_cm7.h ****   reg_value  =  (reg_value                                   |
 478              		.loc 2 1877 13 view .LVU123
 479 0008 1B04     		lsls	r3, r3, #16
 480 000a 1B0C     		lsrs	r3, r3, #16
 481              	.LVL31:
1878:Drivers/CMSIS/Include/core_cm7.h ****                 ((uint32_t)0x5FAUL << SCB_AIRCR_VECTKEY_Pos) |
 482              		.loc 2 1878 3 is_stmt 1 view .LVU124
1880:Drivers/CMSIS/Include/core_cm7.h ****   SCB->AIRCR =  reg_value;
 483              		.loc 2 1880 35 is_stmt 0 view .LVU125
 484 000c 0002     		lsls	r0, r0, #8
 485              	.LVL32:
1880:Drivers/CMSIS/Include/core_cm7.h ****   SCB->AIRCR =  reg_value;
 486              		.loc 2 1880 35 view .LVU126
 487 000e 00F4E060 		and	r0, r0, #1792
1879:Drivers/CMSIS/Include/core_cm7.h ****                 (PriorityGroupTmp << SCB_AIRCR_PRIGROUP_Pos)  );              /* Insert write key a
 488              		.loc 2 1879 62 view .LVU127
 489 0012 0343     		orrs	r3, r3, r0
 490              	.LVL33:
1878:Drivers/CMSIS/Include/core_cm7.h ****                 ((uint32_t)0x5FAUL << SCB_AIRCR_VECTKEY_Pos) |
 491              		.loc 2 1878 14 view .LVU128
 492 0014 024A     		ldr	r2, .L32+4
 493 0016 1A43     		orrs	r2, r2, r3
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 67


 494              	.LVL34:
1881:Drivers/CMSIS/Include/core_cm7.h **** }
 495              		.loc 2 1881 3 is_stmt 1 view .LVU129
1881:Drivers/CMSIS/Include/core_cm7.h **** }
 496              		.loc 2 1881 14 is_stmt 0 view .LVU130
 497 0018 CA60     		str	r2, [r1, #12]
 498              	.LVL35:
1881:Drivers/CMSIS/Include/core_cm7.h **** }
 499              		.loc 2 1881 14 view .LVU131
 500              	.LBE45:
 501              	.LBE44:
 149:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** }
 502              		.loc 1 149 1 view .LVU132
 503 001a 7047     		bx	lr
 504              	.L33:
 505              		.align	2
 506              	.L32:
 507 001c 00ED00E0 		.word	-*********
 508 0020 0000FA05 		.word	*********
 509              		.cfi_endproc
 510              	.LFE144:
 512              		.section	.text.HAL_NVIC_SetPriority,"ax",%progbits
 513              		.align	1
 514              		.global	HAL_NVIC_SetPriority
 515              		.syntax unified
 516              		.thumb
 517              		.thumb_func
 519              	HAL_NVIC_SetPriority:
 520              	.LVL36:
 521              	.LFB145:
 150:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** 
 151:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** /**
 152:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * @brief  Sets the priority of an interrupt.
 153:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * @param  IRQn External interrupt number.
 154:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   *         This parameter can be an enumerator of IRQn_Type enumeration
 155:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   *         (For the complete STM32 Devices IRQ Channels list, please refer to the appropriate CMSI
 156:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * @param  PreemptPriority The preemption priority for the IRQn channel.
 157:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   *         This parameter can be a value between 0 and 15
 158:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   *         A lower priority value indicates a higher priority
 159:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * @param  SubPriority the subpriority level for the IRQ channel.
 160:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   *         This parameter can be a value between 0 and 15
 161:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   *         A lower priority value indicates a higher priority.
 162:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * @retval None
 163:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   */
 164:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** void HAL_NVIC_SetPriority(IRQn_Type IRQn, uint32_t PreemptPriority, uint32_t SubPriority)
 165:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** {
 522              		.loc 1 165 1 is_stmt 1 view -0
 523              		.cfi_startproc
 524              		@ args = 0, pretend = 0, frame = 0
 525              		@ frame_needed = 0, uses_anonymous_args = 0
 526              		.loc 1 165 1 is_stmt 0 view .LVU134
 527 0000 10B5     		push	{r4, lr}
 528              	.LCFI2:
 529              		.cfi_def_cfa_offset 8
 530              		.cfi_offset 4, -8
 531              		.cfi_offset 14, -4
 532 0002 0446     		mov	r4, r0
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 68


 166:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   uint32_t prioritygroup;
 533              		.loc 1 166 3 is_stmt 1 view .LVU135
 167:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** 
 168:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   /* Check the parameters */
 169:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   assert_param(IS_NVIC_SUB_PRIORITY(SubPriority));
 534              		.loc 1 169 3 view .LVU136
 170:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   assert_param(IS_NVIC_PREEMPTION_PRIORITY(PreemptPriority));
 535              		.loc 1 170 3 view .LVU137
 171:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** 
 172:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   prioritygroup = NVIC_GetPriorityGrouping();
 536              		.loc 1 172 3 view .LVU138
 537              	.LBB46:
 538              	.LBI46:
1890:Drivers/CMSIS/Include/core_cm7.h **** {
 539              		.loc 2 1890 26 view .LVU139
 540              	.LBB47:
1892:Drivers/CMSIS/Include/core_cm7.h **** }
 541              		.loc 2 1892 3 view .LVU140
1892:Drivers/CMSIS/Include/core_cm7.h **** }
 542              		.loc 2 1892 26 is_stmt 0 view .LVU141
 543 0004 054B     		ldr	r3, .L36
 544 0006 D868     		ldr	r0, [r3, #12]
 545              	.LVL37:
1892:Drivers/CMSIS/Include/core_cm7.h **** }
 546              		.loc 2 1892 26 view .LVU142
 547              	.LBE47:
 548              	.LBE46:
 173:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** 
 174:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   NVIC_SetPriority(IRQn, NVIC_EncodePriority(prioritygroup, PreemptPriority, SubPriority));
 549              		.loc 1 174 3 is_stmt 1 view .LVU143
 550 0008 C0F30220 		ubfx	r0, r0, #8, #3
 551              	.LVL38:
 552              		.loc 1 174 3 is_stmt 0 view .LVU144
 553 000c FFF7FEFF 		bl	NVIC_EncodePriority
 554              	.LVL39:
 555              		.loc 1 174 3 view .LVU145
 556 0010 0146     		mov	r1, r0
 557              		.loc 1 174 3 discriminator 1 view .LVU146
 558 0012 2046     		mov	r0, r4
 559 0014 FFF7FEFF 		bl	__NVIC_SetPriority
 560              	.LVL40:
 175:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** }
 561              		.loc 1 175 1 view .LVU147
 562 0018 10BD     		pop	{r4, pc}
 563              	.LVL41:
 564              	.L37:
 565              		.loc 1 175 1 view .LVU148
 566 001a 00BF     		.align	2
 567              	.L36:
 568 001c 00ED00E0 		.word	-*********
 569              		.cfi_endproc
 570              	.LFE145:
 572              		.section	.text.HAL_NVIC_EnableIRQ,"ax",%progbits
 573              		.align	1
 574              		.global	HAL_NVIC_EnableIRQ
 575              		.syntax unified
 576              		.thumb
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 69


 577              		.thumb_func
 579              	HAL_NVIC_EnableIRQ:
 580              	.LVL42:
 581              	.LFB146:
 176:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** 
 177:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** /**
 178:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * @brief  Enables a device specific interrupt in the NVIC interrupt controller.
 179:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * @note   To configure interrupts priority correctly, the NVIC_PriorityGroupConfig()
 180:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   *         function should be called before.
 181:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * @param  IRQn External interrupt number.
 182:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   *         This parameter can be an enumerator of IRQn_Type enumeration
 183:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   *         (For the complete STM32 Devices IRQ Channels list, please refer to the appropriate CMSI
 184:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * @retval None
 185:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   */
 186:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** void HAL_NVIC_EnableIRQ(IRQn_Type IRQn)
 187:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** {
 582              		.loc 1 187 1 is_stmt 1 view -0
 583              		.cfi_startproc
 584              		@ args = 0, pretend = 0, frame = 0
 585              		@ frame_needed = 0, uses_anonymous_args = 0
 586              		.loc 1 187 1 is_stmt 0 view .LVU150
 587 0000 08B5     		push	{r3, lr}
 588              	.LCFI3:
 589              		.cfi_def_cfa_offset 8
 590              		.cfi_offset 3, -8
 591              		.cfi_offset 14, -4
 188:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   /* Check the parameters */
 189:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   assert_param(IS_NVIC_DEVICE_IRQ(IRQn));
 592              		.loc 1 189 3 is_stmt 1 view .LVU151
 190:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** 
 191:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   /* Enable interrupt */
 192:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   NVIC_EnableIRQ(IRQn);
 593              		.loc 1 192 3 view .LVU152
 594 0002 FFF7FEFF 		bl	__NVIC_EnableIRQ
 595              	.LVL43:
 193:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** }
 596              		.loc 1 193 1 is_stmt 0 view .LVU153
 597 0006 08BD     		pop	{r3, pc}
 598              		.cfi_endproc
 599              	.LFE146:
 601              		.section	.text.HAL_NVIC_DisableIRQ,"ax",%progbits
 602              		.align	1
 603              		.global	HAL_NVIC_DisableIRQ
 604              		.syntax unified
 605              		.thumb
 606              		.thumb_func
 608              	HAL_NVIC_DisableIRQ:
 609              	.LVL44:
 610              	.LFB147:
 194:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** 
 195:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** /**
 196:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * @brief  Disables a device specific interrupt in the NVIC interrupt controller.
 197:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * @param  IRQn External interrupt number.
 198:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   *         This parameter can be an enumerator of IRQn_Type enumeration
 199:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   *         (For the complete STM32 Devices IRQ Channels list, please refer to the appropriate CMSI
 200:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * @retval None
 201:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   */
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 70


 202:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** void HAL_NVIC_DisableIRQ(IRQn_Type IRQn)
 203:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** {
 611              		.loc 1 203 1 is_stmt 1 view -0
 612              		.cfi_startproc
 613              		@ args = 0, pretend = 0, frame = 0
 614              		@ frame_needed = 0, uses_anonymous_args = 0
 615              		.loc 1 203 1 is_stmt 0 view .LVU155
 616 0000 08B5     		push	{r3, lr}
 617              	.LCFI4:
 618              		.cfi_def_cfa_offset 8
 619              		.cfi_offset 3, -8
 620              		.cfi_offset 14, -4
 204:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   /* Check the parameters */
 205:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   assert_param(IS_NVIC_DEVICE_IRQ(IRQn));
 621              		.loc 1 205 3 is_stmt 1 view .LVU156
 206:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** 
 207:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   /* Disable interrupt */
 208:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   NVIC_DisableIRQ(IRQn);
 622              		.loc 1 208 3 view .LVU157
 623 0002 FFF7FEFF 		bl	__NVIC_DisableIRQ
 624              	.LVL45:
 209:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** }
 625              		.loc 1 209 1 is_stmt 0 view .LVU158
 626 0006 08BD     		pop	{r3, pc}
 627              		.cfi_endproc
 628              	.LFE147:
 630              		.section	.text.HAL_NVIC_SystemReset,"ax",%progbits
 631              		.align	1
 632              		.global	HAL_NVIC_SystemReset
 633              		.syntax unified
 634              		.thumb
 635              		.thumb_func
 637              	HAL_NVIC_SystemReset:
 638              	.LFB148:
 210:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** 
 211:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** /**
 212:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * @brief  Initiates a system reset request to reset the MCU.
 213:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * @retval None
 214:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   */
 215:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** void HAL_NVIC_SystemReset(void)
 216:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** {
 639              		.loc 1 216 1 is_stmt 1 view -0
 640              		.cfi_startproc
 641              		@ Volatile: function does not return.
 642              		@ args = 0, pretend = 0, frame = 0
 643              		@ frame_needed = 0, uses_anonymous_args = 0
 644 0000 08B5     		push	{r3, lr}
 645              	.LCFI5:
 646              		.cfi_def_cfa_offset 8
 647              		.cfi_offset 3, -8
 648              		.cfi_offset 14, -4
 217:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   /* System Reset */
 218:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   NVIC_SystemReset();
 649              		.loc 1 218 3 view .LVU160
 650 0002 FFF7FEFF 		bl	__NVIC_SystemReset
 651              	.LVL46:
 652              		.cfi_endproc
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 71


 653              	.LFE148:
 655              		.section	.text.HAL_SYSTICK_Config,"ax",%progbits
 656              		.align	1
 657              		.global	HAL_SYSTICK_Config
 658              		.syntax unified
 659              		.thumb
 660              		.thumb_func
 662              	HAL_SYSTICK_Config:
 663              	.LVL47:
 664              	.LFB149:
 219:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** }
 220:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** 
 221:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** /**
 222:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * @brief  Initializes the System Timer and its interrupt, and starts the System Tick Timer.
 223:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   *         Counter is in free running mode to generate periodic interrupts.
 224:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * @param  TicksNumb Specifies the ticks Number of ticks between two interrupts.
 225:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * @retval status   - 0  Function succeeded.
 226:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   *                  - 1  Function failed.
 227:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   */
 228:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** uint32_t HAL_SYSTICK_Config(uint32_t TicksNumb)
 229:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** {
 665              		.loc 1 229 1 view -0
 666              		.cfi_startproc
 667              		@ args = 0, pretend = 0, frame = 0
 668              		@ frame_needed = 0, uses_anonymous_args = 0
 669              		@ link register save eliminated.
 230:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****    return SysTick_Config(TicksNumb);
 670              		.loc 1 230 4 view .LVU162
 671              	.LBB48:
 672              	.LBI48:
2168:Drivers/CMSIS/Include/core_cm7.h ****   }
2169:Drivers/CMSIS/Include/core_cm7.h **** }
2170:Drivers/CMSIS/Include/core_cm7.h **** 
2171:Drivers/CMSIS/Include/core_cm7.h **** /*@} end of CMSIS_Core_NVICFunctions */
2172:Drivers/CMSIS/Include/core_cm7.h **** 
2173:Drivers/CMSIS/Include/core_cm7.h **** 
2174:Drivers/CMSIS/Include/core_cm7.h **** /* ##########################  MPU functions  #################################### */
2175:Drivers/CMSIS/Include/core_cm7.h **** 
2176:Drivers/CMSIS/Include/core_cm7.h **** #if defined (__MPU_PRESENT) && (__MPU_PRESENT == 1U)
2177:Drivers/CMSIS/Include/core_cm7.h **** 
2178:Drivers/CMSIS/Include/core_cm7.h **** #include "mpu_armv7.h"
2179:Drivers/CMSIS/Include/core_cm7.h **** 
2180:Drivers/CMSIS/Include/core_cm7.h **** #endif
2181:Drivers/CMSIS/Include/core_cm7.h **** 
2182:Drivers/CMSIS/Include/core_cm7.h **** 
2183:Drivers/CMSIS/Include/core_cm7.h **** /* ##########################  FPU functions  #################################### */
2184:Drivers/CMSIS/Include/core_cm7.h **** /**
2185:Drivers/CMSIS/Include/core_cm7.h ****   \ingroup  CMSIS_Core_FunctionInterface
2186:Drivers/CMSIS/Include/core_cm7.h ****   \defgroup CMSIS_Core_FpuFunctions FPU Functions
2187:Drivers/CMSIS/Include/core_cm7.h ****   \brief    Function that provides FPU type.
2188:Drivers/CMSIS/Include/core_cm7.h ****   @{
2189:Drivers/CMSIS/Include/core_cm7.h ****  */
2190:Drivers/CMSIS/Include/core_cm7.h **** 
2191:Drivers/CMSIS/Include/core_cm7.h **** /**
2192:Drivers/CMSIS/Include/core_cm7.h ****   \brief   get FPU type
2193:Drivers/CMSIS/Include/core_cm7.h ****   \details returns the FPU type
2194:Drivers/CMSIS/Include/core_cm7.h ****   \returns
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 72


2195:Drivers/CMSIS/Include/core_cm7.h ****    - \b  0: No FPU
2196:Drivers/CMSIS/Include/core_cm7.h ****    - \b  1: Single precision FPU
2197:Drivers/CMSIS/Include/core_cm7.h ****    - \b  2: Double + Single precision FPU
2198:Drivers/CMSIS/Include/core_cm7.h ****  */
2199:Drivers/CMSIS/Include/core_cm7.h **** __STATIC_INLINE uint32_t SCB_GetFPUType(void)
2200:Drivers/CMSIS/Include/core_cm7.h **** {
2201:Drivers/CMSIS/Include/core_cm7.h ****   uint32_t mvfr0;
2202:Drivers/CMSIS/Include/core_cm7.h **** 
2203:Drivers/CMSIS/Include/core_cm7.h ****   mvfr0 = SCB->MVFR0;
2204:Drivers/CMSIS/Include/core_cm7.h ****   if      ((mvfr0 & (FPU_MVFR0_Single_precision_Msk | FPU_MVFR0_Double_precision_Msk)) == 0x220U)
2205:Drivers/CMSIS/Include/core_cm7.h ****   {
2206:Drivers/CMSIS/Include/core_cm7.h ****     return 2U;           /* Double + Single precision FPU */
2207:Drivers/CMSIS/Include/core_cm7.h ****   }
2208:Drivers/CMSIS/Include/core_cm7.h ****   else if ((mvfr0 & (FPU_MVFR0_Single_precision_Msk | FPU_MVFR0_Double_precision_Msk)) == 0x020U)
2209:Drivers/CMSIS/Include/core_cm7.h ****   {
2210:Drivers/CMSIS/Include/core_cm7.h ****     return 1U;           /* Single precision FPU */
2211:Drivers/CMSIS/Include/core_cm7.h ****   }
2212:Drivers/CMSIS/Include/core_cm7.h ****   else
2213:Drivers/CMSIS/Include/core_cm7.h ****   {
2214:Drivers/CMSIS/Include/core_cm7.h ****     return 0U;           /* No FPU */
2215:Drivers/CMSIS/Include/core_cm7.h ****   }
2216:Drivers/CMSIS/Include/core_cm7.h **** }
2217:Drivers/CMSIS/Include/core_cm7.h **** 
2218:Drivers/CMSIS/Include/core_cm7.h **** /*@} end of CMSIS_Core_FpuFunctions */
2219:Drivers/CMSIS/Include/core_cm7.h **** 
2220:Drivers/CMSIS/Include/core_cm7.h **** 
2221:Drivers/CMSIS/Include/core_cm7.h **** 
2222:Drivers/CMSIS/Include/core_cm7.h **** /* ##########################  Cache functions  #################################### */
2223:Drivers/CMSIS/Include/core_cm7.h **** /**
2224:Drivers/CMSIS/Include/core_cm7.h ****   \ingroup  CMSIS_Core_FunctionInterface
2225:Drivers/CMSIS/Include/core_cm7.h ****   \defgroup CMSIS_Core_CacheFunctions Cache Functions
2226:Drivers/CMSIS/Include/core_cm7.h ****   \brief    Functions that configure Instruction and Data cache.
2227:Drivers/CMSIS/Include/core_cm7.h ****   @{
2228:Drivers/CMSIS/Include/core_cm7.h ****  */
2229:Drivers/CMSIS/Include/core_cm7.h **** 
2230:Drivers/CMSIS/Include/core_cm7.h **** /* Cache Size ID Register Macros */
2231:Drivers/CMSIS/Include/core_cm7.h **** #define CCSIDR_WAYS(x)         (((x) & SCB_CCSIDR_ASSOCIATIVITY_Msk) >> SCB_CCSIDR_ASSOCIATIVITY_Po
2232:Drivers/CMSIS/Include/core_cm7.h **** #define CCSIDR_SETS(x)         (((x) & SCB_CCSIDR_NUMSETS_Msk      ) >> SCB_CCSIDR_NUMSETS_Pos     
2233:Drivers/CMSIS/Include/core_cm7.h **** 
2234:Drivers/CMSIS/Include/core_cm7.h **** #define __SCB_DCACHE_LINE_SIZE  32U /*!< Cortex-M7 cache line size is fixed to 32 bytes (8 words). 
2235:Drivers/CMSIS/Include/core_cm7.h **** #define __SCB_ICACHE_LINE_SIZE  32U /*!< Cortex-M7 cache line size is fixed to 32 bytes (8 words). 
2236:Drivers/CMSIS/Include/core_cm7.h **** 
2237:Drivers/CMSIS/Include/core_cm7.h **** /**
2238:Drivers/CMSIS/Include/core_cm7.h ****   \brief   Enable I-Cache
2239:Drivers/CMSIS/Include/core_cm7.h ****   \details Turns on I-Cache
2240:Drivers/CMSIS/Include/core_cm7.h ****   */
2241:Drivers/CMSIS/Include/core_cm7.h **** __STATIC_FORCEINLINE void SCB_EnableICache (void)
2242:Drivers/CMSIS/Include/core_cm7.h **** {
2243:Drivers/CMSIS/Include/core_cm7.h ****   #if defined (__ICACHE_PRESENT) && (__ICACHE_PRESENT == 1U)
2244:Drivers/CMSIS/Include/core_cm7.h ****     if (SCB->CCR & SCB_CCR_IC_Msk) return;  /* return if ICache is already enabled */
2245:Drivers/CMSIS/Include/core_cm7.h **** 
2246:Drivers/CMSIS/Include/core_cm7.h ****     __DSB();
2247:Drivers/CMSIS/Include/core_cm7.h ****     __ISB();
2248:Drivers/CMSIS/Include/core_cm7.h ****     SCB->ICIALLU = 0UL;                     /* invalidate I-Cache */
2249:Drivers/CMSIS/Include/core_cm7.h ****     __DSB();
2250:Drivers/CMSIS/Include/core_cm7.h ****     __ISB();
2251:Drivers/CMSIS/Include/core_cm7.h ****     SCB->CCR |=  (uint32_t)SCB_CCR_IC_Msk;  /* enable I-Cache */
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 73


2252:Drivers/CMSIS/Include/core_cm7.h ****     __DSB();
2253:Drivers/CMSIS/Include/core_cm7.h ****     __ISB();
2254:Drivers/CMSIS/Include/core_cm7.h ****   #endif
2255:Drivers/CMSIS/Include/core_cm7.h **** }
2256:Drivers/CMSIS/Include/core_cm7.h **** 
2257:Drivers/CMSIS/Include/core_cm7.h **** 
2258:Drivers/CMSIS/Include/core_cm7.h **** /**
2259:Drivers/CMSIS/Include/core_cm7.h ****   \brief   Disable I-Cache
2260:Drivers/CMSIS/Include/core_cm7.h ****   \details Turns off I-Cache
2261:Drivers/CMSIS/Include/core_cm7.h ****   */
2262:Drivers/CMSIS/Include/core_cm7.h **** __STATIC_FORCEINLINE void SCB_DisableICache (void)
2263:Drivers/CMSIS/Include/core_cm7.h **** {
2264:Drivers/CMSIS/Include/core_cm7.h ****   #if defined (__ICACHE_PRESENT) && (__ICACHE_PRESENT == 1U)
2265:Drivers/CMSIS/Include/core_cm7.h ****     __DSB();
2266:Drivers/CMSIS/Include/core_cm7.h ****     __ISB();
2267:Drivers/CMSIS/Include/core_cm7.h ****     SCB->CCR &= ~(uint32_t)SCB_CCR_IC_Msk;  /* disable I-Cache */
2268:Drivers/CMSIS/Include/core_cm7.h ****     SCB->ICIALLU = 0UL;                     /* invalidate I-Cache */
2269:Drivers/CMSIS/Include/core_cm7.h ****     __DSB();
2270:Drivers/CMSIS/Include/core_cm7.h ****     __ISB();
2271:Drivers/CMSIS/Include/core_cm7.h ****   #endif
2272:Drivers/CMSIS/Include/core_cm7.h **** }
2273:Drivers/CMSIS/Include/core_cm7.h **** 
2274:Drivers/CMSIS/Include/core_cm7.h **** 
2275:Drivers/CMSIS/Include/core_cm7.h **** /**
2276:Drivers/CMSIS/Include/core_cm7.h ****   \brief   Invalidate I-Cache
2277:Drivers/CMSIS/Include/core_cm7.h ****   \details Invalidates I-Cache
2278:Drivers/CMSIS/Include/core_cm7.h ****   */
2279:Drivers/CMSIS/Include/core_cm7.h **** __STATIC_FORCEINLINE void SCB_InvalidateICache (void)
2280:Drivers/CMSIS/Include/core_cm7.h **** {
2281:Drivers/CMSIS/Include/core_cm7.h ****   #if defined (__ICACHE_PRESENT) && (__ICACHE_PRESENT == 1U)
2282:Drivers/CMSIS/Include/core_cm7.h ****     __DSB();
2283:Drivers/CMSIS/Include/core_cm7.h ****     __ISB();
2284:Drivers/CMSIS/Include/core_cm7.h ****     SCB->ICIALLU = 0UL;
2285:Drivers/CMSIS/Include/core_cm7.h ****     __DSB();
2286:Drivers/CMSIS/Include/core_cm7.h ****     __ISB();
2287:Drivers/CMSIS/Include/core_cm7.h ****   #endif
2288:Drivers/CMSIS/Include/core_cm7.h **** }
2289:Drivers/CMSIS/Include/core_cm7.h **** 
2290:Drivers/CMSIS/Include/core_cm7.h **** 
2291:Drivers/CMSIS/Include/core_cm7.h **** /**
2292:Drivers/CMSIS/Include/core_cm7.h ****   \brief   I-Cache Invalidate by address
2293:Drivers/CMSIS/Include/core_cm7.h ****   \details Invalidates I-Cache for the given address.
2294:Drivers/CMSIS/Include/core_cm7.h ****            I-Cache is invalidated starting from a 32 byte aligned address in 32 byte granularity.
2295:Drivers/CMSIS/Include/core_cm7.h ****            I-Cache memory blocks which are part of given address + given size are invalidated.
2296:Drivers/CMSIS/Include/core_cm7.h ****   \param[in]   addr    address
2297:Drivers/CMSIS/Include/core_cm7.h ****   \param[in]   isize   size of memory block (in number of bytes)
2298:Drivers/CMSIS/Include/core_cm7.h **** */
2299:Drivers/CMSIS/Include/core_cm7.h **** __STATIC_FORCEINLINE void SCB_InvalidateICache_by_Addr (void *addr, int32_t isize)
2300:Drivers/CMSIS/Include/core_cm7.h **** {
2301:Drivers/CMSIS/Include/core_cm7.h ****   #if defined (__ICACHE_PRESENT) && (__ICACHE_PRESENT == 1U)
2302:Drivers/CMSIS/Include/core_cm7.h ****     if ( isize > 0 ) {
2303:Drivers/CMSIS/Include/core_cm7.h ****        int32_t op_size = isize + (((uint32_t)addr) & (__SCB_ICACHE_LINE_SIZE - 1U));
2304:Drivers/CMSIS/Include/core_cm7.h ****       uint32_t op_addr = (uint32_t)addr /* & ~(__SCB_ICACHE_LINE_SIZE - 1U) */;
2305:Drivers/CMSIS/Include/core_cm7.h **** 
2306:Drivers/CMSIS/Include/core_cm7.h ****       __DSB();
2307:Drivers/CMSIS/Include/core_cm7.h **** 
2308:Drivers/CMSIS/Include/core_cm7.h ****       do {
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 74


2309:Drivers/CMSIS/Include/core_cm7.h ****         SCB->ICIMVAU = op_addr;             /* register accepts only 32byte aligned values, only bi
2310:Drivers/CMSIS/Include/core_cm7.h ****         op_addr += __SCB_ICACHE_LINE_SIZE;
2311:Drivers/CMSIS/Include/core_cm7.h ****         op_size -= __SCB_ICACHE_LINE_SIZE;
2312:Drivers/CMSIS/Include/core_cm7.h ****       } while ( op_size > 0 );
2313:Drivers/CMSIS/Include/core_cm7.h **** 
2314:Drivers/CMSIS/Include/core_cm7.h ****       __DSB();
2315:Drivers/CMSIS/Include/core_cm7.h ****       __ISB();
2316:Drivers/CMSIS/Include/core_cm7.h ****     }
2317:Drivers/CMSIS/Include/core_cm7.h ****   #endif
2318:Drivers/CMSIS/Include/core_cm7.h **** }
2319:Drivers/CMSIS/Include/core_cm7.h **** 
2320:Drivers/CMSIS/Include/core_cm7.h **** 
2321:Drivers/CMSIS/Include/core_cm7.h **** /**
2322:Drivers/CMSIS/Include/core_cm7.h ****   \brief   Enable D-Cache
2323:Drivers/CMSIS/Include/core_cm7.h ****   \details Turns on D-Cache
2324:Drivers/CMSIS/Include/core_cm7.h ****   */
2325:Drivers/CMSIS/Include/core_cm7.h **** __STATIC_FORCEINLINE void SCB_EnableDCache (void)
2326:Drivers/CMSIS/Include/core_cm7.h **** {
2327:Drivers/CMSIS/Include/core_cm7.h ****   #if defined (__DCACHE_PRESENT) && (__DCACHE_PRESENT == 1U)
2328:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t ccsidr;
2329:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t sets;
2330:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t ways;
2331:Drivers/CMSIS/Include/core_cm7.h **** 
2332:Drivers/CMSIS/Include/core_cm7.h ****     if (SCB->CCR & SCB_CCR_DC_Msk) return;  /* return if DCache is already enabled */
2333:Drivers/CMSIS/Include/core_cm7.h **** 
2334:Drivers/CMSIS/Include/core_cm7.h ****     SCB->CSSELR = 0U;                       /* select Level 1 data cache */
2335:Drivers/CMSIS/Include/core_cm7.h ****     __DSB();
2336:Drivers/CMSIS/Include/core_cm7.h **** 
2337:Drivers/CMSIS/Include/core_cm7.h ****     ccsidr = SCB->CCSIDR;
2338:Drivers/CMSIS/Include/core_cm7.h **** 
2339:Drivers/CMSIS/Include/core_cm7.h ****                                             /* invalidate D-Cache */
2340:Drivers/CMSIS/Include/core_cm7.h ****     sets = (uint32_t)(CCSIDR_SETS(ccsidr));
2341:Drivers/CMSIS/Include/core_cm7.h ****     do {
2342:Drivers/CMSIS/Include/core_cm7.h ****       ways = (uint32_t)(CCSIDR_WAYS(ccsidr));
2343:Drivers/CMSIS/Include/core_cm7.h ****       do {
2344:Drivers/CMSIS/Include/core_cm7.h ****         SCB->DCISW = (((sets << SCB_DCISW_SET_Pos) & SCB_DCISW_SET_Msk) |
2345:Drivers/CMSIS/Include/core_cm7.h ****                       ((ways << SCB_DCISW_WAY_Pos) & SCB_DCISW_WAY_Msk)  );
2346:Drivers/CMSIS/Include/core_cm7.h ****         #if defined ( __CC_ARM )
2347:Drivers/CMSIS/Include/core_cm7.h ****           __schedule_barrier();
2348:Drivers/CMSIS/Include/core_cm7.h ****         #endif
2349:Drivers/CMSIS/Include/core_cm7.h ****       } while (ways-- != 0U);
2350:Drivers/CMSIS/Include/core_cm7.h ****     } while(sets-- != 0U);
2351:Drivers/CMSIS/Include/core_cm7.h ****     __DSB();
2352:Drivers/CMSIS/Include/core_cm7.h **** 
2353:Drivers/CMSIS/Include/core_cm7.h ****     SCB->CCR |=  (uint32_t)SCB_CCR_DC_Msk;  /* enable D-Cache */
2354:Drivers/CMSIS/Include/core_cm7.h **** 
2355:Drivers/CMSIS/Include/core_cm7.h ****     __DSB();
2356:Drivers/CMSIS/Include/core_cm7.h ****     __ISB();
2357:Drivers/CMSIS/Include/core_cm7.h ****   #endif
2358:Drivers/CMSIS/Include/core_cm7.h **** }
2359:Drivers/CMSIS/Include/core_cm7.h **** 
2360:Drivers/CMSIS/Include/core_cm7.h **** 
2361:Drivers/CMSIS/Include/core_cm7.h **** /**
2362:Drivers/CMSIS/Include/core_cm7.h ****   \brief   Disable D-Cache
2363:Drivers/CMSIS/Include/core_cm7.h ****   \details Turns off D-Cache
2364:Drivers/CMSIS/Include/core_cm7.h ****   */
2365:Drivers/CMSIS/Include/core_cm7.h **** __STATIC_FORCEINLINE void SCB_DisableDCache (void)
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 75


2366:Drivers/CMSIS/Include/core_cm7.h **** {
2367:Drivers/CMSIS/Include/core_cm7.h ****   #if defined (__DCACHE_PRESENT) && (__DCACHE_PRESENT == 1U)
2368:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t ccsidr;
2369:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t sets;
2370:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t ways;
2371:Drivers/CMSIS/Include/core_cm7.h **** 
2372:Drivers/CMSIS/Include/core_cm7.h ****     SCB->CSSELR = 0U;                       /* select Level 1 data cache */
2373:Drivers/CMSIS/Include/core_cm7.h ****     __DSB();
2374:Drivers/CMSIS/Include/core_cm7.h **** 
2375:Drivers/CMSIS/Include/core_cm7.h ****     SCB->CCR &= ~(uint32_t)SCB_CCR_DC_Msk;  /* disable D-Cache */
2376:Drivers/CMSIS/Include/core_cm7.h ****     __DSB();
2377:Drivers/CMSIS/Include/core_cm7.h **** 
2378:Drivers/CMSIS/Include/core_cm7.h ****     ccsidr = SCB->CCSIDR;
2379:Drivers/CMSIS/Include/core_cm7.h **** 
2380:Drivers/CMSIS/Include/core_cm7.h ****                                             /* clean & invalidate D-Cache */
2381:Drivers/CMSIS/Include/core_cm7.h ****     sets = (uint32_t)(CCSIDR_SETS(ccsidr));
2382:Drivers/CMSIS/Include/core_cm7.h ****     do {
2383:Drivers/CMSIS/Include/core_cm7.h ****       ways = (uint32_t)(CCSIDR_WAYS(ccsidr));
2384:Drivers/CMSIS/Include/core_cm7.h ****       do {
2385:Drivers/CMSIS/Include/core_cm7.h ****         SCB->DCCISW = (((sets << SCB_DCCISW_SET_Pos) & SCB_DCCISW_SET_Msk) |
2386:Drivers/CMSIS/Include/core_cm7.h ****                        ((ways << SCB_DCCISW_WAY_Pos) & SCB_DCCISW_WAY_Msk)  );
2387:Drivers/CMSIS/Include/core_cm7.h ****         #if defined ( __CC_ARM )
2388:Drivers/CMSIS/Include/core_cm7.h ****           __schedule_barrier();
2389:Drivers/CMSIS/Include/core_cm7.h ****         #endif
2390:Drivers/CMSIS/Include/core_cm7.h ****       } while (ways-- != 0U);
2391:Drivers/CMSIS/Include/core_cm7.h ****     } while(sets-- != 0U);
2392:Drivers/CMSIS/Include/core_cm7.h **** 
2393:Drivers/CMSIS/Include/core_cm7.h ****     __DSB();
2394:Drivers/CMSIS/Include/core_cm7.h ****     __ISB();
2395:Drivers/CMSIS/Include/core_cm7.h ****   #endif
2396:Drivers/CMSIS/Include/core_cm7.h **** }
2397:Drivers/CMSIS/Include/core_cm7.h **** 
2398:Drivers/CMSIS/Include/core_cm7.h **** 
2399:Drivers/CMSIS/Include/core_cm7.h **** /**
2400:Drivers/CMSIS/Include/core_cm7.h ****   \brief   Invalidate D-Cache
2401:Drivers/CMSIS/Include/core_cm7.h ****   \details Invalidates D-Cache
2402:Drivers/CMSIS/Include/core_cm7.h ****   */
2403:Drivers/CMSIS/Include/core_cm7.h **** __STATIC_FORCEINLINE void SCB_InvalidateDCache (void)
2404:Drivers/CMSIS/Include/core_cm7.h **** {
2405:Drivers/CMSIS/Include/core_cm7.h ****   #if defined (__DCACHE_PRESENT) && (__DCACHE_PRESENT == 1U)
2406:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t ccsidr;
2407:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t sets;
2408:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t ways;
2409:Drivers/CMSIS/Include/core_cm7.h **** 
2410:Drivers/CMSIS/Include/core_cm7.h ****     SCB->CSSELR = 0U;                       /* select Level 1 data cache */
2411:Drivers/CMSIS/Include/core_cm7.h ****     __DSB();
2412:Drivers/CMSIS/Include/core_cm7.h **** 
2413:Drivers/CMSIS/Include/core_cm7.h ****     ccsidr = SCB->CCSIDR;
2414:Drivers/CMSIS/Include/core_cm7.h **** 
2415:Drivers/CMSIS/Include/core_cm7.h ****                                             /* invalidate D-Cache */
2416:Drivers/CMSIS/Include/core_cm7.h ****     sets = (uint32_t)(CCSIDR_SETS(ccsidr));
2417:Drivers/CMSIS/Include/core_cm7.h ****     do {
2418:Drivers/CMSIS/Include/core_cm7.h ****       ways = (uint32_t)(CCSIDR_WAYS(ccsidr));
2419:Drivers/CMSIS/Include/core_cm7.h ****       do {
2420:Drivers/CMSIS/Include/core_cm7.h ****         SCB->DCISW = (((sets << SCB_DCISW_SET_Pos) & SCB_DCISW_SET_Msk) |
2421:Drivers/CMSIS/Include/core_cm7.h ****                       ((ways << SCB_DCISW_WAY_Pos) & SCB_DCISW_WAY_Msk)  );
2422:Drivers/CMSIS/Include/core_cm7.h ****         #if defined ( __CC_ARM )
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 76


2423:Drivers/CMSIS/Include/core_cm7.h ****           __schedule_barrier();
2424:Drivers/CMSIS/Include/core_cm7.h ****         #endif
2425:Drivers/CMSIS/Include/core_cm7.h ****       } while (ways-- != 0U);
2426:Drivers/CMSIS/Include/core_cm7.h ****     } while(sets-- != 0U);
2427:Drivers/CMSIS/Include/core_cm7.h **** 
2428:Drivers/CMSIS/Include/core_cm7.h ****     __DSB();
2429:Drivers/CMSIS/Include/core_cm7.h ****     __ISB();
2430:Drivers/CMSIS/Include/core_cm7.h ****   #endif
2431:Drivers/CMSIS/Include/core_cm7.h **** }
2432:Drivers/CMSIS/Include/core_cm7.h **** 
2433:Drivers/CMSIS/Include/core_cm7.h **** 
2434:Drivers/CMSIS/Include/core_cm7.h **** /**
2435:Drivers/CMSIS/Include/core_cm7.h ****   \brief   Clean D-Cache
2436:Drivers/CMSIS/Include/core_cm7.h ****   \details Cleans D-Cache
2437:Drivers/CMSIS/Include/core_cm7.h ****   */
2438:Drivers/CMSIS/Include/core_cm7.h **** __STATIC_FORCEINLINE void SCB_CleanDCache (void)
2439:Drivers/CMSIS/Include/core_cm7.h **** {
2440:Drivers/CMSIS/Include/core_cm7.h ****   #if defined (__DCACHE_PRESENT) && (__DCACHE_PRESENT == 1U)
2441:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t ccsidr;
2442:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t sets;
2443:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t ways;
2444:Drivers/CMSIS/Include/core_cm7.h **** 
2445:Drivers/CMSIS/Include/core_cm7.h ****     SCB->CSSELR = 0U;                       /* select Level 1 data cache */
2446:Drivers/CMSIS/Include/core_cm7.h ****     __DSB();
2447:Drivers/CMSIS/Include/core_cm7.h **** 
2448:Drivers/CMSIS/Include/core_cm7.h ****     ccsidr = SCB->CCSIDR;
2449:Drivers/CMSIS/Include/core_cm7.h **** 
2450:Drivers/CMSIS/Include/core_cm7.h ****                                             /* clean D-Cache */
2451:Drivers/CMSIS/Include/core_cm7.h ****     sets = (uint32_t)(CCSIDR_SETS(ccsidr));
2452:Drivers/CMSIS/Include/core_cm7.h ****     do {
2453:Drivers/CMSIS/Include/core_cm7.h ****       ways = (uint32_t)(CCSIDR_WAYS(ccsidr));
2454:Drivers/CMSIS/Include/core_cm7.h ****       do {
2455:Drivers/CMSIS/Include/core_cm7.h ****         SCB->DCCSW = (((sets << SCB_DCCSW_SET_Pos) & SCB_DCCSW_SET_Msk) |
2456:Drivers/CMSIS/Include/core_cm7.h ****                       ((ways << SCB_DCCSW_WAY_Pos) & SCB_DCCSW_WAY_Msk)  );
2457:Drivers/CMSIS/Include/core_cm7.h ****         #if defined ( __CC_ARM )
2458:Drivers/CMSIS/Include/core_cm7.h ****           __schedule_barrier();
2459:Drivers/CMSIS/Include/core_cm7.h ****         #endif
2460:Drivers/CMSIS/Include/core_cm7.h ****       } while (ways-- != 0U);
2461:Drivers/CMSIS/Include/core_cm7.h ****     } while(sets-- != 0U);
2462:Drivers/CMSIS/Include/core_cm7.h **** 
2463:Drivers/CMSIS/Include/core_cm7.h ****     __DSB();
2464:Drivers/CMSIS/Include/core_cm7.h ****     __ISB();
2465:Drivers/CMSIS/Include/core_cm7.h ****   #endif
2466:Drivers/CMSIS/Include/core_cm7.h **** }
2467:Drivers/CMSIS/Include/core_cm7.h **** 
2468:Drivers/CMSIS/Include/core_cm7.h **** 
2469:Drivers/CMSIS/Include/core_cm7.h **** /**
2470:Drivers/CMSIS/Include/core_cm7.h ****   \brief   Clean & Invalidate D-Cache
2471:Drivers/CMSIS/Include/core_cm7.h ****   \details Cleans and Invalidates D-Cache
2472:Drivers/CMSIS/Include/core_cm7.h ****   */
2473:Drivers/CMSIS/Include/core_cm7.h **** __STATIC_FORCEINLINE void SCB_CleanInvalidateDCache (void)
2474:Drivers/CMSIS/Include/core_cm7.h **** {
2475:Drivers/CMSIS/Include/core_cm7.h ****   #if defined (__DCACHE_PRESENT) && (__DCACHE_PRESENT == 1U)
2476:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t ccsidr;
2477:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t sets;
2478:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t ways;
2479:Drivers/CMSIS/Include/core_cm7.h **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 77


2480:Drivers/CMSIS/Include/core_cm7.h ****     SCB->CSSELR = 0U;                       /* select Level 1 data cache */
2481:Drivers/CMSIS/Include/core_cm7.h ****     __DSB();
2482:Drivers/CMSIS/Include/core_cm7.h **** 
2483:Drivers/CMSIS/Include/core_cm7.h ****     ccsidr = SCB->CCSIDR;
2484:Drivers/CMSIS/Include/core_cm7.h **** 
2485:Drivers/CMSIS/Include/core_cm7.h ****                                             /* clean & invalidate D-Cache */
2486:Drivers/CMSIS/Include/core_cm7.h ****     sets = (uint32_t)(CCSIDR_SETS(ccsidr));
2487:Drivers/CMSIS/Include/core_cm7.h ****     do {
2488:Drivers/CMSIS/Include/core_cm7.h ****       ways = (uint32_t)(CCSIDR_WAYS(ccsidr));
2489:Drivers/CMSIS/Include/core_cm7.h ****       do {
2490:Drivers/CMSIS/Include/core_cm7.h ****         SCB->DCCISW = (((sets << SCB_DCCISW_SET_Pos) & SCB_DCCISW_SET_Msk) |
2491:Drivers/CMSIS/Include/core_cm7.h ****                        ((ways << SCB_DCCISW_WAY_Pos) & SCB_DCCISW_WAY_Msk)  );
2492:Drivers/CMSIS/Include/core_cm7.h ****         #if defined ( __CC_ARM )
2493:Drivers/CMSIS/Include/core_cm7.h ****           __schedule_barrier();
2494:Drivers/CMSIS/Include/core_cm7.h ****         #endif
2495:Drivers/CMSIS/Include/core_cm7.h ****       } while (ways-- != 0U);
2496:Drivers/CMSIS/Include/core_cm7.h ****     } while(sets-- != 0U);
2497:Drivers/CMSIS/Include/core_cm7.h **** 
2498:Drivers/CMSIS/Include/core_cm7.h ****     __DSB();
2499:Drivers/CMSIS/Include/core_cm7.h ****     __ISB();
2500:Drivers/CMSIS/Include/core_cm7.h ****   #endif
2501:Drivers/CMSIS/Include/core_cm7.h **** }
2502:Drivers/CMSIS/Include/core_cm7.h **** 
2503:Drivers/CMSIS/Include/core_cm7.h **** 
2504:Drivers/CMSIS/Include/core_cm7.h **** /**
2505:Drivers/CMSIS/Include/core_cm7.h ****   \brief   D-Cache Invalidate by address
2506:Drivers/CMSIS/Include/core_cm7.h ****   \details Invalidates D-Cache for the given address.
2507:Drivers/CMSIS/Include/core_cm7.h ****            D-Cache is invalidated starting from a 32 byte aligned address in 32 byte granularity.
2508:Drivers/CMSIS/Include/core_cm7.h ****            D-Cache memory blocks which are part of given address + given size are invalidated.
2509:Drivers/CMSIS/Include/core_cm7.h ****   \param[in]   addr    address
2510:Drivers/CMSIS/Include/core_cm7.h ****   \param[in]   dsize   size of memory block (in number of bytes)
2511:Drivers/CMSIS/Include/core_cm7.h **** */
2512:Drivers/CMSIS/Include/core_cm7.h **** __STATIC_FORCEINLINE void SCB_InvalidateDCache_by_Addr (void *addr, int32_t dsize)
2513:Drivers/CMSIS/Include/core_cm7.h **** {
2514:Drivers/CMSIS/Include/core_cm7.h ****   #if defined (__DCACHE_PRESENT) && (__DCACHE_PRESENT == 1U)
2515:Drivers/CMSIS/Include/core_cm7.h ****     if ( dsize > 0 ) { 
2516:Drivers/CMSIS/Include/core_cm7.h ****        int32_t op_size = dsize + (((uint32_t)addr) & (__SCB_DCACHE_LINE_SIZE - 1U));
2517:Drivers/CMSIS/Include/core_cm7.h ****       uint32_t op_addr = (uint32_t)addr /* & ~(__SCB_DCACHE_LINE_SIZE - 1U) */;
2518:Drivers/CMSIS/Include/core_cm7.h ****     
2519:Drivers/CMSIS/Include/core_cm7.h ****       __DSB();
2520:Drivers/CMSIS/Include/core_cm7.h **** 
2521:Drivers/CMSIS/Include/core_cm7.h ****       do {
2522:Drivers/CMSIS/Include/core_cm7.h ****         SCB->DCIMVAC = op_addr;             /* register accepts only 32byte aligned values, only bi
2523:Drivers/CMSIS/Include/core_cm7.h ****         op_addr += __SCB_DCACHE_LINE_SIZE;
2524:Drivers/CMSIS/Include/core_cm7.h ****         op_size -= __SCB_DCACHE_LINE_SIZE;
2525:Drivers/CMSIS/Include/core_cm7.h ****       } while ( op_size > 0 );
2526:Drivers/CMSIS/Include/core_cm7.h **** 
2527:Drivers/CMSIS/Include/core_cm7.h ****       __DSB();
2528:Drivers/CMSIS/Include/core_cm7.h ****       __ISB();
2529:Drivers/CMSIS/Include/core_cm7.h ****     }
2530:Drivers/CMSIS/Include/core_cm7.h ****   #endif
2531:Drivers/CMSIS/Include/core_cm7.h **** }
2532:Drivers/CMSIS/Include/core_cm7.h **** 
2533:Drivers/CMSIS/Include/core_cm7.h **** 
2534:Drivers/CMSIS/Include/core_cm7.h **** /**
2535:Drivers/CMSIS/Include/core_cm7.h ****   \brief   D-Cache Clean by address
2536:Drivers/CMSIS/Include/core_cm7.h ****   \details Cleans D-Cache for the given address
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 78


2537:Drivers/CMSIS/Include/core_cm7.h ****            D-Cache is cleaned starting from a 32 byte aligned address in 32 byte granularity.
2538:Drivers/CMSIS/Include/core_cm7.h ****            D-Cache memory blocks which are part of given address + given size are cleaned.
2539:Drivers/CMSIS/Include/core_cm7.h ****   \param[in]   addr    address
2540:Drivers/CMSIS/Include/core_cm7.h ****   \param[in]   dsize   size of memory block (in number of bytes)
2541:Drivers/CMSIS/Include/core_cm7.h **** */
2542:Drivers/CMSIS/Include/core_cm7.h **** __STATIC_FORCEINLINE void SCB_CleanDCache_by_Addr (uint32_t *addr, int32_t dsize)
2543:Drivers/CMSIS/Include/core_cm7.h **** {
2544:Drivers/CMSIS/Include/core_cm7.h ****   #if defined (__DCACHE_PRESENT) && (__DCACHE_PRESENT == 1U)
2545:Drivers/CMSIS/Include/core_cm7.h ****     if ( dsize > 0 ) { 
2546:Drivers/CMSIS/Include/core_cm7.h ****        int32_t op_size = dsize + (((uint32_t)addr) & (__SCB_DCACHE_LINE_SIZE - 1U));
2547:Drivers/CMSIS/Include/core_cm7.h ****       uint32_t op_addr = (uint32_t)addr /* & ~(__SCB_DCACHE_LINE_SIZE - 1U) */;
2548:Drivers/CMSIS/Include/core_cm7.h ****     
2549:Drivers/CMSIS/Include/core_cm7.h ****       __DSB();
2550:Drivers/CMSIS/Include/core_cm7.h **** 
2551:Drivers/CMSIS/Include/core_cm7.h ****       do {
2552:Drivers/CMSIS/Include/core_cm7.h ****         SCB->DCCMVAC = op_addr;             /* register accepts only 32byte aligned values, only bi
2553:Drivers/CMSIS/Include/core_cm7.h ****         op_addr += __SCB_DCACHE_LINE_SIZE;
2554:Drivers/CMSIS/Include/core_cm7.h ****         op_size -= __SCB_DCACHE_LINE_SIZE;
2555:Drivers/CMSIS/Include/core_cm7.h ****       } while ( op_size > 0 );
2556:Drivers/CMSIS/Include/core_cm7.h **** 
2557:Drivers/CMSIS/Include/core_cm7.h ****       __DSB();
2558:Drivers/CMSIS/Include/core_cm7.h ****       __ISB();
2559:Drivers/CMSIS/Include/core_cm7.h ****     }
2560:Drivers/CMSIS/Include/core_cm7.h ****   #endif
2561:Drivers/CMSIS/Include/core_cm7.h **** }
2562:Drivers/CMSIS/Include/core_cm7.h **** 
2563:Drivers/CMSIS/Include/core_cm7.h **** 
2564:Drivers/CMSIS/Include/core_cm7.h **** /**
2565:Drivers/CMSIS/Include/core_cm7.h ****   \brief   D-Cache Clean and Invalidate by address
2566:Drivers/CMSIS/Include/core_cm7.h ****   \details Cleans and invalidates D_Cache for the given address
2567:Drivers/CMSIS/Include/core_cm7.h ****            D-Cache is cleaned and invalidated starting from a 32 byte aligned address in 32 byte gr
2568:Drivers/CMSIS/Include/core_cm7.h ****            D-Cache memory blocks which are part of given address + given size are cleaned and inval
2569:Drivers/CMSIS/Include/core_cm7.h ****   \param[in]   addr    address (aligned to 32-byte boundary)
2570:Drivers/CMSIS/Include/core_cm7.h ****   \param[in]   dsize   size of memory block (in number of bytes)
2571:Drivers/CMSIS/Include/core_cm7.h **** */
2572:Drivers/CMSIS/Include/core_cm7.h **** __STATIC_FORCEINLINE void SCB_CleanInvalidateDCache_by_Addr (uint32_t *addr, int32_t dsize)
2573:Drivers/CMSIS/Include/core_cm7.h **** {
2574:Drivers/CMSIS/Include/core_cm7.h ****   #if defined (__DCACHE_PRESENT) && (__DCACHE_PRESENT == 1U)
2575:Drivers/CMSIS/Include/core_cm7.h ****     if ( dsize > 0 ) { 
2576:Drivers/CMSIS/Include/core_cm7.h ****        int32_t op_size = dsize + (((uint32_t)addr) & (__SCB_DCACHE_LINE_SIZE - 1U));
2577:Drivers/CMSIS/Include/core_cm7.h ****       uint32_t op_addr = (uint32_t)addr /* & ~(__SCB_DCACHE_LINE_SIZE - 1U) */;
2578:Drivers/CMSIS/Include/core_cm7.h ****     
2579:Drivers/CMSIS/Include/core_cm7.h ****       __DSB();
2580:Drivers/CMSIS/Include/core_cm7.h **** 
2581:Drivers/CMSIS/Include/core_cm7.h ****       do {
2582:Drivers/CMSIS/Include/core_cm7.h ****         SCB->DCCIMVAC = op_addr;            /* register accepts only 32byte aligned values, only bi
2583:Drivers/CMSIS/Include/core_cm7.h ****         op_addr +=          __SCB_DCACHE_LINE_SIZE;
2584:Drivers/CMSIS/Include/core_cm7.h ****         op_size -=          __SCB_DCACHE_LINE_SIZE;
2585:Drivers/CMSIS/Include/core_cm7.h ****       } while ( op_size > 0 );
2586:Drivers/CMSIS/Include/core_cm7.h **** 
2587:Drivers/CMSIS/Include/core_cm7.h ****       __DSB();
2588:Drivers/CMSIS/Include/core_cm7.h ****       __ISB();
2589:Drivers/CMSIS/Include/core_cm7.h ****     }
2590:Drivers/CMSIS/Include/core_cm7.h ****   #endif
2591:Drivers/CMSIS/Include/core_cm7.h **** }
2592:Drivers/CMSIS/Include/core_cm7.h **** 
2593:Drivers/CMSIS/Include/core_cm7.h **** /*@} end of CMSIS_Core_CacheFunctions */
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 79


2594:Drivers/CMSIS/Include/core_cm7.h **** 
2595:Drivers/CMSIS/Include/core_cm7.h **** 
2596:Drivers/CMSIS/Include/core_cm7.h **** 
2597:Drivers/CMSIS/Include/core_cm7.h **** /* ##################################    SysTick function  ########################################
2598:Drivers/CMSIS/Include/core_cm7.h **** /**
2599:Drivers/CMSIS/Include/core_cm7.h ****   \ingroup  CMSIS_Core_FunctionInterface
2600:Drivers/CMSIS/Include/core_cm7.h ****   \defgroup CMSIS_Core_SysTickFunctions SysTick Functions
2601:Drivers/CMSIS/Include/core_cm7.h ****   \brief    Functions that configure the System.
2602:Drivers/CMSIS/Include/core_cm7.h ****   @{
2603:Drivers/CMSIS/Include/core_cm7.h ****  */
2604:Drivers/CMSIS/Include/core_cm7.h **** 
2605:Drivers/CMSIS/Include/core_cm7.h **** #if defined (__Vendor_SysTickConfig) && (__Vendor_SysTickConfig == 0U)
2606:Drivers/CMSIS/Include/core_cm7.h **** 
2607:Drivers/CMSIS/Include/core_cm7.h **** /**
2608:Drivers/CMSIS/Include/core_cm7.h ****   \brief   System Tick Configuration
2609:Drivers/CMSIS/Include/core_cm7.h ****   \details Initializes the System Timer and its interrupt, and starts the System Tick Timer.
2610:Drivers/CMSIS/Include/core_cm7.h ****            Counter is in free running mode to generate periodic interrupts.
2611:Drivers/CMSIS/Include/core_cm7.h ****   \param [in]  ticks  Number of ticks between two interrupts.
2612:Drivers/CMSIS/Include/core_cm7.h ****   \return          0  Function succeeded.
2613:Drivers/CMSIS/Include/core_cm7.h ****   \return          1  Function failed.
2614:Drivers/CMSIS/Include/core_cm7.h ****   \note    When the variable <b>__Vendor_SysTickConfig</b> is set to 1, then the
2615:Drivers/CMSIS/Include/core_cm7.h ****            function <b>SysTick_Config</b> is not included. In this case, the file <b><i>device</i>.
2616:Drivers/CMSIS/Include/core_cm7.h ****            must contain a vendor-specific implementation of this function.
2617:Drivers/CMSIS/Include/core_cm7.h ****  */
2618:Drivers/CMSIS/Include/core_cm7.h **** __STATIC_INLINE uint32_t SysTick_Config(uint32_t ticks)
 673              		.loc 2 2618 26 view .LVU163
 674              	.LBB49:
2619:Drivers/CMSIS/Include/core_cm7.h **** {
2620:Drivers/CMSIS/Include/core_cm7.h ****   if ((ticks - 1UL) > SysTick_LOAD_RELOAD_Msk)
 675              		.loc 2 2620 3 view .LVU164
 676              		.loc 2 2620 14 is_stmt 0 view .LVU165
 677 0000 0138     		subs	r0, r0, #1
 678              	.LVL48:
 679              		.loc 2 2620 6 view .LVU166
 680 0002 B0F1807F 		cmp	r0, #16777216
 681 0006 0BD2     		bcs	.L46
2621:Drivers/CMSIS/Include/core_cm7.h ****   {
2622:Drivers/CMSIS/Include/core_cm7.h ****     return (1UL);                                                   /* Reload value impossible */
2623:Drivers/CMSIS/Include/core_cm7.h ****   }
2624:Drivers/CMSIS/Include/core_cm7.h **** 
2625:Drivers/CMSIS/Include/core_cm7.h ****   SysTick->LOAD  = (uint32_t)(ticks - 1UL);                         /* set reload register */
 682              		.loc 2 2625 3 is_stmt 1 view .LVU167
 683              		.loc 2 2625 18 is_stmt 0 view .LVU168
 684 0008 4FF0E023 		mov	r3, #-*********
 685 000c 5861     		str	r0, [r3, #20]
2626:Drivers/CMSIS/Include/core_cm7.h ****   NVIC_SetPriority (SysTick_IRQn, (1UL << __NVIC_PRIO_BITS) - 1UL); /* set Priority for Systick Int
 686              		.loc 2 2626 3 is_stmt 1 view .LVU169
 687              	.LVL49:
 688              	.LBB50:
 689              	.LBI50:
2032:Drivers/CMSIS/Include/core_cm7.h **** {
 690              		.loc 2 2032 22 view .LVU170
 691              	.LBB51:
2034:Drivers/CMSIS/Include/core_cm7.h ****   {
 692              		.loc 2 2034 3 view .LVU171
2040:Drivers/CMSIS/Include/core_cm7.h ****   }
 693              		.loc 2 2040 5 view .LVU172
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 80


2040:Drivers/CMSIS/Include/core_cm7.h ****   }
 694              		.loc 2 2040 47 is_stmt 0 view .LVU173
 695 000e 054A     		ldr	r2, .L47
 696 0010 F021     		movs	r1, #240
 697 0012 82F82310 		strb	r1, [r2, #35]
 698              	.LVL50:
2040:Drivers/CMSIS/Include/core_cm7.h ****   }
 699              		.loc 2 2040 47 view .LVU174
 700              	.LBE51:
 701              	.LBE50:
2627:Drivers/CMSIS/Include/core_cm7.h ****   SysTick->VAL   = 0UL;                                             /* Load the SysTick Counter Val
 702              		.loc 2 2627 3 is_stmt 1 view .LVU175
 703              		.loc 2 2627 18 is_stmt 0 view .LVU176
 704 0016 0020     		movs	r0, #0
 705              	.LVL51:
 706              		.loc 2 2627 18 view .LVU177
 707 0018 9861     		str	r0, [r3, #24]
2628:Drivers/CMSIS/Include/core_cm7.h ****   SysTick->CTRL  = SysTick_CTRL_CLKSOURCE_Msk |
 708              		.loc 2 2628 3 is_stmt 1 view .LVU178
 709              		.loc 2 2628 18 is_stmt 0 view .LVU179
 710 001a 0722     		movs	r2, #7
 711 001c 1A61     		str	r2, [r3, #16]
2629:Drivers/CMSIS/Include/core_cm7.h ****                    SysTick_CTRL_TICKINT_Msk   |
2630:Drivers/CMSIS/Include/core_cm7.h ****                    SysTick_CTRL_ENABLE_Msk;                         /* Enable SysTick IRQ and SysTi
2631:Drivers/CMSIS/Include/core_cm7.h ****   return (0UL);                                                     /* Function successful */
 712              		.loc 2 2631 3 is_stmt 1 view .LVU180
 713              		.loc 2 2631 10 is_stmt 0 view .LVU181
 714 001e 7047     		bx	lr
 715              	.L46:
2622:Drivers/CMSIS/Include/core_cm7.h ****   }
 716              		.loc 2 2622 12 view .LVU182
 717 0020 0120     		movs	r0, #1
 718              	.LVL52:
2622:Drivers/CMSIS/Include/core_cm7.h ****   }
 719              		.loc 2 2622 12 view .LVU183
 720              	.LBE49:
 721              	.LBE48:
 231:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** }
 722              		.loc 1 231 1 view .LVU184
 723 0022 7047     		bx	lr
 724              	.L48:
 725              		.align	2
 726              	.L47:
 727 0024 00ED00E0 		.word	-*********
 728              		.cfi_endproc
 729              	.LFE149:
 731              		.section	.text.HAL_MPU_Disable,"ax",%progbits
 732              		.align	1
 733              		.global	HAL_MPU_Disable
 734              		.syntax unified
 735              		.thumb
 736              		.thumb_func
 738              	HAL_MPU_Disable:
 739              	.LFB150:
 232:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** /**
 233:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * @}
 234:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   */
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 81


 235:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** 
 236:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** /** @defgroup CORTEX_Exported_Functions_Group2 Peripheral Control functions
 237:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****  *  @brief   Cortex control functions
 238:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****  *
 239:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** @verbatim
 240:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   ==============================================================================
 241:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****                       ##### Peripheral Control functions #####
 242:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   ==============================================================================
 243:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****     [..]
 244:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****       This subsection provides a set of functions allowing to control the CORTEX
 245:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****       (NVIC, SYSTICK, MPU) functionalities.
 246:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** 
 247:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** 
 248:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** @endverbatim
 249:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * @{
 250:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   */
 251:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** #if (__MPU_PRESENT == 1)
 252:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** /**
 253:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * @brief  Disables the MPU
 254:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * @retval None
 255:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   */
 256:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** void HAL_MPU_Disable(void)
 257:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** {
 740              		.loc 1 257 1 is_stmt 1 view -0
 741              		.cfi_startproc
 742              		@ args = 0, pretend = 0, frame = 0
 743              		@ frame_needed = 0, uses_anonymous_args = 0
 744              		@ link register save eliminated.
 258:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   /* Make sure outstanding transfers are done */
 259:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   __DMB();
 745              		.loc 1 259 3 view .LVU186
 746              	.LBB52:
 747              	.LBI52:
 947:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 948:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 949:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 950:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 951:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Data Memory Barrier
 952:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Ensures the apparent order of the explicit memory operations before
 953:Drivers/CMSIS/Include/cmsis_gcc.h ****            and after the instruction, without ensuring their completion.
 954:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 955:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __DMB(void)
 748              		.loc 3 955 27 view .LVU187
 749              	.LBB53:
 956:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 957:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("dmb 0xF":::"memory");
 750              		.loc 3 957 3 view .LVU188
 751              		.syntax unified
 752              	@ 957 "Drivers/CMSIS/Include/cmsis_gcc.h" 1
 753 0000 BFF35F8F 		dmb 0xF
 754              	@ 0 "" 2
 755              		.thumb
 756              		.syntax unified
 757              	.LBE53:
 758              	.LBE52:
 260:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** 
 261:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   /* Disable fault exceptions */
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 82


 262:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   SCB->SHCSR &= ~SCB_SHCSR_MEMFAULTENA_Msk;
 759              		.loc 1 262 3 view .LVU189
 760              		.loc 1 262 6 is_stmt 0 view .LVU190
 761 0004 044B     		ldr	r3, .L50
 762 0006 5A6A     		ldr	r2, [r3, #36]
 763              		.loc 1 262 14 view .LVU191
 764 0008 22F48032 		bic	r2, r2, #65536
 765 000c 5A62     		str	r2, [r3, #36]
 263:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** 
 264:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   /* Disable the MPU and clear the control register*/
 265:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   MPU->CTRL = 0;
 766              		.loc 1 265 3 is_stmt 1 view .LVU192
 767              		.loc 1 265 13 is_stmt 0 view .LVU193
 768 000e 0022     		movs	r2, #0
 769 0010 C3F89420 		str	r2, [r3, #148]
 266:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** }
 770              		.loc 1 266 1 view .LVU194
 771 0014 7047     		bx	lr
 772              	.L51:
 773 0016 00BF     		.align	2
 774              	.L50:
 775 0018 00ED00E0 		.word	-*********
 776              		.cfi_endproc
 777              	.LFE150:
 779              		.section	.text.HAL_MPU_Enable,"ax",%progbits
 780              		.align	1
 781              		.global	HAL_MPU_Enable
 782              		.syntax unified
 783              		.thumb
 784              		.thumb_func
 786              	HAL_MPU_Enable:
 787              	.LVL53:
 788              	.LFB151:
 267:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** 
 268:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** /**
 269:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * @brief  Enables the MPU
 270:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * @param  MPU_Control Specifies the control mode of the MPU during hard fault,
 271:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   *         NMI, FAULTMASK and privileged access to the default memory
 272:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   *         This parameter can be one of the following values:
 273:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   *            @arg MPU_HFNMI_PRIVDEF_NONE
 274:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   *            @arg MPU_HARDFAULT_NMI
 275:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   *            @arg MPU_PRIVILEGED_DEFAULT
 276:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   *            @arg MPU_HFNMI_PRIVDEF
 277:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * @retval None
 278:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   */
 279:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** void HAL_MPU_Enable(uint32_t MPU_Control)
 280:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** {
 789              		.loc 1 280 1 is_stmt 1 view -0
 790              		.cfi_startproc
 791              		@ args = 0, pretend = 0, frame = 0
 792              		@ frame_needed = 0, uses_anonymous_args = 0
 793              		@ link register save eliminated.
 281:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   /* Enable the MPU */
 282:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   MPU->CTRL = MPU_Control | MPU_CTRL_ENABLE_Msk;
 794              		.loc 1 282 3 view .LVU196
 795              		.loc 1 282 27 is_stmt 0 view .LVU197
 796 0000 40F00100 		orr	r0, r0, #1
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 83


 797              	.LVL54:
 798              		.loc 1 282 13 view .LVU198
 799 0004 054B     		ldr	r3, .L53
 800 0006 C3F89400 		str	r0, [r3, #148]
 283:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** 
 284:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   /* Enable fault exceptions */
 285:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   SCB->SHCSR |= SCB_SHCSR_MEMFAULTENA_Msk;
 801              		.loc 1 285 3 is_stmt 1 view .LVU199
 802              		.loc 1 285 6 is_stmt 0 view .LVU200
 803 000a 5A6A     		ldr	r2, [r3, #36]
 804              		.loc 1 285 14 view .LVU201
 805 000c 42F48032 		orr	r2, r2, #65536
 806 0010 5A62     		str	r2, [r3, #36]
 286:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** 
 287:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   /* Ensure MPU setting take effects */
 288:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   __DSB();
 807              		.loc 1 288 3 is_stmt 1 view .LVU202
 808              	.LBB54:
 809              	.LBI54:
 944:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 810              		.loc 3 944 27 view .LVU203
 811              	.LBB55:
 946:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 812              		.loc 3 946 3 view .LVU204
 813              		.syntax unified
 814              	@ 946 "Drivers/CMSIS/Include/cmsis_gcc.h" 1
 815 0012 BFF34F8F 		dsb 0xF
 816              	@ 0 "" 2
 817              		.thumb
 818              		.syntax unified
 819              	.LBE55:
 820              	.LBE54:
 289:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   __ISB();
 821              		.loc 1 289 3 view .LVU205
 822              	.LBB56:
 823              	.LBI56:
 933:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 824              		.loc 3 933 27 view .LVU206
 825              	.LBB57:
 935:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 826              		.loc 3 935 3 view .LVU207
 827              		.syntax unified
 828              	@ 935 "Drivers/CMSIS/Include/cmsis_gcc.h" 1
 829 0016 BFF36F8F 		isb 0xF
 830              	@ 0 "" 2
 831              		.thumb
 832              		.syntax unified
 833              	.LBE57:
 834              	.LBE56:
 290:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** }
 835              		.loc 1 290 1 is_stmt 0 view .LVU208
 836 001a 7047     		bx	lr
 837              	.L54:
 838              		.align	2
 839              	.L53:
 840 001c 00ED00E0 		.word	-*********
 841              		.cfi_endproc
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 84


 842              	.LFE151:
 844              		.section	.text.HAL_MPU_EnableRegion,"ax",%progbits
 845              		.align	1
 846              		.global	HAL_MPU_EnableRegion
 847              		.syntax unified
 848              		.thumb
 849              		.thumb_func
 851              	HAL_MPU_EnableRegion:
 852              	.LVL55:
 853              	.LFB152:
 291:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** 
 292:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** /**
 293:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * @brief  Enables the MPU Region.
 294:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * @retval None
 295:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   */
 296:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** void HAL_MPU_EnableRegion(uint32_t RegionNumber)
 297:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** {
 854              		.loc 1 297 1 is_stmt 1 view -0
 855              		.cfi_startproc
 856              		@ args = 0, pretend = 0, frame = 0
 857              		@ frame_needed = 0, uses_anonymous_args = 0
 858              		@ link register save eliminated.
 298:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   /* Check the parameters */
 299:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   assert_param(IS_MPU_REGION_NUMBER(RegionNumber));
 859              		.loc 1 299 3 view .LVU210
 300:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** 
 301:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   /* Set the Region number */
 302:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   MPU->RNR = RegionNumber;
 860              		.loc 1 302 3 view .LVU211
 861              		.loc 1 302 12 is_stmt 0 view .LVU212
 862 0000 044B     		ldr	r3, .L56
 863 0002 C3F89800 		str	r0, [r3, #152]
 303:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** 
 304:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   /* Enable the Region */
 305:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   SET_BIT(MPU->RASR, MPU_RASR_ENABLE_Msk);
 864              		.loc 1 305 3 is_stmt 1 view .LVU213
 865 0006 D3F8A020 		ldr	r2, [r3, #160]
 866 000a 42F00102 		orr	r2, r2, #1
 867 000e C3F8A020 		str	r2, [r3, #160]
 306:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** }
 868              		.loc 1 306 1 is_stmt 0 view .LVU214
 869 0012 7047     		bx	lr
 870              	.L57:
 871              		.align	2
 872              	.L56:
 873 0014 00ED00E0 		.word	-*********
 874              		.cfi_endproc
 875              	.LFE152:
 877              		.section	.text.HAL_MPU_DisableRegion,"ax",%progbits
 878              		.align	1
 879              		.global	HAL_MPU_DisableRegion
 880              		.syntax unified
 881              		.thumb
 882              		.thumb_func
 884              	HAL_MPU_DisableRegion:
 885              	.LVL56:
 886              	.LFB153:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 85


 307:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** 
 308:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** /**
 309:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * @brief  Disables the MPU Region.
 310:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * @retval None
 311:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   */
 312:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** void HAL_MPU_DisableRegion(uint32_t RegionNumber)
 313:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** {
 887              		.loc 1 313 1 is_stmt 1 view -0
 888              		.cfi_startproc
 889              		@ args = 0, pretend = 0, frame = 0
 890              		@ frame_needed = 0, uses_anonymous_args = 0
 891              		@ link register save eliminated.
 314:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   /* Check the parameters */
 315:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   assert_param(IS_MPU_REGION_NUMBER(RegionNumber));
 892              		.loc 1 315 3 view .LVU216
 316:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** 
 317:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   /* Set the Region number */
 318:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   MPU->RNR = RegionNumber;
 893              		.loc 1 318 3 view .LVU217
 894              		.loc 1 318 12 is_stmt 0 view .LVU218
 895 0000 044B     		ldr	r3, .L59
 896 0002 C3F89800 		str	r0, [r3, #152]
 319:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** 
 320:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   /* Disable the Region */
 321:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   CLEAR_BIT(MPU->RASR, MPU_RASR_ENABLE_Msk);
 897              		.loc 1 321 3 is_stmt 1 view .LVU219
 898 0006 D3F8A020 		ldr	r2, [r3, #160]
 899 000a 22F00102 		bic	r2, r2, #1
 900 000e C3F8A020 		str	r2, [r3, #160]
 322:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** }
 901              		.loc 1 322 1 is_stmt 0 view .LVU220
 902 0012 7047     		bx	lr
 903              	.L60:
 904              		.align	2
 905              	.L59:
 906 0014 00ED00E0 		.word	-*********
 907              		.cfi_endproc
 908              	.LFE153:
 910              		.section	.text.HAL_MPU_ConfigRegion,"ax",%progbits
 911              		.align	1
 912              		.global	HAL_MPU_ConfigRegion
 913              		.syntax unified
 914              		.thumb
 915              		.thumb_func
 917              	HAL_MPU_ConfigRegion:
 918              	.LVL57:
 919              	.LFB154:
 323:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** 
 324:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** /**
 325:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * @brief  Initializes and configures the Region and the memory to be protected.
 326:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * @param MPU_Init Pointer to a MPU_Region_InitTypeDef structure that contains
 327:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   *                the initialization and configuration information.
 328:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * @retval None
 329:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   */
 330:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** void HAL_MPU_ConfigRegion(const MPU_Region_InitTypeDef *MPU_Init)
 331:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** {
 920              		.loc 1 331 1 is_stmt 1 view -0
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 86


 921              		.cfi_startproc
 922              		@ args = 0, pretend = 0, frame = 0
 923              		@ frame_needed = 0, uses_anonymous_args = 0
 924              		@ link register save eliminated.
 332:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   /* Check the parameters */
 333:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   assert_param(IS_MPU_REGION_NUMBER(MPU_Init->Number));
 925              		.loc 1 333 3 view .LVU222
 334:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   assert_param(IS_MPU_REGION_ENABLE(MPU_Init->Enable));
 926              		.loc 1 334 3 view .LVU223
 335:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   assert_param(IS_MPU_INSTRUCTION_ACCESS(MPU_Init->DisableExec));
 927              		.loc 1 335 3 view .LVU224
 336:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   assert_param(IS_MPU_REGION_PERMISSION_ATTRIBUTE(MPU_Init->AccessPermission));
 928              		.loc 1 336 3 view .LVU225
 337:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   assert_param(IS_MPU_TEX_LEVEL(MPU_Init->TypeExtField));
 929              		.loc 1 337 3 view .LVU226
 338:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   assert_param(IS_MPU_ACCESS_SHAREABLE(MPU_Init->IsShareable));
 930              		.loc 1 338 3 view .LVU227
 339:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   assert_param(IS_MPU_ACCESS_CACHEABLE(MPU_Init->IsCacheable));
 931              		.loc 1 339 3 view .LVU228
 340:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   assert_param(IS_MPU_ACCESS_BUFFERABLE(MPU_Init->IsBufferable));
 932              		.loc 1 340 3 view .LVU229
 341:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   assert_param(IS_MPU_SUB_REGION_DISABLE(MPU_Init->SubRegionDisable));
 933              		.loc 1 341 3 view .LVU230
 342:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   assert_param(IS_MPU_REGION_SIZE(MPU_Init->Size));
 934              		.loc 1 342 3 view .LVU231
 343:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** 
 344:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   /* Set the Region number */
 345:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   MPU->RNR = MPU_Init->Number;
 935              		.loc 1 345 3 view .LVU232
 936              		.loc 1 345 22 is_stmt 0 view .LVU233
 937 0000 4378     		ldrb	r3, [r0, #1]	@ zero_extendqisi2
 938              		.loc 1 345 12 view .LVU234
 939 0002 144A     		ldr	r2, .L62
 940 0004 C2F89830 		str	r3, [r2, #152]
 346:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** 
 347:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   /* Disable the Region */
 348:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   CLEAR_BIT(MPU->RASR, MPU_RASR_ENABLE_Msk);
 941              		.loc 1 348 3 is_stmt 1 view .LVU235
 942 0008 D2F8A030 		ldr	r3, [r2, #160]
 943 000c 23F00103 		bic	r3, r3, #1
 944 0010 C2F8A030 		str	r3, [r2, #160]
 349:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** 
 350:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   /* Apply configuration */
 351:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   MPU->RBAR = MPU_Init->BaseAddress;
 945              		.loc 1 351 3 view .LVU236
 946              		.loc 1 351 23 is_stmt 0 view .LVU237
 947 0014 4368     		ldr	r3, [r0, #4]
 948              		.loc 1 351 13 view .LVU238
 949 0016 C2F89C30 		str	r3, [r2, #156]
 352:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   MPU->RASR = ((uint32_t)MPU_Init->DisableExec             << MPU_RASR_XN_Pos)   |
 950              		.loc 1 352 3 is_stmt 1 view .LVU239
 951              		.loc 1 352 34 is_stmt 0 view .LVU240
 952 001a 017B     		ldrb	r1, [r0, #12]	@ zero_extendqisi2
 353:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****               ((uint32_t)MPU_Init->AccessPermission        << MPU_RASR_AP_Pos)   |
 953              		.loc 1 353 34 view .LVU241
 954 001c C37A     		ldrb	r3, [r0, #11]	@ zero_extendqisi2
 955              		.loc 1 353 60 view .LVU242
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 87


 956 001e 1B06     		lsls	r3, r3, #24
 352:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   MPU->RASR = ((uint32_t)MPU_Init->DisableExec             << MPU_RASR_XN_Pos)   |
 957              		.loc 1 352 82 view .LVU243
 958 0020 43EA0173 		orr	r3, r3, r1, lsl #28
 354:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****               ((uint32_t)MPU_Init->TypeExtField            << MPU_RASR_TEX_Pos)  |
 959              		.loc 1 354 34 view .LVU244
 960 0024 817A     		ldrb	r1, [r0, #10]	@ zero_extendqisi2
 353:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****               ((uint32_t)MPU_Init->AccessPermission        << MPU_RASR_AP_Pos)   |
 961              		.loc 1 353 82 view .LVU245
 962 0026 43EAC143 		orr	r3, r3, r1, lsl #19
 355:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****               ((uint32_t)MPU_Init->IsShareable             << MPU_RASR_S_Pos)    |
 963              		.loc 1 355 34 view .LVU246
 964 002a 417B     		ldrb	r1, [r0, #13]	@ zero_extendqisi2
 354:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****               ((uint32_t)MPU_Init->TypeExtField            << MPU_RASR_TEX_Pos)  |
 965              		.loc 1 354 82 view .LVU247
 966 002c 43EA8143 		orr	r3, r3, r1, lsl #18
 356:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****               ((uint32_t)MPU_Init->IsCacheable             << MPU_RASR_C_Pos)    |
 967              		.loc 1 356 34 view .LVU248
 968 0030 817B     		ldrb	r1, [r0, #14]	@ zero_extendqisi2
 355:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****               ((uint32_t)MPU_Init->IsShareable             << MPU_RASR_S_Pos)    |
 969              		.loc 1 355 82 view .LVU249
 970 0032 43EA4143 		orr	r3, r3, r1, lsl #17
 357:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****               ((uint32_t)MPU_Init->IsBufferable            << MPU_RASR_B_Pos)    |
 971              		.loc 1 357 34 view .LVU250
 972 0036 C17B     		ldrb	r1, [r0, #15]	@ zero_extendqisi2
 356:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****               ((uint32_t)MPU_Init->IsCacheable             << MPU_RASR_C_Pos)    |
 973              		.loc 1 356 82 view .LVU251
 974 0038 43EA0143 		orr	r3, r3, r1, lsl #16
 358:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****               ((uint32_t)MPU_Init->SubRegionDisable        << MPU_RASR_SRD_Pos)  |
 975              		.loc 1 358 34 view .LVU252
 976 003c 417A     		ldrb	r1, [r0, #9]	@ zero_extendqisi2
 357:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****               ((uint32_t)MPU_Init->IsBufferable            << MPU_RASR_B_Pos)    |
 977              		.loc 1 357 82 view .LVU253
 978 003e 43EA0123 		orr	r3, r3, r1, lsl #8
 359:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****               ((uint32_t)MPU_Init->Size                    << MPU_RASR_SIZE_Pos) |
 979              		.loc 1 359 34 view .LVU254
 980 0042 017A     		ldrb	r1, [r0, #8]	@ zero_extendqisi2
 358:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****               ((uint32_t)MPU_Init->SubRegionDisable        << MPU_RASR_SRD_Pos)  |
 981              		.loc 1 358 82 view .LVU255
 982 0044 43EA4103 		orr	r3, r3, r1, lsl #1
 360:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****               ((uint32_t)MPU_Init->Enable                  << MPU_RASR_ENABLE_Pos);
 983              		.loc 1 360 34 view .LVU256
 984 0048 0178     		ldrb	r1, [r0]	@ zero_extendqisi2
 359:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****               ((uint32_t)MPU_Init->Size                    << MPU_RASR_SIZE_Pos) |
 985              		.loc 1 359 82 view .LVU257
 986 004a 0B43     		orrs	r3, r3, r1
 352:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****               ((uint32_t)MPU_Init->AccessPermission        << MPU_RASR_AP_Pos)   |
 987              		.loc 1 352 13 view .LVU258
 988 004c C2F8A030 		str	r3, [r2, #160]
 361:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** }
 989              		.loc 1 361 1 view .LVU259
 990 0050 7047     		bx	lr
 991              	.L63:
 992 0052 00BF     		.align	2
 993              	.L62:
 994 0054 00ED00E0 		.word	-*********
 995              		.cfi_endproc
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 88


 996              	.LFE154:
 998              		.section	.text.HAL_NVIC_GetPriorityGrouping,"ax",%progbits
 999              		.align	1
 1000              		.global	HAL_NVIC_GetPriorityGrouping
 1001              		.syntax unified
 1002              		.thumb
 1003              		.thumb_func
 1005              	HAL_NVIC_GetPriorityGrouping:
 1006              	.LFB155:
 362:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** #endif /* __MPU_PRESENT */
 363:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** 
 364:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** /**
 365:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * @brief  Gets the priority grouping field from the NVIC Interrupt Controller.
 366:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * @retval Priority grouping field (SCB->AIRCR [10:8] PRIGROUP field)
 367:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   */
 368:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** uint32_t HAL_NVIC_GetPriorityGrouping(void)
 369:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** {
 1007              		.loc 1 369 1 is_stmt 1 view -0
 1008              		.cfi_startproc
 1009              		@ args = 0, pretend = 0, frame = 0
 1010              		@ frame_needed = 0, uses_anonymous_args = 0
 1011              		@ link register save eliminated.
 370:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   /* Get the PRIGROUP[10:8] field value */
 371:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   return NVIC_GetPriorityGrouping();
 1012              		.loc 1 371 3 view .LVU261
 1013              	.LBB58:
 1014              	.LBI58:
1890:Drivers/CMSIS/Include/core_cm7.h **** {
 1015              		.loc 2 1890 26 view .LVU262
 1016              	.LBB59:
1892:Drivers/CMSIS/Include/core_cm7.h **** }
 1017              		.loc 2 1892 3 view .LVU263
1892:Drivers/CMSIS/Include/core_cm7.h **** }
 1018              		.loc 2 1892 26 is_stmt 0 view .LVU264
 1019 0000 024B     		ldr	r3, .L65
 1020 0002 D868     		ldr	r0, [r3, #12]
 1021              	.LBE59:
 1022              	.LBE58:
 372:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** }
 1023              		.loc 1 372 1 view .LVU265
 1024 0004 C0F30220 		ubfx	r0, r0, #8, #3
 1025 0008 7047     		bx	lr
 1026              	.L66:
 1027 000a 00BF     		.align	2
 1028              	.L65:
 1029 000c 00ED00E0 		.word	-*********
 1030              		.cfi_endproc
 1031              	.LFE155:
 1033              		.section	.text.HAL_NVIC_GetPriority,"ax",%progbits
 1034              		.align	1
 1035              		.global	HAL_NVIC_GetPriority
 1036              		.syntax unified
 1037              		.thumb
 1038              		.thumb_func
 1040              	HAL_NVIC_GetPriority:
 1041              	.LVL58:
 1042              	.LFB156:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 89


 373:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** 
 374:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** /**
 375:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * @brief  Gets the priority of an interrupt.
 376:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * @param  IRQn External interrupt number.
 377:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   *         This parameter can be an enumerator of IRQn_Type enumeration
 378:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   *         (For the complete STM32 Devices IRQ Channels list, please refer to the appropriate CMSI
 379:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * @param   PriorityGroup the priority grouping bits length.
 380:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   *         This parameter can be one of the following values:
 381:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   *           @arg NVIC_PRIORITYGROUP_0: 0 bits for preemption priority
 382:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   *                                      4 bits for subpriority
 383:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   *           @arg NVIC_PRIORITYGROUP_1: 1 bits for preemption priority
 384:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   *                                      3 bits for subpriority
 385:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   *           @arg NVIC_PRIORITYGROUP_2: 2 bits for preemption priority
 386:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   *                                      2 bits for subpriority
 387:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   *           @arg NVIC_PRIORITYGROUP_3: 3 bits for preemption priority
 388:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   *                                      1 bits for subpriority
 389:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   *           @arg NVIC_PRIORITYGROUP_4: 4 bits for preemption priority
 390:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   *                                      0 bits for subpriority
 391:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * @param  pPreemptPriority Pointer on the Preemptive priority value (starting from 0).
 392:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * @param  pSubPriority Pointer on the Subpriority value (starting from 0).
 393:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * @retval None
 394:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   */
 395:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** void HAL_NVIC_GetPriority(IRQn_Type IRQn, uint32_t PriorityGroup, uint32_t *pPreemptPriority, uint3
 396:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** {
 1043              		.loc 1 396 1 is_stmt 1 view -0
 1044              		.cfi_startproc
 1045              		@ args = 0, pretend = 0, frame = 0
 1046              		@ frame_needed = 0, uses_anonymous_args = 0
 1047              		.loc 1 396 1 is_stmt 0 view .LVU267
 1048 0000 70B5     		push	{r4, r5, r6, lr}
 1049              	.LCFI6:
 1050              		.cfi_def_cfa_offset 16
 1051              		.cfi_offset 4, -16
 1052              		.cfi_offset 5, -12
 1053              		.cfi_offset 6, -8
 1054              		.cfi_offset 14, -4
 1055 0002 0C46     		mov	r4, r1
 1056 0004 1546     		mov	r5, r2
 1057 0006 1E46     		mov	r6, r3
 397:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   /* Check the parameters */
 398:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   assert_param(IS_NVIC_PRIORITY_GROUP(PriorityGroup));
 1058              		.loc 1 398 3 is_stmt 1 view .LVU268
 399:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****  /* Get priority for Cortex-M system or device specific interrupts */
 400:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   NVIC_DecodePriority(NVIC_GetPriority(IRQn), PriorityGroup, pPreemptPriority, pSubPriority);
 1059              		.loc 1 400 3 view .LVU269
 1060 0008 FFF7FEFF 		bl	__NVIC_GetPriority
 1061              	.LVL59:
 1062              		.loc 1 400 3 is_stmt 0 discriminator 1 view .LVU270
 1063 000c 3346     		mov	r3, r6
 1064 000e 2A46     		mov	r2, r5
 1065 0010 2146     		mov	r1, r4
 1066 0012 FFF7FEFF 		bl	NVIC_DecodePriority
 1067              	.LVL60:
 401:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** }
 1068              		.loc 1 401 1 view .LVU271
 1069 0016 70BD     		pop	{r4, r5, r6, pc}
 1070              		.loc 1 401 1 view .LVU272
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 90


 1071              		.cfi_endproc
 1072              	.LFE156:
 1074              		.section	.text.HAL_NVIC_SetPendingIRQ,"ax",%progbits
 1075              		.align	1
 1076              		.global	HAL_NVIC_SetPendingIRQ
 1077              		.syntax unified
 1078              		.thumb
 1079              		.thumb_func
 1081              	HAL_NVIC_SetPendingIRQ:
 1082              	.LVL61:
 1083              	.LFB157:
 402:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** 
 403:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** /**
 404:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * @brief  Sets Pending bit of an external interrupt.
 405:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * @param  IRQn External interrupt number
 406:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   *         This parameter can be an enumerator of IRQn_Type enumeration
 407:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   *         (For the complete STM32 Devices IRQ Channels list, please refer to the appropriate CMSI
 408:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * @retval None
 409:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   */
 410:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** void HAL_NVIC_SetPendingIRQ(IRQn_Type IRQn)
 411:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** {
 1084              		.loc 1 411 1 is_stmt 1 view -0
 1085              		.cfi_startproc
 1086              		@ args = 0, pretend = 0, frame = 0
 1087              		@ frame_needed = 0, uses_anonymous_args = 0
 1088              		@ link register save eliminated.
 412:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   /* Check the parameters */
 413:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   assert_param(IS_NVIC_DEVICE_IRQ(IRQn));
 1089              		.loc 1 413 3 view .LVU274
 414:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** 
 415:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   /* Set interrupt pending */
 416:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   NVIC_SetPendingIRQ(IRQn);
 1090              		.loc 1 416 3 view .LVU275
 1091              	.LBB60:
 1092              	.LBI60:
1978:Drivers/CMSIS/Include/core_cm7.h **** {
 1093              		.loc 2 1978 22 view .LVU276
 1094              	.LBB61:
1980:Drivers/CMSIS/Include/core_cm7.h ****   {
 1095              		.loc 2 1980 3 view .LVU277
1980:Drivers/CMSIS/Include/core_cm7.h ****   {
 1096              		.loc 2 1980 6 is_stmt 0 view .LVU278
 1097 0000 0028     		cmp	r0, #0
1980:Drivers/CMSIS/Include/core_cm7.h ****   {
 1098              		.loc 2 1980 6 view .LVU279
 1099 0002 08DB     		blt	.L69
1982:Drivers/CMSIS/Include/core_cm7.h ****   }
 1100              		.loc 2 1982 5 is_stmt 1 view .LVU280
1982:Drivers/CMSIS/Include/core_cm7.h ****   }
 1101              		.loc 2 1982 81 is_stmt 0 view .LVU281
 1102 0004 00F01F02 		and	r2, r0, #31
1982:Drivers/CMSIS/Include/core_cm7.h ****   }
 1103              		.loc 2 1982 34 view .LVU282
 1104 0008 4009     		lsrs	r0, r0, #5
 1105              	.LVL62:
1982:Drivers/CMSIS/Include/core_cm7.h ****   }
 1106              		.loc 2 1982 45 view .LVU283
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 91


 1107 000a 0123     		movs	r3, #1
 1108 000c 9340     		lsls	r3, r3, r2
1982:Drivers/CMSIS/Include/core_cm7.h ****   }
 1109              		.loc 2 1982 43 view .LVU284
 1110 000e 4030     		adds	r0, r0, #64
 1111 0010 014A     		ldr	r2, .L71
 1112 0012 42F82030 		str	r3, [r2, r0, lsl #2]
 1113              	.LVL63:
 1114              	.L69:
1982:Drivers/CMSIS/Include/core_cm7.h ****   }
 1115              		.loc 2 1982 43 view .LVU285
 1116              	.LBE61:
 1117              	.LBE60:
 417:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** }
 1118              		.loc 1 417 1 view .LVU286
 1119 0016 7047     		bx	lr
 1120              	.L72:
 1121              		.align	2
 1122              	.L71:
 1123 0018 00E100E0 		.word	-*********
 1124              		.cfi_endproc
 1125              	.LFE157:
 1127              		.section	.text.HAL_NVIC_GetPendingIRQ,"ax",%progbits
 1128              		.align	1
 1129              		.global	HAL_NVIC_GetPendingIRQ
 1130              		.syntax unified
 1131              		.thumb
 1132              		.thumb_func
 1134              	HAL_NVIC_GetPendingIRQ:
 1135              	.LVL64:
 1136              	.LFB158:
 418:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** 
 419:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** /**
 420:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * @brief  Gets Pending Interrupt (reads the pending register in the NVIC
 421:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   *         and returns the pending bit for the specified interrupt).
 422:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * @param  IRQn External interrupt number.
 423:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   *          This parameter can be an enumerator of IRQn_Type enumeration
 424:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   *         (For the complete STM32 Devices IRQ Channels list, please refer to the appropriate CMSI
 425:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * @retval status  - 0  Interrupt status is not pending.
 426:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   *                 - 1  Interrupt status is pending.
 427:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   */
 428:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** uint32_t HAL_NVIC_GetPendingIRQ(IRQn_Type IRQn)
 429:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** {
 1137              		.loc 1 429 1 is_stmt 1 view -0
 1138              		.cfi_startproc
 1139              		@ args = 0, pretend = 0, frame = 0
 1140              		@ frame_needed = 0, uses_anonymous_args = 0
 1141              		@ link register save eliminated.
 430:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   /* Check the parameters */
 431:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   assert_param(IS_NVIC_DEVICE_IRQ(IRQn));
 1142              		.loc 1 431 3 view .LVU288
 432:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** 
 433:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   /* Return 1 if pending else 0 */
 434:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   return NVIC_GetPendingIRQ(IRQn);
 1143              		.loc 1 434 3 view .LVU289
 1144              	.LBB62:
 1145              	.LBI62:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 92


1959:Drivers/CMSIS/Include/core_cm7.h **** {
 1146              		.loc 2 1959 26 view .LVU290
 1147              	.LBB63:
1961:Drivers/CMSIS/Include/core_cm7.h ****   {
 1148              		.loc 2 1961 3 view .LVU291
1961:Drivers/CMSIS/Include/core_cm7.h ****   {
 1149              		.loc 2 1961 6 is_stmt 0 view .LVU292
 1150 0000 0028     		cmp	r0, #0
1961:Drivers/CMSIS/Include/core_cm7.h ****   {
 1151              		.loc 2 1961 6 view .LVU293
 1152 0002 0BDB     		blt	.L75
1963:Drivers/CMSIS/Include/core_cm7.h ****   }
 1153              		.loc 2 1963 5 is_stmt 1 view .LVU294
1963:Drivers/CMSIS/Include/core_cm7.h ****   }
 1154              		.loc 2 1963 54 is_stmt 0 view .LVU295
 1155 0004 4309     		lsrs	r3, r0, #5
1963:Drivers/CMSIS/Include/core_cm7.h ****   }
 1156              		.loc 2 1963 35 view .LVU296
 1157 0006 4033     		adds	r3, r3, #64
 1158 0008 054A     		ldr	r2, .L76
 1159 000a 52F82330 		ldr	r3, [r2, r3, lsl #2]
1963:Drivers/CMSIS/Include/core_cm7.h ****   }
 1160              		.loc 2 1963 91 view .LVU297
 1161 000e 00F01F00 		and	r0, r0, #31
 1162              	.LVL65:
1963:Drivers/CMSIS/Include/core_cm7.h ****   }
 1163              		.loc 2 1963 103 view .LVU298
 1164 0012 23FA00F0 		lsr	r0, r3, r0
1963:Drivers/CMSIS/Include/core_cm7.h ****   }
 1165              		.loc 2 1963 12 view .LVU299
 1166 0016 00F00100 		and	r0, r0, #1
 1167 001a 7047     		bx	lr
 1168              	.L75:
1967:Drivers/CMSIS/Include/core_cm7.h ****   }
 1169              		.loc 2 1967 11 view .LVU300
 1170 001c 0020     		movs	r0, #0
 1171              	.LVL66:
1967:Drivers/CMSIS/Include/core_cm7.h ****   }
 1172              		.loc 2 1967 11 view .LVU301
 1173              	.LBE63:
 1174              	.LBE62:
 435:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** }
 1175              		.loc 1 435 1 view .LVU302
 1176 001e 7047     		bx	lr
 1177              	.L77:
 1178              		.align	2
 1179              	.L76:
 1180 0020 00E100E0 		.word	-*********
 1181              		.cfi_endproc
 1182              	.LFE158:
 1184              		.section	.text.HAL_NVIC_ClearPendingIRQ,"ax",%progbits
 1185              		.align	1
 1186              		.global	HAL_NVIC_ClearPendingIRQ
 1187              		.syntax unified
 1188              		.thumb
 1189              		.thumb_func
 1191              	HAL_NVIC_ClearPendingIRQ:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 93


 1192              	.LVL67:
 1193              	.LFB159:
 436:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** 
 437:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** /**
 438:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * @brief  Clears the pending bit of an external interrupt.
 439:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * @param  IRQn External interrupt number.
 440:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   *         This parameter can be an enumerator of IRQn_Type enumeration
 441:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   *         (For the complete STM32 Devices IRQ Channels list, please refer to the appropriate CMSI
 442:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * @retval None
 443:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   */
 444:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** void HAL_NVIC_ClearPendingIRQ(IRQn_Type IRQn)
 445:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** {
 1194              		.loc 1 445 1 is_stmt 1 view -0
 1195              		.cfi_startproc
 1196              		@ args = 0, pretend = 0, frame = 0
 1197              		@ frame_needed = 0, uses_anonymous_args = 0
 1198              		@ link register save eliminated.
 446:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   /* Check the parameters */
 447:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   assert_param(IS_NVIC_DEVICE_IRQ(IRQn));
 1199              		.loc 1 447 3 view .LVU304
 448:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** 
 449:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   /* Clear pending interrupt */
 450:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   NVIC_ClearPendingIRQ(IRQn);
 1200              		.loc 1 450 3 view .LVU305
 1201              	.LBB64:
 1202              	.LBI64:
1993:Drivers/CMSIS/Include/core_cm7.h **** {
 1203              		.loc 2 1993 22 view .LVU306
 1204              	.LBB65:
1995:Drivers/CMSIS/Include/core_cm7.h ****   {
 1205              		.loc 2 1995 3 view .LVU307
1995:Drivers/CMSIS/Include/core_cm7.h ****   {
 1206              		.loc 2 1995 6 is_stmt 0 view .LVU308
 1207 0000 0028     		cmp	r0, #0
1995:Drivers/CMSIS/Include/core_cm7.h ****   {
 1208              		.loc 2 1995 6 view .LVU309
 1209 0002 08DB     		blt	.L78
1997:Drivers/CMSIS/Include/core_cm7.h ****   }
 1210              		.loc 2 1997 5 is_stmt 1 view .LVU310
1997:Drivers/CMSIS/Include/core_cm7.h ****   }
 1211              		.loc 2 1997 81 is_stmt 0 view .LVU311
 1212 0004 00F01F02 		and	r2, r0, #31
1997:Drivers/CMSIS/Include/core_cm7.h ****   }
 1213              		.loc 2 1997 34 view .LVU312
 1214 0008 4009     		lsrs	r0, r0, #5
 1215              	.LVL68:
1997:Drivers/CMSIS/Include/core_cm7.h ****   }
 1216              		.loc 2 1997 45 view .LVU313
 1217 000a 0123     		movs	r3, #1
 1218 000c 9340     		lsls	r3, r3, r2
1997:Drivers/CMSIS/Include/core_cm7.h ****   }
 1219              		.loc 2 1997 43 view .LVU314
 1220 000e 6030     		adds	r0, r0, #96
 1221 0010 014A     		ldr	r2, .L80
 1222 0012 42F82030 		str	r3, [r2, r0, lsl #2]
 1223              	.LVL69:
 1224              	.L78:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 94


1997:Drivers/CMSIS/Include/core_cm7.h ****   }
 1225              		.loc 2 1997 43 view .LVU315
 1226              	.LBE65:
 1227              	.LBE64:
 451:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** }
 1228              		.loc 1 451 1 view .LVU316
 1229 0016 7047     		bx	lr
 1230              	.L81:
 1231              		.align	2
 1232              	.L80:
 1233 0018 00E100E0 		.word	-*********
 1234              		.cfi_endproc
 1235              	.LFE159:
 1237              		.section	.text.HAL_NVIC_GetActive,"ax",%progbits
 1238              		.align	1
 1239              		.global	HAL_NVIC_GetActive
 1240              		.syntax unified
 1241              		.thumb
 1242              		.thumb_func
 1244              	HAL_NVIC_GetActive:
 1245              	.LVL70:
 1246              	.LFB160:
 452:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** 
 453:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** /**
 454:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * @brief Gets active interrupt ( reads the active register in NVIC and returns the active bit).
 455:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * @param IRQn External interrupt number
 456:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   *         This parameter can be an enumerator of IRQn_Type enumeration
 457:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   *         (For the complete STM32 Devices IRQ Channels list, please refer to the appropriate CMSI
 458:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * @retval status  - 0  Interrupt status is not pending.
 459:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   *                 - 1  Interrupt status is pending.
 460:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   */
 461:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** uint32_t HAL_NVIC_GetActive(IRQn_Type IRQn)
 462:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** {
 1247              		.loc 1 462 1 is_stmt 1 view -0
 1248              		.cfi_startproc
 1249              		@ args = 0, pretend = 0, frame = 0
 1250              		@ frame_needed = 0, uses_anonymous_args = 0
 1251              		@ link register save eliminated.
 463:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   /* Check the parameters */
 464:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   assert_param(IS_NVIC_DEVICE_IRQ(IRQn));
 1252              		.loc 1 464 3 view .LVU318
 465:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** 
 466:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   /* Return 1 if active else 0 */
 467:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   return NVIC_GetActive(IRQn);
 1253              		.loc 1 467 3 view .LVU319
 1254              	.LBB66:
 1255              	.LBI66:
2010:Drivers/CMSIS/Include/core_cm7.h **** {
 1256              		.loc 2 2010 26 view .LVU320
 1257              	.LBB67:
2012:Drivers/CMSIS/Include/core_cm7.h ****   {
 1258              		.loc 2 2012 3 view .LVU321
2012:Drivers/CMSIS/Include/core_cm7.h ****   {
 1259              		.loc 2 2012 6 is_stmt 0 view .LVU322
 1260 0000 0028     		cmp	r0, #0
2012:Drivers/CMSIS/Include/core_cm7.h ****   {
 1261              		.loc 2 2012 6 view .LVU323
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 95


 1262 0002 0BDB     		blt	.L84
2014:Drivers/CMSIS/Include/core_cm7.h ****   }
 1263              		.loc 2 2014 5 is_stmt 1 view .LVU324
2014:Drivers/CMSIS/Include/core_cm7.h ****   }
 1264              		.loc 2 2014 54 is_stmt 0 view .LVU325
 1265 0004 4309     		lsrs	r3, r0, #5
2014:Drivers/CMSIS/Include/core_cm7.h ****   }
 1266              		.loc 2 2014 35 view .LVU326
 1267 0006 8033     		adds	r3, r3, #128
 1268 0008 054A     		ldr	r2, .L85
 1269 000a 52F82330 		ldr	r3, [r2, r3, lsl #2]
2014:Drivers/CMSIS/Include/core_cm7.h ****   }
 1270              		.loc 2 2014 91 view .LVU327
 1271 000e 00F01F00 		and	r0, r0, #31
 1272              	.LVL71:
2014:Drivers/CMSIS/Include/core_cm7.h ****   }
 1273              		.loc 2 2014 103 view .LVU328
 1274 0012 23FA00F0 		lsr	r0, r3, r0
2014:Drivers/CMSIS/Include/core_cm7.h ****   }
 1275              		.loc 2 2014 12 view .LVU329
 1276 0016 00F00100 		and	r0, r0, #1
 1277 001a 7047     		bx	lr
 1278              	.L84:
2018:Drivers/CMSIS/Include/core_cm7.h ****   }
 1279              		.loc 2 2018 11 view .LVU330
 1280 001c 0020     		movs	r0, #0
 1281              	.LVL72:
2018:Drivers/CMSIS/Include/core_cm7.h ****   }
 1282              		.loc 2 2018 11 view .LVU331
 1283              	.LBE67:
 1284              	.LBE66:
 468:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** }
 1285              		.loc 1 468 1 view .LVU332
 1286 001e 7047     		bx	lr
 1287              	.L86:
 1288              		.align	2
 1289              	.L85:
 1290 0020 00E100E0 		.word	-*********
 1291              		.cfi_endproc
 1292              	.LFE160:
 1294              		.section	.text.HAL_SYSTICK_CLKSourceConfig,"ax",%progbits
 1295              		.align	1
 1296              		.global	HAL_SYSTICK_CLKSourceConfig
 1297              		.syntax unified
 1298              		.thumb
 1299              		.thumb_func
 1301              	HAL_SYSTICK_CLKSourceConfig:
 1302              	.LVL73:
 1303              	.LFB161:
 469:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** 
 470:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** /**
 471:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * @brief  Configures the SysTick clock source.
 472:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * @param  CLKSource specifies the SysTick clock source.
 473:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   *         This parameter can be one of the following values:
 474:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   *             @arg SYSTICK_CLKSOURCE_HCLK_DIV8: AHB clock divided by 8 selected as SysTick clock 
 475:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   *             @arg SYSTICK_CLKSOURCE_HCLK: AHB clock selected as SysTick clock source.
 476:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * @retval None
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 96


 477:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   */
 478:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** void HAL_SYSTICK_CLKSourceConfig(uint32_t CLKSource)
 479:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** {
 1304              		.loc 1 479 1 is_stmt 1 view -0
 1305              		.cfi_startproc
 1306              		@ args = 0, pretend = 0, frame = 0
 1307              		@ frame_needed = 0, uses_anonymous_args = 0
 1308              		@ link register save eliminated.
 480:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   /* Check the parameters */
 481:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   assert_param(IS_SYSTICK_CLK_SOURCE(CLKSource));
 1309              		.loc 1 481 3 view .LVU334
 482:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   if (CLKSource == SYSTICK_CLKSOURCE_HCLK)
 1310              		.loc 1 482 3 view .LVU335
 1311              		.loc 1 482 6 is_stmt 0 view .LVU336
 1312 0000 0428     		cmp	r0, #4
 1313 0002 06D0     		beq	.L90
 483:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   {
 484:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****     SysTick->CTRL |= SYSTICK_CLKSOURCE_HCLK;
 485:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   }
 486:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   else
 487:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   {
 488:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****     SysTick->CTRL &= ~SYSTICK_CLKSOURCE_HCLK;
 1314              		.loc 1 488 5 is_stmt 1 view .LVU337
 1315              		.loc 1 488 12 is_stmt 0 view .LVU338
 1316 0004 4FF0E022 		mov	r2, #-*********
 1317 0008 1369     		ldr	r3, [r2, #16]
 1318              		.loc 1 488 19 view .LVU339
 1319 000a 23F00403 		bic	r3, r3, #4
 1320 000e 1361     		str	r3, [r2, #16]
 489:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   }
 490:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** }
 1321              		.loc 1 490 1 view .LVU340
 1322 0010 7047     		bx	lr
 1323              	.L90:
 484:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   }
 1324              		.loc 1 484 5 is_stmt 1 view .LVU341
 484:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   }
 1325              		.loc 1 484 12 is_stmt 0 view .LVU342
 1326 0012 4FF0E022 		mov	r2, #-*********
 1327 0016 1369     		ldr	r3, [r2, #16]
 484:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   }
 1328              		.loc 1 484 19 view .LVU343
 1329 0018 43F00403 		orr	r3, r3, #4
 1330 001c 1361     		str	r3, [r2, #16]
 1331 001e 7047     		bx	lr
 1332              		.cfi_endproc
 1333              	.LFE161:
 1335              		.section	.text.HAL_SYSTICK_Callback,"ax",%progbits
 1336              		.align	1
 1337              		.weak	HAL_SYSTICK_Callback
 1338              		.syntax unified
 1339              		.thumb
 1340              		.thumb_func
 1342              	HAL_SYSTICK_Callback:
 1343              	.LFB163:
 491:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** 
 492:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** /**
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 97


 493:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * @brief  This function handles SYSTICK interrupt request.
 494:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * @retval None
 495:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   */
 496:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** void HAL_SYSTICK_IRQHandler(void)
 497:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** {
 498:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   HAL_SYSTICK_Callback();
 499:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** }
 500:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** 
 501:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** /**
 502:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * @brief  SYSTICK callback.
 503:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * @retval None
 504:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   */
 505:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** __weak void HAL_SYSTICK_Callback(void)
 506:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** {
 1344              		.loc 1 506 1 is_stmt 1 view -0
 1345              		.cfi_startproc
 1346              		@ args = 0, pretend = 0, frame = 0
 1347              		@ frame_needed = 0, uses_anonymous_args = 0
 1348              		@ link register save eliminated.
 507:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   /* NOTE : This function Should not be modified, when the callback is needed,
 508:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****             the HAL_SYSTICK_Callback could be implemented in the user file
 509:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****    */
 510:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** }
 1349              		.loc 1 510 1 view .LVU345
 1350 0000 7047     		bx	lr
 1351              		.cfi_endproc
 1352              	.LFE163:
 1354              		.section	.text.HAL_SYSTICK_IRQHandler,"ax",%progbits
 1355              		.align	1
 1356              		.global	HAL_SYSTICK_IRQHandler
 1357              		.syntax unified
 1358              		.thumb
 1359              		.thumb_func
 1361              	HAL_SYSTICK_IRQHandler:
 1362              	.LFB162:
 497:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   HAL_SYSTICK_Callback();
 1363              		.loc 1 497 1 view -0
 1364              		.cfi_startproc
 1365              		@ args = 0, pretend = 0, frame = 0
 1366              		@ frame_needed = 0, uses_anonymous_args = 0
 1367 0000 08B5     		push	{r3, lr}
 1368              	.LCFI7:
 1369              		.cfi_def_cfa_offset 8
 1370              		.cfi_offset 3, -8
 1371              		.cfi_offset 14, -4
 498:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** }
 1372              		.loc 1 498 3 view .LVU347
 1373 0002 FFF7FEFF 		bl	HAL_SYSTICK_Callback
 1374              	.LVL74:
 499:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** 
 1375              		.loc 1 499 1 is_stmt 0 view .LVU348
 1376 0006 08BD     		pop	{r3, pc}
 1377              		.cfi_endproc
 1378              	.LFE162:
 1380              		.section	.text.HAL_GetCurrentCPUID,"ax",%progbits
 1381              		.align	1
 1382              		.global	HAL_GetCurrentCPUID
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 98


 1383              		.syntax unified
 1384              		.thumb
 1385              		.thumb_func
 1387              	HAL_GetCurrentCPUID:
 1388              	.LFB164:
 511:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** 
 512:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** #if defined(DUAL_CORE)
 513:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** 
 514:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** /**
 515:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * @brief  Returns the current CPU ID.
 516:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   * @retval CPU identifier
 517:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   */
 518:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** uint32_t HAL_GetCurrentCPUID(void)
 519:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** {
 520:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   if (((SCB->CPUID & 0x000000F0U) >> 4 )== 0x7U)
 521:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   {
 522:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****     return  CM7_CPUID;
 523:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   }
 524:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   else
 525:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   {
 526:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****     return CM4_CPUID;
 527:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   }
 528:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** }
 529:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** 
 530:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** #else
 531:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** 
 532:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** /**
 533:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** * @brief  Returns the current CPU ID.
 534:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** * @retval CPU identifier
 535:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** */
 536:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** uint32_t HAL_GetCurrentCPUID(void)
 537:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** {
 1389              		.loc 1 537 1 is_stmt 1 view -0
 1390              		.cfi_startproc
 1391              		@ args = 0, pretend = 0, frame = 0
 1392              		@ frame_needed = 0, uses_anonymous_args = 0
 1393              		@ link register save eliminated.
 538:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c ****   return  CM7_CPUID;
 1394              		.loc 1 538 3 view .LVU350
 539:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c **** }
 1395              		.loc 1 539 1 is_stmt 0 view .LVU351
 1396 0000 0320     		movs	r0, #3
 1397 0002 7047     		bx	lr
 1398              		.cfi_endproc
 1399              	.LFE164:
 1401              		.text
 1402              	.Letext0:
 1403              		.file 4 "Drivers/CMSIS/Device/ST/STM32H7xx/Include/stm32h723xx.h"
 1404              		.file 5 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\../lib/gcc/arm-none-eabi/13.3.1/..
 1405              		.file 6 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\../lib/gcc/arm-none-eabi/13.3.1/..
 1406              		.file 7 "Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_cortex.h"
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 99


DEFINED SYMBOLS
                            *ABS*:00000000 stm32h7xx_hal_cortex.c
C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s:20     .text.__NVIC_EnableIRQ:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s:25     .text.__NVIC_EnableIRQ:00000000 __NVIC_EnableIRQ
C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s:59     .text.__NVIC_EnableIRQ:00000018 $d
C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s:64     .text.__NVIC_DisableIRQ:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s:69     .text.__NVIC_DisableIRQ:00000000 __NVIC_DisableIRQ
C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s:130    .text.__NVIC_DisableIRQ:00000020 $d
C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s:135    .text.__NVIC_SetPriority:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s:140    .text.__NVIC_SetPriority:00000000 __NVIC_SetPriority
C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s:182    .text.__NVIC_SetPriority:0000001c $d
C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s:188    .text.__NVIC_GetPriority:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s:193    .text.__NVIC_GetPriority:00000000 __NVIC_GetPriority
C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s:228    .text.__NVIC_GetPriority:00000018 $d
C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s:234    .text.NVIC_EncodePriority:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s:239    .text.NVIC_EncodePriority:00000000 NVIC_EncodePriority
C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s:301    .text.NVIC_DecodePriority:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s:306    .text.NVIC_DecodePriority:00000000 NVIC_DecodePriority
C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s:375    .text.__NVIC_SystemReset:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s:380    .text.__NVIC_SystemReset:00000000 __NVIC_SystemReset
C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s:441    .text.__NVIC_SystemReset:0000001c $d
C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s:447    .text.HAL_NVIC_SetPriorityGrouping:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s:453    .text.HAL_NVIC_SetPriorityGrouping:00000000 HAL_NVIC_SetPriorityGrouping
C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s:507    .text.HAL_NVIC_SetPriorityGrouping:0000001c $d
C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s:513    .text.HAL_NVIC_SetPriority:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s:519    .text.HAL_NVIC_SetPriority:00000000 HAL_NVIC_SetPriority
C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s:568    .text.HAL_NVIC_SetPriority:0000001c $d
C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s:573    .text.HAL_NVIC_EnableIRQ:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s:579    .text.HAL_NVIC_EnableIRQ:00000000 HAL_NVIC_EnableIRQ
C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s:602    .text.HAL_NVIC_DisableIRQ:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s:608    .text.HAL_NVIC_DisableIRQ:00000000 HAL_NVIC_DisableIRQ
C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s:631    .text.HAL_NVIC_SystemReset:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s:637    .text.HAL_NVIC_SystemReset:00000000 HAL_NVIC_SystemReset
C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s:656    .text.HAL_SYSTICK_Config:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s:662    .text.HAL_SYSTICK_Config:00000000 HAL_SYSTICK_Config
C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s:727    .text.HAL_SYSTICK_Config:00000024 $d
C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s:732    .text.HAL_MPU_Disable:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s:738    .text.HAL_MPU_Disable:00000000 HAL_MPU_Disable
C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s:775    .text.HAL_MPU_Disable:00000018 $d
C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s:780    .text.HAL_MPU_Enable:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s:786    .text.HAL_MPU_Enable:00000000 HAL_MPU_Enable
C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s:840    .text.HAL_MPU_Enable:0000001c $d
C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s:845    .text.HAL_MPU_EnableRegion:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s:851    .text.HAL_MPU_EnableRegion:00000000 HAL_MPU_EnableRegion
C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s:873    .text.HAL_MPU_EnableRegion:00000014 $d
C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s:878    .text.HAL_MPU_DisableRegion:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s:884    .text.HAL_MPU_DisableRegion:00000000 HAL_MPU_DisableRegion
C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s:906    .text.HAL_MPU_DisableRegion:00000014 $d
C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s:911    .text.HAL_MPU_ConfigRegion:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s:917    .text.HAL_MPU_ConfigRegion:00000000 HAL_MPU_ConfigRegion
C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s:994    .text.HAL_MPU_ConfigRegion:00000054 $d
C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s:999    .text.HAL_NVIC_GetPriorityGrouping:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s:1005   .text.HAL_NVIC_GetPriorityGrouping:00000000 HAL_NVIC_GetPriorityGrouping
C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s:1029   .text.HAL_NVIC_GetPriorityGrouping:0000000c $d
C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s:1034   .text.HAL_NVIC_GetPriority:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s:1040   .text.HAL_NVIC_GetPriority:00000000 HAL_NVIC_GetPriority
C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s:1075   .text.HAL_NVIC_SetPendingIRQ:00000000 $t
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s 			page 100


C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s:1081   .text.HAL_NVIC_SetPendingIRQ:00000000 HAL_NVIC_SetPendingIRQ
C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s:1123   .text.HAL_NVIC_SetPendingIRQ:00000018 $d
C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s:1128   .text.HAL_NVIC_GetPendingIRQ:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s:1134   .text.HAL_NVIC_GetPendingIRQ:00000000 HAL_NVIC_GetPendingIRQ
C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s:1180   .text.HAL_NVIC_GetPendingIRQ:00000020 $d
C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s:1185   .text.HAL_NVIC_ClearPendingIRQ:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s:1191   .text.HAL_NVIC_ClearPendingIRQ:00000000 HAL_NVIC_ClearPendingIRQ
C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s:1233   .text.HAL_NVIC_ClearPendingIRQ:00000018 $d
C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s:1238   .text.HAL_NVIC_GetActive:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s:1244   .text.HAL_NVIC_GetActive:00000000 HAL_NVIC_GetActive
C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s:1290   .text.HAL_NVIC_GetActive:00000020 $d
C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s:1295   .text.HAL_SYSTICK_CLKSourceConfig:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s:1301   .text.HAL_SYSTICK_CLKSourceConfig:00000000 HAL_SYSTICK_CLKSourceConfig
C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s:1336   .text.HAL_SYSTICK_Callback:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s:1342   .text.HAL_SYSTICK_Callback:00000000 HAL_SYSTICK_Callback
C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s:1355   .text.HAL_SYSTICK_IRQHandler:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s:1361   .text.HAL_SYSTICK_IRQHandler:00000000 HAL_SYSTICK_IRQHandler
C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s:1381   .text.HAL_GetCurrentCPUID:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccFyJzKU.s:1387   .text.HAL_GetCurrentCPUID:00000000 HAL_GetCurrentCPUID

NO UNDEFINED SYMBOLS
