<?xml version="1.0" encoding="ASCII"?>
<application:Application xmi:version="2.0" xmlns:xmi="http://www.omg.org/XMI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:advanced="http://www.eclipse.org/ui/2010/UIModel/application/ui/advanced" xmlns:application="http://www.eclipse.org/ui/2010/UIModel/application" xmlns:basic="http://www.eclipse.org/ui/2010/UIModel/application/ui/basic" xmlns:menu="http://www.eclipse.org/ui/2010/UIModel/application/ui/menu" xmi:id="_FFaPsGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.e4.legacy.ide.application" contributorURI="platform:/plugin/org.eclipse.ui.workbench" selectedElement="_FFaPsWo6EfCBSKlQ60Nj_w" bindingContexts="_FFaPumo6EfCBSKlQ60Nj_w">
  <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;workbench>&#xD;&#xA;&lt;mruList/>&#xD;&#xA;&lt;/workbench>"/>
  <tags>activeSchemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
  <children xsi:type="basic:TrimmedWindow" xmi:id="_FFaPsWo6EfCBSKlQ60Nj_w" elementId="IDEWindow" contributorURI="platform:/plugin/org.eclipse.ui.workbench" selectedElement="_FU4PEmo6EfCBSKlQ60Nj_w" x="132" y="132" width="1024" height="768">
    <persistedState key="coolBarVisible" value="true"/>
    <persistedState key="perspectiveBarVisible" value="true"/>
    <persistedState key="isRestored" value="true"/>
    <persistedState key="show_in_time" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;show_in_time/>"/>
    <persistedState key="workingSets" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;workingSets/>"/>
    <persistedState key="aggregateWorkingSetId" value="Aggregate for window 1753545816063"/>
    <tags>topLevel</tags>
    <tags>shellMaximized</tags>
    <children xsi:type="basic:PartSashContainer" xmi:id="_FU4PEmo6EfCBSKlQ60Nj_w" selectedElement="_FU42IGo6EfCBSKlQ60Nj_w" horizontal="true">
      <children xsi:type="advanced:PerspectiveStack" xmi:id="_FU42IGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.ide.perspectivestack" containerData="7500" selectedElement="_FaS6kmo6EfCBSKlQ60Nj_w">
        <children xsi:type="advanced:Perspective" xmi:id="_FaS6kmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.CPerspective" selectedElement="_FaS6k2o6EfCBSKlQ60Nj_w" label="C/C++" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/c_pers.gif">
          <persistedState key="persp.hiddenItems" value="persp.hideToolbarSC:print,persp.hideToolbarSC:org.eclipse.ui.edit.undo,persp.hideToolbarSC:org.eclipse.ui.edit.redo,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,"/>
          <tags>persp.actionSet:com.st.stm32cube.ide.mcu.informationcenter.actionSet3</tags>
          <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.text.quicksearch.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.annotationNavigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo</tags>
          <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.SearchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.CElementCreationActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.NavigateActionSet</tags>
          <tags>persp.viewSC:org.eclipse.ui.console.ConsoleView</tags>
          <tags>persp.viewSC:org.eclipse.search.ui.views.SearchView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ContentOutline</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProblemView</tags>
          <tags>persp.viewSC:org.eclipse.cdt.ui.CView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.PropertySheet</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.TaskList</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.ConvertToMakeWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewMakeFromExisting</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizard.project</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewSourceFolderCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewFolderCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewSourceFileCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewHeaderFileCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewFileCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewClassCreationWizard</tags>
          <tags>persp.perspSC:org.eclipse.debug.ui.DebugPerspective</tags>
          <tags>persp.perspSC:org.eclipse.team.ui.TeamSynchronizingPerspective</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.launchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.buildConfigActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.NavigationActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.OpenActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.CodingActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.presentation</tags>
          <tags>persp.showIn:org.eclipse.cdt.ui.includeBrowser</tags>
          <tags>persp.showIn:org.eclipse.cdt.ui.CView</tags>
          <tags>persp.showIn:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.viewSC:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.viewSC:org.eclipse.cdt.ui.includeBrowser</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.breakpointActionSet</tags>
          <tags>persp.newWizSC:com.st.stm32cube.common.projectcreation.ui.stm32projectwizard</tags>
          <tags>persp.newWizSC:com.st.stm32cube.common.projectcreation.ui.stm32projectfromiocwizard</tags>
          <tags>persp.viewSC:org.eclipse.cdt.make.ui.views.MakeView</tags>
          <tags>persp.actionSet:org.eclipse.cdt.make.ui.makeTargetActionSet</tags>
          <tags>persp.showIn:org.eclipse.cdt.codan.internal.ui.views.ProblemDetails</tags>
          <tags>persp.viewSC:org.eclipse.cdt.codan.internal.ui.views.ProblemDetails</tags>
          <tags>persp.viewSC:com.st.stm32cube.ide.mcu.buildanalyzer.view</tags>
          <tags>persp.viewSC:com.st.stm32cube.ide.mcu.stackanalyzer.stackanalyzer.view</tags>
          <tags>persp.newWizSC:com.st.stm32cube.ide.cmake.newwizard</tags>
          <tags>persp.viewSC:com.st.stm32cube.ide.mcu.cyclomaticcomplexity.view</tags>
          <tags>persp.viewSC:com.st.stm32cube.ide.mcu.sfrview</tags>
          <children xsi:type="basic:PartSashContainer" xmi:id="_FaS6k2o6EfCBSKlQ60Nj_w" visible="false" selectedElement="_FaS6mGo6EfCBSKlQ60Nj_w" horizontal="true">
            <children xsi:type="basic:PartStack" xmi:id="_FaS6lGo6EfCBSKlQ60Nj_w" elementId="topLeft" toBeRendered="false" containerData="2500">
              <children xsi:type="advanced:Placeholder" xmi:id="_FaS6lWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.navigator.ProjectExplorer" toBeRendered="false" ref="_FaOCEGo6EfCBSKlQ60Nj_w" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:General</tags>
              </children>
              <children xsi:type="advanced:Placeholder" xmi:id="_FaS6lmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.CView" toBeRendered="false" ref="_FaOpIGo6EfCBSKlQ60Nj_w" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:C/C++</tags>
              </children>
              <children xsi:type="advanced:Placeholder" xmi:id="_FaS6l2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.views.BookmarkView" toBeRendered="false" ref="_FaOpIWo6EfCBSKlQ60Nj_w" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:General</tags>
              </children>
            </children>
            <children xsi:type="basic:PartSashContainer" xmi:id="_FaS6mGo6EfCBSKlQ60Nj_w" visible="false" containerData="7500" selectedElement="_FaS6n2o6EfCBSKlQ60Nj_w">
              <children xsi:type="basic:PartSashContainer" xmi:id="_FaS6mWo6EfCBSKlQ60Nj_w" visible="false" containerData="7500" selectedElement="_FaS6m2o6EfCBSKlQ60Nj_w" horizontal="true">
                <children xsi:type="advanced:Placeholder" xmi:id="_FaS6mmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.editorss" visible="false" containerData="7500" ref="_FaKXsGo6EfCBSKlQ60Nj_w">
                  <tags>Minimized</tags>
                </children>
                <children xsi:type="basic:PartStack" xmi:id="_FaS6m2o6EfCBSKlQ60Nj_w" elementId="topRight" visible="false" containerData="2500" selectedElement="_FaS6nGo6EfCBSKlQ60Nj_w">
                  <tags>Minimized</tags>
                  <children xsi:type="advanced:Placeholder" xmi:id="_FaS6nGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.views.ContentOutline" ref="_FaPQMWo6EfCBSKlQ60Nj_w" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_FaS6nWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.views.minimap.MinimapView" toBeRendered="false" ref="_FaRscGo6EfCBSKlQ60Nj_w" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_FaS6nmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.make.ui.views.MakeView" ref="_FaSTgGo6EfCBSKlQ60Nj_w" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Make</tags>
                  </children>
                </children>
              </children>
              <children xsi:type="basic:PartSashContainer" xmi:id="_FaS6n2o6EfCBSKlQ60Nj_w" visible="false" containerData="2500" selectedElement="_FaS6oGo6EfCBSKlQ60Nj_w" horizontal="true">
                <children xsi:type="basic:PartStack" xmi:id="_FaS6oGo6EfCBSKlQ60Nj_w" elementId="bottom" visible="false" containerData="5000" selectedElement="_FaS6oWo6EfCBSKlQ60Nj_w">
                  <tags>Minimized</tags>
                  <children xsi:type="advanced:Placeholder" xmi:id="_FaS6oWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.views.ProblemView" ref="_FaOpImo6EfCBSKlQ60Nj_w" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_FaS6omo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.views.TaskList" ref="_FaOpI2o6EfCBSKlQ60Nj_w" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_FaS6o2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.console.ConsoleView" ref="_FaOpJGo6EfCBSKlQ60Nj_w" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_FaS6pGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.views.PropertySheet" ref="_FaPQMGo6EfCBSKlQ60Nj_w" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                </children>
                <children xsi:type="basic:PartStack" xmi:id="_FaS6pWo6EfCBSKlQ60Nj_w" elementId="com.st.stm32cube.ide.mcu.buildanalyzer.viewMStack" visible="false" containerData="5000" selectedElement="_FaS6pmo6EfCBSKlQ60Nj_w">
                  <tags>Minimized</tags>
                  <children xsi:type="advanced:Placeholder" xmi:id="_FaS6pmo6EfCBSKlQ60Nj_w" elementId="com.st.stm32cube.ide.mcu.buildanalyzer.view" ref="_FaSTgWo6EfCBSKlQ60Nj_w" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:C/C++</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_FaS6p2o6EfCBSKlQ60Nj_w" elementId="com.st.stm32cube.ide.mcu.stackanalyzer.stackanalyzer.view" ref="_FaS6kGo6EfCBSKlQ60Nj_w" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:C/C++</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_FaS6qGo6EfCBSKlQ60Nj_w" elementId="com.st.stm32cube.ide.mcu.cyclomaticcomplexity.view" ref="_FaS6kWo6EfCBSKlQ60Nj_w" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:C/C++</tags>
                  </children>
                </children>
              </children>
            </children>
          </children>
        </children>
      </children>
      <children xsi:type="basic:PartStack" xmi:id="_FU42IWo6EfCBSKlQ60Nj_w" elementId="stickyFolderRight" toBeRendered="false" containerData="2500">
        <tags>active</tags>
        <children xsi:type="advanced:Placeholder" xmi:id="_FU42Imo6EfCBSKlQ60Nj_w" elementId="org.eclipse.help.ui.HelpView" toBeRendered="false" ref="_FU3oAGo6EfCBSKlQ60Nj_w" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:Help</tags>
        </children>
        <children xsi:type="advanced:Placeholder" xmi:id="_FU42I2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.internal.introview" toBeRendered="false" ref="_FU4PEGo6EfCBSKlQ60Nj_w" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:General</tags>
        </children>
        <children xsi:type="advanced:Placeholder" xmi:id="_FU42JGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" toBeRendered="false" ref="_FU4PEWo6EfCBSKlQ60Nj_w" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:Help</tags>
        </children>
      </children>
    </children>
    <sharedElements xsi:type="basic:Part" xmi:id="_FU3oAGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.help.ui.HelpView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.help.ui.internal.views.HelpView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.help.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_FU4PEGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.internal.introview" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Information Center" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.ViewIntroAdapterPart"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view>&#xD;&#xA;&lt;presentation currentPage=&quot;file:///C:/ST/STM32CubeIDE/STM32CubeIDE/configuration/org.eclipse.osgi/503/0/.cp/welcome/index.html&quot; restore=&quot;true&quot;/>&#xD;&#xA;&lt;standbyPart/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <tags>active</tags>
      <menus xmi:id="_FkVAUGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.internal.introview">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_FkVAUWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.internal.introview" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_FU4PEWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.cheatsheets.views.CheatSheetView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.cheatsheets"/>
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="advanced:Area" xmi:id="_FaKXsGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.editorss">
      <children xsi:type="basic:PartStack" xmi:id="_FaK-wGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.e4.primaryDataStack">
        <tags>EditorStack</tags>
        <tags>org.eclipse.e4.primaryDataStack</tags>
      </children>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_FaOCEGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.navigator.ProjectExplorer" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.navigator.resources.ProjectExplorer"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.navigator.resources"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view CommonNavigator.LINKING_ENABLED=&quot;0&quot; org.eclipse.cdt.ui.cview.groupincludes=&quot;false&quot; org.eclipse.cdt.ui.cview.groupmacros=&quot;false&quot; org.eclipse.cdt.ui.editor.CUChildren=&quot;true&quot; org.eclipse.ui.navigator.resources.workingSets.showTopLevelWorkingSets=&quot;0&quot;>&#xD;&#xA;&lt;lastRecentlyUsedFilters/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_FczcAGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.navigator.ProjectExplorer">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_FczcAWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.navigator.ProjectExplorer" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_FaOpIGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.CView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="C/C++ Projects" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/cview.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.cview.CView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
      <tags>View</tags>
      <tags>categoryTag:C/C++</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_FaOpIWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.views.BookmarkView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.BookmarksView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_FaOpImo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.views.ProblemView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.ProblemsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view PRIMARY_SORT_FIELD=&quot;org.eclipse.ui.ide.severityAndDescriptionField&quot; categoryGroup=&quot;org.eclipse.ui.ide.severity&quot; markerContentGenerator=&quot;org.eclipse.ui.ide.problemsGenerator&quot;>&#xD;&#xA;&lt;columnWidths org.eclipse.ui.ide.locationField=&quot;90&quot; org.eclipse.ui.ide.markerType=&quot;90&quot; org.eclipse.ui.ide.pathField=&quot;120&quot; org.eclipse.ui.ide.resourceField=&quot;90&quot; org.eclipse.ui.ide.severityAndDescriptionField=&quot;300&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.severityAndDescriptionField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.resourceField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.pathField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.locationField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.markerType&quot;/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_FgDkUGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.views.ProblemView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_FgDkUWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.views.ProblemView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_FaOpI2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.views.TaskList" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Tasks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/tasks_tsk.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.TasksView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view PRIMARY_SORT_FIELD=&quot;org.eclipse.ui.ide.completionField&quot; categoryGroup=&quot;none&quot; markerContentGenerator=&quot;org.eclipse.ui.ide.tasksGenerator&quot;>&#xD;&#xA;&lt;columnWidths org.eclipse.ui.ide.completionField=&quot;40&quot; org.eclipse.ui.ide.descriptionField=&quot;300&quot; org.eclipse.ui.ide.locationField=&quot;90&quot; org.eclipse.ui.ide.markerType=&quot;90&quot; org.eclipse.ui.ide.pathField=&quot;120&quot; org.eclipse.ui.ide.priorityField=&quot;30&quot; org.eclipse.ui.ide.resourceField=&quot;90&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.completionField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.priorityField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.descriptionField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.resourceField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.pathField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.locationField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.markerType&quot;/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_FnBH8Go6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.views.TaskList">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_FnBH8Wo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.views.TaskList" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_FaOpJGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.console.ConsoleView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.console.ConsoleView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.console"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_Fn7G4Go6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.console.ConsoleView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_Fn7G4Wo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.console.ConsoleView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_FaPQMGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.views.PropertySheet" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Properties" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/prop_ps.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.properties.PropertySheet"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_FocrUGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.views.PropertySheet">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_FocrUWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.views.PropertySheet" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_FaPQMWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.views.ContentOutline" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.contentoutline.ContentOutline"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_Ffe8kGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.views.ContentOutline">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_Ffe8kWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.views.ContentOutline" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_FaRscGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.views.minimap.MinimapView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Minimap" iconURI="platform:/plugin/org.eclipse.ui.workbench.texteditor/icons/full/eview16/minimap.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.minimap.MinimapView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.workbench.texteditor"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_FaSTgGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.make.ui.views.MakeView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Build Targets" iconURI="platform:/plugin/org.eclipse.cdt.make.ui/icons/view16/make_target.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.make.ui.views.MakeView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.make.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view horizontalPosition=&quot;0&quot; verticalPosition=&quot;0&quot;/>"/>
      <tags>View</tags>
      <tags>categoryTag:Make</tags>
      <menus xmi:id="_FpLEEGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.make.ui.views.MakeView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_FpLEEWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.make.ui.views.MakeView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_FaSTgWo6EfCBSKlQ60Nj_w" elementId="com.st.stm32cube.ide.mcu.buildanalyzer.view" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Build Analyzer" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.buildanalyzer/icons/view_icon.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="elf_analyzer.ElfAnalyzerView"/>
      <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.buildanalyzer"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:C/C++</tags>
      <menus xmi:id="_Fg0ZUGo6EfCBSKlQ60Nj_w" elementId="com.st.stm32cube.ide.mcu.buildanalyzer.view">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_Fg0ZUWo6EfCBSKlQ60Nj_w" elementId="com.st.stm32cube.ide.mcu.buildanalyzer.view" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_FaS6kGo6EfCBSKlQ60Nj_w" elementId="com.st.stm32cube.ide.mcu.stackanalyzer.stackanalyzer.view" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Static Stack Analyzer" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.stackanalyzer/icons/view_icon.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.stackanalyzer.ui.StackAnalyzerView"/>
      <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.stackanalyzer"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:C/C++</tags>
      <menus xmi:id="_Fp0kUGo6EfCBSKlQ60Nj_w" elementId="com.st.stm32cube.ide.mcu.stackanalyzer.stackanalyzer.view">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_Fp0kUWo6EfCBSKlQ60Nj_w" elementId="com.st.stm32cube.ide.mcu.stackanalyzer.stackanalyzer.view" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_FaS6kWo6EfCBSKlQ60Nj_w" elementId="com.st.stm32cube.ide.mcu.cyclomaticcomplexity.view" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Cyclomatic Complexity" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.cyclomaticcomplexity/icons/algorithm.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.cyclomaticcomplexity.CyclomaticView"/>
      <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.cyclomaticcomplexity"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:C/C++</tags>
      <menus xmi:id="_FqfSsGo6EfCBSKlQ60Nj_w" elementId="com.st.stm32cube.ide.mcu.cyclomaticcomplexity.view">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_FqfSsWo6EfCBSKlQ60Nj_w" elementId="com.st.stm32cube.ide.mcu.cyclomaticcomplexity.view" visible="false"/>
    </sharedElements>
    <trimBars xmi:id="_FFaPsmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.main.toolbar" contributorURI="platform:/plugin/org.eclipse.ui.workbench">
      <children xsi:type="menu:ToolBar" xmi:id="_FX_NcGo6EfCBSKlQ60Nj_w" elementId="group.file" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_FX_NcWo6EfCBSKlQ60Nj_w" elementId="group.file" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_FX_0gGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.workbench.file">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_SZJ9sGo6EfCcdME1c210Lg" elementId="print" visible="false" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/print_edit.png" tooltip="Print" command="_FFvm4Go6EfCBSKlQ60Nj_w"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_FX_0gWo6EfCBSKlQ60Nj_w" elementId="group.edit" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_FX_0gmo6EfCBSKlQ60Nj_w" elementId="group.edit" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_FX_0g2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.workbench.edit">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_SZJ9tmo6EfCcdME1c210Lg" elementId="undo" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/undo_edit.png" tooltip="Undo" enabled="false" command="_FFvAOWo6EfCBSKlQ60Nj_w"/>
        <children xsi:type="menu:HandledToolItem" xmi:id="_SZKkwGo6EfCcdME1c210Lg" elementId="redo" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/redo_edit.png" tooltip="Redo" enabled="false" command="_FFvncmo6EfCBSKlQ60Nj_w"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_FX_0hGo6EfCBSKlQ60Nj_w" elementId="additions" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_FX_0hWo6EfCBSKlQ60Nj_w" elementId="additions" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_Fa4wcGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.CElementCreationActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_FbaU4Go6EfCBSKlQ60Nj_w" elementId="org.eclipse.debug.ui.launchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_FbIBAGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.search.searchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_FatxUGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.actionSet.presentation">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_FX_0hmo6EfCBSKlQ60Nj_w" elementId="group.nav" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_FX_0h2o6EfCBSKlQ60Nj_w" elementId="group.nav" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_FX_0iGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.workbench.navigate">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_SZLL0Go6EfCcdME1c210Lg" elementId="org.eclipse.ui.window.pinEditor" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/pin_editor.png" tooltip="Pin Editor" enabled="false" type="Check" command="_FFu_72o6EfCBSKlQ60Nj_w"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_FX_0iWo6EfCBSKlQ60Nj_w" elementId="group.editor" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_FX_0imo6EfCBSKlQ60Nj_w" elementId="group.editor" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_FX_0i2o6EfCBSKlQ60Nj_w" elementId="group.help" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_FX_0jGo6EfCBSKlQ60Nj_w" elementId="group.help" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_FX_0jWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.workbench.help" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_FZDkcGo6EfCBSKlQ60Nj_w" elementId="PerspectiveSpacer" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
        <tags>stretch</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_FZELgGo6EfCBSKlQ60Nj_w" elementId="PerspectiveSwitcher" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.e4.ui.workbench.addons.perspectiveswitcher.PerspectiveSwitcher">
        <tags>Draggable</tags>
        <tags>HIDEABLE</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_FFaPs2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.trim.status" contributorURI="platform:/plugin/org.eclipse.ui.workbench" side="Bottom">
      <children xsi:type="menu:ToolControl" xmi:id="_FFaPtGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.StatusLine" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>stretch</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_FFaPtWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.HeapStatus" contributorURI="platform:/plugin/org.eclipse.ui.workbench" toBeRendered="false" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_FFaPtmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.ProgressBar" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_FFaPt2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.trim.vertical1" contributorURI="platform:/plugin/org.eclipse.ui.workbench" side="Left">
      <children xsi:type="menu:ToolControl" xmi:id="_Flxx0Go6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.ide.perspectivestack(minimized)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_JY76YGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.editorss(IDEWindow).(org.eclipse.cdt.ui.CPerspective)" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_KBZokGo6EfCBSKlQ60Nj_w" elementId="bottom(IDEWindow).(org.eclipse.cdt.ui.CPerspective)" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
        <tags>Draggable</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_FFaPuGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.trim.vertical2" contributorURI="platform:/plugin/org.eclipse.ui.workbench" side="Right">
      <children xsi:type="menu:ToolControl" xmi:id="_Is-zUGo6EfCBSKlQ60Nj_w" elementId="topRight(IDEWindow).(org.eclipse.cdt.ui.CPerspective)" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_J0oX4Go6EfCBSKlQ60Nj_w" elementId="com.st.stm32cube.ide.mcu.buildanalyzer.viewMStack(IDEWindow).(org.eclipse.cdt.ui.CPerspective)" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
        <tags>Draggable</tags>
      </children>
    </trimBars>
  </children>
  <bindingTables xmi:id="_FFaPuWo6EfCBSKlQ60Nj_w" contributorURI="platform:/plugin/org.eclipse.ui.workbench" bindingContext="_FFaPumo6EfCBSKlQ60Nj_w">
    <bindings xmi:id="_FGLrzGo6EfCBSKlQ60Nj_w" keySequence="CTRL+1" command="_FFu_6Go6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGMS4mo6EfCBSKlQ60Nj_w" keySequence="CTRL+SHIFT+L" command="_FFvnJ2o6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGMS7Go6EfCBSKlQ60Nj_w" keySequence="CTRL+SPACE" command="_FFvAIGo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGM59Go6EfCBSKlQ60Nj_w" keySequence="CTRL+V" command="_FFtx3mo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGNg8Wo6EfCBSKlQ60Nj_w" keySequence="CTRL+SHIFT+SPACE" command="_FFvACmo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGNg8mo6EfCBSKlQ60Nj_w" keySequence="CTRL+A" command="_FFvnRGo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGNg-Wo6EfCBSKlQ60Nj_w" keySequence="CTRL+C" command="_FFtxtWo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGNhDmo6EfCBSKlQ60Nj_w" keySequence="CTRL+X" command="_FFvAQWo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGNhD2o6EfCBSKlQ60Nj_w" keySequence="CTRL+Y" command="_FFvncmo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGNhEmo6EfCBSKlQ60Nj_w" keySequence="CTRL+Z" command="_FFvAOWo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOID2o6EfCBSKlQ60Nj_w" keySequence="ALT+PAGE_UP" command="_FFwOAWo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOIEGo6EfCBSKlQ60Nj_w" keySequence="ALT+PAGE_DOWN" command="_FFuZEWo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOIE2o6EfCBSKlQ60Nj_w" keySequence="SHIFT+INSERT" command="_FFtx3mo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOIGGo6EfCBSKlQ60Nj_w" keySequence="ALT+F11" command="_FFuY52o6EfCBSKlQ60Nj_w">
      <tags>platform:win32</tags>
    </bindings>
    <bindings xmi:id="_FGOvIGo6EfCBSKlQ60Nj_w" keySequence="CTRL+F10" command="_FFuYyWo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOvJWo6EfCBSKlQ60Nj_w" keySequence="CTRL+INSERT" command="_FFtxtWo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOvMWo6EfCBSKlQ60Nj_w" keySequence="CTRL+PAGE_UP" command="_FFvm-2o6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOvMmo6EfCBSKlQ60Nj_w" keySequence="CTRL+PAGE_DOWN" command="_FFu_8Go6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOvNGo6EfCBSKlQ60Nj_w" keySequence="ALT+SHIFT+F3" command="_FFvm7Wo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOvOWo6EfCBSKlQ60Nj_w" keySequence="SHIFT+DEL" command="_FFvAQWo6EfCBSKlQ60Nj_w"/>
  </bindingTables>
  <bindingTables xmi:id="_FGE-EGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.textEditorScope" bindingContext="_FFz4Umo6EfCBSKlQ60Nj_w">
    <bindings xmi:id="_FGLEsGo6EfCBSKlQ60Nj_w" keySequence="CTRL+SHIFT+CR" command="_FFvm62o6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGLrwmo6EfCBSKlQ60Nj_w" keySequence="CTRL+BS" command="_FFtKqWo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGLry2o6EfCBSKlQ60Nj_w" keySequence="CTRL+SHIFT+Q" command="_FFu_1Wo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGMS3mo6EfCBSKlQ60Nj_w" keySequence="CTRL+SHIFT+J" command="_FFuZLmo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGMS4Go6EfCBSKlQ60Nj_w" keySequence="CTRL++" command="_FFuY5Wo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGMS5Wo6EfCBSKlQ60Nj_w" keySequence="CTRL+-" command="_FFvnKmo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGM55mo6EfCBSKlQ60Nj_w" keySequence="ALT+CTRL+J" command="_FFu_4mo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGM57mo6EfCBSKlQ60Nj_w" keySequence="ALT+SHIFT+A" command="_FFtx02o6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGM5-Wo6EfCBSKlQ60Nj_w" keySequence="CTRL+J" command="_FFuYz2o6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGM5_Wo6EfCBSKlQ60Nj_w" keySequence="CTRL+L" command="_FFvANGo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGNg_Wo6EfCBSKlQ60Nj_w" keySequence="CTRL+D" command="_FFuY2Wo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGNhCWo6EfCBSKlQ60Nj_w" keySequence="CTRL+=" command="_FFuY5Wo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGNhDGo6EfCBSKlQ60Nj_w" keySequence="ALT+SHIFT+Y" command="_FFtKoWo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGNhFWo6EfCBSKlQ60Nj_w" keySequence="CTRL+SHIFT+DEL" command="_FFvAJWo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGNhFmo6EfCBSKlQ60Nj_w" keySequence="CTRL+SHIFT+X" command="_FFtxwGo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGNhF2o6EfCBSKlQ60Nj_w" keySequence="CTRL+SHIFT+Y" command="_FFvnKWo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOIAWo6EfCBSKlQ60Nj_w" keySequence="CTRL+DEL" command="_FFvANmo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOIBmo6EfCBSKlQ60Nj_w" keySequence="ALT+ARROW_UP" command="_FFwN-2o6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOICGo6EfCBSKlQ60Nj_w" keySequence="ALT+ARROW_DOWN" command="_FFuZHWo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOIEWo6EfCBSKlQ60Nj_w" keySequence="SHIFT+END" command="_FFvnMGo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOIGWo6EfCBSKlQ60Nj_w" keySequence="SHIFT+HOME" command="_FFvnFWo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOIH2o6EfCBSKlQ60Nj_w" keySequence="END" command="_FFvnB2o6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOIIGo6EfCBSKlQ60Nj_w" keySequence="INSERT" command="_FFuYwWo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOIImo6EfCBSKlQ60Nj_w" keySequence="F2" command="_FFu_8mo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOIMGo6EfCBSKlQ60Nj_w" keySequence="HOME" command="_FFvnMmo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOvEGo6EfCBSKlQ60Nj_w" keySequence="ALT+CTRL+ARROW_UP" command="_FFvnX2o6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOvEWo6EfCBSKlQ60Nj_w" keySequence="ALT+CTRL+ARROW_DOWN" command="_FFvnUWo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOvFGo6EfCBSKlQ60Nj_w" keySequence="CTRL+SHIFT+INSERT" command="_FFuZFWo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOvGWo6EfCBSKlQ60Nj_w" keySequence="CTRL+SHIFT+ARROW_LEFT" command="_FFvnM2o6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOvGmo6EfCBSKlQ60Nj_w" keySequence="CTRL+SHIFT+ARROW_RIGHT" command="_FFuZIGo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOvIWo6EfCBSKlQ60Nj_w" keySequence="CTRL+F10" command="_FFvm52o6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOvI2o6EfCBSKlQ60Nj_w" keySequence="CTRL+END" command="_FFuZI2o6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOvLWo6EfCBSKlQ60Nj_w" keySequence="CTRL+ARROW_UP" command="_FFuY-Wo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOvLmo6EfCBSKlQ60Nj_w" keySequence="CTRL+ARROW_DOWN" command="_FFwOE2o6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOvL2o6EfCBSKlQ60Nj_w" keySequence="CTRL+ARROW_LEFT" command="_FFtKqmo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOvMGo6EfCBSKlQ60Nj_w" keySequence="CTRL+ARROW_RIGHT" command="_FFu_0mo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOvM2o6EfCBSKlQ60Nj_w" keySequence="CTRL+HOME" command="_FFtx3Wo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOvNWo6EfCBSKlQ60Nj_w" keySequence="CTRL+NUMPAD_MULTIPLY" command="_FFu_1Go6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOvNmo6EfCBSKlQ60Nj_w" keySequence="CTRL+NUMPAD_ADD" command="_FFvnTGo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOvN2o6EfCBSKlQ60Nj_w" keySequence="CTRL+NUMPAD_SUBTRACT" command="_FFvm6Wo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOvOGo6EfCBSKlQ60Nj_w" keySequence="CTRL+NUMPAD_DIVIDE" command="_FFuY_Go6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOvPWo6EfCBSKlQ60Nj_w" keySequence="CTRL+SHIFT+NUMPAD_MULTIPLY" command="_FFu_4Go6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOvP2o6EfCBSKlQ60Nj_w" keySequence="CTRL+SHIFT+NUMPAD_DIVIDE" command="_FFuYwmo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGPWMGo6EfCBSKlQ60Nj_w" keySequence="ALT+/" command="_FFvnNWo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGPWMmo6EfCBSKlQ60Nj_w" keySequence="SHIFT+CR" command="_FFvnMWo6EfCBSKlQ60Nj_w"/>
  </bindingTables>
  <bindingTables xmi:id="_FGLrwGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.cEditorScope" bindingContext="_FFz4ZGo6EfCBSKlQ60Nj_w">
    <bindings xmi:id="_FGLrwWo6EfCBSKlQ60Nj_w" keySequence="ALT+CTRL+SHIFT+C" command="_FFwOFWo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGLrw2o6EfCBSKlQ60Nj_w" keySequence="CTRL+TAB" command="_FFwOD2o6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGLrxmo6EfCBSKlQ60Nj_w" keySequence="CTRL+SHIFT+P" command="_FFvnCWo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGLr0mo6EfCBSKlQ60Nj_w" keySequence="CTRL+SHIFT+T" command="_FFvACWo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGMS1Wo6EfCBSKlQ60Nj_w" keySequence="CTRL+7" command="_FFtx4Wo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGMS2mo6EfCBSKlQ60Nj_w" keySequence="CTRL+SHIFT+H" command="_FFuY0mo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGMS5mo6EfCBSKlQ60Nj_w" keySequence="CTRL+SHIFT+N" command="_FFu_0Wo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGMS6mo6EfCBSKlQ60Nj_w" keySequence="CTRL+/" command="_FFtx4Wo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGMS62o6EfCBSKlQ60Nj_w" keySequence="CTRL+SHIFT+O" command="_FFvm5mo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGMS7Wo6EfCBSKlQ60Nj_w" keySequence="CTRL+SHIFT+A" command="_FFvm7mo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGMS72o6EfCBSKlQ60Nj_w" keySequence="ALT+CTRL+S" command="_FFvneGo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGMS8Go6EfCBSKlQ60Nj_w" keySequence="CTRL+#" command="_FFu__mo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGMS8Wo6EfCBSKlQ60Nj_w" keySequence="CTRL+SHIFT+C" command="_FFtx4Wo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGMS9Go6EfCBSKlQ60Nj_w" keySequence="CTRL+SHIFT+F" command="_FFwOEGo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGMS9Wo6EfCBSKlQ60Nj_w" keySequence="CTRL+SHIFT+G" command="_FFtKpGo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGMS-Go6EfCBSKlQ60Nj_w" keySequence="ALT+CTRL+H" command="_FFu_62o6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGM54Go6EfCBSKlQ60Nj_w" keySequence="ALT+CTRL+I" command="_FFtx5Wo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGM58mo6EfCBSKlQ60Nj_w" keySequence="CTRL+T" command="_FFuZJ2o6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGM5-Go6EfCBSKlQ60Nj_w" keySequence="CTRL+I" command="_FFuZLGo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGM6Amo6EfCBSKlQ60Nj_w" keySequence="CTRL+SHIFT+/" command="_FFvnS2o6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGNg8Go6EfCBSKlQ60Nj_w" keySequence="CTRL+O" command="_FFtxyGo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGNg82o6EfCBSKlQ60Nj_w" keySequence="ALT+SHIFT+R" command="_FFvAKmo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGNg9mo6EfCBSKlQ60Nj_w" keySequence="ALT+SHIFT+S" command="_FFuYymo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGNg-mo6EfCBSKlQ60Nj_w" keySequence="ALT+SHIFT+T" command="_FFvAAWo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGNhAmo6EfCBSKlQ60Nj_w" keySequence="CTRL+G" command="_FFvnbWo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGNhBmo6EfCBSKlQ60Nj_w" keySequence="ALT+SHIFT+L" command="_FFvnU2o6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGNhB2o6EfCBSKlQ60Nj_w" keySequence="ALT+SHIFT+M" command="_FFtx1Go6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGNhCGo6EfCBSKlQ60Nj_w" keySequence="CTRL+=" command="_FFu__mo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGNhC2o6EfCBSKlQ60Nj_w" keySequence="ALT+SHIFT+O" command="_FFvAQ2o6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGNhDWo6EfCBSKlQ60Nj_w" keySequence="ALT+SHIFT+Z" command="_FFvARGo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOIAGo6EfCBSKlQ60Nj_w" keySequence="CTRL+SHIFT+\" command="_FFtx4mo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOIJGo6EfCBSKlQ60Nj_w" keySequence="F3" command="_FFwOGWo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOIKmo6EfCBSKlQ60Nj_w" keySequence="F4" command="_FFvnRWo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOvF2o6EfCBSKlQ60Nj_w" keySequence="CTRL+SHIFT+ARROW_UP" command="_FFvALmo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOvGGo6EfCBSKlQ60Nj_w" keySequence="CTRL+SHIFT+ARROW_DOWN" command="_FFuZE2o6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOvHGo6EfCBSKlQ60Nj_w" keySequence="ALT+SHIFT+ARROW_UP" command="_FFvnGGo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOvHmo6EfCBSKlQ60Nj_w" keySequence="ALT+SHIFT+ARROW_DOWN" command="_FFwOEWo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOvH2o6EfCBSKlQ60Nj_w" keySequence="ALT+SHIFT+ARROW_LEFT" command="_FFvAIWo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOvImo6EfCBSKlQ60Nj_w" keySequence="ALT+SHIFT+ARROW_RIGHT" command="_FFvnHmo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOvQ2o6EfCBSKlQ60Nj_w" keySequence="ALT+C" command="_FFtx42o6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGPWMWo6EfCBSKlQ60Nj_w" keySequence="SHIFT+TAB" command="_FFuY1Go6EfCBSKlQ60Nj_w"/>
  </bindingTables>
  <bindingTables xmi:id="_FGLrxGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.contexts.window" bindingContext="_FFaPu2o6EfCBSKlQ60Nj_w">
    <bindings xmi:id="_FGLrxWo6EfCBSKlQ60Nj_w" keySequence="ALT+CTRL+SHIFT+L" command="_FFtx62o6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGLryWo6EfCBSKlQ60Nj_w" keySequence="ALT+SHIFT+Q O" command="_FFuZAGo6EfCBSKlQ60Nj_w">
      <parameters xmi:id="_FGLrymo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ContentOutline"/>
    </bindings>
    <bindings xmi:id="_FGLrzWo6EfCBSKlQ60Nj_w" keySequence="ALT+CTRL+B" command="_FFuZCGo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGLrzmo6EfCBSKlQ60Nj_w" keySequence="CTRL+SHIFT+R" command="_FFwOFmo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGLrz2o6EfCBSKlQ60Nj_w" keySequence="ALT+SHIFT+Q Q" command="_FFuZAGo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGLr0Go6EfCBSKlQ60Nj_w" keySequence="CTRL+SHIFT+S" command="_FFuY3mo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGLr0Wo6EfCBSKlQ60Nj_w" keySequence="CTRL+3" command="_FFu_8Wo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGMS0Wo6EfCBSKlQ60Nj_w" keySequence="ALT+SHIFT+Q S" command="_FFuZAGo6EfCBSKlQ60Nj_w">
      <parameters xmi:id="_FGMS0mo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.search.ui.views.SearchView"/>
    </bindings>
    <bindings xmi:id="_FGMS1mo6EfCBSKlQ60Nj_w" keySequence="ALT+SHIFT+Q V" command="_FFuZAGo6EfCBSKlQ60Nj_w">
      <parameters xmi:id="_FGMS12o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.VariableView"/>
    </bindings>
    <bindings xmi:id="_FGMS2Go6EfCBSKlQ60Nj_w" keySequence="ALT+CTRL+G" command="_FFuY8mo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGMS2Wo6EfCBSKlQ60Nj_w" keySequence="CTRL+SHIFT+W" command="_FFvAQGo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGMS3Go6EfCBSKlQ60Nj_w" keySequence="ALT+SHIFT+Q H" command="_FFuZAGo6EfCBSKlQ60Nj_w">
      <parameters xmi:id="_FGMS3Wo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.cheatsheets.views.CheatSheetView"/>
    </bindings>
    <bindings xmi:id="_FGMS32o6EfCBSKlQ60Nj_w" keySequence="CTRL+SHIFT+K" command="_FFuY82o6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGMS4Wo6EfCBSKlQ60Nj_w" keySequence="CTRL+," command="_FFtx5Go6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGMS42o6EfCBSKlQ60Nj_w" keySequence="ALT+SHIFT+Q L" command="_FFuZAGo6EfCBSKlQ60Nj_w">
      <parameters xmi:id="_FGMS5Go6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.pde.runtime.LogView"/>
    </bindings>
    <bindings xmi:id="_FGMS52o6EfCBSKlQ60Nj_w" keySequence="CTRL+." command="_FFvnYmo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGMS7mo6EfCBSKlQ60Nj_w" keySequence="CTRL+SHIFT+B" command="_FFuY9Go6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGMS82o6EfCBSKlQ60Nj_w" keySequence="CTRL+SHIFT+E" command="_FFuZD2o6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGM54mo6EfCBSKlQ60Nj_w" keySequence="ALT+SHIFT+Q X" command="_FFuZAGo6EfCBSKlQ60Nj_w">
      <parameters xmi:id="_FGM542o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ProblemView"/>
    </bindings>
    <bindings xmi:id="_FGM55Go6EfCBSKlQ60Nj_w" keySequence="ALT+SHIFT+Q Y" command="_FFuZAGo6EfCBSKlQ60Nj_w">
      <parameters xmi:id="_FGM55Wo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.sync.views.SynchronizeView"/>
    </bindings>
    <bindings xmi:id="_FGM552o6EfCBSKlQ60Nj_w" keySequence="ALT+SHIFT+Q Z" command="_FFuZAGo6EfCBSKlQ60Nj_w">
      <parameters xmi:id="_FGM56Go6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.ui.GenericHistoryView"/>
    </bindings>
    <bindings xmi:id="_FGM57Go6EfCBSKlQ60Nj_w" keySequence="CTRL+P" command="_FFvm4Go6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGM57Wo6EfCBSKlQ60Nj_w" keySequence="CTRL+Q" command="_FFvm9Wo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGM58Wo6EfCBSKlQ60Nj_w" keySequence="CTRL+S" command="_FFvnK2o6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGM59mo6EfCBSKlQ60Nj_w" keySequence="CTRL+W" command="_FFwN9Go6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGM592o6EfCBSKlQ60Nj_w" keySequence="CTRL+H" command="_FFvAHmo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGM5-mo6EfCBSKlQ60Nj_w" keySequence="CTRL+K" command="_FFuZCmo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGM5_mo6EfCBSKlQ60Nj_w" keySequence="CTRL+M" command="_FFvAF2o6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGM6AWo6EfCBSKlQ60Nj_w" keySequence="CTRL+N" command="_FFwN8Go6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGNg9Wo6EfCBSKlQ60Nj_w" keySequence="CTRL+B" command="_FFtx6Go6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGNg92o6EfCBSKlQ60Nj_w" keySequence="ALT+SHIFT+Q B" command="_FFuZAGo6EfCBSKlQ60Nj_w">
      <parameters xmi:id="_FGNg-Go6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.BreakpointView"/>
    </bindings>
    <bindings xmi:id="_FGNg-2o6EfCBSKlQ60Nj_w" keySequence="ALT+SHIFT+Q C" command="_FFuZAGo6EfCBSKlQ60Nj_w">
      <parameters xmi:id="_FGNg_Go6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.console.ConsoleView"/>
    </bindings>
    <bindings xmi:id="_FGNg_mo6EfCBSKlQ60Nj_w" keySequence="CTRL+E" command="_FFvAMmo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGNg_2o6EfCBSKlQ60Nj_w" keySequence="CTRL+F" command="_FFuY5Go6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGNhBWo6EfCBSKlQ60Nj_w" keySequence="ALT+SHIFT+W" command="_FFvnXmo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGNhCmo6EfCBSKlQ60Nj_w" keySequence="ALT+SHIFT+N" command="_FFvAPGo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGNhE2o6EfCBSKlQ60Nj_w" keySequence="CTRL+_" command="_FFvADmo6EfCBSKlQ60Nj_w">
      <parameters xmi:id="_FGNhFGo6EfCBSKlQ60Nj_w" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="true"/>
    </bindings>
    <bindings xmi:id="_FGNhGGo6EfCBSKlQ60Nj_w" keySequence="CTRL+{" command="_FFvADmo6EfCBSKlQ60Nj_w">
      <parameters xmi:id="_FGNhGWo6EfCBSKlQ60Nj_w" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="false"/>
    </bindings>
    <bindings xmi:id="_FGOIB2o6EfCBSKlQ60Nj_w" keySequence="SHIFT+F9" command="_FFvm42o6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOIC2o6EfCBSKlQ60Nj_w" keySequence="ALT+ARROW_LEFT" command="_FFuYzWo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOIDmo6EfCBSKlQ60Nj_w" keySequence="ALT+ARROW_RIGHT" command="_FFvm7Go6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOIFWo6EfCBSKlQ60Nj_w" keySequence="SHIFT+F5" command="_FFvnWWo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOIF2o6EfCBSKlQ60Nj_w" keySequence="ALT+F7" command="_FFtx32o6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOIHGo6EfCBSKlQ60Nj_w" keySequence="F9" command="_FFvnDWo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOIHWo6EfCBSKlQ60Nj_w" keySequence="F11" command="_FFvnQWo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOIHmo6EfCBSKlQ60Nj_w" keySequence="F12" command="_FFvAI2o6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOIIWo6EfCBSKlQ60Nj_w" keySequence="F2" command="_FFtx5mo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOILWo6EfCBSKlQ60Nj_w" keySequence="F5" command="_FFvnCGo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOIMWo6EfCBSKlQ60Nj_w" keySequence="CTRL+SHIFT+F7" command="_FFvnQmo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOIMmo6EfCBSKlQ60Nj_w" keySequence="CTRL+SHIFT+F8" command="_FFvADWo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOvEmo6EfCBSKlQ60Nj_w" keySequence="ALT+CTRL+ARROW_LEFT" command="_FFvm9Wo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOvE2o6EfCBSKlQ60Nj_w" keySequence="ALT+CTRL+ARROW_RIGHT" command="_FFuY_mo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOvFWo6EfCBSKlQ60Nj_w" keySequence="CTRL+SHIFT+F4" command="_FFvAQGo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOvFmo6EfCBSKlQ60Nj_w" keySequence="CTRL+SHIFT+F6" command="_FFuY42o6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOvG2o6EfCBSKlQ60Nj_w" keySequence="CTRL+F7" command="_FFtxtmo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOvHWo6EfCBSKlQ60Nj_w" keySequence="CTRL+F8" command="_FFu_6mo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOvKWo6EfCBSKlQ60Nj_w" keySequence="CTRL+F4" command="_FFwN9Go6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOvK2o6EfCBSKlQ60Nj_w" keySequence="CTRL+F6" command="_FFuY62o6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOvLGo6EfCBSKlQ60Nj_w" keySequence="ALT+SHIFT+F7" command="_FFuZJmo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOvPGo6EfCBSKlQ60Nj_w" keySequence="CTRL+SHIFT+NUMPAD_MULTIPLY" command="_FFuY3Wo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOvPmo6EfCBSKlQ60Nj_w" keySequence="CTRL+SHIFT+NUMPAD_DIVIDE" command="_FFvAEmo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOvQGo6EfCBSKlQ60Nj_w" keySequence="DEL" command="_FFuY8Go6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGPWL2o6EfCBSKlQ60Nj_w" keySequence="ALT+-" command="_FFtxy2o6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGPWM2o6EfCBSKlQ60Nj_w" keySequence="ALT+CR" command="_FFu__2o6EfCBSKlQ60Nj_w"/>
  </bindingTables>
  <bindingTables xmi:id="_FGLrx2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.genericeditor.genericEditorContext" bindingContext="_FFz4W2o6EfCBSKlQ60Nj_w">
    <bindings xmi:id="_FGLryGo6EfCBSKlQ60Nj_w" keySequence="CTRL+SHIFT+P" command="_FFvnHGo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGMS92o6EfCBSKlQ60Nj_w" keySequence="CTRL+SHIFT+G" command="_FFvnBmo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOIKWo6EfCBSKlQ60Nj_w" keySequence="F3" command="_FFvm9mo6EfCBSKlQ60Nj_w"/>
  </bindingTables>
  <bindingTables xmi:id="_FGLr02o6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.cViewScope" bindingContext="_FFz4ZWo6EfCBSKlQ60Nj_w">
    <bindings xmi:id="_FGMS0Go6EfCBSKlQ60Nj_w" keySequence="CTRL+SHIFT+T" command="_FFvACWo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGMS22o6EfCBSKlQ60Nj_w" keySequence="CTRL+SHIFT+H" command="_FFuY0mo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGMS9mo6EfCBSKlQ60Nj_w" keySequence="CTRL+SHIFT+G" command="_FFtKpGo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGMS-Wo6EfCBSKlQ60Nj_w" keySequence="ALT+CTRL+H" command="_FFu_62o6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGM54Wo6EfCBSKlQ60Nj_w" keySequence="ALT+CTRL+I" command="_FFtx5Wo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGNg9Go6EfCBSKlQ60Nj_w" keySequence="ALT+SHIFT+R" command="_FFvAKmo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGNhA2o6EfCBSKlQ60Nj_w" keySequence="CTRL+G" command="_FFvnbWo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOIJ2o6EfCBSKlQ60Nj_w" keySequence="F3" command="_FFwOGWo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOIK2o6EfCBSKlQ60Nj_w" keySequence="F4" command="_FFvnRWo6EfCBSKlQ60Nj_w"/>
  </bindingTables>
  <bindingTables xmi:id="_FGMS02o6EfCBSKlQ60Nj_w" elementId="org.eclipse.tm.terminal.EditContext" bindingContext="_FFz4UGo6EfCBSKlQ60Nj_w">
    <bindings xmi:id="_FGMS1Go6EfCBSKlQ60Nj_w" keySequence="CTRL+SHIFT+V" command="_FFvnG2o6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGMS8mo6EfCBSKlQ60Nj_w" keySequence="CTRL+SHIFT+C" command="_FFu_9mo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOIBWo6EfCBSKlQ60Nj_w" keySequence="ALT+ARROW_UP" command="_FFtKqGo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOIDWo6EfCBSKlQ60Nj_w" keySequence="ALT+ARROW_RIGHT" command="_FFvnPmo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOIEmo6EfCBSKlQ60Nj_w" keySequence="SHIFT+INSERT" command="_FFvnG2o6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOvJGo6EfCBSKlQ60Nj_w" keySequence="CTRL+INSERT" command="_FFu_9mo6EfCBSKlQ60Nj_w"/>
  </bindingTables>
  <bindingTables xmi:id="_FGMS6Go6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.make.ui.makefileEditorScope" bindingContext="_FFz4Y2o6EfCBSKlQ60Nj_w">
    <bindings xmi:id="_FGMS6Wo6EfCBSKlQ60Nj_w" keySequence="CTRL+/" command="_FFuZBWo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOII2o6EfCBSKlQ60Nj_w" keySequence="F3" command="_FFuY12o6EfCBSKlQ60Nj_w"/>
  </bindingTables>
  <bindingTables xmi:id="_FGM56Wo6EfCBSKlQ60Nj_w" elementId="org.eclipse.debug.ui.memoryview" bindingContext="_FFz4Wmo6EfCBSKlQ60Nj_w">
    <bindings xmi:id="_FGM56mo6EfCBSKlQ60Nj_w" keySequence="ALT+CTRL+M" command="_FFvnQ2o6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGM562o6EfCBSKlQ60Nj_w" keySequence="ALT+CTRL+N" command="_FFvnUGo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGM582o6EfCBSKlQ60Nj_w" keySequence="CTRL+T" command="_FFu_-Wo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGM59Wo6EfCBSKlQ60Nj_w" keySequence="CTRL+W" command="_FFuYx2o6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGM6AGo6EfCBSKlQ60Nj_w" keySequence="CTRL+N" command="_FFuY6Go6EfCBSKlQ60Nj_w"/>
  </bindingTables>
  <bindingTables xmi:id="_FGM572o6EfCBSKlQ60Nj_w" elementId="org.eclipse.debug.ui.debugging" bindingContext="_FFz4XGo6EfCBSKlQ60Nj_w">
    <bindings xmi:id="_FGM58Go6EfCBSKlQ60Nj_w" keySequence="CTRL+R" command="_FFtxvGo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOIGmo6EfCBSKlQ60Nj_w" keySequence="F7" command="_FFvnaWo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOIG2o6EfCBSKlQ60Nj_w" keySequence="F8" command="_FFtx9Go6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOILGo6EfCBSKlQ60Nj_w" keySequence="F5" command="_FFtx9Wo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOILmo6EfCBSKlQ60Nj_w" keySequence="F6" command="_FFvnOWo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOvKGo6EfCBSKlQ60Nj_w" keySequence="CTRL+F2" command="_FFvAJmo6EfCBSKlQ60Nj_w"/>
  </bindingTables>
  <bindingTables xmi:id="_FGM5-2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" bindingContext="_FFz4XWo6EfCBSKlQ60Nj_w">
    <bindings xmi:id="_FGM5_Go6EfCBSKlQ60Nj_w" keySequence="CTRL+SHIFT+," command="_FFvm92o6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGM5_2o6EfCBSKlQ60Nj_w" keySequence="CTRL+SHIFT+." command="_FFvAFGo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGNhBGo6EfCBSKlQ60Nj_w" keySequence="CTRL+G" command="_FFvAFWo6EfCBSKlQ60Nj_w"/>
  </bindingTables>
  <bindingTables xmi:id="_FGNhAGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.context" bindingContext="_FFz4YGo6EfCBSKlQ60Nj_w">
    <bindings xmi:id="_FGNhAWo6EfCBSKlQ60Nj_w" keySequence="CTRL+G" command="_FFvndWo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOIL2o6EfCBSKlQ60Nj_w" keySequence="HOME" command="_FFu_9Go6EfCBSKlQ60Nj_w"/>
  </bindingTables>
  <bindingTables xmi:id="_FGNhEGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.debug.ui.console" bindingContext="_FFz4VGo6EfCBSKlQ60Nj_w">
    <bindings xmi:id="_FGNhEWo6EfCBSKlQ60Nj_w" keySequence="CTRL+Z" command="_FFvnXGo6EfCBSKlQ60Nj_w">
      <tags>platform:win32</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_FGOIAmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.debug.ui.debugging" bindingContext="_FFz4Zmo6EfCBSKlQ60Nj_w">
    <bindings xmi:id="_FGOIA2o6EfCBSKlQ60Nj_w" keySequence="SHIFT+F7" command="_FFuY1mo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOIBGo6EfCBSKlQ60Nj_w" keySequence="SHIFT+F8" command="_FFvADGo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOIFGo6EfCBSKlQ60Nj_w" keySequence="SHIFT+F5" command="_FFvnOmo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOIFmo6EfCBSKlQ60Nj_w" keySequence="SHIFT+F6" command="_FFtx6Wo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOvKmo6EfCBSKlQ60Nj_w" keySequence="CTRL+F5" command="_FFvnNmo6EfCBSKlQ60Nj_w"/>
  </bindingTables>
  <bindingTables xmi:id="_FGOICWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.macroExpansionHoverScope" bindingContext="_FFz4V2o6EfCBSKlQ60Nj_w">
    <bindings xmi:id="_FGOICmo6EfCBSKlQ60Nj_w" keySequence="ALT+ARROW_LEFT" command="_FFuZKWo6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOIDGo6EfCBSKlQ60Nj_w" keySequence="ALT+ARROW_RIGHT" command="_FFu_22o6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOIKGo6EfCBSKlQ60Nj_w" keySequence="F3" command="_FFwOGWo6EfCBSKlQ60Nj_w"/>
  </bindingTables>
  <bindingTables xmi:id="_FGOIJWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.asmEditorScope" bindingContext="_FFz4WWo6EfCBSKlQ60Nj_w">
    <bindings xmi:id="_FGOIJmo6EfCBSKlQ60Nj_w" keySequence="F3" command="_FFwOGWo6EfCBSKlQ60Nj_w"/>
  </bindingTables>
  <bindingTables xmi:id="_FGOvJmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.console.ConsoleView" bindingContext="_FFz4WGo6EfCBSKlQ60Nj_w">
    <bindings xmi:id="_FGOvJ2o6EfCBSKlQ60Nj_w" keySequence="CTRL+INSERT" command="_FFuY3Go6EfCBSKlQ60Nj_w"/>
  </bindingTables>
  <bindingTables xmi:id="_FGOvOmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.tm.terminal.TerminalContext" bindingContext="_FFz4Ymo6EfCBSKlQ60Nj_w">
    <bindings xmi:id="_FGOvO2o6EfCBSKlQ60Nj_w" keySequence="ALT+Y" command="_FFtxx2o6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOvQWo6EfCBSKlQ60Nj_w" keySequence="ALT+A" command="_FFtxx2o6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOvQmo6EfCBSKlQ60Nj_w" keySequence="ALT+B" command="_FFtxx2o6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGOvRGo6EfCBSKlQ60Nj_w" keySequence="ALT+C" command="_FFtxx2o6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGPWIGo6EfCBSKlQ60Nj_w" keySequence="ALT+D" command="_FFtxx2o6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGPWIWo6EfCBSKlQ60Nj_w" keySequence="ALT+E" command="_FFtxx2o6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGPWImo6EfCBSKlQ60Nj_w" keySequence="ALT+F" command="_FFtxx2o6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGPWI2o6EfCBSKlQ60Nj_w" keySequence="ALT+G" command="_FFtxx2o6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGPWJGo6EfCBSKlQ60Nj_w" keySequence="ALT+P" command="_FFtxx2o6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGPWJWo6EfCBSKlQ60Nj_w" keySequence="ALT+R" command="_FFtxx2o6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGPWJmo6EfCBSKlQ60Nj_w" keySequence="ALT+S" command="_FFtxx2o6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGPWJ2o6EfCBSKlQ60Nj_w" keySequence="ALT+T" command="_FFtxx2o6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGPWKGo6EfCBSKlQ60Nj_w" keySequence="ALT+V" command="_FFtxx2o6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGPWKWo6EfCBSKlQ60Nj_w" keySequence="ALT+W" command="_FFtxx2o6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGPWKmo6EfCBSKlQ60Nj_w" keySequence="ALT+H" command="_FFtxx2o6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGPWLWo6EfCBSKlQ60Nj_w" keySequence="ALT+L" command="_FFtxx2o6EfCBSKlQ60Nj_w"/>
    <bindings xmi:id="_FGPWLmo6EfCBSKlQ60Nj_w" keySequence="ALT+N" command="_FFtxx2o6EfCBSKlQ60Nj_w"/>
  </bindingTables>
  <bindingTables xmi:id="_FGPWK2o6EfCBSKlQ60Nj_w" elementId="com.st.stm32cube.common.mx.context" bindingContext="_FFz4YWo6EfCBSKlQ60Nj_w">
    <bindings xmi:id="_FGPWLGo6EfCBSKlQ60Nj_w" keySequence="ALT+K" command="_FFuZGGo6EfCBSKlQ60Nj_w"/>
  </bindingTables>
  <bindingTables xmi:id="_FaLl0Wo6EfCBSKlQ60Nj_w" bindingContext="_FaLl0Go6EfCBSKlQ60Nj_w"/>
  <bindingTables xmi:id="_FaLl02o6EfCBSKlQ60Nj_w" bindingContext="_FaLl0mo6EfCBSKlQ60Nj_w"/>
  <bindingTables xmi:id="_FaLl1Wo6EfCBSKlQ60Nj_w" bindingContext="_FaLl1Go6EfCBSKlQ60Nj_w"/>
  <bindingTables xmi:id="_FaLl12o6EfCBSKlQ60Nj_w" bindingContext="_FaLl1mo6EfCBSKlQ60Nj_w"/>
  <bindingTables xmi:id="_FaLl2Wo6EfCBSKlQ60Nj_w" bindingContext="_FaLl2Go6EfCBSKlQ60Nj_w"/>
  <bindingTables xmi:id="_FaMM4Wo6EfCBSKlQ60Nj_w" bindingContext="_FaMM4Go6EfCBSKlQ60Nj_w"/>
  <bindingTables xmi:id="_FaMM42o6EfCBSKlQ60Nj_w" bindingContext="_FaMM4mo6EfCBSKlQ60Nj_w"/>
  <bindingTables xmi:id="_FaMM5Wo6EfCBSKlQ60Nj_w" bindingContext="_FaMM5Go6EfCBSKlQ60Nj_w"/>
  <bindingTables xmi:id="_FaMM52o6EfCBSKlQ60Nj_w" bindingContext="_FaMM5mo6EfCBSKlQ60Nj_w"/>
  <bindingTables xmi:id="_FaMM6Wo6EfCBSKlQ60Nj_w" bindingContext="_FaMM6Go6EfCBSKlQ60Nj_w"/>
  <bindingTables xmi:id="_FaMM62o6EfCBSKlQ60Nj_w" bindingContext="_FaMM6mo6EfCBSKlQ60Nj_w"/>
  <bindingTables xmi:id="_FaMM7Wo6EfCBSKlQ60Nj_w" bindingContext="_FaMM7Go6EfCBSKlQ60Nj_w"/>
  <bindingTables xmi:id="_FaMM72o6EfCBSKlQ60Nj_w" bindingContext="_FaMM7mo6EfCBSKlQ60Nj_w"/>
  <bindingTables xmi:id="_FaMM8Wo6EfCBSKlQ60Nj_w" bindingContext="_FaMM8Go6EfCBSKlQ60Nj_w"/>
  <bindingTables xmi:id="_FaMM82o6EfCBSKlQ60Nj_w" bindingContext="_FaMM8mo6EfCBSKlQ60Nj_w"/>
  <bindingTables xmi:id="_FaMM9Wo6EfCBSKlQ60Nj_w" bindingContext="_FaMM9Go6EfCBSKlQ60Nj_w"/>
  <bindingTables xmi:id="_FaMM92o6EfCBSKlQ60Nj_w" bindingContext="_FaMM9mo6EfCBSKlQ60Nj_w"/>
  <bindingTables xmi:id="_FaMM-Wo6EfCBSKlQ60Nj_w" bindingContext="_FaMM-Go6EfCBSKlQ60Nj_w"/>
  <bindingTables xmi:id="_FaMM-2o6EfCBSKlQ60Nj_w" bindingContext="_FaMM-mo6EfCBSKlQ60Nj_w"/>
  <bindingTables xmi:id="_FaMM_Wo6EfCBSKlQ60Nj_w" bindingContext="_FaMM_Go6EfCBSKlQ60Nj_w"/>
  <bindingTables xmi:id="_FaMM_2o6EfCBSKlQ60Nj_w" bindingContext="_FaMM_mo6EfCBSKlQ60Nj_w"/>
  <bindingTables xmi:id="_FaMNAWo6EfCBSKlQ60Nj_w" bindingContext="_FaMNAGo6EfCBSKlQ60Nj_w"/>
  <bindingTables xmi:id="_FaMNA2o6EfCBSKlQ60Nj_w" bindingContext="_FaMNAmo6EfCBSKlQ60Nj_w"/>
  <bindingTables xmi:id="_FaMz8Go6EfCBSKlQ60Nj_w" bindingContext="_FaMNBGo6EfCBSKlQ60Nj_w"/>
  <bindingTables xmi:id="_FaMz8mo6EfCBSKlQ60Nj_w" bindingContext="_FaMz8Wo6EfCBSKlQ60Nj_w"/>
  <bindingTables xmi:id="_FaMz9Go6EfCBSKlQ60Nj_w" bindingContext="_FaMz82o6EfCBSKlQ60Nj_w"/>
  <bindingTables xmi:id="_FaMz9mo6EfCBSKlQ60Nj_w" bindingContext="_FaMz9Wo6EfCBSKlQ60Nj_w"/>
  <bindingTables xmi:id="_FaMz-Go6EfCBSKlQ60Nj_w" bindingContext="_FaMz92o6EfCBSKlQ60Nj_w"/>
  <bindingTables xmi:id="_FaMz-mo6EfCBSKlQ60Nj_w" bindingContext="_FaMz-Wo6EfCBSKlQ60Nj_w"/>
  <bindingTables xmi:id="_FaMz_Go6EfCBSKlQ60Nj_w" bindingContext="_FaMz-2o6EfCBSKlQ60Nj_w"/>
  <bindingTables xmi:id="_FaMz_mo6EfCBSKlQ60Nj_w" bindingContext="_FaMz_Wo6EfCBSKlQ60Nj_w"/>
  <bindingTables xmi:id="_FaM0AGo6EfCBSKlQ60Nj_w" bindingContext="_FaMz_2o6EfCBSKlQ60Nj_w"/>
  <bindingTables xmi:id="_FaM0Amo6EfCBSKlQ60Nj_w" bindingContext="_FaM0AWo6EfCBSKlQ60Nj_w"/>
  <bindingTables xmi:id="_FaM0BGo6EfCBSKlQ60Nj_w" bindingContext="_FaM0A2o6EfCBSKlQ60Nj_w"/>
  <rootContext xmi:id="_FFaPumo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.contexts.dialogAndWindow" contributorURI="platform:/plugin/org.eclipse.ui.workbench" name="In Dialogs and Windows" description="Either a dialog or a window is open">
    <children xmi:id="_FFaPu2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.contexts.window" contributorURI="platform:/plugin/org.eclipse.ui.workbench" name="In Windows" description="A window is open">
      <children xmi:id="_FFaPvGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.e4.ui.contexts.views" contributorURI="platform:/plugin/org.eclipse.ui.workbench" name="%bindingcontext.name.bindingView"/>
      <children xmi:id="_FFz4UGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.tm.terminal.EditContext" name="Terminal Control in Focus" description="Show modified keyboard shortcuts in context menu"/>
      <children xmi:id="_FFz4UWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.debug.ui.BreakpointView" name="In Breakpoints View" description="The breakpoints view context"/>
      <children xmi:id="_FFz4Umo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.textEditorScope" name="Editing Text" description="Editing Text Context">
        <children xmi:id="_FFz4WWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.asmEditorScope" name="Assembly Editor" description="Editor for Assembly Source Files"/>
        <children xmi:id="_FFz4W2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.genericeditor.genericEditorContext" name="in Generic Code Editor" description="When editing in the Generic Code Editor"/>
        <children xmi:id="_FFz4Y2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.make.ui.makefileEditorScope" name="Makefile Editor" description="Editor for makefiles"/>
        <children xmi:id="_FFz4ZGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.cEditorScope" name="C/C++ Editor" description="Editor for C/C++ Source Files"/>
      </children>
      <children xmi:id="_FFz4VGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.debug.ui.console" name="In I/O Console" description="In I/O console"/>
      <children xmi:id="_FFz4VWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.compare.compareEditorScope" name="Comparing in an Editor" description="Comparing in an Editor"/>
      <children xmi:id="_FFz4WGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.console.ConsoleView" name="In Console View" description="In Console View"/>
      <children xmi:id="_FFz4Wmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.debug.ui.memoryview" name="In Memory View" description="In memory view"/>
      <children xmi:id="_FFz4XGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.debug.ui.debugging" name="Debugging" description="Debugging programs">
        <children xmi:id="_FFz4XWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" name="In Table Memory Rendering" description="In Table Memory Rendering"/>
        <children xmi:id="_FFz4Xmo6EfCBSKlQ60Nj_w" elementId="com.st.stm32cube.ide.mpu.debug.ui.debugging" name="Debugging C/C++ on MPU - Cortex-M" description="Debugging C/C++ Programs on MPU - Cortex-M"/>
        <children xmi:id="_FFz4X2o6EfCBSKlQ60Nj_w" elementId="com.st.stm32cube.ide.mcu.debug.ui.debugging" name="Debugging C/C++ on MCU" description="Debugging C/C++ Programs on MCU"/>
        <children xmi:id="_FFz4YGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.context" name="In Disassembly" description="When debugging in assembly mode"/>
        <children xmi:id="_FFz4Zmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.debug.ui.debugging" name="Debugging C/C++" description="Debugging C/C++ Programs"/>
      </children>
      <children xmi:id="_FFz4Ymo6EfCBSKlQ60Nj_w" elementId="org.eclipse.tm.terminal.TerminalContext" name="Terminal Typing Connected" description="Override ALT+x menu access keys while typing into the Terminal"/>
      <children xmi:id="_FFz4ZWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.cViewScope" name="In C/C++ Views" description="In C/C++ Views"/>
    </children>
    <children xmi:id="_FFaPvWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.contexts.dialog" contributorURI="platform:/plugin/org.eclipse.ui.workbench" name="In Dialogs" description="A dialog is open"/>
    <children xmi:id="_FFz4V2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.macroExpansionHoverScope" name="In Macro Expansion Hover" description="In Macro Expansion Hover"/>
  </rootContext>
  <rootContext xmi:id="_FFz4U2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.contexts.actionSet" name="Action Set" description="Parent context for action sets"/>
  <rootContext xmi:id="_FFz4Vmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.contexts.workbenchMenu" name="Workbench Menu" description="When no Workbench windows are active"/>
  <rootContext xmi:id="_FFz4YWo6EfCBSKlQ60Nj_w" elementId="com.st.stm32cube.common.mx.context" name="Device Configuration Tool Context" description="Device Configuration Tool  Context"/>
  <rootContext xmi:id="_FaLl0Go6EfCBSKlQ60Nj_w" elementId="com.st.stm32cube.ide.mcu.debug.dsf.oss.ui.debugActionSet" name="Auto::com.st.stm32cube.ide.mcu.debug.dsf.oss.ui.debugActionSet"/>
  <rootContext xmi:id="_FaLl0mo6EfCBSKlQ60Nj_w" elementId="com.st.stm32cube.ide.mcu.informationcenter.actionSet3" name="Auto::com.st.stm32cube.ide.mcu.informationcenter.actionSet3"/>
  <rootContext xmi:id="_FaLl1Go6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.debug.ui.debugActionSet" name="Auto::org.eclipse.cdt.debug.ui.debugActionSet"/>
  <rootContext xmi:id="_FaLl1mo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.debug.ui.reverseDebuggingActionSet" name="Auto::org.eclipse.cdt.debug.ui.reverseDebuggingActionSet"/>
  <rootContext xmi:id="_FaLl2Go6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.debug.ui.tracepointActionSet" name="Auto::org.eclipse.cdt.debug.ui.tracepointActionSet"/>
  <rootContext xmi:id="_FaMM4Go6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.debug.ui.debugViewLayoutActionSet" name="Auto::org.eclipse.cdt.debug.ui.debugViewLayoutActionSet"/>
  <rootContext xmi:id="_FaMM4mo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.dsf.debug.ui.updateModes" name="Auto::org.eclipse.cdt.dsf.debug.ui.updateModes"/>
  <rootContext xmi:id="_FaMM5Go6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.make.ui.updateActionSet" name="Auto::org.eclipse.cdt.make.ui.updateActionSet"/>
  <rootContext xmi:id="_FaMM5mo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.make.ui.makeTargetActionSet" name="Auto::org.eclipse.cdt.make.ui.makeTargetActionSet"/>
  <rootContext xmi:id="_FaMM6Go6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.CodingActionSet" name="Auto::org.eclipse.cdt.ui.CodingActionSet"/>
  <rootContext xmi:id="_FaMM6mo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.SearchActionSet" name="Auto::org.eclipse.cdt.ui.SearchActionSet"/>
  <rootContext xmi:id="_FaMM7Go6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.NavigationActionSet" name="Auto::org.eclipse.cdt.ui.NavigationActionSet"/>
  <rootContext xmi:id="_FaMM7mo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.OpenActionSet" name="Auto::org.eclipse.cdt.ui.OpenActionSet"/>
  <rootContext xmi:id="_FaMM8Go6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.buildConfigActionSet" name="Auto::org.eclipse.cdt.ui.buildConfigActionSet"/>
  <rootContext xmi:id="_FaMM8mo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.CElementCreationActionSet" name="Auto::org.eclipse.cdt.ui.CElementCreationActionSet"/>
  <rootContext xmi:id="_FaMM9Go6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.text.c.actionSet.presentation" name="Auto::org.eclipse.cdt.ui.text.c.actionSet.presentation"/>
  <rootContext xmi:id="_FaMM9mo6EfCBSKlQ60Nj_w" elementId="org.eclipse.debug.ui.breakpointActionSet" name="Auto::org.eclipse.debug.ui.breakpointActionSet"/>
  <rootContext xmi:id="_FaMM-Go6EfCBSKlQ60Nj_w" elementId="org.eclipse.debug.ui.debugActionSet" name="Auto::org.eclipse.debug.ui.debugActionSet"/>
  <rootContext xmi:id="_FaMM-mo6EfCBSKlQ60Nj_w" elementId="org.eclipse.debug.ui.launchActionSet" name="Auto::org.eclipse.debug.ui.launchActionSet"/>
  <rootContext xmi:id="_FaMM_Go6EfCBSKlQ60Nj_w" elementId="org.eclipse.debug.ui.profileActionSet" name="Auto::org.eclipse.debug.ui.profileActionSet"/>
  <rootContext xmi:id="_FaMM_mo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.cheatsheets.actionSet" name="Auto::org.eclipse.ui.cheatsheets.actionSet"/>
  <rootContext xmi:id="_FaMNAGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.search.searchActionSet" name="Auto::org.eclipse.search.searchActionSet"/>
  <rootContext xmi:id="_FaMNAmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.team.ui.actionSet" name="Auto::org.eclipse.team.ui.actionSet"/>
  <rootContext xmi:id="_FaMNBGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.text.quicksearch.actionSet" name="Auto::org.eclipse.text.quicksearch.actionSet"/>
  <rootContext xmi:id="_FaMz8Wo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.actionSet.annotationNavigation" name="Auto::org.eclipse.ui.edit.text.actionSet.annotationNavigation"/>
  <rootContext xmi:id="_FaMz82o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.actionSet.navigation" name="Auto::org.eclipse.ui.edit.text.actionSet.navigation"/>
  <rootContext xmi:id="_FaMz9Wo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo" name="Auto::org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo"/>
  <rootContext xmi:id="_FaMz92o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.externaltools.ExternalToolsSet" name="Auto::org.eclipse.ui.externaltools.ExternalToolsSet"/>
  <rootContext xmi:id="_FaMz-Wo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.NavigateActionSet" name="Auto::org.eclipse.ui.NavigateActionSet"/>
  <rootContext xmi:id="_FaMz-2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.actionSet.keyBindings" name="Auto::org.eclipse.ui.actionSet.keyBindings"/>
  <rootContext xmi:id="_FaMz_Wo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.WorkingSetModificationActionSet" name="Auto::org.eclipse.ui.WorkingSetModificationActionSet"/>
  <rootContext xmi:id="_FaMz_2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.WorkingSetActionSet" name="Auto::org.eclipse.ui.WorkingSetActionSet"/>
  <rootContext xmi:id="_FaM0AWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.actionSet.openFiles" name="Auto::org.eclipse.ui.actionSet.openFiles"/>
  <rootContext xmi:id="_FaM0A2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.actionSet.presentation" name="Auto::org.eclipse.ui.edit.text.actionSet.presentation"/>
  <descriptors xmi:id="_FIkRYGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.e4.ui.compatibility.editor" allowMultiple="true" category="org.eclipse.e4.primaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor">
    <tags>Editor</tags>
    <tags>removeOnHide</tags>
  </descriptors>
  <descriptors xmi:id="_FUje8Go6EfCBSKlQ60Nj_w" elementId="com.st.stm32cube.ide.mcu.buildanalyzer.view" label="Build Analyzer" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.buildanalyzer/icons/view_icon.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="elf_analyzer.ElfAnalyzerView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.buildanalyzer"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_FUlUIGo6EfCBSKlQ60Nj_w" elementId="com.st.stm32cube.ide.mcu.cyclomaticcomplexity.view" label="Cyclomatic Complexity" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.cyclomaticcomplexity/icons/algorithm.png" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.cyclomaticcomplexity.CyclomaticView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.cyclomaticcomplexity"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_FUlUIWo6EfCBSKlQ60Nj_w" elementId="com.st.stm32cube.ide.mcu.debug.swv.core.logview" label="SWV Trace Log" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.debug.swv/icons/SWV_spreadsheet.png" tooltip="" category="SWV" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.debug.swv.core.ui.SWVLogView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.debug.swv"/>
    <tags>View</tags>
    <tags>categoryTag:SWV</tags>
  </descriptors>
  <descriptors xmi:id="_FUl7MGo6EfCBSKlQ60Nj_w" elementId="com.st.stm32cube.ide.mcu.debug.swv.core.statisticalprofiling" label="SWV Statistical Profiling" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.debug.swv/icons/SWV_statistical_profiling.png" tooltip="" category="SWV" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.debug.swv.core.ui.statisticalprofiling.SWVStatisticalProfilingView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.debug.swv"/>
    <tags>View</tags>
    <tags>categoryTag:SWV</tags>
  </descriptors>
  <descriptors xmi:id="_FUl7MWo6EfCBSKlQ60Nj_w" elementId="com.st.stm32cube.ide.mcu.debug.swv.core.itmtrace" label="SWV ITM Data Console" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.debug.swv/icons/console_view.gif" tooltip="" category="SWV" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.debug.swv.core.ui.itmtrace.SWVConsole"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.debug.swv"/>
    <tags>View</tags>
    <tags>categoryTag:SWV</tags>
  </descriptors>
  <descriptors xmi:id="_FUl7Mmo6EfCBSKlQ60Nj_w" elementId="com.st.stm32cube.ide.mcu.debug.swv.core.ui.exception.exceptionlogview" label="SWV Exception Trace Log" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.debug.swv/icons/SWV_Exception_spreadsheet.png" tooltip="" category="SWV" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.debug.swv.core.ui.exception.SWVExceptionLogView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.debug.swv"/>
    <tags>View</tags>
    <tags>categoryTag:SWV</tags>
  </descriptors>
  <descriptors xmi:id="_FUl7M2o6EfCBSKlQ60Nj_w" elementId="com.st.stm32cube.ide.mcu.debug.swv.core.ui.datatraceview" label="SWV Data Trace" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.debug.swv/icons/insp_sbook.gif" tooltip="" category="SWV" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.debug.swv.core.ui.datatrace.SWVDataTraceView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.debug.swv"/>
    <tags>View</tags>
    <tags>categoryTag:SWV</tags>
  </descriptors>
  <descriptors xmi:id="_FUl7NGo6EfCBSKlQ60Nj_w" elementId="com.st.stm32cube.ide.mcu.debug.swv.core.SWVDatatraceTimeline" label="SWV Data Trace Timeline Graph" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.debug.swv/icons/Datatrace_timeline.png" tooltip="" category="SWV" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.debug.swv.core.ui.datatraceTimeline.SWVDatatraceTimeline"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.debug.swv"/>
    <tags>View</tags>
    <tags>categoryTag:SWV</tags>
  </descriptors>
  <descriptors xmi:id="_FUl7NWo6EfCBSKlQ60Nj_w" elementId="com.st.stm32cube.ide.mcu.faultanalyzer.view" label="Fault Analyzer" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.faultanalyzer/icons/clanbomber.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.faultanalyzer.FaultAnalyzerView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.faultanalyzer"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_FUmiQGo6EfCBSKlQ60Nj_w" elementId="com.st.stm32cube.ide.mcu.freertos.queues" label="FreeRTOS Queues" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.freertos/icons/debugt_obj.gif" tooltip="" category="FreeRTOS" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.freertos.queues.FORtosQueues"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.freertos"/>
    <tags>View</tags>
    <tags>categoryTag:FreeRTOS</tags>
  </descriptors>
  <descriptors xmi:id="_FUmiQWo6EfCBSKlQ60Nj_w" elementId="com.st.stm32cube.ide.mcu.freertos.tasklist" label="FreeRTOS Task List" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.freertos/icons/debugt_obj.gif" tooltip="" category="FreeRTOS" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.freertos.tasklist.FORtosTaskList"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.freertos"/>
    <tags>View</tags>
    <tags>categoryTag:FreeRTOS</tags>
  </descriptors>
  <descriptors xmi:id="_FUmiQmo6EfCBSKlQ60Nj_w" elementId="com.st.stm32cube.ide.mcu.freertos.semaphore" label="FreeRTOS Semaphores" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.freertos/icons/debugt_obj.gif" tooltip="" category="FreeRTOS" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.freertos.semaphores.FORtosSemaphores"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.freertos"/>
    <tags>View</tags>
    <tags>categoryTag:FreeRTOS</tags>
  </descriptors>
  <descriptors xmi:id="_FUnJUGo6EfCBSKlQ60Nj_w" elementId="com.st.stm32cube.ide.mcu.freertos.timers" label="FreeRTOS Timers" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.freertos/icons/debugt_obj.gif" tooltip="" category="FreeRTOS" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.freertos.timers.FORtosTimers"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.freertos"/>
    <tags>View</tags>
    <tags>categoryTag:FreeRTOS</tags>
  </descriptors>
  <descriptors xmi:id="_FUnJUWo6EfCBSKlQ60Nj_w" elementId="com.st.stm32cube.ide.mcu.livewatch.LiveExpressionsView" label="Live Expressions" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.livewatch/icons/watchlist_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.livewatch.LiveExpressionsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.livewatch"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_FUnJUmo6EfCBSKlQ60Nj_w" elementId="com.st.stm32cube.ide.mcu.sfrview" label="SFRs" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.sfrview/icons/memory_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.sfrview.ui.SfrView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.sfrview"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_FUnJU2o6EfCBSKlQ60Nj_w" elementId="com.st.stm32cube.ide.mcu.stackanalyzer.stackanalyzer.view" label="Static Stack Analyzer" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.stackanalyzer/icons/view_icon.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.stackanalyzer.ui.StackAnalyzerView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.stackanalyzer"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_FUnwYGo6EfCBSKlQ60Nj_w" elementId="com.st.stm32cube.ide.mcu.tcp.console.view" label="TCP Console" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.tcp.console/icons/console.png" tooltip="" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.tcp.console.ui.TCPConsoleView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.tcp.console"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_FUnwYWo6EfCBSKlQ60Nj_w" elementId="com.st.stm32cube.ide.mcu.threadx.threads" label="ThreadX Thread List" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.threadx/icons/debugt_obj.gif" tooltip="" category="ThreadX" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.threadx.threadlist.ThreadXThreadList"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.threadx"/>
    <tags>View</tags>
    <tags>categoryTag:ThreadX</tags>
  </descriptors>
  <descriptors xmi:id="_FUnwYmo6EfCBSKlQ60Nj_w" elementId="com.st.stm32cube.ide.mcu.threadx.semaphores" label="ThreadX Semaphores" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.threadx/icons/debugt_obj.gif" tooltip="" category="ThreadX" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.threadx.semaphores.ThreadXSemaphoreList"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.threadx"/>
    <tags>View</tags>
    <tags>categoryTag:ThreadX</tags>
  </descriptors>
  <descriptors xmi:id="_FUoXcGo6EfCBSKlQ60Nj_w" elementId="com.st.stm32cube.ide.mcu.threadx.mutexes" label="ThreadX Mutexes" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.threadx/icons/debugt_obj.gif" tooltip="" category="ThreadX" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.threadx.mutexes.ThreadXMutexList"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.threadx"/>
    <tags>View</tags>
    <tags>categoryTag:ThreadX</tags>
  </descriptors>
  <descriptors xmi:id="_FUoXcWo6EfCBSKlQ60Nj_w" elementId="com.st.stm32cube.ide.mcu.threadx.queues" label="ThreadX Message Queues" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.threadx/icons/debugt_obj.gif" tooltip="" category="ThreadX" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.threadx.queues.ThreadXMessageQueues"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.threadx"/>
    <tags>View</tags>
    <tags>categoryTag:ThreadX</tags>
  </descriptors>
  <descriptors xmi:id="_FUoXcmo6EfCBSKlQ60Nj_w" elementId="com.st.stm32cube.ide.mcu.threadx.eventflags" label="ThreadX Event Flags" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.threadx/icons/debugt_obj.gif" tooltip="" category="ThreadX" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.threadx.eventflags.ThreadXEventFlags"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.threadx"/>
    <tags>View</tags>
    <tags>categoryTag:ThreadX</tags>
  </descriptors>
  <descriptors xmi:id="_FUoXc2o6EfCBSKlQ60Nj_w" elementId="com.st.stm32cube.ide.mcu.threadx.timer" label="ThreadX Timers" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.threadx/icons/debugt_obj.gif" tooltip="" category="ThreadX" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.threadx.timers.ThreadXTimers"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.threadx"/>
    <tags>View</tags>
    <tags>categoryTag:ThreadX</tags>
  </descriptors>
  <descriptors xmi:id="_FUoXdGo6EfCBSKlQ60Nj_w" elementId="com.st.stm32cube.ide.mcu.threadx.blockpools" label="ThreadX Memory Block Pools" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.threadx/icons/debugt_obj.gif" tooltip="" category="ThreadX" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.threadx.blockpools.ThreadXMemoryBlockPools"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.threadx"/>
    <tags>View</tags>
    <tags>categoryTag:ThreadX</tags>
  </descriptors>
  <descriptors xmi:id="_FUoXdWo6EfCBSKlQ60Nj_w" elementId="com.st.stm32cube.ide.mcu.threadx.bytepools" label="ThreadX Memory Byte Pools" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.threadx/icons/debugt_obj.gif" tooltip="" category="ThreadX" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.threadx.bytepools.ThreadXMemoryBytePools"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.threadx"/>
    <tags>View</tags>
    <tags>categoryTag:ThreadX</tags>
  </descriptors>
  <descriptors xmi:id="_FUo-gGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.codan.internal.ui.views.ProblemDetails" label="Problem Details" iconURI="platform:/plugin/org.eclipse.cdt.codan.ui/icons/edit_bug.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.codan.internal.ui.views.ProblemDetails"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.codan.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_FUo-gWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.debug.ui.executablesView" label="Executables" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/obj16/exec_view_obj.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.internal.ui.views.executables.ExecutablesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_FUo-gmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.debug.ui.SignalsView" label="Signals" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/view16/signals_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.internal.ui.views.signals.FlexibleSignalsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_FUo-g2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.debug.ui.debuggerConsoleView" label="Debugger Console" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/view16/debugger_console_view.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.internal.ui.views.debuggerconsole.DebuggerConsoleView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_FUplkGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser" label="Memory Browser" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui.memory.memorybrowser/icons/memorybrowser_view.gif" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui.memory.memorybrowser"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_FUplkWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.dsf.gdb.ui.tracecontrol.view" label="Trace Control" iconURI="platform:/plugin/org.eclipse.cdt.dsf.gdb.ui/icons/full/view16/tracecontrol_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.gdb.internal.ui.tracepoints.TraceControlView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.gdb.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_FUqMoGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.dsf.gdb.ui.osresources.view" label="OS Resources" iconURI="platform:/plugin/org.eclipse.cdt.dsf.gdb.ui/icons/full/view16/osresources_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.gdb.internal.ui.osview.OSResourcesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.gdb.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_FUqMoWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.dsf.gdb.ui.debugsources.view" label="Debug Sources" iconURI="platform:/plugin/org.eclipse.cdt.dsf.gdb.ui/icons/full/view16/debugsources_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.gdb.internal.ui.debugsources.DebugSourcesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.gdb.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_FUqMomo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.view" label="Disassembly" iconURI="platform:/plugin/org.eclipse.cdt.dsf.ui/icons/disassembly.gif" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.debug.internal.ui.disassembly.DisassemblyView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_FUqMo2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.make.ui.views.MakeView" label="Build Targets" iconURI="platform:/plugin/org.eclipse.cdt.make.ui/icons/view16/make_target.gif" tooltip="" category="Make" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.make.ui.views.MakeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.make.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Make</tags>
  </descriptors>
  <descriptors xmi:id="_FUqMpGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.CView" label="C/C++ Projects" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/cview.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.cview.CView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_FUqMpWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.IndexView" label="C/C++ Index" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/types.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.indexview.IndexView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_FUqzsGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.includeBrowser" label="Include Browser" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/includeBrowser.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.includebrowser.IBViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_FUqzsWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.callHierarchy" label="Call Hierarchy" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/call_hierarchy.gif" tooltip="" allowMultiple="true" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.callhierarchy.CHViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_FUqzsmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.typeHierarchy" label="Type Hierarchy" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/class_hi.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.typehierarchy.THViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_FUqzs2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.texteditor.TemplatesView" label="Templates" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/templates.gif" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.texteditor.templates.TemplatesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_FUqztGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.debug.ui.DebugView" label="Debug" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/debug_view.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.launch.LaunchView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_FUqztWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.debug.ui.BreakpointView" label="Breakpoints" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/breakpoint_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.breakpoints.BreakpointsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_FUrawGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.debug.ui.VariableView" label="Variables" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/variable_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.variables.VariablesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_FUrawWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.debug.ui.ExpressionView" label="Expressions" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/watchlist_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.expression.ExpressionView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_FUsB0Go6EfCBSKlQ60Nj_w" elementId="org.eclipse.debug.ui.RegisterView" label="Registers" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/register_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.registers.RegistersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_FUsB0Wo6EfCBSKlQ60Nj_w" elementId="org.eclipse.debug.ui.ModuleView" label="Modules" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/module_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.modules.ModulesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_FUsB0mo6EfCBSKlQ60Nj_w" elementId="org.eclipse.debug.ui.MemoryView" label="Memory" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/memory_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.memory.MemoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_FUsB02o6EfCBSKlQ60Nj_w" elementId="org.eclipse.debug.ui.launchView" label="Launch Configurations" iconURI="platform:/plugin/org.eclipse.debug.ui.launchview/icons/run_exc.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.debug.ui.launchview/org.eclipse.debug.ui.launchview.internal.view.LaunchViewImpl">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_FUsB1Go6EfCBSKlQ60Nj_w" elementId="org.eclipse.help.ui.HelpView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.png" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.help.ui.internal.views.HelpView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.help.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_FUso4Go6EfCBSKlQ60Nj_w" elementId="org.eclipse.remote.ui.view.connections" label="Connections" iconURI="platform:/plugin/org.eclipse.remote.ui/icons/connection.gif" tooltip="" category="Connections" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.remote.internal.ui.views.RemoteConnectionsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.remote.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Connections</tags>
  </descriptors>
  <descriptors xmi:id="_FUso4Wo6EfCBSKlQ60Nj_w" elementId="org.eclipse.search.ui.views.SearchView" label="Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.search2.internal.ui.SearchView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.search"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_FUso4mo6EfCBSKlQ60Nj_w" elementId="org.eclipse.team.sync.views.SynchronizeView" label="Synchronize" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/synch_synch.png" tooltip="" allowMultiple="true" category="Version Control (Team)" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.team.internal.ui.synchronize.SynchronizeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.team.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Version Control (Team)</tags>
  </descriptors>
  <descriptors xmi:id="_FUtP8Go6EfCBSKlQ60Nj_w" elementId="org.eclipse.team.ui.GenericHistoryView" label="History" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/history_view.png" tooltip="" allowMultiple="true" category="Version Control (Team)" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.team.internal.ui.history.GenericHistoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.team.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Version Control (Team)</tags>
  </descriptors>
  <descriptors xmi:id="_FUtP8Wo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.internal.introview" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.ViewIntroAdapterPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_FUtP8mo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.browser.view" label="Internal Web Browser" iconURI="platform:/plugin/org.eclipse.ui.browser/icons/obj16/internal_browser.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.browser.WebBrowserView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.browser"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_FUtP82o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.png" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.cheatsheets.views.CheatSheetView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.cheatsheets"/>
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_FUt3AGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.console.ConsoleView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.console.ConsoleView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.console"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_FUt3AWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.views.ProgressView" label="Progress" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/pview.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.progress.ProgressView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_FUt3Amo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.views.BookmarkView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.BookmarksView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_FUueEGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.views.TaskList" label="Tasks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/tasks_tsk.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.TasksView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_FUvFIGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.views.ProblemView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.ProblemsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_FUvFIWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.views.AllMarkersView" label="Markers" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.AllMarkersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_FUvFImo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.navigator.ProjectExplorer" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.navigator.resources.ProjectExplorer"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.navigator.resources"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_FUvsMGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.views.PropertySheet" label="Properties" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/prop_ps.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.properties.PropertySheet"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_FUwTQGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.views.ContentOutline" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.contentoutline.ContentOutline"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_FUwTQWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.pde.runtime.LogView" label="Error Log" iconURI="platform:/plugin/org.eclipse.ui.views.log/icons/eview16/error_log.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.log.LogView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views.log"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_FUw6UGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.views.minimap.MinimapView" label="Minimap" iconURI="platform:/plugin/org.eclipse.ui.workbench.texteditor/icons/full/eview16/minimap.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.minimap.MinimapView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.workbench.texteditor"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_FUw6UWo6EfCBSKlQ60Nj_w" elementId="com.st.stm32cube.common.mx.views.OutputsView" label="Outputs" iconURI="platform:/plugin/com.st.stm32cube.common.mx/icons/MicroXplorer.png" tooltip="" category="Device Configuration Tool" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.common.mx.views.OutPutMxView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.common.mx"/>
    <tags>View</tags>
    <tags>categoryTag:Device Configuration Tool</tags>
  </descriptors>
  <trimContributions xmi:id="_2r10UF9tEeO-yojH_y4TJA" elementId="org.eclipse.ui.ide.application.trimcontribution.QuickAccess" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" toBeRendered="false" parentId="org.eclipse.ui.main.toolbar" positionInParent="last">
    <children xsi:type="menu:ToolControl" xmi:id="_76uUAF9tEeO-yojH_y4TJA" elementId="Spacer Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:PerspectiveSpacer</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_8tJPcF9tEeO-yojH_y4TJA" elementId="SearchField" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.quickaccess.SearchField">
      <tags>move_after:Spacer Glue</tags>
      <tags>HIDEABLE</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_9LgmcF9tEeO-yojH_y4TJA" elementId="Search-PS Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:SearchField</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
  </trimContributions>
  <commands xmi:id="_FFtKoGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.select.pageUp" commandName="Select Page Up" description="Select to the top of the page" category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtKoWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.toggleWordWrap" commandName="Toggle Word Wrap" description="Toggle word wrap in the current text editor" category="_FFsjkmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtKomo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.dsf.gdb.ui.command.selectPreviousTraceRecord" commandName="Previous Trace Record" description="Select Previous Trace Record" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtKo2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.make.ui.targetCreateCommand" commandName="Create Build Target" description="Create a new make build target for the selected container." category="_FFsjoWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtKpGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.search.findrefs" commandName="References" description="Searches for references to the selected element in the workspace" category="_FFsjk2o6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtKpWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.search.ui.openFileSearchPage" commandName="File Search" description="Open the Search dialog's file search page" category="_FFsjpmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtKpmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.ide.copyBuildIdCommand" commandName="Copy Build Id Information To Clipboard" description="Copies the build identification information to the clipboard." category="_FFsjkmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtKp2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.select.textEnd" commandName="Select Text End" description="Select to the end of the text" category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtKqGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.tm.terminal.maximize" commandName="Maximize Active View or Editor" category="_FFsjpGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtKqWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.deletePreviousWord" commandName="Delete Previous Word" description="Delete the previous word" category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtKqmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.goto.wordPrevious" commandName="Previous Word" description="Go to the previous word" category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtKq2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.debug.ui.command.connect" commandName="Connect" description="Connect to a process" category="_FFsjo2o6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtKrGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.select.stopMultiSelection" commandName="End multi-selection" description="Unselects all multi-selections returning to a single cursor " category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtxsGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.window.preferences" commandName="Preferences" description="Open the preferences dialog" category="_FFsjnWo6EfCBSKlQ60Nj_w">
    <parameters xmi:id="_FFtxsWo6EfCBSKlQ60Nj_w" elementId="preferencePageId" name="Preference Page"/>
  </commands>
  <commands xmi:id="_FFtxsmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.delimiter.unix" commandName="Convert Line Delimiters to Unix (LF, \n, 0A, &#xb6;)" description="Converts the line delimiters to Unix (LF, \n, 0A, &#xb6;)" category="_FFsjmmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtxs2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.epp.mpc.ui.command.showMarketplaceWizard" commandName="Eclipse Marketplace" description="Show the Eclipse Marketplace wizard" category="_FFsjrmo6EfCBSKlQ60Nj_w">
    <parameters xmi:id="_FFtxtGo6EfCBSKlQ60Nj_w" elementId="trigger" name="trigger"/>
  </commands>
  <commands xmi:id="_FFtxtWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.copy" commandName="Copy" description="Copy the selection to the clipboard" category="_FFsjkmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtxtmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.window.nextView" commandName="Next View" description="Switch to the next view" category="_FFsjnWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtxt2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.specific_content_assist.command" commandName="C/C++ Content Assist" description="A parameterizable command that invokes content assist with a single completion proposal category" category="_FFsjkmo6EfCBSKlQ60Nj_w">
    <parameters xmi:id="_FFtxuGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.specific_content_assist.category_id" name="type" optional="false"/>
  </commands>
  <commands xmi:id="_FFtxuWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.revertToSaved" commandName="Revert to Saved" description="Revert to the last saved state" category="_FFsjkmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtxumo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.views.properties.NewPropertySheetCommand" commandName="Properties" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtxu2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.navigate.addToWorkingSet" commandName="Add to Working Set" description="Adds the selected object to a working set." category="_FFsjkmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtxvGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.debug.ui.commands.RunToLine" commandName="Run to Line" description="Resume and break when execution reaches the current line" category="_FFsjqGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtxvWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.team.ui.TeamSynchronizingPerspective" commandName="Team Synchronizing" description="Open the Team Synchronizing Perspective" category="_FFsjqmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtxvmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.search.findrefs.workingset" commandName="References in Working Set" description="Searches for references to the selected element in a working set" category="_FFsjk2o6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtxv2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.showChangeRulerInformation" commandName="Show Quick Diff Ruler Tooltip" description="Displays quick diff or revision information for the caret line in a focused hover" category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtxwGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.upperCase" commandName="To Upper Case" description="Changes the selection to upper case" category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtxwWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.navigate.goInto" commandName="Go Into" description="Navigate into the selected item" category="_FFsjmGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtxwmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.epp.mpc.ui.command.showInstalled" commandName="Manage installed plug-ins" description="Update or uninstall plug-ins installed from the Marketplace" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtxw2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.debug.ui.commands.OpenRunConfigurations" commandName="Run..." description="Open run launch configuration dialog" category="_FFsjqGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtxxGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.select.windowEnd" commandName="Select Window End" description="Select to the end of the window" category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtxxWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.window.minimizePart" commandName="Minimize Active View or Editor" description="Minimizes the active view or editor" category="_FFsjnWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtxxmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.remote.ui.command.openConnection" commandName="Open Connection" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtxx2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.tm.terminal.command1" commandName="Terminal view insert" category="_FFsjpGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtxyGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.edit.open.outline" commandName="Show outline" description="Shows outline" category="_FFsjk2o6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtxyWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.debug.ui.command.ungroupDebugContexts" commandName="Ungroup" description="Ungroups the selected debug contexts" category="_FFsjo2o6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtxymo6EfCBSKlQ60Nj_w" elementId="org.eclipse.debug.ui.commands.ToggleLineBreakpoint" commandName="Toggle Line Breakpoint" description="Creates or removes a line breakpoint" category="_FFsjqGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtxy2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.window.showSystemMenu" commandName="Show System Menu" description="Show the system menu" category="_FFsjnWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtxzGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.menu.rebuildIndex" commandName="Rebuild Index" category="_FFsjoWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtxzWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.window.savePerspective" commandName="Save Perspective As" description="Save the current perspective" category="_FFsjnWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtxzmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.debug.ui.command.castToArray" commandName="Cast To Type..." category="_FFsjrGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtxz2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.debug.ui.command.stopTracing" commandName="Stop Tracing " description="Stop Tracing Experiment" category="_FFsjr2o6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtx0Go6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.move" commandName="Move..." description="Move the selected item" category="_FFsjmmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtx0Wo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.navigate.linkWithEditor" commandName="Toggle Link with Editor" description="Toggles linking of a view's selection with the active editor's selection" category="_FFsjmGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtx0mo6EfCBSKlQ60Nj_w" elementId="org.eclipse.compare.ignoreWhiteSpace" commandName="Ignore White Space" description="Ignore white space where applicable" category="_FFsjm2o6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtx02o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.toggleBlockSelectionMode" commandName="Toggle Block Selection" description="Toggle block / column selection in the current text editor" category="_FFsjkmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtx1Go6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.refactor.extract.function" commandName="Extract Function - Refactoring " description="Extracts a function for the selected list of expressions or statements" category="_FFsjn2o6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtx1Wo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.navigate.goToResource" commandName="Go to Resource" description="Go to a particular resource in the active view" category="_FFsjmGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtx1mo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.window.resetPerspective" commandName="Reset Perspective" description="Reset the current perspective to its default state" category="_FFsjnWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtx12o6EfCBSKlQ60Nj_w" elementId="org.eclipse.remote.ui.command.openTerminal" commandName="Open Command Shell" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtx2Go6EfCBSKlQ60Nj_w" elementId="AnsiConsole.command.enable_disable" commandName="Enable / Disable ANSI Support" description="Enable / disable ANSI Support" category="_FFsjmWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtx2Wo6EfCBSKlQ60Nj_w" elementId="com.st.stm32cube.ide.mpu.linux.ide.command.setupopenstlinux" commandName="Setup OpenSTLinux" description="Setup OpenSTLinux" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtx2mo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.project.buildLast" commandName="Repeat Working Set Build" description="Repeat the last working set build" category="_FFsjoWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtx22o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.project.buildProject" commandName="Build Project" description="Build the selected project" category="_FFsjoWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtx3Go6EfCBSKlQ60Nj_w" elementId="org.eclipse.compare.switchLeftAndRight" commandName="Swap Left and Right View" description="Switch the left and right sides in the compare editor" category="_FFsjm2o6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtx3Wo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.goto.textStart" commandName="Text Start" description="Go to the beginning of the text" category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtx3mo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.paste" commandName="Paste" description="Paste from the clipboard" category="_FFsjkmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtx32o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.part.nextPage" commandName="Next Page" description="Switch to the next page" category="_FFsjmGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtx4Go6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.menu.updateUnresolvedIncludes" commandName="Re-resolve Unresolved Includes" category="_FFsjoWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtx4Wo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.edit.text.c.toggle.comment" commandName="Toggle Comment" description="Toggle comment the selected lines" category="_FFsjk2o6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtx4mo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.edit.text.c.remove.block.comment" commandName="Remove Block Comment" description="Removes the block comment enclosing the selection" category="_FFsjk2o6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtx42o6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.refactor.extract.constant" commandName="Extract Constant - Refactoring " description="Extracts a constant for the selected expression" category="_FFsjn2o6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtx5Go6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.navigate.previous" commandName="Previous" description="Navigate to the previous item" category="_FFsjmGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtx5Wo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.edit.open.include.browser" commandName="Open Include Browser" description="Open an include browser on the selected element" category="_FFsjmGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtx5mo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.rename" commandName="Rename" description="Rename the selected item" category="_FFsjmmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtx52o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.clear.mark" commandName="Clear Mark" description="Clear the mark" category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtx6Go6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.project.buildAll" commandName="Build All" description="Build all projects" category="_FFsjoWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtx6Wo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.debug.ui.command.reverseStepOver" commandName="Reverse Step Over" description="Perform Reverse Step Over" category="_FFsjrWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtx6mo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.cut.line.to.beginning" commandName="Cut to Beginning of Line" description="Cut to the beginning of a line of text" category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtx62o6EfCBSKlQ60Nj_w" elementId="org.eclipse.text.quicksearch.commands.quicksearchCommand" commandName="Quick Search" category="_FFsjnGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtx7Go6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.editors.revisions.rendering.cycle" commandName="Cycle Revision Coloring Mode" description="Cycles through the available coloring modes for revisions" category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtx7Wo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.debug.ui.command.editRegisterGroup" commandName="Edit Register Group" description="Edits a Register Group" category="_FFsjlmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtx7mo6EfCBSKlQ60Nj_w" elementId="com.st.stm32cube.ide.mcu.ide.command.generatecode" commandName="Generate Code" description="Generate Code (based on .ioc file content)" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtx72o6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.search.findrefs.project" commandName="References in Project" description="Searches for references to the selected element in the enclosing project" category="_FFsjk2o6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtx8Go6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.search.finddecl.project" commandName="Declaration in Project" description="Searches for declarations of the selected element in the enclosing project" category="_FFsjk2o6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtx8Wo6EfCBSKlQ60Nj_w" elementId="org.eclipse.help.ui.closeTray" commandName="Close User Assistance Tray" description="Close the user assistance tray containing context help information and cheat sheets." category="_FFsjoGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtx8mo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ltk.ui.refactoring.commands.moveResources" commandName="Move Resources" description="Move the selected resources and notify LTK participants." category="_FFsjq2o6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtx82o6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.managedbuilder.ui.rebuildConfigurations" commandName="Build Selected Configurations" category="_FFsjsGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtx9Go6EfCBSKlQ60Nj_w" elementId="org.eclipse.debug.ui.commands.Resume" commandName="Resume" description="Resume" category="_FFsjqGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtx9Wo6EfCBSKlQ60Nj_w" elementId="org.eclipse.debug.ui.commands.StepInto" commandName="Step Into" description="Step into" category="_FFsjqGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFtx9mo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.file.restartWorkbench" commandName="Restart" description="Restart the workbench" category="_FFsjmmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuYwGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.e4.ui.importer.openDirectory" commandName="Open Projects from File System..." category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuYwWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.toggleOverwrite" commandName="Toggle Overwrite" description="Toggle overwrite mode" category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuYwmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.folding.collapse_all" commandName="Collapse All" description="Collapses all folded regions" category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuYw2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.debug.ui.memory.memorybrowser.jumpToMemory" commandName="Jump to Memory" description="Open memory view and add memory monitor for address" category="_FFsjqGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuYxGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.debug.ui.commands.ToggleWatchpoint" commandName="Toggle Watchpoint" description="Creates or removes a watchpoint" category="_FFsjqGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuYxWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.equinox.p2.ui.discovery.commands.ShowRepositoryCatalog" commandName="Show Repository Catalog" category="_FFsjrmo6EfCBSKlQ60Nj_w">
    <parameters xmi:id="_FFuYxmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.equinox.p2.ui.discovery.commands.RepositoryParameter" name="P2 Repository URI"/>
  </commands>
  <commands xmi:id="_FFuYx2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.debug.ui.commands.closeRendering" commandName="Close Rendering" description="Close the selected rendering." category="_FFsjqGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuYyGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.menu.wsselection.command" commandName="Manage Working Sets" category="_FFsjoWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuYyWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.window.showViewMenu" commandName="Show View Menu" description="Show the view menu" category="_FFsjnWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuYymo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.edit.text.c.source.quickMenu" commandName="Show Source Quick Menu" description="Shows the source quick menu" category="_FFsjk2o6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuYy2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.debug.ui.commands.ProfileLast" commandName="Profile" description="Launch in profile mode" category="_FFsjqGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuYzGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.shiftRight" commandName="Shift Right" description="Shift a block of text to the right" category="_FFsjkmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuYzWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.navigate.backwardHistory" commandName="Backward History" description="Move backward in the editor navigation history" category="_FFsjmGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuYzmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.removeTrailingWhitespace" commandName="Remove Trailing Whitespace" description="Removes the trailing whitespace of each line" category="_FFsjmmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuYz2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.findIncremental" commandName="Incremental Find" description="Incremental find" category="_FFsjkmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuY0Go6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.swap.mark" commandName="Swap Mark" description="Swap the mark with the cursor position" category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuY0Wo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.edit.opencview" commandName="Show in C/C++ Project view" description="Shows the selected resource in the C/C++ Project view" category="_FFsjk2o6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuY0mo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.navigate.open.type.in.hierarchy" commandName="Open Type in Hierarchy" description="Open a type in the type hierarchy view" category="_FFsjmGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuY02o6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.navigate.open.element.in.call.hierarchy" commandName="Open Element in Call Hierarchy" description="Open an element in the call hierarchy view" category="_FFsjmGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuY1Go6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.shiftLeft" commandName="Shift Left" description="Shift a block of text to the left" category="_FFsjkmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuY1Wo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.debug.command.breakpointProperties" commandName="C/C++ Breakpoint Properties" description="View and edit properties for a given C/C++ breakpoint" category="_FFsjqGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuY1mo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.debug.ui.command.uncall" commandName="Uncall" description="Perform Uncall" category="_FFsjrWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuY12o6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.make.ui.edit.text.makefile.opendecl" commandName="Open declaration" description="Follow to the directive definition" category="_FFsjpWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuY2Go6EfCBSKlQ60Nj_w" elementId="org.eclipse.debug.ui.commands.Restart" commandName="Restart" description="Restart a process or debug target without terminating and re-launching" category="_FFsjqGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuY2Wo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.delete.line" commandName="Delete Line" description="Delete a line of text" category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuY2mo6EfCBSKlQ60Nj_w" elementId="org.eclipse.debug.ui.DebugPerspective" commandName="Debug" description="Open the debug perspective" category="_FFsjqmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuY22o6EfCBSKlQ60Nj_w" elementId="com.st.stm32cube.ide.mcu.debug.launch.command.restartConfigurationCommand" commandName="Restart Configuration Command" category="_FFsjnmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuY3Go6EfCBSKlQ60Nj_w" elementId="AnsiConsole.command.copy_without_escapes" commandName="Copy Text Without ANSI Escapes" description="Copy the console content to clipboard, removing the escape sequences" category="_FFsjmWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuY3Wo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.navigate.expandAll" commandName="Expand All" description="Expand the current tree" category="_FFsjmGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuY3mo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.file.saveAll" commandName="Save All" description="Save all current contents" category="_FFsjmmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuY32o6EfCBSKlQ60Nj_w" elementId="com.st.stm32cube.ide.mcu.externaltools.test" commandName="Test ExternalTools" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuY4Go6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.file.closeOthers" commandName="Close Others" description="Close all editors except the one that is active" category="_FFsjmmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuY4Wo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.editors.quickdiff.revertLine" commandName="Revert Line" description="Revert the current line" category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuY4mo6EfCBSKlQ60Nj_w" elementId="org.eclipse.debug.ui.commands.OpenDebugConfigurations" commandName="Debug..." description="Open debug launch configuration dialog" category="_FFsjqGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuY42o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.window.previousEditor" commandName="Previous Editor" description="Switch to the previous editor" category="_FFsjnWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuY5Go6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.findReplace" commandName="Find and Replace" description="Find and replace text" category="_FFsjkmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuY5Wo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.zoomIn" commandName="Zoom In" description="Zoom in text, increase default font size for text editors" category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuY5mo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.ide.OpenMarkersView" commandName="Open Another" description="Open another view" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuY52o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.window.fullscreenmode" commandName="Toggle Full Screen" description="Toggles the window between full screen and normal" category="_FFsjnWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuY6Go6EfCBSKlQ60Nj_w" elementId="org.eclipse.debug.ui.commands.newRendering" commandName="New Rendering" description="Add a new rendering." category="_FFsjqGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuY6Wo6EfCBSKlQ60Nj_w" elementId="org.eclipse.equinox.p2.ui.sdk.installationDetails" commandName="Installation Details" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuY6mo6EfCBSKlQ60Nj_w" elementId="org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize resources in the workspace with another location" category="_FFsjkGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuY62o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.window.nextEditor" commandName="Next Editor" description="Switch to the next editor" category="_FFsjnWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuY7Go6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.project.closeUnrelatedProjects" commandName="Close Unrelated Projects" description="Close unrelated projects" category="_FFsjoWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuY7Wo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.ide.markers.copyMarkerResourceQualifiedName" commandName="Copy Resource Qualified Name To Clipboard" description="Copies markers resource qualified name to the clipboard" category="_FFsjkmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuY7mo6EfCBSKlQ60Nj_w" elementId="com.st.stm32cube.ide.common.ui.view_export" commandName="Export view data to file" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuY72o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.help.helpContents" commandName="Help Contents" description="Open the help contents" category="_FFsjoGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuY8Go6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.delete" commandName="Delete" description="Delete the selection" category="_FFsjkmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuY8Wo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.delete.line.to.beginning" commandName="Delete to Beginning of Line" description="Delete to the beginning of a line of text" category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuY8mo6EfCBSKlQ60Nj_w" elementId="org.eclipse.search.ui.performTextSearchWorkspace" commandName="Find Text in Workspace" description="Searches the files in the workspace for specific text." category="_FFsjpmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuY82o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.findPrevious" commandName="Find Previous" description="Find previous item" category="_FFsjkmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuY9Go6EfCBSKlQ60Nj_w" elementId="org.eclipse.debug.ui.commands.ToggleBreakpoint" commandName="Toggle Breakpoint" description="Creates or removes a breakpoint" category="_FFsjqGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuY9Wo6EfCBSKlQ60Nj_w" elementId="org.eclipse.launchbar.ui.command.buildActive" commandName="Build Active Launch Configuration" category="_FFsjp2o6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuY9mo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.addBookmark" commandName="Add Bookmark" description="Add a bookmark" category="_FFsjkmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuY92o6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.debug.ui.command.reverseToggle" commandName="Reverse Toggle" description="Toggle Reverse Debugging" category="_FFsjrWo6EfCBSKlQ60Nj_w">
    <parameters xmi:id="_FFuY-Go6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.commands.radioStateParameter" name="TraceMethod" optional="false"/>
  </commands>
  <commands xmi:id="_FFuY-Wo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.scroll.lineUp" commandName="Scroll Line Up" description="Scroll up one line of text" category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuY-mo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.menu.findUnresolvedIncludes" commandName="Search for Unresolved Includes" category="_FFsjoWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuY-2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.set.mark" commandName="Set Mark" description="Set the mark" category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuY_Go6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.folding.toggle" commandName="Toggle Folding" description="Toggles folding in the current editor" category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuY_Wo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.toggleShowWhitespaceCharacters" commandName="Show Whitespace Characters" description="Shows whitespace characters in current text editor" category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuY_mo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.gotoNextEditPosition" commandName="Next Edit Location" description="Next edit location" category="_FFsjmGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuY_2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.file.revert" commandName="Revert" description="Revert to the last saved state" category="_FFsjmmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuZAGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.views.showView" commandName="Show View" description="Shows a particular view" category="_FFsjkWo6EfCBSKlQ60Nj_w">
    <parameters xmi:id="_FFuZAWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.views.showView.viewId" name="View"/>
    <parameters xmi:id="_FFuZAmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.views.showView.secondaryId" name="Secondary Id"/>
    <parameters xmi:id="_FFuZA2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.views.showView.makeFast" name="As FastView"/>
  </commands>
  <commands xmi:id="_FFuZBGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.refactor.hide.method" commandName="Hide Member Function..." category="_FFsjn2o6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuZBWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.make.ui.edit.text.makefile.toggle.comment" commandName="Toggle Comment" description="Comment/uncomment selected lines with # style comments" category="_FFsjpWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuZBmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.navigate.showResourceByPath" commandName="Show Resource in Navigator" description="Show a resource in the Navigator given its path" category="_FFsjmGo6EfCBSKlQ60Nj_w">
    <parameters xmi:id="_FFuZB2o6EfCBSKlQ60Nj_w" elementId="resourcePath" name="Resource Path" typeId="org.eclipse.ui.ide.resourcePath" optional="false"/>
  </commands>
  <commands xmi:id="_FFuZCGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.debug.ui.commands.SkipAllBreakpoints" commandName="Skip All Breakpoints" description="Sets whether or not any breakpoint should suspend execution" category="_FFsjqGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuZCWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.delimiter.windows" commandName="Convert Line Delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" description="Converts the line delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" category="_FFsjmmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuZCmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.findNext" commandName="Find Next" description="Find next item" category="_FFsjkmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuZC2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.window.hidetrimbars" commandName="Toggle visibility of the window toolbars" description="Toggle the visibility of the toolbars of the current window" category="_FFsjnWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuZDGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.project.buildAutomatically" commandName="Build Automatically" description="Toggle the workspace build automatically function" category="_FFsjoWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuZDWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.file.import" commandName="Import" description="Import" category="_FFsjmmo6EfCBSKlQ60Nj_w">
    <parameters xmi:id="_FFuZDmo6EfCBSKlQ60Nj_w" elementId="importWizardId" name="Import Wizard"/>
  </commands>
  <commands xmi:id="_FFuZD2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.window.switchToEditor" commandName="Switch to Editor" description="Switch to an editor" category="_FFsjnWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuZEGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.help.dynamicHelp" commandName="Show Context Help" description="Open the contextual help" category="_FFsjoGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuZEWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.navigate.nextSubTab" commandName="Next Sub-Tab" description="Switch to the next sub-tab" category="_FFsjmGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuZEmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.toMultiSelection" commandName="To multi-selection" description="Turn current selection into multiple text selections" category="_FFsjkmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuZE2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.edit.text.c.goto.next.member" commandName="Go to Next Member" description="Move the caret to the next member of the translation unit" category="_FFsjk2o6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuZFGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.addTask" commandName="Add Task..." description="Add a task" category="_FFsjkmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuZFWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.toggleInsertMode" commandName="Toggle Insert Mode" description="Toggle insert mode" category="_FFsjkmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuZFmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.cheatsheets.openCheatSheet" commandName="Open Cheat Sheet" description="Open a Cheat Sheet." category="_FFsjoGo6EfCBSKlQ60Nj_w">
    <parameters xmi:id="_FFuZF2o6EfCBSKlQ60Nj_w" elementId="cheatSheetId" name="Identifier"/>
  </commands>
  <commands xmi:id="_FFuZGGo6EfCBSKlQ60Nj_w" elementId="com.st.stm32cube.common.mx.menu.generatecode" commandName="Generate Code" description="Generate Code" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuZGWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.file.closePart" commandName="Close Part" description="Close the active workbench part" category="_FFsjnWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuZGmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.editors.revisions.id.toggle" commandName="Toggle Revision Id Display" description="Toggles the display of the revision id" category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuZG2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.project.cleanAction" commandName="Build Clean" description="Discard old built state" category="_FFsjoWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuZHGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.select.multiCaretDown" commandName="Multi caret down" description="Add a new caret/multi selection below the current line, or remove the first caret/multi selection " category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuZHWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.moveLineDown" commandName="Move Lines Down" description="Moves the selected lines down" category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuZHmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.navigate.back" commandName="Back" description="Navigate back" category="_FFsjmGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuZH2o6EfCBSKlQ60Nj_w" elementId="com.st.stm32cube.common.mx.datarefresh" commandName="Data Refresh" description="Data Refresh" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuZIGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.select.wordNext" commandName="Select Next Word" description="Select the next word" category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuZIWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.equinox.p2.ui.sdk.update" commandName="Check for Updates" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuZImo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.goto.pageDown" commandName="Page Down" description="Go down one page" category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuZI2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.goto.textEnd" commandName="Text End" description="Go to the end of the text" category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuZJGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.launchbar.ui.command.launchActive" commandName="Launch Active Launch Configuration" category="_FFsjp2o6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuZJWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.refactoring.command.ExtractConstant" commandName="Extract Constant..." category="_FFsjn2o6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuZJmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.part.previousPage" commandName="Previous Page" description="Switch to the previous page" category="_FFsjmGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuZJ2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.edit.open.quick.type.hierarchy" commandName="Quick Type Hierarchy" description="Shows quick type hierarchy" category="_FFsjk2o6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuZKGo6EfCBSKlQ60Nj_w" elementId="com.st.stm32cube.ide.mcu.buildanalyzer.showInCommand" commandName="name" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuZKWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.hover.backwardMacroExpansion" commandName="Back" description="Steps backward in macro expansions" category="_FFsjk2o6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuZKmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.debug.ui.command.loadAllSymbols" commandName="Load Symbols For All" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuZK2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.project.closeProject" commandName="Close Project" description="Close the selected project" category="_FFsjoWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuZLGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.edit.text.c.indent" commandName="Indent Line" description="Indents the current line" category="_FFsjk2o6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuZLWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.menu.createParserLog" commandName="Create Parser Log File" category="_FFsjoWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuZLmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.findIncrementalReverse" commandName="Incremental Find Reverse" description="Incremental find reverse" category="_FFsjkmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuZL2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.epp.mpc.ui.command.importFavoritesWizard" commandName="Import Marketplace Favorites" description="Import another user's Marketplace Favorites List" category="_FFsjrmo6EfCBSKlQ60Nj_w">
    <parameters xmi:id="_FFuZMGo6EfCBSKlQ60Nj_w" elementId="favoritesUrl" name="favoritesUrl"/>
  </commands>
  <commands xmi:id="_FFuZMWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.select.addAllMatchesToMultiSelection" commandName="Add all matches to multi-selection" description="Looks for all regions matching the current selection or identifier and adds them to a multi-selection " category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuZMmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.edit.text.c.goto.next.bookmark" commandName="Next Bookmark" description="Goes to the next bookmark of the selected file" category="_FFsjk2o6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuZM2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.debug.ui.command.debugNewExecutable" commandName="Debug New Executable" description="Debug a new executable" category="_FFsjo2o6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuZNGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.debug.ui.command.addRegisterGroup" commandName="Add RegisterGroup" description="Adds a Register Group" category="_FFsjlmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuZNWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.debug.ui.command.resumeWithoutSignal" commandName="Resume Without Signal" description="Resume Without Signal" category="_FFsjl2o6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuZNmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.dsf.gdb.ui.command.selectNextTraceRecord" commandName="Next Trace Record" description="Select Next Trace Record" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuZN2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.search.ui.performTextSearchFile" commandName="Find Text in File" description="Searches the files in the file for specific text." category="_FFsjpmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFuZOGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.goto.columnNext" commandName="Next Column" description="Go to the next column" category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFu_0Go6EfCBSKlQ60Nj_w" elementId="org.eclipse.search.ui.performTextSearchWorkingSet" commandName="Find Text in Working Set" description="Searches the files in the working set for specific text." category="_FFsjpmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFu_0Wo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.edit.text.c.add.include" commandName="Add Include" description="Create include statement on selection" category="_FFsjk2o6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFu_0mo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.goto.wordNext" commandName="Next Word" description="Go to the next word" category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFu_02o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.cut.line" commandName="Cut Line" description="Cut a line of text, or multiple lines when invoked again without interruption" category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFu_1Go6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.folding.expand_all" commandName="Expand All" description="Expands all folded regions" category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFu_1Wo6EfCBSKlQ60Nj_w" elementId="org.eclipse.quickdiff.toggle" commandName="Quick Diff Toggle" description="Toggles quick diff information display on the line number ruler" category="_FFsjkmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFu_1mo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.deleteNext" commandName="Delete Next" description="Delete the next character" category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFu_12o6EfCBSKlQ60Nj_w" elementId="com.st.stm32cube.ide.mcu.buildanalyzer.showstate" commandName="name" category="_FFsjrmo6EfCBSKlQ60Nj_w">
    <parameters xmi:id="_FFu_2Go6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.commands.radioStateParameter" name="State" optional="false"/>
  </commands>
  <commands xmi:id="_FFu_2Wo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.toggleShowKeys" commandName="Toggle Show Key Bindings" description="Shows key binding when command is invoked" category="_FFsjnWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFu_2mo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.help.quickStartAction" commandName="Welcome" description="Show help for beginning users" category="_FFsjoGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFu_22o6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.hover.forwardMacroExpansion" commandName="Forward" description="Steps forward in macro expansions" category="_FFsjk2o6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFu_3Go6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.window.hideShowEditors" commandName="Toggle Shared Area Visibility" description="Toggles the visibility of the shared area" category="_FFsjnWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFu_3Wo6EfCBSKlQ60Nj_w" elementId="com.st.stm32cube.common.mx.docsandresources" commandName="Docs And Resources" description="Docs And Resources" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFu_3mo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.help.displayHelp" commandName="Display Help" description="Display a Help topic" category="_FFsjoGo6EfCBSKlQ60Nj_w">
    <parameters xmi:id="_FFu_32o6EfCBSKlQ60Nj_w" elementId="href" name="Help topic href"/>
  </commands>
  <commands xmi:id="_FFu_4Go6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.folding.restore" commandName="Reset Structure" description="Resets the folding structure" category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFu_4Wo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.select.pageDown" commandName="Select Page Down" description="Select to the bottom of the page" category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFu_4mo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.join.lines" commandName="Join Lines" description="Join lines of text" category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFu_42o6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.dsf.ui.addRegistersExpression" commandName="Add Expression Group > Registers" category="_FFsjqGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFu_5Go6EfCBSKlQ60Nj_w" elementId="org.eclipse.help.ui.indexcommand" commandName="Index" description="Show Keyword Index" category="_FFsjoGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFu_5Wo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.window.showContextMenu" commandName="Show Context Menu" description="Show the context menu" category="_FFsjnWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFu_5mo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.debug.ui.command.restoreRegisterGroups" commandName="Restore Default Register Groups" description="Restores the Default Register Groups" category="_FFsjlmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFu_52o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.navigate.forward" commandName="Forward" description="Navigate forward" category="_FFsjmGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFu_6Go6EfCBSKlQ60Nj_w" elementId="org.eclipse.jdt.ui.edit.text.java.correction.assist.proposals" commandName="Quick Fix" description="Suggest possible fixes for a problem" category="_FFsjkmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFu_6Wo6EfCBSKlQ60Nj_w" elementId="org.eclipse.launchbar.ui.command.configureActiveLaunch" commandName="Edit Active Launch Configuration" category="_FFsjp2o6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFu_6mo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.window.nextPerspective" commandName="Next Perspective" description="Switch to the next perspective" category="_FFsjnWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFu_62o6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.edit.open.call.hierarchy" commandName="Open Call Hierarchy" description="Opens the call hierarchy for the selected element" category="_FFsjmGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFu_7Go6EfCBSKlQ60Nj_w" elementId="org.eclipse.debug.ui.commands.console.clear" commandName="Clear Console" description="Clear Console" category="_FFsjkmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFu_7Wo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.dsf.ui.addLocalsExpression" commandName="Add Expression Group > Local Variables" category="_FFsjqGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFu_7mo6EfCBSKlQ60Nj_w" elementId="com.st.stm32cube.common.mx.generatereport" commandName="Generate Report" description="Generate Report" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFu_72o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.window.pinEditor" commandName="Pin Editor" description="Pin the current editor" category="_FFsjnWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFu_8Go6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.navigate.nextTab" commandName="Next Tab" description="Switch to the next tab" category="_FFsjmGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFu_8Wo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.window.quickAccess" commandName="Find Actions" description="Quickly access UI elements" category="_FFsjnWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFu_8mo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.showInformation" commandName="Show Tooltip Description" description="Displays information for the current caret location in a focused hover" category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFu_82o6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.refactor.override.methods" commandName="Override Methods..." description="Generates override methods for a selected class" category="_FFsjk2o6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFu_9Go6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.commands.gotoPC" commandName="Go to Program Counter" description="Navigate to current program counter" category="_FFsjqGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFu_9Wo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.goto.pageUp" commandName="Page Up" description="Go up one page" category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFu_9mo6EfCBSKlQ60Nj_w" elementId="org.eclipse.tm.terminal.copy" commandName="Copy" category="_FFsjpGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFu_92o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.goto.columnPrevious" commandName="Previous Column" description="Go to the previous column" category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFu_-Go6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.refactor.getters.and.setters" commandName="Generate Getters and Setters..." description="Generates getters and setters for a selected field" category="_FFsjk2o6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFu_-Wo6EfCBSKlQ60Nj_w" elementId="org.eclipse.debug.ui.commands.toggleMemoryMonitorsPane" commandName="Toggle Memory Monitors Pane" description="Toggle visibility of the Memory Monitors Pane" category="_FFsjqGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFu_-mo6EfCBSKlQ60Nj_w" elementId="org.eclipse.compare.selectNextChange" commandName="Select Next Change" description="Select Next Change" category="_FFsjm2o6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFu_-2o6EfCBSKlQ60Nj_w" elementId="com.st.stm32cube.ide.mcu.informationcenter.tutorialvideo" commandName="Tutorial Video" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFu__Go6EfCBSKlQ60Nj_w" elementId="org.eclipse.ltk.ui.refactoring.commands.renameResource" commandName="Rename Resource" description="Rename the selected resource and notify LTK participants." category="_FFsjq2o6EfCBSKlQ60Nj_w">
    <parameters xmi:id="_FFu__Wo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ltk.ui.refactoring.commands.renameResource.newName.parameter.key" name="Selected resource's new name."/>
  </commands>
  <commands xmi:id="_FFu__mo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.edit.open.quick.macro.explorer" commandName="Explore Macro Expansion" description="Opens a quick view for macro expansion exploration" category="_FFsjk2o6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFu__2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.file.properties" commandName="Properties" description="Display the properties of the selected item" category="_FFsjmmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvAAGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.showRulerAnnotationInformation" commandName="Show Ruler Annotation Tooltip" description="Displays annotation information for the caret line in a focused hover" category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvAAWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.refactor.toggle.function" commandName="Toggle Function - Refactoring " description="Toggles the implementation between header and implementation file" category="_FFsjn2o6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvAAmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.browser.openBrowser" commandName="Open Browser" description="Opens the default web browser." category="_FFsjnWo6EfCBSKlQ60Nj_w">
    <parameters xmi:id="_FFvAA2o6EfCBSKlQ60Nj_w" elementId="url" name="URL"/>
    <parameters xmi:id="_FFvABGo6EfCBSKlQ60Nj_w" elementId="browserId" name="Browser Id"/>
    <parameters xmi:id="_FFvABWo6EfCBSKlQ60Nj_w" elementId="name" name="Browser Name"/>
    <parameters xmi:id="_FFvABmo6EfCBSKlQ60Nj_w" elementId="tooltip" name="Browser Tooltip"/>
  </commands>
  <commands xmi:id="_FFvAB2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.activeContextInfo" commandName="Show activeContext Info" category="_FFsjnWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvACGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.select.textStart" commandName="Select Text Start" description="Select to the beginning of the text" category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvACWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.navigate.opentype" commandName="Open Element" description="Open an element in an Editor" category="_FFsjk2o6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvACmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.contentAssist.contextInformation" commandName="Context Information" description="Show Context Information" category="_FFsjkmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvAC2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.file.saveAs" commandName="Save As" description="Save the current contents to another location" category="_FFsjmmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvADGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.debug.ui.command.reverseResume" commandName="Reverse Resume" description="Perform Reverse Resume" category="_FFsjrWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvADWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.window.previousPerspective" commandName="Previous Perspective" description="Switch to the previous perspective" category="_FFsjnWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvADmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.window.splitEditor" commandName="Toggle Split Editor" description="Split or join the currently active editor." category="_FFsjnWo6EfCBSKlQ60Nj_w">
    <parameters xmi:id="_FFvAD2o6EfCBSKlQ60Nj_w" elementId="Splitter.isHorizontal" name="Orientation" optional="false"/>
  </commands>
  <commands xmi:id="_FFvAEGo6EfCBSKlQ60Nj_w" elementId="com.st.stm32cube.common.mx.toolbar.generatecode" commandName="Generate Code" description="Generate Code" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvAEWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.menu.freshenAllFiles" commandName="Freshen All Files in Index" category="_FFsjoWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvAEmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.navigate.collapseAll" commandName="Collapse All" description="Collapse the current tree" category="_FFsjmGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvAE2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.compare.copyAllRightToLeft" commandName="Copy All from Right to Left" description="Copy All Changes from Right to Left" category="_FFsjm2o6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvAFGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.debug.ui.command.nextpage" commandName="Next Page of Memory" description="Load next page of memory" category="_FFsjqGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvAFWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.debug.ui.command.gotoaddress" commandName="Go to Address" description="Go to Address" category="_FFsjqGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvAFmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.remote.ui.command.newConnection" commandName="New Connection" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvAF2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.window.maximizePart" commandName="Maximize Active View or Editor" description="Toggles maximize/restore state of active view or editor" category="_FFsjnWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvAGGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.menu.updateWithModifiedFiles" commandName="Update Index with Modified Files" category="_FFsjoWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvAGWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.window.lockToolBar" commandName="Toggle Lock Toolbars" description="Toggle the Lock on the Toolbars" category="_FFsjnWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvAGmo6EfCBSKlQ60Nj_w" elementId="com.st.stm32cube.ide.mcu.debug.swv.core.start_trace" commandName="Start Trace" description="Start Trace" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvAG2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.edit.text.c.find.word" commandName="Find Word" description="Selects a word and find the next occurrence" category="_FFsjk2o6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvAHGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.window.newEditor" commandName="Clone Editor" description="Open another editor on the active editor's input" category="_FFsjnWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvAHWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.debug.ui.commands.Disconnect" commandName="Disconnect" description="Disconnect" category="_FFsjqGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvAHmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.search.ui.openSearchDialog" commandName="Open Search Dialog" description="Open the Search dialog" category="_FFsjpmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvAH2o6EfCBSKlQ60Nj_w" elementId="com.st.stm32cube.ide.mcu.build.ui.commands.modifyIncludePathsBySelection" commandName="Add/remove include path..." category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvAIGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.contentAssist.proposals" commandName="Content Assist" description="Content Assist" category="_FFsjkmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvAIWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.edit.text.c.select.previous" commandName="Select Previous C/C++ Element" description="Expand the selection to enclosing C/C++ element" category="_FFsjkmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvAImo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.navigate.up" commandName="Up" description="Navigate up one level" category="_FFsjmGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvAI2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.window.activateEditor" commandName="Activate Editor" description="Activate the editor" category="_FFsjnWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvAJGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.navigate.removeFromWorkingSet" commandName="Remove From Working Set" description="Removes the selected object from a working set." category="_FFsjkmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvAJWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.delete.line.to.end" commandName="Delete to End of Line" description="Delete to the end of a line of text" category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvAJmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.debug.ui.commands.Terminate" commandName="Terminate" description="Terminate" category="_FFsjqGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvAJ2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.help.ui.ignoreMissingPlaceholders" commandName="Do not warn of missing documentation" description="Sets the help preferences to no longer report a warning about the current set of missing documents." category="_FFsjoGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvAKGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.ide.deleteCompleted" commandName="Delete Completed Tasks" description="Delete the tasks marked as completed" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvAKWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.compare.compareWithOther" commandName="Compare With Other Resource" description="Compare resources, clipboard contents or editors" category="_FFsjm2o6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvAKmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.edit.text.rename.element" commandName="Rename - Refactoring " description="Renames the selected element" category="_FFsjn2o6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvAK2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.managedbuilder.ui.cleanFiles" commandName="Clean Selected File(s)" description="Deletes build output files for the selected source files" category="_FFsjsGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvALGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.editors.revisions.author.toggle" commandName="Toggle Revision Author Display" description="Toggles the display of the revision author" category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvALWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.goto.windowEnd" commandName="Window End" description="Go to the end of the window" category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvALmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.edit.text.c.goto.prev.member" commandName="Go to Previous Member" description="Move the caret to the previous member of the translation unit" category="_FFsjk2o6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvAL2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.perspectives.showPerspective" commandName="Show Perspective" description="Show a particular perspective" category="_FFsjqmo6EfCBSKlQ60Nj_w">
    <parameters xmi:id="_FFvAMGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.perspectives.showPerspective.perspectiveId" name="Parameter"/>
    <parameters xmi:id="_FFvAMWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.perspectives.showPerspective.newWindow" name="In New Window"/>
  </commands>
  <commands xmi:id="_FFvAMmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.window.openEditorDropDown" commandName="Quick Switch Editor" description="Open the editor drop down list" category="_FFsjnWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvAM2o6EfCBSKlQ60Nj_w" elementId="AnsiConsole.command.copy_with_escapes" commandName="Copy Text With ANSI Escapes" description="Copy the console content to clipboard, including the escape sequences" category="_FFsjmWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvANGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.goto.line" commandName="Go to Line" description="Go to a specified line of text" category="_FFsjmGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvANWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.editors.quickdiff.revert" commandName="Revert Lines" description="Revert the current selection, block or deleted lines" category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvANmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.deleteNextWord" commandName="Delete Next Word" description="Delete the next word" category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvAN2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.dsf.debug.ui.refreshAll" commandName="Refresh Debug Views" description="Refresh all data in debug views" category="_FFsjqGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvAOGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.select.lineUp" commandName="Select Line Up" description="Extend the selection to the previous line of text" category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvAOWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.undo" commandName="Undo" description="Undo the last operation" category="_FFsjkmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvAOmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.cut.line.to.end" commandName="Cut to End of Line" description="Cut to the end of a line of text" category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvAO2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.compare.copyLeftToRight" commandName="Copy from Left to Right" description="Copy Current Change from Left to Right" category="_FFsjm2o6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvAPGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.file.newQuickMenu" commandName="New menu" description="Open the New menu" category="_FFsjmmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvAPWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.project.openProject" commandName="Open Project" description="Open a project" category="_FFsjoWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvAPmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.debug.ui.actions.WatchCommand" commandName="Watch" description="Create a watch expression from the current selection and add it to the Expressions view" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvAP2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.file.openWorkspace" commandName="Switch Workspace" description="Open the workspace selection dialog" category="_FFsjmmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvAQGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.file.closeAll" commandName="Close All" description="Close all editors" category="_FFsjmmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvAQWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.cut" commandName="Cut" description="Cut the selection to the clipboard" category="_FFsjkmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvAQmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.project.properties" commandName="Properties" description="Display the properties of the selected item's project " category="_FFsjoWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvAQ2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.edit.text.c.toggleMarkOccurrences" commandName="Toggle Mark Occurrences" description="Toggles mark occurrences in C/C++ editors" category="_FFsjk2o6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvARGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.edit.text.c.surround.with.quickMenu" commandName="Surround With Quick Menu" description="Shows the Surround With quick menu" category="_FFsjk2o6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvARWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.select.columnPrevious" commandName="Select Previous Column" description="Select the previous column" category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvARmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.toggleShowSelectedElementOnly" commandName="Show Selected Element Only" description="Show Selected Element Only" category="_FFsjnWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvAR2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.edit.text.c.uncomment" commandName="Uncomment" description="Uncomments the selected // style comment lines" category="_FFsjk2o6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvASGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.file.export" commandName="Export" description="Export" category="_FFsjmmo6EfCBSKlQ60Nj_w">
    <parameters xmi:id="_FFvASWo6EfCBSKlQ60Nj_w" elementId="exportWizardId" name="Export Wizard"/>
  </commands>
  <commands xmi:id="_FFvASmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ltk.ui.refactoring.commands.deleteResources" commandName="Delete Resources" description="Delete the selected resources and notify LTK participants." category="_FFsjq2o6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvm4Go6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.file.print" commandName="Print" description="Print" category="_FFsjmmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvm4Wo6EfCBSKlQ60Nj_w" elementId="com.st.stm32cube.ide.mcu.build.ui.commands.convert2ccpp" commandName="Convert Project to C or CPP" category="_FFsjrmo6EfCBSKlQ60Nj_w">
    <parameters xmi:id="_FFvm4mo6EfCBSKlQ60Nj_w" elementId="com.st.stm32cube.ide.mcu.build.ui.commands.convert2ccpp.type" name="Convert Type (C/C++)"/>
  </commands>
  <commands xmi:id="_FFvm42o6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.make.ui.targetBuildCommand" commandName="Build Target Build" description="Invoke a make target build for the selected container." category="_FFsjoWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvm5Go6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.select.selectMultiSelectionDown" commandName="Multi selection down relative to anchor selection  " description="Search next matching region and add it to the current selection, or remove first element from current multi-selection " category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvm5Wo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.deleteConfigsCommand" commandName="Reset to Default" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvm5mo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.edit.text.c.organize.includes" commandName="Organize Includes" description="Evaluates all required includes and replaces the current includes" category="_FFsjk2o6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvm52o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.showRulerContextMenu" commandName="Show Ruler Context Menu" description="Show the context menu for the ruler" category="_FFsjnWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvm6Go6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.ide.copyConfigCommand" commandName="Copy Configuration Data To Clipboard" description="Copies the configuration data (system properties, installed bundles, etc) to the clipboard." category="_FFsjkmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvm6Wo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.folding.collapse" commandName="Collapse" description="Collapses the folded region at the current selection" category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvm6mo6EfCBSKlQ60Nj_w" elementId="org.eclipse.epp.mpc.ui.command.showFavorites" commandName="Eclipse Marketplace Favorites" description="Open Marketplace Favorites" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvm62o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.smartEnterInverse" commandName="Insert Line Above Current Line" description="Adds a new line above the current line" category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvm7Go6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.navigate.forwardHistory" commandName="Forward History" description="Move forward in the editor navigation history" category="_FFsjmGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvm7Wo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.window.spy" commandName="Show Contributing Plug-in" description="Shows contribution information for the currently selected element" category="_FFsjnWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvm7mo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.edit.text.c.align.const" commandName="Align const qualifiers" description="Moves const qualifiers to the left or right of the type depending on the workspace preferences" category="_FFsjk2o6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvm72o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.help.helpSearch" commandName="Help Search" description="Open the help search" category="_FFsjoGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvm8Go6EfCBSKlQ60Nj_w" elementId="com.st.stm32cube.ide.mcu.ide.connectionToMyST" commandName="Connection to myST" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvm8Wo6EfCBSKlQ60Nj_w" elementId="com.st.stm32cube.ide.mpu.remote.serial.connectconsole" commandName="Connect Console Command" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvm8mo6EfCBSKlQ60Nj_w" elementId="org.eclipse.search.ui.performTextSearchProject" commandName="Find Text in Project" description="Searches the files in the project for specific text." category="_FFsjpmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvm82o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.goto.lineDown" commandName="Line Down" description="Go down one line of text" category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvm9Go6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.project.rebuildAll" commandName="Rebuild All" description="Rebuild all projects" category="_FFsjoWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvm9Wo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.gotoLastEditPosition" commandName="Previous Edit Location" description="Previous edit location" category="_FFsjmGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvm9mo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.open.hyperlink" commandName="Open Hyperlink" description="Opens the hyperlink at the caret location or opens a chooser if more than one hyperlink is available" category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvm92o6EfCBSKlQ60Nj_w" elementId="org.eclipse.debug.ui.command.prevpage" commandName="Previous Page of Memory" description="Load previous page of memory" category="_FFsjqGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvm-Go6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.openLocalFile" commandName="Open File..." description="Open a file" category="_FFsjmmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvm-Wo6EfCBSKlQ60Nj_w" elementId="org.eclipse.launchbar.ui.command.stop" commandName="Stop" category="_FFsjp2o6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvm-mo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.ide.configureFilters" commandName="Filters..." description="Configure the filters to apply to the markers view" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvm-2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.navigate.previousTab" commandName="Previous Tab" description="Switch to the previous tab" category="_FFsjmGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvm_Go6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.dialogs.openMessageDialog" commandName="Open Message Dialog" description="Open a Message Dialog" category="_FFsjqWo6EfCBSKlQ60Nj_w">
    <parameters xmi:id="_FFvm_Wo6EfCBSKlQ60Nj_w" elementId="title" name="Title"/>
    <parameters xmi:id="_FFvm_mo6EfCBSKlQ60Nj_w" elementId="message" name="Message"/>
    <parameters xmi:id="_FFvm_2o6EfCBSKlQ60Nj_w" elementId="imageType" name="Image Type Constant" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_FFvnAGo6EfCBSKlQ60Nj_w" elementId="defaultIndex" name="Default Button Index" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_FFvnAWo6EfCBSKlQ60Nj_w" elementId="buttonLabel0" name="First Button Label"/>
    <parameters xmi:id="_FFvnAmo6EfCBSKlQ60Nj_w" elementId="buttonLabel1" name="Second Button Label"/>
    <parameters xmi:id="_FFvnA2o6EfCBSKlQ60Nj_w" elementId="buttonLabel2" name="Third Button Label"/>
    <parameters xmi:id="_FFvnBGo6EfCBSKlQ60Nj_w" elementId="buttonLabel3" name="Fourth Button Label"/>
    <parameters xmi:id="_FFvnBWo6EfCBSKlQ60Nj_w" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="_FFvnBmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.genericeditor.findReferences" commandName="Find References" description="Find other code items referencing the current selected item." category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnB2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.goto.lineEnd" commandName="Line End" description="Go to the end of the line of text" category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnCGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.file.refresh" commandName="Refresh" description="Refresh the selected items" category="_FFsjmmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnCWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.edit.text.c.goto.matching.bracket" commandName="Go to Matching Bracket" description="Moves the cursor to the matching bracket" category="_FFsjk2o6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnCmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.debug.ui.commands.RunLast" commandName="Run" description="Launch in run mode" category="_FFsjqGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnC2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.window.closeAllPerspectives" commandName="Close All Perspectives" description="Close all open perspectives" category="_FFsjnWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnDGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.externalTools.commands.OpenExternalToolsConfigurations" commandName="External Tools..." description="Open external tools launch configuration dialog" category="_FFsjqGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnDWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.make.ui.targetBuildLastCommand" commandName="Rebuild Last Target" description="Rebuild the last make target for the selected container or project." category="_FFsjoWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnDmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.dsf.gdb.ui.command.osview.connect" commandName="Connect" description="Connect to selected processes" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnD2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.debug.ui.commands.TerminateAll" commandName="Terminate/Disconnect All" description="Terminate/Disconnect All" category="_FFsjqGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnEGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.managedbuilder.ui.convertTarget" commandName="Convert To" category="_FFsjoWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnEWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.deletePrevious" commandName="Delete Previous" description="Delete the previous character" category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnEmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.debug.ui.command.loadSymbols" commandName="Load Symbols" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnE2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.managedbuilder.ui.buildFiles" commandName="Build Selected File(s)" description="Rebuilds the selected source files" category="_FFsjsGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnFGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.debug.ui.command.groupDebugContexts" commandName="Group" description="Groups the selected debug contexts" category="_FFsjo2o6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnFWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.select.lineStart" commandName="Select Line Start" description="Select to the beginning of the line of text" category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnFmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.editors.lineNumberToggle" commandName="Show Line Numbers" description="Toggle display of line numbers" category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnF2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.select.columnNext" commandName="Select Next Column" description="Select the next column" category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnGGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.edit.text.c.select.enclosing" commandName="Select Enclosing C/C++ Element" description="Expand the selection to enclosing C/C++ element" category="_FFsjkmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnGWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.debug.ui.commands.TerminateAndRelaunch" commandName="Terminate and Relaunch" description="Terminate and Relaunch" category="_FFsjqGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnGmo6EfCBSKlQ60Nj_w" elementId="com.st.stm32cube.ide.mcu.debug.launch.command.restartCommand" commandName="Restart Command" category="_FFsjnmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnG2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.tm.terminal.paste" commandName="Paste" category="_FFsjpGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnHGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.genericeditor.gotoMatchingBracket" commandName="Go to Matching Bracket" description="Moves the cursor to the matching bracket" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnHWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.debug.ui.commands.viewMemory" commandName="View Memory" description="View variable in memory view" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnHmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.edit.text.c.select.next" commandName="Select Next C/C++ Element" description="Expand the selection to next C/C++ element" category="_FFsjkmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnH2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.ide.showInSystemExplorer" commandName="Show In (System Explorer)" description="Show in system's explorer (file manager)" category="_FFsjmGo6EfCBSKlQ60Nj_w">
    <parameters xmi:id="_FFvnIGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.ide.showInSystemExplorer.path" name="Resource System Path Parameter"/>
  </commands>
  <commands xmi:id="_FFvnIWo6EfCBSKlQ60Nj_w" elementId="com.st.stm32cube.common.mx.checkforupdates" commandName="Check For Embedded Software Packages Updates" description="Check For Embedded Software Packages Updates" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnImo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.select.lineDown" commandName="Select Line Down" description="Extend the selection to the next line of text" category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnI2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.debug.ui.commands.RemoveAllBreakpoints" commandName="Remove All Breakpoints" description="Removes all breakpoints" category="_FFsjqGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnJGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.navigator.resources.nested.changeProjectPresentation" commandName="P&amp;rojects Presentation" category="_FFsjrmo6EfCBSKlQ60Nj_w">
    <parameters xmi:id="_FFvnJWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.navigator.resources.nested.enabled" name="&amp;Hierarchical"/>
    <parameters xmi:id="_FFvnJmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.commands.radioStateParameter" name="Nested Project view - Radio State" optional="false"/>
  </commands>
  <commands xmi:id="_FFvnJ2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.window.showKeyAssist" commandName="Show Key Assist" description="Show the key assist dialog" category="_FFsjnWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnKGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.genericeditor.togglehighlight" commandName="Toggle Highlight" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnKWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.lowerCase" commandName="To Lower Case" description="Changes the selection to lower case" category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnKmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.zoomOut" commandName="Zoom Out" description="Zoom out text, decrease default font size for text editors" category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnK2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.file.save" commandName="Save" description="Save the current contents" category="_FFsjmmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnLGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.managedbuilder.ui.cleanAllConfigurations" commandName="Clean All Configurations" category="_FFsjsGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnLWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.team.ui.applyPatch" commandName="Apply Patch..." description="Apply a patch to one or more workspace projects." category="_FFsjkGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnLmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.debug.ui.command.saveTraceData" commandName="Save Trace Data " description="Save Trace Data to File" category="_FFsjr2o6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnL2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.help.tipsAndTricksAction" commandName="Tips and Tricks" description="Open the tips and tricks help page" category="_FFsjoGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnMGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.select.lineEnd" commandName="Select Line End" description="Select to the end of the line of text" category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnMWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.smartEnter" commandName="Insert Line Below Current Line" description="Adds a new line below the current line" category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnMmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.goto.lineStart" commandName="Line Start" description="Go to the start of the line of text" category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnM2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.select.wordPrevious" commandName="Select Previous Word" description="Select the previous word" category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnNGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.debug.ui.commands.Suspend" commandName="Suspend" description="Suspend" category="_FFsjqGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnNWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.hippieCompletion" commandName="Word Completion" description="Context insensitive completion" category="_FFsjkmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnNmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.debug.ui.command.StepIntoSelection" commandName="Step Into Selection" description="Step into the current selected statement" category="_FFsjqGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnN2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.remote.ui.command.deleteConnection" commandName="Delete Connection" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnOGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.team.ui.synchronizeLast" commandName="Repeat last synchronization" description="Repeat the last synchronization" category="_FFsjkGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnOWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.debug.ui.commands.StepOver" commandName="Step Over" description="Step over" category="_FFsjqGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnOmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.debug.ui.command.reverseStepInto" commandName="Reverse Step Into" description="Perform Reverse Step Into" category="_FFsjrWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnO2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.compare.selectPreviousChange" commandName="Select Previous Change" description="Select Previous Change" category="_FFsjm2o6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnPGo6EfCBSKlQ60Nj_w" elementId="com.st.stm32cube.ide.mcu.threadx.trx_to_file_command" commandName="Export trace" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnPWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.ide.configureColumns" commandName="Configure Columns..." description="Configure the columns in the markers view" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnPmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.tm.terminal.quickaccess" commandName="Quick Access" category="_FFsjpGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnP2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.file.exit" commandName="Exit" description="Exit the application" category="_FFsjmmo6EfCBSKlQ60Nj_w">
    <parameters xmi:id="_FFvnQGo6EfCBSKlQ60Nj_w" elementId="mayPrompt" name="may prompt"/>
  </commands>
  <commands xmi:id="_FFvnQWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.debug.ui.commands.DebugLast" commandName="Debug" description="Launch in debug mode" category="_FFsjqGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnQmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.window.previousView" commandName="Previous View" description="Switch to the previous view" category="_FFsjnWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnQ2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.debug.ui.commands.addMemoryMonitor" commandName="Add Memory Block" description="Add memory block" category="_FFsjqGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnRGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.selectAll" commandName="Select All" description="Select all" category="_FFsjkmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnRWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.edit.open.type.hierarchy" commandName="Open Type Hierarchy" description="Open a type hierarchy on the selected element" category="_FFsjmGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnRmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.ide.markers.copyDescription" commandName="Copy Description To Clipboard" description="Copies markers description field to the clipboard" category="_FFsjkmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnR2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.debug.ui.command.removeRegisterGroups" commandName="Remove Register Groups" description="Removes one or more Register Groups" category="_FFsjlmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnSGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.refactor.implement.method" commandName="Implement Method - Source Generation " description="Implements a method for a selected method declaration" category="_FFsjk2o6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnSWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.debug.ui.commands.DropToFrame" commandName="Drop to Frame" description="Drop to Frame" category="_FFsjqGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnSmo6EfCBSKlQ60Nj_w" elementId="com.st.stm32cube.ide.cmake.cmake_run_builder" commandName="cmake_run_builder" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnS2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.edit.text.c.add.block.comment" commandName="Add Block Comment" description="Encloses the selection with a block comment" category="_FFsjk2o6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnTGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.folding.expand" commandName="Expand" description="Expands the folded region at the current selection" category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnTWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.project.rebuildProject" commandName="Rebuild Project" description="Rebuild the selected projects" category="_FFsjoWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnTmo6EfCBSKlQ60Nj_w" elementId="com.st.stm32cube.ide.mcu.debug.swv.core.openconfig" commandName="Config" description="Configure SWV" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnT2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.debug.ui.command.castToType" commandName="Cast To Type..." category="_FFsjrGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnUGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.debug.ui.commands.nextMemoryBlock" commandName="Next Memory Monitor" description="Show renderings from next memory monitor." category="_FFsjqGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnUWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.copyLineDown" commandName="Copy Lines" description="Duplicates the selected lines and moves the selection to the copy" category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnUmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.menu.manage.configs.command" commandName="Manage Build Configurations" category="_FFsjoWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnU2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.refactor.extract.local.variable" commandName="Extract Local Variable - Refactoring " description="Extracts a local variable for the selected expression" category="_FFsjn2o6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnVGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.window.togglestatusbar" commandName="Toggle Statusbar" description="Toggle the visibility of the bottom status bar" category="_FFsjnWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnVWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.select.selectMultiSelectionUp" commandName="Multi selection up relative to anchor selection" description="Search next matching region above and add it to the current selection, or remove last element from current multi-selection " category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnVmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.edit.text.c.comment" commandName="Comment" description="Turns the selected lines into // style comments" category="_FFsjk2o6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnV2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.help.installationDialog" commandName="Installation Information" description="Open the installation dialog" category="_FFsjoGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnWGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.select.multiCaretUp" commandName="Multi caret up" description="Add a new caret/multi selection above the current line, or remove the last caret/multi selection " category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnWWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.debug.ui.commands.ToggleStepFilters" commandName="Use Step Filters" description="Toggles enablement of debug step filters" category="_FFsjqGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnWmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.goto.lineUp" commandName="Line Up" description="Go up one line of text" category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnW2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.goto.windowStart" commandName="Window Start" description="Go to the start of the window" category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnXGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.debug.ui.commands.eof" commandName="EOF" description="Send end of file" category="_FFsjqGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnXWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.managedbuilder.ui.buildAllConfigurations" commandName="Build All Configurations" category="_FFsjsGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnXmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.navigate.showInQuickMenu" commandName="Show In..." description="Open the Show In menu" category="_FFsjmGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnX2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.copyLineUp" commandName="Duplicate Lines" description="Duplicates the selected lines and leaves the selection unchanged" category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnYGo6EfCBSKlQ60Nj_w" elementId="com.st.stm32cube.ide.mcu.debug.stlink.fwupgrade" commandName="ST-LINK Upgrade" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnYWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.debug.ui.commands.ToggleMethodBreakpoint" commandName="Toggle Method Breakpoint" description="Creates or removes a method breakpoint" category="_FFsjqGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnYmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.navigate.next" commandName="Next" description="Navigate to the next item" category="_FFsjmGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnY2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.window.closePerspective" commandName="Close Perspective" description="Close the current perspective" category="_FFsjnWo6EfCBSKlQ60Nj_w">
    <parameters xmi:id="_FFvnZGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.window.closePerspective.perspectiveId" name="Perspective Id"/>
  </commands>
  <commands xmi:id="_FFvnZWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.cheatsheets.openCheatSheetURL" commandName="Open Cheat Sheet from URL" description="Open a Cheat Sheet from file at a specified URL." category="_FFsjoGo6EfCBSKlQ60Nj_w">
    <parameters xmi:id="_FFvnZmo6EfCBSKlQ60Nj_w" elementId="cheatSheetId" name="Identifier" optional="false"/>
    <parameters xmi:id="_FFvnZ2o6EfCBSKlQ60Nj_w" elementId="name" name="Name" optional="false"/>
    <parameters xmi:id="_FFvnaGo6EfCBSKlQ60Nj_w" elementId="url" name="URL" optional="false"/>
  </commands>
  <commands xmi:id="_FFvnaWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.debug.ui.commands.StepReturn" commandName="Step Return" description="Step return" category="_FFsjqGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnamo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.browser.openBundleResource" commandName="Open Resource in Browser" description="Opens a bundle resource in the default web browser." category="_FFsjnWo6EfCBSKlQ60Nj_w">
    <parameters xmi:id="_FFvna2o6EfCBSKlQ60Nj_w" elementId="plugin" name="Plugin"/>
    <parameters xmi:id="_FFvnbGo6EfCBSKlQ60Nj_w" elementId="path" name="Path"/>
  </commands>
  <commands xmi:id="_FFvnbWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.search.finddecl" commandName="Declaration" description="Searches for declarations of the selected element in the workspace" category="_FFsjk2o6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnbmo6EfCBSKlQ60Nj_w" elementId="com.st.stm32cube.ide.cmake.commands.cmakeimport" commandName="cmakeimport" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnb2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.help.aboutAction" commandName="About" description="Open the about dialog" category="_FFsjoGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvncGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.debug.ui.command.restoreDefaultType" commandName="Restore Original Type" description="View and edit properties for a given C/C++ breakpoint" category="_FFsjrGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvncWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.file.closeAllSaved" commandName="Close All Saved" description="Close all saved editors" category="_FFsjmmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvncmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.redo" commandName="Redo" description="Redo the last operation" category="_FFsjkmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnc2o6EfCBSKlQ60Nj_w" elementId="com.st.stm32cube.ide.mcu.p2.list" commandName="P2 IU List" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvndGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.refactoring.command.ExtractLocalVariable" commandName="Extract Local Variable..." category="_FFsjn2o6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvndWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.commands.gotoAddress" commandName="Go to Address..." description="Navigate to address" category="_FFsjqGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvndmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.navigate.selectWorkingSets" commandName="Select Working Sets" description="Select the working sets that are applicable for this window." category="_FFsjnWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvnd2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.codan.commands.runCodanCommand" commandName="Run Code Analysis" category="_FFsjomo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFvneGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.edit.text.c.sort.lines" commandName="Sort Lines" description="Sort selected lines alphabetically" category="_FFsjk2o6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFwN8Go6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.newWizard" commandName="New" description="Open the New item wizard" category="_FFsjmmo6EfCBSKlQ60Nj_w">
    <parameters xmi:id="_FFwN8Wo6EfCBSKlQ60Nj_w" elementId="newWizardId" name="New Wizard"/>
  </commands>
  <commands xmi:id="_FFwN8mo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.window.newWindow" commandName="New Window" description="Open another window" category="_FFsjnWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFwN82o6EfCBSKlQ60Nj_w" elementId="org.eclipse.e4.ui.importer.configureProject" commandName="Configure and Detect Nested Projects..." category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFwN9Go6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.file.close" commandName="Close" description="Close the active editor" category="_FFsjmmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFwN9Wo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.search.finddecl.workingset" commandName="Declaration in Working Set" description="Searches for declarations of the selected element in a working set" category="_FFsjk2o6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFwN9mo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.excludeCommand" commandName="Exclude from Build" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFwN92o6EfCBSKlQ60Nj_w" elementId="org.eclipse.equinox.p2.ui.sdk.install" commandName="Install New Software..." category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFwN-Go6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.window.customizePerspective" commandName="Customize Perspective" description="Customize the current perspective" category="_FFsjnWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFwN-Wo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.externaltools.ExternalToolMenuDelegateToolbar" commandName="Run Last Launched External Tool" description="Runs the last launched external Tool" category="_FFsjqGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFwN-mo6EfCBSKlQ60Nj_w" elementId="org.eclipse.remote.ui.command.closeConnection" commandName="Close Connection" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFwN-2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.moveLineUp" commandName="Move Lines Up" description="Moves the selected lines up" category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFwN_Go6EfCBSKlQ60Nj_w" elementId="org.eclipse.equinox.p2.ui.discovery.commands.ShowBundleCatalog" commandName="Show Bundle Catalog" category="_FFsjrmo6EfCBSKlQ60Nj_w">
    <parameters xmi:id="_FFwN_Wo6EfCBSKlQ60Nj_w" elementId="org.eclipse.equinox.p2.ui.discovery.commands.DirectoryParameter" name="Directory URL"/>
    <parameters xmi:id="_FFwN_mo6EfCBSKlQ60Nj_w" elementId="org.eclipse.equinox.p2.ui.discovery.commands.TagsParameter" name="Tags"/>
  </commands>
  <commands xmi:id="_FFwN_2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.userstorage.ui.showPullDown" commandName="Show Pull Down Menu" category="_FFsjoGo6EfCBSKlQ60Nj_w">
    <parameters xmi:id="_FFwOAGo6EfCBSKlQ60Nj_w" elementId="intoolbar" name="In Tool Bar" optional="false"/>
  </commands>
  <commands xmi:id="_FFwOAWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.navigate.previousSubTab" commandName="Previous Sub-Tab" description="Switch to the previous sub-tab" category="_FFsjmGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFwOAmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.navigate.showIn" commandName="Show In" category="_FFsjmGo6EfCBSKlQ60Nj_w">
    <parameters xmi:id="_FFwOA2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.navigate.showIn.targetId" name="Show In Target Id" optional="false"/>
  </commands>
  <commands xmi:id="_FFwOBGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.dialogs.openInputDialog" commandName="Open Input Dialog" description="Open an Input Dialog" category="_FFsjqWo6EfCBSKlQ60Nj_w">
    <parameters xmi:id="_FFwOBWo6EfCBSKlQ60Nj_w" elementId="title" name="Title"/>
    <parameters xmi:id="_FFwOBmo6EfCBSKlQ60Nj_w" elementId="message" name="Message"/>
    <parameters xmi:id="_FFwOB2o6EfCBSKlQ60Nj_w" elementId="initialValue" name="Initial Value"/>
    <parameters xmi:id="_FFwOCGo6EfCBSKlQ60Nj_w" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="_FFwOCWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.compare.copyRightToLeft" commandName="Copy from Right to Left" description="Copy Current Change from Right to Left" category="_FFsjm2o6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFwOCmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.debug.ui.commands.OpenProfileConfigurations" commandName="Profile..." description="Open profile launch configuration dialog" category="_FFsjqGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFwOC2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.ide.markCompleted" commandName="Mark Completed" description="Mark the selected tasks as completed" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFwODGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.debug.ui.command.startTracing" commandName="Start Tracing " description="Start Tracing Experiment" category="_FFsjr2o6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFwODWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.recenter" commandName="Recenter" description="Scroll cursor line to center, top and bottom" category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFwODmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.select.windowStart" commandName="Select Window Start" description="Select to the start of the window" category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFwOD2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.edit.text.c.toggle.source.header" commandName="Toggle Source/Header" description="Toggles between corresponding source and header files" category="_FFsjk2o6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFwOEGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.edit.text.c.format" commandName="Format" description="Formats Source Code" category="_FFsjk2o6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFwOEWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.edit.text.c.select.last" commandName="Restore Last C/C++ Selection" description="Restore last selection in C/C++ editor" category="_FFsjkmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFwOEmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.commands.rulerToggleBreakpoint" commandName="Toggle Breakpoint" description="Toggle breakpoint in disassembly ruler" category="_FFsjqGo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFwOE2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.edit.text.scroll.lineDown" commandName="Scroll Line Down" description="Scroll down one line of text" category="_FFsjlWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFwOFGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.ToggleCoolbarAction" commandName="Toggle Main Toolbar Visibility" description="Toggles the visibility of the window toolbar" category="_FFsjnWo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFwOFWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.edit.text.c.copy.qualified.name" commandName="Copy Qualified Name" description="Copy a fully qualified name to the system clipboard" category="_FFsjk2o6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFwOFmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.navigate.openResource" commandName="Open Resource" description="Open an editor on a particular resource" category="_FFsjmGo6EfCBSKlQ60Nj_w">
    <parameters xmi:id="_FFwOF2o6EfCBSKlQ60Nj_w" elementId="filePath" name="File Path" typeId="org.eclipse.ui.ide.resourcePath"/>
  </commands>
  <commands xmi:id="_FFwOGGo6EfCBSKlQ60Nj_w" elementId="com.st.stm32cube.common.mx.manageembeddedsoftwarepackages" commandName="Manage Embedded Software Packages" description="Manage Embedded Software Packages" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFwOGWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.edit.opendecl" commandName="Open Declaration" description="Opens an editor on the selected element's declaration(s)" category="_FFsjk2o6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FFwOGmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.compare.copyAllLeftToRight" commandName="Copy All from Left to Right" description="Copy All Changes from Left to Right" category="_FFsjm2o6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FUGzAGo6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::com.st.stm32cube.ide.mcu.debug.dsf.oss.ui.debugActionSet/com.st.stm32cube.ide.mcu.debug.dsf.oss.ui.action.TerminateAndRelaunch" commandName="Terminate And Relaunch" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FUHaEGo6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::com.st.stm32cube.ide.mcu.informationcenter.actionSet3/com.st.stm32cube.ide.mcu.informationcenter.action1" commandName="Information Center" description="Information Center" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FUHaEWo6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugActionSet/org.eclipse.cdt.debug.ui.actions.ResumeAtLine" commandName="Resume at Line (C/C++)" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FUHaEmo6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugActionSet/org.eclipse.cdt.debug.ui.actions.MoveToLine" commandName="Move to Line (C/C++)" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FUHaE2o6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugActionSet/org.eclipse.cdt.debug.ui.actions.ToggleInstructionStepMode" commandName="Instruction Stepping Mode" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FUHaFGo6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.cdt.make.ui.updateActionSet/org.eclipse.cdt.make.ui.UpdateMakeAction" commandName="Update Old Make Project..." description="Update Old Make Project" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FUIBIGo6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.cdt.make.ui.makeTargetActionSet/org.eclipse.cdt.make.ui.actions.buildLastTargetAction" commandName="Rebuild Last Target" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FUIBIWo6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.cdt.make.ui.makeTargetActionSet/org.eclipse.cdt.make.ui.makeTargetAction" commandName="Build..." category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FUIBImo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ltk.ui.refactor.show.refactoring.history" commandName="History..." category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FUIoMGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ltk.ui.refactor.create.refactoring.script" commandName="Create Script..." category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FUIoMWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ltk.ui.refactor.apply.refactoring.script" commandName="Apply Script..." category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FUIoMmo6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.cdt.ui.SearchActionSet/org.eclipse.cdt.ui.actions.OpenCSearchPage" commandName="C/C++..." category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FUIoM2o6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.cdt.ui.buildConfigActionSet/org.eclipse.cdt.ui.buildActiveConfigToolbarAction" commandName="Build Active Configuration" description="Build the active configurations of selected projects" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FUIoNGo6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.cdt.ui.buildConfigActionSet/org.eclipse.cdt.ui.buildConfigToolbarAction" commandName="Active Build Configuration" description="Manage configurations for the current project" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FUIoNWo6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.cdt.ui.CElementCreationActionSet/org.eclipse.cdt.ui.actions.NewTypeDropDown" commandName="Class..." description="New C++ Class" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FUIoNmo6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.cdt.ui.CElementCreationActionSet/org.eclipse.cdt.ui.actions.NewFileDropDown" commandName="Source File..." description="New C/C++ Source File" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FUIoN2o6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.cdt.ui.CElementCreationActionSet/org.eclipse.cdt.ui.actions.NewFolderDropDown" commandName="Source Folder..." description="New C/C++ Source Folder" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FUIoOGo6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.cdt.ui.CElementCreationActionSet/org.eclipse.cdt.ui.actions.NewProjectDropDown" commandName="Project..." description="New C/C++ Project" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FUIoOWo6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunWithConfigurationAction" commandName="Run As" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FUIoOmo6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunHistoryMenuAction" commandName="Run History" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FUJPQGo6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunDropDownAction" commandName="Run" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FUJPQWo6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugWithConfigurationAction" commandName="Debug As" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FUJPQmo6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugHistoryMenuAction" commandName="Debug History" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FUJPQ2o6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugDropDownAction" commandName="Debug" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FUJPRGo6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileDropDownAction" commandName="Profile" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FUJPRWo6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileWithConfigurationAction" commandName="Profile As" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FUJPRmo6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileHistoryMenuAction" commandName="Profile History" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FUJPR2o6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.ui.cheatsheets.actionSet/org.eclipse.ui.cheatsheets.actions.CheatSheetHelpMenuAction" commandName="Cheat Sheets..." category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FUJPSGo6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.search.searchActionSet/org.eclipse.search.OpenSearchDialogPage" commandName="Search..." description="Search" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FUJPSWo6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize..." category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FUJPSmo6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.ConfigureProject" commandName="Share Project..." description="Share the project with others using a version and configuration management system." category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FUJPS2o6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.ui.externaltools.ExternalToolsSet/org.eclipse.ui.externaltools.ExternalToolMenuDelegateMenu" commandName="External Tools" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FUJ2UGo6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.CEditor.BreakpointRulerActions/org.eclipse.cdt.debug.ui.CEditor.RulerTobbleBreakpointAction" commandName="%Dummy.label" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FUJ2UWo6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.cdt.ui.editor.asm.AsmEditor.BreakpointRulerActions/org.eclipse.cdt.debug.ui.CEditor.RulerTobbleBreakpointAction" commandName="%Dummy.label" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FUJ2Umo6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="dummy" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FUJ2U2o6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.cdt.internal.ui.text.correction.CSelectRulerAction" commandName="dummy" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FUJ2VGo6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Text Editor Ruler Single-Click" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FULEcGo6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugview.toolbar/org.eclipse.cdt.debug.internal.ui.actions.ToggleInstructionStepModeActionDelegate" commandName="Instruction Stepping Mode" description="Instruction Stepping Mode" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FULEcWo6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugView.menu/org.eclipse.cdt.debug.internal.ui.actions.ShowFullPathsAction" commandName="Show Full Paths" description="Show Full Paths" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FULEcmo6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.internal.ui.actions.ShowFullPathsAction" commandName="Show Full Paths" description="Show Full Paths" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FULEc2o6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.ui.addWatchpoint" commandName="Add Watchpoint (C/C++)..." description="Add Watchpoint (C/C++)" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FULEdGo6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.internal.ui.actions.AddEventBreakpointActionDelegate" commandName="Add Event Breakpoint (C/C++)..." description="Add Event Breakpoint (C/C++)" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FULEdWo6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.ui.addFunctionBreakpoint" commandName="Add Function Breakpoint (C/C++)..." description="Add Function Breakpoint (C/C++)" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FULEdmo6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.ui.addLineBreakpoint" commandName="Add Line Breakpoint (C/C++)..." description="Add Line Breakpoint (C/C++)" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FULEd2o6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.expression.toolbar/org.eclipse.pinclone.expression.pinDebugContext" commandName="Pin to Debug Context" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FULEeGo6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.expression.toolbar/org.eclipse.pinclone.expression.clone" commandName="Open New View" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FULrgGo6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.variable.toolbar/org.eclipse.pinclone.variable.pinDebugContext" commandName="Pin to Debug Context" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FULrgWo6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.variable.toolbar/org.eclipse.pinclone.variable.clone" commandName="Open New View" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FULrgmo6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.register.toolbar/org.eclipse.pinclone.register.pinDebugContext" commandName="Pin to Debug Context" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FULrg2o6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.register.toolbar/org.eclipse.pinclone.register.clone" commandName="Open New View" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FULrhGo6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.cdt.debug.ui.memory.floatingpoint.preferenceaction" commandName="Floating Point Rendering Preferences ..." description="Floating Point Rendering Preferences ..." category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FULrhWo6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memoryBrowser.toolbar/org.eclipse.pinclone.memoryBrowser.pinDebugContext" commandName="Pin to Debug Context" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FULrhmo6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memoryBrowser.toolbar/org.eclipse.pinclone.memoryBrowser.clone" commandName="Open New View" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FULrh2o6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser.clearExpressionList/org.eclipse.cdt.debug.ui.memory.memorybrowser.ClearExpressionListActionID" commandName="Clear Expressions" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FULriGo6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.debug.ui.MemoryView.findNext/org.eclipse.cdt.debug.ui.memory.search.FindNextAction" commandName="Find Next" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FULriWo6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.debug.ui.MemoryView.findReplace/org.eclipse.cdt.debug.ui.memory.search.FindAction" commandName="Find/Replace..." category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FULrimo6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser.findNext/org.eclipse.cdt.debug.ui.memory.search.FindNextAction" commandName="Find Next" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FULri2o6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.cdt.debug.ui.memory.traditional.preferenceaction" commandName="Traditional Rendering Preferences..." description="Traditional Rendering Preferences..." category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FULrjGo6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.cdt.debug.ui.memory.transport.actions.ExportMemoryAction" commandName="Export" description="Export" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FULrjWo6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.cdt.debug.ui.memory.transport.actions.ImportMemoryAction" commandName="Import" description="Import" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FULrjmo6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser.toolbar/org.eclipse.cdt.debug.ui.memory.transport.actions.ExportMemoryAction" commandName="Export" description="Export" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FUMSkGo6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser.toolbar/org.eclipse.cdt.debug.ui.memory.transport.actions.ImportMemoryAction2" commandName="Import" description="Import" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FUMSkWo6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.cdt.dsf.gdb.ui.debugsources.view.refresh/org.eclipse.cdt.dsf.gdb.ui.debugsources.view.refresh" commandName="Refresh" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FUMSkmo6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.breakpoints.update.Refresh/org.eclipse.cdt.dsf.debug.ui.breakpoints.viewmodel.update.actions.refresh" commandName="Refresh" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FUMSk2o6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.variables.update.Refresh/org.eclipse.cdt.dsf.debug.ui.variables.viewmodel.update.actions.refresh" commandName="Refresh" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FUMSlGo6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.registers.update.Refresh/org.eclipse.cdt.dsf.debug.ui.registers.viewmodel.update.actions.refresh" commandName="Refresh" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FUMSlWo6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.expressions.update.Refresh/org.eclipse.cdt.dsf.debug.ui.expressions.viewmodel.update.actions.refresh" commandName="Refresh" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FUMSlmo6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.debugview.update.Refresh/org.eclipse.cdt.dsf.debug.ui.debugview.viewmodel.update.actions.refresh" commandName="Refresh" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FUMSl2o6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.disassembly.toolbar/org.eclipse.pinclone.disassembly.pinDebugContext" commandName="Pin to Debug Context" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FUMSmGo6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.disassembly.toolbar/org.eclipse.pinclone.disassembly.clone" commandName="Open New View" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FUMSmWo6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.debug.ui.PulldownActions/org.eclipse.debug.ui.debugview.pulldown.ViewManagementAction" commandName="View Management..." category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FUMSmmo6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.debug.ui.debugview.toolbar/org.eclipse.debug.ui.debugview.toolbar.removeAllTerminated" commandName="Remove All Terminated" description="Remove All Terminated Launches" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FUMSm2o6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.debug.ui.debugview.toolbar/org.eclipse.debug.ui.debugview.toolbar.collapseAll" commandName="Collapse All" description="Collapse All" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FUM5oGo6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.removeAll" commandName="Remove All" description="Remove All Breakpoints" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FUM5oWo6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.linkWithDebugView" commandName="Link with Debug View" description="Link with Debug View" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FUM5omo6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.workingSets" commandName="Working Sets..." description="Manage Working Sets" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FUM5o2o6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.clearDefaultBreakpointGroup" commandName="Deselect Default Working Set" description="Deselect Default Working Set" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FUM5pGo6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.setDefaultBreakpointGroup" commandName="Select Default Working Set..." description="Select Default Working Set" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FUM5pWo6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.sortByAction" commandName="Sort By" description="Sort By" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FUM5pmo6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.groupByAction" commandName="Group By" description="Show" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FUM5p2o6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.removeAll" commandName="Remove All" description="Remove All Expressions" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FUM5qGo6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.AddWatchExpression" commandName="Add Watch Expression..." description="Create a new watch expression" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FUM5qWo6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.PinMemoryBlockAction" commandName="Pin Memory Monitor" description="Pin Memory Monitor" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FUM5qmo6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.NewMemoryViewAction" commandName="New Memory View" description="New Memory View" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FUM5q2o6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglemonitors" commandName="Toggle Memory Monitors Pane" description="Toggle Memory Monitors Pane" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FUM5rGo6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.linkrenderingpanes" commandName="Link Memory Rendering Panes" description="Link Memory Rendering Panes" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FUM5rWo6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.tablerendering.preferencesaction" commandName="Table Renderings Preferences..." description="&amp;Table Renderings Preferences..." category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FUM5rmo6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglesplitpane" commandName="Toggle Split Pane" description="Toggle Split Pane" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FUNgsGo6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.switchMemoryBlock" commandName="Switch Memory Monitor" description="Switch Memory Monitor" category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <commands xmi:id="_FUNgsWo6EfCBSKlQ60Nj_w" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.memoryViewPreferencesAction" commandName="Preferences..." description="&amp;Preferences..." category="_FFsjrmo6EfCBSKlQ60Nj_w"/>
  <addons xmi:id="_FFaPvmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.e4.core.commands.service" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.core.commands/org.eclipse.e4.core.commands.CommandServiceAddon"/>
  <addons xmi:id="_FFaPv2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.e4.ui.contexts.service" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.services/org.eclipse.e4.ui.services.ContextServiceAddon"/>
  <addons xmi:id="_FFaPwGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.e4.ui.bindings.service" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.bindings/org.eclipse.e4.ui.bindings.BindingServiceAddon"/>
  <addons xmi:id="_FFaPwWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.e4.ui.workbench.commands.model" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.CommandProcessingAddon"/>
  <addons xmi:id="_FFaPwmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.e4.ui.workbench.contexts.model" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.ContextProcessingAddon"/>
  <addons xmi:id="_FFaPw2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.e4.ui.workbench.bindings.model" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.swt/org.eclipse.e4.ui.workbench.swt.util.BindingProcessingAddon"/>
  <addons xmi:id="_FFaPxGo6EfCBSKlQ60Nj_w" elementId="Cleanup Addon" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.cleanupaddon.CleanupAddon"/>
  <addons xmi:id="_FFaPxWo6EfCBSKlQ60Nj_w" elementId="DnD Addon" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.dndaddon.DnDAddon"/>
  <addons xmi:id="_FFaPxmo6EfCBSKlQ60Nj_w" elementId="MinMax Addon" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.MinMaxAddon"/>
  <addons xmi:id="_FFaPx2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.workbench.addon.0" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.HandlerProcessingAddon"/>
  <addons xmi:id="_FFfIMGo6EfCBSKlQ60Nj_w" elementId="SplitterAddon" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.splitteraddon.SplitterAddon"/>
  <addons xmi:id="_SHYRMGo6EfCcdME1c210Lg" elementId="org.eclipse.ui.ide.addon.0" contributionURI="bundleclass://org.eclipse.ui.ide/org.eclipse.ui.internal.ide.addons.SaveAllDirtyPartsAddon"/>
  <addons xmi:id="_dz0JgGOlEeSMMaPQU2nlzw" elementId="org.eclipse.ui.ide.application.addon.0" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.ide.application/org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon"/>
  <categories xmi:id="_FFsjkGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.team.ui.category.team" name="Version control (Team)" description="Actions that apply when working with a version control system"/>
  <categories xmi:id="_FFsjkWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.category.views" name="Views" description="Commands for opening views"/>
  <categories xmi:id="_FFsjkmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.category.edit" name="Edit"/>
  <categories xmi:id="_FFsjk2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.category.source" name="C/C++ Source" description="C/C++ Source Actions"/>
  <categories xmi:id="_FFsjlGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.ide.markerContents" name="Contents" description="The category for menu contents"/>
  <categories xmi:id="_FFsjlWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.category.textEditor" name="Text Editing" description="Text Editing Commands"/>
  <categories xmi:id="_FFsjlmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.debug.ui.category.registerGrouping" name="Register Grouping commands" description="Set of commands for Register Grouping"/>
  <categories xmi:id="_FFsjl2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.debug.ui.category.runControl" name="Run Control Commands" description="Set of commands for Run Control"/>
  <categories xmi:id="_FFsjmGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.category.navigate" name="Navigate"/>
  <categories xmi:id="_FFsjmWo6EfCBSKlQ60Nj_w" elementId="AnsiConsole.command.categoryid" name="ANSI Support Commands"/>
  <categories xmi:id="_FFsjmmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.category.file" name="File"/>
  <categories xmi:id="_FFsjm2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.compare.ui.category.compare" name="Compare" description="Compare command category"/>
  <categories xmi:id="_FFsjnGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.text.quicksearch.commands.category" name="Quick Search"/>
  <categories xmi:id="_FFsjnWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.category.window" name="Window"/>
  <categories xmi:id="_FFsjnmo6EfCBSKlQ60Nj_w" elementId="com.st.stm32cube.ide.mcu.debug.launch.restartCategory" name="Restart Category"/>
  <categories xmi:id="_FFsjn2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.ui.category.refactoring" name="Refactor - C++" description="C/C++ Refactorings"/>
  <categories xmi:id="_FFsjoGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.category.help" name="Help"/>
  <categories xmi:id="_FFsjoWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.category.project" name="Project"/>
  <categories xmi:id="_FFsjomo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.codan.ui.commands.category" name="Code Analysis"/>
  <categories xmi:id="_FFsjo2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.debug.ui.category.debugViewLayout" name="Debug View Layout Commands" description="Set of commands for controlling the Debug View Layout"/>
  <categories xmi:id="_FFsjpGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.tm.terminal.category1" name="Terminal view commands" description="Terminal view commands"/>
  <categories xmi:id="_FFsjpWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.make.ui.category.source" name="Makefile Source" description="Makefile Source Actions"/>
  <categories xmi:id="_FFsjpmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.search.ui.category.search" name="Search" description="Search command category"/>
  <categories xmi:id="_FFsjp2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.launchbar.ui.category.launchBar" name="Launch Bar"/>
  <categories xmi:id="_FFsjqGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.debug.ui.category.run" name="Run/Debug" description="Run/Debug command category"/>
  <categories xmi:id="_FFsjqWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.category.dialogs" name="Dialogs" description="Commands for opening dialogs"/>
  <categories xmi:id="_FFsjqmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.ui.category.perspectives" name="Perspectives" description="Commands for opening perspectives"/>
  <categories xmi:id="_FFsjq2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.ltk.ui.category.refactoring" name="Refactoring"/>
  <categories xmi:id="_FFsjrGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.debug.ui.category.casting" name="Cast to Type or Array" description="Set of commands for displaying variables and expressions as other types or arrays."/>
  <categories xmi:id="_FFsjrWo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.debug.ui.category.reverseDebugging" name="Reverse Debugging Commands" description="Set of commands for Reverse Debugging"/>
  <categories xmi:id="_FFsjrmo6EfCBSKlQ60Nj_w" elementId="org.eclipse.core.commands.categories.autogenerated" name="Uncategorized" description="Commands that were either auto-generated or have no category"/>
  <categories xmi:id="_FFsjr2o6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.debug.ui.category.tracing" name="Tracing Commands" description="Category for Tracing Commands"/>
  <categories xmi:id="_FFsjsGo6EfCBSKlQ60Nj_w" elementId="org.eclipse.cdt.managedbuilder.ui.category.build" name="C/C++ Build" description="C/C++ Build Actions"/>
</application:Application>
