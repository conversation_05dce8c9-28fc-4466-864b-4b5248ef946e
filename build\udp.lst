ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 1


   1              		.cpu cortex-m7
   2              		.arch armv7e-m
   3              		.fpu fpv5-d16
   4              		.eabi_attribute 28, 1
   5              		.eabi_attribute 20, 1
   6              		.eabi_attribute 21, 1
   7              		.eabi_attribute 23, 3
   8              		.eabi_attribute 24, 1
   9              		.eabi_attribute 25, 1
  10              		.eabi_attribute 26, 1
  11              		.eabi_attribute 30, 1
  12              		.eabi_attribute 34, 1
  13              		.eabi_attribute 18, 4
  14              		.file	"udp.c"
  15              		.text
  16              	.Ltext0:
  17              		.cfi_sections	.debug_frame
  18              		.file 1 "Middlewares/Third_Party/LwIP/src/core/udp.c"
  19              		.section	.text.udp_new_port,"ax",%progbits
  20              		.align	1
  21              		.syntax unified
  22              		.thumb
  23              		.thumb_func
  25              	udp_new_port:
  26              	.LFB171:
   1:Middlewares/Third_Party/LwIP/src/core/udp.c **** /**
   2:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * @file
   3:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * User Datagram Protocol module\n
   4:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * The code for the User Datagram Protocol UDP & UDPLite (RFC 3828).\n
   5:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * See also @ref udp_raw
   6:Middlewares/Third_Party/LwIP/src/core/udp.c ****  *
   7:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * @defgroup udp_raw UDP
   8:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * @ingroup callbackstyle_api
   9:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * User Datagram Protocol module\n
  10:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * @see @ref api
  11:Middlewares/Third_Party/LwIP/src/core/udp.c ****  */
  12:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
  13:Middlewares/Third_Party/LwIP/src/core/udp.c **** /*
  14:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * Copyright (c) 2001-2004 Swedish Institute of Computer Science.
  15:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * All rights reserved.
  16:Middlewares/Third_Party/LwIP/src/core/udp.c ****  *
  17:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * Redistribution and use in source and binary forms, with or without modification,
  18:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * are permitted provided that the following conditions are met:
  19:Middlewares/Third_Party/LwIP/src/core/udp.c ****  *
  20:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * 1. Redistributions of source code must retain the above copyright notice,
  21:Middlewares/Third_Party/LwIP/src/core/udp.c ****  *    this list of conditions and the following disclaimer.
  22:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * 2. Redistributions in binary form must reproduce the above copyright notice,
  23:Middlewares/Third_Party/LwIP/src/core/udp.c ****  *    this list of conditions and the following disclaimer in the documentation
  24:Middlewares/Third_Party/LwIP/src/core/udp.c ****  *    and/or other materials provided with the distribution.
  25:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * 3. The name of the author may not be used to endorse or promote products
  26:Middlewares/Third_Party/LwIP/src/core/udp.c ****  *    derived from this software without specific prior written permission.
  27:Middlewares/Third_Party/LwIP/src/core/udp.c ****  *
  28:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR IMPLIED
  29:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
  30:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT
  31:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * SHALL THE AUTHOR BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
  32:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 2


  33:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
  34:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
  35:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
  36:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY
  37:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * OF SUCH DAMAGE.
  38:Middlewares/Third_Party/LwIP/src/core/udp.c ****  *
  39:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * This file is part of the lwIP TCP/IP stack.
  40:Middlewares/Third_Party/LwIP/src/core/udp.c ****  *
  41:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * Author: Adam Dunkels <<EMAIL>>
  42:Middlewares/Third_Party/LwIP/src/core/udp.c ****  *
  43:Middlewares/Third_Party/LwIP/src/core/udp.c ****  */
  44:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
  45:Middlewares/Third_Party/LwIP/src/core/udp.c **** /* @todo Check the use of '(struct udp_pcb).chksum_len_rx'!
  46:Middlewares/Third_Party/LwIP/src/core/udp.c ****  */
  47:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
  48:Middlewares/Third_Party/LwIP/src/core/udp.c **** #include "lwip/opt.h"
  49:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
  50:Middlewares/Third_Party/LwIP/src/core/udp.c **** #if LWIP_UDP /* don't build if not configured for use in lwipopts.h */
  51:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
  52:Middlewares/Third_Party/LwIP/src/core/udp.c **** #include "lwip/udp.h"
  53:Middlewares/Third_Party/LwIP/src/core/udp.c **** #include "lwip/def.h"
  54:Middlewares/Third_Party/LwIP/src/core/udp.c **** #include "lwip/memp.h"
  55:Middlewares/Third_Party/LwIP/src/core/udp.c **** #include "lwip/inet_chksum.h"
  56:Middlewares/Third_Party/LwIP/src/core/udp.c **** #include "lwip/ip_addr.h"
  57:Middlewares/Third_Party/LwIP/src/core/udp.c **** #include "lwip/ip6.h"
  58:Middlewares/Third_Party/LwIP/src/core/udp.c **** #include "lwip/ip6_addr.h"
  59:Middlewares/Third_Party/LwIP/src/core/udp.c **** #include "lwip/netif.h"
  60:Middlewares/Third_Party/LwIP/src/core/udp.c **** #include "lwip/icmp.h"
  61:Middlewares/Third_Party/LwIP/src/core/udp.c **** #include "lwip/icmp6.h"
  62:Middlewares/Third_Party/LwIP/src/core/udp.c **** #include "lwip/stats.h"
  63:Middlewares/Third_Party/LwIP/src/core/udp.c **** #include "lwip/snmp.h"
  64:Middlewares/Third_Party/LwIP/src/core/udp.c **** #include "lwip/dhcp.h"
  65:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
  66:Middlewares/Third_Party/LwIP/src/core/udp.c **** #include <string.h>
  67:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
  68:Middlewares/Third_Party/LwIP/src/core/udp.c **** #ifndef UDP_LOCAL_PORT_RANGE_START
  69:Middlewares/Third_Party/LwIP/src/core/udp.c **** /* From http://www.iana.org/assignments/port-numbers:
  70:Middlewares/Third_Party/LwIP/src/core/udp.c ****    "The Dynamic and/or Private Ports are those from 49152 through 65535" */
  71:Middlewares/Third_Party/LwIP/src/core/udp.c **** #define UDP_LOCAL_PORT_RANGE_START  0xc000
  72:Middlewares/Third_Party/LwIP/src/core/udp.c **** #define UDP_LOCAL_PORT_RANGE_END    0xffff
  73:Middlewares/Third_Party/LwIP/src/core/udp.c **** #define UDP_ENSURE_LOCAL_PORT_RANGE(port) ((u16_t)(((port) & (u16_t)~UDP_LOCAL_PORT_RANGE_START) + 
  74:Middlewares/Third_Party/LwIP/src/core/udp.c **** #endif
  75:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
  76:Middlewares/Third_Party/LwIP/src/core/udp.c **** /* last local UDP port */
  77:Middlewares/Third_Party/LwIP/src/core/udp.c **** static u16_t udp_port = UDP_LOCAL_PORT_RANGE_START;
  78:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
  79:Middlewares/Third_Party/LwIP/src/core/udp.c **** /* The list of UDP PCBs */
  80:Middlewares/Third_Party/LwIP/src/core/udp.c **** /* exported in udp.h (was static) */
  81:Middlewares/Third_Party/LwIP/src/core/udp.c **** struct udp_pcb *udp_pcbs;
  82:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
  83:Middlewares/Third_Party/LwIP/src/core/udp.c **** /**
  84:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * Initialize this module.
  85:Middlewares/Third_Party/LwIP/src/core/udp.c ****  */
  86:Middlewares/Third_Party/LwIP/src/core/udp.c **** void
  87:Middlewares/Third_Party/LwIP/src/core/udp.c **** udp_init(void)
  88:Middlewares/Third_Party/LwIP/src/core/udp.c **** {
  89:Middlewares/Third_Party/LwIP/src/core/udp.c **** #ifdef LWIP_RAND
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 3


  90:Middlewares/Third_Party/LwIP/src/core/udp.c ****   udp_port = UDP_ENSURE_LOCAL_PORT_RANGE(LWIP_RAND());
  91:Middlewares/Third_Party/LwIP/src/core/udp.c **** #endif /* LWIP_RAND */
  92:Middlewares/Third_Party/LwIP/src/core/udp.c **** }
  93:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
  94:Middlewares/Third_Party/LwIP/src/core/udp.c **** /**
  95:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * Allocate a new local UDP port.
  96:Middlewares/Third_Party/LwIP/src/core/udp.c ****  *
  97:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * @return a new (free) local UDP port number
  98:Middlewares/Third_Party/LwIP/src/core/udp.c ****  */
  99:Middlewares/Third_Party/LwIP/src/core/udp.c **** static u16_t
 100:Middlewares/Third_Party/LwIP/src/core/udp.c **** udp_new_port(void)
 101:Middlewares/Third_Party/LwIP/src/core/udp.c **** {
  27              		.loc 1 101 1 view -0
  28              		.cfi_startproc
  29              		@ args = 0, pretend = 0, frame = 0
  30              		@ frame_needed = 0, uses_anonymous_args = 0
  31              		@ link register save eliminated.
 102:Middlewares/Third_Party/LwIP/src/core/udp.c ****   u16_t n = 0;
  32              		.loc 1 102 3 view .LVU1
  33              	.LVL0:
  34              		.loc 1 102 9 is_stmt 0 view .LVU2
  35 0000 0020     		movs	r0, #0
  36              	.LVL1:
  37              	.L2:
 103:Middlewares/Third_Party/LwIP/src/core/udp.c ****   struct udp_pcb *pcb;
  38              		.loc 1 103 3 is_stmt 1 view .LVU3
 104:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 105:Middlewares/Third_Party/LwIP/src/core/udp.c **** again:
 106:Middlewares/Third_Party/LwIP/src/core/udp.c ****   if (udp_port++ == UDP_LOCAL_PORT_RANGE_END) {
  39              		.loc 1 106 3 view .LVU4
  40              		.loc 1 106 15 is_stmt 0 view .LVU5
  41 0002 104A     		ldr	r2, .L12
  42 0004 1388     		ldrh	r3, [r2]
  43 0006 591C     		adds	r1, r3, #1
  44 0008 1180     		strh	r1, [r2]	@ movhi
  45              		.loc 1 106 6 view .LVU6
  46 000a 4FF6FF72 		movw	r2, #65535
  47 000e 9342     		cmp	r3, r2
  48 0010 09D0     		beq	.L9
  49              	.L3:
 107:Middlewares/Third_Party/LwIP/src/core/udp.c ****     udp_port = UDP_LOCAL_PORT_RANGE_START;
 108:Middlewares/Third_Party/LwIP/src/core/udp.c ****   }
 109:Middlewares/Third_Party/LwIP/src/core/udp.c ****   /* Check all PCBs. */
 110:Middlewares/Third_Party/LwIP/src/core/udp.c ****   for (pcb = udp_pcbs; pcb != NULL; pcb = pcb->next) {
  50              		.loc 1 110 3 is_stmt 1 view .LVU7
  51              		.loc 1 110 12 is_stmt 0 view .LVU8
  52 0012 0D4B     		ldr	r3, .L12+4
  53 0014 1B68     		ldr	r3, [r3]
  54              	.LVL2:
  55              	.L4:
  56              		.loc 1 110 28 is_stmt 1 discriminator 1 view .LVU9
  57 0016 93B1     		cbz	r3, .L10
 111:Middlewares/Third_Party/LwIP/src/core/udp.c ****     if (pcb->local_port == udp_port) {
  58              		.loc 1 111 5 view .LVU10
  59              		.loc 1 111 12 is_stmt 0 view .LVU11
  60 0018 598A     		ldrh	r1, [r3, #18]
  61              		.loc 1 111 25 view .LVU12
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 4


  62 001a 0A4A     		ldr	r2, .L12
  63 001c 1288     		ldrh	r2, [r2]
  64              		.loc 1 111 8 view .LVU13
  65 001e 9142     		cmp	r1, r2
  66 0020 06D0     		beq	.L11
 110:Middlewares/Third_Party/LwIP/src/core/udp.c ****     if (pcb->local_port == udp_port) {
  67              		.loc 1 110 41 is_stmt 1 discriminator 2 view .LVU14
  68 0022 DB68     		ldr	r3, [r3, #12]
  69              	.LVL3:
 110:Middlewares/Third_Party/LwIP/src/core/udp.c ****     if (pcb->local_port == udp_port) {
  70              		.loc 1 110 41 is_stmt 0 discriminator 2 view .LVU15
  71 0024 F7E7     		b	.L4
  72              	.LVL4:
  73              	.L9:
 107:Middlewares/Third_Party/LwIP/src/core/udp.c ****     udp_port = UDP_LOCAL_PORT_RANGE_START;
  74              		.loc 1 107 5 is_stmt 1 view .LVU16
 107:Middlewares/Third_Party/LwIP/src/core/udp.c ****     udp_port = UDP_LOCAL_PORT_RANGE_START;
  75              		.loc 1 107 14 is_stmt 0 view .LVU17
  76 0026 074B     		ldr	r3, .L12
  77 0028 4FF44042 		mov	r2, #49152
  78 002c 1A80     		strh	r2, [r3]	@ movhi
  79 002e F0E7     		b	.L3
  80              	.LVL5:
  81              	.L11:
 112:Middlewares/Third_Party/LwIP/src/core/udp.c ****       if (++n > (UDP_LOCAL_PORT_RANGE_END - UDP_LOCAL_PORT_RANGE_START)) {
  82              		.loc 1 112 7 is_stmt 1 view .LVU18
  83              		.loc 1 112 10 is_stmt 0 view .LVU19
  84 0030 0130     		adds	r0, r0, #1
  85              	.LVL6:
  86              		.loc 1 112 10 view .LVU20
  87 0032 80B2     		uxth	r0, r0
  88              	.LVL7:
  89              		.loc 1 112 10 view .LVU21
  90 0034 B0F5804F 		cmp	r0, #16384
  91 0038 E3D3     		bcc	.L2
 113:Middlewares/Third_Party/LwIP/src/core/udp.c ****         return 0;
  92              		.loc 1 113 16 view .LVU22
  93 003a 0020     		movs	r0, #0
  94              	.LVL8:
 114:Middlewares/Third_Party/LwIP/src/core/udp.c ****       }
 115:Middlewares/Third_Party/LwIP/src/core/udp.c ****       goto again;
 116:Middlewares/Third_Party/LwIP/src/core/udp.c ****     }
 117:Middlewares/Third_Party/LwIP/src/core/udp.c ****   }
 118:Middlewares/Third_Party/LwIP/src/core/udp.c ****   return udp_port;
 119:Middlewares/Third_Party/LwIP/src/core/udp.c **** }
  95              		.loc 1 119 1 view .LVU23
  96 003c 7047     		bx	lr
  97              	.LVL9:
  98              	.L10:
 118:Middlewares/Third_Party/LwIP/src/core/udp.c **** }
  99              		.loc 1 118 3 is_stmt 1 view .LVU24
 118:Middlewares/Third_Party/LwIP/src/core/udp.c **** }
 100              		.loc 1 118 10 is_stmt 0 view .LVU25
 101 003e 014B     		ldr	r3, .L12
 102              	.LVL10:
 118:Middlewares/Third_Party/LwIP/src/core/udp.c **** }
 103              		.loc 1 118 10 view .LVU26
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 5


 104 0040 1888     		ldrh	r0, [r3]
 105              	.LVL11:
 118:Middlewares/Third_Party/LwIP/src/core/udp.c **** }
 106              		.loc 1 118 10 view .LVU27
 107 0042 7047     		bx	lr
 108              	.L13:
 109              		.align	2
 110              	.L12:
 111 0044 00000000 		.word	udp_port
 112 0048 00000000 		.word	udp_pcbs
 113              		.cfi_endproc
 114              	.LFE171:
 116              		.section	.rodata.udp_input_local_match.str1.4,"aMS",%progbits,1
 117              		.align	2
 118              	.LC0:
 119 0000 4D696464 		.ascii	"Middlewares/Third_Party/LwIP/src/core/udp.c\000"
 119      6C657761 
 119      7265732F 
 119      54686972 
 119      645F5061 
 120              		.align	2
 121              	.LC1:
 122 002c 7564705F 		.ascii	"udp_input_local_match: invalid pcb\000"
 122      696E7075 
 122      745F6C6F 
 122      63616C5F 
 122      6D617463 
 123 004f 00       		.align	2
 124              	.LC2:
 125 0050 41737365 		.ascii	"Assertion \"%s\" failed at line %d in %s\012\000"
 125      7274696F 
 125      6E202225 
 125      73222066 
 125      61696C65 
 126              		.align	2
 127              	.LC3:
 128 0078 7564705F 		.ascii	"udp_input_local_match: invalid netif\000"
 128      696E7075 
 128      745F6C6F 
 128      63616C5F 
 128      6D617463 
 129              		.section	.text.udp_input_local_match,"ax",%progbits
 130              		.align	1
 131              		.syntax unified
 132              		.thumb
 133              		.thumb_func
 135              	udp_input_local_match:
 136              	.LVL12:
 137              	.LFB172:
 120:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 121:Middlewares/Third_Party/LwIP/src/core/udp.c **** /** Common code to see if the current input packet matches the pcb
 122:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * (current input packet is accessed via ip(4/6)_current_* macros)
 123:Middlewares/Third_Party/LwIP/src/core/udp.c ****  *
 124:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * @param pcb pcb to check
 125:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * @param inp network interface on which the datagram was received (only used for IPv4)
 126:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * @param broadcast 1 if his is an IPv4 broadcast (global or subnet-only), 0 otherwise (only used f
 127:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * @return 1 on match, 0 otherwise
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 6


 128:Middlewares/Third_Party/LwIP/src/core/udp.c ****  */
 129:Middlewares/Third_Party/LwIP/src/core/udp.c **** static u8_t
 130:Middlewares/Third_Party/LwIP/src/core/udp.c **** udp_input_local_match(struct udp_pcb *pcb, struct netif *inp, u8_t broadcast)
 131:Middlewares/Third_Party/LwIP/src/core/udp.c **** {
 138              		.loc 1 131 1 is_stmt 1 view -0
 139              		.cfi_startproc
 140              		@ args = 0, pretend = 0, frame = 0
 141              		@ frame_needed = 0, uses_anonymous_args = 0
 142              		.loc 1 131 1 is_stmt 0 view .LVU29
 143 0000 F8B5     		push	{r3, r4, r5, r6, r7, lr}
 144              	.LCFI0:
 145              		.cfi_def_cfa_offset 24
 146              		.cfi_offset 3, -24
 147              		.cfi_offset 4, -20
 148              		.cfi_offset 5, -16
 149              		.cfi_offset 6, -12
 150              		.cfi_offset 7, -8
 151              		.cfi_offset 14, -4
 152 0002 0E46     		mov	r6, r1
 153 0004 1546     		mov	r5, r2
 132:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_UNUSED_ARG(inp);       /* in IPv6 only case */
 154              		.loc 1 132 3 is_stmt 1 view .LVU30
 133:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_UNUSED_ARG(broadcast); /* in IPv6 only case */
 155              		.loc 1 133 3 view .LVU31
 134:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 135:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ASSERT("udp_input_local_match: invalid pcb", pcb != NULL);
 156              		.loc 1 135 3 view .LVU32
 157              		.loc 1 135 3 view .LVU33
 158 0006 0746     		mov	r7, r0
 159 0008 C8B1     		cbz	r0, .L29
 160              	.LVL13:
 161              	.L15:
 162              		.loc 1 135 3 discriminator 3 view .LVU34
 163              		.loc 1 135 3 discriminator 3 view .LVU35
 136:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ASSERT("udp_input_local_match: invalid netif", inp != NULL);
 164              		.loc 1 136 3 view .LVU36
 165              		.loc 1 136 3 view .LVU37
 166 000a FEB1     		cbz	r6, .L30
 167              	.L16:
 168              		.loc 1 136 3 discriminator 3 view .LVU38
 169              		.loc 1 136 3 discriminator 3 view .LVU39
 137:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 138:Middlewares/Third_Party/LwIP/src/core/udp.c ****   /* check if PCB is bound to specific netif */
 139:Middlewares/Third_Party/LwIP/src/core/udp.c ****   if ((pcb->netif_idx != NETIF_NO_INDEX) &&
 170              		.loc 1 139 3 view .LVU40
 171              		.loc 1 139 11 is_stmt 0 view .LVU41
 172 000c 3C7A     		ldrb	r4, [r7, #8]	@ zero_extendqisi2
 173              		.loc 1 139 6 view .LVU42
 174 000e 3CB1     		cbz	r4, .L17
 140:Middlewares/Third_Party/LwIP/src/core/udp.c ****       (pcb->netif_idx != netif_get_index(ip_data.current_input_netif))) {
 175              		.loc 1 140 26 view .LVU43
 176 0010 1E4B     		ldr	r3, .L31
 177 0012 5B68     		ldr	r3, [r3, #4]
 178 0014 93F83030 		ldrb	r3, [r3, #48]	@ zero_extendqisi2
 179 0018 0133     		adds	r3, r3, #1
 180 001a DBB2     		uxtb	r3, r3
 139:Middlewares/Third_Party/LwIP/src/core/udp.c ****       (pcb->netif_idx != netif_get_index(ip_data.current_input_netif))) {
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 7


 181              		.loc 1 139 42 discriminator 1 view .LVU44
 182 001c 9C42     		cmp	r4, r3
 183 001e 25D1     		bne	.L20
 184              	.L17:
 141:Middlewares/Third_Party/LwIP/src/core/udp.c ****     return 0;
 142:Middlewares/Third_Party/LwIP/src/core/udp.c ****   }
 143:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 144:Middlewares/Third_Party/LwIP/src/core/udp.c ****   /* Dual-stack: PCBs listening to any IP type also listen to any IP address */
 145:Middlewares/Third_Party/LwIP/src/core/udp.c ****   if (IP_IS_ANY_TYPE_VAL(pcb->local_ip)) {
 185              		.loc 1 145 3 is_stmt 1 view .LVU45
 146:Middlewares/Third_Party/LwIP/src/core/udp.c **** #if LWIP_IPV4 && IP_SOF_BROADCAST_RECV
 147:Middlewares/Third_Party/LwIP/src/core/udp.c ****     if ((broadcast != 0) && !ip_get_option(pcb, SOF_BROADCAST)) {
 148:Middlewares/Third_Party/LwIP/src/core/udp.c ****       return 0;
 149:Middlewares/Third_Party/LwIP/src/core/udp.c ****     }
 150:Middlewares/Third_Party/LwIP/src/core/udp.c **** #endif /* LWIP_IPV4 && IP_SOF_BROADCAST_RECV */
 151:Middlewares/Third_Party/LwIP/src/core/udp.c ****     return 1;
 152:Middlewares/Third_Party/LwIP/src/core/udp.c ****   }
 153:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 154:Middlewares/Third_Party/LwIP/src/core/udp.c ****   /* Only need to check PCB if incoming IP version matches PCB IP version */
 155:Middlewares/Third_Party/LwIP/src/core/udp.c ****   if (IP_ADDR_PCB_VERSION_MATCH_EXACT(pcb, ip_current_dest_addr())) {
 186              		.loc 1 155 3 view .LVU46
 156:Middlewares/Third_Party/LwIP/src/core/udp.c **** #if LWIP_IPV4
 157:Middlewares/Third_Party/LwIP/src/core/udp.c ****     /* Special case: IPv4 broadcast: all or broadcasts in my subnet
 158:Middlewares/Third_Party/LwIP/src/core/udp.c ****      * Note: broadcast variable can only be 1 if it is an IPv4 broadcast */
 159:Middlewares/Third_Party/LwIP/src/core/udp.c ****     if (broadcast != 0) {
 187              		.loc 1 159 5 view .LVU47
 188              		.loc 1 159 8 is_stmt 0 view .LVU48
 189 0020 DDB1     		cbz	r5, .L19
 160:Middlewares/Third_Party/LwIP/src/core/udp.c **** #if IP_SOF_BROADCAST_RECV
 161:Middlewares/Third_Party/LwIP/src/core/udp.c ****       if (ip_get_option(pcb, SOF_BROADCAST))
 162:Middlewares/Third_Party/LwIP/src/core/udp.c **** #endif /* IP_SOF_BROADCAST_RECV */
 163:Middlewares/Third_Party/LwIP/src/core/udp.c ****       {
 164:Middlewares/Third_Party/LwIP/src/core/udp.c ****         if (ip4_addr_isany(ip_2_ip4(&pcb->local_ip)) ||
 190              		.loc 1 164 9 is_stmt 1 view .LVU49
 191              		.loc 1 164 12 is_stmt 0 view .LVU50
 192 0022 2FB3     		cbz	r7, .L21
 193              		.loc 1 164 13 discriminator 1 view .LVU51
 194 0024 3B68     		ldr	r3, [r7]
 195 0026 2BB3     		cbz	r3, .L22
 165:Middlewares/Third_Party/LwIP/src/core/udp.c ****             ((ip4_current_dest_addr()->addr == IPADDR_BROADCAST)) ||
 196              		.loc 1 165 38 view .LVU52
 197 0028 184A     		ldr	r2, .L31
 198 002a 5269     		ldr	r2, [r2, #20]
 164:Middlewares/Third_Party/LwIP/src/core/udp.c ****             ((ip4_current_dest_addr()->addr == IPADDR_BROADCAST)) ||
 199              		.loc 1 164 54 discriminator 2 view .LVU53
 200 002c B2F1FF3F 		cmp	r2, #-1
 201 0030 22D0     		beq	.L23
 166:Middlewares/Third_Party/LwIP/src/core/udp.c ****             ip4_addr_netcmp(ip_2_ip4(&pcb->local_ip), ip4_current_dest_addr(), netif_ip4_netmask(in
 202              		.loc 1 166 13 view .LVU54
 203 0032 B168     		ldr	r1, [r6, #8]
 204 0034 5340     		eors	r3, r3, r2
 165:Middlewares/Third_Party/LwIP/src/core/udp.c ****             ((ip4_current_dest_addr()->addr == IPADDR_BROADCAST)) ||
 205              		.loc 1 165 67 view .LVU55
 206 0036 0B42     		tst	r3, r1
 207 0038 20D0     		beq	.L24
 167:Middlewares/Third_Party/LwIP/src/core/udp.c ****           return 1;
 168:Middlewares/Third_Party/LwIP/src/core/udp.c ****         }
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 8


 169:Middlewares/Third_Party/LwIP/src/core/udp.c ****       }
 170:Middlewares/Third_Party/LwIP/src/core/udp.c ****     } else
 171:Middlewares/Third_Party/LwIP/src/core/udp.c **** #endif /* LWIP_IPV4 */
 172:Middlewares/Third_Party/LwIP/src/core/udp.c ****       /* Handle IPv4 and IPv6: all or exact match */
 173:Middlewares/Third_Party/LwIP/src/core/udp.c ****       if (ip_addr_isany(&pcb->local_ip) || ip_addr_cmp(&pcb->local_ip, ip_current_dest_addr())) {
 174:Middlewares/Third_Party/LwIP/src/core/udp.c ****         return 1;
 175:Middlewares/Third_Party/LwIP/src/core/udp.c ****       }
 176:Middlewares/Third_Party/LwIP/src/core/udp.c ****   }
 177:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 178:Middlewares/Third_Party/LwIP/src/core/udp.c ****   return 0;
 208              		.loc 1 178 10 view .LVU56
 209 003a 0020     		movs	r0, #0
 210 003c 17E0     		b	.L18
 211              	.LVL14:
 212              	.L29:
 135:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ASSERT("udp_input_local_match: invalid netif", inp != NULL);
 213              		.loc 1 135 3 is_stmt 1 discriminator 1 view .LVU57
 135:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ASSERT("udp_input_local_match: invalid netif", inp != NULL);
 214              		.loc 1 135 3 discriminator 1 view .LVU58
 215 003e 144B     		ldr	r3, .L31+4
 216 0040 8722     		movs	r2, #135
 217              	.LVL15:
 135:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ASSERT("udp_input_local_match: invalid netif", inp != NULL);
 218              		.loc 1 135 3 is_stmt 0 discriminator 1 view .LVU59
 219 0042 1449     		ldr	r1, .L31+8
 220              	.LVL16:
 135:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ASSERT("udp_input_local_match: invalid netif", inp != NULL);
 221              		.loc 1 135 3 discriminator 1 view .LVU60
 222 0044 1448     		ldr	r0, .L31+12
 223              	.LVL17:
 135:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ASSERT("udp_input_local_match: invalid netif", inp != NULL);
 224              		.loc 1 135 3 discriminator 1 view .LVU61
 225 0046 FFF7FEFF 		bl	printf
 226              	.LVL18:
 227 004a DEE7     		b	.L15
 228              	.L30:
 136:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 229              		.loc 1 136 3 is_stmt 1 discriminator 1 view .LVU62
 136:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 230              		.loc 1 136 3 discriminator 1 view .LVU63
 231 004c 104B     		ldr	r3, .L31+4
 232 004e 8822     		movs	r2, #136
 233 0050 1249     		ldr	r1, .L31+16
 234 0052 1148     		ldr	r0, .L31+12
 235 0054 FFF7FEFF 		bl	printf
 236              	.LVL19:
 237 0058 D8E7     		b	.L16
 238              	.L19:
 173:Middlewares/Third_Party/LwIP/src/core/udp.c ****         return 1;
 239              		.loc 1 173 7 view .LVU64
 173:Middlewares/Third_Party/LwIP/src/core/udp.c ****         return 1;
 240              		.loc 1 173 10 is_stmt 0 view .LVU65
 241 005a 8FB1     		cbz	r7, .L25
 173:Middlewares/Third_Party/LwIP/src/core/udp.c ****         return 1;
 242              		.loc 1 173 11 discriminator 1 view .LVU66
 243 005c 3B68     		ldr	r3, [r7]
 244 005e 8BB1     		cbz	r3, .L26
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 9


 173:Middlewares/Third_Party/LwIP/src/core/udp.c ****         return 1;
 245              		.loc 1 173 44 discriminator 2 view .LVU67
 246 0060 0A4A     		ldr	r2, .L31
 247 0062 5269     		ldr	r2, [r2, #20]
 173:Middlewares/Third_Party/LwIP/src/core/udp.c ****         return 1;
 248              		.loc 1 173 41 discriminator 2 view .LVU68
 249 0064 9342     		cmp	r3, r2
 250 0066 0FD0     		beq	.L27
 251              		.loc 1 178 10 view .LVU69
 252 0068 2846     		mov	r0, r5
 253 006a 00E0     		b	.L18
 254              	.L20:
 141:Middlewares/Third_Party/LwIP/src/core/udp.c ****   }
 255              		.loc 1 141 12 view .LVU70
 256 006c 0020     		movs	r0, #0
 257              	.L18:
 179:Middlewares/Third_Party/LwIP/src/core/udp.c **** }
 258              		.loc 1 179 1 view .LVU71
 259 006e F8BD     		pop	{r3, r4, r5, r6, r7, pc}
 260              	.LVL20:
 261              	.L21:
 167:Middlewares/Third_Party/LwIP/src/core/udp.c ****         }
 262              		.loc 1 167 18 view .LVU72
 263 0070 0120     		movs	r0, #1
 264 0072 FCE7     		b	.L18
 265              	.L22:
 167:Middlewares/Third_Party/LwIP/src/core/udp.c ****         }
 266              		.loc 1 167 18 view .LVU73
 267 0074 0120     		movs	r0, #1
 268 0076 FAE7     		b	.L18
 269              	.L23:
 270 0078 0120     		movs	r0, #1
 271 007a F8E7     		b	.L18
 272              	.L24:
 167:Middlewares/Third_Party/LwIP/src/core/udp.c ****         }
 273              		.loc 1 167 18 view .LVU74
 274 007c 0120     		movs	r0, #1
 275 007e F6E7     		b	.L18
 276              	.L25:
 174:Middlewares/Third_Party/LwIP/src/core/udp.c ****       }
 277              		.loc 1 174 16 view .LVU75
 278 0080 0120     		movs	r0, #1
 279 0082 F4E7     		b	.L18
 280              	.L26:
 174:Middlewares/Third_Party/LwIP/src/core/udp.c ****       }
 281              		.loc 1 174 16 view .LVU76
 282 0084 0120     		movs	r0, #1
 283 0086 F2E7     		b	.L18
 284              	.L27:
 285 0088 0120     		movs	r0, #1
 286 008a F0E7     		b	.L18
 287              	.L32:
 288              		.align	2
 289              	.L31:
 290 008c 00000000 		.word	ip_data
 291 0090 00000000 		.word	.LC0
 292 0094 2C000000 		.word	.LC1
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 10


 293 0098 50000000 		.word	.LC2
 294 009c 78000000 		.word	.LC3
 295              		.cfi_endproc
 296              	.LFE172:
 298              		.section	.text.udp_init,"ax",%progbits
 299              		.align	1
 300              		.global	udp_init
 301              		.syntax unified
 302              		.thumb
 303              		.thumb_func
 305              	udp_init:
 306              	.LFB170:
  88:Middlewares/Third_Party/LwIP/src/core/udp.c **** #ifdef LWIP_RAND
 307              		.loc 1 88 1 is_stmt 1 view -0
 308              		.cfi_startproc
 309              		@ args = 0, pretend = 0, frame = 0
 310              		@ frame_needed = 0, uses_anonymous_args = 0
 311 0000 08B5     		push	{r3, lr}
 312              	.LCFI1:
 313              		.cfi_def_cfa_offset 8
 314              		.cfi_offset 3, -8
 315              		.cfi_offset 14, -4
  90:Middlewares/Third_Party/LwIP/src/core/udp.c **** #endif /* LWIP_RAND */
 316              		.loc 1 90 3 view .LVU78
  90:Middlewares/Third_Party/LwIP/src/core/udp.c **** #endif /* LWIP_RAND */
 317              		.loc 1 90 14 is_stmt 0 view .LVU79
 318 0002 FFF7FEFF 		bl	rand
 319              	.LVL21:
  90:Middlewares/Third_Party/LwIP/src/core/udp.c **** #endif /* LWIP_RAND */
 320              		.loc 1 90 14 discriminator 1 view .LVU80
 321 0006 024B     		ldr	r3, .L35
 322 0008 0343     		orrs	r3, r3, r0
  90:Middlewares/Third_Party/LwIP/src/core/udp.c **** #endif /* LWIP_RAND */
 323              		.loc 1 90 12 discriminator 1 view .LVU81
 324 000a 024A     		ldr	r2, .L35+4
 325 000c 1380     		strh	r3, [r2]	@ movhi
  92:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 326              		.loc 1 92 1 view .LVU82
 327 000e 08BD     		pop	{r3, pc}
 328              	.L36:
 329              		.align	2
 330              	.L35:
 331 0010 00C0FFFF 		.word	-16384
 332 0014 00000000 		.word	udp_port
 333              		.cfi_endproc
 334              	.LFE170:
 336              		.section	.rodata.udp_input.str1.4,"aMS",%progbits,1
 337              		.align	2
 338              	.LC4:
 339 0000 7564705F 		.ascii	"udp_input: invalid pbuf\000"
 339      696E7075 
 339      743A2069 
 339      6E76616C 
 339      69642070 
 340              		.align	2
 341              	.LC5:
 342 0018 7564705F 		.ascii	"udp_input: invalid netif\000"
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 11


 342      696E7075 
 342      743A2069 
 342      6E76616C 
 342      6964206E 
 343 0031 000000   		.align	2
 344              	.LC6:
 345 0034 70627566 		.ascii	"pbuf_remove_header failed\012\000"
 345      5F72656D 
 345      6F76655F 
 345      68656164 
 345      65722066 
 346              		.section	.text.udp_input,"ax",%progbits
 347              		.align	1
 348              		.global	udp_input
 349              		.syntax unified
 350              		.thumb
 351              		.thumb_func
 353              	udp_input:
 354              	.LVL22:
 355              	.LFB173:
 180:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 181:Middlewares/Third_Party/LwIP/src/core/udp.c **** /**
 182:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * Process an incoming UDP datagram.
 183:Middlewares/Third_Party/LwIP/src/core/udp.c ****  *
 184:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * Given an incoming UDP datagram (as a chain of pbufs) this function
 185:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * finds a corresponding UDP PCB and hands over the pbuf to the pcbs
 186:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * recv function. If no pcb is found or the datagram is incorrect, the
 187:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * pbuf is freed.
 188:Middlewares/Third_Party/LwIP/src/core/udp.c ****  *
 189:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * @param p pbuf to be demultiplexed to a UDP PCB (p->payload pointing to the UDP header)
 190:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * @param inp network interface on which the datagram was received.
 191:Middlewares/Third_Party/LwIP/src/core/udp.c ****  *
 192:Middlewares/Third_Party/LwIP/src/core/udp.c ****  */
 193:Middlewares/Third_Party/LwIP/src/core/udp.c **** void
 194:Middlewares/Third_Party/LwIP/src/core/udp.c **** udp_input(struct pbuf *p, struct netif *inp)
 195:Middlewares/Third_Party/LwIP/src/core/udp.c **** {
 356              		.loc 1 195 1 is_stmt 1 view -0
 357              		.cfi_startproc
 358              		@ args = 0, pretend = 0, frame = 0
 359              		@ frame_needed = 0, uses_anonymous_args = 0
 360              		.loc 1 195 1 is_stmt 0 view .LVU84
 361 0000 2DE9F04F 		push	{r4, r5, r6, r7, r8, r9, r10, fp, lr}
 362              	.LCFI2:
 363              		.cfi_def_cfa_offset 36
 364              		.cfi_offset 4, -36
 365              		.cfi_offset 5, -32
 366              		.cfi_offset 6, -28
 367              		.cfi_offset 7, -24
 368              		.cfi_offset 8, -20
 369              		.cfi_offset 9, -16
 370              		.cfi_offset 10, -12
 371              		.cfi_offset 11, -8
 372              		.cfi_offset 14, -4
 373 0004 83B0     		sub	sp, sp, #12
 374              	.LCFI3:
 375              		.cfi_def_cfa_offset 48
 376 0006 0F46     		mov	r7, r1
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 12


 196:Middlewares/Third_Party/LwIP/src/core/udp.c ****   struct udp_hdr *udphdr;
 377              		.loc 1 196 3 is_stmt 1 view .LVU85
 197:Middlewares/Third_Party/LwIP/src/core/udp.c ****   struct udp_pcb *pcb, *prev;
 378              		.loc 1 197 3 view .LVU86
 198:Middlewares/Third_Party/LwIP/src/core/udp.c ****   struct udp_pcb *uncon_pcb;
 379              		.loc 1 198 3 view .LVU87
 199:Middlewares/Third_Party/LwIP/src/core/udp.c ****   u16_t src, dest;
 380              		.loc 1 199 3 view .LVU88
 200:Middlewares/Third_Party/LwIP/src/core/udp.c ****   u8_t broadcast;
 381              		.loc 1 200 3 view .LVU89
 201:Middlewares/Third_Party/LwIP/src/core/udp.c ****   u8_t for_us = 0;
 382              		.loc 1 201 3 view .LVU90
 383              	.LVL23:
 202:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 203:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_UNUSED_ARG(inp);
 384              		.loc 1 203 3 view .LVU91
 204:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 205:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ASSERT_CORE_LOCKED();
 385              		.loc 1 205 28 view .LVU92
 206:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 207:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ASSERT("udp_input: invalid pbuf", p != NULL);
 386              		.loc 1 207 3 view .LVU93
 387              		.loc 1 207 3 view .LVU94
 388 0008 8146     		mov	r9, r0
 389 000a D0B1     		cbz	r0, .L58
 390              	.LVL24:
 391              	.L38:
 392              		.loc 1 207 3 discriminator 3 view .LVU95
 393              		.loc 1 207 3 discriminator 3 view .LVU96
 208:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ASSERT("udp_input: invalid netif", inp != NULL);
 394              		.loc 1 208 3 view .LVU97
 395              		.loc 1 208 3 view .LVU98
 396 000c 07B3     		cbz	r7, .L59
 397              	.L39:
 398              		.loc 1 208 3 discriminator 3 view .LVU99
 399              		.loc 1 208 3 discriminator 3 view .LVU100
 209:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 210:Middlewares/Third_Party/LwIP/src/core/udp.c ****   PERF_START;
 400              		.loc 1 210 13 view .LVU101
 211:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 212:Middlewares/Third_Party/LwIP/src/core/udp.c ****   UDP_STATS_INC(udp.recv);
 401              		.loc 1 212 26 view .LVU102
 213:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 214:Middlewares/Third_Party/LwIP/src/core/udp.c ****   /* Check minimum length (UDP header) */
 215:Middlewares/Third_Party/LwIP/src/core/udp.c ****   if (p->len < UDP_HLEN) {
 402              		.loc 1 215 3 view .LVU103
 403              		.loc 1 215 8 is_stmt 0 view .LVU104
 404 000e B9F80A30 		ldrh	r3, [r9, #10]
 405              		.loc 1 215 6 view .LVU105
 406 0012 072B     		cmp	r3, #7
 407 0014 23D9     		bls	.L60
 216:Middlewares/Third_Party/LwIP/src/core/udp.c ****     /* drop short packets */
 217:Middlewares/Third_Party/LwIP/src/core/udp.c ****     LWIP_DEBUGF(UDP_DEBUG,
 218:Middlewares/Third_Party/LwIP/src/core/udp.c ****                 ("udp_input: short UDP datagram (%"U16_F" bytes) discarded\n", p->tot_len));
 219:Middlewares/Third_Party/LwIP/src/core/udp.c ****     UDP_STATS_INC(udp.lenerr);
 220:Middlewares/Third_Party/LwIP/src/core/udp.c ****     UDP_STATS_INC(udp.drop);
 221:Middlewares/Third_Party/LwIP/src/core/udp.c ****     MIB2_STATS_INC(mib2.udpinerrors);
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 13


 222:Middlewares/Third_Party/LwIP/src/core/udp.c ****     pbuf_free(p);
 223:Middlewares/Third_Party/LwIP/src/core/udp.c ****     goto end;
 224:Middlewares/Third_Party/LwIP/src/core/udp.c ****   }
 225:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 226:Middlewares/Third_Party/LwIP/src/core/udp.c ****   udphdr = (struct udp_hdr *)p->payload;
 408              		.loc 1 226 3 is_stmt 1 view .LVU106
 409              		.loc 1 226 10 is_stmt 0 view .LVU107
 410 0016 D9F80440 		ldr	r4, [r9, #4]
 411              	.LVL25:
 227:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 228:Middlewares/Third_Party/LwIP/src/core/udp.c ****   /* is broadcast packet ? */
 229:Middlewares/Third_Party/LwIP/src/core/udp.c ****   broadcast = ip_addr_isbroadcast(ip_current_dest_addr(), ip_current_netif());
 412              		.loc 1 229 3 is_stmt 1 view .LVU108
 413              		.loc 1 229 15 is_stmt 0 view .LVU109
 414 001a 534B     		ldr	r3, .L65
 415 001c 1968     		ldr	r1, [r3]
 416 001e 5869     		ldr	r0, [r3, #20]
 417 0020 FFF7FEFF 		bl	ip4_addr_isbroadcast_u32
 418              	.LVL26:
 419 0024 8046     		mov	r8, r0
 420              	.LVL27:
 230:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 231:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_DEBUGF(UDP_DEBUG, ("udp_input: received datagram of length %"U16_F"\n", p->tot_len));
 421              		.loc 1 231 92 is_stmt 1 view .LVU110
 232:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 233:Middlewares/Third_Party/LwIP/src/core/udp.c ****   /* convert src and dest ports to host byte order */
 234:Middlewares/Third_Party/LwIP/src/core/udp.c ****   src = lwip_ntohs(udphdr->src);
 422              		.loc 1 234 3 view .LVU111
 423              		.loc 1 234 9 is_stmt 0 view .LVU112
 424 0026 2088     		ldrh	r0, [r4]	@ unaligned
 425 0028 FFF7FEFF 		bl	lwip_htons
 426              	.LVL28:
 427 002c 8246     		mov	r10, r0
 428              	.LVL29:
 235:Middlewares/Third_Party/LwIP/src/core/udp.c ****   dest = lwip_ntohs(udphdr->dest);
 429              		.loc 1 235 3 is_stmt 1 view .LVU113
 430              		.loc 1 235 10 is_stmt 0 view .LVU114
 431 002e 6088     		ldrh	r0, [r4, #2]	@ unaligned
 432 0030 FFF7FEFF 		bl	lwip_htons
 433              	.LVL30:
 434 0034 0546     		mov	r5, r0
 435              	.LVL31:
 236:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 237:Middlewares/Third_Party/LwIP/src/core/udp.c ****   udp_debug_print(udphdr);
 436              		.loc 1 237 26 is_stmt 1 view .LVU115
 238:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 239:Middlewares/Third_Party/LwIP/src/core/udp.c ****   /* print the UDP source and destination */
 240:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_DEBUGF(UDP_DEBUG, ("udp ("));
 437              		.loc 1 240 36 view .LVU116
 241:Middlewares/Third_Party/LwIP/src/core/udp.c ****   ip_addr_debug_print_val(UDP_DEBUG, *ip_current_dest_addr());
 438              		.loc 1 241 62 view .LVU117
 242:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_DEBUGF(UDP_DEBUG, (", %"U16_F") <-- (", lwip_ntohs(udphdr->dest)));
 439              		.loc 1 242 74 view .LVU118
 243:Middlewares/Third_Party/LwIP/src/core/udp.c ****   ip_addr_debug_print_val(UDP_DEBUG, *ip_current_src_addr());
 440              		.loc 1 243 61 view .LVU119
 244:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_DEBUGF(UDP_DEBUG, (", %"U16_F")\n", lwip_ntohs(udphdr->src)));
 441              		.loc 1 244 69 view .LVU120
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 14


 245:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 246:Middlewares/Third_Party/LwIP/src/core/udp.c ****   pcb = NULL;
 442              		.loc 1 246 3 view .LVU121
 247:Middlewares/Third_Party/LwIP/src/core/udp.c ****   prev = NULL;
 443              		.loc 1 247 3 view .LVU122
 248:Middlewares/Third_Party/LwIP/src/core/udp.c ****   uncon_pcb = NULL;
 444              		.loc 1 248 3 view .LVU123
 249:Middlewares/Third_Party/LwIP/src/core/udp.c ****   /* Iterate through the UDP pcb list for a matching pcb.
 250:Middlewares/Third_Party/LwIP/src/core/udp.c ****    * 'Perfect match' pcbs (connected to the remote port & ip address) are
 251:Middlewares/Third_Party/LwIP/src/core/udp.c ****    * preferred. If no perfect match is found, the first unconnected pcb that
 252:Middlewares/Third_Party/LwIP/src/core/udp.c ****    * matches the local port and ip address gets the datagram. */
 253:Middlewares/Third_Party/LwIP/src/core/udp.c ****   for (pcb = udp_pcbs; pcb != NULL; pcb = pcb->next) {
 445              		.loc 1 253 3 view .LVU124
 446              		.loc 1 253 12 is_stmt 0 view .LVU125
 447 0036 4D4B     		ldr	r3, .L65+4
 448 0038 1C68     		ldr	r4, [r3]
 449              	.LVL32:
 248:Middlewares/Third_Party/LwIP/src/core/udp.c ****   uncon_pcb = NULL;
 450              		.loc 1 248 13 view .LVU126
 451 003a 4FF0000B 		mov	fp, #0
 247:Middlewares/Third_Party/LwIP/src/core/udp.c ****   uncon_pcb = NULL;
 452              		.loc 1 247 8 view .LVU127
 453 003e 5E46     		mov	r6, fp
 454              		.loc 1 253 3 view .LVU128
 455 0040 19E0     		b	.L42
 456              	.LVL33:
 457              	.L58:
 207:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ASSERT("udp_input: invalid netif", inp != NULL);
 458              		.loc 1 207 3 is_stmt 1 discriminator 1 view .LVU129
 207:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ASSERT("udp_input: invalid netif", inp != NULL);
 459              		.loc 1 207 3 discriminator 1 view .LVU130
 460 0042 4B4B     		ldr	r3, .L65+8
 461 0044 CF22     		movs	r2, #207
 462 0046 4B49     		ldr	r1, .L65+12
 463              	.LVL34:
 207:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ASSERT("udp_input: invalid netif", inp != NULL);
 464              		.loc 1 207 3 is_stmt 0 discriminator 1 view .LVU131
 465 0048 4B48     		ldr	r0, .L65+16
 466              	.LVL35:
 207:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ASSERT("udp_input: invalid netif", inp != NULL);
 467              		.loc 1 207 3 discriminator 1 view .LVU132
 468 004a FFF7FEFF 		bl	printf
 469              	.LVL36:
 470 004e DDE7     		b	.L38
 471              	.L59:
 208:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 472              		.loc 1 208 3 is_stmt 1 discriminator 1 view .LVU133
 208:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 473              		.loc 1 208 3 discriminator 1 view .LVU134
 474 0050 474B     		ldr	r3, .L65+8
 475 0052 D022     		movs	r2, #208
 476 0054 4949     		ldr	r1, .L65+20
 477 0056 4848     		ldr	r0, .L65+16
 478 0058 FFF7FEFF 		bl	printf
 479              	.LVL37:
 480 005c D7E7     		b	.L39
 481              	.L60:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 15


 218:Middlewares/Third_Party/LwIP/src/core/udp.c ****     UDP_STATS_INC(udp.lenerr);
 482              		.loc 1 218 92 view .LVU135
 219:Middlewares/Third_Party/LwIP/src/core/udp.c ****     UDP_STATS_INC(udp.drop);
 483              		.loc 1 219 30 view .LVU136
 220:Middlewares/Third_Party/LwIP/src/core/udp.c ****     MIB2_STATS_INC(mib2.udpinerrors);
 484              		.loc 1 220 28 view .LVU137
 221:Middlewares/Third_Party/LwIP/src/core/udp.c ****     pbuf_free(p);
 485              		.loc 1 221 37 view .LVU138
 222:Middlewares/Third_Party/LwIP/src/core/udp.c ****     goto end;
 486              		.loc 1 222 5 view .LVU139
 487 005e 4846     		mov	r0, r9
 488 0060 FFF7FEFF 		bl	pbuf_free
 489              	.LVL38:
 223:Middlewares/Third_Party/LwIP/src/core/udp.c ****   }
 490              		.loc 1 223 5 view .LVU140
 491              	.L41:
 492              	.L37:
 254:Middlewares/Third_Party/LwIP/src/core/udp.c ****     /* print the PCB local and remote address */
 255:Middlewares/Third_Party/LwIP/src/core/udp.c ****     LWIP_DEBUGF(UDP_DEBUG, ("pcb ("));
 256:Middlewares/Third_Party/LwIP/src/core/udp.c ****     ip_addr_debug_print_val(UDP_DEBUG, pcb->local_ip);
 257:Middlewares/Third_Party/LwIP/src/core/udp.c ****     LWIP_DEBUGF(UDP_DEBUG, (", %"U16_F") <-- (", pcb->local_port));
 258:Middlewares/Third_Party/LwIP/src/core/udp.c ****     ip_addr_debug_print_val(UDP_DEBUG, pcb->remote_ip);
 259:Middlewares/Third_Party/LwIP/src/core/udp.c ****     LWIP_DEBUGF(UDP_DEBUG, (", %"U16_F")\n", pcb->remote_port));
 260:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 261:Middlewares/Third_Party/LwIP/src/core/udp.c ****     /* compare PCB local addr+port to UDP destination addr+port */
 262:Middlewares/Third_Party/LwIP/src/core/udp.c ****     if ((pcb->local_port == dest) &&
 263:Middlewares/Third_Party/LwIP/src/core/udp.c ****         (udp_input_local_match(pcb, inp, broadcast) != 0)) {
 264:Middlewares/Third_Party/LwIP/src/core/udp.c ****       if ((pcb->flags & UDP_FLAGS_CONNECTED) == 0) {
 265:Middlewares/Third_Party/LwIP/src/core/udp.c ****         if (uncon_pcb == NULL) {
 266:Middlewares/Third_Party/LwIP/src/core/udp.c ****           /* the first unconnected matching PCB */
 267:Middlewares/Third_Party/LwIP/src/core/udp.c ****           uncon_pcb = pcb;
 268:Middlewares/Third_Party/LwIP/src/core/udp.c **** #if LWIP_IPV4
 269:Middlewares/Third_Party/LwIP/src/core/udp.c ****         } else if (broadcast && ip4_current_dest_addr()->addr == IPADDR_BROADCAST) {
 270:Middlewares/Third_Party/LwIP/src/core/udp.c ****           /* global broadcast address (only valid for IPv4; match was checked before) */
 271:Middlewares/Third_Party/LwIP/src/core/udp.c ****           if (!IP_IS_V4_VAL(uncon_pcb->local_ip) || !ip4_addr_cmp(ip_2_ip4(&uncon_pcb->local_ip), n
 272:Middlewares/Third_Party/LwIP/src/core/udp.c ****             /* uncon_pcb does not match the input netif, check this pcb */
 273:Middlewares/Third_Party/LwIP/src/core/udp.c ****             if (IP_IS_V4_VAL(pcb->local_ip) && ip4_addr_cmp(ip_2_ip4(&pcb->local_ip), netif_ip4_add
 274:Middlewares/Third_Party/LwIP/src/core/udp.c ****               /* better match */
 275:Middlewares/Third_Party/LwIP/src/core/udp.c ****               uncon_pcb = pcb;
 276:Middlewares/Third_Party/LwIP/src/core/udp.c ****             }
 277:Middlewares/Third_Party/LwIP/src/core/udp.c ****           }
 278:Middlewares/Third_Party/LwIP/src/core/udp.c **** #endif /* LWIP_IPV4 */
 279:Middlewares/Third_Party/LwIP/src/core/udp.c ****         }
 280:Middlewares/Third_Party/LwIP/src/core/udp.c **** #if SO_REUSE
 281:Middlewares/Third_Party/LwIP/src/core/udp.c ****         else if (!ip_addr_isany(&pcb->local_ip)) {
 282:Middlewares/Third_Party/LwIP/src/core/udp.c ****           /* prefer specific IPs over catch-all */
 283:Middlewares/Third_Party/LwIP/src/core/udp.c ****           uncon_pcb = pcb;
 284:Middlewares/Third_Party/LwIP/src/core/udp.c ****         }
 285:Middlewares/Third_Party/LwIP/src/core/udp.c **** #endif /* SO_REUSE */
 286:Middlewares/Third_Party/LwIP/src/core/udp.c ****       }
 287:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 288:Middlewares/Third_Party/LwIP/src/core/udp.c ****       /* compare PCB remote addr+port to UDP source addr+port */
 289:Middlewares/Third_Party/LwIP/src/core/udp.c ****       if ((pcb->remote_port == src) &&
 290:Middlewares/Third_Party/LwIP/src/core/udp.c ****           (ip_addr_isany_val(pcb->remote_ip) ||
 291:Middlewares/Third_Party/LwIP/src/core/udp.c ****            ip_addr_cmp(&pcb->remote_ip, ip_current_src_addr()))) {
 292:Middlewares/Third_Party/LwIP/src/core/udp.c ****         /* the first fully matching PCB */
 293:Middlewares/Third_Party/LwIP/src/core/udp.c ****         if (prev != NULL) {
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 16


 294:Middlewares/Third_Party/LwIP/src/core/udp.c ****           /* move the pcb to the front of udp_pcbs so that is
 295:Middlewares/Third_Party/LwIP/src/core/udp.c ****              found faster next time */
 296:Middlewares/Third_Party/LwIP/src/core/udp.c ****           prev->next = pcb->next;
 297:Middlewares/Third_Party/LwIP/src/core/udp.c ****           pcb->next = udp_pcbs;
 298:Middlewares/Third_Party/LwIP/src/core/udp.c ****           udp_pcbs = pcb;
 299:Middlewares/Third_Party/LwIP/src/core/udp.c ****         } else {
 300:Middlewares/Third_Party/LwIP/src/core/udp.c ****           UDP_STATS_INC(udp.cachehit);
 301:Middlewares/Third_Party/LwIP/src/core/udp.c ****         }
 302:Middlewares/Third_Party/LwIP/src/core/udp.c ****         break;
 303:Middlewares/Third_Party/LwIP/src/core/udp.c ****       }
 304:Middlewares/Third_Party/LwIP/src/core/udp.c ****     }
 305:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 306:Middlewares/Third_Party/LwIP/src/core/udp.c ****     prev = pcb;
 307:Middlewares/Third_Party/LwIP/src/core/udp.c ****   }
 308:Middlewares/Third_Party/LwIP/src/core/udp.c ****   /* no fully matching pcb found? then look for an unconnected pcb */
 309:Middlewares/Third_Party/LwIP/src/core/udp.c ****   if (pcb == NULL) {
 310:Middlewares/Third_Party/LwIP/src/core/udp.c ****     pcb = uncon_pcb;
 311:Middlewares/Third_Party/LwIP/src/core/udp.c ****   }
 312:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 313:Middlewares/Third_Party/LwIP/src/core/udp.c ****   /* Check checksum if this is a match or if it was directed at us. */
 314:Middlewares/Third_Party/LwIP/src/core/udp.c ****   if (pcb != NULL) {
 315:Middlewares/Third_Party/LwIP/src/core/udp.c ****     for_us = 1;
 316:Middlewares/Third_Party/LwIP/src/core/udp.c ****   } else {
 317:Middlewares/Third_Party/LwIP/src/core/udp.c **** #if LWIP_IPV6
 318:Middlewares/Third_Party/LwIP/src/core/udp.c ****     if (ip_current_is_v6()) {
 319:Middlewares/Third_Party/LwIP/src/core/udp.c ****       for_us = netif_get_ip6_addr_match(inp, ip6_current_dest_addr()) >= 0;
 320:Middlewares/Third_Party/LwIP/src/core/udp.c ****     }
 321:Middlewares/Third_Party/LwIP/src/core/udp.c **** #endif /* LWIP_IPV6 */
 322:Middlewares/Third_Party/LwIP/src/core/udp.c **** #if LWIP_IPV4
 323:Middlewares/Third_Party/LwIP/src/core/udp.c ****     if (!ip_current_is_v6()) {
 324:Middlewares/Third_Party/LwIP/src/core/udp.c ****       for_us = ip4_addr_cmp(netif_ip4_addr(inp), ip4_current_dest_addr());
 325:Middlewares/Third_Party/LwIP/src/core/udp.c ****     }
 326:Middlewares/Third_Party/LwIP/src/core/udp.c **** #endif /* LWIP_IPV4 */
 327:Middlewares/Third_Party/LwIP/src/core/udp.c ****   }
 328:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 329:Middlewares/Third_Party/LwIP/src/core/udp.c ****   if (for_us) {
 330:Middlewares/Third_Party/LwIP/src/core/udp.c ****     LWIP_DEBUGF(UDP_DEBUG | LWIP_DBG_TRACE, ("udp_input: calculating checksum\n"));
 331:Middlewares/Third_Party/LwIP/src/core/udp.c **** #if CHECKSUM_CHECK_UDP
 332:Middlewares/Third_Party/LwIP/src/core/udp.c ****     IF__NETIF_CHECKSUM_ENABLED(inp, NETIF_CHECKSUM_CHECK_UDP) {
 333:Middlewares/Third_Party/LwIP/src/core/udp.c **** #if LWIP_UDPLITE
 334:Middlewares/Third_Party/LwIP/src/core/udp.c ****       if (ip_current_header_proto() == IP_PROTO_UDPLITE) {
 335:Middlewares/Third_Party/LwIP/src/core/udp.c ****         /* Do the UDP Lite checksum */
 336:Middlewares/Third_Party/LwIP/src/core/udp.c ****         u16_t chklen = lwip_ntohs(udphdr->len);
 337:Middlewares/Third_Party/LwIP/src/core/udp.c ****         if (chklen < sizeof(struct udp_hdr)) {
 338:Middlewares/Third_Party/LwIP/src/core/udp.c ****           if (chklen == 0) {
 339:Middlewares/Third_Party/LwIP/src/core/udp.c ****             /* For UDP-Lite, checksum length of 0 means checksum
 340:Middlewares/Third_Party/LwIP/src/core/udp.c ****                over the complete packet (See RFC 3828 chap. 3.1) */
 341:Middlewares/Third_Party/LwIP/src/core/udp.c ****             chklen = p->tot_len;
 342:Middlewares/Third_Party/LwIP/src/core/udp.c ****           } else {
 343:Middlewares/Third_Party/LwIP/src/core/udp.c ****             /* At least the UDP-Lite header must be covered by the
 344:Middlewares/Third_Party/LwIP/src/core/udp.c ****                checksum! (Again, see RFC 3828 chap. 3.1) */
 345:Middlewares/Third_Party/LwIP/src/core/udp.c ****             goto chkerr;
 346:Middlewares/Third_Party/LwIP/src/core/udp.c ****           }
 347:Middlewares/Third_Party/LwIP/src/core/udp.c ****         }
 348:Middlewares/Third_Party/LwIP/src/core/udp.c ****         if (ip_chksum_pseudo_partial(p, IP_PROTO_UDPLITE,
 349:Middlewares/Third_Party/LwIP/src/core/udp.c ****                                      p->tot_len, chklen,
 350:Middlewares/Third_Party/LwIP/src/core/udp.c ****                                      ip_current_src_addr(), ip_current_dest_addr()) != 0) {
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 17


 351:Middlewares/Third_Party/LwIP/src/core/udp.c ****           goto chkerr;
 352:Middlewares/Third_Party/LwIP/src/core/udp.c ****         }
 353:Middlewares/Third_Party/LwIP/src/core/udp.c ****       } else
 354:Middlewares/Third_Party/LwIP/src/core/udp.c **** #endif /* LWIP_UDPLITE */
 355:Middlewares/Third_Party/LwIP/src/core/udp.c ****       {
 356:Middlewares/Third_Party/LwIP/src/core/udp.c ****         if (udphdr->chksum != 0) {
 357:Middlewares/Third_Party/LwIP/src/core/udp.c ****           if (ip_chksum_pseudo(p, IP_PROTO_UDP, p->tot_len,
 358:Middlewares/Third_Party/LwIP/src/core/udp.c ****                                ip_current_src_addr(),
 359:Middlewares/Third_Party/LwIP/src/core/udp.c ****                                ip_current_dest_addr()) != 0) {
 360:Middlewares/Third_Party/LwIP/src/core/udp.c ****             goto chkerr;
 361:Middlewares/Third_Party/LwIP/src/core/udp.c ****           }
 362:Middlewares/Third_Party/LwIP/src/core/udp.c ****         }
 363:Middlewares/Third_Party/LwIP/src/core/udp.c ****       }
 364:Middlewares/Third_Party/LwIP/src/core/udp.c ****     }
 365:Middlewares/Third_Party/LwIP/src/core/udp.c **** #endif /* CHECKSUM_CHECK_UDP */
 366:Middlewares/Third_Party/LwIP/src/core/udp.c ****     if (pbuf_remove_header(p, UDP_HLEN)) {
 367:Middlewares/Third_Party/LwIP/src/core/udp.c ****       /* Can we cope with this failing? Just assert for now */
 368:Middlewares/Third_Party/LwIP/src/core/udp.c ****       LWIP_ASSERT("pbuf_remove_header failed\n", 0);
 369:Middlewares/Third_Party/LwIP/src/core/udp.c ****       UDP_STATS_INC(udp.drop);
 370:Middlewares/Third_Party/LwIP/src/core/udp.c ****       MIB2_STATS_INC(mib2.udpinerrors);
 371:Middlewares/Third_Party/LwIP/src/core/udp.c ****       pbuf_free(p);
 372:Middlewares/Third_Party/LwIP/src/core/udp.c ****       goto end;
 373:Middlewares/Third_Party/LwIP/src/core/udp.c ****     }
 374:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 375:Middlewares/Third_Party/LwIP/src/core/udp.c ****     if (pcb != NULL) {
 376:Middlewares/Third_Party/LwIP/src/core/udp.c ****       MIB2_STATS_INC(mib2.udpindatagrams);
 377:Middlewares/Third_Party/LwIP/src/core/udp.c **** #if SO_REUSE && SO_REUSE_RXTOALL
 378:Middlewares/Third_Party/LwIP/src/core/udp.c ****       if (ip_get_option(pcb, SOF_REUSEADDR) &&
 379:Middlewares/Third_Party/LwIP/src/core/udp.c ****           (broadcast || ip_addr_ismulticast(ip_current_dest_addr()))) {
 380:Middlewares/Third_Party/LwIP/src/core/udp.c ****         /* pass broadcast- or multicast packets to all multicast pcbs
 381:Middlewares/Third_Party/LwIP/src/core/udp.c ****            if SOF_REUSEADDR is set on the first match */
 382:Middlewares/Third_Party/LwIP/src/core/udp.c ****         struct udp_pcb *mpcb;
 383:Middlewares/Third_Party/LwIP/src/core/udp.c ****         for (mpcb = udp_pcbs; mpcb != NULL; mpcb = mpcb->next) {
 384:Middlewares/Third_Party/LwIP/src/core/udp.c ****           if (mpcb != pcb) {
 385:Middlewares/Third_Party/LwIP/src/core/udp.c ****             /* compare PCB local addr+port to UDP destination addr+port */
 386:Middlewares/Third_Party/LwIP/src/core/udp.c ****             if ((mpcb->local_port == dest) &&
 387:Middlewares/Third_Party/LwIP/src/core/udp.c ****                 (udp_input_local_match(mpcb, inp, broadcast) != 0)) {
 388:Middlewares/Third_Party/LwIP/src/core/udp.c ****               /* pass a copy of the packet to all local matches */
 389:Middlewares/Third_Party/LwIP/src/core/udp.c ****               if (mpcb->recv != NULL) {
 390:Middlewares/Third_Party/LwIP/src/core/udp.c ****                 struct pbuf *q;
 391:Middlewares/Third_Party/LwIP/src/core/udp.c ****                 q = pbuf_clone(PBUF_RAW, PBUF_POOL, p);
 392:Middlewares/Third_Party/LwIP/src/core/udp.c ****                 if (q != NULL) {
 393:Middlewares/Third_Party/LwIP/src/core/udp.c ****                   mpcb->recv(mpcb->recv_arg, mpcb, q, ip_current_src_addr(), src);
 394:Middlewares/Third_Party/LwIP/src/core/udp.c ****                 }
 395:Middlewares/Third_Party/LwIP/src/core/udp.c ****               }
 396:Middlewares/Third_Party/LwIP/src/core/udp.c ****             }
 397:Middlewares/Third_Party/LwIP/src/core/udp.c ****           }
 398:Middlewares/Third_Party/LwIP/src/core/udp.c ****         }
 399:Middlewares/Third_Party/LwIP/src/core/udp.c ****       }
 400:Middlewares/Third_Party/LwIP/src/core/udp.c **** #endif /* SO_REUSE && SO_REUSE_RXTOALL */
 401:Middlewares/Third_Party/LwIP/src/core/udp.c ****       /* callback */
 402:Middlewares/Third_Party/LwIP/src/core/udp.c ****       if (pcb->recv != NULL) {
 403:Middlewares/Third_Party/LwIP/src/core/udp.c ****         /* now the recv function is responsible for freeing p */
 404:Middlewares/Third_Party/LwIP/src/core/udp.c ****         pcb->recv(pcb->recv_arg, pcb, p, ip_current_src_addr(), src);
 405:Middlewares/Third_Party/LwIP/src/core/udp.c ****       } else {
 406:Middlewares/Third_Party/LwIP/src/core/udp.c ****         /* no recv function registered? then we have to free the pbuf! */
 407:Middlewares/Third_Party/LwIP/src/core/udp.c ****         pbuf_free(p);
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 18


 408:Middlewares/Third_Party/LwIP/src/core/udp.c ****         goto end;
 409:Middlewares/Third_Party/LwIP/src/core/udp.c ****       }
 410:Middlewares/Third_Party/LwIP/src/core/udp.c ****     } else {
 411:Middlewares/Third_Party/LwIP/src/core/udp.c ****       LWIP_DEBUGF(UDP_DEBUG | LWIP_DBG_TRACE, ("udp_input: not for us.\n"));
 412:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 413:Middlewares/Third_Party/LwIP/src/core/udp.c **** #if LWIP_ICMP || LWIP_ICMP6
 414:Middlewares/Third_Party/LwIP/src/core/udp.c ****       /* No match was found, send ICMP destination port unreachable unless
 415:Middlewares/Third_Party/LwIP/src/core/udp.c ****          destination address was broadcast/multicast. */
 416:Middlewares/Third_Party/LwIP/src/core/udp.c ****       if (!broadcast && !ip_addr_ismulticast(ip_current_dest_addr())) {
 417:Middlewares/Third_Party/LwIP/src/core/udp.c ****         /* move payload pointer back to ip header */
 418:Middlewares/Third_Party/LwIP/src/core/udp.c ****         pbuf_header_force(p, (s16_t)(ip_current_header_tot_len() + UDP_HLEN));
 419:Middlewares/Third_Party/LwIP/src/core/udp.c ****         icmp_port_unreach(ip_current_is_v6(), p);
 420:Middlewares/Third_Party/LwIP/src/core/udp.c ****       }
 421:Middlewares/Third_Party/LwIP/src/core/udp.c **** #endif /* LWIP_ICMP || LWIP_ICMP6 */
 422:Middlewares/Third_Party/LwIP/src/core/udp.c ****       UDP_STATS_INC(udp.proterr);
 423:Middlewares/Third_Party/LwIP/src/core/udp.c ****       UDP_STATS_INC(udp.drop);
 424:Middlewares/Third_Party/LwIP/src/core/udp.c ****       MIB2_STATS_INC(mib2.udpnoports);
 425:Middlewares/Third_Party/LwIP/src/core/udp.c ****       pbuf_free(p);
 426:Middlewares/Third_Party/LwIP/src/core/udp.c ****     }
 427:Middlewares/Third_Party/LwIP/src/core/udp.c ****   } else {
 428:Middlewares/Third_Party/LwIP/src/core/udp.c ****     pbuf_free(p);
 429:Middlewares/Third_Party/LwIP/src/core/udp.c ****   }
 430:Middlewares/Third_Party/LwIP/src/core/udp.c **** end:
 431:Middlewares/Third_Party/LwIP/src/core/udp.c ****   PERF_STOP("udp_input");
 432:Middlewares/Third_Party/LwIP/src/core/udp.c ****   return;
 433:Middlewares/Third_Party/LwIP/src/core/udp.c **** #if CHECKSUM_CHECK_UDP
 434:Middlewares/Third_Party/LwIP/src/core/udp.c **** chkerr:
 435:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_DEBUGF(UDP_DEBUG | LWIP_DBG_LEVEL_SERIOUS,
 436:Middlewares/Third_Party/LwIP/src/core/udp.c ****               ("udp_input: UDP (or UDP Lite) datagram discarded due to failing checksum\n"));
 437:Middlewares/Third_Party/LwIP/src/core/udp.c ****   UDP_STATS_INC(udp.chkerr);
 438:Middlewares/Third_Party/LwIP/src/core/udp.c ****   UDP_STATS_INC(udp.drop);
 439:Middlewares/Third_Party/LwIP/src/core/udp.c ****   MIB2_STATS_INC(mib2.udpinerrors);
 440:Middlewares/Third_Party/LwIP/src/core/udp.c ****   pbuf_free(p);
 441:Middlewares/Third_Party/LwIP/src/core/udp.c ****   PERF_STOP("udp_input");
 442:Middlewares/Third_Party/LwIP/src/core/udp.c **** #endif /* CHECKSUM_CHECK_UDP */
 443:Middlewares/Third_Party/LwIP/src/core/udp.c **** }
 493              		.loc 1 443 1 is_stmt 0 view .LVU141
 494 0064 03B0     		add	sp, sp, #12
 495              	.LCFI4:
 496              		.cfi_remember_state
 497              		.cfi_def_cfa_offset 36
 498              		@ sp needed
 499 0066 BDE8F08F 		pop	{r4, r5, r6, r7, r8, r9, r10, fp, pc}
 500              	.LVL39:
 501              	.L55:
 502              	.LCFI5:
 503              		.cfi_restore_state
 267:Middlewares/Third_Party/LwIP/src/core/udp.c **** #if LWIP_IPV4
 504              		.loc 1 267 21 view .LVU142
 505 006a A346     		mov	fp, r4
 506              	.LVL40:
 507              	.L44:
 289:Middlewares/Third_Party/LwIP/src/core/udp.c ****           (ip_addr_isany_val(pcb->remote_ip) ||
 508              		.loc 1 289 7 is_stmt 1 view .LVU143
 289:Middlewares/Third_Party/LwIP/src/core/udp.c ****           (ip_addr_isany_val(pcb->remote_ip) ||
 509              		.loc 1 289 15 is_stmt 0 view .LVU144
 510 006c A38A     		ldrh	r3, [r4, #20]
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 19


 289:Middlewares/Third_Party/LwIP/src/core/udp.c ****           (ip_addr_isany_val(pcb->remote_ip) ||
 511              		.loc 1 289 10 view .LVU145
 512 006e 5345     		cmp	r3, r10
 513 0070 26D0     		beq	.L61
 514              	.L43:
 306:Middlewares/Third_Party/LwIP/src/core/udp.c ****   }
 515              		.loc 1 306 5 is_stmt 1 view .LVU146
 516              	.LVL41:
 253:Middlewares/Third_Party/LwIP/src/core/udp.c ****     /* print the PCB local and remote address */
 517              		.loc 1 253 41 discriminator 2 view .LVU147
 306:Middlewares/Third_Party/LwIP/src/core/udp.c ****   }
 518              		.loc 1 306 10 is_stmt 0 view .LVU148
 519 0072 2646     		mov	r6, r4
 253:Middlewares/Third_Party/LwIP/src/core/udp.c ****     /* print the PCB local and remote address */
 520              		.loc 1 253 41 discriminator 2 view .LVU149
 521 0074 E468     		ldr	r4, [r4, #12]
 522              	.LVL42:
 523              	.L42:
 253:Middlewares/Third_Party/LwIP/src/core/udp.c ****     /* print the PCB local and remote address */
 524              		.loc 1 253 28 is_stmt 1 discriminator 1 view .LVU150
 525 0076 002C     		cmp	r4, #0
 526 0078 30D0     		beq	.L46
 255:Middlewares/Third_Party/LwIP/src/core/udp.c ****     ip_addr_debug_print_val(UDP_DEBUG, pcb->local_ip);
 527              		.loc 1 255 38 view .LVU151
 256:Middlewares/Third_Party/LwIP/src/core/udp.c ****     LWIP_DEBUGF(UDP_DEBUG, (", %"U16_F") <-- (", pcb->local_port));
 528              		.loc 1 256 54 view .LVU152
 257:Middlewares/Third_Party/LwIP/src/core/udp.c ****     ip_addr_debug_print_val(UDP_DEBUG, pcb->remote_ip);
 529              		.loc 1 257 67 view .LVU153
 258:Middlewares/Third_Party/LwIP/src/core/udp.c ****     LWIP_DEBUGF(UDP_DEBUG, (", %"U16_F")\n", pcb->remote_port));
 530              		.loc 1 258 55 view .LVU154
 259:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 531              		.loc 1 259 64 view .LVU155
 262:Middlewares/Third_Party/LwIP/src/core/udp.c ****         (udp_input_local_match(pcb, inp, broadcast) != 0)) {
 532              		.loc 1 262 5 view .LVU156
 262:Middlewares/Third_Party/LwIP/src/core/udp.c ****         (udp_input_local_match(pcb, inp, broadcast) != 0)) {
 533              		.loc 1 262 13 is_stmt 0 view .LVU157
 534 007a 638A     		ldrh	r3, [r4, #18]
 262:Middlewares/Third_Party/LwIP/src/core/udp.c ****         (udp_input_local_match(pcb, inp, broadcast) != 0)) {
 535              		.loc 1 262 8 view .LVU158
 536 007c AB42     		cmp	r3, r5
 537 007e F8D1     		bne	.L43
 263:Middlewares/Third_Party/LwIP/src/core/udp.c ****       if ((pcb->flags & UDP_FLAGS_CONNECTED) == 0) {
 538              		.loc 1 263 10 view .LVU159
 539 0080 4246     		mov	r2, r8
 540 0082 3946     		mov	r1, r7
 541 0084 2046     		mov	r0, r4
 542 0086 FFF7FEFF 		bl	udp_input_local_match
 543              	.LVL43:
 262:Middlewares/Third_Party/LwIP/src/core/udp.c ****         (udp_input_local_match(pcb, inp, broadcast) != 0)) {
 544              		.loc 1 262 35 discriminator 1 view .LVU160
 545 008a 0028     		cmp	r0, #0
 546 008c F1D0     		beq	.L43
 264:Middlewares/Third_Party/LwIP/src/core/udp.c ****         if (uncon_pcb == NULL) {
 547              		.loc 1 264 7 is_stmt 1 view .LVU161
 264:Middlewares/Third_Party/LwIP/src/core/udp.c ****         if (uncon_pcb == NULL) {
 548              		.loc 1 264 15 is_stmt 0 view .LVU162
 549 008e 237C     		ldrb	r3, [r4, #16]	@ zero_extendqisi2
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 20


 264:Middlewares/Third_Party/LwIP/src/core/udp.c ****         if (uncon_pcb == NULL) {
 550              		.loc 1 264 10 view .LVU163
 551 0090 13F0040F 		tst	r3, #4
 552 0094 EAD1     		bne	.L44
 265:Middlewares/Third_Party/LwIP/src/core/udp.c ****           /* the first unconnected matching PCB */
 553              		.loc 1 265 9 is_stmt 1 view .LVU164
 265:Middlewares/Third_Party/LwIP/src/core/udp.c ****           /* the first unconnected matching PCB */
 554              		.loc 1 265 12 is_stmt 0 view .LVU165
 555 0096 BBF1000F 		cmp	fp, #0
 556 009a E6D0     		beq	.L55
 269:Middlewares/Third_Party/LwIP/src/core/udp.c ****           /* global broadcast address (only valid for IPv4; match was checked before) */
 557              		.loc 1 269 16 is_stmt 1 view .LVU166
 269:Middlewares/Third_Party/LwIP/src/core/udp.c ****           /* global broadcast address (only valid for IPv4; match was checked before) */
 558              		.loc 1 269 19 is_stmt 0 view .LVU167
 559 009c B8F1000F 		cmp	r8, #0
 560 00a0 E4D0     		beq	.L44
 269:Middlewares/Third_Party/LwIP/src/core/udp.c ****           /* global broadcast address (only valid for IPv4; match was checked before) */
 561              		.loc 1 269 56 discriminator 1 view .LVU168
 562 00a2 314B     		ldr	r3, .L65
 563 00a4 5B69     		ldr	r3, [r3, #20]
 269:Middlewares/Third_Party/LwIP/src/core/udp.c ****           /* global broadcast address (only valid for IPv4; match was checked before) */
 564              		.loc 1 269 30 discriminator 1 view .LVU169
 565 00a6 B3F1FF3F 		cmp	r3, #-1
 566 00aa DFD1     		bne	.L44
 271:Middlewares/Third_Party/LwIP/src/core/udp.c ****             /* uncon_pcb does not match the input netif, check this pcb */
 567              		.loc 1 271 11 is_stmt 1 view .LVU170
 271:Middlewares/Third_Party/LwIP/src/core/udp.c ****             /* uncon_pcb does not match the input netif, check this pcb */
 568              		.loc 1 271 54 is_stmt 0 view .LVU171
 569 00ac DBF80020 		ldr	r2, [fp]
 570 00b0 7B68     		ldr	r3, [r7, #4]
 271:Middlewares/Third_Party/LwIP/src/core/udp.c ****             /* uncon_pcb does not match the input netif, check this pcb */
 571              		.loc 1 271 14 view .LVU172
 572 00b2 9A42     		cmp	r2, r3
 573 00b4 DAD0     		beq	.L44
 273:Middlewares/Third_Party/LwIP/src/core/udp.c ****               /* better match */
 574              		.loc 1 273 13 is_stmt 1 view .LVU173
 273:Middlewares/Third_Party/LwIP/src/core/udp.c ****               /* better match */
 575              		.loc 1 273 48 is_stmt 0 view .LVU174
 576 00b6 2268     		ldr	r2, [r4]
 273:Middlewares/Third_Party/LwIP/src/core/udp.c ****               /* better match */
 577              		.loc 1 273 16 view .LVU175
 578 00b8 9342     		cmp	r3, r2
 579 00ba D7D1     		bne	.L44
 580              	.LVL44:
 275:Middlewares/Third_Party/LwIP/src/core/udp.c ****             }
 581              		.loc 1 275 25 view .LVU176
 582 00bc A346     		mov	fp, r4
 583 00be D5E7     		b	.L44
 584              	.LVL45:
 585              	.L61:
 290:Middlewares/Third_Party/LwIP/src/core/udp.c ****            ip_addr_cmp(&pcb->remote_ip, ip_current_src_addr()))) {
 586              		.loc 1 290 12 view .LVU177
 587 00c0 6368     		ldr	r3, [r4, #4]
 289:Middlewares/Third_Party/LwIP/src/core/udp.c ****           (ip_addr_isany_val(pcb->remote_ip) ||
 588              		.loc 1 289 37 discriminator 1 view .LVU178
 589 00c2 1BB1     		cbz	r3, .L45
 291:Middlewares/Third_Party/LwIP/src/core/udp.c ****         /* the first fully matching PCB */
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 21


 590              		.loc 1 291 12 view .LVU179
 591 00c4 284A     		ldr	r2, .L65
 592 00c6 1269     		ldr	r2, [r2, #16]
 290:Middlewares/Third_Party/LwIP/src/core/udp.c ****            ip_addr_cmp(&pcb->remote_ip, ip_current_src_addr()))) {
 593              		.loc 1 290 46 view .LVU180
 594 00c8 9342     		cmp	r3, r2
 595 00ca D2D1     		bne	.L43
 596              	.L45:
 293:Middlewares/Third_Party/LwIP/src/core/udp.c ****           /* move the pcb to the front of udp_pcbs so that is
 597              		.loc 1 293 9 is_stmt 1 view .LVU181
 293:Middlewares/Third_Party/LwIP/src/core/udp.c ****           /* move the pcb to the front of udp_pcbs so that is
 598              		.loc 1 293 12 is_stmt 0 view .LVU182
 599 00cc 36B1     		cbz	r6, .L46
 296:Middlewares/Third_Party/LwIP/src/core/udp.c ****           pcb->next = udp_pcbs;
 600              		.loc 1 296 11 is_stmt 1 view .LVU183
 296:Middlewares/Third_Party/LwIP/src/core/udp.c ****           pcb->next = udp_pcbs;
 601              		.loc 1 296 27 is_stmt 0 view .LVU184
 602 00ce E368     		ldr	r3, [r4, #12]
 296:Middlewares/Third_Party/LwIP/src/core/udp.c ****           pcb->next = udp_pcbs;
 603              		.loc 1 296 22 view .LVU185
 604 00d0 F360     		str	r3, [r6, #12]
 297:Middlewares/Third_Party/LwIP/src/core/udp.c ****           udp_pcbs = pcb;
 605              		.loc 1 297 11 is_stmt 1 view .LVU186
 297:Middlewares/Third_Party/LwIP/src/core/udp.c ****           udp_pcbs = pcb;
 606              		.loc 1 297 21 is_stmt 0 view .LVU187
 607 00d2 264B     		ldr	r3, .L65+4
 608 00d4 1A68     		ldr	r2, [r3]
 609 00d6 E260     		str	r2, [r4, #12]
 298:Middlewares/Third_Party/LwIP/src/core/udp.c ****         } else {
 610              		.loc 1 298 11 is_stmt 1 view .LVU188
 298:Middlewares/Third_Party/LwIP/src/core/udp.c ****         } else {
 611              		.loc 1 298 20 is_stmt 0 view .LVU189
 612 00d8 1C60     		str	r4, [r3]
 309:Middlewares/Third_Party/LwIP/src/core/udp.c ****     pcb = uncon_pcb;
 613              		.loc 1 309 3 is_stmt 1 view .LVU190
 614 00da 11E0     		b	.L47
 615              	.L46:
 309:Middlewares/Third_Party/LwIP/src/core/udp.c ****     pcb = uncon_pcb;
 616              		.loc 1 309 3 view .LVU191
 309:Middlewares/Third_Party/LwIP/src/core/udp.c ****     pcb = uncon_pcb;
 617              		.loc 1 309 6 is_stmt 0 view .LVU192
 618 00dc 7CB1     		cbz	r4, .L62
 619              	.LVL46:
 620              	.L49:
 330:Middlewares/Third_Party/LwIP/src/core/udp.c **** #if CHECKSUM_CHECK_UDP
 621              		.loc 1 330 83 is_stmt 1 view .LVU193
 366:Middlewares/Third_Party/LwIP/src/core/udp.c ****       /* Can we cope with this failing? Just assert for now */
 622              		.loc 1 366 5 view .LVU194
 366:Middlewares/Third_Party/LwIP/src/core/udp.c ****       /* Can we cope with this failing? Just assert for now */
 623              		.loc 1 366 9 is_stmt 0 view .LVU195
 624 00de 0821     		movs	r1, #8
 625 00e0 4846     		mov	r0, r9
 626 00e2 FFF7FEFF 		bl	pbuf_remove_header
 627              	.LVL47:
 366:Middlewares/Third_Party/LwIP/src/core/udp.c ****       /* Can we cope with this failing? Just assert for now */
 628              		.loc 1 366 8 discriminator 1 view .LVU196
 629 00e6 B0B9     		cbnz	r0, .L63
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 22


 375:Middlewares/Third_Party/LwIP/src/core/udp.c ****       MIB2_STATS_INC(mib2.udpindatagrams);
 630              		.loc 1 375 5 is_stmt 1 view .LVU197
 375:Middlewares/Third_Party/LwIP/src/core/udp.c ****       MIB2_STATS_INC(mib2.udpindatagrams);
 631              		.loc 1 375 8 is_stmt 0 view .LVU198
 632 00e8 24B3     		cbz	r4, .L52
 376:Middlewares/Third_Party/LwIP/src/core/udp.c **** #if SO_REUSE && SO_REUSE_RXTOALL
 633              		.loc 1 376 42 is_stmt 1 view .LVU199
 402:Middlewares/Third_Party/LwIP/src/core/udp.c ****         /* now the recv function is responsible for freeing p */
 634              		.loc 1 402 7 view .LVU200
 402:Middlewares/Third_Party/LwIP/src/core/udp.c ****         /* now the recv function is responsible for freeing p */
 635              		.loc 1 402 14 is_stmt 0 view .LVU201
 636 00ea A569     		ldr	r5, [r4, #24]
 637              	.LVL48:
 402:Middlewares/Third_Party/LwIP/src/core/udp.c ****         /* now the recv function is responsible for freeing p */
 638              		.loc 1 402 10 view .LVU202
 639 00ec F5B1     		cbz	r5, .L53
 404:Middlewares/Third_Party/LwIP/src/core/udp.c ****       } else {
 640              		.loc 1 404 9 is_stmt 1 view .LVU203
 641 00ee E069     		ldr	r0, [r4, #28]
 642 00f0 CDF800A0 		str	r10, [sp]
 643 00f4 224B     		ldr	r3, .L65+24
 644 00f6 4A46     		mov	r2, r9
 645 00f8 2146     		mov	r1, r4
 646 00fa A847     		blx	r5
 647              	.LVL49:
 648 00fc B2E7     		b	.L37
 649              	.LVL50:
 650              	.L62:
 310:Middlewares/Third_Party/LwIP/src/core/udp.c ****   }
 651              		.loc 1 310 9 is_stmt 0 view .LVU204
 652 00fe 5C46     		mov	r4, fp
 653              	.LVL51:
 654              	.L47:
 314:Middlewares/Third_Party/LwIP/src/core/udp.c ****     for_us = 1;
 655              		.loc 1 314 3 is_stmt 1 view .LVU205
 314:Middlewares/Third_Party/LwIP/src/core/udp.c ****     for_us = 1;
 656              		.loc 1 314 6 is_stmt 0 view .LVU206
 657 0100 002C     		cmp	r4, #0
 658 0102 ECD1     		bne	.L49
 323:Middlewares/Third_Party/LwIP/src/core/udp.c ****       for_us = ip4_addr_cmp(netif_ip4_addr(inp), ip4_current_dest_addr());
 659              		.loc 1 323 5 is_stmt 1 view .LVU207
 324:Middlewares/Third_Party/LwIP/src/core/udp.c ****     }
 660              		.loc 1 324 7 view .LVU208
 324:Middlewares/Third_Party/LwIP/src/core/udp.c ****     }
 661              		.loc 1 324 16 is_stmt 0 view .LVU209
 662 0104 7A68     		ldr	r2, [r7, #4]
 663 0106 184B     		ldr	r3, .L65
 664 0108 5B69     		ldr	r3, [r3, #20]
 665              	.LVL52:
 329:Middlewares/Third_Party/LwIP/src/core/udp.c ****     LWIP_DEBUGF(UDP_DEBUG | LWIP_DBG_TRACE, ("udp_input: calculating checksum\n"));
 666              		.loc 1 329 3 is_stmt 1 view .LVU210
 329:Middlewares/Third_Party/LwIP/src/core/udp.c ****     LWIP_DEBUGF(UDP_DEBUG | LWIP_DBG_TRACE, ("udp_input: calculating checksum\n"));
 667              		.loc 1 329 6 is_stmt 0 view .LVU211
 668 010a 9A42     		cmp	r2, r3
 669 010c E7D0     		beq	.L49
 428:Middlewares/Third_Party/LwIP/src/core/udp.c ****   }
 670              		.loc 1 428 5 is_stmt 1 view .LVU212
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 23


 671 010e 4846     		mov	r0, r9
 672 0110 FFF7FEFF 		bl	pbuf_free
 673              	.LVL53:
 431:Middlewares/Third_Party/LwIP/src/core/udp.c ****   return;
 674              		.loc 1 431 25 view .LVU213
 432:Middlewares/Third_Party/LwIP/src/core/udp.c **** #if CHECKSUM_CHECK_UDP
 675              		.loc 1 432 3 view .LVU214
 676 0114 A6E7     		b	.L37
 677              	.LVL54:
 678              	.L63:
 368:Middlewares/Third_Party/LwIP/src/core/udp.c ****       UDP_STATS_INC(udp.drop);
 679              		.loc 1 368 7 view .LVU215
 368:Middlewares/Third_Party/LwIP/src/core/udp.c ****       UDP_STATS_INC(udp.drop);
 680              		.loc 1 368 7 view .LVU216
 368:Middlewares/Third_Party/LwIP/src/core/udp.c ****       UDP_STATS_INC(udp.drop);
 681              		.loc 1 368 7 discriminator 1 view .LVU217
 368:Middlewares/Third_Party/LwIP/src/core/udp.c ****       UDP_STATS_INC(udp.drop);
 682              		.loc 1 368 7 discriminator 1 view .LVU218
 683 0116 164B     		ldr	r3, .L65+8
 684 0118 4FF4B872 		mov	r2, #368
 685 011c 1949     		ldr	r1, .L65+28
 686 011e 1648     		ldr	r0, .L65+16
 687 0120 FFF7FEFF 		bl	printf
 688              	.LVL55:
 368:Middlewares/Third_Party/LwIP/src/core/udp.c ****       UDP_STATS_INC(udp.drop);
 689              		.loc 1 368 7 discriminator 3 view .LVU219
 368:Middlewares/Third_Party/LwIP/src/core/udp.c ****       UDP_STATS_INC(udp.drop);
 690              		.loc 1 368 7 discriminator 3 view .LVU220
 369:Middlewares/Third_Party/LwIP/src/core/udp.c ****       MIB2_STATS_INC(mib2.udpinerrors);
 691              		.loc 1 369 30 view .LVU221
 370:Middlewares/Third_Party/LwIP/src/core/udp.c ****       pbuf_free(p);
 692              		.loc 1 370 39 view .LVU222
 371:Middlewares/Third_Party/LwIP/src/core/udp.c ****       goto end;
 693              		.loc 1 371 7 view .LVU223
 694 0124 4846     		mov	r0, r9
 695 0126 FFF7FEFF 		bl	pbuf_free
 696              	.LVL56:
 372:Middlewares/Third_Party/LwIP/src/core/udp.c ****     }
 697              		.loc 1 372 7 view .LVU224
 698 012a 9BE7     		b	.L37
 699              	.LVL57:
 700              	.L53:
 407:Middlewares/Third_Party/LwIP/src/core/udp.c ****         goto end;
 701              		.loc 1 407 9 view .LVU225
 702 012c 4846     		mov	r0, r9
 703 012e FFF7FEFF 		bl	pbuf_free
 704              	.LVL58:
 408:Middlewares/Third_Party/LwIP/src/core/udp.c ****       }
 705              		.loc 1 408 9 view .LVU226
 706 0132 97E7     		b	.L37
 707              	.LVL59:
 708              	.L52:
 411:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 709              		.loc 1 411 76 view .LVU227
 416:Middlewares/Third_Party/LwIP/src/core/udp.c ****         /* move payload pointer back to ip header */
 710              		.loc 1 416 7 view .LVU228
 416:Middlewares/Third_Party/LwIP/src/core/udp.c ****         /* move payload pointer back to ip header */
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 24


 711              		.loc 1 416 10 is_stmt 0 view .LVU229
 712 0134 B8F1000F 		cmp	r8, #0
 713 0138 05D1     		bne	.L54
 416:Middlewares/Third_Party/LwIP/src/core/udp.c ****         /* move payload pointer back to ip header */
 714              		.loc 1 416 26 discriminator 1 view .LVU230
 715 013a 0B4B     		ldr	r3, .L65
 716 013c 5B69     		ldr	r3, [r3, #20]
 717 013e 03F0F003 		and	r3, r3, #240
 416:Middlewares/Third_Party/LwIP/src/core/udp.c ****         /* move payload pointer back to ip header */
 718              		.loc 1 416 22 discriminator 1 view .LVU231
 719 0142 E02B     		cmp	r3, #224
 720 0144 03D1     		bne	.L64
 721              	.L54:
 422:Middlewares/Third_Party/LwIP/src/core/udp.c ****       UDP_STATS_INC(udp.drop);
 722              		.loc 1 422 33 is_stmt 1 view .LVU232
 423:Middlewares/Third_Party/LwIP/src/core/udp.c ****       MIB2_STATS_INC(mib2.udpnoports);
 723              		.loc 1 423 30 view .LVU233
 424:Middlewares/Third_Party/LwIP/src/core/udp.c ****       pbuf_free(p);
 724              		.loc 1 424 38 view .LVU234
 425:Middlewares/Third_Party/LwIP/src/core/udp.c ****     }
 725              		.loc 1 425 7 view .LVU235
 726 0146 4846     		mov	r0, r9
 727 0148 FFF7FEFF 		bl	pbuf_free
 728              	.LVL60:
 729 014c 8AE7     		b	.L37
 730              	.L64:
 418:Middlewares/Third_Party/LwIP/src/core/udp.c ****         icmp_port_unreach(ip_current_is_v6(), p);
 731              		.loc 1 418 9 view .LVU236
 418:Middlewares/Third_Party/LwIP/src/core/udp.c ****         icmp_port_unreach(ip_current_is_v6(), p);
 732              		.loc 1 418 38 is_stmt 0 view .LVU237
 733 014e 064B     		ldr	r3, .L65
 734 0150 9989     		ldrh	r1, [r3, #12]
 418:Middlewares/Third_Party/LwIP/src/core/udp.c ****         icmp_port_unreach(ip_current_is_v6(), p);
 735              		.loc 1 418 66 view .LVU238
 736 0152 0831     		adds	r1, r1, #8
 418:Middlewares/Third_Party/LwIP/src/core/udp.c ****         icmp_port_unreach(ip_current_is_v6(), p);
 737              		.loc 1 418 9 view .LVU239
 738 0154 09B2     		sxth	r1, r1
 739 0156 4846     		mov	r0, r9
 740 0158 FFF7FEFF 		bl	pbuf_header_force
 741              	.LVL61:
 419:Middlewares/Third_Party/LwIP/src/core/udp.c ****       }
 742              		.loc 1 419 9 is_stmt 1 view .LVU240
 743 015c 0321     		movs	r1, #3
 744 015e 4846     		mov	r0, r9
 745 0160 FFF7FEFF 		bl	icmp_dest_unreach
 746              	.LVL62:
 747 0164 EFE7     		b	.L54
 748              	.L66:
 749 0166 00BF     		.align	2
 750              	.L65:
 751 0168 00000000 		.word	ip_data
 752 016c 00000000 		.word	udp_pcbs
 753 0170 00000000 		.word	.LC0
 754 0174 00000000 		.word	.LC4
 755 0178 50000000 		.word	.LC2
 756 017c 18000000 		.word	.LC5
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 25


 757 0180 10000000 		.word	ip_data+16
 758 0184 34000000 		.word	.LC6
 759              		.cfi_endproc
 760              	.LFE173:
 762              		.section	.rodata.udp_bind.str1.4,"aMS",%progbits,1
 763              		.align	2
 764              	.LC7:
 765 0000 7564705F 		.ascii	"udp_bind: invalid pcb\000"
 765      62696E64 
 765      3A20696E 
 765      76616C69 
 765      64207063 
 766              		.section	.text.udp_bind,"ax",%progbits
 767              		.align	1
 768              		.global	udp_bind
 769              		.syntax unified
 770              		.thumb
 771              		.thumb_func
 773              	udp_bind:
 774              	.LVL63:
 775              	.LFB178:
 444:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 445:Middlewares/Third_Party/LwIP/src/core/udp.c **** /**
 446:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * @ingroup udp_raw
 447:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * Sends the pbuf p using UDP. The pbuf is not deallocated.
 448:Middlewares/Third_Party/LwIP/src/core/udp.c ****  *
 449:Middlewares/Third_Party/LwIP/src/core/udp.c ****  *
 450:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * @param pcb UDP PCB used to send the data.
 451:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * @param p chain of pbuf's to be sent.
 452:Middlewares/Third_Party/LwIP/src/core/udp.c ****  *
 453:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * The datagram will be sent to the current remote_ip & remote_port
 454:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * stored in pcb. If the pcb is not bound to a port, it will
 455:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * automatically be bound to a random port.
 456:Middlewares/Third_Party/LwIP/src/core/udp.c ****  *
 457:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * @return lwIP error code.
 458:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * - ERR_OK. Successful. No error occurred.
 459:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * - ERR_MEM. Out of memory.
 460:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * - ERR_RTE. Could not find route to destination address.
 461:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * - ERR_VAL. No PCB or PCB is dual-stack
 462:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * - More errors could be returned by lower protocol layers.
 463:Middlewares/Third_Party/LwIP/src/core/udp.c ****  *
 464:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * @see udp_disconnect() udp_sendto()
 465:Middlewares/Third_Party/LwIP/src/core/udp.c ****  */
 466:Middlewares/Third_Party/LwIP/src/core/udp.c **** err_t
 467:Middlewares/Third_Party/LwIP/src/core/udp.c **** udp_send(struct udp_pcb *pcb, struct pbuf *p)
 468:Middlewares/Third_Party/LwIP/src/core/udp.c **** {
 469:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_send: invalid pcb", pcb != NULL, return ERR_ARG);
 470:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_send: invalid pbuf", p != NULL, return ERR_ARG);
 471:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 472:Middlewares/Third_Party/LwIP/src/core/udp.c ****   if (IP_IS_ANY_TYPE_VAL(pcb->remote_ip)) {
 473:Middlewares/Third_Party/LwIP/src/core/udp.c ****     return ERR_VAL;
 474:Middlewares/Third_Party/LwIP/src/core/udp.c ****   }
 475:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 476:Middlewares/Third_Party/LwIP/src/core/udp.c ****   /* send to the packet using remote ip and port stored in the pcb */
 477:Middlewares/Third_Party/LwIP/src/core/udp.c ****   return udp_sendto(pcb, p, &pcb->remote_ip, pcb->remote_port);
 478:Middlewares/Third_Party/LwIP/src/core/udp.c **** }
 479:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 26


 480:Middlewares/Third_Party/LwIP/src/core/udp.c **** #if LWIP_CHECKSUM_ON_COPY && CHECKSUM_GEN_UDP
 481:Middlewares/Third_Party/LwIP/src/core/udp.c **** /** @ingroup udp_raw
 482:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * Same as udp_send() but with checksum
 483:Middlewares/Third_Party/LwIP/src/core/udp.c ****  */
 484:Middlewares/Third_Party/LwIP/src/core/udp.c **** err_t
 485:Middlewares/Third_Party/LwIP/src/core/udp.c **** udp_send_chksum(struct udp_pcb *pcb, struct pbuf *p,
 486:Middlewares/Third_Party/LwIP/src/core/udp.c ****                 u8_t have_chksum, u16_t chksum)
 487:Middlewares/Third_Party/LwIP/src/core/udp.c **** {
 488:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_send_chksum: invalid pcb", pcb != NULL, return ERR_ARG);
 489:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_send_chksum: invalid pbuf", p != NULL, return ERR_ARG);
 490:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 491:Middlewares/Third_Party/LwIP/src/core/udp.c ****   if (IP_IS_ANY_TYPE_VAL(pcb->remote_ip)) {
 492:Middlewares/Third_Party/LwIP/src/core/udp.c ****     return ERR_VAL;
 493:Middlewares/Third_Party/LwIP/src/core/udp.c ****   }
 494:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 495:Middlewares/Third_Party/LwIP/src/core/udp.c ****   /* send to the packet using remote ip and port stored in the pcb */
 496:Middlewares/Third_Party/LwIP/src/core/udp.c ****   return udp_sendto_chksum(pcb, p, &pcb->remote_ip, pcb->remote_port,
 497:Middlewares/Third_Party/LwIP/src/core/udp.c ****                            have_chksum, chksum);
 498:Middlewares/Third_Party/LwIP/src/core/udp.c **** }
 499:Middlewares/Third_Party/LwIP/src/core/udp.c **** #endif /* LWIP_CHECKSUM_ON_COPY && CHECKSUM_GEN_UDP */
 500:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 501:Middlewares/Third_Party/LwIP/src/core/udp.c **** /**
 502:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * @ingroup udp_raw
 503:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * Send data to a specified address using UDP.
 504:Middlewares/Third_Party/LwIP/src/core/udp.c ****  *
 505:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * @param pcb UDP PCB used to send the data.
 506:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * @param p chain of pbuf's to be sent.
 507:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * @param dst_ip Destination IP address.
 508:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * @param dst_port Destination UDP port.
 509:Middlewares/Third_Party/LwIP/src/core/udp.c ****  *
 510:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * dst_ip & dst_port are expected to be in the same byte order as in the pcb.
 511:Middlewares/Third_Party/LwIP/src/core/udp.c ****  *
 512:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * If the PCB already has a remote address association, it will
 513:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * be restored after the data is sent.
 514:Middlewares/Third_Party/LwIP/src/core/udp.c ****  *
 515:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * @return lwIP error code (@see udp_send for possible error codes)
 516:Middlewares/Third_Party/LwIP/src/core/udp.c ****  *
 517:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * @see udp_disconnect() udp_send()
 518:Middlewares/Third_Party/LwIP/src/core/udp.c ****  */
 519:Middlewares/Third_Party/LwIP/src/core/udp.c **** err_t
 520:Middlewares/Third_Party/LwIP/src/core/udp.c **** udp_sendto(struct udp_pcb *pcb, struct pbuf *p,
 521:Middlewares/Third_Party/LwIP/src/core/udp.c ****            const ip_addr_t *dst_ip, u16_t dst_port)
 522:Middlewares/Third_Party/LwIP/src/core/udp.c **** {
 523:Middlewares/Third_Party/LwIP/src/core/udp.c **** #if LWIP_CHECKSUM_ON_COPY && CHECKSUM_GEN_UDP
 524:Middlewares/Third_Party/LwIP/src/core/udp.c ****   return udp_sendto_chksum(pcb, p, dst_ip, dst_port, 0, 0);
 525:Middlewares/Third_Party/LwIP/src/core/udp.c **** }
 526:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 527:Middlewares/Third_Party/LwIP/src/core/udp.c **** /** @ingroup udp_raw
 528:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * Same as udp_sendto(), but with checksum */
 529:Middlewares/Third_Party/LwIP/src/core/udp.c **** err_t
 530:Middlewares/Third_Party/LwIP/src/core/udp.c **** udp_sendto_chksum(struct udp_pcb *pcb, struct pbuf *p, const ip_addr_t *dst_ip,
 531:Middlewares/Third_Party/LwIP/src/core/udp.c ****                   u16_t dst_port, u8_t have_chksum, u16_t chksum)
 532:Middlewares/Third_Party/LwIP/src/core/udp.c **** {
 533:Middlewares/Third_Party/LwIP/src/core/udp.c **** #endif /* LWIP_CHECKSUM_ON_COPY && CHECKSUM_GEN_UDP */
 534:Middlewares/Third_Party/LwIP/src/core/udp.c ****   struct netif *netif;
 535:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 536:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto: invalid pcb", pcb != NULL, return ERR_ARG);
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 27


 537:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto: invalid pbuf", p != NULL, return ERR_ARG);
 538:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto: invalid dst_ip", dst_ip != NULL, return ERR_ARG);
 539:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 540:Middlewares/Third_Party/LwIP/src/core/udp.c ****   if (!IP_ADDR_PCB_VERSION_MATCH(pcb, dst_ip)) {
 541:Middlewares/Third_Party/LwIP/src/core/udp.c ****     return ERR_VAL;
 542:Middlewares/Third_Party/LwIP/src/core/udp.c ****   }
 543:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 544:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_DEBUGF(UDP_DEBUG | LWIP_DBG_TRACE, ("udp_send\n"));
 545:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 546:Middlewares/Third_Party/LwIP/src/core/udp.c ****   if (pcb->netif_idx != NETIF_NO_INDEX) {
 547:Middlewares/Third_Party/LwIP/src/core/udp.c ****     netif = netif_get_by_index(pcb->netif_idx);
 548:Middlewares/Third_Party/LwIP/src/core/udp.c ****   } else {
 549:Middlewares/Third_Party/LwIP/src/core/udp.c **** #if LWIP_MULTICAST_TX_OPTIONS
 550:Middlewares/Third_Party/LwIP/src/core/udp.c ****     netif = NULL;
 551:Middlewares/Third_Party/LwIP/src/core/udp.c ****     if (ip_addr_ismulticast(dst_ip)) {
 552:Middlewares/Third_Party/LwIP/src/core/udp.c ****       /* For IPv6, the interface to use for packets with a multicast destination
 553:Middlewares/Third_Party/LwIP/src/core/udp.c ****        * is specified using an interface index. The same approach may be used for
 554:Middlewares/Third_Party/LwIP/src/core/udp.c ****        * IPv4 as well, in which case it overrides the IPv4 multicast override
 555:Middlewares/Third_Party/LwIP/src/core/udp.c ****        * address below. Here we have to look up the netif by going through the
 556:Middlewares/Third_Party/LwIP/src/core/udp.c ****        * list, but by doing so we skip a route lookup. If the interface index has
 557:Middlewares/Third_Party/LwIP/src/core/udp.c ****        * gone stale, we fall through and do the regular route lookup after all. */
 558:Middlewares/Third_Party/LwIP/src/core/udp.c ****       if (pcb->mcast_ifindex != NETIF_NO_INDEX) {
 559:Middlewares/Third_Party/LwIP/src/core/udp.c ****         netif = netif_get_by_index(pcb->mcast_ifindex);
 560:Middlewares/Third_Party/LwIP/src/core/udp.c ****       }
 561:Middlewares/Third_Party/LwIP/src/core/udp.c **** #if LWIP_IPV4
 562:Middlewares/Third_Party/LwIP/src/core/udp.c ****       else
 563:Middlewares/Third_Party/LwIP/src/core/udp.c **** #if LWIP_IPV6
 564:Middlewares/Third_Party/LwIP/src/core/udp.c ****         if (IP_IS_V4(dst_ip))
 565:Middlewares/Third_Party/LwIP/src/core/udp.c **** #endif /* LWIP_IPV6 */
 566:Middlewares/Third_Party/LwIP/src/core/udp.c ****         {
 567:Middlewares/Third_Party/LwIP/src/core/udp.c ****           /* IPv4 does not use source-based routing by default, so we use an
 568:Middlewares/Third_Party/LwIP/src/core/udp.c ****              administratively selected interface for multicast by default.
 569:Middlewares/Third_Party/LwIP/src/core/udp.c ****              However, this can be overridden by setting an interface address
 570:Middlewares/Third_Party/LwIP/src/core/udp.c ****              in pcb->mcast_ip4 that is used for routing. If this routing lookup
 571:Middlewares/Third_Party/LwIP/src/core/udp.c ****              fails, we try regular routing as though no override was set. */
 572:Middlewares/Third_Party/LwIP/src/core/udp.c ****           if (!ip4_addr_isany_val(pcb->mcast_ip4) &&
 573:Middlewares/Third_Party/LwIP/src/core/udp.c ****               !ip4_addr_cmp(&pcb->mcast_ip4, IP4_ADDR_BROADCAST)) {
 574:Middlewares/Third_Party/LwIP/src/core/udp.c ****             netif = ip4_route_src(ip_2_ip4(&pcb->local_ip), &pcb->mcast_ip4);
 575:Middlewares/Third_Party/LwIP/src/core/udp.c ****           }
 576:Middlewares/Third_Party/LwIP/src/core/udp.c ****         }
 577:Middlewares/Third_Party/LwIP/src/core/udp.c **** #endif /* LWIP_IPV4 */
 578:Middlewares/Third_Party/LwIP/src/core/udp.c ****     }
 579:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 580:Middlewares/Third_Party/LwIP/src/core/udp.c ****     if (netif == NULL)
 581:Middlewares/Third_Party/LwIP/src/core/udp.c **** #endif /* LWIP_MULTICAST_TX_OPTIONS */
 582:Middlewares/Third_Party/LwIP/src/core/udp.c ****     {
 583:Middlewares/Third_Party/LwIP/src/core/udp.c ****       /* find the outgoing network interface for this packet */
 584:Middlewares/Third_Party/LwIP/src/core/udp.c ****       netif = ip_route(&pcb->local_ip, dst_ip);
 585:Middlewares/Third_Party/LwIP/src/core/udp.c ****     }
 586:Middlewares/Third_Party/LwIP/src/core/udp.c ****   }
 587:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 588:Middlewares/Third_Party/LwIP/src/core/udp.c ****   /* no outgoing network interface could be found? */
 589:Middlewares/Third_Party/LwIP/src/core/udp.c ****   if (netif == NULL) {
 590:Middlewares/Third_Party/LwIP/src/core/udp.c ****     LWIP_DEBUGF(UDP_DEBUG | LWIP_DBG_LEVEL_SERIOUS, ("udp_send: No route to "));
 591:Middlewares/Third_Party/LwIP/src/core/udp.c ****     ip_addr_debug_print(UDP_DEBUG | LWIP_DBG_LEVEL_SERIOUS, dst_ip);
 592:Middlewares/Third_Party/LwIP/src/core/udp.c ****     LWIP_DEBUGF(UDP_DEBUG, ("\n"));
 593:Middlewares/Third_Party/LwIP/src/core/udp.c ****     UDP_STATS_INC(udp.rterr);
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 28


 594:Middlewares/Third_Party/LwIP/src/core/udp.c ****     return ERR_RTE;
 595:Middlewares/Third_Party/LwIP/src/core/udp.c ****   }
 596:Middlewares/Third_Party/LwIP/src/core/udp.c **** #if LWIP_CHECKSUM_ON_COPY && CHECKSUM_GEN_UDP
 597:Middlewares/Third_Party/LwIP/src/core/udp.c ****   return udp_sendto_if_chksum(pcb, p, dst_ip, dst_port, netif, have_chksum, chksum);
 598:Middlewares/Third_Party/LwIP/src/core/udp.c **** #else /* LWIP_CHECKSUM_ON_COPY && CHECKSUM_GEN_UDP */
 599:Middlewares/Third_Party/LwIP/src/core/udp.c ****   return udp_sendto_if(pcb, p, dst_ip, dst_port, netif);
 600:Middlewares/Third_Party/LwIP/src/core/udp.c **** #endif /* LWIP_CHECKSUM_ON_COPY && CHECKSUM_GEN_UDP */
 601:Middlewares/Third_Party/LwIP/src/core/udp.c **** }
 602:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 603:Middlewares/Third_Party/LwIP/src/core/udp.c **** /**
 604:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * @ingroup udp_raw
 605:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * Send data to a specified address using UDP.
 606:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * The netif used for sending can be specified.
 607:Middlewares/Third_Party/LwIP/src/core/udp.c ****  *
 608:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * This function exists mainly for DHCP, to be able to send UDP packets
 609:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * on a netif that is still down.
 610:Middlewares/Third_Party/LwIP/src/core/udp.c ****  *
 611:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * @param pcb UDP PCB used to send the data.
 612:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * @param p chain of pbuf's to be sent.
 613:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * @param dst_ip Destination IP address.
 614:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * @param dst_port Destination UDP port.
 615:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * @param netif the netif used for sending.
 616:Middlewares/Third_Party/LwIP/src/core/udp.c ****  *
 617:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * dst_ip & dst_port are expected to be in the same byte order as in the pcb.
 618:Middlewares/Third_Party/LwIP/src/core/udp.c ****  *
 619:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * @return lwIP error code (@see udp_send for possible error codes)
 620:Middlewares/Third_Party/LwIP/src/core/udp.c ****  *
 621:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * @see udp_disconnect() udp_send()
 622:Middlewares/Third_Party/LwIP/src/core/udp.c ****  */
 623:Middlewares/Third_Party/LwIP/src/core/udp.c **** err_t
 624:Middlewares/Third_Party/LwIP/src/core/udp.c **** udp_sendto_if(struct udp_pcb *pcb, struct pbuf *p,
 625:Middlewares/Third_Party/LwIP/src/core/udp.c ****               const ip_addr_t *dst_ip, u16_t dst_port, struct netif *netif)
 626:Middlewares/Third_Party/LwIP/src/core/udp.c **** {
 627:Middlewares/Third_Party/LwIP/src/core/udp.c **** #if LWIP_CHECKSUM_ON_COPY && CHECKSUM_GEN_UDP
 628:Middlewares/Third_Party/LwIP/src/core/udp.c ****   return udp_sendto_if_chksum(pcb, p, dst_ip, dst_port, netif, 0, 0);
 629:Middlewares/Third_Party/LwIP/src/core/udp.c **** }
 630:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 631:Middlewares/Third_Party/LwIP/src/core/udp.c **** /** Same as udp_sendto_if(), but with checksum */
 632:Middlewares/Third_Party/LwIP/src/core/udp.c **** err_t
 633:Middlewares/Third_Party/LwIP/src/core/udp.c **** udp_sendto_if_chksum(struct udp_pcb *pcb, struct pbuf *p, const ip_addr_t *dst_ip,
 634:Middlewares/Third_Party/LwIP/src/core/udp.c ****                      u16_t dst_port, struct netif *netif, u8_t have_chksum,
 635:Middlewares/Third_Party/LwIP/src/core/udp.c ****                      u16_t chksum)
 636:Middlewares/Third_Party/LwIP/src/core/udp.c **** {
 637:Middlewares/Third_Party/LwIP/src/core/udp.c **** #endif /* LWIP_CHECKSUM_ON_COPY && CHECKSUM_GEN_UDP */
 638:Middlewares/Third_Party/LwIP/src/core/udp.c ****   const ip_addr_t *src_ip;
 639:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 640:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if: invalid pcb", pcb != NULL, return ERR_ARG);
 641:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if: invalid pbuf", p != NULL, return ERR_ARG);
 642:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if: invalid dst_ip", dst_ip != NULL, return ERR_ARG);
 643:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if: invalid netif", netif != NULL, return ERR_ARG);
 644:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 645:Middlewares/Third_Party/LwIP/src/core/udp.c ****   if (!IP_ADDR_PCB_VERSION_MATCH(pcb, dst_ip)) {
 646:Middlewares/Third_Party/LwIP/src/core/udp.c ****     return ERR_VAL;
 647:Middlewares/Third_Party/LwIP/src/core/udp.c ****   }
 648:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 649:Middlewares/Third_Party/LwIP/src/core/udp.c ****   /* PCB local address is IP_ANY_ADDR or multicast? */
 650:Middlewares/Third_Party/LwIP/src/core/udp.c **** #if LWIP_IPV6
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 29


 651:Middlewares/Third_Party/LwIP/src/core/udp.c ****   if (IP_IS_V6(dst_ip)) {
 652:Middlewares/Third_Party/LwIP/src/core/udp.c ****     if (ip6_addr_isany(ip_2_ip6(&pcb->local_ip)) ||
 653:Middlewares/Third_Party/LwIP/src/core/udp.c ****         ip6_addr_ismulticast(ip_2_ip6(&pcb->local_ip))) {
 654:Middlewares/Third_Party/LwIP/src/core/udp.c ****       src_ip = ip6_select_source_address(netif, ip_2_ip6(dst_ip));
 655:Middlewares/Third_Party/LwIP/src/core/udp.c ****       if (src_ip == NULL) {
 656:Middlewares/Third_Party/LwIP/src/core/udp.c ****         /* No suitable source address was found. */
 657:Middlewares/Third_Party/LwIP/src/core/udp.c ****         return ERR_RTE;
 658:Middlewares/Third_Party/LwIP/src/core/udp.c ****       }
 659:Middlewares/Third_Party/LwIP/src/core/udp.c ****     } else {
 660:Middlewares/Third_Party/LwIP/src/core/udp.c ****       /* use UDP PCB local IPv6 address as source address, if still valid. */
 661:Middlewares/Third_Party/LwIP/src/core/udp.c ****       if (netif_get_ip6_addr_match(netif, ip_2_ip6(&pcb->local_ip)) < 0) {
 662:Middlewares/Third_Party/LwIP/src/core/udp.c ****         /* Address isn't valid anymore. */
 663:Middlewares/Third_Party/LwIP/src/core/udp.c ****         return ERR_RTE;
 664:Middlewares/Third_Party/LwIP/src/core/udp.c ****       }
 665:Middlewares/Third_Party/LwIP/src/core/udp.c ****       src_ip = &pcb->local_ip;
 666:Middlewares/Third_Party/LwIP/src/core/udp.c ****     }
 667:Middlewares/Third_Party/LwIP/src/core/udp.c ****   }
 668:Middlewares/Third_Party/LwIP/src/core/udp.c **** #endif /* LWIP_IPV6 */
 669:Middlewares/Third_Party/LwIP/src/core/udp.c **** #if LWIP_IPV4 && LWIP_IPV6
 670:Middlewares/Third_Party/LwIP/src/core/udp.c ****   else
 671:Middlewares/Third_Party/LwIP/src/core/udp.c **** #endif /* LWIP_IPV4 && LWIP_IPV6 */
 672:Middlewares/Third_Party/LwIP/src/core/udp.c **** #if LWIP_IPV4
 673:Middlewares/Third_Party/LwIP/src/core/udp.c ****     if (ip4_addr_isany(ip_2_ip4(&pcb->local_ip)) ||
 674:Middlewares/Third_Party/LwIP/src/core/udp.c ****         ip4_addr_ismulticast(ip_2_ip4(&pcb->local_ip))) {
 675:Middlewares/Third_Party/LwIP/src/core/udp.c ****       /* if the local_ip is any or multicast
 676:Middlewares/Third_Party/LwIP/src/core/udp.c ****        * use the outgoing network interface IP address as source address */
 677:Middlewares/Third_Party/LwIP/src/core/udp.c ****       src_ip = netif_ip_addr4(netif);
 678:Middlewares/Third_Party/LwIP/src/core/udp.c ****     } else {
 679:Middlewares/Third_Party/LwIP/src/core/udp.c ****       /* check if UDP PCB local IP address is correct
 680:Middlewares/Third_Party/LwIP/src/core/udp.c ****        * this could be an old address if netif->ip_addr has changed */
 681:Middlewares/Third_Party/LwIP/src/core/udp.c ****       if (!ip4_addr_cmp(ip_2_ip4(&(pcb->local_ip)), netif_ip4_addr(netif))) {
 682:Middlewares/Third_Party/LwIP/src/core/udp.c ****         /* local_ip doesn't match, drop the packet */
 683:Middlewares/Third_Party/LwIP/src/core/udp.c ****         return ERR_RTE;
 684:Middlewares/Third_Party/LwIP/src/core/udp.c ****       }
 685:Middlewares/Third_Party/LwIP/src/core/udp.c ****       /* use UDP PCB local IP address as source address */
 686:Middlewares/Third_Party/LwIP/src/core/udp.c ****       src_ip = &pcb->local_ip;
 687:Middlewares/Third_Party/LwIP/src/core/udp.c ****     }
 688:Middlewares/Third_Party/LwIP/src/core/udp.c **** #endif /* LWIP_IPV4 */
 689:Middlewares/Third_Party/LwIP/src/core/udp.c **** #if LWIP_CHECKSUM_ON_COPY && CHECKSUM_GEN_UDP
 690:Middlewares/Third_Party/LwIP/src/core/udp.c ****   return udp_sendto_if_src_chksum(pcb, p, dst_ip, dst_port, netif, have_chksum, chksum, src_ip);
 691:Middlewares/Third_Party/LwIP/src/core/udp.c **** #else /* LWIP_CHECKSUM_ON_COPY && CHECKSUM_GEN_UDP */
 692:Middlewares/Third_Party/LwIP/src/core/udp.c ****   return udp_sendto_if_src(pcb, p, dst_ip, dst_port, netif, src_ip);
 693:Middlewares/Third_Party/LwIP/src/core/udp.c **** #endif /* LWIP_CHECKSUM_ON_COPY && CHECKSUM_GEN_UDP */
 694:Middlewares/Third_Party/LwIP/src/core/udp.c **** }
 695:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 696:Middlewares/Third_Party/LwIP/src/core/udp.c **** /** @ingroup udp_raw
 697:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * Same as @ref udp_sendto_if, but with source address */
 698:Middlewares/Third_Party/LwIP/src/core/udp.c **** err_t
 699:Middlewares/Third_Party/LwIP/src/core/udp.c **** udp_sendto_if_src(struct udp_pcb *pcb, struct pbuf *p,
 700:Middlewares/Third_Party/LwIP/src/core/udp.c ****                   const ip_addr_t *dst_ip, u16_t dst_port, struct netif *netif, const ip_addr_t *sr
 701:Middlewares/Third_Party/LwIP/src/core/udp.c **** {
 702:Middlewares/Third_Party/LwIP/src/core/udp.c **** #if LWIP_CHECKSUM_ON_COPY && CHECKSUM_GEN_UDP
 703:Middlewares/Third_Party/LwIP/src/core/udp.c ****   return udp_sendto_if_src_chksum(pcb, p, dst_ip, dst_port, netif, 0, 0, src_ip);
 704:Middlewares/Third_Party/LwIP/src/core/udp.c **** }
 705:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 706:Middlewares/Third_Party/LwIP/src/core/udp.c **** /** Same as udp_sendto_if_src(), but with checksum */
 707:Middlewares/Third_Party/LwIP/src/core/udp.c **** err_t
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 30


 708:Middlewares/Third_Party/LwIP/src/core/udp.c **** udp_sendto_if_src_chksum(struct udp_pcb *pcb, struct pbuf *p, const ip_addr_t *dst_ip,
 709:Middlewares/Third_Party/LwIP/src/core/udp.c ****                          u16_t dst_port, struct netif *netif, u8_t have_chksum,
 710:Middlewares/Third_Party/LwIP/src/core/udp.c ****                          u16_t chksum, const ip_addr_t *src_ip)
 711:Middlewares/Third_Party/LwIP/src/core/udp.c **** {
 712:Middlewares/Third_Party/LwIP/src/core/udp.c **** #endif /* LWIP_CHECKSUM_ON_COPY && CHECKSUM_GEN_UDP */
 713:Middlewares/Third_Party/LwIP/src/core/udp.c ****   struct udp_hdr *udphdr;
 714:Middlewares/Third_Party/LwIP/src/core/udp.c ****   err_t err;
 715:Middlewares/Third_Party/LwIP/src/core/udp.c ****   struct pbuf *q; /* q will be sent down the stack */
 716:Middlewares/Third_Party/LwIP/src/core/udp.c ****   u8_t ip_proto;
 717:Middlewares/Third_Party/LwIP/src/core/udp.c ****   u8_t ttl;
 718:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 719:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ASSERT_CORE_LOCKED();
 720:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 721:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if_src: invalid pcb", pcb != NULL, return ERR_ARG);
 722:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if_src: invalid pbuf", p != NULL, return ERR_ARG);
 723:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if_src: invalid dst_ip", dst_ip != NULL, return ERR_ARG);
 724:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if_src: invalid src_ip", src_ip != NULL, return ERR_ARG);
 725:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if_src: invalid netif", netif != NULL, return ERR_ARG);
 726:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 727:Middlewares/Third_Party/LwIP/src/core/udp.c ****   if (!IP_ADDR_PCB_VERSION_MATCH(pcb, src_ip) ||
 728:Middlewares/Third_Party/LwIP/src/core/udp.c ****       !IP_ADDR_PCB_VERSION_MATCH(pcb, dst_ip)) {
 729:Middlewares/Third_Party/LwIP/src/core/udp.c ****     return ERR_VAL;
 730:Middlewares/Third_Party/LwIP/src/core/udp.c ****   }
 731:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 732:Middlewares/Third_Party/LwIP/src/core/udp.c **** #if LWIP_IPV4 && IP_SOF_BROADCAST
 733:Middlewares/Third_Party/LwIP/src/core/udp.c ****   /* broadcast filter? */
 734:Middlewares/Third_Party/LwIP/src/core/udp.c ****   if (!ip_get_option(pcb, SOF_BROADCAST) &&
 735:Middlewares/Third_Party/LwIP/src/core/udp.c **** #if LWIP_IPV6
 736:Middlewares/Third_Party/LwIP/src/core/udp.c ****       IP_IS_V4(dst_ip) &&
 737:Middlewares/Third_Party/LwIP/src/core/udp.c **** #endif /* LWIP_IPV6 */
 738:Middlewares/Third_Party/LwIP/src/core/udp.c ****       ip_addr_isbroadcast(dst_ip, netif)) {
 739:Middlewares/Third_Party/LwIP/src/core/udp.c ****     LWIP_DEBUGF(UDP_DEBUG | LWIP_DBG_LEVEL_SERIOUS,
 740:Middlewares/Third_Party/LwIP/src/core/udp.c ****                 ("udp_sendto_if: SOF_BROADCAST not enabled on pcb %p\n", (void *)pcb));
 741:Middlewares/Third_Party/LwIP/src/core/udp.c ****     return ERR_VAL;
 742:Middlewares/Third_Party/LwIP/src/core/udp.c ****   }
 743:Middlewares/Third_Party/LwIP/src/core/udp.c **** #endif /* LWIP_IPV4 && IP_SOF_BROADCAST */
 744:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 745:Middlewares/Third_Party/LwIP/src/core/udp.c ****   /* if the PCB is not yet bound to a port, bind it here */
 746:Middlewares/Third_Party/LwIP/src/core/udp.c ****   if (pcb->local_port == 0) {
 747:Middlewares/Third_Party/LwIP/src/core/udp.c ****     LWIP_DEBUGF(UDP_DEBUG | LWIP_DBG_TRACE, ("udp_send: not yet bound to a port, binding now\n"));
 748:Middlewares/Third_Party/LwIP/src/core/udp.c ****     err = udp_bind(pcb, &pcb->local_ip, pcb->local_port);
 749:Middlewares/Third_Party/LwIP/src/core/udp.c ****     if (err != ERR_OK) {
 750:Middlewares/Third_Party/LwIP/src/core/udp.c ****       LWIP_DEBUGF(UDP_DEBUG | LWIP_DBG_TRACE | LWIP_DBG_LEVEL_SERIOUS, ("udp_send: forced port bind
 751:Middlewares/Third_Party/LwIP/src/core/udp.c ****       return err;
 752:Middlewares/Third_Party/LwIP/src/core/udp.c ****     }
 753:Middlewares/Third_Party/LwIP/src/core/udp.c ****   }
 754:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 755:Middlewares/Third_Party/LwIP/src/core/udp.c ****   /* packet too large to add a UDP header without causing an overflow? */
 756:Middlewares/Third_Party/LwIP/src/core/udp.c ****   if ((u16_t)(p->tot_len + UDP_HLEN) < p->tot_len) {
 757:Middlewares/Third_Party/LwIP/src/core/udp.c ****     return ERR_MEM;
 758:Middlewares/Third_Party/LwIP/src/core/udp.c ****   }
 759:Middlewares/Third_Party/LwIP/src/core/udp.c ****   /* not enough space to add an UDP header to first pbuf in given p chain? */
 760:Middlewares/Third_Party/LwIP/src/core/udp.c ****   if (pbuf_add_header(p, UDP_HLEN)) {
 761:Middlewares/Third_Party/LwIP/src/core/udp.c ****     /* allocate header in a separate new pbuf */
 762:Middlewares/Third_Party/LwIP/src/core/udp.c ****     q = pbuf_alloc(PBUF_IP, UDP_HLEN, PBUF_RAM);
 763:Middlewares/Third_Party/LwIP/src/core/udp.c ****     /* new header pbuf could not be allocated? */
 764:Middlewares/Third_Party/LwIP/src/core/udp.c ****     if (q == NULL) {
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 31


 765:Middlewares/Third_Party/LwIP/src/core/udp.c ****       LWIP_DEBUGF(UDP_DEBUG | LWIP_DBG_TRACE | LWIP_DBG_LEVEL_SERIOUS, ("udp_send: could not alloca
 766:Middlewares/Third_Party/LwIP/src/core/udp.c ****       return ERR_MEM;
 767:Middlewares/Third_Party/LwIP/src/core/udp.c ****     }
 768:Middlewares/Third_Party/LwIP/src/core/udp.c ****     if (p->tot_len != 0) {
 769:Middlewares/Third_Party/LwIP/src/core/udp.c ****       /* chain header q in front of given pbuf p (only if p contains data) */
 770:Middlewares/Third_Party/LwIP/src/core/udp.c ****       pbuf_chain(q, p);
 771:Middlewares/Third_Party/LwIP/src/core/udp.c ****     }
 772:Middlewares/Third_Party/LwIP/src/core/udp.c ****     /* first pbuf q points to header pbuf */
 773:Middlewares/Third_Party/LwIP/src/core/udp.c ****     LWIP_DEBUGF(UDP_DEBUG,
 774:Middlewares/Third_Party/LwIP/src/core/udp.c ****                 ("udp_send: added header pbuf %p before given pbuf %p\n", (void *)q, (void *)p));
 775:Middlewares/Third_Party/LwIP/src/core/udp.c ****   } else {
 776:Middlewares/Third_Party/LwIP/src/core/udp.c ****     /* adding space for header within p succeeded */
 777:Middlewares/Third_Party/LwIP/src/core/udp.c ****     /* first pbuf q equals given pbuf */
 778:Middlewares/Third_Party/LwIP/src/core/udp.c ****     q = p;
 779:Middlewares/Third_Party/LwIP/src/core/udp.c ****     LWIP_DEBUGF(UDP_DEBUG, ("udp_send: added header in given pbuf %p\n", (void *)p));
 780:Middlewares/Third_Party/LwIP/src/core/udp.c ****   }
 781:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ASSERT("check that first pbuf can hold struct udp_hdr",
 782:Middlewares/Third_Party/LwIP/src/core/udp.c ****               (q->len >= sizeof(struct udp_hdr)));
 783:Middlewares/Third_Party/LwIP/src/core/udp.c ****   /* q now represents the packet to be sent */
 784:Middlewares/Third_Party/LwIP/src/core/udp.c ****   udphdr = (struct udp_hdr *)q->payload;
 785:Middlewares/Third_Party/LwIP/src/core/udp.c ****   udphdr->src = lwip_htons(pcb->local_port);
 786:Middlewares/Third_Party/LwIP/src/core/udp.c ****   udphdr->dest = lwip_htons(dst_port);
 787:Middlewares/Third_Party/LwIP/src/core/udp.c ****   /* in UDP, 0 checksum means 'no checksum' */
 788:Middlewares/Third_Party/LwIP/src/core/udp.c ****   udphdr->chksum = 0x0000;
 789:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 790:Middlewares/Third_Party/LwIP/src/core/udp.c ****   /* Multicast Loop? */
 791:Middlewares/Third_Party/LwIP/src/core/udp.c **** #if LWIP_MULTICAST_TX_OPTIONS
 792:Middlewares/Third_Party/LwIP/src/core/udp.c ****   if (((pcb->flags & UDP_FLAGS_MULTICAST_LOOP) != 0) && ip_addr_ismulticast(dst_ip)) {
 793:Middlewares/Third_Party/LwIP/src/core/udp.c ****     q->flags |= PBUF_FLAG_MCASTLOOP;
 794:Middlewares/Third_Party/LwIP/src/core/udp.c ****   }
 795:Middlewares/Third_Party/LwIP/src/core/udp.c **** #endif /* LWIP_MULTICAST_TX_OPTIONS */
 796:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 797:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_DEBUGF(UDP_DEBUG, ("udp_send: sending datagram of length %"U16_F"\n", q->tot_len));
 798:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 799:Middlewares/Third_Party/LwIP/src/core/udp.c **** #if LWIP_UDPLITE
 800:Middlewares/Third_Party/LwIP/src/core/udp.c ****   /* UDP Lite protocol? */
 801:Middlewares/Third_Party/LwIP/src/core/udp.c ****   if (pcb->flags & UDP_FLAGS_UDPLITE) {
 802:Middlewares/Third_Party/LwIP/src/core/udp.c ****     u16_t chklen, chklen_hdr;
 803:Middlewares/Third_Party/LwIP/src/core/udp.c ****     LWIP_DEBUGF(UDP_DEBUG, ("udp_send: UDP LITE packet length %"U16_F"\n", q->tot_len));
 804:Middlewares/Third_Party/LwIP/src/core/udp.c ****     /* set UDP message length in UDP header */
 805:Middlewares/Third_Party/LwIP/src/core/udp.c ****     chklen_hdr = chklen = pcb->chksum_len_tx;
 806:Middlewares/Third_Party/LwIP/src/core/udp.c ****     if ((chklen < sizeof(struct udp_hdr)) || (chklen > q->tot_len)) {
 807:Middlewares/Third_Party/LwIP/src/core/udp.c ****       if (chklen != 0) {
 808:Middlewares/Third_Party/LwIP/src/core/udp.c ****         LWIP_DEBUGF(UDP_DEBUG, ("udp_send: UDP LITE pcb->chksum_len is illegal: %"U16_F"\n", chklen
 809:Middlewares/Third_Party/LwIP/src/core/udp.c ****       }
 810:Middlewares/Third_Party/LwIP/src/core/udp.c ****       /* For UDP-Lite, checksum length of 0 means checksum
 811:Middlewares/Third_Party/LwIP/src/core/udp.c ****          over the complete packet. (See RFC 3828 chap. 3.1)
 812:Middlewares/Third_Party/LwIP/src/core/udp.c ****          At least the UDP-Lite header must be covered by the
 813:Middlewares/Third_Party/LwIP/src/core/udp.c ****          checksum, therefore, if chksum_len has an illegal
 814:Middlewares/Third_Party/LwIP/src/core/udp.c ****          value, we generate the checksum over the complete
 815:Middlewares/Third_Party/LwIP/src/core/udp.c ****          packet to be safe. */
 816:Middlewares/Third_Party/LwIP/src/core/udp.c ****       chklen_hdr = 0;
 817:Middlewares/Third_Party/LwIP/src/core/udp.c ****       chklen = q->tot_len;
 818:Middlewares/Third_Party/LwIP/src/core/udp.c ****     }
 819:Middlewares/Third_Party/LwIP/src/core/udp.c ****     udphdr->len = lwip_htons(chklen_hdr);
 820:Middlewares/Third_Party/LwIP/src/core/udp.c ****     /* calculate checksum */
 821:Middlewares/Third_Party/LwIP/src/core/udp.c **** #if CHECKSUM_GEN_UDP
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 32


 822:Middlewares/Third_Party/LwIP/src/core/udp.c ****     IF__NETIF_CHECKSUM_ENABLED(netif, NETIF_CHECKSUM_GEN_UDP) {
 823:Middlewares/Third_Party/LwIP/src/core/udp.c **** #if LWIP_CHECKSUM_ON_COPY
 824:Middlewares/Third_Party/LwIP/src/core/udp.c ****       if (have_chksum) {
 825:Middlewares/Third_Party/LwIP/src/core/udp.c ****         chklen = UDP_HLEN;
 826:Middlewares/Third_Party/LwIP/src/core/udp.c ****       }
 827:Middlewares/Third_Party/LwIP/src/core/udp.c **** #endif /* LWIP_CHECKSUM_ON_COPY */
 828:Middlewares/Third_Party/LwIP/src/core/udp.c ****       udphdr->chksum = ip_chksum_pseudo_partial(q, IP_PROTO_UDPLITE,
 829:Middlewares/Third_Party/LwIP/src/core/udp.c ****                        q->tot_len, chklen, src_ip, dst_ip);
 830:Middlewares/Third_Party/LwIP/src/core/udp.c **** #if LWIP_CHECKSUM_ON_COPY
 831:Middlewares/Third_Party/LwIP/src/core/udp.c ****       if (have_chksum) {
 832:Middlewares/Third_Party/LwIP/src/core/udp.c ****         u32_t acc;
 833:Middlewares/Third_Party/LwIP/src/core/udp.c ****         acc = udphdr->chksum + (u16_t)~(chksum);
 834:Middlewares/Third_Party/LwIP/src/core/udp.c ****         udphdr->chksum = FOLD_U32T(acc);
 835:Middlewares/Third_Party/LwIP/src/core/udp.c ****       }
 836:Middlewares/Third_Party/LwIP/src/core/udp.c **** #endif /* LWIP_CHECKSUM_ON_COPY */
 837:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 838:Middlewares/Third_Party/LwIP/src/core/udp.c ****       /* chksum zero must become 0xffff, as zero means 'no checksum' */
 839:Middlewares/Third_Party/LwIP/src/core/udp.c ****       if (udphdr->chksum == 0x0000) {
 840:Middlewares/Third_Party/LwIP/src/core/udp.c ****         udphdr->chksum = 0xffff;
 841:Middlewares/Third_Party/LwIP/src/core/udp.c ****       }
 842:Middlewares/Third_Party/LwIP/src/core/udp.c ****     }
 843:Middlewares/Third_Party/LwIP/src/core/udp.c **** #endif /* CHECKSUM_GEN_UDP */
 844:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 845:Middlewares/Third_Party/LwIP/src/core/udp.c ****     ip_proto = IP_PROTO_UDPLITE;
 846:Middlewares/Third_Party/LwIP/src/core/udp.c ****   } else
 847:Middlewares/Third_Party/LwIP/src/core/udp.c **** #endif /* LWIP_UDPLITE */
 848:Middlewares/Third_Party/LwIP/src/core/udp.c ****   {      /* UDP */
 849:Middlewares/Third_Party/LwIP/src/core/udp.c ****     LWIP_DEBUGF(UDP_DEBUG, ("udp_send: UDP packet length %"U16_F"\n", q->tot_len));
 850:Middlewares/Third_Party/LwIP/src/core/udp.c ****     udphdr->len = lwip_htons(q->tot_len);
 851:Middlewares/Third_Party/LwIP/src/core/udp.c ****     /* calculate checksum */
 852:Middlewares/Third_Party/LwIP/src/core/udp.c **** #if CHECKSUM_GEN_UDP
 853:Middlewares/Third_Party/LwIP/src/core/udp.c ****     IF__NETIF_CHECKSUM_ENABLED(netif, NETIF_CHECKSUM_GEN_UDP) {
 854:Middlewares/Third_Party/LwIP/src/core/udp.c ****       /* Checksum is mandatory over IPv6. */
 855:Middlewares/Third_Party/LwIP/src/core/udp.c ****       if (IP_IS_V6(dst_ip) || (pcb->flags & UDP_FLAGS_NOCHKSUM) == 0) {
 856:Middlewares/Third_Party/LwIP/src/core/udp.c ****         u16_t udpchksum;
 857:Middlewares/Third_Party/LwIP/src/core/udp.c **** #if LWIP_CHECKSUM_ON_COPY
 858:Middlewares/Third_Party/LwIP/src/core/udp.c ****         if (have_chksum) {
 859:Middlewares/Third_Party/LwIP/src/core/udp.c ****           u32_t acc;
 860:Middlewares/Third_Party/LwIP/src/core/udp.c ****           udpchksum = ip_chksum_pseudo_partial(q, IP_PROTO_UDP,
 861:Middlewares/Third_Party/LwIP/src/core/udp.c ****                                                q->tot_len, UDP_HLEN, src_ip, dst_ip);
 862:Middlewares/Third_Party/LwIP/src/core/udp.c ****           acc = udpchksum + (u16_t)~(chksum);
 863:Middlewares/Third_Party/LwIP/src/core/udp.c ****           udpchksum = FOLD_U32T(acc);
 864:Middlewares/Third_Party/LwIP/src/core/udp.c ****         } else
 865:Middlewares/Third_Party/LwIP/src/core/udp.c **** #endif /* LWIP_CHECKSUM_ON_COPY */
 866:Middlewares/Third_Party/LwIP/src/core/udp.c ****         {
 867:Middlewares/Third_Party/LwIP/src/core/udp.c ****           udpchksum = ip_chksum_pseudo(q, IP_PROTO_UDP, q->tot_len,
 868:Middlewares/Third_Party/LwIP/src/core/udp.c ****                                        src_ip, dst_ip);
 869:Middlewares/Third_Party/LwIP/src/core/udp.c ****         }
 870:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 871:Middlewares/Third_Party/LwIP/src/core/udp.c ****         /* chksum zero must become 0xffff, as zero means 'no checksum' */
 872:Middlewares/Third_Party/LwIP/src/core/udp.c ****         if (udpchksum == 0x0000) {
 873:Middlewares/Third_Party/LwIP/src/core/udp.c ****           udpchksum = 0xffff;
 874:Middlewares/Third_Party/LwIP/src/core/udp.c ****         }
 875:Middlewares/Third_Party/LwIP/src/core/udp.c ****         udphdr->chksum = udpchksum;
 876:Middlewares/Third_Party/LwIP/src/core/udp.c ****       }
 877:Middlewares/Third_Party/LwIP/src/core/udp.c ****     }
 878:Middlewares/Third_Party/LwIP/src/core/udp.c **** #endif /* CHECKSUM_GEN_UDP */
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 33


 879:Middlewares/Third_Party/LwIP/src/core/udp.c ****     ip_proto = IP_PROTO_UDP;
 880:Middlewares/Third_Party/LwIP/src/core/udp.c ****   }
 881:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 882:Middlewares/Third_Party/LwIP/src/core/udp.c ****   /* Determine TTL to use */
 883:Middlewares/Third_Party/LwIP/src/core/udp.c **** #if LWIP_MULTICAST_TX_OPTIONS
 884:Middlewares/Third_Party/LwIP/src/core/udp.c ****   ttl = (ip_addr_ismulticast(dst_ip) ? udp_get_multicast_ttl(pcb) : pcb->ttl);
 885:Middlewares/Third_Party/LwIP/src/core/udp.c **** #else /* LWIP_MULTICAST_TX_OPTIONS */
 886:Middlewares/Third_Party/LwIP/src/core/udp.c ****   ttl = pcb->ttl;
 887:Middlewares/Third_Party/LwIP/src/core/udp.c **** #endif /* LWIP_MULTICAST_TX_OPTIONS */
 888:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 889:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_DEBUGF(UDP_DEBUG, ("udp_send: UDP checksum 0x%04"X16_F"\n", udphdr->chksum));
 890:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_DEBUGF(UDP_DEBUG, ("udp_send: ip_output_if (,,,,0x%02"X16_F",)\n", (u16_t)ip_proto));
 891:Middlewares/Third_Party/LwIP/src/core/udp.c ****   /* output to IP */
 892:Middlewares/Third_Party/LwIP/src/core/udp.c ****   NETIF_SET_HINTS(netif, &(pcb->netif_hints));
 893:Middlewares/Third_Party/LwIP/src/core/udp.c ****   err = ip_output_if_src(q, src_ip, dst_ip, ttl, pcb->tos, ip_proto, netif);
 894:Middlewares/Third_Party/LwIP/src/core/udp.c ****   NETIF_RESET_HINTS(netif);
 895:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 896:Middlewares/Third_Party/LwIP/src/core/udp.c ****   /* @todo: must this be increased even if error occurred? */
 897:Middlewares/Third_Party/LwIP/src/core/udp.c ****   MIB2_STATS_INC(mib2.udpoutdatagrams);
 898:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 899:Middlewares/Third_Party/LwIP/src/core/udp.c ****   /* did we chain a separate header pbuf earlier? */
 900:Middlewares/Third_Party/LwIP/src/core/udp.c ****   if (q != p) {
 901:Middlewares/Third_Party/LwIP/src/core/udp.c ****     /* free the header pbuf */
 902:Middlewares/Third_Party/LwIP/src/core/udp.c ****     pbuf_free(q);
 903:Middlewares/Third_Party/LwIP/src/core/udp.c ****     q = NULL;
 904:Middlewares/Third_Party/LwIP/src/core/udp.c ****     /* p is still referenced by the caller, and will live on */
 905:Middlewares/Third_Party/LwIP/src/core/udp.c ****   }
 906:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 907:Middlewares/Third_Party/LwIP/src/core/udp.c ****   UDP_STATS_INC(udp.xmit);
 908:Middlewares/Third_Party/LwIP/src/core/udp.c ****   return err;
 909:Middlewares/Third_Party/LwIP/src/core/udp.c **** }
 910:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 911:Middlewares/Third_Party/LwIP/src/core/udp.c **** /**
 912:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * @ingroup udp_raw
 913:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * Bind an UDP PCB.
 914:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * 
 915:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * @param pcb UDP PCB to be bound with a local address ipaddr and port.
 916:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * @param ipaddr local IP address to bind with. Use IP_ANY_TYPE to
 917:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * bind to all local interfaces.
 918:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * @param port local UDP port to bind with. Use 0 to automatically bind
 919:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * to a random port between UDP_LOCAL_PORT_RANGE_START and
 920:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * UDP_LOCAL_PORT_RANGE_END.
 921:Middlewares/Third_Party/LwIP/src/core/udp.c ****  *
 922:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * ipaddr & port are expected to be in the same byte order as in the pcb.
 923:Middlewares/Third_Party/LwIP/src/core/udp.c ****  *
 924:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * @return lwIP error code.
 925:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * - ERR_OK. Successful. No error occurred.
 926:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * - ERR_USE. The specified ipaddr and port are already bound to by
 927:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * another UDP PCB.
 928:Middlewares/Third_Party/LwIP/src/core/udp.c ****  *
 929:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * @see udp_disconnect()
 930:Middlewares/Third_Party/LwIP/src/core/udp.c ****  */
 931:Middlewares/Third_Party/LwIP/src/core/udp.c **** err_t
 932:Middlewares/Third_Party/LwIP/src/core/udp.c **** udp_bind(struct udp_pcb *pcb, const ip_addr_t *ipaddr, u16_t port)
 933:Middlewares/Third_Party/LwIP/src/core/udp.c **** {
 776              		.loc 1 933 1 view -0
 777              		.cfi_startproc
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 34


 778              		@ args = 0, pretend = 0, frame = 0
 779              		@ frame_needed = 0, uses_anonymous_args = 0
 780              		.loc 1 933 1 is_stmt 0 view .LVU242
 781 0000 70B5     		push	{r4, r5, r6, lr}
 782              	.LCFI6:
 783              		.cfi_def_cfa_offset 16
 784              		.cfi_offset 4, -16
 785              		.cfi_offset 5, -12
 786              		.cfi_offset 6, -8
 787              		.cfi_offset 14, -4
 788 0002 0446     		mov	r4, r0
 934:Middlewares/Third_Party/LwIP/src/core/udp.c ****   struct udp_pcb *ipcb;
 789              		.loc 1 934 3 is_stmt 1 view .LVU243
 935:Middlewares/Third_Party/LwIP/src/core/udp.c ****   u8_t rebind;
 790              		.loc 1 935 3 view .LVU244
 936:Middlewares/Third_Party/LwIP/src/core/udp.c **** #if LWIP_IPV6 && LWIP_IPV6_SCOPES
 937:Middlewares/Third_Party/LwIP/src/core/udp.c ****   ip_addr_t zoned_ipaddr;
 938:Middlewares/Third_Party/LwIP/src/core/udp.c **** #endif /* LWIP_IPV6 && LWIP_IPV6_SCOPES */
 939:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 940:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ASSERT_CORE_LOCKED();
 791              		.loc 1 940 28 view .LVU245
 941:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 942:Middlewares/Third_Party/LwIP/src/core/udp.c **** #if LWIP_IPV4
 943:Middlewares/Third_Party/LwIP/src/core/udp.c ****   /* Don't propagate NULL pointer (IPv4 ANY) to subsequent functions */
 944:Middlewares/Third_Party/LwIP/src/core/udp.c ****   if (ipaddr == NULL) {
 792              		.loc 1 944 3 view .LVU246
 793              		.loc 1 944 6 is_stmt 0 view .LVU247
 794 0004 0D46     		mov	r5, r1
 795 0006 21B1     		cbz	r1, .L89
 796              	.L68:
 797              	.LVL64:
 945:Middlewares/Third_Party/LwIP/src/core/udp.c ****     ipaddr = IP4_ADDR_ANY;
 946:Middlewares/Third_Party/LwIP/src/core/udp.c ****   }
 947:Middlewares/Third_Party/LwIP/src/core/udp.c **** #else /* LWIP_IPV4 */
 948:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_bind: invalid ipaddr", ipaddr != NULL, return ERR_ARG);
 949:Middlewares/Third_Party/LwIP/src/core/udp.c **** #endif /* LWIP_IPV4 */
 950:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 951:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_bind: invalid pcb", pcb != NULL, return ERR_ARG);
 798              		.loc 1 951 3 is_stmt 1 view .LVU248
 799              		.loc 1 951 3 view .LVU249
 800 0008 2CB1     		cbz	r4, .L90
 801              		.loc 1 951 3 discriminator 2 view .LVU250
 952:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 953:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_DEBUGF(UDP_DEBUG | LWIP_DBG_TRACE, ("udp_bind(ipaddr = "));
 802              		.loc 1 953 66 view .LVU251
 954:Middlewares/Third_Party/LwIP/src/core/udp.c ****   ip_addr_debug_print(UDP_DEBUG | LWIP_DBG_TRACE, ipaddr);
 803              		.loc 1 954 58 view .LVU252
 955:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_DEBUGF(UDP_DEBUG | LWIP_DBG_TRACE, (", port = %"U16_F")\n", port));
 804              		.loc 1 955 74 view .LVU253
 956:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 957:Middlewares/Third_Party/LwIP/src/core/udp.c ****   rebind = 0;
 805              		.loc 1 957 3 view .LVU254
 806              	.LVL65:
 958:Middlewares/Third_Party/LwIP/src/core/udp.c ****   /* Check for double bind and rebind of the same pcb */
 959:Middlewares/Third_Party/LwIP/src/core/udp.c ****   for (ipcb = udp_pcbs; ipcb != NULL; ipcb = ipcb->next) {
 807              		.loc 1 959 3 view .LVU255
 808              		.loc 1 959 13 is_stmt 0 view .LVU256
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 35


 809 000a 254B     		ldr	r3, .L94
 810 000c 1B68     		ldr	r3, [r3]
 811              	.LVL66:
 812              		.loc 1 959 13 view .LVU257
 813 000e 1946     		mov	r1, r3
 814              		.loc 1 959 3 view .LVU258
 815 0010 0CE0     		b	.L71
 816              	.LVL67:
 817              	.L89:
 945:Middlewares/Third_Party/LwIP/src/core/udp.c ****   }
 818              		.loc 1 945 12 view .LVU259
 819 0012 244D     		ldr	r5, .L94+4
 820 0014 F8E7     		b	.L68
 821              	.LVL68:
 822              	.L90:
 951:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 823              		.loc 1 951 3 is_stmt 1 discriminator 1 view .LVU260
 951:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 824              		.loc 1 951 3 discriminator 1 view .LVU261
 825 0016 244B     		ldr	r3, .L94+8
 826 0018 40F2B732 		movw	r2, #951
 827              	.LVL69:
 951:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 828              		.loc 1 951 3 is_stmt 0 discriminator 1 view .LVU262
 829 001c 2349     		ldr	r1, .L94+12
 830 001e 2448     		ldr	r0, .L94+16
 831              	.LVL70:
 951:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 832              		.loc 1 951 3 discriminator 1 view .LVU263
 833 0020 FFF7FEFF 		bl	printf
 834              	.LVL71:
 951:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 835              		.loc 1 951 3 is_stmt 1 discriminator 1 view .LVU264
 951:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 836              		.loc 1 951 3 discriminator 1 view .LVU265
 837 0024 6FF00F00 		mvn	r0, #15
 951:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 838              		.loc 1 951 3 is_stmt 0 view .LVU266
 839 0028 15E0     		b	.L70
 840              	.LVL72:
 841              	.L92:
 842              		.loc 1 959 44 is_stmt 1 discriminator 2 view .LVU267
 843 002a C968     		ldr	r1, [r1, #12]
 844              	.LVL73:
 845              	.L71:
 846              		.loc 1 959 30 discriminator 1 view .LVU268
 847 002c 19B1     		cbz	r1, .L91
 960:Middlewares/Third_Party/LwIP/src/core/udp.c ****     /* is this UDP PCB already on active list? */
 961:Middlewares/Third_Party/LwIP/src/core/udp.c ****     if (pcb == ipcb) {
 848              		.loc 1 961 5 view .LVU269
 849              		.loc 1 961 8 is_stmt 0 view .LVU270
 850 002e A142     		cmp	r1, r4
 851 0030 FBD1     		bne	.L92
 962:Middlewares/Third_Party/LwIP/src/core/udp.c ****       rebind = 1;
 852              		.loc 1 962 14 view .LVU271
 853 0032 0126     		movs	r6, #1
 854 0034 00E0     		b	.L72
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 36


 855              	.L91:
 957:Middlewares/Third_Party/LwIP/src/core/udp.c ****   /* Check for double bind and rebind of the same pcb */
 856              		.loc 1 957 10 view .LVU272
 857 0036 0026     		movs	r6, #0
 858              	.L72:
 859              	.LVL74:
 963:Middlewares/Third_Party/LwIP/src/core/udp.c ****       break;
 964:Middlewares/Third_Party/LwIP/src/core/udp.c ****     }
 965:Middlewares/Third_Party/LwIP/src/core/udp.c ****   }
 966:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 967:Middlewares/Third_Party/LwIP/src/core/udp.c **** #if LWIP_IPV6 && LWIP_IPV6_SCOPES
 968:Middlewares/Third_Party/LwIP/src/core/udp.c ****   /* If the given IP address should have a zone but doesn't, assign one now.
 969:Middlewares/Third_Party/LwIP/src/core/udp.c ****    * This is legacy support: scope-aware callers should always provide properly
 970:Middlewares/Third_Party/LwIP/src/core/udp.c ****    * zoned source addresses. Do the zone selection before the address-in-use
 971:Middlewares/Third_Party/LwIP/src/core/udp.c ****    * check below; as such we have to make a temporary copy of the address. */
 972:Middlewares/Third_Party/LwIP/src/core/udp.c ****   if (IP_IS_V6(ipaddr) && ip6_addr_lacks_zone(ip_2_ip6(ipaddr), IP6_UNKNOWN)) {
 973:Middlewares/Third_Party/LwIP/src/core/udp.c ****     ip_addr_copy(zoned_ipaddr, *ipaddr);
 974:Middlewares/Third_Party/LwIP/src/core/udp.c ****     ip6_addr_select_zone(ip_2_ip6(&zoned_ipaddr), ip_2_ip6(&zoned_ipaddr));
 975:Middlewares/Third_Party/LwIP/src/core/udp.c ****     ipaddr = &zoned_ipaddr;
 976:Middlewares/Third_Party/LwIP/src/core/udp.c ****   }
 977:Middlewares/Third_Party/LwIP/src/core/udp.c **** #endif /* LWIP_IPV6 && LWIP_IPV6_SCOPES */
 978:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 979:Middlewares/Third_Party/LwIP/src/core/udp.c ****   /* no port specified? */
 980:Middlewares/Third_Party/LwIP/src/core/udp.c ****   if (port == 0) {
 860              		.loc 1 980 3 is_stmt 1 view .LVU273
 861              		.loc 1 980 6 is_stmt 0 view .LVU274
 862 0038 7AB9     		cbnz	r2, .L74
 981:Middlewares/Third_Party/LwIP/src/core/udp.c ****     port = udp_new_port();
 863              		.loc 1 981 5 is_stmt 1 view .LVU275
 864              		.loc 1 981 12 is_stmt 0 view .LVU276
 865 003a FFF7FEFF 		bl	udp_new_port
 866              	.LVL75:
 982:Middlewares/Third_Party/LwIP/src/core/udp.c ****     if (port == 0) {
 867              		.loc 1 982 5 is_stmt 1 view .LVU277
 868              		.loc 1 982 8 is_stmt 0 view .LVU278
 869 003e 0246     		mov	r2, r0
 870 0040 F8B1     		cbz	r0, .L93
 871              	.LVL76:
 872              	.L75:
 983:Middlewares/Third_Party/LwIP/src/core/udp.c ****       /* no more ports available in local range */
 984:Middlewares/Third_Party/LwIP/src/core/udp.c ****       LWIP_DEBUGF(UDP_DEBUG, ("udp_bind: out of free UDP ports\n"));
 985:Middlewares/Third_Party/LwIP/src/core/udp.c ****       return ERR_USE;
 986:Middlewares/Third_Party/LwIP/src/core/udp.c ****     }
 987:Middlewares/Third_Party/LwIP/src/core/udp.c ****   } else {
 988:Middlewares/Third_Party/LwIP/src/core/udp.c ****     for (ipcb = udp_pcbs; ipcb != NULL; ipcb = ipcb->next) {
 989:Middlewares/Third_Party/LwIP/src/core/udp.c ****       if (pcb != ipcb) {
 990:Middlewares/Third_Party/LwIP/src/core/udp.c ****         /* By default, we don't allow to bind to a port that any other udp
 991:Middlewares/Third_Party/LwIP/src/core/udp.c ****            PCB is already bound to, unless *all* PCBs with that port have tha
 992:Middlewares/Third_Party/LwIP/src/core/udp.c ****            REUSEADDR flag set. */
 993:Middlewares/Third_Party/LwIP/src/core/udp.c **** #if SO_REUSE
 994:Middlewares/Third_Party/LwIP/src/core/udp.c ****         if (!ip_get_option(pcb, SOF_REUSEADDR) ||
 995:Middlewares/Third_Party/LwIP/src/core/udp.c ****             !ip_get_option(ipcb, SOF_REUSEADDR))
 996:Middlewares/Third_Party/LwIP/src/core/udp.c **** #endif /* SO_REUSE */
 997:Middlewares/Third_Party/LwIP/src/core/udp.c ****         {
 998:Middlewares/Third_Party/LwIP/src/core/udp.c ****           /* port matches that of PCB in list and REUSEADDR not set -> reject */
 999:Middlewares/Third_Party/LwIP/src/core/udp.c ****           if ((ipcb->local_port == port) &&
1000:Middlewares/Third_Party/LwIP/src/core/udp.c ****               /* IP address matches or any IP used? */
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 37


1001:Middlewares/Third_Party/LwIP/src/core/udp.c ****               (ip_addr_cmp(&ipcb->local_ip, ipaddr) || ip_addr_isany(ipaddr) ||
1002:Middlewares/Third_Party/LwIP/src/core/udp.c ****               ip_addr_isany(&ipcb->local_ip))) {
1003:Middlewares/Third_Party/LwIP/src/core/udp.c ****             /* other PCB already binds to this local IP and port */
1004:Middlewares/Third_Party/LwIP/src/core/udp.c ****             LWIP_DEBUGF(UDP_DEBUG,
1005:Middlewares/Third_Party/LwIP/src/core/udp.c ****                         ("udp_bind: local port %"U16_F" already bound by another pcb\n", port));
1006:Middlewares/Third_Party/LwIP/src/core/udp.c ****             return ERR_USE;
1007:Middlewares/Third_Party/LwIP/src/core/udp.c ****           }
1008:Middlewares/Third_Party/LwIP/src/core/udp.c ****         }
1009:Middlewares/Third_Party/LwIP/src/core/udp.c ****       }
1010:Middlewares/Third_Party/LwIP/src/core/udp.c ****     }
1011:Middlewares/Third_Party/LwIP/src/core/udp.c ****   }
1012:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
1013:Middlewares/Third_Party/LwIP/src/core/udp.c ****   ip_addr_set_ipaddr(&pcb->local_ip, ipaddr);
 873              		.loc 1 1013 3 is_stmt 1 view .LVU279
 874 0042 E5B1     		cbz	r5, .L86
 875              		.loc 1 1013 3 is_stmt 0 discriminator 1 view .LVU280
 876 0044 2B68     		ldr	r3, [r5]
 877              	.L78:
 878              		.loc 1 1013 3 discriminator 4 view .LVU281
 879 0046 2360     		str	r3, [r4]
1014:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
1015:Middlewares/Third_Party/LwIP/src/core/udp.c ****   pcb->local_port = port;
 880              		.loc 1 1015 3 is_stmt 1 view .LVU282
 881              		.loc 1 1015 19 is_stmt 0 view .LVU283
 882 0048 6282     		strh	r2, [r4, #18]	@ movhi
1016:Middlewares/Third_Party/LwIP/src/core/udp.c ****   mib2_udp_bind(pcb);
 883              		.loc 1 1016 21 is_stmt 1 view .LVU284
1017:Middlewares/Third_Party/LwIP/src/core/udp.c ****   /* pcb not active yet? */
1018:Middlewares/Third_Party/LwIP/src/core/udp.c ****   if (rebind == 0) {
 884              		.loc 1 1018 3 view .LVU285
 885              		.loc 1 1018 6 is_stmt 0 view .LVU286
 886 004a 36BB     		cbnz	r6, .L87
1019:Middlewares/Third_Party/LwIP/src/core/udp.c ****     /* place the PCB on the active list if not already there */
1020:Middlewares/Third_Party/LwIP/src/core/udp.c ****     pcb->next = udp_pcbs;
 887              		.loc 1 1020 5 is_stmt 1 view .LVU287
 888              		.loc 1 1020 15 is_stmt 0 view .LVU288
 889 004c 144B     		ldr	r3, .L94
 890 004e 1A68     		ldr	r2, [r3]
 891              	.LVL77:
 892              		.loc 1 1020 15 view .LVU289
 893 0050 E260     		str	r2, [r4, #12]
1021:Middlewares/Third_Party/LwIP/src/core/udp.c ****     udp_pcbs = pcb;
 894              		.loc 1 1021 5 is_stmt 1 view .LVU290
 895              		.loc 1 1021 14 is_stmt 0 view .LVU291
 896 0052 1C60     		str	r4, [r3]
1022:Middlewares/Third_Party/LwIP/src/core/udp.c ****   }
1023:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_DEBUGF(UDP_DEBUG | LWIP_DBG_TRACE | LWIP_DBG_STATE, ("udp_bind: bound to "));
1024:Middlewares/Third_Party/LwIP/src/core/udp.c ****   ip_addr_debug_print_val(UDP_DEBUG | LWIP_DBG_TRACE | LWIP_DBG_STATE, pcb->local_ip);
1025:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_DEBUGF(UDP_DEBUG | LWIP_DBG_TRACE | LWIP_DBG_STATE, (", port %"U16_F")\n", pcb->local_port))
1026:Middlewares/Third_Party/LwIP/src/core/udp.c ****   return ERR_OK;
 897              		.loc 1 1026 10 view .LVU292
 898 0054 0020     		movs	r0, #0
 899              	.LVL78:
 900              	.L70:
1027:Middlewares/Third_Party/LwIP/src/core/udp.c **** }
 901              		.loc 1 1027 1 view .LVU293
 902 0056 70BD     		pop	{r4, r5, r6, pc}
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 38


 903              	.LVL79:
 904              	.L76:
 988:Middlewares/Third_Party/LwIP/src/core/udp.c ****       if (pcb != ipcb) {
 905              		.loc 1 988 46 is_stmt 1 discriminator 2 view .LVU294
 906 0058 DB68     		ldr	r3, [r3, #12]
 907              	.LVL80:
 908              	.L74:
 988:Middlewares/Third_Party/LwIP/src/core/udp.c ****       if (pcb != ipcb) {
 909              		.loc 1 988 32 discriminator 1 view .LVU295
 910 005a 002B     		cmp	r3, #0
 911 005c F1D0     		beq	.L75
 989:Middlewares/Third_Party/LwIP/src/core/udp.c ****         /* By default, we don't allow to bind to a port that any other udp
 912              		.loc 1 989 7 view .LVU296
 989:Middlewares/Third_Party/LwIP/src/core/udp.c ****         /* By default, we don't allow to bind to a port that any other udp
 913              		.loc 1 989 10 is_stmt 0 view .LVU297
 914 005e A342     		cmp	r3, r4
 915 0060 FAD0     		beq	.L76
 999:Middlewares/Third_Party/LwIP/src/core/udp.c ****               /* IP address matches or any IP used? */
 916              		.loc 1 999 11 is_stmt 1 view .LVU298
 999:Middlewares/Third_Party/LwIP/src/core/udp.c ****               /* IP address matches or any IP used? */
 917              		.loc 1 999 20 is_stmt 0 view .LVU299
 918 0062 598A     		ldrh	r1, [r3, #18]
 999:Middlewares/Third_Party/LwIP/src/core/udp.c ****               /* IP address matches or any IP used? */
 919              		.loc 1 999 14 view .LVU300
 920 0064 9142     		cmp	r1, r2
 921 0066 F7D1     		bne	.L76
1001:Middlewares/Third_Party/LwIP/src/core/udp.c ****               ip_addr_isany(&ipcb->local_ip))) {
 922              		.loc 1 1001 16 view .LVU301
 923 0068 1868     		ldr	r0, [r3]
 924 006a 2968     		ldr	r1, [r5]
 999:Middlewares/Third_Party/LwIP/src/core/udp.c ****               /* IP address matches or any IP used? */
 925              		.loc 1 999 42 discriminator 1 view .LVU302
 926 006c 8842     		cmp	r0, r1
 927 006e 0BD0     		beq	.L82
1001:Middlewares/Third_Party/LwIP/src/core/udp.c ****               ip_addr_isany(&ipcb->local_ip))) {
 928              		.loc 1 1001 56 discriminator 1 view .LVU303
 929 0070 69B1     		cbz	r1, .L83
1001:Middlewares/Third_Party/LwIP/src/core/udp.c ****               ip_addr_isany(&ipcb->local_ip))) {
 930              		.loc 1 1001 78 discriminator 2 view .LVU304
 931 0072 7BB1     		cbz	r3, .L84
1002:Middlewares/Third_Party/LwIP/src/core/udp.c ****             /* other PCB already binds to this local IP and port */
 932              		.loc 1 1002 15 view .LVU305
 933 0074 0028     		cmp	r0, #0
 934 0076 EFD1     		bne	.L76
1006:Middlewares/Third_Party/LwIP/src/core/udp.c ****           }
 935              		.loc 1 1006 20 view .LVU306
 936 0078 6FF00700 		mvn	r0, #7
 937 007c EBE7     		b	.L70
 938              	.LVL81:
 939              	.L86:
1013:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 940              		.loc 1 1013 3 discriminator 2 view .LVU307
 941 007e 0023     		movs	r3, #0
 942 0080 E1E7     		b	.L78
 943              	.LVL82:
 944              	.L93:
 985:Middlewares/Third_Party/LwIP/src/core/udp.c ****     }
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 39


 945              		.loc 1 985 14 view .LVU308
 946 0082 6FF00700 		mvn	r0, #7
 947              	.LVL83:
 985:Middlewares/Third_Party/LwIP/src/core/udp.c ****     }
 948              		.loc 1 985 14 view .LVU309
 949 0086 E6E7     		b	.L70
 950              	.LVL84:
 951              	.L82:
1006:Middlewares/Third_Party/LwIP/src/core/udp.c ****           }
 952              		.loc 1 1006 20 view .LVU310
 953 0088 6FF00700 		mvn	r0, #7
 954 008c E3E7     		b	.L70
 955              	.L83:
 956 008e 6FF00700 		mvn	r0, #7
 957 0092 E0E7     		b	.L70
 958              	.L84:
 959 0094 6FF00700 		mvn	r0, #7
 960 0098 DDE7     		b	.L70
 961              	.LVL85:
 962              	.L87:
1026:Middlewares/Third_Party/LwIP/src/core/udp.c **** }
 963              		.loc 1 1026 10 view .LVU311
 964 009a 0020     		movs	r0, #0
 965 009c DBE7     		b	.L70
 966              	.L95:
 967 009e 00BF     		.align	2
 968              	.L94:
 969 00a0 00000000 		.word	udp_pcbs
 970 00a4 00000000 		.word	ip_addr_any
 971 00a8 00000000 		.word	.LC0
 972 00ac 00000000 		.word	.LC7
 973 00b0 50000000 		.word	.LC2
 974              		.cfi_endproc
 975              	.LFE178:
 977              		.section	.rodata.udp_sendto_if_src.str1.4,"aMS",%progbits,1
 978              		.align	2
 979              	.LC8:
 980 0000 7564705F 		.ascii	"udp_sendto_if_src: invalid pcb\000"
 980      73656E64 
 980      746F5F69 
 980      665F7372 
 980      633A2069 
 981 001f 00       		.align	2
 982              	.LC9:
 983 0020 7564705F 		.ascii	"udp_sendto_if_src: invalid pbuf\000"
 983      73656E64 
 983      746F5F69 
 983      665F7372 
 983      633A2069 
 984              		.align	2
 985              	.LC10:
 986 0040 7564705F 		.ascii	"udp_sendto_if_src: invalid dst_ip\000"
 986      73656E64 
 986      746F5F69 
 986      665F7372 
 986      633A2069 
 987 0062 0000     		.align	2
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 40


 988              	.LC11:
 989 0064 7564705F 		.ascii	"udp_sendto_if_src: invalid src_ip\000"
 989      73656E64 
 989      746F5F69 
 989      665F7372 
 989      633A2069 
 990 0086 0000     		.align	2
 991              	.LC12:
 992 0088 7564705F 		.ascii	"udp_sendto_if_src: invalid netif\000"
 992      73656E64 
 992      746F5F69 
 992      665F7372 
 992      633A2069 
 993 00a9 000000   		.align	2
 994              	.LC13:
 995 00ac 63686563 		.ascii	"check that first pbuf can hold struct udp_hdr\000"
 995      6B207468 
 995      61742066 
 995      69727374 
 995      20706275 
 996              		.section	.text.udp_sendto_if_src,"ax",%progbits
 997              		.align	1
 998              		.global	udp_sendto_if_src
 999              		.syntax unified
 1000              		.thumb
 1001              		.thumb_func
 1003              	udp_sendto_if_src:
 1004              	.LVL86:
 1005              	.LFB177:
 701:Middlewares/Third_Party/LwIP/src/core/udp.c **** #if LWIP_CHECKSUM_ON_COPY && CHECKSUM_GEN_UDP
 1006              		.loc 1 701 1 is_stmt 1 view -0
 1007              		.cfi_startproc
 1008              		@ args = 8, pretend = 0, frame = 0
 1009              		@ frame_needed = 0, uses_anonymous_args = 0
 701:Middlewares/Third_Party/LwIP/src/core/udp.c **** #if LWIP_CHECKSUM_ON_COPY && CHECKSUM_GEN_UDP
 1010              		.loc 1 701 1 is_stmt 0 view .LVU313
 1011 0000 2DE9F04F 		push	{r4, r5, r6, r7, r8, r9, r10, fp, lr}
 1012              	.LCFI7:
 1013              		.cfi_def_cfa_offset 36
 1014              		.cfi_offset 4, -36
 1015              		.cfi_offset 5, -32
 1016              		.cfi_offset 6, -28
 1017              		.cfi_offset 7, -24
 1018              		.cfi_offset 8, -20
 1019              		.cfi_offset 9, -16
 1020              		.cfi_offset 10, -12
 1021              		.cfi_offset 11, -8
 1022              		.cfi_offset 14, -4
 1023 0004 85B0     		sub	sp, sp, #20
 1024              	.LCFI8:
 1025              		.cfi_def_cfa_offset 56
 1026 0006 DDF838B0 		ldr	fp, [sp, #56]
 1027 000a DDF83CA0 		ldr	r10, [sp, #60]
 713:Middlewares/Third_Party/LwIP/src/core/udp.c ****   err_t err;
 1028              		.loc 1 713 3 is_stmt 1 view .LVU314
 714:Middlewares/Third_Party/LwIP/src/core/udp.c ****   struct pbuf *q; /* q will be sent down the stack */
 1029              		.loc 1 714 3 view .LVU315
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 41


 715:Middlewares/Third_Party/LwIP/src/core/udp.c ****   u8_t ip_proto;
 1030              		.loc 1 715 3 view .LVU316
 716:Middlewares/Third_Party/LwIP/src/core/udp.c ****   u8_t ttl;
 1031              		.loc 1 716 3 view .LVU317
 717:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 1032              		.loc 1 717 3 view .LVU318
 719:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 1033              		.loc 1 719 28 view .LVU319
 721:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if_src: invalid pbuf", p != NULL, return ERR_ARG);
 1034              		.loc 1 721 3 view .LVU320
 721:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if_src: invalid pbuf", p != NULL, return ERR_ARG);
 1035              		.loc 1 721 3 view .LVU321
 1036 000e 0028     		cmp	r0, #0
 1037 0010 45D0     		beq	.L110
 1038 0012 0C46     		mov	r4, r1
 1039 0014 9046     		mov	r8, r2
 1040 0016 9946     		mov	r9, r3
 1041 0018 0646     		mov	r6, r0
 721:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if_src: invalid pbuf", p != NULL, return ERR_ARG);
 1042              		.loc 1 721 3 discriminator 2 view .LVU322
 722:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if_src: invalid dst_ip", dst_ip != NULL, return ERR_ARG);
 1043              		.loc 1 722 3 view .LVU323
 722:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if_src: invalid dst_ip", dst_ip != NULL, return ERR_ARG);
 1044              		.loc 1 722 3 view .LVU324
 1045 001a 0029     		cmp	r1, #0
 1046 001c 49D0     		beq	.L111
 722:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if_src: invalid dst_ip", dst_ip != NULL, return ERR_ARG);
 1047              		.loc 1 722 3 discriminator 2 view .LVU325
 723:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if_src: invalid src_ip", src_ip != NULL, return ERR_ARG);
 1048              		.loc 1 723 3 view .LVU326
 723:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if_src: invalid src_ip", src_ip != NULL, return ERR_ARG);
 1049              		.loc 1 723 3 view .LVU327
 1050 001e 002A     		cmp	r2, #0
 1051 0020 51D0     		beq	.L112
 723:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if_src: invalid src_ip", src_ip != NULL, return ERR_ARG);
 1052              		.loc 1 723 3 discriminator 2 view .LVU328
 724:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if_src: invalid netif", netif != NULL, return ERR_ARG);
 1053              		.loc 1 724 3 view .LVU329
 724:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if_src: invalid netif", netif != NULL, return ERR_ARG);
 1054              		.loc 1 724 3 view .LVU330
 1055 0022 BAF1000F 		cmp	r10, #0
 1056 0026 58D0     		beq	.L113
 724:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if_src: invalid netif", netif != NULL, return ERR_ARG);
 1057              		.loc 1 724 3 discriminator 2 view .LVU331
 725:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 1058              		.loc 1 725 3 view .LVU332
 725:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 1059              		.loc 1 725 3 view .LVU333
 1060 0028 BBF1000F 		cmp	fp, #0
 1061 002c 5FD0     		beq	.L114
 725:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 1062              		.loc 1 725 3 discriminator 2 view .LVU334
 727:Middlewares/Third_Party/LwIP/src/core/udp.c ****       !IP_ADDR_PCB_VERSION_MATCH(pcb, dst_ip)) {
 1063              		.loc 1 727 3 view .LVU335
 746:Middlewares/Third_Party/LwIP/src/core/udp.c ****     LWIP_DEBUGF(UDP_DEBUG | LWIP_DBG_TRACE, ("udp_send: not yet bound to a port, binding now\n"));
 1064              		.loc 1 746 3 view .LVU336
 746:Middlewares/Third_Party/LwIP/src/core/udp.c ****     LWIP_DEBUGF(UDP_DEBUG | LWIP_DBG_TRACE, ("udp_send: not yet bound to a port, binding now\n"));
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 42


 1065              		.loc 1 746 10 is_stmt 0 view .LVU337
 1066 002e 428A     		ldrh	r2, [r0, #18]
 1067              	.LVL87:
 746:Middlewares/Third_Party/LwIP/src/core/udp.c ****     LWIP_DEBUGF(UDP_DEBUG | LWIP_DBG_TRACE, ("udp_send: not yet bound to a port, binding now\n"));
 1068              		.loc 1 746 6 view .LVU338
 1069 0030 002A     		cmp	r2, #0
 1070 0032 66D0     		beq	.L115
 1071              	.LVL88:
 1072              	.L103:
 756:Middlewares/Third_Party/LwIP/src/core/udp.c ****     return ERR_MEM;
 1073              		.loc 1 756 3 is_stmt 1 view .LVU339
 756:Middlewares/Third_Party/LwIP/src/core/udp.c ****     return ERR_MEM;
 1074              		.loc 1 756 16 is_stmt 0 view .LVU340
 1075 0034 2289     		ldrh	r2, [r4, #8]
 756:Middlewares/Third_Party/LwIP/src/core/udp.c ****     return ERR_MEM;
 1076              		.loc 1 756 6 view .LVU341
 1077 0036 4FF6F773 		movw	r3, #65527
 1078 003a 9A42     		cmp	r2, r3
 1079 003c 7FD8     		bhi	.L106
 760:Middlewares/Third_Party/LwIP/src/core/udp.c ****     /* allocate header in a separate new pbuf */
 1080              		.loc 1 760 3 is_stmt 1 view .LVU342
 760:Middlewares/Third_Party/LwIP/src/core/udp.c ****     /* allocate header in a separate new pbuf */
 1081              		.loc 1 760 7 is_stmt 0 view .LVU343
 1082 003e 0821     		movs	r1, #8
 1083 0040 2046     		mov	r0, r4
 1084 0042 FFF7FEFF 		bl	pbuf_add_header
 1085              	.LVL89:
 760:Middlewares/Third_Party/LwIP/src/core/udp.c ****     /* allocate header in a separate new pbuf */
 1086              		.loc 1 760 6 discriminator 1 view .LVU344
 1087 0046 0028     		cmp	r0, #0
 1088 0048 62D1     		bne	.L116
 778:Middlewares/Third_Party/LwIP/src/core/udp.c ****     LWIP_DEBUGF(UDP_DEBUG, ("udp_send: added header in given pbuf %p\n", (void *)p));
 1089              		.loc 1 778 7 view .LVU345
 1090 004a 2546     		mov	r5, r4
 1091              	.L104:
 1092              	.LVL90:
 779:Middlewares/Third_Party/LwIP/src/core/udp.c ****   }
 1093              		.loc 1 779 85 is_stmt 1 view .LVU346
 781:Middlewares/Third_Party/LwIP/src/core/udp.c ****               (q->len >= sizeof(struct udp_hdr)));
 1094              		.loc 1 781 3 view .LVU347
 781:Middlewares/Third_Party/LwIP/src/core/udp.c ****               (q->len >= sizeof(struct udp_hdr)));
 1095              		.loc 1 781 3 view .LVU348
 1096 004c 6B89     		ldrh	r3, [r5, #10]
 1097 004e 072B     		cmp	r3, #7
 1098 0050 6DD9     		bls	.L117
 1099              	.L105:
 781:Middlewares/Third_Party/LwIP/src/core/udp.c ****               (q->len >= sizeof(struct udp_hdr)));
 1100              		.loc 1 781 3 discriminator 3 view .LVU349
 781:Middlewares/Third_Party/LwIP/src/core/udp.c ****               (q->len >= sizeof(struct udp_hdr)));
 1101              		.loc 1 781 3 discriminator 3 view .LVU350
 784:Middlewares/Third_Party/LwIP/src/core/udp.c ****   udphdr->src = lwip_htons(pcb->local_port);
 1102              		.loc 1 784 3 view .LVU351
 784:Middlewares/Third_Party/LwIP/src/core/udp.c ****   udphdr->src = lwip_htons(pcb->local_port);
 1103              		.loc 1 784 10 is_stmt 0 view .LVU352
 1104 0052 6F68     		ldr	r7, [r5, #4]
 1105              	.LVL91:
 785:Middlewares/Third_Party/LwIP/src/core/udp.c ****   udphdr->dest = lwip_htons(dst_port);
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 43


 1106              		.loc 1 785 3 is_stmt 1 view .LVU353
 785:Middlewares/Third_Party/LwIP/src/core/udp.c ****   udphdr->dest = lwip_htons(dst_port);
 1107              		.loc 1 785 17 is_stmt 0 view .LVU354
 1108 0054 708A     		ldrh	r0, [r6, #18]
 1109 0056 FFF7FEFF 		bl	lwip_htons
 1110              	.LVL92:
 785:Middlewares/Third_Party/LwIP/src/core/udp.c ****   udphdr->dest = lwip_htons(dst_port);
 1111              		.loc 1 785 15 discriminator 1 view .LVU355
 1112 005a 3880     		strh	r0, [r7]	@ unaligned
 786:Middlewares/Third_Party/LwIP/src/core/udp.c ****   /* in UDP, 0 checksum means 'no checksum' */
 1113              		.loc 1 786 3 is_stmt 1 view .LVU356
 786:Middlewares/Third_Party/LwIP/src/core/udp.c ****   /* in UDP, 0 checksum means 'no checksum' */
 1114              		.loc 1 786 18 is_stmt 0 view .LVU357
 1115 005c 4846     		mov	r0, r9
 1116 005e FFF7FEFF 		bl	lwip_htons
 1117              	.LVL93:
 786:Middlewares/Third_Party/LwIP/src/core/udp.c ****   /* in UDP, 0 checksum means 'no checksum' */
 1118              		.loc 1 786 16 discriminator 1 view .LVU358
 1119 0062 7880     		strh	r0, [r7, #2]	@ unaligned
 788:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 1120              		.loc 1 788 3 is_stmt 1 view .LVU359
 788:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 1121              		.loc 1 788 18 is_stmt 0 view .LVU360
 1122 0064 0023     		movs	r3, #0
 1123 0066 BB71     		strb	r3, [r7, #6]
 1124 0068 FB71     		strb	r3, [r7, #7]
 797:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 1125              		.loc 1 797 90 is_stmt 1 view .LVU361
 849:Middlewares/Third_Party/LwIP/src/core/udp.c ****     udphdr->len = lwip_htons(q->tot_len);
 1126              		.loc 1 849 83 view .LVU362
 850:Middlewares/Third_Party/LwIP/src/core/udp.c ****     /* calculate checksum */
 1127              		.loc 1 850 5 view .LVU363
 850:Middlewares/Third_Party/LwIP/src/core/udp.c ****     /* calculate checksum */
 1128              		.loc 1 850 19 is_stmt 0 view .LVU364
 1129 006a 2889     		ldrh	r0, [r5, #8]
 1130 006c FFF7FEFF 		bl	lwip_htons
 1131              	.LVL94:
 850:Middlewares/Third_Party/LwIP/src/core/udp.c ****     /* calculate checksum */
 1132              		.loc 1 850 17 discriminator 1 view .LVU365
 1133 0070 B880     		strh	r0, [r7, #4]	@ unaligned
 879:Middlewares/Third_Party/LwIP/src/core/udp.c ****   }
 1134              		.loc 1 879 5 is_stmt 1 view .LVU366
 1135              	.LVL95:
 886:Middlewares/Third_Party/LwIP/src/core/udp.c **** #endif /* LWIP_MULTICAST_TX_OPTIONS */
 1136              		.loc 1 886 3 view .LVU367
 889:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_DEBUGF(UDP_DEBUG, ("udp_send: ip_output_if (,,,,0x%02"X16_F",)\n", (u16_t)ip_proto));
 1137              		.loc 1 889 84 view .LVU368
 890:Middlewares/Third_Party/LwIP/src/core/udp.c ****   /* output to IP */
 1138              		.loc 1 890 92 view .LVU369
 892:Middlewares/Third_Party/LwIP/src/core/udp.c ****   err = ip_output_if_src(q, src_ip, dst_ip, ttl, pcb->tos, ip_proto, netif);
 1139              		.loc 1 892 46 view .LVU370
 893:Middlewares/Third_Party/LwIP/src/core/udp.c ****   NETIF_RESET_HINTS(netif);
 1140              		.loc 1 893 3 view .LVU371
 893:Middlewares/Third_Party/LwIP/src/core/udp.c ****   NETIF_RESET_HINTS(netif);
 1141              		.loc 1 893 9 is_stmt 0 view .LVU372
 1142 0072 CDF808B0 		str	fp, [sp, #8]
 1143              	.LVL96:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 44


 893:Middlewares/Third_Party/LwIP/src/core/udp.c ****   NETIF_RESET_HINTS(netif);
 1144              		.loc 1 893 9 view .LVU373
 1145 0076 1123     		movs	r3, #17
 1146 0078 0193     		str	r3, [sp, #4]
 1147 007a B37A     		ldrb	r3, [r6, #10]	@ zero_extendqisi2
 1148 007c 0093     		str	r3, [sp]
 1149 007e F37A     		ldrb	r3, [r6, #11]	@ zero_extendqisi2
 1150 0080 4246     		mov	r2, r8
 1151 0082 5146     		mov	r1, r10
 1152 0084 2846     		mov	r0, r5
 1153 0086 FFF7FEFF 		bl	ip4_output_if_src
 1154              	.LVL97:
 1155 008a 0746     		mov	r7, r0
 1156              	.LVL98:
 894:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 1157              		.loc 1 894 27 is_stmt 1 view .LVU374
 897:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 1158              		.loc 1 897 39 view .LVU375
 900:Middlewares/Third_Party/LwIP/src/core/udp.c ****     /* free the header pbuf */
 1159              		.loc 1 900 3 view .LVU376
 900:Middlewares/Third_Party/LwIP/src/core/udp.c ****     /* free the header pbuf */
 1160              		.loc 1 900 6 is_stmt 0 view .LVU377
 1161 008c A542     		cmp	r5, r4
 1162 008e 02D0     		beq	.L98
 902:Middlewares/Third_Party/LwIP/src/core/udp.c ****     q = NULL;
 1163              		.loc 1 902 5 is_stmt 1 view .LVU378
 1164 0090 2846     		mov	r0, r5
 1165 0092 FFF7FEFF 		bl	pbuf_free
 1166              	.LVL99:
 903:Middlewares/Third_Party/LwIP/src/core/udp.c ****     /* p is still referenced by the caller, and will live on */
 1167              		.loc 1 903 5 view .LVU379
 1168              	.L98:
 909:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 1169              		.loc 1 909 1 is_stmt 0 view .LVU380
 1170 0096 3846     		mov	r0, r7
 1171 0098 05B0     		add	sp, sp, #20
 1172              	.LCFI9:
 1173              		.cfi_remember_state
 1174              		.cfi_def_cfa_offset 36
 1175              		@ sp needed
 1176 009a BDE8F08F 		pop	{r4, r5, r6, r7, r8, r9, r10, fp, pc}
 1177              	.LVL100:
 1178              	.L110:
 1179              	.LCFI10:
 1180              		.cfi_restore_state
 721:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if_src: invalid pbuf", p != NULL, return ERR_ARG);
 1181              		.loc 1 721 3 is_stmt 1 discriminator 1 view .LVU381
 721:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if_src: invalid pbuf", p != NULL, return ERR_ARG);
 1182              		.loc 1 721 3 discriminator 1 view .LVU382
 1183 009e 2B4B     		ldr	r3, .L118
 1184              	.LVL101:
 721:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if_src: invalid pbuf", p != NULL, return ERR_ARG);
 1185              		.loc 1 721 3 is_stmt 0 discriminator 1 view .LVU383
 1186 00a0 40F2D122 		movw	r2, #721
 1187              	.LVL102:
 721:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if_src: invalid pbuf", p != NULL, return ERR_ARG);
 1188              		.loc 1 721 3 discriminator 1 view .LVU384
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 45


 1189 00a4 2A49     		ldr	r1, .L118+4
 1190              	.LVL103:
 721:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if_src: invalid pbuf", p != NULL, return ERR_ARG);
 1191              		.loc 1 721 3 discriminator 1 view .LVU385
 1192 00a6 2B48     		ldr	r0, .L118+8
 1193              	.LVL104:
 721:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if_src: invalid pbuf", p != NULL, return ERR_ARG);
 1194              		.loc 1 721 3 discriminator 1 view .LVU386
 1195 00a8 FFF7FEFF 		bl	printf
 1196              	.LVL105:
 721:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if_src: invalid pbuf", p != NULL, return ERR_ARG);
 1197              		.loc 1 721 3 is_stmt 1 discriminator 1 view .LVU387
 721:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if_src: invalid pbuf", p != NULL, return ERR_ARG);
 1198              		.loc 1 721 3 discriminator 1 view .LVU388
 1199 00ac 6FF00F07 		mvn	r7, #15
 721:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if_src: invalid pbuf", p != NULL, return ERR_ARG);
 1200              		.loc 1 721 3 is_stmt 0 view .LVU389
 1201 00b0 F1E7     		b	.L98
 1202              	.LVL106:
 1203              	.L111:
 722:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if_src: invalid dst_ip", dst_ip != NULL, return ERR_ARG);
 1204              		.loc 1 722 3 is_stmt 1 discriminator 1 view .LVU390
 722:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if_src: invalid dst_ip", dst_ip != NULL, return ERR_ARG);
 1205              		.loc 1 722 3 discriminator 1 view .LVU391
 1206 00b2 264B     		ldr	r3, .L118
 1207              	.LVL107:
 722:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if_src: invalid dst_ip", dst_ip != NULL, return ERR_ARG);
 1208              		.loc 1 722 3 is_stmt 0 discriminator 1 view .LVU392
 1209 00b4 40F2D222 		movw	r2, #722
 1210              	.LVL108:
 722:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if_src: invalid dst_ip", dst_ip != NULL, return ERR_ARG);
 1211              		.loc 1 722 3 discriminator 1 view .LVU393
 1212 00b8 2749     		ldr	r1, .L118+12
 1213              	.LVL109:
 722:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if_src: invalid dst_ip", dst_ip != NULL, return ERR_ARG);
 1214              		.loc 1 722 3 discriminator 1 view .LVU394
 1215 00ba 2648     		ldr	r0, .L118+8
 1216              	.LVL110:
 722:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if_src: invalid dst_ip", dst_ip != NULL, return ERR_ARG);
 1217              		.loc 1 722 3 discriminator 1 view .LVU395
 1218 00bc FFF7FEFF 		bl	printf
 1219              	.LVL111:
 722:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if_src: invalid dst_ip", dst_ip != NULL, return ERR_ARG);
 1220              		.loc 1 722 3 is_stmt 1 discriminator 1 view .LVU396
 722:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if_src: invalid dst_ip", dst_ip != NULL, return ERR_ARG);
 1221              		.loc 1 722 3 discriminator 1 view .LVU397
 1222 00c0 6FF00F07 		mvn	r7, #15
 722:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if_src: invalid dst_ip", dst_ip != NULL, return ERR_ARG);
 1223              		.loc 1 722 3 is_stmt 0 view .LVU398
 1224 00c4 E7E7     		b	.L98
 1225              	.LVL112:
 1226              	.L112:
 723:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if_src: invalid src_ip", src_ip != NULL, return ERR_ARG);
 1227              		.loc 1 723 3 is_stmt 1 discriminator 1 view .LVU399
 723:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if_src: invalid src_ip", src_ip != NULL, return ERR_ARG);
 1228              		.loc 1 723 3 discriminator 1 view .LVU400
 1229 00c6 214B     		ldr	r3, .L118
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 46


 1230              	.LVL113:
 723:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if_src: invalid src_ip", src_ip != NULL, return ERR_ARG);
 1231              		.loc 1 723 3 is_stmt 0 discriminator 1 view .LVU401
 1232 00c8 40F2D322 		movw	r2, #723
 1233              	.LVL114:
 723:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if_src: invalid src_ip", src_ip != NULL, return ERR_ARG);
 1234              		.loc 1 723 3 discriminator 1 view .LVU402
 1235 00cc 2349     		ldr	r1, .L118+16
 1236              	.LVL115:
 723:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if_src: invalid src_ip", src_ip != NULL, return ERR_ARG);
 1237              		.loc 1 723 3 discriminator 1 view .LVU403
 1238 00ce 2148     		ldr	r0, .L118+8
 1239              	.LVL116:
 723:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if_src: invalid src_ip", src_ip != NULL, return ERR_ARG);
 1240              		.loc 1 723 3 discriminator 1 view .LVU404
 1241 00d0 FFF7FEFF 		bl	printf
 1242              	.LVL117:
 723:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if_src: invalid src_ip", src_ip != NULL, return ERR_ARG);
 1243              		.loc 1 723 3 is_stmt 1 discriminator 1 view .LVU405
 723:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if_src: invalid src_ip", src_ip != NULL, return ERR_ARG);
 1244              		.loc 1 723 3 discriminator 1 view .LVU406
 1245 00d4 6FF00F07 		mvn	r7, #15
 723:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if_src: invalid src_ip", src_ip != NULL, return ERR_ARG);
 1246              		.loc 1 723 3 is_stmt 0 view .LVU407
 1247 00d8 DDE7     		b	.L98
 1248              	.LVL118:
 1249              	.L113:
 724:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if_src: invalid netif", netif != NULL, return ERR_ARG);
 1250              		.loc 1 724 3 is_stmt 1 discriminator 1 view .LVU408
 724:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if_src: invalid netif", netif != NULL, return ERR_ARG);
 1251              		.loc 1 724 3 discriminator 1 view .LVU409
 1252 00da 1C4B     		ldr	r3, .L118
 1253              	.LVL119:
 724:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if_src: invalid netif", netif != NULL, return ERR_ARG);
 1254              		.loc 1 724 3 is_stmt 0 discriminator 1 view .LVU410
 1255 00dc 4FF43572 		mov	r2, #724
 1256              	.LVL120:
 724:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if_src: invalid netif", netif != NULL, return ERR_ARG);
 1257              		.loc 1 724 3 discriminator 1 view .LVU411
 1258 00e0 1F49     		ldr	r1, .L118+20
 1259              	.LVL121:
 724:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if_src: invalid netif", netif != NULL, return ERR_ARG);
 1260              		.loc 1 724 3 discriminator 1 view .LVU412
 1261 00e2 1C48     		ldr	r0, .L118+8
 1262              	.LVL122:
 724:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if_src: invalid netif", netif != NULL, return ERR_ARG);
 1263              		.loc 1 724 3 discriminator 1 view .LVU413
 1264 00e4 FFF7FEFF 		bl	printf
 1265              	.LVL123:
 724:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if_src: invalid netif", netif != NULL, return ERR_ARG);
 1266              		.loc 1 724 3 is_stmt 1 discriminator 1 view .LVU414
 724:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if_src: invalid netif", netif != NULL, return ERR_ARG);
 1267              		.loc 1 724 3 discriminator 1 view .LVU415
 1268 00e8 6FF00F07 		mvn	r7, #15
 724:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if_src: invalid netif", netif != NULL, return ERR_ARG);
 1269              		.loc 1 724 3 is_stmt 0 view .LVU416
 1270 00ec D3E7     		b	.L98
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 47


 1271              	.LVL124:
 1272              	.L114:
 725:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 1273              		.loc 1 725 3 is_stmt 1 discriminator 1 view .LVU417
 725:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 1274              		.loc 1 725 3 discriminator 1 view .LVU418
 1275 00ee 174B     		ldr	r3, .L118
 1276              	.LVL125:
 725:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 1277              		.loc 1 725 3 is_stmt 0 discriminator 1 view .LVU419
 1278 00f0 40F2D522 		movw	r2, #725
 1279              	.LVL126:
 725:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 1280              		.loc 1 725 3 discriminator 1 view .LVU420
 1281 00f4 1B49     		ldr	r1, .L118+24
 1282              	.LVL127:
 725:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 1283              		.loc 1 725 3 discriminator 1 view .LVU421
 1284 00f6 1748     		ldr	r0, .L118+8
 1285              	.LVL128:
 725:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 1286              		.loc 1 725 3 discriminator 1 view .LVU422
 1287 00f8 FFF7FEFF 		bl	printf
 1288              	.LVL129:
 725:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 1289              		.loc 1 725 3 is_stmt 1 discriminator 1 view .LVU423
 725:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 1290              		.loc 1 725 3 discriminator 1 view .LVU424
 1291 00fc 6FF00F07 		mvn	r7, #15
 725:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 1292              		.loc 1 725 3 is_stmt 0 view .LVU425
 1293 0100 C9E7     		b	.L98
 1294              	.LVL130:
 1295              	.L115:
 747:Middlewares/Third_Party/LwIP/src/core/udp.c ****     err = udp_bind(pcb, &pcb->local_ip, pcb->local_port);
 1296              		.loc 1 747 98 is_stmt 1 view .LVU426
 748:Middlewares/Third_Party/LwIP/src/core/udp.c ****     if (err != ERR_OK) {
 1297              		.loc 1 748 5 view .LVU427
 748:Middlewares/Third_Party/LwIP/src/core/udp.c ****     if (err != ERR_OK) {
 1298              		.loc 1 748 11 is_stmt 0 view .LVU428
 1299 0102 0146     		mov	r1, r0
 1300              	.LVL131:
 748:Middlewares/Third_Party/LwIP/src/core/udp.c ****     if (err != ERR_OK) {
 1301              		.loc 1 748 11 view .LVU429
 1302 0104 FFF7FEFF 		bl	udp_bind
 1303              	.LVL132:
 749:Middlewares/Third_Party/LwIP/src/core/udp.c ****       LWIP_DEBUGF(UDP_DEBUG | LWIP_DBG_TRACE | LWIP_DBG_LEVEL_SERIOUS, ("udp_send: forced port bind
 1304              		.loc 1 749 5 is_stmt 1 view .LVU430
 749:Middlewares/Third_Party/LwIP/src/core/udp.c ****       LWIP_DEBUGF(UDP_DEBUG | LWIP_DBG_TRACE | LWIP_DBG_LEVEL_SERIOUS, ("udp_send: forced port bind
 1305              		.loc 1 749 8 is_stmt 0 view .LVU431
 1306 0108 0746     		mov	r7, r0
 1307 010a 0028     		cmp	r0, #0
 1308 010c 92D0     		beq	.L103
 1309 010e C2E7     		b	.L98
 1310              	.LVL133:
 1311              	.L116:
 762:Middlewares/Third_Party/LwIP/src/core/udp.c ****     /* new header pbuf could not be allocated? */
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 48


 1312              		.loc 1 762 5 is_stmt 1 view .LVU432
 762:Middlewares/Third_Party/LwIP/src/core/udp.c ****     /* new header pbuf could not be allocated? */
 1313              		.loc 1 762 9 is_stmt 0 view .LVU433
 1314 0110 4FF42072 		mov	r2, #640
 1315 0114 0821     		movs	r1, #8
 1316 0116 2220     		movs	r0, #34
 1317 0118 FFF7FEFF 		bl	pbuf_alloc
 1318              	.LVL134:
 764:Middlewares/Third_Party/LwIP/src/core/udp.c ****       LWIP_DEBUGF(UDP_DEBUG | LWIP_DBG_TRACE | LWIP_DBG_LEVEL_SERIOUS, ("udp_send: could not alloca
 1319              		.loc 1 764 5 is_stmt 1 view .LVU434
 764:Middlewares/Third_Party/LwIP/src/core/udp.c ****       LWIP_DEBUGF(UDP_DEBUG | LWIP_DBG_TRACE | LWIP_DBG_LEVEL_SERIOUS, ("udp_send: could not alloca
 1320              		.loc 1 764 8 is_stmt 0 view .LVU435
 1321 011c 0546     		mov	r5, r0
 1322 011e 88B1     		cbz	r0, .L108
 768:Middlewares/Third_Party/LwIP/src/core/udp.c ****       /* chain header q in front of given pbuf p (only if p contains data) */
 1323              		.loc 1 768 5 is_stmt 1 view .LVU436
 768:Middlewares/Third_Party/LwIP/src/core/udp.c ****       /* chain header q in front of given pbuf p (only if p contains data) */
 1324              		.loc 1 768 10 is_stmt 0 view .LVU437
 1325 0120 2389     		ldrh	r3, [r4, #8]
 768:Middlewares/Third_Party/LwIP/src/core/udp.c ****       /* chain header q in front of given pbuf p (only if p contains data) */
 1326              		.loc 1 768 8 view .LVU438
 1327 0122 002B     		cmp	r3, #0
 1328 0124 92D0     		beq	.L104
 770:Middlewares/Third_Party/LwIP/src/core/udp.c ****     }
 1329              		.loc 1 770 7 is_stmt 1 view .LVU439
 1330 0126 2146     		mov	r1, r4
 1331 0128 FFF7FEFF 		bl	pbuf_chain
 1332              	.LVL135:
 770:Middlewares/Third_Party/LwIP/src/core/udp.c ****     }
 1333              		.loc 1 770 7 is_stmt 0 view .LVU440
 1334 012c 8EE7     		b	.L104
 1335              	.L117:
 781:Middlewares/Third_Party/LwIP/src/core/udp.c ****               (q->len >= sizeof(struct udp_hdr)));
 1336              		.loc 1 781 3 is_stmt 1 discriminator 1 view .LVU441
 781:Middlewares/Third_Party/LwIP/src/core/udp.c ****               (q->len >= sizeof(struct udp_hdr)));
 1337              		.loc 1 781 3 discriminator 1 view .LVU442
 1338 012e 074B     		ldr	r3, .L118
 1339 0130 40F20D32 		movw	r2, #781
 1340 0134 0C49     		ldr	r1, .L118+28
 1341 0136 0748     		ldr	r0, .L118+8
 1342 0138 FFF7FEFF 		bl	printf
 1343              	.LVL136:
 1344 013c 89E7     		b	.L105
 1345              	.LVL137:
 1346              	.L106:
 757:Middlewares/Third_Party/LwIP/src/core/udp.c ****   }
 1347              		.loc 1 757 12 is_stmt 0 view .LVU443
 1348 013e 4FF0FF37 		mov	r7, #-1
 1349 0142 A8E7     		b	.L98
 1350              	.LVL138:
 1351              	.L108:
 766:Middlewares/Third_Party/LwIP/src/core/udp.c ****     }
 1352              		.loc 1 766 14 view .LVU444
 1353 0144 4FF0FF37 		mov	r7, #-1
 1354 0148 A5E7     		b	.L98
 1355              	.L119:
 1356 014a 00BF     		.align	2
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 49


 1357              	.L118:
 1358 014c 00000000 		.word	.LC0
 1359 0150 00000000 		.word	.LC8
 1360 0154 50000000 		.word	.LC2
 1361 0158 20000000 		.word	.LC9
 1362 015c 40000000 		.word	.LC10
 1363 0160 64000000 		.word	.LC11
 1364 0164 88000000 		.word	.LC12
 1365 0168 AC000000 		.word	.LC13
 1366              		.cfi_endproc
 1367              	.LFE177:
 1369              		.section	.rodata.udp_sendto_if.str1.4,"aMS",%progbits,1
 1370              		.align	2
 1371              	.LC14:
 1372 0000 7564705F 		.ascii	"udp_sendto_if: invalid pcb\000"
 1372      73656E64 
 1372      746F5F69 
 1372      663A2069 
 1372      6E76616C 
 1373 001b 00       		.align	2
 1374              	.LC15:
 1375 001c 7564705F 		.ascii	"udp_sendto_if: invalid pbuf\000"
 1375      73656E64 
 1375      746F5F69 
 1375      663A2069 
 1375      6E76616C 
 1376              		.align	2
 1377              	.LC16:
 1378 0038 7564705F 		.ascii	"udp_sendto_if: invalid dst_ip\000"
 1378      73656E64 
 1378      746F5F69 
 1378      663A2069 
 1378      6E76616C 
 1379 0056 0000     		.align	2
 1380              	.LC17:
 1381 0058 7564705F 		.ascii	"udp_sendto_if: invalid netif\000"
 1381      73656E64 
 1381      746F5F69 
 1381      663A2069 
 1381      6E76616C 
 1382              		.section	.text.udp_sendto_if,"ax",%progbits
 1383              		.align	1
 1384              		.global	udp_sendto_if
 1385              		.syntax unified
 1386              		.thumb
 1387              		.thumb_func
 1389              	udp_sendto_if:
 1390              	.LVL139:
 1391              	.LFB176:
 626:Middlewares/Third_Party/LwIP/src/core/udp.c **** #if LWIP_CHECKSUM_ON_COPY && CHECKSUM_GEN_UDP
 1392              		.loc 1 626 1 is_stmt 1 view -0
 1393              		.cfi_startproc
 1394              		@ args = 4, pretend = 0, frame = 0
 1395              		@ frame_needed = 0, uses_anonymous_args = 0
 626:Middlewares/Third_Party/LwIP/src/core/udp.c **** #if LWIP_CHECKSUM_ON_COPY && CHECKSUM_GEN_UDP
 1396              		.loc 1 626 1 is_stmt 0 view .LVU446
 1397 0000 F0B5     		push	{r4, r5, r6, r7, lr}
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 50


 1398              	.LCFI11:
 1399              		.cfi_def_cfa_offset 20
 1400              		.cfi_offset 4, -20
 1401              		.cfi_offset 5, -16
 1402              		.cfi_offset 6, -12
 1403              		.cfi_offset 7, -8
 1404              		.cfi_offset 14, -4
 1405 0002 83B0     		sub	sp, sp, #12
 1406              	.LCFI12:
 1407              		.cfi_def_cfa_offset 32
 1408 0004 089D     		ldr	r5, [sp, #32]
 638:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 1409              		.loc 1 638 3 is_stmt 1 view .LVU447
 640:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if: invalid pbuf", p != NULL, return ERR_ARG);
 1410              		.loc 1 640 3 view .LVU448
 640:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if: invalid pbuf", p != NULL, return ERR_ARG);
 1411              		.loc 1 640 3 view .LVU449
 1412 0006 90B1     		cbz	r0, .L131
 1413 0008 0446     		mov	r4, r0
 640:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if: invalid pbuf", p != NULL, return ERR_ARG);
 1414              		.loc 1 640 3 discriminator 2 view .LVU450
 641:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if: invalid dst_ip", dst_ip != NULL, return ERR_ARG);
 1415              		.loc 1 641 3 view .LVU451
 641:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if: invalid dst_ip", dst_ip != NULL, return ERR_ARG);
 1416              		.loc 1 641 3 view .LVU452
 1417 000a D1B1     		cbz	r1, .L132
 641:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if: invalid dst_ip", dst_ip != NULL, return ERR_ARG);
 1418              		.loc 1 641 3 discriminator 2 view .LVU453
 642:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if: invalid netif", netif != NULL, return ERR_ARG);
 1419              		.loc 1 642 3 view .LVU454
 642:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if: invalid netif", netif != NULL, return ERR_ARG);
 1420              		.loc 1 642 3 view .LVU455
 1421 000c 1AB3     		cbz	r2, .L133
 642:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if: invalid netif", netif != NULL, return ERR_ARG);
 1422              		.loc 1 642 3 discriminator 2 view .LVU456
 643:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 1423              		.loc 1 643 3 view .LVU457
 643:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 1424              		.loc 1 643 3 view .LVU458
 1425 000e 65B3     		cbz	r5, .L134
 643:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 1426              		.loc 1 643 3 discriminator 2 view .LVU459
 645:Middlewares/Third_Party/LwIP/src/core/udp.c ****     return ERR_VAL;
 1427              		.loc 1 645 3 view .LVU460
 673:Middlewares/Third_Party/LwIP/src/core/udp.c ****         ip4_addr_ismulticast(ip_2_ip4(&pcb->local_ip))) {
 1428              		.loc 1 673 5 view .LVU461
 673:Middlewares/Third_Party/LwIP/src/core/udp.c ****         ip4_addr_ismulticast(ip_2_ip4(&pcb->local_ip))) {
 1429              		.loc 1 673 8 is_stmt 0 view .LVU462
 1430 0010 0028     		cmp	r0, #0
 1431 0012 34D0     		beq	.L126
 673:Middlewares/Third_Party/LwIP/src/core/udp.c ****         ip4_addr_ismulticast(ip_2_ip4(&pcb->local_ip))) {
 1432              		.loc 1 673 9 discriminator 1 view .LVU463
 1433 0014 0668     		ldr	r6, [r0]
 1434 0016 96B3     		cbz	r6, .L126
 674:Middlewares/Third_Party/LwIP/src/core/udp.c ****       /* if the local_ip is any or multicast
 1435              		.loc 1 674 9 view .LVU464
 1436 0018 06F0F00C 		and	ip, r6, #240
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 51


 673:Middlewares/Third_Party/LwIP/src/core/udp.c ****         ip4_addr_ismulticast(ip_2_ip4(&pcb->local_ip))) {
 1437              		.loc 1 673 50 discriminator 2 view .LVU465
 1438 001c BCF1E00F 		cmp	ip, #224
 1439 0020 2DD0     		beq	.L126
 681:Middlewares/Third_Party/LwIP/src/core/udp.c ****         /* local_ip doesn't match, drop the packet */
 1440              		.loc 1 681 7 is_stmt 1 view .LVU466
 681:Middlewares/Third_Party/LwIP/src/core/udp.c ****         /* local_ip doesn't match, drop the packet */
 1441              		.loc 1 681 10 is_stmt 0 view .LVU467
 1442 0022 6F68     		ldr	r7, [r5, #4]
 1443 0024 BE42     		cmp	r6, r7
 1444 0026 2BD0     		beq	.L128
 683:Middlewares/Third_Party/LwIP/src/core/udp.c ****       }
 1445              		.loc 1 683 16 view .LVU468
 1446 0028 6FF00300 		mvn	r0, #3
 1447              	.LVL140:
 683:Middlewares/Third_Party/LwIP/src/core/udp.c ****       }
 1448              		.loc 1 683 16 view .LVU469
 1449 002c 2DE0     		b	.L122
 1450              	.LVL141:
 1451              	.L131:
 640:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if: invalid pbuf", p != NULL, return ERR_ARG);
 1452              		.loc 1 640 3 is_stmt 1 discriminator 1 view .LVU470
 640:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if: invalid pbuf", p != NULL, return ERR_ARG);
 1453              		.loc 1 640 3 discriminator 1 view .LVU471
 1454 002e 184B     		ldr	r3, .L135
 1455              	.LVL142:
 640:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if: invalid pbuf", p != NULL, return ERR_ARG);
 1456              		.loc 1 640 3 is_stmt 0 discriminator 1 view .LVU472
 1457 0030 4FF42072 		mov	r2, #640
 1458              	.LVL143:
 640:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if: invalid pbuf", p != NULL, return ERR_ARG);
 1459              		.loc 1 640 3 discriminator 1 view .LVU473
 1460 0034 1749     		ldr	r1, .L135+4
 1461              	.LVL144:
 640:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if: invalid pbuf", p != NULL, return ERR_ARG);
 1462              		.loc 1 640 3 discriminator 1 view .LVU474
 1463 0036 1848     		ldr	r0, .L135+8
 1464              	.LVL145:
 640:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if: invalid pbuf", p != NULL, return ERR_ARG);
 1465              		.loc 1 640 3 discriminator 1 view .LVU475
 1466 0038 FFF7FEFF 		bl	printf
 1467              	.LVL146:
 640:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if: invalid pbuf", p != NULL, return ERR_ARG);
 1468              		.loc 1 640 3 is_stmt 1 discriminator 1 view .LVU476
 640:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if: invalid pbuf", p != NULL, return ERR_ARG);
 1469              		.loc 1 640 3 discriminator 1 view .LVU477
 1470 003c 6FF00F00 		mvn	r0, #15
 640:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if: invalid pbuf", p != NULL, return ERR_ARG);
 1471              		.loc 1 640 3 is_stmt 0 view .LVU478
 1472 0040 23E0     		b	.L122
 1473              	.LVL147:
 1474              	.L132:
 641:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if: invalid dst_ip", dst_ip != NULL, return ERR_ARG);
 1475              		.loc 1 641 3 is_stmt 1 discriminator 1 view .LVU479
 641:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if: invalid dst_ip", dst_ip != NULL, return ERR_ARG);
 1476              		.loc 1 641 3 discriminator 1 view .LVU480
 1477 0042 134B     		ldr	r3, .L135
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 52


 1478              	.LVL148:
 641:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if: invalid dst_ip", dst_ip != NULL, return ERR_ARG);
 1479              		.loc 1 641 3 is_stmt 0 discriminator 1 view .LVU481
 1480 0044 40F28122 		movw	r2, #641
 1481              	.LVL149:
 641:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if: invalid dst_ip", dst_ip != NULL, return ERR_ARG);
 1482              		.loc 1 641 3 discriminator 1 view .LVU482
 1483 0048 1449     		ldr	r1, .L135+12
 1484              	.LVL150:
 641:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if: invalid dst_ip", dst_ip != NULL, return ERR_ARG);
 1485              		.loc 1 641 3 discriminator 1 view .LVU483
 1486 004a 1348     		ldr	r0, .L135+8
 1487              	.LVL151:
 641:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if: invalid dst_ip", dst_ip != NULL, return ERR_ARG);
 1488              		.loc 1 641 3 discriminator 1 view .LVU484
 1489 004c FFF7FEFF 		bl	printf
 1490              	.LVL152:
 641:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if: invalid dst_ip", dst_ip != NULL, return ERR_ARG);
 1491              		.loc 1 641 3 is_stmt 1 discriminator 1 view .LVU485
 641:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if: invalid dst_ip", dst_ip != NULL, return ERR_ARG);
 1492              		.loc 1 641 3 discriminator 1 view .LVU486
 1493 0050 6FF00F00 		mvn	r0, #15
 641:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if: invalid dst_ip", dst_ip != NULL, return ERR_ARG);
 1494              		.loc 1 641 3 is_stmt 0 view .LVU487
 1495 0054 19E0     		b	.L122
 1496              	.LVL153:
 1497              	.L133:
 642:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if: invalid netif", netif != NULL, return ERR_ARG);
 1498              		.loc 1 642 3 is_stmt 1 discriminator 1 view .LVU488
 642:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if: invalid netif", netif != NULL, return ERR_ARG);
 1499              		.loc 1 642 3 discriminator 1 view .LVU489
 1500 0056 0E4B     		ldr	r3, .L135
 1501              	.LVL154:
 642:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if: invalid netif", netif != NULL, return ERR_ARG);
 1502              		.loc 1 642 3 is_stmt 0 discriminator 1 view .LVU490
 1503 0058 40F28222 		movw	r2, #642
 1504              	.LVL155:
 642:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if: invalid netif", netif != NULL, return ERR_ARG);
 1505              		.loc 1 642 3 discriminator 1 view .LVU491
 1506 005c 1049     		ldr	r1, .L135+16
 1507              	.LVL156:
 642:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if: invalid netif", netif != NULL, return ERR_ARG);
 1508              		.loc 1 642 3 discriminator 1 view .LVU492
 1509 005e 0E48     		ldr	r0, .L135+8
 1510              	.LVL157:
 642:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if: invalid netif", netif != NULL, return ERR_ARG);
 1511              		.loc 1 642 3 discriminator 1 view .LVU493
 1512 0060 FFF7FEFF 		bl	printf
 1513              	.LVL158:
 642:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if: invalid netif", netif != NULL, return ERR_ARG);
 1514              		.loc 1 642 3 is_stmt 1 discriminator 1 view .LVU494
 642:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if: invalid netif", netif != NULL, return ERR_ARG);
 1515              		.loc 1 642 3 discriminator 1 view .LVU495
 1516 0064 6FF00F00 		mvn	r0, #15
 642:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto_if: invalid netif", netif != NULL, return ERR_ARG);
 1517              		.loc 1 642 3 is_stmt 0 view .LVU496
 1518 0068 0FE0     		b	.L122
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 53


 1519              	.LVL159:
 1520              	.L134:
 643:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 1521              		.loc 1 643 3 is_stmt 1 discriminator 1 view .LVU497
 643:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 1522              		.loc 1 643 3 discriminator 1 view .LVU498
 1523 006a 094B     		ldr	r3, .L135
 1524              	.LVL160:
 643:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 1525              		.loc 1 643 3 is_stmt 0 discriminator 1 view .LVU499
 1526 006c 40F28322 		movw	r2, #643
 1527              	.LVL161:
 643:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 1528              		.loc 1 643 3 discriminator 1 view .LVU500
 1529 0070 0C49     		ldr	r1, .L135+20
 1530              	.LVL162:
 643:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 1531              		.loc 1 643 3 discriminator 1 view .LVU501
 1532 0072 0948     		ldr	r0, .L135+8
 1533              	.LVL163:
 643:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 1534              		.loc 1 643 3 discriminator 1 view .LVU502
 1535 0074 FFF7FEFF 		bl	printf
 1536              	.LVL164:
 643:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 1537              		.loc 1 643 3 is_stmt 1 discriminator 1 view .LVU503
 643:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 1538              		.loc 1 643 3 discriminator 1 view .LVU504
 1539 0078 6FF00F00 		mvn	r0, #15
 643:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 1540              		.loc 1 643 3 is_stmt 0 view .LVU505
 1541 007c 05E0     		b	.L122
 1542              	.LVL165:
 1543              	.L126:
 677:Middlewares/Third_Party/LwIP/src/core/udp.c ****     } else {
 1544              		.loc 1 677 7 is_stmt 1 view .LVU506
 677:Middlewares/Third_Party/LwIP/src/core/udp.c ****     } else {
 1545              		.loc 1 677 14 is_stmt 0 view .LVU507
 1546 007e 281D     		adds	r0, r5, #4
 1547              	.LVL166:
 1548              	.L128:
 692:Middlewares/Third_Party/LwIP/src/core/udp.c **** #endif /* LWIP_CHECKSUM_ON_COPY && CHECKSUM_GEN_UDP */
 1549              		.loc 1 692 3 is_stmt 1 view .LVU508
 692:Middlewares/Third_Party/LwIP/src/core/udp.c **** #endif /* LWIP_CHECKSUM_ON_COPY && CHECKSUM_GEN_UDP */
 1550              		.loc 1 692 10 is_stmt 0 view .LVU509
 1551 0080 0190     		str	r0, [sp, #4]
 1552 0082 0095     		str	r5, [sp]
 1553 0084 2046     		mov	r0, r4
 1554              	.LVL167:
 692:Middlewares/Third_Party/LwIP/src/core/udp.c **** #endif /* LWIP_CHECKSUM_ON_COPY && CHECKSUM_GEN_UDP */
 1555              		.loc 1 692 10 view .LVU510
 1556 0086 FFF7FEFF 		bl	udp_sendto_if_src
 1557              	.LVL168:
 1558              	.L122:
 694:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 1559              		.loc 1 694 1 view .LVU511
 1560 008a 03B0     		add	sp, sp, #12
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 54


 1561              	.LCFI13:
 1562              		.cfi_def_cfa_offset 20
 1563              		@ sp needed
 1564 008c F0BD     		pop	{r4, r5, r6, r7, pc}
 1565              	.LVL169:
 1566              	.L136:
 694:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 1567              		.loc 1 694 1 view .LVU512
 1568 008e 00BF     		.align	2
 1569              	.L135:
 1570 0090 00000000 		.word	.LC0
 1571 0094 00000000 		.word	.LC14
 1572 0098 50000000 		.word	.LC2
 1573 009c 1C000000 		.word	.LC15
 1574 00a0 38000000 		.word	.LC16
 1575 00a4 58000000 		.word	.LC17
 1576              		.cfi_endproc
 1577              	.LFE176:
 1579              		.section	.rodata.udp_sendto.str1.4,"aMS",%progbits,1
 1580              		.align	2
 1581              	.LC18:
 1582 0000 7564705F 		.ascii	"udp_sendto: invalid pcb\000"
 1582      73656E64 
 1582      746F3A20 
 1582      696E7661 
 1582      6C696420 
 1583              		.align	2
 1584              	.LC19:
 1585 0018 7564705F 		.ascii	"udp_sendto: invalid pbuf\000"
 1585      73656E64 
 1585      746F3A20 
 1585      696E7661 
 1585      6C696420 
 1586 0031 000000   		.align	2
 1587              	.LC20:
 1588 0034 7564705F 		.ascii	"udp_sendto: invalid dst_ip\000"
 1588      73656E64 
 1588      746F3A20 
 1588      696E7661 
 1588      6C696420 
 1589              		.section	.text.udp_sendto,"ax",%progbits
 1590              		.align	1
 1591              		.global	udp_sendto
 1592              		.syntax unified
 1593              		.thumb
 1594              		.thumb_func
 1596              	udp_sendto:
 1597              	.LVL170:
 1598              	.LFB175:
 522:Middlewares/Third_Party/LwIP/src/core/udp.c **** #if LWIP_CHECKSUM_ON_COPY && CHECKSUM_GEN_UDP
 1599              		.loc 1 522 1 is_stmt 1 view -0
 1600              		.cfi_startproc
 1601              		@ args = 0, pretend = 0, frame = 0
 1602              		@ frame_needed = 0, uses_anonymous_args = 0
 522:Middlewares/Third_Party/LwIP/src/core/udp.c **** #if LWIP_CHECKSUM_ON_COPY && CHECKSUM_GEN_UDP
 1603              		.loc 1 522 1 is_stmt 0 view .LVU514
 1604 0000 F0B5     		push	{r4, r5, r6, r7, lr}
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 55


 1605              	.LCFI14:
 1606              		.cfi_def_cfa_offset 20
 1607              		.cfi_offset 4, -20
 1608              		.cfi_offset 5, -16
 1609              		.cfi_offset 6, -12
 1610              		.cfi_offset 7, -8
 1611              		.cfi_offset 14, -4
 1612 0002 83B0     		sub	sp, sp, #12
 1613              	.LCFI15:
 1614              		.cfi_def_cfa_offset 32
 534:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 1615              		.loc 1 534 3 is_stmt 1 view .LVU515
 536:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto: invalid pbuf", p != NULL, return ERR_ARG);
 1616              		.loc 1 536 3 view .LVU516
 536:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto: invalid pbuf", p != NULL, return ERR_ARG);
 1617              		.loc 1 536 3 view .LVU517
 1618 0004 98B1     		cbz	r0, .L146
 1619 0006 0D46     		mov	r5, r1
 1620 0008 1446     		mov	r4, r2
 1621 000a 1E46     		mov	r6, r3
 1622 000c 0746     		mov	r7, r0
 536:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto: invalid pbuf", p != NULL, return ERR_ARG);
 1623              		.loc 1 536 3 discriminator 2 view .LVU518
 537:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto: invalid dst_ip", dst_ip != NULL, return ERR_ARG);
 1624              		.loc 1 537 3 view .LVU519
 537:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto: invalid dst_ip", dst_ip != NULL, return ERR_ARG);
 1625              		.loc 1 537 3 view .LVU520
 1626 000e C1B1     		cbz	r1, .L147
 537:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto: invalid dst_ip", dst_ip != NULL, return ERR_ARG);
 1627              		.loc 1 537 3 discriminator 2 view .LVU521
 538:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 1628              		.loc 1 538 3 view .LVU522
 538:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 1629              		.loc 1 538 3 view .LVU523
 1630 0010 0AB3     		cbz	r2, .L148
 538:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 1631              		.loc 1 538 3 discriminator 2 view .LVU524
 540:Middlewares/Third_Party/LwIP/src/core/udp.c ****     return ERR_VAL;
 1632              		.loc 1 540 3 view .LVU525
 544:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 1633              		.loc 1 544 58 view .LVU526
 546:Middlewares/Third_Party/LwIP/src/core/udp.c ****     netif = netif_get_by_index(pcb->netif_idx);
 1634              		.loc 1 546 3 view .LVU527
 546:Middlewares/Third_Party/LwIP/src/core/udp.c ****     netif = netif_get_by_index(pcb->netif_idx);
 1635              		.loc 1 546 10 is_stmt 0 view .LVU528
 1636 0012 007A     		ldrb	r0, [r0, #8]	@ zero_extendqisi2
 1637              	.LVL171:
 546:Middlewares/Third_Party/LwIP/src/core/udp.c ****     netif = netif_get_by_index(pcb->netif_idx);
 1638              		.loc 1 546 6 view .LVU529
 1639 0014 48B3     		cbz	r0, .L142
 547:Middlewares/Third_Party/LwIP/src/core/udp.c ****   } else {
 1640              		.loc 1 547 5 is_stmt 1 view .LVU530
 547:Middlewares/Third_Party/LwIP/src/core/udp.c ****   } else {
 1641              		.loc 1 547 13 is_stmt 0 view .LVU531
 1642 0016 FFF7FEFF 		bl	netif_get_by_index
 1643              	.LVL172:
 1644              	.L143:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 56


 589:Middlewares/Third_Party/LwIP/src/core/udp.c ****     LWIP_DEBUGF(UDP_DEBUG | LWIP_DBG_LEVEL_SERIOUS, ("udp_send: No route to "));
 1645              		.loc 1 589 3 is_stmt 1 view .LVU532
 589:Middlewares/Third_Party/LwIP/src/core/udp.c ****     LWIP_DEBUGF(UDP_DEBUG | LWIP_DBG_LEVEL_SERIOUS, ("udp_send: No route to "));
 1646              		.loc 1 589 6 is_stmt 0 view .LVU533
 1647 001a 50B3     		cbz	r0, .L144
 599:Middlewares/Third_Party/LwIP/src/core/udp.c **** #endif /* LWIP_CHECKSUM_ON_COPY && CHECKSUM_GEN_UDP */
 1648              		.loc 1 599 3 is_stmt 1 view .LVU534
 599:Middlewares/Third_Party/LwIP/src/core/udp.c **** #endif /* LWIP_CHECKSUM_ON_COPY && CHECKSUM_GEN_UDP */
 1649              		.loc 1 599 10 is_stmt 0 view .LVU535
 1650 001c 0090     		str	r0, [sp]
 1651 001e 3346     		mov	r3, r6
 1652 0020 2246     		mov	r2, r4
 1653 0022 2946     		mov	r1, r5
 1654 0024 3846     		mov	r0, r7
 1655              	.LVL173:
 599:Middlewares/Third_Party/LwIP/src/core/udp.c **** #endif /* LWIP_CHECKSUM_ON_COPY && CHECKSUM_GEN_UDP */
 1656              		.loc 1 599 10 view .LVU536
 1657 0026 FFF7FEFF 		bl	udp_sendto_if
 1658              	.LVL174:
 1659              	.L139:
 601:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 1660              		.loc 1 601 1 view .LVU537
 1661 002a 03B0     		add	sp, sp, #12
 1662              	.LCFI16:
 1663              		.cfi_remember_state
 1664              		.cfi_def_cfa_offset 20
 1665              		@ sp needed
 1666 002c F0BD     		pop	{r4, r5, r6, r7, pc}
 1667              	.LVL175:
 1668              	.L146:
 1669              	.LCFI17:
 1670              		.cfi_restore_state
 536:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto: invalid pbuf", p != NULL, return ERR_ARG);
 1671              		.loc 1 536 3 is_stmt 1 discriminator 1 view .LVU538
 536:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto: invalid pbuf", p != NULL, return ERR_ARG);
 1672              		.loc 1 536 3 discriminator 1 view .LVU539
 1673 002e 124B     		ldr	r3, .L149
 1674              	.LVL176:
 536:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto: invalid pbuf", p != NULL, return ERR_ARG);
 1675              		.loc 1 536 3 is_stmt 0 discriminator 1 view .LVU540
 1676 0030 4FF40672 		mov	r2, #536
 1677              	.LVL177:
 536:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto: invalid pbuf", p != NULL, return ERR_ARG);
 1678              		.loc 1 536 3 discriminator 1 view .LVU541
 1679 0034 1149     		ldr	r1, .L149+4
 1680              	.LVL178:
 536:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto: invalid pbuf", p != NULL, return ERR_ARG);
 1681              		.loc 1 536 3 discriminator 1 view .LVU542
 1682 0036 1248     		ldr	r0, .L149+8
 1683              	.LVL179:
 536:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto: invalid pbuf", p != NULL, return ERR_ARG);
 1684              		.loc 1 536 3 discriminator 1 view .LVU543
 1685 0038 FFF7FEFF 		bl	printf
 1686              	.LVL180:
 536:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto: invalid pbuf", p != NULL, return ERR_ARG);
 1687              		.loc 1 536 3 is_stmt 1 discriminator 1 view .LVU544
 536:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto: invalid pbuf", p != NULL, return ERR_ARG);
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 57


 1688              		.loc 1 536 3 discriminator 1 view .LVU545
 1689 003c 6FF00F00 		mvn	r0, #15
 536:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto: invalid pbuf", p != NULL, return ERR_ARG);
 1690              		.loc 1 536 3 is_stmt 0 view .LVU546
 1691 0040 F3E7     		b	.L139
 1692              	.LVL181:
 1693              	.L147:
 537:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto: invalid dst_ip", dst_ip != NULL, return ERR_ARG);
 1694              		.loc 1 537 3 is_stmt 1 discriminator 1 view .LVU547
 537:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto: invalid dst_ip", dst_ip != NULL, return ERR_ARG);
 1695              		.loc 1 537 3 discriminator 1 view .LVU548
 1696 0042 0D4B     		ldr	r3, .L149
 1697              	.LVL182:
 537:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto: invalid dst_ip", dst_ip != NULL, return ERR_ARG);
 1698              		.loc 1 537 3 is_stmt 0 discriminator 1 view .LVU549
 1699 0044 40F21922 		movw	r2, #537
 1700              	.LVL183:
 537:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto: invalid dst_ip", dst_ip != NULL, return ERR_ARG);
 1701              		.loc 1 537 3 discriminator 1 view .LVU550
 1702 0048 0E49     		ldr	r1, .L149+12
 1703              	.LVL184:
 537:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto: invalid dst_ip", dst_ip != NULL, return ERR_ARG);
 1704              		.loc 1 537 3 discriminator 1 view .LVU551
 1705 004a 0D48     		ldr	r0, .L149+8
 1706              	.LVL185:
 537:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto: invalid dst_ip", dst_ip != NULL, return ERR_ARG);
 1707              		.loc 1 537 3 discriminator 1 view .LVU552
 1708 004c FFF7FEFF 		bl	printf
 1709              	.LVL186:
 537:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto: invalid dst_ip", dst_ip != NULL, return ERR_ARG);
 1710              		.loc 1 537 3 is_stmt 1 discriminator 1 view .LVU553
 537:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto: invalid dst_ip", dst_ip != NULL, return ERR_ARG);
 1711              		.loc 1 537 3 discriminator 1 view .LVU554
 1712 0050 6FF00F00 		mvn	r0, #15
 537:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_sendto: invalid dst_ip", dst_ip != NULL, return ERR_ARG);
 1713              		.loc 1 537 3 is_stmt 0 view .LVU555
 1714 0054 E9E7     		b	.L139
 1715              	.LVL187:
 1716              	.L148:
 538:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 1717              		.loc 1 538 3 is_stmt 1 discriminator 1 view .LVU556
 538:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 1718              		.loc 1 538 3 discriminator 1 view .LVU557
 1719 0056 084B     		ldr	r3, .L149
 1720              	.LVL188:
 538:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 1721              		.loc 1 538 3 is_stmt 0 discriminator 1 view .LVU558
 1722 0058 40F21A22 		movw	r2, #538
 1723              	.LVL189:
 538:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 1724              		.loc 1 538 3 discriminator 1 view .LVU559
 1725 005c 0A49     		ldr	r1, .L149+16
 1726              	.LVL190:
 538:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 1727              		.loc 1 538 3 discriminator 1 view .LVU560
 1728 005e 0848     		ldr	r0, .L149+8
 1729              	.LVL191:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 58


 538:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 1730              		.loc 1 538 3 discriminator 1 view .LVU561
 1731 0060 FFF7FEFF 		bl	printf
 1732              	.LVL192:
 538:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 1733              		.loc 1 538 3 is_stmt 1 discriminator 1 view .LVU562
 538:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 1734              		.loc 1 538 3 discriminator 1 view .LVU563
 1735 0064 6FF00F00 		mvn	r0, #15
 538:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 1736              		.loc 1 538 3 is_stmt 0 view .LVU564
 1737 0068 DFE7     		b	.L139
 1738              	.LVL193:
 1739              	.L142:
 584:Middlewares/Third_Party/LwIP/src/core/udp.c ****     }
 1740              		.loc 1 584 7 is_stmt 1 view .LVU565
 584:Middlewares/Third_Party/LwIP/src/core/udp.c ****     }
 1741              		.loc 1 584 15 is_stmt 0 view .LVU566
 1742 006a 1046     		mov	r0, r2
 1743 006c FFF7FEFF 		bl	ip4_route
 1744              	.LVL194:
 584:Middlewares/Third_Party/LwIP/src/core/udp.c ****     }
 1745              		.loc 1 584 15 view .LVU567
 1746 0070 D3E7     		b	.L143
 1747              	.L144:
 594:Middlewares/Third_Party/LwIP/src/core/udp.c ****   }
 1748              		.loc 1 594 12 view .LVU568
 1749 0072 6FF00300 		mvn	r0, #3
 1750              	.LVL195:
 594:Middlewares/Third_Party/LwIP/src/core/udp.c ****   }
 1751              		.loc 1 594 12 view .LVU569
 1752 0076 D8E7     		b	.L139
 1753              	.L150:
 1754              		.align	2
 1755              	.L149:
 1756 0078 00000000 		.word	.LC0
 1757 007c 00000000 		.word	.LC18
 1758 0080 50000000 		.word	.LC2
 1759 0084 18000000 		.word	.LC19
 1760 0088 34000000 		.word	.LC20
 1761              		.cfi_endproc
 1762              	.LFE175:
 1764              		.section	.rodata.udp_send.str1.4,"aMS",%progbits,1
 1765              		.align	2
 1766              	.LC21:
 1767 0000 7564705F 		.ascii	"udp_send: invalid pcb\000"
 1767      73656E64 
 1767      3A20696E 
 1767      76616C69 
 1767      64207063 
 1768 0016 0000     		.align	2
 1769              	.LC22:
 1770 0018 7564705F 		.ascii	"udp_send: invalid pbuf\000"
 1770      73656E64 
 1770      3A20696E 
 1770      76616C69 
 1770      64207062 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 59


 1771              		.section	.text.udp_send,"ax",%progbits
 1772              		.align	1
 1773              		.global	udp_send
 1774              		.syntax unified
 1775              		.thumb
 1776              		.thumb_func
 1778              	udp_send:
 1779              	.LVL196:
 1780              	.LFB174:
 468:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_send: invalid pcb", pcb != NULL, return ERR_ARG);
 1781              		.loc 1 468 1 is_stmt 1 view -0
 1782              		.cfi_startproc
 1783              		@ args = 0, pretend = 0, frame = 0
 1784              		@ frame_needed = 0, uses_anonymous_args = 0
 468:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_send: invalid pcb", pcb != NULL, return ERR_ARG);
 1785              		.loc 1 468 1 is_stmt 0 view .LVU571
 1786 0000 08B5     		push	{r3, lr}
 1787              	.LCFI18:
 1788              		.cfi_def_cfa_offset 8
 1789              		.cfi_offset 3, -8
 1790              		.cfi_offset 14, -4
 469:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_send: invalid pbuf", p != NULL, return ERR_ARG);
 1791              		.loc 1 469 3 is_stmt 1 view .LVU572
 469:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_send: invalid pbuf", p != NULL, return ERR_ARG);
 1792              		.loc 1 469 3 view .LVU573
 1793 0002 28B1     		cbz	r0, .L156
 469:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_send: invalid pbuf", p != NULL, return ERR_ARG);
 1794              		.loc 1 469 3 discriminator 2 view .LVU574
 470:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 1795              		.loc 1 470 3 view .LVU575
 470:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 1796              		.loc 1 470 3 view .LVU576
 1797 0004 71B1     		cbz	r1, .L157
 470:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 1798              		.loc 1 470 3 discriminator 2 view .LVU577
 472:Middlewares/Third_Party/LwIP/src/core/udp.c ****     return ERR_VAL;
 1799              		.loc 1 472 3 view .LVU578
 477:Middlewares/Third_Party/LwIP/src/core/udp.c **** }
 1800              		.loc 1 477 3 view .LVU579
 477:Middlewares/Third_Party/LwIP/src/core/udp.c **** }
 1801              		.loc 1 477 10 is_stmt 0 view .LVU580
 1802 0006 838A     		ldrh	r3, [r0, #20]
 1803 0008 021D     		adds	r2, r0, #4
 1804 000a FFF7FEFF 		bl	udp_sendto
 1805              	.LVL197:
 1806              	.L153:
 478:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 1807              		.loc 1 478 1 view .LVU581
 1808 000e 08BD     		pop	{r3, pc}
 1809              	.LVL198:
 1810              	.L156:
 469:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_send: invalid pbuf", p != NULL, return ERR_ARG);
 1811              		.loc 1 469 3 is_stmt 1 discriminator 1 view .LVU582
 469:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_send: invalid pbuf", p != NULL, return ERR_ARG);
 1812              		.loc 1 469 3 discriminator 1 view .LVU583
 1813 0010 094B     		ldr	r3, .L158
 1814 0012 40F2D512 		movw	r2, #469
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 60


 1815 0016 0949     		ldr	r1, .L158+4
 1816              	.LVL199:
 469:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_send: invalid pbuf", p != NULL, return ERR_ARG);
 1817              		.loc 1 469 3 is_stmt 0 discriminator 1 view .LVU584
 1818 0018 0948     		ldr	r0, .L158+8
 1819              	.LVL200:
 469:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_send: invalid pbuf", p != NULL, return ERR_ARG);
 1820              		.loc 1 469 3 discriminator 1 view .LVU585
 1821 001a FFF7FEFF 		bl	printf
 1822              	.LVL201:
 469:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_send: invalid pbuf", p != NULL, return ERR_ARG);
 1823              		.loc 1 469 3 is_stmt 1 discriminator 1 view .LVU586
 469:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_send: invalid pbuf", p != NULL, return ERR_ARG);
 1824              		.loc 1 469 3 discriminator 1 view .LVU587
 1825 001e 6FF00F00 		mvn	r0, #15
 469:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_send: invalid pbuf", p != NULL, return ERR_ARG);
 1826              		.loc 1 469 3 is_stmt 0 view .LVU588
 1827 0022 F4E7     		b	.L153
 1828              	.LVL202:
 1829              	.L157:
 470:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 1830              		.loc 1 470 3 is_stmt 1 discriminator 1 view .LVU589
 470:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 1831              		.loc 1 470 3 discriminator 1 view .LVU590
 1832 0024 044B     		ldr	r3, .L158
 1833 0026 4FF4EB72 		mov	r2, #470
 1834 002a 0649     		ldr	r1, .L158+12
 1835              	.LVL203:
 470:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 1836              		.loc 1 470 3 is_stmt 0 discriminator 1 view .LVU591
 1837 002c 0448     		ldr	r0, .L158+8
 1838              	.LVL204:
 470:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 1839              		.loc 1 470 3 discriminator 1 view .LVU592
 1840 002e FFF7FEFF 		bl	printf
 1841              	.LVL205:
 470:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 1842              		.loc 1 470 3 is_stmt 1 discriminator 1 view .LVU593
 470:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 1843              		.loc 1 470 3 discriminator 1 view .LVU594
 1844 0032 6FF00F00 		mvn	r0, #15
 470:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 1845              		.loc 1 470 3 is_stmt 0 view .LVU595
 1846 0036 EAE7     		b	.L153
 1847              	.L159:
 1848              		.align	2
 1849              	.L158:
 1850 0038 00000000 		.word	.LC0
 1851 003c 00000000 		.word	.LC21
 1852 0040 50000000 		.word	.LC2
 1853 0044 18000000 		.word	.LC22
 1854              		.cfi_endproc
 1855              	.LFE174:
 1857              		.section	.text.udp_bind_netif,"ax",%progbits
 1858              		.align	1
 1859              		.global	udp_bind_netif
 1860              		.syntax unified
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 61


 1861              		.thumb
 1862              		.thumb_func
 1864              	udp_bind_netif:
 1865              	.LVL206:
 1866              	.LFB179:
1028:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
1029:Middlewares/Third_Party/LwIP/src/core/udp.c **** /**
1030:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * @ingroup udp_raw
1031:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * Bind an UDP PCB to a specific netif.
1032:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * After calling this function, all packets received via this PCB
1033:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * are guaranteed to have come in via the specified netif, and all
1034:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * outgoing packets will go out via the specified netif.
1035:Middlewares/Third_Party/LwIP/src/core/udp.c ****  *
1036:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * @param pcb UDP PCB to be bound.
1037:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * @param netif netif to bind udp pcb to. Can be NULL.
1038:Middlewares/Third_Party/LwIP/src/core/udp.c ****  *
1039:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * @see udp_disconnect()
1040:Middlewares/Third_Party/LwIP/src/core/udp.c ****  */
1041:Middlewares/Third_Party/LwIP/src/core/udp.c **** void
1042:Middlewares/Third_Party/LwIP/src/core/udp.c **** udp_bind_netif(struct udp_pcb *pcb, const struct netif *netif)
1043:Middlewares/Third_Party/LwIP/src/core/udp.c **** {
 1867              		.loc 1 1043 1 is_stmt 1 view -0
 1868              		.cfi_startproc
 1869              		@ args = 0, pretend = 0, frame = 0
 1870              		@ frame_needed = 0, uses_anonymous_args = 0
 1871              		@ link register save eliminated.
1044:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ASSERT_CORE_LOCKED();
 1872              		.loc 1 1044 28 view .LVU597
1045:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
1046:Middlewares/Third_Party/LwIP/src/core/udp.c ****   if (netif != NULL) {
 1873              		.loc 1 1046 3 view .LVU598
 1874              		.loc 1 1046 6 is_stmt 0 view .LVU599
 1875 0000 21B1     		cbz	r1, .L161
1047:Middlewares/Third_Party/LwIP/src/core/udp.c ****     pcb->netif_idx = netif_get_index(netif);
 1876              		.loc 1 1047 5 is_stmt 1 view .LVU600
 1877              		.loc 1 1047 22 is_stmt 0 view .LVU601
 1878 0002 91F83030 		ldrb	r3, [r1, #48]	@ zero_extendqisi2
 1879 0006 0133     		adds	r3, r3, #1
 1880              		.loc 1 1047 20 view .LVU602
 1881 0008 0372     		strb	r3, [r0, #8]
 1882 000a 7047     		bx	lr
 1883              	.L161:
1048:Middlewares/Third_Party/LwIP/src/core/udp.c ****   } else {
1049:Middlewares/Third_Party/LwIP/src/core/udp.c ****     pcb->netif_idx = NETIF_NO_INDEX;
 1884              		.loc 1 1049 5 is_stmt 1 view .LVU603
 1885              		.loc 1 1049 20 is_stmt 0 view .LVU604
 1886 000c 0023     		movs	r3, #0
 1887 000e 0372     		strb	r3, [r0, #8]
1050:Middlewares/Third_Party/LwIP/src/core/udp.c ****   }
1051:Middlewares/Third_Party/LwIP/src/core/udp.c **** }
 1888              		.loc 1 1051 1 view .LVU605
 1889 0010 7047     		bx	lr
 1890              		.cfi_endproc
 1891              	.LFE179:
 1893              		.section	.rodata.udp_connect.str1.4,"aMS",%progbits,1
 1894              		.align	2
 1895              	.LC23:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 62


 1896 0000 7564705F 		.ascii	"udp_connect: invalid pcb\000"
 1896      636F6E6E 
 1896      6563743A 
 1896      20696E76 
 1896      616C6964 
 1897 0019 000000   		.align	2
 1898              	.LC24:
 1899 001c 7564705F 		.ascii	"udp_connect: invalid ipaddr\000"
 1899      636F6E6E 
 1899      6563743A 
 1899      20696E76 
 1899      616C6964 
 1900              		.section	.text.udp_connect,"ax",%progbits
 1901              		.align	1
 1902              		.global	udp_connect
 1903              		.syntax unified
 1904              		.thumb
 1905              		.thumb_func
 1907              	udp_connect:
 1908              	.LVL207:
 1909              	.LFB180:
1052:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
1053:Middlewares/Third_Party/LwIP/src/core/udp.c **** /**
1054:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * @ingroup udp_raw
1055:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * Sets the remote end of the pcb. This function does not generate any
1056:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * network traffic, but only sets the remote address of the pcb.
1057:Middlewares/Third_Party/LwIP/src/core/udp.c ****  *
1058:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * @param pcb UDP PCB to be connected with remote address ipaddr and port.
1059:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * @param ipaddr remote IP address to connect with.
1060:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * @param port remote UDP port to connect with.
1061:Middlewares/Third_Party/LwIP/src/core/udp.c ****  *
1062:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * @return lwIP error code
1063:Middlewares/Third_Party/LwIP/src/core/udp.c ****  *
1064:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * ipaddr & port are expected to be in the same byte order as in the pcb.
1065:Middlewares/Third_Party/LwIP/src/core/udp.c ****  *
1066:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * The udp pcb is bound to a random local port if not already bound.
1067:Middlewares/Third_Party/LwIP/src/core/udp.c ****  *
1068:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * @see udp_disconnect()
1069:Middlewares/Third_Party/LwIP/src/core/udp.c ****  */
1070:Middlewares/Third_Party/LwIP/src/core/udp.c **** err_t
1071:Middlewares/Third_Party/LwIP/src/core/udp.c **** udp_connect(struct udp_pcb *pcb, const ip_addr_t *ipaddr, u16_t port)
1072:Middlewares/Third_Party/LwIP/src/core/udp.c **** {
 1910              		.loc 1 1072 1 is_stmt 1 view -0
 1911              		.cfi_startproc
 1912              		@ args = 0, pretend = 0, frame = 0
 1913              		@ frame_needed = 0, uses_anonymous_args = 0
 1914              		.loc 1 1072 1 is_stmt 0 view .LVU607
 1915 0000 70B5     		push	{r4, r5, r6, lr}
 1916              	.LCFI19:
 1917              		.cfi_def_cfa_offset 16
 1918              		.cfi_offset 4, -16
 1919              		.cfi_offset 5, -12
 1920              		.cfi_offset 6, -8
 1921              		.cfi_offset 14, -4
1073:Middlewares/Third_Party/LwIP/src/core/udp.c ****   struct udp_pcb *ipcb;
 1922              		.loc 1 1073 3 is_stmt 1 view .LVU608
1074:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 63


1075:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ASSERT_CORE_LOCKED();
 1923              		.loc 1 1075 28 view .LVU609
1076:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
1077:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_connect: invalid pcb", pcb != NULL, return ERR_ARG);
 1924              		.loc 1 1077 3 view .LVU610
 1925              		.loc 1 1077 3 view .LVU611
 1926 0002 A0B1     		cbz	r0, .L172
 1927 0004 0D46     		mov	r5, r1
 1928 0006 1646     		mov	r6, r2
 1929 0008 0446     		mov	r4, r0
 1930              		.loc 1 1077 3 discriminator 2 view .LVU612
1078:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_connect: invalid ipaddr", ipaddr != NULL, return ERR_ARG);
 1931              		.loc 1 1078 3 view .LVU613
 1932              		.loc 1 1078 3 view .LVU614
 1933 000a D1B1     		cbz	r1, .L173
 1934              		.loc 1 1078 3 discriminator 2 view .LVU615
1079:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
1080:Middlewares/Third_Party/LwIP/src/core/udp.c ****   if (pcb->local_port == 0) {
 1935              		.loc 1 1080 3 view .LVU616
 1936              		.loc 1 1080 10 is_stmt 0 view .LVU617
 1937 000c 428A     		ldrh	r2, [r0, #18]
 1938              	.LVL208:
 1939              		.loc 1 1080 6 view .LVU618
 1940 000e 12B3     		cbz	r2, .L174
 1941              	.LVL209:
 1942              	.L167:
1081:Middlewares/Third_Party/LwIP/src/core/udp.c ****     err_t err = udp_bind(pcb, &pcb->local_ip, pcb->local_port);
1082:Middlewares/Third_Party/LwIP/src/core/udp.c ****     if (err != ERR_OK) {
1083:Middlewares/Third_Party/LwIP/src/core/udp.c ****       return err;
1084:Middlewares/Third_Party/LwIP/src/core/udp.c ****     }
1085:Middlewares/Third_Party/LwIP/src/core/udp.c ****   }
1086:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
1087:Middlewares/Third_Party/LwIP/src/core/udp.c ****   ip_addr_set_ipaddr(&pcb->remote_ip, ipaddr);
 1943              		.loc 1 1087 3 is_stmt 1 view .LVU619
 1944              		.loc 1 1087 3 is_stmt 0 discriminator 1 view .LVU620
 1945 0010 2B68     		ldr	r3, [r5]
 1946              		.loc 1 1087 3 discriminator 4 view .LVU621
 1947 0012 6360     		str	r3, [r4, #4]
1088:Middlewares/Third_Party/LwIP/src/core/udp.c **** #if LWIP_IPV6 && LWIP_IPV6_SCOPES
1089:Middlewares/Third_Party/LwIP/src/core/udp.c ****   /* If the given IP address should have a zone but doesn't, assign one now,
1090:Middlewares/Third_Party/LwIP/src/core/udp.c ****    * using the bound address to make a more informed decision when possible. */
1091:Middlewares/Third_Party/LwIP/src/core/udp.c ****   if (IP_IS_V6(&pcb->remote_ip) &&
1092:Middlewares/Third_Party/LwIP/src/core/udp.c ****       ip6_addr_lacks_zone(ip_2_ip6(&pcb->remote_ip), IP6_UNKNOWN)) {
1093:Middlewares/Third_Party/LwIP/src/core/udp.c ****     ip6_addr_select_zone(ip_2_ip6(&pcb->remote_ip), ip_2_ip6(&pcb->local_ip));
1094:Middlewares/Third_Party/LwIP/src/core/udp.c ****   }
1095:Middlewares/Third_Party/LwIP/src/core/udp.c **** #endif /* LWIP_IPV6 && LWIP_IPV6_SCOPES */
1096:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
1097:Middlewares/Third_Party/LwIP/src/core/udp.c ****   pcb->remote_port = port;
 1948              		.loc 1 1097 3 is_stmt 1 view .LVU622
 1949              		.loc 1 1097 20 is_stmt 0 view .LVU623
 1950 0014 A682     		strh	r6, [r4, #20]	@ movhi
1098:Middlewares/Third_Party/LwIP/src/core/udp.c ****   pcb->flags |= UDP_FLAGS_CONNECTED;
 1951              		.loc 1 1098 3 is_stmt 1 view .LVU624
 1952              		.loc 1 1098 6 is_stmt 0 view .LVU625
 1953 0016 237C     		ldrb	r3, [r4, #16]	@ zero_extendqisi2
 1954              		.loc 1 1098 14 view .LVU626
 1955 0018 43F00403 		orr	r3, r3, #4
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 64


 1956 001c 2374     		strb	r3, [r4, #16]
1099:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
1100:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_DEBUGF(UDP_DEBUG | LWIP_DBG_TRACE | LWIP_DBG_STATE, ("udp_connect: connected to "));
 1957              		.loc 1 1100 91 is_stmt 1 view .LVU627
1101:Middlewares/Third_Party/LwIP/src/core/udp.c ****   ip_addr_debug_print_val(UDP_DEBUG | LWIP_DBG_TRACE | LWIP_DBG_STATE,
1102:Middlewares/Third_Party/LwIP/src/core/udp.c ****                           pcb->remote_ip);
 1958              		.loc 1 1102 42 view .LVU628
1103:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_DEBUGF(UDP_DEBUG | LWIP_DBG_TRACE | LWIP_DBG_STATE, (", port %"U16_F")\n", pcb->remote_port)
 1959              		.loc 1 1103 101 view .LVU629
1104:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
1105:Middlewares/Third_Party/LwIP/src/core/udp.c ****   /* Insert UDP PCB into the list of active UDP PCBs. */
1106:Middlewares/Third_Party/LwIP/src/core/udp.c ****   for (ipcb = udp_pcbs; ipcb != NULL; ipcb = ipcb->next) {
 1960              		.loc 1 1106 3 view .LVU630
 1961              		.loc 1 1106 13 is_stmt 0 view .LVU631
 1962 001e 144B     		ldr	r3, .L176
 1963 0020 1A68     		ldr	r2, [r3]
 1964              	.LVL210:
 1965              		.loc 1 1106 13 view .LVU632
 1966 0022 1346     		mov	r3, r2
 1967              	.LVL211:
 1968              	.L168:
 1969              		.loc 1 1106 30 is_stmt 1 discriminator 1 view .LVU633
 1970 0024 EBB1     		cbz	r3, .L175
1107:Middlewares/Third_Party/LwIP/src/core/udp.c ****     if (pcb == ipcb) {
 1971              		.loc 1 1107 5 view .LVU634
 1972              		.loc 1 1107 8 is_stmt 0 view .LVU635
 1973 0026 A342     		cmp	r3, r4
 1974 0028 20D0     		beq	.L170
1106:Middlewares/Third_Party/LwIP/src/core/udp.c ****     if (pcb == ipcb) {
 1975              		.loc 1 1106 44 is_stmt 1 discriminator 2 view .LVU636
 1976 002a DB68     		ldr	r3, [r3, #12]
 1977              	.LVL212:
1106:Middlewares/Third_Party/LwIP/src/core/udp.c ****     if (pcb == ipcb) {
 1978              		.loc 1 1106 44 is_stmt 0 discriminator 2 view .LVU637
 1979 002c FAE7     		b	.L168
 1980              	.LVL213:
 1981              	.L172:
1077:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_connect: invalid ipaddr", ipaddr != NULL, return ERR_ARG);
 1982              		.loc 1 1077 3 is_stmt 1 discriminator 1 view .LVU638
1077:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_connect: invalid ipaddr", ipaddr != NULL, return ERR_ARG);
 1983              		.loc 1 1077 3 discriminator 1 view .LVU639
 1984 002e 114B     		ldr	r3, .L176+4
 1985 0030 40F23542 		movw	r2, #1077
 1986              	.LVL214:
1077:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_connect: invalid ipaddr", ipaddr != NULL, return ERR_ARG);
 1987              		.loc 1 1077 3 is_stmt 0 discriminator 1 view .LVU640
 1988 0034 1049     		ldr	r1, .L176+8
 1989              	.LVL215:
1077:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_connect: invalid ipaddr", ipaddr != NULL, return ERR_ARG);
 1990              		.loc 1 1077 3 discriminator 1 view .LVU641
 1991 0036 1148     		ldr	r0, .L176+12
 1992              	.LVL216:
1077:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_connect: invalid ipaddr", ipaddr != NULL, return ERR_ARG);
 1993              		.loc 1 1077 3 discriminator 1 view .LVU642
 1994 0038 FFF7FEFF 		bl	printf
 1995              	.LVL217:
1077:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_connect: invalid ipaddr", ipaddr != NULL, return ERR_ARG);
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 65


 1996              		.loc 1 1077 3 is_stmt 1 discriminator 1 view .LVU643
1077:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_connect: invalid ipaddr", ipaddr != NULL, return ERR_ARG);
 1997              		.loc 1 1077 3 discriminator 1 view .LVU644
 1998 003c 6FF00F00 		mvn	r0, #15
1077:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_connect: invalid ipaddr", ipaddr != NULL, return ERR_ARG);
 1999              		.loc 1 1077 3 is_stmt 0 view .LVU645
 2000 0040 13E0     		b	.L165
 2001              	.LVL218:
 2002              	.L173:
1078:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 2003              		.loc 1 1078 3 is_stmt 1 discriminator 1 view .LVU646
1078:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 2004              		.loc 1 1078 3 discriminator 1 view .LVU647
 2005 0042 0C4B     		ldr	r3, .L176+4
 2006 0044 40F23642 		movw	r2, #1078
 2007              	.LVL219:
1078:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 2008              		.loc 1 1078 3 is_stmt 0 discriminator 1 view .LVU648
 2009 0048 0D49     		ldr	r1, .L176+16
 2010              	.LVL220:
1078:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 2011              		.loc 1 1078 3 discriminator 1 view .LVU649
 2012 004a 0C48     		ldr	r0, .L176+12
 2013              	.LVL221:
1078:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 2014              		.loc 1 1078 3 discriminator 1 view .LVU650
 2015 004c FFF7FEFF 		bl	printf
 2016              	.LVL222:
1078:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 2017              		.loc 1 1078 3 is_stmt 1 discriminator 1 view .LVU651
1078:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 2018              		.loc 1 1078 3 discriminator 1 view .LVU652
 2019 0050 6FF00F00 		mvn	r0, #15
1078:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 2020              		.loc 1 1078 3 is_stmt 0 view .LVU653
 2021 0054 09E0     		b	.L165
 2022              	.LVL223:
 2023              	.L174:
 2024              	.LBB2:
1081:Middlewares/Third_Party/LwIP/src/core/udp.c ****     if (err != ERR_OK) {
 2025              		.loc 1 1081 5 is_stmt 1 view .LVU654
1081:Middlewares/Third_Party/LwIP/src/core/udp.c ****     if (err != ERR_OK) {
 2026              		.loc 1 1081 17 is_stmt 0 view .LVU655
 2027 0056 0146     		mov	r1, r0
 2028              	.LVL224:
1081:Middlewares/Third_Party/LwIP/src/core/udp.c ****     if (err != ERR_OK) {
 2029              		.loc 1 1081 17 view .LVU656
 2030 0058 FFF7FEFF 		bl	udp_bind
 2031              	.LVL225:
1082:Middlewares/Third_Party/LwIP/src/core/udp.c ****       return err;
 2032              		.loc 1 1082 5 is_stmt 1 view .LVU657
1082:Middlewares/Third_Party/LwIP/src/core/udp.c ****       return err;
 2033              		.loc 1 1082 8 is_stmt 0 view .LVU658
 2034 005c 0028     		cmp	r0, #0
 2035 005e D7D0     		beq	.L167
 2036 0060 03E0     		b	.L165
 2037              	.LVL226:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 66


 2038              	.L175:
1082:Middlewares/Third_Party/LwIP/src/core/udp.c ****       return err;
 2039              		.loc 1 1082 8 view .LVU659
 2040              	.LBE2:
1108:Middlewares/Third_Party/LwIP/src/core/udp.c ****       /* already on the list, just return */
1109:Middlewares/Third_Party/LwIP/src/core/udp.c ****       return ERR_OK;
1110:Middlewares/Third_Party/LwIP/src/core/udp.c ****     }
1111:Middlewares/Third_Party/LwIP/src/core/udp.c ****   }
1112:Middlewares/Third_Party/LwIP/src/core/udp.c ****   /* PCB not yet on the list, add PCB now */
1113:Middlewares/Third_Party/LwIP/src/core/udp.c ****   pcb->next = udp_pcbs;
 2041              		.loc 1 1113 3 is_stmt 1 view .LVU660
 2042              		.loc 1 1113 13 is_stmt 0 view .LVU661
 2043 0062 E260     		str	r2, [r4, #12]
1114:Middlewares/Third_Party/LwIP/src/core/udp.c ****   udp_pcbs = pcb;
 2044              		.loc 1 1114 3 is_stmt 1 view .LVU662
 2045              		.loc 1 1114 12 is_stmt 0 view .LVU663
 2046 0064 024B     		ldr	r3, .L176
 2047              	.LVL227:
 2048              		.loc 1 1114 12 view .LVU664
 2049 0066 1C60     		str	r4, [r3]
1115:Middlewares/Third_Party/LwIP/src/core/udp.c ****   return ERR_OK;
 2050              		.loc 1 1115 3 is_stmt 1 view .LVU665
 2051              		.loc 1 1115 10 is_stmt 0 view .LVU666
 2052 0068 0020     		movs	r0, #0
 2053              	.LVL228:
 2054              	.L165:
1116:Middlewares/Third_Party/LwIP/src/core/udp.c **** }
 2055              		.loc 1 1116 1 view .LVU667
 2056 006a 70BD     		pop	{r4, r5, r6, pc}
 2057              	.LVL229:
 2058              	.L170:
1109:Middlewares/Third_Party/LwIP/src/core/udp.c ****     }
 2059              		.loc 1 1109 14 view .LVU668
 2060 006c 0020     		movs	r0, #0
 2061 006e FCE7     		b	.L165
 2062              	.L177:
 2063              		.align	2
 2064              	.L176:
 2065 0070 00000000 		.word	udp_pcbs
 2066 0074 00000000 		.word	.LC0
 2067 0078 00000000 		.word	.LC23
 2068 007c 50000000 		.word	.LC2
 2069 0080 1C000000 		.word	.LC24
 2070              		.cfi_endproc
 2071              	.LFE180:
 2073              		.section	.rodata.udp_disconnect.str1.4,"aMS",%progbits,1
 2074              		.align	2
 2075              	.LC25:
 2076 0000 7564705F 		.ascii	"udp_disconnect: invalid pcb\000"
 2076      64697363 
 2076      6F6E6E65 
 2076      63743A20 
 2076      696E7661 
 2077              		.section	.text.udp_disconnect,"ax",%progbits
 2078              		.align	1
 2079              		.global	udp_disconnect
 2080              		.syntax unified
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 67


 2081              		.thumb
 2082              		.thumb_func
 2084              	udp_disconnect:
 2085              	.LVL230:
 2086              	.LFB181:
1117:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
1118:Middlewares/Third_Party/LwIP/src/core/udp.c **** /**
1119:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * @ingroup udp_raw
1120:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * Remove the remote end of the pcb. This function does not generate
1121:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * any network traffic, but only removes the remote address of the pcb.
1122:Middlewares/Third_Party/LwIP/src/core/udp.c ****  *
1123:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * @param pcb the udp pcb to disconnect.
1124:Middlewares/Third_Party/LwIP/src/core/udp.c ****  */
1125:Middlewares/Third_Party/LwIP/src/core/udp.c **** void
1126:Middlewares/Third_Party/LwIP/src/core/udp.c **** udp_disconnect(struct udp_pcb *pcb)
1127:Middlewares/Third_Party/LwIP/src/core/udp.c **** {
 2087              		.loc 1 1127 1 is_stmt 1 view -0
 2088              		.cfi_startproc
 2089              		@ args = 0, pretend = 0, frame = 0
 2090              		@ frame_needed = 0, uses_anonymous_args = 0
 2091              		.loc 1 1127 1 is_stmt 0 view .LVU670
 2092 0000 08B5     		push	{r3, lr}
 2093              	.LCFI20:
 2094              		.cfi_def_cfa_offset 8
 2095              		.cfi_offset 3, -8
 2096              		.cfi_offset 14, -4
1128:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ASSERT_CORE_LOCKED();
 2097              		.loc 1 1128 28 is_stmt 1 view .LVU671
1129:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
1130:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_disconnect: invalid pcb", pcb != NULL, return);
 2098              		.loc 1 1130 3 view .LVU672
 2099              		.loc 1 1130 3 view .LVU673
 2100 0002 40B1     		cbz	r0, .L182
 2101              		.loc 1 1130 3 discriminator 2 view .LVU674
1131:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
1132:Middlewares/Third_Party/LwIP/src/core/udp.c ****   /* reset remote address association */
1133:Middlewares/Third_Party/LwIP/src/core/udp.c **** #if LWIP_IPV4 && LWIP_IPV6
1134:Middlewares/Third_Party/LwIP/src/core/udp.c ****   if (IP_IS_ANY_TYPE_VAL(pcb->local_ip)) {
1135:Middlewares/Third_Party/LwIP/src/core/udp.c ****     ip_addr_copy(pcb->remote_ip, *IP_ANY_TYPE);
1136:Middlewares/Third_Party/LwIP/src/core/udp.c ****   } else {
1137:Middlewares/Third_Party/LwIP/src/core/udp.c **** #endif
1138:Middlewares/Third_Party/LwIP/src/core/udp.c ****     ip_addr_set_any(IP_IS_V6_VAL(pcb->remote_ip), &pcb->remote_ip);
 2102              		.loc 1 1138 5 view .LVU675
 2103 0004 0022     		movs	r2, #0
 2104 0006 4260     		str	r2, [r0, #4]
1139:Middlewares/Third_Party/LwIP/src/core/udp.c **** #if LWIP_IPV4 && LWIP_IPV6
1140:Middlewares/Third_Party/LwIP/src/core/udp.c ****   }
1141:Middlewares/Third_Party/LwIP/src/core/udp.c **** #endif
1142:Middlewares/Third_Party/LwIP/src/core/udp.c ****   pcb->remote_port = 0;
 2105              		.loc 1 1142 3 view .LVU676
 2106              		.loc 1 1142 20 is_stmt 0 view .LVU677
 2107 0008 8282     		strh	r2, [r0, #20]	@ movhi
1143:Middlewares/Third_Party/LwIP/src/core/udp.c ****   pcb->netif_idx = NETIF_NO_INDEX;
 2108              		.loc 1 1143 3 is_stmt 1 view .LVU678
 2109              		.loc 1 1143 18 is_stmt 0 view .LVU679
 2110 000a 0272     		strb	r2, [r0, #8]
1144:Middlewares/Third_Party/LwIP/src/core/udp.c ****   /* mark PCB as unconnected */
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 68


1145:Middlewares/Third_Party/LwIP/src/core/udp.c ****   udp_clear_flags(pcb, UDP_FLAGS_CONNECTED);
 2111              		.loc 1 1145 3 is_stmt 1 view .LVU680
 2112              		.loc 1 1145 3 view .LVU681
 2113 000c 027C     		ldrb	r2, [r0, #16]	@ zero_extendqisi2
 2114 000e 02F0FB02 		and	r2, r2, #251
 2115 0012 0274     		strb	r2, [r0, #16]
 2116              		.loc 1 1145 3 discriminator 1 view .LVU682
 2117              	.LVL231:
 2118              	.L178:
1146:Middlewares/Third_Party/LwIP/src/core/udp.c **** }
 2119              		.loc 1 1146 1 is_stmt 0 view .LVU683
 2120 0014 08BD     		pop	{r3, pc}
 2121              	.LVL232:
 2122              	.L182:
1130:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 2123              		.loc 1 1130 3 is_stmt 1 discriminator 1 view .LVU684
1130:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 2124              		.loc 1 1130 3 discriminator 1 view .LVU685
 2125 0016 044B     		ldr	r3, .L183
 2126 0018 40F26A42 		movw	r2, #1130
 2127 001c 0349     		ldr	r1, .L183+4
 2128 001e 0448     		ldr	r0, .L183+8
 2129              	.LVL233:
1130:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 2130              		.loc 1 1130 3 is_stmt 0 discriminator 1 view .LVU686
 2131 0020 FFF7FEFF 		bl	printf
 2132              	.LVL234:
1130:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 2133              		.loc 1 1130 3 is_stmt 1 discriminator 1 view .LVU687
1130:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 2134              		.loc 1 1130 3 discriminator 1 view .LVU688
1130:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 2135              		.loc 1 1130 3 is_stmt 0 view .LVU689
 2136 0024 F6E7     		b	.L178
 2137              	.L184:
 2138 0026 00BF     		.align	2
 2139              	.L183:
 2140 0028 00000000 		.word	.LC0
 2141 002c 00000000 		.word	.LC25
 2142 0030 50000000 		.word	.LC2
 2143              		.cfi_endproc
 2144              	.LFE181:
 2146              		.section	.rodata.udp_recv.str1.4,"aMS",%progbits,1
 2147              		.align	2
 2148              	.LC26:
 2149 0000 7564705F 		.ascii	"udp_recv: invalid pcb\000"
 2149      72656376 
 2149      3A20696E 
 2149      76616C69 
 2149      64207063 
 2150              		.section	.text.udp_recv,"ax",%progbits
 2151              		.align	1
 2152              		.global	udp_recv
 2153              		.syntax unified
 2154              		.thumb
 2155              		.thumb_func
 2157              	udp_recv:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 69


 2158              	.LVL235:
 2159              	.LFB182:
1147:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
1148:Middlewares/Third_Party/LwIP/src/core/udp.c **** /**
1149:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * @ingroup udp_raw
1150:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * Set a receive callback for a UDP PCB.
1151:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * This callback will be called when receiving a datagram for the pcb.
1152:Middlewares/Third_Party/LwIP/src/core/udp.c ****  *
1153:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * @param pcb the pcb for which to set the recv callback
1154:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * @param recv function pointer of the callback function
1155:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * @param recv_arg additional argument to pass to the callback function
1156:Middlewares/Third_Party/LwIP/src/core/udp.c ****  */
1157:Middlewares/Third_Party/LwIP/src/core/udp.c **** void
1158:Middlewares/Third_Party/LwIP/src/core/udp.c **** udp_recv(struct udp_pcb *pcb, udp_recv_fn recv, void *recv_arg)
1159:Middlewares/Third_Party/LwIP/src/core/udp.c **** {
 2160              		.loc 1 1159 1 is_stmt 1 view -0
 2161              		.cfi_startproc
 2162              		@ args = 0, pretend = 0, frame = 0
 2163              		@ frame_needed = 0, uses_anonymous_args = 0
 2164              		.loc 1 1159 1 is_stmt 0 view .LVU691
 2165 0000 08B5     		push	{r3, lr}
 2166              	.LCFI21:
 2167              		.cfi_def_cfa_offset 8
 2168              		.cfi_offset 3, -8
 2169              		.cfi_offset 14, -4
1160:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ASSERT_CORE_LOCKED();
 2170              		.loc 1 1160 28 is_stmt 1 view .LVU692
1161:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
1162:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_recv: invalid pcb", pcb != NULL, return);
 2171              		.loc 1 1162 3 view .LVU693
 2172              		.loc 1 1162 3 view .LVU694
 2173 0002 10B1     		cbz	r0, .L189
 2174              		.loc 1 1162 3 discriminator 2 view .LVU695
1163:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
1164:Middlewares/Third_Party/LwIP/src/core/udp.c ****   /* remember recv() callback and user data */
1165:Middlewares/Third_Party/LwIP/src/core/udp.c ****   pcb->recv = recv;
 2175              		.loc 1 1165 3 view .LVU696
 2176              		.loc 1 1165 13 is_stmt 0 view .LVU697
 2177 0004 8161     		str	r1, [r0, #24]
1166:Middlewares/Third_Party/LwIP/src/core/udp.c ****   pcb->recv_arg = recv_arg;
 2178              		.loc 1 1166 3 is_stmt 1 view .LVU698
 2179              		.loc 1 1166 17 is_stmt 0 view .LVU699
 2180 0006 C261     		str	r2, [r0, #28]
 2181              	.LVL236:
 2182              	.L185:
1167:Middlewares/Third_Party/LwIP/src/core/udp.c **** }
 2183              		.loc 1 1167 1 view .LVU700
 2184 0008 08BD     		pop	{r3, pc}
 2185              	.LVL237:
 2186              	.L189:
1162:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 2187              		.loc 1 1162 3 is_stmt 1 discriminator 1 view .LVU701
1162:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 2188              		.loc 1 1162 3 discriminator 1 view .LVU702
 2189 000a 044B     		ldr	r3, .L190
 2190 000c 40F28A42 		movw	r2, #1162
 2191              	.LVL238:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 70


1162:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 2192              		.loc 1 1162 3 is_stmt 0 discriminator 1 view .LVU703
 2193 0010 0349     		ldr	r1, .L190+4
 2194              	.LVL239:
1162:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 2195              		.loc 1 1162 3 discriminator 1 view .LVU704
 2196 0012 0448     		ldr	r0, .L190+8
 2197              	.LVL240:
1162:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 2198              		.loc 1 1162 3 discriminator 1 view .LVU705
 2199 0014 FFF7FEFF 		bl	printf
 2200              	.LVL241:
1162:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 2201              		.loc 1 1162 3 is_stmt 1 discriminator 1 view .LVU706
1162:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 2202              		.loc 1 1162 3 discriminator 1 view .LVU707
1162:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 2203              		.loc 1 1162 3 is_stmt 0 view .LVU708
 2204 0018 F6E7     		b	.L185
 2205              	.L191:
 2206 001a 00BF     		.align	2
 2207              	.L190:
 2208 001c 00000000 		.word	.LC0
 2209 0020 00000000 		.word	.LC26
 2210 0024 50000000 		.word	.LC2
 2211              		.cfi_endproc
 2212              	.LFE182:
 2214              		.section	.rodata.udp_remove.str1.4,"aMS",%progbits,1
 2215              		.align	2
 2216              	.LC27:
 2217 0000 7564705F 		.ascii	"udp_remove: invalid pcb\000"
 2217      72656D6F 
 2217      76653A20 
 2217      696E7661 
 2217      6C696420 
 2218              		.section	.text.udp_remove,"ax",%progbits
 2219              		.align	1
 2220              		.global	udp_remove
 2221              		.syntax unified
 2222              		.thumb
 2223              		.thumb_func
 2225              	udp_remove:
 2226              	.LVL242:
 2227              	.LFB183:
1168:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
1169:Middlewares/Third_Party/LwIP/src/core/udp.c **** /**
1170:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * @ingroup udp_raw
1171:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * Removes and deallocates the pcb.  
1172:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * 
1173:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * @param pcb UDP PCB to be removed. The PCB is removed from the list of
1174:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * UDP PCB's and the data structure is freed from memory.
1175:Middlewares/Third_Party/LwIP/src/core/udp.c ****  *
1176:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * @see udp_new()
1177:Middlewares/Third_Party/LwIP/src/core/udp.c ****  */
1178:Middlewares/Third_Party/LwIP/src/core/udp.c **** void
1179:Middlewares/Third_Party/LwIP/src/core/udp.c **** udp_remove(struct udp_pcb *pcb)
1180:Middlewares/Third_Party/LwIP/src/core/udp.c **** {
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 71


 2228              		.loc 1 1180 1 is_stmt 1 view -0
 2229              		.cfi_startproc
 2230              		@ args = 0, pretend = 0, frame = 0
 2231              		@ frame_needed = 0, uses_anonymous_args = 0
 2232              		.loc 1 1180 1 is_stmt 0 view .LVU710
 2233 0000 08B5     		push	{r3, lr}
 2234              	.LCFI22:
 2235              		.cfi_def_cfa_offset 8
 2236              		.cfi_offset 3, -8
 2237              		.cfi_offset 14, -4
1181:Middlewares/Third_Party/LwIP/src/core/udp.c ****   struct udp_pcb *pcb2;
 2238              		.loc 1 1181 3 is_stmt 1 view .LVU711
1182:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
1183:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ASSERT_CORE_LOCKED();
 2239              		.loc 1 1183 28 view .LVU712
1184:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
1185:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ERROR("udp_remove: invalid pcb", pcb != NULL, return);
 2240              		.loc 1 1185 3 view .LVU713
 2241              		.loc 1 1185 3 view .LVU714
 2242 0002 50B1     		cbz	r0, .L201
 2243 0004 0146     		mov	r1, r0
 2244              		.loc 1 1185 3 discriminator 2 view .LVU715
1186:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
1187:Middlewares/Third_Party/LwIP/src/core/udp.c ****   mib2_udp_unbind(pcb);
 2245              		.loc 1 1187 23 view .LVU716
1188:Middlewares/Third_Party/LwIP/src/core/udp.c ****   /* pcb to be removed is first in list? */
1189:Middlewares/Third_Party/LwIP/src/core/udp.c ****   if (udp_pcbs == pcb) {
 2246              		.loc 1 1189 3 view .LVU717
 2247              		.loc 1 1189 16 is_stmt 0 view .LVU718
 2248 0006 0F4B     		ldr	r3, .L203
 2249 0008 1A68     		ldr	r2, [r3]
 2250              		.loc 1 1189 6 view .LVU719
 2251 000a 8242     		cmp	r2, r0
 2252 000c 11D1     		bne	.L195
1190:Middlewares/Third_Party/LwIP/src/core/udp.c ****     /* make list start at 2nd pcb */
1191:Middlewares/Third_Party/LwIP/src/core/udp.c ****     udp_pcbs = udp_pcbs->next;
 2253              		.loc 1 1191 5 is_stmt 1 view .LVU720
 2254              		.loc 1 1191 24 is_stmt 0 view .LVU721
 2255 000e D268     		ldr	r2, [r2, #12]
 2256              		.loc 1 1191 14 view .LVU722
 2257 0010 1A60     		str	r2, [r3]
 2258              	.L196:
1192:Middlewares/Third_Party/LwIP/src/core/udp.c ****     /* pcb not 1st in list */
1193:Middlewares/Third_Party/LwIP/src/core/udp.c ****   } else {
1194:Middlewares/Third_Party/LwIP/src/core/udp.c ****     for (pcb2 = udp_pcbs; pcb2 != NULL; pcb2 = pcb2->next) {
1195:Middlewares/Third_Party/LwIP/src/core/udp.c ****       /* find pcb in udp_pcbs list */
1196:Middlewares/Third_Party/LwIP/src/core/udp.c ****       if (pcb2->next != NULL && pcb2->next == pcb) {
1197:Middlewares/Third_Party/LwIP/src/core/udp.c ****         /* remove pcb from list */
1198:Middlewares/Third_Party/LwIP/src/core/udp.c ****         pcb2->next = pcb->next;
1199:Middlewares/Third_Party/LwIP/src/core/udp.c ****         break;
1200:Middlewares/Third_Party/LwIP/src/core/udp.c ****       }
1201:Middlewares/Third_Party/LwIP/src/core/udp.c ****     }
1202:Middlewares/Third_Party/LwIP/src/core/udp.c ****   }
1203:Middlewares/Third_Party/LwIP/src/core/udp.c ****   memp_free(MEMP_UDP_PCB, pcb);
 2259              		.loc 1 1203 3 is_stmt 1 view .LVU723
 2260 0012 0020     		movs	r0, #0
 2261              	.LVL243:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 72


 2262              		.loc 1 1203 3 is_stmt 0 view .LVU724
 2263 0014 FFF7FEFF 		bl	memp_free
 2264              	.LVL244:
 2265              	.L192:
1204:Middlewares/Third_Party/LwIP/src/core/udp.c **** }
 2266              		.loc 1 1204 1 view .LVU725
 2267 0018 08BD     		pop	{r3, pc}
 2268              	.LVL245:
 2269              	.L201:
1185:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 2270              		.loc 1 1185 3 is_stmt 1 discriminator 1 view .LVU726
1185:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 2271              		.loc 1 1185 3 discriminator 1 view .LVU727
 2272 001a 0B4B     		ldr	r3, .L203+4
 2273 001c 40F2A142 		movw	r2, #1185
 2274 0020 0A49     		ldr	r1, .L203+8
 2275 0022 0B48     		ldr	r0, .L203+12
 2276              	.LVL246:
1185:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 2277              		.loc 1 1185 3 is_stmt 0 discriminator 1 view .LVU728
 2278 0024 FFF7FEFF 		bl	printf
 2279              	.LVL247:
1185:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 2280              		.loc 1 1185 3 is_stmt 1 discriminator 1 view .LVU729
1185:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 2281              		.loc 1 1185 3 discriminator 1 view .LVU730
1185:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
 2282              		.loc 1 1185 3 is_stmt 0 view .LVU731
 2283 0028 F6E7     		b	.L192
 2284              	.LVL248:
 2285              	.L202:
1198:Middlewares/Third_Party/LwIP/src/core/udp.c ****         break;
 2286              		.loc 1 1198 9 is_stmt 1 view .LVU732
1198:Middlewares/Third_Party/LwIP/src/core/udp.c ****         break;
 2287              		.loc 1 1198 25 is_stmt 0 view .LVU733
 2288 002a CB68     		ldr	r3, [r1, #12]
1198:Middlewares/Third_Party/LwIP/src/core/udp.c ****         break;
 2289              		.loc 1 1198 20 view .LVU734
 2290 002c D360     		str	r3, [r2, #12]
1199:Middlewares/Third_Party/LwIP/src/core/udp.c ****       }
 2291              		.loc 1 1199 9 is_stmt 1 view .LVU735
 2292 002e F0E7     		b	.L196
 2293              	.L198:
1199:Middlewares/Third_Party/LwIP/src/core/udp.c ****       }
 2294              		.loc 1 1199 9 is_stmt 0 view .LVU736
 2295 0030 1A46     		mov	r2, r3
 2296              	.LVL249:
 2297              	.L195:
1194:Middlewares/Third_Party/LwIP/src/core/udp.c ****       /* find pcb in udp_pcbs list */
 2298              		.loc 1 1194 32 is_stmt 1 discriminator 1 view .LVU737
 2299 0032 002A     		cmp	r2, #0
 2300 0034 EDD0     		beq	.L196
1196:Middlewares/Third_Party/LwIP/src/core/udp.c ****         /* remove pcb from list */
 2301              		.loc 1 1196 7 view .LVU738
1196:Middlewares/Third_Party/LwIP/src/core/udp.c ****         /* remove pcb from list */
 2302              		.loc 1 1196 15 is_stmt 0 view .LVU739
 2303 0036 D368     		ldr	r3, [r2, #12]
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 73


1196:Middlewares/Third_Party/LwIP/src/core/udp.c ****         /* remove pcb from list */
 2304              		.loc 1 1196 10 view .LVU740
 2305 0038 002B     		cmp	r3, #0
 2306 003a F9D0     		beq	.L198
1196:Middlewares/Third_Party/LwIP/src/core/udp.c ****         /* remove pcb from list */
 2307              		.loc 1 1196 30 discriminator 1 view .LVU741
 2308 003c 8B42     		cmp	r3, r1
 2309 003e F4D0     		beq	.L202
 2310 0040 1A46     		mov	r2, r3
 2311              	.LVL250:
1196:Middlewares/Third_Party/LwIP/src/core/udp.c ****         /* remove pcb from list */
 2312              		.loc 1 1196 30 discriminator 1 view .LVU742
 2313 0042 F6E7     		b	.L195
 2314              	.L204:
 2315              		.align	2
 2316              	.L203:
 2317 0044 00000000 		.word	udp_pcbs
 2318 0048 00000000 		.word	.LC0
 2319 004c 00000000 		.word	.LC27
 2320 0050 50000000 		.word	.LC2
 2321              		.cfi_endproc
 2322              	.LFE183:
 2324              		.section	.text.udp_new,"ax",%progbits
 2325              		.align	1
 2326              		.global	udp_new
 2327              		.syntax unified
 2328              		.thumb
 2329              		.thumb_func
 2331              	udp_new:
 2332              	.LFB184:
1205:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
1206:Middlewares/Third_Party/LwIP/src/core/udp.c **** /**
1207:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * @ingroup udp_raw
1208:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * Creates a new UDP pcb which can be used for UDP communication. The
1209:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * pcb is not active until it has either been bound to a local address
1210:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * or connected to a remote address.
1211:Middlewares/Third_Party/LwIP/src/core/udp.c ****  *
1212:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * @return The UDP PCB which was created. NULL if the PCB data structure
1213:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * could not be allocated.
1214:Middlewares/Third_Party/LwIP/src/core/udp.c ****  *
1215:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * @see udp_remove()
1216:Middlewares/Third_Party/LwIP/src/core/udp.c ****  */
1217:Middlewares/Third_Party/LwIP/src/core/udp.c **** struct udp_pcb *
1218:Middlewares/Third_Party/LwIP/src/core/udp.c **** udp_new(void)
1219:Middlewares/Third_Party/LwIP/src/core/udp.c **** {
 2333              		.loc 1 1219 1 is_stmt 1 view -0
 2334              		.cfi_startproc
 2335              		@ args = 0, pretend = 0, frame = 0
 2336              		@ frame_needed = 0, uses_anonymous_args = 0
 2337 0000 10B5     		push	{r4, lr}
 2338              	.LCFI23:
 2339              		.cfi_def_cfa_offset 8
 2340              		.cfi_offset 4, -8
 2341              		.cfi_offset 14, -4
1220:Middlewares/Third_Party/LwIP/src/core/udp.c ****   struct udp_pcb *pcb;
 2342              		.loc 1 1220 3 view .LVU744
1221:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 74


1222:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ASSERT_CORE_LOCKED();
 2343              		.loc 1 1222 28 view .LVU745
1223:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
1224:Middlewares/Third_Party/LwIP/src/core/udp.c ****   pcb = (struct udp_pcb *)memp_malloc(MEMP_UDP_PCB);
 2344              		.loc 1 1224 3 view .LVU746
 2345              		.loc 1 1224 27 is_stmt 0 view .LVU747
 2346 0002 0020     		movs	r0, #0
 2347 0004 FFF7FEFF 		bl	memp_malloc
 2348              	.LVL251:
1225:Middlewares/Third_Party/LwIP/src/core/udp.c ****   /* could allocate UDP PCB? */
1226:Middlewares/Third_Party/LwIP/src/core/udp.c ****   if (pcb != NULL) {
 2349              		.loc 1 1226 3 is_stmt 1 view .LVU748
 2350              		.loc 1 1226 6 is_stmt 0 view .LVU749
 2351 0008 0446     		mov	r4, r0
 2352 000a 28B1     		cbz	r0, .L205
1227:Middlewares/Third_Party/LwIP/src/core/udp.c ****     /* UDP Lite: by initializing to all zeroes, chksum_len is set to 0
1228:Middlewares/Third_Party/LwIP/src/core/udp.c ****      * which means checksum is generated over the whole datagram per default
1229:Middlewares/Third_Party/LwIP/src/core/udp.c ****      * (recommended as default by RFC 3828). */
1230:Middlewares/Third_Party/LwIP/src/core/udp.c ****     /* initialize PCB to all zeroes */
1231:Middlewares/Third_Party/LwIP/src/core/udp.c ****     memset(pcb, 0, sizeof(struct udp_pcb));
 2353              		.loc 1 1231 5 is_stmt 1 view .LVU750
 2354 000c 2022     		movs	r2, #32
 2355 000e 0021     		movs	r1, #0
 2356 0010 FFF7FEFF 		bl	memset
 2357              	.LVL252:
1232:Middlewares/Third_Party/LwIP/src/core/udp.c ****     pcb->ttl = UDP_TTL;
 2358              		.loc 1 1232 5 view .LVU751
 2359              		.loc 1 1232 14 is_stmt 0 view .LVU752
 2360 0014 FF23     		movs	r3, #255
 2361 0016 E372     		strb	r3, [r4, #11]
1233:Middlewares/Third_Party/LwIP/src/core/udp.c **** #if LWIP_MULTICAST_TX_OPTIONS
1234:Middlewares/Third_Party/LwIP/src/core/udp.c ****     udp_set_multicast_ttl(pcb, UDP_TTL);
1235:Middlewares/Third_Party/LwIP/src/core/udp.c **** #endif /* LWIP_MULTICAST_TX_OPTIONS */
1236:Middlewares/Third_Party/LwIP/src/core/udp.c ****   }
1237:Middlewares/Third_Party/LwIP/src/core/udp.c ****   return pcb;
 2362              		.loc 1 1237 3 is_stmt 1 view .LVU753
 2363              	.L205:
1238:Middlewares/Third_Party/LwIP/src/core/udp.c **** }
 2364              		.loc 1 1238 1 is_stmt 0 view .LVU754
 2365 0018 2046     		mov	r0, r4
 2366 001a 10BD     		pop	{r4, pc}
 2367              		.loc 1 1238 1 view .LVU755
 2368              		.cfi_endproc
 2369              	.LFE184:
 2371              		.section	.text.udp_new_ip_type,"ax",%progbits
 2372              		.align	1
 2373              		.global	udp_new_ip_type
 2374              		.syntax unified
 2375              		.thumb
 2376              		.thumb_func
 2378              	udp_new_ip_type:
 2379              	.LVL253:
 2380              	.LFB185:
1239:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
1240:Middlewares/Third_Party/LwIP/src/core/udp.c **** /**
1241:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * @ingroup udp_raw
1242:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * Create a UDP PCB for specific IP type.
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 75


1243:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * The pcb is not active until it has either been bound to a local address
1244:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * or connected to a remote address.
1245:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * 
1246:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * @param type IP address type, see @ref lwip_ip_addr_type definitions.
1247:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * If you want to listen to IPv4 and IPv6 (dual-stack) packets,
1248:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * supply @ref IPADDR_TYPE_ANY as argument and bind to @ref IP_ANY_TYPE.
1249:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * @return The UDP PCB which was created. NULL if the PCB data structure
1250:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * could not be allocated.
1251:Middlewares/Third_Party/LwIP/src/core/udp.c ****  *
1252:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * @see udp_remove()
1253:Middlewares/Third_Party/LwIP/src/core/udp.c ****  */
1254:Middlewares/Third_Party/LwIP/src/core/udp.c **** struct udp_pcb *
1255:Middlewares/Third_Party/LwIP/src/core/udp.c **** udp_new_ip_type(u8_t type)
1256:Middlewares/Third_Party/LwIP/src/core/udp.c **** {
 2381              		.loc 1 1256 1 is_stmt 1 view -0
 2382              		.cfi_startproc
 2383              		@ args = 0, pretend = 0, frame = 0
 2384              		@ frame_needed = 0, uses_anonymous_args = 0
 2385              		.loc 1 1256 1 is_stmt 0 view .LVU757
 2386 0000 08B5     		push	{r3, lr}
 2387              	.LCFI24:
 2388              		.cfi_def_cfa_offset 8
 2389              		.cfi_offset 3, -8
 2390              		.cfi_offset 14, -4
1257:Middlewares/Third_Party/LwIP/src/core/udp.c ****   struct udp_pcb *pcb;
 2391              		.loc 1 1257 3 is_stmt 1 view .LVU758
1258:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
1259:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_ASSERT_CORE_LOCKED();
 2392              		.loc 1 1259 28 view .LVU759
1260:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
1261:Middlewares/Third_Party/LwIP/src/core/udp.c ****   pcb = udp_new();
 2393              		.loc 1 1261 3 view .LVU760
 2394              		.loc 1 1261 9 is_stmt 0 view .LVU761
 2395 0002 FFF7FEFF 		bl	udp_new
 2396              	.LVL254:
1262:Middlewares/Third_Party/LwIP/src/core/udp.c **** #if LWIP_IPV4 && LWIP_IPV6
1263:Middlewares/Third_Party/LwIP/src/core/udp.c ****   if (pcb != NULL) {
1264:Middlewares/Third_Party/LwIP/src/core/udp.c ****     IP_SET_TYPE_VAL(pcb->local_ip,  type);
1265:Middlewares/Third_Party/LwIP/src/core/udp.c ****     IP_SET_TYPE_VAL(pcb->remote_ip, type);
1266:Middlewares/Third_Party/LwIP/src/core/udp.c ****   }
1267:Middlewares/Third_Party/LwIP/src/core/udp.c **** #else
1268:Middlewares/Third_Party/LwIP/src/core/udp.c ****   LWIP_UNUSED_ARG(type);
 2397              		.loc 1 1268 3 is_stmt 1 view .LVU762
1269:Middlewares/Third_Party/LwIP/src/core/udp.c **** #endif /* LWIP_IPV4 && LWIP_IPV6 */
1270:Middlewares/Third_Party/LwIP/src/core/udp.c ****   return pcb;
 2398              		.loc 1 1270 3 view .LVU763
1271:Middlewares/Third_Party/LwIP/src/core/udp.c **** }
 2399              		.loc 1 1271 1 is_stmt 0 view .LVU764
 2400 0006 08BD     		pop	{r3, pc}
 2401              		.cfi_endproc
 2402              	.LFE185:
 2404              		.section	.text.udp_netif_ip_addr_changed,"ax",%progbits
 2405              		.align	1
 2406              		.global	udp_netif_ip_addr_changed
 2407              		.syntax unified
 2408              		.thumb
 2409              		.thumb_func
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 76


 2411              	udp_netif_ip_addr_changed:
 2412              	.LVL255:
 2413              	.LFB186:
1272:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
1273:Middlewares/Third_Party/LwIP/src/core/udp.c **** /** This function is called from netif.c when address is changed
1274:Middlewares/Third_Party/LwIP/src/core/udp.c ****  *
1275:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * @param old_addr IP address of the netif before change
1276:Middlewares/Third_Party/LwIP/src/core/udp.c ****  * @param new_addr IP address of the netif after change
1277:Middlewares/Third_Party/LwIP/src/core/udp.c ****  */
1278:Middlewares/Third_Party/LwIP/src/core/udp.c **** void udp_netif_ip_addr_changed(const ip_addr_t *old_addr, const ip_addr_t *new_addr)
1279:Middlewares/Third_Party/LwIP/src/core/udp.c **** {
 2414              		.loc 1 1279 1 is_stmt 1 view -0
 2415              		.cfi_startproc
 2416              		@ args = 0, pretend = 0, frame = 0
 2417              		@ frame_needed = 0, uses_anonymous_args = 0
 2418              		@ link register save eliminated.
1280:Middlewares/Third_Party/LwIP/src/core/udp.c ****   struct udp_pcb *upcb;
 2419              		.loc 1 1280 3 view .LVU766
1281:Middlewares/Third_Party/LwIP/src/core/udp.c **** 
1282:Middlewares/Third_Party/LwIP/src/core/udp.c ****   if (!ip_addr_isany(old_addr) && !ip_addr_isany(new_addr)) {
 2420              		.loc 1 1282 3 view .LVU767
 2421              		.loc 1 1282 6 is_stmt 0 view .LVU768
 2422 0000 B8B1     		cbz	r0, .L216
 2423 0002 0246     		mov	r2, r0
 2424              		.loc 1 1282 8 discriminator 1 view .LVU769
 2425 0004 0368     		ldr	r3, [r0]
 2426              		.loc 1 1282 7 discriminator 1 view .LVU770
 2427 0006 A3B1     		cbz	r3, .L216
 2428              		.loc 1 1282 32 discriminator 2 view .LVU771
 2429 0008 99B1     		cbz	r1, .L216
 2430              		.loc 1 1282 36 discriminator 3 view .LVU772
 2431 000a 0B68     		ldr	r3, [r1]
 2432              		.loc 1 1282 35 discriminator 3 view .LVU773
 2433 000c 8BB1     		cbz	r3, .L216
1283:Middlewares/Third_Party/LwIP/src/core/udp.c ****     for (upcb = udp_pcbs; upcb != NULL; upcb = upcb->next) {
 2434              		.loc 1 1283 5 is_stmt 1 view .LVU774
 2435              		.loc 1 1283 15 is_stmt 0 view .LVU775
 2436 000e 094B     		ldr	r3, .L222
 2437 0010 1B68     		ldr	r3, [r3]
 2438              	.LVL256:
 2439              		.loc 1 1283 32 is_stmt 1 discriminator 1 view .LVU776
 2440 0012 6BB1     		cbz	r3, .L220
1279:Middlewares/Third_Party/LwIP/src/core/udp.c ****   struct udp_pcb *upcb;
 2441              		.loc 1 1279 1 is_stmt 0 view .LVU777
 2442 0014 10B4     		push	{r4}
 2443              	.LCFI25:
 2444              		.cfi_def_cfa_offset 4
 2445              		.cfi_offset 4, -4
 2446 0016 01E0     		b	.L214
 2447              	.LVL257:
 2448              	.L213:
 2449              		.loc 1 1283 46 is_stmt 1 discriminator 2 view .LVU778
 2450 0018 DB68     		ldr	r3, [r3, #12]
 2451              	.LVL258:
 2452              		.loc 1 1283 32 discriminator 1 view .LVU779
 2453 001a 33B1     		cbz	r3, .L221
 2454              	.L214:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 77


1284:Middlewares/Third_Party/LwIP/src/core/udp.c ****       /* PCB bound to current local interface address? */
1285:Middlewares/Third_Party/LwIP/src/core/udp.c ****       if (ip_addr_cmp(&upcb->local_ip, old_addr)) {
 2455              		.loc 1 1285 7 view .LVU780
 2456              		.loc 1 1285 11 is_stmt 0 view .LVU781
 2457 001c 1068     		ldr	r0, [r2]
 2458              		.loc 1 1285 10 view .LVU782
 2459 001e 1C68     		ldr	r4, [r3]
 2460 0020 8442     		cmp	r4, r0
 2461 0022 F9D1     		bne	.L213
1286:Middlewares/Third_Party/LwIP/src/core/udp.c ****         /* The PCB is bound to the old ipaddr and
1287:Middlewares/Third_Party/LwIP/src/core/udp.c ****          * is set to bound to the new one instead */
1288:Middlewares/Third_Party/LwIP/src/core/udp.c ****         ip_addr_copy(upcb->local_ip, *new_addr);
 2462              		.loc 1 1288 9 is_stmt 1 view .LVU783
 2463 0024 0868     		ldr	r0, [r1]
 2464 0026 1860     		str	r0, [r3]
 2465 0028 F6E7     		b	.L213
 2466              	.L221:
1289:Middlewares/Third_Party/LwIP/src/core/udp.c ****       }
1290:Middlewares/Third_Party/LwIP/src/core/udp.c ****     }
1291:Middlewares/Third_Party/LwIP/src/core/udp.c ****   }
1292:Middlewares/Third_Party/LwIP/src/core/udp.c **** }
 2467              		.loc 1 1292 1 is_stmt 0 view .LVU784
 2468 002a 5DF8044B 		ldr	r4, [sp], #4
 2469              	.LCFI26:
 2470              		.cfi_restore 4
 2471              		.cfi_def_cfa_offset 0
 2472 002e 7047     		bx	lr
 2473              	.LVL259:
 2474              	.L220:
 2475              		.loc 1 1292 1 view .LVU785
 2476 0030 7047     		bx	lr
 2477              	.LVL260:
 2478              	.L216:
 2479              		.loc 1 1292 1 view .LVU786
 2480 0032 7047     		bx	lr
 2481              	.L223:
 2482              		.align	2
 2483              	.L222:
 2484 0034 00000000 		.word	udp_pcbs
 2485              		.cfi_endproc
 2486              	.LFE186:
 2488              		.global	udp_pcbs
 2489              		.section	.bss.udp_pcbs,"aw",%nobits
 2490              		.align	2
 2493              	udp_pcbs:
 2494 0000 00000000 		.space	4
 2495              		.section	.data.udp_port,"aw"
 2496              		.align	1
 2499              	udp_port:
 2500 0000 00C0     		.short	-16384
 2501              		.text
 2502              	.Letext0:
 2503              		.file 2 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\../lib/gcc/arm-none-eabi/13.3.1/..
 2504              		.file 3 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\../lib/gcc/arm-none-eabi/13.3.1/..
 2505              		.file 4 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\../lib/gcc/arm-none-eabi/13.3.1/in
 2506              		.file 5 "Middlewares/Third_Party/LwIP/src/include/lwip/arch.h"
 2507              		.file 6 "Middlewares/Third_Party/LwIP/src/include/lwip/err.h"
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 78


 2508              		.file 7 "Middlewares/Third_Party/LwIP/src/include/lwip/pbuf.h"
 2509              		.file 8 "Middlewares/Third_Party/LwIP/src/include/lwip/ip4_addr.h"
 2510              		.file 9 "Middlewares/Third_Party/LwIP/src/include/lwip/ip_addr.h"
 2511              		.file 10 "Middlewares/Third_Party/LwIP/src/include/lwip/memp.h"
 2512              		.file 11 "Middlewares/Third_Party/LwIP/src/include/lwip/netif.h"
 2513              		.file 12 "Middlewares/Third_Party/LwIP/src/include/lwip/prot/ip4.h"
 2514              		.file 13 "Middlewares/Third_Party/LwIP/src/include/lwip/ip.h"
 2515              		.file 14 "Middlewares/Third_Party/LwIP/src/include/lwip/prot/udp.h"
 2516              		.file 15 "Middlewares/Third_Party/LwIP/src/include/lwip/udp.h"
 2517              		.file 16 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\../lib/gcc/arm-none-eabi/13.3.1/.
 2518              		.file 17 "Middlewares/Third_Party/LwIP/src/include/lwip/ip4.h"
 2519              		.file 18 "Middlewares/Third_Party/LwIP/src/include/lwip/icmp.h"
 2520              		.file 19 "Middlewares/Third_Party/LwIP/src/include/lwip/def.h"
 2521              		.file 20 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\../lib/gcc/arm-none-eabi/13.3.1/.
 2522              		.file 21 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\../lib/gcc/arm-none-eabi/13.3.1/.
 2523              		.file 22 "<built-in>"
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 79


DEFINED SYMBOLS
                            *ABS*:00000000 udp.c
C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s:20     .text.udp_new_port:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s:25     .text.udp_new_port:00000000 udp_new_port
C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s:111    .text.udp_new_port:00000044 $d
C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s:2499   .data.udp_port:00000000 udp_port
C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s:2493   .bss.udp_pcbs:00000000 udp_pcbs
C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s:117    .rodata.udp_input_local_match.str1.4:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s:130    .text.udp_input_local_match:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s:135    .text.udp_input_local_match:00000000 udp_input_local_match
C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s:290    .text.udp_input_local_match:0000008c $d
C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s:299    .text.udp_init:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s:305    .text.udp_init:00000000 udp_init
C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s:331    .text.udp_init:00000010 $d
C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s:337    .rodata.udp_input.str1.4:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s:347    .text.udp_input:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s:353    .text.udp_input:00000000 udp_input
C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s:751    .text.udp_input:00000168 $d
C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s:763    .rodata.udp_bind.str1.4:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s:767    .text.udp_bind:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s:773    .text.udp_bind:00000000 udp_bind
C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s:969    .text.udp_bind:000000a0 $d
C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s:978    .rodata.udp_sendto_if_src.str1.4:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s:997    .text.udp_sendto_if_src:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s:1003   .text.udp_sendto_if_src:00000000 udp_sendto_if_src
C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s:1358   .text.udp_sendto_if_src:0000014c $d
C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s:1370   .rodata.udp_sendto_if.str1.4:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s:1383   .text.udp_sendto_if:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s:1389   .text.udp_sendto_if:00000000 udp_sendto_if
C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s:1570   .text.udp_sendto_if:00000090 $d
C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s:1580   .rodata.udp_sendto.str1.4:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s:1590   .text.udp_sendto:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s:1596   .text.udp_sendto:00000000 udp_sendto
C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s:1756   .text.udp_sendto:00000078 $d
C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s:1765   .rodata.udp_send.str1.4:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s:1772   .text.udp_send:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s:1778   .text.udp_send:00000000 udp_send
C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s:1850   .text.udp_send:00000038 $d
C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s:1858   .text.udp_bind_netif:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s:1864   .text.udp_bind_netif:00000000 udp_bind_netif
C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s:1894   .rodata.udp_connect.str1.4:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s:1901   .text.udp_connect:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s:1907   .text.udp_connect:00000000 udp_connect
C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s:2065   .text.udp_connect:00000070 $d
C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s:2074   .rodata.udp_disconnect.str1.4:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s:2078   .text.udp_disconnect:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s:2084   .text.udp_disconnect:00000000 udp_disconnect
C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s:2140   .text.udp_disconnect:00000028 $d
C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s:2147   .rodata.udp_recv.str1.4:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s:2151   .text.udp_recv:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s:2157   .text.udp_recv:00000000 udp_recv
C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s:2208   .text.udp_recv:0000001c $d
C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s:2215   .rodata.udp_remove.str1.4:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s:2219   .text.udp_remove:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s:2225   .text.udp_remove:00000000 udp_remove
C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s:2317   .text.udp_remove:00000044 $d
C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s:2325   .text.udp_new:00000000 $t
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s 			page 80


C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s:2331   .text.udp_new:00000000 udp_new
C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s:2372   .text.udp_new_ip_type:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s:2378   .text.udp_new_ip_type:00000000 udp_new_ip_type
C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s:2405   .text.udp_netif_ip_addr_changed:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s:2411   .text.udp_netif_ip_addr_changed:00000000 udp_netif_ip_addr_changed
C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s:2484   .text.udp_netif_ip_addr_changed:00000034 $d
C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s:2490   .bss.udp_pcbs:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\ccKA6XAX.s:2496   .data.udp_port:00000000 $d

UNDEFINED SYMBOLS
printf
ip_data
rand
ip4_addr_isbroadcast_u32
lwip_htons
pbuf_free
pbuf_remove_header
pbuf_header_force
icmp_dest_unreach
ip_addr_any
pbuf_add_header
ip4_output_if_src
pbuf_alloc
pbuf_chain
netif_get_by_index
ip4_route
memp_free
memp_malloc
memset
