ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cclvXS5R.s 			page 1


   1              		.cpu cortex-m7
   2              		.arch armv7e-m
   3              		.fpu fpv5-d16
   4              		.eabi_attribute 28, 1
   5              		.eabi_attribute 20, 1
   6              		.eabi_attribute 21, 1
   7              		.eabi_attribute 23, 3
   8              		.eabi_attribute 24, 1
   9              		.eabi_attribute 25, 1
  10              		.eabi_attribute 26, 1
  11              		.eabi_attribute 30, 1
  12              		.eabi_attribute 34, 1
  13              		.eabi_attribute 18, 4
  14              		.file	"netbuf.c"
  15              		.text
  16              	.Ltext0:
  17              		.cfi_sections	.debug_frame
  18              		.file 1 "Middlewares/Third_Party/LwIP/src/api/netbuf.c"
  19              		.section	.text.netbuf_new,"ax",%progbits
  20              		.align	1
  21              		.global	netbuf_new
  22              		.syntax unified
  23              		.thumb
  24              		.thumb_func
  26              	netbuf_new:
  27              	.LFB170:
   1:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** /**
   2:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  * @file
   3:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  * Network buffer management
   4:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  *
   5:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  * @defgroup netbuf Network buffers
   6:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  * @ingroup netconn
   7:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  * Network buffer descriptor for @ref netconn. Based on @ref pbuf internally
   8:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  * to avoid copying data around.\n
   9:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  * Buffers must not be shared accross multiple threads, all functions except
  10:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  * netbuf_new() and netbuf_delete() are not thread-safe.
  11:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  */
  12:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** 
  13:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** /*
  14:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  * Copyright (c) 2001-2004 Swedish Institute of Computer Science.
  15:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  * All rights reserved.
  16:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  *
  17:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  * Redistribution and use in source and binary forms, with or without modification,
  18:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  * are permitted provided that the following conditions are met:
  19:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  *
  20:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  * 1. Redistributions of source code must retain the above copyright notice,
  21:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  *    this list of conditions and the following disclaimer.
  22:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  * 2. Redistributions in binary form must reproduce the above copyright notice,
  23:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  *    this list of conditions and the following disclaimer in the documentation
  24:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  *    and/or other materials provided with the distribution.
  25:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  * 3. The name of the author may not be used to endorse or promote products
  26:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  *    derived from this software without specific prior written permission.
  27:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  *
  28:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  * THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR IMPLIED
  29:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
  30:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  * MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT
  31:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  * SHALL THE AUTHOR BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cclvXS5R.s 			page 2


  32:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  * EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT
  33:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  * OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
  34:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
  35:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
  36:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY
  37:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  * OF SUCH DAMAGE.
  38:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  *
  39:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  * This file is part of the lwIP TCP/IP stack.
  40:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  *
  41:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  * Author: Adam Dunkels <<EMAIL>>
  42:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  *
  43:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  */
  44:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** 
  45:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** #include "lwip/opt.h"
  46:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** 
  47:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** #if LWIP_NETCONN /* don't build if not configured for use in lwipopts.h */
  48:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** 
  49:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** #include "lwip/netbuf.h"
  50:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** #include "lwip/memp.h"
  51:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** 
  52:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** #include <string.h>
  53:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** 
  54:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** /**
  55:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  * @ingroup netbuf
  56:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  * Create (allocate) and initialize a new netbuf.
  57:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  * The netbuf doesn't yet contain a packet buffer!
  58:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  *
  59:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  * @return a pointer to a new netbuf
  60:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  *         NULL on lack of memory
  61:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  */
  62:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** struct
  63:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** netbuf *netbuf_new(void)
  64:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** {
  28              		.loc 1 64 1 view -0
  29              		.cfi_startproc
  30              		@ args = 0, pretend = 0, frame = 0
  31              		@ frame_needed = 0, uses_anonymous_args = 0
  32 0000 08B5     		push	{r3, lr}
  33              	.LCFI0:
  34              		.cfi_def_cfa_offset 8
  35              		.cfi_offset 3, -8
  36              		.cfi_offset 14, -4
  65:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   struct netbuf *buf;
  37              		.loc 1 65 3 view .LVU1
  66:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** 
  67:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   buf = (struct netbuf *)memp_malloc(MEMP_NETBUF);
  38              		.loc 1 67 3 view .LVU2
  39              		.loc 1 67 26 is_stmt 0 view .LVU3
  40 0002 0620     		movs	r0, #6
  41 0004 FFF7FEFF 		bl	memp_malloc
  42              	.LVL0:
  68:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   if (buf != NULL) {
  43              		.loc 1 68 3 is_stmt 1 view .LVU4
  44              		.loc 1 68 6 is_stmt 0 view .LVU5
  45 0008 0346     		mov	r3, r0
  46 000a 20B1     		cbz	r0, .L1
  69:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****     memset(buf, 0, sizeof(struct netbuf));
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cclvXS5R.s 			page 3


  47              		.loc 1 69 5 is_stmt 1 view .LVU6
  48 000c 0022     		movs	r2, #0
  49 000e 0260     		str	r2, [r0]	@ unaligned
  50 0010 4260     		str	r2, [r0, #4]	@ unaligned
  51 0012 8260     		str	r2, [r0, #8]	@ unaligned
  52 0014 C260     		str	r2, [r0, #12]	@ unaligned
  70:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   }
  71:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   return buf;
  53              		.loc 1 71 3 view .LVU7
  54              	.L1:
  72:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** }
  55              		.loc 1 72 1 is_stmt 0 view .LVU8
  56 0016 1846     		mov	r0, r3
  57              	.LVL1:
  58              		.loc 1 72 1 view .LVU9
  59 0018 08BD     		pop	{r3, pc}
  60              		.loc 1 72 1 view .LVU10
  61              		.cfi_endproc
  62              	.LFE170:
  64              		.section	.text.netbuf_delete,"ax",%progbits
  65              		.align	1
  66              		.global	netbuf_delete
  67              		.syntax unified
  68              		.thumb
  69              		.thumb_func
  71              	netbuf_delete:
  72              	.LVL2:
  73              	.LFB171:
  73:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** 
  74:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** /**
  75:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  * @ingroup netbuf
  76:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  * Deallocate a netbuf allocated by netbuf_new().
  77:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  *
  78:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  * @param buf pointer to a netbuf allocated by netbuf_new()
  79:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  */
  80:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** void
  81:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** netbuf_delete(struct netbuf *buf)
  82:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** {
  74              		.loc 1 82 1 is_stmt 1 view -0
  75              		.cfi_startproc
  76              		@ args = 0, pretend = 0, frame = 0
  77              		@ frame_needed = 0, uses_anonymous_args = 0
  83:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   if (buf != NULL) {
  78              		.loc 1 83 3 view .LVU12
  79              		.loc 1 83 6 is_stmt 0 view .LVU13
  80 0000 68B1     		cbz	r0, .L8
  82:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   if (buf != NULL) {
  81              		.loc 1 82 1 view .LVU14
  82 0002 10B5     		push	{r4, lr}
  83              	.LCFI1:
  84              		.cfi_def_cfa_offset 8
  85              		.cfi_offset 4, -8
  86              		.cfi_offset 14, -4
  87 0004 0446     		mov	r4, r0
  84:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****     if (buf->p != NULL) {
  88              		.loc 1 84 5 is_stmt 1 view .LVU15
  89              		.loc 1 84 12 is_stmt 0 view .LVU16
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cclvXS5R.s 			page 4


  90 0006 0068     		ldr	r0, [r0]
  91              	.LVL3:
  92              		.loc 1 84 8 view .LVU17
  93 0008 20B1     		cbz	r0, .L6
  85:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****       pbuf_free(buf->p);
  94              		.loc 1 85 7 is_stmt 1 view .LVU18
  95 000a FFF7FEFF 		bl	pbuf_free
  96              	.LVL4:
  86:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****       buf->p = buf->ptr = NULL;
  97              		.loc 1 86 7 view .LVU19
  98              		.loc 1 86 25 is_stmt 0 view .LVU20
  99 000e 0023     		movs	r3, #0
 100 0010 6360     		str	r3, [r4, #4]
 101              		.loc 1 86 14 view .LVU21
 102 0012 2360     		str	r3, [r4]
 103              	.L6:
  87:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****     }
  88:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****     memp_free(MEMP_NETBUF, buf);
 104              		.loc 1 88 5 is_stmt 1 view .LVU22
 105 0014 2146     		mov	r1, r4
 106 0016 0620     		movs	r0, #6
 107 0018 FFF7FEFF 		bl	memp_free
 108              	.LVL5:
  89:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   }
  90:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** }
 109              		.loc 1 90 1 is_stmt 0 view .LVU23
 110 001c 10BD     		pop	{r4, pc}
 111              	.LVL6:
 112              	.L8:
 113              	.LCFI2:
 114              		.cfi_def_cfa_offset 0
 115              		.cfi_restore 4
 116              		.cfi_restore 14
 117              		.loc 1 90 1 view .LVU24
 118 001e 7047     		bx	lr
 119              		.cfi_endproc
 120              	.LFE171:
 122              		.section	.rodata.netbuf_alloc.str1.4,"aMS",%progbits,1
 123              		.align	2
 124              	.LC0:
 125 0000 4D696464 		.ascii	"Middlewares/Third_Party/LwIP/src/api/netbuf.c\000"
 125      6C657761 
 125      7265732F 
 125      54686972 
 125      645F5061 
 126 002e 0000     		.align	2
 127              	.LC1:
 128 0030 6E657462 		.ascii	"netbuf_alloc: invalid buf\000"
 128      75665F61 
 128      6C6C6F63 
 128      3A20696E 
 128      76616C69 
 129 004a 0000     		.align	2
 130              	.LC2:
 131 004c 41737365 		.ascii	"Assertion \"%s\" failed at line %d in %s\012\000"
 131      7274696F 
 131      6E202225 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cclvXS5R.s 			page 5


 131      73222066 
 131      61696C65 
 132              		.align	2
 133              	.LC3:
 134 0074 63686563 		.ascii	"check that first pbuf can hold size\000"
 134      6B207468 
 134      61742066 
 134      69727374 
 134      20706275 
 135              		.section	.text.netbuf_alloc,"ax",%progbits
 136              		.align	1
 137              		.global	netbuf_alloc
 138              		.syntax unified
 139              		.thumb
 140              		.thumb_func
 142              	netbuf_alloc:
 143              	.LVL7:
 144              	.LFB172:
  91:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** 
  92:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** /**
  93:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  * @ingroup netbuf
  94:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  * Allocate memory for a packet buffer for a given netbuf.
  95:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  *
  96:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  * @param buf the netbuf for which to allocate a packet buffer
  97:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  * @param size the size of the packet buffer to allocate
  98:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  * @return pointer to the allocated memory
  99:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  *         NULL if no memory could be allocated
 100:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  */
 101:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** void *
 102:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** netbuf_alloc(struct netbuf *buf, u16_t size)
 103:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** {
 145              		.loc 1 103 1 is_stmt 1 view -0
 146              		.cfi_startproc
 147              		@ args = 0, pretend = 0, frame = 0
 148              		@ frame_needed = 0, uses_anonymous_args = 0
 149              		.loc 1 103 1 is_stmt 0 view .LVU26
 150 0000 38B5     		push	{r3, r4, r5, lr}
 151              	.LCFI3:
 152              		.cfi_def_cfa_offset 16
 153              		.cfi_offset 3, -16
 154              		.cfi_offset 4, -12
 155              		.cfi_offset 5, -8
 156              		.cfi_offset 14, -4
 104:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   LWIP_ERROR("netbuf_alloc: invalid buf", (buf != NULL), return NULL;);
 157              		.loc 1 104 3 is_stmt 1 view .LVU27
 158              		.loc 1 104 3 view .LVU28
 159 0002 0546     		mov	r5, r0
 160 0004 98B1     		cbz	r0, .L17
 161 0006 0C46     		mov	r4, r1
 162              		.loc 1 104 3 discriminator 2 view .LVU29
 163              		.loc 1 104 3 discriminator 2 view .LVU30
 105:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** 
 106:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   /* Deallocate any previously allocated memory. */
 107:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   if (buf->p != NULL) {
 164              		.loc 1 107 3 view .LVU31
 165              		.loc 1 107 10 is_stmt 0 view .LVU32
 166 0008 0068     		ldr	r0, [r0]
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cclvXS5R.s 			page 6


 167              	.LVL8:
 168              		.loc 1 107 6 view .LVU33
 169 000a 08B1     		cbz	r0, .L14
 108:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****     pbuf_free(buf->p);
 170              		.loc 1 108 5 is_stmt 1 view .LVU34
 171 000c FFF7FEFF 		bl	pbuf_free
 172              	.LVL9:
 173              	.L14:
 109:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   }
 110:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   buf->p = pbuf_alloc(PBUF_TRANSPORT, size, PBUF_RAM);
 174              		.loc 1 110 3 view .LVU35
 175              		.loc 1 110 12 is_stmt 0 view .LVU36
 176 0010 4FF42072 		mov	r2, #640
 177 0014 2146     		mov	r1, r4
 178 0016 3620     		movs	r0, #54
 179 0018 FFF7FEFF 		bl	pbuf_alloc
 180              	.LVL10:
 181              		.loc 1 110 10 discriminator 1 view .LVU37
 182 001c 2860     		str	r0, [r5]
 111:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   if (buf->p == NULL) {
 183              		.loc 1 111 3 is_stmt 1 view .LVU38
 184              		.loc 1 111 6 is_stmt 0 view .LVU39
 185 001e 28B1     		cbz	r0, .L11
 112:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****     return NULL;
 113:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   }
 114:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   LWIP_ASSERT("check that first pbuf can hold size",
 186              		.loc 1 114 3 is_stmt 1 view .LVU40
 187              		.loc 1 114 3 view .LVU41
 188 0020 4389     		ldrh	r3, [r0, #10]
 189 0022 A342     		cmp	r3, r4
 190 0024 0BD3     		bcc	.L18
 191              	.L15:
 192              		.loc 1 114 3 discriminator 3 view .LVU42
 193              		.loc 1 114 3 discriminator 3 view .LVU43
 115:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****               (buf->p->len >= size));
 116:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   buf->ptr = buf->p;
 194              		.loc 1 116 3 view .LVU44
 195              		.loc 1 116 17 is_stmt 0 view .LVU45
 196 0026 2B68     		ldr	r3, [r5]
 197              		.loc 1 116 12 view .LVU46
 198 0028 6B60     		str	r3, [r5, #4]
 117:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   return buf->p->payload;
 199              		.loc 1 117 3 is_stmt 1 view .LVU47
 200              		.loc 1 117 16 is_stmt 0 view .LVU48
 201 002a 5868     		ldr	r0, [r3, #4]
 202              	.LVL11:
 203              	.L11:
 118:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** }
 204              		.loc 1 118 1 view .LVU49
 205 002c 38BD     		pop	{r3, r4, r5, pc}
 206              	.LVL12:
 207              	.L17:
 104:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** 
 208              		.loc 1 104 3 is_stmt 1 discriminator 1 view .LVU50
 104:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** 
 209              		.loc 1 104 3 discriminator 1 view .LVU51
 210 002e 074B     		ldr	r3, .L19
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cclvXS5R.s 			page 7


 211 0030 6822     		movs	r2, #104
 212 0032 0749     		ldr	r1, .L19+4
 213              	.LVL13:
 104:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** 
 214              		.loc 1 104 3 is_stmt 0 discriminator 1 view .LVU52
 215 0034 0748     		ldr	r0, .L19+8
 216              	.LVL14:
 104:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** 
 217              		.loc 1 104 3 discriminator 1 view .LVU53
 218 0036 FFF7FEFF 		bl	printf
 219              	.LVL15:
 104:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** 
 220              		.loc 1 104 3 is_stmt 1 discriminator 1 view .LVU54
 104:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** 
 221              		.loc 1 104 3 discriminator 1 view .LVU55
 222 003a 2846     		mov	r0, r5
 104:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** 
 223              		.loc 1 104 3 is_stmt 0 view .LVU56
 224 003c F6E7     		b	.L11
 225              	.L18:
 114:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****               (buf->p->len >= size));
 226              		.loc 1 114 3 is_stmt 1 discriminator 1 view .LVU57
 114:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****               (buf->p->len >= size));
 227              		.loc 1 114 3 discriminator 1 view .LVU58
 228 003e 034B     		ldr	r3, .L19
 229 0040 7222     		movs	r2, #114
 230 0042 0549     		ldr	r1, .L19+12
 231 0044 0348     		ldr	r0, .L19+8
 232 0046 FFF7FEFF 		bl	printf
 233              	.LVL16:
 234 004a ECE7     		b	.L15
 235              	.L20:
 236              		.align	2
 237              	.L19:
 238 004c 00000000 		.word	.LC0
 239 0050 30000000 		.word	.LC1
 240 0054 4C000000 		.word	.LC2
 241 0058 74000000 		.word	.LC3
 242              		.cfi_endproc
 243              	.LFE172:
 245              		.section	.rodata.netbuf_free.str1.4,"aMS",%progbits,1
 246              		.align	2
 247              	.LC4:
 248 0000 6E657462 		.ascii	"netbuf_free: invalid buf\000"
 248      75665F66 
 248      7265653A 
 248      20696E76 
 248      616C6964 
 249              		.section	.text.netbuf_free,"ax",%progbits
 250              		.align	1
 251              		.global	netbuf_free
 252              		.syntax unified
 253              		.thumb
 254              		.thumb_func
 256              	netbuf_free:
 257              	.LVL17:
 258              	.LFB173:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cclvXS5R.s 			page 8


 119:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** 
 120:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** /**
 121:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  * @ingroup netbuf
 122:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  * Free the packet buffer included in a netbuf
 123:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  *
 124:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  * @param buf pointer to the netbuf which contains the packet buffer to free
 125:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  */
 126:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** void
 127:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** netbuf_free(struct netbuf *buf)
 128:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** {
 259              		.loc 1 128 1 view -0
 260              		.cfi_startproc
 261              		@ args = 0, pretend = 0, frame = 0
 262              		@ frame_needed = 0, uses_anonymous_args = 0
 263              		.loc 1 128 1 is_stmt 0 view .LVU60
 264 0000 10B5     		push	{r4, lr}
 265              	.LCFI4:
 266              		.cfi_def_cfa_offset 8
 267              		.cfi_offset 4, -8
 268              		.cfi_offset 14, -4
 129:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   LWIP_ERROR("netbuf_free: invalid buf", (buf != NULL), return;);
 269              		.loc 1 129 3 is_stmt 1 view .LVU61
 270              		.loc 1 129 3 view .LVU62
 271 0002 40B1     		cbz	r0, .L26
 272 0004 0446     		mov	r4, r0
 273              		.loc 1 129 3 discriminator 2 view .LVU63
 274              		.loc 1 129 3 discriminator 2 view .LVU64
 130:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   if (buf->p != NULL) {
 275              		.loc 1 130 3 view .LVU65
 276              		.loc 1 130 10 is_stmt 0 view .LVU66
 277 0006 0068     		ldr	r0, [r0]
 278              	.LVL18:
 279              		.loc 1 130 6 view .LVU67
 280 0008 08B1     		cbz	r0, .L24
 131:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****     pbuf_free(buf->p);
 281              		.loc 1 131 5 is_stmt 1 view .LVU68
 282 000a FFF7FEFF 		bl	pbuf_free
 283              	.LVL19:
 284              	.L24:
 132:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   }
 133:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   buf->p = buf->ptr = NULL;
 285              		.loc 1 133 3 view .LVU69
 286              		.loc 1 133 21 is_stmt 0 view .LVU70
 287 000e 0023     		movs	r3, #0
 288 0010 6360     		str	r3, [r4, #4]
 289              		.loc 1 133 10 view .LVU71
 290 0012 2360     		str	r3, [r4]
 291              	.LVL20:
 292              	.L21:
 134:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** #if LWIP_CHECKSUM_ON_COPY
 135:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   buf->flags = 0;
 136:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   buf->toport_chksum = 0;
 137:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** #endif /* LWIP_CHECKSUM_ON_COPY */
 138:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** }
 293              		.loc 1 138 1 view .LVU72
 294 0014 10BD     		pop	{r4, pc}
 295              	.LVL21:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cclvXS5R.s 			page 9


 296              	.L26:
 129:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   if (buf->p != NULL) {
 297              		.loc 1 129 3 is_stmt 1 discriminator 1 view .LVU73
 129:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   if (buf->p != NULL) {
 298              		.loc 1 129 3 discriminator 1 view .LVU74
 299 0016 034B     		ldr	r3, .L27
 300 0018 8122     		movs	r2, #129
 301 001a 0349     		ldr	r1, .L27+4
 302 001c 0348     		ldr	r0, .L27+8
 303              	.LVL22:
 129:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   if (buf->p != NULL) {
 304              		.loc 1 129 3 is_stmt 0 discriminator 1 view .LVU75
 305 001e FFF7FEFF 		bl	printf
 306              	.LVL23:
 129:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   if (buf->p != NULL) {
 307              		.loc 1 129 3 is_stmt 1 discriminator 1 view .LVU76
 129:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   if (buf->p != NULL) {
 308              		.loc 1 129 3 discriminator 1 view .LVU77
 129:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   if (buf->p != NULL) {
 309              		.loc 1 129 3 is_stmt 0 view .LVU78
 310 0022 F7E7     		b	.L21
 311              	.L28:
 312              		.align	2
 313              	.L27:
 314 0024 00000000 		.word	.LC0
 315 0028 00000000 		.word	.LC4
 316 002c 4C000000 		.word	.LC2
 317              		.cfi_endproc
 318              	.LFE173:
 320              		.section	.rodata.netbuf_ref.str1.4,"aMS",%progbits,1
 321              		.align	2
 322              	.LC5:
 323 0000 6E657462 		.ascii	"netbuf_ref: invalid buf\000"
 323      75665F72 
 323      65663A20 
 323      696E7661 
 323      6C696420 
 324              		.section	.text.netbuf_ref,"ax",%progbits
 325              		.align	1
 326              		.global	netbuf_ref
 327              		.syntax unified
 328              		.thumb
 329              		.thumb_func
 331              	netbuf_ref:
 332              	.LVL24:
 333              	.LFB174:
 139:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** 
 140:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** /**
 141:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  * @ingroup netbuf
 142:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  * Let a netbuf reference existing (non-volatile) data.
 143:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  *
 144:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  * @param buf netbuf which should reference the data
 145:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  * @param dataptr pointer to the data to reference
 146:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  * @param size size of the data
 147:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  * @return ERR_OK if data is referenced
 148:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  *         ERR_MEM if data couldn't be referenced due to lack of memory
 149:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  */
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cclvXS5R.s 			page 10


 150:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** err_t
 151:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** netbuf_ref(struct netbuf *buf, const void *dataptr, u16_t size)
 152:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** {
 334              		.loc 1 152 1 is_stmt 1 view -0
 335              		.cfi_startproc
 336              		@ args = 0, pretend = 0, frame = 0
 337              		@ frame_needed = 0, uses_anonymous_args = 0
 338              		.loc 1 152 1 is_stmt 0 view .LVU80
 339 0000 70B5     		push	{r4, r5, r6, lr}
 340              	.LCFI5:
 341              		.cfi_def_cfa_offset 16
 342              		.cfi_offset 4, -16
 343              		.cfi_offset 5, -12
 344              		.cfi_offset 6, -8
 345              		.cfi_offset 14, -4
 153:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   LWIP_ERROR("netbuf_ref: invalid buf", (buf != NULL), return ERR_ARG;);
 346              		.loc 1 153 3 is_stmt 1 view .LVU81
 347              		.loc 1 153 3 view .LVU82
 348 0002 B0B1     		cbz	r0, .L35
 349 0004 0E46     		mov	r6, r1
 350 0006 1546     		mov	r5, r2
 351 0008 0446     		mov	r4, r0
 352              		.loc 1 153 3 discriminator 2 view .LVU83
 353              		.loc 1 153 3 discriminator 2 view .LVU84
 154:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   if (buf->p != NULL) {
 354              		.loc 1 154 3 view .LVU85
 355              		.loc 1 154 10 is_stmt 0 view .LVU86
 356 000a 0068     		ldr	r0, [r0]
 357              	.LVL25:
 358              		.loc 1 154 6 view .LVU87
 359 000c 08B1     		cbz	r0, .L32
 155:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****     pbuf_free(buf->p);
 360              		.loc 1 155 5 is_stmt 1 view .LVU88
 361 000e FFF7FEFF 		bl	pbuf_free
 362              	.LVL26:
 363              	.L32:
 156:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   }
 157:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   buf->p = pbuf_alloc(PBUF_TRANSPORT, 0, PBUF_REF);
 364              		.loc 1 157 3 view .LVU89
 365              		.loc 1 157 12 is_stmt 0 view .LVU90
 366 0012 4122     		movs	r2, #65
 367 0014 0021     		movs	r1, #0
 368 0016 3620     		movs	r0, #54
 369 0018 FFF7FEFF 		bl	pbuf_alloc
 370              	.LVL27:
 371              		.loc 1 157 10 discriminator 1 view .LVU91
 372 001c 2060     		str	r0, [r4]
 158:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   if (buf->p == NULL) {
 373              		.loc 1 158 3 is_stmt 1 view .LVU92
 374              		.loc 1 158 6 is_stmt 0 view .LVU93
 375 001e 88B1     		cbz	r0, .L36
 159:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****     buf->ptr = NULL;
 160:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****     return ERR_MEM;
 161:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   }
 162:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   ((struct pbuf_rom *)buf->p)->payload = dataptr;
 376              		.loc 1 162 3 is_stmt 1 view .LVU94
 377              		.loc 1 162 40 is_stmt 0 view .LVU95
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cclvXS5R.s 			page 11


 378 0020 4660     		str	r6, [r0, #4]
 163:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   buf->p->len = buf->p->tot_len = size;
 379              		.loc 1 163 3 is_stmt 1 view .LVU96
 380              		.loc 1 163 20 is_stmt 0 view .LVU97
 381 0022 2368     		ldr	r3, [r4]
 382              		.loc 1 163 33 view .LVU98
 383 0024 1D81     		strh	r5, [r3, #8]	@ movhi
 384              		.loc 1 163 6 view .LVU99
 385 0026 2368     		ldr	r3, [r4]
 386              		.loc 1 163 15 view .LVU100
 387 0028 5D81     		strh	r5, [r3, #10]	@ movhi
 164:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   buf->ptr = buf->p;
 388              		.loc 1 164 3 is_stmt 1 view .LVU101
 389              		.loc 1 164 17 is_stmt 0 view .LVU102
 390 002a 2368     		ldr	r3, [r4]
 391              		.loc 1 164 12 view .LVU103
 392 002c 6360     		str	r3, [r4, #4]
 165:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   return ERR_OK;
 393              		.loc 1 165 3 is_stmt 1 view .LVU104
 394              		.loc 1 165 10 is_stmt 0 view .LVU105
 395 002e 0020     		movs	r0, #0
 396              	.LVL28:
 397              	.L31:
 166:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** }
 398              		.loc 1 166 1 view .LVU106
 399 0030 70BD     		pop	{r4, r5, r6, pc}
 400              	.LVL29:
 401              	.L35:
 153:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   if (buf->p != NULL) {
 402              		.loc 1 153 3 is_stmt 1 discriminator 1 view .LVU107
 153:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   if (buf->p != NULL) {
 403              		.loc 1 153 3 discriminator 1 view .LVU108
 404 0032 074B     		ldr	r3, .L37
 405 0034 9922     		movs	r2, #153
 406              	.LVL30:
 153:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   if (buf->p != NULL) {
 407              		.loc 1 153 3 is_stmt 0 discriminator 1 view .LVU109
 408 0036 0749     		ldr	r1, .L37+4
 409              	.LVL31:
 153:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   if (buf->p != NULL) {
 410              		.loc 1 153 3 discriminator 1 view .LVU110
 411 0038 0748     		ldr	r0, .L37+8
 412              	.LVL32:
 153:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   if (buf->p != NULL) {
 413              		.loc 1 153 3 discriminator 1 view .LVU111
 414 003a FFF7FEFF 		bl	printf
 415              	.LVL33:
 153:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   if (buf->p != NULL) {
 416              		.loc 1 153 3 is_stmt 1 discriminator 1 view .LVU112
 153:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   if (buf->p != NULL) {
 417              		.loc 1 153 3 discriminator 1 view .LVU113
 418 003e 6FF00F00 		mvn	r0, #15
 153:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   if (buf->p != NULL) {
 419              		.loc 1 153 3 is_stmt 0 view .LVU114
 420 0042 F5E7     		b	.L31
 421              	.LVL34:
 422              	.L36:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cclvXS5R.s 			page 12


 159:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****     return ERR_MEM;
 423              		.loc 1 159 5 is_stmt 1 view .LVU115
 159:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****     return ERR_MEM;
 424              		.loc 1 159 14 is_stmt 0 view .LVU116
 425 0044 0023     		movs	r3, #0
 426 0046 6360     		str	r3, [r4, #4]
 160:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   }
 427              		.loc 1 160 5 is_stmt 1 view .LVU117
 160:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   }
 428              		.loc 1 160 12 is_stmt 0 view .LVU118
 429 0048 4FF0FF30 		mov	r0, #-1
 430 004c F0E7     		b	.L31
 431              	.L38:
 432 004e 00BF     		.align	2
 433              	.L37:
 434 0050 00000000 		.word	.LC0
 435 0054 00000000 		.word	.LC5
 436 0058 4C000000 		.word	.LC2
 437              		.cfi_endproc
 438              	.LFE174:
 440              		.section	.rodata.netbuf_chain.str1.4,"aMS",%progbits,1
 441              		.align	2
 442              	.LC6:
 443 0000 6E657462 		.ascii	"netbuf_chain: invalid head\000"
 443      75665F63 
 443      6861696E 
 443      3A20696E 
 443      76616C69 
 444 001b 00       		.align	2
 445              	.LC7:
 446 001c 6E657462 		.ascii	"netbuf_chain: invalid tail\000"
 446      75665F63 
 446      6861696E 
 446      3A20696E 
 446      76616C69 
 447              		.section	.text.netbuf_chain,"ax",%progbits
 448              		.align	1
 449              		.global	netbuf_chain
 450              		.syntax unified
 451              		.thumb
 452              		.thumb_func
 454              	netbuf_chain:
 455              	.LVL35:
 456              	.LFB175:
 167:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** 
 168:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** /**
 169:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  * @ingroup netbuf
 170:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  * Chain one netbuf to another (@see pbuf_chain)
 171:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  *
 172:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  * @param head the first netbuf
 173:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  * @param tail netbuf to chain after head, freed by this function, may not be reference after retur
 174:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  */
 175:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** void
 176:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** netbuf_chain(struct netbuf *head, struct netbuf *tail)
 177:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** {
 457              		.loc 1 177 1 is_stmt 1 view -0
 458              		.cfi_startproc
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cclvXS5R.s 			page 13


 459              		@ args = 0, pretend = 0, frame = 0
 460              		@ frame_needed = 0, uses_anonymous_args = 0
 461              		.loc 1 177 1 is_stmt 0 view .LVU120
 462 0000 38B5     		push	{r3, r4, r5, lr}
 463              	.LCFI6:
 464              		.cfi_def_cfa_offset 16
 465              		.cfi_offset 3, -16
 466              		.cfi_offset 4, -12
 467              		.cfi_offset 5, -8
 468              		.cfi_offset 14, -4
 178:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   LWIP_ERROR("netbuf_chain: invalid head", (head != NULL), return;);
 469              		.loc 1 178 3 is_stmt 1 view .LVU121
 470              		.loc 1 178 3 view .LVU122
 471 0002 68B1     		cbz	r0, .L44
 472 0004 0C46     		mov	r4, r1
 473 0006 0546     		mov	r5, r0
 474              		.loc 1 178 3 discriminator 2 view .LVU123
 475              		.loc 1 178 3 discriminator 2 view .LVU124
 179:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   LWIP_ERROR("netbuf_chain: invalid tail", (tail != NULL), return;);
 476              		.loc 1 179 3 view .LVU125
 477              		.loc 1 179 3 view .LVU126
 478 0008 89B1     		cbz	r1, .L45
 479              		.loc 1 179 3 discriminator 2 view .LVU127
 480              		.loc 1 179 3 discriminator 2 view .LVU128
 180:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   pbuf_cat(head->p, tail->p);
 481              		.loc 1 180 3 view .LVU129
 482 000a 0968     		ldr	r1, [r1]
 483              	.LVL36:
 484              		.loc 1 180 3 is_stmt 0 view .LVU130
 485 000c 0068     		ldr	r0, [r0]
 486              	.LVL37:
 487              		.loc 1 180 3 view .LVU131
 488 000e FFF7FEFF 		bl	pbuf_cat
 489              	.LVL38:
 181:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   head->ptr = head->p;
 490              		.loc 1 181 3 is_stmt 1 view .LVU132
 491              		.loc 1 181 19 is_stmt 0 view .LVU133
 492 0012 2B68     		ldr	r3, [r5]
 493              		.loc 1 181 13 view .LVU134
 494 0014 6B60     		str	r3, [r5, #4]
 182:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   memp_free(MEMP_NETBUF, tail);
 495              		.loc 1 182 3 is_stmt 1 view .LVU135
 496 0016 2146     		mov	r1, r4
 497 0018 0620     		movs	r0, #6
 498 001a FFF7FEFF 		bl	memp_free
 499              	.LVL39:
 500              	.L39:
 183:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** }
 501              		.loc 1 183 1 is_stmt 0 view .LVU136
 502 001e 38BD     		pop	{r3, r4, r5, pc}
 503              	.LVL40:
 504              	.L44:
 178:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   LWIP_ERROR("netbuf_chain: invalid tail", (tail != NULL), return;);
 505              		.loc 1 178 3 is_stmt 1 discriminator 1 view .LVU137
 178:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   LWIP_ERROR("netbuf_chain: invalid tail", (tail != NULL), return;);
 506              		.loc 1 178 3 discriminator 1 view .LVU138
 507 0020 064B     		ldr	r3, .L46
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cclvXS5R.s 			page 14


 508 0022 B222     		movs	r2, #178
 509 0024 0649     		ldr	r1, .L46+4
 510              	.LVL41:
 178:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   LWIP_ERROR("netbuf_chain: invalid tail", (tail != NULL), return;);
 511              		.loc 1 178 3 is_stmt 0 discriminator 1 view .LVU139
 512 0026 0748     		ldr	r0, .L46+8
 513              	.LVL42:
 178:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   LWIP_ERROR("netbuf_chain: invalid tail", (tail != NULL), return;);
 514              		.loc 1 178 3 discriminator 1 view .LVU140
 515 0028 FFF7FEFF 		bl	printf
 516              	.LVL43:
 178:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   LWIP_ERROR("netbuf_chain: invalid tail", (tail != NULL), return;);
 517              		.loc 1 178 3 is_stmt 1 discriminator 1 view .LVU141
 178:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   LWIP_ERROR("netbuf_chain: invalid tail", (tail != NULL), return;);
 518              		.loc 1 178 3 discriminator 1 view .LVU142
 178:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   LWIP_ERROR("netbuf_chain: invalid tail", (tail != NULL), return;);
 519              		.loc 1 178 3 is_stmt 0 view .LVU143
 520 002c F7E7     		b	.L39
 521              	.LVL44:
 522              	.L45:
 179:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   pbuf_cat(head->p, tail->p);
 523              		.loc 1 179 3 is_stmt 1 discriminator 1 view .LVU144
 179:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   pbuf_cat(head->p, tail->p);
 524              		.loc 1 179 3 discriminator 1 view .LVU145
 525 002e 034B     		ldr	r3, .L46
 526 0030 B322     		movs	r2, #179
 527 0032 0549     		ldr	r1, .L46+12
 528              	.LVL45:
 179:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   pbuf_cat(head->p, tail->p);
 529              		.loc 1 179 3 is_stmt 0 discriminator 1 view .LVU146
 530 0034 0348     		ldr	r0, .L46+8
 531              	.LVL46:
 179:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   pbuf_cat(head->p, tail->p);
 532              		.loc 1 179 3 discriminator 1 view .LVU147
 533 0036 FFF7FEFF 		bl	printf
 534              	.LVL47:
 179:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   pbuf_cat(head->p, tail->p);
 535              		.loc 1 179 3 is_stmt 1 discriminator 1 view .LVU148
 179:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   pbuf_cat(head->p, tail->p);
 536              		.loc 1 179 3 discriminator 1 view .LVU149
 179:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   pbuf_cat(head->p, tail->p);
 537              		.loc 1 179 3 is_stmt 0 view .LVU150
 538 003a F0E7     		b	.L39
 539              	.L47:
 540              		.align	2
 541              	.L46:
 542 003c 00000000 		.word	.LC0
 543 0040 00000000 		.word	.LC6
 544 0044 4C000000 		.word	.LC2
 545 0048 1C000000 		.word	.LC7
 546              		.cfi_endproc
 547              	.LFE175:
 549              		.section	.rodata.netbuf_data.str1.4,"aMS",%progbits,1
 550              		.align	2
 551              	.LC8:
 552 0000 6E657462 		.ascii	"netbuf_data: invalid buf\000"
 552      75665F64 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cclvXS5R.s 			page 15


 552      6174613A 
 552      20696E76 
 552      616C6964 
 553 0019 000000   		.align	2
 554              	.LC9:
 555 001c 6E657462 		.ascii	"netbuf_data: invalid dataptr\000"
 555      75665F64 
 555      6174613A 
 555      20696E76 
 555      616C6964 
 556 0039 000000   		.align	2
 557              	.LC10:
 558 003c 6E657462 		.ascii	"netbuf_data: invalid len\000"
 558      75665F64 
 558      6174613A 
 558      20696E76 
 558      616C6964 
 559              		.section	.text.netbuf_data,"ax",%progbits
 560              		.align	1
 561              		.global	netbuf_data
 562              		.syntax unified
 563              		.thumb
 564              		.thumb_func
 566              	netbuf_data:
 567              	.LVL48:
 568              	.LFB176:
 184:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** 
 185:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** /**
 186:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  * @ingroup netbuf
 187:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  * Get the data pointer and length of the data inside a netbuf.
 188:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  *
 189:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  * @param buf netbuf to get the data from
 190:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  * @param dataptr pointer to a void pointer where to store the data pointer
 191:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  * @param len pointer to an u16_t where the length of the data is stored
 192:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  * @return ERR_OK if the information was retrieved,
 193:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  *         ERR_BUF on error.
 194:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  */
 195:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** err_t
 196:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** netbuf_data(struct netbuf *buf, void **dataptr, u16_t *len)
 197:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** {
 569              		.loc 1 197 1 is_stmt 1 view -0
 570              		.cfi_startproc
 571              		@ args = 0, pretend = 0, frame = 0
 572              		@ frame_needed = 0, uses_anonymous_args = 0
 573              		.loc 1 197 1 is_stmt 0 view .LVU152
 574 0000 08B5     		push	{r3, lr}
 575              	.LCFI7:
 576              		.cfi_def_cfa_offset 8
 577              		.cfi_offset 3, -8
 578              		.cfi_offset 14, -4
 198:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   LWIP_ERROR("netbuf_data: invalid buf", (buf != NULL), return ERR_ARG;);
 579              		.loc 1 198 3 is_stmt 1 view .LVU153
 580              		.loc 1 198 3 view .LVU154
 581 0002 58B1     		cbz	r0, .L55
 582 0004 0346     		mov	r3, r0
 583              		.loc 1 198 3 discriminator 2 view .LVU155
 584              		.loc 1 198 3 discriminator 2 view .LVU156
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cclvXS5R.s 			page 16


 199:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   LWIP_ERROR("netbuf_data: invalid dataptr", (dataptr != NULL), return ERR_ARG;);
 585              		.loc 1 199 3 view .LVU157
 586              		.loc 1 199 3 view .LVU158
 587 0006 91B1     		cbz	r1, .L56
 588              		.loc 1 199 3 discriminator 2 view .LVU159
 589              		.loc 1 199 3 discriminator 2 view .LVU160
 200:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   LWIP_ERROR("netbuf_data: invalid len", (len != NULL), return ERR_ARG;);
 590              		.loc 1 200 3 view .LVU161
 591              		.loc 1 200 3 view .LVU162
 592 0008 D2B1     		cbz	r2, .L57
 593              		.loc 1 200 3 discriminator 2 view .LVU163
 594              		.loc 1 200 3 discriminator 2 view .LVU164
 201:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** 
 202:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   if (buf->ptr == NULL) {
 595              		.loc 1 202 3 view .LVU165
 596              		.loc 1 202 10 is_stmt 0 view .LVU166
 597 000a 4068     		ldr	r0, [r0, #4]
 598              	.LVL49:
 599              		.loc 1 202 6 view .LVU167
 600 000c 08B3     		cbz	r0, .L53
 203:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****     return ERR_BUF;
 204:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   }
 205:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   *dataptr = buf->ptr->payload;
 601              		.loc 1 205 3 is_stmt 1 view .LVU168
 602              		.loc 1 205 22 is_stmt 0 view .LVU169
 603 000e 4068     		ldr	r0, [r0, #4]
 604              		.loc 1 205 12 view .LVU170
 605 0010 0860     		str	r0, [r1]
 206:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   *len = buf->ptr->len;
 606              		.loc 1 206 3 is_stmt 1 view .LVU171
 607              		.loc 1 206 13 is_stmt 0 view .LVU172
 608 0012 5B68     		ldr	r3, [r3, #4]
 609              	.LVL50:
 610              		.loc 1 206 18 view .LVU173
 611 0014 5B89     		ldrh	r3, [r3, #10]
 612              		.loc 1 206 8 view .LVU174
 613 0016 1380     		strh	r3, [r2]	@ movhi
 207:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   return ERR_OK;
 614              		.loc 1 207 3 is_stmt 1 view .LVU175
 615              		.loc 1 207 10 is_stmt 0 view .LVU176
 616 0018 0020     		movs	r0, #0
 617              	.LVL51:
 618              	.L50:
 208:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** }
 619              		.loc 1 208 1 view .LVU177
 620 001a 08BD     		pop	{r3, pc}
 621              	.LVL52:
 622              	.L55:
 198:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   LWIP_ERROR("netbuf_data: invalid dataptr", (dataptr != NULL), return ERR_ARG;);
 623              		.loc 1 198 3 is_stmt 1 discriminator 1 view .LVU178
 198:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   LWIP_ERROR("netbuf_data: invalid dataptr", (dataptr != NULL), return ERR_ARG;);
 624              		.loc 1 198 3 discriminator 1 view .LVU179
 625 001c 0E4B     		ldr	r3, .L58
 626 001e C622     		movs	r2, #198
 627              	.LVL53:
 198:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   LWIP_ERROR("netbuf_data: invalid dataptr", (dataptr != NULL), return ERR_ARG;);
 628              		.loc 1 198 3 is_stmt 0 discriminator 1 view .LVU180
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cclvXS5R.s 			page 17


 629 0020 0E49     		ldr	r1, .L58+4
 630              	.LVL54:
 198:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   LWIP_ERROR("netbuf_data: invalid dataptr", (dataptr != NULL), return ERR_ARG;);
 631              		.loc 1 198 3 discriminator 1 view .LVU181
 632 0022 0F48     		ldr	r0, .L58+8
 633              	.LVL55:
 198:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   LWIP_ERROR("netbuf_data: invalid dataptr", (dataptr != NULL), return ERR_ARG;);
 634              		.loc 1 198 3 discriminator 1 view .LVU182
 635 0024 FFF7FEFF 		bl	printf
 636              	.LVL56:
 198:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   LWIP_ERROR("netbuf_data: invalid dataptr", (dataptr != NULL), return ERR_ARG;);
 637              		.loc 1 198 3 is_stmt 1 discriminator 1 view .LVU183
 198:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   LWIP_ERROR("netbuf_data: invalid dataptr", (dataptr != NULL), return ERR_ARG;);
 638              		.loc 1 198 3 discriminator 1 view .LVU184
 639 0028 6FF00F00 		mvn	r0, #15
 198:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   LWIP_ERROR("netbuf_data: invalid dataptr", (dataptr != NULL), return ERR_ARG;);
 640              		.loc 1 198 3 is_stmt 0 view .LVU185
 641 002c F5E7     		b	.L50
 642              	.LVL57:
 643              	.L56:
 199:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   LWIP_ERROR("netbuf_data: invalid len", (len != NULL), return ERR_ARG;);
 644              		.loc 1 199 3 is_stmt 1 discriminator 1 view .LVU186
 199:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   LWIP_ERROR("netbuf_data: invalid len", (len != NULL), return ERR_ARG;);
 645              		.loc 1 199 3 discriminator 1 view .LVU187
 646 002e 0A4B     		ldr	r3, .L58
 647 0030 C722     		movs	r2, #199
 648              	.LVL58:
 199:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   LWIP_ERROR("netbuf_data: invalid len", (len != NULL), return ERR_ARG;);
 649              		.loc 1 199 3 is_stmt 0 discriminator 1 view .LVU188
 650 0032 0C49     		ldr	r1, .L58+12
 651              	.LVL59:
 199:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   LWIP_ERROR("netbuf_data: invalid len", (len != NULL), return ERR_ARG;);
 652              		.loc 1 199 3 discriminator 1 view .LVU189
 653 0034 0A48     		ldr	r0, .L58+8
 654              	.LVL60:
 199:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   LWIP_ERROR("netbuf_data: invalid len", (len != NULL), return ERR_ARG;);
 655              		.loc 1 199 3 discriminator 1 view .LVU190
 656 0036 FFF7FEFF 		bl	printf
 657              	.LVL61:
 199:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   LWIP_ERROR("netbuf_data: invalid len", (len != NULL), return ERR_ARG;);
 658              		.loc 1 199 3 is_stmt 1 discriminator 1 view .LVU191
 199:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   LWIP_ERROR("netbuf_data: invalid len", (len != NULL), return ERR_ARG;);
 659              		.loc 1 199 3 discriminator 1 view .LVU192
 660 003a 6FF00F00 		mvn	r0, #15
 199:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   LWIP_ERROR("netbuf_data: invalid len", (len != NULL), return ERR_ARG;);
 661              		.loc 1 199 3 is_stmt 0 view .LVU193
 662 003e ECE7     		b	.L50
 663              	.LVL62:
 664              	.L57:
 200:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** 
 665              		.loc 1 200 3 is_stmt 1 discriminator 1 view .LVU194
 200:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** 
 666              		.loc 1 200 3 discriminator 1 view .LVU195
 667 0040 054B     		ldr	r3, .L58
 668 0042 C822     		movs	r2, #200
 669              	.LVL63:
 200:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cclvXS5R.s 			page 18


 670              		.loc 1 200 3 is_stmt 0 discriminator 1 view .LVU196
 671 0044 0849     		ldr	r1, .L58+16
 672              	.LVL64:
 200:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** 
 673              		.loc 1 200 3 discriminator 1 view .LVU197
 674 0046 0648     		ldr	r0, .L58+8
 675              	.LVL65:
 200:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** 
 676              		.loc 1 200 3 discriminator 1 view .LVU198
 677 0048 FFF7FEFF 		bl	printf
 678              	.LVL66:
 200:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** 
 679              		.loc 1 200 3 is_stmt 1 discriminator 1 view .LVU199
 200:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** 
 680              		.loc 1 200 3 discriminator 1 view .LVU200
 681 004c 6FF00F00 		mvn	r0, #15
 200:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** 
 682              		.loc 1 200 3 is_stmt 0 view .LVU201
 683 0050 E3E7     		b	.L50
 684              	.LVL67:
 685              	.L53:
 203:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   }
 686              		.loc 1 203 12 view .LVU202
 687 0052 6FF00100 		mvn	r0, #1
 688 0056 E0E7     		b	.L50
 689              	.L59:
 690              		.align	2
 691              	.L58:
 692 0058 00000000 		.word	.LC0
 693 005c 00000000 		.word	.LC8
 694 0060 4C000000 		.word	.LC2
 695 0064 1C000000 		.word	.LC9
 696 0068 3C000000 		.word	.LC10
 697              		.cfi_endproc
 698              	.LFE176:
 700              		.section	.rodata.netbuf_next.str1.4,"aMS",%progbits,1
 701              		.align	2
 702              	.LC11:
 703 0000 6E657462 		.ascii	"netbuf_next: invalid buf\000"
 703      75665F6E 
 703      6578743A 
 703      20696E76 
 703      616C6964 
 704              		.section	.text.netbuf_next,"ax",%progbits
 705              		.align	1
 706              		.global	netbuf_next
 707              		.syntax unified
 708              		.thumb
 709              		.thumb_func
 711              	netbuf_next:
 712              	.LVL68:
 713              	.LFB177:
 209:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** 
 210:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** /**
 211:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  * @ingroup netbuf
 212:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  * Move the current data pointer of a packet buffer contained in a netbuf
 213:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  * to the next part.
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cclvXS5R.s 			page 19


 214:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  * The packet buffer itself is not modified.
 215:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  *
 216:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  * @param buf the netbuf to modify
 217:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  * @return -1 if there is no next part
 218:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  *         1  if moved to the next part but now there is no next part
 219:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  *         0  if moved to the next part and there are still more parts
 220:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  */
 221:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** s8_t
 222:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** netbuf_next(struct netbuf *buf)
 223:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** {
 714              		.loc 1 223 1 is_stmt 1 view -0
 715              		.cfi_startproc
 716              		@ args = 0, pretend = 0, frame = 0
 717              		@ frame_needed = 0, uses_anonymous_args = 0
 718              		.loc 1 223 1 is_stmt 0 view .LVU204
 719 0000 08B5     		push	{r3, lr}
 720              	.LCFI8:
 721              		.cfi_def_cfa_offset 8
 722              		.cfi_offset 3, -8
 723              		.cfi_offset 14, -4
 224:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   LWIP_ERROR("netbuf_next: invalid buf", (buf != NULL), return -1;);
 724              		.loc 1 224 3 is_stmt 1 view .LVU205
 725              		.loc 1 224 3 view .LVU206
 726 0002 38B1     		cbz	r0, .L66
 727              		.loc 1 224 3 discriminator 2 view .LVU207
 728              		.loc 1 224 3 discriminator 2 view .LVU208
 225:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   if (buf->ptr->next == NULL) {
 729              		.loc 1 225 3 view .LVU209
 730              		.loc 1 225 10 is_stmt 0 view .LVU210
 731 0004 4368     		ldr	r3, [r0, #4]
 732              		.loc 1 225 15 view .LVU211
 733 0006 1B68     		ldr	r3, [r3]
 734              		.loc 1 225 6 view .LVU212
 735 0008 6BB1     		cbz	r3, .L63
 226:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****     return -1;
 227:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   }
 228:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   buf->ptr = buf->ptr->next;
 736              		.loc 1 228 3 is_stmt 1 view .LVU213
 737              		.loc 1 228 12 is_stmt 0 view .LVU214
 738 000a 4360     		str	r3, [r0, #4]
 229:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   if (buf->ptr->next == NULL) {
 739              		.loc 1 229 3 is_stmt 1 view .LVU215
 740              		.loc 1 229 15 is_stmt 0 view .LVU216
 741 000c 1B68     		ldr	r3, [r3]
 742              		.loc 1 229 6 view .LVU217
 743 000e 6BB1     		cbz	r3, .L64
 230:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****     return 1;
 231:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   }
 232:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   return 0;
 744              		.loc 1 232 10 view .LVU218
 745 0010 0020     		movs	r0, #0
 746              	.LVL69:
 747              	.L62:
 233:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** }
 748              		.loc 1 233 1 view .LVU219
 749 0012 08BD     		pop	{r3, pc}
 750              	.LVL70:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cclvXS5R.s 			page 20


 751              	.L66:
 224:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   if (buf->ptr->next == NULL) {
 752              		.loc 1 224 3 is_stmt 1 discriminator 1 view .LVU220
 224:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   if (buf->ptr->next == NULL) {
 753              		.loc 1 224 3 discriminator 1 view .LVU221
 754 0014 064B     		ldr	r3, .L67
 755 0016 E022     		movs	r2, #224
 756 0018 0649     		ldr	r1, .L67+4
 757 001a 0748     		ldr	r0, .L67+8
 758              	.LVL71:
 224:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   if (buf->ptr->next == NULL) {
 759              		.loc 1 224 3 is_stmt 0 discriminator 1 view .LVU222
 760 001c FFF7FEFF 		bl	printf
 761              	.LVL72:
 224:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   if (buf->ptr->next == NULL) {
 762              		.loc 1 224 3 is_stmt 1 discriminator 1 view .LVU223
 224:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   if (buf->ptr->next == NULL) {
 763              		.loc 1 224 3 discriminator 1 view .LVU224
 764 0020 4FF0FF30 		mov	r0, #-1
 224:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   if (buf->ptr->next == NULL) {
 765              		.loc 1 224 3 is_stmt 0 view .LVU225
 766 0024 F5E7     		b	.L62
 767              	.LVL73:
 768              	.L63:
 226:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   }
 769              		.loc 1 226 12 view .LVU226
 770 0026 4FF0FF30 		mov	r0, #-1
 771              	.LVL74:
 226:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   }
 772              		.loc 1 226 12 view .LVU227
 773 002a F2E7     		b	.L62
 774              	.LVL75:
 775              	.L64:
 230:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   }
 776              		.loc 1 230 12 view .LVU228
 777 002c 0120     		movs	r0, #1
 778              	.LVL76:
 230:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   }
 779              		.loc 1 230 12 view .LVU229
 780 002e F0E7     		b	.L62
 781              	.L68:
 782              		.align	2
 783              	.L67:
 784 0030 00000000 		.word	.LC0
 785 0034 00000000 		.word	.LC11
 786 0038 4C000000 		.word	.LC2
 787              		.cfi_endproc
 788              	.LFE177:
 790              		.section	.rodata.netbuf_first.str1.4,"aMS",%progbits,1
 791              		.align	2
 792              	.LC12:
 793 0000 6E657462 		.ascii	"netbuf_first: invalid buf\000"
 793      75665F66 
 793      69727374 
 793      3A20696E 
 793      76616C69 
 794              		.section	.text.netbuf_first,"ax",%progbits
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cclvXS5R.s 			page 21


 795              		.align	1
 796              		.global	netbuf_first
 797              		.syntax unified
 798              		.thumb
 799              		.thumb_func
 801              	netbuf_first:
 802              	.LVL77:
 803              	.LFB178:
 234:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** 
 235:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** /**
 236:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  * @ingroup netbuf
 237:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  * Move the current data pointer of a packet buffer contained in a netbuf
 238:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  * to the beginning of the packet.
 239:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  * The packet buffer itself is not modified.
 240:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  *
 241:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  * @param buf the netbuf to modify
 242:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****  */
 243:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** void
 244:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** netbuf_first(struct netbuf *buf)
 245:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** {
 804              		.loc 1 245 1 is_stmt 1 view -0
 805              		.cfi_startproc
 806              		@ args = 0, pretend = 0, frame = 0
 807              		@ frame_needed = 0, uses_anonymous_args = 0
 808              		.loc 1 245 1 is_stmt 0 view .LVU231
 809 0000 08B5     		push	{r3, lr}
 810              	.LCFI9:
 811              		.cfi_def_cfa_offset 8
 812              		.cfi_offset 3, -8
 813              		.cfi_offset 14, -4
 246:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   LWIP_ERROR("netbuf_first: invalid buf", (buf != NULL), return;);
 814              		.loc 1 246 3 is_stmt 1 view .LVU232
 815              		.loc 1 246 3 view .LVU233
 816 0002 10B1     		cbz	r0, .L73
 817              		.loc 1 246 3 discriminator 2 view .LVU234
 818              		.loc 1 246 3 discriminator 2 view .LVU235
 247:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   buf->ptr = buf->p;
 819              		.loc 1 247 3 view .LVU236
 820              		.loc 1 247 17 is_stmt 0 view .LVU237
 821 0004 0268     		ldr	r2, [r0]
 822              		.loc 1 247 12 view .LVU238
 823 0006 4260     		str	r2, [r0, #4]
 824              	.LVL78:
 825              	.L69:
 248:Middlewares/Third_Party/LwIP/src/api/netbuf.c **** }
 826              		.loc 1 248 1 view .LVU239
 827 0008 08BD     		pop	{r3, pc}
 828              	.LVL79:
 829              	.L73:
 246:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   LWIP_ERROR("netbuf_first: invalid buf", (buf != NULL), return;);
 830              		.loc 1 246 3 is_stmt 1 discriminator 1 view .LVU240
 246:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   LWIP_ERROR("netbuf_first: invalid buf", (buf != NULL), return;);
 831              		.loc 1 246 3 discriminator 1 view .LVU241
 832 000a 034B     		ldr	r3, .L74
 833 000c F622     		movs	r2, #246
 834 000e 0349     		ldr	r1, .L74+4
 835 0010 0348     		ldr	r0, .L74+8
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cclvXS5R.s 			page 22


 836              	.LVL80:
 246:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   LWIP_ERROR("netbuf_first: invalid buf", (buf != NULL), return;);
 837              		.loc 1 246 3 is_stmt 0 discriminator 1 view .LVU242
 838 0012 FFF7FEFF 		bl	printf
 839              	.LVL81:
 246:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   LWIP_ERROR("netbuf_first: invalid buf", (buf != NULL), return;);
 840              		.loc 1 246 3 is_stmt 1 discriminator 1 view .LVU243
 246:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   LWIP_ERROR("netbuf_first: invalid buf", (buf != NULL), return;);
 841              		.loc 1 246 3 discriminator 1 view .LVU244
 246:Middlewares/Third_Party/LwIP/src/api/netbuf.c ****   LWIP_ERROR("netbuf_first: invalid buf", (buf != NULL), return;);
 842              		.loc 1 246 3 is_stmt 0 view .LVU245
 843 0016 F7E7     		b	.L69
 844              	.L75:
 845              		.align	2
 846              	.L74:
 847 0018 00000000 		.word	.LC0
 848 001c 00000000 		.word	.LC12
 849 0020 4C000000 		.word	.LC2
 850              		.cfi_endproc
 851              	.LFE178:
 853              		.text
 854              	.Letext0:
 855              		.file 2 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin/../lib/gcc/arm-none-eabi/13.3.1/../
 856              		.file 3 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin/../lib/gcc/arm-none-eabi/13.3.1/../
 857              		.file 4 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin/../lib/gcc/arm-none-eabi/13.3.1/inc
 858              		.file 5 "Middlewares/Third_Party/LwIP/src/include/lwip/arch.h"
 859              		.file 6 "Middlewares/Third_Party/LwIP/src/include/lwip/err.h"
 860              		.file 7 "Middlewares/Third_Party/LwIP/src/include/lwip/pbuf.h"
 861              		.file 8 "Middlewares/Third_Party/LwIP/src/include/lwip/ip4_addr.h"
 862              		.file 9 "Middlewares/Third_Party/LwIP/src/include/lwip/ip_addr.h"
 863              		.file 10 "Middlewares/Third_Party/LwIP/src/include/lwip/netbuf.h"
 864              		.file 11 "Middlewares/Third_Party/LwIP/src/include/lwip/memp.h"
 865              		.file 12 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin/../lib/gcc/arm-none-eabi/13.3.1/..
 866              		.file 13 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin/../lib/gcc/arm-none-eabi/13.3.1/..
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cclvXS5R.s 			page 23


DEFINED SYMBOLS
                            *ABS*:00000000 netbuf.c
C:\Users\<USER>\AppData\Local\Temp\cclvXS5R.s:20     .text.netbuf_new:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cclvXS5R.s:26     .text.netbuf_new:00000000 netbuf_new
C:\Users\<USER>\AppData\Local\Temp\cclvXS5R.s:65     .text.netbuf_delete:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cclvXS5R.s:71     .text.netbuf_delete:00000000 netbuf_delete
C:\Users\<USER>\AppData\Local\Temp\cclvXS5R.s:123    .rodata.netbuf_alloc.str1.4:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\cclvXS5R.s:136    .text.netbuf_alloc:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cclvXS5R.s:142    .text.netbuf_alloc:00000000 netbuf_alloc
C:\Users\<USER>\AppData\Local\Temp\cclvXS5R.s:238    .text.netbuf_alloc:0000004c $d
C:\Users\<USER>\AppData\Local\Temp\cclvXS5R.s:246    .rodata.netbuf_free.str1.4:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\cclvXS5R.s:250    .text.netbuf_free:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cclvXS5R.s:256    .text.netbuf_free:00000000 netbuf_free
C:\Users\<USER>\AppData\Local\Temp\cclvXS5R.s:314    .text.netbuf_free:00000024 $d
C:\Users\<USER>\AppData\Local\Temp\cclvXS5R.s:321    .rodata.netbuf_ref.str1.4:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\cclvXS5R.s:325    .text.netbuf_ref:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cclvXS5R.s:331    .text.netbuf_ref:00000000 netbuf_ref
C:\Users\<USER>\AppData\Local\Temp\cclvXS5R.s:434    .text.netbuf_ref:00000050 $d
C:\Users\<USER>\AppData\Local\Temp\cclvXS5R.s:441    .rodata.netbuf_chain.str1.4:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\cclvXS5R.s:448    .text.netbuf_chain:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cclvXS5R.s:454    .text.netbuf_chain:00000000 netbuf_chain
C:\Users\<USER>\AppData\Local\Temp\cclvXS5R.s:542    .text.netbuf_chain:0000003c $d
C:\Users\<USER>\AppData\Local\Temp\cclvXS5R.s:550    .rodata.netbuf_data.str1.4:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\cclvXS5R.s:560    .text.netbuf_data:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cclvXS5R.s:566    .text.netbuf_data:00000000 netbuf_data
C:\Users\<USER>\AppData\Local\Temp\cclvXS5R.s:692    .text.netbuf_data:00000058 $d
C:\Users\<USER>\AppData\Local\Temp\cclvXS5R.s:701    .rodata.netbuf_next.str1.4:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\cclvXS5R.s:705    .text.netbuf_next:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cclvXS5R.s:711    .text.netbuf_next:00000000 netbuf_next
C:\Users\<USER>\AppData\Local\Temp\cclvXS5R.s:784    .text.netbuf_next:00000030 $d
C:\Users\<USER>\AppData\Local\Temp\cclvXS5R.s:791    .rodata.netbuf_first.str1.4:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\cclvXS5R.s:795    .text.netbuf_first:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cclvXS5R.s:801    .text.netbuf_first:00000000 netbuf_first
C:\Users\<USER>\AppData\Local\Temp\cclvXS5R.s:847    .text.netbuf_first:00000018 $d

UNDEFINED SYMBOLS
memp_malloc
pbuf_free
memp_free
pbuf_alloc
printf
pbuf_cat
