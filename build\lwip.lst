ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccNX4wXI.s 			page 1


   1              		.cpu cortex-m7
   2              		.arch armv7e-m
   3              		.fpu fpv5-d16
   4              		.eabi_attribute 28, 1
   5              		.eabi_attribute 20, 1
   6              		.eabi_attribute 21, 1
   7              		.eabi_attribute 23, 3
   8              		.eabi_attribute 24, 1
   9              		.eabi_attribute 25, 1
  10              		.eabi_attribute 26, 1
  11              		.eabi_attribute 30, 1
  12              		.eabi_attribute 34, 1
  13              		.eabi_attribute 18, 4
  14              		.file	"lwip.c"
  15              		.text
  16              	.Ltext0:
  17              		.cfi_sections	.debug_frame
  18              		.file 1 "LWIP/App/lwip.c"
  19              		.section	.text.ethernet_link_status_updated,"ax",%progbits
  20              		.align	1
  21              		.syntax unified
  22              		.thumb
  23              		.thumb_func
  25              	ethernet_link_status_updated:
  26              	.LVL0:
  27              	.LFB175:
   1:LWIP/App/lwip.c **** /* USER CODE BEGIN Header */
   2:LWIP/App/lwip.c **** /**
   3:LWIP/App/lwip.c ****  ******************************************************************************
   4:LWIP/App/lwip.c ****   * File Name          : LWIP.c
   5:LWIP/App/lwip.c ****   * Description        : This file provides initialization code for LWIP
   6:LWIP/App/lwip.c ****   *                      middleWare.
   7:LWIP/App/lwip.c ****   ******************************************************************************
   8:LWIP/App/lwip.c ****   * @attention
   9:LWIP/App/lwip.c ****   *
  10:LWIP/App/lwip.c ****   * Copyright (c) 2025 STMicroelectronics.
  11:LWIP/App/lwip.c ****   * All rights reserved.
  12:LWIP/App/lwip.c ****   *
  13:LWIP/App/lwip.c ****   * This software is licensed under terms that can be found in the LICENSE file
  14:LWIP/App/lwip.c ****   * in the root directory of this software component.
  15:LWIP/App/lwip.c ****   * If no LICENSE file comes with this software, it is provided AS-IS.
  16:LWIP/App/lwip.c ****   *
  17:LWIP/App/lwip.c ****   ******************************************************************************
  18:LWIP/App/lwip.c ****   */
  19:LWIP/App/lwip.c **** /* USER CODE END Header */
  20:LWIP/App/lwip.c **** 
  21:LWIP/App/lwip.c **** /* Includes ------------------------------------------------------------------*/
  22:LWIP/App/lwip.c **** #include "lwip.h"
  23:LWIP/App/lwip.c **** #include "lwip/init.h"
  24:LWIP/App/lwip.c **** #include "lwip/netif.h"
  25:LWIP/App/lwip.c **** #if defined ( __CC_ARM )  /* MDK ARM Compiler */
  26:LWIP/App/lwip.c **** #include "lwip/sio.h"
  27:LWIP/App/lwip.c **** #endif /* MDK ARM Compiler */
  28:LWIP/App/lwip.c **** #include "ethernetif.h"
  29:LWIP/App/lwip.c **** #include <string.h>
  30:LWIP/App/lwip.c **** 
  31:LWIP/App/lwip.c **** /* USER CODE BEGIN 0 */
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccNX4wXI.s 			page 2


  32:LWIP/App/lwip.c **** 
  33:LWIP/App/lwip.c **** /* USER CODE END 0 */
  34:LWIP/App/lwip.c **** /* Private function prototypes -----------------------------------------------*/
  35:LWIP/App/lwip.c **** static void ethernet_link_status_updated(struct netif *netif);
  36:LWIP/App/lwip.c **** /* ETH Variables initialization ----------------------------------------------*/
  37:LWIP/App/lwip.c **** void Error_Handler(void);
  38:LWIP/App/lwip.c **** 
  39:LWIP/App/lwip.c **** /* USER CODE BEGIN 1 */
  40:LWIP/App/lwip.c **** 
  41:LWIP/App/lwip.c **** /* USER CODE END 1 */
  42:LWIP/App/lwip.c **** 
  43:LWIP/App/lwip.c **** /* Variables Initialization */
  44:LWIP/App/lwip.c **** struct netif gnetif;
  45:LWIP/App/lwip.c **** ip4_addr_t ipaddr;
  46:LWIP/App/lwip.c **** ip4_addr_t netmask;
  47:LWIP/App/lwip.c **** ip4_addr_t gw;
  48:LWIP/App/lwip.c **** uint8_t IP_ADDRESS[4];
  49:LWIP/App/lwip.c **** uint8_t NETMASK_ADDRESS[4];
  50:LWIP/App/lwip.c **** uint8_t GATEWAY_ADDRESS[4];
  51:LWIP/App/lwip.c **** /* USER CODE BEGIN OS_THREAD_ATTR_CMSIS_RTOS_V2 */
  52:LWIP/App/lwip.c **** #define INTERFACE_THREAD_STACK_SIZE ( 1024 )
  53:LWIP/App/lwip.c **** osThreadAttr_t attributes;
  54:LWIP/App/lwip.c **** /* USER CODE END OS_THREAD_ATTR_CMSIS_RTOS_V2 */
  55:LWIP/App/lwip.c **** 
  56:LWIP/App/lwip.c **** /* USER CODE BEGIN 2 */
  57:LWIP/App/lwip.c **** 
  58:LWIP/App/lwip.c **** /* USER CODE END 2 */
  59:LWIP/App/lwip.c **** 
  60:LWIP/App/lwip.c **** /**
  61:LWIP/App/lwip.c ****   * LwIP initialization function
  62:LWIP/App/lwip.c ****   */
  63:LWIP/App/lwip.c **** void MX_LWIP_Init(void)
  64:LWIP/App/lwip.c **** {
  65:LWIP/App/lwip.c ****   /* IP addresses initialization */
  66:LWIP/App/lwip.c ****   IP_ADDRESS[0] = 192;
  67:LWIP/App/lwip.c ****   IP_ADDRESS[1] = 168;
  68:LWIP/App/lwip.c ****   IP_ADDRESS[2] = 1;
  69:LWIP/App/lwip.c ****   IP_ADDRESS[3] = 200;
  70:LWIP/App/lwip.c ****   NETMASK_ADDRESS[0] = 255;
  71:LWIP/App/lwip.c ****   NETMASK_ADDRESS[1] = 255;
  72:LWIP/App/lwip.c ****   NETMASK_ADDRESS[2] = 255;
  73:LWIP/App/lwip.c ****   NETMASK_ADDRESS[3] = 0;
  74:LWIP/App/lwip.c ****   GATEWAY_ADDRESS[0] = 0;
  75:LWIP/App/lwip.c ****   GATEWAY_ADDRESS[1] = 0;
  76:LWIP/App/lwip.c ****   GATEWAY_ADDRESS[2] = 0;
  77:LWIP/App/lwip.c ****   GATEWAY_ADDRESS[3] = 0;
  78:LWIP/App/lwip.c **** 
  79:LWIP/App/lwip.c **** /* USER CODE BEGIN IP_ADDRESSES */
  80:LWIP/App/lwip.c **** /* USER CODE END IP_ADDRESSES */
  81:LWIP/App/lwip.c **** 
  82:LWIP/App/lwip.c ****   /* Initialize the LwIP stack with RTOS */
  83:LWIP/App/lwip.c ****   tcpip_init( NULL, NULL );
  84:LWIP/App/lwip.c **** 
  85:LWIP/App/lwip.c ****   /* IP addresses initialization without DHCP (IPv4) */
  86:LWIP/App/lwip.c ****   IP4_ADDR(&ipaddr, IP_ADDRESS[0], IP_ADDRESS[1], IP_ADDRESS[2], IP_ADDRESS[3]);
  87:LWIP/App/lwip.c ****   IP4_ADDR(&netmask, NETMASK_ADDRESS[0], NETMASK_ADDRESS[1] , NETMASK_ADDRESS[2], NETMASK_ADDRESS[3
  88:LWIP/App/lwip.c ****   IP4_ADDR(&gw, GATEWAY_ADDRESS[0], GATEWAY_ADDRESS[1], GATEWAY_ADDRESS[2], GATEWAY_ADDRESS[3]);
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccNX4wXI.s 			page 3


  89:LWIP/App/lwip.c **** 
  90:LWIP/App/lwip.c ****   /* add the network interface (IPv4/IPv6) with RTOS */
  91:LWIP/App/lwip.c ****   netif_add(&gnetif, &ipaddr, &netmask, &gw, NULL, &ethernetif_init, &tcpip_input);
  92:LWIP/App/lwip.c **** 
  93:LWIP/App/lwip.c ****   /* Registers the default network interface */
  94:LWIP/App/lwip.c ****   netif_set_default(&gnetif);
  95:LWIP/App/lwip.c **** 
  96:LWIP/App/lwip.c ****   /* We must always bring the network interface up connection or not... */
  97:LWIP/App/lwip.c ****   netif_set_up(&gnetif);
  98:LWIP/App/lwip.c **** 
  99:LWIP/App/lwip.c ****   /* Set the link callback function, this function is called on change of link status*/
 100:LWIP/App/lwip.c ****   netif_set_link_callback(&gnetif, ethernet_link_status_updated);
 101:LWIP/App/lwip.c **** 
 102:LWIP/App/lwip.c ****   /* Create the Ethernet link handler thread */
 103:LWIP/App/lwip.c **** /* USER CODE BEGIN H7_OS_THREAD_NEW_CMSIS_RTOS_V2 */
 104:LWIP/App/lwip.c ****   memset(&attributes, 0x0, sizeof(osThreadAttr_t));
 105:LWIP/App/lwip.c ****   attributes.name = "EthLink";
 106:LWIP/App/lwip.c ****   attributes.stack_size = INTERFACE_THREAD_STACK_SIZE;
 107:LWIP/App/lwip.c ****   attributes.priority = osPriorityBelowNormal;
 108:LWIP/App/lwip.c ****   osThreadNew(ethernet_link_thread, &gnetif, &attributes);
 109:LWIP/App/lwip.c **** /* USER CODE END H7_OS_THREAD_NEW_CMSIS_RTOS_V2 */
 110:LWIP/App/lwip.c **** 
 111:LWIP/App/lwip.c **** /* USER CODE BEGIN 3 */
 112:LWIP/App/lwip.c **** 
 113:LWIP/App/lwip.c **** /* USER CODE END 3 */
 114:LWIP/App/lwip.c **** }
 115:LWIP/App/lwip.c **** 
 116:LWIP/App/lwip.c **** #ifdef USE_OBSOLETE_USER_CODE_SECTION_4
 117:LWIP/App/lwip.c **** /* Kept to help code migration. (See new 4_1, 4_2... sections) */
 118:LWIP/App/lwip.c **** /* Avoid to use this user section which will become obsolete. */
 119:LWIP/App/lwip.c **** /* USER CODE BEGIN 4 */
 120:LWIP/App/lwip.c **** /* USER CODE END 4 */
 121:LWIP/App/lwip.c **** #endif
 122:LWIP/App/lwip.c **** 
 123:LWIP/App/lwip.c **** /**
 124:LWIP/App/lwip.c ****   * @brief  Notify the User about the network interface config status
 125:LWIP/App/lwip.c ****   * @param  netif: the network interface
 126:LWIP/App/lwip.c ****   * @retval None
 127:LWIP/App/lwip.c ****   */
 128:LWIP/App/lwip.c **** static void ethernet_link_status_updated(struct netif *netif)
 129:LWIP/App/lwip.c **** {
  28              		.loc 1 129 1 view -0
  29              		.cfi_startproc
  30              		@ args = 0, pretend = 0, frame = 0
  31              		@ frame_needed = 0, uses_anonymous_args = 0
  32              		@ link register save eliminated.
 130:LWIP/App/lwip.c ****   if (netif_is_up(netif))
  33              		.loc 1 130 3 view .LVU1
 131:LWIP/App/lwip.c ****   {
 132:LWIP/App/lwip.c **** /* USER CODE BEGIN 5 */
 133:LWIP/App/lwip.c **** /* USER CODE END 5 */
 134:LWIP/App/lwip.c ****   }
  34              		.loc 1 134 3 view .LVU2
 135:LWIP/App/lwip.c ****   else /* netif is down */
 136:LWIP/App/lwip.c ****   {
 137:LWIP/App/lwip.c **** /* USER CODE BEGIN 6 */
 138:LWIP/App/lwip.c **** /* USER CODE END 6 */
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccNX4wXI.s 			page 4


 139:LWIP/App/lwip.c ****   }
  35              		.loc 1 139 3 view .LVU3
 140:LWIP/App/lwip.c **** }
  36              		.loc 1 140 1 is_stmt 0 view .LVU4
  37 0000 7047     		bx	lr
  38              		.cfi_endproc
  39              	.LFE175:
  41              		.section	.rodata.MX_LWIP_Init.str1.4,"aMS",%progbits,1
  42              		.align	2
  43              	.LC0:
  44 0000 4574684C 		.ascii	"EthLink\000"
  44      696E6B00 
  45              		.section	.text.MX_LWIP_Init,"ax",%progbits
  46              		.align	1
  47              		.global	MX_LWIP_Init
  48              		.syntax unified
  49              		.thumb
  50              		.thumb_func
  52              	MX_LWIP_Init:
  53              	.LFB174:
  64:LWIP/App/lwip.c ****   /* IP addresses initialization */
  54              		.loc 1 64 1 is_stmt 1 view -0
  55              		.cfi_startproc
  56              		@ args = 0, pretend = 0, frame = 0
  57              		@ frame_needed = 0, uses_anonymous_args = 0
  58 0000 F0B5     		push	{r4, r5, r6, r7, lr}
  59              	.LCFI0:
  60              		.cfi_def_cfa_offset 20
  61              		.cfi_offset 4, -20
  62              		.cfi_offset 5, -16
  63              		.cfi_offset 6, -12
  64              		.cfi_offset 7, -8
  65              		.cfi_offset 14, -4
  66 0002 85B0     		sub	sp, sp, #20
  67              	.LCFI1:
  68              		.cfi_def_cfa_offset 40
  66:LWIP/App/lwip.c ****   IP_ADDRESS[1] = 168;
  69              		.loc 1 66 3 view .LVU6
  66:LWIP/App/lwip.c ****   IP_ADDRESS[1] = 168;
  70              		.loc 1 66 17 is_stmt 0 view .LVU7
  71 0004 424F     		ldr	r7, .L4
  72 0006 C023     		movs	r3, #192
  73 0008 3B70     		strb	r3, [r7]
  67:LWIP/App/lwip.c ****   IP_ADDRESS[2] = 1;
  74              		.loc 1 67 3 is_stmt 1 view .LVU8
  67:LWIP/App/lwip.c ****   IP_ADDRESS[2] = 1;
  75              		.loc 1 67 17 is_stmt 0 view .LVU9
  76 000a A823     		movs	r3, #168
  77 000c 7B70     		strb	r3, [r7, #1]
  68:LWIP/App/lwip.c ****   IP_ADDRESS[3] = 200;
  78              		.loc 1 68 3 is_stmt 1 view .LVU10
  68:LWIP/App/lwip.c ****   IP_ADDRESS[3] = 200;
  79              		.loc 1 68 17 is_stmt 0 view .LVU11
  80 000e 0123     		movs	r3, #1
  81 0010 BB70     		strb	r3, [r7, #2]
  69:LWIP/App/lwip.c ****   NETMASK_ADDRESS[0] = 255;
  82              		.loc 1 69 3 is_stmt 1 view .LVU12
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccNX4wXI.s 			page 5


  69:LWIP/App/lwip.c ****   NETMASK_ADDRESS[0] = 255;
  83              		.loc 1 69 17 is_stmt 0 view .LVU13
  84 0012 C823     		movs	r3, #200
  85 0014 FB70     		strb	r3, [r7, #3]
  70:LWIP/App/lwip.c ****   NETMASK_ADDRESS[1] = 255;
  86              		.loc 1 70 3 is_stmt 1 view .LVU14
  70:LWIP/App/lwip.c ****   NETMASK_ADDRESS[1] = 255;
  87              		.loc 1 70 22 is_stmt 0 view .LVU15
  88 0016 3F4E     		ldr	r6, .L4+4
  89 0018 FF23     		movs	r3, #255
  90 001a 3370     		strb	r3, [r6]
  71:LWIP/App/lwip.c ****   NETMASK_ADDRESS[2] = 255;
  91              		.loc 1 71 3 is_stmt 1 view .LVU16
  71:LWIP/App/lwip.c ****   NETMASK_ADDRESS[2] = 255;
  92              		.loc 1 71 22 is_stmt 0 view .LVU17
  93 001c 7370     		strb	r3, [r6, #1]
  72:LWIP/App/lwip.c ****   NETMASK_ADDRESS[3] = 0;
  94              		.loc 1 72 3 is_stmt 1 view .LVU18
  72:LWIP/App/lwip.c ****   NETMASK_ADDRESS[3] = 0;
  95              		.loc 1 72 22 is_stmt 0 view .LVU19
  96 001e B370     		strb	r3, [r6, #2]
  73:LWIP/App/lwip.c ****   GATEWAY_ADDRESS[0] = 0;
  97              		.loc 1 73 3 is_stmt 1 view .LVU20
  73:LWIP/App/lwip.c ****   GATEWAY_ADDRESS[0] = 0;
  98              		.loc 1 73 22 is_stmt 0 view .LVU21
  99 0020 0024     		movs	r4, #0
 100 0022 F470     		strb	r4, [r6, #3]
  74:LWIP/App/lwip.c ****   GATEWAY_ADDRESS[1] = 0;
 101              		.loc 1 74 3 is_stmt 1 view .LVU22
  74:LWIP/App/lwip.c ****   GATEWAY_ADDRESS[1] = 0;
 102              		.loc 1 74 22 is_stmt 0 view .LVU23
 103 0024 3C4D     		ldr	r5, .L4+8
 104 0026 2C70     		strb	r4, [r5]
  75:LWIP/App/lwip.c ****   GATEWAY_ADDRESS[2] = 0;
 105              		.loc 1 75 3 is_stmt 1 view .LVU24
  75:LWIP/App/lwip.c ****   GATEWAY_ADDRESS[2] = 0;
 106              		.loc 1 75 22 is_stmt 0 view .LVU25
 107 0028 6C70     		strb	r4, [r5, #1]
  76:LWIP/App/lwip.c ****   GATEWAY_ADDRESS[3] = 0;
 108              		.loc 1 76 3 is_stmt 1 view .LVU26
  76:LWIP/App/lwip.c ****   GATEWAY_ADDRESS[3] = 0;
 109              		.loc 1 76 22 is_stmt 0 view .LVU27
 110 002a AC70     		strb	r4, [r5, #2]
  77:LWIP/App/lwip.c **** 
 111              		.loc 1 77 3 is_stmt 1 view .LVU28
  77:LWIP/App/lwip.c **** 
 112              		.loc 1 77 22 is_stmt 0 view .LVU29
 113 002c EC70     		strb	r4, [r5, #3]
  83:LWIP/App/lwip.c **** 
 114              		.loc 1 83 3 is_stmt 1 view .LVU30
 115 002e 2146     		mov	r1, r4
 116 0030 2046     		mov	r0, r4
 117 0032 FFF7FEFF 		bl	tcpip_init
 118              	.LVL1:
  86:LWIP/App/lwip.c ****   IP4_ADDR(&netmask, NETMASK_ADDRESS[0], NETMASK_ADDRESS[1] , NETMASK_ADDRESS[2], NETMASK_ADDRESS[3
 119              		.loc 1 86 3 view .LVU31
 120 0036 3A78     		ldrb	r2, [r7]	@ zero_extendqisi2
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccNX4wXI.s 			page 6


 121 0038 7B78     		ldrb	r3, [r7, #1]	@ zero_extendqisi2
 122 003a 1B04     		lsls	r3, r3, #16
 123 003c 43EA0263 		orr	r3, r3, r2, lsl #24
 124 0040 BA78     		ldrb	r2, [r7, #2]	@ zero_extendqisi2
 125 0042 43EA0223 		orr	r3, r3, r2, lsl #8
 126 0046 F978     		ldrb	r1, [r7, #3]	@ zero_extendqisi2
 127 0048 0B43     		orrs	r3, r3, r1
 128 004a 1A02     		lsls	r2, r3, #8
 129 004c 02F47F02 		and	r2, r2, #16711680
 130 0050 42EA0162 		orr	r2, r2, r1, lsl #24
 131 0054 190A     		lsrs	r1, r3, #8
 132 0056 01F47F41 		and	r1, r1, #65280
 133 005a 0A43     		orrs	r2, r2, r1
 134 005c 42EA1363 		orr	r3, r2, r3, lsr #24
 135 0060 2E49     		ldr	r1, .L4+12
 136 0062 0B60     		str	r3, [r1]
  87:LWIP/App/lwip.c ****   IP4_ADDR(&gw, GATEWAY_ADDRESS[0], GATEWAY_ADDRESS[1], GATEWAY_ADDRESS[2], GATEWAY_ADDRESS[3]);
 137              		.loc 1 87 3 view .LVU32
 138 0064 3278     		ldrb	r2, [r6]	@ zero_extendqisi2
 139 0066 7378     		ldrb	r3, [r6, #1]	@ zero_extendqisi2
 140 0068 1B04     		lsls	r3, r3, #16
 141 006a 43EA0263 		orr	r3, r3, r2, lsl #24
 142 006e B278     		ldrb	r2, [r6, #2]	@ zero_extendqisi2
 143 0070 43EA0223 		orr	r3, r3, r2, lsl #8
 144 0074 F078     		ldrb	r0, [r6, #3]	@ zero_extendqisi2
 145 0076 0343     		orrs	r3, r3, r0
 146 0078 1A02     		lsls	r2, r3, #8
 147 007a 02F47F02 		and	r2, r2, #16711680
 148 007e 42EA0062 		orr	r2, r2, r0, lsl #24
 149 0082 180A     		lsrs	r0, r3, #8
 150 0084 00F47F40 		and	r0, r0, #65280
 151 0088 0243     		orrs	r2, r2, r0
 152 008a 42EA1363 		orr	r3, r2, r3, lsr #24
 153 008e 244A     		ldr	r2, .L4+16
 154 0090 1360     		str	r3, [r2]
  88:LWIP/App/lwip.c **** 
 155              		.loc 1 88 3 view .LVU33
 156 0092 2878     		ldrb	r0, [r5]	@ zero_extendqisi2
 157 0094 6B78     		ldrb	r3, [r5, #1]	@ zero_extendqisi2
 158 0096 1B04     		lsls	r3, r3, #16
 159 0098 43EA0063 		orr	r3, r3, r0, lsl #24
 160 009c A878     		ldrb	r0, [r5, #2]	@ zero_extendqisi2
 161 009e 43EA0023 		orr	r3, r3, r0, lsl #8
 162 00a2 ED78     		ldrb	r5, [r5, #3]	@ zero_extendqisi2
 163 00a4 2B43     		orrs	r3, r3, r5
 164 00a6 1802     		lsls	r0, r3, #8
 165 00a8 00F47F00 		and	r0, r0, #16711680
 166 00ac 40EA0560 		orr	r0, r0, r5, lsl #24
 167 00b0 1D0A     		lsrs	r5, r3, #8
 168 00b2 05F47F45 		and	r5, r5, #65280
 169 00b6 2843     		orrs	r0, r0, r5
 170 00b8 40EA1360 		orr	r0, r0, r3, lsr #24
 171 00bc 194B     		ldr	r3, .L4+20
 172 00be 1860     		str	r0, [r3]
  91:LWIP/App/lwip.c **** 
 173              		.loc 1 91 3 view .LVU34
 174 00c0 194D     		ldr	r5, .L4+24
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccNX4wXI.s 			page 7


 175 00c2 1A48     		ldr	r0, .L4+28
 176 00c4 0290     		str	r0, [sp, #8]
 177 00c6 1A48     		ldr	r0, .L4+32
 178 00c8 0190     		str	r0, [sp, #4]
 179 00ca 0094     		str	r4, [sp]
 180 00cc 2846     		mov	r0, r5
 181 00ce FFF7FEFF 		bl	netif_add
 182              	.LVL2:
  94:LWIP/App/lwip.c **** 
 183              		.loc 1 94 3 view .LVU35
 184 00d2 2846     		mov	r0, r5
 185 00d4 FFF7FEFF 		bl	netif_set_default
 186              	.LVL3:
  97:LWIP/App/lwip.c **** 
 187              		.loc 1 97 3 view .LVU36
 188 00d8 2846     		mov	r0, r5
 189 00da FFF7FEFF 		bl	netif_set_up
 190              	.LVL4:
 100:LWIP/App/lwip.c **** 
 191              		.loc 1 100 3 view .LVU37
 192 00de 1549     		ldr	r1, .L4+36
 193 00e0 2846     		mov	r0, r5
 194 00e2 FFF7FEFF 		bl	netif_set_link_callback
 195              	.LVL5:
 104:LWIP/App/lwip.c ****   attributes.name = "EthLink";
 196              		.loc 1 104 3 view .LVU38
 197 00e6 144E     		ldr	r6, .L4+40
 198 00e8 2422     		movs	r2, #36
 199 00ea 2146     		mov	r1, r4
 200 00ec 3046     		mov	r0, r6
 201 00ee FFF7FEFF 		bl	memset
 202              	.LVL6:
 105:LWIP/App/lwip.c ****   attributes.stack_size = INTERFACE_THREAD_STACK_SIZE;
 203              		.loc 1 105 3 view .LVU39
 105:LWIP/App/lwip.c ****   attributes.stack_size = INTERFACE_THREAD_STACK_SIZE;
 204              		.loc 1 105 19 is_stmt 0 view .LVU40
 205 00f2 124B     		ldr	r3, .L4+44
 206 00f4 3360     		str	r3, [r6]
 106:LWIP/App/lwip.c ****   attributes.priority = osPriorityBelowNormal;
 207              		.loc 1 106 3 is_stmt 1 view .LVU41
 106:LWIP/App/lwip.c ****   attributes.priority = osPriorityBelowNormal;
 208              		.loc 1 106 25 is_stmt 0 view .LVU42
 209 00f6 4FF48063 		mov	r3, #1024
 210 00fa 7361     		str	r3, [r6, #20]
 107:LWIP/App/lwip.c ****   osThreadNew(ethernet_link_thread, &gnetif, &attributes);
 211              		.loc 1 107 3 is_stmt 1 view .LVU43
 107:LWIP/App/lwip.c ****   osThreadNew(ethernet_link_thread, &gnetif, &attributes);
 212              		.loc 1 107 23 is_stmt 0 view .LVU44
 213 00fc 1023     		movs	r3, #16
 214 00fe B361     		str	r3, [r6, #24]
 108:LWIP/App/lwip.c **** /* USER CODE END H7_OS_THREAD_NEW_CMSIS_RTOS_V2 */
 215              		.loc 1 108 3 is_stmt 1 view .LVU45
 216 0100 3246     		mov	r2, r6
 217 0102 2946     		mov	r1, r5
 218 0104 0E48     		ldr	r0, .L4+48
 219 0106 FFF7FEFF 		bl	osThreadNew
 220              	.LVL7:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccNX4wXI.s 			page 8


 114:LWIP/App/lwip.c **** 
 221              		.loc 1 114 1 is_stmt 0 view .LVU46
 222 010a 05B0     		add	sp, sp, #20
 223              	.LCFI2:
 224              		.cfi_def_cfa_offset 20
 225              		@ sp needed
 226 010c F0BD     		pop	{r4, r5, r6, r7, pc}
 227              	.L5:
 228 010e 00BF     		.align	2
 229              	.L4:
 230 0110 00000000 		.word	IP_ADDRESS
 231 0114 00000000 		.word	NETMASK_ADDRESS
 232 0118 00000000 		.word	GATEWAY_ADDRESS
 233 011c 00000000 		.word	ipaddr
 234 0120 00000000 		.word	netmask
 235 0124 00000000 		.word	gw
 236 0128 00000000 		.word	gnetif
 237 012c 00000000 		.word	tcpip_input
 238 0130 00000000 		.word	ethernetif_init
 239 0134 00000000 		.word	ethernet_link_status_updated
 240 0138 00000000 		.word	attributes
 241 013c 00000000 		.word	.LC0
 242 0140 00000000 		.word	ethernet_link_thread
 243              		.cfi_endproc
 244              	.LFE174:
 246              		.global	attributes
 247              		.section	.bss.attributes,"aw",%nobits
 248              		.align	2
 251              	attributes:
 252 0000 00000000 		.space	36
 252      00000000 
 252      00000000 
 252      00000000 
 252      00000000 
 253              		.global	GATEWAY_ADDRESS
 254              		.section	.bss.GATEWAY_ADDRESS,"aw",%nobits
 255              		.align	2
 258              	GATEWAY_ADDRESS:
 259 0000 00000000 		.space	4
 260              		.global	NETMASK_ADDRESS
 261              		.section	.bss.NETMASK_ADDRESS,"aw",%nobits
 262              		.align	2
 265              	NETMASK_ADDRESS:
 266 0000 00000000 		.space	4
 267              		.global	IP_ADDRESS
 268              		.section	.bss.IP_ADDRESS,"aw",%nobits
 269              		.align	2
 272              	IP_ADDRESS:
 273 0000 00000000 		.space	4
 274              		.global	gw
 275              		.section	.bss.gw,"aw",%nobits
 276              		.align	2
 279              	gw:
 280 0000 00000000 		.space	4
 281              		.global	netmask
 282              		.section	.bss.netmask,"aw",%nobits
 283              		.align	2
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccNX4wXI.s 			page 9


 286              	netmask:
 287 0000 00000000 		.space	4
 288              		.global	ipaddr
 289              		.section	.bss.ipaddr,"aw",%nobits
 290              		.align	2
 293              	ipaddr:
 294 0000 00000000 		.space	4
 295              		.global	gnetif
 296              		.section	.bss.gnetif,"aw",%nobits
 297              		.align	2
 300              	gnetif:
 301 0000 00000000 		.space	52
 301      00000000 
 301      00000000 
 301      00000000 
 301      00000000 
 302              		.text
 303              	.Letext0:
 304              		.file 2 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\../lib/gcc/arm-none-eabi/13.3.1/..
 305              		.file 3 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\../lib/gcc/arm-none-eabi/13.3.1/..
 306              		.file 4 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\../lib/gcc/arm-none-eabi/13.3.1/in
 307              		.file 5 "Middlewares/Third_Party/LwIP/src/include/lwip/arch.h"
 308              		.file 6 "Middlewares/Third_Party/LwIP/src/include/lwip/err.h"
 309              		.file 7 "Middlewares/Third_Party/LwIP/src/include/lwip/pbuf.h"
 310              		.file 8 "Middlewares/Third_Party/LwIP/src/include/lwip/ip4_addr.h"
 311              		.file 9 "Middlewares/Third_Party/LwIP/src/include/lwip/ip_addr.h"
 312              		.file 10 "Middlewares/Third_Party/LwIP/src/include/lwip/netif.h"
 313              		.file 11 "Middlewares/Third_Party/LwIP/src/include/lwip/memp.h"
 314              		.file 12 "Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.h"
 315              		.file 13 "Middlewares/Third_Party/LwIP/src/include/lwip/tcpip.h"
 316              		.file 14 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\../lib/gcc/arm-none-eabi/13.3.1/.
 317              		.file 15 "LWIP/Target/ethernetif.h"
 318              		.file 16 "<built-in>"
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccNX4wXI.s 			page 10


DEFINED SYMBOLS
                            *ABS*:00000000 lwip.c
C:\Users\<USER>\AppData\Local\Temp\ccNX4wXI.s:20     .text.ethernet_link_status_updated:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccNX4wXI.s:25     .text.ethernet_link_status_updated:00000000 ethernet_link_status_updated
C:\Users\<USER>\AppData\Local\Temp\ccNX4wXI.s:42     .rodata.MX_LWIP_Init.str1.4:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\ccNX4wXI.s:46     .text.MX_LWIP_Init:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccNX4wXI.s:52     .text.MX_LWIP_Init:00000000 MX_LWIP_Init
C:\Users\<USER>\AppData\Local\Temp\ccNX4wXI.s:230    .text.MX_LWIP_Init:00000110 $d
C:\Users\<USER>\AppData\Local\Temp\ccNX4wXI.s:272    .bss.IP_ADDRESS:00000000 IP_ADDRESS
C:\Users\<USER>\AppData\Local\Temp\ccNX4wXI.s:265    .bss.NETMASK_ADDRESS:00000000 NETMASK_ADDRESS
C:\Users\<USER>\AppData\Local\Temp\ccNX4wXI.s:258    .bss.GATEWAY_ADDRESS:00000000 GATEWAY_ADDRESS
C:\Users\<USER>\AppData\Local\Temp\ccNX4wXI.s:293    .bss.ipaddr:00000000 ipaddr
C:\Users\<USER>\AppData\Local\Temp\ccNX4wXI.s:286    .bss.netmask:00000000 netmask
C:\Users\<USER>\AppData\Local\Temp\ccNX4wXI.s:279    .bss.gw:00000000 gw
C:\Users\<USER>\AppData\Local\Temp\ccNX4wXI.s:300    .bss.gnetif:00000000 gnetif
C:\Users\<USER>\AppData\Local\Temp\ccNX4wXI.s:251    .bss.attributes:00000000 attributes
C:\Users\<USER>\AppData\Local\Temp\ccNX4wXI.s:248    .bss.attributes:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\ccNX4wXI.s:255    .bss.GATEWAY_ADDRESS:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\ccNX4wXI.s:262    .bss.NETMASK_ADDRESS:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\ccNX4wXI.s:269    .bss.IP_ADDRESS:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\ccNX4wXI.s:276    .bss.gw:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\ccNX4wXI.s:283    .bss.netmask:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\ccNX4wXI.s:290    .bss.ipaddr:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\ccNX4wXI.s:297    .bss.gnetif:00000000 $d

UNDEFINED SYMBOLS
tcpip_init
netif_add
netif_set_default
netif_set_up
netif_set_link_callback
memset
osThreadNew
tcpip_input
ethernetif_init
ethernet_link_thread
