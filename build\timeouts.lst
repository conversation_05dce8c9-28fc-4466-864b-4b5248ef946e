ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccMzS9LP.s 			page 1


   1              		.cpu cortex-m7
   2              		.arch armv7e-m
   3              		.fpu fpv5-d16
   4              		.eabi_attribute 28, 1
   5              		.eabi_attribute 20, 1
   6              		.eabi_attribute 21, 1
   7              		.eabi_attribute 23, 3
   8              		.eabi_attribute 24, 1
   9              		.eabi_attribute 25, 1
  10              		.eabi_attribute 26, 1
  11              		.eabi_attribute 30, 1
  12              		.eabi_attribute 34, 1
  13              		.eabi_attribute 18, 4
  14              		.file	"timeouts.c"
  15              		.text
  16              	.Ltext0:
  17              		.cfi_sections	.debug_frame
  18              		.file 1 "Middlewares/Third_Party/LwIP/src/core/timeouts.c"
  19              		.section	.rodata.sys_timeout_abs.str1.4,"aMS",%progbits,1
  20              		.align	2
  21              	.LC0:
  22 0000 4D696464 		.ascii	"Middlewares/Third_Party/LwIP/src/core/timeouts.c\000"
  22      6C657761 
  22      7265732F 
  22      54686972 
  22      645F5061 
  23 0031 000000   		.align	2
  24              	.LC1:
  25 0034 7379735F 		.ascii	"sys_timeout: timeout != NULL, pool MEMP_SYS_TIMEOUT"
  25      74696D65 
  25      6F75743A 
  25      2074696D 
  25      656F7574 
  26 0067 20697320 		.ascii	" is empty\000"
  26      656D7074 
  26      7900
  27 0071 000000   		.align	2
  28              	.LC2:
  29 0074 41737365 		.ascii	"Assertion \"%s\" failed at line %d in %s\012\000"
  29      7274696F 
  29      6E202225 
  29      73222066 
  29      61696C65 
  30              		.section	.text.sys_timeout_abs,"ax",%progbits
  31              		.align	1
  32              		.syntax unified
  33              		.thumb
  34              		.thumb_func
  36              	sys_timeout_abs:
  37              	.LVL0:
  38              	.LFB176:
   1:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** /**
   2:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  * @file
   3:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  * Stack-internal timers implementation.
   4:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  * This file includes timer callbacks for stack-internal timers as well as
   5:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  * functions to set up or stop timers and check for expired timers.
   6:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  *
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccMzS9LP.s 			page 2


   7:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  */
   8:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** 
   9:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** /*
  10:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  * Copyright (c) 2001-2004 Swedish Institute of Computer Science.
  11:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  * All rights reserved.
  12:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  *
  13:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  * Redistribution and use in source and binary forms, with or without modification,
  14:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  * are permitted provided that the following conditions are met:
  15:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  *
  16:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  * 1. Redistributions of source code must retain the above copyright notice,
  17:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  *    this list of conditions and the following disclaimer.
  18:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  * 2. Redistributions in binary form must reproduce the above copyright notice,
  19:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  *    this list of conditions and the following disclaimer in the documentation
  20:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  *    and/or other materials provided with the distribution.
  21:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  * 3. The name of the author may not be used to endorse or promote products
  22:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  *    derived from this software without specific prior written permission.
  23:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  *
  24:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  * THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR IMPLIED
  25:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
  26:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  * MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT
  27:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  * SHALL THE AUTHOR BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
  28:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  * EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT
  29:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  * OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
  30:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
  31:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
  32:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY
  33:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  * OF SUCH DAMAGE.
  34:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  *
  35:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  * This file is part of the lwIP TCP/IP stack.
  36:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  *
  37:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  * Author: Adam Dunkels <<EMAIL>>
  38:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  *         Simon Goldschmidt
  39:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  *
  40:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  */
  41:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** 
  42:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #include "lwip/opt.h"
  43:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** 
  44:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #include "lwip/timeouts.h"
  45:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #include "lwip/priv/tcp_priv.h"
  46:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** 
  47:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #include "lwip/def.h"
  48:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #include "lwip/memp.h"
  49:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #include "lwip/priv/tcpip_priv.h"
  50:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** 
  51:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #include "lwip/ip4_frag.h"
  52:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #include "lwip/etharp.h"
  53:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #include "lwip/dhcp.h"
  54:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #include "lwip/autoip.h"
  55:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #include "lwip/igmp.h"
  56:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #include "lwip/dns.h"
  57:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #include "lwip/nd6.h"
  58:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #include "lwip/ip6_frag.h"
  59:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #include "lwip/mld6.h"
  60:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #include "lwip/dhcp6.h"
  61:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #include "lwip/sys.h"
  62:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #include "lwip/pbuf.h"
  63:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccMzS9LP.s 			page 3


  64:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #if LWIP_DEBUG_TIMERNAMES
  65:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #define HANDLER(x) x, #x
  66:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #else /* LWIP_DEBUG_TIMERNAMES */
  67:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #define HANDLER(x) x
  68:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #endif /* LWIP_DEBUG_TIMERNAMES */
  69:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** 
  70:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #define LWIP_MAX_TIMEOUT  0x7fffffff
  71:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** 
  72:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** /* Check if timer's expiry time is greater than time and care about u32_t wraparounds */
  73:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #define TIME_LESS_THAN(t, compare_to) ( (((u32_t)((t)-(compare_to))) > LWIP_MAX_TIMEOUT) ? 1 : 0 )
  74:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** 
  75:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** /** This array contains all stack-internal cyclic timers. To get the number of
  76:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  * timers, use LWIP_ARRAYSIZE() */
  77:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** const struct lwip_cyclic_timer lwip_cyclic_timers[] = {
  78:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #if LWIP_TCP
  79:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   /* The TCP timer is a special case: it does not have to run always and
  80:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****      is triggered to start from TCP using tcp_timer_needed() */
  81:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   {TCP_TMR_INTERVAL, HANDLER(tcp_tmr)},
  82:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #endif /* LWIP_TCP */
  83:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #if LWIP_IPV4
  84:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #if IP_REASSEMBLY
  85:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   {IP_TMR_INTERVAL, HANDLER(ip_reass_tmr)},
  86:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #endif /* IP_REASSEMBLY */
  87:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #if LWIP_ARP
  88:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   {ARP_TMR_INTERVAL, HANDLER(etharp_tmr)},
  89:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #endif /* LWIP_ARP */
  90:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #if LWIP_DHCP
  91:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   {DHCP_COARSE_TIMER_MSECS, HANDLER(dhcp_coarse_tmr)},
  92:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   {DHCP_FINE_TIMER_MSECS, HANDLER(dhcp_fine_tmr)},
  93:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #endif /* LWIP_DHCP */
  94:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #if LWIP_AUTOIP
  95:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   {AUTOIP_TMR_INTERVAL, HANDLER(autoip_tmr)},
  96:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #endif /* LWIP_AUTOIP */
  97:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #if LWIP_IGMP
  98:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   {IGMP_TMR_INTERVAL, HANDLER(igmp_tmr)},
  99:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #endif /* LWIP_IGMP */
 100:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #endif /* LWIP_IPV4 */
 101:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #if LWIP_DNS
 102:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   {DNS_TMR_INTERVAL, HANDLER(dns_tmr)},
 103:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #endif /* LWIP_DNS */
 104:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #if LWIP_IPV6
 105:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   {ND6_TMR_INTERVAL, HANDLER(nd6_tmr)},
 106:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #if LWIP_IPV6_REASS
 107:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   {IP6_REASS_TMR_INTERVAL, HANDLER(ip6_reass_tmr)},
 108:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #endif /* LWIP_IPV6_REASS */
 109:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #if LWIP_IPV6_MLD
 110:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   {MLD6_TMR_INTERVAL, HANDLER(mld6_tmr)},
 111:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #endif /* LWIP_IPV6_MLD */
 112:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #if LWIP_IPV6_DHCP6
 113:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   {DHCP6_TIMER_MSECS, HANDLER(dhcp6_tmr)},
 114:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #endif /* LWIP_IPV6_DHCP6 */
 115:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #endif /* LWIP_IPV6 */
 116:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** };
 117:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** const int lwip_num_cyclic_timers = LWIP_ARRAYSIZE(lwip_cyclic_timers);
 118:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** 
 119:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #if LWIP_TIMERS && !LWIP_TIMERS_CUSTOM
 120:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccMzS9LP.s 			page 4


 121:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** /** The one and only timeout list */
 122:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** static struct sys_timeo *next_timeout;
 123:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** 
 124:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** static u32_t current_timeout_due_time;
 125:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** 
 126:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #if LWIP_TESTMODE
 127:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** struct sys_timeo**
 128:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** sys_timeouts_get_next_timeout(void)
 129:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** {
 130:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   return &next_timeout;
 131:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** }
 132:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #endif
 133:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** 
 134:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #if LWIP_TCP
 135:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** /** global variable that shows if the tcp timer is currently scheduled or not */
 136:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** static int tcpip_tcp_timer_active;
 137:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** 
 138:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** /**
 139:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  * Timer callback function that calls tcp_tmr() and reschedules itself.
 140:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  *
 141:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  * @param arg unused argument
 142:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  */
 143:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** static void
 144:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** tcpip_tcp_timer(void *arg)
 145:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** {
 146:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   LWIP_UNUSED_ARG(arg);
 147:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** 
 148:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   /* call TCP timer handler */
 149:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   tcp_tmr();
 150:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   /* timer still needed? */
 151:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   if (tcp_active_pcbs || tcp_tw_pcbs) {
 152:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     /* restart timer */
 153:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     sys_timeout(TCP_TMR_INTERVAL, tcpip_tcp_timer, NULL);
 154:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   } else {
 155:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     /* disable timer */
 156:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     tcpip_tcp_timer_active = 0;
 157:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   }
 158:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** }
 159:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** 
 160:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** /**
 161:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  * Called from TCP_REG when registering a new PCB:
 162:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  * the reason is to have the TCP timer only running when
 163:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  * there are active (or time-wait) PCBs.
 164:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  */
 165:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** void
 166:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** tcp_timer_needed(void)
 167:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** {
 168:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   LWIP_ASSERT_CORE_LOCKED();
 169:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** 
 170:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   /* timer is off but needed again? */
 171:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   if (!tcpip_tcp_timer_active && (tcp_active_pcbs || tcp_tw_pcbs)) {
 172:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     /* enable and start timer */
 173:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     tcpip_tcp_timer_active = 1;
 174:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     sys_timeout(TCP_TMR_INTERVAL, tcpip_tcp_timer, NULL);
 175:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   }
 176:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** }
 177:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #endif /* LWIP_TCP */
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccMzS9LP.s 			page 5


 178:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** 
 179:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** static void
 180:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #if LWIP_DEBUG_TIMERNAMES
 181:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** sys_timeout_abs(u32_t abs_time, sys_timeout_handler handler, void *arg, const char *handler_name)
 182:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #else /* LWIP_DEBUG_TIMERNAMES */
 183:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** sys_timeout_abs(u32_t abs_time, sys_timeout_handler handler, void *arg)
 184:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #endif
 185:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** {
  39              		.loc 1 185 1 view -0
  40              		.cfi_startproc
  41              		@ args = 0, pretend = 0, frame = 0
  42              		@ frame_needed = 0, uses_anonymous_args = 0
  43              		.loc 1 185 1 is_stmt 0 view .LVU1
  44 0000 F8B5     		push	{r3, r4, r5, r6, r7, lr}
  45              	.LCFI0:
  46              		.cfi_def_cfa_offset 24
  47              		.cfi_offset 3, -24
  48              		.cfi_offset 4, -20
  49              		.cfi_offset 5, -16
  50              		.cfi_offset 6, -12
  51              		.cfi_offset 7, -8
  52              		.cfi_offset 14, -4
  53 0002 0446     		mov	r4, r0
  54 0004 0F46     		mov	r7, r1
  55 0006 1646     		mov	r6, r2
 186:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   struct sys_timeo *timeout, *t;
  56              		.loc 1 186 3 is_stmt 1 view .LVU2
 187:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** 
 188:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   timeout = (struct sys_timeo *)memp_malloc(MEMP_SYS_TIMEOUT);
  57              		.loc 1 188 3 view .LVU3
  58              		.loc 1 188 33 is_stmt 0 view .LVU4
  59 0008 0A20     		movs	r0, #10
  60              	.LVL1:
  61              		.loc 1 188 33 view .LVU5
  62 000a FFF7FEFF 		bl	memp_malloc
  63              	.LVL2:
 189:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   if (timeout == NULL) {
  64              		.loc 1 189 3 is_stmt 1 view .LVU6
  65              		.loc 1 189 6 is_stmt 0 view .LVU7
  66 000e 80B1     		cbz	r0, .L10
  67 0010 0546     		mov	r5, r0
 190:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     LWIP_ASSERT("sys_timeout: timeout != NULL, pool MEMP_SYS_TIMEOUT is empty", timeout != NULL);
 191:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     return;
 192:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   }
 193:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** 
 194:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   timeout->next = NULL;
  68              		.loc 1 194 3 is_stmt 1 view .LVU8
  69              		.loc 1 194 17 is_stmt 0 view .LVU9
  70 0012 0023     		movs	r3, #0
  71 0014 0360     		str	r3, [r0]
 195:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   timeout->h = handler;
  72              		.loc 1 195 3 is_stmt 1 view .LVU10
  73              		.loc 1 195 14 is_stmt 0 view .LVU11
  74 0016 8760     		str	r7, [r0, #8]
 196:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   timeout->arg = arg;
  75              		.loc 1 196 3 is_stmt 1 view .LVU12
  76              		.loc 1 196 16 is_stmt 0 view .LVU13
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccMzS9LP.s 			page 6


  77 0018 C660     		str	r6, [r0, #12]
 197:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   timeout->time = abs_time;
  78              		.loc 1 197 3 is_stmt 1 view .LVU14
  79              		.loc 1 197 17 is_stmt 0 view .LVU15
  80 001a 4460     		str	r4, [r0, #4]
 198:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** 
 199:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #if LWIP_DEBUG_TIMERNAMES
 200:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   timeout->handler_name = handler_name;
 201:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   LWIP_DEBUGF(TIMERS_DEBUG, ("sys_timeout: %p abs_time=%"U32_F" handler=%s arg=%p\n",
 202:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****                              (void *)timeout, abs_time, handler_name, (void *)arg));
 203:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #endif /* LWIP_DEBUG_TIMERNAMES */
 204:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** 
 205:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   if (next_timeout == NULL) {
  81              		.loc 1 205 3 is_stmt 1 view .LVU16
  82              		.loc 1 205 20 is_stmt 0 view .LVU17
  83 001c 104B     		ldr	r3, .L12
  84 001e 1A68     		ldr	r2, [r3]
  85              		.loc 1 205 6 view .LVU18
  86 0020 72B1     		cbz	r2, .L11
 206:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     next_timeout = timeout;
 207:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     return;
 208:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   }
 209:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   if (TIME_LESS_THAN(timeout->time, next_timeout->time)) {
  87              		.loc 1 209 3 is_stmt 1 view .LVU19
  88              		.loc 1 209 7 is_stmt 0 view .LVU20
  89 0022 5368     		ldr	r3, [r2, #4]
  90 0024 E31A     		subs	r3, r4, r3
  91              		.loc 1 209 6 view .LVU21
  92 0026 002B     		cmp	r3, #0
  93 0028 0FDA     		bge	.L5
 210:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     timeout->next = next_timeout;
  94              		.loc 1 210 5 is_stmt 1 view .LVU22
  95              		.loc 1 210 19 is_stmt 0 view .LVU23
  96 002a 0260     		str	r2, [r0]
 211:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     next_timeout = timeout;
  97              		.loc 1 211 5 is_stmt 1 view .LVU24
  98              		.loc 1 211 18 is_stmt 0 view .LVU25
  99 002c 0C4B     		ldr	r3, .L12
 100 002e 1860     		str	r0, [r3]
 101              	.LVL3:
 102              	.L1:
 212:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   } else {
 213:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     for (t = next_timeout; t != NULL; t = t->next) {
 214:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****       if ((t->next == NULL) || TIME_LESS_THAN(timeout->time, t->next->time)) {
 215:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****         timeout->next = t->next;
 216:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****         t->next = timeout;
 217:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****         break;
 218:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****       }
 219:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     }
 220:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   }
 221:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** }
 103              		.loc 1 221 1 view .LVU26
 104 0030 F8BD     		pop	{r3, r4, r5, r6, r7, pc}
 105              	.LVL4:
 106              	.L10:
 190:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     return;
 107              		.loc 1 190 5 is_stmt 1 view .LVU27
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccMzS9LP.s 			page 7


 190:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     return;
 108              		.loc 1 190 5 view .LVU28
 190:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     return;
 109              		.loc 1 190 5 discriminator 1 view .LVU29
 190:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     return;
 110              		.loc 1 190 5 discriminator 1 view .LVU30
 111 0032 0C4B     		ldr	r3, .L12+4
 112 0034 BE22     		movs	r2, #190
 113 0036 0C49     		ldr	r1, .L12+8
 114 0038 0C48     		ldr	r0, .L12+12
 115              	.LVL5:
 190:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     return;
 116              		.loc 1 190 5 is_stmt 0 discriminator 1 view .LVU31
 117 003a FFF7FEFF 		bl	printf
 118              	.LVL6:
 190:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     return;
 119              		.loc 1 190 5 is_stmt 1 discriminator 3 view .LVU32
 190:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     return;
 120              		.loc 1 190 5 discriminator 3 view .LVU33
 191:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   }
 121              		.loc 1 191 5 view .LVU34
 122 003e F7E7     		b	.L1
 123              	.LVL7:
 124              	.L11:
 206:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     return;
 125              		.loc 1 206 5 view .LVU35
 206:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     return;
 126              		.loc 1 206 18 is_stmt 0 view .LVU36
 127 0040 1860     		str	r0, [r3]
 207:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   }
 128              		.loc 1 207 5 is_stmt 1 view .LVU37
 129 0042 F5E7     		b	.L1
 130              	.LVL8:
 131              	.L6:
 215:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****         t->next = timeout;
 132              		.loc 1 215 9 view .LVU38
 215:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****         t->next = timeout;
 133              		.loc 1 215 23 is_stmt 0 view .LVU39
 134 0044 2B60     		str	r3, [r5]
 216:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****         break;
 135              		.loc 1 216 9 is_stmt 1 view .LVU40
 216:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****         break;
 136              		.loc 1 216 17 is_stmt 0 view .LVU41
 137 0046 1560     		str	r5, [r2]
 217:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****       }
 138              		.loc 1 217 9 is_stmt 1 view .LVU42
 139 0048 F2E7     		b	.L1
 140              	.LVL9:
 141              	.L5:
 213:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****       if ((t->next == NULL) || TIME_LESS_THAN(timeout->time, t->next->time)) {
 142              		.loc 1 213 30 discriminator 1 view .LVU43
 143 004a 002A     		cmp	r2, #0
 144 004c F0D0     		beq	.L1
 214:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****         timeout->next = t->next;
 145              		.loc 1 214 7 view .LVU44
 214:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****         timeout->next = t->next;
 146              		.loc 1 214 13 is_stmt 0 view .LVU45
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccMzS9LP.s 			page 8


 147 004e 1368     		ldr	r3, [r2]
 214:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****         timeout->next = t->next;
 148              		.loc 1 214 10 view .LVU46
 149 0050 002B     		cmp	r3, #0
 150 0052 F7D0     		beq	.L6
 214:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****         timeout->next = t->next;
 151              		.loc 1 214 32 discriminator 1 view .LVU47
 152 0054 5968     		ldr	r1, [r3, #4]
 153 0056 611A     		subs	r1, r4, r1
 214:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****         timeout->next = t->next;
 154              		.loc 1 214 29 discriminator 1 view .LVU48
 155 0058 0029     		cmp	r1, #0
 156 005a F3DB     		blt	.L6
 213:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****       if ((t->next == NULL) || TIME_LESS_THAN(timeout->time, t->next->time)) {
 157              		.loc 1 213 41 discriminator 2 view .LVU49
 158 005c 1A46     		mov	r2, r3
 159              	.LVL10:
 213:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****       if ((t->next == NULL) || TIME_LESS_THAN(timeout->time, t->next->time)) {
 160              		.loc 1 213 41 discriminator 2 view .LVU50
 161 005e F4E7     		b	.L5
 162              	.L13:
 163              		.align	2
 164              	.L12:
 165 0060 00000000 		.word	next_timeout
 166 0064 00000000 		.word	.LC0
 167 0068 34000000 		.word	.LC1
 168 006c 74000000 		.word	.LC2
 169              		.cfi_endproc
 170              	.LFE176:
 172              		.section	.text.lwip_cyclic_timer,"ax",%progbits
 173              		.align	1
 174              		.syntax unified
 175              		.thumb
 176              		.thumb_func
 178              	lwip_cyclic_timer:
 179              	.LVL11:
 180              	.LFB177:
 222:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** 
 223:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** /**
 224:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  * Timer callback function that calls cyclic->handler() and reschedules itself.
 225:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  *
 226:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  * @param arg unused argument
 227:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  */
 228:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #if !LWIP_TESTMODE
 229:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** static
 230:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #endif
 231:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** void
 232:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** lwip_cyclic_timer(void *arg)
 233:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** {
 181              		.loc 1 233 1 is_stmt 1 view -0
 182              		.cfi_startproc
 183              		@ args = 0, pretend = 0, frame = 0
 184              		@ frame_needed = 0, uses_anonymous_args = 0
 185              		.loc 1 233 1 is_stmt 0 view .LVU52
 186 0000 38B5     		push	{r3, r4, r5, lr}
 187              	.LCFI1:
 188              		.cfi_def_cfa_offset 16
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccMzS9LP.s 			page 9


 189              		.cfi_offset 3, -16
 190              		.cfi_offset 4, -12
 191              		.cfi_offset 5, -8
 192              		.cfi_offset 14, -4
 193 0002 0446     		mov	r4, r0
 234:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   u32_t now;
 194              		.loc 1 234 3 is_stmt 1 view .LVU53
 235:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   u32_t next_timeout_time;
 195              		.loc 1 235 3 view .LVU54
 236:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   const struct lwip_cyclic_timer *cyclic = (const struct lwip_cyclic_timer *)arg;
 196              		.loc 1 236 3 view .LVU55
 197              	.LVL12:
 237:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** 
 238:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #if LWIP_DEBUG_TIMERNAMES
 239:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   LWIP_DEBUGF(TIMERS_DEBUG, ("tcpip: %s()\n", cyclic->handler_name));
 240:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #endif
 241:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   cyclic->handler();
 198              		.loc 1 241 3 view .LVU56
 199              		.loc 1 241 9 is_stmt 0 view .LVU57
 200 0004 4368     		ldr	r3, [r0, #4]
 201              		.loc 1 241 3 view .LVU58
 202 0006 9847     		blx	r3
 203              	.LVL13:
 242:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** 
 243:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   now = sys_now();
 204              		.loc 1 243 3 is_stmt 1 view .LVU59
 205              		.loc 1 243 9 is_stmt 0 view .LVU60
 206 0008 FFF7FEFF 		bl	sys_now
 207              	.LVL14:
 208 000c 8446     		mov	ip, r0
 209              	.LVL15:
 244:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   next_timeout_time = (u32_t)(current_timeout_due_time + cyclic->interval_ms);  /* overflow handled
 210              		.loc 1 244 3 is_stmt 1 view .LVU61
 211              		.loc 1 244 64 is_stmt 0 view .LVU62
 212 000e 2568     		ldr	r5, [r4]
 213              		.loc 1 244 23 view .LVU63
 214 0010 094B     		ldr	r3, .L18
 215 0012 1868     		ldr	r0, [r3]
 216              	.LVL16:
 217              		.loc 1 244 21 view .LVU64
 218 0014 2844     		add	r0, r0, r5
 219              	.LVL17:
 245:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   if (TIME_LESS_THAN(next_timeout_time, now)) {
 220              		.loc 1 245 3 is_stmt 1 view .LVU65
 221              		.loc 1 245 7 is_stmt 0 view .LVU66
 222 0016 A0EB0C03 		sub	r3, r0, ip
 223              		.loc 1 245 6 view .LVU67
 224 001a 002B     		cmp	r3, #0
 225 001c 06DA     		bge	.L15
 246:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     /* timer would immediately expire again -> "overload" -> restart without any correction */
 247:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #if LWIP_DEBUG_TIMERNAMES
 248:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     sys_timeout_abs((u32_t)(now + cyclic->interval_ms), lwip_cyclic_timer, arg, cyclic->handler_nam
 249:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #else
 250:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     sys_timeout_abs((u32_t)(now + cyclic->interval_ms), lwip_cyclic_timer, arg);
 226              		.loc 1 250 5 is_stmt 1 view .LVU68
 227 001e 2246     		mov	r2, r4
 228 0020 0649     		ldr	r1, .L18+4
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccMzS9LP.s 			page 10


 229 0022 05EB0C00 		add	r0, r5, ip
 230              	.LVL18:
 231              		.loc 1 250 5 is_stmt 0 view .LVU69
 232 0026 FFF7FEFF 		bl	sys_timeout_abs
 233              	.LVL19:
 234              	.L14:
 251:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #endif
 252:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** 
 253:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   } else {
 254:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     /* correct cyclic interval with handler execution delay and sys_check_timeouts jitter */
 255:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #if LWIP_DEBUG_TIMERNAMES
 256:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     sys_timeout_abs(next_timeout_time, lwip_cyclic_timer, arg, cyclic->handler_name);
 257:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #else
 258:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     sys_timeout_abs(next_timeout_time, lwip_cyclic_timer, arg);
 259:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #endif
 260:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   }
 261:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** }
 235              		.loc 1 261 1 view .LVU70
 236 002a 38BD     		pop	{r3, r4, r5, pc}
 237              	.LVL20:
 238              	.L15:
 258:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #endif
 239              		.loc 1 258 5 is_stmt 1 view .LVU71
 240 002c 2246     		mov	r2, r4
 241 002e 0349     		ldr	r1, .L18+4
 242 0030 FFF7FEFF 		bl	sys_timeout_abs
 243              	.LVL21:
 244              		.loc 1 261 1 is_stmt 0 view .LVU72
 245 0034 F9E7     		b	.L14
 246              	.L19:
 247 0036 00BF     		.align	2
 248              	.L18:
 249 0038 00000000 		.word	current_timeout_due_time
 250 003c 00000000 		.word	lwip_cyclic_timer
 251              		.cfi_endproc
 252              	.LFE177:
 254              		.section	.rodata.sys_timeout.str1.4,"aMS",%progbits,1
 255              		.align	2
 256              	.LC3:
 257 0000 54696D65 		.ascii	"Timeout time too long, max is LWIP_UINT32_MAX/4 mse"
 257      6F757420 
 257      74696D65 
 257      20746F6F 
 257      206C6F6E 
 258 0033 637300   		.ascii	"cs\000"
 259              		.section	.text.sys_timeout,"ax",%progbits
 260              		.align	1
 261              		.global	sys_timeout
 262              		.syntax unified
 263              		.thumb
 264              		.thumb_func
 266              	sys_timeout:
 267              	.LVL22:
 268              	.LFB179:
 262:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** 
 263:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** /** Initialize this module */
 264:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** void sys_timeouts_init(void)
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccMzS9LP.s 			page 11


 265:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** {
 266:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   size_t i;
 267:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   /* tcp_tmr() at index 0 is started on demand */
 268:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   for (i = (LWIP_TCP ? 1 : 0); i < LWIP_ARRAYSIZE(lwip_cyclic_timers); i++) {
 269:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     /* we have to cast via size_t to get rid of const warning
 270:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****       (this is OK as cyclic_timer() casts back to const* */
 271:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     sys_timeout(lwip_cyclic_timers[i].interval_ms, lwip_cyclic_timer, LWIP_CONST_CAST(void *, &lwip
 272:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   }
 273:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** }
 274:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** 
 275:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** /**
 276:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  * Create a one-shot timer (aka timeout). Timeouts are processed in the
 277:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  * following cases:
 278:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  * - while waiting for a message using sys_timeouts_mbox_fetch()
 279:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  * - by calling sys_check_timeouts() (NO_SYS==1 only)
 280:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  *
 281:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  * @param msecs time in milliseconds after that the timer should expire
 282:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  * @param handler callback function to call when msecs have elapsed
 283:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  * @param arg argument to pass to the callback function
 284:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  */
 285:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #if LWIP_DEBUG_TIMERNAMES
 286:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** void
 287:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** sys_timeout_debug(u32_t msecs, sys_timeout_handler handler, void *arg, const char *handler_name)
 288:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #else /* LWIP_DEBUG_TIMERNAMES */
 289:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** void
 290:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** sys_timeout(u32_t msecs, sys_timeout_handler handler, void *arg)
 291:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #endif /* LWIP_DEBUG_TIMERNAMES */
 292:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** {
 269              		.loc 1 292 1 is_stmt 1 view -0
 270              		.cfi_startproc
 271              		@ args = 0, pretend = 0, frame = 0
 272              		@ frame_needed = 0, uses_anonymous_args = 0
 273              		.loc 1 292 1 is_stmt 0 view .LVU74
 274 0000 70B5     		push	{r4, r5, r6, lr}
 275              	.LCFI2:
 276              		.cfi_def_cfa_offset 16
 277              		.cfi_offset 4, -16
 278              		.cfi_offset 5, -12
 279              		.cfi_offset 6, -8
 280              		.cfi_offset 14, -4
 281 0002 0446     		mov	r4, r0
 282 0004 0D46     		mov	r5, r1
 283 0006 1646     		mov	r6, r2
 293:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   u32_t next_timeout_time;
 284              		.loc 1 293 3 is_stmt 1 view .LVU75
 294:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** 
 295:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   LWIP_ASSERT_CORE_LOCKED();
 285              		.loc 1 295 28 view .LVU76
 296:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** 
 297:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   LWIP_ASSERT("Timeout time too long, max is LWIP_UINT32_MAX/4 msecs", msecs <= (LWIP_UINT32_MAX / 
 286              		.loc 1 297 3 view .LVU77
 287              		.loc 1 297 3 view .LVU78
 288 0008 B0F1804F 		cmp	r0, #1073741824
 289 000c 07D2     		bcs	.L23
 290              	.LVL23:
 291              	.L21:
 292              		.loc 1 297 3 discriminator 3 view .LVU79
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccMzS9LP.s 			page 12


 293              		.loc 1 297 3 discriminator 3 view .LVU80
 298:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** 
 299:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   next_timeout_time = (u32_t)(sys_now() + msecs); /* overflow handled by TIME_LESS_THAN macro */ 
 294              		.loc 1 299 3 view .LVU81
 295              		.loc 1 299 31 is_stmt 0 view .LVU82
 296 000e FFF7FEFF 		bl	sys_now
 297              	.LVL24:
 300:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** 
 301:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #if LWIP_DEBUG_TIMERNAMES
 302:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   sys_timeout_abs(next_timeout_time, handler, arg, handler_name);
 303:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #else
 304:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   sys_timeout_abs(next_timeout_time, handler, arg);
 298              		.loc 1 304 3 is_stmt 1 view .LVU83
 299 0012 3246     		mov	r2, r6
 300 0014 2946     		mov	r1, r5
 301 0016 2044     		add	r0, r0, r4
 302              	.LVL25:
 303              		.loc 1 304 3 is_stmt 0 view .LVU84
 304 0018 FFF7FEFF 		bl	sys_timeout_abs
 305              	.LVL26:
 305:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #endif
 306:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** }
 306              		.loc 1 306 1 view .LVU85
 307 001c 70BD     		pop	{r4, r5, r6, pc}
 308              	.LVL27:
 309              	.L23:
 297:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** 
 310              		.loc 1 297 3 is_stmt 1 discriminator 1 view .LVU86
 297:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** 
 311              		.loc 1 297 3 discriminator 1 view .LVU87
 312 001e 044B     		ldr	r3, .L24
 313 0020 40F22912 		movw	r2, #297
 314              	.LVL28:
 297:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** 
 315              		.loc 1 297 3 is_stmt 0 discriminator 1 view .LVU88
 316 0024 0349     		ldr	r1, .L24+4
 317              	.LVL29:
 297:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** 
 318              		.loc 1 297 3 discriminator 1 view .LVU89
 319 0026 0448     		ldr	r0, .L24+8
 320              	.LVL30:
 297:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** 
 321              		.loc 1 297 3 discriminator 1 view .LVU90
 322 0028 FFF7FEFF 		bl	printf
 323              	.LVL31:
 324 002c EFE7     		b	.L21
 325              	.L25:
 326 002e 00BF     		.align	2
 327              	.L24:
 328 0030 00000000 		.word	.LC0
 329 0034 00000000 		.word	.LC3
 330 0038 74000000 		.word	.LC2
 331              		.cfi_endproc
 332              	.LFE179:
 334              		.section	.text.tcp_timer_needed,"ax",%progbits
 335              		.align	1
 336              		.global	tcp_timer_needed
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccMzS9LP.s 			page 13


 337              		.syntax unified
 338              		.thumb
 339              		.thumb_func
 341              	tcp_timer_needed:
 342              	.LFB175:
 167:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   LWIP_ASSERT_CORE_LOCKED();
 343              		.loc 1 167 1 is_stmt 1 view -0
 344              		.cfi_startproc
 345              		@ args = 0, pretend = 0, frame = 0
 346              		@ frame_needed = 0, uses_anonymous_args = 0
 347 0000 08B5     		push	{r3, lr}
 348              	.LCFI3:
 349              		.cfi_def_cfa_offset 8
 350              		.cfi_offset 3, -8
 351              		.cfi_offset 14, -4
 168:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** 
 352              		.loc 1 168 28 view .LVU92
 171:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     /* enable and start timer */
 353              		.loc 1 171 3 view .LVU93
 171:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     /* enable and start timer */
 354              		.loc 1 171 7 is_stmt 0 view .LVU94
 355 0002 0A4B     		ldr	r3, .L31
 356 0004 1B68     		ldr	r3, [r3]
 171:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     /* enable and start timer */
 357              		.loc 1 171 6 view .LVU95
 358 0006 53B9     		cbnz	r3, .L26
 171:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     /* enable and start timer */
 359              		.loc 1 171 35 discriminator 1 view .LVU96
 360 0008 094B     		ldr	r3, .L31+4
 361 000a 1B68     		ldr	r3, [r3]
 171:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     /* enable and start timer */
 362              		.loc 1 171 31 discriminator 1 view .LVU97
 363 000c 43B1     		cbz	r3, .L30
 364              	.L28:
 173:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     sys_timeout(TCP_TMR_INTERVAL, tcpip_tcp_timer, NULL);
 365              		.loc 1 173 5 is_stmt 1 view .LVU98
 173:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     sys_timeout(TCP_TMR_INTERVAL, tcpip_tcp_timer, NULL);
 366              		.loc 1 173 28 is_stmt 0 view .LVU99
 367 000e 074B     		ldr	r3, .L31
 368 0010 0122     		movs	r2, #1
 369 0012 1A60     		str	r2, [r3]
 174:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   }
 370              		.loc 1 174 5 is_stmt 1 view .LVU100
 371 0014 0022     		movs	r2, #0
 372 0016 0749     		ldr	r1, .L31+8
 373 0018 FA20     		movs	r0, #250
 374 001a FFF7FEFF 		bl	sys_timeout
 375              	.LVL32:
 376              	.L26:
 176:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #endif /* LWIP_TCP */
 377              		.loc 1 176 1 is_stmt 0 view .LVU101
 378 001e 08BD     		pop	{r3, pc}
 379              	.L30:
 171:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     /* enable and start timer */
 380              		.loc 1 171 51 discriminator 2 view .LVU102
 381 0020 054B     		ldr	r3, .L31+12
 382 0022 1B68     		ldr	r3, [r3]
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccMzS9LP.s 			page 14


 383 0024 002B     		cmp	r3, #0
 384 0026 F2D1     		bne	.L28
 385 0028 F9E7     		b	.L26
 386              	.L32:
 387 002a 00BF     		.align	2
 388              	.L31:
 389 002c 00000000 		.word	tcpip_tcp_timer_active
 390 0030 00000000 		.word	tcp_active_pcbs
 391 0034 00000000 		.word	tcpip_tcp_timer
 392 0038 00000000 		.word	tcp_tw_pcbs
 393              		.cfi_endproc
 394              	.LFE175:
 396              		.section	.text.tcpip_tcp_timer,"ax",%progbits
 397              		.align	1
 398              		.syntax unified
 399              		.thumb
 400              		.thumb_func
 402              	tcpip_tcp_timer:
 403              	.LVL33:
 404              	.LFB174:
 145:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   LWIP_UNUSED_ARG(arg);
 405              		.loc 1 145 1 is_stmt 1 view -0
 406              		.cfi_startproc
 407              		@ args = 0, pretend = 0, frame = 0
 408              		@ frame_needed = 0, uses_anonymous_args = 0
 145:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   LWIP_UNUSED_ARG(arg);
 409              		.loc 1 145 1 is_stmt 0 view .LVU104
 410 0000 08B5     		push	{r3, lr}
 411              	.LCFI4:
 412              		.cfi_def_cfa_offset 8
 413              		.cfi_offset 3, -8
 414              		.cfi_offset 14, -4
 146:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** 
 415              		.loc 1 146 3 is_stmt 1 view .LVU105
 149:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   /* timer still needed? */
 416              		.loc 1 149 3 view .LVU106
 417 0002 FFF7FEFF 		bl	tcp_tmr
 418              	.LVL34:
 151:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     /* restart timer */
 419              		.loc 1 151 3 view .LVU107
 151:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     /* restart timer */
 420              		.loc 1 151 7 is_stmt 0 view .LVU108
 421 0006 084B     		ldr	r3, .L39
 422 0008 1B68     		ldr	r3, [r3]
 151:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     /* restart timer */
 423              		.loc 1 151 6 view .LVU109
 424 000a 2BB1     		cbz	r3, .L38
 425              	.L34:
 153:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   } else {
 426              		.loc 1 153 5 is_stmt 1 view .LVU110
 427 000c 0022     		movs	r2, #0
 428 000e 0749     		ldr	r1, .L39+4
 429 0010 FA20     		movs	r0, #250
 430 0012 FFF7FEFF 		bl	sys_timeout
 431              	.LVL35:
 432              	.L33:
 158:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccMzS9LP.s 			page 15


 433              		.loc 1 158 1 is_stmt 0 view .LVU111
 434 0016 08BD     		pop	{r3, pc}
 435              	.L38:
 151:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     /* restart timer */
 436              		.loc 1 151 23 discriminator 1 view .LVU112
 437 0018 054B     		ldr	r3, .L39+8
 438 001a 1B68     		ldr	r3, [r3]
 439 001c 002B     		cmp	r3, #0
 440 001e F5D1     		bne	.L34
 156:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   }
 441              		.loc 1 156 5 is_stmt 1 view .LVU113
 156:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   }
 442              		.loc 1 156 28 is_stmt 0 view .LVU114
 443 0020 044B     		ldr	r3, .L39+12
 444 0022 0022     		movs	r2, #0
 445 0024 1A60     		str	r2, [r3]
 158:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** 
 446              		.loc 1 158 1 view .LVU115
 447 0026 F6E7     		b	.L33
 448              	.L40:
 449              		.align	2
 450              	.L39:
 451 0028 00000000 		.word	tcp_active_pcbs
 452 002c 00000000 		.word	tcpip_tcp_timer
 453 0030 00000000 		.word	tcp_tw_pcbs
 454 0034 00000000 		.word	tcpip_tcp_timer_active
 455              		.cfi_endproc
 456              	.LFE174:
 458              		.section	.text.sys_timeouts_init,"ax",%progbits
 459              		.align	1
 460              		.global	sys_timeouts_init
 461              		.syntax unified
 462              		.thumb
 463              		.thumb_func
 465              	sys_timeouts_init:
 466              	.LFB178:
 265:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   size_t i;
 467              		.loc 1 265 1 is_stmt 1 view -0
 468              		.cfi_startproc
 469              		@ args = 0, pretend = 0, frame = 0
 470              		@ frame_needed = 0, uses_anonymous_args = 0
 471 0000 10B5     		push	{r4, lr}
 472              	.LCFI5:
 473              		.cfi_def_cfa_offset 8
 474              		.cfi_offset 4, -8
 475              		.cfi_offset 14, -4
 266:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   /* tcp_tmr() at index 0 is started on demand */
 476              		.loc 1 266 3 view .LVU117
 268:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     /* we have to cast via size_t to get rid of const warning
 477              		.loc 1 268 3 view .LVU118
 478              	.LVL36:
 268:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     /* we have to cast via size_t to get rid of const warning
 479              		.loc 1 268 10 is_stmt 0 view .LVU119
 480 0002 0124     		movs	r4, #1
 268:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     /* we have to cast via size_t to get rid of const warning
 481              		.loc 1 268 3 view .LVU120
 482 0004 08E0     		b	.L42
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccMzS9LP.s 			page 16


 483              	.LVL37:
 484              	.L43:
 271:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   }
 485              		.loc 1 271 5 is_stmt 1 view .LVU121
 271:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   }
 486              		.loc 1 271 38 is_stmt 0 view .LVU122
 487 0006 064B     		ldr	r3, .L45
 271:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   }
 488              		.loc 1 271 5 view .LVU123
 489 0008 03EBC402 		add	r2, r3, r4, lsl #3
 490 000c 0549     		ldr	r1, .L45+4
 491 000e 53F83400 		ldr	r0, [r3, r4, lsl #3]
 492 0012 FFF7FEFF 		bl	sys_timeout
 493              	.LVL38:
 268:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     /* we have to cast via size_t to get rid of const warning
 494              		.loc 1 268 73 is_stmt 1 discriminator 3 view .LVU124
 495 0016 0134     		adds	r4, r4, #1
 496              	.LVL39:
 497              	.L42:
 268:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     /* we have to cast via size_t to get rid of const warning
 498              		.loc 1 268 34 discriminator 1 view .LVU125
 499 0018 022C     		cmp	r4, #2
 500 001a F4D9     		bls	.L43
 273:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** 
 501              		.loc 1 273 1 is_stmt 0 view .LVU126
 502 001c 10BD     		pop	{r4, pc}
 503              	.LVL40:
 504              	.L46:
 273:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** 
 505              		.loc 1 273 1 view .LVU127
 506 001e 00BF     		.align	2
 507              	.L45:
 508 0020 00000000 		.word	lwip_cyclic_timers
 509 0024 00000000 		.word	lwip_cyclic_timer
 510              		.cfi_endproc
 511              	.LFE178:
 513              		.section	.text.sys_untimeout,"ax",%progbits
 514              		.align	1
 515              		.global	sys_untimeout
 516              		.syntax unified
 517              		.thumb
 518              		.thumb_func
 520              	sys_untimeout:
 521              	.LVL41:
 522              	.LFB180:
 307:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** 
 308:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** /**
 309:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  * Go through timeout list (for this task only) and remove the first matching
 310:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  * entry (subsequent entries remain untouched), even though the timeout has not
 311:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  * triggered yet.
 312:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  *
 313:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  * @param handler callback function that would be called by the timeout
 314:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  * @param arg callback argument that would be passed to handler
 315:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** */
 316:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** void
 317:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** sys_untimeout(sys_timeout_handler handler, void *arg)
 318:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** {
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccMzS9LP.s 			page 17


 523              		.loc 1 318 1 is_stmt 1 view -0
 524              		.cfi_startproc
 525              		@ args = 0, pretend = 0, frame = 0
 526              		@ frame_needed = 0, uses_anonymous_args = 0
 319:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   struct sys_timeo *prev_t, *t;
 527              		.loc 1 319 3 view .LVU129
 320:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** 
 321:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   LWIP_ASSERT_CORE_LOCKED();
 528              		.loc 1 321 28 view .LVU130
 322:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** 
 323:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   if (next_timeout == NULL) {
 529              		.loc 1 323 3 view .LVU131
 530              		.loc 1 323 20 is_stmt 0 view .LVU132
 531 0000 0E4B     		ldr	r3, .L59
 532 0002 1B68     		ldr	r3, [r3]
 533              		.loc 1 323 6 view .LVU133
 534 0004 C3B1     		cbz	r3, .L55
 318:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   struct sys_timeo *prev_t, *t;
 535              		.loc 1 318 1 view .LVU134
 536 0006 10B5     		push	{r4, lr}
 537              	.LCFI6:
 538              		.cfi_def_cfa_offset 8
 539              		.cfi_offset 4, -8
 540              		.cfi_offset 14, -4
 324:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     return;
 325:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   }
 326:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** 
 327:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   for (t = next_timeout, prev_t = NULL; t != NULL; prev_t = t, t = t->next) {
 541              		.loc 1 327 33 view .LVU135
 542 0008 0024     		movs	r4, #0
 543 000a 05E0     		b	.L49
 544              	.LVL42:
 545              	.L58:
 328:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     if ((t->h == handler) && (t->arg == arg)) {
 329:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****       /* We have a match */
 330:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****       /* Unlink from previous in list */
 331:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****       if (prev_t == NULL) {
 332:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****         next_timeout = t->next;
 546              		.loc 1 332 9 is_stmt 1 view .LVU136
 547              		.loc 1 332 25 is_stmt 0 view .LVU137
 548 000c 1968     		ldr	r1, [r3]
 549              	.LVL43:
 550              		.loc 1 332 22 view .LVU138
 551 000e 0B4A     		ldr	r2, .L59
 552 0010 1160     		str	r1, [r2]
 553 0012 0CE0     		b	.L52
 554              	.LVL44:
 555              	.L50:
 327:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     if ((t->h == handler) && (t->arg == arg)) {
 556              		.loc 1 327 62 is_stmt 1 discriminator 2 view .LVU139
 327:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     if ((t->h == handler) && (t->arg == arg)) {
 557              		.loc 1 327 59 is_stmt 0 discriminator 2 view .LVU140
 558 0014 1C46     		mov	r4, r3
 327:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     if ((t->h == handler) && (t->arg == arg)) {
 559              		.loc 1 327 66 discriminator 2 view .LVU141
 560 0016 1B68     		ldr	r3, [r3]
 561              	.LVL45:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccMzS9LP.s 			page 18


 562              	.L49:
 327:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     if ((t->h == handler) && (t->arg == arg)) {
 563              		.loc 1 327 43 is_stmt 1 discriminator 1 view .LVU142
 564 0018 6BB1     		cbz	r3, .L47
 328:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     if ((t->h == handler) && (t->arg == arg)) {
 565              		.loc 1 328 5 view .LVU143
 328:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     if ((t->h == handler) && (t->arg == arg)) {
 566              		.loc 1 328 11 is_stmt 0 view .LVU144
 567 001a 9A68     		ldr	r2, [r3, #8]
 328:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     if ((t->h == handler) && (t->arg == arg)) {
 568              		.loc 1 328 8 view .LVU145
 569 001c 8242     		cmp	r2, r0
 570 001e F9D1     		bne	.L50
 328:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     if ((t->h == handler) && (t->arg == arg)) {
 571              		.loc 1 328 32 discriminator 1 view .LVU146
 572 0020 DA68     		ldr	r2, [r3, #12]
 328:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     if ((t->h == handler) && (t->arg == arg)) {
 573              		.loc 1 328 27 discriminator 1 view .LVU147
 574 0022 8A42     		cmp	r2, r1
 575 0024 F6D1     		bne	.L50
 331:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****         next_timeout = t->next;
 576              		.loc 1 331 7 is_stmt 1 view .LVU148
 331:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****         next_timeout = t->next;
 577              		.loc 1 331 10 is_stmt 0 view .LVU149
 578 0026 002C     		cmp	r4, #0
 579 0028 F0D0     		beq	.L58
 333:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****       } else {
 334:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****         prev_t->next = t->next;
 580              		.loc 1 334 9 is_stmt 1 view .LVU150
 581              		.loc 1 334 25 is_stmt 0 view .LVU151
 582 002a 1A68     		ldr	r2, [r3]
 583              		.loc 1 334 22 view .LVU152
 584 002c 2260     		str	r2, [r4]
 585              	.LVL46:
 586              	.L52:
 335:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****       }
 336:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****       memp_free(MEMP_SYS_TIMEOUT, t);
 587              		.loc 1 336 7 is_stmt 1 view .LVU153
 588 002e 1946     		mov	r1, r3
 589 0030 0A20     		movs	r0, #10
 590              	.LVL47:
 591              		.loc 1 336 7 is_stmt 0 view .LVU154
 592 0032 FFF7FEFF 		bl	memp_free
 593              	.LVL48:
 337:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****       return;
 594              		.loc 1 337 7 is_stmt 1 view .LVU155
 595              	.L47:
 338:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     }
 339:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   }
 340:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   return;
 341:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** }
 596              		.loc 1 341 1 is_stmt 0 view .LVU156
 597 0036 10BD     		pop	{r4, pc}
 598              	.LVL49:
 599              	.L55:
 600              	.LCFI7:
 601              		.cfi_def_cfa_offset 0
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccMzS9LP.s 			page 19


 602              		.cfi_restore 4
 603              		.cfi_restore 14
 604              		.loc 1 341 1 view .LVU157
 605 0038 7047     		bx	lr
 606              	.L60:
 607 003a 00BF     		.align	2
 608              	.L59:
 609 003c 00000000 		.word	next_timeout
 610              		.cfi_endproc
 611              	.LFE180:
 613              		.section	.text.sys_check_timeouts,"ax",%progbits
 614              		.align	1
 615              		.global	sys_check_timeouts
 616              		.syntax unified
 617              		.thumb
 618              		.thumb_func
 620              	sys_check_timeouts:
 621              	.LFB181:
 342:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** 
 343:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** /**
 344:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  * @ingroup lwip_nosys
 345:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  * Handle timeouts for NO_SYS==1 (i.e. without using
 346:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  * tcpip_thread/sys_timeouts_mbox_fetch(). Uses sys_now() to call timeout
 347:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  * handler functions when timeouts expire.
 348:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  *
 349:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  * Must be called periodically from your main loop.
 350:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  */
 351:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** void
 352:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** sys_check_timeouts(void)
 353:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** {
 622              		.loc 1 353 1 is_stmt 1 view -0
 623              		.cfi_startproc
 624              		@ args = 0, pretend = 0, frame = 0
 625              		@ frame_needed = 0, uses_anonymous_args = 0
 626 0000 70B5     		push	{r4, r5, r6, lr}
 627              	.LCFI8:
 628              		.cfi_def_cfa_offset 16
 629              		.cfi_offset 4, -16
 630              		.cfi_offset 5, -12
 631              		.cfi_offset 6, -8
 632              		.cfi_offset 14, -4
 354:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   u32_t now;
 633              		.loc 1 354 3 view .LVU159
 355:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** 
 356:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   LWIP_ASSERT_CORE_LOCKED();
 634              		.loc 1 356 28 view .LVU160
 357:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** 
 358:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   /* Process only timers expired at the start of the function. */
 359:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   now = sys_now();
 635              		.loc 1 359 3 view .LVU161
 636              		.loc 1 359 9 is_stmt 0 view .LVU162
 637 0002 FFF7FEFF 		bl	sys_now
 638              	.LVL50:
 639 0006 0546     		mov	r5, r0
 640              	.LVL51:
 641              	.L63:
 360:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccMzS9LP.s 			page 20


 361:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   do {
 642              		.loc 1 361 3 is_stmt 1 view .LVU163
 643              	.LBB2:
 362:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     struct sys_timeo *tmptimeout;
 644              		.loc 1 362 5 view .LVU164
 363:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     sys_timeout_handler handler;
 645              		.loc 1 363 5 view .LVU165
 364:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     void *arg;
 646              		.loc 1 364 5 view .LVU166
 365:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** 
 366:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     PBUF_CHECK_FREE_OOSEQ();
 647              		.loc 1 366 28 view .LVU167
 367:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** 
 368:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     tmptimeout = next_timeout;
 648              		.loc 1 368 5 view .LVU168
 649              		.loc 1 368 16 is_stmt 0 view .LVU169
 650 0008 0B4B     		ldr	r3, .L66
 651 000a 1968     		ldr	r1, [r3]
 652              	.LVL52:
 369:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     if (tmptimeout == NULL) {
 653              		.loc 1 369 5 is_stmt 1 view .LVU170
 654              		.loc 1 369 8 is_stmt 0 view .LVU171
 655 000c 91B1     		cbz	r1, .L61
 370:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****       return;
 371:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     }
 372:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** 
 373:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     if (TIME_LESS_THAN(now, tmptimeout->time)) {
 656              		.loc 1 373 5 is_stmt 1 view .LVU172
 657              		.loc 1 373 9 is_stmt 0 view .LVU173
 658 000e 4B68     		ldr	r3, [r1, #4]
 659 0010 EA1A     		subs	r2, r5, r3
 660              		.loc 1 373 8 view .LVU174
 661 0012 002A     		cmp	r2, #0
 662 0014 0EDB     		blt	.L61
 374:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****       return;
 375:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     }
 376:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** 
 377:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     /* Timeout has expired */
 378:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     next_timeout = tmptimeout->next;
 663              		.loc 1 378 5 is_stmt 1 view .LVU175
 664              		.loc 1 378 30 is_stmt 0 view .LVU176
 665 0016 0868     		ldr	r0, [r1]
 666              		.loc 1 378 18 view .LVU177
 667 0018 074A     		ldr	r2, .L66
 668 001a 1060     		str	r0, [r2]
 379:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     handler = tmptimeout->h;
 669              		.loc 1 379 5 is_stmt 1 view .LVU178
 670              		.loc 1 379 13 is_stmt 0 view .LVU179
 671 001c 8C68     		ldr	r4, [r1, #8]
 672              	.LVL53:
 380:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     arg = tmptimeout->arg;
 673              		.loc 1 380 5 is_stmt 1 view .LVU180
 674              		.loc 1 380 9 is_stmt 0 view .LVU181
 675 001e CE68     		ldr	r6, [r1, #12]
 676              	.LVL54:
 381:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     current_timeout_due_time = tmptimeout->time;
 677              		.loc 1 381 5 is_stmt 1 view .LVU182
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccMzS9LP.s 			page 21


 678              		.loc 1 381 30 is_stmt 0 view .LVU183
 679 0020 064A     		ldr	r2, .L66+4
 680 0022 1360     		str	r3, [r2]
 382:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #if LWIP_DEBUG_TIMERNAMES
 383:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     if (handler != NULL) {
 384:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****       LWIP_DEBUGF(TIMERS_DEBUG, ("sct calling h=%s t=%"U32_F" arg=%p\n",
 385:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****                                  tmptimeout->handler_name, sys_now() - tmptimeout->time, arg));
 386:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     }
 387:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** #endif /* LWIP_DEBUG_TIMERNAMES */
 388:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     memp_free(MEMP_SYS_TIMEOUT, tmptimeout);
 681              		.loc 1 388 5 is_stmt 1 view .LVU184
 682 0024 0A20     		movs	r0, #10
 683 0026 FFF7FEFF 		bl	memp_free
 684              	.LVL55:
 389:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     if (handler != NULL) {
 685              		.loc 1 389 5 view .LVU185
 686              		.loc 1 389 8 is_stmt 0 view .LVU186
 687 002a 002C     		cmp	r4, #0
 688 002c ECD0     		beq	.L63
 390:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****       handler(arg);
 689              		.loc 1 390 7 is_stmt 1 view .LVU187
 690 002e 3046     		mov	r0, r6
 691 0030 A047     		blx	r4
 692              	.LVL56:
 693 0032 E9E7     		b	.L63
 694              	.LVL57:
 695              	.L61:
 696              		.loc 1 390 7 is_stmt 0 view .LVU188
 697              	.LBE2:
 391:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     }
 392:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     LWIP_TCPIP_THREAD_ALIVE();
 393:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** 
 394:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     /* Repeat until all expired timers have been called */
 395:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   } while (1);
 396:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** }
 698              		.loc 1 396 1 view .LVU189
 699 0034 70BD     		pop	{r4, r5, r6, pc}
 700              	.LVL58:
 701              	.L67:
 702              		.loc 1 396 1 view .LVU190
 703 0036 00BF     		.align	2
 704              	.L66:
 705 0038 00000000 		.word	next_timeout
 706 003c 00000000 		.word	current_timeout_due_time
 707              		.cfi_endproc
 708              	.LFE181:
 710              		.section	.text.sys_restart_timeouts,"ax",%progbits
 711              		.align	1
 712              		.global	sys_restart_timeouts
 713              		.syntax unified
 714              		.thumb
 715              		.thumb_func
 717              	sys_restart_timeouts:
 718              	.LFB182:
 397:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** 
 398:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** /** Rebase the timeout times to the current time.
 399:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  * This is necessary if sys_check_timeouts() hasn't been called for a long
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccMzS9LP.s 			page 22


 400:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  * time (e.g. while saving energy) to prevent all timer functions of that
 401:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  * period being called.
 402:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  */
 403:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** void
 404:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** sys_restart_timeouts(void)
 405:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** {
 719              		.loc 1 405 1 is_stmt 1 view -0
 720              		.cfi_startproc
 721              		@ args = 0, pretend = 0, frame = 0
 722              		@ frame_needed = 0, uses_anonymous_args = 0
 723 0000 08B5     		push	{r3, lr}
 724              	.LCFI9:
 725              		.cfi_def_cfa_offset 8
 726              		.cfi_offset 3, -8
 727              		.cfi_offset 14, -4
 406:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   u32_t now;
 728              		.loc 1 406 3 view .LVU192
 407:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   u32_t base;
 729              		.loc 1 407 3 view .LVU193
 408:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   struct sys_timeo *t;
 730              		.loc 1 408 3 view .LVU194
 409:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** 
 410:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   if (next_timeout == NULL) {
 731              		.loc 1 410 3 view .LVU195
 732              		.loc 1 410 20 is_stmt 0 view .LVU196
 733 0002 084B     		ldr	r3, .L73
 734 0004 1B68     		ldr	r3, [r3]
 735              		.loc 1 410 6 view .LVU197
 736 0006 63B1     		cbz	r3, .L68
 411:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     return;
 412:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   }
 413:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** 
 414:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   now = sys_now();
 737              		.loc 1 414 3 is_stmt 1 view .LVU198
 738              		.loc 1 414 9 is_stmt 0 view .LVU199
 739 0008 FFF7FEFF 		bl	sys_now
 740              	.LVL59:
 415:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   base = next_timeout->time;
 741              		.loc 1 415 3 is_stmt 1 view .LVU200
 742              		.loc 1 415 22 is_stmt 0 view .LVU201
 743 000c 054B     		ldr	r3, .L73
 744 000e 1A68     		ldr	r2, [r3]
 745              		.loc 1 415 8 view .LVU202
 746 0010 5168     		ldr	r1, [r2, #4]
 747              	.LVL60:
 416:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** 
 417:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   for (t = next_timeout; t != NULL; t = t->next) {
 748              		.loc 1 417 3 is_stmt 1 view .LVU203
 749              		.loc 1 417 3 is_stmt 0 view .LVU204
 750 0012 04E0     		b	.L70
 751              	.L71:
 418:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     t->time = (t->time - base) + now;
 752              		.loc 1 418 5 is_stmt 1 view .LVU205
 753              		.loc 1 418 17 is_stmt 0 view .LVU206
 754 0014 5368     		ldr	r3, [r2, #4]
 755              		.loc 1 418 24 view .LVU207
 756 0016 5B1A     		subs	r3, r3, r1
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccMzS9LP.s 			page 23


 757              		.loc 1 418 32 view .LVU208
 758 0018 0344     		add	r3, r3, r0
 759              		.loc 1 418 13 view .LVU209
 760 001a 5360     		str	r3, [r2, #4]
 417:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     t->time = (t->time - base) + now;
 761              		.loc 1 417 39 is_stmt 1 discriminator 3 view .LVU210
 762 001c 1268     		ldr	r2, [r2]
 763              	.LVL61:
 764              	.L70:
 417:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     t->time = (t->time - base) + now;
 765              		.loc 1 417 28 discriminator 1 view .LVU211
 766 001e 002A     		cmp	r2, #0
 767 0020 F8D1     		bne	.L71
 768              	.LVL62:
 769              	.L68:
 419:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   }
 420:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** }
 770              		.loc 1 420 1 is_stmt 0 view .LVU212
 771 0022 08BD     		pop	{r3, pc}
 772              	.L74:
 773              		.align	2
 774              	.L73:
 775 0024 00000000 		.word	next_timeout
 776              		.cfi_endproc
 777              	.LFE182:
 779              		.section	.rodata.sys_timeouts_sleeptime.str1.4,"aMS",%progbits,1
 780              		.align	2
 781              	.LC4:
 782 0000 696E7661 		.ascii	"invalid sleeptime\000"
 782      6C696420 
 782      736C6565 
 782      7074696D 
 782      6500
 783              		.section	.text.sys_timeouts_sleeptime,"ax",%progbits
 784              		.align	1
 785              		.global	sys_timeouts_sleeptime
 786              		.syntax unified
 787              		.thumb
 788              		.thumb_func
 790              	sys_timeouts_sleeptime:
 791              	.LFB183:
 421:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** 
 422:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** /** Return the time left before the next timeout is due. If no timeouts are
 423:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  * enqueued, returns 0xffffffff
 424:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****  */
 425:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** u32_t
 426:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** sys_timeouts_sleeptime(void)
 427:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** {
 792              		.loc 1 427 1 is_stmt 1 view -0
 793              		.cfi_startproc
 794              		@ args = 0, pretend = 0, frame = 0
 795              		@ frame_needed = 0, uses_anonymous_args = 0
 796 0000 10B5     		push	{r4, lr}
 797              	.LCFI10:
 798              		.cfi_def_cfa_offset 8
 799              		.cfi_offset 4, -8
 800              		.cfi_offset 14, -4
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccMzS9LP.s 			page 24


 428:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   u32_t now;
 801              		.loc 1 428 3 view .LVU214
 429:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** 
 430:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   LWIP_ASSERT_CORE_LOCKED();
 802              		.loc 1 430 28 view .LVU215
 431:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** 
 432:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   if (next_timeout == NULL) {
 803              		.loc 1 432 3 view .LVU216
 804              		.loc 1 432 20 is_stmt 0 view .LVU217
 805 0002 0D4B     		ldr	r3, .L81
 806 0004 1B68     		ldr	r3, [r3]
 807              		.loc 1 432 6 view .LVU218
 808 0006 83B1     		cbz	r3, .L78
 433:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     return SYS_TIMEOUTS_SLEEPTIME_INFINITE;
 434:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   }
 435:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   now = sys_now();
 809              		.loc 1 435 3 is_stmt 1 view .LVU219
 810              		.loc 1 435 9 is_stmt 0 view .LVU220
 811 0008 FFF7FEFF 		bl	sys_now
 812              	.LVL63:
 436:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   if (TIME_LESS_THAN(next_timeout->time, now)) {
 813              		.loc 1 436 3 is_stmt 1 view .LVU221
 814              		.loc 1 436 7 is_stmt 0 view .LVU222
 815 000c 0A4B     		ldr	r3, .L81
 816 000e 1B68     		ldr	r3, [r3]
 817 0010 5C68     		ldr	r4, [r3, #4]
 818              		.loc 1 436 6 view .LVU223
 819 0012 241A     		subs	r4, r4, r0
 820 0014 0CD4     		bmi	.L79
 821              	.LBB3:
 437:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     return 0;
 438:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   } else {
 439:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     u32_t ret = (u32_t)(next_timeout->time - now);
 822              		.loc 1 439 5 is_stmt 1 view .LVU224
 823              	.LVL64:
 440:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     LWIP_ASSERT("invalid sleeptime", ret <= LWIP_MAX_TIMEOUT);
 824              		.loc 1 440 5 view .LVU225
 825              		.loc 1 440 5 view .LVU226
 826 0016 002C     		cmp	r4, #0
 827 0018 0BDA     		bge	.L75
 828              		.loc 1 440 5 discriminator 1 view .LVU227
 829              		.loc 1 440 5 discriminator 1 view .LVU228
 830 001a 084B     		ldr	r3, .L81+4
 831 001c 4FF4DC72 		mov	r2, #440
 832 0020 0749     		ldr	r1, .L81+8
 833 0022 0848     		ldr	r0, .L81+12
 834              	.LVL65:
 835              		.loc 1 440 5 is_stmt 0 discriminator 1 view .LVU229
 836 0024 FFF7FEFF 		bl	printf
 837              	.LVL66:
 838              		.loc 1 440 5 is_stmt 1 discriminator 3 view .LVU230
 839              		.loc 1 440 5 discriminator 3 view .LVU231
 441:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****     return ret;
 840              		.loc 1 441 5 view .LVU232
 841              		.loc 1 441 12 is_stmt 0 view .LVU233
 842 0028 03E0     		b	.L75
 843              	.LVL67:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccMzS9LP.s 			page 25


 844              	.L78:
 845              		.loc 1 441 12 view .LVU234
 846              	.LBE3:
 433:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   }
 847              		.loc 1 433 12 view .LVU235
 848 002a 4FF0FF34 		mov	r4, #-1
 849 002e 00E0     		b	.L75
 850              	.LVL68:
 851              	.L79:
 437:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   } else {
 852              		.loc 1 437 12 view .LVU236
 853 0030 0024     		movs	r4, #0
 854              	.LVL69:
 855              	.L75:
 442:Middlewares/Third_Party/LwIP/src/core/timeouts.c ****   }
 443:Middlewares/Third_Party/LwIP/src/core/timeouts.c **** }
 856              		.loc 1 443 1 view .LVU237
 857 0032 2046     		mov	r0, r4
 858 0034 10BD     		pop	{r4, pc}
 859              	.L82:
 860 0036 00BF     		.align	2
 861              	.L81:
 862 0038 00000000 		.word	next_timeout
 863 003c 00000000 		.word	.LC0
 864 0040 00000000 		.word	.LC4
 865 0044 74000000 		.word	.LC2
 866              		.cfi_endproc
 867              	.LFE183:
 869              		.section	.bss.tcpip_tcp_timer_active,"aw",%nobits
 870              		.align	2
 873              	tcpip_tcp_timer_active:
 874 0000 00000000 		.space	4
 875              		.section	.bss.current_timeout_due_time,"aw",%nobits
 876              		.align	2
 879              	current_timeout_due_time:
 880 0000 00000000 		.space	4
 881              		.section	.bss.next_timeout,"aw",%nobits
 882              		.align	2
 885              	next_timeout:
 886 0000 00000000 		.space	4
 887              		.global	lwip_num_cyclic_timers
 888              		.section	.rodata.lwip_num_cyclic_timers,"a"
 889              		.align	2
 892              	lwip_num_cyclic_timers:
 893 0000 03000000 		.word	3
 894              		.global	lwip_cyclic_timers
 895              		.section	.rodata.lwip_cyclic_timers,"a"
 896              		.align	2
 899              	lwip_cyclic_timers:
 900 0000 FA000000 		.word	250
 901 0004 00000000 		.word	tcp_tmr
 902 0008 E8030000 		.word	1000
 903 000c 00000000 		.word	ip_reass_tmr
 904 0010 E8030000 		.word	1000
 905 0014 00000000 		.word	etharp_tmr
 906              		.text
 907              	.Letext0:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccMzS9LP.s 			page 26


 908              		.file 2 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\../lib/gcc/arm-none-eabi/13.3.1/..
 909              		.file 3 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\../lib/gcc/arm-none-eabi/13.3.1/..
 910              		.file 4 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\../lib/gcc/arm-none-eabi/13.3.1/in
 911              		.file 5 "Middlewares/Third_Party/LwIP/src/include/lwip/arch.h"
 912              		.file 6 "Middlewares/Third_Party/LwIP/src/include/lwip/err.h"
 913              		.file 7 "Middlewares/Third_Party/LwIP/src/include/lwip/timeouts.h"
 914              		.file 8 "Middlewares/Third_Party/LwIP/src/include/lwip/tcpbase.h"
 915              		.file 9 "Middlewares/Third_Party/LwIP/src/include/lwip/pbuf.h"
 916              		.file 10 "Middlewares/Third_Party/LwIP/src/include/lwip/ip4_addr.h"
 917              		.file 11 "Middlewares/Third_Party/LwIP/src/include/lwip/ip_addr.h"
 918              		.file 12 "Middlewares/Third_Party/LwIP/src/include/lwip/memp.h"
 919              		.file 13 "Middlewares/Third_Party/LwIP/src/include/lwip/tcp.h"
 920              		.file 14 "Middlewares/Third_Party/LwIP/src/include/lwip/priv/tcp_priv.h"
 921              		.file 15 "Middlewares/Third_Party/LwIP/src/include/lwip/prot/tcp.h"
 922              		.file 16 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\../lib/gcc/arm-none-eabi/13.3.1/.
 923              		.file 17 "Middlewares/Third_Party/LwIP/src/include/lwip/etharp.h"
 924              		.file 18 "Middlewares/Third_Party/LwIP/src/include/lwip/ip4_frag.h"
 925              		.file 19 "Middlewares/Third_Party/LwIP/src/include/lwip/sys.h"
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccMzS9LP.s 			page 27


DEFINED SYMBOLS
                            *ABS*:00000000 timeouts.c
C:\Users\<USER>\AppData\Local\Temp\ccMzS9LP.s:20     .rodata.sys_timeout_abs.str1.4:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\ccMzS9LP.s:31     .text.sys_timeout_abs:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccMzS9LP.s:36     .text.sys_timeout_abs:00000000 sys_timeout_abs
C:\Users\<USER>\AppData\Local\Temp\ccMzS9LP.s:165    .text.sys_timeout_abs:00000060 $d
C:\Users\<USER>\AppData\Local\Temp\ccMzS9LP.s:885    .bss.next_timeout:00000000 next_timeout
C:\Users\<USER>\AppData\Local\Temp\ccMzS9LP.s:173    .text.lwip_cyclic_timer:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccMzS9LP.s:178    .text.lwip_cyclic_timer:00000000 lwip_cyclic_timer
C:\Users\<USER>\AppData\Local\Temp\ccMzS9LP.s:249    .text.lwip_cyclic_timer:00000038 $d
C:\Users\<USER>\AppData\Local\Temp\ccMzS9LP.s:879    .bss.current_timeout_due_time:00000000 current_timeout_due_time
C:\Users\<USER>\AppData\Local\Temp\ccMzS9LP.s:255    .rodata.sys_timeout.str1.4:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\ccMzS9LP.s:260    .text.sys_timeout:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccMzS9LP.s:266    .text.sys_timeout:00000000 sys_timeout
C:\Users\<USER>\AppData\Local\Temp\ccMzS9LP.s:328    .text.sys_timeout:00000030 $d
C:\Users\<USER>\AppData\Local\Temp\ccMzS9LP.s:335    .text.tcp_timer_needed:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccMzS9LP.s:341    .text.tcp_timer_needed:00000000 tcp_timer_needed
C:\Users\<USER>\AppData\Local\Temp\ccMzS9LP.s:389    .text.tcp_timer_needed:0000002c $d
C:\Users\<USER>\AppData\Local\Temp\ccMzS9LP.s:873    .bss.tcpip_tcp_timer_active:00000000 tcpip_tcp_timer_active
C:\Users\<USER>\AppData\Local\Temp\ccMzS9LP.s:402    .text.tcpip_tcp_timer:00000000 tcpip_tcp_timer
C:\Users\<USER>\AppData\Local\Temp\ccMzS9LP.s:397    .text.tcpip_tcp_timer:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccMzS9LP.s:451    .text.tcpip_tcp_timer:00000028 $d
C:\Users\<USER>\AppData\Local\Temp\ccMzS9LP.s:459    .text.sys_timeouts_init:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccMzS9LP.s:465    .text.sys_timeouts_init:00000000 sys_timeouts_init
C:\Users\<USER>\AppData\Local\Temp\ccMzS9LP.s:508    .text.sys_timeouts_init:00000020 $d
C:\Users\<USER>\AppData\Local\Temp\ccMzS9LP.s:899    .rodata.lwip_cyclic_timers:00000000 lwip_cyclic_timers
C:\Users\<USER>\AppData\Local\Temp\ccMzS9LP.s:514    .text.sys_untimeout:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccMzS9LP.s:520    .text.sys_untimeout:00000000 sys_untimeout
C:\Users\<USER>\AppData\Local\Temp\ccMzS9LP.s:609    .text.sys_untimeout:0000003c $d
C:\Users\<USER>\AppData\Local\Temp\ccMzS9LP.s:614    .text.sys_check_timeouts:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccMzS9LP.s:620    .text.sys_check_timeouts:00000000 sys_check_timeouts
C:\Users\<USER>\AppData\Local\Temp\ccMzS9LP.s:705    .text.sys_check_timeouts:00000038 $d
C:\Users\<USER>\AppData\Local\Temp\ccMzS9LP.s:711    .text.sys_restart_timeouts:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccMzS9LP.s:717    .text.sys_restart_timeouts:00000000 sys_restart_timeouts
C:\Users\<USER>\AppData\Local\Temp\ccMzS9LP.s:775    .text.sys_restart_timeouts:00000024 $d
C:\Users\<USER>\AppData\Local\Temp\ccMzS9LP.s:780    .rodata.sys_timeouts_sleeptime.str1.4:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\ccMzS9LP.s:784    .text.sys_timeouts_sleeptime:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccMzS9LP.s:790    .text.sys_timeouts_sleeptime:00000000 sys_timeouts_sleeptime
C:\Users\<USER>\AppData\Local\Temp\ccMzS9LP.s:862    .text.sys_timeouts_sleeptime:00000038 $d
C:\Users\<USER>\AppData\Local\Temp\ccMzS9LP.s:870    .bss.tcpip_tcp_timer_active:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\ccMzS9LP.s:876    .bss.current_timeout_due_time:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\ccMzS9LP.s:882    .bss.next_timeout:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\ccMzS9LP.s:892    .rodata.lwip_num_cyclic_timers:00000000 lwip_num_cyclic_timers
C:\Users\<USER>\AppData\Local\Temp\ccMzS9LP.s:889    .rodata.lwip_num_cyclic_timers:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\ccMzS9LP.s:896    .rodata.lwip_cyclic_timers:00000000 $d

UNDEFINED SYMBOLS
memp_malloc
printf
sys_now
tcp_active_pcbs
tcp_tw_pcbs
tcp_tmr
memp_free
ip_reass_tmr
etharp_tmr
