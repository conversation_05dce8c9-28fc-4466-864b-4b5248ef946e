ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccAycewk.s 			page 1


   1              		.cpu cortex-m7
   2              		.arch armv7e-m
   3              		.fpu fpv5-d16
   4              		.eabi_attribute 28, 1
   5              		.eabi_attribute 20, 1
   6              		.eabi_attribute 21, 1
   7              		.eabi_attribute 23, 3
   8              		.eabi_attribute 24, 1
   9              		.eabi_attribute 25, 1
  10              		.eabi_attribute 26, 1
  11              		.eabi_attribute 30, 1
  12              		.eabi_attribute 34, 1
  13              		.eabi_attribute 18, 4
  14              		.file	"init.c"
  15              		.text
  16              	.Ltext0:
  17              		.cfi_sections	.debug_frame
  18              		.file 1 "Middlewares/Third_Party/LwIP/src/core/init.c"
  19              		.section	.text.lwip_init,"ax",%progbits
  20              		.align	1
  21              		.global	lwip_init
  22              		.syntax unified
  23              		.thumb
  24              		.thumb_func
  26              	lwip_init:
  27              	.LFB174:
   1:Middlewares/Third_Party/LwIP/src/core/init.c **** /**
   2:Middlewares/Third_Party/LwIP/src/core/init.c ****  * @file
   3:Middlewares/Third_Party/LwIP/src/core/init.c ****  * Modules initialization
   4:Middlewares/Third_Party/LwIP/src/core/init.c ****  *
   5:Middlewares/Third_Party/LwIP/src/core/init.c ****  */
   6:Middlewares/Third_Party/LwIP/src/core/init.c **** 
   7:Middlewares/Third_Party/LwIP/src/core/init.c **** /*
   8:Middlewares/Third_Party/LwIP/src/core/init.c ****  * Copyright (c) 2001-2004 Swedish Institute of Computer Science.
   9:Middlewares/Third_Party/LwIP/src/core/init.c ****  * All rights reserved.
  10:Middlewares/Third_Party/LwIP/src/core/init.c ****  *
  11:Middlewares/Third_Party/LwIP/src/core/init.c ****  * Redistribution and use in source and binary forms, with or without modification,
  12:Middlewares/Third_Party/LwIP/src/core/init.c ****  * are permitted provided that the following conditions are met:
  13:Middlewares/Third_Party/LwIP/src/core/init.c ****  *
  14:Middlewares/Third_Party/LwIP/src/core/init.c ****  * 1. Redistributions of source code must retain the above copyright notice,
  15:Middlewares/Third_Party/LwIP/src/core/init.c ****  *    this list of conditions and the following disclaimer.
  16:Middlewares/Third_Party/LwIP/src/core/init.c ****  * 2. Redistributions in binary form must reproduce the above copyright notice,
  17:Middlewares/Third_Party/LwIP/src/core/init.c ****  *    this list of conditions and the following disclaimer in the documentation
  18:Middlewares/Third_Party/LwIP/src/core/init.c ****  *    and/or other materials provided with the distribution.
  19:Middlewares/Third_Party/LwIP/src/core/init.c ****  * 3. The name of the author may not be used to endorse or promote products
  20:Middlewares/Third_Party/LwIP/src/core/init.c ****  *    derived from this software without specific prior written permission.
  21:Middlewares/Third_Party/LwIP/src/core/init.c ****  *
  22:Middlewares/Third_Party/LwIP/src/core/init.c ****  * THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR IMPLIED
  23:Middlewares/Third_Party/LwIP/src/core/init.c ****  * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
  24:Middlewares/Third_Party/LwIP/src/core/init.c ****  * MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT
  25:Middlewares/Third_Party/LwIP/src/core/init.c ****  * SHALL THE AUTHOR BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
  26:Middlewares/Third_Party/LwIP/src/core/init.c ****  * EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT
  27:Middlewares/Third_Party/LwIP/src/core/init.c ****  * OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
  28:Middlewares/Third_Party/LwIP/src/core/init.c ****  * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
  29:Middlewares/Third_Party/LwIP/src/core/init.c ****  * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
  30:Middlewares/Third_Party/LwIP/src/core/init.c ****  * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY
  31:Middlewares/Third_Party/LwIP/src/core/init.c ****  * OF SUCH DAMAGE.
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccAycewk.s 			page 2


  32:Middlewares/Third_Party/LwIP/src/core/init.c ****  *
  33:Middlewares/Third_Party/LwIP/src/core/init.c ****  * This file is part of the lwIP TCP/IP stack.
  34:Middlewares/Third_Party/LwIP/src/core/init.c ****  *
  35:Middlewares/Third_Party/LwIP/src/core/init.c ****  * Author: Adam Dunkels <<EMAIL>>
  36:Middlewares/Third_Party/LwIP/src/core/init.c ****  */
  37:Middlewares/Third_Party/LwIP/src/core/init.c **** 
  38:Middlewares/Third_Party/LwIP/src/core/init.c **** #include "lwip/opt.h"
  39:Middlewares/Third_Party/LwIP/src/core/init.c **** 
  40:Middlewares/Third_Party/LwIP/src/core/init.c **** #include "lwip/init.h"
  41:Middlewares/Third_Party/LwIP/src/core/init.c **** #include "lwip/stats.h"
  42:Middlewares/Third_Party/LwIP/src/core/init.c **** #include "lwip/sys.h"
  43:Middlewares/Third_Party/LwIP/src/core/init.c **** #include "lwip/mem.h"
  44:Middlewares/Third_Party/LwIP/src/core/init.c **** #include "lwip/memp.h"
  45:Middlewares/Third_Party/LwIP/src/core/init.c **** #include "lwip/pbuf.h"
  46:Middlewares/Third_Party/LwIP/src/core/init.c **** #include "lwip/netif.h"
  47:Middlewares/Third_Party/LwIP/src/core/init.c **** #include "lwip/sockets.h"
  48:Middlewares/Third_Party/LwIP/src/core/init.c **** #include "lwip/ip.h"
  49:Middlewares/Third_Party/LwIP/src/core/init.c **** #include "lwip/raw.h"
  50:Middlewares/Third_Party/LwIP/src/core/init.c **** #include "lwip/udp.h"
  51:Middlewares/Third_Party/LwIP/src/core/init.c **** #include "lwip/priv/tcp_priv.h"
  52:Middlewares/Third_Party/LwIP/src/core/init.c **** #include "lwip/igmp.h"
  53:Middlewares/Third_Party/LwIP/src/core/init.c **** #include "lwip/dns.h"
  54:Middlewares/Third_Party/LwIP/src/core/init.c **** #include "lwip/timeouts.h"
  55:Middlewares/Third_Party/LwIP/src/core/init.c **** #include "lwip/etharp.h"
  56:Middlewares/Third_Party/LwIP/src/core/init.c **** #include "lwip/ip6.h"
  57:Middlewares/Third_Party/LwIP/src/core/init.c **** #include "lwip/nd6.h"
  58:Middlewares/Third_Party/LwIP/src/core/init.c **** #include "lwip/mld6.h"
  59:Middlewares/Third_Party/LwIP/src/core/init.c **** #include "lwip/api.h"
  60:Middlewares/Third_Party/LwIP/src/core/init.c **** 
  61:Middlewares/Third_Party/LwIP/src/core/init.c **** #include "netif/ppp/ppp_opts.h"
  62:Middlewares/Third_Party/LwIP/src/core/init.c **** #include "netif/ppp/ppp_impl.h"
  63:Middlewares/Third_Party/LwIP/src/core/init.c **** 
  64:Middlewares/Third_Party/LwIP/src/core/init.c **** #ifndef LWIP_SKIP_PACKING_CHECK
  65:Middlewares/Third_Party/LwIP/src/core/init.c **** 
  66:Middlewares/Third_Party/LwIP/src/core/init.c **** #ifdef PACK_STRUCT_USE_INCLUDES
  67:Middlewares/Third_Party/LwIP/src/core/init.c **** #  include "arch/bpstruct.h"
  68:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif
  69:Middlewares/Third_Party/LwIP/src/core/init.c **** PACK_STRUCT_BEGIN
  70:Middlewares/Third_Party/LwIP/src/core/init.c **** struct packed_struct_test {
  71:Middlewares/Third_Party/LwIP/src/core/init.c ****   PACK_STRUCT_FLD_8(u8_t  dummy1);
  72:Middlewares/Third_Party/LwIP/src/core/init.c ****   PACK_STRUCT_FIELD(u32_t dummy2);
  73:Middlewares/Third_Party/LwIP/src/core/init.c **** } PACK_STRUCT_STRUCT;
  74:Middlewares/Third_Party/LwIP/src/core/init.c **** PACK_STRUCT_END
  75:Middlewares/Third_Party/LwIP/src/core/init.c **** #ifdef PACK_STRUCT_USE_INCLUDES
  76:Middlewares/Third_Party/LwIP/src/core/init.c **** #  include "arch/epstruct.h"
  77:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif
  78:Middlewares/Third_Party/LwIP/src/core/init.c **** #define PACKED_STRUCT_TEST_EXPECTED_SIZE 5
  79:Middlewares/Third_Party/LwIP/src/core/init.c **** 
  80:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif
  81:Middlewares/Third_Party/LwIP/src/core/init.c **** 
  82:Middlewares/Third_Party/LwIP/src/core/init.c **** /* Compile-time sanity checks for configuration errors.
  83:Middlewares/Third_Party/LwIP/src/core/init.c ****  * These can be done independently of LWIP_DEBUG, without penalty.
  84:Middlewares/Third_Party/LwIP/src/core/init.c ****  */
  85:Middlewares/Third_Party/LwIP/src/core/init.c **** #ifndef BYTE_ORDER
  86:Middlewares/Third_Party/LwIP/src/core/init.c **** #error "BYTE_ORDER is not defined, you have to define it in your cc.h"
  87:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif
  88:Middlewares/Third_Party/LwIP/src/core/init.c **** #if (!IP_SOF_BROADCAST && IP_SOF_BROADCAST_RECV)
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccAycewk.s 			page 3


  89:Middlewares/Third_Party/LwIP/src/core/init.c **** #error "If you want to use broadcast filter per pcb on recv operations, you have to define IP_SOF_B
  90:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif
  91:Middlewares/Third_Party/LwIP/src/core/init.c **** #if (!LWIP_UDP && LWIP_UDPLITE)
  92:Middlewares/Third_Party/LwIP/src/core/init.c **** #error "If you want to use UDP Lite, you have to define LWIP_UDP=1 in your lwipopts.h"
  93:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif
  94:Middlewares/Third_Party/LwIP/src/core/init.c **** #if (!LWIP_UDP && LWIP_DHCP)
  95:Middlewares/Third_Party/LwIP/src/core/init.c **** #error "If you want to use DHCP, you have to define LWIP_UDP=1 in your lwipopts.h"
  96:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif
  97:Middlewares/Third_Party/LwIP/src/core/init.c **** #if (!LWIP_UDP && !LWIP_RAW && LWIP_MULTICAST_TX_OPTIONS)
  98:Middlewares/Third_Party/LwIP/src/core/init.c **** #error "If you want to use LWIP_MULTICAST_TX_OPTIONS, you have to define LWIP_UDP=1 and/or LWIP_RAW
  99:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif
 100:Middlewares/Third_Party/LwIP/src/core/init.c **** #if (!LWIP_UDP && LWIP_DNS)
 101:Middlewares/Third_Party/LwIP/src/core/init.c **** #error "If you want to use DNS, you have to define LWIP_UDP=1 in your lwipopts.h"
 102:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif
 103:Middlewares/Third_Party/LwIP/src/core/init.c **** #if !MEMP_MEM_MALLOC /* MEMP_NUM_* checks are disabled when not using the pool allocator */
 104:Middlewares/Third_Party/LwIP/src/core/init.c **** #if (LWIP_ARP && ARP_QUEUEING && (MEMP_NUM_ARP_QUEUE<=0))
 105:Middlewares/Third_Party/LwIP/src/core/init.c **** #error "If you want to use ARP Queueing, you have to define MEMP_NUM_ARP_QUEUE>=1 in your lwipopts.
 106:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif
 107:Middlewares/Third_Party/LwIP/src/core/init.c **** #if (LWIP_RAW && (MEMP_NUM_RAW_PCB<=0))
 108:Middlewares/Third_Party/LwIP/src/core/init.c **** #error "If you want to use RAW, you have to define MEMP_NUM_RAW_PCB>=1 in your lwipopts.h"
 109:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif
 110:Middlewares/Third_Party/LwIP/src/core/init.c **** #if (LWIP_UDP && (MEMP_NUM_UDP_PCB<=0))
 111:Middlewares/Third_Party/LwIP/src/core/init.c **** #error "If you want to use UDP, you have to define MEMP_NUM_UDP_PCB>=1 in your lwipopts.h"
 112:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif
 113:Middlewares/Third_Party/LwIP/src/core/init.c **** #if (LWIP_TCP && (MEMP_NUM_TCP_PCB<=0))
 114:Middlewares/Third_Party/LwIP/src/core/init.c **** #error "If you want to use TCP, you have to define MEMP_NUM_TCP_PCB>=1 in your lwipopts.h"
 115:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif
 116:Middlewares/Third_Party/LwIP/src/core/init.c **** #if (LWIP_IGMP && (MEMP_NUM_IGMP_GROUP<=1))
 117:Middlewares/Third_Party/LwIP/src/core/init.c **** #error "If you want to use IGMP, you have to define MEMP_NUM_IGMP_GROUP>1 in your lwipopts.h"
 118:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif
 119:Middlewares/Third_Party/LwIP/src/core/init.c **** #if (LWIP_IGMP && !LWIP_MULTICAST_TX_OPTIONS)
 120:Middlewares/Third_Party/LwIP/src/core/init.c **** #error "If you want to use IGMP, you have to define LWIP_MULTICAST_TX_OPTIONS==1 in your lwipopts.h
 121:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif
 122:Middlewares/Third_Party/LwIP/src/core/init.c **** #if (LWIP_IGMP && !LWIP_IPV4)
 123:Middlewares/Third_Party/LwIP/src/core/init.c **** #error "IGMP needs LWIP_IPV4 enabled in your lwipopts.h"
 124:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif
 125:Middlewares/Third_Party/LwIP/src/core/init.c **** #if ((LWIP_NETCONN || LWIP_SOCKET) && (MEMP_NUM_TCPIP_MSG_API<=0))
 126:Middlewares/Third_Party/LwIP/src/core/init.c **** #error "If you want to use Sequential API, you have to define MEMP_NUM_TCPIP_MSG_API>=1 in your lwi
 127:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif
 128:Middlewares/Third_Party/LwIP/src/core/init.c **** /* There must be sufficient timeouts, taking into account requirements of the subsystems. */
 129:Middlewares/Third_Party/LwIP/src/core/init.c **** #if LWIP_TIMERS && (MEMP_NUM_SYS_TIMEOUT < LWIP_NUM_SYS_TIMEOUT_INTERNAL)
 130:Middlewares/Third_Party/LwIP/src/core/init.c **** #error "MEMP_NUM_SYS_TIMEOUT is too low to accomodate all required timeouts"
 131:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif
 132:Middlewares/Third_Party/LwIP/src/core/init.c **** #if (IP_REASSEMBLY && (MEMP_NUM_REASSDATA > IP_REASS_MAX_PBUFS))
 133:Middlewares/Third_Party/LwIP/src/core/init.c **** #error "MEMP_NUM_REASSDATA > IP_REASS_MAX_PBUFS doesn't make sense since each struct ip_reassdata m
 134:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif
 135:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif /* !MEMP_MEM_MALLOC */
 136:Middlewares/Third_Party/LwIP/src/core/init.c **** #if LWIP_WND_SCALE
 137:Middlewares/Third_Party/LwIP/src/core/init.c **** #if (LWIP_TCP && (TCP_WND > 0xffffffff))
 138:Middlewares/Third_Party/LwIP/src/core/init.c **** #error "If you want to use TCP, TCP_WND must fit in an u32_t, so, you have to reduce it in your lwi
 139:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif
 140:Middlewares/Third_Party/LwIP/src/core/init.c **** #if (LWIP_TCP && (TCP_RCV_SCALE > 14))
 141:Middlewares/Third_Party/LwIP/src/core/init.c **** #error "The maximum valid window scale value is 14!"
 142:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif
 143:Middlewares/Third_Party/LwIP/src/core/init.c **** #if (LWIP_TCP && (TCP_WND > (0xFFFFU << TCP_RCV_SCALE)))
 144:Middlewares/Third_Party/LwIP/src/core/init.c **** #error "TCP_WND is bigger than the configured LWIP_WND_SCALE allows!"
 145:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccAycewk.s 			page 4


 146:Middlewares/Third_Party/LwIP/src/core/init.c **** #if (LWIP_TCP && ((TCP_WND >> TCP_RCV_SCALE) == 0))
 147:Middlewares/Third_Party/LwIP/src/core/init.c **** #error "TCP_WND is too small for the configured LWIP_WND_SCALE (results in zero window)!"
 148:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif
 149:Middlewares/Third_Party/LwIP/src/core/init.c **** #else /* LWIP_WND_SCALE */
 150:Middlewares/Third_Party/LwIP/src/core/init.c **** #if (LWIP_TCP && (TCP_WND > 0xffff))
 151:Middlewares/Third_Party/LwIP/src/core/init.c **** #error "If you want to use TCP, TCP_WND must fit in an u16_t, so, you have to reduce it in your lwi
 152:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif
 153:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif /* LWIP_WND_SCALE */
 154:Middlewares/Third_Party/LwIP/src/core/init.c **** #if (LWIP_TCP && (TCP_SND_QUEUELEN > 0xffff))
 155:Middlewares/Third_Party/LwIP/src/core/init.c **** #error "If you want to use TCP, TCP_SND_QUEUELEN must fit in an u16_t, so, you have to reduce it in
 156:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif
 157:Middlewares/Third_Party/LwIP/src/core/init.c **** #if (LWIP_TCP && (TCP_SND_QUEUELEN < 2))
 158:Middlewares/Third_Party/LwIP/src/core/init.c **** #error "TCP_SND_QUEUELEN must be at least 2 for no-copy TCP writes to work"
 159:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif
 160:Middlewares/Third_Party/LwIP/src/core/init.c **** #if (LWIP_TCP && ((TCP_MAXRTX > 12) || (TCP_SYNMAXRTX > 12)))
 161:Middlewares/Third_Party/LwIP/src/core/init.c **** #error "If you want to use TCP, TCP_MAXRTX and TCP_SYNMAXRTX must less or equal to 12 (due to tcp_b
 162:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif
 163:Middlewares/Third_Party/LwIP/src/core/init.c **** #if (LWIP_TCP && TCP_LISTEN_BACKLOG && ((TCP_DEFAULT_LISTEN_BACKLOG < 0) || (TCP_DEFAULT_LISTEN_BAC
 164:Middlewares/Third_Party/LwIP/src/core/init.c **** #error "If you want to use TCP backlog, TCP_DEFAULT_LISTEN_BACKLOG must fit into an u8_t"
 165:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif
 166:Middlewares/Third_Party/LwIP/src/core/init.c **** #if (LWIP_TCP && LWIP_TCP_SACK_OUT && !TCP_QUEUE_OOSEQ)
 167:Middlewares/Third_Party/LwIP/src/core/init.c **** #error "To use LWIP_TCP_SACK_OUT, TCP_QUEUE_OOSEQ needs to be enabled"
 168:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif
 169:Middlewares/Third_Party/LwIP/src/core/init.c **** #if (LWIP_TCP && LWIP_TCP_SACK_OUT && (LWIP_TCP_MAX_SACK_NUM < 1))
 170:Middlewares/Third_Party/LwIP/src/core/init.c **** #error "LWIP_TCP_MAX_SACK_NUM must be greater than 0"
 171:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif
 172:Middlewares/Third_Party/LwIP/src/core/init.c **** #if (LWIP_NETIF_API && (NO_SYS==1))
 173:Middlewares/Third_Party/LwIP/src/core/init.c **** #error "If you want to use NETIF API, you have to define NO_SYS=0 in your lwipopts.h"
 174:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif
 175:Middlewares/Third_Party/LwIP/src/core/init.c **** #if ((LWIP_SOCKET || LWIP_NETCONN) && (NO_SYS==1))
 176:Middlewares/Third_Party/LwIP/src/core/init.c **** #error "If you want to use Sequential API, you have to define NO_SYS=0 in your lwipopts.h"
 177:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif
 178:Middlewares/Third_Party/LwIP/src/core/init.c **** #if (LWIP_PPP_API && (NO_SYS==1))
 179:Middlewares/Third_Party/LwIP/src/core/init.c **** #error "If you want to use PPP API, you have to define NO_SYS=0 in your lwipopts.h"
 180:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif
 181:Middlewares/Third_Party/LwIP/src/core/init.c **** #if (LWIP_PPP_API && (PPP_SUPPORT==0))
 182:Middlewares/Third_Party/LwIP/src/core/init.c **** #error "If you want to use PPP API, you have to enable PPP_SUPPORT in your lwipopts.h"
 183:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif
 184:Middlewares/Third_Party/LwIP/src/core/init.c **** #if (((!LWIP_DHCP) || (!LWIP_AUTOIP)) && LWIP_DHCP_AUTOIP_COOP)
 185:Middlewares/Third_Party/LwIP/src/core/init.c **** #error "If you want to use DHCP/AUTOIP cooperation mode, you have to define LWIP_DHCP=1 and LWIP_AU
 186:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif
 187:Middlewares/Third_Party/LwIP/src/core/init.c **** #if (((!LWIP_DHCP) || (!LWIP_ARP)) && DHCP_DOES_ARP_CHECK)
 188:Middlewares/Third_Party/LwIP/src/core/init.c **** #error "If you want to use DHCP ARP checking, you have to define LWIP_DHCP=1 and LWIP_ARP=1 in your
 189:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif
 190:Middlewares/Third_Party/LwIP/src/core/init.c **** #if (!LWIP_ARP && LWIP_AUTOIP)
 191:Middlewares/Third_Party/LwIP/src/core/init.c **** #error "If you want to use AUTOIP, you have to define LWIP_ARP=1 in your lwipopts.h"
 192:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif
 193:Middlewares/Third_Party/LwIP/src/core/init.c **** #if (LWIP_TCP && ((LWIP_EVENT_API && LWIP_CALLBACK_API) || (!LWIP_EVENT_API && !LWIP_CALLBACK_API))
 194:Middlewares/Third_Party/LwIP/src/core/init.c **** #error "One and exactly one of LWIP_EVENT_API and LWIP_CALLBACK_API has to be enabled in your lwipo
 195:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif
 196:Middlewares/Third_Party/LwIP/src/core/init.c **** #if (LWIP_ALTCP && LWIP_EVENT_API)
 197:Middlewares/Third_Party/LwIP/src/core/init.c **** #error "The application layered tcp API does not work with LWIP_EVENT_API"
 198:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif
 199:Middlewares/Third_Party/LwIP/src/core/init.c **** #if (MEM_LIBC_MALLOC && MEM_USE_POOLS)
 200:Middlewares/Third_Party/LwIP/src/core/init.c **** #error "MEM_LIBC_MALLOC and MEM_USE_POOLS may not both be simultaneously enabled in your lwipopts.h
 201:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif
 202:Middlewares/Third_Party/LwIP/src/core/init.c **** #if (MEM_USE_POOLS && !MEMP_USE_CUSTOM_POOLS)
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccAycewk.s 			page 5


 203:Middlewares/Third_Party/LwIP/src/core/init.c **** #error "MEM_USE_POOLS requires custom pools (MEMP_USE_CUSTOM_POOLS) to be enabled in your lwipopts.
 204:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif
 205:Middlewares/Third_Party/LwIP/src/core/init.c **** #if (PBUF_POOL_BUFSIZE <= MEM_ALIGNMENT)
 206:Middlewares/Third_Party/LwIP/src/core/init.c **** #error "PBUF_POOL_BUFSIZE must be greater than MEM_ALIGNMENT or the offset may take the full first 
 207:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif
 208:Middlewares/Third_Party/LwIP/src/core/init.c **** #if (DNS_LOCAL_HOSTLIST && !DNS_LOCAL_HOSTLIST_IS_DYNAMIC && !(defined(DNS_LOCAL_HOSTLIST_INIT)))
 209:Middlewares/Third_Party/LwIP/src/core/init.c **** #error "you have to define define DNS_LOCAL_HOSTLIST_INIT {{'host1', 0x123}, {'host2', 0x234}} to i
 210:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif
 211:Middlewares/Third_Party/LwIP/src/core/init.c **** #if PPP_SUPPORT && !PPPOS_SUPPORT && !PPPOE_SUPPORT && !PPPOL2TP_SUPPORT
 212:Middlewares/Third_Party/LwIP/src/core/init.c **** #error "PPP_SUPPORT needs at least one of PPPOS_SUPPORT, PPPOE_SUPPORT or PPPOL2TP_SUPPORT turned o
 213:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif
 214:Middlewares/Third_Party/LwIP/src/core/init.c **** #if PPP_SUPPORT && !PPP_IPV4_SUPPORT && !PPP_IPV6_SUPPORT
 215:Middlewares/Third_Party/LwIP/src/core/init.c **** #error "PPP_SUPPORT needs PPP_IPV4_SUPPORT and/or PPP_IPV6_SUPPORT turned on"
 216:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif
 217:Middlewares/Third_Party/LwIP/src/core/init.c **** #if PPP_SUPPORT && PPP_IPV4_SUPPORT && !LWIP_IPV4
 218:Middlewares/Third_Party/LwIP/src/core/init.c **** #error "PPP_IPV4_SUPPORT needs LWIP_IPV4 turned on"
 219:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif
 220:Middlewares/Third_Party/LwIP/src/core/init.c **** #if PPP_SUPPORT && PPP_IPV6_SUPPORT && !LWIP_IPV6
 221:Middlewares/Third_Party/LwIP/src/core/init.c **** #error "PPP_IPV6_SUPPORT needs LWIP_IPV6 turned on"
 222:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif
 223:Middlewares/Third_Party/LwIP/src/core/init.c **** #if !LWIP_ETHERNET && (LWIP_ARP || PPPOE_SUPPORT)
 224:Middlewares/Third_Party/LwIP/src/core/init.c **** #error "LWIP_ETHERNET needs to be turned on for LWIP_ARP or PPPOE_SUPPORT"
 225:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif
 226:Middlewares/Third_Party/LwIP/src/core/init.c **** #if LWIP_TCPIP_CORE_LOCKING_INPUT && !LWIP_TCPIP_CORE_LOCKING
 227:Middlewares/Third_Party/LwIP/src/core/init.c **** #error "When using LWIP_TCPIP_CORE_LOCKING_INPUT, LWIP_TCPIP_CORE_LOCKING must be enabled, too"
 228:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif
 229:Middlewares/Third_Party/LwIP/src/core/init.c **** #if LWIP_TCP && LWIP_NETIF_TX_SINGLE_PBUF && !TCP_OVERSIZE
 230:Middlewares/Third_Party/LwIP/src/core/init.c **** #error "LWIP_NETIF_TX_SINGLE_PBUF needs TCP_OVERSIZE enabled to create single-pbuf TCP packets"
 231:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif
 232:Middlewares/Third_Party/LwIP/src/core/init.c **** #if LWIP_NETCONN && LWIP_TCP
 233:Middlewares/Third_Party/LwIP/src/core/init.c **** #if NETCONN_COPY != TCP_WRITE_FLAG_COPY
 234:Middlewares/Third_Party/LwIP/src/core/init.c **** #error "NETCONN_COPY != TCP_WRITE_FLAG_COPY"
 235:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif
 236:Middlewares/Third_Party/LwIP/src/core/init.c **** #if NETCONN_MORE != TCP_WRITE_FLAG_MORE
 237:Middlewares/Third_Party/LwIP/src/core/init.c **** #error "NETCONN_MORE != TCP_WRITE_FLAG_MORE"
 238:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif
 239:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif /* LWIP_NETCONN && LWIP_TCP */
 240:Middlewares/Third_Party/LwIP/src/core/init.c **** #if LWIP_SOCKET
 241:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif /* LWIP_SOCKET */
 242:Middlewares/Third_Party/LwIP/src/core/init.c **** 
 243:Middlewares/Third_Party/LwIP/src/core/init.c **** 
 244:Middlewares/Third_Party/LwIP/src/core/init.c **** /* Compile-time checks for deprecated options.
 245:Middlewares/Third_Party/LwIP/src/core/init.c ****  */
 246:Middlewares/Third_Party/LwIP/src/core/init.c **** #ifdef MEMP_NUM_TCPIP_MSG
 247:Middlewares/Third_Party/LwIP/src/core/init.c **** #error "MEMP_NUM_TCPIP_MSG option is deprecated. Remove it from your lwipopts.h."
 248:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif
 249:Middlewares/Third_Party/LwIP/src/core/init.c **** #ifdef TCP_REXMIT_DEBUG
 250:Middlewares/Third_Party/LwIP/src/core/init.c **** #error "TCP_REXMIT_DEBUG option is deprecated. Remove it from your lwipopts.h."
 251:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif
 252:Middlewares/Third_Party/LwIP/src/core/init.c **** #ifdef RAW_STATS
 253:Middlewares/Third_Party/LwIP/src/core/init.c **** #error "RAW_STATS option is deprecated. Remove it from your lwipopts.h."
 254:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif
 255:Middlewares/Third_Party/LwIP/src/core/init.c **** #ifdef ETHARP_QUEUE_FIRST
 256:Middlewares/Third_Party/LwIP/src/core/init.c **** #error "ETHARP_QUEUE_FIRST option is deprecated. Remove it from your lwipopts.h."
 257:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif
 258:Middlewares/Third_Party/LwIP/src/core/init.c **** #ifdef ETHARP_ALWAYS_INSERT
 259:Middlewares/Third_Party/LwIP/src/core/init.c **** #error "ETHARP_ALWAYS_INSERT option is deprecated. Remove it from your lwipopts.h."
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccAycewk.s 			page 6


 260:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif
 261:Middlewares/Third_Party/LwIP/src/core/init.c **** #if !NO_SYS && LWIP_TCPIP_CORE_LOCKING && LWIP_COMPAT_MUTEX && !defined(LWIP_COMPAT_MUTEX_ALLOWED)
 262:Middlewares/Third_Party/LwIP/src/core/init.c **** #error "LWIP_COMPAT_MUTEX cannot prevent priority inversion. It is recommended to implement priorit
 263:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif
 264:Middlewares/Third_Party/LwIP/src/core/init.c **** 
 265:Middlewares/Third_Party/LwIP/src/core/init.c **** #ifndef LWIP_DISABLE_TCP_SANITY_CHECKS
 266:Middlewares/Third_Party/LwIP/src/core/init.c **** #define LWIP_DISABLE_TCP_SANITY_CHECKS  0
 267:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif
 268:Middlewares/Third_Party/LwIP/src/core/init.c **** #ifndef LWIP_DISABLE_MEMP_SANITY_CHECKS
 269:Middlewares/Third_Party/LwIP/src/core/init.c **** #define LWIP_DISABLE_MEMP_SANITY_CHECKS 0
 270:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif
 271:Middlewares/Third_Party/LwIP/src/core/init.c **** 
 272:Middlewares/Third_Party/LwIP/src/core/init.c **** /* MEMP sanity checks */
 273:Middlewares/Third_Party/LwIP/src/core/init.c **** #if MEMP_MEM_MALLOC
 274:Middlewares/Third_Party/LwIP/src/core/init.c **** #if !LWIP_DISABLE_MEMP_SANITY_CHECKS
 275:Middlewares/Third_Party/LwIP/src/core/init.c **** #if LWIP_NETCONN || LWIP_SOCKET
 276:Middlewares/Third_Party/LwIP/src/core/init.c **** #if !MEMP_NUM_NETCONN && LWIP_SOCKET
 277:Middlewares/Third_Party/LwIP/src/core/init.c **** #error "lwip_sanity_check: WARNING: MEMP_NUM_NETCONN cannot be 0 when using sockets!"
 278:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif
 279:Middlewares/Third_Party/LwIP/src/core/init.c **** #else /* MEMP_MEM_MALLOC */
 280:Middlewares/Third_Party/LwIP/src/core/init.c **** #if MEMP_NUM_NETCONN > (MEMP_NUM_TCP_PCB+MEMP_NUM_TCP_PCB_LISTEN+MEMP_NUM_UDP_PCB+MEMP_NUM_RAW_PCB)
 281:Middlewares/Third_Party/LwIP/src/core/init.c **** #error "lwip_sanity_check: WARNING: MEMP_NUM_NETCONN should be less than the sum of MEMP_NUM_{TCP,R
 282:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif
 283:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif /* LWIP_NETCONN || LWIP_SOCKET */
 284:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif /* !LWIP_DISABLE_MEMP_SANITY_CHECKS */
 285:Middlewares/Third_Party/LwIP/src/core/init.c **** #if MEM_USE_POOLS
 286:Middlewares/Third_Party/LwIP/src/core/init.c **** #error "MEMP_MEM_MALLOC and MEM_USE_POOLS cannot be enabled at the same time"
 287:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif
 288:Middlewares/Third_Party/LwIP/src/core/init.c **** #ifdef LWIP_HOOK_MEMP_AVAILABLE
 289:Middlewares/Third_Party/LwIP/src/core/init.c **** #error "LWIP_HOOK_MEMP_AVAILABLE doesn't make sense with MEMP_MEM_MALLOC"
 290:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif
 291:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif /* MEMP_MEM_MALLOC */
 292:Middlewares/Third_Party/LwIP/src/core/init.c **** 
 293:Middlewares/Third_Party/LwIP/src/core/init.c **** /* TCP sanity checks */
 294:Middlewares/Third_Party/LwIP/src/core/init.c **** #if !LWIP_DISABLE_TCP_SANITY_CHECKS
 295:Middlewares/Third_Party/LwIP/src/core/init.c **** #if LWIP_TCP
 296:Middlewares/Third_Party/LwIP/src/core/init.c **** #if !MEMP_MEM_MALLOC && (MEMP_NUM_TCP_SEG < TCP_SND_QUEUELEN)
 297:Middlewares/Third_Party/LwIP/src/core/init.c **** #error "lwip_sanity_check: WARNING: MEMP_NUM_TCP_SEG should be at least as big as TCP_SND_QUEUELEN.
 298:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif
 299:Middlewares/Third_Party/LwIP/src/core/init.c **** #if TCP_SND_BUF < (2 * TCP_MSS)
 300:Middlewares/Third_Party/LwIP/src/core/init.c **** #error "lwip_sanity_check: WARNING: TCP_SND_BUF must be at least as much as (2 * TCP_MSS) for thing
 301:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif
 302:Middlewares/Third_Party/LwIP/src/core/init.c **** #if TCP_SND_QUEUELEN < (2 * (TCP_SND_BUF / TCP_MSS))
 303:Middlewares/Third_Party/LwIP/src/core/init.c **** #error "lwip_sanity_check: WARNING: TCP_SND_QUEUELEN must be at least as much as (2 * TCP_SND_BUF/T
 304:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif
 305:Middlewares/Third_Party/LwIP/src/core/init.c **** #if TCP_SNDLOWAT >= TCP_SND_BUF
 306:Middlewares/Third_Party/LwIP/src/core/init.c **** #error "lwip_sanity_check: WARNING: TCP_SNDLOWAT must be less than TCP_SND_BUF. If you know what yo
 307:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif
 308:Middlewares/Third_Party/LwIP/src/core/init.c **** #if TCP_SNDLOWAT >= (0xFFFF - (4 * TCP_MSS))
 309:Middlewares/Third_Party/LwIP/src/core/init.c **** #error "lwip_sanity_check: WARNING: TCP_SNDLOWAT must at least be 4*MSS below u16_t overflow!"
 310:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif
 311:Middlewares/Third_Party/LwIP/src/core/init.c **** #if TCP_SNDQUEUELOWAT >= TCP_SND_QUEUELEN
 312:Middlewares/Third_Party/LwIP/src/core/init.c **** #error "lwip_sanity_check: WARNING: TCP_SNDQUEUELOWAT must be less than TCP_SND_QUEUELEN. If you kn
 313:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif
 314:Middlewares/Third_Party/LwIP/src/core/init.c **** #if !MEMP_MEM_MALLOC && PBUF_POOL_SIZE && (PBUF_POOL_BUFSIZE <= (PBUF_LINK_ENCAPSULATION_HLEN + PBU
 315:Middlewares/Third_Party/LwIP/src/core/init.c **** #error "lwip_sanity_check: WARNING: PBUF_POOL_BUFSIZE does not provide enough space for protocol he
 316:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccAycewk.s 			page 7


 317:Middlewares/Third_Party/LwIP/src/core/init.c **** #if !MEMP_MEM_MALLOC && PBUF_POOL_SIZE && (TCP_WND > (PBUF_POOL_SIZE * (PBUF_POOL_BUFSIZE - (PBUF_L
 318:Middlewares/Third_Party/LwIP/src/core/init.c **** #error "lwip_sanity_check: WARNING: TCP_WND is larger than space provided by PBUF_POOL_SIZE * (PBUF
 319:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif
 320:Middlewares/Third_Party/LwIP/src/core/init.c **** #if TCP_WND < TCP_MSS
 321:Middlewares/Third_Party/LwIP/src/core/init.c **** #error "lwip_sanity_check: WARNING: TCP_WND is smaller than MSS. If you know what you are doing, de
 322:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif
 323:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif /* LWIP_TCP */
 324:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif /* !LWIP_DISABLE_TCP_SANITY_CHECKS */
 325:Middlewares/Third_Party/LwIP/src/core/init.c **** 
 326:Middlewares/Third_Party/LwIP/src/core/init.c **** /**
 327:Middlewares/Third_Party/LwIP/src/core/init.c ****  * @ingroup lwip_nosys
 328:Middlewares/Third_Party/LwIP/src/core/init.c ****  * Initialize all modules.
 329:Middlewares/Third_Party/LwIP/src/core/init.c ****  * Use this in NO_SYS mode. Use tcpip_init() otherwise.
 330:Middlewares/Third_Party/LwIP/src/core/init.c ****  */
 331:Middlewares/Third_Party/LwIP/src/core/init.c **** void
 332:Middlewares/Third_Party/LwIP/src/core/init.c **** lwip_init(void)
 333:Middlewares/Third_Party/LwIP/src/core/init.c **** {
  28              		.loc 1 333 1 view -0
  29              		.cfi_startproc
  30              		@ args = 0, pretend = 0, frame = 0
  31              		@ frame_needed = 0, uses_anonymous_args = 0
  32 0000 08B5     		push	{r3, lr}
  33              	.LCFI0:
  34              		.cfi_def_cfa_offset 8
  35              		.cfi_offset 3, -8
  36              		.cfi_offset 14, -4
 334:Middlewares/Third_Party/LwIP/src/core/init.c **** #ifndef LWIP_SKIP_CONST_CHECK
 335:Middlewares/Third_Party/LwIP/src/core/init.c ****   int a = 0;
  37              		.loc 1 335 3 view .LVU1
  38              	.LVL0:
 336:Middlewares/Third_Party/LwIP/src/core/init.c ****   LWIP_UNUSED_ARG(a);
  39              		.loc 1 336 3 view .LVU2
 337:Middlewares/Third_Party/LwIP/src/core/init.c ****   LWIP_ASSERT("LWIP_CONST_CAST not implemented correctly. Check your lwIP port.", LWIP_CONST_CAST(v
  40              		.loc 1 337 3 view .LVU3
  41              		.loc 1 337 3 view .LVU4
  42              		.loc 1 337 3 discriminator 3 view .LVU5
  43              		.loc 1 337 3 discriminator 3 view .LVU6
 338:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif
 339:Middlewares/Third_Party/LwIP/src/core/init.c **** #ifndef LWIP_SKIP_PACKING_CHECK
 340:Middlewares/Third_Party/LwIP/src/core/init.c ****   LWIP_ASSERT("Struct packing not implemented correctly. Check your lwIP port.", sizeof(struct pack
  44              		.loc 1 340 3 view .LVU7
  45              		.loc 1 340 3 view .LVU8
  46              		.loc 1 340 3 discriminator 3 view .LVU9
  47              		.loc 1 340 3 discriminator 3 view .LVU10
 341:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif
 342:Middlewares/Third_Party/LwIP/src/core/init.c **** 
 343:Middlewares/Third_Party/LwIP/src/core/init.c ****   /* Modules initialization */
 344:Middlewares/Third_Party/LwIP/src/core/init.c ****   stats_init();
  48              		.loc 1 344 15 view .LVU11
 345:Middlewares/Third_Party/LwIP/src/core/init.c **** #if !NO_SYS
 346:Middlewares/Third_Party/LwIP/src/core/init.c ****   sys_init();
  49              		.loc 1 346 3 view .LVU12
  50 0002 FFF7FEFF 		bl	sys_init
  51              	.LVL1:
 347:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif /* !NO_SYS */
 348:Middlewares/Third_Party/LwIP/src/core/init.c ****   mem_init();
  52              		.loc 1 348 3 view .LVU13
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccAycewk.s 			page 8


  53 0006 FFF7FEFF 		bl	mem_init
  54              	.LVL2:
 349:Middlewares/Third_Party/LwIP/src/core/init.c ****   memp_init();
  55              		.loc 1 349 3 view .LVU14
  56 000a FFF7FEFF 		bl	memp_init
  57              	.LVL3:
 350:Middlewares/Third_Party/LwIP/src/core/init.c ****   pbuf_init();
  58              		.loc 1 350 14 view .LVU15
 351:Middlewares/Third_Party/LwIP/src/core/init.c ****   netif_init();
  59              		.loc 1 351 3 view .LVU16
  60 000e FFF7FEFF 		bl	netif_init
  61              	.LVL4:
 352:Middlewares/Third_Party/LwIP/src/core/init.c **** #if LWIP_IPV4
 353:Middlewares/Third_Party/LwIP/src/core/init.c ****   ip_init();
  62              		.loc 1 353 12 view .LVU17
 354:Middlewares/Third_Party/LwIP/src/core/init.c **** #if LWIP_ARP
 355:Middlewares/Third_Party/LwIP/src/core/init.c ****   etharp_init();
  63              		.loc 1 355 16 view .LVU18
 356:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif /* LWIP_ARP */
 357:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif /* LWIP_IPV4 */
 358:Middlewares/Third_Party/LwIP/src/core/init.c **** #if LWIP_RAW
 359:Middlewares/Third_Party/LwIP/src/core/init.c ****   raw_init();
 360:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif /* LWIP_RAW */
 361:Middlewares/Third_Party/LwIP/src/core/init.c **** #if LWIP_UDP
 362:Middlewares/Third_Party/LwIP/src/core/init.c ****   udp_init();
  64              		.loc 1 362 3 view .LVU19
  65 0012 FFF7FEFF 		bl	udp_init
  66              	.LVL5:
 363:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif /* LWIP_UDP */
 364:Middlewares/Third_Party/LwIP/src/core/init.c **** #if LWIP_TCP
 365:Middlewares/Third_Party/LwIP/src/core/init.c ****   tcp_init();
  67              		.loc 1 365 3 view .LVU20
  68 0016 FFF7FEFF 		bl	tcp_init
  69              	.LVL6:
 366:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif /* LWIP_TCP */
 367:Middlewares/Third_Party/LwIP/src/core/init.c **** #if LWIP_IGMP
 368:Middlewares/Third_Party/LwIP/src/core/init.c ****   igmp_init();
 369:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif /* LWIP_IGMP */
 370:Middlewares/Third_Party/LwIP/src/core/init.c **** #if LWIP_DNS
 371:Middlewares/Third_Party/LwIP/src/core/init.c ****   dns_init();
 372:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif /* LWIP_DNS */
 373:Middlewares/Third_Party/LwIP/src/core/init.c **** #if PPP_SUPPORT
 374:Middlewares/Third_Party/LwIP/src/core/init.c ****   ppp_init();
 375:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif
 376:Middlewares/Third_Party/LwIP/src/core/init.c **** 
 377:Middlewares/Third_Party/LwIP/src/core/init.c **** #if LWIP_TIMERS
 378:Middlewares/Third_Party/LwIP/src/core/init.c ****   sys_timeouts_init();
  70              		.loc 1 378 3 view .LVU21
  71 001a FFF7FEFF 		bl	sys_timeouts_init
  72              	.LVL7:
 379:Middlewares/Third_Party/LwIP/src/core/init.c **** #endif /* LWIP_TIMERS */
 380:Middlewares/Third_Party/LwIP/src/core/init.c **** }
  73              		.loc 1 380 1 is_stmt 0 view .LVU22
  74 001e 08BD     		pop	{r3, pc}
  75              		.cfi_endproc
  76              	.LFE174:
  78              		.text
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccAycewk.s 			page 9


  79              	.Letext0:
  80              		.file 2 "Middlewares/Third_Party/LwIP/src/include/lwip/timeouts.h"
  81              		.file 3 "Middlewares/Third_Party/LwIP/src/include/lwip/priv/tcp_priv.h"
  82              		.file 4 "Middlewares/Third_Party/LwIP/src/include/lwip/udp.h"
  83              		.file 5 "Middlewares/Third_Party/LwIP/src/include/lwip/netif.h"
  84              		.file 6 "Middlewares/Third_Party/LwIP/src/include/lwip/memp.h"
  85              		.file 7 "Middlewares/Third_Party/LwIP/src/include/lwip/mem.h"
  86              		.file 8 "Middlewares/Third_Party/LwIP/src/include/lwip/sys.h"
  87              		.file 9 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\../lib/gcc/arm-none-eabi/13.3.1/in
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccAycewk.s 			page 10


DEFINED SYMBOLS
                            *ABS*:00000000 init.c
C:\Users\<USER>\AppData\Local\Temp\ccAycewk.s:20     .text.lwip_init:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccAycewk.s:26     .text.lwip_init:00000000 lwip_init

UNDEFINED SYMBOLS
sys_init
mem_init
memp_init
netif_init
udp_init
tcp_init
sys_timeouts_init
