!SESSION 2025-07-26 21:33:31.638 -----------------------------------------------
eclipse.buildId=Version 1.18.1
java.version=21.0.3
java.vendor=Eclipse Adoptium
BootLoader constants: OS=win32, ARCH=x86_64, WS=win32, NL=en_IN
Command-line arguments:  -os win32 -ws win32 -arch x86_64 -data D:\Programs\stm32\micrROS_test

!ENTRY com.st.stm32cube.ide.mcu.informationcenter 4 4 2025-07-26 21:33:36.083
!MESSAGE CubeMX plugin appears to be active, Log4j initialization might be too late.

!ENTRY com.st.stm32cube.ide.mcu.informationcenter 1 1 2025-07-26 21:33:36.083
!MESSAGE Log4j2 initialized with config file D:\Programs\stm32\micrROS_test\.metadata\.log4j2.xml

!ENTRY com.st.stm32cube.ide.mcu.ide 1 1 2025-07-26 21:33:38.623
!MESSAGE Started RMI Server, listening on port 41337
!SESSION 2025-07-26 21:34:48.263 -----------------------------------------------
eclipse.buildId=Version 1.18.1
java.version=21.0.3
java.vendor=Eclipse Adoptium
BootLoader constants: OS=win32, ARCH=x86_64, WS=win32, NL=en_IN
Framework arguments:  D:\Programs\stm32\micrROS_test\.project
Command-line arguments:  -os win32 -ws win32 -arch x86_64 D:\Programs\stm32\micrROS_test\.project

!ENTRY com.st.stm32cube.ide.mcu.informationcenter 4 4 2025-07-26 21:35:02.775
!MESSAGE CubeMX plugin appears to be active, Log4j initialization might be too late.

!ENTRY com.st.stm32cube.ide.mcu.informationcenter 1 1 2025-07-26 21:35:02.776
!MESSAGE Log4j2 initialized with config file D:\Programs\stm32\micrROS_test\.metadata\.log4j2.xml

!ENTRY com.st.stm32cube.ide.mcu.ide 1 1 2025-07-26 21:35:04.874
!MESSAGE Started RMI Server, listening on port 41337

!ENTRY org.eclipse.core.jobs 2 2 2025-07-26 21:35:13.183
!MESSAGE Job found still running after platform shutdown.  Jobs should be canceled by the plugin that scheduled them during shutdown: org.eclipse.ui.internal.Workbench$39 RUNNING
	 at java.base/java.lang.Thread.sleep0(Native Method)
	 at java.base/java.lang.Thread.sleep(Thread.java:509)
	 at java.prefs/java.util.prefs.WindowsPreferences.WindowsRegCreateKeyEx1(WindowsPreferences.java:237)
	 at java.prefs/java.util.prefs.WindowsPreferences.<init>(WindowsPreferences.java:462)
	 at java.prefs/java.util.prefs.WindowsPreferences.getSystemRoot(WindowsPreferences.java:126)
	 at java.prefs/java.util.prefs.WindowsPreferencesFactory.systemRoot(WindowsPreferencesFactory.java:49)
	 at java.prefs/java.util.prefs.Preferences.systemRoot(Preferences.java:474)
	 at com.st.microxplorer.plugins.updater.util.WinRegistry.getMethod(WinRegistry.java:356)
	 at com.st.microxplorer.plugins.updater.util.WinRegistry.<clinit>(WinRegistry.java:341)
	 at com.st.microxplorer.plugins.updater.engine.MainUpdater.<init>(MainUpdater.java:239)
	 at com.st.microxplorer.plugins.updater.Updater.initPhase2(Updater.java:1162)
	 at com.st.microxplorer.plugins.updater.Updater.setUserPreferencesHandler(Updater.java:1216)
	 at com.st.stm32cube.common.mx.core.CubeMxInstance.<init>(CubeMxInstance.java:141)
	 at com.st.stm32cube.common.mx.core.CubeMxInstance.getInstance(CubeMxInstance.java:91)
	 at com.st.stm32cube.common.mx.userauth.UserAuthAdapter.<init>(UserAuthAdapter.java:25)
	 at com.st.stm32cube.common.mx.userauth.UserAuthProvider.getUserAuthAdapter(UserAuthProvider.java:16)
	 at com.st.stm32cube.common.ecosystemintegration.ui.core.MCUEcoSystemIntegrationUiHelper.getUserAuth(MCUEcoSystemIntegrationUiHelper.java:100)
	 at com.st.stm32cube.ide.mcu.userauth.UserAuthStartup.earlyStartup(UserAuthStartup.java:32)
	 at org.eclipse.ui.internal.EarlyStartupRunnable.runEarlyStartup(EarlyStartupRunnable.java:79)
	 at org.eclipse.ui.internal.EarlyStartupRunnable.run(EarlyStartupRunnable.java:55)
	 at org.eclipse.core.runtime.SafeRunner.run(SafeRunner.java:47)
	 at org.eclipse.ui.internal.Workbench$39.run(Workbench.java:2733)
	 at org.eclipse.core.internal.jobs.Worker.run(Worker.java:63)
