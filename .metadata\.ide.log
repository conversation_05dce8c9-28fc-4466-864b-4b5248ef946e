2025-07-26 21:33:36,195 [INFO] Activator:176 - 


2025-07-26 21:33:36,196 [INFO] Activator:177 - !SESSION log4j initialized
2025-07-26 21:33:38,083 [INFO] LogOutputStream:77 - [STDOUT_REDIRECT] 
2025-07-26 21:33:39,346 [INFO] ApplicationProperties:184 - Using Application install path: C:\ST\STM32CubeIDE\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.14.1.202504091515
2025-07-26 21:33:39,357 [INFO] DbMcusXml:78 - Set database path to: C:\ST\STM32CubeIDE\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.14.1.202504091515\\db\/mcu/
2025-07-26 21:33:39,357 [INFO] ApiDb:274 - Set plugin database path to: C:\ST\STM32CubeIDE\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.14.1.202504091515\\db\/plugins/boardmanager/
2025-07-26 21:33:39,358 [WARN] ApiDb:259 - Overriding images path with different value:  => C:\ST\STM32CubeIDE\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.14.1.202504091515\\db\/plugins/mcufinder/images/
2025-07-26 21:33:39,363 [INFO] ApiDb:250 - Set database path to: C:\Users\<USER>\.stmcufinder\plugins\mcufinder//mcu/
2025-07-26 21:33:39,365 [INFO] DbMcusAds:125 - Set database path to: C:\Users\<USER>\.stmcufinder\plugins\mcufinder//mcu/
2025-07-26 21:33:39,366 [INFO] CrossReferenceDbSqlite:203 - Set database path to: C:\Users\<USER>\.stmcufinder\plugins\mcufinder//mcu/cs/
2025-07-26 21:33:39,435 [INFO] RulesReader:64 - Compatibility file has been processed (302 Rules)
2025-07-26 21:33:39,482 [INFO] DbMcusXml:78 - Set database path to: C:\ST\STM32CubeIDE\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.14.1.202504091515\\db\/mcu/
2025-07-26 21:33:39,482 [INFO] ApiDb:274 - Set plugin database path to: C:\ST\STM32CubeIDE\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.14.1.202504091515\\db\/plugins/boardmanager/
2025-07-26 21:33:39,483 [INFO] ApiDb:261 - Set plugin images path to: C:\ST\STM32CubeIDE\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.14.1.202504091515\\db\/plugins/mcufinder/images/
2025-07-26 21:33:39,483 [WARN] DbFile:41 - Overriding database path with different value: C:\Users\<USER>\.stmcufinder\plugins\mcufinder/ => C:\Users\<USER>\.stmcufinder\plugins\mcufinder
2025-07-26 21:33:39,483 [INFO] ApiDb:250 - Set database path to: C:\Users\<USER>\.stmcufinder\plugins\mcufinder//mcu/
2025-07-26 21:33:39,483 [WARN] DbFile:41 - Overriding database path with different value: C:\Users\<USER>\.stmcufinder\plugins\mcufinder/ => C:\Users\<USER>\.stmcufinder\plugins\mcufinder
2025-07-26 21:33:39,483 [INFO] DbMcusAds:125 - Set database path to: C:\Users\<USER>\.stmcufinder\plugins\mcufinder//mcu/
2025-07-26 21:33:39,483 [WARN] DbFile:41 - Overriding database path with different value: C:\Users\<USER>\.stmcufinder\plugins\mcufinder/ => C:\Users\<USER>\.stmcufinder\plugins\mcufinder
2025-07-26 21:33:39,483 [WARN] DbFile:41 - Overriding database path with different value: C:\Users\<USER>\.stmcufinder\plugins\mcufinder/ => C:\Users\<USER>\.stmcufinder\plugins\mcufinder
2025-07-26 21:33:39,483 [INFO] CrossReferenceDbSqlite:203 - Set database path to: C:\Users\<USER>\.stmcufinder\plugins\mcufinder//mcu/cs/
2025-07-26 21:33:39,519 [INFO] MainPanel:272 - HeapMemory: 268435456
2025-07-26 21:33:39,569 [INFO] DbMcusXml:78 - Set database path to: C:\ST\STM32CubeIDE\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.14.1.202504091515\\db\/mcu/
2025-07-26 21:33:39,569 [INFO] ApiDb:274 - Set plugin database path to: C:\ST\STM32CubeIDE\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.14.1.202504091515\\db\/plugins/boardmanager/
2025-07-26 21:33:39,569 [INFO] ApiDb:261 - Set plugin images path to: C:\ST\STM32CubeIDE\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.14.1.202504091515\\db\/plugins/mcufinder/images/
2025-07-26 21:33:39,569 [WARN] DbFile:41 - Overriding database path with different value: C:\Users\<USER>\.stmcufinder\plugins\mcufinder/ => C:\Users\<USER>\.stmcufinder\plugins\mcufinder
2025-07-26 21:33:39,569 [INFO] ApiDb:250 - Set database path to: C:\Users\<USER>\.stmcufinder\plugins\mcufinder//mcu/
2025-07-26 21:33:39,569 [WARN] DbFile:41 - Overriding database path with different value: C:\Users\<USER>\.stmcufinder\plugins\mcufinder/ => C:\Users\<USER>\.stmcufinder\plugins\mcufinder
2025-07-26 21:33:39,569 [INFO] DbMcusAds:125 - Set database path to: C:\Users\<USER>\.stmcufinder\plugins\mcufinder//mcu/
2025-07-26 21:33:39,569 [WARN] DbFile:41 - Overriding database path with different value: C:\Users\<USER>\.stmcufinder\plugins\mcufinder/ => C:\Users\<USER>\.stmcufinder\plugins\mcufinder
2025-07-26 21:33:39,569 [WARN] DbFile:41 - Overriding database path with different value: C:\Users\<USER>\.stmcufinder\plugins\mcufinder/ => C:\Users\<USER>\.stmcufinder\plugins\mcufinder
2025-07-26 21:33:39,570 [INFO] CrossReferenceDbSqlite:203 - Set database path to: C:\Users\<USER>\.stmcufinder\plugins\mcufinder//mcu/cs/
2025-07-26 21:33:39,579 [INFO] ApplicationProperties:184 - Using Application install path: C:\ST\STM32CubeIDE\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.14.1.202504091515
2025-07-26 21:33:39,580 [INFO] PluginManage:196 - Search for loadable plugins [exclusion list=, ]
2025-07-26 21:33:39,581 [INFO] PluginManage:310 - Check plugin analytics
2025-07-26 21:33:39,715 [INFO] AnalyticsPlugin:253 - Accepted Software Licenses: 
2025-07-26 21:33:39,715 [INFO] AnalyticsPlugin:255 - Accepted CMSIS Pack Licenses: 
2025-07-26 21:33:39,715 [INFO] AnalyticsPlugin:257 - Accepted Firmware Licenses: 
2025-07-26 21:33:39,718 [INFO] PluginManage:359 - Loaded plugin analytics (category:tool,tabindex:-1)
2025-07-26 21:33:39,719 [INFO] PluginManage:310 - Check plugin cadmodel
2025-07-26 21:33:39,723 [INFO] CADModel:105 - Init CAD model plugin
2025-07-26 21:33:39,723 [INFO] PluginManage:359 - Loaded plugin cadmodel (category:power,tabindex:5)
2025-07-26 21:33:39,723 [INFO] PluginManage:310 - Check plugin clock
2025-07-26 21:33:39,732 [INFO] PluginManage:359 - Loaded plugin clock (category:base,tabindex:2)
2025-07-26 21:33:39,732 [INFO] PluginManage:310 - Check plugin ddr
2025-07-26 21:33:39,735 [INFO] PluginManage:359 - Loaded plugin ddr (category:tool,tabindex:6)
2025-07-26 21:33:39,735 [INFO] PluginManage:310 - Check plugin filemanager
2025-07-26 21:33:39,873 [INFO] PluginManage:359 - Loaded plugin filemanager (category:base,tabindex:10)
2025-07-26 21:33:39,873 [INFO] PluginManage:310 - Check plugin ipmanager
2025-07-26 21:33:39,879 [INFO] PluginManage:359 - Loaded plugin ipmanager (category:base,tabindex:5)
2025-07-26 21:33:39,879 [INFO] PluginManage:310 - Check plugin lpbam
2025-07-26 21:33:39,885 [INFO] PluginManage:359 - Loaded plugin lpbam (category:base,tabindex:0)
2025-07-26 21:33:39,885 [INFO] PluginManage:310 - Check plugin memorymap
2025-07-26 21:33:39,892 [INFO] PluginManage:359 - Loaded plugin memorymap (category:base,tabindex:4)
2025-07-26 21:33:39,892 [INFO] PluginManage:310 - Check plugin pinoutandconfiguration
2025-07-26 21:33:39,899 [INFO] PluginManage:359 - Loaded plugin pinoutandconfiguration (category:base,tabindex:1)
2025-07-26 21:33:39,899 [INFO] PluginManage:310 - Check plugin pinoutconfig
2025-07-26 21:33:39,947 [WARN] SupportedApi:132 - Cannot load RTOS API schema: s4s-elt-must-match.1: The content of 'definitions' must match (annotation?, (simpleType | complexType)?, (unique | key | keyref)*)). A problem was found starting at: attribute.
2025-07-26 21:33:40,013 [INFO] PluginManage:359 - Loaded plugin pinoutconfig (category:base,tabindex:0)
2025-07-26 21:33:40,013 [INFO] PluginManage:310 - Check plugin power
2025-07-26 21:33:40,016 [INFO] PluginManage:359 - Loaded plugin power (category:power,tabindex:4)
2025-07-26 21:33:40,017 [INFO] PluginManage:310 - Check plugin projectmanager
2025-07-26 21:33:40,024 [INFO] PluginManage:359 - Loaded plugin projectmanager (category:projectmanager,tabindex:4)
2025-07-26 21:33:40,024 [INFO] PluginManage:310 - Check plugin rif
2025-07-26 21:33:40,027 [INFO] PluginManage:359 - Loaded plugin rif (category:base,tabindex:3)
2025-07-26 21:33:40,027 [INFO] PluginManage:310 - Check plugin thirdparty
2025-07-26 21:33:40,100 [WARN] IntegrityCheckThread:84 - waiting for thirdparty lock release [integrity check]
2025-07-26 21:33:40,100 [INFO] IntegrityCheckThread:86 - entering critical section [integrity check]
2025-07-26 21:33:40,100 [INFO] PluginManage:359 - Loaded plugin thirdparty (category:base,tabindex:-1)
2025-07-26 21:33:40,100 [INFO] ThirdPartyUpdaterWithRetryManager:70 - Updater plugin not ready yet. [1/15]
2025-07-26 21:33:40,100 [INFO] PluginManage:310 - Check plugin tools
2025-07-26 21:33:40,102 [INFO] PluginManage:359 - Loaded plugin tools (category:base,tabindex:7)
2025-07-26 21:33:40,102 [INFO] PluginManage:310 - Check plugin tutovideos
2025-07-26 21:33:40,186 [INFO] PluginManage:359 - Loaded plugin tutovideos (category:base,tabindex:-1)
2025-07-26 21:33:40,186 [INFO] PluginManage:310 - Check plugin updater
2025-07-26 21:33:40,198 [INFO] PluginManage:359 - Loaded plugin updater (category:base,tabindex:12)
2025-07-26 21:33:40,198 [INFO] PluginManage:310 - Check plugin userauth
2025-07-26 21:33:40,200 [INFO] UserAuth:118 - Init User Auth plugin
2025-07-26 21:33:40,201 [INFO] PluginManage:359 - Loaded plugin userauth (category:base,tabindex:14)
2025-07-26 21:33:40,201 [INFO] PluginManage:283 - PluginManage : Loaded plugins [18]
2025-07-26 21:33:40,302 [INFO] PinOutPanel:1589 - setPackage(No Configuration,No Configuration)
2025-07-26 21:33:40,345 [INFO] CADModel:165 - CPN selected for project level
2025-07-26 21:33:40,345 [INFO] CADModel:114 - Register for checkConnection events
2025-07-26 21:33:40,355 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:33:40,355 [INFO] PluginManager:220 - loadIPPluginJar : add adc
2025-07-26 21:33:40,357 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:33:40,357 [INFO] PluginManager:220 - loadIPPluginJar : add aes
2025-07-26 21:33:40,358 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:33:40,358 [INFO] PluginManager:220 - loadIPPluginJar : add can
2025-07-26 21:33:40,359 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:33:40,359 [INFO] PluginManager:220 - loadIPPluginJar : add comp
2025-07-26 21:33:40,360 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:33:40,360 [INFO] PluginManager:220 - loadIPPluginJar : add cryp
2025-07-26 21:33:40,362 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:33:40,362 [INFO] PluginManager:220 - loadIPPluginJar : add ddr_ctrl_phy
2025-07-26 21:33:40,363 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:33:40,363 [INFO] PluginManager:220 - loadIPPluginJar : add dfsdm
2025-07-26 21:33:40,366 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:33:40,367 [INFO] PluginManager:220 - loadIPPluginJar : add dma
2025-07-26 21:33:40,369 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:33:40,369 [INFO] PluginManager:220 - loadIPPluginJar : add dma3
2025-07-26 21:33:40,370 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:33:40,370 [INFO] PluginManager:220 - loadIPPluginJar : add extmemmanager
2025-07-26 21:33:40,371 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:33:40,371 [INFO] PluginManager:220 - loadIPPluginJar : add fatfs
2025-07-26 21:33:40,372 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:33:40,374 [INFO] PluginManager:220 - loadIPPluginJar : add fmc
2025-07-26 21:33:40,378 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:33:40,378 [INFO] PluginManager:220 - loadIPPluginJar : add freertos
2025-07-26 21:33:40,378 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:33:40,378 [INFO] PluginManager:220 - loadIPPluginJar : add genericplugin
2025-07-26 21:33:40,379 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:33:40,379 [INFO] PluginManager:220 - loadIPPluginJar : add gfxmmu
2025-07-26 21:33:40,382 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:33:40,382 [INFO] PluginManager:220 - loadIPPluginJar : add gic
2025-07-26 21:33:40,383 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:33:40,383 [INFO] PluginManager:220 - loadIPPluginJar : add gpio
2025-07-26 21:33:40,384 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:33:40,384 [INFO] PluginManager:220 - loadIPPluginJar : add gtzc
2025-07-26 21:33:40,385 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:33:40,385 [INFO] PluginManager:220 - loadIPPluginJar : add hash
2025-07-26 21:33:40,387 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:33:40,387 [INFO] PluginManager:220 - loadIPPluginJar : add i2c
2025-07-26 21:33:40,388 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:33:40,388 [INFO] PluginManager:220 - loadIPPluginJar : add i2s
2025-07-26 21:33:40,389 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:33:40,389 [INFO] PluginManager:220 - loadIPPluginJar : add i3c
2025-07-26 21:33:40,391 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:33:40,391 [INFO] PluginManager:220 - loadIPPluginJar : add ipddr
2025-07-26 21:33:40,395 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:33:40,396 [INFO] PluginManager:220 - loadIPPluginJar : add linkedlist
2025-07-26 21:33:40,399 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:33:40,399 [INFO] PluginManager:220 - loadIPPluginJar : add lorawan
2025-07-26 21:33:40,400 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:33:40,400 [INFO] PluginManager:220 - loadIPPluginJar : add ltdc
2025-07-26 21:33:40,403 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:33:40,403 [INFO] PluginManager:220 - loadIPPluginJar : add mdma
2025-07-26 21:33:40,404 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:33:40,404 [INFO] PluginManager:220 - loadIPPluginJar : add nvic
2025-07-26 21:33:40,406 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:33:40,406 [INFO] PluginManager:220 - loadIPPluginJar : add opamp
2025-07-26 21:33:40,407 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:33:40,407 [INFO] PluginManager:220 - loadIPPluginJar : add openamp
2025-07-26 21:33:40,409 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:33:40,409 [INFO] PluginManager:220 - loadIPPluginJar : add pdm2pcm
2025-07-26 21:33:40,413 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:33:40,415 [INFO] PluginManager:220 - loadIPPluginJar : add plateformsettings
2025-07-26 21:33:40,416 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:33:40,416 [INFO] PluginManager:220 - loadIPPluginJar : add quadspi
2025-07-26 21:33:40,418 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:33:40,420 [INFO] PluginManager:220 - loadIPPluginJar : add radio
2025-07-26 21:33:40,422 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:33:40,422 [INFO] PluginManager:220 - loadIPPluginJar : add resmgrutility
2025-07-26 21:33:40,423 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:33:40,423 [INFO] PluginManager:220 - loadIPPluginJar : add sai
2025-07-26 21:33:40,425 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:33:40,425 [INFO] PluginManager:220 - loadIPPluginJar : add spi
2025-07-26 21:33:40,428 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:33:40,428 [INFO] PluginManager:220 - loadIPPluginJar : add stm32_wpan
2025-07-26 21:33:40,429 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:33:40,429 [INFO] PluginManager:220 - loadIPPluginJar : add tim
2025-07-26 21:33:40,430 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:33:40,430 [INFO] PluginManager:220 - loadIPPluginJar : add touchsensing
2025-07-26 21:33:40,431 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:33:40,431 [INFO] PluginManager:220 - loadIPPluginJar : add tracer_emb
2025-07-26 21:33:40,433 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:33:40,433 [INFO] PluginManager:220 - loadIPPluginJar : add ts
2025-07-26 21:33:40,433 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:33:40,433 [INFO] PluginManager:220 - loadIPPluginJar : add tsc
2025-07-26 21:33:40,434 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:33:40,434 [INFO] PluginManager:220 - loadIPPluginJar : add ucpd
2025-07-26 21:33:40,436 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:33:40,436 [INFO] PluginManager:220 - loadIPPluginJar : add usart
2025-07-26 21:33:40,438 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:33:40,438 [INFO] PluginManager:220 - loadIPPluginJar : add usbx
2025-07-26 21:33:40,538 [FATAL] Updater:351 - Updater called before beeing initialized
2025-07-26 21:33:40,554 [INFO] RulesReader:64 - Compatibility file has been processed (302 Rules)
2025-07-26 21:33:40,560 [INFO] RulesReader:64 - Compatibility file has been processed (302 Rules)
2025-07-26 21:33:40,567 [INFO] CADModel:165 - CPN selected for project level
2025-07-26 21:33:40,567 [INFO] CADModel:114 - Register for checkConnection events
2025-07-26 21:33:40,567 [FATAL] Updater:351 - Updater called before beeing initialized
2025-07-26 21:33:40,567 [ERROR] CADModel:125 - Updater not yet initialized, retry later 
2025-07-26 21:33:40,668 [FATAL] Updater:351 - Updater called before beeing initialized
2025-07-26 21:33:40,669 [INFO] CADModel:165 - CPN selected for project level
2025-07-26 21:33:40,669 [INFO] CADModel:114 - Register for checkConnection events
2025-07-26 21:33:40,669 [FATAL] Updater:351 - Updater called before beeing initialized
2025-07-26 21:33:40,669 [ERROR] CADModel:125 - Updater not yet initialized, retry later 
2025-07-26 21:33:40,671 [FATAL] Updater:351 - Updater called before beeing initialized
2025-07-26 21:33:40,736 [FATAL] Updater:351 - Updater called before beeing initialized
2025-07-26 21:33:40,745 [INFO] DbMcusAds:53 - JSON generation date=Tue Jul 08 13:44:23 IST 2025 (1751962463562)
2025-07-26 21:33:40,745 [FATAL] Updater:351 - Updater called before beeing initialized
2025-07-26 21:33:40,766 [WARN] DetailPanel:346 - Failed to get advertising image, set to default
2025-07-26 21:33:40,890 [FATAL] Updater:351 - Updater called before beeing initialized
2025-07-26 21:33:40,892 [FATAL] Updater:351 - Updater called before beeing initialized
2025-07-26 21:33:40,892 [FATAL] Updater:351 - Updater called before beeing initialized
2025-07-26 21:33:40,892 [WARN] DetailPanel:346 - Failed to get advertising image, set to default
2025-07-26 21:33:40,892 [FATAL] Updater:351 - Updater called before beeing initialized
2025-07-26 21:33:40,915 [ERROR] Updater:1198 - MainUpdater not yet initialized. External WinMGr cannot be set.
2025-07-26 21:33:40,916 [INFO] Updater:1134 - Updater Version found : 6.14.1
2025-07-26 21:33:40,924 [INFO] ApplicationProperties:184 - Using Application install path: C:\ST\STM32CubeIDE\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.14.1.202504091515
2025-07-26 21:33:42,492 [ERROR] LogOutputStream:75 - [STDERR_REDIRECT] Jul 26, 2025 9:33:42 PM java.util.prefs.WindowsPreferences <init>
2025-07-26 21:33:42,493 [ERROR] LogOutputStream:75 - [STDERR_REDIRECT] WARNING: Could not open/create prefs root node Software\JavaSoft\Prefs at root 0xffffffff80000002. Windows RegCreateKeyEx(...) returned error code 5.
2025-07-26 21:33:42,495 [ERROR] LogOutputStream:75 - [STDERR_REDIRECT] 
2025-07-26 21:33:42,841 [INFO] MainUpdater:2872 - connection check result : 10
2025-07-26 21:33:42,842 [INFO] MainUpdater:289 - Updater Check For Update Now.
2025-07-26 21:33:42,842 [INFO] MicroXplorer:498 - Change Database Version : DB.6.0.141
2025-07-26 21:33:42,854 [INFO] McuFinderGlobals:63 - Set McuFinder mode to 2 (CubeIDE integrated)
2025-07-26 21:33:42,855 [INFO] UserAuth:484 - Internet connection configuration mode: 1
2025-07-26 21:33:42,884 [INFO] JxBrowserEngine:152 - Initiate JxBrowser Engine with user profile folder
2025-07-26 21:33:43,002 [INFO] CheckServerUpdateThread:120 - End of CheckServer Thread
2025-07-26 21:33:43,262 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.FP-SNS-MOTENVWB1.1.4.0
2025-07-26 21:33:43,271 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.FP-ATR-ASTRA1.2.0.2
2025-07-26 21:33:43,274 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.FP-ATR-ASTRA1.2.0.1
2025-07-26 21:33:43,291 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-SMBUS.2.1.0
2025-07-26 21:33:43,299 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-WB05N.1.1.0
2025-07-26 21:33:43,303 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-ST60.1.0.0
2025-07-26 21:33:43,332 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-AZRTOS-F7.1.1.0
2025-07-26 21:33:43,363 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-MEMS1.11.0.0
2025-07-26 21:33:43,441 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.FP-SNS-FLIGHT1.5.0.2
2025-07-26 21:33:43,452 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-AZRTOS-H7.3.4.0
2025-07-26 21:33:43,458 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-DISPLAY.3.0.0
2025-07-26 21:33:43,462 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-BLE1.7.0.0
2025-07-26 21:33:43,465 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-NFC10.1.0.0
2025-07-26 21:33:43,471 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-BLEMGR.4.0.0
2025-07-26 21:33:43,476 [WARN] PackLoader:240 - Cannot read IP mode file for emotas.I-CUBE-CANOPEN.1.3.0
2025-07-26 21:33:43,479 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-BLEMGR.3.1.0
2025-07-26 21:33:43,482 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-AI.10.0.0
2025-07-26 21:33:43,485 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.FP-SNS-SMARTAG2.1.2.0
2025-07-26 21:33:43,487 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.FP-SNS-FLIGHT1.5.1.0
2025-07-26 21:33:43,507 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-MEMS1.11.1.0
2025-07-26 21:33:43,582 [INFO] LogOutputStream:77 - [STDOUT_REDIRECT] 1 : Invalid condition id : UX_CORESTACK_Condition cause : null
2025-07-26 21:33:43,605 [INFO] LogOutputStream:77 - [STDOUT_REDIRECT] 1 : Invalid condition id : UX_CORESTACK_Condition cause : null
2025-07-26 21:33:43,611 [INFO] LogOutputStream:77 - [STDOUT_REDIRECT] 1 : Invalid condition id : UX_CORESTACK_Condition cause : null
2025-07-26 21:33:43,614 [INFO] LogOutputStream:77 - [STDOUT_REDIRECT] 1 : Invalid condition id : UX_CORESTACK_Condition cause : null
2025-07-26 21:33:43,615 [INFO] LogOutputStream:77 - [STDOUT_REDIRECT] 1 : Invalid condition id : UX_CORESTACK_Condition cause : null
2025-07-26 21:33:43,617 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-AZRTOS-WL.2.0.0
2025-07-26 21:33:43,622 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.FP-SNS-MOTENV1.5.0.0
2025-07-26 21:33:43,625 [WARN] PackLoader:240 - Cannot read IP mode file for wolfSSL.I-CUBE-wolfMQTT.1.19.0
2025-07-26 21:33:43,632 [WARN] PackLoader:240 - Cannot read IP mode file for WES.I-CUBE-Cesium.1.3.0
2025-07-26 21:33:43,636 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-BLE2.3.3.0
2025-07-26 21:33:43,639 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-NFC9.1.0.0
2025-07-26 21:33:43,641 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-BLE1.7.1.0
2025-07-26 21:33:43,645 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-TCPP.4.1.0
2025-07-26 21:33:43,648 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-EEPRMA1.5.2.0
2025-07-26 21:33:43,650 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-AI.10.1.0
2025-07-26 21:33:43,668 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-MEMS1.11.2.0
2025-07-26 21:33:43,722 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-AZRTOS-G0.1.1.0
2025-07-26 21:33:43,725 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-SAFEA1.1.2.2
2025-07-26 21:33:43,728 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-NFC4.3.0.0
2025-07-26 21:33:43,733 [WARN] PackLoader:240 - Cannot read IP mode file for EmbeddedOffice.I-CUBE-FS-RTOS.1.0.1
2025-07-26 21:33:43,736 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-WB05N.2.0.0
2025-07-26 21:33:43,738 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-TCPP.4.2.0
2025-07-26 21:33:43,741 [WARN] PackLoader:240 - Cannot read IP mode file for RealThread.X-CUBE-RT-Thread_Nano.4.1.1
2025-07-26 21:33:43,743 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.FP-ATR-SIGFOX1.3.2.0
2025-07-26 21:33:43,746 [WARN] ConditionMgr:438 - getConditionDescription Invalid condition id : Cortex-A Device cause : null
2025-07-26 21:33:43,757 [WARN] ConditionMgr:1044 - genDependencies : Invalid condition id : Cortex-A Device cause : null
2025-07-26 21:33:43,757 [WARN] ConditionMgr:1044 - genDependencies : Invalid condition id : Cortex-A Device cause : null
2025-07-26 21:33:43,757 [WARN] ConditionMgr:1044 - genDependencies : Invalid condition id : Cortex-A Device cause : null
2025-07-26 21:33:43,760 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-EEPRMA1.5.1.0
2025-07-26 21:33:43,762 [WARN] PackLoader:240 - Cannot read IP mode file for wolfSSL.I-CUBE-wolfSSL.5.7.4
2025-07-26 21:33:43,765 [WARN] PackLoader:240 - Cannot read IP mode file for ITTIA_DB.I-CUBE-ITTIADB.8.9.0
2025-07-26 21:33:43,771 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-ST67W61.1.0.0
2025-07-26 21:33:43,783 [WARN] PackLoader:240 - Cannot read IP mode file for SEGGER.I-CUBE-embOS.1.3.1
2025-07-26 21:33:43,797 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-ALGOBUILD.1.4.0
2025-07-26 21:33:43,819 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-MEMS1.11.3.0
2025-07-26 21:33:43,866 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-AI.9.1.0
2025-07-26 21:33:43,869 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-PM33A1.1.0.0
2025-07-26 21:33:43,876 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-AZRTOS-F4.1.1.0
2025-07-26 21:33:43,879 [WARN] PackLoader:240 - Cannot read IP mode file for Avnet-IotConnect.X-CUBE-IoTC-DA16k-PMOD.1.0.0
2025-07-26 21:33:43,881 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-ISPU.2.1.0
2025-07-26 21:33:43,884 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-FREERTOS.1.2.0
2025-07-26 21:33:43,888 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-NFC12.1.0.0
2025-07-26 21:33:43,895 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-AZRTOS-L5.2.0.0
2025-07-26 21:33:43,899 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-NFC6.3.1.0
2025-07-26 21:33:43,901 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-DPower.1.2.0
2025-07-26 21:33:43,903 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-FREERTOS.1.3.1
2025-07-26 21:33:43,905 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-FREERTOS.1.3.0
2025-07-26 21:33:43,907 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-NFC7.1.0.1
2025-07-26 21:33:43,915 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.FP-SNS-STAIOTCFT.1.0.0
2025-07-26 21:33:43,920 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-DPower.1.3.0
2025-07-26 21:33:43,943 [WARN] ConditionMgr:438 - getConditionDescription Invalid condition id : LAN8742 Phy interface Condition cause : null
2025-07-26 21:33:43,944 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-AZRTOS-L4.2.0.0
2025-07-26 21:33:43,944 [WARN] ConditionMgr:1044 - genDependencies : Invalid condition id : LAN8742 Phy interface Condition cause : null
2025-07-26 21:33:43,944 [WARN] ConditionMgr:1044 - genDependencies : Invalid condition id : LAN8742 Phy interface Condition cause : null
2025-07-26 21:33:43,944 [WARN] ConditionMgr:1044 - genDependencies : Invalid condition id : LAN8742 Phy interface Condition cause : null
2025-07-26 21:33:43,957 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-SFXS2LP1.4.0.0
2025-07-26 21:33:43,966 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-AZRTOS-H7.3.3.0
2025-07-26 21:33:43,973 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-BLEMGR.4.1.0
2025-07-26 21:33:43,979 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-IPS.3.0.0
2025-07-26 21:33:43,985 [WARN] ConditionMgr:438 - getConditionDescription Invalid condition id : UX DEVICE CLASS RTOS Condition cause : null
2025-07-26 21:33:43,986 [WARN] ConditionMgr:438 - getConditionDescription Invalid condition id : UX DEVICE CLASS RTOS Condition cause : null
2025-07-26 21:33:43,986 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-AZRTOS-WB.2.0.0
2025-07-26 21:33:43,986 [WARN] ConditionMgr:1044 - genDependencies : Invalid condition id : UX DEVICE CLASS RTOS Condition cause : null
2025-07-26 21:33:43,987 [WARN] ConditionMgr:1044 - genDependencies : Invalid condition id : UX DEVICE CLASS RTOS Condition cause : null
2025-07-26 21:33:43,987 [WARN] ConditionMgr:1044 - genDependencies : Invalid condition id : UX DEVICE CLASS RTOS Condition cause : null
2025-07-26 21:33:43,987 [WARN] ConditionMgr:1044 - genDependencies : Invalid condition id : UX DEVICE CLASS RTOS Condition cause : null
2025-07-26 21:33:43,987 [WARN] ConditionMgr:1044 - genDependencies : Invalid condition id : UX DEVICE CLASS RTOS Condition cause : null
2025-07-26 21:33:43,990 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-GNSS1.7.0.0
2025-07-26 21:33:43,992 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-GNSS1.7.0.1
2025-07-26 21:33:43,997 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-TOUCHGFX.4.25.0
2025-07-26 21:33:44,000 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.FP-SNS-STBOX1.2.0.0
2025-07-26 21:33:44,005 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-SUBG2.5.0.0
2025-07-26 21:33:44,014 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-AZRTOS-H7RS.1.1.0
2025-07-26 21:33:44,018 [WARN] PackLoader:240 - Cannot read IP mode file for Cesanta.I-CUBE-Mongoose.7.13.0
2025-07-26 21:33:44,023 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-AZRTOS-G4.2.0.0
2025-07-26 21:33:44,026 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-NFC7.2.0.0
2025-07-26 21:33:44,031 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-IPS.3.1.0
2025-07-26 21:33:44,035 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-ALS.1.0.2
2025-07-26 21:33:44,037 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-TOUCHGFX.4.24.2
2025-07-26 21:33:44,040 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-TOUCHGFX.4.24.1
2025-07-26 21:33:44,042 [WARN] PackLoader:240 - Cannot read IP mode file for wolfSSL.I-CUBE-wolfTPM.3.4.0
2025-07-26 21:33:44,044 [WARN] PackLoader:240 - Cannot read IP mode file for portGmbH.I-Cube-SoM-uGOAL.1.1.0
2025-07-26 21:33:44,050 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-AZRTOS-H7RS.1.0.0
2025-07-26 21:33:44,056 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-TOF1.3.4.2
2025-07-26 21:33:44,071 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-TOF1.3.4.3
2025-07-26 21:33:44,084 [WARN] PackLoader:240 - Cannot read IP mode file for Infineon.AIROC-Wi-Fi-Bluetooth-STM32.1.6.1
2025-07-26 21:33:44,088 [WARN] PackLoader:240 - Cannot read IP mode file for wolfSSL.I-CUBE-wolfSSH.1.4.17
2025-07-26 21:33:44,088 [INFO] ThirdParty:978 - Integrity check success = true
2025-07-26 21:33:44,088 [INFO] IntegrityCheckThread:100 - exiting critical section [integrity check]
2025-07-26 21:33:44,088 [INFO] IntegrityCheckThread:103 - End integrity checks thread
2025-07-26 21:33:44,277 [INFO] WebApp:169 - Instantiating new browser for Auth
2025-07-26 21:33:44,771 [INFO] WebApp:463 - Apply proxy settings
2025-07-26 21:33:44,771 [INFO] WebApp:548 - Chromium requires no authentication
2025-07-26 21:33:44,775 [INFO] WebApp:491 - Direct internet connection detected
2025-07-26 21:33:44,787 [INFO] WebApp:894 - Register for checkConnection events
2025-07-26 21:33:44,787 [INFO] WebApp:463 - Apply proxy settings
2025-07-26 21:33:44,787 [INFO] WebApp:548 - Chromium requires no authentication
2025-07-26 21:33:44,787 [INFO] WebApp:491 - Direct internet connection detected
2025-07-26 21:33:44,866 [INFO] WebApp:225 - Starting web application
2025-07-26 21:33:44,866 [INFO] WebApp:593 - Web application path used C:\ST\STM32CubeIDE\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.14.1.202504091515\db\plugins\mcufinder\reactClient1\index.html
2025-07-26 21:33:44,915 [INFO] UserAuth:484 - Internet connection configuration mode: 1
2025-07-26 21:33:45,054 [INFO] WebApp:191 - Connection restablished
2025-07-26 21:34:44,574 [ERROR] LogOutputStream:75 - [STDERR_REDIRECT] 
2025-07-26 21:35:02,975 [INFO] Activator:176 - 


2025-07-26 21:35:02,976 [INFO] Activator:177 - !SESSION log4j initialized
2025-07-26 21:35:04,585 [INFO] LogOutputStream:77 - [STDOUT_REDIRECT] 
2025-07-26 21:35:10,587 [INFO] ApplicationProperties:184 - Using Application install path: C:\ST\STM32CubeIDE\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.14.1.202504091515
2025-07-26 21:35:10,597 [INFO] DbMcusXml:78 - Set database path to: C:\ST\STM32CubeIDE\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.14.1.202504091515\\db\/mcu/
2025-07-26 21:35:10,597 [INFO] ApiDb:274 - Set plugin database path to: C:\ST\STM32CubeIDE\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.14.1.202504091515\\db\/plugins/boardmanager/
2025-07-26 21:35:10,597 [WARN] ApiDb:259 - Overriding images path with different value:  => C:\ST\STM32CubeIDE\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.14.1.202504091515\\db\/plugins/mcufinder/images/
2025-07-26 21:35:10,601 [INFO] ApiDb:250 - Set database path to: C:\Users\<USER>\.stmcufinder\plugins\mcufinder//mcu/
2025-07-26 21:35:10,602 [INFO] DbMcusAds:125 - Set database path to: C:\Users\<USER>\.stmcufinder\plugins\mcufinder//mcu/
2025-07-26 21:35:10,603 [INFO] CrossReferenceDbSqlite:203 - Set database path to: C:\Users\<USER>\.stmcufinder\plugins\mcufinder//mcu/cs/
2025-07-26 21:35:10,676 [INFO] RulesReader:64 - Compatibility file has been processed (302 Rules)
2025-07-26 21:35:10,731 [INFO] DbMcusXml:78 - Set database path to: C:\ST\STM32CubeIDE\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.14.1.202504091515\\db\/mcu/
2025-07-26 21:35:10,731 [INFO] ApiDb:274 - Set plugin database path to: C:\ST\STM32CubeIDE\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.14.1.202504091515\\db\/plugins/boardmanager/
2025-07-26 21:35:10,732 [INFO] ApiDb:261 - Set plugin images path to: C:\ST\STM32CubeIDE\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.14.1.202504091515\\db\/plugins/mcufinder/images/
2025-07-26 21:35:10,732 [WARN] DbFile:41 - Overriding database path with different value: C:\Users\<USER>\.stmcufinder\plugins\mcufinder/ => C:\Users\<USER>\.stmcufinder\plugins\mcufinder
2025-07-26 21:35:10,732 [INFO] ApiDb:250 - Set database path to: C:\Users\<USER>\.stmcufinder\plugins\mcufinder//mcu/
2025-07-26 21:35:10,732 [WARN] DbFile:41 - Overriding database path with different value: C:\Users\<USER>\.stmcufinder\plugins\mcufinder/ => C:\Users\<USER>\.stmcufinder\plugins\mcufinder
2025-07-26 21:35:10,732 [INFO] DbMcusAds:125 - Set database path to: C:\Users\<USER>\.stmcufinder\plugins\mcufinder//mcu/
2025-07-26 21:35:10,732 [WARN] DbFile:41 - Overriding database path with different value: C:\Users\<USER>\.stmcufinder\plugins\mcufinder/ => C:\Users\<USER>\.stmcufinder\plugins\mcufinder
2025-07-26 21:35:10,732 [WARN] DbFile:41 - Overriding database path with different value: C:\Users\<USER>\.stmcufinder\plugins\mcufinder/ => C:\Users\<USER>\.stmcufinder\plugins\mcufinder
2025-07-26 21:35:10,732 [INFO] CrossReferenceDbSqlite:203 - Set database path to: C:\Users\<USER>\.stmcufinder\plugins\mcufinder//mcu/cs/
2025-07-26 21:35:10,774 [INFO] MainPanel:272 - HeapMemory: 268435456
2025-07-26 21:35:10,846 [INFO] DbMcusXml:78 - Set database path to: C:\ST\STM32CubeIDE\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.14.1.202504091515\\db\/mcu/
2025-07-26 21:35:10,846 [INFO] ApiDb:274 - Set plugin database path to: C:\ST\STM32CubeIDE\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.14.1.202504091515\\db\/plugins/boardmanager/
2025-07-26 21:35:10,846 [INFO] ApiDb:261 - Set plugin images path to: C:\ST\STM32CubeIDE\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.14.1.202504091515\\db\/plugins/mcufinder/images/
2025-07-26 21:35:10,846 [WARN] DbFile:41 - Overriding database path with different value: C:\Users\<USER>\.stmcufinder\plugins\mcufinder/ => C:\Users\<USER>\.stmcufinder\plugins\mcufinder
2025-07-26 21:35:10,846 [INFO] ApiDb:250 - Set database path to: C:\Users\<USER>\.stmcufinder\plugins\mcufinder//mcu/
2025-07-26 21:35:10,846 [WARN] DbFile:41 - Overriding database path with different value: C:\Users\<USER>\.stmcufinder\plugins\mcufinder/ => C:\Users\<USER>\.stmcufinder\plugins\mcufinder
2025-07-26 21:35:10,846 [INFO] DbMcusAds:125 - Set database path to: C:\Users\<USER>\.stmcufinder\plugins\mcufinder//mcu/
2025-07-26 21:35:10,846 [WARN] DbFile:41 - Overriding database path with different value: C:\Users\<USER>\.stmcufinder\plugins\mcufinder/ => C:\Users\<USER>\.stmcufinder\plugins\mcufinder
2025-07-26 21:35:10,846 [WARN] DbFile:41 - Overriding database path with different value: C:\Users\<USER>\.stmcufinder\plugins\mcufinder/ => C:\Users\<USER>\.stmcufinder\plugins\mcufinder
2025-07-26 21:35:10,846 [INFO] CrossReferenceDbSqlite:203 - Set database path to: C:\Users\<USER>\.stmcufinder\plugins\mcufinder//mcu/cs/
2025-07-26 21:35:10,855 [INFO] ApplicationProperties:184 - Using Application install path: C:\ST\STM32CubeIDE\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.14.1.202504091515
2025-07-26 21:35:10,856 [INFO] PluginManage:196 - Search for loadable plugins [exclusion list=, ]
2025-07-26 21:35:10,857 [INFO] PluginManage:310 - Check plugin analytics
2025-07-26 21:35:11,011 [INFO] AnalyticsPlugin:253 - Accepted Software Licenses: 
2025-07-26 21:35:11,011 [INFO] AnalyticsPlugin:255 - Accepted CMSIS Pack Licenses: 
2025-07-26 21:35:11,011 [INFO] AnalyticsPlugin:257 - Accepted Firmware Licenses: 
2025-07-26 21:35:11,014 [INFO] PluginManage:359 - Loaded plugin analytics (category:tool,tabindex:-1)
2025-07-26 21:35:11,015 [INFO] PluginManage:310 - Check plugin cadmodel
2025-07-26 21:35:11,020 [INFO] CADModel:105 - Init CAD model plugin
2025-07-26 21:35:11,020 [INFO] PluginManage:359 - Loaded plugin cadmodel (category:power,tabindex:5)
2025-07-26 21:35:11,020 [INFO] PluginManage:310 - Check plugin clock
2025-07-26 21:35:11,028 [INFO] PluginManage:359 - Loaded plugin clock (category:base,tabindex:2)
2025-07-26 21:35:11,029 [INFO] PluginManage:310 - Check plugin ddr
2025-07-26 21:35:11,030 [INFO] PluginManage:359 - Loaded plugin ddr (category:tool,tabindex:6)
2025-07-26 21:35:11,031 [INFO] PluginManage:310 - Check plugin filemanager
2025-07-26 21:35:11,145 [INFO] PluginManage:359 - Loaded plugin filemanager (category:base,tabindex:10)
2025-07-26 21:35:11,146 [INFO] PluginManage:310 - Check plugin ipmanager
2025-07-26 21:35:11,150 [INFO] PluginManage:359 - Loaded plugin ipmanager (category:base,tabindex:5)
2025-07-26 21:35:11,150 [INFO] PluginManage:310 - Check plugin lpbam
2025-07-26 21:35:11,155 [INFO] PluginManage:359 - Loaded plugin lpbam (category:base,tabindex:0)
2025-07-26 21:35:11,155 [INFO] PluginManage:310 - Check plugin memorymap
2025-07-26 21:35:11,162 [INFO] PluginManage:359 - Loaded plugin memorymap (category:base,tabindex:4)
2025-07-26 21:35:11,162 [INFO] PluginManage:310 - Check plugin pinoutandconfiguration
2025-07-26 21:35:11,166 [INFO] PluginManage:359 - Loaded plugin pinoutandconfiguration (category:base,tabindex:1)
2025-07-26 21:35:11,167 [INFO] PluginManage:310 - Check plugin pinoutconfig
2025-07-26 21:35:11,248 [WARN] SupportedApi:132 - Cannot load RTOS API schema: s4s-elt-must-match.1: The content of 'definitions' must match (annotation?, (simpleType | complexType)?, (unique | key | keyref)*)). A problem was found starting at: attribute.
2025-07-26 21:35:11,351 [INFO] PluginManage:359 - Loaded plugin pinoutconfig (category:base,tabindex:0)
2025-07-26 21:35:11,352 [INFO] PluginManage:310 - Check plugin power
2025-07-26 21:35:11,356 [INFO] PluginManage:359 - Loaded plugin power (category:power,tabindex:4)
2025-07-26 21:35:11,356 [INFO] PluginManage:310 - Check plugin projectmanager
2025-07-26 21:35:11,365 [INFO] PluginManage:359 - Loaded plugin projectmanager (category:projectmanager,tabindex:4)
2025-07-26 21:35:11,365 [INFO] PluginManage:310 - Check plugin rif
2025-07-26 21:35:11,369 [INFO] PluginManage:359 - Loaded plugin rif (category:base,tabindex:3)
2025-07-26 21:35:11,369 [INFO] PluginManage:310 - Check plugin thirdparty
2025-07-26 21:35:11,453 [WARN] IntegrityCheckThread:84 - waiting for thirdparty lock release [integrity check]
2025-07-26 21:35:11,453 [INFO] IntegrityCheckThread:86 - entering critical section [integrity check]
2025-07-26 21:35:11,454 [INFO] PluginManage:359 - Loaded plugin thirdparty (category:base,tabindex:-1)
2025-07-26 21:35:11,454 [INFO] ThirdPartyUpdaterWithRetryManager:70 - Updater plugin not ready yet. [1/15]
2025-07-26 21:35:11,454 [INFO] PluginManage:310 - Check plugin tools
2025-07-26 21:35:11,457 [INFO] PluginManage:359 - Loaded plugin tools (category:base,tabindex:7)
2025-07-26 21:35:11,457 [INFO] PluginManage:310 - Check plugin tutovideos
2025-07-26 21:35:11,541 [INFO] PluginManage:359 - Loaded plugin tutovideos (category:base,tabindex:-1)
2025-07-26 21:35:11,541 [INFO] PluginManage:310 - Check plugin updater
2025-07-26 21:35:11,555 [INFO] PluginManage:359 - Loaded plugin updater (category:base,tabindex:12)
2025-07-26 21:35:11,555 [INFO] PluginManage:310 - Check plugin userauth
2025-07-26 21:35:11,559 [INFO] UserAuth:118 - Init User Auth plugin
2025-07-26 21:35:11,559 [INFO] PluginManage:359 - Loaded plugin userauth (category:base,tabindex:14)
2025-07-26 21:35:11,560 [INFO] PluginManage:283 - PluginManage : Loaded plugins [18]
2025-07-26 21:35:11,681 [INFO] PinOutPanel:1589 - setPackage(No Configuration,No Configuration)
2025-07-26 21:35:11,730 [INFO] CADModel:165 - CPN selected for project level
2025-07-26 21:35:11,730 [INFO] CADModel:114 - Register for checkConnection events
2025-07-26 21:35:11,739 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:35:11,739 [INFO] PluginManager:220 - loadIPPluginJar : add adc
2025-07-26 21:35:11,741 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:35:11,741 [INFO] PluginManager:220 - loadIPPluginJar : add aes
2025-07-26 21:35:11,742 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:35:11,742 [INFO] PluginManager:220 - loadIPPluginJar : add can
2025-07-26 21:35:11,743 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:35:11,743 [INFO] PluginManager:220 - loadIPPluginJar : add comp
2025-07-26 21:35:11,744 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:35:11,744 [INFO] PluginManager:220 - loadIPPluginJar : add cryp
2025-07-26 21:35:11,746 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:35:11,746 [INFO] PluginManager:220 - loadIPPluginJar : add ddr_ctrl_phy
2025-07-26 21:35:11,747 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:35:11,747 [INFO] PluginManager:220 - loadIPPluginJar : add dfsdm
2025-07-26 21:35:11,749 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:35:11,750 [INFO] PluginManager:220 - loadIPPluginJar : add dma
2025-07-26 21:35:11,751 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:35:11,751 [INFO] PluginManager:220 - loadIPPluginJar : add dma3
2025-07-26 21:35:11,752 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:35:11,752 [INFO] PluginManager:220 - loadIPPluginJar : add extmemmanager
2025-07-26 21:35:11,753 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:35:11,753 [INFO] PluginManager:220 - loadIPPluginJar : add fatfs
2025-07-26 21:35:11,755 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:35:11,755 [INFO] PluginManager:220 - loadIPPluginJar : add fmc
2025-07-26 21:35:11,760 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:35:11,760 [INFO] PluginManager:220 - loadIPPluginJar : add freertos
2025-07-26 21:35:11,760 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:35:11,760 [INFO] PluginManager:220 - loadIPPluginJar : add genericplugin
2025-07-26 21:35:11,761 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:35:11,761 [INFO] PluginManager:220 - loadIPPluginJar : add gfxmmu
2025-07-26 21:35:11,763 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:35:11,763 [INFO] PluginManager:220 - loadIPPluginJar : add gic
2025-07-26 21:35:11,765 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:35:11,765 [INFO] PluginManager:220 - loadIPPluginJar : add gpio
2025-07-26 21:35:11,766 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:35:11,766 [INFO] PluginManager:220 - loadIPPluginJar : add gtzc
2025-07-26 21:35:11,767 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:35:11,767 [INFO] PluginManager:220 - loadIPPluginJar : add hash
2025-07-26 21:35:11,768 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:35:11,768 [INFO] PluginManager:220 - loadIPPluginJar : add i2c
2025-07-26 21:35:11,769 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:35:11,769 [INFO] PluginManager:220 - loadIPPluginJar : add i2s
2025-07-26 21:35:11,772 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:35:11,772 [INFO] PluginManager:220 - loadIPPluginJar : add i3c
2025-07-26 21:35:11,775 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:35:11,775 [INFO] PluginManager:220 - loadIPPluginJar : add ipddr
2025-07-26 21:35:11,778 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:35:11,778 [INFO] PluginManager:220 - loadIPPluginJar : add linkedlist
2025-07-26 21:35:11,780 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:35:11,780 [INFO] PluginManager:220 - loadIPPluginJar : add lorawan
2025-07-26 21:35:11,781 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:35:11,781 [INFO] PluginManager:220 - loadIPPluginJar : add ltdc
2025-07-26 21:35:11,783 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:35:11,783 [INFO] PluginManager:220 - loadIPPluginJar : add mdma
2025-07-26 21:35:11,785 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:35:11,785 [INFO] PluginManager:220 - loadIPPluginJar : add nvic
2025-07-26 21:35:11,786 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:35:11,786 [INFO] PluginManager:220 - loadIPPluginJar : add opamp
2025-07-26 21:35:11,787 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:35:11,788 [INFO] PluginManager:220 - loadIPPluginJar : add openamp
2025-07-26 21:35:11,789 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:35:11,789 [INFO] PluginManager:220 - loadIPPluginJar : add pdm2pcm
2025-07-26 21:35:11,791 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:35:11,793 [INFO] PluginManager:220 - loadIPPluginJar : add plateformsettings
2025-07-26 21:35:11,793 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:35:11,793 [INFO] PluginManager:220 - loadIPPluginJar : add quadspi
2025-07-26 21:35:11,796 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:35:11,797 [INFO] PluginManager:220 - loadIPPluginJar : add radio
2025-07-26 21:35:11,798 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:35:11,798 [INFO] PluginManager:220 - loadIPPluginJar : add resmgrutility
2025-07-26 21:35:11,800 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:35:11,800 [INFO] PluginManager:220 - loadIPPluginJar : add sai
2025-07-26 21:35:11,801 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:35:11,801 [INFO] PluginManager:220 - loadIPPluginJar : add spi
2025-07-26 21:35:11,803 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:35:11,803 [INFO] PluginManager:220 - loadIPPluginJar : add stm32_wpan
2025-07-26 21:35:11,804 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:35:11,804 [INFO] PluginManager:220 - loadIPPluginJar : add tim
2025-07-26 21:35:11,805 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:35:11,805 [INFO] PluginManager:220 - loadIPPluginJar : add touchsensing
2025-07-26 21:35:11,806 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:35:11,806 [INFO] PluginManager:220 - loadIPPluginJar : add tracer_emb
2025-07-26 21:35:11,807 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:35:11,807 [INFO] PluginManager:220 - loadIPPluginJar : add ts
2025-07-26 21:35:11,808 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:35:11,808 [INFO] PluginManager:220 - loadIPPluginJar : add tsc
2025-07-26 21:35:11,809 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:35:11,809 [INFO] PluginManager:220 - loadIPPluginJar : add ucpd
2025-07-26 21:35:11,809 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:35:11,809 [INFO] PluginManager:220 - loadIPPluginJar : add usart
2025-07-26 21:35:11,811 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-26 21:35:11,812 [INFO] PluginManager:220 - loadIPPluginJar : add usbx
2025-07-26 21:35:11,914 [FATAL] Updater:351 - Updater called before beeing initialized
2025-07-26 21:35:11,929 [INFO] RulesReader:64 - Compatibility file has been processed (302 Rules)
2025-07-26 21:35:11,936 [INFO] RulesReader:64 - Compatibility file has been processed (302 Rules)
2025-07-26 21:35:11,943 [INFO] CADModel:165 - CPN selected for project level
2025-07-26 21:35:11,943 [INFO] CADModel:114 - Register for checkConnection events
2025-07-26 21:35:11,943 [FATAL] Updater:351 - Updater called before beeing initialized
2025-07-26 21:35:11,943 [ERROR] CADModel:125 - Updater not yet initialized, retry later 
2025-07-26 21:35:12,031 [FATAL] Updater:351 - Updater called before beeing initialized
2025-07-26 21:35:12,032 [INFO] CADModel:165 - CPN selected for project level
2025-07-26 21:35:12,032 [INFO] CADModel:114 - Register for checkConnection events
2025-07-26 21:35:12,032 [FATAL] Updater:351 - Updater called before beeing initialized
2025-07-26 21:35:12,032 [ERROR] CADModel:125 - Updater not yet initialized, retry later 
2025-07-26 21:35:12,034 [FATAL] Updater:351 - Updater called before beeing initialized
2025-07-26 21:35:12,096 [FATAL] Updater:351 - Updater called before beeing initialized
2025-07-26 21:35:12,104 [INFO] DbMcusAds:53 - JSON generation date=Tue Jul 08 13:44:23 IST 2025 (1751962463562)
2025-07-26 21:35:12,104 [FATAL] Updater:351 - Updater called before beeing initialized
2025-07-26 21:35:12,128 [WARN] DetailPanel:346 - Failed to get advertising image, set to default
2025-07-26 21:35:12,276 [FATAL] Updater:351 - Updater called before beeing initialized
2025-07-26 21:35:12,280 [FATAL] Updater:351 - Updater called before beeing initialized
2025-07-26 21:35:12,280 [FATAL] Updater:351 - Updater called before beeing initialized
2025-07-26 21:35:12,280 [WARN] DetailPanel:346 - Failed to get advertising image, set to default
2025-07-26 21:35:12,280 [FATAL] Updater:351 - Updater called before beeing initialized
2025-07-26 21:35:12,328 [ERROR] Updater:1198 - MainUpdater not yet initialized. External WinMGr cannot be set.
2025-07-26 21:35:12,332 [INFO] Updater:1134 - Updater Version found : 6.14.1
2025-07-26 21:35:12,353 [INFO] ApplicationProperties:184 - Using Application install path: C:\ST\STM32CubeIDE\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.14.1.202504091515
