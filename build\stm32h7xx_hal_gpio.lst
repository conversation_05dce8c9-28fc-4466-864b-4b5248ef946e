ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccO15YcJ.s 			page 1


   1              		.cpu cortex-m7
   2              		.arch armv7e-m
   3              		.fpu fpv5-d16
   4              		.eabi_attribute 28, 1
   5              		.eabi_attribute 20, 1
   6              		.eabi_attribute 21, 1
   7              		.eabi_attribute 23, 3
   8              		.eabi_attribute 24, 1
   9              		.eabi_attribute 25, 1
  10              		.eabi_attribute 26, 1
  11              		.eabi_attribute 30, 1
  12              		.eabi_attribute 34, 1
  13              		.eabi_attribute 18, 4
  14              		.file	"stm32h7xx_hal_gpio.c"
  15              		.text
  16              	.Ltext0:
  17              		.cfi_sections	.debug_frame
  18              		.file 1 "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c"
  19              		.section	.text.HAL_GPIO_Init,"ax",%progbits
  20              		.align	1
  21              		.global	HAL_GPIO_Init
  22              		.syntax unified
  23              		.thumb
  24              		.thumb_func
  26              	HAL_GPIO_Init:
  27              	.LVL0:
  28              	.LFB144:
   1:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** /**
   2:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   ******************************************************************************
   3:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   * @file    stm32h7xx_hal_gpio.c
   4:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   * <AUTHOR> Application Team
   5:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   * @brief   GPIO HAL module driver.
   6:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   *          This file provides firmware functions to manage the following
   7:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   *          functionalities of the General Purpose Input/Output (GPIO) peripheral:
   8:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   *           + Initialization and de-initialization functions
   9:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   *           + IO operation functions
  10:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   *
  11:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   ******************************************************************************
  12:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   * @attention
  13:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   *
  14:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   * Copyright (c) 2017 STMicroelectronics.
  15:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   * All rights reserved.
  16:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   *
  17:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   * This software is licensed under terms that can be found in the LICENSE file
  18:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   * in the root directory of this software component.
  19:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   * If no LICENSE file comes with this software, it is provided AS-IS.
  20:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   *
  21:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   ******************************************************************************
  22:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   @verbatim
  23:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   ==============================================================================
  24:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****                     ##### GPIO Peripheral features #####
  25:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   ==============================================================================
  26:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   [..]
  27:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****     (+) Each port bit of the general-purpose I/O (GPIO) ports can be individually
  28:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         configured by software in several modes:
  29:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         (++) Input mode
  30:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         (++) Analog mode
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccO15YcJ.s 			page 2


  31:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         (++) Output mode
  32:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         (++) Alternate function mode
  33:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         (++) External interrupt/event lines
  34:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
  35:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****     (+) During and just after reset, the alternate functions and external interrupt
  36:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         lines are not active and the I/O ports are configured in input floating mode.
  37:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
  38:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****     (+) All GPIO pins have weak internal pull-up and pull-down resistors, which can be
  39:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         activated or not.
  40:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
  41:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****     (+) In Output or Alternate mode, each IO can be configured on open-drain or push-pull
  42:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         type and the IO speed can be selected depending on the VDD value.
  43:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
  44:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****     (+) The microcontroller IO pins are connected to onboard peripherals/modules through a
  45:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         multiplexer that allows only one peripheral alternate function (AF) connected
  46:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****        to an IO pin at a time. In this way, there can be no conflict between peripherals
  47:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****        sharing the same IO pin.
  48:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
  49:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****     (+) All ports have external interrupt/event capability. To use external interrupt
  50:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         lines, the port must be configured in input mode. All available GPIO pins are
  51:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         connected to the 16 external interrupt/event lines from EXTI0 to EXTI15.
  52:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
  53:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   The external interrupt/event controller consists of up to 23 edge detectors
  54:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         (16 lines are connected to GPIO) for generating event/interrupt requests (each
  55:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         input line can be independently configured to select the type (interrupt or event)
  56:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         and the corresponding trigger event (rising or falling or both). Each line can
  57:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         also be masked independently.
  58:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
  59:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****                      ##### How to use this driver #####
  60:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   ==============================================================================
  61:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   [..]
  62:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****     (#) Enable the GPIO AHB clock using the following function: __HAL_RCC_GPIOx_CLK_ENABLE().
  63:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
  64:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****     (#) Configure the GPIO pin(s) using HAL_GPIO_Init().
  65:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         (++) Configure the IO mode using "Mode" member from GPIO_InitTypeDef structure
  66:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         (++) Activate Pull-up, Pull-down resistor using "Pull" member from GPIO_InitTypeDef
  67:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****              structure.
  68:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         (++) In case of Output or alternate function mode selection: the speed is
  69:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****              configured through "Speed" member from GPIO_InitTypeDef structure.
  70:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         (++) In alternate mode is selection, the alternate function connected to the IO
  71:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****              is configured through "Alternate" member from GPIO_InitTypeDef structure.
  72:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         (++) Analog mode is required when a pin is to be used as ADC channel
  73:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****              or DAC output.
  74:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         (++) In case of external interrupt/event selection the "Mode" member from
  75:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****              GPIO_InitTypeDef structure select the type (interrupt or event) and
  76:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****              the corresponding trigger event (rising or falling or both).
  77:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
  78:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****     (#) In case of external interrupt/event mode selection, configure NVIC IRQ priority
  79:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         mapped to the EXTI line using HAL_NVIC_SetPriority() and enable it using
  80:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         HAL_NVIC_EnableIRQ().
  81:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
  82:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****     (#) To get the level of a pin configured in input mode use HAL_GPIO_ReadPin().
  83:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
  84:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****     (#) To set/reset the level of a pin configured in output mode use
  85:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         HAL_GPIO_WritePin()/HAL_GPIO_TogglePin().
  86:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
  87:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****    (#) To lock pin configuration until next reset use HAL_GPIO_LockPin().
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccO15YcJ.s 			page 3


  88:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
  89:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
  90:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****     (#) During and just after reset, the alternate functions are not
  91:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         active and the GPIO pins are configured in input floating mode (except JTAG
  92:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         pins).
  93:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
  94:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****     (#) The LSE oscillator pins OSC32_IN and OSC32_OUT can be used as general purpose
  95:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         (PC14 and PC15, respectively) when the LSE oscillator is off. The LSE has
  96:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         priority over the GPIO function.
  97:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
  98:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****     (#) The HSE oscillator pins OSC_IN/OSC_OUT can be used as
  99:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         general purpose PH0 and PH1, respectively, when the HSE oscillator is off.
 100:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         The HSE has priority over the GPIO function.
 101:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 102:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   @endverbatim
 103:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   ******************************************************************************
 104:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   */
 105:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 106:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** /* Includes ------------------------------------------------------------------*/
 107:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** #include "stm32h7xx_hal.h"
 108:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 109:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** /** @addtogroup STM32H7xx_HAL_Driver
 110:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   * @{
 111:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   */
 112:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 113:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** /** @defgroup GPIO  GPIO
 114:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   * @brief GPIO HAL module driver
 115:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   * @{
 116:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   */
 117:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 118:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** #ifdef HAL_GPIO_MODULE_ENABLED
 119:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 120:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** /* Private typedef -----------------------------------------------------------*/
 121:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** /* Private defines ------------------------------------------------------------*/
 122:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** /** @addtogroup GPIO_Private_Constants GPIO Private Constants
 123:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   * @{
 124:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   */
 125:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 126:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** #if defined(DUAL_CORE)
 127:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** #define EXTI_CPU1             (0x01000000U)
 128:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** #define EXTI_CPU2             (0x02000000U)
 129:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** #endif /*DUAL_CORE*/
 130:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** #define GPIO_NUMBER           (16U)
 131:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** /**
 132:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   * @}
 133:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   */
 134:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** /* Private macro -------------------------------------------------------------*/
 135:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** /* Private variables ---------------------------------------------------------*/
 136:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** /* Private function prototypes -----------------------------------------------*/
 137:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** /* Private functions ---------------------------------------------------------*/
 138:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** /* Exported functions --------------------------------------------------------*/
 139:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** /** @defgroup GPIO_Exported_Functions GPIO Exported Functions
 140:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   * @{
 141:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   */
 142:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 143:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** /** @defgroup GPIO_Exported_Functions_Group1 Initialization and de-initialization functions
 144:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****  *  @brief    Initialization and Configuration functions
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccO15YcJ.s 			page 4


 145:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****  *
 146:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** @verbatim
 147:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****  ===============================================================================
 148:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****               ##### Initialization and de-initialization functions #####
 149:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****  ===============================================================================
 150:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   [..]
 151:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****     This section provides functions allowing to initialize and de-initialize the GPIOs
 152:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****     to be ready for use.
 153:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 154:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** @endverbatim
 155:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   * @{
 156:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   */
 157:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 158:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** /**
 159:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   * @brief  Initializes the GPIOx peripheral according to the specified parameters in the GPIO_Init
 160:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   * @param  GPIOx: where x can be (A..K) to select the GPIO peripheral.
 161:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   * @param  GPIO_Init: pointer to a GPIO_InitTypeDef structure that contains
 162:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   *         the configuration information for the specified GPIO peripheral.
 163:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   * @retval None
 164:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   */
 165:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** void HAL_GPIO_Init(GPIO_TypeDef  *GPIOx, const GPIO_InitTypeDef *GPIO_Init)
 166:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** {
  29              		.loc 1 166 1 view -0
  30              		.cfi_startproc
  31              		@ args = 0, pretend = 0, frame = 8
  32              		@ frame_needed = 0, uses_anonymous_args = 0
  33              		.loc 1 166 1 is_stmt 0 view .LVU1
  34 0000 F0B5     		push	{r4, r5, r6, r7, lr}
  35              	.LCFI0:
  36              		.cfi_def_cfa_offset 20
  37              		.cfi_offset 4, -20
  38              		.cfi_offset 5, -16
  39              		.cfi_offset 6, -12
  40              		.cfi_offset 7, -8
  41              		.cfi_offset 14, -4
  42 0002 83B0     		sub	sp, sp, #12
  43              	.LCFI1:
  44              		.cfi_def_cfa_offset 32
 167:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   uint32_t position = 0x00U;
  45              		.loc 1 167 3 is_stmt 1 view .LVU2
  46              	.LVL1:
 168:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   uint32_t iocurrent;
  47              		.loc 1 168 3 view .LVU3
 169:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   uint32_t temp;
  48              		.loc 1 169 3 view .LVU4
 170:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   EXTI_Core_TypeDef *EXTI_CurrentCPU;
  49              		.loc 1 170 3 view .LVU5
 171:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 172:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** #if defined(DUAL_CORE) && defined(CORE_CM4)
 173:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   EXTI_CurrentCPU = EXTI_D2; /* EXTI for CM4 CPU */
 174:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** #else
 175:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   EXTI_CurrentCPU = EXTI_D1; /* EXTI for CM7 CPU */
  50              		.loc 1 175 3 view .LVU6
 176:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** #endif
 177:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 178:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   /* Check the parameters */
 179:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   assert_param(IS_GPIO_ALL_INSTANCE(GPIOx));
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccO15YcJ.s 			page 5


  51              		.loc 1 179 3 view .LVU7
 180:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   assert_param(IS_GPIO_PIN(GPIO_Init->Pin));
  52              		.loc 1 180 3 view .LVU8
 181:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   assert_param(IS_GPIO_MODE(GPIO_Init->Mode));
  53              		.loc 1 181 3 view .LVU9
 182:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 183:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   /* Configure the port pins */
 184:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   while (((GPIO_Init->Pin) >> position) != 0x00U)
  54              		.loc 1 184 3 view .LVU10
 167:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   uint32_t iocurrent;
  55              		.loc 1 167 12 is_stmt 0 view .LVU11
  56 0004 0023     		movs	r3, #0
  57              		.loc 1 184 9 view .LVU12
  58 0006 6BE0     		b	.L2
  59              	.LVL2:
  60              	.L24:
 185:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   {
 186:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****     /* Get current io position */
 187:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****     iocurrent = (GPIO_Init->Pin) & (1UL << position);
 188:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 189:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****     if (iocurrent != 0x00U)
 190:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****     {
 191:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       /*--------------------- GPIO Mode Configuration ------------------------*/
 192:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       /* In case of Output or Alternate function mode selection */
 193:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       if (((GPIO_Init->Mode & GPIO_MODE) == MODE_OUTPUT) || ((GPIO_Init->Mode & GPIO_MODE) == MODE_
 194:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       {
 195:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         /* Check the Speed parameter */
 196:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         assert_param(IS_GPIO_SPEED(GPIO_Init->Speed));
  61              		.loc 1 196 9 is_stmt 1 view .LVU13
 197:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 198:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         /* Configure the IO Speed */
 199:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         temp = GPIOx->OSPEEDR;
  62              		.loc 1 199 9 view .LVU14
  63              		.loc 1 199 14 is_stmt 0 view .LVU15
  64 0008 8568     		ldr	r5, [r0, #8]
  65              	.LVL3:
 200:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         temp &= ~(GPIO_OSPEEDR_OSPEED0 << (position * 2U));
  66              		.loc 1 200 9 is_stmt 1 view .LVU16
  67              		.loc 1 200 53 is_stmt 0 view .LVU17
  68 000a 5E00     		lsls	r6, r3, #1
  69              		.loc 1 200 40 view .LVU18
  70 000c 0324     		movs	r4, #3
  71 000e B440     		lsls	r4, r4, r6
  72              		.loc 1 200 14 view .LVU19
  73 0010 25EA0405 		bic	r5, r5, r4
  74              	.LVL4:
 201:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         temp |= (GPIO_Init->Speed << (position * 2U));
  75              		.loc 1 201 9 is_stmt 1 view .LVU20
  76              		.loc 1 201 27 is_stmt 0 view .LVU21
  77 0014 CC68     		ldr	r4, [r1, #12]
  78              		.loc 1 201 35 view .LVU22
  79 0016 B440     		lsls	r4, r4, r6
  80              		.loc 1 201 14 view .LVU23
  81 0018 2C43     		orrs	r4, r4, r5
  82              	.LVL5:
 202:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         GPIOx->OSPEEDR = temp;
  83              		.loc 1 202 9 is_stmt 1 view .LVU24
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccO15YcJ.s 			page 6


  84              		.loc 1 202 24 is_stmt 0 view .LVU25
  85 001a 8460     		str	r4, [r0, #8]
 203:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 204:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         /* Configure the IO Output Type */
 205:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         temp = GPIOx->OTYPER;
  86              		.loc 1 205 9 is_stmt 1 view .LVU26
  87              		.loc 1 205 14 is_stmt 0 view .LVU27
  88 001c 4568     		ldr	r5, [r0, #4]
  89              	.LVL6:
 206:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         temp &= ~(GPIO_OTYPER_OT0 << position) ;
  90              		.loc 1 206 9 is_stmt 1 view .LVU28
  91              		.loc 1 206 14 is_stmt 0 view .LVU29
  92 001e 25EA0C05 		bic	r5, r5, ip
  93              	.LVL7:
 207:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         temp |= (((GPIO_Init->Mode & OUTPUT_TYPE) >> OUTPUT_TYPE_Pos) << position);
  94              		.loc 1 207 9 is_stmt 1 view .LVU30
  95              		.loc 1 207 29 is_stmt 0 view .LVU31
  96 0022 4C68     		ldr	r4, [r1, #4]
  97              		.loc 1 207 51 view .LVU32
  98 0024 C4F30014 		ubfx	r4, r4, #4, #1
  99              		.loc 1 207 71 view .LVU33
 100 0028 9C40     		lsls	r4, r4, r3
 101              		.loc 1 207 14 view .LVU34
 102 002a 2C43     		orrs	r4, r4, r5
 103              	.LVL8:
 208:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         GPIOx->OTYPER = temp;
 104              		.loc 1 208 9 is_stmt 1 view .LVU35
 105              		.loc 1 208 23 is_stmt 0 view .LVU36
 106 002c 4460     		str	r4, [r0, #4]
 107 002e 69E0     		b	.L4
 108              	.LVL9:
 109              	.L25:
 209:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       }
 210:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 211:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       if ((GPIO_Init->Mode & GPIO_MODE) != MODE_ANALOG)
 212:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       {
 213:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****        /* Check the Pull parameter */
 214:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****        assert_param(IS_GPIO_PULL(GPIO_Init->Pull));
 215:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 216:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       /* Activate the Pull-up or Pull down resistor for the current IO */
 217:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       temp = GPIOx->PUPDR;
 218:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       temp &= ~(GPIO_PUPDR_PUPD0 << (position * 2U));
 219:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       temp |= ((GPIO_Init->Pull) << (position * 2U));
 220:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       GPIOx->PUPDR = temp;
 221:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       }
 222:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 223:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       /* In case of Alternate function mode selection */
 224:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       if ((GPIO_Init->Mode & GPIO_MODE) == MODE_AF)
 225:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       {
 226:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         /* Check the Alternate function parameters */
 227:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         assert_param(IS_GPIO_AF_INSTANCE(GPIOx));
 110              		.loc 1 227 9 is_stmt 1 view .LVU37
 228:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         assert_param(IS_GPIO_AF(GPIO_Init->Alternate));
 111              		.loc 1 228 9 view .LVU38
 229:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 230:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         /* Configure Alternate function mapped with the current IO */
 231:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         temp = GPIOx->AFR[position >> 3U];
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccO15YcJ.s 			page 7


 112              		.loc 1 231 9 view .LVU39
 113              		.loc 1 231 36 is_stmt 0 view .LVU40
 114 0030 DD08     		lsrs	r5, r3, #3
 115              		.loc 1 231 14 view .LVU41
 116 0032 0835     		adds	r5, r5, #8
 117 0034 50F82540 		ldr	r4, [r0, r5, lsl #2]
 118              	.LVL10:
 232:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         temp &= ~(0xFU << ((position & 0x07U) * 4U));
 119              		.loc 1 232 9 is_stmt 1 view .LVU42
 120              		.loc 1 232 38 is_stmt 0 view .LVU43
 121 0038 03F0070C 		and	ip, r3, #7
 122              		.loc 1 232 47 view .LVU44
 123 003c 4FEA8C0C 		lsl	ip, ip, #2
 124              		.loc 1 232 24 view .LVU45
 125 0040 4FF00F0E 		mov	lr, #15
 126 0044 0EFA0CFE 		lsl	lr, lr, ip
 127              		.loc 1 232 14 view .LVU46
 128 0048 24EA0E0E 		bic	lr, r4, lr
 129              	.LVL11:
 233:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         temp |= ((GPIO_Init->Alternate) << ((position & 0x07U) * 4U));
 130              		.loc 1 233 9 is_stmt 1 view .LVU47
 131              		.loc 1 233 28 is_stmt 0 view .LVU48
 132 004c 0C69     		ldr	r4, [r1, #16]
 133              		.loc 1 233 41 view .LVU49
 134 004e 04FA0CF4 		lsl	r4, r4, ip
 135              		.loc 1 233 14 view .LVU50
 136 0052 44EA0E04 		orr	r4, r4, lr
 137              	.LVL12:
 234:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         GPIOx->AFR[position >> 3U] = temp;
 138              		.loc 1 234 9 is_stmt 1 view .LVU51
 139              		.loc 1 234 36 is_stmt 0 view .LVU52
 140 0056 40F82540 		str	r4, [r0, r5, lsl #2]
 141 005a 6BE0     		b	.L6
 142              	.LVL13:
 143              	.L26:
 235:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       }
 236:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 237:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       /* Configure IO Direction mode (Input, Output, Alternate or Analog) */
 238:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       temp = GPIOx->MODER;
 239:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       temp &= ~(GPIO_MODER_MODE0 << (position * 2U));
 240:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       temp |= ((GPIO_Init->Mode & GPIO_MODE) << (position * 2U));
 241:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       GPIOx->MODER = temp;
 242:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 243:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       /*--------------------- EXTI Mode Configuration ------------------------*/
 244:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       /* Configure the External Interrupt or event for the current IO */
 245:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       if ((GPIO_Init->Mode & EXTI_MODE) != 0x00U)
 246:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       {
 247:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         /* Enable SYSCFG Clock */
 248:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         __HAL_RCC_SYSCFG_CLK_ENABLE();
 249:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 250:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         temp = SYSCFG->EXTICR[position >> 2U];
 251:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         temp &= ~(0x0FUL << (4U * (position & 0x03U)));
 252:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         temp |= (GPIO_GET_INDEX(GPIOx) << (4U * (position & 0x03U)));
 144              		.loc 1 252 18 discriminator 17 view .LVU53
 145 005c 0924     		movs	r4, #9
 146 005e 00E0     		b	.L7
 147              	.L13:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccO15YcJ.s 			page 8


 148              		.loc 1 252 18 discriminator 2 view .LVU54
 149 0060 0024     		movs	r4, #0
 150              	.L7:
 151              		.loc 1 252 40 discriminator 36 view .LVU55
 152 0062 04FA0EF4 		lsl	r4, r4, lr
 153              		.loc 1 252 14 discriminator 36 view .LVU56
 154 0066 2C43     		orrs	r4, r4, r5
 155              	.LVL14:
 253:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         SYSCFG->EXTICR[position >> 2U] = temp;
 156              		.loc 1 253 9 is_stmt 1 view .LVU57
 157              		.loc 1 253 40 is_stmt 0 view .LVU58
 158 0068 0CF1020C 		add	ip, ip, #2
 159 006c 664D     		ldr	r5, .L27
 160 006e 45F82C40 		str	r4, [r5, ip, lsl #2]
 254:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 255:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         /* Clear Rising Falling edge configuration */
 256:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         temp = EXTI->RTSR1;
 161              		.loc 1 256 9 is_stmt 1 view .LVU59
 162              		.loc 1 256 14 is_stmt 0 view .LVU60
 163 0072 4FF0B044 		mov	r4, #1476395008
 164              	.LVL15:
 165              		.loc 1 256 14 view .LVU61
 166 0076 2568     		ldr	r5, [r4]
 167              	.LVL16:
 257:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         temp &= ~(iocurrent);
 168              		.loc 1 257 9 is_stmt 1 view .LVU62
 169              		.loc 1 257 17 is_stmt 0 view .LVU63
 170 0078 D443     		mvns	r4, r2
 171              		.loc 1 257 14 view .LVU64
 172 007a 25EA0206 		bic	r6, r5, r2
 173              	.LVL17:
 258:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         if ((GPIO_Init->Mode & TRIGGER_RISING) != 0x00U)
 174              		.loc 1 258 9 is_stmt 1 view .LVU65
 175              		.loc 1 258 12 is_stmt 0 view .LVU66
 176 007e 4F68     		ldr	r7, [r1, #4]
 177 0080 17F4801F 		tst	r7, #1048576
 178 0084 01D0     		beq	.L8
 259:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         {
 260:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****           temp |= iocurrent;
 179              		.loc 1 260 11 is_stmt 1 view .LVU67
 180              		.loc 1 260 16 is_stmt 0 view .LVU68
 181 0086 42EA0506 		orr	r6, r2, r5
 182              	.LVL18:
 183              	.L8:
 261:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         }
 262:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         EXTI->RTSR1 = temp;
 184              		.loc 1 262 9 is_stmt 1 view .LVU69
 185              		.loc 1 262 21 is_stmt 0 view .LVU70
 186 008a 4FF0B045 		mov	r5, #1476395008
 187 008e 2E60     		str	r6, [r5]
 263:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 264:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         temp = EXTI->FTSR1;
 188              		.loc 1 264 9 is_stmt 1 view .LVU71
 189              		.loc 1 264 14 is_stmt 0 view .LVU72
 190 0090 6D68     		ldr	r5, [r5, #4]
 191              	.LVL19:
 265:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         temp &= ~(iocurrent);
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccO15YcJ.s 			page 9


 192              		.loc 1 265 9 is_stmt 1 view .LVU73
 193              		.loc 1 265 14 is_stmt 0 view .LVU74
 194 0092 04EA0506 		and	r6, r4, r5
 195              	.LVL20:
 266:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         if ((GPIO_Init->Mode & TRIGGER_FALLING) != 0x00U)
 196              		.loc 1 266 9 is_stmt 1 view .LVU75
 197              		.loc 1 266 12 is_stmt 0 view .LVU76
 198 0096 4F68     		ldr	r7, [r1, #4]
 199 0098 17F4001F 		tst	r7, #2097152
 200 009c 01D0     		beq	.L9
 267:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         {
 268:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****           temp |= iocurrent;
 201              		.loc 1 268 11 is_stmt 1 view .LVU77
 202              		.loc 1 268 16 is_stmt 0 view .LVU78
 203 009e 42EA0506 		orr	r6, r2, r5
 204              	.LVL21:
 205              	.L9:
 269:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         }
 270:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         EXTI->FTSR1 = temp;
 206              		.loc 1 270 9 is_stmt 1 view .LVU79
 207              		.loc 1 270 21 is_stmt 0 view .LVU80
 208 00a2 4FF0B045 		mov	r5, #1476395008
 209 00a6 6E60     		str	r6, [r5, #4]
 271:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 272:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         temp = EXTI_CurrentCPU->EMR1;
 210              		.loc 1 272 9 is_stmt 1 view .LVU81
 211              		.loc 1 272 14 is_stmt 0 view .LVU82
 212 00a8 D5F88450 		ldr	r5, [r5, #132]
 213              	.LVL22:
 273:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         temp &= ~(iocurrent);
 214              		.loc 1 273 9 is_stmt 1 view .LVU83
 215              		.loc 1 273 14 is_stmt 0 view .LVU84
 216 00ac 04EA0506 		and	r6, r4, r5
 217              	.LVL23:
 274:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         if ((GPIO_Init->Mode & EXTI_EVT) != 0x00U)
 218              		.loc 1 274 9 is_stmt 1 view .LVU85
 219              		.loc 1 274 12 is_stmt 0 view .LVU86
 220 00b0 4F68     		ldr	r7, [r1, #4]
 221 00b2 17F4003F 		tst	r7, #131072
 222 00b6 01D0     		beq	.L10
 275:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         {
 276:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****           temp |= iocurrent;
 223              		.loc 1 276 11 is_stmt 1 view .LVU87
 224              		.loc 1 276 16 is_stmt 0 view .LVU88
 225 00b8 42EA0506 		orr	r6, r2, r5
 226              	.LVL24:
 227              	.L10:
 277:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         }
 278:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         EXTI_CurrentCPU->EMR1 = temp;
 228              		.loc 1 278 9 is_stmt 1 view .LVU89
 229              		.loc 1 278 31 is_stmt 0 view .LVU90
 230 00bc 4FF0B045 		mov	r5, #1476395008
 231 00c0 C5F88460 		str	r6, [r5, #132]
 279:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 280:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         /* Clear EXTI line configuration */
 281:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         temp = EXTI_CurrentCPU->IMR1;
 232              		.loc 1 281 9 is_stmt 1 view .LVU91
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccO15YcJ.s 			page 10


 233              		.loc 1 281 14 is_stmt 0 view .LVU92
 234 00c4 D5F88050 		ldr	r5, [r5, #128]
 235              	.LVL25:
 282:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         temp &= ~(iocurrent);
 236              		.loc 1 282 9 is_stmt 1 view .LVU93
 237              		.loc 1 282 14 is_stmt 0 view .LVU94
 238 00c8 2C40     		ands	r4, r4, r5
 239              	.LVL26:
 283:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         if ((GPIO_Init->Mode & EXTI_IT) != 0x00U)
 240              		.loc 1 283 9 is_stmt 1 view .LVU95
 241              		.loc 1 283 23 is_stmt 0 view .LVU96
 242 00ca 4E68     		ldr	r6, [r1, #4]
 243              		.loc 1 283 12 view .LVU97
 244 00cc 16F4803F 		tst	r6, #65536
 245 00d0 01D0     		beq	.L11
 284:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         {
 285:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****           temp |= iocurrent;
 246              		.loc 1 285 11 is_stmt 1 view .LVU98
 247              		.loc 1 285 16 is_stmt 0 view .LVU99
 248 00d2 42EA0504 		orr	r4, r2, r5
 249              	.LVL27:
 250              	.L11:
 286:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         }
 287:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         EXTI_CurrentCPU->IMR1 = temp;
 251              		.loc 1 287 9 is_stmt 1 view .LVU100
 252              		.loc 1 287 31 is_stmt 0 view .LVU101
 253 00d6 4FF0B042 		mov	r2, #1476395008
 254              	.LVL28:
 255              		.loc 1 287 31 view .LVU102
 256 00da C2F88040 		str	r4, [r2, #128]
 257              	.LVL29:
 258              	.L3:
 288:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       }
 289:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****     }
 290:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 291:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****     position++;
 259              		.loc 1 291 5 is_stmt 1 view .LVU103
 260              		.loc 1 291 13 is_stmt 0 view .LVU104
 261 00de 0133     		adds	r3, r3, #1
 262              	.LVL30:
 263              	.L2:
 184:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   {
 264              		.loc 1 184 41 is_stmt 1 view .LVU105
 184:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   {
 265              		.loc 1 184 21 is_stmt 0 view .LVU106
 266 00e0 0A68     		ldr	r2, [r1]
 184:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   {
 267              		.loc 1 184 41 view .LVU107
 268 00e2 32FA03F4 		lsrs	r4, r2, r3
 269 00e6 00F08C80 		beq	.L23
 187:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 270              		.loc 1 187 5 is_stmt 1 view .LVU108
 187:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 271              		.loc 1 187 41 is_stmt 0 view .LVU109
 272 00ea 4FF0010C 		mov	ip, #1
 273 00ee 0CFA03FC 		lsl	ip, ip, r3
 274              	.LVL31:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccO15YcJ.s 			page 11


 189:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****     {
 275              		.loc 1 189 5 is_stmt 1 view .LVU110
 189:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****     {
 276              		.loc 1 189 8 is_stmt 0 view .LVU111
 277 00f2 1CEA0202 		ands	r2, ip, r2
 278              	.LVL32:
 189:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****     {
 279              		.loc 1 189 8 view .LVU112
 280 00f6 F2D0     		beq	.L3
 193:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       {
 281              		.loc 1 193 7 is_stmt 1 view .LVU113
 193:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       {
 282              		.loc 1 193 22 is_stmt 0 view .LVU114
 283 00f8 4C68     		ldr	r4, [r1, #4]
 193:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       {
 284              		.loc 1 193 29 view .LVU115
 285 00fa 04F00304 		and	r4, r4, #3
 193:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       {
 286              		.loc 1 193 58 view .LVU116
 287 00fe 013C     		subs	r4, r4, #1
 193:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       {
 288              		.loc 1 193 10 view .LVU117
 289 0100 012C     		cmp	r4, #1
 290 0102 81D9     		bls	.L24
 291              	.L4:
 211:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       {
 292              		.loc 1 211 7 is_stmt 1 view .LVU118
 211:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       {
 293              		.loc 1 211 21 is_stmt 0 view .LVU119
 294 0104 4C68     		ldr	r4, [r1, #4]
 211:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       {
 295              		.loc 1 211 28 view .LVU120
 296 0106 04F00304 		and	r4, r4, #3
 211:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       {
 297              		.loc 1 211 10 view .LVU121
 298 010a 032C     		cmp	r4, #3
 299 010c 0CD0     		beq	.L5
 214:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 300              		.loc 1 214 8 is_stmt 1 view .LVU122
 217:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       temp &= ~(GPIO_PUPDR_PUPD0 << (position * 2U));
 301              		.loc 1 217 7 view .LVU123
 217:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       temp &= ~(GPIO_PUPDR_PUPD0 << (position * 2U));
 302              		.loc 1 217 12 is_stmt 0 view .LVU124
 303 010e C468     		ldr	r4, [r0, #12]
 304              	.LVL33:
 218:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       temp |= ((GPIO_Init->Pull) << (position * 2U));
 305              		.loc 1 218 7 is_stmt 1 view .LVU125
 218:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       temp |= ((GPIO_Init->Pull) << (position * 2U));
 306              		.loc 1 218 47 is_stmt 0 view .LVU126
 307 0110 5D00     		lsls	r5, r3, #1
 218:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       temp |= ((GPIO_Init->Pull) << (position * 2U));
 308              		.loc 1 218 34 view .LVU127
 309 0112 4FF0030C 		mov	ip, #3
 310 0116 0CFA05FC 		lsl	ip, ip, r5
 218:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       temp |= ((GPIO_Init->Pull) << (position * 2U));
 311              		.loc 1 218 12 view .LVU128
 312 011a 24EA0C0C 		bic	ip, r4, ip
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccO15YcJ.s 			page 12


 313              	.LVL34:
 219:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       GPIOx->PUPDR = temp;
 314              		.loc 1 219 7 is_stmt 1 view .LVU129
 219:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       GPIOx->PUPDR = temp;
 315              		.loc 1 219 26 is_stmt 0 view .LVU130
 316 011e 8C68     		ldr	r4, [r1, #8]
 219:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       GPIOx->PUPDR = temp;
 317              		.loc 1 219 34 view .LVU131
 318 0120 AC40     		lsls	r4, r4, r5
 219:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       GPIOx->PUPDR = temp;
 319              		.loc 1 219 12 view .LVU132
 320 0122 44EA0C04 		orr	r4, r4, ip
 321              	.LVL35:
 220:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       }
 322              		.loc 1 220 7 is_stmt 1 view .LVU133
 220:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       }
 323              		.loc 1 220 20 is_stmt 0 view .LVU134
 324 0126 C460     		str	r4, [r0, #12]
 325              	.LVL36:
 326              	.L5:
 224:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       {
 327              		.loc 1 224 7 is_stmt 1 view .LVU135
 224:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       {
 328              		.loc 1 224 21 is_stmt 0 view .LVU136
 329 0128 4C68     		ldr	r4, [r1, #4]
 224:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       {
 330              		.loc 1 224 28 view .LVU137
 331 012a 04F00304 		and	r4, r4, #3
 224:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       {
 332              		.loc 1 224 10 view .LVU138
 333 012e 022C     		cmp	r4, #2
 334 0130 3FF47EAF 		beq	.L25
 335              	.L6:
 238:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       temp &= ~(GPIO_MODER_MODE0 << (position * 2U));
 336              		.loc 1 238 7 is_stmt 1 view .LVU139
 238:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       temp &= ~(GPIO_MODER_MODE0 << (position * 2U));
 337              		.loc 1 238 12 is_stmt 0 view .LVU140
 338 0134 0468     		ldr	r4, [r0]
 339              	.LVL37:
 239:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       temp |= ((GPIO_Init->Mode & GPIO_MODE) << (position * 2U));
 340              		.loc 1 239 7 is_stmt 1 view .LVU141
 239:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       temp |= ((GPIO_Init->Mode & GPIO_MODE) << (position * 2U));
 341              		.loc 1 239 47 is_stmt 0 view .LVU142
 342 0136 4FEA430E 		lsl	lr, r3, #1
 239:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       temp |= ((GPIO_Init->Mode & GPIO_MODE) << (position * 2U));
 343              		.loc 1 239 34 view .LVU143
 344 013a 4FF0030C 		mov	ip, #3
 345 013e 0CFA0EFC 		lsl	ip, ip, lr
 239:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       temp |= ((GPIO_Init->Mode & GPIO_MODE) << (position * 2U));
 346              		.loc 1 239 12 view .LVU144
 347 0142 24EA0C0C 		bic	ip, r4, ip
 348              	.LVL38:
 240:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       GPIOx->MODER = temp;
 349              		.loc 1 240 7 is_stmt 1 view .LVU145
 240:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       GPIOx->MODER = temp;
 350              		.loc 1 240 26 is_stmt 0 view .LVU146
 351 0146 4C68     		ldr	r4, [r1, #4]
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccO15YcJ.s 			page 13


 240:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       GPIOx->MODER = temp;
 352              		.loc 1 240 33 view .LVU147
 353 0148 04F00304 		and	r4, r4, #3
 240:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       GPIOx->MODER = temp;
 354              		.loc 1 240 46 view .LVU148
 355 014c 04FA0EF4 		lsl	r4, r4, lr
 240:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       GPIOx->MODER = temp;
 356              		.loc 1 240 12 view .LVU149
 357 0150 44EA0C04 		orr	r4, r4, ip
 358              	.LVL39:
 241:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 359              		.loc 1 241 7 is_stmt 1 view .LVU150
 241:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 360              		.loc 1 241 20 is_stmt 0 view .LVU151
 361 0154 0460     		str	r4, [r0]
 245:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       {
 362              		.loc 1 245 7 is_stmt 1 view .LVU152
 245:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       {
 363              		.loc 1 245 21 is_stmt 0 view .LVU153
 364 0156 4C68     		ldr	r4, [r1, #4]
 365              	.LVL40:
 245:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       {
 366              		.loc 1 245 10 view .LVU154
 367 0158 14F4403F 		tst	r4, #196608
 368 015c BFD0     		beq	.L3
 248:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 369              		.loc 1 248 9 is_stmt 1 view .LVU155
 370              	.LBB2:
 248:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 371              		.loc 1 248 9 view .LVU156
 248:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 372              		.loc 1 248 9 view .LVU157
 373 015e 2B4C     		ldr	r4, .L27+4
 374 0160 D4F8F450 		ldr	r5, [r4, #244]
 375 0164 45F00205 		orr	r5, r5, #2
 376 0168 C4F8F450 		str	r5, [r4, #244]
 377              	.LVL41:
 248:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 378              		.loc 1 248 9 view .LVU158
 379 016c D4F8F440 		ldr	r4, [r4, #244]
 380 0170 04F00204 		and	r4, r4, #2
 381 0174 0194     		str	r4, [sp, #4]
 248:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 382              		.loc 1 248 9 view .LVU159
 383 0176 019C     		ldr	r4, [sp, #4]
 384              	.LBE2:
 248:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 385              		.loc 1 248 9 view .LVU160
 250:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         temp &= ~(0x0FUL << (4U * (position & 0x03U)));
 386              		.loc 1 250 9 view .LVU161
 250:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         temp &= ~(0x0FUL << (4U * (position & 0x03U)));
 387              		.loc 1 250 40 is_stmt 0 view .LVU162
 388 0178 4FEA930C 		lsr	ip, r3, #2
 250:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         temp &= ~(0x0FUL << (4U * (position & 0x03U)));
 389              		.loc 1 250 14 view .LVU163
 390 017c 0CF10205 		add	r5, ip, #2
 391 0180 214C     		ldr	r4, .L27
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccO15YcJ.s 			page 14


 392 0182 54F82550 		ldr	r5, [r4, r5, lsl #2]
 393              	.LVL42:
 251:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         temp |= (GPIO_GET_INDEX(GPIOx) << (4U * (position & 0x03U)));
 394              		.loc 1 251 9 is_stmt 1 view .LVU164
 251:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         temp |= (GPIO_GET_INDEX(GPIOx) << (4U * (position & 0x03U)));
 395              		.loc 1 251 45 is_stmt 0 view .LVU165
 396 0186 03F0030E 		and	lr, r3, #3
 251:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         temp |= (GPIO_GET_INDEX(GPIOx) << (4U * (position & 0x03U)));
 397              		.loc 1 251 33 view .LVU166
 398 018a 4FEA8E0E 		lsl	lr, lr, #2
 251:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         temp |= (GPIO_GET_INDEX(GPIOx) << (4U * (position & 0x03U)));
 399              		.loc 1 251 26 view .LVU167
 400 018e 0F24     		movs	r4, #15
 401 0190 04FA0EF4 		lsl	r4, r4, lr
 251:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         temp |= (GPIO_GET_INDEX(GPIOx) << (4U * (position & 0x03U)));
 402              		.loc 1 251 14 view .LVU168
 403 0194 25EA0405 		bic	r5, r5, r4
 404              	.LVL43:
 252:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         SYSCFG->EXTICR[position >> 2U] = temp;
 405              		.loc 1 252 9 is_stmt 1 view .LVU169
 252:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         SYSCFG->EXTICR[position >> 2U] = temp;
 406              		.loc 1 252 18 is_stmt 0 view .LVU170
 407 0198 1D4C     		ldr	r4, .L27+8
 408 019a A042     		cmp	r0, r4
 409 019c 3FF460AF 		beq	.L13
 252:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         SYSCFG->EXTICR[position >> 2U] = temp;
 410              		.loc 1 252 18 discriminator 1 view .LVU171
 411 01a0 04F58064 		add	r4, r4, #1024
 412 01a4 A042     		cmp	r0, r4
 413 01a6 1ED0     		beq	.L14
 252:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         SYSCFG->EXTICR[position >> 2U] = temp;
 414              		.loc 1 252 18 discriminator 3 view .LVU172
 415 01a8 04F58064 		add	r4, r4, #1024
 416 01ac A042     		cmp	r0, r4
 417 01ae 1CD0     		beq	.L15
 252:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         SYSCFG->EXTICR[position >> 2U] = temp;
 418              		.loc 1 252 18 discriminator 5 view .LVU173
 419 01b0 04F58064 		add	r4, r4, #1024
 420 01b4 A042     		cmp	r0, r4
 421 01b6 1AD0     		beq	.L16
 252:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         SYSCFG->EXTICR[position >> 2U] = temp;
 422              		.loc 1 252 18 discriminator 7 view .LVU174
 423 01b8 04F58064 		add	r4, r4, #1024
 424 01bc A042     		cmp	r0, r4
 425 01be 18D0     		beq	.L17
 252:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         SYSCFG->EXTICR[position >> 2U] = temp;
 426              		.loc 1 252 18 discriminator 9 view .LVU175
 427 01c0 04F58064 		add	r4, r4, #1024
 428 01c4 A042     		cmp	r0, r4
 429 01c6 16D0     		beq	.L18
 252:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         SYSCFG->EXTICR[position >> 2U] = temp;
 430              		.loc 1 252 18 discriminator 11 view .LVU176
 431 01c8 04F58064 		add	r4, r4, #1024
 432 01cc A042     		cmp	r0, r4
 433 01ce 14D0     		beq	.L19
 252:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         SYSCFG->EXTICR[position >> 2U] = temp;
 434              		.loc 1 252 18 discriminator 13 view .LVU177
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccO15YcJ.s 			page 15


 435 01d0 04F58064 		add	r4, r4, #1024
 436 01d4 A042     		cmp	r0, r4
 437 01d6 12D0     		beq	.L20
 252:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         SYSCFG->EXTICR[position >> 2U] = temp;
 438              		.loc 1 252 18 discriminator 15 view .LVU178
 439 01d8 04F50064 		add	r4, r4, #2048
 440 01dc A042     		cmp	r0, r4
 441 01de 3FF43DAF 		beq	.L26
 252:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         SYSCFG->EXTICR[position >> 2U] = temp;
 442              		.loc 1 252 18 discriminator 18 view .LVU179
 443 01e2 0A24     		movs	r4, #10
 444 01e4 3DE7     		b	.L7
 445              	.L14:
 252:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         SYSCFG->EXTICR[position >> 2U] = temp;
 446              		.loc 1 252 18 discriminator 4 view .LVU180
 447 01e6 0124     		movs	r4, #1
 448 01e8 3BE7     		b	.L7
 449              	.L15:
 252:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         SYSCFG->EXTICR[position >> 2U] = temp;
 450              		.loc 1 252 18 discriminator 6 view .LVU181
 451 01ea 0224     		movs	r4, #2
 452 01ec 39E7     		b	.L7
 453              	.L16:
 252:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         SYSCFG->EXTICR[position >> 2U] = temp;
 454              		.loc 1 252 18 discriminator 8 view .LVU182
 455 01ee 0324     		movs	r4, #3
 456 01f0 37E7     		b	.L7
 457              	.L17:
 252:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         SYSCFG->EXTICR[position >> 2U] = temp;
 458              		.loc 1 252 18 discriminator 10 view .LVU183
 459 01f2 0424     		movs	r4, #4
 460 01f4 35E7     		b	.L7
 461              	.L18:
 252:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         SYSCFG->EXTICR[position >> 2U] = temp;
 462              		.loc 1 252 18 discriminator 12 view .LVU184
 463 01f6 0524     		movs	r4, #5
 464 01f8 33E7     		b	.L7
 465              	.L19:
 252:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         SYSCFG->EXTICR[position >> 2U] = temp;
 466              		.loc 1 252 18 discriminator 14 view .LVU185
 467 01fa 0624     		movs	r4, #6
 468 01fc 31E7     		b	.L7
 469              	.L20:
 252:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         SYSCFG->EXTICR[position >> 2U] = temp;
 470              		.loc 1 252 18 discriminator 16 view .LVU186
 471 01fe 0724     		movs	r4, #7
 472 0200 2FE7     		b	.L7
 473              	.LVL44:
 474              	.L23:
 292:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   }
 293:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** }
 475              		.loc 1 293 1 view .LVU187
 476 0202 03B0     		add	sp, sp, #12
 477              	.LCFI2:
 478              		.cfi_def_cfa_offset 20
 479              		@ sp needed
 480 0204 F0BD     		pop	{r4, r5, r6, r7, pc}
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccO15YcJ.s 			page 16


 481              	.L28:
 482 0206 00BF     		.align	2
 483              	.L27:
 484 0208 00040058 		.word	1476396032
 485 020c 00440258 		.word	1476543488
 486 0210 00000258 		.word	1476526080
 487              		.cfi_endproc
 488              	.LFE144:
 490              		.section	.text.HAL_GPIO_DeInit,"ax",%progbits
 491              		.align	1
 492              		.global	HAL_GPIO_DeInit
 493              		.syntax unified
 494              		.thumb
 495              		.thumb_func
 497              	HAL_GPIO_DeInit:
 498              	.LVL45:
 499              	.LFB145:
 294:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 295:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** /**
 296:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   * @brief  De-initializes the GPIOx peripheral registers to their default reset values.
 297:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   * @param  GPIOx: where x can be (A..K) to select the GPIO peripheral.
 298:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   * @param  GPIO_Pin: specifies the port bit to be written.
 299:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   *          This parameter can be one of GPIO_PIN_x where x can be (0..15).
 300:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   * @retval None
 301:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   */
 302:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** void HAL_GPIO_DeInit(GPIO_TypeDef  *GPIOx, uint32_t GPIO_Pin)
 303:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** {
 500              		.loc 1 303 1 is_stmt 1 view -0
 501              		.cfi_startproc
 502              		@ args = 0, pretend = 0, frame = 0
 503              		@ frame_needed = 0, uses_anonymous_args = 0
 304:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   uint32_t position = 0x00U;
 504              		.loc 1 304 3 view .LVU189
 305:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   uint32_t iocurrent;
 505              		.loc 1 305 3 view .LVU190
 306:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   uint32_t tmp;
 506              		.loc 1 306 3 view .LVU191
 307:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   EXTI_Core_TypeDef *EXTI_CurrentCPU;
 507              		.loc 1 307 3 view .LVU192
 308:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 309:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** #if defined(DUAL_CORE) && defined(CORE_CM4)
 310:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   EXTI_CurrentCPU = EXTI_D2; /* EXTI for CM4 CPU */
 311:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** #else
 312:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   EXTI_CurrentCPU = EXTI_D1; /* EXTI for CM7 CPU */
 508              		.loc 1 312 3 view .LVU193
 313:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** #endif
 314:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 315:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   /* Check the parameters */
 316:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   assert_param(IS_GPIO_ALL_INSTANCE(GPIOx));
 509              		.loc 1 316 3 view .LVU194
 317:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   assert_param(IS_GPIO_PIN(GPIO_Pin));
 510              		.loc 1 317 3 view .LVU195
 318:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 319:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   /* Configure the port pins */
 320:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   while ((GPIO_Pin >> position) != 0x00U)
 511              		.loc 1 320 3 view .LVU196
 304:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   uint32_t iocurrent;
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccO15YcJ.s 			page 17


 512              		.loc 1 304 12 is_stmt 0 view .LVU197
 513 0000 0023     		movs	r3, #0
 514              	.LVL46:
 515              		.loc 1 320 33 is_stmt 1 view .LVU198
 516 0002 31FA03F2 		lsrs	r2, r1, r3
 517 0006 00F09780 		beq	.L47
 303:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   uint32_t position = 0x00U;
 518              		.loc 1 303 1 is_stmt 0 view .LVU199
 519 000a F0B5     		push	{r4, r5, r6, r7, lr}
 520              	.LCFI3:
 521              		.cfi_def_cfa_offset 20
 522              		.cfi_offset 4, -20
 523              		.cfi_offset 5, -16
 524              		.cfi_offset 6, -12
 525              		.cfi_offset 7, -8
 526              		.cfi_offset 14, -4
 527 000c 2EE0     		b	.L34
 528              	.LVL47:
 529              	.L50:
 321:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   {
 322:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****     /* Get current io position */
 323:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****     iocurrent = GPIO_Pin & (1UL << position) ;
 324:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 325:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****     if (iocurrent != 0x00U)
 326:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****     {
 327:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       /*------------------------- EXTI Mode Configuration --------------------*/
 328:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       /* Clear the External Interrupt or Event for the current IO */
 329:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       tmp = SYSCFG->EXTICR[position >> 2U];
 330:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       tmp &= (0x0FUL << (4U * (position & 0x03U)));
 331:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       if (tmp == (GPIO_GET_INDEX(GPIOx) << (4U * (position & 0x03U))))
 530              		.loc 1 331 19 discriminator 17 view .LVU200
 531 000e 0925     		movs	r5, #9
 532 0010 00E0     		b	.L32
 533              	.L35:
 534              		.loc 1 331 19 discriminator 2 view .LVU201
 535 0012 0025     		movs	r5, #0
 536              	.L32:
 537              		.loc 1 331 41 discriminator 36 view .LVU202
 538 0014 05FA0CF5 		lsl	r5, r5, ip
 539              		.loc 1 331 10 discriminator 36 view .LVU203
 540 0018 A542     		cmp	r5, r4
 541 001a 6CD0     		beq	.L48
 542              	.LVL48:
 543              	.L33:
 332:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       {
 333:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         /* Clear EXTI line configuration for Current CPU */
 334:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         EXTI_CurrentCPU->IMR1 &= ~(iocurrent);
 335:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         EXTI_CurrentCPU->EMR1 &= ~(iocurrent);
 336:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 337:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         /* Clear Rising Falling edge configuration */
 338:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         EXTI->FTSR1 &= ~(iocurrent);
 339:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         EXTI->RTSR1 &= ~(iocurrent);
 340:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 341:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         tmp = 0x0FUL << (4U * (position & 0x03U));
 342:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         SYSCFG->EXTICR[position >> 2U] &= ~tmp;
 343:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       }
 344:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccO15YcJ.s 			page 18


 345:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       /*------------------------- GPIO Mode Configuration --------------------*/
 346:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       /* Configure IO in Analog Mode */
 347:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       GPIOx->MODER |= (GPIO_MODER_MODE0 << (position * 2U));
 544              		.loc 1 347 7 is_stmt 1 view .LVU204
 545              		.loc 1 347 12 is_stmt 0 view .LVU205
 546 001c 0468     		ldr	r4, [r0]
 547              		.loc 1 347 54 view .LVU206
 548 001e 5D00     		lsls	r5, r3, #1
 549              		.loc 1 347 41 view .LVU207
 550 0020 4FF0030C 		mov	ip, #3
 551 0024 0CFA05FC 		lsl	ip, ip, r5
 552              		.loc 1 347 20 view .LVU208
 553 0028 44EA0C04 		orr	r4, r4, ip
 554 002c 0460     		str	r4, [r0]
 348:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 349:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       /* Configure the default Alternate Function in current IO */
 350:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       GPIOx->AFR[position >> 3U] &= ~(0xFU << ((position & 0x07U) * 4U)) ;
 555              		.loc 1 350 7 is_stmt 1 view .LVU209
 556              		.loc 1 350 17 is_stmt 0 view .LVU210
 557 002e 4FEAD30E 		lsr	lr, r3, #3
 558              	.LVL49:
 559              		.loc 1 350 17 view .LVU211
 560 0032 0EF1080E 		add	lr, lr, #8
 561 0036 50F82E40 		ldr	r4, [r0, lr, lsl #2]
 562              		.loc 1 350 58 view .LVU212
 563 003a 03F00706 		and	r6, r3, #7
 564              		.loc 1 350 67 view .LVU213
 565 003e B600     		lsls	r6, r6, #2
 566              		.loc 1 350 44 view .LVU214
 567 0040 0F25     		movs	r5, #15
 568 0042 B540     		lsls	r5, r5, r6
 569              		.loc 1 350 34 view .LVU215
 570 0044 24EA0504 		bic	r4, r4, r5
 571 0048 40F82E40 		str	r4, [r0, lr, lsl #2]
 351:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 352:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       /* Deactivate the Pull-up and Pull-down resistor for the current IO */
 353:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       GPIOx->PUPDR &= ~(GPIO_PUPDR_PUPD0 << (position * 2U));
 572              		.loc 1 353 7 is_stmt 1 view .LVU216
 573              		.loc 1 353 12 is_stmt 0 view .LVU217
 574 004c C468     		ldr	r4, [r0, #12]
 575              		.loc 1 353 20 view .LVU218
 576 004e 24EA0C04 		bic	r4, r4, ip
 577 0052 C460     		str	r4, [r0, #12]
 354:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 355:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       /* Configure the default value IO Output Type */
 356:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       GPIOx->OTYPER  &= ~(GPIO_OTYPER_OT0 << position) ;
 578              		.loc 1 356 7 is_stmt 1 view .LVU219
 579              		.loc 1 356 12 is_stmt 0 view .LVU220
 580 0054 4468     		ldr	r4, [r0, #4]
 581              		.loc 1 356 22 view .LVU221
 582 0056 24EA0202 		bic	r2, r4, r2
 583              	.LVL50:
 584              		.loc 1 356 22 view .LVU222
 585 005a 4260     		str	r2, [r0, #4]
 357:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 358:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       /* Configure the default value for IO Speed */
 359:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       GPIOx->OSPEEDR &= ~(GPIO_OSPEEDR_OSPEED0 << (position * 2U));
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccO15YcJ.s 			page 19


 586              		.loc 1 359 7 is_stmt 1 view .LVU223
 587              		.loc 1 359 12 is_stmt 0 view .LVU224
 588 005c 8268     		ldr	r2, [r0, #8]
 589              		.loc 1 359 22 view .LVU225
 590 005e 22EA0C02 		bic	r2, r2, ip
 591 0062 8260     		str	r2, [r0, #8]
 592              	.L31:
 360:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****     }
 361:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 362:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****     position++;
 593              		.loc 1 362 5 is_stmt 1 view .LVU226
 594              		.loc 1 362 13 is_stmt 0 view .LVU227
 595 0064 0133     		adds	r3, r3, #1
 596              	.LVL51:
 320:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   {
 597              		.loc 1 320 33 is_stmt 1 view .LVU228
 598 0066 31FA03F2 		lsrs	r2, r1, r3
 599 006a 64D0     		beq	.L49
 600              	.LVL52:
 601              	.L34:
 323:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 602              		.loc 1 323 5 view .LVU229
 323:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 603              		.loc 1 323 33 is_stmt 0 view .LVU230
 604 006c 0122     		movs	r2, #1
 605 006e 9A40     		lsls	r2, r2, r3
 606              	.LVL53:
 325:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****     {
 607              		.loc 1 325 5 is_stmt 1 view .LVU231
 325:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****     {
 608              		.loc 1 325 8 is_stmt 0 view .LVU232
 609 0070 12EA010E 		ands	lr, r2, r1
 610              	.LVL54:
 325:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****     {
 611              		.loc 1 325 8 view .LVU233
 612 0074 F6D0     		beq	.L31
 329:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       tmp &= (0x0FUL << (4U * (position & 0x03U)));
 613              		.loc 1 329 7 is_stmt 1 view .LVU234
 329:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       tmp &= (0x0FUL << (4U * (position & 0x03U)));
 614              		.loc 1 329 37 is_stmt 0 view .LVU235
 615 0076 9E08     		lsrs	r6, r3, #2
 329:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       tmp &= (0x0FUL << (4U * (position & 0x03U)));
 616              		.loc 1 329 11 view .LVU236
 617 0078 B51C     		adds	r5, r6, #2
 618 007a 304C     		ldr	r4, .L51
 619 007c 54F82540 		ldr	r4, [r4, r5, lsl #2]
 620              	.LVL55:
 330:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       if (tmp == (GPIO_GET_INDEX(GPIOx) << (4U * (position & 0x03U))))
 621              		.loc 1 330 7 is_stmt 1 view .LVU237
 330:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       if (tmp == (GPIO_GET_INDEX(GPIOx) << (4U * (position & 0x03U))))
 622              		.loc 1 330 41 is_stmt 0 view .LVU238
 623 0080 03F0030C 		and	ip, r3, #3
 330:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       if (tmp == (GPIO_GET_INDEX(GPIOx) << (4U * (position & 0x03U))))
 624              		.loc 1 330 29 view .LVU239
 625 0084 4FEA8C0C 		lsl	ip, ip, #2
 330:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       if (tmp == (GPIO_GET_INDEX(GPIOx) << (4U * (position & 0x03U))))
 626              		.loc 1 330 22 view .LVU240
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccO15YcJ.s 			page 20


 627 0088 0F25     		movs	r5, #15
 628 008a 05FA0CF7 		lsl	r7, r5, ip
 330:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       if (tmp == (GPIO_GET_INDEX(GPIOx) << (4U * (position & 0x03U))))
 629              		.loc 1 330 11 view .LVU241
 630 008e 3C40     		ands	r4, r4, r7
 631              	.LVL56:
 331:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       {
 632              		.loc 1 331 7 is_stmt 1 view .LVU242
 331:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       {
 633              		.loc 1 331 19 is_stmt 0 view .LVU243
 634 0090 2B4D     		ldr	r5, .L51+4
 635 0092 A842     		cmp	r0, r5
 636 0094 BDD0     		beq	.L35
 331:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       {
 637              		.loc 1 331 19 discriminator 1 view .LVU244
 638 0096 05F58065 		add	r5, r5, #1024
 639 009a A842     		cmp	r0, r5
 640 009c 1DD0     		beq	.L36
 331:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       {
 641              		.loc 1 331 19 discriminator 3 view .LVU245
 642 009e 05F58065 		add	r5, r5, #1024
 643 00a2 A842     		cmp	r0, r5
 644 00a4 1BD0     		beq	.L37
 331:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       {
 645              		.loc 1 331 19 discriminator 5 view .LVU246
 646 00a6 05F58065 		add	r5, r5, #1024
 647 00aa A842     		cmp	r0, r5
 648 00ac 19D0     		beq	.L38
 331:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       {
 649              		.loc 1 331 19 discriminator 7 view .LVU247
 650 00ae 05F58065 		add	r5, r5, #1024
 651 00b2 A842     		cmp	r0, r5
 652 00b4 17D0     		beq	.L39
 331:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       {
 653              		.loc 1 331 19 discriminator 9 view .LVU248
 654 00b6 05F58065 		add	r5, r5, #1024
 655 00ba A842     		cmp	r0, r5
 656 00bc 15D0     		beq	.L40
 331:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       {
 657              		.loc 1 331 19 discriminator 11 view .LVU249
 658 00be 05F58065 		add	r5, r5, #1024
 659 00c2 A842     		cmp	r0, r5
 660 00c4 13D0     		beq	.L41
 331:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       {
 661              		.loc 1 331 19 discriminator 13 view .LVU250
 662 00c6 05F58065 		add	r5, r5, #1024
 663 00ca A842     		cmp	r0, r5
 664 00cc 11D0     		beq	.L42
 331:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       {
 665              		.loc 1 331 19 discriminator 15 view .LVU251
 666 00ce 05F50065 		add	r5, r5, #2048
 667 00d2 A842     		cmp	r0, r5
 668 00d4 9BD0     		beq	.L50
 331:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       {
 669              		.loc 1 331 19 discriminator 18 view .LVU252
 670 00d6 0A25     		movs	r5, #10
 671 00d8 9CE7     		b	.L32
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccO15YcJ.s 			page 21


 672              	.L36:
 331:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       {
 673              		.loc 1 331 19 discriminator 4 view .LVU253
 674 00da 0125     		movs	r5, #1
 675 00dc 9AE7     		b	.L32
 676              	.L37:
 331:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       {
 677              		.loc 1 331 19 discriminator 6 view .LVU254
 678 00de 0225     		movs	r5, #2
 679 00e0 98E7     		b	.L32
 680              	.L38:
 331:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       {
 681              		.loc 1 331 19 discriminator 8 view .LVU255
 682 00e2 0325     		movs	r5, #3
 683 00e4 96E7     		b	.L32
 684              	.L39:
 331:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       {
 685              		.loc 1 331 19 discriminator 10 view .LVU256
 686 00e6 0425     		movs	r5, #4
 687 00e8 94E7     		b	.L32
 688              	.L40:
 331:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       {
 689              		.loc 1 331 19 discriminator 12 view .LVU257
 690 00ea 0525     		movs	r5, #5
 691 00ec 92E7     		b	.L32
 692              	.L41:
 331:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       {
 693              		.loc 1 331 19 discriminator 14 view .LVU258
 694 00ee 0625     		movs	r5, #6
 695 00f0 90E7     		b	.L32
 696              	.L42:
 331:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       {
 697              		.loc 1 331 19 discriminator 16 view .LVU259
 698 00f2 0725     		movs	r5, #7
 699 00f4 8EE7     		b	.L32
 700              	.L48:
 334:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         EXTI_CurrentCPU->EMR1 &= ~(iocurrent);
 701              		.loc 1 334 9 is_stmt 1 view .LVU260
 334:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         EXTI_CurrentCPU->EMR1 &= ~(iocurrent);
 702              		.loc 1 334 24 is_stmt 0 view .LVU261
 703 00f6 4FF0B044 		mov	r4, #1476395008
 704              	.LVL57:
 334:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         EXTI_CurrentCPU->EMR1 &= ~(iocurrent);
 705              		.loc 1 334 24 view .LVU262
 706 00fa D4F88050 		ldr	r5, [r4, #128]
 334:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         EXTI_CurrentCPU->EMR1 &= ~(iocurrent);
 707              		.loc 1 334 31 view .LVU263
 708 00fe 25EA0E05 		bic	r5, r5, lr
 709 0102 C4F88050 		str	r5, [r4, #128]
 335:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 710              		.loc 1 335 9 is_stmt 1 view .LVU264
 335:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 711              		.loc 1 335 24 is_stmt 0 view .LVU265
 712 0106 D4F88450 		ldr	r5, [r4, #132]
 335:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 713              		.loc 1 335 31 view .LVU266
 714 010a 25EA0E05 		bic	r5, r5, lr
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccO15YcJ.s 			page 22


 715 010e C4F88450 		str	r5, [r4, #132]
 338:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         EXTI->RTSR1 &= ~(iocurrent);
 716              		.loc 1 338 9 is_stmt 1 view .LVU267
 338:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         EXTI->RTSR1 &= ~(iocurrent);
 717              		.loc 1 338 13 is_stmt 0 view .LVU268
 718 0112 6568     		ldr	r5, [r4, #4]
 338:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         EXTI->RTSR1 &= ~(iocurrent);
 719              		.loc 1 338 21 view .LVU269
 720 0114 25EA0E05 		bic	r5, r5, lr
 721 0118 6560     		str	r5, [r4, #4]
 339:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 722              		.loc 1 339 9 is_stmt 1 view .LVU270
 339:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 723              		.loc 1 339 13 is_stmt 0 view .LVU271
 724 011a 2568     		ldr	r5, [r4]
 339:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 725              		.loc 1 339 21 view .LVU272
 726 011c 25EA0E05 		bic	r5, r5, lr
 727 0120 2560     		str	r5, [r4]
 341:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****         SYSCFG->EXTICR[position >> 2U] &= ~tmp;
 728              		.loc 1 341 9 is_stmt 1 view .LVU273
 729              	.LVL58:
 342:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       }
 730              		.loc 1 342 9 view .LVU274
 342:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       }
 731              		.loc 1 342 23 is_stmt 0 view .LVU275
 732 0122 DFF818C0 		ldr	ip, .L51
 733 0126 B41C     		adds	r4, r6, #2
 734 0128 5CF82450 		ldr	r5, [ip, r4, lsl #2]
 342:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****       }
 735              		.loc 1 342 40 view .LVU276
 736 012c 25EA0705 		bic	r5, r5, r7
 737 0130 4CF82450 		str	r5, [ip, r4, lsl #2]
 738 0134 72E7     		b	.L33
 739              	.LVL59:
 740              	.L49:
 363:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   }
 364:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** }
 741              		.loc 1 364 1 view .LVU277
 742 0136 F0BD     		pop	{r4, r5, r6, r7, pc}
 743              	.LVL60:
 744              	.L47:
 745              	.LCFI4:
 746              		.cfi_def_cfa_offset 0
 747              		.cfi_restore 4
 748              		.cfi_restore 5
 749              		.cfi_restore 6
 750              		.cfi_restore 7
 751              		.cfi_restore 14
 752              		.loc 1 364 1 view .LVU278
 753 0138 7047     		bx	lr
 754              	.L52:
 755 013a 00BF     		.align	2
 756              	.L51:
 757 013c 00040058 		.word	1476396032
 758 0140 00000258 		.word	1476526080
 759              		.cfi_endproc
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccO15YcJ.s 			page 23


 760              	.LFE145:
 762              		.section	.text.HAL_GPIO_ReadPin,"ax",%progbits
 763              		.align	1
 764              		.global	HAL_GPIO_ReadPin
 765              		.syntax unified
 766              		.thumb
 767              		.thumb_func
 769              	HAL_GPIO_ReadPin:
 770              	.LVL61:
 771              	.LFB146:
 365:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 366:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** /**
 367:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   * @}
 368:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   */
 369:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 370:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** /** @defgroup GPIO_Exported_Functions_Group2 IO operation functions
 371:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****  *  @brief GPIO Read, Write, Toggle, Lock and EXTI management functions.
 372:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****  *
 373:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** @verbatim
 374:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****  ===============================================================================
 375:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****                        ##### IO operation functions #####
 376:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****  ===============================================================================
 377:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 378:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** @endverbatim
 379:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   * @{
 380:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   */
 381:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 382:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** /**
 383:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   * @brief  Reads the specified input port pin.
 384:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   * @param  GPIOx: where x can be (A..K) to select the GPIO peripheral.
 385:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   * @param  GPIO_Pin: specifies the port bit to read.
 386:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   *         This parameter can be GPIO_PIN_x where x can be (0..15).
 387:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   * @retval The input port pin value.
 388:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   */
 389:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** GPIO_PinState HAL_GPIO_ReadPin(const GPIO_TypeDef *GPIOx, uint16_t GPIO_Pin)
 390:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** {
 772              		.loc 1 390 1 is_stmt 1 view -0
 773              		.cfi_startproc
 774              		@ args = 0, pretend = 0, frame = 0
 775              		@ frame_needed = 0, uses_anonymous_args = 0
 776              		@ link register save eliminated.
 391:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   GPIO_PinState bitstatus;
 777              		.loc 1 391 3 view .LVU280
 392:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 393:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   /* Check the parameters */
 394:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   assert_param(IS_GPIO_PIN(GPIO_Pin));
 778              		.loc 1 394 3 view .LVU281
 395:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 396:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   if ((GPIOx->IDR & GPIO_Pin) != 0x00U)
 779              		.loc 1 396 3 view .LVU282
 780              		.loc 1 396 13 is_stmt 0 view .LVU283
 781 0000 0369     		ldr	r3, [r0, #16]
 782              		.loc 1 396 6 view .LVU284
 783 0002 1942     		tst	r1, r3
 784 0004 01D0     		beq	.L55
 397:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   {
 398:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****     bitstatus = GPIO_PIN_SET;
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccO15YcJ.s 			page 24


 785              		.loc 1 398 15 view .LVU285
 786 0006 0120     		movs	r0, #1
 787              	.LVL62:
 788              		.loc 1 398 15 view .LVU286
 789 0008 7047     		bx	lr
 790              	.LVL63:
 791              	.L55:
 399:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   }
 400:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   else
 401:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   {
 402:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****     bitstatus = GPIO_PIN_RESET;
 792              		.loc 1 402 15 view .LVU287
 793 000a 0020     		movs	r0, #0
 794              	.LVL64:
 403:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   }
 404:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   return bitstatus;
 795              		.loc 1 404 3 is_stmt 1 view .LVU288
 405:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** }
 796              		.loc 1 405 1 is_stmt 0 view .LVU289
 797 000c 7047     		bx	lr
 798              		.cfi_endproc
 799              	.LFE146:
 801              		.section	.text.HAL_GPIO_WritePin,"ax",%progbits
 802              		.align	1
 803              		.global	HAL_GPIO_WritePin
 804              		.syntax unified
 805              		.thumb
 806              		.thumb_func
 808              	HAL_GPIO_WritePin:
 809              	.LVL65:
 810              	.LFB147:
 406:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 407:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** /**
 408:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   * @brief  Sets or clears the selected data port bit.
 409:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   *
 410:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   * @note   This function uses GPIOx_BSRR register to allow atomic read/modify
 411:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   *         accesses. In this way, there is no risk of an IRQ occurring between
 412:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   *         the read and the modify access.
 413:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   *
 414:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   * @param  GPIOx: where x can be (A..K) to select the GPIO peripheral.
 415:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   * @param  GPIO_Pin: specifies the port bit to be written.
 416:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   *          This parameter can be one of GPIO_PIN_x where x can be (0..15).
 417:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   * @param  PinState: specifies the value to be written to the selected bit.
 418:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   *          This parameter can be one of the GPIO_PinState enum values:
 419:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   *            @arg GPIO_PIN_RESET: to clear the port pin
 420:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   *            @arg GPIO_PIN_SET: to set the port pin
 421:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   * @retval None
 422:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   */
 423:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** void HAL_GPIO_WritePin(GPIO_TypeDef *GPIOx, uint16_t GPIO_Pin, GPIO_PinState PinState)
 424:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** {
 811              		.loc 1 424 1 is_stmt 1 view -0
 812              		.cfi_startproc
 813              		@ args = 0, pretend = 0, frame = 0
 814              		@ frame_needed = 0, uses_anonymous_args = 0
 815              		@ link register save eliminated.
 425:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   /* Check the parameters */
 426:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   assert_param(IS_GPIO_PIN(GPIO_Pin));
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccO15YcJ.s 			page 25


 816              		.loc 1 426 3 view .LVU291
 427:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   assert_param(IS_GPIO_PIN_ACTION(PinState));
 817              		.loc 1 427 3 view .LVU292
 428:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 429:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   if (PinState != GPIO_PIN_RESET)
 818              		.loc 1 429 3 view .LVU293
 819              		.loc 1 429 6 is_stmt 0 view .LVU294
 820 0000 0AB1     		cbz	r2, .L57
 430:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   {
 431:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****     GPIOx->BSRR = GPIO_Pin;
 821              		.loc 1 431 5 is_stmt 1 view .LVU295
 822              		.loc 1 431 17 is_stmt 0 view .LVU296
 823 0002 8161     		str	r1, [r0, #24]
 824 0004 7047     		bx	lr
 825              	.L57:
 432:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   }
 433:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   else
 434:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   {
 435:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****     GPIOx->BSRR = (uint32_t)GPIO_Pin << GPIO_NUMBER;
 826              		.loc 1 435 5 is_stmt 1 view .LVU297
 827              		.loc 1 435 38 is_stmt 0 view .LVU298
 828 0006 0904     		lsls	r1, r1, #16
 829              	.LVL66:
 830              		.loc 1 435 17 view .LVU299
 831 0008 8161     		str	r1, [r0, #24]
 436:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   }
 437:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** }
 832              		.loc 1 437 1 view .LVU300
 833 000a 7047     		bx	lr
 834              		.cfi_endproc
 835              	.LFE147:
 837              		.section	.text.HAL_GPIO_TogglePin,"ax",%progbits
 838              		.align	1
 839              		.global	HAL_GPIO_TogglePin
 840              		.syntax unified
 841              		.thumb
 842              		.thumb_func
 844              	HAL_GPIO_TogglePin:
 845              	.LVL67:
 846              	.LFB148:
 438:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 439:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** /**
 440:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   * @brief  Toggles the specified GPIO pins.
 441:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   * @param  GPIOx: Where x can be (A..K) to select the GPIO peripheral.
 442:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   * @param  GPIO_Pin: Specifies the pins to be toggled.
 443:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   * @retval None
 444:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   */
 445:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** void HAL_GPIO_TogglePin(GPIO_TypeDef *GPIOx, uint16_t GPIO_Pin)
 446:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** {
 847              		.loc 1 446 1 is_stmt 1 view -0
 848              		.cfi_startproc
 849              		@ args = 0, pretend = 0, frame = 0
 850              		@ frame_needed = 0, uses_anonymous_args = 0
 851              		@ link register save eliminated.
 447:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   uint32_t odr;
 852              		.loc 1 447 3 view .LVU302
 448:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccO15YcJ.s 			page 26


 449:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   /* Check the parameters */
 450:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   assert_param(IS_GPIO_PIN(GPIO_Pin));
 853              		.loc 1 450 3 view .LVU303
 451:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 452:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   /* get current Output Data Register value */
 453:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   odr = GPIOx->ODR;
 854              		.loc 1 453 3 view .LVU304
 855              		.loc 1 453 7 is_stmt 0 view .LVU305
 856 0000 4369     		ldr	r3, [r0, #20]
 857              	.LVL68:
 454:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 455:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   /* Set selected pins that were at low level, and reset ones that were high */
 456:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   GPIOx->BSRR = ((odr & GPIO_Pin) << GPIO_NUMBER) | (~odr & GPIO_Pin);
 858              		.loc 1 456 3 is_stmt 1 view .LVU306
 859              		.loc 1 456 23 is_stmt 0 view .LVU307
 860 0002 01EA0302 		and	r2, r1, r3
 861              		.loc 1 456 59 view .LVU308
 862 0006 21EA0301 		bic	r1, r1, r3
 863              	.LVL69:
 864              		.loc 1 456 51 view .LVU309
 865 000a 41EA0241 		orr	r1, r1, r2, lsl #16
 866              		.loc 1 456 15 view .LVU310
 867 000e 8161     		str	r1, [r0, #24]
 457:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** }
 868              		.loc 1 457 1 view .LVU311
 869 0010 7047     		bx	lr
 870              		.cfi_endproc
 871              	.LFE148:
 873              		.section	.text.HAL_GPIO_LockPin,"ax",%progbits
 874              		.align	1
 875              		.global	HAL_GPIO_LockPin
 876              		.syntax unified
 877              		.thumb
 878              		.thumb_func
 880              	HAL_GPIO_LockPin:
 881              	.LVL70:
 882              	.LFB149:
 458:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 459:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** /**
 460:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   * @brief  Locks GPIO Pins configuration registers.
 461:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   * @note   The locked registers are GPIOx_MODER, GPIOx_OTYPER, GPIOx_OSPEEDR,
 462:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   *         GPIOx_PUPDR, GPIOx_AFRL and GPIOx_AFRH.
 463:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   * @note   The configuration of the locked GPIO pins can no longer be modified
 464:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   *         until the next reset.
 465:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   * @param  GPIOx: where x can be (A..K) to select the GPIO peripheral for STM32H7 family
 466:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   * @param  GPIO_Pin: specifies the port bit to be locked.
 467:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   *         This parameter can be any combination of GPIO_PIN_x where x can be (0..15).
 468:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   * @retval None
 469:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   */
 470:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** HAL_StatusTypeDef HAL_GPIO_LockPin(GPIO_TypeDef *GPIOx, uint16_t GPIO_Pin)
 471:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** {
 883              		.loc 1 471 1 is_stmt 1 view -0
 884              		.cfi_startproc
 885              		@ args = 0, pretend = 0, frame = 8
 886              		@ frame_needed = 0, uses_anonymous_args = 0
 887              		@ link register save eliminated.
 888              		.loc 1 471 1 is_stmt 0 view .LVU313
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccO15YcJ.s 			page 27


 889 0000 82B0     		sub	sp, sp, #8
 890              	.LCFI5:
 891              		.cfi_def_cfa_offset 8
 472:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   __IO uint32_t tmp = GPIO_LCKR_LCKK;
 892              		.loc 1 472 3 is_stmt 1 view .LVU314
 893              		.loc 1 472 17 is_stmt 0 view .LVU315
 894 0002 4FF48033 		mov	r3, #65536
 895 0006 0193     		str	r3, [sp, #4]
 473:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 474:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   /* Check the parameters */
 475:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   assert_param(IS_GPIO_LOCK_INSTANCE(GPIOx));
 896              		.loc 1 475 3 is_stmt 1 view .LVU316
 476:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   assert_param(IS_GPIO_PIN(GPIO_Pin));
 897              		.loc 1 476 3 view .LVU317
 477:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 478:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   /* Apply lock key write sequence */
 479:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   tmp |= GPIO_Pin;
 898              		.loc 1 479 3 view .LVU318
 899              		.loc 1 479 7 is_stmt 0 view .LVU319
 900 0008 019B     		ldr	r3, [sp, #4]
 901 000a 0B43     		orrs	r3, r3, r1
 902 000c 0193     		str	r3, [sp, #4]
 480:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   /* Set LCKx bit(s): LCKK='1' + LCK[15-0] */
 481:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   GPIOx->LCKR = tmp;
 903              		.loc 1 481 3 is_stmt 1 view .LVU320
 904              		.loc 1 481 15 is_stmt 0 view .LVU321
 905 000e 019B     		ldr	r3, [sp, #4]
 906 0010 C361     		str	r3, [r0, #28]
 482:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   /* Reset LCKx bit(s): LCKK='0' + LCK[15-0] */
 483:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   GPIOx->LCKR = GPIO_Pin;
 907              		.loc 1 483 3 is_stmt 1 view .LVU322
 908              		.loc 1 483 15 is_stmt 0 view .LVU323
 909 0012 C161     		str	r1, [r0, #28]
 484:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   /* Set LCKx bit(s): LCKK='1' + LCK[15-0] */
 485:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   GPIOx->LCKR = tmp;
 910              		.loc 1 485 3 is_stmt 1 view .LVU324
 911              		.loc 1 485 15 is_stmt 0 view .LVU325
 912 0014 019B     		ldr	r3, [sp, #4]
 913 0016 C361     		str	r3, [r0, #28]
 486:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   /* Read LCKK register. This read is mandatory to complete key lock sequence*/
 487:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   tmp = GPIOx->LCKR;
 914              		.loc 1 487 3 is_stmt 1 view .LVU326
 915              		.loc 1 487 14 is_stmt 0 view .LVU327
 916 0018 C369     		ldr	r3, [r0, #28]
 917              		.loc 1 487 7 view .LVU328
 918 001a 0193     		str	r3, [sp, #4]
 488:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 489:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   /* read again in order to confirm lock is active */
 490:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   if ((GPIOx->LCKR & GPIO_LCKR_LCKK) != 0x00U)
 919              		.loc 1 490 3 is_stmt 1 view .LVU329
 920              		.loc 1 490 13 is_stmt 0 view .LVU330
 921 001c C369     		ldr	r3, [r0, #28]
 922              		.loc 1 490 6 view .LVU331
 923 001e 13F4803F 		tst	r3, #65536
 924 0022 02D0     		beq	.L62
 491:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   {
 492:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****     return HAL_OK;
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccO15YcJ.s 			page 28


 925              		.loc 1 492 12 view .LVU332
 926 0024 0020     		movs	r0, #0
 927              	.LVL71:
 928              	.L61:
 493:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   }
 494:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   else
 495:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   {
 496:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****     return HAL_ERROR;
 497:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   }
 498:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** }
 929              		.loc 1 498 1 view .LVU333
 930 0026 02B0     		add	sp, sp, #8
 931              	.LCFI6:
 932              		.cfi_remember_state
 933              		.cfi_def_cfa_offset 0
 934              		@ sp needed
 935 0028 7047     		bx	lr
 936              	.LVL72:
 937              	.L62:
 938              	.LCFI7:
 939              		.cfi_restore_state
 496:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   }
 940              		.loc 1 496 12 view .LVU334
 941 002a 0120     		movs	r0, #1
 942              	.LVL73:
 496:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   }
 943              		.loc 1 496 12 view .LVU335
 944 002c FBE7     		b	.L61
 945              		.cfi_endproc
 946              	.LFE149:
 948              		.section	.text.HAL_GPIO_EXTI_Callback,"ax",%progbits
 949              		.align	1
 950              		.weak	HAL_GPIO_EXTI_Callback
 951              		.syntax unified
 952              		.thumb
 953              		.thumb_func
 955              	HAL_GPIO_EXTI_Callback:
 956              	.LVL74:
 957              	.LFB151:
 499:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 500:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** /**
 501:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   * @brief  Handle EXTI interrupt request.
 502:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   * @param  GPIO_Pin: Specifies the port pin connected to corresponding EXTI line.
 503:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   * @retval None
 504:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   */
 505:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** void HAL_GPIO_EXTI_IRQHandler(uint16_t GPIO_Pin)
 506:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** {
 507:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** #if defined(DUAL_CORE) && defined(CORE_CM4)
 508:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   if (__HAL_GPIO_EXTID2_GET_IT(GPIO_Pin) != 0x00U)
 509:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   {
 510:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****     __HAL_GPIO_EXTID2_CLEAR_IT(GPIO_Pin);
 511:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****     HAL_GPIO_EXTI_Callback(GPIO_Pin);
 512:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   }
 513:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** #else
 514:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   /* EXTI line interrupt detected */
 515:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   if (__HAL_GPIO_EXTI_GET_IT(GPIO_Pin) != 0x00U)
 516:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   {
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccO15YcJ.s 			page 29


 517:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****     __HAL_GPIO_EXTI_CLEAR_IT(GPIO_Pin);
 518:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****     HAL_GPIO_EXTI_Callback(GPIO_Pin);
 519:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   }
 520:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** #endif
 521:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** }
 522:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 523:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** /**
 524:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   * @brief  EXTI line detection callback.
 525:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   * @param  GPIO_Pin: Specifies the port pin connected to corresponding EXTI line.
 526:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   * @retval None
 527:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   */
 528:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** __weak void HAL_GPIO_EXTI_Callback(uint16_t GPIO_Pin)
 529:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** {
 958              		.loc 1 529 1 is_stmt 1 view -0
 959              		.cfi_startproc
 960              		@ args = 0, pretend = 0, frame = 0
 961              		@ frame_needed = 0, uses_anonymous_args = 0
 962              		@ link register save eliminated.
 530:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   /* Prevent unused argument(s) compilation warning */
 531:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   UNUSED(GPIO_Pin);
 963              		.loc 1 531 3 view .LVU337
 532:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 533:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   /* NOTE: This function Should not be modified, when the callback is needed,
 534:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****            the HAL_GPIO_EXTI_Callback could be implemented in the user file
 535:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****    */
 536:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** }
 964              		.loc 1 536 1 is_stmt 0 view .LVU338
 965 0000 7047     		bx	lr
 966              		.cfi_endproc
 967              	.LFE151:
 969              		.section	.text.HAL_GPIO_EXTI_IRQHandler,"ax",%progbits
 970              		.align	1
 971              		.global	HAL_GPIO_EXTI_IRQHandler
 972              		.syntax unified
 973              		.thumb
 974              		.thumb_func
 976              	HAL_GPIO_EXTI_IRQHandler:
 977              	.LVL75:
 978              	.LFB150:
 506:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** #if defined(DUAL_CORE) && defined(CORE_CM4)
 979              		.loc 1 506 1 is_stmt 1 view -0
 980              		.cfi_startproc
 981              		@ args = 0, pretend = 0, frame = 0
 982              		@ frame_needed = 0, uses_anonymous_args = 0
 506:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** #if defined(DUAL_CORE) && defined(CORE_CM4)
 983              		.loc 1 506 1 is_stmt 0 view .LVU340
 984 0000 08B5     		push	{r3, lr}
 985              	.LCFI8:
 986              		.cfi_def_cfa_offset 8
 987              		.cfi_offset 3, -8
 988              		.cfi_offset 14, -4
 515:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   {
 989              		.loc 1 515 3 is_stmt 1 view .LVU341
 515:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   {
 990              		.loc 1 515 7 is_stmt 0 view .LVU342
 991 0002 4FF0B043 		mov	r3, #1476395008
 992 0006 D3F88830 		ldr	r3, [r3, #136]
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccO15YcJ.s 			page 30


 515:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   {
 993              		.loc 1 515 6 view .LVU343
 994 000a 0342     		tst	r3, r0
 995 000c 00D1     		bne	.L68
 996              	.LVL76:
 997              	.L65:
 521:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 998              		.loc 1 521 1 view .LVU344
 999 000e 08BD     		pop	{r3, pc}
 1000              	.LVL77:
 1001              	.L68:
 517:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****     HAL_GPIO_EXTI_Callback(GPIO_Pin);
 1002              		.loc 1 517 5 is_stmt 1 view .LVU345
 1003 0010 4FF0B043 		mov	r3, #1476395008
 1004 0014 C3F88800 		str	r0, [r3, #136]
 518:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c ****   }
 1005              		.loc 1 518 5 view .LVU346
 1006 0018 FFF7FEFF 		bl	HAL_GPIO_EXTI_Callback
 1007              	.LVL78:
 521:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c **** 
 1008              		.loc 1 521 1 is_stmt 0 view .LVU347
 1009 001c F7E7     		b	.L65
 1010              		.cfi_endproc
 1011              	.LFE150:
 1013              		.text
 1014              	.Letext0:
 1015              		.file 2 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\../lib/gcc/arm-none-eabi/13.3.1/..
 1016              		.file 3 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\../lib/gcc/arm-none-eabi/13.3.1/..
 1017              		.file 4 "Drivers/CMSIS/Device/ST/STM32H7xx/Include/stm32h723xx.h"
 1018              		.file 5 "Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_def.h"
 1019              		.file 6 "Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_gpio.h"
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccO15YcJ.s 			page 31


DEFINED SYMBOLS
                            *ABS*:00000000 stm32h7xx_hal_gpio.c
C:\Users\<USER>\AppData\Local\Temp\ccO15YcJ.s:20     .text.HAL_GPIO_Init:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccO15YcJ.s:26     .text.HAL_GPIO_Init:00000000 HAL_GPIO_Init
C:\Users\<USER>\AppData\Local\Temp\ccO15YcJ.s:484    .text.HAL_GPIO_Init:00000208 $d
C:\Users\<USER>\AppData\Local\Temp\ccO15YcJ.s:491    .text.HAL_GPIO_DeInit:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccO15YcJ.s:497    .text.HAL_GPIO_DeInit:00000000 HAL_GPIO_DeInit
C:\Users\<USER>\AppData\Local\Temp\ccO15YcJ.s:757    .text.HAL_GPIO_DeInit:0000013c $d
C:\Users\<USER>\AppData\Local\Temp\ccO15YcJ.s:763    .text.HAL_GPIO_ReadPin:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccO15YcJ.s:769    .text.HAL_GPIO_ReadPin:00000000 HAL_GPIO_ReadPin
C:\Users\<USER>\AppData\Local\Temp\ccO15YcJ.s:802    .text.HAL_GPIO_WritePin:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccO15YcJ.s:808    .text.HAL_GPIO_WritePin:00000000 HAL_GPIO_WritePin
C:\Users\<USER>\AppData\Local\Temp\ccO15YcJ.s:838    .text.HAL_GPIO_TogglePin:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccO15YcJ.s:844    .text.HAL_GPIO_TogglePin:00000000 HAL_GPIO_TogglePin
C:\Users\<USER>\AppData\Local\Temp\ccO15YcJ.s:874    .text.HAL_GPIO_LockPin:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccO15YcJ.s:880    .text.HAL_GPIO_LockPin:00000000 HAL_GPIO_LockPin
C:\Users\<USER>\AppData\Local\Temp\ccO15YcJ.s:949    .text.HAL_GPIO_EXTI_Callback:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccO15YcJ.s:955    .text.HAL_GPIO_EXTI_Callback:00000000 HAL_GPIO_EXTI_Callback
C:\Users\<USER>\AppData\Local\Temp\ccO15YcJ.s:970    .text.HAL_GPIO_EXTI_IRQHandler:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccO15YcJ.s:976    .text.HAL_GPIO_EXTI_IRQHandler:00000000 HAL_GPIO_EXTI_IRQHandler

NO UNDEFINED SYMBOLS
