ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cciEZQJa.s 			page 1


   1              		.cpu cortex-m7
   2              		.arch armv7e-m
   3              		.fpu fpv5-d16
   4              		.eabi_attribute 28, 1
   5              		.eabi_attribute 20, 1
   6              		.eabi_attribute 21, 1
   7              		.eabi_attribute 23, 3
   8              		.eabi_attribute 24, 1
   9              		.eabi_attribute 25, 1
  10              		.eabi_attribute 26, 1
  11              		.eabi_attribute 30, 1
  12              		.eabi_attribute 34, 1
  13              		.eabi_attribute 18, 4
  14              		.file	"microros_allocators.c"
  15              		.text
  16              	.Ltext0:
  17              		.cfi_sections	.debug_frame
  18              		.file 1 "micro_ros_stm32cubemx_utils/extra_sources/microros_allocators.c"
  19              		.section	.text.microros_allocate,"ax",%progbits
  20              		.align	1
  21              		.global	microros_allocate
  22              		.syntax unified
  23              		.thumb
  24              		.thumb_func
  26              	microros_allocate:
  27              	.LVL0:
  28              	.LFB4:
   1:micro_ros_stm32cubemx_utils/extra_sources/microros_allocators.c **** 
   2:micro_ros_stm32cubemx_utils/extra_sources/microros_allocators.c **** #include <unistd.h>
   3:micro_ros_stm32cubemx_utils/extra_sources/microros_allocators.c **** #include "cmsis_os.h"
   4:micro_ros_stm32cubemx_utils/extra_sources/microros_allocators.c **** 
   5:micro_ros_stm32cubemx_utils/extra_sources/microros_allocators.c **** int absoluteUsedMemory = 0;
   6:micro_ros_stm32cubemx_utils/extra_sources/microros_allocators.c **** int usedMemory = 0;
   7:micro_ros_stm32cubemx_utils/extra_sources/microros_allocators.c **** 
   8:micro_ros_stm32cubemx_utils/extra_sources/microros_allocators.c **** void *pvPortMallocMicroROS( size_t xWantedSize );
   9:micro_ros_stm32cubemx_utils/extra_sources/microros_allocators.c **** void vPortFreeMicroROS( void *pv );
  10:micro_ros_stm32cubemx_utils/extra_sources/microros_allocators.c **** void *pvPortReallocMicroROS( void *pv, size_t xWantedSize );
  11:micro_ros_stm32cubemx_utils/extra_sources/microros_allocators.c **** size_t getBlockSize( void *pv );
  12:micro_ros_stm32cubemx_utils/extra_sources/microros_allocators.c **** void *pvPortCallocMicroROS( size_t num, size_t xWantedSize );
  13:micro_ros_stm32cubemx_utils/extra_sources/microros_allocators.c **** 
  14:micro_ros_stm32cubemx_utils/extra_sources/microros_allocators.c **** void * microros_allocate(size_t size, void * state){
  29              		.loc 1 14 52 view -0
  30              		.cfi_startproc
  31              		@ args = 0, pretend = 0, frame = 0
  32              		@ frame_needed = 0, uses_anonymous_args = 0
  33              		.loc 1 14 52 is_stmt 0 view .LVU1
  34 0000 08B5     		push	{r3, lr}
  35              	.LCFI0:
  36              		.cfi_def_cfa_offset 8
  37              		.cfi_offset 3, -8
  38              		.cfi_offset 14, -4
  15:micro_ros_stm32cubemx_utils/extra_sources/microros_allocators.c ****   (void) state;
  39              		.loc 1 15 3 is_stmt 1 view .LVU2
  16:micro_ros_stm32cubemx_utils/extra_sources/microros_allocators.c ****   // printf("-- Alloc %d (prev: %d B)\n",size, xPortGetFreeHeapSize());
  17:micro_ros_stm32cubemx_utils/extra_sources/microros_allocators.c ****   absoluteUsedMemory += size;
  40              		.loc 1 17 3 view .LVU3
  41              		.loc 1 17 22 is_stmt 0 view .LVU4
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cciEZQJa.s 			page 2


  42 0002 0549     		ldr	r1, .L3
  43              	.LVL1:
  44              		.loc 1 17 22 view .LVU5
  45 0004 0A68     		ldr	r2, [r1]
  46 0006 0244     		add	r2, r2, r0
  47 0008 0A60     		str	r2, [r1]
  18:micro_ros_stm32cubemx_utils/extra_sources/microros_allocators.c ****   usedMemory += size;
  48              		.loc 1 18 3 is_stmt 1 view .LVU6
  49              		.loc 1 18 14 is_stmt 0 view .LVU7
  50 000a 0449     		ldr	r1, .L3+4
  51 000c 0A68     		ldr	r2, [r1]
  52 000e 0244     		add	r2, r2, r0
  53 0010 0A60     		str	r2, [r1]
  19:micro_ros_stm32cubemx_utils/extra_sources/microros_allocators.c ****   return pvPortMallocMicroROS(size);
  54              		.loc 1 19 3 is_stmt 1 view .LVU8
  55              		.loc 1 19 10 is_stmt 0 view .LVU9
  56 0012 FFF7FEFF 		bl	pvPortMallocMicroROS
  57              	.LVL2:
  20:micro_ros_stm32cubemx_utils/extra_sources/microros_allocators.c **** }
  58              		.loc 1 20 1 view .LVU10
  59 0016 08BD     		pop	{r3, pc}
  60              	.L4:
  61              		.align	2
  62              	.L3:
  63 0018 00000000 		.word	absoluteUsedMemory
  64 001c 00000000 		.word	usedMemory
  65              		.cfi_endproc
  66              	.LFE4:
  68              		.section	.text.microros_deallocate,"ax",%progbits
  69              		.align	1
  70              		.global	microros_deallocate
  71              		.syntax unified
  72              		.thumb
  73              		.thumb_func
  75              	microros_deallocate:
  76              	.LVL3:
  77              	.LFB5:
  21:micro_ros_stm32cubemx_utils/extra_sources/microros_allocators.c **** 
  22:micro_ros_stm32cubemx_utils/extra_sources/microros_allocators.c **** void microros_deallocate(void * pointer, void * state){
  78              		.loc 1 22 55 is_stmt 1 view -0
  79              		.cfi_startproc
  80              		@ args = 0, pretend = 0, frame = 0
  81              		@ frame_needed = 0, uses_anonymous_args = 0
  23:micro_ros_stm32cubemx_utils/extra_sources/microros_allocators.c ****   (void) state;
  82              		.loc 1 23 3 view .LVU12
  24:micro_ros_stm32cubemx_utils/extra_sources/microros_allocators.c ****   // printf("-- Free %d (prev: %d B)\n",getBlockSize(pointer), xPortGetFreeHeapSize());
  25:micro_ros_stm32cubemx_utils/extra_sources/microros_allocators.c ****   if (NULL != pointer){
  83              		.loc 1 25 3 view .LVU13
  84              		.loc 1 25 6 is_stmt 0 view .LVU14
  85 0000 58B1     		cbz	r0, .L8
  22:micro_ros_stm32cubemx_utils/extra_sources/microros_allocators.c ****   (void) state;
  86              		.loc 1 22 55 view .LVU15
  87 0002 10B5     		push	{r4, lr}
  88              	.LCFI1:
  89              		.cfi_def_cfa_offset 8
  90              		.cfi_offset 4, -8
  91              		.cfi_offset 14, -4
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cciEZQJa.s 			page 3


  92 0004 0446     		mov	r4, r0
  26:micro_ros_stm32cubemx_utils/extra_sources/microros_allocators.c ****     usedMemory -= getBlockSize(pointer);
  93              		.loc 1 26 5 is_stmt 1 view .LVU16
  94              		.loc 1 26 19 is_stmt 0 view .LVU17
  95 0006 FFF7FEFF 		bl	getBlockSize
  96              	.LVL4:
  97              		.loc 1 26 16 discriminator 1 view .LVU18
  98 000a 044A     		ldr	r2, .L11
  99 000c 1368     		ldr	r3, [r2]
 100 000e 1B1A     		subs	r3, r3, r0
 101 0010 1360     		str	r3, [r2]
  27:micro_ros_stm32cubemx_utils/extra_sources/microros_allocators.c ****     vPortFreeMicroROS(pointer);
 102              		.loc 1 27 5 is_stmt 1 view .LVU19
 103 0012 2046     		mov	r0, r4
 104 0014 FFF7FEFF 		bl	vPortFreeMicroROS
 105              	.LVL5:
  28:micro_ros_stm32cubemx_utils/extra_sources/microros_allocators.c ****   }
  29:micro_ros_stm32cubemx_utils/extra_sources/microros_allocators.c **** }
 106              		.loc 1 29 1 is_stmt 0 view .LVU20
 107 0018 10BD     		pop	{r4, pc}
 108              	.LVL6:
 109              	.L8:
 110              	.LCFI2:
 111              		.cfi_def_cfa_offset 0
 112              		.cfi_restore 4
 113              		.cfi_restore 14
 114              		.loc 1 29 1 view .LVU21
 115 001a 7047     		bx	lr
 116              	.L12:
 117              		.align	2
 118              	.L11:
 119 001c 00000000 		.word	usedMemory
 120              		.cfi_endproc
 121              	.LFE5:
 123              		.section	.text.microros_reallocate,"ax",%progbits
 124              		.align	1
 125              		.global	microros_reallocate
 126              		.syntax unified
 127              		.thumb
 128              		.thumb_func
 130              	microros_reallocate:
 131              	.LVL7:
 132              	.LFB6:
  30:micro_ros_stm32cubemx_utils/extra_sources/microros_allocators.c **** 
  31:micro_ros_stm32cubemx_utils/extra_sources/microros_allocators.c **** void * microros_reallocate(void * pointer, size_t size, void * state){
 133              		.loc 1 31 70 is_stmt 1 view -0
 134              		.cfi_startproc
 135              		@ args = 0, pretend = 0, frame = 0
 136              		@ frame_needed = 0, uses_anonymous_args = 0
 137              		.loc 1 31 70 is_stmt 0 view .LVU23
 138 0000 38B5     		push	{r3, r4, r5, lr}
 139              	.LCFI3:
 140              		.cfi_def_cfa_offset 16
 141              		.cfi_offset 3, -16
 142              		.cfi_offset 4, -12
 143              		.cfi_offset 5, -8
 144              		.cfi_offset 14, -4
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cciEZQJa.s 			page 4


 145 0002 0C46     		mov	r4, r1
  32:micro_ros_stm32cubemx_utils/extra_sources/microros_allocators.c ****   (void) state;
 146              		.loc 1 32 3 is_stmt 1 view .LVU24
  33:micro_ros_stm32cubemx_utils/extra_sources/microros_allocators.c ****   // printf("-- Realloc %d -> %d (prev: %d B)\n",getBlockSize(pointer),size, xPortGetFreeHeapSize()
  34:micro_ros_stm32cubemx_utils/extra_sources/microros_allocators.c ****   absoluteUsedMemory += size;
 147              		.loc 1 34 3 view .LVU25
 148              		.loc 1 34 22 is_stmt 0 view .LVU26
 149 0004 0C4A     		ldr	r2, .L18
 150              	.LVL8:
 151              		.loc 1 34 22 view .LVU27
 152 0006 1368     		ldr	r3, [r2]
 153 0008 0B44     		add	r3, r3, r1
 154 000a 1360     		str	r3, [r2]
  35:micro_ros_stm32cubemx_utils/extra_sources/microros_allocators.c ****   usedMemory += size;
 155              		.loc 1 35 3 is_stmt 1 view .LVU28
 156              		.loc 1 35 14 is_stmt 0 view .LVU29
 157 000c 0B4A     		ldr	r2, .L18+4
 158 000e 1368     		ldr	r3, [r2]
 159 0010 0B44     		add	r3, r3, r1
 160 0012 1360     		str	r3, [r2]
  36:micro_ros_stm32cubemx_utils/extra_sources/microros_allocators.c ****   if (NULL == pointer){
 161              		.loc 1 36 3 is_stmt 1 view .LVU30
 162              		.loc 1 36 6 is_stmt 0 view .LVU31
 163 0014 58B1     		cbz	r0, .L17
 164 0016 0546     		mov	r5, r0
  37:micro_ros_stm32cubemx_utils/extra_sources/microros_allocators.c ****     return pvPortMallocMicroROS(size);
  38:micro_ros_stm32cubemx_utils/extra_sources/microros_allocators.c ****   } else {
  39:micro_ros_stm32cubemx_utils/extra_sources/microros_allocators.c ****     usedMemory -= getBlockSize(pointer);
 165              		.loc 1 39 5 is_stmt 1 view .LVU32
 166              		.loc 1 39 19 is_stmt 0 view .LVU33
 167 0018 FFF7FEFF 		bl	getBlockSize
 168              	.LVL9:
 169              		.loc 1 39 16 discriminator 1 view .LVU34
 170 001c 074A     		ldr	r2, .L18+4
 171 001e 1368     		ldr	r3, [r2]
 172 0020 1B1A     		subs	r3, r3, r0
 173 0022 1360     		str	r3, [r2]
  40:micro_ros_stm32cubemx_utils/extra_sources/microros_allocators.c ****     return pvPortReallocMicroROS(pointer,size);
 174              		.loc 1 40 5 is_stmt 1 view .LVU35
 175              		.loc 1 40 12 is_stmt 0 view .LVU36
 176 0024 2146     		mov	r1, r4
 177 0026 2846     		mov	r0, r5
 178 0028 FFF7FEFF 		bl	pvPortReallocMicroROS
 179              	.LVL10:
 180              	.L13:
  41:micro_ros_stm32cubemx_utils/extra_sources/microros_allocators.c ****   }
  42:micro_ros_stm32cubemx_utils/extra_sources/microros_allocators.c **** }
 181              		.loc 1 42 1 view .LVU37
 182 002c 38BD     		pop	{r3, r4, r5, pc}
 183              	.LVL11:
 184              	.L17:
  37:micro_ros_stm32cubemx_utils/extra_sources/microros_allocators.c ****   } else {
 185              		.loc 1 37 5 is_stmt 1 view .LVU38
  37:micro_ros_stm32cubemx_utils/extra_sources/microros_allocators.c ****   } else {
 186              		.loc 1 37 12 is_stmt 0 view .LVU39
 187 002e 0846     		mov	r0, r1
 188              	.LVL12:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cciEZQJa.s 			page 5


  37:micro_ros_stm32cubemx_utils/extra_sources/microros_allocators.c ****   } else {
 189              		.loc 1 37 12 view .LVU40
 190 0030 FFF7FEFF 		bl	pvPortMallocMicroROS
 191              	.LVL13:
  37:micro_ros_stm32cubemx_utils/extra_sources/microros_allocators.c ****   } else {
 192              		.loc 1 37 12 view .LVU41
 193 0034 FAE7     		b	.L13
 194              	.L19:
 195 0036 00BF     		.align	2
 196              	.L18:
 197 0038 00000000 		.word	absoluteUsedMemory
 198 003c 00000000 		.word	usedMemory
 199              		.cfi_endproc
 200              	.LFE6:
 202              		.section	.text.microros_zero_allocate,"ax",%progbits
 203              		.align	1
 204              		.global	microros_zero_allocate
 205              		.syntax unified
 206              		.thumb
 207              		.thumb_func
 209              	microros_zero_allocate:
 210              	.LVL14:
 211              	.LFB7:
  43:micro_ros_stm32cubemx_utils/extra_sources/microros_allocators.c **** 
  44:micro_ros_stm32cubemx_utils/extra_sources/microros_allocators.c **** void * microros_zero_allocate(size_t number_of_elements, size_t size_of_element, void * state){
 212              		.loc 1 44 95 is_stmt 1 view -0
 213              		.cfi_startproc
 214              		@ args = 0, pretend = 0, frame = 0
 215              		@ frame_needed = 0, uses_anonymous_args = 0
 216              		.loc 1 44 95 is_stmt 0 view .LVU43
 217 0000 10B5     		push	{r4, lr}
 218              	.LCFI4:
 219              		.cfi_def_cfa_offset 8
 220              		.cfi_offset 4, -8
 221              		.cfi_offset 14, -4
  45:micro_ros_stm32cubemx_utils/extra_sources/microros_allocators.c ****   (void) state;
 222              		.loc 1 45 3 is_stmt 1 view .LVU44
  46:micro_ros_stm32cubemx_utils/extra_sources/microros_allocators.c ****   // printf("-- Calloc %d x %d = %d -> (prev: %d B)\n",number_of_elements,size_of_element, number_o
  47:micro_ros_stm32cubemx_utils/extra_sources/microros_allocators.c ****   absoluteUsedMemory += number_of_elements*size_of_element;
 223              		.loc 1 47 3 view .LVU45
 224              		.loc 1 47 43 is_stmt 0 view .LVU46
 225 0002 01FB00F3 		mul	r3, r1, r0
 226              		.loc 1 47 22 view .LVU47
 227 0006 054C     		ldr	r4, .L22
 228 0008 2268     		ldr	r2, [r4]
 229              	.LVL15:
 230              		.loc 1 47 22 view .LVU48
 231 000a 1A44     		add	r2, r2, r3
 232 000c 2260     		str	r2, [r4]
  48:micro_ros_stm32cubemx_utils/extra_sources/microros_allocators.c ****   usedMemory += number_of_elements*size_of_element;
 233              		.loc 1 48 3 is_stmt 1 view .LVU49
 234              		.loc 1 48 14 is_stmt 0 view .LVU50
 235 000e 044C     		ldr	r4, .L22+4
 236 0010 2268     		ldr	r2, [r4]
 237 0012 1A44     		add	r2, r2, r3
 238 0014 2260     		str	r2, [r4]
  49:micro_ros_stm32cubemx_utils/extra_sources/microros_allocators.c ****   return pvPortCallocMicroROS(number_of_elements,size_of_element);
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cciEZQJa.s 			page 6


 239              		.loc 1 49 3 is_stmt 1 view .LVU51
 240              		.loc 1 49 10 is_stmt 0 view .LVU52
 241 0016 FFF7FEFF 		bl	pvPortCallocMicroROS
 242              	.LVL16:
  50:micro_ros_stm32cubemx_utils/extra_sources/microros_allocators.c **** }...
 243              		.loc 1 50 1 view .LVU53
 244 001a 10BD     		pop	{r4, pc}
 245              	.L23:
 246              		.align	2
 247              	.L22:
 248 001c 00000000 		.word	absoluteUsedMemory
 249 0020 00000000 		.word	usedMemory
 250              		.cfi_endproc
 251              	.LFE7:
 253              		.global	usedMemory
 254              		.section	.bss.usedMemory,"aw",%nobits
 255              		.align	2
 258              	usedMemory:
 259 0000 00000000 		.space	4
 260              		.global	absoluteUsedMemory
 261              		.section	.bss.absoluteUsedMemory,"aw",%nobits
 262              		.align	2
 265              	absoluteUsedMemory:
 266 0000 00000000 		.space	4
 267              		.text
 268              	.Letext0:
 269              		.file 2 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin/../lib/gcc/arm-none-eabi/13.3.1/inc
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cciEZQJa.s 			page 7


DEFINED SYMBOLS
                            *ABS*:00000000 microros_allocators.c
C:\Users\<USER>\AppData\Local\Temp\cciEZQJa.s:20     .text.microros_allocate:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cciEZQJa.s:26     .text.microros_allocate:00000000 microros_allocate
C:\Users\<USER>\AppData\Local\Temp\cciEZQJa.s:63     .text.microros_allocate:00000018 $d
C:\Users\<USER>\AppData\Local\Temp\cciEZQJa.s:265    .bss.absoluteUsedMemory:00000000 absoluteUsedMemory
C:\Users\<USER>\AppData\Local\Temp\cciEZQJa.s:258    .bss.usedMemory:00000000 usedMemory
C:\Users\<USER>\AppData\Local\Temp\cciEZQJa.s:69     .text.microros_deallocate:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cciEZQJa.s:75     .text.microros_deallocate:00000000 microros_deallocate
C:\Users\<USER>\AppData\Local\Temp\cciEZQJa.s:119    .text.microros_deallocate:0000001c $d
C:\Users\<USER>\AppData\Local\Temp\cciEZQJa.s:124    .text.microros_reallocate:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cciEZQJa.s:130    .text.microros_reallocate:00000000 microros_reallocate
C:\Users\<USER>\AppData\Local\Temp\cciEZQJa.s:197    .text.microros_reallocate:00000038 $d
C:\Users\<USER>\AppData\Local\Temp\cciEZQJa.s:203    .text.microros_zero_allocate:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cciEZQJa.s:209    .text.microros_zero_allocate:00000000 microros_zero_allocate
C:\Users\<USER>\AppData\Local\Temp\cciEZQJa.s:248    .text.microros_zero_allocate:0000001c $d
C:\Users\<USER>\AppData\Local\Temp\cciEZQJa.s:255    .bss.usedMemory:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\cciEZQJa.s:262    .bss.absoluteUsedMemory:00000000 $d

UNDEFINED SYMBOLS
pvPortMallocMicroROS
getBlockSize
vPortFreeMicroROS
pvPortReallocMicroROS
pvPortCallocMicroROS
