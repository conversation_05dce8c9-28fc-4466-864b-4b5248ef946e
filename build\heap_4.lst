ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccpDCkRu.s 			page 1


   1              		.cpu cortex-m7
   2              		.arch armv7e-m
   3              		.fpu fpv5-d16
   4              		.eabi_attribute 28, 1
   5              		.eabi_attribute 20, 1
   6              		.eabi_attribute 21, 1
   7              		.eabi_attribute 23, 3
   8              		.eabi_attribute 24, 1
   9              		.eabi_attribute 25, 1
  10              		.eabi_attribute 26, 1
  11              		.eabi_attribute 30, 1
  12              		.eabi_attribute 34, 1
  13              		.eabi_attribute 18, 4
  14              		.file	"heap_4.c"
  15              		.text
  16              	.Ltext0:
  17              		.cfi_sections	.debug_frame
  18              		.file 1 "Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c"
  19              		.section	.text.prvHeapInit,"ax",%progbits
  20              		.align	1
  21              		.syntax unified
  22              		.thumb
  23              		.thumb_func
  25              	prvHeapInit:
  26              	.LFB9:
   1:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** /*
   2:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c ****  * FreeRTOS Kernel V10.3.1
   3:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c ****  * Copyright (C) 2020 Amazon.com, Inc. or its affiliates.  All Rights Reserved.
   4:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c ****  *
   5:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c ****  * Permission is hereby granted, free of charge, to any person obtaining a copy of
   6:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c ****  * this software and associated documentation files (the "Software"), to deal in
   7:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c ****  * the Software without restriction, including without limitation the rights to
   8:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c ****  * use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of
   9:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c ****  * the Software, and to permit persons to whom the Software is furnished to do so,
  10:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c ****  * subject to the following conditions:
  11:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c ****  *
  12:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c ****  * The above copyright notice and this permission notice shall be included in all
  13:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c ****  * copies or substantial portions of the Software.
  14:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c ****  *
  15:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c ****  * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
  16:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c ****  * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS
  17:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c ****  * FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR
  18:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c ****  * COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER
  19:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c ****  * IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
  20:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c ****  * CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
  21:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c ****  *
  22:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c ****  * http://www.FreeRTOS.org
  23:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c ****  * http://aws.amazon.com/freertos
  24:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c ****  *
  25:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c ****  * 1 tab == 4 spaces!
  26:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c ****  */
  27:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
  28:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** /*
  29:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c ****  * A sample implementation of pvPortMalloc() and vPortFree() that combines
  30:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c ****  * (coalescences) adjacent memory blocks as they are freed, and in so doing
  31:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c ****  * limits memory fragmentation.
  32:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c ****  *
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccpDCkRu.s 			page 2


  33:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c ****  * See heap_1.c, heap_2.c and heap_3.c for alternative implementations, and the
  34:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c ****  * memory management pages of http://www.FreeRTOS.org for more information.
  35:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c ****  */
  36:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** #include <stdlib.h>
  37:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
  38:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** /* Defining MPU_WRAPPERS_INCLUDED_FROM_API_FILE prevents task.h from redefining
  39:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** all the API functions to use the MPU wrappers.  That should only be done when
  40:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** task.h is included from an application file. */
  41:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** #define MPU_WRAPPERS_INCLUDED_FROM_API_FILE
  42:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
  43:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** #include "FreeRTOS.h"
  44:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** #include "task.h"
  45:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
  46:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** #undef MPU_WRAPPERS_INCLUDED_FROM_API_FILE
  47:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
  48:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** #if( configSUPPORT_DYNAMIC_ALLOCATION == 0 )
  49:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	#error This file must not be used if configSUPPORT_DYNAMIC_ALLOCATION is 0
  50:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** #endif
  51:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
  52:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** /* Block sizes must not get too small. */
  53:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** #define heapMINIMUM_BLOCK_SIZE	( ( size_t ) ( xHeapStructSize << 1 ) )
  54:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
  55:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** /* Assumes 8bit bytes! */
  56:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** #define heapBITS_PER_BYTE		( ( size_t ) 8 )
  57:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
  58:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** /* Allocate the memory for the heap. */
  59:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** #if( configAPPLICATION_ALLOCATED_HEAP == 1 )
  60:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	/* The application writer has already defined the array used for the RTOS
  61:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	heap - probably so it can be placed in a special segment or address. */
  62:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	extern uint8_t ucHeap[ configTOTAL_HEAP_SIZE ];
  63:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** #else
  64:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	static uint8_t ucHeap[ configTOTAL_HEAP_SIZE ];
  65:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** #endif /* configAPPLICATION_ALLOCATED_HEAP */
  66:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
  67:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** /* Define the linked list structure.  This is used to link free blocks in order
  68:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** of their memory address. */
  69:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** typedef struct A_BLOCK_LINK
  70:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** {
  71:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	struct A_BLOCK_LINK *pxNextFreeBlock;	/*<< The next free block in the list. */
  72:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	size_t xBlockSize;						/*<< The size of the free block. */
  73:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** } BlockLink_t;
  74:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
  75:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** /*-----------------------------------------------------------*/
  76:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
  77:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** /*
  78:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c ****  * Inserts a block of memory that is being freed into the correct position in
  79:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c ****  * the list of free memory blocks.  The block being freed will be merged with
  80:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c ****  * the block in front it and/or the block behind it if the memory blocks are
  81:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c ****  * adjacent to each other.
  82:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c ****  */
  83:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** static void prvInsertBlockIntoFreeList( BlockLink_t *pxBlockToInsert );
  84:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
  85:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** /*
  86:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c ****  * Called automatically to setup the required heap structures the first time
  87:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c ****  * pvPortMalloc() is called.
  88:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c ****  */
  89:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** static void prvHeapInit( void );
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccpDCkRu.s 			page 3


  90:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
  91:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** /*-----------------------------------------------------------*/
  92:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
  93:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** /* The size of the structure placed at the beginning of each allocated memory
  94:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** block must by correctly byte aligned. */
  95:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** static const size_t xHeapStructSize	= ( sizeof( BlockLink_t ) + ( ( size_t ) ( portBYTE_ALIGNMENT -
  96:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
  97:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** /* Create a couple of list links to mark the start and end of the list. */
  98:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** static BlockLink_t xStart, *pxEnd = NULL;
  99:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 100:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** /* Keeps track of the number of calls to allocate and free memory as well as the
 101:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** number of free bytes remaining, but says nothing about fragmentation. */
 102:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** static size_t xFreeBytesRemaining = 0U;
 103:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** static size_t xMinimumEverFreeBytesRemaining = 0U;
 104:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** static size_t xNumberOfSuccessfulAllocations = 0;
 105:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** static size_t xNumberOfSuccessfulFrees = 0;
 106:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 107:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** /* Gets set to the top bit of an size_t type.  When this bit in the xBlockSize
 108:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** member of an BlockLink_t structure is set then the block belongs to the
 109:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** application.  When the bit is free the block is still part of the free heap
 110:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** space. */
 111:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** static size_t xBlockAllocatedBit = 0;
 112:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 113:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** /*-----------------------------------------------------------*/
 114:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 115:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** void *pvPortMalloc( size_t xWantedSize )
 116:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** {
 117:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** BlockLink_t *pxBlock, *pxPreviousBlock, *pxNewBlockLink;
 118:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** void *pvReturn = NULL;
 119:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 120:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	vTaskSuspendAll();
 121:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	{
 122:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		/* If this is the first call to malloc then the heap will require
 123:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		initialisation to setup the list of free blocks. */
 124:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		if( pxEnd == NULL )
 125:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		{
 126:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 			prvHeapInit();
 127:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		}
 128:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		else
 129:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		{
 130:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 			mtCOVERAGE_TEST_MARKER();
 131:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		}
 132:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 133:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		/* Check the requested block size is not so large that the top bit is
 134:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		set.  The top bit of the block size member of the BlockLink_t structure
 135:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		is used to determine who owns the block - the application or the
 136:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		kernel, so it must be free. */
 137:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		if( ( xWantedSize & xBlockAllocatedBit ) == 0 )
 138:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		{
 139:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 			/* The wanted size is increased so it can contain a BlockLink_t
 140:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 			structure in addition to the requested amount of bytes. */
 141:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 			if( xWantedSize > 0 )
 142:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 			{
 143:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 				xWantedSize += xHeapStructSize;
 144:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 145:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 				/* Ensure that blocks are always aligned to the required number
 146:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 				of bytes. */
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccpDCkRu.s 			page 4


 147:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 				if( ( xWantedSize & portBYTE_ALIGNMENT_MASK ) != 0x00 )
 148:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 				{
 149:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 					/* Byte alignment required. */
 150:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 					xWantedSize += ( portBYTE_ALIGNMENT - ( xWantedSize & portBYTE_ALIGNMENT_MASK ) );
 151:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 					configASSERT( ( xWantedSize & portBYTE_ALIGNMENT_MASK ) == 0 );
 152:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 				}
 153:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 				else
 154:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 				{
 155:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 					mtCOVERAGE_TEST_MARKER();
 156:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 				}
 157:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 			}
 158:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 			else
 159:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 			{
 160:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 				mtCOVERAGE_TEST_MARKER();
 161:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 			}
 162:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 163:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 			if( ( xWantedSize > 0 ) && ( xWantedSize <= xFreeBytesRemaining ) )
 164:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 			{
 165:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 				/* Traverse the list from the start	(lowest address) block until
 166:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 				one	of adequate size is found. */
 167:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 				pxPreviousBlock = &xStart;
 168:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 				pxBlock = xStart.pxNextFreeBlock;
 169:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 				while( ( pxBlock->xBlockSize < xWantedSize ) && ( pxBlock->pxNextFreeBlock != NULL ) )
 170:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 				{
 171:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 					pxPreviousBlock = pxBlock;
 172:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 					pxBlock = pxBlock->pxNextFreeBlock;
 173:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 				}
 174:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 175:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 				/* If the end marker was reached then a block of adequate size
 176:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 				was	not found. */
 177:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 				if( pxBlock != pxEnd )
 178:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 				{
 179:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 					/* Return the memory space pointed to - jumping over the
 180:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 					BlockLink_t structure at its start. */
 181:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 					pvReturn = ( void * ) ( ( ( uint8_t * ) pxPreviousBlock->pxNextFreeBlock ) + xHeapStructSize )
 182:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 183:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 					/* This block is being returned for use so must be taken out
 184:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 					of the list of free blocks. */
 185:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 					pxPreviousBlock->pxNextFreeBlock = pxBlock->pxNextFreeBlock;
 186:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 187:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 					/* If the block is larger than required it can be split into
 188:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 					two. */
 189:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 					if( ( pxBlock->xBlockSize - xWantedSize ) > heapMINIMUM_BLOCK_SIZE )
 190:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 					{
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 						/* This block is to be split into two.  Create a new
 192:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 						block following the number of bytes requested. The void
 193:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 						cast is used to prevent byte alignment warnings from the
 194:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 						compiler. */
 195:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 						pxNewBlockLink = ( void * ) ( ( ( uint8_t * ) pxBlock ) + xWantedSize );
 196:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 						configASSERT( ( ( ( size_t ) pxNewBlockLink ) & portBYTE_ALIGNMENT_MASK ) == 0 );
 197:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 198:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 						/* Calculate the sizes of two blocks split from the
 199:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 						single block. */
 200:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 						pxNewBlockLink->xBlockSize = pxBlock->xBlockSize - xWantedSize;
 201:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 						pxBlock->xBlockSize = xWantedSize;
 202:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 203:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 						/* Insert the new block into the list of free blocks. */
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccpDCkRu.s 			page 5


 204:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 						prvInsertBlockIntoFreeList( pxNewBlockLink );
 205:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 					}
 206:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 					else
 207:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 					{
 208:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 						mtCOVERAGE_TEST_MARKER();
 209:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 					}
 210:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 211:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 					xFreeBytesRemaining -= pxBlock->xBlockSize;
 212:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 213:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 					if( xFreeBytesRemaining < xMinimumEverFreeBytesRemaining )
 214:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 					{
 215:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 						xMinimumEverFreeBytesRemaining = xFreeBytesRemaining;
 216:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 					}
 217:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 					else
 218:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 					{
 219:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 						mtCOVERAGE_TEST_MARKER();
 220:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 					}
 221:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 222:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 					/* The block is being returned - it is allocated and owned
 223:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 					by the application and has no "next" block. */
 224:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 					pxBlock->xBlockSize |= xBlockAllocatedBit;
 225:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 					pxBlock->pxNextFreeBlock = NULL;
 226:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 					xNumberOfSuccessfulAllocations++;
 227:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 				}
 228:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 				else
 229:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 				{
 230:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 					mtCOVERAGE_TEST_MARKER();
 231:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 				}
 232:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 			}
 233:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 			else
 234:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 			{
 235:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 				mtCOVERAGE_TEST_MARKER();
 236:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 			}
 237:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		}
 238:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		else
 239:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		{
 240:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 			mtCOVERAGE_TEST_MARKER();
 241:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		}
 242:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 243:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		traceMALLOC( pvReturn, xWantedSize );
 244:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	}
 245:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	( void ) xTaskResumeAll();
 246:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 247:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	#if( configUSE_MALLOC_FAILED_HOOK == 1 )
 248:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	{
 249:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		if( pvReturn == NULL )
 250:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		{
 251:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 			extern void vApplicationMallocFailedHook( void );
 252:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 			vApplicationMallocFailedHook();
 253:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		}
 254:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		else
 255:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		{
 256:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 			mtCOVERAGE_TEST_MARKER();
 257:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		}
 258:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	}
 259:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	#endif
 260:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccpDCkRu.s 			page 6


 261:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	configASSERT( ( ( ( size_t ) pvReturn ) & ( size_t ) portBYTE_ALIGNMENT_MASK ) == 0 );
 262:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	return pvReturn;
 263:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** }
 264:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** /*-----------------------------------------------------------*/
 265:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 266:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** void vPortFree( void *pv )
 267:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** {
 268:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** uint8_t *puc = ( uint8_t * ) pv;
 269:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** BlockLink_t *pxLink;
 270:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 271:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	if( pv != NULL )
 272:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	{
 273:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		/* The memory being freed will have an BlockLink_t structure immediately
 274:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		before it. */
 275:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		puc -= xHeapStructSize;
 276:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 277:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		/* This casting is to keep the compiler from issuing warnings. */
 278:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		pxLink = ( void * ) puc;
 279:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 280:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		/* Check the block is actually allocated. */
 281:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		configASSERT( ( pxLink->xBlockSize & xBlockAllocatedBit ) != 0 );
 282:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		configASSERT( pxLink->pxNextFreeBlock == NULL );
 283:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 284:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		if( ( pxLink->xBlockSize & xBlockAllocatedBit ) != 0 )
 285:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		{
 286:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 			if( pxLink->pxNextFreeBlock == NULL )
 287:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 			{
 288:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 				/* The block is being returned to the heap - it is no longer
 289:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 				allocated. */
 290:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 				pxLink->xBlockSize &= ~xBlockAllocatedBit;
 291:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 292:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 				vTaskSuspendAll();
 293:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 				{
 294:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 					/* Add this block to the list of free blocks. */
 295:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 					xFreeBytesRemaining += pxLink->xBlockSize;
 296:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 					traceFREE( pv, pxLink->xBlockSize );
 297:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 					prvInsertBlockIntoFreeList( ( ( BlockLink_t * ) pxLink ) );
 298:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 					xNumberOfSuccessfulFrees++;
 299:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 				}
 300:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 				( void ) xTaskResumeAll();
 301:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 			}
 302:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 			else
 303:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 			{
 304:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 				mtCOVERAGE_TEST_MARKER();
 305:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 			}
 306:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		}
 307:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		else
 308:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		{
 309:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 			mtCOVERAGE_TEST_MARKER();
 310:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		}
 311:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	}
 312:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** }
 313:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** /*-----------------------------------------------------------*/
 314:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 315:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** size_t xPortGetFreeHeapSize( void )
 316:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** {
 317:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	return xFreeBytesRemaining;
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccpDCkRu.s 			page 7


 318:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** }
 319:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** /*-----------------------------------------------------------*/
 320:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 321:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** size_t xPortGetMinimumEverFreeHeapSize( void )
 322:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** {
 323:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	return xMinimumEverFreeBytesRemaining;
 324:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** }
 325:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** /*-----------------------------------------------------------*/
 326:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 327:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** void vPortInitialiseBlocks( void )
 328:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** {
 329:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	/* This just exists to keep the linker quiet. */
 330:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** }
 331:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** /*-----------------------------------------------------------*/
 332:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 333:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** static void prvHeapInit( void )
 334:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** {
  27              		.loc 1 334 1 view -0
  28              		.cfi_startproc
  29              		@ args = 0, pretend = 0, frame = 0
  30              		@ frame_needed = 0, uses_anonymous_args = 0
  31              		@ link register save eliminated.
 335:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** BlockLink_t *pxFirstFreeBlock;
  32              		.loc 1 335 1 view .LVU1
 336:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** uint8_t *pucAlignedHeap;
  33              		.loc 1 336 1 view .LVU2
 337:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** size_t uxAddress;
  34              		.loc 1 337 1 view .LVU3
 338:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** size_t xTotalHeapSize = configTOTAL_HEAP_SIZE;
  35              		.loc 1 338 1 view .LVU4
  36              	.LVL0:
 339:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 340:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	/* Ensure the heap starts on a correctly aligned boundary. */
 341:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	uxAddress = ( size_t ) ucHeap;
  37              		.loc 1 341 2 view .LVU5
  38              		.loc 1 341 12 is_stmt 0 view .LVU6
  39 0000 124A     		ldr	r2, .L4
  40              	.LVL1:
 342:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 343:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	if( ( uxAddress & portBYTE_ALIGNMENT_MASK ) != 0 )
  41              		.loc 1 343 2 is_stmt 1 view .LVU7
  42              		.loc 1 343 4 is_stmt 0 view .LVU8
  43 0002 12F0070F 		tst	r2, #7
  44 0006 1ED0     		beq	.L3
 344:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	{
 345:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		uxAddress += ( portBYTE_ALIGNMENT - 1 );
  45              		.loc 1 345 3 is_stmt 1 view .LVU9
  46              		.loc 1 345 13 is_stmt 0 view .LVU10
  47 0008 D11D     		adds	r1, r2, #7
  48              	.LVL2:
 346:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		uxAddress &= ~( ( size_t ) portBYTE_ALIGNMENT_MASK );
  49              		.loc 1 346 3 is_stmt 1 view .LVU11
  50              		.loc 1 346 13 is_stmt 0 view .LVU12
  51 000a 21F00701 		bic	r1, r1, #7
  52              	.LVL3:
 347:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		xTotalHeapSize -= uxAddress - ( size_t ) ucHeap;
  53              		.loc 1 347 3 is_stmt 1 view .LVU13
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccpDCkRu.s 			page 8


  54              		.loc 1 347 18 is_stmt 0 view .LVU14
  55 000e C1F57053 		rsb	r3, r1, #15360
  56 0012 1344     		add	r3, r3, r2
  57              	.LVL4:
 346:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		uxAddress &= ~( ( size_t ) portBYTE_ALIGNMENT_MASK );
  58              		.loc 1 346 13 view .LVU15
  59 0014 0A46     		mov	r2, r1
  60              	.LVL5:
  61              	.L2:
 348:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	}
 349:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 350:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	pucAlignedHeap = ( uint8_t * ) uxAddress;
  62              		.loc 1 350 2 is_stmt 1 view .LVU16
 351:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 352:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	/* xStart is used to hold a pointer to the first item in the list of free
 353:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	blocks.  The void cast is used to prevent compiler warnings. */
 354:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	xStart.pxNextFreeBlock = ( void * ) pucAlignedHeap;
  63              		.loc 1 354 2 view .LVU17
  64              		.loc 1 354 25 is_stmt 0 view .LVU18
  65 0016 0E48     		ldr	r0, .L4+4
  66 0018 0260     		str	r2, [r0]
 355:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	xStart.xBlockSize = ( size_t ) 0;
  67              		.loc 1 355 2 is_stmt 1 view .LVU19
  68              		.loc 1 355 20 is_stmt 0 view .LVU20
  69 001a 0021     		movs	r1, #0
  70 001c 4160     		str	r1, [r0, #4]
 356:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 357:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	/* pxEnd is used to mark the end of the list of free blocks and is inserted
 358:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	at the end of the heap space. */
 359:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	uxAddress = ( ( size_t ) pucAlignedHeap ) + xTotalHeapSize;
  71              		.loc 1 359 2 is_stmt 1 view .LVU21
  72              		.loc 1 359 12 is_stmt 0 view .LVU22
  73 001e 1344     		add	r3, r3, r2
  74              	.LVL6:
 360:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	uxAddress -= xHeapStructSize;
  75              		.loc 1 360 2 is_stmt 1 view .LVU23
  76              		.loc 1 360 12 is_stmt 0 view .LVU24
  77 0020 083B     		subs	r3, r3, #8
  78              	.LVL7:
 361:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	uxAddress &= ~( ( size_t ) portBYTE_ALIGNMENT_MASK );
  79              		.loc 1 361 2 is_stmt 1 view .LVU25
  80              		.loc 1 361 12 is_stmt 0 view .LVU26
  81 0022 23F00703 		bic	r3, r3, #7
  82              	.LVL8:
 362:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	pxEnd = ( void * ) uxAddress;
  83              		.loc 1 362 2 is_stmt 1 view .LVU27
  84              		.loc 1 362 8 is_stmt 0 view .LVU28
  85 0026 0B48     		ldr	r0, .L4+8
  86 0028 0360     		str	r3, [r0]
 363:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	pxEnd->xBlockSize = 0;
  87              		.loc 1 363 2 is_stmt 1 view .LVU29
  88              		.loc 1 363 20 is_stmt 0 view .LVU30
  89 002a 5960     		str	r1, [r3, #4]
 364:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	pxEnd->pxNextFreeBlock = NULL;
  90              		.loc 1 364 2 is_stmt 1 view .LVU31
  91              		.loc 1 364 25 is_stmt 0 view .LVU32
  92 002c 1960     		str	r1, [r3]
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccpDCkRu.s 			page 9


 365:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 366:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	/* To start with there is a single free block that is sized to take up the
 367:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	entire heap space, minus the space taken by pxEnd. */
 368:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	pxFirstFreeBlock = ( void * ) pucAlignedHeap;
  93              		.loc 1 368 2 is_stmt 1 view .LVU33
  94              	.LVL9:
 369:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	pxFirstFreeBlock->xBlockSize = uxAddress - ( size_t ) pxFirstFreeBlock;
  95              		.loc 1 369 2 view .LVU34
  96              		.loc 1 369 43 is_stmt 0 view .LVU35
  97 002e 991A     		subs	r1, r3, r2
  98              		.loc 1 369 31 view .LVU36
  99 0030 5160     		str	r1, [r2, #4]
 370:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	pxFirstFreeBlock->pxNextFreeBlock = pxEnd;
 100              		.loc 1 370 2 is_stmt 1 view .LVU37
 101              		.loc 1 370 36 is_stmt 0 view .LVU38
 102 0032 1360     		str	r3, [r2]
 371:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 372:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	/* Only one block exists - and it covers the entire usable heap space. */
 373:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	xMinimumEverFreeBytesRemaining = pxFirstFreeBlock->xBlockSize;
 103              		.loc 1 373 2 is_stmt 1 view .LVU39
 104              		.loc 1 373 33 is_stmt 0 view .LVU40
 105 0034 084B     		ldr	r3, .L4+12
 106              	.LVL10:
 107              		.loc 1 373 33 view .LVU41
 108 0036 1960     		str	r1, [r3]
 374:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	xFreeBytesRemaining = pxFirstFreeBlock->xBlockSize;
 109              		.loc 1 374 2 is_stmt 1 view .LVU42
 110              		.loc 1 374 22 is_stmt 0 view .LVU43
 111 0038 084B     		ldr	r3, .L4+16
 112 003a 1960     		str	r1, [r3]
 375:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 376:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	/* Work out the position of the top bit in a size_t variable. */
 377:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	xBlockAllocatedBit = ( ( size_t ) 1 ) << ( ( sizeof( size_t ) * heapBITS_PER_BYTE ) - 1 );
 113              		.loc 1 377 2 is_stmt 1 view .LVU44
 114              		.loc 1 377 21 is_stmt 0 view .LVU45
 115 003c 084B     		ldr	r3, .L4+20
 116 003e 4FF00042 		mov	r2, #-2147483648
 117              	.LVL11:
 118              		.loc 1 377 21 view .LVU46
 119 0042 1A60     		str	r2, [r3]
 378:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** }
 120              		.loc 1 378 1 view .LVU47
 121 0044 7047     		bx	lr
 122              	.LVL12:
 123              	.L3:
 338:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 124              		.loc 1 338 8 view .LVU48
 125 0046 4FF47053 		mov	r3, #15360
 126 004a E4E7     		b	.L2
 127              	.L5:
 128              		.align	2
 129              	.L4:
 130 004c 00000000 		.word	ucHeap
 131 0050 00000000 		.word	xStart
 132 0054 00000000 		.word	pxEnd
 133 0058 00000000 		.word	xMinimumEverFreeBytesRemaining
 134 005c 00000000 		.word	xFreeBytesRemaining
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccpDCkRu.s 			page 10


 135 0060 00000000 		.word	xBlockAllocatedBit
 136              		.cfi_endproc
 137              	.LFE9:
 139              		.section	.text.prvInsertBlockIntoFreeList,"ax",%progbits
 140              		.align	1
 141              		.syntax unified
 142              		.thumb
 143              		.thumb_func
 145              	prvInsertBlockIntoFreeList:
 146              	.LVL13:
 147              	.LFB10:
 379:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** /*-----------------------------------------------------------*/
 380:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 381:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** static void prvInsertBlockIntoFreeList( BlockLink_t *pxBlockToInsert )
 382:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** {
 148              		.loc 1 382 1 is_stmt 1 view -0
 149              		.cfi_startproc
 150              		@ args = 0, pretend = 0, frame = 0
 151              		@ frame_needed = 0, uses_anonymous_args = 0
 152              		@ link register save eliminated.
 383:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** BlockLink_t *pxIterator;
 153              		.loc 1 383 1 view .LVU50
 384:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** uint8_t *puc;
 154              		.loc 1 384 1 view .LVU51
 385:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 386:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	/* Iterate through the list until a block is found that has a higher address
 387:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	than the block being inserted. */
 388:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	for( pxIterator = &xStart; pxIterator->pxNextFreeBlock < pxBlockToInsert; pxIterator = pxIterator-
 155              		.loc 1 388 2 view .LVU52
 156              		.loc 1 388 18 is_stmt 0 view .LVU53
 157 0000 164B     		ldr	r3, .L21
 158              	.LVL14:
 159              	.L7:
 160              		.loc 1 388 57 is_stmt 1 discriminator 1 view .LVU54
 161 0002 1A46     		mov	r2, r3
 162              		.loc 1 388 39 is_stmt 0 discriminator 1 view .LVU55
 163 0004 1B68     		ldr	r3, [r3]
 164              	.LVL15:
 165              		.loc 1 388 57 discriminator 1 view .LVU56
 166 0006 8342     		cmp	r3, r0
 167 0008 FBD3     		bcc	.L7
 389:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	{
 390:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		/* Nothing to do here, just iterate to the right position. */
 391:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	}
 392:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 393:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	/* Do the block being inserted, and the block it is being inserted after
 394:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	make a contiguous block of memory? */
 395:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	puc = ( uint8_t * ) pxIterator;
 168              		.loc 1 395 2 is_stmt 1 view .LVU57
 169              	.LVL16:
 396:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	if( ( puc + pxIterator->xBlockSize ) == ( uint8_t * ) pxBlockToInsert )
 170              		.loc 1 396 2 view .LVU58
 171              		.loc 1 396 24 is_stmt 0 view .LVU59
 172 000a 5168     		ldr	r1, [r2, #4]
 173              		.loc 1 396 12 view .LVU60
 174 000c 02EB010C 		add	ip, r2, r1
 175              		.loc 1 396 4 view .LVU61
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccpDCkRu.s 			page 11


 176 0010 8445     		cmp	ip, r0
 177 0012 09D0     		beq	.L19
 178              	.L8:
 397:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	{
 398:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		pxIterator->xBlockSize += pxBlockToInsert->xBlockSize;
 399:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		pxBlockToInsert = pxIterator;
 400:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	}
 401:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	else
 402:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	{
 403:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		mtCOVERAGE_TEST_MARKER();
 179              		.loc 1 403 27 is_stmt 1 view .LVU62
 404:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	}
 405:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 406:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	/* Do the block being inserted, and the block it is being inserted before
 407:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	make a contiguous block of memory? */
 408:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	puc = ( uint8_t * ) pxBlockToInsert;
 180              		.loc 1 408 2 view .LVU63
 181              	.LVL17:
 409:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	if( ( puc + pxBlockToInsert->xBlockSize ) == ( uint8_t * ) pxIterator->pxNextFreeBlock )
 182              		.loc 1 409 2 view .LVU64
 183              		.loc 1 409 29 is_stmt 0 view .LVU65
 184 0014 4168     		ldr	r1, [r0, #4]
 185              		.loc 1 409 12 view .LVU66
 186 0016 00EB010C 		add	ip, r0, r1
 187              		.loc 1 409 4 view .LVU67
 188 001a 6345     		cmp	r3, ip
 189 001c 09D0     		beq	.L20
 410:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	{
 411:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		if( pxIterator->pxNextFreeBlock != pxEnd )
 412:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		{
 413:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 			/* Form one big block from the two blocks. */
 414:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 			pxBlockToInsert->xBlockSize += pxIterator->pxNextFreeBlock->xBlockSize;
 415:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 			pxBlockToInsert->pxNextFreeBlock = pxIterator->pxNextFreeBlock->pxNextFreeBlock;
 416:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		}
 417:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		else
 418:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		{
 419:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 			pxBlockToInsert->pxNextFreeBlock = pxEnd;
 420:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		}
 421:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	}
 422:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	else
 423:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	{
 424:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		pxBlockToInsert->pxNextFreeBlock = pxIterator->pxNextFreeBlock;
 190              		.loc 1 424 3 is_stmt 1 view .LVU68
 191              		.loc 1 424 36 is_stmt 0 view .LVU69
 192 001e 0360     		str	r3, [r0]
 425:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	}
 426:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 427:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	/* If the block being inserted plugged a gab, so was merged with the block
 428:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	before and the block after, then it's pxNextFreeBlock pointer will have
 429:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	already been set, and should not be set here as that would make it point
 430:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	to itself. */
 431:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	if( pxIterator != pxBlockToInsert )
 193              		.loc 1 431 2 is_stmt 1 view .LVU70
 194              		.loc 1 431 4 is_stmt 0 view .LVU71
 195 0020 9042     		cmp	r0, r2
 196 0022 19D0     		beq	.L16
 432:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	{
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccpDCkRu.s 			page 12


 433:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		pxIterator->pxNextFreeBlock = pxBlockToInsert;
 197              		.loc 1 433 3 is_stmt 1 view .LVU72
 198              		.loc 1 433 31 is_stmt 0 view .LVU73
 199 0024 1060     		str	r0, [r2]
 434:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	}
 435:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	else
 436:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	{
 437:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		mtCOVERAGE_TEST_MARKER();
 200              		.loc 1 437 27 is_stmt 1 view .LVU74
 201 0026 7047     		bx	lr
 202              	.LVL18:
 203              	.L19:
 398:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		pxBlockToInsert = pxIterator;
 204              		.loc 1 398 3 view .LVU75
 398:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		pxBlockToInsert = pxIterator;
 205              		.loc 1 398 44 is_stmt 0 view .LVU76
 206 0028 4068     		ldr	r0, [r0, #4]
 207              	.LVL19:
 398:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		pxBlockToInsert = pxIterator;
 208              		.loc 1 398 26 view .LVU77
 209 002a 0144     		add	r1, r1, r0
 210 002c 5160     		str	r1, [r2, #4]
 399:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	}
 211              		.loc 1 399 3 is_stmt 1 view .LVU78
 212              	.LVL20:
 399:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	}
 213              		.loc 1 399 19 is_stmt 0 view .LVU79
 214 002e 1046     		mov	r0, r2
 215 0030 F0E7     		b	.L8
 216              	.LVL21:
 217              	.L20:
 382:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** BlockLink_t *pxIterator;
 218              		.loc 1 382 1 view .LVU80
 219 0032 10B4     		push	{r4}
 220              	.LCFI0:
 221              		.cfi_def_cfa_offset 4
 222              		.cfi_offset 4, -4
 411:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		{
 223              		.loc 1 411 3 is_stmt 1 view .LVU81
 411:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		{
 224              		.loc 1 411 35 is_stmt 0 view .LVU82
 225 0034 0A4C     		ldr	r4, .L21+4
 226 0036 2468     		ldr	r4, [r4]
 411:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		{
 227              		.loc 1 411 5 view .LVU83
 228 0038 A342     		cmp	r3, r4
 229 003a 0BD0     		beq	.L10
 414:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 			pxBlockToInsert->pxNextFreeBlock = pxIterator->pxNextFreeBlock->pxNextFreeBlock;
 230              		.loc 1 414 4 is_stmt 1 view .LVU84
 414:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 			pxBlockToInsert->pxNextFreeBlock = pxIterator->pxNextFreeBlock->pxNextFreeBlock;
 231              		.loc 1 414 62 is_stmt 0 view .LVU85
 232 003c 5B68     		ldr	r3, [r3, #4]
 414:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 			pxBlockToInsert->pxNextFreeBlock = pxIterator->pxNextFreeBlock->pxNextFreeBlock;
 233              		.loc 1 414 32 view .LVU86
 234 003e 1944     		add	r1, r1, r3
 235 0040 4160     		str	r1, [r0, #4]
 415:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		}
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccpDCkRu.s 			page 13


 236              		.loc 1 415 4 is_stmt 1 view .LVU87
 415:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		}
 237              		.loc 1 415 49 is_stmt 0 view .LVU88
 238 0042 1368     		ldr	r3, [r2]
 415:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		}
 239              		.loc 1 415 66 view .LVU89
 240 0044 1B68     		ldr	r3, [r3]
 415:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		}
 241              		.loc 1 415 37 view .LVU90
 242 0046 0360     		str	r3, [r0]
 243              	.L11:
 431:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	{
 244              		.loc 1 431 2 is_stmt 1 view .LVU91
 431:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	{
 245              		.loc 1 431 4 is_stmt 0 view .LVU92
 246 0048 9042     		cmp	r0, r2
 247 004a 00D0     		beq	.L6
 433:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	}
 248              		.loc 1 433 3 is_stmt 1 view .LVU93
 433:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	}
 249              		.loc 1 433 31 is_stmt 0 view .LVU94
 250 004c 1060     		str	r0, [r2]
 251              		.loc 1 437 27 is_stmt 1 view .LVU95
 252              	.L6:
 438:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	}
 439:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** }
 253              		.loc 1 439 1 is_stmt 0 view .LVU96
 254 004e 5DF8044B 		ldr	r4, [sp], #4
 255              	.LCFI1:
 256              		.cfi_remember_state
 257              		.cfi_restore 4
 258              		.cfi_def_cfa_offset 0
 259 0052 7047     		bx	lr
 260              	.L10:
 261              	.LCFI2:
 262              		.cfi_restore_state
 419:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		}
 263              		.loc 1 419 4 is_stmt 1 view .LVU97
 419:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		}
 264              		.loc 1 419 37 is_stmt 0 view .LVU98
 265 0054 0460     		str	r4, [r0]
 266 0056 F7E7     		b	.L11
 267              	.L16:
 268              	.LCFI3:
 269              		.cfi_def_cfa_offset 0
 270              		.cfi_restore 4
 419:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		}
 271              		.loc 1 419 37 view .LVU99
 272 0058 7047     		bx	lr
 273              	.L22:
 274 005a 00BF     		.align	2
 275              	.L21:
 276 005c 00000000 		.word	xStart
 277 0060 00000000 		.word	pxEnd
 278              		.cfi_endproc
 279              	.LFE10:
 281              		.section	.text.pvPortMalloc,"ax",%progbits
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccpDCkRu.s 			page 14


 282              		.align	1
 283              		.global	pvPortMalloc
 284              		.syntax unified
 285              		.thumb
 286              		.thumb_func
 288              	pvPortMalloc:
 289              	.LVL22:
 290              	.LFB4:
 116:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** BlockLink_t *pxBlock, *pxPreviousBlock, *pxNewBlockLink;
 291              		.loc 1 116 1 is_stmt 1 view -0
 292              		.cfi_startproc
 293              		@ args = 0, pretend = 0, frame = 0
 294              		@ frame_needed = 0, uses_anonymous_args = 0
 116:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** BlockLink_t *pxBlock, *pxPreviousBlock, *pxNewBlockLink;
 295              		.loc 1 116 1 is_stmt 0 view .LVU101
 296 0000 38B5     		push	{r3, r4, r5, lr}
 297              	.LCFI4:
 298              		.cfi_def_cfa_offset 16
 299              		.cfi_offset 3, -16
 300              		.cfi_offset 4, -12
 301              		.cfi_offset 5, -8
 302              		.cfi_offset 14, -4
 303 0002 0446     		mov	r4, r0
 117:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** void *pvReturn = NULL;
 304              		.loc 1 117 1 is_stmt 1 view .LVU102
 118:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 305              		.loc 1 118 1 view .LVU103
 306              	.LVL23:
 120:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	{
 307              		.loc 1 120 2 view .LVU104
 308 0004 FFF7FEFF 		bl	vTaskSuspendAll
 309              	.LVL24:
 124:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		{
 310              		.loc 1 124 3 view .LVU105
 124:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		{
 311              		.loc 1 124 13 is_stmt 0 view .LVU106
 312 0008 384B     		ldr	r3, .L43
 313 000a 1B68     		ldr	r3, [r3]
 124:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		{
 314              		.loc 1 124 5 view .LVU107
 315 000c B3B1     		cbz	r3, .L42
 316              	.L24:
 130:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		}
 317              		.loc 1 130 28 is_stmt 1 view .LVU108
 137:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		{
 318              		.loc 1 137 3 view .LVU109
 137:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		{
 319              		.loc 1 137 21 is_stmt 0 view .LVU110
 320 000e 384B     		ldr	r3, .L43+4
 321 0010 1B68     		ldr	r3, [r3]
 137:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		{
 322              		.loc 1 137 5 view .LVU111
 323 0012 1C42     		tst	r4, r3
 324 0014 50D1     		bne	.L35
 141:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 			{
 325              		.loc 1 141 4 is_stmt 1 view .LVU112
 141:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 			{
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccpDCkRu.s 			page 15


 326              		.loc 1 141 6 is_stmt 0 view .LVU113
 327 0016 002C     		cmp	r4, #0
 328 0018 50D0     		beq	.L36
 143:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 329              		.loc 1 143 5 is_stmt 1 view .LVU114
 143:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 330              		.loc 1 143 17 is_stmt 0 view .LVU115
 331 001a 04F10802 		add	r2, r4, #8
 332              	.LVL25:
 147:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 				{
 333              		.loc 1 147 5 is_stmt 1 view .LVU116
 147:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 				{
 334              		.loc 1 147 7 is_stmt 0 view .LVU117
 335 001e 14F0070F 		tst	r4, #7
 336 0022 02D0     		beq	.L26
 150:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 					configASSERT( ( xWantedSize & portBYTE_ALIGNMENT_MASK ) == 0 );
 337              		.loc 1 150 6 is_stmt 1 view .LVU118
 150:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 					configASSERT( ( xWantedSize & portBYTE_ALIGNMENT_MASK ) == 0 );
 338              		.loc 1 150 18 is_stmt 0 view .LVU119
 339 0024 22F00702 		bic	r2, r2, #7
 340              	.LVL26:
 150:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 					configASSERT( ( xWantedSize & portBYTE_ALIGNMENT_MASK ) == 0 );
 341              		.loc 1 150 18 view .LVU120
 342 0028 0832     		adds	r2, r2, #8
 343              	.LVL27:
 151:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 				}
 344              		.loc 1 151 6 is_stmt 1 view .LVU121
 345              	.L26:
 160:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 			}
 346              		.loc 1 160 29 view .LVU122
 163:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 			{
 347              		.loc 1 163 4 view .LVU123
 163:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 			{
 348              		.loc 1 163 6 is_stmt 0 view .LVU124
 349 002a 002A     		cmp	r2, #0
 350 002c 55D0     		beq	.L37
 163:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 			{
 351              		.loc 1 163 45 discriminator 1 view .LVU125
 352 002e 314B     		ldr	r3, .L43+8
 353 0030 1B68     		ldr	r3, [r3]
 163:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 			{
 354              		.loc 1 163 28 discriminator 1 view .LVU126
 355 0032 9342     		cmp	r3, r2
 356 0034 53D3     		bcc	.L38
 167:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 				pxBlock = xStart.pxNextFreeBlock;
 357              		.loc 1 167 5 is_stmt 1 view .LVU127
 358              	.LVL28:
 168:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 				while( ( pxBlock->xBlockSize < xWantedSize ) && ( pxBlock->pxNextFreeBlock != NULL ) )
 359              		.loc 1 168 5 view .LVU128
 168:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 				while( ( pxBlock->xBlockSize < xWantedSize ) && ( pxBlock->pxNextFreeBlock != NULL ) )
 360              		.loc 1 168 13 is_stmt 0 view .LVU129
 361 0036 3049     		ldr	r1, .L43+12
 362 0038 0C68     		ldr	r4, [r1]
 363              	.LVL29:
 169:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 				{
 364              		.loc 1 169 5 is_stmt 1 view .LVU130
 169:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 				{
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccpDCkRu.s 			page 16


 365              		.loc 1 169 10 is_stmt 0 view .LVU131
 366 003a 04E0     		b	.L28
 367              	.LVL30:
 368              	.L42:
 126:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		}
 369              		.loc 1 126 4 is_stmt 1 view .LVU132
 370 003c FFF7FEFF 		bl	prvHeapInit
 371              	.LVL31:
 372 0040 E5E7     		b	.L24
 373              	.LVL32:
 374              	.L39:
 171:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 					pxBlock = pxBlock->pxNextFreeBlock;
 375              		.loc 1 171 22 is_stmt 0 view .LVU133
 376 0042 2146     		mov	r1, r4
 377              	.LVL33:
 172:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 				}
 378              		.loc 1 172 14 view .LVU134
 379 0044 1C46     		mov	r4, r3
 380              	.LVL34:
 381              	.L28:
 169:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 				{
 382              		.loc 1 169 50 is_stmt 1 view .LVU135
 169:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 				{
 383              		.loc 1 169 21 is_stmt 0 view .LVU136
 384 0046 6368     		ldr	r3, [r4, #4]
 169:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 				{
 385              		.loc 1 169 50 view .LVU137
 386 0048 9342     		cmp	r3, r2
 387 004a 02D2     		bcs	.L27
 169:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 				{
 388              		.loc 1 169 62 discriminator 1 view .LVU138
 389 004c 2368     		ldr	r3, [r4]
 169:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 				{
 390              		.loc 1 169 50 discriminator 1 view .LVU139
 391 004e 002B     		cmp	r3, #0
 392 0050 F7D1     		bne	.L39
 393              	.L27:
 177:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 				{
 394              		.loc 1 177 5 is_stmt 1 view .LVU140
 177:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 				{
 395              		.loc 1 177 17 is_stmt 0 view .LVU141
 396 0052 264B     		ldr	r3, .L43
 397 0054 1B68     		ldr	r3, [r3]
 177:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 				{
 398              		.loc 1 177 7 view .LVU142
 399 0056 A342     		cmp	r3, r4
 400 0058 43D0     		beq	.L40
 181:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 401              		.loc 1 181 6 is_stmt 1 view .LVU143
 181:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 402              		.loc 1 181 61 is_stmt 0 view .LVU144
 403 005a 0D68     		ldr	r5, [r1]
 181:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 404              		.loc 1 181 15 view .LVU145
 405 005c 0835     		adds	r5, r5, #8
 406              	.LVL35:
 185:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccpDCkRu.s 			page 17


 407              		.loc 1 185 6 is_stmt 1 view .LVU146
 185:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 408              		.loc 1 185 48 is_stmt 0 view .LVU147
 409 005e 2368     		ldr	r3, [r4]
 185:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 410              		.loc 1 185 39 view .LVU148
 411 0060 0B60     		str	r3, [r1]
 189:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 					{
 412              		.loc 1 189 6 is_stmt 1 view .LVU149
 189:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 					{
 413              		.loc 1 189 19 is_stmt 0 view .LVU150
 414 0062 6368     		ldr	r3, [r4, #4]
 189:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 					{
 415              		.loc 1 189 32 view .LVU151
 416 0064 9B1A     		subs	r3, r3, r2
 189:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 					{
 417              		.loc 1 189 8 view .LVU152
 418 0066 102B     		cmp	r3, #16
 419 0068 10D9     		bls	.L29
 195:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 						configASSERT( ( ( ( size_t ) pxNewBlockLink ) & portBYTE_ALIGNMENT_MASK ) == 0 );
 420              		.loc 1 195 7 is_stmt 1 view .LVU153
 195:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 						configASSERT( ( ( ( size_t ) pxNewBlockLink ) & portBYTE_ALIGNMENT_MASK ) == 0 );
 421              		.loc 1 195 22 is_stmt 0 view .LVU154
 422 006a A018     		adds	r0, r4, r2
 423              	.LVL36:
 196:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 424              		.loc 1 196 7 is_stmt 1 view .LVU155
 425 006c 10F0070F 		tst	r0, #7
 426 0070 08D0     		beq	.L30
 196:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 427              		.loc 1 196 7 discriminator 1 view .LVU156
 428              	.LBB12:
 429              	.LBI12:
 430              		.file 2 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h"
   1:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /*
   2:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * FreeRTOS Kernel V10.3.1
   3:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * Copyright (C) 2020 Amazon.com, Inc. or its affiliates.  All Rights Reserved.
   4:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  *
   5:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * Permission is hereby granted, free of charge, to any person obtaining a copy of
   6:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * this software and associated documentation files (the "Software"), to deal in
   7:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * the Software without restriction, including without limitation the rights to
   8:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of
   9:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * the Software, and to permit persons to whom the Software is furnished to do so,
  10:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * subject to the following conditions:
  11:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  *
  12:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * The above copyright notice and this permission notice shall be included in all
  13:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * copies or substantial portions of the Software.
  14:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  *
  15:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
  16:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS
  17:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR
  18:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER
  19:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
  20:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
  21:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  *
  22:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * http://www.FreeRTOS.org
  23:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * http://aws.amazon.com/freertos
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccpDCkRu.s 			page 18


  24:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  *
  25:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * 1 tab == 4 spaces!
  26:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  */
  27:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
  28:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
  29:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #ifndef PORTMACRO_H
  30:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define PORTMACRO_H
  31:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
  32:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #ifdef __cplusplus
  33:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** extern "C" {
  34:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #endif
  35:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
  36:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /*-----------------------------------------------------------
  37:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * Port specific definitions.
  38:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  *
  39:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * The settings in this file configure FreeRTOS correctly for the
  40:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * given hardware and compiler.
  41:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  *
  42:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * These settings should not be altered.
  43:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  *-----------------------------------------------------------
  44:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  */
  45:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
  46:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /* Type definitions. */
  47:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portCHAR		char
  48:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portFLOAT		float
  49:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portDOUBLE		double
  50:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portLONG		long
  51:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portSHORT		short
  52:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portSTACK_TYPE	uint32_t
  53:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portBASE_TYPE	long
  54:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
  55:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** typedef portSTACK_TYPE StackType_t;
  56:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** typedef long BaseType_t;
  57:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** typedef unsigned long UBaseType_t;
  58:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
  59:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #if( configUSE_16_BIT_TICKS == 1 )
  60:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	typedef uint16_t TickType_t;
  61:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	#define portMAX_DELAY ( TickType_t ) 0xffff
  62:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #else
  63:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	typedef uint32_t TickType_t;
  64:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	#define portMAX_DELAY ( TickType_t ) 0xffffffffUL
  65:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
  66:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	/* 32-bit tick type on a 32-bit architecture, so reads of the tick count do
  67:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	not need to be guarded with a critical section. */
  68:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	#define portTICK_TYPE_IS_ATOMIC 1
  69:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #endif
  70:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /*-----------------------------------------------------------*/
  71:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
  72:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /* Architecture specifics. */
  73:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portSTACK_GROWTH			( -1 )
  74:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portTICK_PERIOD_MS			( ( TickType_t ) 1000 / configTICK_RATE_HZ )
  75:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portBYTE_ALIGNMENT			8
  76:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /*-----------------------------------------------------------*/
  77:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
  78:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /* Scheduler utilities. */
  79:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portYIELD() 															\
  80:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {																				\
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccpDCkRu.s 			page 19


  81:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	/* Set a PendSV to request a context switch. */								\
  82:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	portNVIC_INT_CTRL_REG = portNVIC_PENDSVSET_BIT;								\
  83:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 																				\
  84:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	/* Barriers are normally not required but do ensure the code is completely	\
  85:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	within the specified behaviour for the architecture. */						\
  86:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	__asm volatile( "dsb" ::: "memory" );										\
  87:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	__asm volatile( "isb" );													\
  88:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** }
  89:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
  90:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portNVIC_INT_CTRL_REG		( * ( ( volatile uint32_t * ) 0xe000ed04 ) )
  91:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portNVIC_PENDSVSET_BIT		( 1UL << 28UL )
  92:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portEND_SWITCHING_ISR( xSwitchRequired ) if( xSwitchRequired != pdFALSE ) portYIELD()
  93:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portYIELD_FROM_ISR( x ) portEND_SWITCHING_ISR( x )
  94:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /*-----------------------------------------------------------*/
  95:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
  96:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /* Critical section management. */
  97:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** extern void vPortEnterCritical( void );
  98:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** extern void vPortExitCritical( void );
  99:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portSET_INTERRUPT_MASK_FROM_ISR()		ulPortRaiseBASEPRI()
 100:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portCLEAR_INTERRUPT_MASK_FROM_ISR(x)	vPortSetBASEPRI(x)
 101:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portDISABLE_INTERRUPTS()				vPortRaiseBASEPRI()
 102:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portENABLE_INTERRUPTS()					vPortSetBASEPRI(0)
 103:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portENTER_CRITICAL()					vPortEnterCritical()
 104:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portEXIT_CRITICAL()						vPortExitCritical()
 105:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 106:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /*-----------------------------------------------------------*/
 107:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 108:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /* Task function macros as described on the FreeRTOS.org WEB site.  These are
 109:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** not necessary for to use this port.  They are defined so the common demo files
 110:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** (which build with all the ports) will build. */
 111:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portTASK_FUNCTION_PROTO( vFunction, pvParameters ) void vFunction( void *pvParameters )
 112:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portTASK_FUNCTION( vFunction, pvParameters ) void vFunction( void *pvParameters )
 113:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /*-----------------------------------------------------------*/
 114:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 115:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /* Tickless idle/low power functionality. */
 116:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #ifndef portSUPPRESS_TICKS_AND_SLEEP
 117:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	extern void vPortSuppressTicksAndSleep( TickType_t xExpectedIdleTime );
 118:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	#define portSUPPRESS_TICKS_AND_SLEEP( xExpectedIdleTime ) vPortSuppressTicksAndSleep( xExpectedIdl
 119:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #endif
 120:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /*-----------------------------------------------------------*/
 121:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 122:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /* Architecture specific optimisations. */
 123:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #ifndef configUSE_PORT_OPTIMISED_TASK_SELECTION
 124:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	#define configUSE_PORT_OPTIMISED_TASK_SELECTION 1
 125:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #endif
 126:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 127:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #if configUSE_PORT_OPTIMISED_TASK_SELECTION == 1
 128:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 129:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	/* Generic helper function. */
 130:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	__attribute__( ( always_inline ) ) static inline uint8_t ucPortCountLeadingZeros( uint32_t ulBitma
 131:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	{
 132:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	uint8_t ucReturn;
 133:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 134:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 		__asm volatile ( "clz %0, %1" : "=r" ( ucReturn ) : "r" ( ulBitmap ) : "memory" );
 135:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 		return ucReturn;
 136:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	}
 137:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccpDCkRu.s 			page 20


 138:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	/* Check the configuration. */
 139:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	#if( configMAX_PRIORITIES > 32 )
 140:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 		#error configUSE_PORT_OPTIMISED_TASK_SELECTION can only be set to 1 when configMAX_PRIORITIES is 
 141:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	#endif
 142:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 143:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	/* Store/clear the ready priorities in a bit map. */
 144:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	#define portRECORD_READY_PRIORITY( uxPriority, uxReadyPriorities ) ( uxReadyPriorities ) |= ( 1UL 
 145:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	#define portRESET_READY_PRIORITY( uxPriority, uxReadyPriorities ) ( uxReadyPriorities ) &= ~( 1UL 
 146:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 147:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	/*-----------------------------------------------------------*/
 148:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 149:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	#define portGET_HIGHEST_PRIORITY( uxTopPriority, uxReadyPriorities ) uxTopPriority = ( 31UL - ( ui
 150:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 151:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #endif /* configUSE_PORT_OPTIMISED_TASK_SELECTION */
 152:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 153:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /*-----------------------------------------------------------*/
 154:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 155:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #ifdef configASSERT
 156:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	void vPortValidateInterruptPriority( void );
 157:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	#define portASSERT_IF_INTERRUPT_PRIORITY_INVALID() 	vPortValidateInterruptPriority()
 158:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #endif
 159:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 160:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /* portNOP() is not required by this port. */
 161:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portNOP()
 162:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 163:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portINLINE	__inline
 164:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 165:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #ifndef portFORCE_INLINE
 166:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	#define portFORCE_INLINE inline __attribute__(( always_inline))
 167:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #endif
 168:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 169:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** portFORCE_INLINE static BaseType_t xPortIsInsideInterrupt( void )
 170:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 171:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** uint32_t ulCurrentInterrupt;
 172:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** BaseType_t xReturn;
 173:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 174:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	/* Obtain the number of the currently executing interrupt. */
 175:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	__asm volatile( "mrs %0, ipsr" : "=r"( ulCurrentInterrupt ) :: "memory" );
 176:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 177:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	if( ulCurrentInterrupt == 0 )
 178:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	{
 179:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 		xReturn = pdFALSE;
 180:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	}
 181:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	else
 182:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	{
 183:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 		xReturn = pdTRUE;
 184:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	}
 185:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 186:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	return xReturn;
 187:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** }
 188:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 189:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /*-----------------------------------------------------------*/
 190:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** portFORCE_INLINE static void vPortRaiseBASEPRI( void )
 431              		.loc 2 191 30 view .LVU157
 432              	.LBB13:
 192:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccpDCkRu.s 			page 21


 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** uint32_t ulNewBASEPRI;
 433              		.loc 2 193 1 view .LVU158
 194:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 195:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	__asm volatile
 434              		.loc 2 195 2 view .LVU159
 435              		.syntax unified
 436              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 437 0072 4FF05003 			mov r3, #80												
 438 0076 83F31188 		msr basepri, r3											
 439 007a BFF36F8F 		isb														
 440 007e BFF34F8F 		dsb														
 441              	
 442              	@ 0 "" 2
 443              		.thumb
 444              		.syntax unified
 445              	.L31:
 446              	.LBE13:
 447              	.LBE12:
 196:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 448              		.loc 1 196 7 discriminator 3 view .LVU160
 196:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 449              		.loc 1 196 7 discriminator 3 view .LVU161
 450 0082 FEE7     		b	.L31
 451              	.L30:
 196:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 452              		.loc 1 196 87 discriminator 2 view .LVU162
 200:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 						pxBlock->xBlockSize = xWantedSize;
 453              		.loc 1 200 7 view .LVU163
 200:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 						pxBlock->xBlockSize = xWantedSize;
 454              		.loc 1 200 34 is_stmt 0 view .LVU164
 455 0084 4360     		str	r3, [r0, #4]
 201:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 456              		.loc 1 201 7 is_stmt 1 view .LVU165
 201:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 457              		.loc 1 201 27 is_stmt 0 view .LVU166
 458 0086 6260     		str	r2, [r4, #4]
 204:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 					}
 459              		.loc 1 204 7 is_stmt 1 view .LVU167
 460 0088 FFF7FEFF 		bl	prvInsertBlockIntoFreeList
 461              	.LVL37:
 462              	.L29:
 208:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 					}
 463              		.loc 1 208 31 view .LVU168
 211:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 464              		.loc 1 211 6 view .LVU169
 211:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 465              		.loc 1 211 36 is_stmt 0 view .LVU170
 466 008c 6268     		ldr	r2, [r4, #4]
 211:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 467              		.loc 1 211 26 view .LVU171
 468 008e 1949     		ldr	r1, .L43+8
 469 0090 0B68     		ldr	r3, [r1]
 470 0092 9B1A     		subs	r3, r3, r2
 471 0094 0B60     		str	r3, [r1]
 213:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 					{
 472              		.loc 1 213 6 is_stmt 1 view .LVU172
 213:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 					{
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccpDCkRu.s 			page 22


 473              		.loc 1 213 30 is_stmt 0 view .LVU173
 474 0096 1949     		ldr	r1, .L43+16
 475 0098 0968     		ldr	r1, [r1]
 213:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 					{
 476              		.loc 1 213 8 view .LVU174
 477 009a 8B42     		cmp	r3, r1
 478 009c 01D2     		bcs	.L32
 215:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 					}
 479              		.loc 1 215 7 is_stmt 1 view .LVU175
 215:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 					}
 480              		.loc 1 215 38 is_stmt 0 view .LVU176
 481 009e 1749     		ldr	r1, .L43+16
 482 00a0 0B60     		str	r3, [r1]
 483              	.L32:
 219:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 					}
 484              		.loc 1 219 31 is_stmt 1 view .LVU177
 224:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 					pxBlock->pxNextFreeBlock = NULL;
 485              		.loc 1 224 6 view .LVU178
 224:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 					pxBlock->pxNextFreeBlock = NULL;
 486              		.loc 1 224 26 is_stmt 0 view .LVU179
 487 00a2 134B     		ldr	r3, .L43+4
 488 00a4 1B68     		ldr	r3, [r3]
 489 00a6 1343     		orrs	r3, r3, r2
 490 00a8 6360     		str	r3, [r4, #4]
 225:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 					xNumberOfSuccessfulAllocations++;
 491              		.loc 1 225 6 is_stmt 1 view .LVU180
 225:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 					xNumberOfSuccessfulAllocations++;
 492              		.loc 1 225 31 is_stmt 0 view .LVU181
 493 00aa 0023     		movs	r3, #0
 494 00ac 2360     		str	r3, [r4]
 226:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 				}
 495              		.loc 1 226 6 is_stmt 1 view .LVU182
 226:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 				}
 496              		.loc 1 226 36 is_stmt 0 view .LVU183
 497 00ae 144A     		ldr	r2, .L43+20
 498 00b0 1368     		ldr	r3, [r2]
 499 00b2 0133     		adds	r3, r3, #1
 500 00b4 1360     		str	r3, [r2]
 501 00b6 02E0     		b	.L25
 502              	.LVL38:
 503              	.L35:
 118:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 504              		.loc 1 118 7 view .LVU184
 505 00b8 0025     		movs	r5, #0
 506 00ba 00E0     		b	.L25
 507              	.L36:
 508 00bc 0025     		movs	r5, #0
 509              	.LVL39:
 510              	.L25:
 240:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		}
 511              		.loc 1 240 28 is_stmt 1 view .LVU185
 243:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	}
 512              		.loc 1 243 39 view .LVU186
 245:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 513              		.loc 1 245 2 view .LVU187
 245:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 514              		.loc 1 245 11 is_stmt 0 view .LVU188
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccpDCkRu.s 			page 23


 515 00be FFF7FEFF 		bl	xTaskResumeAll
 516              	.LVL40:
 261:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	return pvReturn;
 517              		.loc 1 261 2 is_stmt 1 view .LVU189
 518 00c2 15F0070F 		tst	r5, #7
 519 00c6 0ED0     		beq	.L23
 261:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	return pvReturn;
 520              		.loc 1 261 2 discriminator 1 view .LVU190
 521              	.LBB14:
 522              	.LBI14:
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 523              		.loc 2 191 30 view .LVU191
 524              	.LBB15:
 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 525              		.loc 2 193 1 view .LVU192
 526              		.loc 2 195 2 view .LVU193
 527              		.syntax unified
 528              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 529 00c8 4FF05003 			mov r3, #80												
 530 00cc 83F31188 		msr basepri, r3											
 531 00d0 BFF36F8F 		isb														
 532 00d4 BFF34F8F 		dsb														
 533              	
 534              	@ 0 "" 2
 535              		.thumb
 536              		.syntax unified
 537              	.L34:
 538              	.LBE15:
 539              	.LBE14:
 261:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	return pvReturn;
 540              		.loc 1 261 2 discriminator 3 view .LVU194
 261:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	return pvReturn;
 541              		.loc 1 261 2 discriminator 3 view .LVU195
 542 00d8 FEE7     		b	.L34
 543              	.LVL41:
 544              	.L37:
 118:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 545              		.loc 1 118 7 is_stmt 0 view .LVU196
 546 00da 0025     		movs	r5, #0
 547 00dc EFE7     		b	.L25
 548              	.L38:
 549 00de 0025     		movs	r5, #0
 550 00e0 EDE7     		b	.L25
 551              	.LVL42:
 552              	.L40:
 118:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 553              		.loc 1 118 7 view .LVU197
 554 00e2 0025     		movs	r5, #0
 555 00e4 EBE7     		b	.L25
 556              	.LVL43:
 557              	.L23:
 263:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** /*-----------------------------------------------------------*/
 558              		.loc 1 263 1 view .LVU198
 559 00e6 2846     		mov	r0, r5
 560 00e8 38BD     		pop	{r3, r4, r5, pc}
 561              	.LVL44:
 562              	.L44:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccpDCkRu.s 			page 24


 263:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** /*-----------------------------------------------------------*/
 563              		.loc 1 263 1 view .LVU199
 564 00ea 00BF     		.align	2
 565              	.L43:
 566 00ec 00000000 		.word	pxEnd
 567 00f0 00000000 		.word	xBlockAllocatedBit
 568 00f4 00000000 		.word	xFreeBytesRemaining
 569 00f8 00000000 		.word	xStart
 570 00fc 00000000 		.word	xMinimumEverFreeBytesRemaining
 571 0100 00000000 		.word	xNumberOfSuccessfulAllocations
 572              		.cfi_endproc
 573              	.LFE4:
 575              		.section	.text.vPortFree,"ax",%progbits
 576              		.align	1
 577              		.global	vPortFree
 578              		.syntax unified
 579              		.thumb
 580              		.thumb_func
 582              	vPortFree:
 583              	.LVL45:
 584              	.LFB5:
 267:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** uint8_t *puc = ( uint8_t * ) pv;
 585              		.loc 1 267 1 is_stmt 1 view -0
 586              		.cfi_startproc
 587              		@ args = 0, pretend = 0, frame = 0
 588              		@ frame_needed = 0, uses_anonymous_args = 0
 268:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** BlockLink_t *pxLink;
 589              		.loc 1 268 1 view .LVU201
 269:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 590              		.loc 1 269 1 view .LVU202
 271:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	{
 591              		.loc 1 271 2 view .LVU203
 271:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	{
 592              		.loc 1 271 4 is_stmt 0 view .LVU204
 593 0000 0028     		cmp	r0, #0
 594 0002 34D0     		beq	.L52
 267:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** uint8_t *puc = ( uint8_t * ) pv;
 595              		.loc 1 267 1 view .LVU205
 596 0004 38B5     		push	{r3, r4, r5, lr}
 597              	.LCFI5:
 598              		.cfi_def_cfa_offset 16
 599              		.cfi_offset 3, -16
 600              		.cfi_offset 4, -12
 601              		.cfi_offset 5, -8
 602              		.cfi_offset 14, -4
 603 0006 0446     		mov	r4, r0
 275:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 604              		.loc 1 275 3 is_stmt 1 view .LVU206
 275:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 605              		.loc 1 275 7 is_stmt 0 view .LVU207
 606 0008 A0F10805 		sub	r5, r0, #8
 607              	.LVL46:
 278:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 608              		.loc 1 278 3 is_stmt 1 view .LVU208
 281:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		configASSERT( pxLink->pxNextFreeBlock == NULL );
 609              		.loc 1 281 3 view .LVU209
 610 000c 50F8043C 		ldr	r3, [r0, #-4]
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccpDCkRu.s 			page 25


 611 0010 174A     		ldr	r2, .L55
 612 0012 1268     		ldr	r2, [r2]
 613 0014 1342     		tst	r3, r2
 614 0016 08D1     		bne	.L47
 281:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		configASSERT( pxLink->pxNextFreeBlock == NULL );
 615              		.loc 1 281 3 discriminator 1 view .LVU210
 616              	.LBB16:
 617              	.LBI16:
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 618              		.loc 2 191 30 view .LVU211
 619              	.LBB17:
 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 620              		.loc 2 193 1 view .LVU212
 621              		.loc 2 195 2 view .LVU213
 622              		.syntax unified
 623              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 624 0018 4FF05003 			mov r3, #80												
 625 001c 83F31188 		msr basepri, r3											
 626 0020 BFF36F8F 		isb														
 627 0024 BFF34F8F 		dsb														
 628              	
 629              	@ 0 "" 2
 630              		.thumb
 631              		.syntax unified
 632              	.L48:
 633              	.LBE17:
 634              	.LBE16:
 281:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		configASSERT( pxLink->pxNextFreeBlock == NULL );
 635              		.loc 1 281 3 discriminator 3 view .LVU214
 281:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		configASSERT( pxLink->pxNextFreeBlock == NULL );
 636              		.loc 1 281 3 discriminator 3 view .LVU215
 637 0028 FEE7     		b	.L48
 638              	.L47:
 281:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		configASSERT( pxLink->pxNextFreeBlock == NULL );
 639              		.loc 1 281 67 discriminator 2 view .LVU216
 282:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 640              		.loc 1 282 3 view .LVU217
 641 002a 50F8081C 		ldr	r1, [r0, #-8]
 642 002e 41B1     		cbz	r1, .L49
 282:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 643              		.loc 1 282 3 discriminator 1 view .LVU218
 644              	.LBB18:
 645              	.LBI18:
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 646              		.loc 2 191 30 view .LVU219
 647              	.LBB19:
 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 648              		.loc 2 193 1 view .LVU220
 649              		.loc 2 195 2 view .LVU221
 650              		.syntax unified
 651              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 652 0030 4FF05003 			mov r3, #80												
 653 0034 83F31188 		msr basepri, r3											
 654 0038 BFF36F8F 		isb														
 655 003c BFF34F8F 		dsb														
 656              	
 657              	@ 0 "" 2
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccpDCkRu.s 			page 26


 658              		.thumb
 659              		.syntax unified
 660              	.L50:
 661              	.LBE19:
 662              	.LBE18:
 282:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 663              		.loc 1 282 3 discriminator 3 view .LVU222
 282:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 664              		.loc 1 282 3 discriminator 3 view .LVU223
 665 0040 FEE7     		b	.L50
 666              	.L49:
 282:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 667              		.loc 1 282 50 discriminator 2 view .LVU224
 284:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		{
 668              		.loc 1 284 3 view .LVU225
 286:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 			{
 669              		.loc 1 286 4 view .LVU226
 290:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 670              		.loc 1 290 5 view .LVU227
 290:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 671              		.loc 1 290 24 is_stmt 0 view .LVU228
 672 0042 23EA0203 		bic	r3, r3, r2
 673 0046 40F8043C 		str	r3, [r0, #-4]
 292:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 				{
 674              		.loc 1 292 5 is_stmt 1 view .LVU229
 675 004a FFF7FEFF 		bl	vTaskSuspendAll
 676              	.LVL47:
 295:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 					traceFREE( pv, pxLink->xBlockSize );
 677              		.loc 1 295 6 view .LVU230
 295:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 					traceFREE( pv, pxLink->xBlockSize );
 678              		.loc 1 295 35 is_stmt 0 view .LVU231
 679 004e 54F8041C 		ldr	r1, [r4, #-4]
 295:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 					traceFREE( pv, pxLink->xBlockSize );
 680              		.loc 1 295 26 view .LVU232
 681 0052 084A     		ldr	r2, .L55+4
 682 0054 1368     		ldr	r3, [r2]
 683 0056 0B44     		add	r3, r3, r1
 684 0058 1360     		str	r3, [r2]
 296:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 					prvInsertBlockIntoFreeList( ( ( BlockLink_t * ) pxLink ) );
 685              		.loc 1 296 41 is_stmt 1 view .LVU233
 297:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 					xNumberOfSuccessfulFrees++;
 686              		.loc 1 297 6 view .LVU234
 687 005a 2846     		mov	r0, r5
 688 005c FFF7FEFF 		bl	prvInsertBlockIntoFreeList
 689              	.LVL48:
 298:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 				}
 690              		.loc 1 298 6 view .LVU235
 298:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 				}
 691              		.loc 1 298 30 is_stmt 0 view .LVU236
 692 0060 054A     		ldr	r2, .L55+8
 693 0062 1368     		ldr	r3, [r2]
 694 0064 0133     		adds	r3, r3, #1
 695 0066 1360     		str	r3, [r2]
 300:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 			}
 696              		.loc 1 300 5 is_stmt 1 view .LVU237
 300:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 			}
 697              		.loc 1 300 14 is_stmt 0 view .LVU238
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccpDCkRu.s 			page 27


 698 0068 FFF7FEFF 		bl	xTaskResumeAll
 699              	.LVL49:
 309:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		}
 700              		.loc 1 309 28 is_stmt 1 view .LVU239
 312:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** /*-----------------------------------------------------------*/
 701              		.loc 1 312 1 is_stmt 0 view .LVU240
 702 006c 38BD     		pop	{r3, r4, r5, pc}
 703              	.LVL50:
 704              	.L52:
 705              	.LCFI6:
 706              		.cfi_def_cfa_offset 0
 707              		.cfi_restore 3
 708              		.cfi_restore 4
 709              		.cfi_restore 5
 710              		.cfi_restore 14
 312:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** /*-----------------------------------------------------------*/
 711              		.loc 1 312 1 view .LVU241
 712 006e 7047     		bx	lr
 713              	.L56:
 714              		.align	2
 715              	.L55:
 716 0070 00000000 		.word	xBlockAllocatedBit
 717 0074 00000000 		.word	xFreeBytesRemaining
 718 0078 00000000 		.word	xNumberOfSuccessfulFrees
 719              		.cfi_endproc
 720              	.LFE5:
 722              		.section	.text.xPortGetFreeHeapSize,"ax",%progbits
 723              		.align	1
 724              		.global	xPortGetFreeHeapSize
 725              		.syntax unified
 726              		.thumb
 727              		.thumb_func
 729              	xPortGetFreeHeapSize:
 730              	.LFB6:
 316:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	return xFreeBytesRemaining;
 731              		.loc 1 316 1 is_stmt 1 view -0
 732              		.cfi_startproc
 733              		@ args = 0, pretend = 0, frame = 0
 734              		@ frame_needed = 0, uses_anonymous_args = 0
 735              		@ link register save eliminated.
 317:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** }
 736              		.loc 1 317 2 view .LVU243
 318:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** /*-----------------------------------------------------------*/
 737              		.loc 1 318 1 is_stmt 0 view .LVU244
 738 0000 014B     		ldr	r3, .L58
 739 0002 1868     		ldr	r0, [r3]
 740 0004 7047     		bx	lr
 741              	.L59:
 742 0006 00BF     		.align	2
 743              	.L58:
 744 0008 00000000 		.word	xFreeBytesRemaining
 745              		.cfi_endproc
 746              	.LFE6:
 748              		.section	.text.xPortGetMinimumEverFreeHeapSize,"ax",%progbits
 749              		.align	1
 750              		.global	xPortGetMinimumEverFreeHeapSize
 751              		.syntax unified
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccpDCkRu.s 			page 28


 752              		.thumb
 753              		.thumb_func
 755              	xPortGetMinimumEverFreeHeapSize:
 756              	.LFB7:
 322:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	return xMinimumEverFreeBytesRemaining;
 757              		.loc 1 322 1 is_stmt 1 view -0
 758              		.cfi_startproc
 759              		@ args = 0, pretend = 0, frame = 0
 760              		@ frame_needed = 0, uses_anonymous_args = 0
 761              		@ link register save eliminated.
 323:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** }
 762              		.loc 1 323 2 view .LVU246
 324:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** /*-----------------------------------------------------------*/
 763              		.loc 1 324 1 is_stmt 0 view .LVU247
 764 0000 014B     		ldr	r3, .L61
 765 0002 1868     		ldr	r0, [r3]
 766 0004 7047     		bx	lr
 767              	.L62:
 768 0006 00BF     		.align	2
 769              	.L61:
 770 0008 00000000 		.word	xMinimumEverFreeBytesRemaining
 771              		.cfi_endproc
 772              	.LFE7:
 774              		.section	.text.vPortInitialiseBlocks,"ax",%progbits
 775              		.align	1
 776              		.global	vPortInitialiseBlocks
 777              		.syntax unified
 778              		.thumb
 779              		.thumb_func
 781              	vPortInitialiseBlocks:
 782              	.LFB8:
 328:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	/* This just exists to keep the linker quiet. */
 783              		.loc 1 328 1 is_stmt 1 view -0
 784              		.cfi_startproc
 785              		@ args = 0, pretend = 0, frame = 0
 786              		@ frame_needed = 0, uses_anonymous_args = 0
 787              		@ link register save eliminated.
 330:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** /*-----------------------------------------------------------*/
 788              		.loc 1 330 1 view .LVU249
 789 0000 7047     		bx	lr
 790              		.cfi_endproc
 791              	.LFE8:
 793              		.section	.text.vPortGetHeapStats,"ax",%progbits
 794              		.align	1
 795              		.global	vPortGetHeapStats
 796              		.syntax unified
 797              		.thumb
 798              		.thumb_func
 800              	vPortGetHeapStats:
 801              	.LVL51:
 802              	.LFB11:
 440:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** /*-----------------------------------------------------------*/
 441:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 442:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** void vPortGetHeapStats( HeapStats_t *pxHeapStats )
 443:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** {
 803              		.loc 1 443 1 view -0
 804              		.cfi_startproc
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccpDCkRu.s 			page 29


 805              		@ args = 0, pretend = 0, frame = 0
 806              		@ frame_needed = 0, uses_anonymous_args = 0
 807              		.loc 1 443 1 is_stmt 0 view .LVU251
 808 0000 F8B5     		push	{r3, r4, r5, r6, r7, lr}
 809              	.LCFI7:
 810              		.cfi_def_cfa_offset 24
 811              		.cfi_offset 3, -24
 812              		.cfi_offset 4, -20
 813              		.cfi_offset 5, -16
 814              		.cfi_offset 6, -12
 815              		.cfi_offset 7, -8
 816              		.cfi_offset 14, -4
 817 0002 0746     		mov	r7, r0
 444:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** BlockLink_t *pxBlock;
 818              		.loc 1 444 1 is_stmt 1 view .LVU252
 445:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** size_t xBlocks = 0, xMaxSize = 0, xMinSize = portMAX_DELAY; /* portMAX_DELAY used as a portable way
 819              		.loc 1 445 1 view .LVU253
 820              	.LVL52:
 446:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 447:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	vTaskSuspendAll();
 821              		.loc 1 447 2 view .LVU254
 822 0004 FFF7FEFF 		bl	vTaskSuspendAll
 823              	.LVL53:
 448:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	{
 449:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		pxBlock = xStart.pxNextFreeBlock;
 824              		.loc 1 449 3 view .LVU255
 825              		.loc 1 449 11 is_stmt 0 view .LVU256
 826 0008 174B     		ldr	r3, .L73
 827 000a 1B68     		ldr	r3, [r3]
 828              	.LVL54:
 450:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 451:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		/* pxBlock will be NULL if the heap has not been initialised.  The heap
 452:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		is initialised automatically when the first allocation is made. */
 453:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		if( pxBlock != NULL )
 829              		.loc 1 453 3 is_stmt 1 view .LVU257
 830              		.loc 1 453 5 is_stmt 0 view .LVU258
 831 000c 93B1     		cbz	r3, .L69
 445:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 832              		.loc 1 445 35 view .LVU259
 833 000e 4FF0FF36 		mov	r6, #-1
 445:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 834              		.loc 1 445 21 view .LVU260
 835 0012 0025     		movs	r5, #0
 445:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 836              		.loc 1 445 8 view .LVU261
 837 0014 2C46     		mov	r4, r5
 838 0016 04E0     		b	.L68
 839              	.LVL55:
 840              	.L67:
 454:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		{
 455:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 			do
 456:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 			{
 457:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 				/* Increment the number of blocks and record the largest block seen
 458:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 				so far. */
 459:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 				xBlocks++;
 460:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 461:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 				if( pxBlock->xBlockSize > xMaxSize )
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccpDCkRu.s 			page 30


 462:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 				{
 463:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 					xMaxSize = pxBlock->xBlockSize;
 464:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 				}
 465:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 466:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 				if( pxBlock->xBlockSize < xMinSize )
 467:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 				{
 468:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 					xMinSize = pxBlock->xBlockSize;
 469:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 				}
 470:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 471:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 				/* Move to the next block in the chain until the last block is
 472:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 				reached. */
 473:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 				pxBlock = pxBlock->pxNextFreeBlock;
 841              		.loc 1 473 5 is_stmt 1 view .LVU262
 842              		.loc 1 473 13 is_stmt 0 view .LVU263
 843 0018 1B68     		ldr	r3, [r3]
 844              	.LVL56:
 474:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 			} while( pxBlock != pxEnd );
 845              		.loc 1 474 21 is_stmt 1 view .LVU264
 846 001a 144A     		ldr	r2, .L73+4
 847 001c 1268     		ldr	r2, [r2]
 848 001e 9A42     		cmp	r2, r3
 849 0020 0CD0     		beq	.L65
 850              	.LVL57:
 851              	.L68:
 455:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 			{
 852              		.loc 1 455 4 view .LVU265
 459:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 853              		.loc 1 459 5 view .LVU266
 459:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 854              		.loc 1 459 12 is_stmt 0 view .LVU267
 855 0022 0134     		adds	r4, r4, #1
 856              	.LVL58:
 461:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 				{
 857              		.loc 1 461 5 is_stmt 1 view .LVU268
 461:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 				{
 858              		.loc 1 461 16 is_stmt 0 view .LVU269
 859 0024 5A68     		ldr	r2, [r3, #4]
 461:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 				{
 860              		.loc 1 461 7 view .LVU270
 861 0026 AA42     		cmp	r2, r5
 862 0028 00D9     		bls	.L66
 463:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 				}
 863              		.loc 1 463 15 view .LVU271
 864 002a 1546     		mov	r5, r2
 865              	.LVL59:
 866              	.L66:
 466:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 				{
 867              		.loc 1 466 5 is_stmt 1 view .LVU272
 466:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 				{
 868              		.loc 1 466 7 is_stmt 0 view .LVU273
 869 002c B242     		cmp	r2, r6
 870 002e F3D2     		bcs	.L67
 468:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 				}
 871              		.loc 1 468 15 view .LVU274
 872 0030 1646     		mov	r6, r2
 873              	.LVL60:
 468:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 				}
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccpDCkRu.s 			page 31


 874              		.loc 1 468 15 view .LVU275
 875 0032 F1E7     		b	.L67
 876              	.LVL61:
 877              	.L69:
 445:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 878              		.loc 1 445 35 view .LVU276
 879 0034 4FF0FF36 		mov	r6, #-1
 445:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 880              		.loc 1 445 21 view .LVU277
 881 0038 0025     		movs	r5, #0
 445:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 882              		.loc 1 445 8 view .LVU278
 883 003a 2C46     		mov	r4, r5
 884              	.LVL62:
 885              	.L65:
 475:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		}
 476:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	}
 477:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	xTaskResumeAll();
 886              		.loc 1 477 2 is_stmt 1 view .LVU279
 887 003c FFF7FEFF 		bl	xTaskResumeAll
 888              	.LVL63:
 478:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 479:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	pxHeapStats->xSizeOfLargestFreeBlockInBytes = xMaxSize;
 889              		.loc 1 479 2 view .LVU280
 890              		.loc 1 479 46 is_stmt 0 view .LVU281
 891 0040 7D60     		str	r5, [r7, #4]
 480:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	pxHeapStats->xSizeOfSmallestFreeBlockInBytes = xMinSize;
 892              		.loc 1 480 2 is_stmt 1 view .LVU282
 893              		.loc 1 480 47 is_stmt 0 view .LVU283
 894 0042 BE60     		str	r6, [r7, #8]
 481:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	pxHeapStats->xNumberOfFreeBlocks = xBlocks;
 895              		.loc 1 481 2 is_stmt 1 view .LVU284
 896              		.loc 1 481 35 is_stmt 0 view .LVU285
 897 0044 FC60     		str	r4, [r7, #12]
 482:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 
 483:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	taskENTER_CRITICAL();
 898              		.loc 1 483 2 is_stmt 1 view .LVU286
 899 0046 FFF7FEFF 		bl	vPortEnterCritical
 900              	.LVL64:
 484:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	{
 485:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		pxHeapStats->xAvailableHeapSpaceInBytes = xFreeBytesRemaining;
 901              		.loc 1 485 3 view .LVU287
 902              		.loc 1 485 43 is_stmt 0 view .LVU288
 903 004a 094B     		ldr	r3, .L73+8
 904 004c 1B68     		ldr	r3, [r3]
 905 004e 3B60     		str	r3, [r7]
 486:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		pxHeapStats->xNumberOfSuccessfulAllocations = xNumberOfSuccessfulAllocations;
 906              		.loc 1 486 3 is_stmt 1 view .LVU289
 907              		.loc 1 486 47 is_stmt 0 view .LVU290
 908 0050 084B     		ldr	r3, .L73+12
 909 0052 1B68     		ldr	r3, [r3]
 910 0054 7B61     		str	r3, [r7, #20]
 487:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		pxHeapStats->xNumberOfSuccessfulFrees = xNumberOfSuccessfulFrees;
 911              		.loc 1 487 3 is_stmt 1 view .LVU291
 912              		.loc 1 487 41 is_stmt 0 view .LVU292
 913 0056 084B     		ldr	r3, .L73+16
 914 0058 1B68     		ldr	r3, [r3]
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccpDCkRu.s 			page 32


 915 005a BB61     		str	r3, [r7, #24]
 488:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 		pxHeapStats->xMinimumEverFreeBytesRemaining = xMinimumEverFreeBytesRemaining;
 916              		.loc 1 488 3 is_stmt 1 view .LVU293
 917              		.loc 1 488 47 is_stmt 0 view .LVU294
 918 005c 074B     		ldr	r3, .L73+20
 919 005e 1B68     		ldr	r3, [r3]
 920 0060 3B61     		str	r3, [r7, #16]
 489:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	}
 490:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** 	taskEXIT_CRITICAL();
 921              		.loc 1 490 2 is_stmt 1 view .LVU295
 922 0062 FFF7FEFF 		bl	vPortExitCritical
 923              	.LVL65:
 491:Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c **** }
 924              		.loc 1 491 1 is_stmt 0 view .LVU296
 925 0066 F8BD     		pop	{r3, r4, r5, r6, r7, pc}
 926              	.LVL66:
 927              	.L74:
 928              		.loc 1 491 1 view .LVU297
 929              		.align	2
 930              	.L73:
 931 0068 00000000 		.word	xStart
 932 006c 00000000 		.word	pxEnd
 933 0070 00000000 		.word	xFreeBytesRemaining
 934 0074 00000000 		.word	xNumberOfSuccessfulAllocations
 935 0078 00000000 		.word	xNumberOfSuccessfulFrees
 936 007c 00000000 		.word	xMinimumEverFreeBytesRemaining
 937              		.cfi_endproc
 938              	.LFE11:
 940              		.section	.bss.xBlockAllocatedBit,"aw",%nobits
 941              		.align	2
 944              	xBlockAllocatedBit:
 945 0000 00000000 		.space	4
 946              		.section	.bss.xNumberOfSuccessfulFrees,"aw",%nobits
 947              		.align	2
 950              	xNumberOfSuccessfulFrees:
 951 0000 00000000 		.space	4
 952              		.section	.bss.xNumberOfSuccessfulAllocations,"aw",%nobits
 953              		.align	2
 956              	xNumberOfSuccessfulAllocations:
 957 0000 00000000 		.space	4
 958              		.section	.bss.xMinimumEverFreeBytesRemaining,"aw",%nobits
 959              		.align	2
 962              	xMinimumEverFreeBytesRemaining:
 963 0000 00000000 		.space	4
 964              		.section	.bss.xFreeBytesRemaining,"aw",%nobits
 965              		.align	2
 968              	xFreeBytesRemaining:
 969 0000 00000000 		.space	4
 970              		.section	.bss.pxEnd,"aw",%nobits
 971              		.align	2
 974              	pxEnd:
 975 0000 00000000 		.space	4
 976              		.section	.bss.xStart,"aw",%nobits
 977              		.align	2
 980              	xStart:
 981 0000 00000000 		.space	8
 981      00000000 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccpDCkRu.s 			page 33


 982              		.section	.bss.ucHeap,"aw",%nobits
 983              		.align	2
 986              	ucHeap:
 987 0000 00000000 		.space	15360
 987      00000000 
 987      00000000 
 987      00000000 
 987      00000000 
 988              		.text
 989              	.Letext0:
 990              		.file 3 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\../lib/gcc/arm-none-eabi/13.3.1/in
 991              		.file 4 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\../lib/gcc/arm-none-eabi/13.3.1/..
 992              		.file 5 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\../lib/gcc/arm-none-eabi/13.3.1/..
 993              		.file 6 "Middlewares/Third_Party/FreeRTOS/Source/include/portable.h"
 994              		.file 7 "Middlewares/Third_Party/FreeRTOS/Source/include/task.h"
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccpDCkRu.s 			page 34


DEFINED SYMBOLS
                            *ABS*:00000000 heap_4.c
C:\Users\<USER>\AppData\Local\Temp\ccpDCkRu.s:20     .text.prvHeapInit:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccpDCkRu.s:25     .text.prvHeapInit:00000000 prvHeapInit
C:\Users\<USER>\AppData\Local\Temp\ccpDCkRu.s:130    .text.prvHeapInit:0000004c $d
C:\Users\<USER>\AppData\Local\Temp\ccpDCkRu.s:986    .bss.ucHeap:00000000 ucHeap
C:\Users\<USER>\AppData\Local\Temp\ccpDCkRu.s:980    .bss.xStart:00000000 xStart
C:\Users\<USER>\AppData\Local\Temp\ccpDCkRu.s:974    .bss.pxEnd:00000000 pxEnd
C:\Users\<USER>\AppData\Local\Temp\ccpDCkRu.s:962    .bss.xMinimumEverFreeBytesRemaining:00000000 xMinimumEverFreeBytesRemaining
C:\Users\<USER>\AppData\Local\Temp\ccpDCkRu.s:968    .bss.xFreeBytesRemaining:00000000 xFreeBytesRemaining
C:\Users\<USER>\AppData\Local\Temp\ccpDCkRu.s:944    .bss.xBlockAllocatedBit:00000000 xBlockAllocatedBit
C:\Users\<USER>\AppData\Local\Temp\ccpDCkRu.s:140    .text.prvInsertBlockIntoFreeList:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccpDCkRu.s:145    .text.prvInsertBlockIntoFreeList:00000000 prvInsertBlockIntoFreeList
C:\Users\<USER>\AppData\Local\Temp\ccpDCkRu.s:276    .text.prvInsertBlockIntoFreeList:0000005c $d
C:\Users\<USER>\AppData\Local\Temp\ccpDCkRu.s:282    .text.pvPortMalloc:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccpDCkRu.s:288    .text.pvPortMalloc:00000000 pvPortMalloc
C:\Users\<USER>\AppData\Local\Temp\ccpDCkRu.s:566    .text.pvPortMalloc:000000ec $d
C:\Users\<USER>\AppData\Local\Temp\ccpDCkRu.s:956    .bss.xNumberOfSuccessfulAllocations:00000000 xNumberOfSuccessfulAllocations
C:\Users\<USER>\AppData\Local\Temp\ccpDCkRu.s:576    .text.vPortFree:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccpDCkRu.s:582    .text.vPortFree:00000000 vPortFree
C:\Users\<USER>\AppData\Local\Temp\ccpDCkRu.s:716    .text.vPortFree:00000070 $d
C:\Users\<USER>\AppData\Local\Temp\ccpDCkRu.s:950    .bss.xNumberOfSuccessfulFrees:00000000 xNumberOfSuccessfulFrees
C:\Users\<USER>\AppData\Local\Temp\ccpDCkRu.s:723    .text.xPortGetFreeHeapSize:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccpDCkRu.s:729    .text.xPortGetFreeHeapSize:00000000 xPortGetFreeHeapSize
C:\Users\<USER>\AppData\Local\Temp\ccpDCkRu.s:744    .text.xPortGetFreeHeapSize:00000008 $d
C:\Users\<USER>\AppData\Local\Temp\ccpDCkRu.s:749    .text.xPortGetMinimumEverFreeHeapSize:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccpDCkRu.s:755    .text.xPortGetMinimumEverFreeHeapSize:00000000 xPortGetMinimumEverFreeHeapSize
C:\Users\<USER>\AppData\Local\Temp\ccpDCkRu.s:770    .text.xPortGetMinimumEverFreeHeapSize:00000008 $d
C:\Users\<USER>\AppData\Local\Temp\ccpDCkRu.s:775    .text.vPortInitialiseBlocks:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccpDCkRu.s:781    .text.vPortInitialiseBlocks:00000000 vPortInitialiseBlocks
C:\Users\<USER>\AppData\Local\Temp\ccpDCkRu.s:794    .text.vPortGetHeapStats:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccpDCkRu.s:800    .text.vPortGetHeapStats:00000000 vPortGetHeapStats
C:\Users\<USER>\AppData\Local\Temp\ccpDCkRu.s:931    .text.vPortGetHeapStats:00000068 $d
C:\Users\<USER>\AppData\Local\Temp\ccpDCkRu.s:941    .bss.xBlockAllocatedBit:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\ccpDCkRu.s:947    .bss.xNumberOfSuccessfulFrees:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\ccpDCkRu.s:953    .bss.xNumberOfSuccessfulAllocations:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\ccpDCkRu.s:959    .bss.xMinimumEverFreeBytesRemaining:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\ccpDCkRu.s:965    .bss.xFreeBytesRemaining:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\ccpDCkRu.s:971    .bss.pxEnd:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\ccpDCkRu.s:977    .bss.xStart:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\ccpDCkRu.s:983    .bss.ucHeap:00000000 $d

UNDEFINED SYMBOLS
vTaskSuspendAll
xTaskResumeAll
vPortEnterCritical
vPortExitCritical
