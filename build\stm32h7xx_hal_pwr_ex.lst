ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 1


   1              		.cpu cortex-m7
   2              		.arch armv7e-m
   3              		.fpu fpv5-d16
   4              		.eabi_attribute 28, 1
   5              		.eabi_attribute 20, 1
   6              		.eabi_attribute 21, 1
   7              		.eabi_attribute 23, 3
   8              		.eabi_attribute 24, 1
   9              		.eabi_attribute 25, 1
  10              		.eabi_attribute 26, 1
  11              		.eabi_attribute 30, 1
  12              		.eabi_attribute 34, 1
  13              		.eabi_attribute 18, 4
  14              		.file	"stm32h7xx_hal_pwr_ex.c"
  15              		.text
  16              	.Ltext0:
  17              		.cfi_sections	.debug_frame
  18              		.file 1 "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c"
  19              		.section	.text.HAL_PWREx_ConfigSupply,"ax",%progbits
  20              		.align	1
  21              		.global	HAL_PWREx_ConfigSupply
  22              		.syntax unified
  23              		.thumb
  24              		.thumb_func
  26              	HAL_PWREx_ConfigSupply:
  27              	.LVL0:
  28              	.LFB144:
   1:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /**
   2:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   ******************************************************************************
   3:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @file    stm32h7xx_hal_pwr_ex.c
   4:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * <AUTHOR> Application Team
   5:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @brief   Extended PWR HAL module driver.
   6:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *          This file provides firmware functions to manage the following
   7:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *          functionalities of PWR extension peripheral:
   8:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *           + Peripheral Extended features functions
   9:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   ******************************************************************************
  10:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @attention
  11:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *
  12:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * Copyright (c) 2017 STMicroelectronics.
  13:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * All rights reserved.
  14:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *
  15:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * This software is licensed under terms that can be found in the LICENSE file
  16:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * in the root directory of this software component.
  17:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * If no LICENSE file comes with this software, it is provided AS-IS.
  18:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *
  19:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   ******************************************************************************
  20:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   @verbatim
  21:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   ==============================================================================
  22:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****                         ##### How to use this driver #####
  23:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   ==============================================================================
  24:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   [..]
  25:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****    (#) Call HAL_PWREx_ConfigSupply() function to configure the regulator supply
  26:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        with the following different setups according to hardware (support SMPS):
  27:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        (+) PWR_DIRECT_SMPS_SUPPLY
  28:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        (+) PWR_SMPS_1V8_SUPPLIES_LDO
  29:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        (+) PWR_SMPS_2V5_SUPPLIES_LDO
  30:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        (+) PWR_SMPS_1V8_SUPPLIES_EXT_AND_LDO
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 2


  31:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        (+) PWR_SMPS_2V5_SUPPLIES_EXT_AND_LDO
  32:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        (+) PWR_SMPS_1V8_SUPPLIES_EXT
  33:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        (+) PWR_SMPS_2V5_SUPPLIES_EXT
  34:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        (+) PWR_LDO_SUPPLY
  35:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        (+) PWR_EXTERNAL_SOURCE_SUPPLY
  36:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
  37:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****    (#) Call HAL_PWREx_GetSupplyConfig() function to get the current supply setup.
  38:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
  39:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****    (#) Call HAL_PWREx_ControlVoltageScaling() function to configure the main
  40:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        internal regulator output voltage. The voltage scaling could be one of
  41:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        the following scales :
  42:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        (+) PWR_REGULATOR_VOLTAGE_SCALE0
  43:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        (+) PWR_REGULATOR_VOLTAGE_SCALE1
  44:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        (+) PWR_REGULATOR_VOLTAGE_SCALE2
  45:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        (+) PWR_REGULATOR_VOLTAGE_SCALE3
  46:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
  47:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****    (#) Call HAL_PWREx_GetVoltageRange() function to get the current output
  48:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        voltage applied to the main regulator.
  49:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
  50:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****    (#) Call HAL_PWREx_ControlStopModeVoltageScaling() function to configure the
  51:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        main internal regulator output voltage in STOP mode. The voltage scaling
  52:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        in STOP mode could be one of the following scales :
  53:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        (+) PWR_REGULATOR_SVOS_SCALE3
  54:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        (+) PWR_REGULATOR_SVOS_SCALE4
  55:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        (+) PWR_REGULATOR_SVOS_SCALE5
  56:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
  57:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****    (#) Call HAL_PWREx_GetStopModeVoltageRange() function to get the current
  58:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        output voltage applied to the main regulator in STOP mode.
  59:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
  60:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****    (#) Call HAL_PWREx_EnterSTOP2Mode() function to enter the system in STOP mode
  61:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        with core domain in D2STOP mode. This API is used only for STM32H7Axxx
  62:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        and STM32H7Bxxx devices.
  63:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        Please ensure to clear all CPU pending events by calling
  64:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        HAL_PWREx_ClearPendingEvent() function when trying to enter the Cortex-Mx
  65:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        in DEEP-SLEEP mode with __WFE() entry.
  66:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
  67:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****    (#) Call HAL_PWREx_EnterSTOPMode() function to enter the selected domain in
  68:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        DSTOP mode. Call this API with all available power domains to enter the
  69:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        system in STOP mode.
  70:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        Please ensure to clear all CPU pending events by calling
  71:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        HAL_PWREx_ClearPendingEvent() function when trying to enter the Cortex-Mx
  72:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        in DEEP-SLEEP mode with __WFE() entry.
  73:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
  74:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****    (#) Call HAL_PWREx_ClearPendingEvent() function always before entring the
  75:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        Cortex-Mx in any low power mode (SLEEP/DEEP-SLEEP) using WFE entry.
  76:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
  77:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****    (#) Call HAL_PWREx_EnterSTANDBYMode() function to enter the selected domain
  78:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        in DSTANDBY mode. Call this API with all available power domains to enter
  79:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        the system in STANDBY mode.
  80:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
  81:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****    (#) Call HAL_PWREx_ConfigD3Domain() function to setup the D3/SRD domain state
  82:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        (RUN/STOP) when the system enter to low power mode.
  83:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
  84:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****    (#) Call HAL_PWREx_ClearDomainFlags() function to clear the CPU flags for the
  85:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        selected power domain. This API is used only for dual core devices.
  86:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
  87:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****    (#) Call HAL_PWREx_HoldCore() and HAL_PWREx_ReleaseCore() functions to hold
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 3


  88:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        and release the selected CPU and and their domain peripherals when
  89:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        exiting STOP mode. These APIs are used only for dual core devices.
  90:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
  91:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****    (#) Call HAL_PWREx_EnableFlashPowerDown() and
  92:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        HAL_PWREx_DisableFlashPowerDown() functions to enable and disable the
  93:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        Flash Power Down in STOP mode.
  94:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
  95:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****    (#) Call HAL_PWREx_EnableMemoryShutOff() and
  96:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        HAL_PWREx_DisableMemoryShutOff() functions to enable and disable the
  97:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        memory block shut-off in DStop or DStop2. These APIs are used only for
  98:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        STM32H7Axxx and STM32H7Bxxx lines.
  99:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 100:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****    (#) Call HAL_PWREx_EnableWakeUpPin() and HAL_PWREx_DisableWakeUpPin()
 101:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        functions to enable and disable the Wake-up pin functionality for
 102:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        the selected pin.
 103:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 104:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****    (#) Call HAL_PWREx_GetWakeupFlag() and HAL_PWREx_ClearWakeupFlag()
 105:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        functions to manage wake-up flag for the selected pin.
 106:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 107:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****    (#) Call HAL_PWREx_WAKEUP_PIN_IRQHandler() function to handle all wake-up
 108:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        pins interrupts.
 109:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 110:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****    (#) Call HAL_PWREx_EnableBkUpReg() and HAL_PWREx_DisableBkUpReg() functions
 111:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        to enable and disable the backup domain regulator.
 112:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 113:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****    (#) Call HAL_PWREx_EnableUSBReg(), HAL_PWREx_DisableUSBReg(),
 114:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        HAL_PWREx_EnableUSBVoltageDetector() and
 115:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        HAL_PWREx_DisableUSBVoltageDetector() functions to manage USB power
 116:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        regulation functionalities.
 117:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 118:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****    (#) Call HAL_PWREx_EnableBatteryCharging() and
 119:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        HAL_PWREx_DisableBatteryCharging() functions to enable and disable the
 120:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        battery charging feature with the selected resistor.
 121:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 122:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****    (#) Call HAL_PWREx_EnableAnalogBooster() and
 123:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        HAL_PWREx_DisableAnalogBooster() functions to enable and disable the
 124:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        AVD boost feature when the VDD supply voltage is below 2V7.
 125:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 126:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****    (#) Call HAL_PWREx_EnableMonitoring() and HAL_PWREx_DisableMonitoring()
 127:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        functions to enable and disable the VBAT and Temperature monitoring.
 128:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        When VBAT and Temperature monitoring feature is enables, use
 129:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        HAL_PWREx_GetTemperatureLevel() and HAL_PWREx_GetVBATLevel() to get
 130:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        respectively the Temperature level and VBAT level.
 131:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 132:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****    (#) Call HAL_PWREx_GetMMCVoltage() and HAL_PWREx_DisableMonitoring()
 133:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        function to get VDDMMC voltage level. This API is used only for
 134:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        STM32H7Axxx and STM32H7Bxxx lines
 135:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 136:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****    (#) Call HAL_PWREx_ConfigAVD() after setting parameter to be configured
 137:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        (event mode and voltage threshold) in order to set up the Analog Voltage
 138:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        Detector then use HAL_PWREx_EnableAVD() and  HAL_PWREx_DisableAVD()
 139:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        functions to start and stop the AVD detection.
 140:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        (+) AVD level could be one of the following values :
 141:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****              (++) 1V7
 142:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****              (++) 2V1
 143:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****              (++) 2V5
 144:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****              (++) 2V8
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 4


 145:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 146:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****    (#) Call HAL_PWREx_PVD_AVD_IRQHandler() function to handle the PWR PVD and
 147:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        AVD interrupt request.
 148:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 149:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   @endverbatim
 150:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   */
 151:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 152:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /* Includes ------------------------------------------------------------------*/
 153:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #include "stm32h7xx_hal.h"
 154:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 155:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /** @addtogroup STM32H7xx_HAL_Driver
 156:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @{
 157:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   */
 158:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 159:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /** @defgroup PWREx PWREx
 160:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @brief PWR Extended HAL module driver
 161:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @{
 162:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   */
 163:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 164:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #ifdef HAL_PWR_MODULE_ENABLED
 165:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 166:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /* Private typedef -----------------------------------------------------------*/
 167:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /* Private define ------------------------------------------------------------*/
 168:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 169:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /** @addtogroup PWREx_Private_Constants
 170:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @{
 171:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   */
 172:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 173:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /** @defgroup PWREx_AVD_Mode_Mask PWR Extended AVD Mode Mask
 174:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @{
 175:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   */
 176:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #define AVD_MODE_IT              (0x00010000U)
 177:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #define AVD_MODE_EVT             (0x00020000U)
 178:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #define AVD_RISING_EDGE          (0x00000001U)
 179:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #define AVD_FALLING_EDGE         (0x00000002U)
 180:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #define AVD_RISING_FALLING_EDGE  (0x00000003U)
 181:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /**
 182:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @}
 183:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   */
 184:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 185:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /** @defgroup PWREx_REG_SET_TIMEOUT PWR Extended Flag Setting Time Out Value
 186:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @{
 187:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   */
 188:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #define PWR_FLAG_SETTING_DELAY   (1000U)
 189:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /**
 190:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @}
 191:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   */
 192:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 193:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /** @defgroup PWREx_WakeUp_Pins_Offsets PWREx Wake-Up Pins masks and offsets
 194:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @{
 195:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   */
 196:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /* Wake-Up Pins EXTI register mask */
 197:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #if defined (EXTI_IMR2_IM57)
 198:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #define PWR_EXTI_WAKEUP_PINS_MASK  (EXTI_IMR2_IM55 | EXTI_IMR2_IM56 |\
 199:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****                                     EXTI_IMR2_IM57 | EXTI_IMR2_IM58 |\
 200:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****                                     EXTI_IMR2_IM59 | EXTI_IMR2_IM60)
 201:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #else
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 5


 202:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #define PWR_EXTI_WAKEUP_PINS_MASK  (EXTI_IMR2_IM55 | EXTI_IMR2_IM56 |\
 203:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****                                     EXTI_IMR2_IM58 | EXTI_IMR2_IM60)
 204:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #endif /* defined (EXTI_IMR2_IM57) */
 205:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 206:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /* Wake-Up Pins PWR Pin Pull shift offsets */
 207:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #define PWR_WAKEUP_PINS_PULL_SHIFT_OFFSET (2U)
 208:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /**
 209:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @}
 210:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   */
 211:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 212:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /**
 213:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @}
 214:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   */
 215:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 216:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /* Private macro -------------------------------------------------------------*/
 217:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /* Private variables ---------------------------------------------------------*/
 218:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /* Private function prototypes -----------------------------------------------*/
 219:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /* Private functions ---------------------------------------------------------*/
 220:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /* Exported types ------------------------------------------------------------*/
 221:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /* Exported functions --------------------------------------------------------*/
 222:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 223:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /** @defgroup PWREx_Exported_Functions PWREx Exported Functions
 224:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @{
 225:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   */
 226:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 227:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /** @defgroup PWREx_Exported_Functions_Group1 Power Supply Control Functions
 228:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @brief    Power supply control functions
 229:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *
 230:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** @verbatim
 231:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****  ===============================================================================
 232:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****                   ##### Power supply control functions #####
 233:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****  ===============================================================================
 234:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     [..]
 235:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****    (#) When the system is powered on, the POR monitors VDD supply. Once VDD is
 236:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        above the POR threshold level, the voltage regulator is enabled in the
 237:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        default supply configuration:
 238:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       (+) The Voltage converter output level is set at 1V0 in accordance with
 239:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           the VOS3 level configured in PWR (D3/SRD) domain control register
 240:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           (PWR_D3CR/PWR_SRDCR).
 241:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       (+) The system is kept in reset mode as long as VCORE is not ok.
 242:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       (+) Once VCORE is ok, the system is taken out of reset and the HSI
 243:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           oscillator is enabled.
 244:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       (+) Once the oscillator is stable, the system is initialized: Flash memory
 245:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           and option bytes are loaded and the CPU starts in Run* mode.
 246:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       (+) The software shall then initialize the system including supply
 247:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           configuration programming using the HAL_PWREx_ConfigSupply().
 248:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       (+) Once the supply configuration has been configured, the
 249:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           HAL_PWREx_ConfigSupply() function checks the ACTVOSRDY bit in PWR
 250:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           control status register 1 (PWR_CSR1) to guarantee a valid voltage
 251:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           levels:
 252:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        (++) As long as ACTVOSRDY indicates that voltage levels are invalid, the
 253:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****             system is in limited Run* mode, write accesses to the RAMs are not
 254:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****             permitted and VOS shall not be changed.
 255:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        (++) Once ACTVOSRDY indicates that voltage levels are valid, the system
 256:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****             is in normal Run mode, write accesses to RAMs are allowed and VOS
 257:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****             can be changed.
 258:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 6


 259:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** @endverbatim
 260:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @{
 261:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   */
 262:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 263:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /**
 264:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @brief Configure the system Power Supply.
 265:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @param  SupplySource : Specifies the Power Supply source to set after a
 266:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *                        system startup.
 267:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         This parameter can be one of the following values :
 268:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *            @arg PWR_DIRECT_SMPS_SUPPLY : The SMPS supplies the Vcore Power
 269:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *                                          Domains. The LDO is Bypassed.
 270:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *            @arg PWR_SMPS_1V8_SUPPLIES_LDO : The SMPS 1.8V output supplies
 271:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *                                             the LDO. The Vcore Power Domains
 272:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *                                             are supplied from the LDO.
 273:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *            @arg PWR_SMPS_2V5_SUPPLIES_LDO : The SMPS 2.5V output supplies
 274:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *                                             the LDO. The Vcore Power Domains
 275:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *                                             are supplied from the LDO.
 276:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *            @arg PWR_SMPS_1V8_SUPPLIES_EXT_AND_LDO : The SMPS 1.8V output
 277:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *                                                     supplies external
 278:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *                                                     circuits and the LDO.
 279:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *                                                     The Vcore Power Domains
 280:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *                                                     are supplied from the
 281:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *                                                     LDO.
 282:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *            @arg PWR_SMPS_2V5_SUPPLIES_EXT_AND_LDO : The SMPS 2.5V output
 283:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *                                                     supplies external
 284:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *                                                     circuits and the LDO.
 285:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *                                                     The Vcore Power Domains
 286:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *                                                     are supplied from the
 287:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *                                                     LDO.
 288:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *            @arg PWR_SMPS_1V8_SUPPLIES_EXT : The SMPS 1.8V output supplies
 289:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *                                             external circuits. The LDO is
 290:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *                                             Bypassed. The Vcore Power
 291:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *                                             Domains are supplied from
 292:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *                                             external source.
 293:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *            @arg PWR_SMPS_2V5_SUPPLIES_EXT : The SMPS 2.5V output supplies
 294:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *                                             external circuits. The LDO is
 295:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *                                             Bypassed. The Vcore Power
 296:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *                                             Domains are supplied from
 297:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *                                             external source.
 298:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *            @arg PWR_LDO_SUPPLY : The LDO regulator supplies the Vcore Power
 299:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *                                  Domains. The SMPS regulator is Bypassed.
 300:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *            @arg PWR_EXTERNAL_SOURCE_SUPPLY : The SMPS and the LDO are
 301:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *                                              Bypassed. The Vcore Power
 302:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *                                              Domains are supplied from
 303:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *                                              external source.
 304:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @note   The PWR_LDO_SUPPLY and PWR_EXTERNAL_SOURCE_SUPPLY are used by all
 305:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         H7 lines.
 306:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         The PWR_DIRECT_SMPS_SUPPLY, PWR_SMPS_1V8_SUPPLIES_LDO,
 307:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         PWR_SMPS_2V5_SUPPLIES_LDO, PWR_SMPS_1V8_SUPPLIES_EXT_AND_LDO,
 308:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         PWR_SMPS_2V5_SUPPLIES_EXT_AND_LDO, PWR_SMPS_1V8_SUPPLIES_EXT and
 309:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         PWR_SMPS_2V5_SUPPLIES_EXT are used only for lines that supports SMPS
 310:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         regulator.
 311:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @note   This API is deprecated and is kept only for backward compatibility's sake.
 312:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         The power supply configuration is handled as part of the system initialization
 313:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         process during startup.
 314:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         For more details, please refer to the power control chapter in the reference manual
 315:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @retval HAL status.
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 7


 316:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   */
 317:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** HAL_StatusTypeDef HAL_PWREx_ConfigSupply (uint32_t SupplySource)
 318:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** {
  29              		.loc 1 318 1 view -0
  30              		.cfi_startproc
  31              		@ args = 0, pretend = 0, frame = 0
  32              		@ frame_needed = 0, uses_anonymous_args = 0
 319:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   uint32_t tickstart;
  33              		.loc 1 319 3 view .LVU1
 320:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 321:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Check the parameters */
 322:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   assert_param (IS_PWR_SUPPLY (SupplySource));
  34              		.loc 1 322 3 view .LVU2
 323:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 324:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Check if supply source was configured */
 325:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #if defined (PWR_FLAG_SCUEN)
 326:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   if (__HAL_PWR_GET_FLAG (PWR_FLAG_SCUEN) == 0U)
  35              		.loc 1 326 3 view .LVU3
  36              		.loc 1 326 7 is_stmt 0 view .LVU4
  37 0000 134B     		ldr	r3, .L12
  38 0002 DB68     		ldr	r3, [r3, #12]
  39              		.loc 1 326 6 view .LVU5
  40 0004 13F0040F 		tst	r3, #4
  41 0008 07D1     		bne	.L2
 327:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #else
 328:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   if ((PWR->CR3 & (PWR_CR3_SMPSEN | PWR_CR3_LDOEN | PWR_CR3_BYPASS)) != (PWR_CR3_SMPSEN | PWR_CR3_L
 329:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #endif /* defined (PWR_FLAG_SCUEN) */
 330:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   {
 331:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     /* Check supply configuration */
 332:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     if ((PWR->CR3 & PWR_SUPPLY_CONFIG_MASK) != SupplySource)
  42              		.loc 1 332 5 is_stmt 1 view .LVU6
  43              		.loc 1 332 13 is_stmt 0 view .LVU7
  44 000a 114B     		ldr	r3, .L12
  45 000c DB68     		ldr	r3, [r3, #12]
  46              		.loc 1 332 19 view .LVU8
  47 000e 03F00703 		and	r3, r3, #7
  48              		.loc 1 332 8 view .LVU9
  49 0012 8342     		cmp	r3, r0
  50 0014 1AD0     		beq	.L6
 333:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     {
 334:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       /* Supply configuration update locked, can't apply a new supply config */
 335:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       return HAL_ERROR;
  51              		.loc 1 335 14 view .LVU10
  52 0016 0120     		movs	r0, #1
  53              	.LVL1:
  54              		.loc 1 335 14 view .LVU11
  55 0018 7047     		bx	lr
  56              	.LVL2:
  57              	.L2:
 318:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   uint32_t tickstart;
  58              		.loc 1 318 1 view .LVU12
  59 001a 10B5     		push	{r4, lr}
  60              	.LCFI0:
  61              		.cfi_def_cfa_offset 8
  62              		.cfi_offset 4, -8
  63              		.cfi_offset 14, -4
 336:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     }
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 8


 337:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     else
 338:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     {
 339:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       /* Supply configuration update locked, but new supply configuration
 340:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****          matches with old supply configuration : nothing to do
 341:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       */
 342:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       return HAL_OK;
 343:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     }
 344:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   }
 345:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 346:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Set the power supply configuration */
 347:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   MODIFY_REG (PWR->CR3, PWR_SUPPLY_CONFIG_MASK, SupplySource);
  64              		.loc 1 347 3 is_stmt 1 view .LVU13
  65 001c 0C4A     		ldr	r2, .L12
  66 001e D368     		ldr	r3, [r2, #12]
  67 0020 23F00703 		bic	r3, r3, #7
  68 0024 0343     		orrs	r3, r3, r0
  69 0026 D360     		str	r3, [r2, #12]
 348:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 349:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Get tick */
 350:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   tickstart = HAL_GetTick ();
  70              		.loc 1 350 3 view .LVU14
  71              		.loc 1 350 15 is_stmt 0 view .LVU15
  72 0028 FFF7FEFF 		bl	HAL_GetTick
  73              	.LVL3:
  74              		.loc 1 350 15 view .LVU16
  75 002c 0446     		mov	r4, r0
  76              	.LVL4:
 351:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 352:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Wait till voltage level flag is set */
 353:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   while (__HAL_PWR_GET_FLAG (PWR_FLAG_ACTVOSRDY) == 0U)
  77              		.loc 1 353 3 is_stmt 1 view .LVU17
  78              	.L4:
  79              		.loc 1 353 50 view .LVU18
  80              		.loc 1 353 10 is_stmt 0 view .LVU19
  81 002e 084B     		ldr	r3, .L12
  82 0030 5B68     		ldr	r3, [r3, #4]
  83              		.loc 1 353 50 view .LVU20
  84 0032 13F4005F 		tst	r3, #8192
  85 0036 07D1     		bne	.L11
 354:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   {
 355:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     if ((HAL_GetTick () - tickstart) > PWR_FLAG_SETTING_DELAY)
  86              		.loc 1 355 5 is_stmt 1 view .LVU21
  87              		.loc 1 355 10 is_stmt 0 view .LVU22
  88 0038 FFF7FEFF 		bl	HAL_GetTick
  89              	.LVL5:
  90              		.loc 1 355 25 discriminator 1 view .LVU23
  91 003c 001B     		subs	r0, r0, r4
  92              		.loc 1 355 8 discriminator 1 view .LVU24
  93 003e B0F57A7F 		cmp	r0, #1000
  94 0042 F4D9     		bls	.L4
 356:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     {
 357:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       return HAL_ERROR;
  95              		.loc 1 357 14 view .LVU25
  96 0044 0120     		movs	r0, #1
  97 0046 00E0     		b	.L3
  98              	.L11:
 358:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     }
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 9


 359:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   }
 360:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 361:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #if defined (SMPS)
 362:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* When the SMPS supplies external circuits verify that SDEXTRDY flag is set */
 363:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   if ((SupplySource == PWR_SMPS_1V8_SUPPLIES_EXT_AND_LDO) ||
 364:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       (SupplySource == PWR_SMPS_2V5_SUPPLIES_EXT_AND_LDO) ||
 365:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       (SupplySource == PWR_SMPS_1V8_SUPPLIES_EXT)         ||
 366:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       (SupplySource == PWR_SMPS_2V5_SUPPLIES_EXT))
 367:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   {
 368:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     /* Get the current tick number */
 369:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     tickstart = HAL_GetTick ();
 370:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 371:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     /* Wait till SMPS external supply ready flag is set */
 372:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     while (__HAL_PWR_GET_FLAG (PWR_FLAG_SMPSEXTRDY) == 0U)
 373:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     {
 374:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       if ((HAL_GetTick () - tickstart) > PWR_FLAG_SETTING_DELAY)
 375:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       {
 376:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****         return HAL_ERROR;
 377:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       }
 378:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     }
 379:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   }
 380:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #endif /* defined (SMPS) */
 381:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 382:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   return HAL_OK;
  99              		.loc 1 382 10 view .LVU26
 100 0048 0020     		movs	r0, #0
 101              	.L3:
 383:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** }
 102              		.loc 1 383 1 view .LVU27
 103 004a 10BD     		pop	{r4, pc}
 104              	.LVL6:
 105              	.L6:
 106              	.LCFI1:
 107              		.cfi_def_cfa_offset 0
 108              		.cfi_restore 4
 109              		.cfi_restore 14
 342:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     }
 110              		.loc 1 342 14 view .LVU28
 111 004c 0020     		movs	r0, #0
 112              	.LVL7:
 113              		.loc 1 383 1 view .LVU29
 114 004e 7047     		bx	lr
 115              	.L13:
 116              		.align	2
 117              	.L12:
 118 0050 00480258 		.word	1476544512
 119              		.cfi_endproc
 120              	.LFE144:
 122              		.section	.text.HAL_PWREx_GetSupplyConfig,"ax",%progbits
 123              		.align	1
 124              		.global	HAL_PWREx_GetSupplyConfig
 125              		.syntax unified
 126              		.thumb
 127              		.thumb_func
 129              	HAL_PWREx_GetSupplyConfig:
 130              	.LFB145:
 384:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 10


 385:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /**
 386:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @brief Get the power supply configuration.
 387:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @retval The supply configuration.
 388:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   */
 389:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** uint32_t HAL_PWREx_GetSupplyConfig (void)
 390:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** {
 131              		.loc 1 390 1 is_stmt 1 view -0
 132              		.cfi_startproc
 133              		@ args = 0, pretend = 0, frame = 0
 134              		@ frame_needed = 0, uses_anonymous_args = 0
 135              		@ link register save eliminated.
 391:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   return (PWR->CR3 & PWR_SUPPLY_CONFIG_MASK);
 136              		.loc 1 391 3 view .LVU31
 137              		.loc 1 391 14 is_stmt 0 view .LVU32
 138 0000 024B     		ldr	r3, .L15
 139 0002 D868     		ldr	r0, [r3, #12]
 392:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** }
 140              		.loc 1 392 1 view .LVU33
 141 0004 00F00700 		and	r0, r0, #7
 142 0008 7047     		bx	lr
 143              	.L16:
 144 000a 00BF     		.align	2
 145              	.L15:
 146 000c 00480258 		.word	1476544512
 147              		.cfi_endproc
 148              	.LFE145:
 150              		.section	.text.HAL_PWREx_ControlVoltageScaling,"ax",%progbits
 151              		.align	1
 152              		.global	HAL_PWREx_ControlVoltageScaling
 153              		.syntax unified
 154              		.thumb
 155              		.thumb_func
 157              	HAL_PWREx_ControlVoltageScaling:
 158              	.LVL8:
 159              	.LFB146:
 393:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 394:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /**
 395:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @brief Configure the main internal regulator output voltage.
 396:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @param  VoltageScaling : Specifies the regulator output voltage to achieve
 397:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *                          a tradeoff between performance and power
 398:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *                          consumption.
 399:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *          This parameter can be one of the following values :
 400:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *            @arg PWR_REGULATOR_VOLTAGE_SCALE0 : Regulator voltage output
 401:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *                                                Scale 0 mode.
 402:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *            @arg PWR_REGULATOR_VOLTAGE_SCALE1 : Regulator voltage output
 403:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *                                                range 1 mode.
 404:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *            @arg PWR_REGULATOR_VOLTAGE_SCALE2 : Regulator voltage output
 405:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *                                                range 2 mode.
 406:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *            @arg PWR_REGULATOR_VOLTAGE_SCALE3 : Regulator voltage output
 407:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *                                                range 3 mode.
 408:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @note   For STM32H74x and STM32H75x lines, configuring Voltage Scale 0 is
 409:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         only possible when Vcore is supplied from LDO (Low DropOut). The
 410:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         SYSCFG Clock must be enabled through __HAL_RCC_SYSCFG_CLK_ENABLE()
 411:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         macro before configuring Voltage Scale 0.
 412:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         To enter low power mode , and if current regulator voltage is
 413:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         Voltage Scale 0 then first switch to Voltage Scale 1 before entering
 414:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         low power mode.
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 11


 415:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @retval HAL Status
 416:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   */
 417:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** HAL_StatusTypeDef HAL_PWREx_ControlVoltageScaling (uint32_t VoltageScaling)
 418:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** {
 160              		.loc 1 418 1 is_stmt 1 view -0
 161              		.cfi_startproc
 162              		@ args = 0, pretend = 0, frame = 0
 163              		@ frame_needed = 0, uses_anonymous_args = 0
 419:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   uint32_t tickstart;
 164              		.loc 1 419 3 view .LVU35
 420:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 421:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Check the parameters */
 422:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   assert_param (IS_PWR_REGULATOR_VOLTAGE (VoltageScaling));
 165              		.loc 1 422 3 view .LVU36
 423:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 424:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Get the voltage scaling  */
 425:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   if ((PWR->CSR1 & PWR_CSR1_ACTVOS) == VoltageScaling)
 166              		.loc 1 425 3 view .LVU37
 167              		.loc 1 425 11 is_stmt 0 view .LVU38
 168 0000 104B     		ldr	r3, .L28
 169 0002 5B68     		ldr	r3, [r3, #4]
 170              		.loc 1 425 18 view .LVU39
 171 0004 03F44043 		and	r3, r3, #49152
 172              		.loc 1 425 6 view .LVU40
 173 0008 8342     		cmp	r3, r0
 174 000a 18D0     		beq	.L21
 418:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   uint32_t tickstart;
 175              		.loc 1 418 1 view .LVU41
 176 000c 10B5     		push	{r4, lr}
 177              	.LCFI2:
 178              		.cfi_def_cfa_offset 8
 179              		.cfi_offset 4, -8
 180              		.cfi_offset 14, -4
 426:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   {
 427:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     /* Old and new voltage scaling configuration match : nothing to do */
 428:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     return HAL_OK;
 429:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   }
 430:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 431:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #if defined (PWR_SRDCR_VOS)
 432:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Set the voltage range */
 433:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   MODIFY_REG (PWR->SRDCR, PWR_SRDCR_VOS, VoltageScaling);
 434:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #else
 435:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #if defined(SYSCFG_PWRCR_ODEN) /* STM32H74xxx and STM32H75xxx lines */
 436:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   if (VoltageScaling == PWR_REGULATOR_VOLTAGE_SCALE0)
 437:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   {
 438:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     if ((PWR->CR3 & PWR_CR3_LDOEN) == PWR_CR3_LDOEN)
 439:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     {
 440:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       /* Set the voltage range */
 441:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       MODIFY_REG (PWR->D3CR, PWR_D3CR_VOS, PWR_REGULATOR_VOLTAGE_SCALE1);
 442:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 443:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       /* Get tick */
 444:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       tickstart = HAL_GetTick ();
 445:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 446:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       /* Wait till voltage level flag is set */
 447:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       while (__HAL_PWR_GET_FLAG (PWR_FLAG_ACTVOSRDY) == 0U)
 448:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       {
 449:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****         if ((HAL_GetTick () - tickstart) > PWR_FLAG_SETTING_DELAY)
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 12


 450:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****         {
 451:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           return HAL_ERROR;
 452:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****         }
 453:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       }
 454:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 455:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       /* Enable the PWR overdrive */
 456:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       SET_BIT (SYSCFG->PWRCR, SYSCFG_PWRCR_ODEN);
 457:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     }
 458:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     else
 459:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     {
 460:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       /* The voltage scale 0 is only possible when LDO regulator is enabled */
 461:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       return HAL_ERROR;
 462:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     }
 463:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   }
 464:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   else
 465:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   {
 466:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     if ((PWR->CSR1 & PWR_CSR1_ACTVOS) == PWR_REGULATOR_VOLTAGE_SCALE1)
 467:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     {
 468:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       if ((SYSCFG->PWRCR & SYSCFG_PWRCR_ODEN) != 0U)
 469:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       {
 470:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****         /* Disable the PWR overdrive */
 471:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****         CLEAR_BIT(SYSCFG->PWRCR, SYSCFG_PWRCR_ODEN);
 472:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 473:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****         /* Get tick */
 474:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****         tickstart = HAL_GetTick ();
 475:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 476:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****         /* Wait till voltage level flag is set */
 477:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****         while (__HAL_PWR_GET_FLAG (PWR_FLAG_ACTVOSRDY) == 0U)
 478:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****         {
 479:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           if ((HAL_GetTick () - tickstart) > PWR_FLAG_SETTING_DELAY)
 480:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           {
 481:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****             return HAL_ERROR;
 482:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           }
 483:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****         }
 484:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       }
 485:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     }
 486:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 487:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     /* Set the voltage range */
 488:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     MODIFY_REG (PWR->D3CR, PWR_D3CR_VOS, VoltageScaling);
 489:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   }
 490:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #else  /* STM32H72xxx and STM32H73xxx lines */
 491:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Set the voltage range */
 492:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   MODIFY_REG(PWR->D3CR, PWR_D3CR_VOS, VoltageScaling);
 181              		.loc 1 492 3 is_stmt 1 view .LVU42
 182 000e 0D4A     		ldr	r2, .L28
 183 0010 9369     		ldr	r3, [r2, #24]
 184 0012 23F44043 		bic	r3, r3, #49152
 185 0016 0343     		orrs	r3, r3, r0
 186 0018 9361     		str	r3, [r2, #24]
 493:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #endif /* defined (SYSCFG_PWRCR_ODEN) */
 494:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #endif /* defined (PWR_SRDCR_VOS) */
 495:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 496:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Get tick */
 497:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   tickstart = HAL_GetTick ();
 187              		.loc 1 497 3 view .LVU43
 188              		.loc 1 497 15 is_stmt 0 view .LVU44
 189 001a FFF7FEFF 		bl	HAL_GetTick
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 13


 190              	.LVL9:
 191              		.loc 1 497 15 view .LVU45
 192 001e 0446     		mov	r4, r0
 193              	.LVL10:
 498:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 499:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Wait till voltage level flag is set */
 500:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   while (__HAL_PWR_GET_FLAG (PWR_FLAG_ACTVOSRDY) == 0U)
 194              		.loc 1 500 3 is_stmt 1 view .LVU46
 195              	.L19:
 196              		.loc 1 500 50 view .LVU47
 197              		.loc 1 500 10 is_stmt 0 view .LVU48
 198 0020 084B     		ldr	r3, .L28
 199 0022 5B68     		ldr	r3, [r3, #4]
 200              		.loc 1 500 50 view .LVU49
 201 0024 13F4005F 		tst	r3, #8192
 202 0028 07D1     		bne	.L27
 501:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   {
 502:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     if ((HAL_GetTick() - tickstart) > PWR_FLAG_SETTING_DELAY)
 203              		.loc 1 502 5 is_stmt 1 view .LVU50
 204              		.loc 1 502 10 is_stmt 0 view .LVU51
 205 002a FFF7FEFF 		bl	HAL_GetTick
 206              	.LVL11:
 207              		.loc 1 502 24 discriminator 1 view .LVU52
 208 002e 001B     		subs	r0, r0, r4
 209              		.loc 1 502 8 discriminator 1 view .LVU53
 210 0030 B0F57A7F 		cmp	r0, #1000
 211 0034 F4D9     		bls	.L19
 503:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     {
 504:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       return HAL_ERROR;
 212              		.loc 1 504 14 view .LVU54
 213 0036 0120     		movs	r0, #1
 214 0038 00E0     		b	.L18
 215              	.L27:
 505:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     }
 506:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   }
 507:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 508:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   return HAL_OK;
 216              		.loc 1 508 10 view .LVU55
 217 003a 0020     		movs	r0, #0
 218              	.L18:
 509:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** }
 219              		.loc 1 509 1 view .LVU56
 220 003c 10BD     		pop	{r4, pc}
 221              	.LVL12:
 222              	.L21:
 223              	.LCFI3:
 224              		.cfi_def_cfa_offset 0
 225              		.cfi_restore 4
 226              		.cfi_restore 14
 428:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   }
 227              		.loc 1 428 12 view .LVU57
 228 003e 0020     		movs	r0, #0
 229              	.LVL13:
 230              		.loc 1 509 1 view .LVU58
 231 0040 7047     		bx	lr
 232              	.L29:
 233 0042 00BF     		.align	2
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 14


 234              	.L28:
 235 0044 00480258 		.word	1476544512
 236              		.cfi_endproc
 237              	.LFE146:
 239              		.section	.text.HAL_PWREx_GetVoltageRange,"ax",%progbits
 240              		.align	1
 241              		.global	HAL_PWREx_GetVoltageRange
 242              		.syntax unified
 243              		.thumb
 244              		.thumb_func
 246              	HAL_PWREx_GetVoltageRange:
 247              	.LFB147:
 510:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 511:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /**
 512:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @brief Get the main internal regulator output voltage. Reflecting the last
 513:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *        VOS value applied to the PMU.
 514:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @retval The current applied VOS selection.
 515:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   */
 516:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** uint32_t HAL_PWREx_GetVoltageRange (void)
 517:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** {
 248              		.loc 1 517 1 is_stmt 1 view -0
 249              		.cfi_startproc
 250              		@ args = 0, pretend = 0, frame = 0
 251              		@ frame_needed = 0, uses_anonymous_args = 0
 252              		@ link register save eliminated.
 518:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Get the active voltage scaling */
 519:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   return (PWR->CSR1 & PWR_CSR1_ACTVOS);
 253              		.loc 1 519 3 view .LVU60
 254              		.loc 1 519 14 is_stmt 0 view .LVU61
 255 0000 024B     		ldr	r3, .L31
 256 0002 5868     		ldr	r0, [r3, #4]
 520:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** }
 257              		.loc 1 520 1 view .LVU62
 258 0004 00F44040 		and	r0, r0, #49152
 259 0008 7047     		bx	lr
 260              	.L32:
 261 000a 00BF     		.align	2
 262              	.L31:
 263 000c 00480258 		.word	1476544512
 264              		.cfi_endproc
 265              	.LFE147:
 267              		.section	.text.HAL_PWREx_ControlStopModeVoltageScaling,"ax",%progbits
 268              		.align	1
 269              		.global	HAL_PWREx_ControlStopModeVoltageScaling
 270              		.syntax unified
 271              		.thumb
 272              		.thumb_func
 274              	HAL_PWREx_ControlStopModeVoltageScaling:
 275              	.LVL14:
 276              	.LFB148:
 521:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 522:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /**
 523:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @brief Configure the main internal regulator output voltage in STOP mode.
 524:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @param  VoltageScaling : Specifies the regulator output voltage when the
 525:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         system enters Stop mode to achieve a tradeoff between performance
 526:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         and power consumption.
 527:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *          This parameter can be one of the following values:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 15


 528:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *            @arg PWR_REGULATOR_SVOS_SCALE3 : Regulator voltage output range
 529:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *                                             3 mode.
 530:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *            @arg PWR_REGULATOR_SVOS_SCALE4 : Regulator voltage output range
 531:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *                                             4 mode.
 532:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *            @arg PWR_REGULATOR_SVOS_SCALE5 : Regulator voltage output range
 533:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *                                             5 mode.
 534:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @note   The Stop mode voltage scaling for SVOS4 and SVOS5 sets the voltage
 535:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         regulator in Low-power (LP) mode to further reduce power consumption.
 536:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         When preselecting SVOS3, the use of the voltage regulator low-power
 537:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         mode (LP) can be selected by LPDS register bit.
 538:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @note   The selected SVOS4 and SVOS5 levels add an additional startup delay
 539:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         when exiting from system Stop mode.
 540:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @retval HAL Status.
 541:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   */
 542:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** HAL_StatusTypeDef HAL_PWREx_ControlStopModeVoltageScaling (uint32_t VoltageScaling)
 543:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** {
 277              		.loc 1 543 1 is_stmt 1 view -0
 278              		.cfi_startproc
 279              		@ args = 0, pretend = 0, frame = 0
 280              		@ frame_needed = 0, uses_anonymous_args = 0
 281              		@ link register save eliminated.
 544:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Check the parameters */
 545:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   assert_param (IS_PWR_STOP_MODE_REGULATOR_VOLTAGE (VoltageScaling));
 282              		.loc 1 545 3 view .LVU64
 546:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 547:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Return the stop mode voltage range */
 548:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   MODIFY_REG (PWR->CR1, PWR_CR1_SVOS, VoltageScaling);
 283              		.loc 1 548 3 view .LVU65
 284 0000 034A     		ldr	r2, .L34
 285 0002 1368     		ldr	r3, [r2]
 286 0004 23F44043 		bic	r3, r3, #49152
 287 0008 0343     		orrs	r3, r3, r0
 288 000a 1360     		str	r3, [r2]
 549:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 550:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   return HAL_OK;
 289              		.loc 1 550 3 view .LVU66
 551:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** }
 290              		.loc 1 551 1 is_stmt 0 view .LVU67
 291 000c 0020     		movs	r0, #0
 292              	.LVL15:
 293              		.loc 1 551 1 view .LVU68
 294 000e 7047     		bx	lr
 295              	.L35:
 296              		.align	2
 297              	.L34:
 298 0010 00480258 		.word	1476544512
 299              		.cfi_endproc
 300              	.LFE148:
 302              		.section	.text.HAL_PWREx_GetStopModeVoltageRange,"ax",%progbits
 303              		.align	1
 304              		.global	HAL_PWREx_GetStopModeVoltageRange
 305              		.syntax unified
 306              		.thumb
 307              		.thumb_func
 309              	HAL_PWREx_GetStopModeVoltageRange:
 310              	.LFB149:
 552:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 16


 553:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /**
 554:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @brief Get the main internal regulator output voltage in STOP mode.
 555:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @retval The actual applied VOS selection.
 556:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   */
 557:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** uint32_t HAL_PWREx_GetStopModeVoltageRange (void)
 558:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** {
 311              		.loc 1 558 1 is_stmt 1 view -0
 312              		.cfi_startproc
 313              		@ args = 0, pretend = 0, frame = 0
 314              		@ frame_needed = 0, uses_anonymous_args = 0
 315              		@ link register save eliminated.
 559:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Return the stop voltage scaling */
 560:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   return (PWR->CR1 & PWR_CR1_SVOS);
 316              		.loc 1 560 3 view .LVU70
 317              		.loc 1 560 14 is_stmt 0 view .LVU71
 318 0000 024B     		ldr	r3, .L37
 319 0002 1868     		ldr	r0, [r3]
 561:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** }
 320              		.loc 1 561 1 view .LVU72
 321 0004 00F44040 		and	r0, r0, #49152
 322 0008 7047     		bx	lr
 323              	.L38:
 324 000a 00BF     		.align	2
 325              	.L37:
 326 000c 00480258 		.word	1476544512
 327              		.cfi_endproc
 328              	.LFE149:
 330              		.section	.text.HAL_PWREx_EnterSTOPMode,"ax",%progbits
 331              		.align	1
 332              		.global	HAL_PWREx_EnterSTOPMode
 333              		.syntax unified
 334              		.thumb
 335              		.thumb_func
 337              	HAL_PWREx_EnterSTOPMode:
 338              	.LVL16:
 339              	.LFB150:
 562:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /**
 563:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @}
 564:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   */
 565:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 566:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /** @defgroup PWREx_Exported_Functions_Group2 Low Power Control Functions
 567:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @brief    Low power control functions
 568:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *
 569:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** @verbatim
 570:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****  ===============================================================================
 571:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****                      ##### Low power control functions #####
 572:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****  ===============================================================================
 573:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 574:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     *** Domains Low Power modes configuration ***
 575:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     =============================================
 576:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     [..]
 577:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       This section provides the extended low power mode control APIs.
 578:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       The system presents 3 principles domains (D1, D2 and D3) that can be
 579:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       operated in low-power modes (DSTOP or DSTANDBY mode):
 580:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 581:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       (+) DSTOP mode to enters a domain to STOP mode:
 582:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        (++) D1 domain and/or D2 domain enters DSTOP mode only when the CPU
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 17


 583:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****             subsystem is in CSTOP mode and has allocated peripheral in the
 584:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****             domain.
 585:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****             In DSTOP mode the domain bus matrix clock is stopped.
 586:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        (++) The system enters STOP mode using one of the following scenarios:
 587:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****         (+++) D1 domain enters DSTANDBY mode (powered off) and D2, D3 domains
 588:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****               enter DSTOP mode.
 589:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****         (+++) D2 domain enters DSTANDBY mode (powered off) and D1, D3 domains
 590:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****               enter DSTOP mode.
 591:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****         (+++) D3 domain enters DSTANDBY mode (powered off) and D1, D2 domains
 592:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****               enter DSTOP mode.
 593:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****         (+++) D1 and D2 domains enter DSTANDBY mode (powered off) and D3 domain
 594:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****               enters DSTOP mode.
 595:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****         (+++) D1 and D3 domains enter DSTANDBY mode (powered off) and D2 domain
 596:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****               enters DSTOP mode.
 597:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****         (+++) D2 and D3 domains enter DSTANDBY mode (powered off) and D1 domain
 598:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****               enters DSTOP mode.
 599:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****         (+++) D1, D2 and D3 domains enter DSTOP mode.
 600:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        (++) When the system enters STOP mode, the clocks are stopped and the
 601:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****             regulator is running in main or low power mode.
 602:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        (++) D3 domain can be kept in Run mode regardless of the CPU status when
 603:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****             enter STOP mode by using HAL_PWREx_ConfigD3Domain(D3State) function.
 604:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 605:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       (+) DSTANDBY mode to enters a domain to STANDBY mode:
 606:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        (++) The DSTANDBY mode is entered when the PDDS_Dn bit in PWR CPU control
 607:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****             register (PWR_CPUCR) for the Dn domain selects Standby mode.
 608:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        (++) The system enters STANDBY mode only when D1, D2 and D3 domains enter
 609:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****             DSTANDBY mode. Consequently the VCORE supply regulator is powered
 610:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****             off.
 611:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 612:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****    *** DSTOP mode ***
 613:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****    ==================
 614:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     [..]
 615:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       In DStop mode the domain bus matrix clock is stopped.
 616:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       The Flash memory can enter low-power Stop mode when it is enabled through
 617:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       FLPS in PWR_CR1 register. This allows a trade-off between domain DStop
 618:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       restart time and low power consumption.
 619:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     [..]
 620:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       In DStop mode domain peripherals using the LSI or LSE clock and
 621:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       peripherals having a kernel clock request are still able to operate.
 622:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     [..]
 623:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       Before entering DSTOP mode it is recommended to call SCB_CleanDCache
 624:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       function in order to clean the D-Cache and guarantee the data integrity
 625:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       for the SRAM memories.
 626:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 627:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       (+) Entry:
 628:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****          The DSTOP mode is entered using the HAL_PWREx_EnterSTOPMode(Regulator,
 629:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****          STOPEntry, Domain) function with:
 630:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****          (++) Regulator:
 631:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           (+++) PWR_MAINREGULATOR_ON     : Main regulator ON.
 632:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           (+++) PWR_LOWPOWERREGULATOR_ON : Low Power regulator ON.
 633:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****          (++) STOPEntry:
 634:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           (+++) PWR_STOPENTRY_WFI : enter STOP mode with WFI instruction
 635:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           (+++) PWR_STOPENTRY_WFE : enter STOP mode with WFE instruction
 636:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****          (++) Domain:
 637:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           (+++) PWR_D1_DOMAIN : Enters D1/CD domain to DSTOP mode.
 638:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           (+++) PWR_D2_DOMAIN : Enters D2 domain to DSTOP mode.
 639:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           (+++) PWR_D3_DOMAIN : Enters D3/SRD domain to DSTOP mode.
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 18


 640:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 641:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       (+) Exit:
 642:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****         Any EXTI Line (Internal or External) configured in Interrupt/Event mode.
 643:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 644:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****    *** DSTANDBY mode ***
 645:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****    =====================
 646:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     [..]
 647:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       In DStandby mode:
 648:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****         (+) The domain bus matrix clock is stopped.
 649:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****         (+) The domain is powered down and the domain RAM and register contents
 650:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****             are lost.
 651:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     [..]
 652:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       Before entering DSTANDBY mode it is recommended to call SCB_CleanDCache
 653:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       function in order to clean the D-Cache and guarantee the data integrity
 654:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       for the SRAM memories.
 655:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 656:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       (+) Entry:
 657:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****          The DSTANDBY mode is entered using the HAL_PWREx_EnterSTANDBYMode
 658:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****          (Domain) function with:
 659:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        (++) Domain:
 660:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****         (+++) PWR_D1_DOMAIN : Enters D1/CD domain to DSTANDBY mode.
 661:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****         (+++) PWR_D2_DOMAIN : Enters D2 domain to DSTANDBY mode.
 662:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****         (+++) PWR_D3_DOMAIN : Enters D3/SRD domain to DSTANDBY mode.
 663:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 664:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       (+) Exit:
 665:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****         WKUP pin rising or falling edge, RTC alarm (Alarm A and Alarm B), RTC
 666:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****         wakeup, tamper event, time stamp event, external reset in NRST pin,
 667:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****         IWDG reset.
 668:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 669:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****    *** Keep D3/SRD in RUN mode ***
 670:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****    ===============================
 671:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     [..]
 672:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       D3/SRD domain can be kept in Run mode regardless of the CPU status when
 673:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       entering STOP mode by using HAL_PWREx_ConfigD3Domain(D3State) function
 674:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       with :
 675:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        (+) D3State:
 676:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****         (++) PWR_D3_DOMAIN_STOP : D3/SDR domain follows the CPU sub-system
 677:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****                                   mode.
 678:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****         (++) PWR_D3_DOMAIN_RUN : D3/SRD domain remains in Run mode regardless
 679:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****                                  of CPU subsystem mode.
 680:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 681:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     *** FLASH Power Down configuration ****
 682:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     =======================================
 683:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     [..]
 684:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       By setting the FLPS bit in the PWR_CR1 register using the
 685:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       HAL_PWREx_EnableFlashPowerDown() function, the Flash memory also enters
 686:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       power down mode when the device enters STOP mode. When the Flash memory is
 687:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       in power down mode, an additional startup delay is incurred when waking up
 688:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       from STOP mode.
 689:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 690:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     *** Wakeup Pins configuration ****
 691:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     ===================================
 692:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     [..]
 693:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       Wakeup pins allow the system to exit from Standby mode. The configuration
 694:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       of wakeup pins is done with the HAL_PWREx_EnableWakeUpPin(sPinParams)
 695:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       function with:
 696:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        (+) sPinParams: structure to enable and configure a wakeup pin:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 19


 697:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****         (++) WakeUpPin: Wakeup pin to be enabled.
 698:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****         (++) PinPolarity: Wakeup pin polarity (rising or falling edge).
 699:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****         (++) PinPull: Wakeup pin pull (no pull, pull-up or pull-down).
 700:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     [..]
 701:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       The wakeup pins are internally connected to the EXTI lines [55-60] to
 702:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       generate an interrupt if enabled. The EXTI lines configuration is done by
 703:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       the HAL_EXTI_Dx_EventInputConfig() functions defined in the stm32h7xxhal.c
 704:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       file.
 705:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     [..]
 706:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       When a wakeup pin event is received the HAL_PWREx_WAKEUP_PIN_IRQHandler is
 707:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       called and the appropriate flag is set in the PWR_WKUPFR register. Then in
 708:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       the HAL_PWREx_WAKEUP_PIN_IRQHandler function the wakeup pin flag will be
 709:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       cleared and the appropriate user callback will be called. The user can add
 710:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       his own code by customization of function pointer HAL_PWREx_WKUPx_Callback.
 711:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 712:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** @endverbatim
 713:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @{
 714:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   */
 715:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 716:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #if defined (PWR_CPUCR_RETDS_CD)
 717:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /**
 718:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @brief Enter the system to STOP mode with main domain in DSTOP2.
 719:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @note   In STOP mode, the domain bus matrix clock is stalled.
 720:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @note   In STOP mode, memories and registers are maintained and peripherals
 721:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         in CPU domain are no longer operational.
 722:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @note   All clocks in the VCORE domain are stopped, the PLL, the HSI and the
 723:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         HSE oscillators are disabled. Only Peripherals that have wakeup
 724:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         capability can switch on the HSI to receive a frame, and switch off
 725:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         the HSI after receiving the frame if it is not a wakeup frame. In
 726:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         this case the HSI clock is propagated only to the peripheral
 727:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         requesting it.
 728:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @note   When exiting STOP mode by issuing an interrupt or a wakeup event,
 729:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         the HSI RC oscillator is selected as system clock if STOPWUCK bit in
 730:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         RCC_CFGR register is set.
 731:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @param  Regulator : Specifies the regulator state in STOP mode.
 732:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *          This parameter can be one of the following values:
 733:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *            @arg PWR_MAINREGULATOR_ON     : STOP mode with regulator ON.
 734:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *            @arg PWR_LOWPOWERREGULATOR_ON : STOP mode with low power
 735:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *                                            regulator ON.
 736:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @param  STOPEntry : Specifies if STOP mode in entered with WFI or WFE
 737:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *                     intrinsic instruction.
 738:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *          This parameter can be one of the following values:
 739:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *            @arg PWR_STOPENTRY_WFI : Enter STOP mode with WFI instruction.
 740:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *            @arg PWR_STOPENTRY_WFE : Enter STOP mode with WFE instruction.
 741:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @retval None.
 742:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   */
 743:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** void HAL_PWREx_EnterSTOP2Mode (uint32_t Regulator, uint8_t STOPEntry)
 744:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** {
 745:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Check the parameters */
 746:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   assert_param (IS_PWR_REGULATOR (Regulator));
 747:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   assert_param (IS_PWR_STOP_ENTRY (STOPEntry));
 748:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 749:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Select the regulator state in Stop mode */
 750:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   MODIFY_REG (PWR->CR1, PWR_CR1_LPDS, Regulator);
 751:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 752:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Go to DStop2 mode (deep retention) when CPU domain enters Deepsleep */
 753:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   SET_BIT (PWR->CPUCR, PWR_CPUCR_RETDS_CD);
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 20


 754:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 755:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Keep DSTOP mode when SmartRun domain enters Deepsleep */
 756:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   CLEAR_BIT (PWR->CPUCR, PWR_CPUCR_PDDS_SRD);
 757:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 758:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Set SLEEPDEEP bit of Cortex System Control Register */
 759:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   SET_BIT (SCB->SCR, SCB_SCR_SLEEPDEEP_Msk);
 760:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 761:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Ensure that all instructions are done before entering STOP mode */
 762:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   __ISB ();
 763:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   __DSB ();
 764:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 765:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Select Stop mode entry */
 766:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   if (STOPEntry == PWR_STOPENTRY_WFI)
 767:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   {
 768:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     /* Request Wait For Interrupt */
 769:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     __WFI ();
 770:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   }
 771:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   else
 772:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   {
 773:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     /* Request Wait For Event */
 774:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     __WFE ();
 775:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   }
 776:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 777:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Clear SLEEPDEEP bit of Cortex-Mx in the System Control Register */
 778:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   CLEAR_BIT (SCB->SCR, SCB_SCR_SLEEPDEEP_Msk);
 779:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** }
 780:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #endif /* defined (PWR_CPUCR_RETDS_CD) */
 781:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 782:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /**
 783:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @brief Enter a Domain to DSTOP mode.
 784:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @note   This API gives flexibility to manage independently each domain STOP
 785:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         mode. For dual core lines, this API should be executed with the
 786:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         corresponding Cortex-Mx to enter domain to DSTOP mode. When it is
 787:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         executed by all available Cortex-Mx, the system enter to STOP mode.
 788:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         For single core lines, calling this API with domain parameter set to
 789:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         PWR_D1_DOMAIN (D1/CD), the whole system will enter in STOP mode
 790:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         independently of PWR_CPUCR_PDDS_Dx bits values if RUN_D3 bit in the
 791:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         CPUCR_RUN_D3 is cleared.
 792:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @note   In DStop mode the domain bus matrix clock is stopped.
 793:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @note   The system D3/SRD domain enter Stop mode only when the CPU subsystem
 794:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         is in CStop mode, the EXTI wakeup sources are inactive and at least
 795:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         one PDDS_Dn bit in PWR CPU control register (PWR_CPUCR) for
 796:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         any domain request Stop.
 797:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @note   Before entering DSTOP mode it is recommended to call SCB_CleanDCache
 798:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         function in order to clean the D-Cache and guarantee the data
 799:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         integrity for the SRAM memories.
 800:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @note   In System Stop mode, the domain peripherals that use the LSI or LSE
 801:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         clock, and the peripherals that have a kernel clock request to
 802:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         select HSI or CSI as source, are still able to operate.
 803:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @param  Regulator : Specifies the regulator state in STOP mode.
 804:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *          This parameter can be one of the following values:
 805:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *            @arg PWR_MAINREGULATOR_ON     : STOP mode with regulator ON.
 806:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *            @arg PWR_LOWPOWERREGULATOR_ON : STOP mode with low power
 807:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *                                            regulator ON.
 808:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @param  STOPEntry : Specifies if STOP mode in entered with WFI or WFE
 809:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *                     intrinsic instruction.
 810:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *          This parameter can be one of the following values:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 21


 811:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *            @arg PWR_STOPENTRY_WFI : Enter STOP mode with WFI instruction.
 812:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *            @arg PWR_STOPENTRY_WFE : Enter STOP mode with WFE instruction.
 813:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @param  Domain : Specifies the Domain to enter in DSTOP mode.
 814:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *          This parameter can be one of the following values:
 815:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *            @arg PWR_D1_DOMAIN : Enter D1/CD Domain to DSTOP mode.
 816:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *            @arg PWR_D2_DOMAIN : Enter D2 Domain to DSTOP mode.
 817:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *            @arg PWR_D3_DOMAIN : Enter D3/SRD Domain to DSTOP mode.
 818:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @retval None.
 819:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   */
 820:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** void HAL_PWREx_EnterSTOPMode (uint32_t Regulator, uint8_t STOPEntry, uint32_t Domain)
 821:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** {
 340              		.loc 1 821 1 is_stmt 1 view -0
 341              		.cfi_startproc
 342              		@ args = 0, pretend = 0, frame = 0
 343              		@ frame_needed = 0, uses_anonymous_args = 0
 344              		@ link register save eliminated.
 345              		.loc 1 821 1 is_stmt 0 view .LVU74
 346 0000 10B4     		push	{r4}
 347              	.LCFI4:
 348              		.cfi_def_cfa_offset 4
 349              		.cfi_offset 4, -4
 822:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Check the parameters */
 823:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   assert_param (IS_PWR_REGULATOR (Regulator));
 350              		.loc 1 823 3 is_stmt 1 view .LVU75
 824:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   assert_param (IS_PWR_STOP_ENTRY (STOPEntry));
 351              		.loc 1 824 3 view .LVU76
 825:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   assert_param (IS_PWR_DOMAIN (Domain));
 352              		.loc 1 825 3 view .LVU77
 826:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 827:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Select the regulator state in Stop mode */
 828:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   MODIFY_REG (PWR->CR1, PWR_CR1_LPDS, Regulator);
 353              		.loc 1 828 3 view .LVU78
 354 0002 174C     		ldr	r4, .L48
 355 0004 2368     		ldr	r3, [r4]
 356 0006 23F00103 		bic	r3, r3, #1
 357 000a 0343     		orrs	r3, r3, r0
 358 000c 2360     		str	r3, [r4]
 829:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 830:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Select the domain Power Down DeepSleep */
 831:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   if (Domain == PWR_D1_DOMAIN)
 359              		.loc 1 831 3 view .LVU79
 360              		.loc 1 831 6 is_stmt 0 view .LVU80
 361 000e CAB9     		cbnz	r2, .L40
 832:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   {
 833:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #if defined (DUAL_CORE)
 834:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     /* Check current core */
 835:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     if (HAL_GetCurrentCPUID () != CM7_CPUID)
 836:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     {
 837:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       /*
 838:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****          When the domain selected and the cortex-mx don't match, entering stop
 839:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****          mode will not be performed
 840:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       */
 841:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       return;
 842:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     }
 843:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #endif /* defined (DUAL_CORE) */
 844:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 845:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     /* Keep DSTOP mode when D1/CD domain enters Deepsleep */
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 22


 846:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     CLEAR_BIT (PWR->CPUCR, PWR_CPUCR_PDDS_D1);
 362              		.loc 1 846 5 is_stmt 1 view .LVU81
 363 0010 2369     		ldr	r3, [r4, #16]
 364 0012 23F00103 		bic	r3, r3, #1
 365 0016 2361     		str	r3, [r4, #16]
 847:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 848:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     /* Set SLEEPDEEP bit of Cortex System Control Register */
 849:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     SET_BIT (SCB->SCR, SCB_SCR_SLEEPDEEP_Msk);
 366              		.loc 1 849 5 view .LVU82
 367 0018 124A     		ldr	r2, .L48+4
 368              	.LVL17:
 369              		.loc 1 849 5 is_stmt 0 view .LVU83
 370 001a 1369     		ldr	r3, [r2, #16]
 371 001c 43F00403 		orr	r3, r3, #4
 372 0020 1361     		str	r3, [r2, #16]
 850:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 851:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     /* Ensure that all instructions are done before entering STOP mode */
 852:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     __DSB ();
 373              		.loc 1 852 5 is_stmt 1 view .LVU84
 374              	.LBB22:
 375              	.LBI22:
 376              		.file 2 "Drivers/CMSIS/Include/cmsis_gcc.h"
   1:Drivers/CMSIS/Include/cmsis_gcc.h **** /**************************************************************************//**
   2:Drivers/CMSIS/Include/cmsis_gcc.h ****  * @file     cmsis_gcc.h
   3:Drivers/CMSIS/Include/cmsis_gcc.h ****  * @brief    CMSIS compiler GCC header file
   4:Drivers/CMSIS/Include/cmsis_gcc.h ****  * @version  V5.2.0
   5:Drivers/CMSIS/Include/cmsis_gcc.h ****  * @date     08. May 2019
   6:Drivers/CMSIS/Include/cmsis_gcc.h ****  ******************************************************************************/
   7:Drivers/CMSIS/Include/cmsis_gcc.h **** /*
   8:Drivers/CMSIS/Include/cmsis_gcc.h ****  * Copyright (c) 2009-2019 Arm Limited. All rights reserved.
   9:Drivers/CMSIS/Include/cmsis_gcc.h ****  *
  10:Drivers/CMSIS/Include/cmsis_gcc.h ****  * SPDX-License-Identifier: Apache-2.0
  11:Drivers/CMSIS/Include/cmsis_gcc.h ****  *
  12:Drivers/CMSIS/Include/cmsis_gcc.h ****  * Licensed under the Apache License, Version 2.0 (the License); you may
  13:Drivers/CMSIS/Include/cmsis_gcc.h ****  * not use this file except in compliance with the License.
  14:Drivers/CMSIS/Include/cmsis_gcc.h ****  * You may obtain a copy of the License at
  15:Drivers/CMSIS/Include/cmsis_gcc.h ****  *
  16:Drivers/CMSIS/Include/cmsis_gcc.h ****  * www.apache.org/licenses/LICENSE-2.0
  17:Drivers/CMSIS/Include/cmsis_gcc.h ****  *
  18:Drivers/CMSIS/Include/cmsis_gcc.h ****  * Unless required by applicable law or agreed to in writing, software
  19:Drivers/CMSIS/Include/cmsis_gcc.h ****  * distributed under the License is distributed on an AS IS BASIS, WITHOUT
  20:Drivers/CMSIS/Include/cmsis_gcc.h ****  * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  21:Drivers/CMSIS/Include/cmsis_gcc.h ****  * See the License for the specific language governing permissions and
  22:Drivers/CMSIS/Include/cmsis_gcc.h ****  * limitations under the License.
  23:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
  24:Drivers/CMSIS/Include/cmsis_gcc.h **** 
  25:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef __CMSIS_GCC_H
  26:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __CMSIS_GCC_H
  27:Drivers/CMSIS/Include/cmsis_gcc.h **** 
  28:Drivers/CMSIS/Include/cmsis_gcc.h **** /* ignore some GCC warnings */
  29:Drivers/CMSIS/Include/cmsis_gcc.h **** #pragma GCC diagnostic push
  30:Drivers/CMSIS/Include/cmsis_gcc.h **** #pragma GCC diagnostic ignored "-Wsign-conversion"
  31:Drivers/CMSIS/Include/cmsis_gcc.h **** #pragma GCC diagnostic ignored "-Wconversion"
  32:Drivers/CMSIS/Include/cmsis_gcc.h **** #pragma GCC diagnostic ignored "-Wunused-parameter"
  33:Drivers/CMSIS/Include/cmsis_gcc.h **** 
  34:Drivers/CMSIS/Include/cmsis_gcc.h **** /* Fallback for __has_builtin */
  35:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef __has_builtin
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 23


  36:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __has_builtin(x) (0)
  37:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  38:Drivers/CMSIS/Include/cmsis_gcc.h **** 
  39:Drivers/CMSIS/Include/cmsis_gcc.h **** /* CMSIS compiler specific defines */
  40:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __ASM
  41:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __ASM                                  __asm
  42:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  43:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __INLINE
  44:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __INLINE                               inline
  45:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  46:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __STATIC_INLINE
  47:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __STATIC_INLINE                        static inline
  48:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  49:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __STATIC_FORCEINLINE                 
  50:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __STATIC_FORCEINLINE                   __attribute__((always_inline)) static inline
  51:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif                                           
  52:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __NO_RETURN
  53:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __NO_RETURN                            __attribute__((__noreturn__))
  54:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  55:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __USED
  56:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __USED                                 __attribute__((used))
  57:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  58:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __WEAK
  59:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __WEAK                                 __attribute__((weak))
  60:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  61:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __PACKED
  62:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __PACKED                               __attribute__((packed, aligned(1)))
  63:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  64:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __PACKED_STRUCT
  65:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __PACKED_STRUCT                        struct __attribute__((packed, aligned(1)))
  66:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  67:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __PACKED_UNION
  68:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __PACKED_UNION                         union __attribute__((packed, aligned(1)))
  69:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  70:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __UNALIGNED_UINT32        /* deprecated */
  71:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic push
  72:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic ignored "-Wpacked"
  73:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic ignored "-Wattributes"
  74:Drivers/CMSIS/Include/cmsis_gcc.h ****   struct __attribute__((packed)) T_UINT32 { uint32_t v; };
  75:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic pop
  76:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __UNALIGNED_UINT32(x)                  (((struct T_UINT32 *)(x))->v)
  77:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  78:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __UNALIGNED_UINT16_WRITE
  79:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic push
  80:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic ignored "-Wpacked"
  81:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic ignored "-Wattributes"
  82:Drivers/CMSIS/Include/cmsis_gcc.h ****   __PACKED_STRUCT T_UINT16_WRITE { uint16_t v; };
  83:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic pop
  84:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __UNALIGNED_UINT16_WRITE(addr, val)    (void)((((struct T_UINT16_WRITE *)(void *)(addr))-
  85:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  86:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __UNALIGNED_UINT16_READ
  87:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic push
  88:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic ignored "-Wpacked"
  89:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic ignored "-Wattributes"
  90:Drivers/CMSIS/Include/cmsis_gcc.h ****   __PACKED_STRUCT T_UINT16_READ { uint16_t v; };
  91:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic pop
  92:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __UNALIGNED_UINT16_READ(addr)          (((const struct T_UINT16_READ *)(const void *)(add
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 24


  93:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  94:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __UNALIGNED_UINT32_WRITE
  95:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic push
  96:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic ignored "-Wpacked"
  97:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic ignored "-Wattributes"
  98:Drivers/CMSIS/Include/cmsis_gcc.h ****   __PACKED_STRUCT T_UINT32_WRITE { uint32_t v; };
  99:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic pop
 100:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __UNALIGNED_UINT32_WRITE(addr, val)    (void)((((struct T_UINT32_WRITE *)(void *)(addr))-
 101:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 102:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __UNALIGNED_UINT32_READ
 103:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic push
 104:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic ignored "-Wpacked"
 105:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic ignored "-Wattributes"
 106:Drivers/CMSIS/Include/cmsis_gcc.h ****   __PACKED_STRUCT T_UINT32_READ { uint32_t v; };
 107:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic pop
 108:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __UNALIGNED_UINT32_READ(addr)          (((const struct T_UINT32_READ *)(const void *)(add
 109:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 110:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __ALIGNED
 111:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __ALIGNED(x)                           __attribute__((aligned(x)))
 112:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 113:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __RESTRICT
 114:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __RESTRICT                             __restrict
 115:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 116:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __COMPILER_BARRIER
 117:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __COMPILER_BARRIER()                   __ASM volatile("":::"memory")
 118:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 119:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 120:Drivers/CMSIS/Include/cmsis_gcc.h **** /* #########################  Startup and Lowlevel Init  ######################## */
 121:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 122:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef __PROGRAM_START
 123:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 124:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 125:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Initializes data and bss sections
 126:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details This default implementations initialized all data and additional bss
 127:Drivers/CMSIS/Include/cmsis_gcc.h ****            sections relying on .copy.table and .zero.table specified properly
 128:Drivers/CMSIS/Include/cmsis_gcc.h ****            in the used linker script.
 129:Drivers/CMSIS/Include/cmsis_gcc.h ****   
 130:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 131:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE __NO_RETURN void __cmsis_start(void)
 132:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 133:Drivers/CMSIS/Include/cmsis_gcc.h ****   extern void _start(void) __NO_RETURN;
 134:Drivers/CMSIS/Include/cmsis_gcc.h ****   
 135:Drivers/CMSIS/Include/cmsis_gcc.h ****   typedef struct {
 136:Drivers/CMSIS/Include/cmsis_gcc.h ****     uint32_t const* src;
 137:Drivers/CMSIS/Include/cmsis_gcc.h ****     uint32_t* dest;
 138:Drivers/CMSIS/Include/cmsis_gcc.h ****     uint32_t  wlen;
 139:Drivers/CMSIS/Include/cmsis_gcc.h ****   } __copy_table_t;
 140:Drivers/CMSIS/Include/cmsis_gcc.h ****   
 141:Drivers/CMSIS/Include/cmsis_gcc.h ****   typedef struct {
 142:Drivers/CMSIS/Include/cmsis_gcc.h ****     uint32_t* dest;
 143:Drivers/CMSIS/Include/cmsis_gcc.h ****     uint32_t  wlen;
 144:Drivers/CMSIS/Include/cmsis_gcc.h ****   } __zero_table_t;
 145:Drivers/CMSIS/Include/cmsis_gcc.h ****   
 146:Drivers/CMSIS/Include/cmsis_gcc.h ****   extern const __copy_table_t __copy_table_start__;
 147:Drivers/CMSIS/Include/cmsis_gcc.h ****   extern const __copy_table_t __copy_table_end__;
 148:Drivers/CMSIS/Include/cmsis_gcc.h ****   extern const __zero_table_t __zero_table_start__;
 149:Drivers/CMSIS/Include/cmsis_gcc.h ****   extern const __zero_table_t __zero_table_end__;
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 25


 150:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 151:Drivers/CMSIS/Include/cmsis_gcc.h ****   for (__copy_table_t const* pTable = &__copy_table_start__; pTable < &__copy_table_end__; ++pTable
 152:Drivers/CMSIS/Include/cmsis_gcc.h ****     for(uint32_t i=0u; i<pTable->wlen; ++i) {
 153:Drivers/CMSIS/Include/cmsis_gcc.h ****       pTable->dest[i] = pTable->src[i];
 154:Drivers/CMSIS/Include/cmsis_gcc.h ****     }
 155:Drivers/CMSIS/Include/cmsis_gcc.h ****   }
 156:Drivers/CMSIS/Include/cmsis_gcc.h ****  
 157:Drivers/CMSIS/Include/cmsis_gcc.h ****   for (__zero_table_t const* pTable = &__zero_table_start__; pTable < &__zero_table_end__; ++pTable
 158:Drivers/CMSIS/Include/cmsis_gcc.h ****     for(uint32_t i=0u; i<pTable->wlen; ++i) {
 159:Drivers/CMSIS/Include/cmsis_gcc.h ****       pTable->dest[i] = 0u;
 160:Drivers/CMSIS/Include/cmsis_gcc.h ****     }
 161:Drivers/CMSIS/Include/cmsis_gcc.h ****   }
 162:Drivers/CMSIS/Include/cmsis_gcc.h ****  
 163:Drivers/CMSIS/Include/cmsis_gcc.h ****   _start();
 164:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 165:Drivers/CMSIS/Include/cmsis_gcc.h ****   
 166:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __PROGRAM_START           __cmsis_start
 167:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 168:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 169:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef __INITIAL_SP
 170:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __INITIAL_SP              __StackTop
 171:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 172:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 173:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef __STACK_LIMIT
 174:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __STACK_LIMIT             __StackLimit
 175:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 176:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 177:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef __VECTOR_TABLE
 178:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __VECTOR_TABLE            __Vectors
 179:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 180:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 181:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef __VECTOR_TABLE_ATTRIBUTE
 182:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __VECTOR_TABLE_ATTRIBUTE  __attribute((used, section(".vectors")))
 183:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 184:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 185:Drivers/CMSIS/Include/cmsis_gcc.h **** /* ###########################  Core Function Access  ########################### */
 186:Drivers/CMSIS/Include/cmsis_gcc.h **** /** \ingroup  CMSIS_Core_FunctionInterface
 187:Drivers/CMSIS/Include/cmsis_gcc.h ****     \defgroup CMSIS_Core_RegAccFunctions CMSIS Core Register Access Functions
 188:Drivers/CMSIS/Include/cmsis_gcc.h ****   @{
 189:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 190:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 191:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 192:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Enable IRQ Interrupts
 193:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Enables IRQ interrupts by clearing the I-bit in the CPSR.
 194:Drivers/CMSIS/Include/cmsis_gcc.h ****            Can only be executed in Privileged modes.
 195:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 196:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __enable_irq(void)
 197:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 198:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("cpsie i" : : : "memory");
 199:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 200:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 201:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 202:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 203:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Disable IRQ Interrupts
 204:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Disables IRQ interrupts by setting the I-bit in the CPSR.
 205:Drivers/CMSIS/Include/cmsis_gcc.h ****            Can only be executed in Privileged modes.
 206:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 26


 207:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __disable_irq(void)
 208:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 209:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("cpsid i" : : : "memory");
 210:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 211:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 212:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 213:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 214:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get Control Register
 215:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the content of the Control Register.
 216:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               Control Register value
 217:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 218:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __get_CONTROL(void)
 219:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 220:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 221:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 222:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, control" : "=r" (result) );
 223:Drivers/CMSIS/Include/cmsis_gcc.h ****   return(result);
 224:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 225:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 226:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 227:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (defined (__ARM_FEATURE_CMSE ) && (__ARM_FEATURE_CMSE == 3))
 228:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 229:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get Control Register (non-secure)
 230:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the content of the non-secure Control Register when in secure mode.
 231:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               non-secure Control Register value
 232:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 233:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __TZ_get_CONTROL_NS(void)
 234:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 235:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 236:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 237:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, control_ns" : "=r" (result) );
 238:Drivers/CMSIS/Include/cmsis_gcc.h ****   return(result);
 239:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 240:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 241:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 242:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 243:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 244:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Set Control Register
 245:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Writes the given value to the Control Register.
 246:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    control  Control Register value to set
 247:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 248:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __set_CONTROL(uint32_t control)
 249:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 250:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MSR control, %0" : : "r" (control) : "memory");
 251:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 252:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 253:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 254:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (defined (__ARM_FEATURE_CMSE ) && (__ARM_FEATURE_CMSE == 3))
 255:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 256:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Set Control Register (non-secure)
 257:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Writes the given value to the non-secure Control Register when in secure state.
 258:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    control  Control Register value to set
 259:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 260:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __TZ_set_CONTROL_NS(uint32_t control)
 261:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 262:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MSR control_ns, %0" : : "r" (control) : "memory");
 263:Drivers/CMSIS/Include/cmsis_gcc.h **** }
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 27


 264:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 265:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 266:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 267:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 268:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get IPSR Register
 269:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the content of the IPSR Register.
 270:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               IPSR Register value
 271:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 272:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __get_IPSR(void)
 273:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 274:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 275:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 276:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, ipsr" : "=r" (result) );
 277:Drivers/CMSIS/Include/cmsis_gcc.h ****   return(result);
 278:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 279:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 280:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 281:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 282:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get APSR Register
 283:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the content of the APSR Register.
 284:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               APSR Register value
 285:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 286:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __get_APSR(void)
 287:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 288:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 289:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 290:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, apsr" : "=r" (result) );
 291:Drivers/CMSIS/Include/cmsis_gcc.h ****   return(result);
 292:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 293:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 294:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 295:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 296:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get xPSR Register
 297:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the content of the xPSR Register.
 298:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               xPSR Register value
 299:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 300:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __get_xPSR(void)
 301:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 302:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 303:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 304:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, xpsr" : "=r" (result) );
 305:Drivers/CMSIS/Include/cmsis_gcc.h ****   return(result);
 306:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 307:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 308:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 309:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 310:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get Process Stack Pointer
 311:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the current value of the Process Stack Pointer (PSP).
 312:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               PSP Register value
 313:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 314:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __get_PSP(void)
 315:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 316:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 317:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 318:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, psp"  : "=r" (result) );
 319:Drivers/CMSIS/Include/cmsis_gcc.h ****   return(result);
 320:Drivers/CMSIS/Include/cmsis_gcc.h **** }
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 28


 321:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 322:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 323:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (defined (__ARM_FEATURE_CMSE ) && (__ARM_FEATURE_CMSE == 3))
 324:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 325:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get Process Stack Pointer (non-secure)
 326:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the current value of the non-secure Process Stack Pointer (PSP) when in secure s
 327:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               PSP Register value
 328:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 329:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __TZ_get_PSP_NS(void)
 330:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 331:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 332:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 333:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, psp_ns"  : "=r" (result) );
 334:Drivers/CMSIS/Include/cmsis_gcc.h ****   return(result);
 335:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 336:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 337:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 338:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 339:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 340:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Set Process Stack Pointer
 341:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Assigns the given value to the Process Stack Pointer (PSP).
 342:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    topOfProcStack  Process Stack Pointer value to set
 343:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 344:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __set_PSP(uint32_t topOfProcStack)
 345:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 346:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MSR psp, %0" : : "r" (topOfProcStack) : );
 347:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 348:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 349:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 350:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (defined (__ARM_FEATURE_CMSE ) && (__ARM_FEATURE_CMSE == 3))
 351:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 352:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Set Process Stack Pointer (non-secure)
 353:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Assigns the given value to the non-secure Process Stack Pointer (PSP) when in secure sta
 354:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    topOfProcStack  Process Stack Pointer value to set
 355:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 356:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __TZ_set_PSP_NS(uint32_t topOfProcStack)
 357:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 358:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MSR psp_ns, %0" : : "r" (topOfProcStack) : );
 359:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 360:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 361:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 362:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 363:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 364:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get Main Stack Pointer
 365:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the current value of the Main Stack Pointer (MSP).
 366:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               MSP Register value
 367:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 368:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __get_MSP(void)
 369:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 370:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 371:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 372:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, msp" : "=r" (result) );
 373:Drivers/CMSIS/Include/cmsis_gcc.h ****   return(result);
 374:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 375:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 376:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 377:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (defined (__ARM_FEATURE_CMSE ) && (__ARM_FEATURE_CMSE == 3))
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 29


 378:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 379:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get Main Stack Pointer (non-secure)
 380:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the current value of the non-secure Main Stack Pointer (MSP) when in secure stat
 381:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               MSP Register value
 382:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 383:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __TZ_get_MSP_NS(void)
 384:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 385:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 386:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 387:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, msp_ns" : "=r" (result) );
 388:Drivers/CMSIS/Include/cmsis_gcc.h ****   return(result);
 389:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 390:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 391:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 392:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 393:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 394:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Set Main Stack Pointer
 395:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Assigns the given value to the Main Stack Pointer (MSP).
 396:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    topOfMainStack  Main Stack Pointer value to set
 397:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 398:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __set_MSP(uint32_t topOfMainStack)
 399:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 400:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MSR msp, %0" : : "r" (topOfMainStack) : );
 401:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 402:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 403:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 404:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (defined (__ARM_FEATURE_CMSE ) && (__ARM_FEATURE_CMSE == 3))
 405:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 406:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Set Main Stack Pointer (non-secure)
 407:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Assigns the given value to the non-secure Main Stack Pointer (MSP) when in secure state.
 408:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    topOfMainStack  Main Stack Pointer value to set
 409:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 410:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __TZ_set_MSP_NS(uint32_t topOfMainStack)
 411:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 412:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MSR msp_ns, %0" : : "r" (topOfMainStack) : );
 413:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 414:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 415:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 416:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 417:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (defined (__ARM_FEATURE_CMSE ) && (__ARM_FEATURE_CMSE == 3))
 418:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 419:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get Stack Pointer (non-secure)
 420:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the current value of the non-secure Stack Pointer (SP) when in secure state.
 421:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               SP Register value
 422:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 423:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __TZ_get_SP_NS(void)
 424:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 425:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 426:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 427:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, sp_ns" : "=r" (result) );
 428:Drivers/CMSIS/Include/cmsis_gcc.h ****   return(result);
 429:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 430:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 431:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 432:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 433:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Set Stack Pointer (non-secure)
 434:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Assigns the given value to the non-secure Stack Pointer (SP) when in secure state.
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 30


 435:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    topOfStack  Stack Pointer value to set
 436:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 437:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __TZ_set_SP_NS(uint32_t topOfStack)
 438:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 439:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MSR sp_ns, %0" : : "r" (topOfStack) : );
 440:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 441:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 442:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 443:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 444:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 445:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get Priority Mask
 446:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the current state of the priority mask bit from the Priority Mask Register.
 447:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               Priority Mask value
 448:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 449:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __get_PRIMASK(void)
 450:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 451:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 452:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 453:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, primask" : "=r" (result) :: "memory");
 454:Drivers/CMSIS/Include/cmsis_gcc.h ****   return(result);
 455:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 456:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 457:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 458:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (defined (__ARM_FEATURE_CMSE ) && (__ARM_FEATURE_CMSE == 3))
 459:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 460:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get Priority Mask (non-secure)
 461:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the current state of the non-secure priority mask bit from the Priority Mask Reg
 462:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               Priority Mask value
 463:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 464:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __TZ_get_PRIMASK_NS(void)
 465:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 466:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 467:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 468:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, primask_ns" : "=r" (result) :: "memory");
 469:Drivers/CMSIS/Include/cmsis_gcc.h ****   return(result);
 470:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 471:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 472:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 473:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 474:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 475:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Set Priority Mask
 476:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Assigns the given value to the Priority Mask Register.
 477:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    priMask  Priority Mask
 478:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 479:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __set_PRIMASK(uint32_t priMask)
 480:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 481:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MSR primask, %0" : : "r" (priMask) : "memory");
 482:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 483:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 484:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 485:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (defined (__ARM_FEATURE_CMSE ) && (__ARM_FEATURE_CMSE == 3))
 486:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 487:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Set Priority Mask (non-secure)
 488:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Assigns the given value to the non-secure Priority Mask Register when in secure state.
 489:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    priMask  Priority Mask
 490:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 491:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __TZ_set_PRIMASK_NS(uint32_t priMask)
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 31


 492:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 493:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MSR primask_ns, %0" : : "r" (priMask) : "memory");
 494:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 495:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 496:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 497:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 498:Drivers/CMSIS/Include/cmsis_gcc.h **** #if ((defined (__ARM_ARCH_7M__      ) && (__ARM_ARCH_7M__      == 1)) || \
 499:Drivers/CMSIS/Include/cmsis_gcc.h ****      (defined (__ARM_ARCH_7EM__     ) && (__ARM_ARCH_7EM__     == 1)) || \
 500:Drivers/CMSIS/Include/cmsis_gcc.h ****      (defined (__ARM_ARCH_8M_MAIN__ ) && (__ARM_ARCH_8M_MAIN__ == 1))    )
 501:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 502:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Enable FIQ
 503:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Enables FIQ interrupts by clearing the F-bit in the CPSR.
 504:Drivers/CMSIS/Include/cmsis_gcc.h ****            Can only be executed in Privileged modes.
 505:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 506:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __enable_fault_irq(void)
 507:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 508:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("cpsie f" : : : "memory");
 509:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 510:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 511:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 512:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 513:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Disable FIQ
 514:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Disables FIQ interrupts by setting the F-bit in the CPSR.
 515:Drivers/CMSIS/Include/cmsis_gcc.h ****            Can only be executed in Privileged modes.
 516:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 517:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __disable_fault_irq(void)
 518:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 519:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("cpsid f" : : : "memory");
 520:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 521:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 522:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 523:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 524:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get Base Priority
 525:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the current value of the Base Priority register.
 526:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               Base Priority register value
 527:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 528:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __get_BASEPRI(void)
 529:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 530:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 531:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 532:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, basepri" : "=r" (result) );
 533:Drivers/CMSIS/Include/cmsis_gcc.h ****   return(result);
 534:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 535:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 536:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 537:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (defined (__ARM_FEATURE_CMSE ) && (__ARM_FEATURE_CMSE == 3))
 538:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 539:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get Base Priority (non-secure)
 540:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the current value of the non-secure Base Priority register when in secure state.
 541:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               Base Priority register value
 542:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 543:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __TZ_get_BASEPRI_NS(void)
 544:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 545:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 546:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 547:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, basepri_ns" : "=r" (result) );
 548:Drivers/CMSIS/Include/cmsis_gcc.h ****   return(result);
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 32


 549:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 550:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 551:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 552:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 553:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 554:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Set Base Priority
 555:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Assigns the given value to the Base Priority register.
 556:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    basePri  Base Priority value to set
 557:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 558:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __set_BASEPRI(uint32_t basePri)
 559:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 560:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MSR basepri, %0" : : "r" (basePri) : "memory");
 561:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 562:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 563:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 564:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (defined (__ARM_FEATURE_CMSE ) && (__ARM_FEATURE_CMSE == 3))
 565:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 566:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Set Base Priority (non-secure)
 567:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Assigns the given value to the non-secure Base Priority register when in secure state.
 568:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    basePri  Base Priority value to set
 569:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 570:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __TZ_set_BASEPRI_NS(uint32_t basePri)
 571:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 572:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MSR basepri_ns, %0" : : "r" (basePri) : "memory");
 573:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 574:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 575:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 576:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 577:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 578:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Set Base Priority with condition
 579:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Assigns the given value to the Base Priority register only if BASEPRI masking is disable
 580:Drivers/CMSIS/Include/cmsis_gcc.h ****            or the new value increases the BASEPRI priority level.
 581:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    basePri  Base Priority value to set
 582:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 583:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __set_BASEPRI_MAX(uint32_t basePri)
 584:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 585:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MSR basepri_max, %0" : : "r" (basePri) : "memory");
 586:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 587:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 588:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 589:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 590:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get Fault Mask
 591:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the current value of the Fault Mask register.
 592:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               Fault Mask register value
 593:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 594:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __get_FAULTMASK(void)
 595:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 596:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 597:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 598:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, faultmask" : "=r" (result) );
 599:Drivers/CMSIS/Include/cmsis_gcc.h ****   return(result);
 600:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 601:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 602:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 603:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (defined (__ARM_FEATURE_CMSE ) && (__ARM_FEATURE_CMSE == 3))
 604:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 605:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get Fault Mask (non-secure)
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 33


 606:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the current value of the non-secure Fault Mask register when in secure state.
 607:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               Fault Mask register value
 608:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 609:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __TZ_get_FAULTMASK_NS(void)
 610:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 611:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 612:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 613:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, faultmask_ns" : "=r" (result) );
 614:Drivers/CMSIS/Include/cmsis_gcc.h ****   return(result);
 615:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 616:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 617:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 618:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 619:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 620:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Set Fault Mask
 621:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Assigns the given value to the Fault Mask register.
 622:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    faultMask  Fault Mask value to set
 623:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 624:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __set_FAULTMASK(uint32_t faultMask)
 625:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 626:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MSR faultmask, %0" : : "r" (faultMask) : "memory");
 627:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 628:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 629:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 630:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (defined (__ARM_FEATURE_CMSE ) && (__ARM_FEATURE_CMSE == 3))
 631:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 632:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Set Fault Mask (non-secure)
 633:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Assigns the given value to the non-secure Fault Mask register when in secure state.
 634:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    faultMask  Fault Mask value to set
 635:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 636:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __TZ_set_FAULTMASK_NS(uint32_t faultMask)
 637:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 638:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MSR faultmask_ns, %0" : : "r" (faultMask) : "memory");
 639:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 640:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 641:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 642:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif /* ((defined (__ARM_ARCH_7M__      ) && (__ARM_ARCH_7M__      == 1)) || \
 643:Drivers/CMSIS/Include/cmsis_gcc.h ****            (defined (__ARM_ARCH_7EM__     ) && (__ARM_ARCH_7EM__     == 1)) || \
 644:Drivers/CMSIS/Include/cmsis_gcc.h ****            (defined (__ARM_ARCH_8M_MAIN__ ) && (__ARM_ARCH_8M_MAIN__ == 1))    ) */
 645:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 646:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 647:Drivers/CMSIS/Include/cmsis_gcc.h **** #if ((defined (__ARM_ARCH_8M_MAIN__ ) && (__ARM_ARCH_8M_MAIN__ == 1)) || \
 648:Drivers/CMSIS/Include/cmsis_gcc.h ****      (defined (__ARM_ARCH_8M_BASE__ ) && (__ARM_ARCH_8M_BASE__ == 1))    )
 649:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 650:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 651:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get Process Stack Pointer Limit
 652:Drivers/CMSIS/Include/cmsis_gcc.h ****   Devices without ARMv8-M Main Extensions (i.e. Cortex-M23) lack the non-secure
 653:Drivers/CMSIS/Include/cmsis_gcc.h ****   Stack Pointer Limit register hence zero is returned always in non-secure
 654:Drivers/CMSIS/Include/cmsis_gcc.h ****   mode.
 655:Drivers/CMSIS/Include/cmsis_gcc.h ****   
 656:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the current value of the Process Stack Pointer Limit (PSPLIM).
 657:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               PSPLIM Register value
 658:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 659:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __get_PSPLIM(void)
 660:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 661:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (!(defined (__ARM_ARCH_8M_MAIN__ ) && (__ARM_ARCH_8M_MAIN__ == 1)) && \
 662:Drivers/CMSIS/Include/cmsis_gcc.h ****     (!defined (__ARM_FEATURE_CMSE) || (__ARM_FEATURE_CMSE < 3)))
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 34


 663:Drivers/CMSIS/Include/cmsis_gcc.h ****     // without main extensions, the non-secure PSPLIM is RAZ/WI
 664:Drivers/CMSIS/Include/cmsis_gcc.h ****   return 0U;
 665:Drivers/CMSIS/Include/cmsis_gcc.h **** #else
 666:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 667:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, psplim"  : "=r" (result) );
 668:Drivers/CMSIS/Include/cmsis_gcc.h ****   return result;
 669:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 670:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 671:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 672:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (defined (__ARM_FEATURE_CMSE) && (__ARM_FEATURE_CMSE == 3))
 673:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 674:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get Process Stack Pointer Limit (non-secure)
 675:Drivers/CMSIS/Include/cmsis_gcc.h ****   Devices without ARMv8-M Main Extensions (i.e. Cortex-M23) lack the non-secure
 676:Drivers/CMSIS/Include/cmsis_gcc.h ****   Stack Pointer Limit register hence zero is returned always.
 677:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 678:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the current value of the non-secure Process Stack Pointer Limit (PSPLIM) when in
 679:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               PSPLIM Register value
 680:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 681:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __TZ_get_PSPLIM_NS(void)
 682:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 683:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (!(defined (__ARM_ARCH_8M_MAIN__ ) && (__ARM_ARCH_8M_MAIN__ == 1)))
 684:Drivers/CMSIS/Include/cmsis_gcc.h ****   // without main extensions, the non-secure PSPLIM is RAZ/WI
 685:Drivers/CMSIS/Include/cmsis_gcc.h ****   return 0U;
 686:Drivers/CMSIS/Include/cmsis_gcc.h **** #else
 687:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 688:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, psplim_ns"  : "=r" (result) );
 689:Drivers/CMSIS/Include/cmsis_gcc.h ****   return result;
 690:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 691:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 692:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 693:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 694:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 695:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 696:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Set Process Stack Pointer Limit
 697:Drivers/CMSIS/Include/cmsis_gcc.h ****   Devices without ARMv8-M Main Extensions (i.e. Cortex-M23) lack the non-secure
 698:Drivers/CMSIS/Include/cmsis_gcc.h ****   Stack Pointer Limit register hence the write is silently ignored in non-secure
 699:Drivers/CMSIS/Include/cmsis_gcc.h ****   mode.
 700:Drivers/CMSIS/Include/cmsis_gcc.h ****   
 701:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Assigns the given value to the Process Stack Pointer Limit (PSPLIM).
 702:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    ProcStackPtrLimit  Process Stack Pointer Limit value to set
 703:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 704:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __set_PSPLIM(uint32_t ProcStackPtrLimit)
 705:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 706:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (!(defined (__ARM_ARCH_8M_MAIN__ ) && (__ARM_ARCH_8M_MAIN__ == 1)) && \
 707:Drivers/CMSIS/Include/cmsis_gcc.h ****     (!defined (__ARM_FEATURE_CMSE) || (__ARM_FEATURE_CMSE < 3)))
 708:Drivers/CMSIS/Include/cmsis_gcc.h ****   // without main extensions, the non-secure PSPLIM is RAZ/WI
 709:Drivers/CMSIS/Include/cmsis_gcc.h ****   (void)ProcStackPtrLimit;
 710:Drivers/CMSIS/Include/cmsis_gcc.h **** #else
 711:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MSR psplim, %0" : : "r" (ProcStackPtrLimit));
 712:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 713:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 714:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 715:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 716:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (defined (__ARM_FEATURE_CMSE  ) && (__ARM_FEATURE_CMSE   == 3))
 717:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 718:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Set Process Stack Pointer (non-secure)
 719:Drivers/CMSIS/Include/cmsis_gcc.h ****   Devices without ARMv8-M Main Extensions (i.e. Cortex-M23) lack the non-secure
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 35


 720:Drivers/CMSIS/Include/cmsis_gcc.h ****   Stack Pointer Limit register hence the write is silently ignored.
 721:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 722:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Assigns the given value to the non-secure Process Stack Pointer Limit (PSPLIM) when in s
 723:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    ProcStackPtrLimit  Process Stack Pointer Limit value to set
 724:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 725:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __TZ_set_PSPLIM_NS(uint32_t ProcStackPtrLimit)
 726:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 727:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (!(defined (__ARM_ARCH_8M_MAIN__ ) && (__ARM_ARCH_8M_MAIN__ == 1)))
 728:Drivers/CMSIS/Include/cmsis_gcc.h ****   // without main extensions, the non-secure PSPLIM is RAZ/WI
 729:Drivers/CMSIS/Include/cmsis_gcc.h ****   (void)ProcStackPtrLimit;
 730:Drivers/CMSIS/Include/cmsis_gcc.h **** #else
 731:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MSR psplim_ns, %0\n" : : "r" (ProcStackPtrLimit));
 732:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 733:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 734:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 735:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 736:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 737:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 738:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get Main Stack Pointer Limit
 739:Drivers/CMSIS/Include/cmsis_gcc.h ****   Devices without ARMv8-M Main Extensions (i.e. Cortex-M23) lack the non-secure
 740:Drivers/CMSIS/Include/cmsis_gcc.h ****   Stack Pointer Limit register hence zero is returned always in non-secure
 741:Drivers/CMSIS/Include/cmsis_gcc.h ****   mode.
 742:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 743:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the current value of the Main Stack Pointer Limit (MSPLIM).
 744:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               MSPLIM Register value
 745:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 746:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __get_MSPLIM(void)
 747:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 748:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (!(defined (__ARM_ARCH_8M_MAIN__ ) && (__ARM_ARCH_8M_MAIN__ == 1)) && \
 749:Drivers/CMSIS/Include/cmsis_gcc.h ****     (!defined (__ARM_FEATURE_CMSE) || (__ARM_FEATURE_CMSE < 3)))
 750:Drivers/CMSIS/Include/cmsis_gcc.h ****   // without main extensions, the non-secure MSPLIM is RAZ/WI
 751:Drivers/CMSIS/Include/cmsis_gcc.h ****   return 0U;
 752:Drivers/CMSIS/Include/cmsis_gcc.h **** #else
 753:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 754:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, msplim" : "=r" (result) );
 755:Drivers/CMSIS/Include/cmsis_gcc.h ****   return result;
 756:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 757:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 758:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 759:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 760:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (defined (__ARM_FEATURE_CMSE  ) && (__ARM_FEATURE_CMSE   == 3))
 761:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 762:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get Main Stack Pointer Limit (non-secure)
 763:Drivers/CMSIS/Include/cmsis_gcc.h ****   Devices without ARMv8-M Main Extensions (i.e. Cortex-M23) lack the non-secure
 764:Drivers/CMSIS/Include/cmsis_gcc.h ****   Stack Pointer Limit register hence zero is returned always.
 765:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 766:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the current value of the non-secure Main Stack Pointer Limit(MSPLIM) when in sec
 767:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               MSPLIM Register value
 768:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 769:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __TZ_get_MSPLIM_NS(void)
 770:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 771:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (!(defined (__ARM_ARCH_8M_MAIN__ ) && (__ARM_ARCH_8M_MAIN__ == 1)))
 772:Drivers/CMSIS/Include/cmsis_gcc.h ****   // without main extensions, the non-secure MSPLIM is RAZ/WI
 773:Drivers/CMSIS/Include/cmsis_gcc.h ****   return 0U;
 774:Drivers/CMSIS/Include/cmsis_gcc.h **** #else
 775:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 776:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, msplim_ns" : "=r" (result) );
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 36


 777:Drivers/CMSIS/Include/cmsis_gcc.h ****   return result;
 778:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 779:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 780:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 781:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 782:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 783:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 784:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Set Main Stack Pointer Limit
 785:Drivers/CMSIS/Include/cmsis_gcc.h ****   Devices without ARMv8-M Main Extensions (i.e. Cortex-M23) lack the non-secure
 786:Drivers/CMSIS/Include/cmsis_gcc.h ****   Stack Pointer Limit register hence the write is silently ignored in non-secure
 787:Drivers/CMSIS/Include/cmsis_gcc.h ****   mode.
 788:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 789:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Assigns the given value to the Main Stack Pointer Limit (MSPLIM).
 790:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    MainStackPtrLimit  Main Stack Pointer Limit value to set
 791:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 792:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __set_MSPLIM(uint32_t MainStackPtrLimit)
 793:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 794:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (!(defined (__ARM_ARCH_8M_MAIN__ ) && (__ARM_ARCH_8M_MAIN__ == 1)) && \
 795:Drivers/CMSIS/Include/cmsis_gcc.h ****     (!defined (__ARM_FEATURE_CMSE) || (__ARM_FEATURE_CMSE < 3)))
 796:Drivers/CMSIS/Include/cmsis_gcc.h ****   // without main extensions, the non-secure MSPLIM is RAZ/WI
 797:Drivers/CMSIS/Include/cmsis_gcc.h ****   (void)MainStackPtrLimit;
 798:Drivers/CMSIS/Include/cmsis_gcc.h **** #else
 799:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MSR msplim, %0" : : "r" (MainStackPtrLimit));
 800:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 801:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 802:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 803:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 804:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (defined (__ARM_FEATURE_CMSE  ) && (__ARM_FEATURE_CMSE   == 3))
 805:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 806:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Set Main Stack Pointer Limit (non-secure)
 807:Drivers/CMSIS/Include/cmsis_gcc.h ****   Devices without ARMv8-M Main Extensions (i.e. Cortex-M23) lack the non-secure
 808:Drivers/CMSIS/Include/cmsis_gcc.h ****   Stack Pointer Limit register hence the write is silently ignored.
 809:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 810:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Assigns the given value to the non-secure Main Stack Pointer Limit (MSPLIM) when in secu
 811:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    MainStackPtrLimit  Main Stack Pointer value to set
 812:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 813:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __TZ_set_MSPLIM_NS(uint32_t MainStackPtrLimit)
 814:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 815:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (!(defined (__ARM_ARCH_8M_MAIN__ ) && (__ARM_ARCH_8M_MAIN__ == 1)))
 816:Drivers/CMSIS/Include/cmsis_gcc.h ****   // without main extensions, the non-secure MSPLIM is RAZ/WI
 817:Drivers/CMSIS/Include/cmsis_gcc.h ****   (void)MainStackPtrLimit;
 818:Drivers/CMSIS/Include/cmsis_gcc.h **** #else
 819:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MSR msplim_ns, %0" : : "r" (MainStackPtrLimit));
 820:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 821:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 822:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 823:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 824:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif /* ((defined (__ARM_ARCH_8M_MAIN__ ) && (__ARM_ARCH_8M_MAIN__ == 1)) || \
 825:Drivers/CMSIS/Include/cmsis_gcc.h ****            (defined (__ARM_ARCH_8M_BASE__ ) && (__ARM_ARCH_8M_BASE__ == 1))    ) */
 826:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 827:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 828:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 829:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get FPSCR
 830:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the current value of the Floating Point Status/Control register.
 831:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               Floating Point Status/Control register value
 832:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 833:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __get_FPSCR(void)
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 37


 834:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 835:Drivers/CMSIS/Include/cmsis_gcc.h **** #if ((defined (__FPU_PRESENT) && (__FPU_PRESENT == 1U)) && \
 836:Drivers/CMSIS/Include/cmsis_gcc.h ****      (defined (__FPU_USED   ) && (__FPU_USED    == 1U))     )
 837:Drivers/CMSIS/Include/cmsis_gcc.h **** #if __has_builtin(__builtin_arm_get_fpscr) 
 838:Drivers/CMSIS/Include/cmsis_gcc.h **** // Re-enable using built-in when GCC has been fixed
 839:Drivers/CMSIS/Include/cmsis_gcc.h **** // || (__GNUC__ > 7) || (__GNUC__ == 7 && __GNUC_MINOR__ >= 2)
 840:Drivers/CMSIS/Include/cmsis_gcc.h ****   /* see https://gcc.gnu.org/ml/gcc-patches/2017-04/msg00443.html */
 841:Drivers/CMSIS/Include/cmsis_gcc.h ****   return __builtin_arm_get_fpscr();
 842:Drivers/CMSIS/Include/cmsis_gcc.h **** #else
 843:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 844:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 845:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("VMRS %0, fpscr" : "=r" (result) );
 846:Drivers/CMSIS/Include/cmsis_gcc.h ****   return(result);
 847:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 848:Drivers/CMSIS/Include/cmsis_gcc.h **** #else
 849:Drivers/CMSIS/Include/cmsis_gcc.h ****   return(0U);
 850:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 851:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 852:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 853:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 854:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 855:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Set FPSCR
 856:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Assigns the given value to the Floating Point Status/Control register.
 857:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    fpscr  Floating Point Status/Control value to set
 858:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 859:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __set_FPSCR(uint32_t fpscr)
 860:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 861:Drivers/CMSIS/Include/cmsis_gcc.h **** #if ((defined (__FPU_PRESENT) && (__FPU_PRESENT == 1U)) && \
 862:Drivers/CMSIS/Include/cmsis_gcc.h ****      (defined (__FPU_USED   ) && (__FPU_USED    == 1U))     )
 863:Drivers/CMSIS/Include/cmsis_gcc.h **** #if __has_builtin(__builtin_arm_set_fpscr)
 864:Drivers/CMSIS/Include/cmsis_gcc.h **** // Re-enable using built-in when GCC has been fixed
 865:Drivers/CMSIS/Include/cmsis_gcc.h **** // || (__GNUC__ > 7) || (__GNUC__ == 7 && __GNUC_MINOR__ >= 2)
 866:Drivers/CMSIS/Include/cmsis_gcc.h ****   /* see https://gcc.gnu.org/ml/gcc-patches/2017-04/msg00443.html */
 867:Drivers/CMSIS/Include/cmsis_gcc.h ****   __builtin_arm_set_fpscr(fpscr);
 868:Drivers/CMSIS/Include/cmsis_gcc.h **** #else
 869:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("VMSR fpscr, %0" : : "r" (fpscr) : "vfpcc", "memory");
 870:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 871:Drivers/CMSIS/Include/cmsis_gcc.h **** #else
 872:Drivers/CMSIS/Include/cmsis_gcc.h ****   (void)fpscr;
 873:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 874:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 875:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 876:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 877:Drivers/CMSIS/Include/cmsis_gcc.h **** /*@} end of CMSIS_Core_RegAccFunctions */
 878:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 879:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 880:Drivers/CMSIS/Include/cmsis_gcc.h **** /* ##########################  Core Instruction Access  ######################### */
 881:Drivers/CMSIS/Include/cmsis_gcc.h **** /** \defgroup CMSIS_Core_InstructionInterface CMSIS Core Instruction Interface
 882:Drivers/CMSIS/Include/cmsis_gcc.h ****   Access to dedicated instructions
 883:Drivers/CMSIS/Include/cmsis_gcc.h ****   @{
 884:Drivers/CMSIS/Include/cmsis_gcc.h **** */
 885:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 886:Drivers/CMSIS/Include/cmsis_gcc.h **** /* Define macros for porting to both thumb1 and thumb2.
 887:Drivers/CMSIS/Include/cmsis_gcc.h ****  * For thumb1, use low register (r0-r7), specified by constraint "l"
 888:Drivers/CMSIS/Include/cmsis_gcc.h ****  * Otherwise, use general registers, specified by constraint "r" */
 889:Drivers/CMSIS/Include/cmsis_gcc.h **** #if defined (__thumb__) && !defined (__thumb2__)
 890:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __CMSIS_GCC_OUT_REG(r) "=l" (r)
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 38


 891:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __CMSIS_GCC_RW_REG(r) "+l" (r)
 892:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __CMSIS_GCC_USE_REG(r) "l" (r)
 893:Drivers/CMSIS/Include/cmsis_gcc.h **** #else
 894:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __CMSIS_GCC_OUT_REG(r) "=r" (r)
 895:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __CMSIS_GCC_RW_REG(r) "+r" (r)
 896:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __CMSIS_GCC_USE_REG(r) "r" (r)
 897:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 898:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 899:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 900:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   No Operation
 901:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details No Operation does nothing. This instruction can be used for code alignment purposes.
 902:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 903:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __NOP()                             __ASM volatile ("nop")
 904:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 905:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 906:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Wait For Interrupt
 907:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Wait For Interrupt is a hint instruction that suspends execution until one of a number o
 908:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 909:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __WFI()                             __ASM volatile ("wfi")
 910:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 911:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 912:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 913:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Wait For Event
 914:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Wait For Event is a hint instruction that permits the processor to enter
 915:Drivers/CMSIS/Include/cmsis_gcc.h ****            a low-power state until one of a number of events occurs.
 916:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 917:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __WFE()                             __ASM volatile ("wfe")
 918:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 919:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 920:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 921:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Send Event
 922:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Send Event is a hint instruction. It causes an event to be signaled to the CPU.
 923:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 924:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __SEV()                             __ASM volatile ("sev")
 925:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 926:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 927:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 928:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Instruction Synchronization Barrier
 929:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Instruction Synchronization Barrier flushes the pipeline in the processor,
 930:Drivers/CMSIS/Include/cmsis_gcc.h ****            so that all instructions following the ISB are fetched from cache or memory,
 931:Drivers/CMSIS/Include/cmsis_gcc.h ****            after the instruction has been completed.
 932:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 933:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __ISB(void)
 934:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 935:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("isb 0xF":::"memory");
 936:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 937:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 938:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 939:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 940:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Data Synchronization Barrier
 941:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Acts as a special kind of Data Memory Barrier.
 942:Drivers/CMSIS/Include/cmsis_gcc.h ****            It completes when all explicit memory accesses before this instruction complete.
 943:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 944:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __DSB(void)
 377              		.loc 2 944 27 view .LVU85
 378              	.LBB23:
 945:Drivers/CMSIS/Include/cmsis_gcc.h **** {
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 39


 946:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("dsb 0xF":::"memory");
 379              		.loc 2 946 3 view .LVU86
 380              		.syntax unified
 381              	@ 946 "Drivers/CMSIS/Include/cmsis_gcc.h" 1
 382 0022 BFF34F8F 		dsb 0xF
 383              	@ 0 "" 2
 384              		.thumb
 385              		.syntax unified
 386              	.LBE23:
 387              	.LBE22:
 853:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     __ISB ();
 388              		.loc 1 853 5 view .LVU87
 389              	.LBB24:
 390              	.LBI24:
 933:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 391              		.loc 2 933 27 view .LVU88
 392              	.LBB25:
 935:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 393              		.loc 2 935 3 view .LVU89
 394              		.syntax unified
 395              	@ 935 "Drivers/CMSIS/Include/cmsis_gcc.h" 1
 396 0026 BFF36F8F 		isb 0xF
 397              	@ 0 "" 2
 398              		.thumb
 399              		.syntax unified
 400              	.LBE25:
 401              	.LBE24:
 854:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 855:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     /* Select Stop mode entry */
 856:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     if (STOPEntry == PWR_STOPENTRY_WFI)
 402              		.loc 1 856 5 view .LVU90
 403              		.loc 1 856 8 is_stmt 0 view .LVU91
 404 002a 0129     		cmp	r1, #1
 405 002c 08D0     		beq	.L46
 857:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     {
 858:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       /* Request Wait For Interrupt */
 859:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       __WFI ();
 860:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     }
 861:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     else
 862:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     {
 863:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       /* Request Wait For Event */
 864:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       __WFE ();
 406              		.loc 1 864 7 is_stmt 1 view .LVU92
 407              		.syntax unified
 408              	@ 864 "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c" 1
 409 002e 20BF     		wfe
 410              	@ 0 "" 2
 411              		.thumb
 412              		.syntax unified
 413              	.L42:
 865:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     }
 866:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 867:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     /* Clear SLEEPDEEP bit of Cortex-Mx in the System Control Register */
 868:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     CLEAR_BIT (SCB->SCR, SCB_SCR_SLEEPDEEP_Msk);
 414              		.loc 1 868 5 view .LVU93
 415 0030 0C4A     		ldr	r2, .L48+4
 416 0032 1369     		ldr	r3, [r2, #16]
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 40


 417 0034 23F00403 		bic	r3, r3, #4
 418 0038 1361     		str	r3, [r2, #16]
 419              	.L39:
 869:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   }
 870:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #if defined (PWR_CPUCR_PDDS_D2)
 871:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   else if (Domain == PWR_D2_DOMAIN)
 872:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   {
 873:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #if defined (DUAL_CORE)
 874:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     /* Check current core */
 875:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     if (HAL_GetCurrentCPUID () != CM4_CPUID)
 876:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     {
 877:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       /*
 878:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****          When the domain selected and the cortex-mx don't match, entering stop
 879:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****          mode will not be performed
 880:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       */
 881:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       return;
 882:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     }
 883:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 884:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     /* Keep DSTOP mode when D2 domain enters Deepsleep */
 885:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     CLEAR_BIT (PWR->CPU2CR, PWR_CPU2CR_PDDS_D2);
 886:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 887:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     /* Set SLEEPDEEP bit of Cortex System Control Register */
 888:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     SET_BIT (SCB->SCR, SCB_SCR_SLEEPDEEP_Msk);
 889:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 890:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     /* Ensure that all instructions are done before entering STOP mode */
 891:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     __DSB ();
 892:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     __ISB ();
 893:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 894:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     /* Select Stop mode entry */
 895:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     if (STOPEntry == PWR_STOPENTRY_WFI)
 896:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     {
 897:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       /* Request Wait For Interrupt */
 898:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       __WFI ();
 899:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     }
 900:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     else
 901:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     {
 902:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       /* Request Wait For Event */
 903:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       __WFE ();
 904:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     }
 905:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 906:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     /* Clear SLEEPDEEP bit of Cortex-Mx in the System Control Register */
 907:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     CLEAR_BIT (SCB->SCR, SCB_SCR_SLEEPDEEP_Msk);
 908:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #else
 909:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     /* Keep DSTOP mode when D2 domain enters Deepsleep */
 910:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     CLEAR_BIT (PWR->CPUCR, PWR_CPUCR_PDDS_D2);
 911:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #endif  /* defined (DUAL_CORE) */
 912:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   }
 913:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #endif /* defined (PWR_CPUCR_PDDS_D2) */
 914:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   else
 915:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   {
 916:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #if defined (DUAL_CORE)
 917:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     /* Check current core */
 918:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     if (HAL_GetCurrentCPUID () == CM7_CPUID)
 919:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     {
 920:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       /* Keep DSTOP mode when D3 domain enters Deepsleep */
 921:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       CLEAR_BIT (PWR->CPUCR, PWR_CPUCR_PDDS_D3);
 922:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     }
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 41


 923:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     else
 924:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     {
 925:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       /* Keep DSTOP mode when D3 domain enters Deepsleep */
 926:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       CLEAR_BIT (PWR->CPU2CR, PWR_CPU2CR_PDDS_D3);
 927:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     }
 928:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #else
 929:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     /* Keep DSTOP mode when D3/SRD domain enters Deepsleep */
 930:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     CLEAR_BIT (PWR->CPUCR, PWR_CPUCR_PDDS_D3);
 931:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #endif  /* defined (DUAL_CORE) */
 932:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   }
 933:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** }
 420              		.loc 1 933 1 is_stmt 0 view .LVU94
 421 003a 5DF8044B 		ldr	r4, [sp], #4
 422              	.LCFI5:
 423              		.cfi_remember_state
 424              		.cfi_restore 4
 425              		.cfi_def_cfa_offset 0
 426 003e 7047     		bx	lr
 427              	.L46:
 428              	.LCFI6:
 429              		.cfi_restore_state
 859:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     }
 430              		.loc 1 859 7 is_stmt 1 view .LVU95
 431              		.syntax unified
 432              	@ 859 "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c" 1
 433 0040 30BF     		wfi
 434              	@ 0 "" 2
 435              		.thumb
 436              		.syntax unified
 437 0042 F5E7     		b	.L42
 438              	.LVL18:
 439              	.L40:
 871:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   {
 440              		.loc 1 871 8 view .LVU96
 871:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   {
 441              		.loc 1 871 11 is_stmt 0 view .LVU97
 442 0044 012A     		cmp	r2, #1
 443 0046 05D0     		beq	.L47
 930:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #endif  /* defined (DUAL_CORE) */
 444              		.loc 1 930 5 is_stmt 1 view .LVU98
 445 0048 054A     		ldr	r2, .L48
 446              	.LVL19:
 930:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #endif  /* defined (DUAL_CORE) */
 447              		.loc 1 930 5 is_stmt 0 view .LVU99
 448 004a 1369     		ldr	r3, [r2, #16]
 449 004c 23F00403 		bic	r3, r3, #4
 450 0050 1361     		str	r3, [r2, #16]
 451              		.loc 1 933 1 view .LVU100
 452 0052 F2E7     		b	.L39
 453              	.LVL20:
 454              	.L47:
 910:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #endif  /* defined (DUAL_CORE) */
 455              		.loc 1 910 5 is_stmt 1 view .LVU101
 456 0054 024A     		ldr	r2, .L48
 457              	.LVL21:
 910:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #endif  /* defined (DUAL_CORE) */
 458              		.loc 1 910 5 is_stmt 0 view .LVU102
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 42


 459 0056 1369     		ldr	r3, [r2, #16]
 460 0058 23F00203 		bic	r3, r3, #2
 461 005c 1361     		str	r3, [r2, #16]
 462 005e ECE7     		b	.L39
 463              	.L49:
 464              		.align	2
 465              	.L48:
 466 0060 00480258 		.word	1476544512
 467 0064 00ED00E0 		.word	-*********
 468              		.cfi_endproc
 469              	.LFE150:
 471              		.section	.text.HAL_PWREx_ClearPendingEvent,"ax",%progbits
 472              		.align	1
 473              		.global	HAL_PWREx_ClearPendingEvent
 474              		.syntax unified
 475              		.thumb
 476              		.thumb_func
 478              	HAL_PWREx_ClearPendingEvent:
 479              	.LFB151:
 934:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 935:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /**
 936:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @brief Clear pending event.
 937:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @note   This API clears the pending event in order to enter a given CPU
 938:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         to CSLEEP or CSTOP. It should be called just before APIs performing
 939:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         enter low power mode using Wait For Event request.
 940:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @note   Cortex-M7 must be in CRUN mode when calling this API by Cortex-M4.
 941:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @retval None.
 942:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   */
 943:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** void HAL_PWREx_ClearPendingEvent (void)
 944:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** {
 480              		.loc 1 944 1 is_stmt 1 view -0
 481              		.cfi_startproc
 482              		@ args = 0, pretend = 0, frame = 0
 483              		@ frame_needed = 0, uses_anonymous_args = 0
 484              		@ link register save eliminated.
 945:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #if defined (DUAL_CORE)
 946:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Check the current Core */
 947:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   if (HAL_GetCurrentCPUID () == CM7_CPUID)
 948:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   {
 949:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     __WFE ();
 950:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   }
 951:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   else
 952:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   {
 953:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     __SEV ();
 954:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     __WFE ();
 955:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   }
 956:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #else
 957:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   __WFE ();
 485              		.loc 1 957 3 view .LVU104
 486              		.syntax unified
 487              	@ 957 "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c" 1
 488 0000 20BF     		wfe
 489              	@ 0 "" 2
 958:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #endif /* defined (DUAL_CORE) */
 959:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** }
 490              		.loc 1 959 1 is_stmt 0 view .LVU105
 491              		.thumb
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 43


 492              		.syntax unified
 493 0002 7047     		bx	lr
 494              		.cfi_endproc
 495              	.LFE151:
 497              		.section	.text.HAL_PWREx_EnterSTANDBYMode,"ax",%progbits
 498              		.align	1
 499              		.global	HAL_PWREx_EnterSTANDBYMode
 500              		.syntax unified
 501              		.thumb
 502              		.thumb_func
 504              	HAL_PWREx_EnterSTANDBYMode:
 505              	.LVL22:
 506              	.LFB152:
 960:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 961:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /**
 962:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @brief Enter a Domain to DSTANDBY mode.
 963:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @note   This API gives flexibility to manage independently each domain
 964:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         STANDBY mode. For dual core lines, this API should be executed with
 965:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         the corresponding Cortex-Mx to enter domain to DSTANDBY mode. When
 966:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         it is executed by all available Cortex-Mx, the system enter STANDBY
 967:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         mode.
 968:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         For single core lines, calling this API with D1/SRD the selected
 969:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         domain will enter the whole system in STOP if PWR_CPUCR_PDDS_D3 = 0
 970:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         and enter the whole system in STANDBY if PWR_CPUCR_PDDS_D3 = 1.
 971:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @note   The DStandby mode is entered when all PDDS_Dn bits in PWR_CPUCR for
 972:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         the Dn domain select Standby mode. When the system enters Standby
 973:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         mode, the voltage regulator is disabled.
 974:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @note   When D2 or D3 domain is in DStandby mode and the CPU sets the
 975:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         domain PDDS_Dn bit to select Stop mode, the domain remains in
 976:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         DStandby mode. The domain will only exit DStandby when the CPU
 977:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         allocates a peripheral in the domain.
 978:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @note   The system D3/SRD domain enters Standby mode only when the D1 and D2
 979:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         domain are in DStandby.
 980:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @note   Before entering DSTANDBY mode it is recommended to call
 981:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         SCB_CleanDCache function in order to clean the D-Cache and guarantee
 982:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         the data integrity for the SRAM memories.
 983:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @param  Domain : Specifies the Domain to enter to STANDBY mode.
 984:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *          This parameter can be one of the following values:
 985:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *            @arg PWR_D1_DOMAIN: Enter D1/CD Domain to DSTANDBY mode.
 986:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *            @arg PWR_D2_DOMAIN: Enter D2 Domain to DSTANDBY mode.
 987:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *            @arg PWR_D3_DOMAIN: Enter D3/SRD Domain to DSTANDBY mode.
 988:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @retval None
 989:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   */
 990:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** void HAL_PWREx_EnterSTANDBYMode (uint32_t Domain)
 991:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** {
 507              		.loc 1 991 1 is_stmt 1 view -0
 508              		.cfi_startproc
 509              		@ args = 0, pretend = 0, frame = 0
 510              		@ frame_needed = 0, uses_anonymous_args = 0
 511              		@ link register save eliminated.
 992:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Check the parameters */
 993:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   assert_param (IS_PWR_DOMAIN (Domain));
 512              		.loc 1 993 3 view .LVU107
 994:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 995:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Select the domain Power Down DeepSleep */
 996:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   if (Domain == PWR_D1_DOMAIN)
 513              		.loc 1 996 3 view .LVU108
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 44


 514              		.loc 1 996 6 is_stmt 0 view .LVU109
 515 0000 58B9     		cbnz	r0, .L52
 997:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   {
 998:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #if defined (DUAL_CORE)
 999:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     /* Check current core */
1000:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     if (HAL_GetCurrentCPUID () != CM7_CPUID)
1001:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     {
1002:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       /*
1003:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****          When the domain selected and the cortex-mx don't match, entering
1004:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****          standby mode will not be performed
1005:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       */
1006:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       return;
1007:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     }
1008:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #endif /* defined (DUAL_CORE) */
1009:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1010:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     /* Allow DSTANDBY mode when D1/CD domain enters Deepsleep */
1011:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     SET_BIT (PWR-> CPUCR, PWR_CPUCR_PDDS_D1);
 516              		.loc 1 1011 5 is_stmt 1 view .LVU110
 517 0002 0D4A     		ldr	r2, .L56
 518 0004 1369     		ldr	r3, [r2, #16]
 519 0006 43F00103 		orr	r3, r3, #1
 520 000a 1361     		str	r3, [r2, #16]
1012:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1013:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #if defined (DUAL_CORE)
1014:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     /* Allow DSTANDBY mode when D1/CD domain enters Deepsleep */
1015:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     SET_BIT (PWR-> CPU2CR, PWR_CPU2CR_PDDS_D1);
1016:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #endif /*DUAL_CORE*/
1017:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1018:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     /* Set SLEEPDEEP bit of Cortex System Control Register */
1019:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     SET_BIT (SCB->SCR, SCB_SCR_SLEEPDEEP_Msk);
 521              		.loc 1 1019 5 view .LVU111
 522 000c 0B4A     		ldr	r2, .L56+4
 523 000e 1369     		ldr	r3, [r2, #16]
 524 0010 43F00403 		orr	r3, r3, #4
 525 0014 1361     		str	r3, [r2, #16]
1020:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1021:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     /* This option is used to ensure that store operations are completed */
1022:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #if defined (__CC_ARM)
1023:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     __force_stores ();
1024:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #endif /* defined (__CC_ARM) */
1025:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1026:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     /* Request Wait For Interrupt */
1027:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     __WFI ();
 526              		.loc 1 1027 5 view .LVU112
 527              		.syntax unified
 528              	@ 1027 "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c" 1
 529 0016 30BF     		wfi
 530              	@ 0 "" 2
 531              		.thumb
 532              		.syntax unified
 533 0018 7047     		bx	lr
 534              	.L52:
1028:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   }
1029:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #if defined (PWR_CPUCR_PDDS_D2)
1030:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   else if (Domain == PWR_D2_DOMAIN)
 535              		.loc 1 1030 8 view .LVU113
 536              		.loc 1 1030 11 is_stmt 0 view .LVU114
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 45


 537 001a 0128     		cmp	r0, #1
 538 001c 05D0     		beq	.L55
1031:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   {
1032:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     /* Allow DSTANDBY mode when D2 domain enters Deepsleep */
1033:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     SET_BIT (PWR-> CPUCR, PWR_CPUCR_PDDS_D2);
1034:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1035:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #if defined (DUAL_CORE)
1036:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     /* Check current core */
1037:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     if (HAL_GetCurrentCPUID () != CM4_CPUID)
1038:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     {
1039:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       /*
1040:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****          When the domain selected and the cortex-mx don't match, entering
1041:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****          standby mode will not be performed
1042:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       */
1043:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       return;
1044:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     }
1045:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1046:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     /* Allow DSTANDBY mode when D2 domain enters Deepsleep */
1047:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     SET_BIT (PWR-> CPU2CR, PWR_CPU2CR_PDDS_D2);
1048:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1049:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     /* Set SLEEPDEEP bit of Cortex System Control Register */
1050:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     SET_BIT (SCB->SCR, SCB_SCR_SLEEPDEEP_Msk);
1051:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1052:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     /* This option is used to ensure that store operations are completed */
1053:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #if defined (__CC_ARM)
1054:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     __force_stores ();
1055:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #endif /* defined (__CC_ARM) */
1056:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1057:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     /* Request Wait For Interrupt */
1058:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     __WFI ();
1059:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #endif /* defined (DUAL_CORE) */
1060:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   }
1061:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #endif /* defined (PWR_CPUCR_PDDS_D2) */
1062:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   else
1063:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   {
1064:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     /* Allow DSTANDBY mode when D3/SRD domain enters Deepsleep */
1065:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     SET_BIT (PWR->CPUCR, PWR_CPUCR_PDDS_D3);
 539              		.loc 1 1065 5 is_stmt 1 view .LVU115
 540 001e 064A     		ldr	r2, .L56
 541 0020 1369     		ldr	r3, [r2, #16]
 542 0022 43F00403 		orr	r3, r3, #4
 543 0026 1361     		str	r3, [r2, #16]
1066:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1067:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #if defined (DUAL_CORE)
1068:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     /* Allow DSTANDBY mode when D3/SRD domain enters Deepsleep */
1069:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     SET_BIT (PWR->CPU2CR, PWR_CPU2CR_PDDS_D3);
1070:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #endif /* defined (DUAL_CORE) */
1071:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   }
1072:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** }
 544              		.loc 1 1072 1 is_stmt 0 view .LVU116
 545 0028 7047     		bx	lr
 546              	.L55:
1033:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 547              		.loc 1 1033 5 is_stmt 1 view .LVU117
 548 002a 034A     		ldr	r2, .L56
 549 002c 1369     		ldr	r3, [r2, #16]
 550 002e 43F00203 		orr	r3, r3, #2
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 46


 551 0032 1361     		str	r3, [r2, #16]
 552 0034 7047     		bx	lr
 553              	.L57:
 554 0036 00BF     		.align	2
 555              	.L56:
 556 0038 00480258 		.word	1476544512
 557 003c 00ED00E0 		.word	-*********
 558              		.cfi_endproc
 559              	.LFE152:
 561              		.section	.text.HAL_PWREx_ConfigD3Domain,"ax",%progbits
 562              		.align	1
 563              		.global	HAL_PWREx_ConfigD3Domain
 564              		.syntax unified
 565              		.thumb
 566              		.thumb_func
 568              	HAL_PWREx_ConfigD3Domain:
 569              	.LVL23:
 570              	.LFB153:
1073:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1074:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /**
1075:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @brief Configure the D3/SRD Domain state when the System in low power mode.
1076:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @param  D3State : Specifies the D3/SRD state.
1077:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *          This parameter can be one of the following values :
1078:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *            @arg PWR_D3_DOMAIN_STOP : D3/SRD domain will follow the most deep
1079:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *                                      CPU sub-system low power mode.
1080:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *            @arg PWR_D3_DOMAIN_RUN : D3/SRD domain will stay in RUN mode
1081:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *                                     regardless of the CPU sub-system low
1082:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *                                     power mode.
1083:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @retval None
1084:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   */
1085:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** void HAL_PWREx_ConfigD3Domain (uint32_t D3State)
1086:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** {
 571              		.loc 1 1086 1 view -0
 572              		.cfi_startproc
 573              		@ args = 0, pretend = 0, frame = 0
 574              		@ frame_needed = 0, uses_anonymous_args = 0
 575              		@ link register save eliminated.
1087:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Check the parameter */
1088:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   assert_param (IS_D3_STATE (D3State));
 576              		.loc 1 1088 3 view .LVU119
1089:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1090:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Keep D3/SRD in run mode */
1091:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   MODIFY_REG (PWR->CPUCR, PWR_CPUCR_RUN_D3, D3State);
 577              		.loc 1 1091 3 view .LVU120
 578 0000 034A     		ldr	r2, .L59
 579 0002 1369     		ldr	r3, [r2, #16]
 580 0004 23F40063 		bic	r3, r3, #2048
 581 0008 0343     		orrs	r3, r3, r0
 582 000a 1361     		str	r3, [r2, #16]
1092:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** }
 583              		.loc 1 1092 1 is_stmt 0 view .LVU121
 584 000c 7047     		bx	lr
 585              	.L60:
 586 000e 00BF     		.align	2
 587              	.L59:
 588 0010 00480258 		.word	1476544512
 589              		.cfi_endproc
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 47


 590              	.LFE153:
 592              		.section	.text.HAL_PWREx_EnableFlashPowerDown,"ax",%progbits
 593              		.align	1
 594              		.global	HAL_PWREx_EnableFlashPowerDown
 595              		.syntax unified
 596              		.thumb
 597              		.thumb_func
 599              	HAL_PWREx_EnableFlashPowerDown:
 600              	.LFB154:
1093:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1094:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #if defined (DUAL_CORE)
1095:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /**
1096:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @brief Clear HOLD2F, HOLD1F, STOPF, SBF, SBF_D1, and SBF_D2 flags for a
1097:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *        given domain.
1098:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @param  DomainFlags : Specifies the Domain flags to be cleared.
1099:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *          This parameter can be one of the following values:
1100:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *            @arg PWR_D1_DOMAIN_FLAGS : Clear D1 Domain flags.
1101:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *            @arg PWR_D2_DOMAIN_FLAGS : Clear D2 Domain flags.
1102:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *            @arg PWR_ALL_DOMAIN_FLAGS : Clear D1 and D2 Domain flags.
1103:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @retval None.
1104:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   */
1105:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** void HAL_PWREx_ClearDomainFlags (uint32_t DomainFlags)
1106:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** {
1107:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Check the parameter */
1108:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   assert_param (IS_PWR_DOMAIN_FLAG (DomainFlags));
1109:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1110:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* D1 CPU flags */
1111:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   if (DomainFlags == PWR_D1_DOMAIN_FLAGS)
1112:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   {
1113:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     /* Clear D1 domain flags (HOLD2F, STOPF, SBF, SBF_D1, and SBF_D2) */
1114:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     SET_BIT (PWR->CPUCR, PWR_CPUCR_CSSF);
1115:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   }
1116:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* D2 CPU flags */
1117:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   else if (DomainFlags == PWR_D2_DOMAIN_FLAGS)
1118:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   {
1119:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     /* Clear D2 domain flags (HOLD1F, STOPF, SBF, SBF_D1, and SBF_D2) */
1120:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     SET_BIT (PWR->CPU2CR, PWR_CPU2CR_CSSF);
1121:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   }
1122:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   else
1123:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   {
1124:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     /* Clear D1 domain flags (HOLD2F, STOPF, SBF, SBF_D1, and SBF_D2) */
1125:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     SET_BIT (PWR->CPUCR, PWR_CPUCR_CSSF);
1126:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     /* Clear D2 domain flags (HOLD1F, STOPF, SBF, SBF_D1, and SBF_D2) */
1127:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     SET_BIT (PWR->CPU2CR, PWR_CPU2CR_CSSF);
1128:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   }
1129:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** }
1130:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1131:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /**
1132:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @brief Hold the CPU and their domain peripherals when exiting STOP mode.
1133:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @param  CPU : Specifies the core to be held.
1134:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         This parameter can be one of the following values:
1135:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *             @arg PWR_CORE_CPU1: Hold CPU1 and set CPU2 as master.
1136:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *             @arg PWR_CORE_CPU2: Hold CPU2 and set CPU1 as master.
1137:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @retval HAL status
1138:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   */
1139:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** HAL_StatusTypeDef HAL_PWREx_HoldCore (uint32_t CPU)
1140:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** {
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 48


1141:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   HAL_StatusTypeDef status = HAL_OK;
1142:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1143:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Check the parameters */
1144:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   assert_param (IS_PWR_CORE (CPU));
1145:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1146:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Check CPU index */
1147:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   if (CPU == PWR_CORE_CPU2)
1148:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   {
1149:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     /* If CPU1 is not held */
1150:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     if ((PWR->CPU2CR & PWR_CPU2CR_HOLD1) != PWR_CPU2CR_HOLD1)
1151:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     {
1152:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       /* Set HOLD2 bit */
1153:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       SET_BIT (PWR->CPUCR, PWR_CPUCR_HOLD2);
1154:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     }
1155:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     else
1156:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     {
1157:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       status = HAL_ERROR;
1158:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     }
1159:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   }
1160:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   else
1161:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   {
1162:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     /* If CPU2 is not held */
1163:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     if ((PWR->CPUCR & PWR_CPUCR_HOLD2) != PWR_CPUCR_HOLD2)
1164:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     {
1165:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       /* Set HOLD1 bit */
1166:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       SET_BIT (PWR->CPU2CR, PWR_CPU2CR_HOLD1);
1167:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     }
1168:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     else
1169:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     {
1170:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       status = HAL_ERROR;
1171:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     }
1172:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   }
1173:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1174:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   return status;
1175:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** }
1176:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1177:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /**
1178:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @brief Release the CPU and their domain peripherals after a wake-up from
1179:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *        STOP mode.
1180:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @param  CPU: Specifies the core to be released.
1181:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         This parameter can be one of the following values:
1182:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *             @arg  PWR_CORE_CPU1: Release the CPU1 and their domain
1183:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *                   peripherals from holding.
1184:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *             @arg  PWR_CORE_CPU2: Release the CPU2 and their domain
1185:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *                   peripherals from holding.
1186:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @retval None
1187:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   */
1188:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** void HAL_PWREx_ReleaseCore (uint32_t CPU)
1189:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** {
1190:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Check the parameters */
1191:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   assert_param (IS_PWR_CORE (CPU));
1192:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1193:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Check CPU index */
1194:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   if (CPU == PWR_CORE_CPU2)
1195:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   {
1196:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     /* Reset HOLD2 bit */
1197:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     CLEAR_BIT (PWR->CPUCR, PWR_CPUCR_HOLD2);
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 49


1198:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   }
1199:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   else
1200:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   {
1201:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     /* Reset HOLD1 bit */
1202:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     CLEAR_BIT (PWR->CPU2CR, PWR_CPU2CR_HOLD1);
1203:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   }
1204:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** }
1205:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #endif /* defined (DUAL_CORE) */
1206:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1207:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1208:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /**
1209:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @brief Enable the Flash Power Down in Stop mode.
1210:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @note   When Flash Power Down is enabled  the Flash memory enters low-power
1211:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         mode when D1/SRD domain is in DStop mode. This feature allows to
1212:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         obtain the best trade-off between low-power consumption and restart
1213:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         time when exiting from DStop mode.
1214:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @retval None.
1215:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   */
1216:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** void HAL_PWREx_EnableFlashPowerDown (void)
1217:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** {
 601              		.loc 1 1217 1 is_stmt 1 view -0
 602              		.cfi_startproc
 603              		@ args = 0, pretend = 0, frame = 0
 604              		@ frame_needed = 0, uses_anonymous_args = 0
 605              		@ link register save eliminated.
1218:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Enable the Flash Power Down */
1219:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   SET_BIT (PWR->CR1, PWR_CR1_FLPS);
 606              		.loc 1 1219 3 view .LVU123
 607 0000 024A     		ldr	r2, .L62
 608 0002 1368     		ldr	r3, [r2]
 609 0004 43F40073 		orr	r3, r3, #512
 610 0008 1360     		str	r3, [r2]
1220:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** }
 611              		.loc 1 1220 1 is_stmt 0 view .LVU124
 612 000a 7047     		bx	lr
 613              	.L63:
 614              		.align	2
 615              	.L62:
 616 000c 00480258 		.word	1476544512
 617              		.cfi_endproc
 618              	.LFE154:
 620              		.section	.text.HAL_PWREx_DisableFlashPowerDown,"ax",%progbits
 621              		.align	1
 622              		.global	HAL_PWREx_DisableFlashPowerDown
 623              		.syntax unified
 624              		.thumb
 625              		.thumb_func
 627              	HAL_PWREx_DisableFlashPowerDown:
 628              	.LFB155:
1221:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1222:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /**
1223:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @brief Disable the Flash Power Down in Stop mode.
1224:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @note   When Flash Power Down is disabled  the Flash memory is kept on
1225:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         normal mode when D1/SRD domain is in DStop mode. This feature allows
1226:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         to obtain the best trade-off between low-power consumption and
1227:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         restart time when exiting from DStop mode.
1228:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @retval None.
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 50


1229:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   */
1230:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** void HAL_PWREx_DisableFlashPowerDown (void)
1231:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** {
 629              		.loc 1 1231 1 is_stmt 1 view -0
 630              		.cfi_startproc
 631              		@ args = 0, pretend = 0, frame = 0
 632              		@ frame_needed = 0, uses_anonymous_args = 0
 633              		@ link register save eliminated.
1232:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Disable the Flash Power Down */
1233:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   CLEAR_BIT (PWR->CR1, PWR_CR1_FLPS);
 634              		.loc 1 1233 3 view .LVU126
 635 0000 024A     		ldr	r2, .L65
 636 0002 1368     		ldr	r3, [r2]
 637 0004 23F40073 		bic	r3, r3, #512
 638 0008 1360     		str	r3, [r2]
1234:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** }
 639              		.loc 1 1234 1 is_stmt 0 view .LVU127
 640 000a 7047     		bx	lr
 641              	.L66:
 642              		.align	2
 643              	.L65:
 644 000c 00480258 		.word	1476544512
 645              		.cfi_endproc
 646              	.LFE155:
 648              		.section	.text.HAL_PWREx_EnableWakeUpPin,"ax",%progbits
 649              		.align	1
 650              		.global	HAL_PWREx_EnableWakeUpPin
 651              		.syntax unified
 652              		.thumb
 653              		.thumb_func
 655              	HAL_PWREx_EnableWakeUpPin:
 656              	.LVL24:
 657              	.LFB156:
1235:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1236:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #if defined (PWR_CR1_SRDRAMSO)
1237:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /**
1238:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @brief Enable memory block shut-off in DStop or DStop2 modes
1239:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @note   In DStop or DStop2 mode, the content of the memory blocks is
1240:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         maintained. Further power optimization can be obtained by switching
1241:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         off some memory blocks. This optimization implies loss of the memory
1242:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         content. The user can select which memory is discarded during STOP
1243:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         mode by means of xxSO bits.
1244:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @param  MemoryBlock : Specifies the memory block to shut-off during DStop or
1245:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *                       DStop2 mode.
1246:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *          This parameter can be one of the following values:
1247:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *            @arg PWR_SRD_AHB_MEMORY_BLOCK : SmartRun domain AHB memory.
1248:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *            @arg PWR_USB_FDCAN_MEMORY_BLOCK : High-speed interfaces USB and
1249:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *                                              FDCAN memories.
1250:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *            @arg PWR_GFXMMU_JPEG_MEMORY_BLOCK : GFXMMU and JPEG memories.
1251:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *            @arg PWR_TCM_ECM_MEMORY_BLOCK : Instruction TCM and ETM memories.
1252:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *            @arg PWR_RAM1_AHB_MEMORY_BLOCK : AHB RAM1 memory.
1253:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *            @arg PWR_RAM2_AHB_MEMORY_BLOCK : AHB RAM2 memory.
1254:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *            @arg PWR_RAM1_AXI_MEMORY_BLOCK : AXI RAM1 memory.
1255:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *            @arg PWR_RAM2_AXI_MEMORY_BLOCK : AXI RAM2 memory.
1256:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *            @arg PWR_RAM3_AXI_MEMORY_BLOCK : AXI RAM3 memory.
1257:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @retval None.
1258:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   */
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 51


1259:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** void HAL_PWREx_EnableMemoryShutOff (uint32_t MemoryBlock)
1260:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** {
1261:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Check the parameter */
1262:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   assert_param (IS_PWR_MEMORY_BLOCK (MemoryBlock));
1263:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1264:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Enable memory block shut-off */
1265:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   SET_BIT (PWR->CR1, MemoryBlock);
1266:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** }
1267:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1268:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /**
1269:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @brief Disable memory block shut-off in DStop or DStop2 modes
1270:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @param  MemoryBlock : Specifies the memory block to keep content during
1271:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *                       DStop or DStop2 mode.
1272:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *          This parameter can be one of the following values:
1273:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *            @arg PWR_SRD_AHB_MEMORY_BLOCK : SmartRun domain AHB memory.
1274:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *            @arg PWR_USB_FDCAN_MEMORY_BLOCK : High-speed interfaces USB and
1275:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *                                              FDCAN memories.
1276:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *            @arg PWR_GFXMMU_JPEG_MEMORY_BLOCK : GFXMMU and JPEG memories.
1277:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *            @arg PWR_TCM_ECM_MEMORY_BLOCK : Instruction TCM and ETM memories.
1278:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *            @arg PWR_RAM1_AHB_MEMORY_BLOCK : AHB RAM1 memory.
1279:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *            @arg PWR_RAM2_AHB_MEMORY_BLOCK : AHB RAM2 memory.
1280:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *            @arg PWR_RAM1_AXI_MEMORY_BLOCK : AXI RAM1 memory.
1281:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *            @arg PWR_RAM2_AXI_MEMORY_BLOCK : AXI RAM2 memory.
1282:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *            @arg PWR_RAM3_AXI_MEMORY_BLOCK : AXI RAM3 memory.
1283:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @retval None.
1284:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   */
1285:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** void HAL_PWREx_DisableMemoryShutOff (uint32_t MemoryBlock)
1286:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** {
1287:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Check the parameter */
1288:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   assert_param (IS_PWR_MEMORY_BLOCK (MemoryBlock));
1289:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1290:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Disable memory block shut-off */
1291:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   CLEAR_BIT (PWR->CR1, MemoryBlock);
1292:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** }
1293:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #endif /* defined (PWR_CR1_SRDRAMSO) */
1294:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1295:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /**
1296:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @brief Enable the Wake-up PINx functionality.
1297:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @param  sPinParams : Pointer to a PWREx_WakeupPinTypeDef structure that
1298:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *                      contains the configuration information for the wake-up
1299:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *                      Pin.
1300:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @note   For dual core devices, please ensure to configure the EXTI lines for
1301:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         the different Cortex-Mx. All combination are allowed: wake up only
1302:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         Cortex-M7, wake up only Cortex-M4 and wake up Cortex-M7 and
1303:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         Cortex-M4.
1304:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @retval None.
1305:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   */
1306:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** void HAL_PWREx_EnableWakeUpPin (const PWREx_WakeupPinTypeDef *sPinParams)
1307:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** {
 658              		.loc 1 1307 1 is_stmt 1 view -0
 659              		.cfi_startproc
 660              		@ args = 0, pretend = 0, frame = 0
 661              		@ frame_needed = 0, uses_anonymous_args = 0
 662              		@ link register save eliminated.
 663              		.loc 1 1307 1 is_stmt 0 view .LVU129
 664 0000 10B4     		push	{r4}
 665              	.LCFI7:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 52


 666              		.cfi_def_cfa_offset 4
 667              		.cfi_offset 4, -4
1308:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   uint32_t pinConfig;
 668              		.loc 1 1308 3 is_stmt 1 view .LVU130
1309:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   uint32_t regMask;
 669              		.loc 1 1309 3 view .LVU131
1310:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   const uint32_t pullMask = PWR_WKUPEPR_WKUPPUPD1;
 670              		.loc 1 1310 3 view .LVU132
 671              	.LVL25:
1311:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1312:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Check the parameters */
1313:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   assert_param (IS_PWR_WAKEUP_PIN (sPinParams->WakeUpPin));
 672              		.loc 1 1313 3 view .LVU133
1314:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   assert_param (IS_PWR_WAKEUP_PIN_POLARITY (sPinParams->PinPolarity));
 673              		.loc 1 1314 3 view .LVU134
1315:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   assert_param (IS_PWR_WAKEUP_PIN_PULL (sPinParams->PinPull));
 674              		.loc 1 1315 3 view .LVU135
1316:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1317:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   pinConfig = sPinParams->WakeUpPin | \
 675              		.loc 1 1317 3 view .LVU136
 676              		.loc 1 1317 25 is_stmt 0 view .LVU137
 677 0002 0368     		ldr	r3, [r0]
1318:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****               (sPinParams->PinPolarity << ((POSITION_VAL(sPinParams->WakeUpPin) + PWR_WKUPEPR_WKUPP
 678              		.loc 1 1318 26 view .LVU138
 679 0004 4168     		ldr	r1, [r0, #4]
 680              	.LVL26:
 681              	.LBB26:
 682              	.LBI26:
 947:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 948:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 949:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 950:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 951:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Data Memory Barrier
 952:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Ensures the apparent order of the explicit memory operations before
 953:Drivers/CMSIS/Include/cmsis_gcc.h ****            and after the instruction, without ensuring their completion.
 954:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 955:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __DMB(void)
 956:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 957:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("dmb 0xF":::"memory");
 958:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 959:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 960:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 961:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 962:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Reverse byte order (32 bit)
 963:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Reverses the byte order in unsigned integer value. For example, 0x12345678 becomes 0x785
 964:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    value  Value to reverse
 965:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               Reversed value
 966:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 967:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __REV(uint32_t value)
 968:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 969:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (__GNUC__ > 4) || (__GNUC__ == 4 && __GNUC_MINOR__ >= 5)
 970:Drivers/CMSIS/Include/cmsis_gcc.h ****   return __builtin_bswap32(value);
 971:Drivers/CMSIS/Include/cmsis_gcc.h **** #else
 972:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 973:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 974:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("rev %0, %1" : __CMSIS_GCC_OUT_REG (result) : __CMSIS_GCC_USE_REG (value) );
 975:Drivers/CMSIS/Include/cmsis_gcc.h ****   return result;
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 53


 976:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 977:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 978:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 979:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 980:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 981:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Reverse byte order (16 bit)
 982:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Reverses the byte order within each halfword of a word. For example, 0x12345678 becomes 
 983:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    value  Value to reverse
 984:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               Reversed value
 985:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 986:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __REV16(uint32_t value)
 987:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 988:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 989:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 990:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("rev16 %0, %1" : __CMSIS_GCC_OUT_REG (result) : __CMSIS_GCC_USE_REG (value) );
 991:Drivers/CMSIS/Include/cmsis_gcc.h ****   return result;
 992:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 993:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 994:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 995:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 996:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Reverse byte order (16 bit)
 997:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Reverses the byte order in a 16-bit value and returns the signed 16-bit result. For exam
 998:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    value  Value to reverse
 999:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               Reversed value
1000:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
1001:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE int16_t __REVSH(int16_t value)
1002:Drivers/CMSIS/Include/cmsis_gcc.h **** {
1003:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (__GNUC__ > 4) || (__GNUC__ == 4 && __GNUC_MINOR__ >= 8)
1004:Drivers/CMSIS/Include/cmsis_gcc.h ****   return (int16_t)__builtin_bswap16(value);
1005:Drivers/CMSIS/Include/cmsis_gcc.h **** #else
1006:Drivers/CMSIS/Include/cmsis_gcc.h ****   int16_t result;
1007:Drivers/CMSIS/Include/cmsis_gcc.h **** 
1008:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("revsh %0, %1" : __CMSIS_GCC_OUT_REG (result) : __CMSIS_GCC_USE_REG (value) );
1009:Drivers/CMSIS/Include/cmsis_gcc.h ****   return result;
1010:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
1011:Drivers/CMSIS/Include/cmsis_gcc.h **** }
1012:Drivers/CMSIS/Include/cmsis_gcc.h **** 
1013:Drivers/CMSIS/Include/cmsis_gcc.h **** 
1014:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
1015:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Rotate Right in unsigned value (32 bit)
1016:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Rotate Right (immediate) provides the value of the contents of a register rotated by a v
1017:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    op1  Value to rotate
1018:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    op2  Number of Bits to rotate
1019:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               Rotated value
1020:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
1021:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __ROR(uint32_t op1, uint32_t op2)
1022:Drivers/CMSIS/Include/cmsis_gcc.h **** {
1023:Drivers/CMSIS/Include/cmsis_gcc.h ****   op2 %= 32U;
1024:Drivers/CMSIS/Include/cmsis_gcc.h ****   if (op2 == 0U)
1025:Drivers/CMSIS/Include/cmsis_gcc.h ****   {
1026:Drivers/CMSIS/Include/cmsis_gcc.h ****     return op1;
1027:Drivers/CMSIS/Include/cmsis_gcc.h ****   }
1028:Drivers/CMSIS/Include/cmsis_gcc.h ****   return (op1 >> op2) | (op1 << (32U - op2));
1029:Drivers/CMSIS/Include/cmsis_gcc.h **** }
1030:Drivers/CMSIS/Include/cmsis_gcc.h **** 
1031:Drivers/CMSIS/Include/cmsis_gcc.h **** 
1032:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 54


1033:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Breakpoint
1034:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Causes the processor to enter Debug state.
1035:Drivers/CMSIS/Include/cmsis_gcc.h ****            Debug tools can use this to investigate system state when the instruction at a particula
1036:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    value  is ignored by the processor.
1037:Drivers/CMSIS/Include/cmsis_gcc.h ****                  If required, a debugger can use it to store additional information about the break
1038:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
1039:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __BKPT(value)                       __ASM volatile ("bkpt "#value)
1040:Drivers/CMSIS/Include/cmsis_gcc.h **** 
1041:Drivers/CMSIS/Include/cmsis_gcc.h **** 
1042:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
1043:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Reverse bit order of value
1044:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Reverses the bit order of the given value.
1045:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    value  Value to reverse
1046:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               Reversed value
1047:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
1048:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __RBIT(uint32_t value)
 683              		.loc 2 1048 31 is_stmt 1 view .LVU139
 684              	.LBB27:
1049:Drivers/CMSIS/Include/cmsis_gcc.h **** {
1050:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 685              		.loc 2 1050 3 view .LVU140
1051:Drivers/CMSIS/Include/cmsis_gcc.h **** 
1052:Drivers/CMSIS/Include/cmsis_gcc.h **** #if ((defined (__ARM_ARCH_7M__      ) && (__ARM_ARCH_7M__      == 1)) || \
1053:Drivers/CMSIS/Include/cmsis_gcc.h ****      (defined (__ARM_ARCH_7EM__     ) && (__ARM_ARCH_7EM__     == 1)) || \
1054:Drivers/CMSIS/Include/cmsis_gcc.h ****      (defined (__ARM_ARCH_8M_MAIN__ ) && (__ARM_ARCH_8M_MAIN__ == 1))    )
1055:Drivers/CMSIS/Include/cmsis_gcc.h ****    __ASM volatile ("rbit %0, %1" : "=r" (result) : "r" (value) );
 686              		.loc 2 1055 4 view .LVU141
 687              		.syntax unified
 688              	@ 1055 "Drivers/CMSIS/Include/cmsis_gcc.h" 1
 689 0006 93FAA3F2 		rbit r2, r3
 690              	@ 0 "" 2
 691              	.LVL27:
1056:Drivers/CMSIS/Include/cmsis_gcc.h **** #else
1057:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t s = (4U /*sizeof(v)*/ * 8U) - 1U; /* extra shift needed at end */
1058:Drivers/CMSIS/Include/cmsis_gcc.h **** 
1059:Drivers/CMSIS/Include/cmsis_gcc.h ****   result = value;                      /* r will be reversed bits of v; first get LSB of v */
1060:Drivers/CMSIS/Include/cmsis_gcc.h ****   for (value >>= 1U; value != 0U; value >>= 1U)
1061:Drivers/CMSIS/Include/cmsis_gcc.h ****   {
1062:Drivers/CMSIS/Include/cmsis_gcc.h ****     result <<= 1U;
1063:Drivers/CMSIS/Include/cmsis_gcc.h ****     result |= value & 1U;
1064:Drivers/CMSIS/Include/cmsis_gcc.h ****     s--;
1065:Drivers/CMSIS/Include/cmsis_gcc.h ****   }
1066:Drivers/CMSIS/Include/cmsis_gcc.h ****   result <<= s;                        /* shift when v's highest bits are zero */
1067:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
1068:Drivers/CMSIS/Include/cmsis_gcc.h ****   return result;
 692              		.loc 2 1068 3 view .LVU142
 693              		.loc 2 1068 3 is_stmt 0 view .LVU143
 694              		.thumb
 695              		.syntax unified
 696              	.LBE27:
 697              	.LBE26:
 698              	.LBB28:
 699              	.LBI28:
1069:Drivers/CMSIS/Include/cmsis_gcc.h **** }
1070:Drivers/CMSIS/Include/cmsis_gcc.h **** 
1071:Drivers/CMSIS/Include/cmsis_gcc.h **** 
1072:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 55


1073:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Count leading zeros
1074:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Counts the number of leading zeros of a data value.
1075:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]  value  Value to count the leading zeros
1076:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return             number of leading zeros in value
1077:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
1078:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint8_t __CLZ(uint32_t value)
 700              		.loc 2 1078 30 is_stmt 1 view .LVU144
 701              	.LBB29:
1079:Drivers/CMSIS/Include/cmsis_gcc.h **** {
1080:Drivers/CMSIS/Include/cmsis_gcc.h ****   /* Even though __builtin_clz produces a CLZ instruction on ARM, formally
1081:Drivers/CMSIS/Include/cmsis_gcc.h ****      __builtin_clz(0) is undefined behaviour, so handle this case specially.
1082:Drivers/CMSIS/Include/cmsis_gcc.h ****      This guarantees ARM-compatible results if happening to compile on a non-ARM
1083:Drivers/CMSIS/Include/cmsis_gcc.h ****      target, and ensures the compiler doesn't decide to activate any
1084:Drivers/CMSIS/Include/cmsis_gcc.h ****      optimisations using the logic "value was passed to __builtin_clz, so it
1085:Drivers/CMSIS/Include/cmsis_gcc.h ****      is non-zero".
1086:Drivers/CMSIS/Include/cmsis_gcc.h ****      ARM GCC 7.3 and possibly earlier will optimise this test away, leaving a
1087:Drivers/CMSIS/Include/cmsis_gcc.h ****      single CLZ instruction.
1088:Drivers/CMSIS/Include/cmsis_gcc.h ****    */
1089:Drivers/CMSIS/Include/cmsis_gcc.h ****   if (value == 0U)
 702              		.loc 2 1089 3 view .LVU145
 703              		.loc 2 1089 6 is_stmt 0 view .LVU146
 704 000a 002A     		cmp	r2, #0
 705 000c 42D0     		beq	.L72
1090:Drivers/CMSIS/Include/cmsis_gcc.h ****   {
1091:Drivers/CMSIS/Include/cmsis_gcc.h ****     return 32U;
1092:Drivers/CMSIS/Include/cmsis_gcc.h ****   }
1093:Drivers/CMSIS/Include/cmsis_gcc.h ****   return __builtin_clz(value);
 706              		.loc 2 1093 3 is_stmt 1 view .LVU147
 707              		.loc 2 1093 10 is_stmt 0 discriminator 1 view .LVU148
 708 000e B2FA82F2 		clz	r2, r2
 709              	.LVL28:
 710              	.L68:
 711              		.loc 2 1093 10 discriminator 1 view .LVU149
 712              	.LBE29:
 713              	.LBE28:
 714              		.loc 1 1318 81 discriminator 2 view .LVU150
 715 0012 0832     		adds	r2, r2, #8
 716              		.loc 1 1318 107 discriminator 2 view .LVU151
 717 0014 02F01F02 		and	r2, r2, #31
 718              		.loc 1 1318 40 discriminator 2 view .LVU152
 719 0018 01FA02F2 		lsl	r2, r1, r2
1317:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****               (sPinParams->PinPolarity << ((POSITION_VAL(sPinParams->WakeUpPin) + PWR_WKUPEPR_WKUPP
 720              		.loc 1 1317 37 view .LVU153
 721 001c 43EA0201 		orr	r1, r3, r2
1319:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****               (sPinParams->PinPull << (((POSITION_VAL(sPinParams->WakeUpPin) * PWR_WAKEUP_PINS_PULL
 722              		.loc 1 1319 26 view .LVU154
 723 0020 8468     		ldr	r4, [r0, #8]
 724              	.LVL29:
 725              	.LBB31:
 726              	.LBI31:
1048:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 727              		.loc 2 1048 31 is_stmt 1 view .LVU155
 728              	.LBB32:
1050:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 729              		.loc 2 1050 3 view .LVU156
1055:Drivers/CMSIS/Include/cmsis_gcc.h **** #else
 730              		.loc 2 1055 4 view .LVU157
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 56


 731              		.syntax unified
 732              	@ 1055 "Drivers/CMSIS/Include/cmsis_gcc.h" 1
 733 0022 93FAA3F2 		rbit r2, r3
 734              	@ 0 "" 2
 735              	.LVL30:
1068:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 736              		.loc 2 1068 3 view .LVU158
1068:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 737              		.loc 2 1068 3 is_stmt 0 view .LVU159
 738              		.thumb
 739              		.syntax unified
 740              	.LBE32:
 741              	.LBE31:
 742              	.LBB33:
 743              	.LBI33:
1078:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 744              		.loc 2 1078 30 is_stmt 1 view .LVU160
 745              	.LBB34:
1089:Drivers/CMSIS/Include/cmsis_gcc.h ****   {
 746              		.loc 2 1089 3 view .LVU161
1089:Drivers/CMSIS/Include/cmsis_gcc.h ****   {
 747              		.loc 2 1089 6 is_stmt 0 view .LVU162
 748 0026 BAB3     		cbz	r2, .L73
 749              		.loc 2 1093 3 is_stmt 1 view .LVU163
 750              		.loc 2 1093 10 is_stmt 0 discriminator 1 view .LVU164
 751 0028 B2FA82F2 		clz	r2, r2
 752              	.LVL31:
 753              	.L69:
 754              		.loc 2 1093 10 discriminator 1 view .LVU165
 755              	.LBE34:
 756              	.LBE33:
 757              		.loc 1 1319 115 discriminator 2 view .LVU166
 758 002c 0832     		adds	r2, r2, #8
 759 002e 5200     		lsls	r2, r2, #1
 760              		.loc 1 1319 144 discriminator 2 view .LVU167
 761 0030 02F01E02 		and	r2, r2, #30
 762              		.loc 1 1319 36 discriminator 2 view .LVU168
 763 0034 04FA02F2 		lsl	r2, r4, r2
1317:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****               (sPinParams->PinPolarity << ((POSITION_VAL(sPinParams->WakeUpPin) + PWR_WKUPEPR_WKUPP
 764              		.loc 1 1317 13 view .LVU169
 765 0038 0A43     		orrs	r2, r2, r1
 766              	.LVL32:
1320:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1321:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   regMask   = sPinParams->WakeUpPin | \
 767              		.loc 1 1321 3 is_stmt 1 view .LVU170
 768              	.LBB36:
 769              	.LBI36:
1048:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 770              		.loc 2 1048 31 view .LVU171
 771              	.LBB37:
1050:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 772              		.loc 2 1050 3 view .LVU172
1055:Drivers/CMSIS/Include/cmsis_gcc.h **** #else
 773              		.loc 2 1055 4 view .LVU173
 774              		.syntax unified
 775              	@ 1055 "Drivers/CMSIS/Include/cmsis_gcc.h" 1
 776 003a 93FAA3F1 		rbit r1, r3
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 57


 777              	@ 0 "" 2
 778              	.LVL33:
1068:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 779              		.loc 2 1068 3 view .LVU174
1068:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 780              		.loc 2 1068 3 is_stmt 0 view .LVU175
 781              		.thumb
 782              		.syntax unified
 783              	.LBE37:
 784              	.LBE36:
 785              	.LBB38:
 786              	.LBI38:
1078:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 787              		.loc 2 1078 30 is_stmt 1 view .LVU176
 788              	.LBB39:
1089:Drivers/CMSIS/Include/cmsis_gcc.h ****   {
 789              		.loc 2 1089 3 view .LVU177
1089:Drivers/CMSIS/Include/cmsis_gcc.h ****   {
 790              		.loc 2 1089 6 is_stmt 0 view .LVU178
 791 003e 69B3     		cbz	r1, .L74
 792              		.loc 2 1093 3 is_stmt 1 view .LVU179
 793              		.loc 2 1093 10 is_stmt 0 discriminator 1 view .LVU180
 794 0040 B1FA81F1 		clz	r1, r1
 795              	.LVL34:
 796              	.L70:
 797              		.loc 2 1093 10 discriminator 1 view .LVU181
 798              	.LBE39:
 799              	.LBE38:
1322:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****               (PWR_WKUPEPR_WKUPP1 << (POSITION_VAL(sPinParams->WakeUpPin) & 0x1FU)) | \
 800              		.loc 1 1322 75 discriminator 2 view .LVU182
 801 0044 01F01F0C 		and	ip, r1, #31
 802              		.loc 1 1322 35 discriminator 2 view .LVU183
 803 0048 4FF48071 		mov	r1, #256
 804 004c 01FA0CF1 		lsl	r1, r1, ip
1321:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****               (PWR_WKUPEPR_WKUPP1 << (POSITION_VAL(sPinParams->WakeUpPin) & 0x1FU)) | \
 805              		.loc 1 1321 37 view .LVU184
 806 0050 1943     		orrs	r1, r1, r3
 807              	.LVL35:
 808              	.LBB41:
 809              	.LBI41:
1048:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 810              		.loc 2 1048 31 is_stmt 1 view .LVU185
 811              	.LBB42:
1050:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 812              		.loc 2 1050 3 view .LVU186
1055:Drivers/CMSIS/Include/cmsis_gcc.h **** #else
 813              		.loc 2 1055 4 view .LVU187
 814              		.syntax unified
 815              	@ 1055 "Drivers/CMSIS/Include/cmsis_gcc.h" 1
 816 0052 93FAA3F3 		rbit r3, r3
 817              	@ 0 "" 2
 818              	.LVL36:
1068:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 819              		.loc 2 1068 3 view .LVU188
1068:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 820              		.loc 2 1068 3 is_stmt 0 view .LVU189
 821              		.thumb
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 58


 822              		.syntax unified
 823              	.LBE42:
 824              	.LBE41:
 825              	.LBB43:
 826              	.LBI43:
1078:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 827              		.loc 2 1078 30 is_stmt 1 view .LVU190
 828              	.LBB44:
1089:Drivers/CMSIS/Include/cmsis_gcc.h ****   {
 829              		.loc 2 1089 3 view .LVU191
1089:Drivers/CMSIS/Include/cmsis_gcc.h ****   {
 830              		.loc 2 1089 6 is_stmt 0 view .LVU192
 831 0056 1BB3     		cbz	r3, .L75
 832              		.loc 2 1093 3 is_stmt 1 view .LVU193
 833              		.loc 2 1093 10 is_stmt 0 discriminator 1 view .LVU194
 834 0058 B3FA83F3 		clz	r3, r3
 835              	.LVL37:
 836              	.L71:
 837              		.loc 2 1093 10 discriminator 1 view .LVU195
 838              	.LBE44:
 839              	.LBE43:
1323:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****               (pullMask << ((POSITION_VAL(sPinParams->WakeUpPin) * PWR_WAKEUP_PINS_PULL_SHIFT_OFFSE
 840              		.loc 1 1323 66 discriminator 2 view .LVU196
 841 005c 5B00     		lsls	r3, r3, #1
 842              		.loc 1 1323 103 discriminator 2 view .LVU197
 843 005e 03F01E03 		and	r3, r3, #30
 844              		.loc 1 1323 25 discriminator 2 view .LVU198
 845 0062 4FF4403C 		mov	ip, #196608
 846 0066 0CFA03F3 		lsl	r3, ip, r3
1321:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****               (PWR_WKUPEPR_WKUPP1 << (POSITION_VAL(sPinParams->WakeUpPin) & 0x1FU)) | \
 847              		.loc 1 1321 13 view .LVU199
 848 006a 0B43     		orrs	r3, r3, r1
 849              	.LVL38:
1324:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1325:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Enable and Specify the Wake-Up pin polarity and the pull configuration
1326:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****      for the event detection (rising or falling edge) */
1327:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   MODIFY_REG (PWR->WKUPEPR, regMask, pinConfig);
 850              		.loc 1 1327 3 is_stmt 1 view .LVU200
 851 006c 0D4C     		ldr	r4, .L77
 852 006e A16A     		ldr	r1, [r4, #40]
 853 0070 21EA0303 		bic	r3, r1, r3
 854              	.LVL39:
 855              		.loc 1 1327 3 is_stmt 0 view .LVU201
 856 0074 1343     		orrs	r3, r3, r2
 857 0076 A362     		str	r3, [r4, #40]
1328:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #ifndef DUAL_CORE
1329:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Configure the Wakeup Pin EXTI Line */
1330:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   MODIFY_REG (EXTI->IMR2, PWR_EXTI_WAKEUP_PINS_MASK, (sPinParams->WakeUpPin << EXTI_IMR2_IM55_Pos))
 858              		.loc 1 1330 3 is_stmt 1 view .LVU202
 859 0078 4FF0B042 		mov	r2, #1476395008
 860              	.LVL40:
 861              		.loc 1 1330 3 is_stmt 0 view .LVU203
 862 007c D2F89030 		ldr	r3, [r2, #144]
 863 0080 23F0AC53 		bic	r3, r3, #360710144
 864 0084 0168     		ldr	r1, [r0]
 865 0086 43EAC153 		orr	r3, r3, r1, lsl #23
 866 008a C2F89030 		str	r3, [r2, #144]
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 59


1331:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #endif /* !DUAL_CORE */
1332:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** }
 867              		.loc 1 1332 1 view .LVU204
 868 008e 5DF8044B 		ldr	r4, [sp], #4
 869              	.LCFI8:
 870              		.cfi_remember_state
 871              		.cfi_restore 4
 872              		.cfi_def_cfa_offset 0
 873 0092 7047     		bx	lr
 874              	.LVL41:
 875              	.L72:
 876              	.LCFI9:
 877              		.cfi_restore_state
 878              	.LBB46:
 879              	.LBB30:
1091:Drivers/CMSIS/Include/cmsis_gcc.h ****   }
 880              		.loc 2 1091 12 view .LVU205
 881 0094 2022     		movs	r2, #32
 882              	.LVL42:
1091:Drivers/CMSIS/Include/cmsis_gcc.h ****   }
 883              		.loc 2 1091 12 view .LVU206
 884 0096 BCE7     		b	.L68
 885              	.LVL43:
 886              	.L73:
1091:Drivers/CMSIS/Include/cmsis_gcc.h ****   }
 887              		.loc 2 1091 12 view .LVU207
 888              	.LBE30:
 889              	.LBE46:
 890              	.LBB47:
 891              	.LBB35:
 892 0098 2022     		movs	r2, #32
 893              	.LVL44:
1091:Drivers/CMSIS/Include/cmsis_gcc.h ****   }
 894              		.loc 2 1091 12 view .LVU208
 895 009a C7E7     		b	.L69
 896              	.LVL45:
 897              	.L74:
1091:Drivers/CMSIS/Include/cmsis_gcc.h ****   }
 898              		.loc 2 1091 12 view .LVU209
 899              	.LBE35:
 900              	.LBE47:
 901              	.LBB48:
 902              	.LBB40:
 903 009c 2021     		movs	r1, #32
 904              	.LVL46:
1091:Drivers/CMSIS/Include/cmsis_gcc.h ****   }
 905              		.loc 2 1091 12 view .LVU210
 906 009e D1E7     		b	.L70
 907              	.LVL47:
 908              	.L75:
1091:Drivers/CMSIS/Include/cmsis_gcc.h ****   }
 909              		.loc 2 1091 12 view .LVU211
 910              	.LBE40:
 911              	.LBE48:
 912              	.LBB49:
 913              	.LBB45:
 914 00a0 2023     		movs	r3, #32
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 60


 915              	.LVL48:
1091:Drivers/CMSIS/Include/cmsis_gcc.h ****   }
 916              		.loc 2 1091 12 view .LVU212
 917 00a2 DBE7     		b	.L71
 918              	.L78:
 919              		.align	2
 920              	.L77:
 921 00a4 00480258 		.word	1476544512
 922              	.LBE45:
 923              	.LBE49:
 924              		.cfi_endproc
 925              	.LFE156:
 927              		.section	.text.HAL_PWREx_DisableWakeUpPin,"ax",%progbits
 928              		.align	1
 929              		.global	HAL_PWREx_DisableWakeUpPin
 930              		.syntax unified
 931              		.thumb
 932              		.thumb_func
 934              	HAL_PWREx_DisableWakeUpPin:
 935              	.LVL49:
 936              	.LFB157:
1333:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1334:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /**
1335:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @brief Disable the Wake-up PINx functionality.
1336:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @param  WakeUpPin : Specifies the Wake-Up pin to be disabled.
1337:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *          This parameter can be one of the following values:
1338:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *           @arg PWR_WAKEUP_PIN1 : Disable PA0  wake-up PIN.
1339:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *           @arg PWR_WAKEUP_PIN2 : Disable PA2  wake-up PIN.
1340:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *           @arg PWR_WAKEUP_PIN3 : Disable PI8  wake-up PIN.
1341:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *           @arg PWR_WAKEUP_PIN4 : Disable PC13 wake-up PIN.
1342:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *           @arg PWR_WAKEUP_PIN5 : Disable PI11 wake-up PIN.
1343:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *           @arg PWR_WAKEUP_PIN6 : Disable PC1  wake-up PIN.
1344:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @note   The PWR_WAKEUP_PIN3 and PWR_WAKEUP_PIN5 are available only for
1345:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         devices that support GPIOI port.
1346:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @retval None
1347:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   */
1348:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** void HAL_PWREx_DisableWakeUpPin (uint32_t WakeUpPin)
1349:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** {
 937              		.loc 1 1349 1 is_stmt 1 view -0
 938              		.cfi_startproc
 939              		@ args = 0, pretend = 0, frame = 0
 940              		@ frame_needed = 0, uses_anonymous_args = 0
 941              		@ link register save eliminated.
1350:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Check the parameter */
1351:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   assert_param (IS_PWR_WAKEUP_PIN (WakeUpPin));
 942              		.loc 1 1351 3 view .LVU214
1352:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1353:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Disable the WakeUpPin */
1354:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   CLEAR_BIT (PWR->WKUPEPR, WakeUpPin);
 943              		.loc 1 1354 3 view .LVU215
 944 0000 024A     		ldr	r2, .L80
 945 0002 936A     		ldr	r3, [r2, #40]
 946 0004 23EA0003 		bic	r3, r3, r0
 947 0008 9362     		str	r3, [r2, #40]
1355:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** }
 948              		.loc 1 1355 1 is_stmt 0 view .LVU216
 949 000a 7047     		bx	lr
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 61


 950              	.L81:
 951              		.align	2
 952              	.L80:
 953 000c 00480258 		.word	1476544512
 954              		.cfi_endproc
 955              	.LFE157:
 957              		.section	.text.HAL_PWREx_GetWakeupFlag,"ax",%progbits
 958              		.align	1
 959              		.global	HAL_PWREx_GetWakeupFlag
 960              		.syntax unified
 961              		.thumb
 962              		.thumb_func
 964              	HAL_PWREx_GetWakeupFlag:
 965              	.LVL50:
 966              	.LFB158:
1356:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1357:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /**
1358:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @brief Get the Wake-Up Pin pending flags.
1359:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @param  WakeUpFlag : Specifies the Wake-Up PIN flag to be checked.
1360:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *          This parameter can be one of the following values:
1361:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *            @arg PWR_WAKEUP_FLAG1    : Get wakeup event received from PA0.
1362:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *            @arg PWR_WAKEUP_FLAG2    : Get wakeup event received from PA2.
1363:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *            @arg PWR_WAKEUP_FLAG3    : Get wakeup event received from PI8.
1364:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *            @arg PWR_WAKEUP_FLAG4    : Get wakeup event received from PC13.
1365:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *            @arg PWR_WAKEUP_FLAG5    : Get wakeup event received from PI11.
1366:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *            @arg PWR_WAKEUP_FLAG6    : Get wakeup event received from PC1.
1367:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *            @arg PWR_WAKEUP_FLAG_ALL : Get Wakeup event received from all
1368:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *                                      wake up pins.
1369:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @note   The PWR_WAKEUP_FLAG3 and PWR_WAKEUP_FLAG5 are available only for
1370:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         devices that support GPIOI port.
1371:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @retval The Wake-Up pin flag.
1372:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   */
1373:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** uint32_t HAL_PWREx_GetWakeupFlag (uint32_t WakeUpFlag)
1374:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** {
 967              		.loc 1 1374 1 is_stmt 1 view -0
 968              		.cfi_startproc
 969              		@ args = 0, pretend = 0, frame = 0
 970              		@ frame_needed = 0, uses_anonymous_args = 0
 971              		@ link register save eliminated.
1375:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Check the parameters */
1376:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   assert_param (IS_PWR_WAKEUP_FLAG (WakeUpFlag));
 972              		.loc 1 1376 3 view .LVU218
1377:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1378:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Return the wake up pin flag */
1379:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   return (PWR->WKUPFR & WakeUpFlag);
 973              		.loc 1 1379 3 view .LVU219
 974              		.loc 1 1379 14 is_stmt 0 view .LVU220
 975 0000 014B     		ldr	r3, .L83
 976 0002 5B6A     		ldr	r3, [r3, #36]
1380:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** }
 977              		.loc 1 1380 1 view .LVU221
 978 0004 1840     		ands	r0, r0, r3
 979              	.LVL51:
 980              		.loc 1 1380 1 view .LVU222
 981 0006 7047     		bx	lr
 982              	.L84:
 983              		.align	2
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 62


 984              	.L83:
 985 0008 00480258 		.word	1476544512
 986              		.cfi_endproc
 987              	.LFE158:
 989              		.section	.text.HAL_PWREx_ClearWakeupFlag,"ax",%progbits
 990              		.align	1
 991              		.global	HAL_PWREx_ClearWakeupFlag
 992              		.syntax unified
 993              		.thumb
 994              		.thumb_func
 996              	HAL_PWREx_ClearWakeupFlag:
 997              	.LVL52:
 998              	.LFB159:
1381:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1382:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /**
1383:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @brief Clear the Wake-Up pin pending flag.
1384:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @param  WakeUpFlag: Specifies the Wake-Up PIN flag to clear.
1385:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *          This parameter can be one of the following values:
1386:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *            @arg PWR_WAKEUP_FLAG1 : Clear the wakeup event received from PA0.
1387:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *            @arg PWR_WAKEUP_FLAG2 : Clear the wakeup event received from PA2.
1388:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *            @arg PWR_WAKEUP_FLAG3 : Clear the wakeup event received from PI8.
1389:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *            @arg PWR_WAKEUP_FLAG4 : Clear the wakeup event received from PC13.
1390:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *            @arg PWR_WAKEUP_FLAG5 : Clear the wakeup event received from PI11.
1391:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *            @arg PWR_WAKEUP_FLAG6 : Clear the wakeup event received from PC1.
1392:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *            @arg PWR_WAKEUP_FLAG_ALL : Clear the wakeup events received from
1393:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *                                      all wake up pins.
1394:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @note   The PWR_WAKEUP_FLAG3 and PWR_WAKEUP_FLAG5 are available only for
1395:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         devices that support GPIOI port.
1396:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @retval HAL status.
1397:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   */
1398:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** HAL_StatusTypeDef HAL_PWREx_ClearWakeupFlag (uint32_t WakeUpFlag)
1399:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** {
 999              		.loc 1 1399 1 is_stmt 1 view -0
 1000              		.cfi_startproc
 1001              		@ args = 0, pretend = 0, frame = 0
 1002              		@ frame_needed = 0, uses_anonymous_args = 0
 1003              		@ link register save eliminated.
1400:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Check the parameter */
1401:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   assert_param (IS_PWR_WAKEUP_FLAG (WakeUpFlag));
 1004              		.loc 1 1401 3 view .LVU224
1402:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1403:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Clear the wake up event received from wake up pin x */
1404:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   SET_BIT (PWR->WKUPCR, WakeUpFlag);
 1005              		.loc 1 1404 3 view .LVU225
 1006 0000 054B     		ldr	r3, .L88
 1007 0002 1A6A     		ldr	r2, [r3, #32]
 1008 0004 0243     		orrs	r2, r2, r0
 1009 0006 1A62     		str	r2, [r3, #32]
1405:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1406:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Check if the wake up event is well cleared */
1407:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   if ((PWR->WKUPFR & WakeUpFlag) != 0U)
 1010              		.loc 1 1407 3 view .LVU226
 1011              		.loc 1 1407 11 is_stmt 0 view .LVU227
 1012 0008 5B6A     		ldr	r3, [r3, #36]
 1013              		.loc 1 1407 6 view .LVU228
 1014 000a 0342     		tst	r3, r0
 1015 000c 01D1     		bne	.L87
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 63


1408:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   {
1409:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     return HAL_ERROR;
1410:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   }
1411:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1412:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   return HAL_OK;
 1016              		.loc 1 1412 10 view .LVU229
 1017 000e 0020     		movs	r0, #0
 1018              	.LVL53:
 1019              		.loc 1 1412 10 view .LVU230
 1020 0010 7047     		bx	lr
 1021              	.LVL54:
 1022              	.L87:
1409:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   }
 1023              		.loc 1 1409 12 view .LVU231
 1024 0012 0120     		movs	r0, #1
 1025              	.LVL55:
1413:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** }
 1026              		.loc 1 1413 1 view .LVU232
 1027 0014 7047     		bx	lr
 1028              	.L89:
 1029 0016 00BF     		.align	2
 1030              	.L88:
 1031 0018 00480258 		.word	1476544512
 1032              		.cfi_endproc
 1033              	.LFE159:
 1035              		.section	.text.HAL_PWREx_WKUP1_Callback,"ax",%progbits
 1036              		.align	1
 1037              		.weak	HAL_PWREx_WKUP1_Callback
 1038              		.syntax unified
 1039              		.thumb
 1040              		.thumb_func
 1042              	HAL_PWREx_WKUP1_Callback:
 1043              	.LFB161:
1414:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1415:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /**
1416:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @brief This function handles the PWR WAKEUP PIN interrupt request.
1417:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @note   This API should be called under the WAKEUP_PIN_IRQHandler().
1418:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @retval None.
1419:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   */
1420:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** void HAL_PWREx_WAKEUP_PIN_IRQHandler (void)
1421:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** {
1422:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Wakeup pin EXTI line interrupt detected */
1423:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   if (READ_BIT(PWR->WKUPFR, PWR_WKUPFR_WKUPF1) != 0U)
1424:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   {
1425:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     /* Clear PWR WKUPF1 flag */
1426:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     __HAL_PWR_CLEAR_WAKEUPFLAG (PWR_FLAG_WKUP1);
1427:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1428:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     /* PWR WKUP1 interrupt user callback */
1429:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     HAL_PWREx_WKUP1_Callback ();
1430:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   }
1431:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   else if (READ_BIT (PWR->WKUPFR, PWR_WKUPFR_WKUPF2) != 0U)
1432:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   {
1433:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     /* Clear PWR WKUPF2 flag */
1434:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     __HAL_PWR_CLEAR_WAKEUPFLAG (PWR_FLAG_WKUP2);
1435:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1436:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     /* PWR WKUP2 interrupt user callback */
1437:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     HAL_PWREx_WKUP2_Callback ();
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 64


1438:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   }
1439:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #if defined (PWR_WKUPFR_WKUPF3)
1440:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   else if (READ_BIT (PWR->WKUPFR, PWR_WKUPFR_WKUPF3) != 0U)
1441:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   {
1442:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     /* Clear PWR WKUPF3 flag */
1443:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     __HAL_PWR_CLEAR_WAKEUPFLAG (PWR_FLAG_WKUP3);
1444:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1445:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     /* PWR WKUP3 interrupt user callback */
1446:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     HAL_PWREx_WKUP3_Callback ();
1447:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   }
1448:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #endif /* defined (PWR_WKUPFR_WKUPF3) */
1449:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   else if (READ_BIT (PWR->WKUPFR, PWR_WKUPFR_WKUPF4) != 0U)
1450:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   {
1451:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     /* Clear PWR WKUPF4 flag */
1452:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     __HAL_PWR_CLEAR_WAKEUPFLAG (PWR_FLAG_WKUP4);
1453:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1454:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     /* PWR WKUP4 interrupt user callback */
1455:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     HAL_PWREx_WKUP4_Callback ();
1456:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   }
1457:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #if defined (PWR_WKUPFR_WKUPF5)
1458:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   else if (READ_BIT (PWR->WKUPFR, PWR_WKUPFR_WKUPF5) != 0U)
1459:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   {
1460:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     /* Clear PWR WKUPF5 flag */
1461:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     __HAL_PWR_CLEAR_WAKEUPFLAG (PWR_FLAG_WKUP5);
1462:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1463:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     /* PWR WKUP5 interrupt user callback */
1464:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     HAL_PWREx_WKUP5_Callback ();
1465:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   }
1466:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #endif /* defined (PWR_WKUPFR_WKUPF5) */
1467:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   else
1468:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   {
1469:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     /* Clear PWR WKUPF6 flag */
1470:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     __HAL_PWR_CLEAR_WAKEUPFLAG (PWR_FLAG_WKUP6);
1471:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1472:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     /* PWR WKUP6 interrupt user callback */
1473:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     HAL_PWREx_WKUP6_Callback ();
1474:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   }
1475:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** }
1476:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1477:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /**
1478:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @brief PWR WKUP1 interrupt callback.
1479:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @retval None.
1480:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   */
1481:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** __weak void HAL_PWREx_WKUP1_Callback (void)
1482:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** {
 1044              		.loc 1 1482 1 is_stmt 1 view -0
 1045              		.cfi_startproc
 1046              		@ args = 0, pretend = 0, frame = 0
 1047              		@ frame_needed = 0, uses_anonymous_args = 0
 1048              		@ link register save eliminated.
1483:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* NOTE : This function should not be modified, when the callback is needed,
1484:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****             the HAL_PWREx_WKUP1Callback can be implemented in the user file
1485:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   */
1486:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** }
 1049              		.loc 1 1486 1 view .LVU234
 1050 0000 7047     		bx	lr
 1051              		.cfi_endproc
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 65


 1052              	.LFE161:
 1054              		.section	.text.HAL_PWREx_WKUP2_Callback,"ax",%progbits
 1055              		.align	1
 1056              		.weak	HAL_PWREx_WKUP2_Callback
 1057              		.syntax unified
 1058              		.thumb
 1059              		.thumb_func
 1061              	HAL_PWREx_WKUP2_Callback:
 1062              	.LFB162:
1487:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1488:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /**
1489:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @brief PWR WKUP2 interrupt callback.
1490:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @retval None.
1491:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   */
1492:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** __weak void HAL_PWREx_WKUP2_Callback (void)
1493:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** {
 1063              		.loc 1 1493 1 view -0
 1064              		.cfi_startproc
 1065              		@ args = 0, pretend = 0, frame = 0
 1066              		@ frame_needed = 0, uses_anonymous_args = 0
 1067              		@ link register save eliminated.
1494:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* NOTE : This function should not be modified, when the callback is needed,
1495:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****             the HAL_PWREx_WKUP2Callback can be implemented in the user file
1496:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   */
1497:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** }
 1068              		.loc 1 1497 1 view .LVU236
 1069 0000 7047     		bx	lr
 1070              		.cfi_endproc
 1071              	.LFE162:
 1073              		.section	.text.HAL_PWREx_WKUP4_Callback,"ax",%progbits
 1074              		.align	1
 1075              		.weak	HAL_PWREx_WKUP4_Callback
 1076              		.syntax unified
 1077              		.thumb
 1078              		.thumb_func
 1080              	HAL_PWREx_WKUP4_Callback:
 1081              	.LFB163:
1498:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1499:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #if defined (PWR_WKUPFR_WKUPF3)
1500:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /**
1501:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @brief PWR WKUP3 interrupt callback.
1502:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @retval None.
1503:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   */
1504:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** __weak void HAL_PWREx_WKUP3_Callback (void)
1505:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** {
1506:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* NOTE : This function should not be modified, when the callback is needed,
1507:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****             the HAL_PWREx_WKUP3Callback can be implemented in the user file
1508:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   */
1509:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** }
1510:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #endif /* defined (PWR_WKUPFR_WKUPF3) */
1511:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1512:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /**
1513:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @brief PWR WKUP4 interrupt callback.
1514:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @retval None.
1515:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   */
1516:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** __weak void HAL_PWREx_WKUP4_Callback (void)
1517:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** {
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 66


 1082              		.loc 1 1517 1 view -0
 1083              		.cfi_startproc
 1084              		@ args = 0, pretend = 0, frame = 0
 1085              		@ frame_needed = 0, uses_anonymous_args = 0
 1086              		@ link register save eliminated.
1518:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* NOTE : This function should not be modified, when the callback is needed,
1519:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****             the HAL_PWREx_WKUP4Callback can be implemented in the user file
1520:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   */
1521:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** }
 1087              		.loc 1 1521 1 view .LVU238
 1088 0000 7047     		bx	lr
 1089              		.cfi_endproc
 1090              	.LFE163:
 1092              		.section	.text.HAL_PWREx_WKUP6_Callback,"ax",%progbits
 1093              		.align	1
 1094              		.weak	HAL_PWREx_WKUP6_Callback
 1095              		.syntax unified
 1096              		.thumb
 1097              		.thumb_func
 1099              	HAL_PWREx_WKUP6_Callback:
 1100              	.LFB164:
1522:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1523:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #if defined (PWR_WKUPFR_WKUPF5)
1524:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /**
1525:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @brief PWR WKUP5 interrupt callback.
1526:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @retval None.
1527:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   */
1528:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** __weak void HAL_PWREx_WKUP5_Callback (void)
1529:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** {
1530:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* NOTE : This function should not be modified, when the callback is needed,
1531:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****             the HAL_PWREx_WKUP5Callback can be implemented in the user file
1532:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   */
1533:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** }
1534:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #endif /* defined (PWR_WKUPFR_WKUPF5) */
1535:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1536:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /**
1537:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @brief PWR WKUP6 interrupt callback.
1538:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @retval None.
1539:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   */
1540:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** __weak void HAL_PWREx_WKUP6_Callback (void)
1541:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** {
 1101              		.loc 1 1541 1 view -0
 1102              		.cfi_startproc
 1103              		@ args = 0, pretend = 0, frame = 0
 1104              		@ frame_needed = 0, uses_anonymous_args = 0
 1105              		@ link register save eliminated.
1542:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* NOTE : This function should not be modified, when the callback is needed,
1543:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****             the HAL_PWREx_WKUP6Callback can be implemented in the user file
1544:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   */
1545:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** }
 1106              		.loc 1 1545 1 view .LVU240
 1107 0000 7047     		bx	lr
 1108              		.cfi_endproc
 1109              	.LFE164:
 1111              		.section	.text.HAL_PWREx_WAKEUP_PIN_IRQHandler,"ax",%progbits
 1112              		.align	1
 1113              		.global	HAL_PWREx_WAKEUP_PIN_IRQHandler
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 67


 1114              		.syntax unified
 1115              		.thumb
 1116              		.thumb_func
 1118              	HAL_PWREx_WAKEUP_PIN_IRQHandler:
 1119              	.LFB160:
1421:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Wakeup pin EXTI line interrupt detected */
 1120              		.loc 1 1421 1 view -0
 1121              		.cfi_startproc
 1122              		@ args = 0, pretend = 0, frame = 0
 1123              		@ frame_needed = 0, uses_anonymous_args = 0
 1124 0000 08B5     		push	{r3, lr}
 1125              	.LCFI10:
 1126              		.cfi_def_cfa_offset 8
 1127              		.cfi_offset 3, -8
 1128              		.cfi_offset 14, -4
1423:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   {
 1129              		.loc 1 1423 3 view .LVU242
1423:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   {
 1130              		.loc 1 1423 7 is_stmt 0 view .LVU243
 1131 0002 174B     		ldr	r3, .L102
 1132 0004 5B6A     		ldr	r3, [r3, #36]
1423:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   {
 1133              		.loc 1 1423 6 view .LVU244
 1134 0006 13F0010F 		tst	r3, #1
 1135 000a 11D1     		bne	.L100
1431:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   {
 1136              		.loc 1 1431 8 is_stmt 1 view .LVU245
1431:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   {
 1137              		.loc 1 1431 12 is_stmt 0 view .LVU246
 1138 000c 144B     		ldr	r3, .L102
 1139 000e 5B6A     		ldr	r3, [r3, #36]
1431:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   {
 1140              		.loc 1 1431 11 view .LVU247
 1141 0010 13F0020F 		tst	r3, #2
 1142 0014 14D1     		bne	.L101
1449:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   {
 1143              		.loc 1 1449 8 is_stmt 1 view .LVU248
1449:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   {
 1144              		.loc 1 1449 12 is_stmt 0 view .LVU249
 1145 0016 124B     		ldr	r3, .L102
 1146 0018 5B6A     		ldr	r3, [r3, #36]
1449:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   {
 1147              		.loc 1 1449 11 view .LVU250
 1148 001a 13F0080F 		tst	r3, #8
 1149 001e 17D0     		beq	.L98
1452:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 1150              		.loc 1 1452 5 is_stmt 1 view .LVU251
 1151 0020 0F4A     		ldr	r2, .L102
 1152 0022 136A     		ldr	r3, [r2, #32]
 1153 0024 43F00803 		orr	r3, r3, #8
 1154 0028 1362     		str	r3, [r2, #32]
1455:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   }
 1155              		.loc 1 1455 5 view .LVU252
 1156 002a FFF7FEFF 		bl	HAL_PWREx_WKUP4_Callback
 1157              	.LVL56:
 1158 002e 06E0     		b	.L94
 1159              	.L100:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 68


1426:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 1160              		.loc 1 1426 5 view .LVU253
 1161 0030 0B4A     		ldr	r2, .L102
 1162 0032 136A     		ldr	r3, [r2, #32]
 1163 0034 43F00103 		orr	r3, r3, #1
 1164 0038 1362     		str	r3, [r2, #32]
1429:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   }
 1165              		.loc 1 1429 5 view .LVU254
 1166 003a FFF7FEFF 		bl	HAL_PWREx_WKUP1_Callback
 1167              	.LVL57:
 1168              	.L94:
1475:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 1169              		.loc 1 1475 1 is_stmt 0 view .LVU255
 1170 003e 08BD     		pop	{r3, pc}
 1171              	.L101:
1434:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 1172              		.loc 1 1434 5 is_stmt 1 view .LVU256
 1173 0040 074A     		ldr	r2, .L102
 1174 0042 136A     		ldr	r3, [r2, #32]
 1175 0044 43F00203 		orr	r3, r3, #2
 1176 0048 1362     		str	r3, [r2, #32]
1437:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   }
 1177              		.loc 1 1437 5 view .LVU257
 1178 004a FFF7FEFF 		bl	HAL_PWREx_WKUP2_Callback
 1179              	.LVL58:
 1180 004e F6E7     		b	.L94
 1181              	.L98:
1470:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 1182              		.loc 1 1470 5 view .LVU258
 1183 0050 034A     		ldr	r2, .L102
 1184 0052 136A     		ldr	r3, [r2, #32]
 1185 0054 43F02003 		orr	r3, r3, #32
 1186 0058 1362     		str	r3, [r2, #32]
1473:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   }
 1187              		.loc 1 1473 5 view .LVU259
 1188 005a FFF7FEFF 		bl	HAL_PWREx_WKUP6_Callback
 1189              	.LVL59:
1475:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 1190              		.loc 1 1475 1 is_stmt 0 view .LVU260
 1191 005e EEE7     		b	.L94
 1192              	.L103:
 1193              		.align	2
 1194              	.L102:
 1195 0060 00480258 		.word	1476544512
 1196              		.cfi_endproc
 1197              	.LFE160:
 1199              		.section	.text.HAL_PWREx_EnableBkUpReg,"ax",%progbits
 1200              		.align	1
 1201              		.global	HAL_PWREx_EnableBkUpReg
 1202              		.syntax unified
 1203              		.thumb
 1204              		.thumb_func
 1206              	HAL_PWREx_EnableBkUpReg:
 1207              	.LFB165:
1546:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /**
1547:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @}
1548:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   */
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 69


1549:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1550:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /** @defgroup PWREx_Exported_Functions_Group3 Peripherals control functions
1551:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @brief    Peripherals control functions
1552:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *
1553:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** @verbatim
1554:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****  ===============================================================================
1555:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****                  ##### Peripherals control functions #####
1556:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****  ===============================================================================
1557:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1558:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     *** Main and Backup Regulators configuration ***
1559:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     ================================================
1560:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     [..]
1561:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       (+) The backup domain includes 4 Kbytes of backup SRAM accessible only
1562:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           from the CPU, and addressed in 32-bit, 16-bit or 8-bit mode. Its
1563:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           content is retained even in Standby or VBAT mode when the low power
1564:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           backup regulator is enabled. It can be considered as an internal
1565:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           EEPROM when VBAT is always present. You can use the
1566:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           HAL_PWREx_EnableBkUpReg() function to enable the low power backup
1567:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           regulator.
1568:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       (+) When the backup domain is supplied by VDD (analog switch connected to
1569:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           VDD) the backup SRAM is powered from VDD which replaces the VBAT power
1570:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           supply to save battery life.
1571:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       (+) The backup SRAM is not mass erased by a tamper event. It is read
1572:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           protected to prevent confidential data, such as cryptographic private
1573:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           key, from being accessed. The backup SRAM can be erased only through
1574:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           the Flash interface when a protection level change from level 1 to
1575:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           level 0 is requested.
1576:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       -@- Refer to the description of Read protection (RDP) in the Flash
1577:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           programming manual.
1578:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       (+) The main internal regulator can be configured to have a tradeoff
1579:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           between performance and power consumption when the device does not
1580:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           operate at the maximum frequency. This is done through
1581:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           HAL_PWREx_ControlVoltageScaling(VOS) function which configure the VOS
1582:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           bit in PWR_D3CR register.
1583:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       (+) The main internal regulator can be configured to operate in Low Power
1584:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           mode when the system enters STOP mode to further reduce power
1585:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           consumption.
1586:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           This is done through HAL_PWREx_ControlStopModeVoltageScaling(SVOS)
1587:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           function which configure the SVOS bit in PWR_CR1 register.
1588:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           The selected SVOS4 and SVOS5 levels add an additional startup delay
1589:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           when exiting from system Stop mode.
1590:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     -@- Refer to the product datasheets for more details.
1591:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1592:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     *** USB Regulator configuration ***
1593:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     ===================================
1594:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     [..]
1595:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       (+) The USB transceivers are supplied from a dedicated VDD33USB supply
1596:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           that can be provided either by the integrated USB regulator, or by an
1597:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           external USB supply.
1598:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       (+) The USB regulator is enabled by HAL_PWREx_EnableUSBReg() function, the
1599:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           VDD33USB is then provided from the USB regulator.
1600:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       (+) When the USB regulator is enabled, the VDD33USB supply level detector
1601:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           shall be enabled through  HAL_PWREx_EnableUSBVoltageDetector()
1602:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           function.
1603:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       (+) The USB regulator is disabled through HAL_PWREx_DisableUSBReg()
1604:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           function and VDD33USB can be provided from an external supply. In this
1605:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           case VDD33USB and VDD50USB shall be connected together.
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 70


1606:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1607:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     *** VBAT battery charging ***
1608:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     =============================
1609:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     [..]
1610:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       (+) When VDD is present, the external battery connected to VBAT can be
1611:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           charged through an internal resistance. VBAT charging can be performed
1612:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           either through a 5 KOhm resistor or through a 1.5 KOhm resistor.
1613:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       (+) VBAT charging is enabled by HAL_PWREx_EnableBatteryCharging
1614:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           (ResistorValue) function with:
1615:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****        (++) ResistorValue:
1616:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****         (+++) PWR_BATTERY_CHARGING_RESISTOR_5: 5 KOhm resistor.
1617:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****         (+++) PWR_BATTERY_CHARGING_RESISTOR_1_5: 1.5 KOhm resistor.
1618:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       (+) VBAT charging is disabled by HAL_PWREx_DisableBatteryCharging()
1619:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           function.
1620:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1621:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** @endverbatim
1622:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @{
1623:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   */
1624:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1625:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /**
1626:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @brief Enable the Backup Regulator.
1627:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @retval HAL status.
1628:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   */
1629:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** HAL_StatusTypeDef HAL_PWREx_EnableBkUpReg (void)
1630:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** {
 1208              		.loc 1 1630 1 is_stmt 1 view -0
 1209              		.cfi_startproc
 1210              		@ args = 0, pretend = 0, frame = 0
 1211              		@ frame_needed = 0, uses_anonymous_args = 0
 1212 0000 10B5     		push	{r4, lr}
 1213              	.LCFI11:
 1214              		.cfi_def_cfa_offset 8
 1215              		.cfi_offset 4, -8
 1216              		.cfi_offset 14, -4
1631:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   uint32_t tickstart;
 1217              		.loc 1 1631 3 view .LVU262
1632:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1633:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Enable the Backup regulator */
1634:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   SET_BIT (PWR->CR2, PWR_CR2_BREN);
 1218              		.loc 1 1634 3 view .LVU263
 1219 0002 0B4A     		ldr	r2, .L111
 1220 0004 9368     		ldr	r3, [r2, #8]
 1221 0006 43F00103 		orr	r3, r3, #1
 1222 000a 9360     		str	r3, [r2, #8]
1635:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1636:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Get tick */
1637:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   tickstart = HAL_GetTick ();
 1223              		.loc 1 1637 3 view .LVU264
 1224              		.loc 1 1637 15 is_stmt 0 view .LVU265
 1225 000c FFF7FEFF 		bl	HAL_GetTick
 1226              	.LVL60:
 1227 0010 0446     		mov	r4, r0
 1228              	.LVL61:
1638:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1639:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Wait till Backup regulator ready flag is set */
1640:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   while (__HAL_PWR_GET_FLAG (PWR_FLAG_BRR) == 0U)
 1229              		.loc 1 1640 3 is_stmt 1 view .LVU266
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 71


 1230              	.L105:
 1231              		.loc 1 1640 44 view .LVU267
 1232              		.loc 1 1640 10 is_stmt 0 view .LVU268
 1233 0012 074B     		ldr	r3, .L111
 1234 0014 9B68     		ldr	r3, [r3, #8]
 1235              		.loc 1 1640 44 view .LVU269
 1236 0016 13F4803F 		tst	r3, #65536
 1237 001a 07D1     		bne	.L110
1641:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   {
1642:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     if ((HAL_GetTick() - tickstart ) > PWR_FLAG_SETTING_DELAY)
 1238              		.loc 1 1642 5 is_stmt 1 view .LVU270
 1239              		.loc 1 1642 10 is_stmt 0 view .LVU271
 1240 001c FFF7FEFF 		bl	HAL_GetTick
 1241              	.LVL62:
 1242              		.loc 1 1642 24 discriminator 1 view .LVU272
 1243 0020 001B     		subs	r0, r0, r4
 1244              		.loc 1 1642 8 discriminator 1 view .LVU273
 1245 0022 B0F57A7F 		cmp	r0, #1000
 1246 0026 F4D9     		bls	.L105
1643:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     {
1644:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       return HAL_ERROR;
 1247              		.loc 1 1644 14 view .LVU274
 1248 0028 0120     		movs	r0, #1
 1249 002a 00E0     		b	.L106
 1250              	.L110:
1645:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     }
1646:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   }
1647:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1648:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   return HAL_OK;
 1251              		.loc 1 1648 10 view .LVU275
 1252 002c 0020     		movs	r0, #0
 1253              	.L106:
1649:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** }
 1254              		.loc 1 1649 1 view .LVU276
 1255 002e 10BD     		pop	{r4, pc}
 1256              	.LVL63:
 1257              	.L112:
 1258              		.loc 1 1649 1 view .LVU277
 1259              		.align	2
 1260              	.L111:
 1261 0030 00480258 		.word	1476544512
 1262              		.cfi_endproc
 1263              	.LFE165:
 1265              		.section	.text.HAL_PWREx_DisableBkUpReg,"ax",%progbits
 1266              		.align	1
 1267              		.global	HAL_PWREx_DisableBkUpReg
 1268              		.syntax unified
 1269              		.thumb
 1270              		.thumb_func
 1272              	HAL_PWREx_DisableBkUpReg:
 1273              	.LFB166:
1650:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1651:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /**
1652:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @brief Disable the Backup Regulator.
1653:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @retval HAL status.
1654:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   */
1655:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** HAL_StatusTypeDef HAL_PWREx_DisableBkUpReg (void)
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 72


1656:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** {
 1274              		.loc 1 1656 1 is_stmt 1 view -0
 1275              		.cfi_startproc
 1276              		@ args = 0, pretend = 0, frame = 0
 1277              		@ frame_needed = 0, uses_anonymous_args = 0
 1278 0000 10B5     		push	{r4, lr}
 1279              	.LCFI12:
 1280              		.cfi_def_cfa_offset 8
 1281              		.cfi_offset 4, -8
 1282              		.cfi_offset 14, -4
1657:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   uint32_t tickstart;
 1283              		.loc 1 1657 3 view .LVU279
1658:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1659:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Disable the Backup regulator */
1660:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   CLEAR_BIT (PWR->CR2, PWR_CR2_BREN);
 1284              		.loc 1 1660 3 view .LVU280
 1285 0002 0B4A     		ldr	r2, .L120
 1286 0004 9368     		ldr	r3, [r2, #8]
 1287 0006 23F00103 		bic	r3, r3, #1
 1288 000a 9360     		str	r3, [r2, #8]
1661:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1662:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Get tick */
1663:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   tickstart = HAL_GetTick ();
 1289              		.loc 1 1663 3 view .LVU281
 1290              		.loc 1 1663 15 is_stmt 0 view .LVU282
 1291 000c FFF7FEFF 		bl	HAL_GetTick
 1292              	.LVL64:
 1293 0010 0446     		mov	r4, r0
 1294              	.LVL65:
1664:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1665:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Wait till Backup regulator ready flag is reset */
1666:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   while (__HAL_PWR_GET_FLAG (PWR_FLAG_BRR) != 0U)
 1295              		.loc 1 1666 3 is_stmt 1 view .LVU283
 1296              	.L114:
 1297              		.loc 1 1666 44 view .LVU284
 1298              		.loc 1 1666 10 is_stmt 0 view .LVU285
 1299 0012 074B     		ldr	r3, .L120
 1300 0014 9B68     		ldr	r3, [r3, #8]
 1301              		.loc 1 1666 44 view .LVU286
 1302 0016 13F4803F 		tst	r3, #65536
 1303 001a 07D0     		beq	.L119
1667:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   {
1668:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     if ((HAL_GetTick() - tickstart ) > PWR_FLAG_SETTING_DELAY)
 1304              		.loc 1 1668 5 is_stmt 1 view .LVU287
 1305              		.loc 1 1668 10 is_stmt 0 view .LVU288
 1306 001c FFF7FEFF 		bl	HAL_GetTick
 1307              	.LVL66:
 1308              		.loc 1 1668 24 discriminator 1 view .LVU289
 1309 0020 001B     		subs	r0, r0, r4
 1310              		.loc 1 1668 8 discriminator 1 view .LVU290
 1311 0022 B0F57A7F 		cmp	r0, #1000
 1312 0026 F4D9     		bls	.L114
1669:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     {
1670:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       return HAL_ERROR;
 1313              		.loc 1 1670 14 view .LVU291
 1314 0028 0120     		movs	r0, #1
 1315 002a 00E0     		b	.L115
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 73


 1316              	.L119:
1671:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     }
1672:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   }
1673:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1674:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   return HAL_OK;
 1317              		.loc 1 1674 10 view .LVU292
 1318 002c 0020     		movs	r0, #0
 1319              	.L115:
1675:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** }
 1320              		.loc 1 1675 1 view .LVU293
 1321 002e 10BD     		pop	{r4, pc}
 1322              	.LVL67:
 1323              	.L121:
 1324              		.loc 1 1675 1 view .LVU294
 1325              		.align	2
 1326              	.L120:
 1327 0030 00480258 		.word	1476544512
 1328              		.cfi_endproc
 1329              	.LFE166:
 1331              		.section	.text.HAL_PWREx_EnableUSBReg,"ax",%progbits
 1332              		.align	1
 1333              		.global	HAL_PWREx_EnableUSBReg
 1334              		.syntax unified
 1335              		.thumb
 1336              		.thumb_func
 1338              	HAL_PWREx_EnableUSBReg:
 1339              	.LFB167:
1676:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1677:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /**
1678:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @brief Enable the USB Regulator.
1679:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @retval HAL status.
1680:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   */
1681:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** HAL_StatusTypeDef HAL_PWREx_EnableUSBReg (void)
1682:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** {
 1340              		.loc 1 1682 1 is_stmt 1 view -0
 1341              		.cfi_startproc
 1342              		@ args = 0, pretend = 0, frame = 0
 1343              		@ frame_needed = 0, uses_anonymous_args = 0
 1344 0000 10B5     		push	{r4, lr}
 1345              	.LCFI13:
 1346              		.cfi_def_cfa_offset 8
 1347              		.cfi_offset 4, -8
 1348              		.cfi_offset 14, -4
1683:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   uint32_t tickstart;
 1349              		.loc 1 1683 3 view .LVU296
1684:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1685:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Enable the USB regulator */
1686:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   SET_BIT (PWR->CR3, PWR_CR3_USBREGEN);
 1350              		.loc 1 1686 3 view .LVU297
 1351 0002 0B4A     		ldr	r2, .L129
 1352 0004 D368     		ldr	r3, [r2, #12]
 1353 0006 43F00073 		orr	r3, r3, #33554432
 1354 000a D360     		str	r3, [r2, #12]
1687:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1688:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Get tick */
1689:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   tickstart = HAL_GetTick ();
 1355              		.loc 1 1689 3 view .LVU298
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 74


 1356              		.loc 1 1689 15 is_stmt 0 view .LVU299
 1357 000c FFF7FEFF 		bl	HAL_GetTick
 1358              	.LVL68:
 1359 0010 0446     		mov	r4, r0
 1360              	.LVL69:
1690:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1691:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Wait till the USB regulator ready flag is set */
1692:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   while (__HAL_PWR_GET_FLAG (PWR_FLAG_USB33RDY) == 0U)
 1361              		.loc 1 1692 3 is_stmt 1 view .LVU300
 1362              	.L123:
 1363              		.loc 1 1692 49 view .LVU301
 1364              		.loc 1 1692 10 is_stmt 0 view .LVU302
 1365 0012 074B     		ldr	r3, .L129
 1366 0014 DB68     		ldr	r3, [r3, #12]
 1367              		.loc 1 1692 49 view .LVU303
 1368 0016 13F0806F 		tst	r3, #67108864
 1369 001a 07D1     		bne	.L128
1693:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   {
1694:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     if ((HAL_GetTick() - tickstart ) > PWR_FLAG_SETTING_DELAY)
 1370              		.loc 1 1694 5 is_stmt 1 view .LVU304
 1371              		.loc 1 1694 10 is_stmt 0 view .LVU305
 1372 001c FFF7FEFF 		bl	HAL_GetTick
 1373              	.LVL70:
 1374              		.loc 1 1694 24 discriminator 1 view .LVU306
 1375 0020 001B     		subs	r0, r0, r4
 1376              		.loc 1 1694 8 discriminator 1 view .LVU307
 1377 0022 B0F57A7F 		cmp	r0, #1000
 1378 0026 F4D9     		bls	.L123
1695:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     {
1696:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       return HAL_ERROR;
 1379              		.loc 1 1696 14 view .LVU308
 1380 0028 0120     		movs	r0, #1
 1381 002a 00E0     		b	.L124
 1382              	.L128:
1697:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     }
1698:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   }
1699:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1700:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   return HAL_OK;
 1383              		.loc 1 1700 10 view .LVU309
 1384 002c 0020     		movs	r0, #0
 1385              	.L124:
1701:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** }
 1386              		.loc 1 1701 1 view .LVU310
 1387 002e 10BD     		pop	{r4, pc}
 1388              	.LVL71:
 1389              	.L130:
 1390              		.loc 1 1701 1 view .LVU311
 1391              		.align	2
 1392              	.L129:
 1393 0030 00480258 		.word	1476544512
 1394              		.cfi_endproc
 1395              	.LFE167:
 1397              		.section	.text.HAL_PWREx_DisableUSBReg,"ax",%progbits
 1398              		.align	1
 1399              		.global	HAL_PWREx_DisableUSBReg
 1400              		.syntax unified
 1401              		.thumb
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 75


 1402              		.thumb_func
 1404              	HAL_PWREx_DisableUSBReg:
 1405              	.LFB168:
1702:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1703:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /**
1704:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @brief Disable the USB Regulator.
1705:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @retval HAL status.
1706:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   */
1707:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** HAL_StatusTypeDef HAL_PWREx_DisableUSBReg (void)
1708:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** {
 1406              		.loc 1 1708 1 is_stmt 1 view -0
 1407              		.cfi_startproc
 1408              		@ args = 0, pretend = 0, frame = 0
 1409              		@ frame_needed = 0, uses_anonymous_args = 0
 1410 0000 10B5     		push	{r4, lr}
 1411              	.LCFI14:
 1412              		.cfi_def_cfa_offset 8
 1413              		.cfi_offset 4, -8
 1414              		.cfi_offset 14, -4
1709:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   uint32_t tickstart;
 1415              		.loc 1 1709 3 view .LVU313
1710:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1711:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Disable the USB regulator */
1712:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   CLEAR_BIT (PWR->CR3, PWR_CR3_USBREGEN);
 1416              		.loc 1 1712 3 view .LVU314
 1417 0002 0B4A     		ldr	r2, .L138
 1418 0004 D368     		ldr	r3, [r2, #12]
 1419 0006 23F00073 		bic	r3, r3, #33554432
 1420 000a D360     		str	r3, [r2, #12]
1713:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1714:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Get tick */
1715:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   tickstart = HAL_GetTick ();
 1421              		.loc 1 1715 3 view .LVU315
 1422              		.loc 1 1715 15 is_stmt 0 view .LVU316
 1423 000c FFF7FEFF 		bl	HAL_GetTick
 1424              	.LVL72:
 1425 0010 0446     		mov	r4, r0
 1426              	.LVL73:
1716:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1717:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Wait till the USB regulator ready flag is reset */
1718:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   while(__HAL_PWR_GET_FLAG (PWR_FLAG_USB33RDY) != 0U)
 1427              		.loc 1 1718 3 is_stmt 1 view .LVU317
 1428              	.L132:
 1429              		.loc 1 1718 48 view .LVU318
 1430              		.loc 1 1718 9 is_stmt 0 view .LVU319
 1431 0012 074B     		ldr	r3, .L138
 1432 0014 DB68     		ldr	r3, [r3, #12]
 1433              		.loc 1 1718 48 view .LVU320
 1434 0016 13F0806F 		tst	r3, #67108864
 1435 001a 07D0     		beq	.L137
1719:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   {
1720:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     if ((HAL_GetTick() - tickstart ) > PWR_FLAG_SETTING_DELAY)
 1436              		.loc 1 1720 5 is_stmt 1 view .LVU321
 1437              		.loc 1 1720 10 is_stmt 0 view .LVU322
 1438 001c FFF7FEFF 		bl	HAL_GetTick
 1439              	.LVL74:
 1440              		.loc 1 1720 24 discriminator 1 view .LVU323
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 76


 1441 0020 001B     		subs	r0, r0, r4
 1442              		.loc 1 1720 8 discriminator 1 view .LVU324
 1443 0022 B0F57A7F 		cmp	r0, #1000
 1444 0026 F4D9     		bls	.L132
1721:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     {
1722:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       return HAL_ERROR;
 1445              		.loc 1 1722 14 view .LVU325
 1446 0028 0120     		movs	r0, #1
 1447 002a 00E0     		b	.L133
 1448              	.L137:
1723:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     }
1724:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   }
1725:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1726:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   return HAL_OK;
 1449              		.loc 1 1726 10 view .LVU326
 1450 002c 0020     		movs	r0, #0
 1451              	.L133:
1727:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** }
 1452              		.loc 1 1727 1 view .LVU327
 1453 002e 10BD     		pop	{r4, pc}
 1454              	.LVL75:
 1455              	.L139:
 1456              		.loc 1 1727 1 view .LVU328
 1457              		.align	2
 1458              	.L138:
 1459 0030 00480258 		.word	1476544512
 1460              		.cfi_endproc
 1461              	.LFE168:
 1463              		.section	.text.HAL_PWREx_EnableUSBVoltageDetector,"ax",%progbits
 1464              		.align	1
 1465              		.global	HAL_PWREx_EnableUSBVoltageDetector
 1466              		.syntax unified
 1467              		.thumb
 1468              		.thumb_func
 1470              	HAL_PWREx_EnableUSBVoltageDetector:
 1471              	.LFB169:
1728:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1729:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /**
1730:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @brief Enable the USB voltage level detector.
1731:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @retval None.
1732:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   */
1733:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** void HAL_PWREx_EnableUSBVoltageDetector (void)
1734:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** {
 1472              		.loc 1 1734 1 is_stmt 1 view -0
 1473              		.cfi_startproc
 1474              		@ args = 0, pretend = 0, frame = 0
 1475              		@ frame_needed = 0, uses_anonymous_args = 0
 1476              		@ link register save eliminated.
1735:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Enable the USB voltage detector */
1736:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   SET_BIT (PWR->CR3, PWR_CR3_USB33DEN);
 1477              		.loc 1 1736 3 view .LVU330
 1478 0000 024A     		ldr	r2, .L141
 1479 0002 D368     		ldr	r3, [r2, #12]
 1480 0004 43F08073 		orr	r3, r3, #16777216
 1481 0008 D360     		str	r3, [r2, #12]
1737:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** }
 1482              		.loc 1 1737 1 is_stmt 0 view .LVU331
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 77


 1483 000a 7047     		bx	lr
 1484              	.L142:
 1485              		.align	2
 1486              	.L141:
 1487 000c 00480258 		.word	1476544512
 1488              		.cfi_endproc
 1489              	.LFE169:
 1491              		.section	.text.HAL_PWREx_DisableUSBVoltageDetector,"ax",%progbits
 1492              		.align	1
 1493              		.global	HAL_PWREx_DisableUSBVoltageDetector
 1494              		.syntax unified
 1495              		.thumb
 1496              		.thumb_func
 1498              	HAL_PWREx_DisableUSBVoltageDetector:
 1499              	.LFB170:
1738:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1739:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /**
1740:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @brief Disable the USB voltage level detector.
1741:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @retval None.
1742:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   */
1743:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** void HAL_PWREx_DisableUSBVoltageDetector (void)
1744:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** {
 1500              		.loc 1 1744 1 is_stmt 1 view -0
 1501              		.cfi_startproc
 1502              		@ args = 0, pretend = 0, frame = 0
 1503              		@ frame_needed = 0, uses_anonymous_args = 0
 1504              		@ link register save eliminated.
1745:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Disable the USB voltage detector */
1746:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   CLEAR_BIT (PWR->CR3, PWR_CR3_USB33DEN);
 1505              		.loc 1 1746 3 view .LVU333
 1506 0000 024A     		ldr	r2, .L144
 1507 0002 D368     		ldr	r3, [r2, #12]
 1508 0004 23F08073 		bic	r3, r3, #16777216
 1509 0008 D360     		str	r3, [r2, #12]
1747:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** }
 1510              		.loc 1 1747 1 is_stmt 0 view .LVU334
 1511 000a 7047     		bx	lr
 1512              	.L145:
 1513              		.align	2
 1514              	.L144:
 1515 000c 00480258 		.word	1476544512
 1516              		.cfi_endproc
 1517              	.LFE170:
 1519              		.section	.text.HAL_PWREx_EnableBatteryCharging,"ax",%progbits
 1520              		.align	1
 1521              		.global	HAL_PWREx_EnableBatteryCharging
 1522              		.syntax unified
 1523              		.thumb
 1524              		.thumb_func
 1526              	HAL_PWREx_EnableBatteryCharging:
 1527              	.LVL76:
 1528              	.LFB171:
1748:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1749:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /**
1750:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @brief Enable the Battery charging.
1751:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @note   When VDD is present, charge the external battery through an internal
1752:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         resistor.
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 78


1753:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @param  ResistorValue : Specifies the charging resistor.
1754:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *          This parameter can be one of the following values :
1755:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *            @arg PWR_BATTERY_CHARGING_RESISTOR_5 : 5 KOhm resistor.
1756:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *            @arg PWR_BATTERY_CHARGING_RESISTOR_1_5 : 1.5 KOhm resistor.
1757:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @retval None.
1758:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   */
1759:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** void HAL_PWREx_EnableBatteryCharging (uint32_t ResistorValue)
1760:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** {
 1529              		.loc 1 1760 1 is_stmt 1 view -0
 1530              		.cfi_startproc
 1531              		@ args = 0, pretend = 0, frame = 0
 1532              		@ frame_needed = 0, uses_anonymous_args = 0
 1533              		@ link register save eliminated.
1761:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Check the parameter */
1762:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   assert_param (IS_PWR_BATTERY_RESISTOR_SELECT (ResistorValue));
 1534              		.loc 1 1762 3 view .LVU336
1763:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1764:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Specify the charging resistor */
1765:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   MODIFY_REG (PWR->CR3, PWR_CR3_VBRS, ResistorValue);
 1535              		.loc 1 1765 3 view .LVU337
 1536 0000 054A     		ldr	r2, .L147
 1537 0002 D368     		ldr	r3, [r2, #12]
 1538 0004 23F40073 		bic	r3, r3, #512
 1539 0008 0343     		orrs	r3, r3, r0
 1540 000a D360     		str	r3, [r2, #12]
1766:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1767:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Enable the Battery charging */
1768:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   SET_BIT (PWR->CR3, PWR_CR3_VBE);
 1541              		.loc 1 1768 3 view .LVU338
 1542 000c D368     		ldr	r3, [r2, #12]
 1543 000e 43F48073 		orr	r3, r3, #256
 1544 0012 D360     		str	r3, [r2, #12]
1769:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** }
 1545              		.loc 1 1769 1 is_stmt 0 view .LVU339
 1546 0014 7047     		bx	lr
 1547              	.L148:
 1548 0016 00BF     		.align	2
 1549              	.L147:
 1550 0018 00480258 		.word	1476544512
 1551              		.cfi_endproc
 1552              	.LFE171:
 1554              		.section	.text.HAL_PWREx_DisableBatteryCharging,"ax",%progbits
 1555              		.align	1
 1556              		.global	HAL_PWREx_DisableBatteryCharging
 1557              		.syntax unified
 1558              		.thumb
 1559              		.thumb_func
 1561              	HAL_PWREx_DisableBatteryCharging:
 1562              	.LFB172:
1770:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1771:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /**
1772:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @brief Disable the Battery charging.
1773:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @retval None.
1774:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   */
1775:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** void HAL_PWREx_DisableBatteryCharging (void)
1776:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** {
 1563              		.loc 1 1776 1 is_stmt 1 view -0
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 79


 1564              		.cfi_startproc
 1565              		@ args = 0, pretend = 0, frame = 0
 1566              		@ frame_needed = 0, uses_anonymous_args = 0
 1567              		@ link register save eliminated.
1777:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Disable the Battery charging */
1778:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   CLEAR_BIT (PWR->CR3, PWR_CR3_VBE);
 1568              		.loc 1 1778 3 view .LVU341
 1569 0000 024A     		ldr	r2, .L150
 1570 0002 D368     		ldr	r3, [r2, #12]
 1571 0004 23F48073 		bic	r3, r3, #256
 1572 0008 D360     		str	r3, [r2, #12]
1779:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** }
 1573              		.loc 1 1779 1 is_stmt 0 view .LVU342
 1574 000a 7047     		bx	lr
 1575              	.L151:
 1576              		.align	2
 1577              	.L150:
 1578 000c 00480258 		.word	1476544512
 1579              		.cfi_endproc
 1580              	.LFE172:
 1582              		.section	.text.HAL_PWREx_EnableMonitoring,"ax",%progbits
 1583              		.align	1
 1584              		.global	HAL_PWREx_EnableMonitoring
 1585              		.syntax unified
 1586              		.thumb
 1587              		.thumb_func
 1589              	HAL_PWREx_EnableMonitoring:
 1590              	.LFB173:
1780:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1781:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #if defined (PWR_CR1_BOOSTE)
1782:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /**
1783:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @brief Enable the booster to guarantee the analog switch AC performance when
1784:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *        the VDD supply voltage is below 2V7.
1785:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @note   The VDD supply voltage can be monitored through the PVD and the PLS
1786:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         field bits.
1787:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @retval None.
1788:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   */
1789:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** void HAL_PWREx_EnableAnalogBooster (void)
1790:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** {
1791:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Enable the Analog voltage */
1792:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   SET_BIT (PWR->CR1, PWR_CR1_AVD_READY);
1793:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1794:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Enable VDDA booster */
1795:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   SET_BIT (PWR->CR1, PWR_CR1_BOOSTE);
1796:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** }
1797:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1798:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /**
1799:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @brief Disable the analog booster.
1800:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @retval None.
1801:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   */
1802:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** void HAL_PWREx_DisableAnalogBooster (void)
1803:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** {
1804:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Disable VDDA booster */
1805:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   CLEAR_BIT (PWR->CR1, PWR_CR1_BOOSTE);
1806:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1807:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Disable the Analog voltage */
1808:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   CLEAR_BIT (PWR->CR1, PWR_CR1_AVD_READY);
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 80


1809:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** }
1810:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #endif /* defined (PWR_CR1_BOOSTE) */
1811:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /**
1812:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @}
1813:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   */
1814:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1815:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /** @defgroup PWREx_Exported_Functions_Group4 Power Monitoring functions
1816:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @brief    Power Monitoring functions
1817:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *
1818:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** @verbatim
1819:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****  ===============================================================================
1820:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****                  ##### Power Monitoring functions #####
1821:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****  ===============================================================================
1822:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1823:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     *** VBAT and Temperature supervision ***
1824:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     ========================================
1825:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     [..]
1826:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       (+) The VBAT battery voltage supply can be monitored by comparing it with
1827:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           two threshold levels: VBAThigh and VBATlow. VBATH flag and VBATL flags
1828:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           in the PWR control register 2 (PWR_CR2), indicate if VBAT is higher or
1829:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           lower than the threshold.
1830:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       (+) The temperature can be monitored by comparing it with two threshold
1831:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           levels, TEMPhigh and TEMPlow. TEMPH and TEMPL flags, in the PWR
1832:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           control register 2 (PWR_CR2), indicate whether the device temperature
1833:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           is higher or lower than the threshold.
1834:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       (+) The VBAT and the temperature monitoring is enabled by
1835:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           HAL_PWREx_EnableMonitoring() function and disabled by
1836:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           HAL_PWREx_DisableMonitoring() function.
1837:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       (+) The HAL_PWREx_GetVBATLevel() function returns the VBAT level which can
1838:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           be : PWR_VBAT_BELOW_LOW_THRESHOLD or PWR_VBAT_ABOVE_HIGH_THRESHOLD or
1839:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           PWR_VBAT_BETWEEN_HIGH_LOW_THRESHOLD.
1840:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       (+) The HAL_PWREx_GetTemperatureLevel() function returns the Temperature
1841:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           level which can be :
1842:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           PWR_TEMP_BELOW_LOW_THRESHOLD or PWR_TEMP_ABOVE_HIGH_THRESHOLD or
1843:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           PWR_TEMP_BETWEEN_HIGH_LOW_THRESHOLD.
1844:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1845:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     *** AVD configuration ***
1846:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     =========================
1847:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     [..]
1848:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       (+) The AVD is used to monitor the VDDA power supply by comparing it to a
1849:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           threshold selected by the AVD Level (ALS[3:0] bits in the PWR_CR1
1850:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           register).
1851:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       (+) A AVDO flag is available to indicate if VDDA is higher or lower
1852:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           than the AVD threshold. This event is internally connected to the EXTI
1853:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           line 16 to generate an interrupt if enabled.
1854:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           It is configurable through __HAL_PWR_AVD_EXTI_ENABLE_IT() macro.
1855:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       (+) The AVD is stopped in System Standby mode.
1856:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1857:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** @endverbatim
1858:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @{
1859:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   */
1860:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1861:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /**
1862:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @brief Enable the VBAT and temperature monitoring.
1863:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @retval HAL status.
1864:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   */
1865:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** void HAL_PWREx_EnableMonitoring (void)
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 81


1866:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** {
 1591              		.loc 1 1866 1 is_stmt 1 view -0
 1592              		.cfi_startproc
 1593              		@ args = 0, pretend = 0, frame = 0
 1594              		@ frame_needed = 0, uses_anonymous_args = 0
 1595              		@ link register save eliminated.
1867:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Enable the VBAT and Temperature monitoring */
1868:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   SET_BIT (PWR->CR2, PWR_CR2_MONEN);
 1596              		.loc 1 1868 3 view .LVU344
 1597 0000 024A     		ldr	r2, .L153
 1598 0002 9368     		ldr	r3, [r2, #8]
 1599 0004 43F01003 		orr	r3, r3, #16
 1600 0008 9360     		str	r3, [r2, #8]
1869:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** }
 1601              		.loc 1 1869 1 is_stmt 0 view .LVU345
 1602 000a 7047     		bx	lr
 1603              	.L154:
 1604              		.align	2
 1605              	.L153:
 1606 000c 00480258 		.word	1476544512
 1607              		.cfi_endproc
 1608              	.LFE173:
 1610              		.section	.text.HAL_PWREx_DisableMonitoring,"ax",%progbits
 1611              		.align	1
 1612              		.global	HAL_PWREx_DisableMonitoring
 1613              		.syntax unified
 1614              		.thumb
 1615              		.thumb_func
 1617              	HAL_PWREx_DisableMonitoring:
 1618              	.LFB174:
1870:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1871:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /**
1872:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @brief Disable the VBAT and temperature monitoring.
1873:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @retval HAL status.
1874:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   */
1875:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** void HAL_PWREx_DisableMonitoring (void)
1876:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** {
 1619              		.loc 1 1876 1 is_stmt 1 view -0
 1620              		.cfi_startproc
 1621              		@ args = 0, pretend = 0, frame = 0
 1622              		@ frame_needed = 0, uses_anonymous_args = 0
 1623              		@ link register save eliminated.
1877:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Disable the VBAT and Temperature monitoring */
1878:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   CLEAR_BIT (PWR->CR2, PWR_CR2_MONEN);
 1624              		.loc 1 1878 3 view .LVU347
 1625 0000 024A     		ldr	r2, .L156
 1626 0002 9368     		ldr	r3, [r2, #8]
 1627 0004 23F01003 		bic	r3, r3, #16
 1628 0008 9360     		str	r3, [r2, #8]
1879:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** }
 1629              		.loc 1 1879 1 is_stmt 0 view .LVU348
 1630 000a 7047     		bx	lr
 1631              	.L157:
 1632              		.align	2
 1633              	.L156:
 1634 000c 00480258 		.word	1476544512
 1635              		.cfi_endproc
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 82


 1636              	.LFE174:
 1638              		.section	.text.HAL_PWREx_GetTemperatureLevel,"ax",%progbits
 1639              		.align	1
 1640              		.global	HAL_PWREx_GetTemperatureLevel
 1641              		.syntax unified
 1642              		.thumb
 1643              		.thumb_func
 1645              	HAL_PWREx_GetTemperatureLevel:
 1646              	.LFB175:
1880:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1881:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /**
1882:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @brief Indicate whether the junction temperature is between, above or below
1883:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *        the thresholds.
1884:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @retval Temperature level.
1885:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   */
1886:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** uint32_t HAL_PWREx_GetTemperatureLevel (void)
1887:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** {
 1647              		.loc 1 1887 1 is_stmt 1 view -0
 1648              		.cfi_startproc
 1649              		@ args = 0, pretend = 0, frame = 0
 1650              		@ frame_needed = 0, uses_anonymous_args = 0
 1651              		@ link register save eliminated.
1888:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   uint32_t tempLevel, regValue;
 1652              		.loc 1 1888 3 view .LVU350
1889:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1890:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Read the temperature flags */
1891:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   regValue = READ_BIT (PWR->CR2, (PWR_CR2_TEMPH | PWR_CR2_TEMPL));
 1653              		.loc 1 1891 3 view .LVU351
 1654              		.loc 1 1891 14 is_stmt 0 view .LVU352
 1655 0000 054B     		ldr	r3, .L161
 1656 0002 9868     		ldr	r0, [r3, #8]
 1657              		.loc 1 1891 12 view .LVU353
 1658 0004 00F44000 		and	r0, r0, #12582912
 1659              	.LVL77:
1892:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1893:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Check if the temperature is below the threshold */
1894:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   if (regValue == PWR_CR2_TEMPL)
 1660              		.loc 1 1894 3 is_stmt 1 view .LVU354
 1661              		.loc 1 1894 6 is_stmt 0 view .LVU355
 1662 0008 B0F5800F 		cmp	r0, #4194304
 1663 000c 03D0     		beq	.L158
1895:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   {
1896:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     tempLevel = PWR_TEMP_BELOW_LOW_THRESHOLD;
1897:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   }
1898:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Check if the temperature is above the threshold */
1899:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   else if (regValue == PWR_CR2_TEMPH)
 1664              		.loc 1 1899 8 is_stmt 1 view .LVU356
 1665              		.loc 1 1899 11 is_stmt 0 view .LVU357
 1666 000e B0F5000F 		cmp	r0, #8388608
 1667 0012 00D0     		beq	.L158
1900:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   {
1901:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     tempLevel = PWR_TEMP_ABOVE_HIGH_THRESHOLD;
1902:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   }
1903:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* The temperature is between the thresholds */
1904:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   else
1905:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   {
1906:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     tempLevel = PWR_TEMP_BETWEEN_HIGH_LOW_THRESHOLD;
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 83


 1668              		.loc 1 1906 15 view .LVU358
 1669 0014 0020     		movs	r0, #0
 1670              	.LVL78:
1907:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   }
1908:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1909:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   return tempLevel;
 1671              		.loc 1 1909 3 is_stmt 1 view .LVU359
 1672              	.L158:
1910:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** }
 1673              		.loc 1 1910 1 is_stmt 0 view .LVU360
 1674 0016 7047     		bx	lr
 1675              	.L162:
 1676              		.align	2
 1677              	.L161:
 1678 0018 00480258 		.word	1476544512
 1679              		.cfi_endproc
 1680              	.LFE175:
 1682              		.section	.text.HAL_PWREx_GetVBATLevel,"ax",%progbits
 1683              		.align	1
 1684              		.global	HAL_PWREx_GetVBATLevel
 1685              		.syntax unified
 1686              		.thumb
 1687              		.thumb_func
 1689              	HAL_PWREx_GetVBATLevel:
 1690              	.LFB176:
1911:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1912:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /**
1913:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @brief Indicate whether the Battery voltage level is between, above or below
1914:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *        the thresholds.
1915:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @retval VBAT level.
1916:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   */
1917:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** uint32_t HAL_PWREx_GetVBATLevel (void)
1918:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** {
 1691              		.loc 1 1918 1 is_stmt 1 view -0
 1692              		.cfi_startproc
 1693              		@ args = 0, pretend = 0, frame = 0
 1694              		@ frame_needed = 0, uses_anonymous_args = 0
 1695              		@ link register save eliminated.
1919:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   uint32_t VBATLevel, regValue;
 1696              		.loc 1 1919 3 view .LVU362
1920:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1921:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Read the VBAT flags */
1922:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   regValue = READ_BIT (PWR->CR2, (PWR_CR2_VBATH | PWR_CR2_VBATL));
 1697              		.loc 1 1922 3 view .LVU363
 1698              		.loc 1 1922 14 is_stmt 0 view .LVU364
 1699 0000 054B     		ldr	r3, .L166
 1700 0002 9868     		ldr	r0, [r3, #8]
 1701              		.loc 1 1922 12 view .LVU365
 1702 0004 00F44010 		and	r0, r0, #3145728
 1703              	.LVL79:
1923:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1924:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Check if the VBAT is below the threshold */
1925:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   if (regValue == PWR_CR2_VBATL)
 1704              		.loc 1 1925 3 is_stmt 1 view .LVU366
 1705              		.loc 1 1925 6 is_stmt 0 view .LVU367
 1706 0008 B0F5801F 		cmp	r0, #1048576
 1707 000c 03D0     		beq	.L163
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 84


1926:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   {
1927:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     VBATLevel = PWR_VBAT_BELOW_LOW_THRESHOLD;
1928:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   }
1929:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Check if the VBAT is above the threshold */
1930:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   else if (regValue == PWR_CR2_VBATH)
 1708              		.loc 1 1930 8 is_stmt 1 view .LVU368
 1709              		.loc 1 1930 11 is_stmt 0 view .LVU369
 1710 000e B0F5001F 		cmp	r0, #2097152
 1711 0012 00D0     		beq	.L163
1931:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   {
1932:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     VBATLevel = PWR_VBAT_ABOVE_HIGH_THRESHOLD;
1933:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   }
1934:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* The VBAT is between the thresholds */
1935:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   else
1936:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   {
1937:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     VBATLevel = PWR_VBAT_BETWEEN_HIGH_LOW_THRESHOLD;
 1712              		.loc 1 1937 15 view .LVU370
 1713 0014 0020     		movs	r0, #0
 1714              	.LVL80:
1938:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   }
1939:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1940:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   return VBATLevel;
 1715              		.loc 1 1940 3 is_stmt 1 view .LVU371
 1716              	.L163:
1941:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** }
 1717              		.loc 1 1941 1 is_stmt 0 view .LVU372
 1718 0016 7047     		bx	lr
 1719              	.L167:
 1720              		.align	2
 1721              	.L166:
 1722 0018 00480258 		.word	1476544512
 1723              		.cfi_endproc
 1724              	.LFE176:
 1726              		.section	.text.HAL_PWREx_ConfigAVD,"ax",%progbits
 1727              		.align	1
 1728              		.global	HAL_PWREx_ConfigAVD
 1729              		.syntax unified
 1730              		.thumb
 1731              		.thumb_func
 1733              	HAL_PWREx_ConfigAVD:
 1734              	.LVL81:
 1735              	.LFB177:
1942:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1943:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #if defined (PWR_CSR1_MMCVDO)
1944:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /**
1945:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @brief Get the VDDMMC voltage level.
1946:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @retval The VDDMMC voltage level.
1947:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   */
1948:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** PWREx_MMC_VoltageLevel HAL_PWREx_GetMMCVoltage (void)
1949:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** {
1950:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   PWREx_MMC_VoltageLevel mmc_voltage;
1951:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1952:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Check voltage detector output on VDDMMC value */
1953:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   if ((PWR->CSR1 & PWR_CSR1_MMCVDO_Msk) == 0U)
1954:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   {
1955:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     mmc_voltage = PWR_MMC_VOLTAGE_BELOW_1V2;
1956:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   }
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 85


1957:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   else
1958:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   {
1959:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     mmc_voltage = PWR_MMC_VOLTAGE_EQUAL_ABOVE_1V2;
1960:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   }
1961:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1962:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   return mmc_voltage;
1963:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** }
1964:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #endif /* defined (PWR_CSR1_MMCVDO) */
1965:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1966:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /**
1967:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @brief  Configure the event mode and the voltage threshold detected by the
1968:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         Analog Voltage Detector (AVD).
1969:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @param  sConfigAVD : Pointer to an PWREx_AVDTypeDef structure that contains
1970:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *                      the configuration information for the AVD.
1971:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @note   Refer to the electrical characteristics of your device datasheet for
1972:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         more details about the voltage threshold corresponding to each
1973:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         detection level.
1974:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @note   For dual core devices, please ensure to configure the EXTI lines for
1975:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         the different Cortex-Mx through PWR_Exported_Macro provided by this
1976:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         driver. All combination are allowed: wake up only Cortex-M7, wake up
1977:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   *         only Cortex-M4 and wake up Cortex-M7 and Cortex-M4.
1978:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @retval None.
1979:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   */
1980:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** void HAL_PWREx_ConfigAVD (const PWREx_AVDTypeDef *sConfigAVD)
1981:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** {
 1736              		.loc 1 1981 1 is_stmt 1 view -0
 1737              		.cfi_startproc
 1738              		@ args = 0, pretend = 0, frame = 0
 1739              		@ frame_needed = 0, uses_anonymous_args = 0
 1740              		@ link register save eliminated.
1982:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Check the parameters */
1983:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   assert_param (IS_PWR_AVD_LEVEL (sConfigAVD->AVDLevel));
 1741              		.loc 1 1983 3 view .LVU374
1984:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   assert_param (IS_PWR_AVD_MODE (sConfigAVD->Mode));
 1742              		.loc 1 1984 3 view .LVU375
1985:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1986:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Set the ALS[18:17] bits according to AVDLevel value */
1987:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   MODIFY_REG (PWR->CR1, PWR_CR1_ALS, sConfigAVD->AVDLevel);
 1743              		.loc 1 1987 3 view .LVU376
 1744 0000 244A     		ldr	r2, .L173
 1745 0002 1368     		ldr	r3, [r2]
 1746 0004 23F4C023 		bic	r3, r3, #393216
 1747 0008 0168     		ldr	r1, [r0]
 1748 000a 0B43     		orrs	r3, r3, r1
 1749 000c 1360     		str	r3, [r2]
1988:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1989:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Clear any previous config */
1990:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #if !defined (DUAL_CORE)
1991:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   __HAL_PWR_AVD_EXTI_DISABLE_EVENT ();
 1750              		.loc 1 1991 3 view .LVU377
 1751 000e 4FF0B043 		mov	r3, #1476395008
 1752 0012 D3F88420 		ldr	r2, [r3, #132]
 1753 0016 22F48032 		bic	r2, r2, #65536
 1754 001a C3F88420 		str	r2, [r3, #132]
1992:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   __HAL_PWR_AVD_EXTI_DISABLE_IT ();
 1755              		.loc 1 1992 3 view .LVU378
 1756 001e D3F88020 		ldr	r2, [r3, #128]
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 86


 1757 0022 22F48032 		bic	r2, r2, #65536
 1758 0026 C3F88020 		str	r2, [r3, #128]
1993:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #endif /* !defined (DUAL_CORE) */
1994:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1995:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   __HAL_PWR_AVD_EXTI_DISABLE_RISING_EDGE ();
 1759              		.loc 1 1995 3 view .LVU379
 1760 002a 1A68     		ldr	r2, [r3]
 1761 002c 22F48032 		bic	r2, r2, #65536
 1762 0030 1A60     		str	r2, [r3]
1996:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   __HAL_PWR_AVD_EXTI_DISABLE_FALLING_EDGE ();
 1763              		.loc 1 1996 3 view .LVU380
 1764 0032 5A68     		ldr	r2, [r3, #4]
 1765 0034 22F48032 		bic	r2, r2, #65536
 1766 0038 5A60     		str	r2, [r3, #4]
1997:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
1998:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #if !defined (DUAL_CORE)
1999:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Configure the interrupt mode */
2000:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   if ((sConfigAVD->Mode & AVD_MODE_IT) == AVD_MODE_IT)
 1767              		.loc 1 2000 3 view .LVU381
 1768              		.loc 1 2000 18 is_stmt 0 view .LVU382
 1769 003a 4368     		ldr	r3, [r0, #4]
 1770              		.loc 1 2000 6 view .LVU383
 1771 003c 13F4803F 		tst	r3, #65536
 1772 0040 07D0     		beq	.L169
2001:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   {
2002:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     __HAL_PWR_AVD_EXTI_ENABLE_IT ();
 1773              		.loc 1 2002 5 is_stmt 1 view .LVU384
 1774 0042 4FF0B042 		mov	r2, #1476395008
 1775 0046 D2F88030 		ldr	r3, [r2, #128]
 1776 004a 43F48033 		orr	r3, r3, #65536
 1777 004e C2F88030 		str	r3, [r2, #128]
 1778              	.L169:
2003:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   }
2004:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
2005:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Configure the event mode */
2006:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   if ((sConfigAVD->Mode & AVD_MODE_EVT) == AVD_MODE_EVT)
 1779              		.loc 1 2006 3 view .LVU385
 1780              		.loc 1 2006 18 is_stmt 0 view .LVU386
 1781 0052 4368     		ldr	r3, [r0, #4]
 1782              		.loc 1 2006 6 view .LVU387
 1783 0054 13F4003F 		tst	r3, #131072
 1784 0058 07D0     		beq	.L170
2007:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   {
2008:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     __HAL_PWR_AVD_EXTI_ENABLE_EVENT ();
 1785              		.loc 1 2008 5 is_stmt 1 view .LVU388
 1786 005a 4FF0B042 		mov	r2, #1476395008
 1787 005e D2F88430 		ldr	r3, [r2, #132]
 1788 0062 43F48033 		orr	r3, r3, #65536
 1789 0066 C2F88430 		str	r3, [r2, #132]
 1790              	.L170:
2009:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   }
2010:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #endif /* !defined (DUAL_CORE) */
2011:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
2012:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Rising edge configuration */
2013:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   if ((sConfigAVD->Mode & AVD_RISING_EDGE) == AVD_RISING_EDGE)
 1791              		.loc 1 2013 3 view .LVU389
 1792              		.loc 1 2013 18 is_stmt 0 view .LVU390
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 87


 1793 006a 4368     		ldr	r3, [r0, #4]
 1794              		.loc 1 2013 6 view .LVU391
 1795 006c 13F0010F 		tst	r3, #1
 1796 0070 05D0     		beq	.L171
2014:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   {
2015:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     __HAL_PWR_AVD_EXTI_ENABLE_RISING_EDGE ();
 1797              		.loc 1 2015 5 is_stmt 1 view .LVU392
 1798 0072 4FF0B042 		mov	r2, #1476395008
 1799 0076 1368     		ldr	r3, [r2]
 1800 0078 43F48033 		orr	r3, r3, #65536
 1801 007c 1360     		str	r3, [r2]
 1802              	.L171:
2016:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   }
2017:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
2018:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Falling edge configuration */
2019:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   if ((sConfigAVD->Mode & AVD_FALLING_EDGE) == AVD_FALLING_EDGE)
 1803              		.loc 1 2019 3 view .LVU393
 1804              		.loc 1 2019 18 is_stmt 0 view .LVU394
 1805 007e 4368     		ldr	r3, [r0, #4]
 1806              		.loc 1 2019 6 view .LVU395
 1807 0080 13F0020F 		tst	r3, #2
 1808 0084 05D0     		beq	.L168
2020:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   {
2021:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     __HAL_PWR_AVD_EXTI_ENABLE_FALLING_EDGE ();
 1809              		.loc 1 2021 5 is_stmt 1 view .LVU396
 1810 0086 4FF0B042 		mov	r2, #1476395008
 1811 008a 5368     		ldr	r3, [r2, #4]
 1812 008c 43F48033 		orr	r3, r3, #65536
 1813 0090 5360     		str	r3, [r2, #4]
 1814              	.L168:
2022:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   }
2023:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** }
 1815              		.loc 1 2023 1 is_stmt 0 view .LVU397
 1816 0092 7047     		bx	lr
 1817              	.L174:
 1818              		.align	2
 1819              	.L173:
 1820 0094 00480258 		.word	1476544512
 1821              		.cfi_endproc
 1822              	.LFE177:
 1824              		.section	.text.HAL_PWREx_EnableAVD,"ax",%progbits
 1825              		.align	1
 1826              		.global	HAL_PWREx_EnableAVD
 1827              		.syntax unified
 1828              		.thumb
 1829              		.thumb_func
 1831              	HAL_PWREx_EnableAVD:
 1832              	.LFB178:
2024:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
2025:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /**
2026:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @brief Enable the Analog Voltage Detector (AVD).
2027:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @retval None.
2028:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   */
2029:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** void HAL_PWREx_EnableAVD (void)
2030:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** {
 1833              		.loc 1 2030 1 is_stmt 1 view -0
 1834              		.cfi_startproc
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 88


 1835              		@ args = 0, pretend = 0, frame = 0
 1836              		@ frame_needed = 0, uses_anonymous_args = 0
 1837              		@ link register save eliminated.
2031:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Enable the Analog Voltage Detector */
2032:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   SET_BIT (PWR->CR1, PWR_CR1_AVDEN);
 1838              		.loc 1 2032 3 view .LVU399
 1839 0000 024A     		ldr	r2, .L176
 1840 0002 1368     		ldr	r3, [r2]
 1841 0004 43F48033 		orr	r3, r3, #65536
 1842 0008 1360     		str	r3, [r2]
2033:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** }
 1843              		.loc 1 2033 1 is_stmt 0 view .LVU400
 1844 000a 7047     		bx	lr
 1845              	.L177:
 1846              		.align	2
 1847              	.L176:
 1848 000c 00480258 		.word	1476544512
 1849              		.cfi_endproc
 1850              	.LFE178:
 1852              		.section	.text.HAL_PWREx_DisableAVD,"ax",%progbits
 1853              		.align	1
 1854              		.global	HAL_PWREx_DisableAVD
 1855              		.syntax unified
 1856              		.thumb
 1857              		.thumb_func
 1859              	HAL_PWREx_DisableAVD:
 1860              	.LFB179:
2034:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
2035:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /**
2036:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @brief Disable the Analog Voltage Detector(AVD).
2037:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @retval None.
2038:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   */
2039:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** void HAL_PWREx_DisableAVD (void)
2040:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** {
 1861              		.loc 1 2040 1 is_stmt 1 view -0
 1862              		.cfi_startproc
 1863              		@ args = 0, pretend = 0, frame = 0
 1864              		@ frame_needed = 0, uses_anonymous_args = 0
 1865              		@ link register save eliminated.
2041:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Disable the Analog Voltage Detector */
2042:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   CLEAR_BIT (PWR->CR1, PWR_CR1_AVDEN);
 1866              		.loc 1 2042 3 view .LVU402
 1867 0000 024A     		ldr	r2, .L179
 1868 0002 1368     		ldr	r3, [r2]
 1869 0004 23F48033 		bic	r3, r3, #65536
 1870 0008 1360     		str	r3, [r2]
2043:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** }
 1871              		.loc 1 2043 1 is_stmt 0 view .LVU403
 1872 000a 7047     		bx	lr
 1873              	.L180:
 1874              		.align	2
 1875              	.L179:
 1876 000c 00480258 		.word	1476544512
 1877              		.cfi_endproc
 1878              	.LFE179:
 1880              		.section	.text.HAL_PWREx_AVDCallback,"ax",%progbits
 1881              		.align	1
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 89


 1882              		.weak	HAL_PWREx_AVDCallback
 1883              		.syntax unified
 1884              		.thumb
 1885              		.thumb_func
 1887              	HAL_PWREx_AVDCallback:
 1888              	.LFB181:
2044:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
2045:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /**
2046:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @brief  This function handles the PWR PVD/AVD interrupt request.
2047:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @note   This API should be called under the PVD_AVD_IRQHandler().
2048:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @retval None
2049:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   */
2050:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** void HAL_PWREx_PVD_AVD_IRQHandler (void)
2051:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** {
2052:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Check if the Programmable Voltage Detector is enabled (PVD) */
2053:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   if (READ_BIT (PWR->CR1, PWR_CR1_PVDEN) != 0U)
2054:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   {
2055:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #if defined (DUAL_CORE)
2056:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     if (HAL_GetCurrentCPUID () == CM7_CPUID)
2057:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #endif /* defined (DUAL_CORE) */
2058:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     {
2059:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       /* Check PWR D1/CD EXTI flag */
2060:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       if (__HAL_PWR_PVD_EXTI_GET_FLAG () != 0U)
2061:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       {
2062:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****         /* PWR PVD interrupt user callback */
2063:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****         HAL_PWR_PVDCallback ();
2064:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
2065:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****         if(__HAL_PWR_GET_FLAG (PWR_FLAG_AVDO) == 0U)
2066:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****         {
2067:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           /* Clear PWR EXTI D1/CD pending bit */
2068:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           __HAL_PWR_PVD_EXTI_CLEAR_FLAG ();
2069:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****         }
2070:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       }
2071:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     }
2072:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #if defined (DUAL_CORE)
2073:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     else
2074:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     {
2075:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       /* Check PWR EXTI D2 flag */
2076:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       if (__HAL_PWR_PVD_EXTID2_GET_FLAG () != 0U)
2077:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       {
2078:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****         /* PWR PVD interrupt user callback */
2079:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****         HAL_PWR_PVDCallback ();
2080:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
2081:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****         if(__HAL_PWR_GET_FLAG (PWR_FLAG_AVDO) == 0U)
2082:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****         {
2083:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           /* Clear PWR EXTI D2 pending bit */
2084:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           __HAL_PWR_PVD_EXTID2_CLEAR_FLAG ();
2085:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****         }
2086:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       }
2087:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     }
2088:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #endif /* defined (DUAL_CORE) */
2089:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   }
2090:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
2091:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Check if the Analog Voltage Detector is enabled (AVD) */
2092:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   if (READ_BIT (PWR->CR1, PWR_CR1_AVDEN) != 0U)
2093:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   {
2094:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #if defined (DUAL_CORE)
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 90


2095:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     if (HAL_GetCurrentCPUID () == CM7_CPUID)
2096:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #endif /* defined (DUAL_CORE) */
2097:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     {
2098:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       /* Check PWR EXTI D1/CD flag */
2099:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       if (__HAL_PWR_AVD_EXTI_GET_FLAG () != 0U)
2100:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       {
2101:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****         /* PWR AVD interrupt user callback */
2102:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****         HAL_PWREx_AVDCallback ();
2103:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
2104:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****         if(__HAL_PWR_GET_FLAG (PWR_FLAG_PVDO) == 0U)
2105:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****         {
2106:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           /* Clear PWR EXTI D1/CD pending bit */
2107:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           __HAL_PWR_AVD_EXTI_CLEAR_FLAG ();
2108:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****         }
2109:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       }
2110:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     }
2111:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #if defined (DUAL_CORE)
2112:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     else
2113:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     {
2114:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       /* Check PWR EXTI D2 flag */
2115:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       if (__HAL_PWR_AVD_EXTID2_GET_FLAG () != 0U)
2116:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       {
2117:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****         /* PWR AVD interrupt user callback */
2118:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****         HAL_PWREx_AVDCallback ();
2119:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
2120:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****         if(__HAL_PWR_GET_FLAG (PWR_FLAG_PVDO) == 0U)
2121:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****         {
2122:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           /* Clear PWR EXTI D2 pending bit */
2123:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****           __HAL_PWR_AVD_EXTID2_CLEAR_FLAG ();
2124:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****         }
2125:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       }
2126:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****     }
2127:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** #endif /* defined (DUAL_CORE) */
2128:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   }
2129:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** }
2130:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
2131:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** /**
2132:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @brief PWR AVD interrupt callback.
2133:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   * @retval None.
2134:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   */
2135:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** __weak void HAL_PWREx_AVDCallback (void)
2136:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** {
 1889              		.loc 1 2136 1 is_stmt 1 view -0
 1890              		.cfi_startproc
 1891              		@ args = 0, pretend = 0, frame = 0
 1892              		@ frame_needed = 0, uses_anonymous_args = 0
 1893              		@ link register save eliminated.
2137:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* NOTE : This function should not be modified, when the callback is needed,
2138:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****             the HAL_PWR_AVDCallback can be implemented in the user file
2139:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   */
2140:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** }
 1894              		.loc 1 2140 1 view .LVU405
 1895 0000 7047     		bx	lr
 1896              		.cfi_endproc
 1897              	.LFE181:
 1899              		.section	.text.HAL_PWREx_PVD_AVD_IRQHandler,"ax",%progbits
 1900              		.align	1
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 91


 1901              		.global	HAL_PWREx_PVD_AVD_IRQHandler
 1902              		.syntax unified
 1903              		.thumb
 1904              		.thumb_func
 1906              	HAL_PWREx_PVD_AVD_IRQHandler:
 1907              	.LFB180:
2051:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   /* Check if the Programmable Voltage Detector is enabled (PVD) */
 1908              		.loc 1 2051 1 view -0
 1909              		.cfi_startproc
 1910              		@ args = 0, pretend = 0, frame = 0
 1911              		@ frame_needed = 0, uses_anonymous_args = 0
 1912 0000 08B5     		push	{r3, lr}
 1913              	.LCFI15:
 1914              		.cfi_def_cfa_offset 8
 1915              		.cfi_offset 3, -8
 1916              		.cfi_offset 14, -4
2053:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   {
 1917              		.loc 1 2053 3 view .LVU407
2053:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   {
 1918              		.loc 1 2053 7 is_stmt 0 view .LVU408
 1919 0002 1C4B     		ldr	r3, .L188
 1920 0004 1B68     		ldr	r3, [r3]
2053:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   {
 1921              		.loc 1 2053 6 view .LVU409
 1922 0006 13F0100F 		tst	r3, #16
 1923 000a 06D0     		beq	.L183
2060:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       {
 1924              		.loc 1 2060 7 is_stmt 1 view .LVU410
2060:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       {
 1925              		.loc 1 2060 11 is_stmt 0 view .LVU411
 1926 000c 4FF0B043 		mov	r3, #1476395008
 1927 0010 D3F88830 		ldr	r3, [r3, #136]
2060:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       {
 1928              		.loc 1 2060 10 view .LVU412
 1929 0014 13F4803F 		tst	r3, #65536
 1930 0018 0CD1     		bne	.L186
 1931              	.L183:
2092:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   {
 1932              		.loc 1 2092 3 is_stmt 1 view .LVU413
2092:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   {
 1933              		.loc 1 2092 7 is_stmt 0 view .LVU414
 1934 001a 164B     		ldr	r3, .L188
 1935 001c 1B68     		ldr	r3, [r3]
2092:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****   {
 1936              		.loc 1 2092 6 view .LVU415
 1937 001e 13F4803F 		tst	r3, #65536
 1938 0022 06D0     		beq	.L182
2099:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       {
 1939              		.loc 1 2099 7 is_stmt 1 view .LVU416
2099:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       {
 1940              		.loc 1 2099 11 is_stmt 0 view .LVU417
 1941 0024 4FF0B043 		mov	r3, #1476395008
 1942 0028 D3F88830 		ldr	r3, [r3, #136]
2099:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****       {
 1943              		.loc 1 2099 10 view .LVU418
 1944 002c 13F4803F 		tst	r3, #65536
 1945 0030 10D1     		bne	.L187
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 92


 1946              	.L182:
2129:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 1947              		.loc 1 2129 1 view .LVU419
 1948 0032 08BD     		pop	{r3, pc}
 1949              	.L186:
2063:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 1950              		.loc 1 2063 9 is_stmt 1 view .LVU420
 1951 0034 FFF7FEFF 		bl	HAL_PWR_PVDCallback
 1952              	.LVL82:
2065:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****         {
 1953              		.loc 1 2065 9 view .LVU421
2065:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****         {
 1954              		.loc 1 2065 12 is_stmt 0 view .LVU422
 1955 0038 0E4B     		ldr	r3, .L188
 1956 003a 5B68     		ldr	r3, [r3, #4]
2065:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****         {
 1957              		.loc 1 2065 11 view .LVU423
 1958 003c 13F4803F 		tst	r3, #65536
 1959 0040 EBD1     		bne	.L183
2068:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****         }
 1960              		.loc 1 2068 11 is_stmt 1 view .LVU424
 1961 0042 4FF0B042 		mov	r2, #1476395008
 1962 0046 D2F88830 		ldr	r3, [r2, #136]
 1963 004a 43F48033 		orr	r3, r3, #65536
 1964 004e C2F88830 		str	r3, [r2, #136]
 1965 0052 E2E7     		b	.L183
 1966              	.L187:
2102:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 1967              		.loc 1 2102 9 view .LVU425
 1968 0054 FFF7FEFF 		bl	HAL_PWREx_AVDCallback
 1969              	.LVL83:
2104:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****         {
 1970              		.loc 1 2104 9 view .LVU426
2104:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****         {
 1971              		.loc 1 2104 12 is_stmt 0 view .LVU427
 1972 0058 064B     		ldr	r3, .L188
 1973 005a 5B68     		ldr	r3, [r3, #4]
2104:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****         {
 1974              		.loc 1 2104 11 view .LVU428
 1975 005c 13F0100F 		tst	r3, #16
 1976 0060 E7D1     		bne	.L182
2107:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c ****         }
 1977              		.loc 1 2107 11 is_stmt 1 view .LVU429
 1978 0062 4FF0B042 		mov	r2, #1476395008
 1979 0066 D2F88830 		ldr	r3, [r2, #136]
 1980 006a 43F48033 		orr	r3, r3, #65536
 1981 006e C2F88830 		str	r3, [r2, #136]
2129:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c **** 
 1982              		.loc 1 2129 1 is_stmt 0 view .LVU430
 1983 0072 DEE7     		b	.L182
 1984              	.L189:
 1985              		.align	2
 1986              	.L188:
 1987 0074 00480258 		.word	1476544512
 1988              		.cfi_endproc
 1989              	.LFE180:
 1991              		.text
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 93


 1992              	.Letext0:
 1993              		.file 3 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin/../lib/gcc/arm-none-eabi/13.3.1/../
 1994              		.file 4 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin/../lib/gcc/arm-none-eabi/13.3.1/../
 1995              		.file 5 "Drivers/CMSIS/Include/core_cm7.h"
 1996              		.file 6 "Drivers/CMSIS/Device/ST/STM32H7xx/Include/stm32h723xx.h"
 1997              		.file 7 "Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_def.h"
 1998              		.file 8 "Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_pwr_ex.h"
 1999              		.file 9 "Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_pwr.h"
 2000              		.file 10 "Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal.h"
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 94


DEFINED SYMBOLS
                            *ABS*:00000000 stm32h7xx_hal_pwr_ex.c
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:20     .text.HAL_PWREx_ConfigSupply:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:26     .text.HAL_PWREx_ConfigSupply:00000000 HAL_PWREx_ConfigSupply
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:118    .text.HAL_PWREx_ConfigSupply:00000050 $d
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:123    .text.HAL_PWREx_GetSupplyConfig:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:129    .text.HAL_PWREx_GetSupplyConfig:00000000 HAL_PWREx_GetSupplyConfig
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:146    .text.HAL_PWREx_GetSupplyConfig:0000000c $d
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:151    .text.HAL_PWREx_ControlVoltageScaling:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:157    .text.HAL_PWREx_ControlVoltageScaling:00000000 HAL_PWREx_ControlVoltageScaling
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:235    .text.HAL_PWREx_ControlVoltageScaling:00000044 $d
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:240    .text.HAL_PWREx_GetVoltageRange:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:246    .text.HAL_PWREx_GetVoltageRange:00000000 HAL_PWREx_GetVoltageRange
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:263    .text.HAL_PWREx_GetVoltageRange:0000000c $d
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:268    .text.HAL_PWREx_ControlStopModeVoltageScaling:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:274    .text.HAL_PWREx_ControlStopModeVoltageScaling:00000000 HAL_PWREx_ControlStopModeVoltageScaling
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:298    .text.HAL_PWREx_ControlStopModeVoltageScaling:00000010 $d
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:303    .text.HAL_PWREx_GetStopModeVoltageRange:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:309    .text.HAL_PWREx_GetStopModeVoltageRange:00000000 HAL_PWREx_GetStopModeVoltageRange
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:326    .text.HAL_PWREx_GetStopModeVoltageRange:0000000c $d
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:331    .text.HAL_PWREx_EnterSTOPMode:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:337    .text.HAL_PWREx_EnterSTOPMode:00000000 HAL_PWREx_EnterSTOPMode
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:466    .text.HAL_PWREx_EnterSTOPMode:00000060 $d
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:472    .text.HAL_PWREx_ClearPendingEvent:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:478    .text.HAL_PWREx_ClearPendingEvent:00000000 HAL_PWREx_ClearPendingEvent
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:498    .text.HAL_PWREx_EnterSTANDBYMode:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:504    .text.HAL_PWREx_EnterSTANDBYMode:00000000 HAL_PWREx_EnterSTANDBYMode
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:556    .text.HAL_PWREx_EnterSTANDBYMode:00000038 $d
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:562    .text.HAL_PWREx_ConfigD3Domain:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:568    .text.HAL_PWREx_ConfigD3Domain:00000000 HAL_PWREx_ConfigD3Domain
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:588    .text.HAL_PWREx_ConfigD3Domain:00000010 $d
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:593    .text.HAL_PWREx_EnableFlashPowerDown:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:599    .text.HAL_PWREx_EnableFlashPowerDown:00000000 HAL_PWREx_EnableFlashPowerDown
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:616    .text.HAL_PWREx_EnableFlashPowerDown:0000000c $d
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:621    .text.HAL_PWREx_DisableFlashPowerDown:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:627    .text.HAL_PWREx_DisableFlashPowerDown:00000000 HAL_PWREx_DisableFlashPowerDown
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:644    .text.HAL_PWREx_DisableFlashPowerDown:0000000c $d
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:649    .text.HAL_PWREx_EnableWakeUpPin:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:655    .text.HAL_PWREx_EnableWakeUpPin:00000000 HAL_PWREx_EnableWakeUpPin
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:921    .text.HAL_PWREx_EnableWakeUpPin:000000a4 $d
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:928    .text.HAL_PWREx_DisableWakeUpPin:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:934    .text.HAL_PWREx_DisableWakeUpPin:00000000 HAL_PWREx_DisableWakeUpPin
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:953    .text.HAL_PWREx_DisableWakeUpPin:0000000c $d
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:958    .text.HAL_PWREx_GetWakeupFlag:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:964    .text.HAL_PWREx_GetWakeupFlag:00000000 HAL_PWREx_GetWakeupFlag
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:985    .text.HAL_PWREx_GetWakeupFlag:00000008 $d
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:990    .text.HAL_PWREx_ClearWakeupFlag:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:996    .text.HAL_PWREx_ClearWakeupFlag:00000000 HAL_PWREx_ClearWakeupFlag
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:1031   .text.HAL_PWREx_ClearWakeupFlag:00000018 $d
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:1036   .text.HAL_PWREx_WKUP1_Callback:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:1042   .text.HAL_PWREx_WKUP1_Callback:00000000 HAL_PWREx_WKUP1_Callback
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:1055   .text.HAL_PWREx_WKUP2_Callback:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:1061   .text.HAL_PWREx_WKUP2_Callback:00000000 HAL_PWREx_WKUP2_Callback
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:1074   .text.HAL_PWREx_WKUP4_Callback:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:1080   .text.HAL_PWREx_WKUP4_Callback:00000000 HAL_PWREx_WKUP4_Callback
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:1093   .text.HAL_PWREx_WKUP6_Callback:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:1099   .text.HAL_PWREx_WKUP6_Callback:00000000 HAL_PWREx_WKUP6_Callback
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 95


C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:1112   .text.HAL_PWREx_WAKEUP_PIN_IRQHandler:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:1118   .text.HAL_PWREx_WAKEUP_PIN_IRQHandler:00000000 HAL_PWREx_WAKEUP_PIN_IRQHandler
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:1195   .text.HAL_PWREx_WAKEUP_PIN_IRQHandler:00000060 $d
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:1200   .text.HAL_PWREx_EnableBkUpReg:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:1206   .text.HAL_PWREx_EnableBkUpReg:00000000 HAL_PWREx_EnableBkUpReg
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:1261   .text.HAL_PWREx_EnableBkUpReg:00000030 $d
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:1266   .text.HAL_PWREx_DisableBkUpReg:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:1272   .text.HAL_PWREx_DisableBkUpReg:00000000 HAL_PWREx_DisableBkUpReg
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:1327   .text.HAL_PWREx_DisableBkUpReg:00000030 $d
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:1332   .text.HAL_PWREx_EnableUSBReg:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:1338   .text.HAL_PWREx_EnableUSBReg:00000000 HAL_PWREx_EnableUSBReg
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:1393   .text.HAL_PWREx_EnableUSBReg:00000030 $d
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:1398   .text.HAL_PWREx_DisableUSBReg:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:1404   .text.HAL_PWREx_DisableUSBReg:00000000 HAL_PWREx_DisableUSBReg
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:1459   .text.HAL_PWREx_DisableUSBReg:00000030 $d
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:1464   .text.HAL_PWREx_EnableUSBVoltageDetector:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:1470   .text.HAL_PWREx_EnableUSBVoltageDetector:00000000 HAL_PWREx_EnableUSBVoltageDetector
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:1487   .text.HAL_PWREx_EnableUSBVoltageDetector:0000000c $d
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:1492   .text.HAL_PWREx_DisableUSBVoltageDetector:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:1498   .text.HAL_PWREx_DisableUSBVoltageDetector:00000000 HAL_PWREx_DisableUSBVoltageDetector
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:1515   .text.HAL_PWREx_DisableUSBVoltageDetector:0000000c $d
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:1520   .text.HAL_PWREx_EnableBatteryCharging:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:1526   .text.HAL_PWREx_EnableBatteryCharging:00000000 HAL_PWREx_EnableBatteryCharging
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:1550   .text.HAL_PWREx_EnableBatteryCharging:00000018 $d
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:1555   .text.HAL_PWREx_DisableBatteryCharging:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:1561   .text.HAL_PWREx_DisableBatteryCharging:00000000 HAL_PWREx_DisableBatteryCharging
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:1578   .text.HAL_PWREx_DisableBatteryCharging:0000000c $d
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:1583   .text.HAL_PWREx_EnableMonitoring:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:1589   .text.HAL_PWREx_EnableMonitoring:00000000 HAL_PWREx_EnableMonitoring
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:1606   .text.HAL_PWREx_EnableMonitoring:0000000c $d
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:1611   .text.HAL_PWREx_DisableMonitoring:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:1617   .text.HAL_PWREx_DisableMonitoring:00000000 HAL_PWREx_DisableMonitoring
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:1634   .text.HAL_PWREx_DisableMonitoring:0000000c $d
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:1639   .text.HAL_PWREx_GetTemperatureLevel:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:1645   .text.HAL_PWREx_GetTemperatureLevel:00000000 HAL_PWREx_GetTemperatureLevel
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:1678   .text.HAL_PWREx_GetTemperatureLevel:00000018 $d
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:1683   .text.HAL_PWREx_GetVBATLevel:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:1689   .text.HAL_PWREx_GetVBATLevel:00000000 HAL_PWREx_GetVBATLevel
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:1722   .text.HAL_PWREx_GetVBATLevel:00000018 $d
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:1727   .text.HAL_PWREx_ConfigAVD:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:1733   .text.HAL_PWREx_ConfigAVD:00000000 HAL_PWREx_ConfigAVD
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:1820   .text.HAL_PWREx_ConfigAVD:00000094 $d
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:1825   .text.HAL_PWREx_EnableAVD:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:1831   .text.HAL_PWREx_EnableAVD:00000000 HAL_PWREx_EnableAVD
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:1848   .text.HAL_PWREx_EnableAVD:0000000c $d
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:1853   .text.HAL_PWREx_DisableAVD:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:1859   .text.HAL_PWREx_DisableAVD:00000000 HAL_PWREx_DisableAVD
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:1876   .text.HAL_PWREx_DisableAVD:0000000c $d
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:1881   .text.HAL_PWREx_AVDCallback:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:1887   .text.HAL_PWREx_AVDCallback:00000000 HAL_PWREx_AVDCallback
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:1900   .text.HAL_PWREx_PVD_AVD_IRQHandler:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:1906   .text.HAL_PWREx_PVD_AVD_IRQHandler:00000000 HAL_PWREx_PVD_AVD_IRQHandler
C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s:1987   .text.HAL_PWREx_PVD_AVD_IRQHandler:00000074 $d

UNDEFINED SYMBOLS
HAL_GetTick
HAL_PWR_PVDCallback
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccgrH2sv.s 			page 96


