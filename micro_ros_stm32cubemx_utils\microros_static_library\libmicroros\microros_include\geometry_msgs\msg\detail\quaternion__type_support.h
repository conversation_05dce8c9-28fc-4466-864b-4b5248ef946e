// generated from rosidl_generator_c/resource/idl__type_support.h.em
// with input from geometry_msgs:msg/Quaternion.idl
// generated code does not contain a copyright notice

#ifndef GEOMETRY_MSGS__MSG__DETAIL__QUATERNION__TYPE_SUPPORT_H_
#define GEOMETRY_MSGS__MSG__DETAIL__QUATERNION__TYPE_SUPPORT_H_

#include "rosidl_typesupport_interface/macros.h"

#include "geometry_msgs/msg/rosidl_generator_c__visibility_control.h"

#ifdef __cplusplus
extern "C"
{
#endif

#include "rosidl_runtime_c/message_type_support_struct.h"

// Forward declare the get type support functions for this type.
ROSIDL_GENERATOR_C_PUBLIC_geometry_msgs
const rosidl_message_type_support_t *
ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(
  rosidl_typesupport_c,
  geometry_msgs,
  msg,
  Quaternion
)();

#ifdef __cplusplus
}
#endif

#endif  // GEOMETRY_MSGS__MSG__DETAIL__QUATERNION__TYPE_SUPPORT_H_
