ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccXMbSw6.s 			page 1


   1              		.cpu cortex-m7
   2              		.arch armv7e-m
   3              		.fpu fpv5-d16
   4              		.eabi_attribute 28, 1
   5              		.eabi_attribute 20, 1
   6              		.eabi_attribute 21, 1
   7              		.eabi_attribute 23, 3
   8              		.eabi_attribute 24, 1
   9              		.eabi_attribute 25, 1
  10              		.eabi_attribute 26, 1
  11              		.eabi_attribute 30, 1
  12              		.eabi_attribute 34, 1
  13              		.eabi_attribute 18, 4
  14              		.file	"if_api.c"
  15              		.text
  16              	.Ltext0:
  17              		.cfi_sections	.debug_frame
  18              		.file 1 "Middlewares/Third_Party/LwIP/src/api/if_api.c"
  19              		.section	.text.lwip_if_indextoname,"ax",%progbits
  20              		.align	1
  21              		.global	lwip_if_indextoname
  22              		.syntax unified
  23              		.thumb
  24              		.thumb_func
  26              	lwip_if_indextoname:
  27              	.LVL0:
  28              	.LFB174:
   1:Middlewares/Third_Party/LwIP/src/api/if_api.c **** /**
   2:Middlewares/Third_Party/LwIP/src/api/if_api.c ****  * @file
   3:Middlewares/Third_Party/LwIP/src/api/if_api.c ****  * Interface Identification APIs from:
   4:Middlewares/Third_Party/LwIP/src/api/if_api.c ****  *              RFC 3493: Basic Socket Interface Extensions for IPv6
   5:Middlewares/Third_Party/LwIP/src/api/if_api.c ****  *                  Section 4: Interface Identification
   6:Middlewares/Third_Party/LwIP/src/api/if_api.c ****  *
   7:Middlewares/Third_Party/LwIP/src/api/if_api.c ****  * @defgroup if_api Interface Identification API
   8:Middlewares/Third_Party/LwIP/src/api/if_api.c ****  * @ingroup socket
   9:Middlewares/Third_Party/LwIP/src/api/if_api.c ****  */
  10:Middlewares/Third_Party/LwIP/src/api/if_api.c **** 
  11:Middlewares/Third_Party/LwIP/src/api/if_api.c **** /*
  12:Middlewares/Third_Party/LwIP/src/api/if_api.c ****  * Copyright (c) 2017 Joel Cunningham, Garmin International, Inc. <<EMAIL>>
  13:Middlewares/Third_Party/LwIP/src/api/if_api.c ****  * All rights reserved.
  14:Middlewares/Third_Party/LwIP/src/api/if_api.c ****  *
  15:Middlewares/Third_Party/LwIP/src/api/if_api.c ****  * Redistribution and use in source and binary forms, with or without modification,
  16:Middlewares/Third_Party/LwIP/src/api/if_api.c ****  * are permitted provided that the following conditions are met:
  17:Middlewares/Third_Party/LwIP/src/api/if_api.c ****  *
  18:Middlewares/Third_Party/LwIP/src/api/if_api.c ****  * 1. Redistributions of source code must retain the above copyright notice,
  19:Middlewares/Third_Party/LwIP/src/api/if_api.c ****  *    this list of conditions and the following disclaimer.
  20:Middlewares/Third_Party/LwIP/src/api/if_api.c ****  * 2. Redistributions in binary form must reproduce the above copyright notice,
  21:Middlewares/Third_Party/LwIP/src/api/if_api.c ****  *    this list of conditions and the following disclaimer in the documentation
  22:Middlewares/Third_Party/LwIP/src/api/if_api.c ****  *    and/or other materials provided with the distribution.
  23:Middlewares/Third_Party/LwIP/src/api/if_api.c ****  * 3. The name of the author may not be used to endorse or promote products
  24:Middlewares/Third_Party/LwIP/src/api/if_api.c ****  *    derived from this software without specific prior written permission.
  25:Middlewares/Third_Party/LwIP/src/api/if_api.c ****  *
  26:Middlewares/Third_Party/LwIP/src/api/if_api.c ****  * THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR IMPLIED
  27:Middlewares/Third_Party/LwIP/src/api/if_api.c ****  * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
  28:Middlewares/Third_Party/LwIP/src/api/if_api.c ****  * MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT
  29:Middlewares/Third_Party/LwIP/src/api/if_api.c ****  * SHALL THE AUTHOR BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
  30:Middlewares/Third_Party/LwIP/src/api/if_api.c ****  * EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccXMbSw6.s 			page 2


  31:Middlewares/Third_Party/LwIP/src/api/if_api.c ****  * OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
  32:Middlewares/Third_Party/LwIP/src/api/if_api.c ****  * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
  33:Middlewares/Third_Party/LwIP/src/api/if_api.c ****  * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
  34:Middlewares/Third_Party/LwIP/src/api/if_api.c ****  * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY
  35:Middlewares/Third_Party/LwIP/src/api/if_api.c ****  * OF SUCH DAMAGE.
  36:Middlewares/Third_Party/LwIP/src/api/if_api.c ****  *
  37:Middlewares/Third_Party/LwIP/src/api/if_api.c ****  * This file is part of the lwIP TCP/IP stack.
  38:Middlewares/Third_Party/LwIP/src/api/if_api.c ****  *
  39:Middlewares/Third_Party/LwIP/src/api/if_api.c ****  * Author: Joel Cunningham <<EMAIL>>
  40:Middlewares/Third_Party/LwIP/src/api/if_api.c ****  *
  41:Middlewares/Third_Party/LwIP/src/api/if_api.c ****  */
  42:Middlewares/Third_Party/LwIP/src/api/if_api.c **** #include "lwip/opt.h"
  43:Middlewares/Third_Party/LwIP/src/api/if_api.c **** 
  44:Middlewares/Third_Party/LwIP/src/api/if_api.c **** #if LWIP_SOCKET
  45:Middlewares/Third_Party/LwIP/src/api/if_api.c **** 
  46:Middlewares/Third_Party/LwIP/src/api/if_api.c **** #include "lwip/errno.h"
  47:Middlewares/Third_Party/LwIP/src/api/if_api.c **** #include "lwip/if_api.h"
  48:Middlewares/Third_Party/LwIP/src/api/if_api.c **** #include "lwip/netifapi.h"
  49:Middlewares/Third_Party/LwIP/src/api/if_api.c **** #include "lwip/priv/sockets_priv.h"
  50:Middlewares/Third_Party/LwIP/src/api/if_api.c **** 
  51:Middlewares/Third_Party/LwIP/src/api/if_api.c **** /**
  52:Middlewares/Third_Party/LwIP/src/api/if_api.c ****  * @ingroup if_api
  53:Middlewares/Third_Party/LwIP/src/api/if_api.c ****  * Maps an interface index to its corresponding name.
  54:Middlewares/Third_Party/LwIP/src/api/if_api.c ****  * @param ifindex interface index
  55:Middlewares/Third_Party/LwIP/src/api/if_api.c ****  * @param ifname shall point to a buffer of at least {IF_NAMESIZE} bytes
  56:Middlewares/Third_Party/LwIP/src/api/if_api.c ****  * @return If ifindex is an interface index, then the function shall return the
  57:Middlewares/Third_Party/LwIP/src/api/if_api.c ****  * value supplied in ifname, which points to a buffer now containing the interface name.
  58:Middlewares/Third_Party/LwIP/src/api/if_api.c ****  * Otherwise, the function shall return a NULL pointer.
  59:Middlewares/Third_Party/LwIP/src/api/if_api.c ****  */
  60:Middlewares/Third_Party/LwIP/src/api/if_api.c **** char *
  61:Middlewares/Third_Party/LwIP/src/api/if_api.c **** lwip_if_indextoname(unsigned int ifindex, char *ifname)
  62:Middlewares/Third_Party/LwIP/src/api/if_api.c **** {
  29              		.loc 1 62 1 view -0
  30              		.cfi_startproc
  31              		@ args = 0, pretend = 0, frame = 0
  32              		@ frame_needed = 0, uses_anonymous_args = 0
  33              		@ link register save eliminated.
  63:Middlewares/Third_Party/LwIP/src/api/if_api.c **** #if LWIP_NETIF_API
  64:Middlewares/Third_Party/LwIP/src/api/if_api.c ****   if (ifindex <= 0xff) {
  65:Middlewares/Third_Party/LwIP/src/api/if_api.c ****     err_t err = netifapi_netif_index_to_name((u8_t)ifindex, ifname);
  66:Middlewares/Third_Party/LwIP/src/api/if_api.c ****     if (!err && ifname[0] != '\0') {
  67:Middlewares/Third_Party/LwIP/src/api/if_api.c ****       return ifname;
  68:Middlewares/Third_Party/LwIP/src/api/if_api.c ****     }
  69:Middlewares/Third_Party/LwIP/src/api/if_api.c ****   }
  70:Middlewares/Third_Party/LwIP/src/api/if_api.c **** #else /* LWIP_NETIF_API */
  71:Middlewares/Third_Party/LwIP/src/api/if_api.c ****   LWIP_UNUSED_ARG(ifindex);
  34              		.loc 1 71 3 view .LVU1
  72:Middlewares/Third_Party/LwIP/src/api/if_api.c ****   LWIP_UNUSED_ARG(ifname);
  35              		.loc 1 72 3 view .LVU2
  73:Middlewares/Third_Party/LwIP/src/api/if_api.c **** #endif /* LWIP_NETIF_API */
  74:Middlewares/Third_Party/LwIP/src/api/if_api.c ****   set_errno(ENXIO);
  36              		.loc 1 74 3 view .LVU3
  37              		.loc 1 74 3 view .LVU4
  38              		.loc 1 74 3 discriminator 1 view .LVU5
  39 0000 024B     		ldr	r3, .L2
  40 0002 0622     		movs	r2, #6
  41 0004 1A60     		str	r2, [r3]
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccXMbSw6.s 			page 3


  42              		.loc 1 74 3 discriminator 3 view .LVU6
  75:Middlewares/Third_Party/LwIP/src/api/if_api.c ****   return NULL;
  43              		.loc 1 75 3 view .LVU7
  76:Middlewares/Third_Party/LwIP/src/api/if_api.c **** }
  44              		.loc 1 76 1 is_stmt 0 view .LVU8
  45 0006 0020     		movs	r0, #0
  46              	.LVL1:
  47              		.loc 1 76 1 view .LVU9
  48 0008 7047     		bx	lr
  49              	.L3:
  50 000a 00BF     		.align	2
  51              	.L2:
  52 000c 00000000 		.word	errno
  53              		.cfi_endproc
  54              	.LFE174:
  56              		.section	.text.lwip_if_nametoindex,"ax",%progbits
  57              		.align	1
  58              		.global	lwip_if_nametoindex
  59              		.syntax unified
  60              		.thumb
  61              		.thumb_func
  63              	lwip_if_nametoindex:
  64              	.LVL2:
  65              	.LFB175:
  77:Middlewares/Third_Party/LwIP/src/api/if_api.c **** 
  78:Middlewares/Third_Party/LwIP/src/api/if_api.c **** /**
  79:Middlewares/Third_Party/LwIP/src/api/if_api.c ****  * @ingroup if_api
  80:Middlewares/Third_Party/LwIP/src/api/if_api.c ****  * Returs the interface index corresponding to name ifname.
  81:Middlewares/Third_Party/LwIP/src/api/if_api.c ****  * @param ifname Interface name
  82:Middlewares/Third_Party/LwIP/src/api/if_api.c ****  * @return The corresponding index if ifname is the name of an interface;
  83:Middlewares/Third_Party/LwIP/src/api/if_api.c ****  * otherwise, zero.
  84:Middlewares/Third_Party/LwIP/src/api/if_api.c ****  */
  85:Middlewares/Third_Party/LwIP/src/api/if_api.c **** unsigned int
  86:Middlewares/Third_Party/LwIP/src/api/if_api.c **** lwip_if_nametoindex(const char *ifname)
  87:Middlewares/Third_Party/LwIP/src/api/if_api.c **** {
  66              		.loc 1 87 1 is_stmt 1 view -0
  67              		.cfi_startproc
  68              		@ args = 0, pretend = 0, frame = 0
  69              		@ frame_needed = 0, uses_anonymous_args = 0
  70              		@ link register save eliminated.
  88:Middlewares/Third_Party/LwIP/src/api/if_api.c **** #if LWIP_NETIF_API
  89:Middlewares/Third_Party/LwIP/src/api/if_api.c ****   err_t err;
  90:Middlewares/Third_Party/LwIP/src/api/if_api.c ****   u8_t idx;
  91:Middlewares/Third_Party/LwIP/src/api/if_api.c **** 
  92:Middlewares/Third_Party/LwIP/src/api/if_api.c ****   err = netifapi_netif_name_to_index(ifname, &idx);
  93:Middlewares/Third_Party/LwIP/src/api/if_api.c ****   if (!err) {
  94:Middlewares/Third_Party/LwIP/src/api/if_api.c ****     return idx;
  95:Middlewares/Third_Party/LwIP/src/api/if_api.c ****   }
  96:Middlewares/Third_Party/LwIP/src/api/if_api.c **** #else /* LWIP_NETIF_API */
  97:Middlewares/Third_Party/LwIP/src/api/if_api.c ****   LWIP_UNUSED_ARG(ifname);
  71              		.loc 1 97 3 view .LVU11
  98:Middlewares/Third_Party/LwIP/src/api/if_api.c **** #endif /* LWIP_NETIF_API */
  99:Middlewares/Third_Party/LwIP/src/api/if_api.c ****   return 0; /* invalid index */
  72              		.loc 1 99 3 view .LVU12
 100:Middlewares/Third_Party/LwIP/src/api/if_api.c **** }
  73              		.loc 1 100 1 is_stmt 0 view .LVU13
  74 0000 0020     		movs	r0, #0
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccXMbSw6.s 			page 4


  75              	.LVL3:
  76              		.loc 1 100 1 view .LVU14
  77 0002 7047     		bx	lr
  78              		.cfi_endproc
  79              	.LFE175:
  81              		.text
  82              	.Letext0:
  83              		.file 2 "Middlewares/Third_Party/LwIP/src/include/lwip/errno.h"
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccXMbSw6.s 			page 5


DEFINED SYMBOLS
                            *ABS*:00000000 if_api.c
C:\Users\<USER>\AppData\Local\Temp\ccXMbSw6.s:20     .text.lwip_if_indextoname:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccXMbSw6.s:26     .text.lwip_if_indextoname:00000000 lwip_if_indextoname
C:\Users\<USER>\AppData\Local\Temp\ccXMbSw6.s:52     .text.lwip_if_indextoname:0000000c $d
C:\Users\<USER>\AppData\Local\Temp\ccXMbSw6.s:57     .text.lwip_if_nametoindex:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccXMbSw6.s:63     .text.lwip_if_nametoindex:00000000 lwip_if_nametoindex

UNDEFINED SYMBOLS
errno
