ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc8WlH0D.s 			page 1


   1              		.cpu cortex-m7
   2              		.arch armv7e-m
   3              		.fpu fpv5-d16
   4              		.eabi_attribute 28, 1
   5              		.eabi_attribute 20, 1
   6              		.eabi_attribute 21, 1
   7              		.eabi_attribute 23, 3
   8              		.eabi_attribute 24, 1
   9              		.eabi_attribute 25, 1
  10              		.eabi_attribute 26, 1
  11              		.eabi_attribute 30, 1
  12              		.eabi_attribute 34, 1
  13              		.eabi_attribute 18, 4
  14              		.file	"stm32h7xx_hal_exti.c"
  15              		.text
  16              	.Ltext0:
  17              		.cfi_sections	.debug_frame
  18              		.file 1 "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c"
  19              		.section	.text.HAL_EXTI_SetConfigLine,"ax",%progbits
  20              		.align	1
  21              		.global	HAL_EXTI_SetConfigLine
  22              		.syntax unified
  23              		.thumb
  24              		.thumb_func
  26              	HAL_EXTI_SetConfigLine:
  27              	.LVL0:
  28              	.LFB144:
   1:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** /**
   2:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   ******************************************************************************
   3:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   * @file    stm32h7xx_hal_exti.c
   4:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   * <AUTHOR> Application Team
   5:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   * @brief   EXTI HAL module driver.
   6:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   *          This file provides firmware functions to manage the following
   7:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   *          functionalities of the General Purpose Input/Output (EXTI) peripheral:
   8:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   *           + Initialization and de-initialization functions
   9:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   *           + IO operation functions
  10:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   *
  11:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   ******************************************************************************
  12:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   * @attention
  13:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   *
  14:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   * Copyright (c) 2017 STMicroelectronics.
  15:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   * All rights reserved.
  16:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   *
  17:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   * This software is licensed under terms that can be found in the LICENSE file
  18:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   * in the root directory of this software component.
  19:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   * If no LICENSE file comes with this software, it is provided AS-IS.
  20:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   *
  21:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   ******************************************************************************
  22:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   @verbatim
  23:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   ==============================================================================
  24:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****                     ##### EXTI Peripheral features #####
  25:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   ==============================================================================
  26:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   [..]
  27:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     (+) Each Exti line can be configured within this driver.
  28:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
  29:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     (+) Exti line can be configured in 3 different modes
  30:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****         (++) Interrupt (CORE1 or CORE2 in case of dual core line )
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc8WlH0D.s 			page 2


  31:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****         (++) Event (CORE1 or CORE2 in case of dual core line )
  32:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****         (++) a combination of the previous
  33:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
  34:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     (+) Configurable Exti lines can be configured with 3 different triggers
  35:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****         (++) Rising
  36:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****         (++) Falling
  37:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****         (++) Both of them
  38:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
  39:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     (+) When set in interrupt mode, configurable Exti lines have two diffenrents
  40:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****         interrupt pending registers which allow to distinguish which transition
  41:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****         occurs:
  42:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****         (++) Rising edge pending interrupt
  43:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****         (++) Falling
  44:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
  45:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     (+) Exti lines 0 to 15 are linked to gpio pin number 0 to 15. Gpio port can
  46:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****         be selected through multiplexer.
  47:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
  48:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     (+) PendClearSource used to set the D3 Smart Run Domain autoamtic pend clear source.
  49:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****         It is applicable for line with wkaeup target is Any (CPU1 , CPU2 and D3 smart run domain).
  50:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****         Value can be one of the following:
  51:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****         (++)  EXTI_D3_PENDCLR_SRC_NONE : no pend clear source is selected :
  52:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****               In this case corresponding bit of D2PMRx register is set to 0
  53:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****                 (+++) On a configurable Line : the D3 domain wakeup signal is
  54:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****                       automatically cleared after after the Delay + Rising Edge detect
  55:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****                 (+++) On a direct Line : the D3 domain wakeup signal is
  56:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****                       cleared after the direct event input signal is cleared
  57:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
  58:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****         (++)  EXTI_D3_PENDCLR_SRC_DMACH6 : no pend clear source is selected :
  59:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****               In this case corresponding bit of D2PMRx register is set to 1
  60:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****               and corresponding bits(2) of D3PCRxL/H is set to b00 :
  61:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****                 DMA ch6 event selected as D3 domain pendclear source
  62:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
  63:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****         (++)  EXTI_D3_PENDCLR_SRC_DMACH7 : no pend clear source is selected :
  64:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****               In this case corresponding bit of D2PMRx register is set to 1
  65:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****               and corresponding bits(2) of D3PCRxL/H is set to b01 :
  66:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****                 DMA ch7 event selected as D3 domain pendclear source
  67:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
  68:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****         (++)  EXTI_D3_PENDCLR_SRC_LPTIM4 : no pend clear source is selected :
  69:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****               In this case corresponding bit of D2PMRx register is set to 1
  70:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****               and corresponding bits(2) of D3PCRxL/H is set to b10 :
  71:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****                 LPTIM4 out selected as D3 domain pendclear source
  72:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
  73:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****         (++)  EXTI_D3_PENDCLR_SRC_LPTIM5 : no pend clear source is selected :
  74:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****               In this case corresponding bit of D2PMRx register is set to 1
  75:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****               and corresponding bits(2) of D3PCRxL/H is set to b11 :
  76:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****                 LPTIM5 out selected as D3 domain pendclear source
  77:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
  78:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
  79:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****                      ##### How to use this driver #####
  80:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   ==============================================================================
  81:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   [..]
  82:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
  83:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     (#) Configure the EXTI line using HAL_EXTI_SetConfigLine().
  84:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****         (++) Choose the interrupt line number by setting "Line" member from
  85:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****              EXTI_ConfigTypeDef structure.
  86:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****         (++) Configure the interrupt and/or event mode using "Mode" member from
  87:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****              EXTI_ConfigTypeDef structure.
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc8WlH0D.s 			page 3


  88:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****         (++) For configurable lines, configure rising and/or falling trigger
  89:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****              "Trigger" member from EXTI_ConfigTypeDef structure.
  90:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****         (++) For Exti lines linked to gpio, choose gpio port using "GPIOSel"
  91:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****              member from GPIO_InitTypeDef structure.
  92:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****         (++) For Exti lines with wkaeup target is Any (CPU1 , CPU2 and D3 smart run domain),
  93:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****              choose gpio D3 PendClearSource using PendClearSource
  94:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****              member from EXTI_PendClear_Source structure.
  95:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
  96:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     (#) Get current Exti configuration of a dedicated line using
  97:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****         HAL_EXTI_GetConfigLine().
  98:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****         (++) Provide exiting handle as parameter.
  99:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****         (++) Provide pointer on EXTI_ConfigTypeDef structure as second parameter.
 100:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 101:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     (#) Clear Exti configuration of a dedicated line using HAL_EXTI_ClearConfigLine().
 102:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****         (++) Provide exiting handle as parameter.
 103:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 104:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     (#) Register callback to treat Exti interrupts using HAL_EXTI_RegisterCallback().
 105:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****         (++) Provide exiting handle as first parameter.
 106:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****         (++) Provide which callback will be registered using one value from
 107:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****              EXTI_CallbackIDTypeDef.
 108:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****         (++) Provide callback function pointer.
 109:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 110:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     (#) Get interrupt pending bit using HAL_EXTI_GetPending().
 111:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 112:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     (#) Clear interrupt pending bit using HAL_EXTI_ClearPending().
 113:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 114:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     (#) Generate software interrupt using HAL_EXTI_GenerateSWI().
 115:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 116:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   @endverbatim
 117:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   */
 118:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 119:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** /* Includes ------------------------------------------------------------------*/
 120:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** #include "stm32h7xx_hal.h"
 121:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 122:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** /** @addtogroup STM32H7xx_HAL_Driver
 123:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   * @{
 124:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   */
 125:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 126:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** /** @addtogroup EXTI
 127:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   * @{
 128:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   */
 129:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 130:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** #ifdef HAL_EXTI_MODULE_ENABLED
 131:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 132:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** /* Private typedef -----------------------------------------------------------*/
 133:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** /* Private defines ------------------------------------------------------------*/
 134:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** /** @defgroup EXTI_Private_Constants EXTI Private Constants
 135:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   * @{
 136:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   */
 137:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** #define EXTI_MODE_OFFSET                    0x04U   /* 0x10: offset between CPU IMR/EMR registers *
 138:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** #define EXTI_CONFIG_OFFSET                  0x08U   /* 0x20: offset between CPU Rising/Falling conf
 139:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** /**
 140:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   * @}
 141:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   */
 142:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 143:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** /* Private macros ------------------------------------------------------------*/
 144:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** /* Private variables ---------------------------------------------------------*/
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc8WlH0D.s 			page 4


 145:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** /* Private function prototypes -----------------------------------------------*/
 146:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** /* Exported functions --------------------------------------------------------*/
 147:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 148:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** /** @addtogroup EXTI_Exported_Functions
 149:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   * @{
 150:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   */
 151:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 152:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** /** @addtogroup EXTI_Exported_Functions_Group1
 153:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****  *  @brief    Configuration functions
 154:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****  *
 155:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** @verbatim
 156:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****  ===============================================================================
 157:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****               ##### Configuration functions #####
 158:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****  ===============================================================================
 159:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 160:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** @endverbatim
 161:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   * @{
 162:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   */
 163:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 164:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** /**
 165:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   * @brief  Set configuration of a dedicated Exti line.
 166:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   * @param  hexti Exti handle.
 167:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   * @param  pExtiConfig Pointer on EXTI configuration to be set.
 168:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   * @retval HAL Status.
 169:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   */
 170:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** HAL_StatusTypeDef HAL_EXTI_SetConfigLine(EXTI_HandleTypeDef *hexti, EXTI_ConfigTypeDef *pExtiConfig
 171:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** {
  29              		.loc 1 171 1 view -0
  30              		.cfi_startproc
  31              		@ args = 0, pretend = 0, frame = 0
  32              		@ frame_needed = 0, uses_anonymous_args = 0
 172:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   __IO uint32_t *regaddr;
  33              		.loc 1 172 3 view .LVU1
 173:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   uint32_t regval;
  34              		.loc 1 173 3 view .LVU2
 174:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   uint32_t linepos;
  35              		.loc 1 174 3 view .LVU3
 175:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   uint32_t maskline;
  36              		.loc 1 175 3 view .LVU4
 176:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   uint32_t offset;
  37              		.loc 1 176 3 view .LVU5
 177:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   uint32_t pcrlinepos;
  38              		.loc 1 177 3 view .LVU6
 178:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 179:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   /* Check null pointer */
 180:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   if ((hexti == NULL) || (pExtiConfig == NULL))
  39              		.loc 1 180 3 view .LVU7
  40              		.loc 1 180 6 is_stmt 0 view .LVU8
  41 0000 0029     		cmp	r1, #0
  42 0002 18BF     		it	ne
  43 0004 0028     		cmpne	r0, #0
  44 0006 00F08C80 		beq	.L15
 171:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   __IO uint32_t *regaddr;
  45              		.loc 1 171 1 view .LVU9
  46 000a F0B5     		push	{r4, r5, r6, r7, lr}
  47              	.LCFI0:
  48              		.cfi_def_cfa_offset 20
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc8WlH0D.s 			page 5


  49              		.cfi_offset 4, -20
  50              		.cfi_offset 5, -16
  51              		.cfi_offset 6, -12
  52              		.cfi_offset 7, -8
  53              		.cfi_offset 14, -4
  54 000c 0A46     		mov	r2, r1
 181:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   {
 182:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     return HAL_ERROR;
 183:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   }
 184:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 185:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   /* Check the parameters */
 186:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   assert_param(IS_EXTI_LINE(pExtiConfig->Line));
  55              		.loc 1 186 3 is_stmt 1 view .LVU10
 187:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   assert_param(IS_EXTI_MODE(pExtiConfig->Mode));
  56              		.loc 1 187 3 view .LVU11
 188:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 189:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   /* Assign line number to handle */
 190:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   hexti->Line = pExtiConfig->Line;
  57              		.loc 1 190 3 view .LVU12
  58              		.loc 1 190 28 is_stmt 0 view .LVU13
  59 000e 0968     		ldr	r1, [r1]
  60              	.LVL1:
  61              		.loc 1 190 15 view .LVU14
  62 0010 0160     		str	r1, [r0]
 191:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 192:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   /* compute line register offset and line mask */
 193:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   offset = ((pExtiConfig->Line & EXTI_REG_MASK) >> EXTI_REG_SHIFT);
  63              		.loc 1 193 3 is_stmt 1 view .LVU15
  64              		.loc 1 193 10 is_stmt 0 view .LVU16
  65 0012 C1F3014C 		ubfx	ip, r1, #16, #2
  66              	.LVL2:
 194:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   linepos = (pExtiConfig->Line & EXTI_PIN_MASK);
  67              		.loc 1 194 3 is_stmt 1 view .LVU17
  68              		.loc 1 194 11 is_stmt 0 view .LVU18
  69 0016 01F01F0E 		and	lr, r1, #31
  70              	.LVL3:
 195:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   maskline = (1UL << linepos);
  71              		.loc 1 195 3 is_stmt 1 view .LVU19
  72              		.loc 1 195 12 is_stmt 0 view .LVU20
  73 001a 0123     		movs	r3, #1
  74 001c 03FA0EF3 		lsl	r3, r3, lr
  75              	.LVL4:
 196:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 197:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   /* Configure triggers for configurable lines */
 198:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   if ((pExtiConfig->Line & EXTI_CONFIG) != 0x00U)
  76              		.loc 1 198 3 is_stmt 1 view .LVU21
  77              		.loc 1 198 6 is_stmt 0 view .LVU22
  78 0020 11F0007F 		tst	r1, #33554432
  79 0024 18D0     		beq	.L3
 199:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   {
 200:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     assert_param(IS_EXTI_TRIGGER(pExtiConfig->Trigger));
  80              		.loc 1 200 5 is_stmt 1 view .LVU23
 201:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 202:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     /* Configure rising trigger */
 203:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     regaddr = (__IO uint32_t *)(&EXTI->RTSR1 + (EXTI_CONFIG_OFFSET * offset));
  81              		.loc 1 203 5 view .LVU24
  82              		.loc 1 203 15 is_stmt 0 view .LVU25
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc8WlH0D.s 			page 6


  83 0026 4FEA4C10 		lsl	r0, ip, #5
  84              	.LVL5:
  85              		.loc 1 203 13 view .LVU26
  86 002a 00F1B046 		add	r6, r0, #1476395008
  87              	.LVL6:
 204:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     regval = *regaddr;
  88              		.loc 1 204 5 is_stmt 1 view .LVU27
  89              		.loc 1 204 12 is_stmt 0 view .LVU28
  90 002e 3468     		ldr	r4, [r6]
  91              	.LVL7:
 205:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 206:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     /* Mask or set line */
 207:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     if ((pExtiConfig->Trigger & EXTI_TRIGGER_RISING) != 0x00U)
  92              		.loc 1 207 5 is_stmt 1 view .LVU29
  93              		.loc 1 207 21 is_stmt 0 view .LVU30
  94 0030 9568     		ldr	r5, [r2, #8]
  95              		.loc 1 207 8 view .LVU31
  96 0032 15F0010F 		tst	r5, #1
  97 0036 29D0     		beq	.L4
 208:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     {
 209:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       regval |= maskline;
  98              		.loc 1 209 7 is_stmt 1 view .LVU32
  99              		.loc 1 209 14 is_stmt 0 view .LVU33
 100 0038 1C43     		orrs	r4, r4, r3
 101              	.LVL8:
 102              	.L5:
 210:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     }
 211:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     else
 212:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     {
 213:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       regval &= ~maskline;
 214:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     }
 215:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 216:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     /* Store rising trigger mode */
 217:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     *regaddr = regval;
 103              		.loc 1 217 5 is_stmt 1 view .LVU34
 104              		.loc 1 217 14 is_stmt 0 view .LVU35
 105 003a 3460     		str	r4, [r6]
 218:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 219:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     /* Configure falling trigger */
 220:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     regaddr = (__IO uint32_t *)(&EXTI->FTSR1 + (EXTI_CONFIG_OFFSET * offset));
 106              		.loc 1 220 5 is_stmt 1 view .LVU36
 107              		.loc 1 220 13 is_stmt 0 view .LVU37
 108 003c 3A4E     		ldr	r6, .L23
 109              	.LVL9:
 221:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     regval = *regaddr;
 110              		.loc 1 221 5 is_stmt 1 view .LVU38
 111              		.loc 1 221 12 is_stmt 0 view .LVU39
 112 003e 8459     		ldr	r4, [r0, r6]
 113              	.LVL10:
 222:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 223:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     /* Mask or set line */
 224:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     if ((pExtiConfig->Trigger & EXTI_TRIGGER_FALLING) != 0x00U)
 114              		.loc 1 224 5 is_stmt 1 view .LVU40
 115              		.loc 1 224 21 is_stmt 0 view .LVU41
 116 0040 9568     		ldr	r5, [r2, #8]
 117              		.loc 1 224 8 view .LVU42
 118 0042 15F0020F 		tst	r5, #2
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc8WlH0D.s 			page 7


 119 0046 24D0     		beq	.L6
 225:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     {
 226:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       regval |= maskline;
 120              		.loc 1 226 7 is_stmt 1 view .LVU43
 121              		.loc 1 226 14 is_stmt 0 view .LVU44
 122 0048 1C43     		orrs	r4, r4, r3
 123              	.LVL11:
 124              	.L7:
 227:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     }
 228:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     else
 229:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     {
 230:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       regval &= ~maskline;
 231:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     }
 232:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 233:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     /* Store falling trigger mode */
 234:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     *regaddr = regval;
 125              		.loc 1 234 5 is_stmt 1 view .LVU45
 126              		.loc 1 234 14 is_stmt 0 view .LVU46
 127 004a 8451     		str	r4, [r0, r6]
 235:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 236:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     /* Configure gpio port selection in case of gpio exti line */
 237:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     if ((pExtiConfig->Line & EXTI_GPIO) == EXTI_GPIO)
 128              		.loc 1 237 5 is_stmt 1 view .LVU47
 129              		.loc 1 237 21 is_stmt 0 view .LVU48
 130 004c 1068     		ldr	r0, [r2]
 131              	.LVL12:
 132              		.loc 1 237 28 view .LVU49
 133 004e 00F0C060 		and	r0, r0, #100663296
 134              		.loc 1 237 8 view .LVU50
 135 0052 B0F1C06F 		cmp	r0, #100663296
 136 0056 1FD0     		beq	.L21
 137              	.LVL13:
 138              	.L3:
 238:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     {
 239:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       assert_param(IS_EXTI_GPIO_PORT(pExtiConfig->GPIOSel));
 240:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       assert_param(IS_EXTI_GPIO_PIN(linepos));
 241:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 242:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       regval = SYSCFG->EXTICR[(linepos >> 2U) & 0x03UL];
 243:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       regval &= ~(SYSCFG_EXTICR1_EXTI0 << (SYSCFG_EXTICR1_EXTI1_Pos * (linepos & 0x03U)));
 244:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       regval |= (pExtiConfig->GPIOSel << (SYSCFG_EXTICR1_EXTI1_Pos * (linepos & 0x03U)));
 245:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       SYSCFG->EXTICR[(linepos >> 2U) & 0x03UL] = regval;
 246:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     }
 247:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   }
 248:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 249:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   /* Configure interrupt mode : read current mode */
 250:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   regaddr = (__IO uint32_t *)(&EXTI->IMR1 + (EXTI_MODE_OFFSET * offset));
 139              		.loc 1 250 3 is_stmt 1 view .LVU51
 140              		.loc 1 250 13 is_stmt 0 view .LVU52
 141 0058 4FEA0C10 		lsl	r0, ip, #4
 142              		.loc 1 250 11 view .LVU53
 143 005c 334E     		ldr	r6, .L23+4
 144              	.LVL14:
 251:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   regval = *regaddr;
 145              		.loc 1 251 3 is_stmt 1 view .LVU54
 146              		.loc 1 251 10 is_stmt 0 view .LVU55
 147 005e 8459     		ldr	r4, [r0, r6]
 148              	.LVL15:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc8WlH0D.s 			page 8


 252:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 253:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   /* Mask or set line */
 254:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   if ((pExtiConfig->Mode & EXTI_MODE_INTERRUPT) != 0x00U)
 149              		.loc 1 254 3 is_stmt 1 view .LVU56
 150              		.loc 1 254 19 is_stmt 0 view .LVU57
 151 0060 5568     		ldr	r5, [r2, #4]
 152              		.loc 1 254 6 view .LVU58
 153 0062 15F0010F 		tst	r5, #1
 154 0066 2AD0     		beq	.L8
 255:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   {
 256:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     regval |= maskline;
 155              		.loc 1 256 5 is_stmt 1 view .LVU59
 156              		.loc 1 256 12 is_stmt 0 view .LVU60
 157 0068 1C43     		orrs	r4, r4, r3
 158              	.LVL16:
 159              	.L9:
 257:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   }
 258:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   else
 259:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   {
 260:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     regval &= ~maskline;
 261:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   }
 262:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 263:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   /* Store interrupt mode */
 264:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   *regaddr = regval;
 160              		.loc 1 264 3 is_stmt 1 view .LVU61
 161              		.loc 1 264 12 is_stmt 0 view .LVU62
 162 006a 8451     		str	r4, [r0, r6]
 265:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 266:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   /* The event mode cannot be configured if the line does not support it */
 267:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   assert_param(((pExtiConfig->Line & EXTI_EVENT) == EXTI_EVENT) || ((pExtiConfig->Mode & EXTI_MODE_
 163              		.loc 1 267 3 is_stmt 1 view .LVU63
 268:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 269:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   /* Configure event mode : read current mode */
 270:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   regaddr = (__IO uint32_t *)(&EXTI->EMR1 + (EXTI_MODE_OFFSET * offset));
 164              		.loc 1 270 3 view .LVU64
 165              		.loc 1 270 11 is_stmt 0 view .LVU65
 166 006c 304D     		ldr	r5, .L23+8
 167              	.LVL17:
 271:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   regval = *regaddr;
 168              		.loc 1 271 3 is_stmt 1 view .LVU66
 169              		.loc 1 271 10 is_stmt 0 view .LVU67
 170 006e 4459     		ldr	r4, [r0, r5]
 171              	.LVL18:
 272:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 273:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   /* Mask or set line */
 274:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   if ((pExtiConfig->Mode & EXTI_MODE_EVENT) != 0x00U)
 172              		.loc 1 274 3 is_stmt 1 view .LVU68
 173              		.loc 1 274 6 is_stmt 0 view .LVU69
 174 0070 5668     		ldr	r6, [r2, #4]
 175 0072 16F0020F 		tst	r6, #2
 176 0076 25D0     		beq	.L10
 275:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   {
 276:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     regval |= maskline;
 177              		.loc 1 276 5 is_stmt 1 view .LVU70
 178              		.loc 1 276 12 is_stmt 0 view .LVU71
 179 0078 1C43     		orrs	r4, r4, r3
 180              	.LVL19:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc8WlH0D.s 			page 9


 181              	.L11:
 277:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   }
 278:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   else
 279:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   {
 280:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     regval &= ~maskline;
 281:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   }
 282:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 283:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   /* Store event mode */
 284:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   *regaddr = regval;
 182              		.loc 1 284 3 is_stmt 1 view .LVU72
 183              		.loc 1 284 12 is_stmt 0 view .LVU73
 184 007a 4451     		str	r4, [r0, r5]
 285:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 286:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** #if defined (DUAL_CORE)
 287:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   /* Configure interrupt mode for Core2 : read current mode */
 288:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   regaddr = (__IO uint32_t *)(&EXTI->C2IMR1 + (EXTI_MODE_OFFSET * offset));
 289:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   regval = *regaddr;
 290:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 291:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   /* Mask or set line */
 292:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   if ((pExtiConfig->Mode & EXTI_MODE_CORE2_INTERRUPT) != 0x00U)
 293:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   {
 294:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     regval |= maskline;
 295:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   }
 296:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   else
 297:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   {
 298:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     regval &= ~maskline;
 299:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   }
 300:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 301:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   /* Store interrupt mode */
 302:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   *regaddr = regval;
 303:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 304:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   /* The event mode cannot be configured if the line does not support it */
 305:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   assert_param(((pExtiConfig->Line & EXTI_EVENT) == EXTI_EVENT) || ((pExtiConfig->Mode & EXTI_MODE_
 306:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 307:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   /* Configure event mode : read current mode */
 308:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   regaddr = (__IO uint32_t *)(&EXTI->C2EMR1 + (EXTI_MODE_OFFSET * offset));
 309:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   regval = *regaddr;
 310:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 311:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   /* Mask or set line */
 312:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   if ((pExtiConfig->Mode & EXTI_MODE_CORE2_EVENT) != 0x00U)
 313:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   {
 314:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     regval |= maskline;
 315:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   }
 316:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   else
 317:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   {
 318:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     regval &= ~maskline;
 319:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   }
 320:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 321:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   /* Store event mode */
 322:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   *regaddr = regval;
 323:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** #endif /* DUAL_CORE */
 324:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 325:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   /* Configure the D3 PendClear source in case of Wakeup target is Any */
 326:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   if ((pExtiConfig->Line & EXTI_TARGET_MASK) == EXTI_TARGET_MSK_ALL)
 185              		.loc 1 326 3 is_stmt 1 view .LVU74
 186              		.loc 1 326 19 is_stmt 0 view .LVU75
 187 007c 1068     		ldr	r0, [r2]
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc8WlH0D.s 			page 10


 188              	.LVL20:
 189              		.loc 1 326 26 view .LVU76
 190 007e 00F44010 		and	r0, r0, #3145728
 191              		.loc 1 326 6 view .LVU77
 192 0082 B0F5401F 		cmp	r0, #3145728
 193 0086 20D0     		beq	.L22
 327:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   {
 328:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     assert_param(IS_EXTI_D3_PENDCLR_SRC(pExtiConfig->PendClearSource));
 329:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 330:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     /*Calc the PMR register address for the given line */
 331:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     regaddr = (__IO uint32_t *)(&EXTI->D3PMR1 + (EXTI_CONFIG_OFFSET * offset));
 332:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     regval = *regaddr;
 333:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 334:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     if(pExtiConfig->PendClearSource == EXTI_D3_PENDCLR_SRC_NONE)
 335:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     {
 336:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       /* Clear D3PMRx register for the given line */
 337:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       regval &= ~maskline;
 338:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       /* Store D3PMRx register value */
 339:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       *regaddr = regval;
 340:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     }
 341:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     else
 342:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     {
 343:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       /* Set D3PMRx register to 1 for the given line */
 344:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       regval |= maskline;
 345:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       /* Store D3PMRx register value */
 346:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       *regaddr = regval;
 347:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 348:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       if(linepos < 16UL)
 349:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       {
 350:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****         regaddr = (__IO uint32_t *)(&EXTI->D3PCR1L + (EXTI_CONFIG_OFFSET * offset));
 351:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****         pcrlinepos = 1UL << linepos;
 352:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       }
 353:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       else
 354:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       {
 355:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****         regaddr = (__IO uint32_t *)(&EXTI->D3PCR1H + (EXTI_CONFIG_OFFSET * offset));
 356:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****         pcrlinepos = 1UL << (linepos - 16UL);
 357:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       }
 358:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 359:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       regval = (*regaddr & (~(pcrlinepos * pcrlinepos * 3UL))) | (pcrlinepos * pcrlinepos * (pExtiC
 360:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       *regaddr = regval;
 361:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     }
 362:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   }
 363:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 364:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   return HAL_OK;
 194              		.loc 1 364 10 view .LVU78
 195 0088 0020     		movs	r0, #0
 196              	.LVL21:
 197              	.L2:
 365:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** }
 198              		.loc 1 365 1 view .LVU79
 199 008a F0BD     		pop	{r4, r5, r6, r7, pc}
 200              	.LVL22:
 201              	.L4:
 213:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     }
 202              		.loc 1 213 7 is_stmt 1 view .LVU80
 213:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     }
 203              		.loc 1 213 14 is_stmt 0 view .LVU81
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc8WlH0D.s 			page 11


 204 008c 24EA0304 		bic	r4, r4, r3
 205              	.LVL23:
 213:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     }
 206              		.loc 1 213 14 view .LVU82
 207 0090 D3E7     		b	.L5
 208              	.LVL24:
 209              	.L6:
 230:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     }
 210              		.loc 1 230 7 is_stmt 1 view .LVU83
 230:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     }
 211              		.loc 1 230 14 is_stmt 0 view .LVU84
 212 0092 24EA0304 		bic	r4, r4, r3
 213              	.LVL25:
 230:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     }
 214              		.loc 1 230 14 view .LVU85
 215 0096 D8E7     		b	.L7
 216              	.LVL26:
 217              	.L21:
 239:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       assert_param(IS_EXTI_GPIO_PIN(linepos));
 218              		.loc 1 239 7 is_stmt 1 view .LVU86
 240:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 219              		.loc 1 240 7 view .LVU87
 242:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       regval &= ~(SYSCFG_EXTICR1_EXTI0 << (SYSCFG_EXTICR1_EXTI1_Pos * (linepos & 0x03U)));
 220              		.loc 1 242 7 view .LVU88
 242:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       regval &= ~(SYSCFG_EXTICR1_EXTI0 << (SYSCFG_EXTICR1_EXTI1_Pos * (linepos & 0x03U)));
 221              		.loc 1 242 47 is_stmt 0 view .LVU89
 222 0098 CEF38100 		ubfx	r0, lr, #2, #2
 242:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       regval &= ~(SYSCFG_EXTICR1_EXTI0 << (SYSCFG_EXTICR1_EXTI1_Pos * (linepos & 0x03U)));
 223              		.loc 1 242 14 view .LVU90
 224 009c 254F     		ldr	r7, .L23+12
 225 009e 0230     		adds	r0, r0, #2
 226 00a0 57F82060 		ldr	r6, [r7, r0, lsl #2]
 227              	.LVL27:
 243:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       regval |= (pExtiConfig->GPIOSel << (SYSCFG_EXTICR1_EXTI1_Pos * (linepos & 0x03U)));
 228              		.loc 1 243 7 is_stmt 1 view .LVU91
 243:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       regval |= (pExtiConfig->GPIOSel << (SYSCFG_EXTICR1_EXTI1_Pos * (linepos & 0x03U)));
 229              		.loc 1 243 80 is_stmt 0 view .LVU92
 230 00a4 01F00305 		and	r5, r1, #3
 243:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       regval |= (pExtiConfig->GPIOSel << (SYSCFG_EXTICR1_EXTI1_Pos * (linepos & 0x03U)));
 231              		.loc 1 243 69 view .LVU93
 232 00a8 AD00     		lsls	r5, r5, #2
 243:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       regval |= (pExtiConfig->GPIOSel << (SYSCFG_EXTICR1_EXTI1_Pos * (linepos & 0x03U)));
 233              		.loc 1 243 40 view .LVU94
 234 00aa 0F24     		movs	r4, #15
 235 00ac AC40     		lsls	r4, r4, r5
 243:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       regval |= (pExtiConfig->GPIOSel << (SYSCFG_EXTICR1_EXTI1_Pos * (linepos & 0x03U)));
 236              		.loc 1 243 14 view .LVU95
 237 00ae 26EA0406 		bic	r6, r6, r4
 238              	.LVL28:
 244:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       SYSCFG->EXTICR[(linepos >> 2U) & 0x03UL] = regval;
 239              		.loc 1 244 7 is_stmt 1 view .LVU96
 244:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       SYSCFG->EXTICR[(linepos >> 2U) & 0x03UL] = regval;
 240              		.loc 1 244 29 is_stmt 0 view .LVU97
 241 00b2 D468     		ldr	r4, [r2, #12]
 244:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       SYSCFG->EXTICR[(linepos >> 2U) & 0x03UL] = regval;
 242              		.loc 1 244 39 view .LVU98
 243 00b4 AC40     		lsls	r4, r4, r5
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc8WlH0D.s 			page 12


 244:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       SYSCFG->EXTICR[(linepos >> 2U) & 0x03UL] = regval;
 244              		.loc 1 244 14 view .LVU99
 245 00b6 3443     		orrs	r4, r4, r6
 246              	.LVL29:
 245:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     }
 247              		.loc 1 245 7 is_stmt 1 view .LVU100
 245:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     }
 248              		.loc 1 245 48 is_stmt 0 view .LVU101
 249 00b8 47F82040 		str	r4, [r7, r0, lsl #2]
 250 00bc CCE7     		b	.L3
 251              	.LVL30:
 252              	.L8:
 260:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   }
 253              		.loc 1 260 5 is_stmt 1 view .LVU102
 260:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   }
 254              		.loc 1 260 12 is_stmt 0 view .LVU103
 255 00be 24EA0304 		bic	r4, r4, r3
 256              	.LVL31:
 260:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   }
 257              		.loc 1 260 12 view .LVU104
 258 00c2 D2E7     		b	.L9
 259              	.LVL32:
 260              	.L10:
 280:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   }
 261              		.loc 1 280 5 is_stmt 1 view .LVU105
 280:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   }
 262              		.loc 1 280 12 is_stmt 0 view .LVU106
 263 00c4 24EA0304 		bic	r4, r4, r3
 264              	.LVL33:
 280:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   }
 265              		.loc 1 280 12 view .LVU107
 266 00c8 D7E7     		b	.L11
 267              	.LVL34:
 268              	.L22:
 328:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 269              		.loc 1 328 5 is_stmt 1 view .LVU108
 331:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     regval = *regaddr;
 270              		.loc 1 331 5 view .LVU109
 331:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     regval = *regaddr;
 271              		.loc 1 331 15 is_stmt 0 view .LVU110
 272 00ca 4FEA4C1C 		lsl	ip, ip, #5
 273              	.LVL35:
 331:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     regval = *regaddr;
 274              		.loc 1 331 13 view .LVU111
 275 00ce 1A4C     		ldr	r4, .L23+16
 276              	.LVL36:
 332:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 277              		.loc 1 332 5 is_stmt 1 view .LVU112
 332:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 278              		.loc 1 332 12 is_stmt 0 view .LVU113
 279 00d0 5CF80400 		ldr	r0, [ip, r4]
 280              	.LVL37:
 334:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     {
 281              		.loc 1 334 5 is_stmt 1 view .LVU114
 334:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     {
 282              		.loc 1 334 19 is_stmt 0 view .LVU115
 283 00d4 1569     		ldr	r5, [r2, #16]
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc8WlH0D.s 			page 13


 334:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     {
 284              		.loc 1 334 7 view .LVU116
 285 00d6 2DB9     		cbnz	r5, .L12
 337:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       /* Store D3PMRx register value */
 286              		.loc 1 337 7 is_stmt 1 view .LVU117
 337:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       /* Store D3PMRx register value */
 287              		.loc 1 337 14 is_stmt 0 view .LVU118
 288 00d8 20EA0300 		bic	r0, r0, r3
 289              	.LVL38:
 339:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     }
 290              		.loc 1 339 7 is_stmt 1 view .LVU119
 339:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     }
 291              		.loc 1 339 16 is_stmt 0 view .LVU120
 292 00dc 4CF80400 		str	r0, [ip, r4]
 364:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** }
 293              		.loc 1 364 10 view .LVU121
 294 00e0 0020     		movs	r0, #0
 295              	.LVL39:
 364:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** }
 296              		.loc 1 364 10 view .LVU122
 297 00e2 D2E7     		b	.L2
 298              	.LVL40:
 299              	.L12:
 344:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       /* Store D3PMRx register value */
 300              		.loc 1 344 7 is_stmt 1 view .LVU123
 344:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       /* Store D3PMRx register value */
 301              		.loc 1 344 14 is_stmt 0 view .LVU124
 302 00e4 1843     		orrs	r0, r0, r3
 303              	.LVL41:
 346:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 304              		.loc 1 346 7 is_stmt 1 view .LVU125
 346:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 305              		.loc 1 346 16 is_stmt 0 view .LVU126
 306 00e6 4CF80400 		str	r0, [ip, r4]
 348:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       {
 307              		.loc 1 348 7 is_stmt 1 view .LVU127
 348:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       {
 308              		.loc 1 348 9 is_stmt 0 view .LVU128
 309 00ea 11F0100F 		tst	r1, #16
 310 00ee 10D1     		bne	.L13
 350:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****         pcrlinepos = 1UL << linepos;
 311              		.loc 1 350 9 is_stmt 1 view .LVU129
 350:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****         pcrlinepos = 1UL << linepos;
 312              		.loc 1 350 17 is_stmt 0 view .LVU130
 313 00f0 1248     		ldr	r0, .L23+20
 314              	.LVL42:
 350:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****         pcrlinepos = 1UL << linepos;
 315              		.loc 1 350 17 view .LVU131
 316 00f2 6044     		add	r0, r0, ip
 317              	.LVL43:
 351:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       }
 318              		.loc 1 351 9 is_stmt 1 view .LVU132
 319              	.L14:
 359:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       *regaddr = regval;
 320              		.loc 1 359 7 view .LVU133
 359:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       *regaddr = regval;
 321              		.loc 1 359 17 is_stmt 0 view .LVU134
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc8WlH0D.s 			page 14


 322 00f4 0168     		ldr	r1, [r0]
 323              	.LVL44:
 359:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       *regaddr = regval;
 324              		.loc 1 359 42 view .LVU135
 325 00f6 03FB03F3 		mul	r3, r3, r3
 326              	.LVL45:
 359:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       *regaddr = regval;
 327              		.loc 1 359 55 view .LVU136
 328 00fa 03EB4304 		add	r4, r3, r3, lsl #1
 329              	.LVL46:
 359:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       *regaddr = regval;
 330              		.loc 1 359 26 view .LVU137
 331 00fe 21EA0401 		bic	r1, r1, r4
 359:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       *regaddr = regval;
 332              		.loc 1 359 105 view .LVU138
 333 0102 1269     		ldr	r2, [r2, #16]
 334              	.LVL47:
 359:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       *regaddr = regval;
 335              		.loc 1 359 123 view .LVU139
 336 0104 013A     		subs	r2, r2, #1
 359:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       *regaddr = regval;
 337              		.loc 1 359 91 view .LVU140
 338 0106 02FB03F3 		mul	r3, r2, r3
 359:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       *regaddr = regval;
 339              		.loc 1 359 14 view .LVU141
 340 010a 0B43     		orrs	r3, r3, r1
 341              	.LVL48:
 360:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     }
 342              		.loc 1 360 7 is_stmt 1 view .LVU142
 360:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     }
 343              		.loc 1 360 16 is_stmt 0 view .LVU143
 344 010c 0360     		str	r3, [r0]
 364:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** }
 345              		.loc 1 364 10 view .LVU144
 346 010e 0020     		movs	r0, #0
 347              	.LVL49:
 364:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** }
 348              		.loc 1 364 10 view .LVU145
 349 0110 BBE7     		b	.L2
 350              	.LVL50:
 351              	.L13:
 355:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****         pcrlinepos = 1UL << (linepos - 16UL);
 352              		.loc 1 355 9 is_stmt 1 view .LVU146
 355:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****         pcrlinepos = 1UL << (linepos - 16UL);
 353              		.loc 1 355 17 is_stmt 0 view .LVU147
 354 0112 0B48     		ldr	r0, .L23+24
 355              	.LVL51:
 355:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****         pcrlinepos = 1UL << (linepos - 16UL);
 356              		.loc 1 355 17 view .LVU148
 357 0114 6044     		add	r0, r0, ip
 358              	.LVL52:
 356:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       }
 359              		.loc 1 356 9 is_stmt 1 view .LVU149
 356:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       }
 360              		.loc 1 356 38 is_stmt 0 view .LVU150
 361 0116 AEF1100E 		sub	lr, lr, #16
 362              	.LVL53:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc8WlH0D.s 			page 15


 356:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       }
 363              		.loc 1 356 20 view .LVU151
 364 011a 0123     		movs	r3, #1
 365              	.LVL54:
 356:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       }
 366              		.loc 1 356 20 view .LVU152
 367 011c 03FA0EF3 		lsl	r3, r3, lr
 368              	.LVL55:
 356:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       }
 369              		.loc 1 356 20 view .LVU153
 370 0120 E8E7     		b	.L14
 371              	.LVL56:
 372              	.L15:
 373              	.LCFI1:
 374              		.cfi_def_cfa_offset 0
 375              		.cfi_restore 4
 376              		.cfi_restore 5
 377              		.cfi_restore 6
 378              		.cfi_restore 7
 379              		.cfi_restore 14
 182:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   }
 380              		.loc 1 182 12 view .LVU154
 381 0122 0120     		movs	r0, #1
 382              	.LVL57:
 383              		.loc 1 365 1 view .LVU155
 384 0124 7047     		bx	lr
 385              	.L24:
 386 0126 00BF     		.align	2
 387              	.L23:
 388 0128 04000058 		.word	1476395012
 389 012c 80000058 		.word	1476395136
 390 0130 84000058 		.word	1476395140
 391 0134 00040058 		.word	1476396032
 392 0138 0C000058 		.word	1476395020
 393 013c 10000058 		.word	1476395024
 394 0140 14000058 		.word	1476395028
 395              		.cfi_endproc
 396              	.LFE144:
 398              		.section	.text.HAL_EXTI_GetConfigLine,"ax",%progbits
 399              		.align	1
 400              		.global	HAL_EXTI_GetConfigLine
 401              		.syntax unified
 402              		.thumb
 403              		.thumb_func
 405              	HAL_EXTI_GetConfigLine:
 406              	.LVL58:
 407              	.LFB145:
 366:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 367:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 368:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** /**
 369:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   * @brief  Get configuration of a dedicated Exti line.
 370:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   * @param  hexti Exti handle.
 371:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   * @param  pExtiConfig Pointer on structure to store Exti configuration.
 372:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   * @retval HAL Status.
 373:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   */
 374:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** HAL_StatusTypeDef HAL_EXTI_GetConfigLine(EXTI_HandleTypeDef *hexti, EXTI_ConfigTypeDef *pExtiConfig
 375:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** {
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc8WlH0D.s 			page 16


 408              		.loc 1 375 1 is_stmt 1 view -0
 409              		.cfi_startproc
 410              		@ args = 0, pretend = 0, frame = 0
 411              		@ frame_needed = 0, uses_anonymous_args = 0
 412              		@ link register save eliminated.
 376:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   __IO uint32_t *regaddr;
 413              		.loc 1 376 3 view .LVU157
 377:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   uint32_t regval;
 414              		.loc 1 377 3 view .LVU158
 378:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   uint32_t linepos;
 415              		.loc 1 378 3 view .LVU159
 379:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   uint32_t maskline;
 416              		.loc 1 379 3 view .LVU160
 380:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   uint32_t offset;
 417              		.loc 1 380 3 view .LVU161
 381:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   uint32_t pcrlinepos;
 418              		.loc 1 381 3 view .LVU162
 382:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 383:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   /* Check null pointer */
 384:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   if ((hexti == NULL) || (pExtiConfig == NULL))
 419              		.loc 1 384 3 view .LVU163
 420              		.loc 1 384 6 is_stmt 0 view .LVU164
 421 0000 0029     		cmp	r1, #0
 422 0002 18BF     		it	ne
 423 0004 0028     		cmpne	r0, #0
 424 0006 6DD0     		beq	.L34
 375:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   __IO uint32_t *regaddr;
 425              		.loc 1 375 1 view .LVU165
 426 0008 70B4     		push	{r4, r5, r6}
 427              	.LCFI2:
 428              		.cfi_def_cfa_offset 12
 429              		.cfi_offset 4, -12
 430              		.cfi_offset 5, -8
 431              		.cfi_offset 6, -4
 432 000a 0B46     		mov	r3, r1
 385:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   {
 386:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     return HAL_ERROR;
 387:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   }
 388:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 389:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   /* Check the parameter */
 390:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   assert_param(IS_EXTI_LINE(hexti->Line));
 433              		.loc 1 390 3 is_stmt 1 view .LVU166
 391:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 392:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   /* Store handle line number to configuration structure */
 393:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   pExtiConfig->Line = hexti->Line;
 434              		.loc 1 393 3 view .LVU167
 435              		.loc 1 393 28 is_stmt 0 view .LVU168
 436 000c 0168     		ldr	r1, [r0]
 437              	.LVL59:
 438              		.loc 1 393 21 view .LVU169
 439 000e 1960     		str	r1, [r3]
 394:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 395:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   /* compute line register offset and line mask */
 396:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   offset = ((pExtiConfig->Line & EXTI_REG_MASK) >> EXTI_REG_SHIFT);
 440              		.loc 1 396 3 is_stmt 1 view .LVU170
 441              		.loc 1 396 10 is_stmt 0 view .LVU171
 442 0010 C1F30140 		ubfx	r0, r1, #16, #2
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc8WlH0D.s 			page 17


 443              	.LVL60:
 397:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   linepos = (pExtiConfig->Line & EXTI_PIN_MASK);
 444              		.loc 1 397 3 is_stmt 1 view .LVU172
 445              		.loc 1 397 11 is_stmt 0 view .LVU173
 446 0014 01F01F0C 		and	ip, r1, #31
 447              	.LVL61:
 398:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   maskline = (1UL << linepos);
 448              		.loc 1 398 3 is_stmt 1 view .LVU174
 449              		.loc 1 398 12 is_stmt 0 view .LVU175
 450 0018 0122     		movs	r2, #1
 451 001a 02FA0CF2 		lsl	r2, r2, ip
 452              	.LVL62:
 399:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 400:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   /* 1] Get core mode : interrupt */
 401:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   regaddr = (__IO uint32_t *)(&EXTI->IMR1 + (EXTI_MODE_OFFSET * offset));
 453              		.loc 1 401 3 is_stmt 1 view .LVU176
 454              		.loc 1 401 13 is_stmt 0 view .LVU177
 455 001e 0401     		lsls	r4, r0, #4
 456              		.loc 1 401 11 view .LVU178
 457 0020 324D     		ldr	r5, .L43
 458              	.LVL63:
 402:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   regval = *regaddr;
 459              		.loc 1 402 3 is_stmt 1 view .LVU179
 460              		.loc 1 402 10 is_stmt 0 view .LVU180
 461 0022 6559     		ldr	r5, [r4, r5]
 462              	.LVL64:
 403:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 404:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   pExtiConfig->Mode = EXTI_MODE_NONE;
 463              		.loc 1 404 3 is_stmt 1 view .LVU181
 464              		.loc 1 404 21 is_stmt 0 view .LVU182
 465 0024 0026     		movs	r6, #0
 466 0026 5E60     		str	r6, [r3, #4]
 405:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 406:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   /* Check if selected line is enable */
 407:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   if ((regval & maskline) != 0x00U)
 467              		.loc 1 407 3 is_stmt 1 view .LVU183
 468              		.loc 1 407 6 is_stmt 0 view .LVU184
 469 0028 2A42     		tst	r2, r5
 470 002a 01D0     		beq	.L27
 408:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   {
 409:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     pExtiConfig->Mode = EXTI_MODE_INTERRUPT;
 471              		.loc 1 409 5 is_stmt 1 view .LVU185
 472              		.loc 1 409 23 is_stmt 0 view .LVU186
 473 002c 0125     		movs	r5, #1
 474              	.LVL65:
 475              		.loc 1 409 23 view .LVU187
 476 002e 5D60     		str	r5, [r3, #4]
 477              	.L27:
 410:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   }
 411:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 412:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   /* Get event mode */
 413:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   regaddr = (__IO uint32_t *)(&EXTI->EMR1 + (EXTI_MODE_OFFSET * offset));
 478              		.loc 1 413 3 is_stmt 1 view .LVU188
 479              		.loc 1 413 11 is_stmt 0 view .LVU189
 480 0030 2F4D     		ldr	r5, .L43+4
 481              	.LVL66:
 414:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   regval = *regaddr;
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc8WlH0D.s 			page 18


 482              		.loc 1 414 3 is_stmt 1 view .LVU190
 483              		.loc 1 414 10 is_stmt 0 view .LVU191
 484 0032 6459     		ldr	r4, [r4, r5]
 485              	.LVL67:
 415:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 416:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   /* Check if selected line is enable */
 417:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   if ((regval & maskline) != 0x00U)
 486              		.loc 1 417 3 is_stmt 1 view .LVU192
 487              		.loc 1 417 6 is_stmt 0 view .LVU193
 488 0034 2242     		tst	r2, r4
 489 0036 03D0     		beq	.L28
 418:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   {
 419:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     pExtiConfig->Mode |= EXTI_MODE_EVENT;
 490              		.loc 1 419 5 is_stmt 1 view .LVU194
 491              		.loc 1 419 16 is_stmt 0 view .LVU195
 492 0038 5C68     		ldr	r4, [r3, #4]
 493              	.LVL68:
 494              		.loc 1 419 23 view .LVU196
 495 003a 44F00204 		orr	r4, r4, #2
 496 003e 5C60     		str	r4, [r3, #4]
 497              	.L28:
 420:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   }
 421:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** #if defined (DUAL_CORE)
 422:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   regaddr = (__IO uint32_t *)(&EXTI->C2IMR1 + (EXTI_MODE_OFFSET * offset));
 423:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   regval = *regaddr;
 424:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 425:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   /* Check if selected line is enable */
 426:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   if ((regval & maskline) != 0x00U)
 427:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   {
 428:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     pExtiConfig->Mode = EXTI_MODE_CORE2_INTERRUPT;
 429:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   }
 430:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 431:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   /* Get event mode */
 432:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   regaddr = (__IO uint32_t *)(&EXTI->C2EMR1 + (EXTI_MODE_OFFSET * offset));
 433:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   regval = *regaddr;
 434:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 435:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   /* Check if selected line is enable */
 436:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   if ((regval & maskline) != 0x00U)
 437:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   {
 438:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     pExtiConfig->Mode |= EXTI_MODE_CORE2_EVENT;
 439:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   }
 440:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** #endif /*DUAL_CORE*/
 441:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 442:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   /* Get default Trigger and GPIOSel configuration */
 443:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   pExtiConfig->Trigger = EXTI_TRIGGER_NONE;
 498              		.loc 1 443 3 is_stmt 1 view .LVU197
 499              		.loc 1 443 24 is_stmt 0 view .LVU198
 500 0040 0024     		movs	r4, #0
 501 0042 9C60     		str	r4, [r3, #8]
 444:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   pExtiConfig->GPIOSel = 0x00U;
 502              		.loc 1 444 3 is_stmt 1 view .LVU199
 503              		.loc 1 444 24 is_stmt 0 view .LVU200
 504 0044 DC60     		str	r4, [r3, #12]
 445:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 446:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   /* 2] Get trigger for configurable lines : rising */
 447:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   if ((pExtiConfig->Line & EXTI_CONFIG) != 0x00U)
 505              		.loc 1 447 3 is_stmt 1 view .LVU201
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc8WlH0D.s 			page 19


 506              		.loc 1 447 6 is_stmt 0 view .LVU202
 507 0046 11F0007F 		tst	r1, #33554432
 508 004a 14D0     		beq	.L29
 448:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   {
 449:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     regaddr = (__IO uint32_t *)(&EXTI->RTSR1 + (EXTI_CONFIG_OFFSET * offset));
 509              		.loc 1 449 5 is_stmt 1 view .LVU203
 510              		.loc 1 449 15 is_stmt 0 view .LVU204
 511 004c 4401     		lsls	r4, r0, #5
 512              		.loc 1 449 13 view .LVU205
 513 004e 04F1B045 		add	r5, r4, #1476395008
 514              	.LVL69:
 450:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     regval = *regaddr;
 515              		.loc 1 450 5 is_stmt 1 view .LVU206
 516              		.loc 1 450 12 is_stmt 0 view .LVU207
 517 0052 2D68     		ldr	r5, [r5]
 518              	.LVL70:
 451:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 452:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     /* Check if configuration of selected line is enable */
 453:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     if ((regval & maskline) != 0x00U)
 519              		.loc 1 453 5 is_stmt 1 view .LVU208
 520              		.loc 1 453 8 is_stmt 0 view .LVU209
 521 0054 2A42     		tst	r2, r5
 522 0056 01D0     		beq	.L30
 454:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     {
 455:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       pExtiConfig->Trigger = EXTI_TRIGGER_RISING;
 523              		.loc 1 455 7 is_stmt 1 view .LVU210
 524              		.loc 1 455 28 is_stmt 0 view .LVU211
 525 0058 0125     		movs	r5, #1
 526              	.LVL71:
 527              		.loc 1 455 28 view .LVU212
 528 005a 9D60     		str	r5, [r3, #8]
 529              	.L30:
 456:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     }
 457:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 458:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     /* Get falling configuration */
 459:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     regaddr = (__IO uint32_t *)(&EXTI->FTSR1 + (EXTI_CONFIG_OFFSET * offset));
 530              		.loc 1 459 5 is_stmt 1 view .LVU213
 531              		.loc 1 459 13 is_stmt 0 view .LVU214
 532 005c 254D     		ldr	r5, .L43+8
 533              	.LVL72:
 460:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     regval = *regaddr;
 534              		.loc 1 460 5 is_stmt 1 view .LVU215
 535              		.loc 1 460 12 is_stmt 0 view .LVU216
 536 005e 6459     		ldr	r4, [r4, r5]
 537              	.LVL73:
 461:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 462:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     /* Check if configuration of selected line is enable */
 463:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     if ((regval & maskline) != 0x00U)
 538              		.loc 1 463 5 is_stmt 1 view .LVU217
 539              		.loc 1 463 8 is_stmt 0 view .LVU218
 540 0060 2242     		tst	r2, r4
 541 0062 03D0     		beq	.L31
 464:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     {
 465:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       pExtiConfig->Trigger |= EXTI_TRIGGER_FALLING;
 542              		.loc 1 465 7 is_stmt 1 view .LVU219
 543              		.loc 1 465 18 is_stmt 0 view .LVU220
 544 0064 9C68     		ldr	r4, [r3, #8]
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc8WlH0D.s 			page 20


 545              	.LVL74:
 546              		.loc 1 465 28 view .LVU221
 547 0066 44F00204 		orr	r4, r4, #2
 548 006a 9C60     		str	r4, [r3, #8]
 549              	.L31:
 466:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     }
 467:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 468:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     /* Get Gpio port selection for gpio lines */
 469:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     if ((pExtiConfig->Line & EXTI_GPIO) == EXTI_GPIO)
 550              		.loc 1 469 5 is_stmt 1 view .LVU222
 551              		.loc 1 469 28 is_stmt 0 view .LVU223
 552 006c 01F0C064 		and	r4, r1, #100663296
 553              		.loc 1 469 8 view .LVU224
 554 0070 B4F1C06F 		cmp	r4, #100663296
 555 0074 09D0     		beq	.L41
 556              	.LVL75:
 557              	.L29:
 470:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     {
 471:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       assert_param(IS_EXTI_GPIO_PIN(linepos));
 472:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 473:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       regval = SYSCFG->EXTICR[(linepos >> 2U) & 0x03UL];
 474:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       pExtiConfig->GPIOSel = (regval >> (SYSCFG_EXTICR1_EXTI1_Pos * (linepos & 0x03u))) & SYSCFG_EX
 475:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     }
 476:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   }
 477:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 478:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   /* Get default Pend Clear Source */
 479:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   pExtiConfig->PendClearSource = EXTI_D3_PENDCLR_SRC_NONE;
 558              		.loc 1 479 3 is_stmt 1 view .LVU225
 559              		.loc 1 479 32 is_stmt 0 view .LVU226
 560 0076 0024     		movs	r4, #0
 561 0078 1C61     		str	r4, [r3, #16]
 480:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 481:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   /* 3] Get D3 Pend Clear source */
 482:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   if ((pExtiConfig->Line & EXTI_TARGET_MASK) == EXTI_TARGET_MSK_ALL)
 562              		.loc 1 482 3 is_stmt 1 view .LVU227
 563              		.loc 1 482 26 is_stmt 0 view .LVU228
 564 007a 01F44014 		and	r4, r1, #3145728
 565              		.loc 1 482 6 view .LVU229
 566 007e B4F5401F 		cmp	r4, #3145728
 567 0082 10D0     		beq	.L42
 483:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   {
 484:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     regaddr = (__IO uint32_t *)(&EXTI->D3PMR1 + (EXTI_CONFIG_OFFSET * offset));
 485:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     if(((*regaddr) & linepos) != 0UL)
 486:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     {
 487:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       /* if wakeup target is any and PMR set, the read pend clear source from  D3PCRxL/H */
 488:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       if(linepos < 16UL)
 489:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       {
 490:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****         regaddr = (__IO uint32_t *)(&EXTI->D3PCR1L + (EXTI_CONFIG_OFFSET * offset));
 491:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****         pcrlinepos = 1UL << linepos;
 492:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       }
 493:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       else
 494:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       {
 495:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****         regaddr = (__IO uint32_t *)(&EXTI->D3PCR1H + (EXTI_CONFIG_OFFSET * offset));
 496:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****         pcrlinepos = 1UL << (linepos - 16UL);
 497:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       }
 498:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 499:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       pExtiConfig->PendClearSource = 1UL + ((*regaddr & (pcrlinepos * pcrlinepos * 3UL)) / (pcrline
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc8WlH0D.s 			page 21


 500:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     }
 501:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   }
 502:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 503:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   return HAL_OK;
 568              		.loc 1 503 10 view .LVU230
 569 0084 0020     		movs	r0, #0
 570              	.LVL76:
 571              	.L26:
 504:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** }
 572              		.loc 1 504 1 view .LVU231
 573 0086 70BC     		pop	{r4, r5, r6}
 574              	.LCFI3:
 575              		.cfi_remember_state
 576              		.cfi_restore 6
 577              		.cfi_restore 5
 578              		.cfi_restore 4
 579              		.cfi_def_cfa_offset 0
 580 0088 7047     		bx	lr
 581              	.LVL77:
 582              	.L41:
 583              	.LCFI4:
 584              		.cfi_restore_state
 471:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 585              		.loc 1 471 7 is_stmt 1 view .LVU232
 473:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       pExtiConfig->GPIOSel = (regval >> (SYSCFG_EXTICR1_EXTI1_Pos * (linepos & 0x03u))) & SYSCFG_EX
 586              		.loc 1 473 7 view .LVU233
 473:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       pExtiConfig->GPIOSel = (regval >> (SYSCFG_EXTICR1_EXTI1_Pos * (linepos & 0x03u))) & SYSCFG_EX
 587              		.loc 1 473 47 is_stmt 0 view .LVU234
 588 008a CCF38104 		ubfx	r4, ip, #2, #2
 473:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       pExtiConfig->GPIOSel = (regval >> (SYSCFG_EXTICR1_EXTI1_Pos * (linepos & 0x03u))) & SYSCFG_EX
 589              		.loc 1 473 14 view .LVU235
 590 008e 0234     		adds	r4, r4, #2
 591 0090 194D     		ldr	r5, .L43+12
 592              	.LVL78:
 473:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       pExtiConfig->GPIOSel = (regval >> (SYSCFG_EXTICR1_EXTI1_Pos * (linepos & 0x03u))) & SYSCFG_EX
 593              		.loc 1 473 14 view .LVU236
 594 0092 55F82440 		ldr	r4, [r5, r4, lsl #2]
 595              	.LVL79:
 474:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     }
 596              		.loc 1 474 7 is_stmt 1 view .LVU237
 474:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     }
 597              		.loc 1 474 78 is_stmt 0 view .LVU238
 598 0096 01F00305 		and	r5, r1, #3
 474:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     }
 599              		.loc 1 474 67 view .LVU239
 600 009a AD00     		lsls	r5, r5, #2
 474:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     }
 601              		.loc 1 474 38 view .LVU240
 602 009c EC40     		lsrs	r4, r4, r5
 603              	.LVL80:
 474:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     }
 604              		.loc 1 474 89 view .LVU241
 605 009e 04F00F04 		and	r4, r4, #15
 474:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     }
 606              		.loc 1 474 28 view .LVU242
 607 00a2 DC60     		str	r4, [r3, #12]
 608 00a4 E7E7     		b	.L29
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc8WlH0D.s 			page 22


 609              	.LVL81:
 610              	.L42:
 484:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     if(((*regaddr) & linepos) != 0UL)
 611              		.loc 1 484 5 is_stmt 1 view .LVU243
 484:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     if(((*regaddr) & linepos) != 0UL)
 612              		.loc 1 484 15 is_stmt 0 view .LVU244
 613 00a6 4001     		lsls	r0, r0, #5
 614              	.LVL82:
 484:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     if(((*regaddr) & linepos) != 0UL)
 615              		.loc 1 484 13 view .LVU245
 616 00a8 144C     		ldr	r4, .L43+16
 617              	.LVL83:
 485:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     {
 618              		.loc 1 485 5 is_stmt 1 view .LVU246
 485:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     {
 619              		.loc 1 485 10 is_stmt 0 view .LVU247
 620 00aa 0459     		ldr	r4, [r0, r4]
 621              	.LVL84:
 485:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     {
 622              		.loc 1 485 7 view .LVU248
 623 00ac 14EA0C0F 		tst	r4, ip
 624 00b0 1AD0     		beq	.L36
 488:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       {
 625              		.loc 1 488 7 is_stmt 1 view .LVU249
 488:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       {
 626              		.loc 1 488 9 is_stmt 0 view .LVU250
 627 00b2 11F0100F 		tst	r1, #16
 628 00b6 0DD1     		bne	.L32
 490:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****         pcrlinepos = 1UL << linepos;
 629              		.loc 1 490 9 is_stmt 1 view .LVU251
 490:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****         pcrlinepos = 1UL << linepos;
 630              		.loc 1 490 17 is_stmt 0 view .LVU252
 631 00b8 1149     		ldr	r1, .L43+20
 632              	.LVL85:
 490:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****         pcrlinepos = 1UL << linepos;
 633              		.loc 1 490 17 view .LVU253
 634 00ba 0144     		add	r1, r1, r0
 635              	.LVL86:
 491:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       }
 636              		.loc 1 491 9 is_stmt 1 view .LVU254
 637              	.L33:
 499:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     }
 638              		.loc 1 499 7 view .LVU255
 499:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     }
 639              		.loc 1 499 46 is_stmt 0 view .LVU256
 640 00bc 0968     		ldr	r1, [r1]
 641              	.LVL87:
 499:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     }
 642              		.loc 1 499 69 view .LVU257
 643 00be 02FB02F2 		mul	r2, r2, r2
 644              	.LVL88:
 499:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     }
 645              		.loc 1 499 82 view .LVU258
 646 00c2 02EB4200 		add	r0, r2, r2, lsl #1
 499:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     }
 647              		.loc 1 499 55 view .LVU259
 648 00c6 0140     		ands	r1, r1, r0
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc8WlH0D.s 			page 23


 499:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     }
 649              		.loc 1 499 90 view .LVU260
 650 00c8 B1FBF2F2 		udiv	r2, r1, r2
 499:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     }
 651              		.loc 1 499 42 view .LVU261
 652 00cc 0132     		adds	r2, r2, #1
 499:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     }
 653              		.loc 1 499 36 view .LVU262
 654 00ce 1A61     		str	r2, [r3, #16]
 503:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** }
 655              		.loc 1 503 10 view .LVU263
 656 00d0 0020     		movs	r0, #0
 657 00d2 D8E7     		b	.L26
 658              	.LVL89:
 659              	.L32:
 495:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****         pcrlinepos = 1UL << (linepos - 16UL);
 660              		.loc 1 495 9 is_stmt 1 view .LVU264
 495:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****         pcrlinepos = 1UL << (linepos - 16UL);
 661              		.loc 1 495 17 is_stmt 0 view .LVU265
 662 00d4 0B49     		ldr	r1, .L43+24
 663              	.LVL90:
 495:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****         pcrlinepos = 1UL << (linepos - 16UL);
 664              		.loc 1 495 17 view .LVU266
 665 00d6 0144     		add	r1, r1, r0
 666              	.LVL91:
 496:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       }
 667              		.loc 1 496 9 is_stmt 1 view .LVU267
 496:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       }
 668              		.loc 1 496 38 is_stmt 0 view .LVU268
 669 00d8 ACF1100C 		sub	ip, ip, #16
 670              	.LVL92:
 496:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       }
 671              		.loc 1 496 20 view .LVU269
 672 00dc 0122     		movs	r2, #1
 673              	.LVL93:
 496:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       }
 674              		.loc 1 496 20 view .LVU270
 675 00de 02FA0CF2 		lsl	r2, r2, ip
 676              	.LVL94:
 496:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       }
 677              		.loc 1 496 20 view .LVU271
 678 00e2 EBE7     		b	.L33
 679              	.LVL95:
 680              	.L34:
 681              	.LCFI5:
 682              		.cfi_def_cfa_offset 0
 683              		.cfi_restore 4
 684              		.cfi_restore 5
 685              		.cfi_restore 6
 386:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   }
 686              		.loc 1 386 12 view .LVU272
 687 00e4 0120     		movs	r0, #1
 688              	.LVL96:
 689              		.loc 1 504 1 view .LVU273
 690 00e6 7047     		bx	lr
 691              	.LVL97:
 692              	.L36:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc8WlH0D.s 			page 24


 693              	.LCFI6:
 694              		.cfi_def_cfa_offset 12
 695              		.cfi_offset 4, -12
 696              		.cfi_offset 5, -8
 697              		.cfi_offset 6, -4
 503:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** }
 698              		.loc 1 503 10 view .LVU274
 699 00e8 0020     		movs	r0, #0
 700              	.LVL98:
 503:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** }
 701              		.loc 1 503 10 view .LVU275
 702 00ea CCE7     		b	.L26
 703              	.L44:
 704              		.align	2
 705              	.L43:
 706 00ec 80000058 		.word	1476395136
 707 00f0 84000058 		.word	1476395140
 708 00f4 04000058 		.word	1476395012
 709 00f8 00040058 		.word	1476396032
 710 00fc 0C000058 		.word	1476395020
 711 0100 10000058 		.word	1476395024
 712 0104 14000058 		.word	1476395028
 713              		.cfi_endproc
 714              	.LFE145:
 716              		.section	.text.HAL_EXTI_ClearConfigLine,"ax",%progbits
 717              		.align	1
 718              		.global	HAL_EXTI_ClearConfigLine
 719              		.syntax unified
 720              		.thumb
 721              		.thumb_func
 723              	HAL_EXTI_ClearConfigLine:
 724              	.LVL99:
 725              	.LFB146:
 505:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 506:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 507:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** /**
 508:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   * @brief  Clear whole configuration of a dedicated Exti line.
 509:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   * @param  hexti Exti handle.
 510:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   * @retval HAL Status.
 511:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   */
 512:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** HAL_StatusTypeDef HAL_EXTI_ClearConfigLine(const EXTI_HandleTypeDef *hexti)
 513:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** {
 726              		.loc 1 513 1 is_stmt 1 view -0
 727              		.cfi_startproc
 728              		@ args = 0, pretend = 0, frame = 0
 729              		@ frame_needed = 0, uses_anonymous_args = 0
 514:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   __IO uint32_t *regaddr;
 730              		.loc 1 514 3 view .LVU277
 515:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   uint32_t regval;
 731              		.loc 1 515 3 view .LVU278
 516:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   uint32_t linepos;
 732              		.loc 1 516 3 view .LVU279
 517:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   uint32_t maskline;
 733              		.loc 1 517 3 view .LVU280
 518:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   uint32_t offset;
 734              		.loc 1 518 3 view .LVU281
 519:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   uint32_t pcrlinepos;
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc8WlH0D.s 			page 25


 735              		.loc 1 519 3 view .LVU282
 520:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 521:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   /* Check null pointer */
 522:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   if (hexti == NULL)
 736              		.loc 1 522 3 view .LVU283
 737              		.loc 1 522 6 is_stmt 0 view .LVU284
 738 0000 0028     		cmp	r0, #0
 739 0002 65D0     		beq	.L50
 513:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   __IO uint32_t *regaddr;
 740              		.loc 1 513 1 view .LVU285
 741 0004 2DE9F041 		push	{r4, r5, r6, r7, r8, lr}
 742              	.LCFI7:
 743              		.cfi_def_cfa_offset 24
 744              		.cfi_offset 4, -24
 745              		.cfi_offset 5, -20
 746              		.cfi_offset 6, -16
 747              		.cfi_offset 7, -12
 748              		.cfi_offset 8, -8
 749              		.cfi_offset 14, -4
 750 0008 0146     		mov	r1, r0
 523:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   {
 524:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     return HAL_ERROR;
 525:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   }
 526:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 527:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   /* Check the parameter */
 528:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   assert_param(IS_EXTI_LINE(hexti->Line));
 751              		.loc 1 528 3 is_stmt 1 view .LVU286
 529:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 530:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   /* compute line register offset and line mask */
 531:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   offset = ((hexti->Line & EXTI_REG_MASK) >> EXTI_REG_SHIFT);
 752              		.loc 1 531 3 view .LVU287
 753              		.loc 1 531 19 is_stmt 0 view .LVU288
 754 000a 0568     		ldr	r5, [r0]
 755              		.loc 1 531 10 view .LVU289
 756 000c C5F30140 		ubfx	r0, r5, #16, #2
 757              	.LVL100:
 532:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   linepos = (hexti->Line & EXTI_PIN_MASK);
 758              		.loc 1 532 3 is_stmt 1 view .LVU290
 759              		.loc 1 532 11 is_stmt 0 view .LVU291
 760 0010 05F01F0E 		and	lr, r5, #31
 761              	.LVL101:
 533:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   maskline = (1UL << linepos);
 762              		.loc 1 533 3 is_stmt 1 view .LVU292
 763              		.loc 1 533 12 is_stmt 0 view .LVU293
 764 0014 0123     		movs	r3, #1
 765 0016 03FA0EF3 		lsl	r3, r3, lr
 766              	.LVL102:
 534:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 535:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   /* 1] Clear interrupt mode */
 536:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   regaddr = (__IO uint32_t *)(&EXTI->IMR1 + (EXTI_MODE_OFFSET * offset));
 767              		.loc 1 536 3 is_stmt 1 view .LVU294
 768              		.loc 1 536 13 is_stmt 0 view .LVU295
 769 001a 0201     		lsls	r2, r0, #4
 770              		.loc 1 536 11 view .LVU296
 771 001c 2D4E     		ldr	r6, .L58
 772              	.LVL103:
 537:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   regval = (*regaddr & ~maskline);
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc8WlH0D.s 			page 26


 773              		.loc 1 537 3 is_stmt 1 view .LVU297
 774              		.loc 1 537 13 is_stmt 0 view .LVU298
 775 001e 9459     		ldr	r4, [r2, r6]
 776              		.loc 1 537 24 view .LVU299
 777 0020 6FEA030C 		mvn	ip, r3
 778              		.loc 1 537 10 view .LVU300
 779 0024 24EA0304 		bic	r4, r4, r3
 780              	.LVL104:
 538:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   *regaddr = regval;
 781              		.loc 1 538 3 is_stmt 1 view .LVU301
 782              		.loc 1 538 12 is_stmt 0 view .LVU302
 783 0028 9451     		str	r4, [r2, r6]
 539:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 540:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   /* 2] Clear event mode */
 541:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   regaddr = (__IO uint32_t *)(&EXTI->EMR1 + (EXTI_MODE_OFFSET * offset));
 784              		.loc 1 541 3 is_stmt 1 view .LVU303
 785              		.loc 1 541 11 is_stmt 0 view .LVU304
 786 002a 0436     		adds	r6, r6, #4
 787              	.LVL105:
 542:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   regval = (*regaddr & ~maskline);
 788              		.loc 1 542 3 is_stmt 1 view .LVU305
 789              		.loc 1 542 13 is_stmt 0 view .LVU306
 790 002c 9459     		ldr	r4, [r2, r6]
 791              	.LVL106:
 792              		.loc 1 542 10 view .LVU307
 793 002e 24EA0304 		bic	r4, r4, r3
 794              	.LVL107:
 543:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   *regaddr = regval;
 795              		.loc 1 543 3 is_stmt 1 view .LVU308
 796              		.loc 1 543 12 is_stmt 0 view .LVU309
 797 0032 9451     		str	r4, [r2, r6]
 544:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 545:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** #if defined (DUAL_CORE)
 546:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     /* 1] Clear CM4 interrupt mode */
 547:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   regaddr = (__IO uint32_t *)(&EXTI->C2IMR1 + (EXTI_MODE_OFFSET * offset));
 548:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   regval = (*regaddr & ~maskline);
 549:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   *regaddr = regval;
 550:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 551:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   /* 2] Clear CM4 event mode */
 552:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   regaddr = (__IO uint32_t *)(&EXTI->C2EMR1 + (EXTI_MODE_OFFSET * offset));
 553:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   regval = (*regaddr & ~maskline);
 554:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   *regaddr = regval;
 555:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** #endif /* DUAL_CORE */
 556:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 557:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   /* 3] Clear triggers in case of configurable lines */
 558:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   if ((hexti->Line & EXTI_CONFIG) != 0x00U)
 798              		.loc 1 558 3 is_stmt 1 view .LVU310
 799              		.loc 1 558 13 is_stmt 0 view .LVU311
 800 0034 0A68     		ldr	r2, [r1]
 801              	.LVL108:
 802              		.loc 1 558 6 view .LVU312
 803 0036 12F0007F 		tst	r2, #33554432
 804 003a 11D0     		beq	.L47
 559:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   {
 560:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     regaddr = (__IO uint32_t *)(&EXTI->RTSR1 + (EXTI_CONFIG_OFFSET * offset));
 805              		.loc 1 560 5 is_stmt 1 view .LVU313
 806              		.loc 1 560 15 is_stmt 0 view .LVU314
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc8WlH0D.s 			page 27


 807 003c 4201     		lsls	r2, r0, #5
 808              		.loc 1 560 13 view .LVU315
 809 003e 02F1B046 		add	r6, r2, #1476395008
 810              	.LVL109:
 561:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     regval = (*regaddr & ~maskline);
 811              		.loc 1 561 5 is_stmt 1 view .LVU316
 812              		.loc 1 561 15 is_stmt 0 view .LVU317
 813 0042 3468     		ldr	r4, [r6]
 814              	.LVL110:
 815              		.loc 1 561 12 view .LVU318
 816 0044 0CEA0404 		and	r4, ip, r4
 817              	.LVL111:
 562:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     *regaddr = regval;
 818              		.loc 1 562 5 is_stmt 1 view .LVU319
 819              		.loc 1 562 14 is_stmt 0 view .LVU320
 820 0048 3460     		str	r4, [r6]
 563:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 564:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     regaddr = (__IO uint32_t *)(&EXTI->FTSR1 + (EXTI_CONFIG_OFFSET * offset));
 821              		.loc 1 564 5 is_stmt 1 view .LVU321
 822              		.loc 1 564 13 is_stmt 0 view .LVU322
 823 004a 234E     		ldr	r6, .L58+4
 824              	.LVL112:
 565:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     regval = (*regaddr & ~maskline);
 825              		.loc 1 565 5 is_stmt 1 view .LVU323
 826              		.loc 1 565 15 is_stmt 0 view .LVU324
 827 004c 9459     		ldr	r4, [r2, r6]
 828              	.LVL113:
 829              		.loc 1 565 12 view .LVU325
 830 004e 0CEA0404 		and	r4, ip, r4
 831              	.LVL114:
 566:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     *regaddr = regval;
 832              		.loc 1 566 5 is_stmt 1 view .LVU326
 833              		.loc 1 566 14 is_stmt 0 view .LVU327
 834 0052 9451     		str	r4, [r2, r6]
 567:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 568:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     /* Get Gpio port selection for gpio lines */
 569:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     if ((hexti->Line & EXTI_GPIO) == EXTI_GPIO)
 835              		.loc 1 569 5 is_stmt 1 view .LVU328
 836              		.loc 1 569 15 is_stmt 0 view .LVU329
 837 0054 0A68     		ldr	r2, [r1]
 838              	.LVL115:
 839              		.loc 1 569 22 view .LVU330
 840 0056 02F0C062 		and	r2, r2, #100663296
 841              		.loc 1 569 8 view .LVU331
 842 005a B2F1C06F 		cmp	r2, #100663296
 843 005e 08D0     		beq	.L56
 844              	.LVL116:
 845              	.L47:
 570:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     {
 571:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       assert_param(IS_EXTI_GPIO_PIN(linepos));
 572:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 573:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       regval = SYSCFG->EXTICR[(linepos >> 2U) & 0x03UL];
 574:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       regval &= ~(SYSCFG_EXTICR1_EXTI0 << (SYSCFG_EXTICR1_EXTI1_Pos * (linepos & 0x03UL)));
 575:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       SYSCFG->EXTICR[(linepos >> 2U) & 0x03UL] = regval;
 576:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     }
 577:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   }
 578:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc8WlH0D.s 			page 28


 579:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   /* 4] Clear D3 Config lines */
 580:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   if ((hexti->Line & EXTI_TARGET_MASK) == EXTI_TARGET_MSK_ALL)
 846              		.loc 1 580 3 is_stmt 1 view .LVU332
 847              		.loc 1 580 13 is_stmt 0 view .LVU333
 848 0060 0A68     		ldr	r2, [r1]
 849              		.loc 1 580 20 view .LVU334
 850 0062 02F44012 		and	r2, r2, #3145728
 851              		.loc 1 580 6 view .LVU335
 852 0066 B2F5401F 		cmp	r2, #3145728
 853 006a 14D0     		beq	.L57
 581:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   {
 582:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     regaddr = (__IO uint32_t *)(&EXTI->D3PMR1 + (EXTI_CONFIG_OFFSET * offset));
 583:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     *regaddr = (*regaddr & ~maskline);
 584:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 585:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     if(linepos < 16UL)
 586:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     {
 587:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       regaddr = (__IO uint32_t *)(&EXTI->D3PCR1L + (EXTI_CONFIG_OFFSET * offset));
 588:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       pcrlinepos = 1UL << linepos;
 589:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     }
 590:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     else
 591:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     {
 592:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       regaddr = (__IO uint32_t *)(&EXTI->D3PCR1H + (EXTI_CONFIG_OFFSET * offset));
 593:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       pcrlinepos = 1UL << (linepos - 16UL);
 594:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     }
 595:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 596:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     /*Clear D3 PendClear source */
 597:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     *regaddr &= (~(pcrlinepos * pcrlinepos * 3UL));
 598:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   }
 599:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 600:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   return HAL_OK;
 854              		.loc 1 600 10 view .LVU336
 855 006c 0020     		movs	r0, #0
 856              	.LVL117:
 857              	.L46:
 601:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** }
 858              		.loc 1 601 1 view .LVU337
 859 006e BDE8F081 		pop	{r4, r5, r6, r7, r8, pc}
 860              	.LVL118:
 861              	.L56:
 571:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 862              		.loc 1 571 7 is_stmt 1 view .LVU338
 573:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       regval &= ~(SYSCFG_EXTICR1_EXTI0 << (SYSCFG_EXTICR1_EXTI1_Pos * (linepos & 0x03UL)));
 863              		.loc 1 573 7 view .LVU339
 573:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       regval &= ~(SYSCFG_EXTICR1_EXTI0 << (SYSCFG_EXTICR1_EXTI1_Pos * (linepos & 0x03UL)));
 864              		.loc 1 573 47 is_stmt 0 view .LVU340
 865 0072 CEF38102 		ubfx	r2, lr, #2, #2
 573:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       regval &= ~(SYSCFG_EXTICR1_EXTI0 << (SYSCFG_EXTICR1_EXTI1_Pos * (linepos & 0x03UL)));
 866              		.loc 1 573 14 view .LVU341
 867 0076 194F     		ldr	r7, .L58+8
 868 0078 0232     		adds	r2, r2, #2
 869 007a 57F82240 		ldr	r4, [r7, r2, lsl #2]
 870              	.LVL119:
 574:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       SYSCFG->EXTICR[(linepos >> 2U) & 0x03UL] = regval;
 871              		.loc 1 574 7 is_stmt 1 view .LVU342
 574:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       SYSCFG->EXTICR[(linepos >> 2U) & 0x03UL] = regval;
 872              		.loc 1 574 80 is_stmt 0 view .LVU343
 873 007e 05F00308 		and	r8, r5, #3
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc8WlH0D.s 			page 29


 574:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       SYSCFG->EXTICR[(linepos >> 2U) & 0x03UL] = regval;
 874              		.loc 1 574 69 view .LVU344
 875 0082 4FEA8808 		lsl	r8, r8, #2
 574:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       SYSCFG->EXTICR[(linepos >> 2U) & 0x03UL] = regval;
 876              		.loc 1 574 40 view .LVU345
 877 0086 0F26     		movs	r6, #15
 878              	.LVL120:
 574:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       SYSCFG->EXTICR[(linepos >> 2U) & 0x03UL] = regval;
 879              		.loc 1 574 40 view .LVU346
 880 0088 06FA08F6 		lsl	r6, r6, r8
 574:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       SYSCFG->EXTICR[(linepos >> 2U) & 0x03UL] = regval;
 881              		.loc 1 574 14 view .LVU347
 882 008c 24EA0604 		bic	r4, r4, r6
 883              	.LVL121:
 575:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     }
 884              		.loc 1 575 7 is_stmt 1 view .LVU348
 575:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     }
 885              		.loc 1 575 48 is_stmt 0 view .LVU349
 886 0090 47F82240 		str	r4, [r7, r2, lsl #2]
 887 0094 E4E7     		b	.L47
 888              	.LVL122:
 889              	.L57:
 582:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     *regaddr = (*regaddr & ~maskline);
 890              		.loc 1 582 5 is_stmt 1 view .LVU350
 582:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     *regaddr = (*regaddr & ~maskline);
 891              		.loc 1 582 15 is_stmt 0 view .LVU351
 892 0096 4001     		lsls	r0, r0, #5
 893              	.LVL123:
 582:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     *regaddr = (*regaddr & ~maskline);
 894              		.loc 1 582 13 view .LVU352
 895 0098 1149     		ldr	r1, .L58+12
 896              	.LVL124:
 583:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 897              		.loc 1 583 5 is_stmt 1 view .LVU353
 583:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 898              		.loc 1 583 17 is_stmt 0 view .LVU354
 899 009a 4258     		ldr	r2, [r0, r1]
 583:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 900              		.loc 1 583 26 view .LVU355
 901 009c 0CEA0202 		and	r2, ip, r2
 583:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 902              		.loc 1 583 14 view .LVU356
 903 00a0 4250     		str	r2, [r0, r1]
 585:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     {
 904              		.loc 1 585 5 is_stmt 1 view .LVU357
 585:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     {
 905              		.loc 1 585 7 is_stmt 0 view .LVU358
 906 00a2 15F0100F 		tst	r5, #16
 907 00a6 0BD1     		bne	.L48
 587:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       pcrlinepos = 1UL << linepos;
 908              		.loc 1 587 7 is_stmt 1 view .LVU359
 587:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       pcrlinepos = 1UL << linepos;
 909              		.loc 1 587 15 is_stmt 0 view .LVU360
 910 00a8 0E4A     		ldr	r2, .L58+16
 911 00aa 0244     		add	r2, r2, r0
 912              	.LVL125:
 588:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     }
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc8WlH0D.s 			page 30


 913              		.loc 1 588 7 is_stmt 1 view .LVU361
 914              	.L49:
 597:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   }
 915              		.loc 1 597 5 view .LVU362
 916 00ac 1168     		ldr	r1, [r2]
 597:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   }
 917              		.loc 1 597 31 is_stmt 0 view .LVU363
 918 00ae 03FB03F3 		mul	r3, r3, r3
 919              	.LVL126:
 597:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   }
 920              		.loc 1 597 44 view .LVU364
 921 00b2 03EB4303 		add	r3, r3, r3, lsl #1
 597:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   }
 922              		.loc 1 597 14 view .LVU365
 923 00b6 21EA0303 		bic	r3, r1, r3
 924 00ba 1360     		str	r3, [r2]
 600:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** }
 925              		.loc 1 600 10 view .LVU366
 926 00bc 0020     		movs	r0, #0
 927 00be D6E7     		b	.L46
 928              	.LVL127:
 929              	.L48:
 592:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       pcrlinepos = 1UL << (linepos - 16UL);
 930              		.loc 1 592 7 is_stmt 1 view .LVU367
 592:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       pcrlinepos = 1UL << (linepos - 16UL);
 931              		.loc 1 592 15 is_stmt 0 view .LVU368
 932 00c0 094A     		ldr	r2, .L58+20
 933 00c2 0244     		add	r2, r2, r0
 934              	.LVL128:
 593:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     }
 935              		.loc 1 593 7 is_stmt 1 view .LVU369
 593:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     }
 936              		.loc 1 593 36 is_stmt 0 view .LVU370
 937 00c4 AEF1100E 		sub	lr, lr, #16
 938              	.LVL129:
 593:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     }
 939              		.loc 1 593 18 view .LVU371
 940 00c8 0123     		movs	r3, #1
 941              	.LVL130:
 593:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     }
 942              		.loc 1 593 18 view .LVU372
 943 00ca 03FA0EF3 		lsl	r3, r3, lr
 944              	.LVL131:
 593:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     }
 945              		.loc 1 593 18 view .LVU373
 946 00ce EDE7     		b	.L49
 947              	.LVL132:
 948              	.L50:
 949              	.LCFI8:
 950              		.cfi_def_cfa_offset 0
 951              		.cfi_restore 4
 952              		.cfi_restore 5
 953              		.cfi_restore 6
 954              		.cfi_restore 7
 955              		.cfi_restore 8
 956              		.cfi_restore 14
 524:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   }
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc8WlH0D.s 			page 31


 957              		.loc 1 524 12 view .LVU374
 958 00d0 0120     		movs	r0, #1
 959              	.LVL133:
 960              		.loc 1 601 1 view .LVU375
 961 00d2 7047     		bx	lr
 962              	.L59:
 963              		.align	2
 964              	.L58:
 965 00d4 80000058 		.word	1476395136
 966 00d8 04000058 		.word	1476395012
 967 00dc 00040058 		.word	1476396032
 968 00e0 0C000058 		.word	1476395020
 969 00e4 10000058 		.word	1476395024
 970 00e8 14000058 		.word	1476395028
 971              		.cfi_endproc
 972              	.LFE146:
 974              		.section	.text.HAL_EXTI_RegisterCallback,"ax",%progbits
 975              		.align	1
 976              		.global	HAL_EXTI_RegisterCallback
 977              		.syntax unified
 978              		.thumb
 979              		.thumb_func
 981              	HAL_EXTI_RegisterCallback:
 982              	.LVL134:
 983              	.LFB147:
 602:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 603:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 604:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** /**
 605:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   * @brief  Register callback for a dedicated Exti line.
 606:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   * @param  hexti Exti handle.
 607:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   * @param  CallbackID User callback identifier.
 608:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   *         This parameter can be one of @arg @ref EXTI_CallbackIDTypeDef values.
 609:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   * @param  pPendingCbfn function pointer to be stored as callback.
 610:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   * @retval HAL Status.
 611:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   */
 612:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** HAL_StatusTypeDef HAL_EXTI_RegisterCallback(EXTI_HandleTypeDef *hexti, EXTI_CallbackIDTypeDef Callb
 613:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** {
 984              		.loc 1 613 1 is_stmt 1 view -0
 985              		.cfi_startproc
 986              		@ args = 0, pretend = 0, frame = 0
 987              		@ frame_needed = 0, uses_anonymous_args = 0
 988              		@ link register save eliminated.
 614:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   HAL_StatusTypeDef status = HAL_OK;
 989              		.loc 1 614 3 view .LVU377
 615:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 616:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   /* Check null pointer */
 617:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   if (hexti == NULL)
 990              		.loc 1 617 3 view .LVU378
 991              		.loc 1 617 6 is_stmt 0 view .LVU379
 992 0000 18B1     		cbz	r0, .L62
 618:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   {
 619:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     return HAL_ERROR;
 620:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   }
 621:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 622:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   switch (CallbackID)
 993              		.loc 1 622 3 is_stmt 1 view .LVU380
 994 0002 21B9     		cbnz	r1, .L63
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc8WlH0D.s 			page 32


 623:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   {
 624:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     case  HAL_EXTI_COMMON_CB_ID:
 625:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       hexti->PendingCallback = pPendingCbfn;
 995              		.loc 1 625 7 view .LVU381
 996              		.loc 1 625 30 is_stmt 0 view .LVU382
 997 0004 4260     		str	r2, [r0, #4]
 626:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       break;
 998              		.loc 1 626 7 is_stmt 1 view .LVU383
 614:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 999              		.loc 1 614 21 is_stmt 0 view .LVU384
 1000 0006 0846     		mov	r0, r1
 1001              	.LVL135:
 1002              		.loc 1 626 7 view .LVU385
 1003 0008 7047     		bx	lr
 1004              	.LVL136:
 1005              	.L62:
 619:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   }
 1006              		.loc 1 619 12 view .LVU386
 1007 000a 0120     		movs	r0, #1
 1008              	.LVL137:
 619:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   }
 1009              		.loc 1 619 12 view .LVU387
 1010 000c 7047     		bx	lr
 1011              	.LVL138:
 1012              	.L63:
 627:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 628:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     default:
 629:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       status = HAL_ERROR;
 1013              		.loc 1 629 14 view .LVU388
 1014 000e 0120     		movs	r0, #1
 1015              	.LVL139:
 630:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       break;
 631:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   }
 632:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 633:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   return status;
 634:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** }
 1016              		.loc 1 634 1 view .LVU389
 1017 0010 7047     		bx	lr
 1018              		.cfi_endproc
 1019              	.LFE147:
 1021              		.section	.text.HAL_EXTI_GetHandle,"ax",%progbits
 1022              		.align	1
 1023              		.global	HAL_EXTI_GetHandle
 1024              		.syntax unified
 1025              		.thumb
 1026              		.thumb_func
 1028              	HAL_EXTI_GetHandle:
 1029              	.LVL140:
 1030              	.LFB148:
 635:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 636:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 637:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** /**
 638:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   * @brief  Store line number as handle private field.
 639:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   * @param  hexti Exti handle.
 640:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   * @param  ExtiLine Exti line number.
 641:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   *         This parameter can be from 0 to @ref EXTI_LINE_NB.
 642:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   * @retval HAL Status.
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc8WlH0D.s 			page 33


 643:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   */
 644:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** HAL_StatusTypeDef HAL_EXTI_GetHandle(EXTI_HandleTypeDef *hexti, uint32_t ExtiLine)
 645:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** {
 1031              		.loc 1 645 1 is_stmt 1 view -0
 1032              		.cfi_startproc
 1033              		@ args = 0, pretend = 0, frame = 0
 1034              		@ frame_needed = 0, uses_anonymous_args = 0
 1035              		@ link register save eliminated.
 646:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   /* Check the parameters */
 647:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   assert_param(IS_EXTI_LINE(ExtiLine));
 1036              		.loc 1 647 3 view .LVU391
 648:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 649:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   /* Check null pointer */
 650:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   if (hexti == NULL)
 1037              		.loc 1 650 3 view .LVU392
 1038              		.loc 1 650 6 is_stmt 0 view .LVU393
 1039 0000 10B1     		cbz	r0, .L66
 651:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   {
 652:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     return HAL_ERROR;
 653:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   }
 654:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   else
 655:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   {
 656:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     /* Store line number as handle private field */
 657:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     hexti->Line = ExtiLine;
 1040              		.loc 1 657 5 is_stmt 1 view .LVU394
 1041              		.loc 1 657 17 is_stmt 0 view .LVU395
 1042 0002 0160     		str	r1, [r0]
 658:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 659:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     return HAL_OK;
 1043              		.loc 1 659 5 is_stmt 1 view .LVU396
 1044              		.loc 1 659 12 is_stmt 0 view .LVU397
 1045 0004 0020     		movs	r0, #0
 1046              	.LVL141:
 1047              		.loc 1 659 12 view .LVU398
 1048 0006 7047     		bx	lr
 1049              	.LVL142:
 1050              	.L66:
 652:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   }
 1051              		.loc 1 652 12 view .LVU399
 1052 0008 0120     		movs	r0, #1
 1053              	.LVL143:
 660:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   }
 661:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** }
 1054              		.loc 1 661 1 view .LVU400
 1055 000a 7047     		bx	lr
 1056              		.cfi_endproc
 1057              	.LFE148:
 1059              		.section	.text.HAL_EXTI_IRQHandler,"ax",%progbits
 1060              		.align	1
 1061              		.global	HAL_EXTI_IRQHandler
 1062              		.syntax unified
 1063              		.thumb
 1064              		.thumb_func
 1066              	HAL_EXTI_IRQHandler:
 1067              	.LVL144:
 1068              	.LFB149:
 662:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc8WlH0D.s 			page 34


 663:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 664:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** /**
 665:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   * @}
 666:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   */
 667:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 668:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** /** @addtogroup EXTI_Exported_Functions_Group2
 669:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****  *  @brief EXTI IO functions.
 670:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****  *
 671:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** @verbatim
 672:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****  ===============================================================================
 673:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****                        ##### IO operation functions #####
 674:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****  ===============================================================================
 675:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 676:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** @endverbatim
 677:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   * @{
 678:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   */
 679:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 680:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** /**
 681:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   * @brief  Handle EXTI interrupt request.
 682:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   * @param  hexti Exti handle.
 683:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   * @retval none.
 684:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   */
 685:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** void HAL_EXTI_IRQHandler(const EXTI_HandleTypeDef *hexti)
 686:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** {
 1069              		.loc 1 686 1 is_stmt 1 view -0
 1070              		.cfi_startproc
 1071              		@ args = 0, pretend = 0, frame = 0
 1072              		@ frame_needed = 0, uses_anonymous_args = 0
 1073              		.loc 1 686 1 is_stmt 0 view .LVU402
 1074 0000 10B5     		push	{r4, lr}
 1075              	.LCFI9:
 1076              		.cfi_def_cfa_offset 8
 1077              		.cfi_offset 4, -8
 1078              		.cfi_offset 14, -4
 687:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   __IO uint32_t *regaddr;
 1079              		.loc 1 687 3 is_stmt 1 view .LVU403
 688:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   uint32_t regval;
 1080              		.loc 1 688 3 view .LVU404
 689:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   uint32_t maskline;
 1081              		.loc 1 689 3 view .LVU405
 690:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   uint32_t offset;
 1082              		.loc 1 690 3 view .LVU406
 691:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 692:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   /* Compute line register offset and line mask */
 693:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   offset = ((hexti->Line & EXTI_REG_MASK) >> EXTI_REG_SHIFT);
 1083              		.loc 1 693 3 view .LVU407
 1084              		.loc 1 693 19 is_stmt 0 view .LVU408
 1085 0002 0368     		ldr	r3, [r0]
 1086              		.loc 1 693 10 view .LVU409
 1087 0004 C3F30142 		ubfx	r2, r3, #16, #2
 1088              	.LVL145:
 694:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   maskline = (1UL << (hexti->Line & EXTI_PIN_MASK));
 1089              		.loc 1 694 3 is_stmt 1 view .LVU410
 1090              		.loc 1 694 35 is_stmt 0 view .LVU411
 1091 0008 03F01F03 		and	r3, r3, #31
 1092              		.loc 1 694 12 view .LVU412
 1093 000c 0121     		movs	r1, #1
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc8WlH0D.s 			page 35


 1094 000e 9940     		lsls	r1, r1, r3
 1095              	.LVL146:
 695:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 696:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** #if defined(DUAL_CORE)
 697:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   if (HAL_GetCurrentCPUID() == CM7_CPUID)
 698:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   {
 699:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     /* Get pending register address */
 700:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     regaddr = (__IO uint32_t *)(&EXTI->PR1 + (EXTI_MODE_OFFSET * offset));
 701:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   }
 702:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   else /* Cortex-M4*/
 703:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   {
 704:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     /* Get pending register address */
 705:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     regaddr = (__IO uint32_t *)(&EXTI->C2PR1 + (EXTI_MODE_OFFSET * offset));
 706:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   }
 707:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** #else
 708:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   regaddr = (__IO uint32_t *)(&EXTI->PR1 + (EXTI_MODE_OFFSET * offset));
 1096              		.loc 1 708 3 is_stmt 1 view .LVU413
 1097              		.loc 1 708 13 is_stmt 0 view .LVU414
 1098 0010 1301     		lsls	r3, r2, #4
 1099              		.loc 1 708 11 view .LVU415
 1100 0012 044A     		ldr	r2, .L70
 1101              	.LVL147:
 709:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** #endif /* DUAL_CORE */
 710:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 711:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   /* Get pending bit  */
 712:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   regval = (*regaddr & maskline);
 1102              		.loc 1 712 3 is_stmt 1 view .LVU416
 1103              		.loc 1 712 13 is_stmt 0 view .LVU417
 1104 0014 9C58     		ldr	r4, [r3, r2]
 1105              	.LVL148:
 713:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 714:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   if (regval != 0x00U)
 1106              		.loc 1 714 3 is_stmt 1 view .LVU418
 1107              		.loc 1 714 6 is_stmt 0 view .LVU419
 1108 0016 0C42     		tst	r4, r1
 1109 0018 03D0     		beq	.L67
 715:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   {
 716:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     /* Clear pending bit */
 717:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     *regaddr = maskline;
 1110              		.loc 1 717 5 is_stmt 1 view .LVU420
 1111              		.loc 1 717 14 is_stmt 0 view .LVU421
 1112 001a 9950     		str	r1, [r3, r2]
 1113              	.LVL149:
 718:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 719:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     /* Call callback */
 720:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     if (hexti->PendingCallback != NULL)
 1114              		.loc 1 720 5 is_stmt 1 view .LVU422
 1115              		.loc 1 720 14 is_stmt 0 view .LVU423
 1116 001c 4368     		ldr	r3, [r0, #4]
 1117              	.LVL150:
 1118              		.loc 1 720 8 view .LVU424
 1119 001e 03B1     		cbz	r3, .L67
 721:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     {
 722:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****       hexti->PendingCallback();
 1120              		.loc 1 722 7 is_stmt 1 view .LVU425
 1121 0020 9847     		blx	r3
 1122              	.LVL151:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc8WlH0D.s 			page 36


 1123              	.L67:
 723:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     }
 724:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   }
 725:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** }
 1124              		.loc 1 725 1 is_stmt 0 view .LVU426
 1125 0022 10BD     		pop	{r4, pc}
 1126              	.L71:
 1127              		.loc 1 725 1 view .LVU427
 1128              		.align	2
 1129              	.L70:
 1130 0024 88000058 		.word	1476395144
 1131              		.cfi_endproc
 1132              	.LFE149:
 1134              		.section	.text.HAL_EXTI_GetPending,"ax",%progbits
 1135              		.align	1
 1136              		.global	HAL_EXTI_GetPending
 1137              		.syntax unified
 1138              		.thumb
 1139              		.thumb_func
 1141              	HAL_EXTI_GetPending:
 1142              	.LVL152:
 1143              	.LFB150:
 726:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 727:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 728:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** /**
 729:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   * @brief  Get interrupt pending bit of a dedicated line.
 730:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   * @param  hexti Exti handle.
 731:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   * @param  Edge Specify which pending edge as to be checked.
 732:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   *         This parameter can be one of the following values:
 733:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   *           @arg @ref EXTI_TRIGGER_RISING_FALLING
 734:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   *         This parameter is kept for compatibility with other series.
 735:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   * @retval 1 if interrupt is pending else 0.
 736:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   */
 737:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** uint32_t HAL_EXTI_GetPending(const EXTI_HandleTypeDef *hexti, uint32_t Edge)
 738:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** {
 1144              		.loc 1 738 1 is_stmt 1 view -0
 1145              		.cfi_startproc
 1146              		@ args = 0, pretend = 0, frame = 0
 1147              		@ frame_needed = 0, uses_anonymous_args = 0
 1148              		@ link register save eliminated.
 739:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   const __IO uint32_t *regaddr;
 1149              		.loc 1 739 3 view .LVU429
 740:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   uint32_t regval;
 1150              		.loc 1 740 3 view .LVU430
 741:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   uint32_t linepos;
 1151              		.loc 1 741 3 view .LVU431
 742:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   uint32_t maskline;
 1152              		.loc 1 742 3 view .LVU432
 743:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   uint32_t offset;
 1153              		.loc 1 743 3 view .LVU433
 744:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 745:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   /* Prevent unused argument(s) compilation warning */
 746:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   UNUSED(Edge);
 1154              		.loc 1 746 3 view .LVU434
 747:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 748:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   /* Check parameters */
 749:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   assert_param(IS_EXTI_LINE(hexti->Line));
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc8WlH0D.s 			page 37


 1155              		.loc 1 749 3 view .LVU435
 750:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   assert_param(IS_EXTI_CONFIG_LINE(hexti->Line));
 1156              		.loc 1 750 3 view .LVU436
 751:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   assert_param(IS_EXTI_PENDING_EDGE(Edge));
 1157              		.loc 1 751 3 view .LVU437
 752:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 753:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   /* compute line register offset and line mask */
 754:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   offset = ((hexti->Line & EXTI_REG_MASK) >> EXTI_REG_SHIFT);
 1158              		.loc 1 754 3 view .LVU438
 1159              		.loc 1 754 19 is_stmt 0 view .LVU439
 1160 0000 0368     		ldr	r3, [r0]
 1161              		.loc 1 754 10 view .LVU440
 1162 0002 C3F30141 		ubfx	r1, r3, #16, #2
 1163              	.LVL153:
 755:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   linepos = (hexti->Line & EXTI_PIN_MASK);
 1164              		.loc 1 755 3 is_stmt 1 view .LVU441
 1165              		.loc 1 755 11 is_stmt 0 view .LVU442
 1166 0006 03F01F03 		and	r3, r3, #31
 1167              	.LVL154:
 756:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   maskline = (1UL << linepos);
 1168              		.loc 1 756 3 is_stmt 1 view .LVU443
 1169              		.loc 1 756 12 is_stmt 0 view .LVU444
 1170 000a 0122     		movs	r2, #1
 1171 000c 9A40     		lsls	r2, r2, r3
 1172              	.LVL155:
 757:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 758:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** #if defined(DUAL_CORE)
 759:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   if (HAL_GetCurrentCPUID() == CM7_CPUID)
 760:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   {
 761:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     /* Get pending register address */
 762:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     regaddr = (__IO uint32_t *)(&EXTI->PR1 + (EXTI_MODE_OFFSET * offset));
 763:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   }
 764:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   else /* Cortex-M4 */
 765:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   {
 766:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     /* Get pending register address */
 767:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     regaddr = (__IO uint32_t *)(&EXTI->C2PR1 + (EXTI_MODE_OFFSET * offset));
 768:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   }
 769:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** #else
 770:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   regaddr = (__IO uint32_t *)(&EXTI->PR1 + (EXTI_MODE_OFFSET * offset));
 1173              		.loc 1 770 3 is_stmt 1 view .LVU445
 1174              		.loc 1 770 13 is_stmt 0 view .LVU446
 1175 000e 0901     		lsls	r1, r1, #4
 1176              	.LVL156:
 1177              		.loc 1 770 11 view .LVU447
 1178 0010 0248     		ldr	r0, .L73
 1179              	.LVL157:
 771:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** #endif /* DUAL_CORE */
 772:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 773:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   /* return 1 if bit is set else 0 */
 774:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   regval = ((*regaddr & maskline) >> linepos);
 1180              		.loc 1 774 3 is_stmt 1 view .LVU448
 1181              		.loc 1 774 14 is_stmt 0 view .LVU449
 1182 0012 0858     		ldr	r0, [r1, r0]
 1183              	.LVL158:
 1184              		.loc 1 774 23 view .LVU450
 1185 0014 1040     		ands	r0, r0, r2
 1186              	.LVL159:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc8WlH0D.s 			page 38


 775:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   return regval;
 1187              		.loc 1 775 3 is_stmt 1 view .LVU451
 776:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** }
 1188              		.loc 1 776 1 is_stmt 0 view .LVU452
 1189 0016 D840     		lsrs	r0, r0, r3
 1190              	.LVL160:
 1191              		.loc 1 776 1 view .LVU453
 1192 0018 7047     		bx	lr
 1193              	.L74:
 1194 001a 00BF     		.align	2
 1195              	.L73:
 1196 001c 88000058 		.word	1476395144
 1197              		.cfi_endproc
 1198              	.LFE150:
 1200              		.section	.text.HAL_EXTI_ClearPending,"ax",%progbits
 1201              		.align	1
 1202              		.global	HAL_EXTI_ClearPending
 1203              		.syntax unified
 1204              		.thumb
 1205              		.thumb_func
 1207              	HAL_EXTI_ClearPending:
 1208              	.LVL161:
 1209              	.LFB151:
 777:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 778:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 779:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** /**
 780:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   * @brief  Clear interrupt pending bit of a dedicated line.
 781:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   * @param  hexti Exti handle.
 782:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   * @param  Edge Specify which pending edge as to be clear.
 783:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   *         This parameter can be one of the following values:
 784:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   *           @arg @ref EXTI_TRIGGER_RISING_FALLING
 785:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   *         This parameter is kept for compatibility with other series.
 786:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   * @retval None.
 787:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   */
 788:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** void HAL_EXTI_ClearPending(const EXTI_HandleTypeDef *hexti, uint32_t Edge)
 789:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** {
 1210              		.loc 1 789 1 is_stmt 1 view -0
 1211              		.cfi_startproc
 1212              		@ args = 0, pretend = 0, frame = 0
 1213              		@ frame_needed = 0, uses_anonymous_args = 0
 1214              		@ link register save eliminated.
 790:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   __IO uint32_t *regaddr;
 1215              		.loc 1 790 3 view .LVU455
 791:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   uint32_t maskline;
 1216              		.loc 1 791 3 view .LVU456
 792:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   uint32_t offset;
 1217              		.loc 1 792 3 view .LVU457
 793:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 794:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   /* Prevent unused argument(s) compilation warning */
 795:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   UNUSED(Edge);
 1218              		.loc 1 795 3 view .LVU458
 796:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 797:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   /* Check parameters */
 798:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   assert_param(IS_EXTI_LINE(hexti->Line));
 1219              		.loc 1 798 3 view .LVU459
 799:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   assert_param(IS_EXTI_CONFIG_LINE(hexti->Line));
 1220              		.loc 1 799 3 view .LVU460
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc8WlH0D.s 			page 39


 800:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   assert_param(IS_EXTI_PENDING_EDGE(Edge));
 1221              		.loc 1 800 3 view .LVU461
 801:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 802:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   /* compute line register offset and line mask */
 803:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   offset = ((hexti->Line & EXTI_REG_MASK) >> EXTI_REG_SHIFT);
 1222              		.loc 1 803 3 view .LVU462
 1223              		.loc 1 803 19 is_stmt 0 view .LVU463
 1224 0000 0368     		ldr	r3, [r0]
 1225              		.loc 1 803 10 view .LVU464
 1226 0002 C3F30142 		ubfx	r2, r3, #16, #2
 1227              	.LVL162:
 804:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   maskline = (1UL << (hexti->Line & EXTI_PIN_MASK));
 1228              		.loc 1 804 3 is_stmt 1 view .LVU465
 1229              		.loc 1 804 35 is_stmt 0 view .LVU466
 1230 0006 03F01F03 		and	r3, r3, #31
 1231              		.loc 1 804 12 view .LVU467
 1232 000a 0121     		movs	r1, #1
 1233              	.LVL163:
 1234              		.loc 1 804 12 view .LVU468
 1235 000c 9940     		lsls	r1, r1, r3
 1236              	.LVL164:
 805:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 806:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** #if defined(DUAL_CORE)
 807:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   if (HAL_GetCurrentCPUID() == CM7_CPUID)
 808:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   {
 809:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     /* Get pending register address */
 810:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     regaddr = (__IO uint32_t *)(&EXTI->PR1 + (EXTI_MODE_OFFSET * offset));
 811:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   }
 812:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   else /* Cortex-M4 */
 813:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   {
 814:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     /* Get pending register address */
 815:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****     regaddr = (__IO uint32_t *)(&EXTI->C2PR1 + (EXTI_MODE_OFFSET * offset));
 816:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   }
 817:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** #else
 818:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   regaddr = (__IO uint32_t *)(&EXTI->PR1 + (EXTI_MODE_OFFSET * offset));
 1237              		.loc 1 818 3 is_stmt 1 view .LVU469
 1238              		.loc 1 818 13 is_stmt 0 view .LVU470
 1239 000e 1301     		lsls	r3, r2, #4
 1240              		.loc 1 818 11 view .LVU471
 1241 0010 014A     		ldr	r2, .L76
 1242              	.LVL165:
 819:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** #endif /* DUAL_CORE */
 820:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 821:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   /* Clear Pending bit */
 822:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   *regaddr =  maskline;
 1243              		.loc 1 822 3 is_stmt 1 view .LVU472
 1244              		.loc 1 822 12 is_stmt 0 view .LVU473
 1245 0012 9950     		str	r1, [r3, r2]
 1246              	.LVL166:
 823:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** }
 1247              		.loc 1 823 1 view .LVU474
 1248 0014 7047     		bx	lr
 1249              	.L77:
 1250 0016 00BF     		.align	2
 1251              	.L76:
 1252 0018 88000058 		.word	1476395144
 1253              		.cfi_endproc
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc8WlH0D.s 			page 40


 1254              	.LFE151:
 1256              		.section	.text.HAL_EXTI_GenerateSWI,"ax",%progbits
 1257              		.align	1
 1258              		.global	HAL_EXTI_GenerateSWI
 1259              		.syntax unified
 1260              		.thumb
 1261              		.thumb_func
 1263              	HAL_EXTI_GenerateSWI:
 1264              	.LVL167:
 1265              	.LFB152:
 824:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 825:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** /**
 826:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   * @brief  Generate a software interrupt for a dedicated line.
 827:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   * @param  hexti Exti handle.
 828:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   * @retval None.
 829:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   */
 830:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** void HAL_EXTI_GenerateSWI(const EXTI_HandleTypeDef *hexti)
 831:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** {
 1266              		.loc 1 831 1 is_stmt 1 view -0
 1267              		.cfi_startproc
 1268              		@ args = 0, pretend = 0, frame = 0
 1269              		@ frame_needed = 0, uses_anonymous_args = 0
 1270              		@ link register save eliminated.
 832:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   __IO uint32_t *regaddr;
 1271              		.loc 1 832 3 view .LVU476
 833:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   uint32_t maskline;
 1272              		.loc 1 833 3 view .LVU477
 834:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   uint32_t offset;
 1273              		.loc 1 834 3 view .LVU478
 835:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 836:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   /* Check parameters */
 837:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   assert_param(IS_EXTI_LINE(hexti->Line));
 1274              		.loc 1 837 3 view .LVU479
 838:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   assert_param(IS_EXTI_CONFIG_LINE(hexti->Line));
 1275              		.loc 1 838 3 view .LVU480
 839:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 840:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   /* compute line register offset and line mask */
 841:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   offset = ((hexti->Line & EXTI_REG_MASK) >> EXTI_REG_SHIFT);
 1276              		.loc 1 841 3 view .LVU481
 1277              		.loc 1 841 19 is_stmt 0 view .LVU482
 1278 0000 0368     		ldr	r3, [r0]
 1279              		.loc 1 841 10 view .LVU483
 1280 0002 C3F30142 		ubfx	r2, r3, #16, #2
 1281              	.LVL168:
 842:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   maskline = (1UL << (hexti->Line & EXTI_PIN_MASK));
 1282              		.loc 1 842 3 is_stmt 1 view .LVU484
 1283              		.loc 1 842 35 is_stmt 0 view .LVU485
 1284 0006 03F01F03 		and	r3, r3, #31
 1285              		.loc 1 842 12 view .LVU486
 1286 000a 0121     		movs	r1, #1
 1287 000c 9940     		lsls	r1, r1, r3
 1288              	.LVL169:
 843:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** 
 844:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   regaddr = (__IO uint32_t *)(&EXTI->SWIER1 + (EXTI_CONFIG_OFFSET * offset));
 1289              		.loc 1 844 3 is_stmt 1 view .LVU487
 1290              		.loc 1 844 13 is_stmt 0 view .LVU488
 1291 000e 5301     		lsls	r3, r2, #5
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc8WlH0D.s 			page 41


 1292              		.loc 1 844 11 view .LVU489
 1293 0010 014A     		ldr	r2, .L79
 1294              	.LVL170:
 845:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c ****   *regaddr = maskline;
 1295              		.loc 1 845 3 is_stmt 1 view .LVU490
 1296              		.loc 1 845 12 is_stmt 0 view .LVU491
 1297 0012 9950     		str	r1, [r3, r2]
 1298              	.LVL171:
 846:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c **** }
 1299              		.loc 1 846 1 view .LVU492
 1300 0014 7047     		bx	lr
 1301              	.L80:
 1302 0016 00BF     		.align	2
 1303              	.L79:
 1304 0018 08000058 		.word	1476395016
 1305              		.cfi_endproc
 1306              	.LFE152:
 1308              		.text
 1309              	.Letext0:
 1310              		.file 2 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\../lib/gcc/arm-none-eabi/13.3.1/..
 1311              		.file 3 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\../lib/gcc/arm-none-eabi/13.3.1/..
 1312              		.file 4 "Drivers/CMSIS/Device/ST/STM32H7xx/Include/stm32h723xx.h"
 1313              		.file 5 "Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_def.h"
 1314              		.file 6 "Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_exti.h"
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc8WlH0D.s 			page 42


DEFINED SYMBOLS
                            *ABS*:00000000 stm32h7xx_hal_exti.c
C:\Users\<USER>\AppData\Local\Temp\cc8WlH0D.s:20     .text.HAL_EXTI_SetConfigLine:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cc8WlH0D.s:26     .text.HAL_EXTI_SetConfigLine:00000000 HAL_EXTI_SetConfigLine
C:\Users\<USER>\AppData\Local\Temp\cc8WlH0D.s:388    .text.HAL_EXTI_SetConfigLine:00000128 $d
C:\Users\<USER>\AppData\Local\Temp\cc8WlH0D.s:399    .text.HAL_EXTI_GetConfigLine:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cc8WlH0D.s:405    .text.HAL_EXTI_GetConfigLine:00000000 HAL_EXTI_GetConfigLine
C:\Users\<USER>\AppData\Local\Temp\cc8WlH0D.s:706    .text.HAL_EXTI_GetConfigLine:000000ec $d
C:\Users\<USER>\AppData\Local\Temp\cc8WlH0D.s:717    .text.HAL_EXTI_ClearConfigLine:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cc8WlH0D.s:723    .text.HAL_EXTI_ClearConfigLine:00000000 HAL_EXTI_ClearConfigLine
C:\Users\<USER>\AppData\Local\Temp\cc8WlH0D.s:965    .text.HAL_EXTI_ClearConfigLine:000000d4 $d
C:\Users\<USER>\AppData\Local\Temp\cc8WlH0D.s:975    .text.HAL_EXTI_RegisterCallback:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cc8WlH0D.s:981    .text.HAL_EXTI_RegisterCallback:00000000 HAL_EXTI_RegisterCallback
C:\Users\<USER>\AppData\Local\Temp\cc8WlH0D.s:1022   .text.HAL_EXTI_GetHandle:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cc8WlH0D.s:1028   .text.HAL_EXTI_GetHandle:00000000 HAL_EXTI_GetHandle
C:\Users\<USER>\AppData\Local\Temp\cc8WlH0D.s:1060   .text.HAL_EXTI_IRQHandler:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cc8WlH0D.s:1066   .text.HAL_EXTI_IRQHandler:00000000 HAL_EXTI_IRQHandler
C:\Users\<USER>\AppData\Local\Temp\cc8WlH0D.s:1130   .text.HAL_EXTI_IRQHandler:00000024 $d
C:\Users\<USER>\AppData\Local\Temp\cc8WlH0D.s:1135   .text.HAL_EXTI_GetPending:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cc8WlH0D.s:1141   .text.HAL_EXTI_GetPending:00000000 HAL_EXTI_GetPending
C:\Users\<USER>\AppData\Local\Temp\cc8WlH0D.s:1196   .text.HAL_EXTI_GetPending:0000001c $d
C:\Users\<USER>\AppData\Local\Temp\cc8WlH0D.s:1201   .text.HAL_EXTI_ClearPending:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cc8WlH0D.s:1207   .text.HAL_EXTI_ClearPending:00000000 HAL_EXTI_ClearPending
C:\Users\<USER>\AppData\Local\Temp\cc8WlH0D.s:1252   .text.HAL_EXTI_ClearPending:00000018 $d
C:\Users\<USER>\AppData\Local\Temp\cc8WlH0D.s:1257   .text.HAL_EXTI_GenerateSWI:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cc8WlH0D.s:1263   .text.HAL_EXTI_GenerateSWI:00000000 HAL_EXTI_GenerateSWI
C:\Users\<USER>\AppData\Local\Temp\cc8WlH0D.s:1304   .text.HAL_EXTI_GenerateSWI:00000018 $d

NO UNDEFINED SYMBOLS
