ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccaJPOi1.s 			page 1


   1              		.cpu cortex-m7
   2              		.arch armv7e-m
   3              		.fpu fpv5-d16
   4              		.eabi_attribute 28, 1
   5              		.eabi_attribute 20, 1
   6              		.eabi_attribute 21, 1
   7              		.eabi_attribute 23, 3
   8              		.eabi_attribute 24, 1
   9              		.eabi_attribute 25, 1
  10              		.eabi_attribute 26, 1
  11              		.eabi_attribute 30, 1
  12              		.eabi_attribute 34, 1
  13              		.eabi_attribute 18, 4
  14              		.file	"sys.c"
  15              		.text
  16              	.Ltext0:
  17              		.cfi_sections	.debug_frame
  18              		.file 1 "Middlewares/Third_Party/LwIP/src/core/sys.c"
  19              		.section	.text.sys_msleep,"ax",%progbits
  20              		.align	1
  21              		.global	sys_msleep
  22              		.syntax unified
  23              		.thumb
  24              		.thumb_func
  26              	sys_msleep:
  27              	.LVL0:
  28              	.LFB174:
   1:Middlewares/Third_Party/LwIP/src/core/sys.c **** /**
   2:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * @file
   3:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * lwIP Operating System abstraction
   4:Middlewares/Third_Party/LwIP/src/core/sys.c ****  *
   5:Middlewares/Third_Party/LwIP/src/core/sys.c ****  */
   6:Middlewares/Third_Party/LwIP/src/core/sys.c **** 
   7:Middlewares/Third_Party/LwIP/src/core/sys.c **** /*
   8:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * Copyright (c) 2001-2004 Swedish Institute of Computer Science.
   9:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * All rights reserved.
  10:Middlewares/Third_Party/LwIP/src/core/sys.c ****  *
  11:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * Redistribution and use in source and binary forms, with or without modification,
  12:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * are permitted provided that the following conditions are met:
  13:Middlewares/Third_Party/LwIP/src/core/sys.c ****  *
  14:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * 1. Redistributions of source code must retain the above copyright notice,
  15:Middlewares/Third_Party/LwIP/src/core/sys.c ****  *    this list of conditions and the following disclaimer.
  16:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * 2. Redistributions in binary form must reproduce the above copyright notice,
  17:Middlewares/Third_Party/LwIP/src/core/sys.c ****  *    this list of conditions and the following disclaimer in the documentation
  18:Middlewares/Third_Party/LwIP/src/core/sys.c ****  *    and/or other materials provided with the distribution.
  19:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * 3. The name of the author may not be used to endorse or promote products
  20:Middlewares/Third_Party/LwIP/src/core/sys.c ****  *    derived from this software without specific prior written permission.
  21:Middlewares/Third_Party/LwIP/src/core/sys.c ****  *
  22:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR IMPLIED
  23:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
  24:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT
  25:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * SHALL THE AUTHOR BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
  26:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT
  27:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
  28:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
  29:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
  30:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccaJPOi1.s 			page 2


  31:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * OF SUCH DAMAGE.
  32:Middlewares/Third_Party/LwIP/src/core/sys.c ****  *
  33:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * This file is part of the lwIP TCP/IP stack.
  34:Middlewares/Third_Party/LwIP/src/core/sys.c ****  *
  35:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * Author: Adam Dunkels <<EMAIL>>
  36:Middlewares/Third_Party/LwIP/src/core/sys.c ****  *
  37:Middlewares/Third_Party/LwIP/src/core/sys.c ****  */
  38:Middlewares/Third_Party/LwIP/src/core/sys.c **** 
  39:Middlewares/Third_Party/LwIP/src/core/sys.c **** /**
  40:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * @defgroup sys_layer Porting (system abstraction layer)
  41:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * @ingroup lwip
  42:Middlewares/Third_Party/LwIP/src/core/sys.c ****  *
  43:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * @defgroup sys_os OS abstraction layer
  44:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * @ingroup sys_layer
  45:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * No need to implement functions in this section in NO_SYS mode.
  46:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * The OS-specific code should be implemented in arch/sys_arch.h
  47:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * and sys_arch.c of your port.
  48:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * 
  49:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * The operating system emulation layer provides a common interface
  50:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * between the lwIP code and the underlying operating system kernel. The
  51:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * general idea is that porting lwIP to new architectures requires only
  52:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * small changes to a few header files and a new sys_arch
  53:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * implementation. It is also possible to do a sys_arch implementation
  54:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * that does not rely on any underlying operating system.
  55:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * 
  56:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * The sys_arch provides semaphores, mailboxes and mutexes to lwIP. For the full
  57:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * lwIP functionality, multiple threads support can be implemented in the
  58:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * sys_arch, but this is not required for the basic lwIP
  59:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * functionality. Timer scheduling is implemented in lwIP, but can be implemented
  60:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * by the sys_arch port (LWIP_TIMERS_CUSTOM==1).
  61:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * 
  62:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * In addition to the source file providing the functionality of sys_arch,
  63:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * the OS emulation layer must provide several header files defining
  64:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * macros used throughout lwip.  The files required and the macros they
  65:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * must define are listed below the sys_arch description.
  66:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * 
  67:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * Since lwIP 1.4.0, semaphore, mutexes and mailbox functions are prototyped in a way that
  68:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * allows both using pointers or actual OS structures to be used. This way, memory
  69:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * required for such types can be either allocated in place (globally or on the
  70:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * stack) or on the heap (allocated internally in the "*_new()" functions).
  71:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * 
  72:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * Note:
  73:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * -----
  74:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * Be careful with using mem_malloc() in sys_arch. When malloc() refers to
  75:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * mem_malloc() you can run into a circular function call problem. In mem.c
  76:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * mem_init() tries to allocate a semaphore using mem_malloc, which of course
  77:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * can't be performed when sys_arch uses mem_malloc.
  78:Middlewares/Third_Party/LwIP/src/core/sys.c ****  *
  79:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * @defgroup sys_sem Semaphores
  80:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * @ingroup sys_os
  81:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * Semaphores can be either counting or binary - lwIP works with both
  82:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * kinds.
  83:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * Semaphores are represented by the type "sys_sem_t" which is typedef'd
  84:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * in the sys_arch.h file. Mailboxes are equivalently represented by the
  85:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * type "sys_mbox_t". Mutexes are represented by the type "sys_mutex_t".
  86:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * lwIP does not place any restrictions on how these types are represented
  87:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * internally.
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccaJPOi1.s 			page 3


  88:Middlewares/Third_Party/LwIP/src/core/sys.c ****  *
  89:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * @defgroup sys_mutex Mutexes
  90:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * @ingroup sys_os
  91:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * Mutexes are recommended to correctly handle priority inversion,
  92:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * especially if you use LWIP_CORE_LOCKING .
  93:Middlewares/Third_Party/LwIP/src/core/sys.c ****  *
  94:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * @defgroup sys_mbox Mailboxes
  95:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * @ingroup sys_os
  96:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * Mailboxes should be implemented as a queue which allows multiple messages
  97:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * to be posted (implementing as a rendez-vous point where only one message can be
  98:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * posted at a time can have a highly negative impact on performance). A message
  99:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * in a mailbox is just a pointer, nothing more. 
 100:Middlewares/Third_Party/LwIP/src/core/sys.c ****  *
 101:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * @defgroup sys_time Time
 102:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * @ingroup sys_layer
 103:Middlewares/Third_Party/LwIP/src/core/sys.c ****  *
 104:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * @defgroup sys_prot Critical sections
 105:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * @ingroup sys_layer
 106:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * Used to protect short regions of code against concurrent access.
 107:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * - Your system is a bare-metal system (probably with an RTOS)
 108:Middlewares/Third_Party/LwIP/src/core/sys.c ****  *   and interrupts are under your control:
 109:Middlewares/Third_Party/LwIP/src/core/sys.c ****  *   Implement this as LockInterrupts() / UnlockInterrupts()
 110:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * - Your system uses an RTOS with deferred interrupt handling from a
 111:Middlewares/Third_Party/LwIP/src/core/sys.c ****  *   worker thread: Implement as a global mutex or lock/unlock scheduler
 112:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * - Your system uses a high-level OS with e.g. POSIX signals:
 113:Middlewares/Third_Party/LwIP/src/core/sys.c ****  *   Implement as a global mutex
 114:Middlewares/Third_Party/LwIP/src/core/sys.c ****  *
 115:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * @defgroup sys_misc Misc
 116:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * @ingroup sys_os
 117:Middlewares/Third_Party/LwIP/src/core/sys.c ****  */
 118:Middlewares/Third_Party/LwIP/src/core/sys.c **** 
 119:Middlewares/Third_Party/LwIP/src/core/sys.c **** #include "lwip/opt.h"
 120:Middlewares/Third_Party/LwIP/src/core/sys.c **** 
 121:Middlewares/Third_Party/LwIP/src/core/sys.c **** #include "lwip/sys.h"
 122:Middlewares/Third_Party/LwIP/src/core/sys.c **** 
 123:Middlewares/Third_Party/LwIP/src/core/sys.c **** /* Most of the functions defined in sys.h must be implemented in the
 124:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * architecture-dependent file sys_arch.c */
 125:Middlewares/Third_Party/LwIP/src/core/sys.c **** 
 126:Middlewares/Third_Party/LwIP/src/core/sys.c **** #if !NO_SYS
 127:Middlewares/Third_Party/LwIP/src/core/sys.c **** 
 128:Middlewares/Third_Party/LwIP/src/core/sys.c **** #ifndef sys_msleep
 129:Middlewares/Third_Party/LwIP/src/core/sys.c **** /**
 130:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * Sleep for some ms. Timeouts are NOT processed while sleeping.
 131:Middlewares/Third_Party/LwIP/src/core/sys.c ****  *
 132:Middlewares/Third_Party/LwIP/src/core/sys.c ****  * @param ms number of milliseconds to sleep
 133:Middlewares/Third_Party/LwIP/src/core/sys.c ****  */
 134:Middlewares/Third_Party/LwIP/src/core/sys.c **** void
 135:Middlewares/Third_Party/LwIP/src/core/sys.c **** sys_msleep(u32_t ms)
 136:Middlewares/Third_Party/LwIP/src/core/sys.c **** {
  29              		.loc 1 136 1 view -0
  30              		.cfi_startproc
  31              		@ args = 0, pretend = 0, frame = 8
  32              		@ frame_needed = 0, uses_anonymous_args = 0
 137:Middlewares/Third_Party/LwIP/src/core/sys.c ****   if (ms > 0) {
  33              		.loc 1 137 3 view .LVU1
  34              		.loc 1 137 6 is_stmt 0 view .LVU2
  35 0000 00B9     		cbnz	r0, .L8
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccaJPOi1.s 			page 4


  36 0002 7047     		bx	lr
  37              	.L8:
 136:Middlewares/Third_Party/LwIP/src/core/sys.c ****   if (ms > 0) {
  38              		.loc 1 136 1 view .LVU3
  39 0004 10B5     		push	{r4, lr}
  40              	.LCFI0:
  41              		.cfi_def_cfa_offset 8
  42              		.cfi_offset 4, -8
  43              		.cfi_offset 14, -4
  44 0006 82B0     		sub	sp, sp, #8
  45              	.LCFI1:
  46              		.cfi_def_cfa_offset 16
  47 0008 0446     		mov	r4, r0
  48              	.LBB2:
 138:Middlewares/Third_Party/LwIP/src/core/sys.c ****     sys_sem_t delaysem;
  49              		.loc 1 138 5 is_stmt 1 view .LVU4
 139:Middlewares/Third_Party/LwIP/src/core/sys.c ****     err_t err = sys_sem_new(&delaysem, 0);
  50              		.loc 1 139 5 view .LVU5
  51              		.loc 1 139 17 is_stmt 0 view .LVU6
  52 000a 0021     		movs	r1, #0
  53 000c 01A8     		add	r0, sp, #4
  54              	.LVL1:
  55              		.loc 1 139 17 view .LVU7
  56 000e FFF7FEFF 		bl	sys_sem_new
  57              	.LVL2:
 140:Middlewares/Third_Party/LwIP/src/core/sys.c ****     if (err == ERR_OK) {
  58              		.loc 1 140 5 is_stmt 1 view .LVU8
  59              		.loc 1 140 8 is_stmt 0 view .LVU9
  60 0012 08B1     		cbz	r0, .L9
  61              	.LVL3:
  62              	.L1:
  63              		.loc 1 140 8 view .LVU10
  64              	.LBE2:
 141:Middlewares/Third_Party/LwIP/src/core/sys.c ****       sys_arch_sem_wait(&delaysem, ms);
 142:Middlewares/Third_Party/LwIP/src/core/sys.c ****       sys_sem_free(&delaysem);
 143:Middlewares/Third_Party/LwIP/src/core/sys.c ****     }
 144:Middlewares/Third_Party/LwIP/src/core/sys.c ****   }
 145:Middlewares/Third_Party/LwIP/src/core/sys.c **** }
  65              		.loc 1 145 1 view .LVU11
  66 0014 02B0     		add	sp, sp, #8
  67              	.LCFI2:
  68              		.cfi_remember_state
  69              		.cfi_def_cfa_offset 8
  70              		@ sp needed
  71 0016 10BD     		pop	{r4, pc}
  72              	.LVL4:
  73              	.L9:
  74              	.LCFI3:
  75              		.cfi_restore_state
  76              	.LBB3:
 141:Middlewares/Third_Party/LwIP/src/core/sys.c ****       sys_arch_sem_wait(&delaysem, ms);
  77              		.loc 1 141 7 is_stmt 1 view .LVU12
  78 0018 2146     		mov	r1, r4
  79 001a 01A8     		add	r0, sp, #4
  80              	.LVL5:
 141:Middlewares/Third_Party/LwIP/src/core/sys.c ****       sys_arch_sem_wait(&delaysem, ms);
  81              		.loc 1 141 7 is_stmt 0 view .LVU13
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccaJPOi1.s 			page 5


  82 001c FFF7FEFF 		bl	sys_arch_sem_wait
  83              	.LVL6:
 142:Middlewares/Third_Party/LwIP/src/core/sys.c ****     }
  84              		.loc 1 142 7 is_stmt 1 view .LVU14
  85 0020 01A8     		add	r0, sp, #4
  86 0022 FFF7FEFF 		bl	sys_sem_free
  87              	.LVL7:
  88              	.LBE3:
  89              		.loc 1 145 1 is_stmt 0 view .LVU15
  90 0026 F5E7     		b	.L1
  91              		.cfi_endproc
  92              	.LFE174:
  94              		.text
  95              	.Letext0:
  96              		.file 2 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin/../lib/gcc/arm-none-eabi/13.3.1/../
  97              		.file 3 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin/../lib/gcc/arm-none-eabi/13.3.1/../
  98              		.file 4 "Middlewares/Third_Party/LwIP/src/include/lwip/arch.h"
  99              		.file 5 "Middlewares/Third_Party/LwIP/src/include/lwip/err.h"
 100              		.file 6 "Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.h"
 101              		.file 7 "Middlewares/Third_Party/LwIP/system/arch/sys_arch.h"
 102              		.file 8 "Middlewares/Third_Party/LwIP/src/include/lwip/sys.h"
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccaJPOi1.s 			page 6


DEFINED SYMBOLS
                            *ABS*:00000000 sys.c
C:\Users\<USER>\AppData\Local\Temp\ccaJPOi1.s:20     .text.sys_msleep:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccaJPOi1.s:26     .text.sys_msleep:00000000 sys_msleep

UNDEFINED SYMBOLS
sys_sem_new
sys_arch_sem_wait
sys_sem_free
