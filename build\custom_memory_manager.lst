ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc6tgLS5.s 			page 1


   1              		.cpu cortex-m7
   2              		.arch armv7e-m
   3              		.fpu fpv5-d16
   4              		.eabi_attribute 28, 1
   5              		.eabi_attribute 20, 1
   6              		.eabi_attribute 21, 1
   7              		.eabi_attribute 23, 3
   8              		.eabi_attribute 24, 1
   9              		.eabi_attribute 25, 1
  10              		.eabi_attribute 26, 1
  11              		.eabi_attribute 30, 1
  12              		.eabi_attribute 34, 1
  13              		.eabi_attribute 18, 4
  14              		.file	"custom_memory_manager.c"
  15              		.text
  16              	.Ltext0:
  17              		.cfi_sections	.debug_frame
  18              		.file 1 "micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c"
  19              		.section	.text.prvHeapInit,"ax",%progbits
  20              		.align	1
  21              		.syntax unified
  22              		.thumb
  23              		.thumb_func
  25              	prvHeapInit:
  26              	.LFB12:
   1:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** /*
   2:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c ****  * A custom implementation of pvPortMallocMicroROS() and vPortFreeMicroROS() with realloc and
   3:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c ****  * calloc features based on FreeRTOS heap4.c.
   4:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c ****  */
   5:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c ****  
   6:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** #include <stdlib.h>
   7:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** #include <string.h>
   8:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
   9:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** /* Defining MPU_WRAPPERS_INCLUDED_FROM_API_FILE prevents task.h from redefining
  10:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** all the API functions to use the MPU wrappers.  That should only be done when
  11:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** task.h is included from an application file. */
  12:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** #define MPU_WRAPPERS_INCLUDED_FROM_API_FILE
  13:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
  14:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** #include "FreeRTOS.h"
  15:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** #include "task.h"
  16:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
  17:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** #undef MPU_WRAPPERS_INCLUDED_FROM_API_FILE
  18:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
  19:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** #if( configSUPPORT_DYNAMIC_ALLOCATION == 0 )
  20:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	#error This file must not be used if configSUPPORT_DYNAMIC_ALLOCATION is 0
  21:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** #endif
  22:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
  23:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** /* Block sizes must not get too small. */
  24:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** #define heapMINIMUM_BLOCK_SIZE	( ( size_t ) ( xHeapStructSize << 1 ) )
  25:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
  26:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** /* Assumes 8bit bytes! */
  27:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** #define heapBITS_PER_BYTE		( ( size_t ) 8 )
  28:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
  29:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** /* Allocate the memory for the heap. */
  30:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** static uint8_t ucHeap[ configTOTAL_HEAP_SIZE ];
  31:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
  32:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** /* Define the linked list structure.  This is used to link free blocks in order
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc6tgLS5.s 			page 2


  33:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** of their memory address. */
  34:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** typedef struct A_BLOCK_LINK
  35:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** {
  36:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	struct A_BLOCK_LINK *pxNextFreeBlock;	/*<< The next free block in the list. */
  37:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	size_t xBlockSize;						/*<< The size of the free block. */
  38:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** } BlockLink_t;
  39:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
  40:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** /*-----------------------------------------------------------*/
  41:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
  42:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** /*
  43:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c ****  * Inserts a block of memory that is being freed into the correct position in
  44:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c ****  * the list of free memory blocks.  The block being freed will be merged with
  45:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c ****  * the block in front it and/or the block behind it if the memory blocks are
  46:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c ****  * adjacent to each other.
  47:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c ****  */
  48:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** static void prvInsertBlockIntoFreeList( BlockLink_t *pxBlockToInsert );
  49:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
  50:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** /*
  51:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c ****  * Called automatically to setup the required heap structures the first time
  52:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c ****  * pvPortMallocMicroROS() is called.
  53:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c ****  */
  54:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** static void prvHeapInit( void );
  55:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
  56:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** /*-----------------------------------------------------------*/
  57:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
  58:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** /* The size of the structure placed at the beginning of each allocated memory
  59:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** block must by correctly byte aligned. */
  60:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** static const size_t xHeapStructSize	= ( sizeof( BlockLink_t ) + ( ( size_t ) ( portBYTE_ALIGNMENT -
  61:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
  62:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** /* Create a couple of list links to mark the start and end of the list. */
  63:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** static BlockLink_t xStart, *pxEnd = NULL;
  64:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
  65:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** /* Keeps track of the number of free bytes remaining, but says nothing about
  66:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** fragmentation. */
  67:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** static size_t xFreeBytesRemaining = 0U;
  68:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** static size_t xMinimumEverFreeBytesRemaining = 0U;
  69:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
  70:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** /* Gets set to the top bit of an size_t type.  When this bit in the xBlockSize
  71:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** member of an BlockLink_t structure is set then the block belongs to the
  72:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** application.  When the bit is free the block is still part of the free heap
  73:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** space. */
  74:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** static size_t xBlockAllocatedBit = 0;
  75:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
  76:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** /*-----------------------------------------------------------*/
  77:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
  78:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** void *pvPortMallocMicroROS( size_t xWantedSize )
  79:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** {
  80:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** BlockLink_t *pxBlock, *pxPreviousBlock, *pxNewBlockLink;
  81:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** void *pvReturn = NULL;
  82:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
  83:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	vTaskSuspendAll();
  84:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	{
  85:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		/* If this is the first call to malloc then the heap will require
  86:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		initialisation to setup the list of free blocks. */
  87:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		if( pxEnd == NULL )
  88:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		{
  89:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 			prvHeapInit();
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc6tgLS5.s 			page 3


  90:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		}
  91:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		else
  92:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		{
  93:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 			mtCOVERAGE_TEST_MARKER();
  94:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		}
  95:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
  96:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		/* Check the requested block size is not so large that the top bit is
  97:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		set.  The top bit of the block size member of the BlockLink_t structure
  98:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		is used to determine who owns the block - the application or the
  99:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		kernel, so it must be free. */
 100:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		if( ( xWantedSize & xBlockAllocatedBit ) == 0 )
 101:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		{
 102:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 			/* The wanted size is increased so it can contain a BlockLink_t
 103:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 			structure in addition to the requested amount of bytes. */
 104:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 			if( xWantedSize > 0 )
 105:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 			{
 106:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 				xWantedSize += xHeapStructSize;
 107:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 108:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 				/* Ensure that blocks are always aligned to the required number
 109:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 				of bytes. */
 110:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 				if( ( xWantedSize & portBYTE_ALIGNMENT_MASK ) != 0x00 )
 111:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 				{
 112:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 					/* Byte alignment required. */
 113:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 					xWantedSize += ( portBYTE_ALIGNMENT - ( xWantedSize & portBYTE_ALIGNMENT_MASK ) );
 114:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 					configASSERT( ( xWantedSize & portBYTE_ALIGNMENT_MASK ) == 0 );
 115:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 				}
 116:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 				else
 117:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 				{
 118:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 					mtCOVERAGE_TEST_MARKER();
 119:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 				}
 120:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 			}
 121:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 			else
 122:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 			{
 123:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 				mtCOVERAGE_TEST_MARKER();
 124:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 			}
 125:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 126:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 			if( ( xWantedSize > 0 ) && ( xWantedSize <= xFreeBytesRemaining ) )
 127:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 			{
 128:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 				/* Traverse the list from the start	(lowest address) block until
 129:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 				one	of adequate size is found. */
 130:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 				pxPreviousBlock = &xStart;
 131:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 				pxBlock = xStart.pxNextFreeBlock;
 132:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 				while( ( pxBlock->xBlockSize < xWantedSize ) && ( pxBlock->pxNextFreeBlock != NULL ) )
 133:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 				{
 134:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 					pxPreviousBlock = pxBlock;
 135:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 					pxBlock = pxBlock->pxNextFreeBlock;
 136:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 				}
 137:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 138:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 				/* If the end marker was reached then a block of adequate size
 139:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 				was	not found. */
 140:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 				if( pxBlock != pxEnd )
 141:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 				{
 142:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 					/* Return the memory space pointed to - jumping over the
 143:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 					BlockLink_t structure at its start. */
 144:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 					pvReturn = ( void * ) ( ( ( uint8_t * ) pxPreviousBlock->pxNextFreeBlock ) + xHeapStructSize )
 145:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 146:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 					/* This block is being returned for use so must be taken out
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc6tgLS5.s 			page 4


 147:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 					of the list of free blocks. */
 148:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 					pxPreviousBlock->pxNextFreeBlock = pxBlock->pxNextFreeBlock;
 149:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 150:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 					/* If the block is larger than required it can be split into
 151:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 					two. */
 152:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 					if( ( pxBlock->xBlockSize - xWantedSize ) > heapMINIMUM_BLOCK_SIZE )
 153:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 					{
 154:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 						/* This block is to be split into two.  Create a new
 155:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 						block following the number of bytes requested. The void
 156:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 						cast is used to prevent byte alignment warnings from the
 157:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 						compiler. */
 158:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 						pxNewBlockLink = ( void * ) ( ( ( uint8_t * ) pxBlock ) + xWantedSize );
 159:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 						configASSERT( ( ( ( size_t ) pxNewBlockLink ) & portBYTE_ALIGNMENT_MASK ) == 0 );
 160:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 161:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 						/* Calculate the sizes of two blocks split from the
 162:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 						single block. */
 163:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 						pxNewBlockLink->xBlockSize = pxBlock->xBlockSize - xWantedSize;
 164:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 						pxBlock->xBlockSize = xWantedSize;
 165:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 166:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 						/* Insert the new block into the list of free blocks. */
 167:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 						prvInsertBlockIntoFreeList( pxNewBlockLink );
 168:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 					}
 169:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 					else
 170:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 					{
 171:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 						mtCOVERAGE_TEST_MARKER();
 172:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 					}
 173:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 174:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 					xFreeBytesRemaining -= pxBlock->xBlockSize;
 175:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 176:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 					if( xFreeBytesRemaining < xMinimumEverFreeBytesRemaining )
 177:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 					{
 178:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 						xMinimumEverFreeBytesRemaining = xFreeBytesRemaining;
 179:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 					}
 180:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 					else
 181:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 					{
 182:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 						mtCOVERAGE_TEST_MARKER();
 183:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 					}
 184:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 185:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 					/* The block is being returned - it is allocated and owned
 186:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 					by the application and has no "next" block. */
 187:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 					pxBlock->xBlockSize |= xBlockAllocatedBit;
 188:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 					pxBlock->pxNextFreeBlock = NULL;
 189:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 				}
 190:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 				else
 191:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 				{
 192:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 					mtCOVERAGE_TEST_MARKER();
 193:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 				}
 194:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 			}
 195:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 			else
 196:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 			{
 197:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 				mtCOVERAGE_TEST_MARKER();
 198:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 			}
 199:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		}
 200:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		else
 201:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		{
 202:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 			mtCOVERAGE_TEST_MARKER();
 203:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		}
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc6tgLS5.s 			page 5


 204:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 205:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		traceMALLOC( pvReturn, xWantedSize );
 206:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	}
 207:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	( void ) xTaskResumeAll();
 208:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 209:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	#if( configUSE_MALLOC_FAILED_HOOK == 1 )
 210:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	{
 211:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		if( pvReturn == NULL )
 212:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		{
 213:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 			extern void vApplicationMallocFailedHook( void );
 214:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 			vApplicationMallocFailedHook();
 215:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		}
 216:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		else
 217:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		{
 218:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 			mtCOVERAGE_TEST_MARKER();
 219:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		}
 220:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	}
 221:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	#endif
 222:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 223:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	configASSERT( ( ( ( size_t ) pvReturn ) & ( size_t ) portBYTE_ALIGNMENT_MASK ) == 0 );
 224:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	return pvReturn;
 225:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** }
 226:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** /*-----------------------------------------------------------*/
 227:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 228:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** void vPortFreeMicroROS( void *pv )
 229:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** {
 230:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** uint8_t *puc = ( uint8_t * ) pv;
 231:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** BlockLink_t *pxLink;
 232:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 233:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	if( pv != NULL )
 234:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	{
 235:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		/* The memory being freed will have an BlockLink_t structure immediately
 236:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		before it. */
 237:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		puc -= xHeapStructSize;
 238:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 239:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		/* This casting is to keep the compiler from issuing warnings. */
 240:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		pxLink = ( void * ) puc;
 241:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 242:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		/* Check the block is actually allocated. */
 243:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		configASSERT( ( pxLink->xBlockSize & xBlockAllocatedBit ) != 0 );
 244:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		configASSERT( pxLink->pxNextFreeBlock == NULL );
 245:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 246:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		if( ( pxLink->xBlockSize & xBlockAllocatedBit ) != 0 )
 247:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		{
 248:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 			if( pxLink->pxNextFreeBlock == NULL )
 249:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 			{
 250:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 				/* The block is being returned to the heap - it is no longer
 251:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 				allocated. */
 252:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 				pxLink->xBlockSize &= ~xBlockAllocatedBit;
 253:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 254:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 				vTaskSuspendAll();
 255:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 				{
 256:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 					/* Add this block to the list of free blocks. */
 257:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 					xFreeBytesRemaining += pxLink->xBlockSize;
 258:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 					traceFREE( pv, pxLink->xBlockSize );
 259:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 					prvInsertBlockIntoFreeList( ( ( BlockLink_t * ) pxLink ) );
 260:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 				}
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc6tgLS5.s 			page 6


 261:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 				( void ) xTaskResumeAll();
 262:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 			}
 263:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 			else
 264:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 			{
 265:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 				mtCOVERAGE_TEST_MARKER();
 266:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 			}
 267:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		}
 268:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		else
 269:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		{
 270:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 			mtCOVERAGE_TEST_MARKER();
 271:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		}
 272:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	}
 273:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** }
 274:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** /*-----------------------------------------------------------*/
 275:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 276:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** /*-----------------------------------------------------------*/
 277:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** size_t getBlockSize( void *pv )
 278:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** {
 279:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 280:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	uint8_t *puc = ( uint8_t * ) pv;
 281:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	BlockLink_t *pxLink;
 282:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 283:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	puc -= xHeapStructSize;
 284:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	pxLink = ( void * ) puc;
 285:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 286:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	size_t count = pxLink->xBlockSize & ~xBlockAllocatedBit;
 287:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 288:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	return count;
 289:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** }
 290:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** /*-----------------------------------------------------------*/
 291:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 292:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** void *pvPortReallocMicroROS( void *pv, size_t xWantedSize )
 293:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** {
 294:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	vTaskSuspendAll();
 295:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 296:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	void * newmem = pvPortMallocMicroROS(xWantedSize);
 297:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	if (newmem != NULL && pv != NULL)
 298:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	{
 299:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		size_t count = getBlockSize(pv) - xHeapStructSize;
 300:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		if (xWantedSize < count)
 301:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		{
 302:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 			count = xWantedSize;
 303:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		}
 304:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		memcpy(newmem, pv, count);
 305:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 306:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		vPortFreeMicroROS(pv);
 307:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	}
 308:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 309:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	( void ) xTaskResumeAll();
 310:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 311:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	return newmem;
 312:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** }
 313:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** /*-----------------------------------------------------------*/
 314:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 315:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** void *pvPortCallocMicroROS( size_t num, size_t xWantedSize )
 316:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** {
 317:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	vTaskSuspendAll();
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc6tgLS5.s 			page 7


 318:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	size_t count = xWantedSize*num;
 319:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 320:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	void * mem = pvPortMallocMicroROS(count);
 321:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c ****   	char *in_dest = (char*)mem;
 322:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 323:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c ****   	while(count--)
 324:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c ****     	*in_dest++ = 0;
 325:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 326:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	( void ) xTaskResumeAll();
 327:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c ****   	return mem;
 328:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** }
 329:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** /*-----------------------------------------------------------*/
 330:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 331:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** size_t xPortGetFreeHeapSizeMicroROS( void )
 332:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** {
 333:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	return xFreeBytesRemaining;
 334:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** }
 335:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** /*-----------------------------------------------------------*/
 336:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 337:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** size_t xPortGetMinimumEverFreeHeapSizeMicroROS( void )
 338:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** {
 339:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	return xMinimumEverFreeBytesRemaining;
 340:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** }
 341:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** /*-----------------------------------------------------------*/
 342:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 343:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** void vPortInitialiseBlocksMicroROS( void )
 344:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** {
 345:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	/* This just exists to keep the linker quiet. */
 346:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** }
 347:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** /*-----------------------------------------------------------*/
 348:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 349:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** static void prvHeapInit( void )
 350:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** {
  27              		.loc 1 350 1 view -0
  28              		.cfi_startproc
  29              		@ args = 0, pretend = 0, frame = 0
  30              		@ frame_needed = 0, uses_anonymous_args = 0
  31              		@ link register save eliminated.
 351:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** BlockLink_t *pxFirstFreeBlock;
  32              		.loc 1 351 1 view .LVU1
 352:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** uint8_t *pucAlignedHeap;
  33              		.loc 1 352 1 view .LVU2
 353:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** size_t uxAddress;
  34              		.loc 1 353 1 view .LVU3
 354:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** size_t xTotalHeapSize = configTOTAL_HEAP_SIZE;
  35              		.loc 1 354 1 view .LVU4
  36              	.LVL0:
 355:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 356:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	/* Ensure the heap starts on a correctly aligned boundary. */
 357:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	uxAddress = ( size_t ) ucHeap;
  37              		.loc 1 357 2 view .LVU5
  38              		.loc 1 357 12 is_stmt 0 view .LVU6
  39 0000 124A     		ldr	r2, .L4
  40              	.LVL1:
 358:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 359:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	if( ( uxAddress & portBYTE_ALIGNMENT_MASK ) != 0 )
  41              		.loc 1 359 2 is_stmt 1 view .LVU7
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc6tgLS5.s 			page 8


  42              		.loc 1 359 4 is_stmt 0 view .LVU8
  43 0002 12F0070F 		tst	r2, #7
  44 0006 1ED0     		beq	.L3
 360:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	{
 361:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		uxAddress += ( portBYTE_ALIGNMENT - 1 );
  45              		.loc 1 361 3 is_stmt 1 view .LVU9
  46              		.loc 1 361 13 is_stmt 0 view .LVU10
  47 0008 D11D     		adds	r1, r2, #7
  48              	.LVL2:
 362:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		uxAddress &= ~( ( size_t ) portBYTE_ALIGNMENT_MASK );
  49              		.loc 1 362 3 is_stmt 1 view .LVU11
  50              		.loc 1 362 13 is_stmt 0 view .LVU12
  51 000a 21F00701 		bic	r1, r1, #7
  52              	.LVL3:
 363:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		xTotalHeapSize -= uxAddress - ( size_t ) ucHeap;
  53              		.loc 1 363 3 is_stmt 1 view .LVU13
  54              		.loc 1 363 18 is_stmt 0 view .LVU14
  55 000e C1F57053 		rsb	r3, r1, #15360
  56 0012 1344     		add	r3, r3, r2
  57              	.LVL4:
 362:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		uxAddress &= ~( ( size_t ) portBYTE_ALIGNMENT_MASK );
  58              		.loc 1 362 13 view .LVU15
  59 0014 0A46     		mov	r2, r1
  60              	.LVL5:
  61              	.L2:
 364:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	}
 365:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 366:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	pucAlignedHeap = ( uint8_t * ) uxAddress;
  62              		.loc 1 366 2 is_stmt 1 view .LVU16
 367:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 368:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	/* xStart is used to hold a pointer to the first item in the list of free
 369:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	blocks.  The void cast is used to prevent compiler warnings. */
 370:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	xStart.pxNextFreeBlock = ( void * ) pucAlignedHeap;
  63              		.loc 1 370 2 view .LVU17
  64              		.loc 1 370 25 is_stmt 0 view .LVU18
  65 0016 0E48     		ldr	r0, .L4+4
  66 0018 0260     		str	r2, [r0]
 371:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	xStart.xBlockSize = ( size_t ) 0;
  67              		.loc 1 371 2 is_stmt 1 view .LVU19
  68              		.loc 1 371 20 is_stmt 0 view .LVU20
  69 001a 0021     		movs	r1, #0
  70 001c 4160     		str	r1, [r0, #4]
 372:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 373:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	/* pxEnd is used to mark the end of the list of free blocks and is inserted
 374:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	at the end of the heap space. */
 375:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	uxAddress = ( ( size_t ) pucAlignedHeap ) + xTotalHeapSize;
  71              		.loc 1 375 2 is_stmt 1 view .LVU21
  72              		.loc 1 375 12 is_stmt 0 view .LVU22
  73 001e 1344     		add	r3, r3, r2
  74              	.LVL6:
 376:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	uxAddress -= xHeapStructSize;
  75              		.loc 1 376 2 is_stmt 1 view .LVU23
  76              		.loc 1 376 12 is_stmt 0 view .LVU24
  77 0020 083B     		subs	r3, r3, #8
  78              	.LVL7:
 377:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	uxAddress &= ~( ( size_t ) portBYTE_ALIGNMENT_MASK );
  79              		.loc 1 377 2 is_stmt 1 view .LVU25
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc6tgLS5.s 			page 9


  80              		.loc 1 377 12 is_stmt 0 view .LVU26
  81 0022 23F00703 		bic	r3, r3, #7
  82              	.LVL8:
 378:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	pxEnd = ( void * ) uxAddress;
  83              		.loc 1 378 2 is_stmt 1 view .LVU27
  84              		.loc 1 378 8 is_stmt 0 view .LVU28
  85 0026 0B48     		ldr	r0, .L4+8
  86 0028 0360     		str	r3, [r0]
 379:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	pxEnd->xBlockSize = 0;
  87              		.loc 1 379 2 is_stmt 1 view .LVU29
  88              		.loc 1 379 20 is_stmt 0 view .LVU30
  89 002a 5960     		str	r1, [r3, #4]
 380:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	pxEnd->pxNextFreeBlock = NULL;
  90              		.loc 1 380 2 is_stmt 1 view .LVU31
  91              		.loc 1 380 25 is_stmt 0 view .LVU32
  92 002c 1960     		str	r1, [r3]
 381:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 382:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	/* To start with there is a single free block that is sized to take up the
 383:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	entire heap space, minus the space taken by pxEnd. */
 384:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	pxFirstFreeBlock = ( void * ) pucAlignedHeap;
  93              		.loc 1 384 2 is_stmt 1 view .LVU33
  94              	.LVL9:
 385:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	pxFirstFreeBlock->xBlockSize = uxAddress - ( size_t ) pxFirstFreeBlock;
  95              		.loc 1 385 2 view .LVU34
  96              		.loc 1 385 43 is_stmt 0 view .LVU35
  97 002e 991A     		subs	r1, r3, r2
  98              		.loc 1 385 31 view .LVU36
  99 0030 5160     		str	r1, [r2, #4]
 386:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	pxFirstFreeBlock->pxNextFreeBlock = pxEnd;
 100              		.loc 1 386 2 is_stmt 1 view .LVU37
 101              		.loc 1 386 36 is_stmt 0 view .LVU38
 102 0032 1360     		str	r3, [r2]
 387:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 388:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	/* Only one block exists - and it covers the entire usable heap space. */
 389:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	xMinimumEverFreeBytesRemaining = pxFirstFreeBlock->xBlockSize;
 103              		.loc 1 389 2 is_stmt 1 view .LVU39
 104              		.loc 1 389 33 is_stmt 0 view .LVU40
 105 0034 084B     		ldr	r3, .L4+12
 106              	.LVL10:
 107              		.loc 1 389 33 view .LVU41
 108 0036 1960     		str	r1, [r3]
 390:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	xFreeBytesRemaining = pxFirstFreeBlock->xBlockSize;
 109              		.loc 1 390 2 is_stmt 1 view .LVU42
 110              		.loc 1 390 22 is_stmt 0 view .LVU43
 111 0038 084B     		ldr	r3, .L4+16
 112 003a 1960     		str	r1, [r3]
 391:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 392:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	/* Work out the position of the top bit in a size_t variable. */
 393:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	xBlockAllocatedBit = ( ( size_t ) 1 ) << ( ( sizeof( size_t ) * heapBITS_PER_BYTE ) - 1 );
 113              		.loc 1 393 2 is_stmt 1 view .LVU44
 114              		.loc 1 393 21 is_stmt 0 view .LVU45
 115 003c 084B     		ldr	r3, .L4+20
 116 003e 4FF00042 		mov	r2, #-2147483648
 117              	.LVL11:
 118              		.loc 1 393 21 view .LVU46
 119 0042 1A60     		str	r2, [r3]
 394:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** }
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc6tgLS5.s 			page 10


 120              		.loc 1 394 1 view .LVU47
 121 0044 7047     		bx	lr
 122              	.LVL12:
 123              	.L3:
 354:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 124              		.loc 1 354 8 view .LVU48
 125 0046 4FF47053 		mov	r3, #15360
 126 004a E4E7     		b	.L2
 127              	.L5:
 128              		.align	2
 129              	.L4:
 130 004c 00000000 		.word	ucHeap
 131 0050 00000000 		.word	xStart
 132 0054 00000000 		.word	pxEnd
 133 0058 00000000 		.word	xMinimumEverFreeBytesRemaining
 134 005c 00000000 		.word	xFreeBytesRemaining
 135 0060 00000000 		.word	xBlockAllocatedBit
 136              		.cfi_endproc
 137              	.LFE12:
 139              		.section	.text.prvInsertBlockIntoFreeList,"ax",%progbits
 140              		.align	1
 141              		.syntax unified
 142              		.thumb
 143              		.thumb_func
 145              	prvInsertBlockIntoFreeList:
 146              	.LVL13:
 147              	.LFB13:
 395:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** /*-----------------------------------------------------------*/
 396:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 397:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** static void prvInsertBlockIntoFreeList( BlockLink_t *pxBlockToInsert )
 398:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** {
 148              		.loc 1 398 1 is_stmt 1 view -0
 149              		.cfi_startproc
 150              		@ args = 0, pretend = 0, frame = 0
 151              		@ frame_needed = 0, uses_anonymous_args = 0
 152              		@ link register save eliminated.
 399:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** BlockLink_t *pxIterator;
 153              		.loc 1 399 1 view .LVU50
 400:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** uint8_t *puc;
 154              		.loc 1 400 1 view .LVU51
 401:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 402:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	/* Iterate through the list until a block is found that has a higher address
 403:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	than the block being inserted. */
 404:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	for( pxIterator = &xStart; pxIterator->pxNextFreeBlock < pxBlockToInsert; pxIterator = pxIterator-
 155              		.loc 1 404 2 view .LVU52
 156              		.loc 1 404 18 is_stmt 0 view .LVU53
 157 0000 164B     		ldr	r3, .L21
 158              	.LVL14:
 159              	.L7:
 160              		.loc 1 404 57 is_stmt 1 discriminator 1 view .LVU54
 161 0002 1A46     		mov	r2, r3
 162              		.loc 1 404 39 is_stmt 0 discriminator 1 view .LVU55
 163 0004 1B68     		ldr	r3, [r3]
 164              	.LVL15:
 165              		.loc 1 404 57 discriminator 1 view .LVU56
 166 0006 8342     		cmp	r3, r0
 167 0008 FBD3     		bcc	.L7
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc6tgLS5.s 			page 11


 405:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	{
 406:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		/* Nothing to do here, just iterate to the right position. */
 407:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	}
 408:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 409:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	/* Do the block being inserted, and the block it is being inserted after
 410:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	make a contiguous block of memory? */
 411:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	puc = ( uint8_t * ) pxIterator;
 168              		.loc 1 411 2 is_stmt 1 view .LVU57
 169              	.LVL16:
 412:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	if( ( puc + pxIterator->xBlockSize ) == ( uint8_t * ) pxBlockToInsert )
 170              		.loc 1 412 2 view .LVU58
 171              		.loc 1 412 24 is_stmt 0 view .LVU59
 172 000a 5168     		ldr	r1, [r2, #4]
 173              		.loc 1 412 12 view .LVU60
 174 000c 02EB010C 		add	ip, r2, r1
 175              		.loc 1 412 4 view .LVU61
 176 0010 8445     		cmp	ip, r0
 177 0012 09D0     		beq	.L19
 178              	.L8:
 413:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	{
 414:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		pxIterator->xBlockSize += pxBlockToInsert->xBlockSize;
 415:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		pxBlockToInsert = pxIterator;
 416:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	}
 417:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	else
 418:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	{
 419:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		mtCOVERAGE_TEST_MARKER();
 179              		.loc 1 419 27 is_stmt 1 view .LVU62
 420:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	}
 421:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 422:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	/* Do the block being inserted, and the block it is being inserted before
 423:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	make a contiguous block of memory? */
 424:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	puc = ( uint8_t * ) pxBlockToInsert;
 180              		.loc 1 424 2 view .LVU63
 181              	.LVL17:
 425:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	if( ( puc + pxBlockToInsert->xBlockSize ) == ( uint8_t * ) pxIterator->pxNextFreeBlock )
 182              		.loc 1 425 2 view .LVU64
 183              		.loc 1 425 29 is_stmt 0 view .LVU65
 184 0014 4168     		ldr	r1, [r0, #4]
 185              		.loc 1 425 12 view .LVU66
 186 0016 00EB010C 		add	ip, r0, r1
 187              		.loc 1 425 4 view .LVU67
 188 001a 6345     		cmp	r3, ip
 189 001c 09D0     		beq	.L20
 426:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	{
 427:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		if( pxIterator->pxNextFreeBlock != pxEnd )
 428:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		{
 429:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 			/* Form one big block from the two blocks. */
 430:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 			pxBlockToInsert->xBlockSize += pxIterator->pxNextFreeBlock->xBlockSize;
 431:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 			pxBlockToInsert->pxNextFreeBlock = pxIterator->pxNextFreeBlock->pxNextFreeBlock;
 432:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		}
 433:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		else
 434:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		{
 435:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 			pxBlockToInsert->pxNextFreeBlock = pxEnd;
 436:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		}
 437:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	}
 438:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	else
 439:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	{
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc6tgLS5.s 			page 12


 440:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		pxBlockToInsert->pxNextFreeBlock = pxIterator->pxNextFreeBlock;
 190              		.loc 1 440 3 is_stmt 1 view .LVU68
 191              		.loc 1 440 36 is_stmt 0 view .LVU69
 192 001e 0360     		str	r3, [r0]
 441:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	}
 442:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 443:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	/* If the block being inserted plugged a gab, so was merged with the block
 444:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	before and the block after, then it's pxNextFreeBlock pointer will have
 445:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	already been set, and should not be set here as that would make it point
 446:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	to itself. */
 447:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	if( pxIterator != pxBlockToInsert )
 193              		.loc 1 447 2 is_stmt 1 view .LVU70
 194              		.loc 1 447 4 is_stmt 0 view .LVU71
 195 0020 9042     		cmp	r0, r2
 196 0022 19D0     		beq	.L16
 448:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	{
 449:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		pxIterator->pxNextFreeBlock = pxBlockToInsert;
 197              		.loc 1 449 3 is_stmt 1 view .LVU72
 198              		.loc 1 449 31 is_stmt 0 view .LVU73
 199 0024 1060     		str	r0, [r2]
 450:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	}
 451:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	else
 452:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	{
 453:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		mtCOVERAGE_TEST_MARKER();
 200              		.loc 1 453 27 is_stmt 1 view .LVU74
 201 0026 7047     		bx	lr
 202              	.LVL18:
 203              	.L19:
 414:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		pxBlockToInsert = pxIterator;
 204              		.loc 1 414 3 view .LVU75
 414:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		pxBlockToInsert = pxIterator;
 205              		.loc 1 414 44 is_stmt 0 view .LVU76
 206 0028 4068     		ldr	r0, [r0, #4]
 207              	.LVL19:
 414:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		pxBlockToInsert = pxIterator;
 208              		.loc 1 414 26 view .LVU77
 209 002a 0144     		add	r1, r1, r0
 210 002c 5160     		str	r1, [r2, #4]
 415:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	}
 211              		.loc 1 415 3 is_stmt 1 view .LVU78
 212              	.LVL20:
 415:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	}
 213              		.loc 1 415 19 is_stmt 0 view .LVU79
 214 002e 1046     		mov	r0, r2
 215 0030 F0E7     		b	.L8
 216              	.LVL21:
 217              	.L20:
 398:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** BlockLink_t *pxIterator;
 218              		.loc 1 398 1 view .LVU80
 219 0032 10B4     		push	{r4}
 220              	.LCFI0:
 221              		.cfi_def_cfa_offset 4
 222              		.cfi_offset 4, -4
 427:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		{
 223              		.loc 1 427 3 is_stmt 1 view .LVU81
 427:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		{
 224              		.loc 1 427 35 is_stmt 0 view .LVU82
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc6tgLS5.s 			page 13


 225 0034 0A4C     		ldr	r4, .L21+4
 226 0036 2468     		ldr	r4, [r4]
 427:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		{
 227              		.loc 1 427 5 view .LVU83
 228 0038 A342     		cmp	r3, r4
 229 003a 0BD0     		beq	.L10
 430:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 			pxBlockToInsert->pxNextFreeBlock = pxIterator->pxNextFreeBlock->pxNextFreeBlock;
 230              		.loc 1 430 4 is_stmt 1 view .LVU84
 430:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 			pxBlockToInsert->pxNextFreeBlock = pxIterator->pxNextFreeBlock->pxNextFreeBlock;
 231              		.loc 1 430 62 is_stmt 0 view .LVU85
 232 003c 5B68     		ldr	r3, [r3, #4]
 430:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 			pxBlockToInsert->pxNextFreeBlock = pxIterator->pxNextFreeBlock->pxNextFreeBlock;
 233              		.loc 1 430 32 view .LVU86
 234 003e 1944     		add	r1, r1, r3
 235 0040 4160     		str	r1, [r0, #4]
 431:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		}
 236              		.loc 1 431 4 is_stmt 1 view .LVU87
 431:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		}
 237              		.loc 1 431 49 is_stmt 0 view .LVU88
 238 0042 1368     		ldr	r3, [r2]
 431:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		}
 239              		.loc 1 431 66 view .LVU89
 240 0044 1B68     		ldr	r3, [r3]
 431:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		}
 241              		.loc 1 431 37 view .LVU90
 242 0046 0360     		str	r3, [r0]
 243              	.L11:
 447:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	{
 244              		.loc 1 447 2 is_stmt 1 view .LVU91
 447:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	{
 245              		.loc 1 447 4 is_stmt 0 view .LVU92
 246 0048 9042     		cmp	r0, r2
 247 004a 00D0     		beq	.L6
 449:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	}
 248              		.loc 1 449 3 is_stmt 1 view .LVU93
 449:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	}
 249              		.loc 1 449 31 is_stmt 0 view .LVU94
 250 004c 1060     		str	r0, [r2]
 251              		.loc 1 453 27 is_stmt 1 view .LVU95
 252              	.L6:
 454:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	}
 455:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** }
 253              		.loc 1 455 1 is_stmt 0 view .LVU96
 254 004e 5DF8044B 		ldr	r4, [sp], #4
 255              	.LCFI1:
 256              		.cfi_remember_state
 257              		.cfi_restore 4
 258              		.cfi_def_cfa_offset 0
 259 0052 7047     		bx	lr
 260              	.L10:
 261              	.LCFI2:
 262              		.cfi_restore_state
 435:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		}
 263              		.loc 1 435 4 is_stmt 1 view .LVU97
 435:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		}
 264              		.loc 1 435 37 is_stmt 0 view .LVU98
 265 0054 0460     		str	r4, [r0]
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc6tgLS5.s 			page 14


 266 0056 F7E7     		b	.L11
 267              	.L16:
 268              	.LCFI3:
 269              		.cfi_def_cfa_offset 0
 270              		.cfi_restore 4
 435:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		}
 271              		.loc 1 435 37 view .LVU99
 272 0058 7047     		bx	lr
 273              	.L22:
 274 005a 00BF     		.align	2
 275              	.L21:
 276 005c 00000000 		.word	xStart
 277 0060 00000000 		.word	pxEnd
 278              		.cfi_endproc
 279              	.LFE13:
 281              		.section	.text.pvPortMallocMicroROS,"ax",%progbits
 282              		.align	1
 283              		.global	pvPortMallocMicroROS
 284              		.syntax unified
 285              		.thumb
 286              		.thumb_func
 288              	pvPortMallocMicroROS:
 289              	.LVL22:
 290              	.LFB4:
  79:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** BlockLink_t *pxBlock, *pxPreviousBlock, *pxNewBlockLink;
 291              		.loc 1 79 1 is_stmt 1 view -0
 292              		.cfi_startproc
 293              		@ args = 0, pretend = 0, frame = 0
 294              		@ frame_needed = 0, uses_anonymous_args = 0
  79:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** BlockLink_t *pxBlock, *pxPreviousBlock, *pxNewBlockLink;
 295              		.loc 1 79 1 is_stmt 0 view .LVU101
 296 0000 38B5     		push	{r3, r4, r5, lr}
 297              	.LCFI4:
 298              		.cfi_def_cfa_offset 16
 299              		.cfi_offset 3, -16
 300              		.cfi_offset 4, -12
 301              		.cfi_offset 5, -8
 302              		.cfi_offset 14, -4
 303 0002 0446     		mov	r4, r0
  80:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** void *pvReturn = NULL;
 304              		.loc 1 80 1 is_stmt 1 view .LVU102
  81:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 305              		.loc 1 81 1 view .LVU103
 306              	.LVL23:
  83:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	{
 307              		.loc 1 83 2 view .LVU104
 308 0004 FFF7FEFF 		bl	vTaskSuspendAll
 309              	.LVL24:
  87:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		{
 310              		.loc 1 87 3 view .LVU105
  87:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		{
 311              		.loc 1 87 13 is_stmt 0 view .LVU106
 312 0008 364B     		ldr	r3, .L43
 313 000a 1B68     		ldr	r3, [r3]
  87:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		{
 314              		.loc 1 87 5 view .LVU107
 315 000c B3B1     		cbz	r3, .L42
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc6tgLS5.s 			page 15


 316              	.L24:
  93:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		}
 317              		.loc 1 93 28 is_stmt 1 view .LVU108
 100:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		{
 318              		.loc 1 100 3 view .LVU109
 100:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		{
 319              		.loc 1 100 21 is_stmt 0 view .LVU110
 320 000e 364B     		ldr	r3, .L43+4
 321 0010 1B68     		ldr	r3, [r3]
 100:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		{
 322              		.loc 1 100 5 view .LVU111
 323 0012 1C42     		tst	r4, r3
 324 0014 4CD1     		bne	.L35
 104:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 			{
 325              		.loc 1 104 4 is_stmt 1 view .LVU112
 104:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 			{
 326              		.loc 1 104 6 is_stmt 0 view .LVU113
 327 0016 002C     		cmp	r4, #0
 328 0018 4CD0     		beq	.L36
 106:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 329              		.loc 1 106 5 is_stmt 1 view .LVU114
 106:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 330              		.loc 1 106 17 is_stmt 0 view .LVU115
 331 001a 04F10802 		add	r2, r4, #8
 332              	.LVL25:
 110:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 				{
 333              		.loc 1 110 5 is_stmt 1 view .LVU116
 110:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 				{
 334              		.loc 1 110 7 is_stmt 0 view .LVU117
 335 001e 14F0070F 		tst	r4, #7
 336 0022 02D0     		beq	.L26
 113:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 					configASSERT( ( xWantedSize & portBYTE_ALIGNMENT_MASK ) == 0 );
 337              		.loc 1 113 6 is_stmt 1 view .LVU118
 113:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 					configASSERT( ( xWantedSize & portBYTE_ALIGNMENT_MASK ) == 0 );
 338              		.loc 1 113 18 is_stmt 0 view .LVU119
 339 0024 22F00702 		bic	r2, r2, #7
 340              	.LVL26:
 113:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 					configASSERT( ( xWantedSize & portBYTE_ALIGNMENT_MASK ) == 0 );
 341              		.loc 1 113 18 view .LVU120
 342 0028 0832     		adds	r2, r2, #8
 343              	.LVL27:
 114:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 				}
 344              		.loc 1 114 6 is_stmt 1 view .LVU121
 345              	.L26:
 123:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 			}
 346              		.loc 1 123 29 view .LVU122
 126:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 			{
 347              		.loc 1 126 4 view .LVU123
 126:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 			{
 348              		.loc 1 126 6 is_stmt 0 view .LVU124
 349 002a 002A     		cmp	r2, #0
 350 002c 51D0     		beq	.L37
 126:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 			{
 351              		.loc 1 126 45 discriminator 1 view .LVU125
 352 002e 2F4B     		ldr	r3, .L43+8
 353 0030 1B68     		ldr	r3, [r3]
 126:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 			{
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc6tgLS5.s 			page 16


 354              		.loc 1 126 28 discriminator 1 view .LVU126
 355 0032 9342     		cmp	r3, r2
 356 0034 4FD3     		bcc	.L38
 130:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 				pxBlock = xStart.pxNextFreeBlock;
 357              		.loc 1 130 5 is_stmt 1 view .LVU127
 358              	.LVL28:
 131:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 				while( ( pxBlock->xBlockSize < xWantedSize ) && ( pxBlock->pxNextFreeBlock != NULL ) )
 359              		.loc 1 131 5 view .LVU128
 131:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 				while( ( pxBlock->xBlockSize < xWantedSize ) && ( pxBlock->pxNextFreeBlock != NULL ) )
 360              		.loc 1 131 13 is_stmt 0 view .LVU129
 361 0036 2E49     		ldr	r1, .L43+12
 362 0038 0C68     		ldr	r4, [r1]
 363              	.LVL29:
 132:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 				{
 364              		.loc 1 132 5 is_stmt 1 view .LVU130
 132:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 				{
 365              		.loc 1 132 10 is_stmt 0 view .LVU131
 366 003a 04E0     		b	.L28
 367              	.LVL30:
 368              	.L42:
  89:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		}
 369              		.loc 1 89 4 is_stmt 1 view .LVU132
 370 003c FFF7FEFF 		bl	prvHeapInit
 371              	.LVL31:
 372 0040 E5E7     		b	.L24
 373              	.LVL32:
 374              	.L39:
 134:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 					pxBlock = pxBlock->pxNextFreeBlock;
 375              		.loc 1 134 22 is_stmt 0 view .LVU133
 376 0042 2146     		mov	r1, r4
 377              	.LVL33:
 135:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 				}
 378              		.loc 1 135 14 view .LVU134
 379 0044 1C46     		mov	r4, r3
 380              	.LVL34:
 381              	.L28:
 132:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 				{
 382              		.loc 1 132 50 is_stmt 1 view .LVU135
 132:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 				{
 383              		.loc 1 132 21 is_stmt 0 view .LVU136
 384 0046 6368     		ldr	r3, [r4, #4]
 132:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 				{
 385              		.loc 1 132 50 view .LVU137
 386 0048 9342     		cmp	r3, r2
 387 004a 02D2     		bcs	.L27
 132:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 				{
 388              		.loc 1 132 62 discriminator 1 view .LVU138
 389 004c 2368     		ldr	r3, [r4]
 132:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 				{
 390              		.loc 1 132 50 discriminator 1 view .LVU139
 391 004e 002B     		cmp	r3, #0
 392 0050 F7D1     		bne	.L39
 393              	.L27:
 140:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 				{
 394              		.loc 1 140 5 is_stmt 1 view .LVU140
 140:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 				{
 395              		.loc 1 140 17 is_stmt 0 view .LVU141
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc6tgLS5.s 			page 17


 396 0052 244B     		ldr	r3, .L43
 397 0054 1B68     		ldr	r3, [r3]
 140:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 				{
 398              		.loc 1 140 7 view .LVU142
 399 0056 A342     		cmp	r3, r4
 400 0058 3FD0     		beq	.L40
 144:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 401              		.loc 1 144 6 is_stmt 1 view .LVU143
 144:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 402              		.loc 1 144 61 is_stmt 0 view .LVU144
 403 005a 0D68     		ldr	r5, [r1]
 144:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 404              		.loc 1 144 15 view .LVU145
 405 005c 0835     		adds	r5, r5, #8
 406              	.LVL35:
 148:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 407              		.loc 1 148 6 is_stmt 1 view .LVU146
 148:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 408              		.loc 1 148 48 is_stmt 0 view .LVU147
 409 005e 2368     		ldr	r3, [r4]
 148:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 410              		.loc 1 148 39 view .LVU148
 411 0060 0B60     		str	r3, [r1]
 152:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 					{
 412              		.loc 1 152 6 is_stmt 1 view .LVU149
 152:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 					{
 413              		.loc 1 152 19 is_stmt 0 view .LVU150
 414 0062 6368     		ldr	r3, [r4, #4]
 152:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 					{
 415              		.loc 1 152 32 view .LVU151
 416 0064 9B1A     		subs	r3, r3, r2
 152:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 					{
 417              		.loc 1 152 8 view .LVU152
 418 0066 102B     		cmp	r3, #16
 419 0068 10D9     		bls	.L29
 158:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 						configASSERT( ( ( ( size_t ) pxNewBlockLink ) & portBYTE_ALIGNMENT_MASK ) == 0 );
 420              		.loc 1 158 7 is_stmt 1 view .LVU153
 158:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 						configASSERT( ( ( ( size_t ) pxNewBlockLink ) & portBYTE_ALIGNMENT_MASK ) == 0 );
 421              		.loc 1 158 22 is_stmt 0 view .LVU154
 422 006a A018     		adds	r0, r4, r2
 423              	.LVL36:
 159:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 424              		.loc 1 159 7 is_stmt 1 view .LVU155
 425 006c 10F0070F 		tst	r0, #7
 426 0070 08D0     		beq	.L30
 159:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 427              		.loc 1 159 7 discriminator 1 view .LVU156
 428              	.LBB12:
 429              	.LBI12:
 430              		.file 2 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h"
   1:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /*
   2:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * FreeRTOS Kernel V10.3.1
   3:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * Copyright (C) 2020 Amazon.com, Inc. or its affiliates.  All Rights Reserved.
   4:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  *
   5:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * Permission is hereby granted, free of charge, to any person obtaining a copy of
   6:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * this software and associated documentation files (the "Software"), to deal in
   7:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * the Software without restriction, including without limitation the rights to
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc6tgLS5.s 			page 18


   8:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of
   9:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * the Software, and to permit persons to whom the Software is furnished to do so,
  10:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * subject to the following conditions:
  11:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  *
  12:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * The above copyright notice and this permission notice shall be included in all
  13:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * copies or substantial portions of the Software.
  14:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  *
  15:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
  16:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS
  17:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR
  18:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER
  19:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
  20:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
  21:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  *
  22:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * http://www.FreeRTOS.org
  23:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * http://aws.amazon.com/freertos
  24:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  *
  25:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * 1 tab == 4 spaces!
  26:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  */
  27:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
  28:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
  29:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #ifndef PORTMACRO_H
  30:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define PORTMACRO_H
  31:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
  32:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #ifdef __cplusplus
  33:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** extern "C" {
  34:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #endif
  35:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
  36:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /*-----------------------------------------------------------
  37:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * Port specific definitions.
  38:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  *
  39:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * The settings in this file configure FreeRTOS correctly for the
  40:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * given hardware and compiler.
  41:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  *
  42:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * These settings should not be altered.
  43:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  *-----------------------------------------------------------
  44:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  */
  45:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
  46:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /* Type definitions. */
  47:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portCHAR		char
  48:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portFLOAT		float
  49:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portDOUBLE		double
  50:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portLONG		long
  51:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portSHORT		short
  52:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portSTACK_TYPE	uint32_t
  53:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portBASE_TYPE	long
  54:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
  55:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** typedef portSTACK_TYPE StackType_t;
  56:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** typedef long BaseType_t;
  57:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** typedef unsigned long UBaseType_t;
  58:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
  59:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #if( configUSE_16_BIT_TICKS == 1 )
  60:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	typedef uint16_t TickType_t;
  61:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	#define portMAX_DELAY ( TickType_t ) 0xffff
  62:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #else
  63:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	typedef uint32_t TickType_t;
  64:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	#define portMAX_DELAY ( TickType_t ) 0xffffffffUL
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc6tgLS5.s 			page 19


  65:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
  66:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	/* 32-bit tick type on a 32-bit architecture, so reads of the tick count do
  67:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	not need to be guarded with a critical section. */
  68:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	#define portTICK_TYPE_IS_ATOMIC 1
  69:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #endif
  70:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /*-----------------------------------------------------------*/
  71:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
  72:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /* Architecture specifics. */
  73:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portSTACK_GROWTH			( -1 )
  74:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portTICK_PERIOD_MS			( ( TickType_t ) 1000 / configTICK_RATE_HZ )
  75:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portBYTE_ALIGNMENT			8
  76:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /*-----------------------------------------------------------*/
  77:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
  78:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /* Scheduler utilities. */
  79:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portYIELD() 															\
  80:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {																				\
  81:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	/* Set a PendSV to request a context switch. */								\
  82:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	portNVIC_INT_CTRL_REG = portNVIC_PENDSVSET_BIT;								\
  83:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 																				\
  84:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	/* Barriers are normally not required but do ensure the code is completely	\
  85:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	within the specified behaviour for the architecture. */						\
  86:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	__asm volatile( "dsb" ::: "memory" );										\
  87:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	__asm volatile( "isb" );													\
  88:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** }
  89:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
  90:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portNVIC_INT_CTRL_REG		( * ( ( volatile uint32_t * ) 0xe000ed04 ) )
  91:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portNVIC_PENDSVSET_BIT		( 1UL << 28UL )
  92:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portEND_SWITCHING_ISR( xSwitchRequired ) if( xSwitchRequired != pdFALSE ) portYIELD()
  93:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portYIELD_FROM_ISR( x ) portEND_SWITCHING_ISR( x )
  94:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /*-----------------------------------------------------------*/
  95:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
  96:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /* Critical section management. */
  97:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** extern void vPortEnterCritical( void );
  98:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** extern void vPortExitCritical( void );
  99:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portSET_INTERRUPT_MASK_FROM_ISR()		ulPortRaiseBASEPRI()
 100:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portCLEAR_INTERRUPT_MASK_FROM_ISR(x)	vPortSetBASEPRI(x)
 101:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portDISABLE_INTERRUPTS()				vPortRaiseBASEPRI()
 102:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portENABLE_INTERRUPTS()					vPortSetBASEPRI(0)
 103:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portENTER_CRITICAL()					vPortEnterCritical()
 104:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portEXIT_CRITICAL()						vPortExitCritical()
 105:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 106:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /*-----------------------------------------------------------*/
 107:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 108:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /* Task function macros as described on the FreeRTOS.org WEB site.  These are
 109:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** not necessary for to use this port.  They are defined so the common demo files
 110:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** (which build with all the ports) will build. */
 111:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portTASK_FUNCTION_PROTO( vFunction, pvParameters ) void vFunction( void *pvParameters )
 112:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portTASK_FUNCTION( vFunction, pvParameters ) void vFunction( void *pvParameters )
 113:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /*-----------------------------------------------------------*/
 114:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 115:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /* Tickless idle/low power functionality. */
 116:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #ifndef portSUPPRESS_TICKS_AND_SLEEP
 117:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	extern void vPortSuppressTicksAndSleep( TickType_t xExpectedIdleTime );
 118:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	#define portSUPPRESS_TICKS_AND_SLEEP( xExpectedIdleTime ) vPortSuppressTicksAndSleep( xExpectedIdl
 119:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #endif
 120:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /*-----------------------------------------------------------*/
 121:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc6tgLS5.s 			page 20


 122:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /* Architecture specific optimisations. */
 123:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #ifndef configUSE_PORT_OPTIMISED_TASK_SELECTION
 124:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	#define configUSE_PORT_OPTIMISED_TASK_SELECTION 1
 125:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #endif
 126:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 127:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #if configUSE_PORT_OPTIMISED_TASK_SELECTION == 1
 128:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 129:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	/* Generic helper function. */
 130:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	__attribute__( ( always_inline ) ) static inline uint8_t ucPortCountLeadingZeros( uint32_t ulBitma
 131:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	{
 132:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	uint8_t ucReturn;
 133:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 134:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 		__asm volatile ( "clz %0, %1" : "=r" ( ucReturn ) : "r" ( ulBitmap ) : "memory" );
 135:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 		return ucReturn;
 136:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	}
 137:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 138:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	/* Check the configuration. */
 139:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	#if( configMAX_PRIORITIES > 32 )
 140:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 		#error configUSE_PORT_OPTIMISED_TASK_SELECTION can only be set to 1 when configMAX_PRIORITIES is 
 141:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	#endif
 142:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 143:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	/* Store/clear the ready priorities in a bit map. */
 144:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	#define portRECORD_READY_PRIORITY( uxPriority, uxReadyPriorities ) ( uxReadyPriorities ) |= ( 1UL 
 145:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	#define portRESET_READY_PRIORITY( uxPriority, uxReadyPriorities ) ( uxReadyPriorities ) &= ~( 1UL 
 146:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 147:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	/*-----------------------------------------------------------*/
 148:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 149:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	#define portGET_HIGHEST_PRIORITY( uxTopPriority, uxReadyPriorities ) uxTopPriority = ( 31UL - ( ui
 150:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 151:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #endif /* configUSE_PORT_OPTIMISED_TASK_SELECTION */
 152:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 153:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /*-----------------------------------------------------------*/
 154:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 155:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #ifdef configASSERT
 156:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	void vPortValidateInterruptPriority( void );
 157:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	#define portASSERT_IF_INTERRUPT_PRIORITY_INVALID() 	vPortValidateInterruptPriority()
 158:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #endif
 159:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 160:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /* portNOP() is not required by this port. */
 161:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portNOP()
 162:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 163:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portINLINE	__inline
 164:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 165:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #ifndef portFORCE_INLINE
 166:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	#define portFORCE_INLINE inline __attribute__(( always_inline))
 167:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #endif
 168:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 169:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** portFORCE_INLINE static BaseType_t xPortIsInsideInterrupt( void )
 170:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 171:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** uint32_t ulCurrentInterrupt;
 172:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** BaseType_t xReturn;
 173:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 174:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	/* Obtain the number of the currently executing interrupt. */
 175:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	__asm volatile( "mrs %0, ipsr" : "=r"( ulCurrentInterrupt ) :: "memory" );
 176:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 177:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	if( ulCurrentInterrupt == 0 )
 178:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	{
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc6tgLS5.s 			page 21


 179:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 		xReturn = pdFALSE;
 180:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	}
 181:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	else
 182:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	{
 183:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 		xReturn = pdTRUE;
 184:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	}
 185:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 186:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	return xReturn;
 187:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** }
 188:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 189:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /*-----------------------------------------------------------*/
 190:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** portFORCE_INLINE static void vPortRaiseBASEPRI( void )
 431              		.loc 2 191 30 view .LVU157
 432              	.LBB13:
 192:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** uint32_t ulNewBASEPRI;
 433              		.loc 2 193 1 view .LVU158
 194:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 195:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	__asm volatile
 434              		.loc 2 195 2 view .LVU159
 435              		.syntax unified
 436              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 437 0072 4FF05003 			mov r3, #80												
 438 0076 83F31188 		msr basepri, r3											
 439 007a BFF36F8F 		isb														
 440 007e BFF34F8F 		dsb														
 441              	
 442              	@ 0 "" 2
 443              		.thumb
 444              		.syntax unified
 445              	.L31:
 446              	.LBE13:
 447              	.LBE12:
 159:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 448              		.loc 1 159 7 discriminator 3 view .LVU160
 159:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 449              		.loc 1 159 7 discriminator 3 view .LVU161
 450 0082 FEE7     		b	.L31
 451              	.L30:
 159:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 452              		.loc 1 159 87 discriminator 2 view .LVU162
 163:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 						pxBlock->xBlockSize = xWantedSize;
 453              		.loc 1 163 7 view .LVU163
 163:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 						pxBlock->xBlockSize = xWantedSize;
 454              		.loc 1 163 34 is_stmt 0 view .LVU164
 455 0084 4360     		str	r3, [r0, #4]
 164:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 456              		.loc 1 164 7 is_stmt 1 view .LVU165
 164:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 457              		.loc 1 164 27 is_stmt 0 view .LVU166
 458 0086 6260     		str	r2, [r4, #4]
 167:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 					}
 459              		.loc 1 167 7 is_stmt 1 view .LVU167
 460 0088 FFF7FEFF 		bl	prvInsertBlockIntoFreeList
 461              	.LVL37:
 462              	.L29:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc6tgLS5.s 			page 22


 171:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 					}
 463              		.loc 1 171 31 view .LVU168
 174:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 464              		.loc 1 174 6 view .LVU169
 174:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 465              		.loc 1 174 36 is_stmt 0 view .LVU170
 466 008c 6268     		ldr	r2, [r4, #4]
 174:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 467              		.loc 1 174 26 view .LVU171
 468 008e 1749     		ldr	r1, .L43+8
 469 0090 0B68     		ldr	r3, [r1]
 470 0092 9B1A     		subs	r3, r3, r2
 471 0094 0B60     		str	r3, [r1]
 176:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 					{
 472              		.loc 1 176 6 is_stmt 1 view .LVU172
 176:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 					{
 473              		.loc 1 176 30 is_stmt 0 view .LVU173
 474 0096 1749     		ldr	r1, .L43+16
 475 0098 0968     		ldr	r1, [r1]
 176:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 					{
 476              		.loc 1 176 8 view .LVU174
 477 009a 8B42     		cmp	r3, r1
 478 009c 01D2     		bcs	.L32
 178:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 					}
 479              		.loc 1 178 7 is_stmt 1 view .LVU175
 178:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 					}
 480              		.loc 1 178 38 is_stmt 0 view .LVU176
 481 009e 1549     		ldr	r1, .L43+16
 482 00a0 0B60     		str	r3, [r1]
 483              	.L32:
 182:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 					}
 484              		.loc 1 182 31 is_stmt 1 view .LVU177
 187:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 					pxBlock->pxNextFreeBlock = NULL;
 485              		.loc 1 187 6 view .LVU178
 187:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 					pxBlock->pxNextFreeBlock = NULL;
 486              		.loc 1 187 26 is_stmt 0 view .LVU179
 487 00a2 114B     		ldr	r3, .L43+4
 488 00a4 1B68     		ldr	r3, [r3]
 489 00a6 1343     		orrs	r3, r3, r2
 490 00a8 6360     		str	r3, [r4, #4]
 188:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 				}
 491              		.loc 1 188 6 is_stmt 1 view .LVU180
 188:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 				}
 492              		.loc 1 188 31 is_stmt 0 view .LVU181
 493 00aa 0023     		movs	r3, #0
 494 00ac 2360     		str	r3, [r4]
 495 00ae 02E0     		b	.L25
 496              	.LVL38:
 497              	.L35:
  81:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 498              		.loc 1 81 7 view .LVU182
 499 00b0 0025     		movs	r5, #0
 500 00b2 00E0     		b	.L25
 501              	.L36:
 502 00b4 0025     		movs	r5, #0
 503              	.LVL39:
 504              	.L25:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc6tgLS5.s 			page 23


 202:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		}
 505              		.loc 1 202 28 is_stmt 1 view .LVU183
 205:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	}
 506              		.loc 1 205 39 view .LVU184
 207:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 507              		.loc 1 207 2 view .LVU185
 207:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 508              		.loc 1 207 11 is_stmt 0 view .LVU186
 509 00b6 FFF7FEFF 		bl	xTaskResumeAll
 510              	.LVL40:
 223:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	return pvReturn;
 511              		.loc 1 223 2 is_stmt 1 view .LVU187
 512 00ba 15F0070F 		tst	r5, #7
 513 00be 0ED0     		beq	.L23
 223:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	return pvReturn;
 514              		.loc 1 223 2 discriminator 1 view .LVU188
 515              	.LBB14:
 516              	.LBI14:
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 517              		.loc 2 191 30 view .LVU189
 518              	.LBB15:
 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 519              		.loc 2 193 1 view .LVU190
 520              		.loc 2 195 2 view .LVU191
 521              		.syntax unified
 522              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 523 00c0 4FF05003 			mov r3, #80												
 524 00c4 83F31188 		msr basepri, r3											
 525 00c8 BFF36F8F 		isb														
 526 00cc BFF34F8F 		dsb														
 527              	
 528              	@ 0 "" 2
 529              		.thumb
 530              		.syntax unified
 531              	.L34:
 532              	.LBE15:
 533              	.LBE14:
 223:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	return pvReturn;
 534              		.loc 1 223 2 discriminator 3 view .LVU192
 223:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	return pvReturn;
 535              		.loc 1 223 2 discriminator 3 view .LVU193
 536 00d0 FEE7     		b	.L34
 537              	.LVL41:
 538              	.L37:
  81:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 539              		.loc 1 81 7 is_stmt 0 view .LVU194
 540 00d2 0025     		movs	r5, #0
 541 00d4 EFE7     		b	.L25
 542              	.L38:
 543 00d6 0025     		movs	r5, #0
 544 00d8 EDE7     		b	.L25
 545              	.LVL42:
 546              	.L40:
  81:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 547              		.loc 1 81 7 view .LVU195
 548 00da 0025     		movs	r5, #0
 549 00dc EBE7     		b	.L25
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc6tgLS5.s 			page 24


 550              	.LVL43:
 551              	.L23:
 225:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** /*-----------------------------------------------------------*/
 552              		.loc 1 225 1 view .LVU196
 553 00de 2846     		mov	r0, r5
 554 00e0 38BD     		pop	{r3, r4, r5, pc}
 555              	.LVL44:
 556              	.L44:
 225:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** /*-----------------------------------------------------------*/
 557              		.loc 1 225 1 view .LVU197
 558 00e2 00BF     		.align	2
 559              	.L43:
 560 00e4 00000000 		.word	pxEnd
 561 00e8 00000000 		.word	xBlockAllocatedBit
 562 00ec 00000000 		.word	xFreeBytesRemaining
 563 00f0 00000000 		.word	xStart
 564 00f4 00000000 		.word	xMinimumEverFreeBytesRemaining
 565              		.cfi_endproc
 566              	.LFE4:
 568              		.section	.text.vPortFreeMicroROS,"ax",%progbits
 569              		.align	1
 570              		.global	vPortFreeMicroROS
 571              		.syntax unified
 572              		.thumb
 573              		.thumb_func
 575              	vPortFreeMicroROS:
 576              	.LVL45:
 577              	.LFB5:
 229:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** uint8_t *puc = ( uint8_t * ) pv;
 578              		.loc 1 229 1 is_stmt 1 view -0
 579              		.cfi_startproc
 580              		@ args = 0, pretend = 0, frame = 0
 581              		@ frame_needed = 0, uses_anonymous_args = 0
 230:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** BlockLink_t *pxLink;
 582              		.loc 1 230 1 view .LVU199
 231:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 583              		.loc 1 231 1 view .LVU200
 233:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	{
 584              		.loc 1 233 2 view .LVU201
 233:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	{
 585              		.loc 1 233 4 is_stmt 0 view .LVU202
 586 0000 80B3     		cbz	r0, .L52
 229:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** uint8_t *puc = ( uint8_t * ) pv;
 587              		.loc 1 229 1 view .LVU203
 588 0002 38B5     		push	{r3, r4, r5, lr}
 589              	.LCFI5:
 590              		.cfi_def_cfa_offset 16
 591              		.cfi_offset 3, -16
 592              		.cfi_offset 4, -12
 593              		.cfi_offset 5, -8
 594              		.cfi_offset 14, -4
 595 0004 0446     		mov	r4, r0
 237:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 596              		.loc 1 237 3 is_stmt 1 view .LVU204
 237:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 597              		.loc 1 237 7 is_stmt 0 view .LVU205
 598 0006 A0F10805 		sub	r5, r0, #8
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc6tgLS5.s 			page 25


 599              	.LVL46:
 240:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 600              		.loc 1 240 3 is_stmt 1 view .LVU206
 243:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		configASSERT( pxLink->pxNextFreeBlock == NULL );
 601              		.loc 1 243 3 view .LVU207
 602 000a 50F8043C 		ldr	r3, [r0, #-4]
 603 000e 164A     		ldr	r2, .L55
 604 0010 1268     		ldr	r2, [r2]
 605 0012 1342     		tst	r3, r2
 606 0014 08D1     		bne	.L47
 243:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		configASSERT( pxLink->pxNextFreeBlock == NULL );
 607              		.loc 1 243 3 discriminator 1 view .LVU208
 608              	.LBB16:
 609              	.LBI16:
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 610              		.loc 2 191 30 view .LVU209
 611              	.LBB17:
 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 612              		.loc 2 193 1 view .LVU210
 613              		.loc 2 195 2 view .LVU211
 614              		.syntax unified
 615              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 616 0016 4FF05003 			mov r3, #80												
 617 001a 83F31188 		msr basepri, r3											
 618 001e BFF36F8F 		isb														
 619 0022 BFF34F8F 		dsb														
 620              	
 621              	@ 0 "" 2
 622              		.thumb
 623              		.syntax unified
 624              	.L48:
 625              	.LBE17:
 626              	.LBE16:
 243:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		configASSERT( pxLink->pxNextFreeBlock == NULL );
 627              		.loc 1 243 3 discriminator 3 view .LVU212
 243:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		configASSERT( pxLink->pxNextFreeBlock == NULL );
 628              		.loc 1 243 3 discriminator 3 view .LVU213
 629 0026 FEE7     		b	.L48
 630              	.L47:
 243:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		configASSERT( pxLink->pxNextFreeBlock == NULL );
 631              		.loc 1 243 67 discriminator 2 view .LVU214
 244:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 632              		.loc 1 244 3 view .LVU215
 633 0028 50F8081C 		ldr	r1, [r0, #-8]
 634 002c 41B1     		cbz	r1, .L49
 244:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 635              		.loc 1 244 3 discriminator 1 view .LVU216
 636              	.LBB18:
 637              	.LBI18:
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 638              		.loc 2 191 30 view .LVU217
 639              	.LBB19:
 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 640              		.loc 2 193 1 view .LVU218
 641              		.loc 2 195 2 view .LVU219
 642              		.syntax unified
 643              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc6tgLS5.s 			page 26


 644 002e 4FF05003 			mov r3, #80												
 645 0032 83F31188 		msr basepri, r3											
 646 0036 BFF36F8F 		isb														
 647 003a BFF34F8F 		dsb														
 648              	
 649              	@ 0 "" 2
 650              		.thumb
 651              		.syntax unified
 652              	.L50:
 653              	.LBE19:
 654              	.LBE18:
 244:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 655              		.loc 1 244 3 discriminator 3 view .LVU220
 244:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 656              		.loc 1 244 3 discriminator 3 view .LVU221
 657 003e FEE7     		b	.L50
 658              	.L49:
 244:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 659              		.loc 1 244 50 discriminator 2 view .LVU222
 246:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		{
 660              		.loc 1 246 3 view .LVU223
 248:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 			{
 661              		.loc 1 248 4 view .LVU224
 252:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 662              		.loc 1 252 5 view .LVU225
 252:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 663              		.loc 1 252 24 is_stmt 0 view .LVU226
 664 0040 23EA0203 		bic	r3, r3, r2
 665 0044 40F8043C 		str	r3, [r0, #-4]
 254:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 				{
 666              		.loc 1 254 5 is_stmt 1 view .LVU227
 667 0048 FFF7FEFF 		bl	vTaskSuspendAll
 668              	.LVL47:
 257:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 					traceFREE( pv, pxLink->xBlockSize );
 669              		.loc 1 257 6 view .LVU228
 257:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 					traceFREE( pv, pxLink->xBlockSize );
 670              		.loc 1 257 35 is_stmt 0 view .LVU229
 671 004c 54F8041C 		ldr	r1, [r4, #-4]
 257:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 					traceFREE( pv, pxLink->xBlockSize );
 672              		.loc 1 257 26 view .LVU230
 673 0050 064A     		ldr	r2, .L55+4
 674 0052 1368     		ldr	r3, [r2]
 675 0054 0B44     		add	r3, r3, r1
 676 0056 1360     		str	r3, [r2]
 258:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 					prvInsertBlockIntoFreeList( ( ( BlockLink_t * ) pxLink ) );
 677              		.loc 1 258 41 is_stmt 1 view .LVU231
 259:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 				}
 678              		.loc 1 259 6 view .LVU232
 679 0058 2846     		mov	r0, r5
 680 005a FFF7FEFF 		bl	prvInsertBlockIntoFreeList
 681              	.LVL48:
 261:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 			}
 682              		.loc 1 261 5 view .LVU233
 261:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 			}
 683              		.loc 1 261 14 is_stmt 0 view .LVU234
 684 005e FFF7FEFF 		bl	xTaskResumeAll
 685              	.LVL49:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc6tgLS5.s 			page 27


 270:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		}
 686              		.loc 1 270 28 is_stmt 1 view .LVU235
 273:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** /*-----------------------------------------------------------*/
 687              		.loc 1 273 1 is_stmt 0 view .LVU236
 688 0062 38BD     		pop	{r3, r4, r5, pc}
 689              	.LVL50:
 690              	.L52:
 691              	.LCFI6:
 692              		.cfi_def_cfa_offset 0
 693              		.cfi_restore 3
 694              		.cfi_restore 4
 695              		.cfi_restore 5
 696              		.cfi_restore 14
 273:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** /*-----------------------------------------------------------*/
 697              		.loc 1 273 1 view .LVU237
 698 0064 7047     		bx	lr
 699              	.L56:
 700 0066 00BF     		.align	2
 701              	.L55:
 702 0068 00000000 		.word	xBlockAllocatedBit
 703 006c 00000000 		.word	xFreeBytesRemaining
 704              		.cfi_endproc
 705              	.LFE5:
 707              		.section	.text.getBlockSize,"ax",%progbits
 708              		.align	1
 709              		.global	getBlockSize
 710              		.syntax unified
 711              		.thumb
 712              		.thumb_func
 714              	getBlockSize:
 715              	.LVL51:
 716              	.LFB6:
 278:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 717              		.loc 1 278 1 is_stmt 1 view -0
 718              		.cfi_startproc
 719              		@ args = 0, pretend = 0, frame = 0
 720              		@ frame_needed = 0, uses_anonymous_args = 0
 721              		@ link register save eliminated.
 280:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	BlockLink_t *pxLink;
 722              		.loc 1 280 2 view .LVU239
 281:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 723              		.loc 1 281 2 view .LVU240
 283:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	pxLink = ( void * ) puc;
 724              		.loc 1 283 2 view .LVU241
 284:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 725              		.loc 1 284 2 view .LVU242
 286:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 726              		.loc 1 286 2 view .LVU243
 286:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 727              		.loc 1 286 23 is_stmt 0 view .LVU244
 728 0000 50F8043C 		ldr	r3, [r0, #-4]
 286:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 729              		.loc 1 286 38 view .LVU245
 730 0004 024A     		ldr	r2, .L58
 731 0006 1068     		ldr	r0, [r2]
 732              	.LVL52:
 288:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** }
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc6tgLS5.s 			page 28


 733              		.loc 1 288 2 is_stmt 1 view .LVU246
 289:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** /*-----------------------------------------------------------*/
 734              		.loc 1 289 1 is_stmt 0 view .LVU247
 735 0008 23EA0000 		bic	r0, r3, r0
 736              	.LVL53:
 289:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** /*-----------------------------------------------------------*/
 737              		.loc 1 289 1 view .LVU248
 738 000c 7047     		bx	lr
 739              	.L59:
 740 000e 00BF     		.align	2
 741              	.L58:
 742 0010 00000000 		.word	xBlockAllocatedBit
 743              		.cfi_endproc
 744              	.LFE6:
 746              		.section	.text.pvPortReallocMicroROS,"ax",%progbits
 747              		.align	1
 748              		.global	pvPortReallocMicroROS
 749              		.syntax unified
 750              		.thumb
 751              		.thumb_func
 753              	pvPortReallocMicroROS:
 754              	.LVL54:
 755              	.LFB7:
 293:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	vTaskSuspendAll();
 756              		.loc 1 293 1 is_stmt 1 view -0
 757              		.cfi_startproc
 758              		@ args = 0, pretend = 0, frame = 0
 759              		@ frame_needed = 0, uses_anonymous_args = 0
 293:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	vTaskSuspendAll();
 760              		.loc 1 293 1 is_stmt 0 view .LVU250
 761 0000 70B5     		push	{r4, r5, r6, lr}
 762              	.LCFI7:
 763              		.cfi_def_cfa_offset 16
 764              		.cfi_offset 4, -16
 765              		.cfi_offset 5, -12
 766              		.cfi_offset 6, -8
 767              		.cfi_offset 14, -4
 768 0002 0446     		mov	r4, r0
 769 0004 0D46     		mov	r5, r1
 294:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 770              		.loc 1 294 2 is_stmt 1 view .LVU251
 771 0006 FFF7FEFF 		bl	vTaskSuspendAll
 772              	.LVL55:
 296:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	if (newmem != NULL && pv != NULL)
 773              		.loc 1 296 2 view .LVU252
 296:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	if (newmem != NULL && pv != NULL)
 774              		.loc 1 296 18 is_stmt 0 view .LVU253
 775 000a 2846     		mov	r0, r5
 776 000c FFF7FEFF 		bl	pvPortMallocMicroROS
 777              	.LVL56:
 297:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	{
 778              		.loc 1 297 2 is_stmt 1 view .LVU254
 297:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	{
 779              		.loc 1 297 13 is_stmt 0 view .LVU255
 780 0010 0646     		mov	r6, r0
 297:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	{
 781              		.loc 1 297 5 view .LVU256
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc6tgLS5.s 			page 29


 782 0012 0028     		cmp	r0, #0
 783 0014 18BF     		it	ne
 784 0016 002C     		cmpne	r4, #0
 785 0018 0ED0     		beq	.L61
 786              	.LBB20:
 299:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		if (xWantedSize < count)
 787              		.loc 1 299 3 is_stmt 1 view .LVU257
 299:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		if (xWantedSize < count)
 788              		.loc 1 299 18 is_stmt 0 view .LVU258
 789 001a 2046     		mov	r0, r4
 790              	.LVL57:
 299:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		if (xWantedSize < count)
 791              		.loc 1 299 18 view .LVU259
 792 001c FFF7FEFF 		bl	getBlockSize
 793              	.LVL58:
 299:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		if (xWantedSize < count)
 794              		.loc 1 299 10 discriminator 1 view .LVU260
 795 0020 0838     		subs	r0, r0, #8
 796              	.LVL59:
 300:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		{
 797              		.loc 1 300 3 is_stmt 1 view .LVU261
 300:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		{
 798              		.loc 1 300 6 is_stmt 0 view .LVU262
 799 0022 8542     		cmp	r5, r0
 800 0024 00D3     		bcc	.L62
 299:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 		if (xWantedSize < count)
 801              		.loc 1 299 10 discriminator 1 view .LVU263
 802 0026 0546     		mov	r5, r0
 803              	.LVL60:
 804              	.L62:
 304:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 805              		.loc 1 304 3 is_stmt 1 view .LVU264
 806 0028 2A46     		mov	r2, r5
 807 002a 2146     		mov	r1, r4
 808 002c 3046     		mov	r0, r6
 809 002e FFF7FEFF 		bl	memcpy
 810              	.LVL61:
 306:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	}
 811              		.loc 1 306 3 view .LVU265
 812 0032 2046     		mov	r0, r4
 813 0034 FFF7FEFF 		bl	vPortFreeMicroROS
 814              	.LVL62:
 815              	.L61:
 306:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	}
 816              		.loc 1 306 3 is_stmt 0 view .LVU266
 817              	.LBE20:
 309:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 818              		.loc 1 309 2 is_stmt 1 view .LVU267
 309:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 819              		.loc 1 309 11 is_stmt 0 view .LVU268
 820 0038 FFF7FEFF 		bl	xTaskResumeAll
 821              	.LVL63:
 311:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** }
 822              		.loc 1 311 2 is_stmt 1 view .LVU269
 312:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** /*-----------------------------------------------------------*/
 823              		.loc 1 312 1 is_stmt 0 view .LVU270
 824 003c 3046     		mov	r0, r6
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc6tgLS5.s 			page 30


 825 003e 70BD     		pop	{r4, r5, r6, pc}
 312:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** /*-----------------------------------------------------------*/
 826              		.loc 1 312 1 view .LVU271
 827              		.cfi_endproc
 828              	.LFE7:
 830              		.section	.text.pvPortCallocMicroROS,"ax",%progbits
 831              		.align	1
 832              		.global	pvPortCallocMicroROS
 833              		.syntax unified
 834              		.thumb
 835              		.thumb_func
 837              	pvPortCallocMicroROS:
 838              	.LVL64:
 839              	.LFB8:
 316:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	vTaskSuspendAll();
 840              		.loc 1 316 1 is_stmt 1 view -0
 841              		.cfi_startproc
 842              		@ args = 0, pretend = 0, frame = 0
 843              		@ frame_needed = 0, uses_anonymous_args = 0
 316:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	vTaskSuspendAll();
 844              		.loc 1 316 1 is_stmt 0 view .LVU273
 845 0000 38B5     		push	{r3, r4, r5, lr}
 846              	.LCFI8:
 847              		.cfi_def_cfa_offset 16
 848              		.cfi_offset 3, -16
 849              		.cfi_offset 4, -12
 850              		.cfi_offset 5, -8
 851              		.cfi_offset 14, -4
 852 0002 0546     		mov	r5, r0
 853 0004 0C46     		mov	r4, r1
 317:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	size_t count = xWantedSize*num;
 854              		.loc 1 317 2 is_stmt 1 view .LVU274
 855 0006 FFF7FEFF 		bl	vTaskSuspendAll
 856              	.LVL65:
 318:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 857              		.loc 1 318 2 view .LVU275
 318:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 858              		.loc 1 318 9 is_stmt 0 view .LVU276
 859 000a 05FB04F4 		mul	r4, r5, r4
 860              	.LVL66:
 320:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c ****   	char *in_dest = (char*)mem;
 861              		.loc 1 320 2 is_stmt 1 view .LVU277
 320:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c ****   	char *in_dest = (char*)mem;
 862              		.loc 1 320 15 is_stmt 0 view .LVU278
 863 000e 2046     		mov	r0, r4
 864 0010 FFF7FEFF 		bl	pvPortMallocMicroROS
 865              	.LVL67:
 866 0014 0546     		mov	r5, r0
 867              	.LVL68:
 321:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 868              		.loc 1 321 4 is_stmt 1 view .LVU279
 323:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c ****     	*in_dest++ = 0;
 869              		.loc 1 323 4 view .LVU280
 321:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 870              		.loc 1 321 10 is_stmt 0 view .LVU281
 871 0016 0346     		mov	r3, r0
 323:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c ****     	*in_dest++ = 0;
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc6tgLS5.s 			page 31


 872              		.loc 1 323 9 view .LVU282
 873 0018 03E0     		b	.L65
 874              	.LVL69:
 875              	.L66:
 324:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 876              		.loc 1 324 6 is_stmt 1 view .LVU283
 324:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 
 877              		.loc 1 324 17 is_stmt 0 view .LVU284
 878 001a 0021     		movs	r1, #0
 879 001c 03F8011B 		strb	r1, [r3], #1
 880              	.LVL70:
 323:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c ****     	*in_dest++ = 0;
 881              		.loc 1 323 15 view .LVU285
 882 0020 1446     		mov	r4, r2
 883              	.LVL71:
 884              	.L65:
 323:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c ****     	*in_dest++ = 0;
 885              		.loc 1 323 10 is_stmt 1 view .LVU286
 323:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c ****     	*in_dest++ = 0;
 886              		.loc 1 323 15 is_stmt 0 view .LVU287
 887 0022 621E     		subs	r2, r4, #1
 888              	.LVL72:
 323:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c ****     	*in_dest++ = 0;
 889              		.loc 1 323 10 view .LVU288
 890 0024 002C     		cmp	r4, #0
 891 0026 F8D1     		bne	.L66
 326:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c ****   	return mem;
 892              		.loc 1 326 2 is_stmt 1 view .LVU289
 326:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c ****   	return mem;
 893              		.loc 1 326 11 is_stmt 0 view .LVU290
 894 0028 FFF7FEFF 		bl	xTaskResumeAll
 895              	.LVL73:
 327:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** }
 896              		.loc 1 327 4 is_stmt 1 view .LVU291
 328:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** /*-----------------------------------------------------------*/
 897              		.loc 1 328 1 is_stmt 0 view .LVU292
 898 002c 2846     		mov	r0, r5
 899 002e 38BD     		pop	{r3, r4, r5, pc}
 328:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** /*-----------------------------------------------------------*/
 900              		.loc 1 328 1 view .LVU293
 901              		.cfi_endproc
 902              	.LFE8:
 904              		.section	.text.xPortGetFreeHeapSizeMicroROS,"ax",%progbits
 905              		.align	1
 906              		.global	xPortGetFreeHeapSizeMicroROS
 907              		.syntax unified
 908              		.thumb
 909              		.thumb_func
 911              	xPortGetFreeHeapSizeMicroROS:
 912              	.LFB9:
 332:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	return xFreeBytesRemaining;
 913              		.loc 1 332 1 is_stmt 1 view -0
 914              		.cfi_startproc
 915              		@ args = 0, pretend = 0, frame = 0
 916              		@ frame_needed = 0, uses_anonymous_args = 0
 917              		@ link register save eliminated.
 333:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** }
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc6tgLS5.s 			page 32


 918              		.loc 1 333 2 view .LVU295
 334:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** /*-----------------------------------------------------------*/
 919              		.loc 1 334 1 is_stmt 0 view .LVU296
 920 0000 014B     		ldr	r3, .L69
 921 0002 1868     		ldr	r0, [r3]
 922 0004 7047     		bx	lr
 923              	.L70:
 924 0006 00BF     		.align	2
 925              	.L69:
 926 0008 00000000 		.word	xFreeBytesRemaining
 927              		.cfi_endproc
 928              	.LFE9:
 930              		.section	.text.xPortGetMinimumEverFreeHeapSizeMicroROS,"ax",%progbits
 931              		.align	1
 932              		.global	xPortGetMinimumEverFreeHeapSizeMicroROS
 933              		.syntax unified
 934              		.thumb
 935              		.thumb_func
 937              	xPortGetMinimumEverFreeHeapSizeMicroROS:
 938              	.LFB10:
 338:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	return xMinimumEverFreeBytesRemaining;
 939              		.loc 1 338 1 is_stmt 1 view -0
 940              		.cfi_startproc
 941              		@ args = 0, pretend = 0, frame = 0
 942              		@ frame_needed = 0, uses_anonymous_args = 0
 943              		@ link register save eliminated.
 339:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** }
 944              		.loc 1 339 2 view .LVU298
 340:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** /*-----------------------------------------------------------*/
 945              		.loc 1 340 1 is_stmt 0 view .LVU299
 946 0000 014B     		ldr	r3, .L72
 947 0002 1868     		ldr	r0, [r3]
 948 0004 7047     		bx	lr
 949              	.L73:
 950 0006 00BF     		.align	2
 951              	.L72:
 952 0008 00000000 		.word	xMinimumEverFreeBytesRemaining
 953              		.cfi_endproc
 954              	.LFE10:
 956              		.section	.text.vPortInitialiseBlocksMicroROS,"ax",%progbits
 957              		.align	1
 958              		.global	vPortInitialiseBlocksMicroROS
 959              		.syntax unified
 960              		.thumb
 961              		.thumb_func
 963              	vPortInitialiseBlocksMicroROS:
 964              	.LFB11:
 344:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** 	/* This just exists to keep the linker quiet. */
 965              		.loc 1 344 1 is_stmt 1 view -0
 966              		.cfi_startproc
 967              		@ args = 0, pretend = 0, frame = 0
 968              		@ frame_needed = 0, uses_anonymous_args = 0
 969              		@ link register save eliminated.
 346:micro_ros_stm32cubemx_utils/extra_sources/custom_memory_manager.c **** /*-----------------------------------------------------------*/
 970              		.loc 1 346 1 view .LVU301
 971 0000 7047     		bx	lr
 972              		.cfi_endproc
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc6tgLS5.s 			page 33


 973              	.LFE11:
 975              		.section	.bss.xBlockAllocatedBit,"aw",%nobits
 976              		.align	2
 979              	xBlockAllocatedBit:
 980 0000 00000000 		.space	4
 981              		.section	.bss.xMinimumEverFreeBytesRemaining,"aw",%nobits
 982              		.align	2
 985              	xMinimumEverFreeBytesRemaining:
 986 0000 00000000 		.space	4
 987              		.section	.bss.xFreeBytesRemaining,"aw",%nobits
 988              		.align	2
 991              	xFreeBytesRemaining:
 992 0000 00000000 		.space	4
 993              		.section	.bss.pxEnd,"aw",%nobits
 994              		.align	2
 997              	pxEnd:
 998 0000 00000000 		.space	4
 999              		.section	.bss.xStart,"aw",%nobits
 1000              		.align	2
 1003              	xStart:
 1004 0000 00000000 		.space	8
 1004      00000000 
 1005              		.section	.bss.ucHeap,"aw",%nobits
 1006              		.align	2
 1009              	ucHeap:
 1010 0000 00000000 		.space	15360
 1010      00000000 
 1010      00000000 
 1010      00000000 
 1010      00000000 
 1011              		.text
 1012              	.Letext0:
 1013              		.file 3 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin/../lib/gcc/arm-none-eabi/13.3.1/inc
 1014              		.file 4 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin/../lib/gcc/arm-none-eabi/13.3.1/../
 1015              		.file 5 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin/../lib/gcc/arm-none-eabi/13.3.1/../
 1016              		.file 6 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin/../lib/gcc/arm-none-eabi/13.3.1/../
 1017              		.file 7 "Middlewares/Third_Party/FreeRTOS/Source/include/task.h"
 1018              		.file 8 "<built-in>"
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc6tgLS5.s 			page 34


DEFINED SYMBOLS
                            *ABS*:00000000 custom_memory_manager.c
C:\Users\<USER>\AppData\Local\Temp\cc6tgLS5.s:20     .text.prvHeapInit:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cc6tgLS5.s:25     .text.prvHeapInit:00000000 prvHeapInit
C:\Users\<USER>\AppData\Local\Temp\cc6tgLS5.s:130    .text.prvHeapInit:0000004c $d
C:\Users\<USER>\AppData\Local\Temp\cc6tgLS5.s:1009   .bss.ucHeap:00000000 ucHeap
C:\Users\<USER>\AppData\Local\Temp\cc6tgLS5.s:1003   .bss.xStart:00000000 xStart
C:\Users\<USER>\AppData\Local\Temp\cc6tgLS5.s:997    .bss.pxEnd:00000000 pxEnd
C:\Users\<USER>\AppData\Local\Temp\cc6tgLS5.s:985    .bss.xMinimumEverFreeBytesRemaining:00000000 xMinimumEverFreeBytesRemaining
C:\Users\<USER>\AppData\Local\Temp\cc6tgLS5.s:991    .bss.xFreeBytesRemaining:00000000 xFreeBytesRemaining
C:\Users\<USER>\AppData\Local\Temp\cc6tgLS5.s:979    .bss.xBlockAllocatedBit:00000000 xBlockAllocatedBit
C:\Users\<USER>\AppData\Local\Temp\cc6tgLS5.s:140    .text.prvInsertBlockIntoFreeList:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cc6tgLS5.s:145    .text.prvInsertBlockIntoFreeList:00000000 prvInsertBlockIntoFreeList
C:\Users\<USER>\AppData\Local\Temp\cc6tgLS5.s:276    .text.prvInsertBlockIntoFreeList:0000005c $d
C:\Users\<USER>\AppData\Local\Temp\cc6tgLS5.s:282    .text.pvPortMallocMicroROS:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cc6tgLS5.s:288    .text.pvPortMallocMicroROS:00000000 pvPortMallocMicroROS
C:\Users\<USER>\AppData\Local\Temp\cc6tgLS5.s:560    .text.pvPortMallocMicroROS:000000e4 $d
C:\Users\<USER>\AppData\Local\Temp\cc6tgLS5.s:569    .text.vPortFreeMicroROS:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cc6tgLS5.s:575    .text.vPortFreeMicroROS:00000000 vPortFreeMicroROS
C:\Users\<USER>\AppData\Local\Temp\cc6tgLS5.s:702    .text.vPortFreeMicroROS:00000068 $d
C:\Users\<USER>\AppData\Local\Temp\cc6tgLS5.s:708    .text.getBlockSize:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cc6tgLS5.s:714    .text.getBlockSize:00000000 getBlockSize
C:\Users\<USER>\AppData\Local\Temp\cc6tgLS5.s:742    .text.getBlockSize:00000010 $d
C:\Users\<USER>\AppData\Local\Temp\cc6tgLS5.s:747    .text.pvPortReallocMicroROS:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cc6tgLS5.s:753    .text.pvPortReallocMicroROS:00000000 pvPortReallocMicroROS
C:\Users\<USER>\AppData\Local\Temp\cc6tgLS5.s:831    .text.pvPortCallocMicroROS:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cc6tgLS5.s:837    .text.pvPortCallocMicroROS:00000000 pvPortCallocMicroROS
C:\Users\<USER>\AppData\Local\Temp\cc6tgLS5.s:905    .text.xPortGetFreeHeapSizeMicroROS:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cc6tgLS5.s:911    .text.xPortGetFreeHeapSizeMicroROS:00000000 xPortGetFreeHeapSizeMicroROS
C:\Users\<USER>\AppData\Local\Temp\cc6tgLS5.s:926    .text.xPortGetFreeHeapSizeMicroROS:00000008 $d
C:\Users\<USER>\AppData\Local\Temp\cc6tgLS5.s:931    .text.xPortGetMinimumEverFreeHeapSizeMicroROS:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cc6tgLS5.s:937    .text.xPortGetMinimumEverFreeHeapSizeMicroROS:00000000 xPortGetMinimumEverFreeHeapSizeMicroROS
C:\Users\<USER>\AppData\Local\Temp\cc6tgLS5.s:952    .text.xPortGetMinimumEverFreeHeapSizeMicroROS:00000008 $d
C:\Users\<USER>\AppData\Local\Temp\cc6tgLS5.s:957    .text.vPortInitialiseBlocksMicroROS:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cc6tgLS5.s:963    .text.vPortInitialiseBlocksMicroROS:00000000 vPortInitialiseBlocksMicroROS
C:\Users\<USER>\AppData\Local\Temp\cc6tgLS5.s:976    .bss.xBlockAllocatedBit:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\cc6tgLS5.s:982    .bss.xMinimumEverFreeBytesRemaining:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\cc6tgLS5.s:988    .bss.xFreeBytesRemaining:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\cc6tgLS5.s:994    .bss.pxEnd:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\cc6tgLS5.s:1000   .bss.xStart:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\cc6tgLS5.s:1006   .bss.ucHeap:00000000 $d

UNDEFINED SYMBOLS
vTaskSuspendAll
xTaskResumeAll
memcpy
