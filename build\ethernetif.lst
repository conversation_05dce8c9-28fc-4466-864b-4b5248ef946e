ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 1


   1              		.cpu cortex-m7
   2              		.arch armv7e-m
   3              		.fpu fpv5-d16
   4              		.eabi_attribute 28, 1
   5              		.eabi_attribute 20, 1
   6              		.eabi_attribute 21, 1
   7              		.eabi_attribute 23, 3
   8              		.eabi_attribute 24, 1
   9              		.eabi_attribute 25, 1
  10              		.eabi_attribute 26, 1
  11              		.eabi_attribute 30, 1
  12              		.eabi_attribute 34, 1
  13              		.eabi_attribute 18, 4
  14              		.file	"ethernetif.c"
  15              		.text
  16              	.Ltext0:
  17              		.cfi_sections	.debug_frame
  18              		.file 1 "LWIP/Target/ethernetif.c"
  19              		.section	.text.ETH_PHY_IO_DeInit,"ax",%progbits
  20              		.align	1
  21              		.global	ETH_PHY_IO_DeInit
  22              		.syntax unified
  23              		.thumb
  24              		.thumb_func
  26              	ETH_PHY_IO_DeInit:
  27              	.LFB187:
   1:LWIP/Target/ethernetif.c **** /* USER CODE BEGIN Header */
   2:LWIP/Target/ethernetif.c **** /**
   3:LWIP/Target/ethernetif.c ****   ******************************************************************************
   4:LWIP/Target/ethernetif.c ****   * File Name          : ethernetif.c
   5:LWIP/Target/ethernetif.c ****   * Description        : This file provides code for the configuration
   6:LWIP/Target/ethernetif.c ****   *                      of the ethernetif.c MiddleWare.
   7:LWIP/Target/ethernetif.c ****   ******************************************************************************
   8:LWIP/Target/ethernetif.c ****   * @attention
   9:LWIP/Target/ethernetif.c ****   *
  10:LWIP/Target/ethernetif.c ****   * Copyright (c) 2025 STMicroelectronics.
  11:LWIP/Target/ethernetif.c ****   * All rights reserved.
  12:LWIP/Target/ethernetif.c ****   *
  13:LWIP/Target/ethernetif.c ****   * This software is licensed under terms that can be found in the LICENSE file
  14:LWIP/Target/ethernetif.c ****   * in the root directory of this software component.
  15:LWIP/Target/ethernetif.c ****   * If no LICENSE file comes with this software, it is provided AS-IS.
  16:LWIP/Target/ethernetif.c ****   *
  17:LWIP/Target/ethernetif.c ****   ******************************************************************************
  18:LWIP/Target/ethernetif.c ****   */
  19:LWIP/Target/ethernetif.c **** /* USER CODE END Header */
  20:LWIP/Target/ethernetif.c **** 
  21:LWIP/Target/ethernetif.c **** /* Includes ------------------------------------------------------------------*/
  22:LWIP/Target/ethernetif.c **** #include "main.h"
  23:LWIP/Target/ethernetif.c **** #include "lwip/opt.h"
  24:LWIP/Target/ethernetif.c **** #include "lwip/timeouts.h"
  25:LWIP/Target/ethernetif.c **** #include "netif/ethernet.h"
  26:LWIP/Target/ethernetif.c **** #include "netif/etharp.h"
  27:LWIP/Target/ethernetif.c **** #include "lwip/ethip6.h"
  28:LWIP/Target/ethernetif.c **** #include "ethernetif.h"
  29:LWIP/Target/ethernetif.c **** #include "lan8742.h"
  30:LWIP/Target/ethernetif.c **** #include <string.h>
  31:LWIP/Target/ethernetif.c **** #include "cmsis_os.h"
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 2


  32:LWIP/Target/ethernetif.c **** #include "lwip/tcpip.h"
  33:LWIP/Target/ethernetif.c **** 
  34:LWIP/Target/ethernetif.c **** /* Within 'USER CODE' section, code will be kept by default at each generation */
  35:LWIP/Target/ethernetif.c **** /* USER CODE BEGIN 0 */
  36:LWIP/Target/ethernetif.c **** 
  37:LWIP/Target/ethernetif.c **** /* USER CODE END 0 */
  38:LWIP/Target/ethernetif.c **** 
  39:LWIP/Target/ethernetif.c **** /* Private define ------------------------------------------------------------*/
  40:LWIP/Target/ethernetif.c **** /* The time to block waiting for input. */
  41:LWIP/Target/ethernetif.c **** #define TIME_WAITING_FOR_INPUT ( portMAX_DELAY )
  42:LWIP/Target/ethernetif.c **** /* Time to block waiting for transmissions to finish */
  43:LWIP/Target/ethernetif.c **** #define ETHIF_TX_TIMEOUT (2000U)
  44:LWIP/Target/ethernetif.c **** /* USER CODE BEGIN OS_THREAD_STACK_SIZE_WITH_RTOS */
  45:LWIP/Target/ethernetif.c **** /* Stack size of the interface thread */
  46:LWIP/Target/ethernetif.c **** #define INTERFACE_THREAD_STACK_SIZE ( 350 )
  47:LWIP/Target/ethernetif.c **** /* USER CODE END OS_THREAD_STACK_SIZE_WITH_RTOS */
  48:LWIP/Target/ethernetif.c **** /* Network interface name */
  49:LWIP/Target/ethernetif.c **** #define IFNAME0 's'
  50:LWIP/Target/ethernetif.c **** #define IFNAME1 't'
  51:LWIP/Target/ethernetif.c **** 
  52:LWIP/Target/ethernetif.c **** /* ETH Setting  */
  53:LWIP/Target/ethernetif.c **** #define ETH_DMA_TRANSMIT_TIMEOUT               ( 20U )
  54:LWIP/Target/ethernetif.c **** #define ETH_TX_BUFFER_MAX             ((ETH_TX_DESC_CNT) * 2U)
  55:LWIP/Target/ethernetif.c **** /* ETH_RX_BUFFER_SIZE parameter is defined in lwipopts.h */
  56:LWIP/Target/ethernetif.c **** 
  57:LWIP/Target/ethernetif.c **** /* USER CODE BEGIN 1 */
  58:LWIP/Target/ethernetif.c **** 
  59:LWIP/Target/ethernetif.c **** /* USER CODE END 1 */
  60:LWIP/Target/ethernetif.c **** 
  61:LWIP/Target/ethernetif.c **** /* Private variables ---------------------------------------------------------*/
  62:LWIP/Target/ethernetif.c **** /*
  63:LWIP/Target/ethernetif.c **** @Note: This interface is implemented to operate in zero-copy mode only:
  64:LWIP/Target/ethernetif.c ****         - Rx Buffers will be allocated from LwIP stack Rx memory pool,
  65:LWIP/Target/ethernetif.c ****           then passed to ETH HAL driver.
  66:LWIP/Target/ethernetif.c ****         - Tx Buffers will be allocated from LwIP stack memory heap,
  67:LWIP/Target/ethernetif.c ****           then passed to ETH HAL driver.
  68:LWIP/Target/ethernetif.c **** 
  69:LWIP/Target/ethernetif.c **** @Notes:
  70:LWIP/Target/ethernetif.c ****   1.a. ETH DMA Rx descriptors must be contiguous, the default count is 4,
  71:LWIP/Target/ethernetif.c ****        to customize it please redefine ETH_RX_DESC_CNT in ETH GUI (Rx Descriptor Length)
  72:LWIP/Target/ethernetif.c ****        so that updated value will be generated in stm32xxxx_hal_conf.h
  73:LWIP/Target/ethernetif.c ****   1.b. ETH DMA Tx descriptors must be contiguous, the default count is 4,
  74:LWIP/Target/ethernetif.c ****        to customize it please redefine ETH_TX_DESC_CNT in ETH GUI (Tx Descriptor Length)
  75:LWIP/Target/ethernetif.c ****        so that updated value will be generated in stm32xxxx_hal_conf.h
  76:LWIP/Target/ethernetif.c **** 
  77:LWIP/Target/ethernetif.c ****   2.a. Rx Buffers number must be between ETH_RX_DESC_CNT and 2*ETH_RX_DESC_CNT
  78:LWIP/Target/ethernetif.c ****   2.b. Rx Buffers must have the same size: ETH_RX_BUFFER_SIZE, this value must
  79:LWIP/Target/ethernetif.c ****        passed to ETH DMA in the init field (heth.Init.RxBuffLen)
  80:LWIP/Target/ethernetif.c ****   2.c  The RX Ruffers addresses and sizes must be properly defined to be aligned
  81:LWIP/Target/ethernetif.c ****        to L1-CACHE line size (32 bytes).
  82:LWIP/Target/ethernetif.c **** */
  83:LWIP/Target/ethernetif.c **** 
  84:LWIP/Target/ethernetif.c **** /* Data Type Definitions */
  85:LWIP/Target/ethernetif.c **** typedef enum
  86:LWIP/Target/ethernetif.c **** {
  87:LWIP/Target/ethernetif.c ****   RX_ALLOC_OK       = 0x00,
  88:LWIP/Target/ethernetif.c ****   RX_ALLOC_ERROR    = 0x01
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 3


  89:LWIP/Target/ethernetif.c **** } RxAllocStatusTypeDef;
  90:LWIP/Target/ethernetif.c **** 
  91:LWIP/Target/ethernetif.c **** typedef struct
  92:LWIP/Target/ethernetif.c **** {
  93:LWIP/Target/ethernetif.c ****   struct pbuf_custom pbuf_custom;
  94:LWIP/Target/ethernetif.c ****   uint8_t buff[(ETH_RX_BUFFER_SIZE + 31) & ~31] __ALIGNED(32);
  95:LWIP/Target/ethernetif.c **** } RxBuff_t;
  96:LWIP/Target/ethernetif.c **** 
  97:LWIP/Target/ethernetif.c **** /* Memory Pool Declaration */
  98:LWIP/Target/ethernetif.c **** #define ETH_RX_BUFFER_CNT             12U
  99:LWIP/Target/ethernetif.c **** LWIP_MEMPOOL_DECLARE(RX_POOL, ETH_RX_BUFFER_CNT, sizeof(RxBuff_t), "Zero-copy RX PBUF pool");
 100:LWIP/Target/ethernetif.c **** 
 101:LWIP/Target/ethernetif.c **** /* Variable Definitions */
 102:LWIP/Target/ethernetif.c **** static uint8_t RxAllocStatus;
 103:LWIP/Target/ethernetif.c **** #if defined ( __ICCARM__ ) /*!< IAR Compiler */
 104:LWIP/Target/ethernetif.c **** 
 105:LWIP/Target/ethernetif.c **** #pragma location=0x30000000
 106:LWIP/Target/ethernetif.c **** ETH_DMADescTypeDef  DMARxDscrTab[ETH_RX_DESC_CNT]; /* Ethernet Rx DMA Descriptors */
 107:LWIP/Target/ethernetif.c **** #pragma location=0x30000080
 108:LWIP/Target/ethernetif.c **** ETH_DMADescTypeDef  DMATxDscrTab[ETH_TX_DESC_CNT]; /* Ethernet Tx DMA Descriptors */
 109:LWIP/Target/ethernetif.c **** 
 110:LWIP/Target/ethernetif.c **** #elif defined ( __CC_ARM )  /* MDK ARM Compiler */
 111:LWIP/Target/ethernetif.c **** 
 112:LWIP/Target/ethernetif.c **** __attribute__((at(0x30000000))) ETH_DMADescTypeDef  DMARxDscrTab[ETH_RX_DESC_CNT]; /* Ethernet Rx D
 113:LWIP/Target/ethernetif.c **** __attribute__((at(0x30000080))) ETH_DMADescTypeDef  DMATxDscrTab[ETH_TX_DESC_CNT]; /* Ethernet Tx D
 114:LWIP/Target/ethernetif.c **** 
 115:LWIP/Target/ethernetif.c **** #elif defined ( __GNUC__ ) /* GNU Compiler */
 116:LWIP/Target/ethernetif.c **** 
 117:LWIP/Target/ethernetif.c **** ETH_DMADescTypeDef DMARxDscrTab[ETH_RX_DESC_CNT] __attribute__((section(".RxDecripSection"))); /* E
 118:LWIP/Target/ethernetif.c **** ETH_DMADescTypeDef DMATxDscrTab[ETH_TX_DESC_CNT] __attribute__((section(".TxDecripSection")));   /*
 119:LWIP/Target/ethernetif.c **** 
 120:LWIP/Target/ethernetif.c **** #endif
 121:LWIP/Target/ethernetif.c **** 
 122:LWIP/Target/ethernetif.c **** #if defined ( __ICCARM__ ) /*!< IAR Compiler */
 123:LWIP/Target/ethernetif.c **** #pragma location = 0x30000100
 124:LWIP/Target/ethernetif.c **** extern u8_t memp_memory_RX_POOL_base[];
 125:LWIP/Target/ethernetif.c **** 
 126:LWIP/Target/ethernetif.c **** #elif defined ( __CC_ARM ) /* MDK ARM Compiler */
 127:LWIP/Target/ethernetif.c **** __attribute__((section(".Rx_PoolSection"))) extern u8_t memp_memory_RX_POOL_base[];
 128:LWIP/Target/ethernetif.c **** 
 129:LWIP/Target/ethernetif.c **** #elif defined ( __GNUC__ ) /* GNU */
 130:LWIP/Target/ethernetif.c **** __attribute__((section(".Rx_PoolSection"))) extern u8_t memp_memory_RX_POOL_base[];
 131:LWIP/Target/ethernetif.c **** #endif
 132:LWIP/Target/ethernetif.c **** 
 133:LWIP/Target/ethernetif.c **** /* USER CODE BEGIN 2 */
 134:LWIP/Target/ethernetif.c **** 
 135:LWIP/Target/ethernetif.c **** /* USER CODE END 2 */
 136:LWIP/Target/ethernetif.c **** 
 137:LWIP/Target/ethernetif.c **** osSemaphoreId RxPktSemaphore = NULL;   /* Semaphore to signal incoming packets */
 138:LWIP/Target/ethernetif.c **** osSemaphoreId TxPktSemaphore = NULL;   /* Semaphore to signal transmit packet complete */
 139:LWIP/Target/ethernetif.c **** 
 140:LWIP/Target/ethernetif.c **** /* Global Ethernet handle */
 141:LWIP/Target/ethernetif.c **** ETH_HandleTypeDef heth;
 142:LWIP/Target/ethernetif.c **** ETH_TxPacketConfig TxConfig;
 143:LWIP/Target/ethernetif.c **** 
 144:LWIP/Target/ethernetif.c **** /* Private function prototypes -----------------------------------------------*/
 145:LWIP/Target/ethernetif.c **** int32_t ETH_PHY_IO_Init(void);
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 4


 146:LWIP/Target/ethernetif.c **** int32_t ETH_PHY_IO_DeInit (void);
 147:LWIP/Target/ethernetif.c **** int32_t ETH_PHY_IO_ReadReg(uint32_t DevAddr, uint32_t RegAddr, uint32_t *pRegVal);
 148:LWIP/Target/ethernetif.c **** int32_t ETH_PHY_IO_WriteReg(uint32_t DevAddr, uint32_t RegAddr, uint32_t RegVal);
 149:LWIP/Target/ethernetif.c **** int32_t ETH_PHY_IO_GetTick(void);
 150:LWIP/Target/ethernetif.c **** 
 151:LWIP/Target/ethernetif.c **** lan8742_Object_t LAN8742;
 152:LWIP/Target/ethernetif.c **** lan8742_IOCtx_t  LAN8742_IOCtx = {ETH_PHY_IO_Init,
 153:LWIP/Target/ethernetif.c ****                                   ETH_PHY_IO_DeInit,
 154:LWIP/Target/ethernetif.c ****                                   ETH_PHY_IO_WriteReg,
 155:LWIP/Target/ethernetif.c ****                                   ETH_PHY_IO_ReadReg,
 156:LWIP/Target/ethernetif.c ****                                   ETH_PHY_IO_GetTick};
 157:LWIP/Target/ethernetif.c **** 
 158:LWIP/Target/ethernetif.c **** /* USER CODE BEGIN 3 */
 159:LWIP/Target/ethernetif.c **** 
 160:LWIP/Target/ethernetif.c **** /* USER CODE END 3 */
 161:LWIP/Target/ethernetif.c **** 
 162:LWIP/Target/ethernetif.c **** /* Private functions ---------------------------------------------------------*/
 163:LWIP/Target/ethernetif.c **** void pbuf_free_custom(struct pbuf *p);
 164:LWIP/Target/ethernetif.c **** 
 165:LWIP/Target/ethernetif.c **** /**
 166:LWIP/Target/ethernetif.c ****   * @brief  Ethernet Rx Transfer completed callback
 167:LWIP/Target/ethernetif.c ****   * @param  handlerEth: ETH handler
 168:LWIP/Target/ethernetif.c ****   * @retval None
 169:LWIP/Target/ethernetif.c ****   */
 170:LWIP/Target/ethernetif.c **** void HAL_ETH_RxCpltCallback(ETH_HandleTypeDef *handlerEth)
 171:LWIP/Target/ethernetif.c **** {
 172:LWIP/Target/ethernetif.c ****   osSemaphoreRelease(RxPktSemaphore);
 173:LWIP/Target/ethernetif.c **** }
 174:LWIP/Target/ethernetif.c **** /**
 175:LWIP/Target/ethernetif.c ****   * @brief  Ethernet Tx Transfer completed callback
 176:LWIP/Target/ethernetif.c ****   * @param  handlerEth: ETH handler
 177:LWIP/Target/ethernetif.c ****   * @retval None
 178:LWIP/Target/ethernetif.c ****   */
 179:LWIP/Target/ethernetif.c **** void HAL_ETH_TxCpltCallback(ETH_HandleTypeDef *handlerEth)
 180:LWIP/Target/ethernetif.c **** {
 181:LWIP/Target/ethernetif.c ****   osSemaphoreRelease(TxPktSemaphore);
 182:LWIP/Target/ethernetif.c **** }
 183:LWIP/Target/ethernetif.c **** /**
 184:LWIP/Target/ethernetif.c ****   * @brief  Ethernet DMA transfer error callback
 185:LWIP/Target/ethernetif.c ****   * @param  handlerEth: ETH handler
 186:LWIP/Target/ethernetif.c ****   * @retval None
 187:LWIP/Target/ethernetif.c ****   */
 188:LWIP/Target/ethernetif.c **** void HAL_ETH_ErrorCallback(ETH_HandleTypeDef *handlerEth)
 189:LWIP/Target/ethernetif.c **** {
 190:LWIP/Target/ethernetif.c ****   if((HAL_ETH_GetDMAError(handlerEth) & ETH_DMACSR_RBU) == ETH_DMACSR_RBU)
 191:LWIP/Target/ethernetif.c ****   {
 192:LWIP/Target/ethernetif.c ****      osSemaphoreRelease(RxPktSemaphore);
 193:LWIP/Target/ethernetif.c ****   }
 194:LWIP/Target/ethernetif.c **** }
 195:LWIP/Target/ethernetif.c **** 
 196:LWIP/Target/ethernetif.c **** /* USER CODE BEGIN 4 */
 197:LWIP/Target/ethernetif.c **** 
 198:LWIP/Target/ethernetif.c **** /* USER CODE END 4 */
 199:LWIP/Target/ethernetif.c **** 
 200:LWIP/Target/ethernetif.c **** /*******************************************************************************
 201:LWIP/Target/ethernetif.c ****                        LL Driver Interface ( LwIP stack --> ETH)
 202:LWIP/Target/ethernetif.c **** *******************************************************************************/
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 5


 203:LWIP/Target/ethernetif.c **** /**
 204:LWIP/Target/ethernetif.c ****  * @brief In this function, the hardware should be initialized.
 205:LWIP/Target/ethernetif.c ****  * Called from ethernetif_init().
 206:LWIP/Target/ethernetif.c ****  *
 207:LWIP/Target/ethernetif.c ****  * @param netif the already initialized lwip network interface structure
 208:LWIP/Target/ethernetif.c ****  *        for this ethernetif
 209:LWIP/Target/ethernetif.c ****  */
 210:LWIP/Target/ethernetif.c **** static void low_level_init(struct netif *netif)
 211:LWIP/Target/ethernetif.c **** {
 212:LWIP/Target/ethernetif.c ****   HAL_StatusTypeDef hal_eth_init_status = HAL_OK;
 213:LWIP/Target/ethernetif.c **** /* USER CODE BEGIN OS_THREAD_ATTR_CMSIS_RTOS_V2 */
 214:LWIP/Target/ethernetif.c ****   osThreadAttr_t attributes;
 215:LWIP/Target/ethernetif.c **** /* USER CODE END OS_THREAD_ATTR_CMSIS_RTOS_V2 */
 216:LWIP/Target/ethernetif.c ****   uint32_t duplex, speed = 0;
 217:LWIP/Target/ethernetif.c ****   int32_t PHYLinkState = 0;
 218:LWIP/Target/ethernetif.c ****   ETH_MACConfigTypeDef MACConf = {0};
 219:LWIP/Target/ethernetif.c ****   /* Start ETH HAL Init */
 220:LWIP/Target/ethernetif.c **** 
 221:LWIP/Target/ethernetif.c ****    uint8_t MACAddr[6] ;
 222:LWIP/Target/ethernetif.c ****   heth.Instance = ETH;
 223:LWIP/Target/ethernetif.c ****   MACAddr[0] = 0x00;
 224:LWIP/Target/ethernetif.c ****   MACAddr[1] = 0x80;
 225:LWIP/Target/ethernetif.c ****   MACAddr[2] = 0xE1;
 226:LWIP/Target/ethernetif.c ****   MACAddr[3] = 0x00;
 227:LWIP/Target/ethernetif.c ****   MACAddr[4] = 0x00;
 228:LWIP/Target/ethernetif.c ****   MACAddr[5] = 0x00;
 229:LWIP/Target/ethernetif.c ****   heth.Init.MACAddr = &MACAddr[0];
 230:LWIP/Target/ethernetif.c ****   heth.Init.MediaInterface = HAL_ETH_RMII_MODE;
 231:LWIP/Target/ethernetif.c ****   heth.Init.TxDesc = DMATxDscrTab;
 232:LWIP/Target/ethernetif.c ****   heth.Init.RxDesc = DMARxDscrTab;
 233:LWIP/Target/ethernetif.c ****   heth.Init.RxBuffLen = 1536;
 234:LWIP/Target/ethernetif.c **** 
 235:LWIP/Target/ethernetif.c ****   /* USER CODE BEGIN MACADDRESS */
 236:LWIP/Target/ethernetif.c **** 
 237:LWIP/Target/ethernetif.c ****   /* USER CODE END MACADDRESS */
 238:LWIP/Target/ethernetif.c **** 
 239:LWIP/Target/ethernetif.c ****   hal_eth_init_status = HAL_ETH_Init(&heth);
 240:LWIP/Target/ethernetif.c **** 
 241:LWIP/Target/ethernetif.c ****   memset(&TxConfig, 0 , sizeof(ETH_TxPacketConfig));
 242:LWIP/Target/ethernetif.c ****   TxConfig.Attributes = ETH_TX_PACKETS_FEATURES_CSUM | ETH_TX_PACKETS_FEATURES_CRCPAD;
 243:LWIP/Target/ethernetif.c ****   TxConfig.ChecksumCtrl = ETH_CHECKSUM_IPHDR_PAYLOAD_INSERT_PHDR_CALC;
 244:LWIP/Target/ethernetif.c ****   TxConfig.CRCPadCtrl = ETH_CRC_PAD_INSERT;
 245:LWIP/Target/ethernetif.c **** 
 246:LWIP/Target/ethernetif.c ****   /* End ETH HAL Init */
 247:LWIP/Target/ethernetif.c **** 
 248:LWIP/Target/ethernetif.c ****   /* Initialize the RX POOL */
 249:LWIP/Target/ethernetif.c ****   LWIP_MEMPOOL_INIT(RX_POOL);
 250:LWIP/Target/ethernetif.c **** 
 251:LWIP/Target/ethernetif.c **** #if LWIP_ARP || LWIP_ETHERNET
 252:LWIP/Target/ethernetif.c ****   /* set MAC hardware address length */
 253:LWIP/Target/ethernetif.c ****   netif->hwaddr_len = ETH_HWADDR_LEN;
 254:LWIP/Target/ethernetif.c **** 
 255:LWIP/Target/ethernetif.c ****   /* set MAC hardware address */
 256:LWIP/Target/ethernetif.c ****   netif->hwaddr[0] =  heth.Init.MACAddr[0];
 257:LWIP/Target/ethernetif.c ****   netif->hwaddr[1] =  heth.Init.MACAddr[1];
 258:LWIP/Target/ethernetif.c ****   netif->hwaddr[2] =  heth.Init.MACAddr[2];
 259:LWIP/Target/ethernetif.c ****   netif->hwaddr[3] =  heth.Init.MACAddr[3];
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 6


 260:LWIP/Target/ethernetif.c ****   netif->hwaddr[4] =  heth.Init.MACAddr[4];
 261:LWIP/Target/ethernetif.c ****   netif->hwaddr[5] =  heth.Init.MACAddr[5];
 262:LWIP/Target/ethernetif.c **** 
 263:LWIP/Target/ethernetif.c ****   /* maximum transfer unit */
 264:LWIP/Target/ethernetif.c ****   netif->mtu = ETH_MAX_PAYLOAD;
 265:LWIP/Target/ethernetif.c **** 
 266:LWIP/Target/ethernetif.c ****   /* Accept broadcast address and ARP traffic */
 267:LWIP/Target/ethernetif.c ****   /* don't set NETIF_FLAG_ETHARP if this device is not an ethernet one */
 268:LWIP/Target/ethernetif.c ****   #if LWIP_ARP
 269:LWIP/Target/ethernetif.c ****     netif->flags |= NETIF_FLAG_BROADCAST | NETIF_FLAG_ETHARP;
 270:LWIP/Target/ethernetif.c ****   #else
 271:LWIP/Target/ethernetif.c ****     netif->flags |= NETIF_FLAG_BROADCAST;
 272:LWIP/Target/ethernetif.c ****   #endif /* LWIP_ARP */
 273:LWIP/Target/ethernetif.c **** 
 274:LWIP/Target/ethernetif.c ****   /* create a binary semaphore used for informing ethernetif of frame reception */
 275:LWIP/Target/ethernetif.c ****   RxPktSemaphore = osSemaphoreNew(1, 0, NULL);
 276:LWIP/Target/ethernetif.c **** 
 277:LWIP/Target/ethernetif.c ****   /* create a binary semaphore used for informing ethernetif of frame transmission */
 278:LWIP/Target/ethernetif.c ****   TxPktSemaphore = osSemaphoreNew(1, 0, NULL);
 279:LWIP/Target/ethernetif.c **** 
 280:LWIP/Target/ethernetif.c ****   /* create the task that handles the ETH_MAC */
 281:LWIP/Target/ethernetif.c **** /* USER CODE BEGIN OS_THREAD_NEW_CMSIS_RTOS_V2 */
 282:LWIP/Target/ethernetif.c ****   memset(&attributes, 0x0, sizeof(osThreadAttr_t));
 283:LWIP/Target/ethernetif.c ****   attributes.name = "EthIf";
 284:LWIP/Target/ethernetif.c ****   attributes.stack_size = INTERFACE_THREAD_STACK_SIZE;
 285:LWIP/Target/ethernetif.c ****   attributes.priority = osPriorityRealtime;
 286:LWIP/Target/ethernetif.c ****   osThreadNew(ethernetif_input, netif, &attributes);
 287:LWIP/Target/ethernetif.c **** /* USER CODE END OS_THREAD_NEW_CMSIS_RTOS_V2 */
 288:LWIP/Target/ethernetif.c **** 
 289:LWIP/Target/ethernetif.c **** /* USER CODE BEGIN PHY_PRE_CONFIG */
 290:LWIP/Target/ethernetif.c **** 
 291:LWIP/Target/ethernetif.c **** /* USER CODE END PHY_PRE_CONFIG */
 292:LWIP/Target/ethernetif.c ****   /* Set PHY IO functions */
 293:LWIP/Target/ethernetif.c ****   LAN8742_RegisterBusIO(&LAN8742, &LAN8742_IOCtx);
 294:LWIP/Target/ethernetif.c **** 
 295:LWIP/Target/ethernetif.c ****   /* Initialize the LAN8742 ETH PHY */
 296:LWIP/Target/ethernetif.c ****   if(LAN8742_Init(&LAN8742) != LAN8742_STATUS_OK)
 297:LWIP/Target/ethernetif.c ****   {
 298:LWIP/Target/ethernetif.c ****     netif_set_link_down(netif);
 299:LWIP/Target/ethernetif.c ****     netif_set_down(netif);
 300:LWIP/Target/ethernetif.c ****     return;
 301:LWIP/Target/ethernetif.c ****   }
 302:LWIP/Target/ethernetif.c **** 
 303:LWIP/Target/ethernetif.c ****   if (hal_eth_init_status == HAL_OK)
 304:LWIP/Target/ethernetif.c ****   {
 305:LWIP/Target/ethernetif.c ****     PHYLinkState = LAN8742_GetLinkState(&LAN8742);
 306:LWIP/Target/ethernetif.c **** 
 307:LWIP/Target/ethernetif.c ****     /* Get link state */
 308:LWIP/Target/ethernetif.c ****     if(PHYLinkState <= LAN8742_STATUS_LINK_DOWN)
 309:LWIP/Target/ethernetif.c ****     {
 310:LWIP/Target/ethernetif.c ****       netif_set_link_down(netif);
 311:LWIP/Target/ethernetif.c ****       netif_set_down(netif);
 312:LWIP/Target/ethernetif.c ****     }
 313:LWIP/Target/ethernetif.c ****     else
 314:LWIP/Target/ethernetif.c ****     {
 315:LWIP/Target/ethernetif.c ****       switch (PHYLinkState)
 316:LWIP/Target/ethernetif.c ****       {
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 7


 317:LWIP/Target/ethernetif.c ****       case LAN8742_STATUS_100MBITS_FULLDUPLEX:
 318:LWIP/Target/ethernetif.c ****         duplex = ETH_FULLDUPLEX_MODE;
 319:LWIP/Target/ethernetif.c ****         speed = ETH_SPEED_100M;
 320:LWIP/Target/ethernetif.c ****         break;
 321:LWIP/Target/ethernetif.c ****       case LAN8742_STATUS_100MBITS_HALFDUPLEX:
 322:LWIP/Target/ethernetif.c ****         duplex = ETH_HALFDUPLEX_MODE;
 323:LWIP/Target/ethernetif.c ****         speed = ETH_SPEED_100M;
 324:LWIP/Target/ethernetif.c ****         break;
 325:LWIP/Target/ethernetif.c ****       case LAN8742_STATUS_10MBITS_FULLDUPLEX:
 326:LWIP/Target/ethernetif.c ****         duplex = ETH_FULLDUPLEX_MODE;
 327:LWIP/Target/ethernetif.c ****         speed = ETH_SPEED_10M;
 328:LWIP/Target/ethernetif.c ****         break;
 329:LWIP/Target/ethernetif.c ****       case LAN8742_STATUS_10MBITS_HALFDUPLEX:
 330:LWIP/Target/ethernetif.c ****         duplex = ETH_HALFDUPLEX_MODE;
 331:LWIP/Target/ethernetif.c ****         speed = ETH_SPEED_10M;
 332:LWIP/Target/ethernetif.c ****         break;
 333:LWIP/Target/ethernetif.c ****       default:
 334:LWIP/Target/ethernetif.c ****         duplex = ETH_FULLDUPLEX_MODE;
 335:LWIP/Target/ethernetif.c ****         speed = ETH_SPEED_100M;
 336:LWIP/Target/ethernetif.c ****         break;
 337:LWIP/Target/ethernetif.c ****       }
 338:LWIP/Target/ethernetif.c **** 
 339:LWIP/Target/ethernetif.c ****     /* Get MAC Config MAC */
 340:LWIP/Target/ethernetif.c ****     HAL_ETH_GetMACConfig(&heth, &MACConf);
 341:LWIP/Target/ethernetif.c ****     MACConf.DuplexMode = duplex;
 342:LWIP/Target/ethernetif.c ****     MACConf.Speed = speed;
 343:LWIP/Target/ethernetif.c ****     HAL_ETH_SetMACConfig(&heth, &MACConf);
 344:LWIP/Target/ethernetif.c **** 
 345:LWIP/Target/ethernetif.c ****     HAL_ETH_Start_IT(&heth);
 346:LWIP/Target/ethernetif.c ****     netif_set_up(netif);
 347:LWIP/Target/ethernetif.c ****     netif_set_link_up(netif);
 348:LWIP/Target/ethernetif.c **** /* USER CODE BEGIN PHY_POST_CONFIG */
 349:LWIP/Target/ethernetif.c **** 
 350:LWIP/Target/ethernetif.c **** /* USER CODE END PHY_POST_CONFIG */
 351:LWIP/Target/ethernetif.c ****     }
 352:LWIP/Target/ethernetif.c **** 
 353:LWIP/Target/ethernetif.c ****   }
 354:LWIP/Target/ethernetif.c ****   else
 355:LWIP/Target/ethernetif.c ****   {
 356:LWIP/Target/ethernetif.c ****     Error_Handler();
 357:LWIP/Target/ethernetif.c ****   }
 358:LWIP/Target/ethernetif.c **** #endif /* LWIP_ARP || LWIP_ETHERNET */
 359:LWIP/Target/ethernetif.c **** 
 360:LWIP/Target/ethernetif.c **** /* USER CODE BEGIN LOW_LEVEL_INIT */
 361:LWIP/Target/ethernetif.c **** 
 362:LWIP/Target/ethernetif.c **** /* USER CODE END LOW_LEVEL_INIT */
 363:LWIP/Target/ethernetif.c **** }
 364:LWIP/Target/ethernetif.c **** 
 365:LWIP/Target/ethernetif.c **** /**
 366:LWIP/Target/ethernetif.c ****  * @brief This function should do the actual transmission of the packet. The packet is
 367:LWIP/Target/ethernetif.c ****  * contained in the pbuf that is passed to the function. This pbuf
 368:LWIP/Target/ethernetif.c ****  * might be chained.
 369:LWIP/Target/ethernetif.c ****  *
 370:LWIP/Target/ethernetif.c ****  * @param netif the lwip network interface structure for this ethernetif
 371:LWIP/Target/ethernetif.c ****  * @param p the MAC packet to send (e.g. IP packet including MAC addresses and type)
 372:LWIP/Target/ethernetif.c ****  * @return ERR_OK if the packet could be sent
 373:LWIP/Target/ethernetif.c ****  *         an err_t value if the packet couldn't be sent
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 8


 374:LWIP/Target/ethernetif.c ****  *
 375:LWIP/Target/ethernetif.c ****  * @note Returning ERR_MEM here if a DMA queue of your MAC is full can lead to
 376:LWIP/Target/ethernetif.c ****  *       strange results. You might consider waiting for space in the DMA queue
 377:LWIP/Target/ethernetif.c ****  *       to become available since the stack doesn't retry to send a packet
 378:LWIP/Target/ethernetif.c ****  *       dropped because of memory failure (except for the TCP timers).
 379:LWIP/Target/ethernetif.c ****  */
 380:LWIP/Target/ethernetif.c **** 
 381:LWIP/Target/ethernetif.c **** static err_t low_level_output(struct netif *netif, struct pbuf *p)
 382:LWIP/Target/ethernetif.c **** {
 383:LWIP/Target/ethernetif.c ****   uint32_t i = 0U;
 384:LWIP/Target/ethernetif.c ****   struct pbuf *q = NULL;
 385:LWIP/Target/ethernetif.c ****   err_t errval = ERR_OK;
 386:LWIP/Target/ethernetif.c ****   ETH_BufferTypeDef Txbuffer[ETH_TX_DESC_CNT] = {0};
 387:LWIP/Target/ethernetif.c **** 
 388:LWIP/Target/ethernetif.c ****   memset(Txbuffer, 0 , ETH_TX_DESC_CNT*sizeof(ETH_BufferTypeDef));
 389:LWIP/Target/ethernetif.c **** 
 390:LWIP/Target/ethernetif.c ****   for(q = p; q != NULL; q = q->next)
 391:LWIP/Target/ethernetif.c ****   {
 392:LWIP/Target/ethernetif.c ****     if(i >= ETH_TX_DESC_CNT)
 393:LWIP/Target/ethernetif.c ****       return ERR_IF;
 394:LWIP/Target/ethernetif.c **** 
 395:LWIP/Target/ethernetif.c ****     Txbuffer[i].buffer = q->payload;
 396:LWIP/Target/ethernetif.c ****     Txbuffer[i].len = q->len;
 397:LWIP/Target/ethernetif.c **** 
 398:LWIP/Target/ethernetif.c ****     if(i>0)
 399:LWIP/Target/ethernetif.c ****     {
 400:LWIP/Target/ethernetif.c ****       Txbuffer[i-1].next = &Txbuffer[i];
 401:LWIP/Target/ethernetif.c ****     }
 402:LWIP/Target/ethernetif.c **** 
 403:LWIP/Target/ethernetif.c ****     if(q->next == NULL)
 404:LWIP/Target/ethernetif.c ****     {
 405:LWIP/Target/ethernetif.c ****       Txbuffer[i].next = NULL;
 406:LWIP/Target/ethernetif.c ****     }
 407:LWIP/Target/ethernetif.c **** 
 408:LWIP/Target/ethernetif.c ****     i++;
 409:LWIP/Target/ethernetif.c ****   }
 410:LWIP/Target/ethernetif.c **** 
 411:LWIP/Target/ethernetif.c ****   TxConfig.Length = p->tot_len;
 412:LWIP/Target/ethernetif.c ****   TxConfig.TxBuffer = Txbuffer;
 413:LWIP/Target/ethernetif.c ****   TxConfig.pData = p;
 414:LWIP/Target/ethernetif.c **** 
 415:LWIP/Target/ethernetif.c ****   pbuf_ref(p);
 416:LWIP/Target/ethernetif.c **** 
 417:LWIP/Target/ethernetif.c ****   do
 418:LWIP/Target/ethernetif.c ****   {
 419:LWIP/Target/ethernetif.c ****     if(HAL_ETH_Transmit_IT(&heth, &TxConfig) == HAL_OK)
 420:LWIP/Target/ethernetif.c ****     {
 421:LWIP/Target/ethernetif.c ****       errval = ERR_OK;
 422:LWIP/Target/ethernetif.c ****     }
 423:LWIP/Target/ethernetif.c ****     else
 424:LWIP/Target/ethernetif.c ****     {
 425:LWIP/Target/ethernetif.c **** 
 426:LWIP/Target/ethernetif.c ****       if(HAL_ETH_GetError(&heth) & HAL_ETH_ERROR_BUSY)
 427:LWIP/Target/ethernetif.c ****       {
 428:LWIP/Target/ethernetif.c ****         /* Wait for descriptors to become available */
 429:LWIP/Target/ethernetif.c ****         osSemaphoreAcquire(TxPktSemaphore, ETHIF_TX_TIMEOUT);
 430:LWIP/Target/ethernetif.c ****         HAL_ETH_ReleaseTxPacket(&heth);
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 9


 431:LWIP/Target/ethernetif.c ****         errval = ERR_BUF;
 432:LWIP/Target/ethernetif.c ****       }
 433:LWIP/Target/ethernetif.c ****       else
 434:LWIP/Target/ethernetif.c ****       {
 435:LWIP/Target/ethernetif.c ****         /* Other error */
 436:LWIP/Target/ethernetif.c ****         pbuf_free(p);
 437:LWIP/Target/ethernetif.c ****         errval =  ERR_IF;
 438:LWIP/Target/ethernetif.c ****       }
 439:LWIP/Target/ethernetif.c ****     }
 440:LWIP/Target/ethernetif.c ****   }while(errval == ERR_BUF);
 441:LWIP/Target/ethernetif.c **** 
 442:LWIP/Target/ethernetif.c ****   return errval;
 443:LWIP/Target/ethernetif.c **** }
 444:LWIP/Target/ethernetif.c **** 
 445:LWIP/Target/ethernetif.c **** /**
 446:LWIP/Target/ethernetif.c ****  * @brief Should allocate a pbuf and transfer the bytes of the incoming
 447:LWIP/Target/ethernetif.c ****  * packet from the interface into the pbuf.
 448:LWIP/Target/ethernetif.c ****  *
 449:LWIP/Target/ethernetif.c ****  * @param netif the lwip network interface structure for this ethernetif
 450:LWIP/Target/ethernetif.c ****  * @return a pbuf filled with the received packet (including MAC header)
 451:LWIP/Target/ethernetif.c ****  *         NULL on memory error
 452:LWIP/Target/ethernetif.c ****    */
 453:LWIP/Target/ethernetif.c **** static struct pbuf * low_level_input(struct netif *netif)
 454:LWIP/Target/ethernetif.c **** {
 455:LWIP/Target/ethernetif.c ****   struct pbuf *p = NULL;
 456:LWIP/Target/ethernetif.c **** 
 457:LWIP/Target/ethernetif.c ****   if(RxAllocStatus == RX_ALLOC_OK)
 458:LWIP/Target/ethernetif.c ****   {
 459:LWIP/Target/ethernetif.c ****     HAL_ETH_ReadData(&heth, (void **)&p);
 460:LWIP/Target/ethernetif.c ****   }
 461:LWIP/Target/ethernetif.c **** 
 462:LWIP/Target/ethernetif.c ****   return p;
 463:LWIP/Target/ethernetif.c **** }
 464:LWIP/Target/ethernetif.c **** 
 465:LWIP/Target/ethernetif.c **** /**
 466:LWIP/Target/ethernetif.c ****  * @brief This function should be called when a packet is ready to be read
 467:LWIP/Target/ethernetif.c ****  * from the interface. It uses the function low_level_input() that
 468:LWIP/Target/ethernetif.c ****  * should handle the actual reception of bytes from the network
 469:LWIP/Target/ethernetif.c ****  * interface. Then the type of the received packet is determined and
 470:LWIP/Target/ethernetif.c ****  * the appropriate input function is called.
 471:LWIP/Target/ethernetif.c ****  *
 472:LWIP/Target/ethernetif.c ****  * @param netif the lwip network interface structure for this ethernetif
 473:LWIP/Target/ethernetif.c ****  */
 474:LWIP/Target/ethernetif.c **** void ethernetif_input(void* argument)
 475:LWIP/Target/ethernetif.c **** {
 476:LWIP/Target/ethernetif.c ****   struct pbuf *p = NULL;
 477:LWIP/Target/ethernetif.c ****   struct netif *netif = (struct netif *) argument;
 478:LWIP/Target/ethernetif.c **** 
 479:LWIP/Target/ethernetif.c ****   for( ;; )
 480:LWIP/Target/ethernetif.c ****   {
 481:LWIP/Target/ethernetif.c ****     if (osSemaphoreAcquire(RxPktSemaphore, TIME_WAITING_FOR_INPUT) == osOK)
 482:LWIP/Target/ethernetif.c ****     {
 483:LWIP/Target/ethernetif.c ****       do
 484:LWIP/Target/ethernetif.c ****       {
 485:LWIP/Target/ethernetif.c ****         p = low_level_input( netif );
 486:LWIP/Target/ethernetif.c ****         if (p != NULL)
 487:LWIP/Target/ethernetif.c ****         {
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 10


 488:LWIP/Target/ethernetif.c ****           if (netif->input( p, netif) != ERR_OK )
 489:LWIP/Target/ethernetif.c ****           {
 490:LWIP/Target/ethernetif.c ****             pbuf_free(p);
 491:LWIP/Target/ethernetif.c ****           }
 492:LWIP/Target/ethernetif.c ****         }
 493:LWIP/Target/ethernetif.c ****       } while(p!=NULL);
 494:LWIP/Target/ethernetif.c ****     }
 495:LWIP/Target/ethernetif.c ****   }
 496:LWIP/Target/ethernetif.c **** }
 497:LWIP/Target/ethernetif.c **** 
 498:LWIP/Target/ethernetif.c **** #if !LWIP_ARP
 499:LWIP/Target/ethernetif.c **** /**
 500:LWIP/Target/ethernetif.c ****  * This function has to be completed by user in case of ARP OFF.
 501:LWIP/Target/ethernetif.c ****  *
 502:LWIP/Target/ethernetif.c ****  * @param netif the lwip network interface structure for this ethernetif
 503:LWIP/Target/ethernetif.c ****  * @return ERR_OK if ...
 504:LWIP/Target/ethernetif.c ****  */
 505:LWIP/Target/ethernetif.c **** static err_t low_level_output_arp_off(struct netif *netif, struct pbuf *q, const ip4_addr_t *ipaddr
 506:LWIP/Target/ethernetif.c **** {
 507:LWIP/Target/ethernetif.c ****   err_t errval;
 508:LWIP/Target/ethernetif.c ****   errval = ERR_OK;
 509:LWIP/Target/ethernetif.c **** 
 510:LWIP/Target/ethernetif.c **** /* USER CODE BEGIN 5 */
 511:LWIP/Target/ethernetif.c **** 
 512:LWIP/Target/ethernetif.c **** /* USER CODE END 5 */
 513:LWIP/Target/ethernetif.c **** 
 514:LWIP/Target/ethernetif.c ****   return errval;
 515:LWIP/Target/ethernetif.c **** 
 516:LWIP/Target/ethernetif.c **** }
 517:LWIP/Target/ethernetif.c **** #endif /* LWIP_ARP */
 518:LWIP/Target/ethernetif.c **** 
 519:LWIP/Target/ethernetif.c **** /**
 520:LWIP/Target/ethernetif.c ****  * @brief Should be called at the beginning of the program to set up the
 521:LWIP/Target/ethernetif.c ****  * network interface. It calls the function low_level_init() to do the
 522:LWIP/Target/ethernetif.c ****  * actual setup of the hardware.
 523:LWIP/Target/ethernetif.c ****  *
 524:LWIP/Target/ethernetif.c ****  * This function should be passed as a parameter to netif_add().
 525:LWIP/Target/ethernetif.c ****  *
 526:LWIP/Target/ethernetif.c ****  * @param netif the lwip network interface structure for this ethernetif
 527:LWIP/Target/ethernetif.c ****  * @return ERR_OK if the loopif is initialized
 528:LWIP/Target/ethernetif.c ****  *         ERR_MEM if private data couldn't be allocated
 529:LWIP/Target/ethernetif.c ****  *         any other err_t on error
 530:LWIP/Target/ethernetif.c ****  */
 531:LWIP/Target/ethernetif.c **** err_t ethernetif_init(struct netif *netif)
 532:LWIP/Target/ethernetif.c **** {
 533:LWIP/Target/ethernetif.c ****   LWIP_ASSERT("netif != NULL", (netif != NULL));
 534:LWIP/Target/ethernetif.c **** 
 535:LWIP/Target/ethernetif.c **** #if LWIP_NETIF_HOSTNAME
 536:LWIP/Target/ethernetif.c ****   /* Initialize interface hostname */
 537:LWIP/Target/ethernetif.c ****   netif->hostname = "lwip";
 538:LWIP/Target/ethernetif.c **** #endif /* LWIP_NETIF_HOSTNAME */
 539:LWIP/Target/ethernetif.c **** 
 540:LWIP/Target/ethernetif.c ****   /*
 541:LWIP/Target/ethernetif.c ****    * Initialize the snmp variables and counters inside the struct netif.
 542:LWIP/Target/ethernetif.c ****    * The last argument should be replaced with your link speed, in units
 543:LWIP/Target/ethernetif.c ****    * of bits per second.
 544:LWIP/Target/ethernetif.c ****    */
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 11


 545:LWIP/Target/ethernetif.c ****   // MIB2_INIT_NETIF(netif, snmp_ifType_ethernet_csmacd, LINK_SPEED_OF_YOUR_NETIF_IN_BPS);
 546:LWIP/Target/ethernetif.c **** 
 547:LWIP/Target/ethernetif.c ****   netif->name[0] = IFNAME0;
 548:LWIP/Target/ethernetif.c ****   netif->name[1] = IFNAME1;
 549:LWIP/Target/ethernetif.c ****   /* We directly use etharp_output() here to save a function call.
 550:LWIP/Target/ethernetif.c ****    * You can instead declare your own function an call etharp_output()
 551:LWIP/Target/ethernetif.c ****    * from it if you have to do some checks before sending (e.g. if link
 552:LWIP/Target/ethernetif.c ****    * is available...) */
 553:LWIP/Target/ethernetif.c **** 
 554:LWIP/Target/ethernetif.c **** #if LWIP_IPV4
 555:LWIP/Target/ethernetif.c **** #if LWIP_ARP || LWIP_ETHERNET
 556:LWIP/Target/ethernetif.c **** #if LWIP_ARP
 557:LWIP/Target/ethernetif.c ****   netif->output = etharp_output;
 558:LWIP/Target/ethernetif.c **** #else
 559:LWIP/Target/ethernetif.c ****   /* The user should write its own code in low_level_output_arp_off function */
 560:LWIP/Target/ethernetif.c ****   netif->output = low_level_output_arp_off;
 561:LWIP/Target/ethernetif.c **** #endif /* LWIP_ARP */
 562:LWIP/Target/ethernetif.c **** #endif /* LWIP_ARP || LWIP_ETHERNET */
 563:LWIP/Target/ethernetif.c **** #endif /* LWIP_IPV4 */
 564:LWIP/Target/ethernetif.c **** 
 565:LWIP/Target/ethernetif.c **** #if LWIP_IPV6
 566:LWIP/Target/ethernetif.c ****   netif->output_ip6 = ethip6_output;
 567:LWIP/Target/ethernetif.c **** #endif /* LWIP_IPV6 */
 568:LWIP/Target/ethernetif.c **** 
 569:LWIP/Target/ethernetif.c ****   netif->linkoutput = low_level_output;
 570:LWIP/Target/ethernetif.c **** 
 571:LWIP/Target/ethernetif.c ****   /* initialize the hardware */
 572:LWIP/Target/ethernetif.c ****   low_level_init(netif);
 573:LWIP/Target/ethernetif.c **** 
 574:LWIP/Target/ethernetif.c ****   return ERR_OK;
 575:LWIP/Target/ethernetif.c **** }
 576:LWIP/Target/ethernetif.c **** 
 577:LWIP/Target/ethernetif.c **** /**
 578:LWIP/Target/ethernetif.c ****   * @brief  Custom Rx pbuf free callback
 579:LWIP/Target/ethernetif.c ****   * @param  pbuf: pbuf to be freed
 580:LWIP/Target/ethernetif.c ****   * @retval None
 581:LWIP/Target/ethernetif.c ****   */
 582:LWIP/Target/ethernetif.c **** void pbuf_free_custom(struct pbuf *p)
 583:LWIP/Target/ethernetif.c **** {
 584:LWIP/Target/ethernetif.c ****   struct pbuf_custom* custom_pbuf = (struct pbuf_custom*)p;
 585:LWIP/Target/ethernetif.c ****   LWIP_MEMPOOL_FREE(RX_POOL, custom_pbuf);
 586:LWIP/Target/ethernetif.c **** 
 587:LWIP/Target/ethernetif.c ****   /* If the Rx Buffer Pool was exhausted, signal the ethernetif_input task to
 588:LWIP/Target/ethernetif.c ****    * call HAL_ETH_GetRxDataBuffer to rebuild the Rx descriptors. */
 589:LWIP/Target/ethernetif.c **** 
 590:LWIP/Target/ethernetif.c ****   if (RxAllocStatus == RX_ALLOC_ERROR)
 591:LWIP/Target/ethernetif.c ****   {
 592:LWIP/Target/ethernetif.c ****     RxAllocStatus = RX_ALLOC_OK;
 593:LWIP/Target/ethernetif.c ****     osSemaphoreRelease(RxPktSemaphore);
 594:LWIP/Target/ethernetif.c ****   }
 595:LWIP/Target/ethernetif.c **** }
 596:LWIP/Target/ethernetif.c **** 
 597:LWIP/Target/ethernetif.c **** /* USER CODE BEGIN 6 */
 598:LWIP/Target/ethernetif.c **** 
 599:LWIP/Target/ethernetif.c **** /**
 600:LWIP/Target/ethernetif.c **** * @brief  Returns the current time in milliseconds
 601:LWIP/Target/ethernetif.c **** *         when LWIP_TIMERS == 1 and NO_SYS == 1
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 12


 602:LWIP/Target/ethernetif.c **** * @param  None
 603:LWIP/Target/ethernetif.c **** * @retval Current Time value
 604:LWIP/Target/ethernetif.c **** */
 605:LWIP/Target/ethernetif.c **** u32_t sys_now(void)
 606:LWIP/Target/ethernetif.c **** {
 607:LWIP/Target/ethernetif.c ****   return HAL_GetTick();
 608:LWIP/Target/ethernetif.c **** }
 609:LWIP/Target/ethernetif.c **** 
 610:LWIP/Target/ethernetif.c **** /* USER CODE END 6 */
 611:LWIP/Target/ethernetif.c **** 
 612:LWIP/Target/ethernetif.c **** /**
 613:LWIP/Target/ethernetif.c ****   * @brief  Initializes the ETH MSP.
 614:LWIP/Target/ethernetif.c ****   * @param  ethHandle: ETH handle
 615:LWIP/Target/ethernetif.c ****   * @retval None
 616:LWIP/Target/ethernetif.c ****   */
 617:LWIP/Target/ethernetif.c **** 
 618:LWIP/Target/ethernetif.c **** void HAL_ETH_MspInit(ETH_HandleTypeDef* ethHandle)
 619:LWIP/Target/ethernetif.c **** {
 620:LWIP/Target/ethernetif.c ****   GPIO_InitTypeDef GPIO_InitStruct = {0};
 621:LWIP/Target/ethernetif.c ****   if(ethHandle->Instance==ETH)
 622:LWIP/Target/ethernetif.c ****   {
 623:LWIP/Target/ethernetif.c ****   /* USER CODE BEGIN ETH_MspInit 0 */
 624:LWIP/Target/ethernetif.c **** 
 625:LWIP/Target/ethernetif.c ****   /* USER CODE END ETH_MspInit 0 */
 626:LWIP/Target/ethernetif.c ****     /* Enable Peripheral clock */
 627:LWIP/Target/ethernetif.c ****     __HAL_RCC_ETH1MAC_CLK_ENABLE();
 628:LWIP/Target/ethernetif.c ****     __HAL_RCC_ETH1TX_CLK_ENABLE();
 629:LWIP/Target/ethernetif.c ****     __HAL_RCC_ETH1RX_CLK_ENABLE();
 630:LWIP/Target/ethernetif.c **** 
 631:LWIP/Target/ethernetif.c ****     __HAL_RCC_GPIOC_CLK_ENABLE();
 632:LWIP/Target/ethernetif.c ****     __HAL_RCC_GPIOA_CLK_ENABLE();
 633:LWIP/Target/ethernetif.c ****     __HAL_RCC_GPIOB_CLK_ENABLE();
 634:LWIP/Target/ethernetif.c ****     /**ETH GPIO Configuration
 635:LWIP/Target/ethernetif.c ****     PC1     ------> ETH_MDC
 636:LWIP/Target/ethernetif.c ****     PA1     ------> ETH_REF_CLK
 637:LWIP/Target/ethernetif.c ****     PA2     ------> ETH_MDIO
 638:LWIP/Target/ethernetif.c ****     PA7     ------> ETH_CRS_DV
 639:LWIP/Target/ethernetif.c ****     PC4     ------> ETH_RXD0
 640:LWIP/Target/ethernetif.c ****     PC5     ------> ETH_RXD1
 641:LWIP/Target/ethernetif.c ****     PB11     ------> ETH_TX_EN
 642:LWIP/Target/ethernetif.c ****     PB12     ------> ETH_TXD0
 643:LWIP/Target/ethernetif.c ****     PB13     ------> ETH_TXD1
 644:LWIP/Target/ethernetif.c ****     */
 645:LWIP/Target/ethernetif.c ****     GPIO_InitStruct.Pin = GPIO_PIN_1|GPIO_PIN_4|GPIO_PIN_5;
 646:LWIP/Target/ethernetif.c ****     GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
 647:LWIP/Target/ethernetif.c ****     GPIO_InitStruct.Pull = GPIO_NOPULL;
 648:LWIP/Target/ethernetif.c ****     GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
 649:LWIP/Target/ethernetif.c ****     GPIO_InitStruct.Alternate = GPIO_AF11_ETH;
 650:LWIP/Target/ethernetif.c ****     HAL_GPIO_Init(GPIOC, &GPIO_InitStruct);
 651:LWIP/Target/ethernetif.c **** 
 652:LWIP/Target/ethernetif.c ****     GPIO_InitStruct.Pin = GPIO_PIN_1|GPIO_PIN_2|GPIO_PIN_7;
 653:LWIP/Target/ethernetif.c ****     GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
 654:LWIP/Target/ethernetif.c ****     GPIO_InitStruct.Pull = GPIO_NOPULL;
 655:LWIP/Target/ethernetif.c ****     GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
 656:LWIP/Target/ethernetif.c ****     GPIO_InitStruct.Alternate = GPIO_AF11_ETH;
 657:LWIP/Target/ethernetif.c ****     HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);
 658:LWIP/Target/ethernetif.c **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 13


 659:LWIP/Target/ethernetif.c ****     GPIO_InitStruct.Pin = GPIO_PIN_11|GPIO_PIN_12|GPIO_PIN_13;
 660:LWIP/Target/ethernetif.c ****     GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
 661:LWIP/Target/ethernetif.c ****     GPIO_InitStruct.Pull = GPIO_NOPULL;
 662:LWIP/Target/ethernetif.c ****     GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
 663:LWIP/Target/ethernetif.c ****     GPIO_InitStruct.Alternate = GPIO_AF11_ETH;
 664:LWIP/Target/ethernetif.c ****     HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);
 665:LWIP/Target/ethernetif.c **** 
 666:LWIP/Target/ethernetif.c ****     /* Peripheral interrupt init */
 667:LWIP/Target/ethernetif.c ****     HAL_NVIC_SetPriority(ETH_IRQn, 5, 0);
 668:LWIP/Target/ethernetif.c ****     HAL_NVIC_EnableIRQ(ETH_IRQn);
 669:LWIP/Target/ethernetif.c ****     HAL_NVIC_SetPriority(ETH_WKUP_IRQn, 5, 0);
 670:LWIP/Target/ethernetif.c ****     HAL_NVIC_EnableIRQ(ETH_WKUP_IRQn);
 671:LWIP/Target/ethernetif.c ****   /* USER CODE BEGIN ETH_MspInit 1 */
 672:LWIP/Target/ethernetif.c **** 
 673:LWIP/Target/ethernetif.c ****   /* USER CODE END ETH_MspInit 1 */
 674:LWIP/Target/ethernetif.c ****   }
 675:LWIP/Target/ethernetif.c **** }
 676:LWIP/Target/ethernetif.c **** 
 677:LWIP/Target/ethernetif.c **** void HAL_ETH_MspDeInit(ETH_HandleTypeDef* ethHandle)
 678:LWIP/Target/ethernetif.c **** {
 679:LWIP/Target/ethernetif.c ****   if(ethHandle->Instance==ETH)
 680:LWIP/Target/ethernetif.c ****   {
 681:LWIP/Target/ethernetif.c ****   /* USER CODE BEGIN ETH_MspDeInit 0 */
 682:LWIP/Target/ethernetif.c **** 
 683:LWIP/Target/ethernetif.c ****   /* USER CODE END ETH_MspDeInit 0 */
 684:LWIP/Target/ethernetif.c ****     /* Disable Peripheral clock */
 685:LWIP/Target/ethernetif.c ****     __HAL_RCC_ETH1MAC_CLK_DISABLE();
 686:LWIP/Target/ethernetif.c ****     __HAL_RCC_ETH1TX_CLK_DISABLE();
 687:LWIP/Target/ethernetif.c ****     __HAL_RCC_ETH1RX_CLK_DISABLE();
 688:LWIP/Target/ethernetif.c **** 
 689:LWIP/Target/ethernetif.c ****     /**ETH GPIO Configuration
 690:LWIP/Target/ethernetif.c ****     PC1     ------> ETH_MDC
 691:LWIP/Target/ethernetif.c ****     PA1     ------> ETH_REF_CLK
 692:LWIP/Target/ethernetif.c ****     PA2     ------> ETH_MDIO
 693:LWIP/Target/ethernetif.c ****     PA7     ------> ETH_CRS_DV
 694:LWIP/Target/ethernetif.c ****     PC4     ------> ETH_RXD0
 695:LWIP/Target/ethernetif.c ****     PC5     ------> ETH_RXD1
 696:LWIP/Target/ethernetif.c ****     PB11     ------> ETH_TX_EN
 697:LWIP/Target/ethernetif.c ****     PB12     ------> ETH_TXD0
 698:LWIP/Target/ethernetif.c ****     PB13     ------> ETH_TXD1
 699:LWIP/Target/ethernetif.c ****     */
 700:LWIP/Target/ethernetif.c ****     HAL_GPIO_DeInit(GPIOC, GPIO_PIN_1|GPIO_PIN_4|GPIO_PIN_5);
 701:LWIP/Target/ethernetif.c **** 
 702:LWIP/Target/ethernetif.c ****     HAL_GPIO_DeInit(GPIOA, GPIO_PIN_1|GPIO_PIN_2|GPIO_PIN_7);
 703:LWIP/Target/ethernetif.c **** 
 704:LWIP/Target/ethernetif.c ****     HAL_GPIO_DeInit(GPIOB, GPIO_PIN_11|GPIO_PIN_12|GPIO_PIN_13);
 705:LWIP/Target/ethernetif.c **** 
 706:LWIP/Target/ethernetif.c ****     /* Peripheral interrupt Deinit*/
 707:LWIP/Target/ethernetif.c ****     HAL_NVIC_DisableIRQ(ETH_IRQn);
 708:LWIP/Target/ethernetif.c **** 
 709:LWIP/Target/ethernetif.c ****     HAL_NVIC_DisableIRQ(ETH_WKUP_IRQn);
 710:LWIP/Target/ethernetif.c **** 
 711:LWIP/Target/ethernetif.c ****   /* USER CODE BEGIN ETH_MspDeInit 1 */
 712:LWIP/Target/ethernetif.c **** 
 713:LWIP/Target/ethernetif.c ****   /* USER CODE END ETH_MspDeInit 1 */
 714:LWIP/Target/ethernetif.c ****   }
 715:LWIP/Target/ethernetif.c **** }
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 14


 716:LWIP/Target/ethernetif.c **** 
 717:LWIP/Target/ethernetif.c **** /*******************************************************************************
 718:LWIP/Target/ethernetif.c ****                        PHI IO Functions
 719:LWIP/Target/ethernetif.c **** *******************************************************************************/
 720:LWIP/Target/ethernetif.c **** /**
 721:LWIP/Target/ethernetif.c ****   * @brief  Initializes the MDIO interface GPIO and clocks.
 722:LWIP/Target/ethernetif.c ****   * @param  None
 723:LWIP/Target/ethernetif.c ****   * @retval 0 if OK, -1 if ERROR
 724:LWIP/Target/ethernetif.c ****   */
 725:LWIP/Target/ethernetif.c **** int32_t ETH_PHY_IO_Init(void)
 726:LWIP/Target/ethernetif.c **** {
 727:LWIP/Target/ethernetif.c ****   /* We assume that MDIO GPIO configuration is already done
 728:LWIP/Target/ethernetif.c ****      in the ETH_MspInit() else it should be done here
 729:LWIP/Target/ethernetif.c ****   */
 730:LWIP/Target/ethernetif.c **** 
 731:LWIP/Target/ethernetif.c ****   /* Configure the MDIO Clock */
 732:LWIP/Target/ethernetif.c ****   HAL_ETH_SetMDIOClockRange(&heth);
 733:LWIP/Target/ethernetif.c **** 
 734:LWIP/Target/ethernetif.c ****   return 0;
 735:LWIP/Target/ethernetif.c **** }
 736:LWIP/Target/ethernetif.c **** 
 737:LWIP/Target/ethernetif.c **** /**
 738:LWIP/Target/ethernetif.c ****   * @brief  De-Initializes the MDIO interface .
 739:LWIP/Target/ethernetif.c ****   * @param  None
 740:LWIP/Target/ethernetif.c ****   * @retval 0 if OK, -1 if ERROR
 741:LWIP/Target/ethernetif.c ****   */
 742:LWIP/Target/ethernetif.c **** int32_t ETH_PHY_IO_DeInit (void)
 743:LWIP/Target/ethernetif.c **** {
  28              		.loc 1 743 1 view -0
  29              		.cfi_startproc
  30              		@ args = 0, pretend = 0, frame = 0
  31              		@ frame_needed = 0, uses_anonymous_args = 0
  32              		@ link register save eliminated.
 744:LWIP/Target/ethernetif.c ****   return 0;
  33              		.loc 1 744 3 view .LVU1
 745:LWIP/Target/ethernetif.c **** }
  34              		.loc 1 745 1 is_stmt 0 view .LVU2
  35 0000 0020     		movs	r0, #0
  36 0002 7047     		bx	lr
  37              		.cfi_endproc
  38              	.LFE187:
  40              		.section	.text.low_level_input,"ax",%progbits
  41              		.align	1
  42              		.syntax unified
  43              		.thumb
  44              		.thumb_func
  46              	low_level_input:
  47              	.LVL0:
  48              	.LFB179:
 454:LWIP/Target/ethernetif.c ****   struct pbuf *p = NULL;
  49              		.loc 1 454 1 is_stmt 1 view -0
  50              		.cfi_startproc
  51              		@ args = 0, pretend = 0, frame = 8
  52              		@ frame_needed = 0, uses_anonymous_args = 0
 454:LWIP/Target/ethernetif.c ****   struct pbuf *p = NULL;
  53              		.loc 1 454 1 is_stmt 0 view .LVU4
  54 0000 00B5     		push	{lr}
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 15


  55              	.LCFI0:
  56              		.cfi_def_cfa_offset 4
  57              		.cfi_offset 14, -4
  58 0002 83B0     		sub	sp, sp, #12
  59              	.LCFI1:
  60              		.cfi_def_cfa_offset 16
 455:LWIP/Target/ethernetif.c **** 
  61              		.loc 1 455 3 is_stmt 1 view .LVU5
 455:LWIP/Target/ethernetif.c **** 
  62              		.loc 1 455 16 is_stmt 0 view .LVU6
  63 0004 0023     		movs	r3, #0
  64 0006 0193     		str	r3, [sp, #4]
 457:LWIP/Target/ethernetif.c ****   {
  65              		.loc 1 457 3 is_stmt 1 view .LVU7
 457:LWIP/Target/ethernetif.c ****   {
  66              		.loc 1 457 20 is_stmt 0 view .LVU8
  67 0008 054B     		ldr	r3, .L6
  68 000a 1B78     		ldrb	r3, [r3]	@ zero_extendqisi2
 457:LWIP/Target/ethernetif.c ****   {
  69              		.loc 1 457 5 view .LVU9
  70 000c 1BB1     		cbz	r3, .L5
  71              	.LVL1:
  72              	.L3:
 462:LWIP/Target/ethernetif.c **** }
  73              		.loc 1 462 3 is_stmt 1 view .LVU10
 463:LWIP/Target/ethernetif.c **** 
  74              		.loc 1 463 1 is_stmt 0 view .LVU11
  75 000e 0198     		ldr	r0, [sp, #4]
  76 0010 03B0     		add	sp, sp, #12
  77              	.LCFI2:
  78              		.cfi_remember_state
  79              		.cfi_def_cfa_offset 4
  80              		@ sp needed
  81 0012 5DF804FB 		ldr	pc, [sp], #4
  82              	.LVL2:
  83              	.L5:
  84              	.LCFI3:
  85              		.cfi_restore_state
 459:LWIP/Target/ethernetif.c ****   }
  86              		.loc 1 459 5 is_stmt 1 view .LVU12
  87 0016 01A9     		add	r1, sp, #4
  88 0018 0248     		ldr	r0, .L6+4
  89              	.LVL3:
 459:LWIP/Target/ethernetif.c ****   }
  90              		.loc 1 459 5 is_stmt 0 view .LVU13
  91 001a FFF7FEFF 		bl	HAL_ETH_ReadData
  92              	.LVL4:
  93 001e F6E7     		b	.L3
  94              	.L7:
  95              		.align	2
  96              	.L6:
  97 0020 00000000 		.word	RxAllocStatus
  98 0024 00000000 		.word	heth
  99              		.cfi_endproc
 100              	.LFE179:
 102              		.section	.text.ethernetif_input,"ax",%progbits
 103              		.align	1
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 16


 104              		.global	ethernetif_input
 105              		.syntax unified
 106              		.thumb
 107              		.thumb_func
 109              	ethernetif_input:
 110              	.LVL5:
 111              	.LFB180:
 475:LWIP/Target/ethernetif.c ****   struct pbuf *p = NULL;
 112              		.loc 1 475 1 is_stmt 1 view -0
 113              		.cfi_startproc
 114              		@ args = 0, pretend = 0, frame = 0
 115              		@ frame_needed = 0, uses_anonymous_args = 0
 475:LWIP/Target/ethernetif.c ****   struct pbuf *p = NULL;
 116              		.loc 1 475 1 is_stmt 0 view .LVU15
 117 0000 38B5     		push	{r3, r4, r5, lr}
 118              	.LCFI4:
 119              		.cfi_def_cfa_offset 16
 120              		.cfi_offset 3, -16
 121              		.cfi_offset 4, -12
 122              		.cfi_offset 5, -8
 123              		.cfi_offset 14, -4
 124 0002 0546     		mov	r5, r0
 476:LWIP/Target/ethernetif.c ****   struct netif *netif = (struct netif *) argument;
 125              		.loc 1 476 3 is_stmt 1 view .LVU16
 126              	.LVL6:
 477:LWIP/Target/ethernetif.c **** 
 127              		.loc 1 477 3 view .LVU17
 128              	.L9:
 479:LWIP/Target/ethernetif.c ****   {
 129              		.loc 1 479 3 view .LVU18
 481:LWIP/Target/ethernetif.c ****     {
 130              		.loc 1 481 5 view .LVU19
 481:LWIP/Target/ethernetif.c ****     {
 131              		.loc 1 481 9 is_stmt 0 view .LVU20
 132 0004 4FF0FF31 		mov	r1, #-1
 133 0008 0B4B     		ldr	r3, .L14
 134 000a 1868     		ldr	r0, [r3]
 135 000c FFF7FEFF 		bl	osSemaphoreAcquire
 136              	.LVL7:
 481:LWIP/Target/ethernetif.c ****     {
 137              		.loc 1 481 8 discriminator 1 view .LVU21
 138 0010 0028     		cmp	r0, #0
 139 0012 F7D1     		bne	.L9
 140 0014 01E0     		b	.L11
 141              	.LVL8:
 142              	.L10:
 493:LWIP/Target/ethernetif.c ****     }
 143              		.loc 1 493 16 is_stmt 1 view .LVU22
 144 0016 002C     		cmp	r4, #0
 145 0018 F4D0     		beq	.L9
 146              	.LVL9:
 147              	.L11:
 483:LWIP/Target/ethernetif.c ****       {
 148              		.loc 1 483 7 view .LVU23
 485:LWIP/Target/ethernetif.c ****         if (p != NULL)
 149              		.loc 1 485 9 view .LVU24
 485:LWIP/Target/ethernetif.c ****         if (p != NULL)
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 17


 150              		.loc 1 485 13 is_stmt 0 view .LVU25
 151 001a 2846     		mov	r0, r5
 152 001c FFF7FEFF 		bl	low_level_input
 153              	.LVL10:
 486:LWIP/Target/ethernetif.c ****         {
 154              		.loc 1 486 9 is_stmt 1 view .LVU26
 486:LWIP/Target/ethernetif.c ****         {
 155              		.loc 1 486 12 is_stmt 0 view .LVU27
 156 0020 0446     		mov	r4, r0
 157 0022 0028     		cmp	r0, #0
 158 0024 F7D0     		beq	.L10
 488:LWIP/Target/ethernetif.c ****           {
 159              		.loc 1 488 11 is_stmt 1 view .LVU28
 488:LWIP/Target/ethernetif.c ****           {
 160              		.loc 1 488 20 is_stmt 0 view .LVU29
 161 0026 2B69     		ldr	r3, [r5, #16]
 488:LWIP/Target/ethernetif.c ****           {
 162              		.loc 1 488 15 view .LVU30
 163 0028 2946     		mov	r1, r5
 164 002a 9847     		blx	r3
 165              	.LVL11:
 488:LWIP/Target/ethernetif.c ****           {
 166              		.loc 1 488 14 discriminator 1 view .LVU31
 167 002c 0028     		cmp	r0, #0
 168 002e F2D0     		beq	.L10
 490:LWIP/Target/ethernetif.c ****           }
 169              		.loc 1 490 13 is_stmt 1 view .LVU32
 170 0030 2046     		mov	r0, r4
 171 0032 FFF7FEFF 		bl	pbuf_free
 172              	.LVL12:
 173 0036 EEE7     		b	.L10
 174              	.L15:
 175              		.align	2
 176              	.L14:
 177 0038 00000000 		.word	RxPktSemaphore
 178              		.cfi_endproc
 179              	.LFE180:
 181              		.section	.text.low_level_output,"ax",%progbits
 182              		.align	1
 183              		.syntax unified
 184              		.thumb
 185              		.thumb_func
 187              	low_level_output:
 188              	.LVL13:
 189              	.LFB178:
 382:LWIP/Target/ethernetif.c ****   uint32_t i = 0U;
 190              		.loc 1 382 1 view -0
 191              		.cfi_startproc
 192              		@ args = 0, pretend = 0, frame = 48
 193              		@ frame_needed = 0, uses_anonymous_args = 0
 382:LWIP/Target/ethernetif.c ****   uint32_t i = 0U;
 194              		.loc 1 382 1 is_stmt 0 view .LVU34
 195 0000 30B5     		push	{r4, r5, lr}
 196              	.LCFI5:
 197              		.cfi_def_cfa_offset 12
 198              		.cfi_offset 4, -12
 199              		.cfi_offset 5, -8
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 18


 200              		.cfi_offset 14, -4
 201 0002 8DB0     		sub	sp, sp, #52
 202              	.LCFI6:
 203              		.cfi_def_cfa_offset 64
 204 0004 0C46     		mov	r4, r1
 383:LWIP/Target/ethernetif.c ****   struct pbuf *q = NULL;
 205              		.loc 1 383 3 is_stmt 1 view .LVU35
 206              	.LVL14:
 384:LWIP/Target/ethernetif.c ****   err_t errval = ERR_OK;
 207              		.loc 1 384 3 view .LVU36
 385:LWIP/Target/ethernetif.c ****   ETH_BufferTypeDef Txbuffer[ETH_TX_DESC_CNT] = {0};
 208              		.loc 1 385 3 view .LVU37
 386:LWIP/Target/ethernetif.c **** 
 209              		.loc 1 386 3 view .LVU38
 386:LWIP/Target/ethernetif.c **** 
 210              		.loc 1 386 21 is_stmt 0 view .LVU39
 211 0006 3025     		movs	r5, #48
 212 0008 2A46     		mov	r2, r5
 213 000a 0021     		movs	r1, #0
 214              	.LVL15:
 386:LWIP/Target/ethernetif.c **** 
 215              		.loc 1 386 21 view .LVU40
 216 000c 6846     		mov	r0, sp
 217              	.LVL16:
 386:LWIP/Target/ethernetif.c **** 
 218              		.loc 1 386 21 view .LVU41
 219 000e FFF7FEFF 		bl	memset
 220              	.LVL17:
 388:LWIP/Target/ethernetif.c **** 
 221              		.loc 1 388 3 is_stmt 1 view .LVU42
 222 0012 2A46     		mov	r2, r5
 223 0014 0021     		movs	r1, #0
 224 0016 6846     		mov	r0, sp
 225 0018 FFF7FEFF 		bl	memset
 226              	.LVL18:
 390:LWIP/Target/ethernetif.c ****   {
 227              		.loc 1 390 3 view .LVU43
 390:LWIP/Target/ethernetif.c ****   {
 228              		.loc 1 390 9 is_stmt 0 view .LVU44
 229 001c 2246     		mov	r2, r4
 383:LWIP/Target/ethernetif.c ****   struct pbuf *q = NULL;
 230              		.loc 1 383 12 view .LVU45
 231 001e 0023     		movs	r3, #0
 390:LWIP/Target/ethernetif.c ****   {
 232              		.loc 1 390 3 view .LVU46
 233 0020 03E0     		b	.L17
 234              	.LVL19:
 235              	.L19:
 403:LWIP/Target/ethernetif.c ****     {
 236              		.loc 1 403 5 is_stmt 1 view .LVU47
 403:LWIP/Target/ethernetif.c ****     {
 237              		.loc 1 403 9 is_stmt 0 view .LVU48
 238 0022 1168     		ldr	r1, [r2]
 403:LWIP/Target/ethernetif.c ****     {
 239              		.loc 1 403 7 view .LVU49
 240 0024 F1B1     		cbz	r1, .L28
 241              	.L20:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 19


 408:LWIP/Target/ethernetif.c ****   }
 242              		.loc 1 408 5 is_stmt 1 view .LVU50
 408:LWIP/Target/ethernetif.c ****   }
 243              		.loc 1 408 6 is_stmt 0 view .LVU51
 244 0026 0133     		adds	r3, r3, #1
 245              	.LVL20:
 390:LWIP/Target/ethernetif.c ****   {
 246              		.loc 1 390 27 is_stmt 1 discriminator 2 view .LVU52
 247 0028 1268     		ldr	r2, [r2]
 248              	.LVL21:
 249              	.L17:
 390:LWIP/Target/ethernetif.c ****   {
 250              		.loc 1 390 16 discriminator 1 view .LVU53
 251 002a 22B3     		cbz	r2, .L29
 392:LWIP/Target/ethernetif.c ****       return ERR_IF;
 252              		.loc 1 392 5 view .LVU54
 392:LWIP/Target/ethernetif.c ****       return ERR_IF;
 253              		.loc 1 392 7 is_stmt 0 view .LVU55
 254 002c 032B     		cmp	r3, #3
 255 002e 4ED8     		bhi	.L25
 395:LWIP/Target/ethernetif.c ****     Txbuffer[i].len = q->len;
 256              		.loc 1 395 5 is_stmt 1 view .LVU56
 395:LWIP/Target/ethernetif.c ****     Txbuffer[i].len = q->len;
 257              		.loc 1 395 27 is_stmt 0 view .LVU57
 258 0030 5168     		ldr	r1, [r2, #4]
 395:LWIP/Target/ethernetif.c ****     Txbuffer[i].len = q->len;
 259              		.loc 1 395 24 view .LVU58
 260 0032 03EB430C 		add	ip, r3, r3, lsl #1
 261 0036 0CA8     		add	r0, sp, #48
 262 0038 00EB8C0C 		add	ip, r0, ip, lsl #2
 263 003c 4CF8301C 		str	r1, [ip, #-48]
 396:LWIP/Target/ethernetif.c **** 
 264              		.loc 1 396 5 is_stmt 1 view .LVU59
 396:LWIP/Target/ethernetif.c **** 
 265              		.loc 1 396 24 is_stmt 0 view .LVU60
 266 0040 5189     		ldrh	r1, [r2, #10]
 396:LWIP/Target/ethernetif.c **** 
 267              		.loc 1 396 21 view .LVU61
 268 0042 4CF82C1C 		str	r1, [ip, #-44]
 398:LWIP/Target/ethernetif.c ****     {
 269              		.loc 1 398 5 is_stmt 1 view .LVU62
 398:LWIP/Target/ethernetif.c ****     {
 270              		.loc 1 398 7 is_stmt 0 view .LVU63
 271 0046 002B     		cmp	r3, #0
 272 0048 EBD0     		beq	.L19
 400:LWIP/Target/ethernetif.c ****     }
 273              		.loc 1 400 7 is_stmt 1 view .LVU64
 400:LWIP/Target/ethernetif.c ****     }
 274              		.loc 1 400 17 is_stmt 0 view .LVU65
 275 004a 03F1FF3C 		add	ip, r3, #-1
 400:LWIP/Target/ethernetif.c ****     }
 276              		.loc 1 400 28 view .LVU66
 277 004e 03EB4301 		add	r1, r3, r3, lsl #1
 278 0052 0DEB8101 		add	r1, sp, r1, lsl #2
 400:LWIP/Target/ethernetif.c ****     }
 279              		.loc 1 400 26 view .LVU67
 280 0056 0CEB4C0C 		add	ip, ip, ip, lsl #1
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 20


 281 005a 00EB8C0C 		add	ip, r0, ip, lsl #2
 282 005e 4CF8281C 		str	r1, [ip, #-40]
 283 0062 DEE7     		b	.L19
 284              	.L28:
 405:LWIP/Target/ethernetif.c ****     }
 285              		.loc 1 405 7 is_stmt 1 view .LVU68
 405:LWIP/Target/ethernetif.c ****     }
 286              		.loc 1 405 24 is_stmt 0 view .LVU69
 287 0064 03EB4301 		add	r1, r3, r3, lsl #1
 288 0068 0CA8     		add	r0, sp, #48
 289 006a 00EB8101 		add	r1, r0, r1, lsl #2
 290 006e 0020     		movs	r0, #0
 291 0070 41F8280C 		str	r0, [r1, #-40]
 292 0074 D7E7     		b	.L20
 293              	.L29:
 411:LWIP/Target/ethernetif.c ****   TxConfig.TxBuffer = Txbuffer;
 294              		.loc 1 411 3 is_stmt 1 view .LVU70
 411:LWIP/Target/ethernetif.c ****   TxConfig.TxBuffer = Txbuffer;
 295              		.loc 1 411 22 is_stmt 0 view .LVU71
 296 0076 2289     		ldrh	r2, [r4, #8]
 297              	.LVL22:
 411:LWIP/Target/ethernetif.c ****   TxConfig.TxBuffer = Txbuffer;
 298              		.loc 1 411 19 view .LVU72
 299 0078 174B     		ldr	r3, .L31
 300              	.LVL23:
 411:LWIP/Target/ethernetif.c ****   TxConfig.TxBuffer = Txbuffer;
 301              		.loc 1 411 19 view .LVU73
 302 007a 5A60     		str	r2, [r3, #4]
 412:LWIP/Target/ethernetif.c ****   TxConfig.pData = p;
 303              		.loc 1 412 3 is_stmt 1 view .LVU74
 412:LWIP/Target/ethernetif.c ****   TxConfig.pData = p;
 304              		.loc 1 412 21 is_stmt 0 view .LVU75
 305 007c C3F808D0 		str	sp, [r3, #8]
 413:LWIP/Target/ethernetif.c **** 
 306              		.loc 1 413 3 is_stmt 1 view .LVU76
 413:LWIP/Target/ethernetif.c **** 
 307              		.loc 1 413 18 is_stmt 0 view .LVU77
 308 0080 5C63     		str	r4, [r3, #52]
 415:LWIP/Target/ethernetif.c **** 
 309              		.loc 1 415 3 is_stmt 1 view .LVU78
 310 0082 2046     		mov	r0, r4
 311 0084 FFF7FEFF 		bl	pbuf_ref
 312              	.LVL24:
 313 0088 13E0     		b	.L24
 314              	.LVL25:
 315              	.L30:
 426:LWIP/Target/ethernetif.c ****       {
 316              		.loc 1 426 7 view .LVU79
 426:LWIP/Target/ethernetif.c ****       {
 317              		.loc 1 426 10 is_stmt 0 view .LVU80
 318 008a 1448     		ldr	r0, .L31+4
 319 008c FFF7FEFF 		bl	HAL_ETH_GetError
 320              	.LVL26:
 426:LWIP/Target/ethernetif.c ****       {
 321              		.loc 1 426 9 discriminator 1 view .LVU81
 322 0090 10F0020F 		tst	r0, #2
 323 0094 15D0     		beq	.L23
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 21


 429:LWIP/Target/ethernetif.c ****         HAL_ETH_ReleaseTxPacket(&heth);
 324              		.loc 1 429 9 is_stmt 1 view .LVU82
 325 0096 4FF4FA61 		mov	r1, #2000
 326 009a 114B     		ldr	r3, .L31+8
 327 009c 1868     		ldr	r0, [r3]
 328 009e FFF7FEFF 		bl	osSemaphoreAcquire
 329              	.LVL27:
 430:LWIP/Target/ethernetif.c ****         errval = ERR_BUF;
 330              		.loc 1 430 9 view .LVU83
 331 00a2 0E48     		ldr	r0, .L31+4
 332 00a4 FFF7FEFF 		bl	HAL_ETH_ReleaseTxPacket
 333              	.LVL28:
 431:LWIP/Target/ethernetif.c ****       }
 334              		.loc 1 431 9 view .LVU84
 431:LWIP/Target/ethernetif.c ****       }
 335              		.loc 1 431 16 is_stmt 0 view .LVU85
 336 00a8 6FF00100 		mvn	r0, #1
 337              	.LVL29:
 338              	.L22:
 440:LWIP/Target/ethernetif.c **** 
 339              		.loc 1 440 17 is_stmt 1 view .LVU86
 340 00ac 10F1020F 		cmn	r0, #2
 341 00b0 0FD1     		bne	.L18
 342              	.LVL30:
 343              	.L24:
 417:LWIP/Target/ethernetif.c ****   {
 344              		.loc 1 417 3 view .LVU87
 419:LWIP/Target/ethernetif.c ****     {
 345              		.loc 1 419 5 view .LVU88
 419:LWIP/Target/ethernetif.c ****     {
 346              		.loc 1 419 8 is_stmt 0 view .LVU89
 347 00b2 0949     		ldr	r1, .L31
 348 00b4 0948     		ldr	r0, .L31+4
 349 00b6 FFF7FEFF 		bl	HAL_ETH_Transmit_IT
 350              	.LVL31:
 419:LWIP/Target/ethernetif.c ****     {
 351              		.loc 1 419 7 discriminator 1 view .LVU90
 352 00ba 0028     		cmp	r0, #0
 353 00bc E5D1     		bne	.L30
 421:LWIP/Target/ethernetif.c ****     }
 354              		.loc 1 421 14 view .LVU91
 355 00be 0020     		movs	r0, #0
 356 00c0 F4E7     		b	.L22
 357              	.L23:
 436:LWIP/Target/ethernetif.c ****         errval =  ERR_IF;
 358              		.loc 1 436 9 is_stmt 1 view .LVU92
 359 00c2 2046     		mov	r0, r4
 360 00c4 FFF7FEFF 		bl	pbuf_free
 361              	.LVL32:
 437:LWIP/Target/ethernetif.c ****       }
 362              		.loc 1 437 9 view .LVU93
 437:LWIP/Target/ethernetif.c ****       }
 363              		.loc 1 437 16 is_stmt 0 view .LVU94
 364 00c8 6FF00B00 		mvn	r0, #11
 365 00cc EEE7     		b	.L22
 366              	.LVL33:
 367              	.L25:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 22


 393:LWIP/Target/ethernetif.c **** 
 368              		.loc 1 393 14 view .LVU95
 369 00ce 6FF00B00 		mvn	r0, #11
 370              	.LVL34:
 371              	.L18:
 443:LWIP/Target/ethernetif.c **** 
 372              		.loc 1 443 1 view .LVU96
 373 00d2 0DB0     		add	sp, sp, #52
 374              	.LCFI7:
 375              		.cfi_def_cfa_offset 12
 376              		@ sp needed
 377 00d4 30BD     		pop	{r4, r5, pc}
 378              	.LVL35:
 379              	.L32:
 443:LWIP/Target/ethernetif.c **** 
 380              		.loc 1 443 1 view .LVU97
 381 00d6 00BF     		.align	2
 382              	.L31:
 383 00d8 00000000 		.word	TxConfig
 384 00dc 00000000 		.word	heth
 385 00e0 00000000 		.word	TxPktSemaphore
 386              		.cfi_endproc
 387              	.LFE178:
 389              		.section	.rodata.low_level_init.str1.4,"aMS",%progbits,1
 390              		.align	2
 391              	.LC0:
 392 0000 45746849 		.ascii	"EthIf\000"
 392      6600
 393              		.section	.text.low_level_init,"ax",%progbits
 394              		.align	1
 395              		.syntax unified
 396              		.thumb
 397              		.thumb_func
 399              	low_level_init:
 400              	.LVL36:
 401              	.LFB177:
 211:LWIP/Target/ethernetif.c ****   HAL_StatusTypeDef hal_eth_init_status = HAL_OK;
 402              		.loc 1 211 1 is_stmt 1 view -0
 403              		.cfi_startproc
 404              		@ args = 0, pretend = 0, frame = 144
 405              		@ frame_needed = 0, uses_anonymous_args = 0
 211:LWIP/Target/ethernetif.c ****   HAL_StatusTypeDef hal_eth_init_status = HAL_OK;
 406              		.loc 1 211 1 is_stmt 0 view .LVU99
 407 0000 2DE9F043 		push	{r4, r5, r6, r7, r8, r9, lr}
 408              	.LCFI8:
 409              		.cfi_def_cfa_offset 28
 410              		.cfi_offset 4, -28
 411              		.cfi_offset 5, -24
 412              		.cfi_offset 6, -20
 413              		.cfi_offset 7, -16
 414              		.cfi_offset 8, -12
 415              		.cfi_offset 9, -8
 416              		.cfi_offset 14, -4
 417 0004 A5B0     		sub	sp, sp, #148
 418              	.LCFI9:
 419              		.cfi_def_cfa_offset 176
 420 0006 0446     		mov	r4, r0
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 23


 212:LWIP/Target/ethernetif.c **** /* USER CODE BEGIN OS_THREAD_ATTR_CMSIS_RTOS_V2 */
 421              		.loc 1 212 3 is_stmt 1 view .LVU100
 422              	.LVL37:
 214:LWIP/Target/ethernetif.c **** /* USER CODE END OS_THREAD_ATTR_CMSIS_RTOS_V2 */
 423              		.loc 1 214 3 view .LVU101
 216:LWIP/Target/ethernetif.c ****   int32_t PHYLinkState = 0;
 424              		.loc 1 216 3 view .LVU102
 217:LWIP/Target/ethernetif.c ****   ETH_MACConfigTypeDef MACConf = {0};
 425              		.loc 1 217 3 view .LVU103
 218:LWIP/Target/ethernetif.c ****   /* Start ETH HAL Init */
 426              		.loc 1 218 3 view .LVU104
 218:LWIP/Target/ethernetif.c ****   /* Start ETH HAL Init */
 427              		.loc 1 218 24 is_stmt 0 view .LVU105
 428 0008 6422     		movs	r2, #100
 429 000a 0021     		movs	r1, #0
 430 000c 02A8     		add	r0, sp, #8
 431              	.LVL38:
 218:LWIP/Target/ethernetif.c ****   /* Start ETH HAL Init */
 432              		.loc 1 218 24 view .LVU106
 433 000e FFF7FEFF 		bl	memset
 434              	.LVL39:
 221:LWIP/Target/ethernetif.c ****   heth.Instance = ETH;
 435              		.loc 1 221 4 is_stmt 1 view .LVU107
 222:LWIP/Target/ethernetif.c ****   MACAddr[0] = 0x00;
 436              		.loc 1 222 3 view .LVU108
 222:LWIP/Target/ethernetif.c ****   MACAddr[0] = 0x00;
 437              		.loc 1 222 17 is_stmt 0 view .LVU109
 438 0012 5F4E     		ldr	r6, .L46
 439 0014 5F4B     		ldr	r3, .L46+4
 440 0016 3360     		str	r3, [r6]
 223:LWIP/Target/ethernetif.c ****   MACAddr[1] = 0x80;
 441              		.loc 1 223 3 is_stmt 1 view .LVU110
 223:LWIP/Target/ethernetif.c ****   MACAddr[1] = 0x80;
 442              		.loc 1 223 14 is_stmt 0 view .LVU111
 443 0018 0025     		movs	r5, #0
 444 001a 8DF80050 		strb	r5, [sp]
 224:LWIP/Target/ethernetif.c ****   MACAddr[2] = 0xE1;
 445              		.loc 1 224 3 is_stmt 1 view .LVU112
 224:LWIP/Target/ethernetif.c ****   MACAddr[2] = 0xE1;
 446              		.loc 1 224 14 is_stmt 0 view .LVU113
 447 001e 8023     		movs	r3, #128
 448 0020 8DF80130 		strb	r3, [sp, #1]
 225:LWIP/Target/ethernetif.c ****   MACAddr[3] = 0x00;
 449              		.loc 1 225 3 is_stmt 1 view .LVU114
 225:LWIP/Target/ethernetif.c ****   MACAddr[3] = 0x00;
 450              		.loc 1 225 14 is_stmt 0 view .LVU115
 451 0024 E123     		movs	r3, #225
 452 0026 8DF80230 		strb	r3, [sp, #2]
 226:LWIP/Target/ethernetif.c ****   MACAddr[4] = 0x00;
 453              		.loc 1 226 3 is_stmt 1 view .LVU116
 226:LWIP/Target/ethernetif.c ****   MACAddr[4] = 0x00;
 454              		.loc 1 226 14 is_stmt 0 view .LVU117
 455 002a 8DF80350 		strb	r5, [sp, #3]
 227:LWIP/Target/ethernetif.c ****   MACAddr[5] = 0x00;
 456              		.loc 1 227 3 is_stmt 1 view .LVU118
 227:LWIP/Target/ethernetif.c ****   MACAddr[5] = 0x00;
 457              		.loc 1 227 14 is_stmt 0 view .LVU119
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 24


 458 002e 8DF80450 		strb	r5, [sp, #4]
 228:LWIP/Target/ethernetif.c ****   heth.Init.MACAddr = &MACAddr[0];
 459              		.loc 1 228 3 is_stmt 1 view .LVU120
 228:LWIP/Target/ethernetif.c ****   heth.Init.MACAddr = &MACAddr[0];
 460              		.loc 1 228 14 is_stmt 0 view .LVU121
 461 0032 8DF80550 		strb	r5, [sp, #5]
 229:LWIP/Target/ethernetif.c ****   heth.Init.MediaInterface = HAL_ETH_RMII_MODE;
 462              		.loc 1 229 3 is_stmt 1 view .LVU122
 229:LWIP/Target/ethernetif.c ****   heth.Init.MediaInterface = HAL_ETH_RMII_MODE;
 463              		.loc 1 229 21 is_stmt 0 view .LVU123
 464 0036 C6F804D0 		str	sp, [r6, #4]
 230:LWIP/Target/ethernetif.c ****   heth.Init.TxDesc = DMATxDscrTab;
 465              		.loc 1 230 3 is_stmt 1 view .LVU124
 230:LWIP/Target/ethernetif.c ****   heth.Init.TxDesc = DMATxDscrTab;
 466              		.loc 1 230 28 is_stmt 0 view .LVU125
 467 003a 0127     		movs	r7, #1
 468 003c 3772     		strb	r7, [r6, #8]
 231:LWIP/Target/ethernetif.c ****   heth.Init.RxDesc = DMARxDscrTab;
 469              		.loc 1 231 3 is_stmt 1 view .LVU126
 231:LWIP/Target/ethernetif.c ****   heth.Init.RxDesc = DMARxDscrTab;
 470              		.loc 1 231 20 is_stmt 0 view .LVU127
 471 003e 564B     		ldr	r3, .L46+8
 472 0040 F360     		str	r3, [r6, #12]
 232:LWIP/Target/ethernetif.c ****   heth.Init.RxBuffLen = 1536;
 473              		.loc 1 232 3 is_stmt 1 view .LVU128
 232:LWIP/Target/ethernetif.c ****   heth.Init.RxBuffLen = 1536;
 474              		.loc 1 232 20 is_stmt 0 view .LVU129
 475 0042 564B     		ldr	r3, .L46+12
 476 0044 3361     		str	r3, [r6, #16]
 233:LWIP/Target/ethernetif.c **** 
 477              		.loc 1 233 3 is_stmt 1 view .LVU130
 233:LWIP/Target/ethernetif.c **** 
 478              		.loc 1 233 23 is_stmt 0 view .LVU131
 479 0046 4FF4C063 		mov	r3, #1536
 480 004a 7361     		str	r3, [r6, #20]
 239:LWIP/Target/ethernetif.c **** 
 481              		.loc 1 239 3 is_stmt 1 view .LVU132
 239:LWIP/Target/ethernetif.c **** 
 482              		.loc 1 239 25 is_stmt 0 view .LVU133
 483 004c 3046     		mov	r0, r6
 484 004e FFF7FEFF 		bl	HAL_ETH_Init
 485              	.LVL40:
 486 0052 8046     		mov	r8, r0
 487              	.LVL41:
 241:LWIP/Target/ethernetif.c ****   TxConfig.Attributes = ETH_TX_PACKETS_FEATURES_CSUM | ETH_TX_PACKETS_FEATURES_CRCPAD;
 488              		.loc 1 241 3 is_stmt 1 view .LVU134
 489 0054 DFF86491 		ldr	r9, .L46+44
 490 0058 3822     		movs	r2, #56
 491 005a 2946     		mov	r1, r5
 492 005c 4846     		mov	r0, r9
 493 005e FFF7FEFF 		bl	memset
 494              	.LVL42:
 242:LWIP/Target/ethernetif.c ****   TxConfig.ChecksumCtrl = ETH_CHECKSUM_IPHDR_PAYLOAD_INSERT_PHDR_CALC;
 495              		.loc 1 242 3 view .LVU135
 242:LWIP/Target/ethernetif.c ****   TxConfig.ChecksumCtrl = ETH_CHECKSUM_IPHDR_PAYLOAD_INSERT_PHDR_CALC;
 496              		.loc 1 242 23 is_stmt 0 view .LVU136
 497 0062 2123     		movs	r3, #33
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 25


 498 0064 C9F80030 		str	r3, [r9]
 243:LWIP/Target/ethernetif.c ****   TxConfig.CRCPadCtrl = ETH_CRC_PAD_INSERT;
 499              		.loc 1 243 3 is_stmt 1 view .LVU137
 243:LWIP/Target/ethernetif.c ****   TxConfig.CRCPadCtrl = ETH_CRC_PAD_INSERT;
 500              		.loc 1 243 25 is_stmt 0 view .LVU138
 501 0068 4FF44033 		mov	r3, #196608
 502 006c C9F81430 		str	r3, [r9, #20]
 244:LWIP/Target/ethernetif.c **** 
 503              		.loc 1 244 3 is_stmt 1 view .LVU139
 249:LWIP/Target/ethernetif.c **** 
 504              		.loc 1 249 3 view .LVU140
 505 0070 4B48     		ldr	r0, .L46+16
 506 0072 FFF7FEFF 		bl	memp_init_pool
 507              	.LVL43:
 253:LWIP/Target/ethernetif.c **** 
 508              		.loc 1 253 3 view .LVU141
 253:LWIP/Target/ethernetif.c **** 
 509              		.loc 1 253 21 is_stmt 0 view .LVU142
 510 0076 0623     		movs	r3, #6
 511 0078 84F82C30 		strb	r3, [r4, #44]
 256:LWIP/Target/ethernetif.c ****   netif->hwaddr[1] =  heth.Init.MACAddr[1];
 512              		.loc 1 256 3 is_stmt 1 view .LVU143
 256:LWIP/Target/ethernetif.c ****   netif->hwaddr[1] =  heth.Init.MACAddr[1];
 513              		.loc 1 256 32 is_stmt 0 view .LVU144
 514 007c 7368     		ldr	r3, [r6, #4]
 256:LWIP/Target/ethernetif.c ****   netif->hwaddr[1] =  heth.Init.MACAddr[1];
 515              		.loc 1 256 40 view .LVU145
 516 007e 1A78     		ldrb	r2, [r3]	@ zero_extendqisi2
 256:LWIP/Target/ethernetif.c ****   netif->hwaddr[1] =  heth.Init.MACAddr[1];
 517              		.loc 1 256 20 view .LVU146
 518 0080 84F82620 		strb	r2, [r4, #38]
 257:LWIP/Target/ethernetif.c ****   netif->hwaddr[2] =  heth.Init.MACAddr[2];
 519              		.loc 1 257 3 is_stmt 1 view .LVU147
 257:LWIP/Target/ethernetif.c ****   netif->hwaddr[2] =  heth.Init.MACAddr[2];
 520              		.loc 1 257 20 is_stmt 0 view .LVU148
 521 0084 5A78     		ldrb	r2, [r3, #1]	@ zero_extendqisi2
 522 0086 84F82720 		strb	r2, [r4, #39]
 258:LWIP/Target/ethernetif.c ****   netif->hwaddr[3] =  heth.Init.MACAddr[3];
 523              		.loc 1 258 3 is_stmt 1 view .LVU149
 258:LWIP/Target/ethernetif.c ****   netif->hwaddr[3] =  heth.Init.MACAddr[3];
 524              		.loc 1 258 20 is_stmt 0 view .LVU150
 525 008a 9A78     		ldrb	r2, [r3, #2]	@ zero_extendqisi2
 526 008c 84F82820 		strb	r2, [r4, #40]
 259:LWIP/Target/ethernetif.c ****   netif->hwaddr[4] =  heth.Init.MACAddr[4];
 527              		.loc 1 259 3 is_stmt 1 view .LVU151
 259:LWIP/Target/ethernetif.c ****   netif->hwaddr[4] =  heth.Init.MACAddr[4];
 528              		.loc 1 259 20 is_stmt 0 view .LVU152
 529 0090 DA78     		ldrb	r2, [r3, #3]	@ zero_extendqisi2
 530 0092 84F82920 		strb	r2, [r4, #41]
 260:LWIP/Target/ethernetif.c ****   netif->hwaddr[5] =  heth.Init.MACAddr[5];
 531              		.loc 1 260 3 is_stmt 1 view .LVU153
 260:LWIP/Target/ethernetif.c ****   netif->hwaddr[5] =  heth.Init.MACAddr[5];
 532              		.loc 1 260 20 is_stmt 0 view .LVU154
 533 0096 1A79     		ldrb	r2, [r3, #4]	@ zero_extendqisi2
 534 0098 84F82A20 		strb	r2, [r4, #42]
 261:LWIP/Target/ethernetif.c **** 
 535              		.loc 1 261 3 is_stmt 1 view .LVU155
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 26


 261:LWIP/Target/ethernetif.c **** 
 536              		.loc 1 261 20 is_stmt 0 view .LVU156
 537 009c 5B79     		ldrb	r3, [r3, #5]	@ zero_extendqisi2
 538 009e 84F82B30 		strb	r3, [r4, #43]
 264:LWIP/Target/ethernetif.c **** 
 539              		.loc 1 264 3 is_stmt 1 view .LVU157
 264:LWIP/Target/ethernetif.c **** 
 540              		.loc 1 264 14 is_stmt 0 view .LVU158
 541 00a2 40F2DC53 		movw	r3, #1500
 542 00a6 A384     		strh	r3, [r4, #36]	@ movhi
 269:LWIP/Target/ethernetif.c ****   #else
 543              		.loc 1 269 5 is_stmt 1 view .LVU159
 269:LWIP/Target/ethernetif.c ****   #else
 544              		.loc 1 269 10 is_stmt 0 view .LVU160
 545 00a8 94F82D30 		ldrb	r3, [r4, #45]	@ zero_extendqisi2
 269:LWIP/Target/ethernetif.c ****   #else
 546              		.loc 1 269 18 view .LVU161
 547 00ac 43F00A03 		orr	r3, r3, #10
 548 00b0 84F82D30 		strb	r3, [r4, #45]
 275:LWIP/Target/ethernetif.c **** 
 549              		.loc 1 275 3 is_stmt 1 view .LVU162
 275:LWIP/Target/ethernetif.c **** 
 550              		.loc 1 275 20 is_stmt 0 view .LVU163
 551 00b4 2A46     		mov	r2, r5
 552 00b6 2946     		mov	r1, r5
 553 00b8 3846     		mov	r0, r7
 554 00ba FFF7FEFF 		bl	osSemaphoreNew
 555              	.LVL44:
 275:LWIP/Target/ethernetif.c **** 
 556              		.loc 1 275 18 discriminator 1 view .LVU164
 557 00be 394B     		ldr	r3, .L46+20
 558 00c0 1860     		str	r0, [r3]
 278:LWIP/Target/ethernetif.c **** 
 559              		.loc 1 278 3 is_stmt 1 view .LVU165
 278:LWIP/Target/ethernetif.c **** 
 560              		.loc 1 278 20 is_stmt 0 view .LVU166
 561 00c2 2A46     		mov	r2, r5
 562 00c4 2946     		mov	r1, r5
 563 00c6 3846     		mov	r0, r7
 564 00c8 FFF7FEFF 		bl	osSemaphoreNew
 565              	.LVL45:
 278:LWIP/Target/ethernetif.c **** 
 566              		.loc 1 278 18 discriminator 1 view .LVU167
 567 00cc 364B     		ldr	r3, .L46+24
 568 00ce 1860     		str	r0, [r3]
 282:LWIP/Target/ethernetif.c ****   attributes.name = "EthIf";
 569              		.loc 1 282 3 is_stmt 1 view .LVU168
 570 00d0 2422     		movs	r2, #36
 571 00d2 2946     		mov	r1, r5
 572 00d4 1BA8     		add	r0, sp, #108
 573 00d6 FFF7FEFF 		bl	memset
 574              	.LVL46:
 283:LWIP/Target/ethernetif.c ****   attributes.stack_size = INTERFACE_THREAD_STACK_SIZE;
 575              		.loc 1 283 3 view .LVU169
 283:LWIP/Target/ethernetif.c ****   attributes.stack_size = INTERFACE_THREAD_STACK_SIZE;
 576              		.loc 1 283 19 is_stmt 0 view .LVU170
 577 00da 344B     		ldr	r3, .L46+28
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 27


 578 00dc 1B93     		str	r3, [sp, #108]
 284:LWIP/Target/ethernetif.c ****   attributes.priority = osPriorityRealtime;
 579              		.loc 1 284 3 is_stmt 1 view .LVU171
 284:LWIP/Target/ethernetif.c ****   attributes.priority = osPriorityRealtime;
 580              		.loc 1 284 25 is_stmt 0 view .LVU172
 581 00de 4FF4AF73 		mov	r3, #350
 582 00e2 2093     		str	r3, [sp, #128]
 285:LWIP/Target/ethernetif.c ****   osThreadNew(ethernetif_input, netif, &attributes);
 583              		.loc 1 285 3 is_stmt 1 view .LVU173
 285:LWIP/Target/ethernetif.c ****   osThreadNew(ethernetif_input, netif, &attributes);
 584              		.loc 1 285 23 is_stmt 0 view .LVU174
 585 00e4 3023     		movs	r3, #48
 586 00e6 2193     		str	r3, [sp, #132]
 286:LWIP/Target/ethernetif.c **** /* USER CODE END OS_THREAD_NEW_CMSIS_RTOS_V2 */
 587              		.loc 1 286 3 is_stmt 1 view .LVU175
 588 00e8 1BAA     		add	r2, sp, #108
 589 00ea 2146     		mov	r1, r4
 590 00ec 3048     		ldr	r0, .L46+32
 591 00ee FFF7FEFF 		bl	osThreadNew
 592              	.LVL47:
 293:LWIP/Target/ethernetif.c **** 
 593              		.loc 1 293 3 view .LVU176
 594 00f2 304D     		ldr	r5, .L46+36
 595 00f4 3049     		ldr	r1, .L46+40
 596 00f6 2846     		mov	r0, r5
 597 00f8 FFF7FEFF 		bl	LAN8742_RegisterBusIO
 598              	.LVL48:
 296:LWIP/Target/ethernetif.c ****   {
 599              		.loc 1 296 3 view .LVU177
 296:LWIP/Target/ethernetif.c ****   {
 600              		.loc 1 296 6 is_stmt 0 view .LVU178
 601 00fc 2846     		mov	r0, r5
 602 00fe FFF7FEFF 		bl	LAN8742_Init
 603              	.LVL49:
 296:LWIP/Target/ethernetif.c ****   {
 604              		.loc 1 296 5 discriminator 1 view .LVU179
 605 0102 28BB     		cbnz	r0, .L44
 303:LWIP/Target/ethernetif.c ****   {
 606              		.loc 1 303 3 is_stmt 1 view .LVU180
 303:LWIP/Target/ethernetif.c ****   {
 607              		.loc 1 303 6 is_stmt 0 view .LVU181
 608 0104 B8F1000F 		cmp	r8, #0
 609 0108 3CD1     		bne	.L36
 305:LWIP/Target/ethernetif.c **** 
 610              		.loc 1 305 5 is_stmt 1 view .LVU182
 305:LWIP/Target/ethernetif.c **** 
 611              		.loc 1 305 20 is_stmt 0 view .LVU183
 612 010a 2A48     		ldr	r0, .L46+36
 613 010c FFF7FEFF 		bl	LAN8742_GetLinkState
 614              	.LVL50:
 308:LWIP/Target/ethernetif.c ****     {
 615              		.loc 1 308 5 is_stmt 1 view .LVU184
 308:LWIP/Target/ethernetif.c ****     {
 616              		.loc 1 308 7 is_stmt 0 view .LVU185
 617 0110 0128     		cmp	r0, #1
 618 0112 24DD     		ble	.L45
 315:LWIP/Target/ethernetif.c ****       {
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 28


 619              		.loc 1 315 7 is_stmt 1 view .LVU186
 620 0114 0428     		cmp	r0, #4
 621 0116 29D0     		beq	.L40
 622 0118 0528     		cmp	r0, #5
 623 011a 2BD0     		beq	.L41
 624 011c 0328     		cmp	r0, #3
 625 011e 2CD1     		bne	.L42
 323:LWIP/Target/ethernetif.c ****         break;
 626              		.loc 1 323 15 is_stmt 0 view .LVU187
 627 0120 4FF48046 		mov	r6, #16384
 322:LWIP/Target/ethernetif.c ****         speed = ETH_SPEED_100M;
 628              		.loc 1 322 16 view .LVU188
 629 0124 0027     		movs	r7, #0
 630              	.L39:
 631              	.LVL51:
 340:LWIP/Target/ethernetif.c ****     MACConf.DuplexMode = duplex;
 632              		.loc 1 340 5 is_stmt 1 view .LVU189
 633 0126 1A4D     		ldr	r5, .L46
 634 0128 02A9     		add	r1, sp, #8
 635 012a 2846     		mov	r0, r5
 636              	.LVL52:
 340:LWIP/Target/ethernetif.c ****     MACConf.DuplexMode = duplex;
 637              		.loc 1 340 5 is_stmt 0 view .LVU190
 638 012c FFF7FEFF 		bl	HAL_ETH_GetMACConfig
 639              	.LVL53:
 341:LWIP/Target/ethernetif.c ****     MACConf.Speed = speed;
 640              		.loc 1 341 5 is_stmt 1 view .LVU191
 341:LWIP/Target/ethernetif.c ****     MACConf.Speed = speed;
 641              		.loc 1 341 24 is_stmt 0 view .LVU192
 642 0130 0897     		str	r7, [sp, #32]
 342:LWIP/Target/ethernetif.c ****     HAL_ETH_SetMACConfig(&heth, &MACConf);
 643              		.loc 1 342 5 is_stmt 1 view .LVU193
 342:LWIP/Target/ethernetif.c ****     HAL_ETH_SetMACConfig(&heth, &MACConf);
 644              		.loc 1 342 19 is_stmt 0 view .LVU194
 645 0132 0796     		str	r6, [sp, #28]
 343:LWIP/Target/ethernetif.c **** 
 646              		.loc 1 343 5 is_stmt 1 view .LVU195
 647 0134 02A9     		add	r1, sp, #8
 648 0136 2846     		mov	r0, r5
 649 0138 FFF7FEFF 		bl	HAL_ETH_SetMACConfig
 650              	.LVL54:
 345:LWIP/Target/ethernetif.c ****     netif_set_up(netif);
 651              		.loc 1 345 5 view .LVU196
 652 013c 2846     		mov	r0, r5
 653 013e FFF7FEFF 		bl	HAL_ETH_Start_IT
 654              	.LVL55:
 346:LWIP/Target/ethernetif.c ****     netif_set_link_up(netif);
 655              		.loc 1 346 5 view .LVU197
 656 0142 2046     		mov	r0, r4
 657 0144 FFF7FEFF 		bl	netif_set_up
 658              	.LVL56:
 347:LWIP/Target/ethernetif.c **** /* USER CODE BEGIN PHY_POST_CONFIG */
 659              		.loc 1 347 5 view .LVU198
 660 0148 2046     		mov	r0, r4
 661 014a FFF7FEFF 		bl	netif_set_link_up
 662              	.LVL57:
 663 014e 1BE0     		b	.L33
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 29


 664              	.LVL58:
 665              	.L44:
 298:LWIP/Target/ethernetif.c ****     netif_set_down(netif);
 666              		.loc 1 298 5 view .LVU199
 667 0150 2046     		mov	r0, r4
 668 0152 FFF7FEFF 		bl	netif_set_link_down
 669              	.LVL59:
 299:LWIP/Target/ethernetif.c ****     return;
 670              		.loc 1 299 5 view .LVU200
 671 0156 2046     		mov	r0, r4
 672 0158 FFF7FEFF 		bl	netif_set_down
 673              	.LVL60:
 300:LWIP/Target/ethernetif.c ****   }
 674              		.loc 1 300 5 view .LVU201
 675 015c 14E0     		b	.L33
 676              	.LVL61:
 677              	.L45:
 310:LWIP/Target/ethernetif.c ****       netif_set_down(netif);
 678              		.loc 1 310 7 view .LVU202
 679 015e 2046     		mov	r0, r4
 680              	.LVL62:
 310:LWIP/Target/ethernetif.c ****       netif_set_down(netif);
 681              		.loc 1 310 7 is_stmt 0 view .LVU203
 682 0160 FFF7FEFF 		bl	netif_set_link_down
 683              	.LVL63:
 311:LWIP/Target/ethernetif.c ****     }
 684              		.loc 1 311 7 is_stmt 1 view .LVU204
 685 0164 2046     		mov	r0, r4
 686 0166 FFF7FEFF 		bl	netif_set_down
 687              	.LVL64:
 688 016a 0DE0     		b	.L33
 689              	.LVL65:
 690              	.L40:
 327:LWIP/Target/ethernetif.c ****         break;
 691              		.loc 1 327 15 is_stmt 0 view .LVU205
 692 016c 0026     		movs	r6, #0
 326:LWIP/Target/ethernetif.c ****         speed = ETH_SPEED_10M;
 693              		.loc 1 326 16 view .LVU206
 694 016e 4FF40057 		mov	r7, #8192
 695 0172 D8E7     		b	.L39
 696              	.L41:
 331:LWIP/Target/ethernetif.c ****         break;
 697              		.loc 1 331 15 view .LVU207
 698 0174 0026     		movs	r6, #0
 330:LWIP/Target/ethernetif.c ****         speed = ETH_SPEED_10M;
 699              		.loc 1 330 16 view .LVU208
 700 0176 3746     		mov	r7, r6
 701 0178 D5E7     		b	.L39
 702              	.L42:
 315:LWIP/Target/ethernetif.c ****       {
 703              		.loc 1 315 7 view .LVU209
 704 017a 4FF48046 		mov	r6, #16384
 705 017e 4FF40057 		mov	r7, #8192
 706 0182 D0E7     		b	.L39
 707              	.LVL66:
 708              	.L36:
 356:LWIP/Target/ethernetif.c ****   }
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 30


 709              		.loc 1 356 5 is_stmt 1 view .LVU210
 710 0184 FFF7FEFF 		bl	Error_Handler
 711              	.LVL67:
 712              	.L33:
 363:LWIP/Target/ethernetif.c **** 
 713              		.loc 1 363 1 is_stmt 0 view .LVU211
 714 0188 25B0     		add	sp, sp, #148
 715              	.LCFI10:
 716              		.cfi_def_cfa_offset 28
 717              		@ sp needed
 718 018a BDE8F083 		pop	{r4, r5, r6, r7, r8, r9, pc}
 719              	.LVL68:
 720              	.L47:
 363:LWIP/Target/ethernetif.c **** 
 721              		.loc 1 363 1 view .LVU212
 722 018e 00BF     		.align	2
 723              	.L46:
 724 0190 00000000 		.word	heth
 725 0194 00800240 		.word	1073905664
 726 0198 00000000 		.word	DMATxDscrTab
 727 019c 00000000 		.word	DMARxDscrTab
 728 01a0 00000000 		.word	memp_RX_POOL
 729 01a4 00000000 		.word	RxPktSemaphore
 730 01a8 00000000 		.word	TxPktSemaphore
 731 01ac 00000000 		.word	.LC0
 732 01b0 00000000 		.word	ethernetif_input
 733 01b4 00000000 		.word	LAN8742
 734 01b8 00000000 		.word	LAN8742_IOCtx
 735 01bc 00000000 		.word	TxConfig
 736              		.cfi_endproc
 737              	.LFE177:
 739              		.section	.text.pbuf_free_custom,"ax",%progbits
 740              		.align	1
 741              		.global	pbuf_free_custom
 742              		.syntax unified
 743              		.thumb
 744              		.thumb_func
 746              	pbuf_free_custom:
 747              	.LVL69:
 748              	.LFB182:
 583:LWIP/Target/ethernetif.c ****   struct pbuf_custom* custom_pbuf = (struct pbuf_custom*)p;
 749              		.loc 1 583 1 is_stmt 1 view -0
 750              		.cfi_startproc
 751              		@ args = 0, pretend = 0, frame = 0
 752              		@ frame_needed = 0, uses_anonymous_args = 0
 583:LWIP/Target/ethernetif.c ****   struct pbuf_custom* custom_pbuf = (struct pbuf_custom*)p;
 753              		.loc 1 583 1 is_stmt 0 view .LVU214
 754 0000 08B5     		push	{r3, lr}
 755              	.LCFI11:
 756              		.cfi_def_cfa_offset 8
 757              		.cfi_offset 3, -8
 758              		.cfi_offset 14, -4
 759 0002 0146     		mov	r1, r0
 584:LWIP/Target/ethernetif.c ****   LWIP_MEMPOOL_FREE(RX_POOL, custom_pbuf);
 760              		.loc 1 584 3 is_stmt 1 view .LVU215
 761              	.LVL70:
 585:LWIP/Target/ethernetif.c **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 31


 762              		.loc 1 585 3 view .LVU216
 763 0004 0748     		ldr	r0, .L52
 764              	.LVL71:
 585:LWIP/Target/ethernetif.c **** 
 765              		.loc 1 585 3 is_stmt 0 view .LVU217
 766 0006 FFF7FEFF 		bl	memp_free_pool
 767              	.LVL72:
 590:LWIP/Target/ethernetif.c ****   {
 768              		.loc 1 590 3 is_stmt 1 view .LVU218
 590:LWIP/Target/ethernetif.c ****   {
 769              		.loc 1 590 21 is_stmt 0 view .LVU219
 770 000a 074B     		ldr	r3, .L52+4
 771 000c 1B78     		ldrb	r3, [r3]	@ zero_extendqisi2
 590:LWIP/Target/ethernetif.c ****   {
 772              		.loc 1 590 6 view .LVU220
 773 000e 012B     		cmp	r3, #1
 774 0010 00D0     		beq	.L51
 775              	.L48:
 595:LWIP/Target/ethernetif.c **** 
 776              		.loc 1 595 1 view .LVU221
 777 0012 08BD     		pop	{r3, pc}
 778              	.L51:
 592:LWIP/Target/ethernetif.c ****     osSemaphoreRelease(RxPktSemaphore);
 779              		.loc 1 592 5 is_stmt 1 view .LVU222
 592:LWIP/Target/ethernetif.c ****     osSemaphoreRelease(RxPktSemaphore);
 780              		.loc 1 592 19 is_stmt 0 view .LVU223
 781 0014 044B     		ldr	r3, .L52+4
 782 0016 0022     		movs	r2, #0
 783 0018 1A70     		strb	r2, [r3]
 593:LWIP/Target/ethernetif.c ****   }
 784              		.loc 1 593 5 is_stmt 1 view .LVU224
 785 001a 044B     		ldr	r3, .L52+8
 786 001c 1868     		ldr	r0, [r3]
 787 001e FFF7FEFF 		bl	osSemaphoreRelease
 788              	.LVL73:
 595:LWIP/Target/ethernetif.c **** 
 789              		.loc 1 595 1 is_stmt 0 view .LVU225
 790 0022 F6E7     		b	.L48
 791              	.L53:
 792              		.align	2
 793              	.L52:
 794 0024 00000000 		.word	memp_RX_POOL
 795 0028 00000000 		.word	RxAllocStatus
 796 002c 00000000 		.word	RxPktSemaphore
 797              		.cfi_endproc
 798              	.LFE182:
 800              		.section	.text.ETH_PHY_IO_GetTick,"ax",%progbits
 801              		.align	1
 802              		.global	ETH_PHY_IO_GetTick
 803              		.syntax unified
 804              		.thumb
 805              		.thumb_func
 807              	ETH_PHY_IO_GetTick:
 808              	.LFB190:
 746:LWIP/Target/ethernetif.c **** 
 747:LWIP/Target/ethernetif.c **** /**
 748:LWIP/Target/ethernetif.c ****   * @brief  Read a PHY register through the MDIO interface.
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 32


 749:LWIP/Target/ethernetif.c ****   * @param  DevAddr: PHY port address
 750:LWIP/Target/ethernetif.c ****   * @param  RegAddr: PHY register address
 751:LWIP/Target/ethernetif.c ****   * @param  pRegVal: pointer to hold the register value
 752:LWIP/Target/ethernetif.c ****   * @retval 0 if OK -1 if Error
 753:LWIP/Target/ethernetif.c ****   */
 754:LWIP/Target/ethernetif.c **** int32_t ETH_PHY_IO_ReadReg(uint32_t DevAddr, uint32_t RegAddr, uint32_t *pRegVal)
 755:LWIP/Target/ethernetif.c **** {
 756:LWIP/Target/ethernetif.c ****   if(HAL_ETH_ReadPHYRegister(&heth, DevAddr, RegAddr, pRegVal) != HAL_OK)
 757:LWIP/Target/ethernetif.c ****   {
 758:LWIP/Target/ethernetif.c ****     return -1;
 759:LWIP/Target/ethernetif.c ****   }
 760:LWIP/Target/ethernetif.c **** 
 761:LWIP/Target/ethernetif.c ****   return 0;
 762:LWIP/Target/ethernetif.c **** }
 763:LWIP/Target/ethernetif.c **** 
 764:LWIP/Target/ethernetif.c **** /**
 765:LWIP/Target/ethernetif.c ****   * @brief  Write a value to a PHY register through the MDIO interface.
 766:LWIP/Target/ethernetif.c ****   * @param  DevAddr: PHY port address
 767:LWIP/Target/ethernetif.c ****   * @param  RegAddr: PHY register address
 768:LWIP/Target/ethernetif.c ****   * @param  RegVal: Value to be written
 769:LWIP/Target/ethernetif.c ****   * @retval 0 if OK -1 if Error
 770:LWIP/Target/ethernetif.c ****   */
 771:LWIP/Target/ethernetif.c **** int32_t ETH_PHY_IO_WriteReg(uint32_t DevAddr, uint32_t RegAddr, uint32_t RegVal)
 772:LWIP/Target/ethernetif.c **** {
 773:LWIP/Target/ethernetif.c ****   if(HAL_ETH_WritePHYRegister(&heth, DevAddr, RegAddr, RegVal) != HAL_OK)
 774:LWIP/Target/ethernetif.c ****   {
 775:LWIP/Target/ethernetif.c ****     return -1;
 776:LWIP/Target/ethernetif.c ****   }
 777:LWIP/Target/ethernetif.c **** 
 778:LWIP/Target/ethernetif.c ****   return 0;
 779:LWIP/Target/ethernetif.c **** }
 780:LWIP/Target/ethernetif.c **** 
 781:LWIP/Target/ethernetif.c **** /**
 782:LWIP/Target/ethernetif.c ****   * @brief  Get the time in millisecons used for internal PHY driver process.
 783:LWIP/Target/ethernetif.c ****   * @retval Time value
 784:LWIP/Target/ethernetif.c ****   */
 785:LWIP/Target/ethernetif.c **** int32_t ETH_PHY_IO_GetTick(void)
 786:LWIP/Target/ethernetif.c **** {
 809              		.loc 1 786 1 is_stmt 1 view -0
 810              		.cfi_startproc
 811              		@ args = 0, pretend = 0, frame = 0
 812              		@ frame_needed = 0, uses_anonymous_args = 0
 813 0000 08B5     		push	{r3, lr}
 814              	.LCFI12:
 815              		.cfi_def_cfa_offset 8
 816              		.cfi_offset 3, -8
 817              		.cfi_offset 14, -4
 787:LWIP/Target/ethernetif.c ****   return HAL_GetTick();
 818              		.loc 1 787 3 view .LVU227
 819              		.loc 1 787 10 is_stmt 0 view .LVU228
 820 0002 FFF7FEFF 		bl	HAL_GetTick
 821              	.LVL74:
 788:LWIP/Target/ethernetif.c **** }
 822              		.loc 1 788 1 view .LVU229
 823 0006 08BD     		pop	{r3, pc}
 824              		.cfi_endproc
 825              	.LFE190:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 33


 827              		.section	.text.ETH_PHY_IO_Init,"ax",%progbits
 828              		.align	1
 829              		.global	ETH_PHY_IO_Init
 830              		.syntax unified
 831              		.thumb
 832              		.thumb_func
 834              	ETH_PHY_IO_Init:
 835              	.LFB186:
 726:LWIP/Target/ethernetif.c ****   /* We assume that MDIO GPIO configuration is already done
 836              		.loc 1 726 1 is_stmt 1 view -0
 837              		.cfi_startproc
 838              		@ args = 0, pretend = 0, frame = 0
 839              		@ frame_needed = 0, uses_anonymous_args = 0
 840 0000 08B5     		push	{r3, lr}
 841              	.LCFI13:
 842              		.cfi_def_cfa_offset 8
 843              		.cfi_offset 3, -8
 844              		.cfi_offset 14, -4
 732:LWIP/Target/ethernetif.c **** 
 845              		.loc 1 732 3 view .LVU231
 846 0002 0248     		ldr	r0, .L58
 847 0004 FFF7FEFF 		bl	HAL_ETH_SetMDIOClockRange
 848              	.LVL75:
 734:LWIP/Target/ethernetif.c **** }
 849              		.loc 1 734 3 view .LVU232
 735:LWIP/Target/ethernetif.c **** 
 850              		.loc 1 735 1 is_stmt 0 view .LVU233
 851 0008 0020     		movs	r0, #0
 852 000a 08BD     		pop	{r3, pc}
 853              	.L59:
 854              		.align	2
 855              	.L58:
 856 000c 00000000 		.word	heth
 857              		.cfi_endproc
 858              	.LFE186:
 860              		.section	.text.ETH_PHY_IO_ReadReg,"ax",%progbits
 861              		.align	1
 862              		.global	ETH_PHY_IO_ReadReg
 863              		.syntax unified
 864              		.thumb
 865              		.thumb_func
 867              	ETH_PHY_IO_ReadReg:
 868              	.LVL76:
 869              	.LFB188:
 755:LWIP/Target/ethernetif.c ****   if(HAL_ETH_ReadPHYRegister(&heth, DevAddr, RegAddr, pRegVal) != HAL_OK)
 870              		.loc 1 755 1 is_stmt 1 view -0
 871              		.cfi_startproc
 872              		@ args = 0, pretend = 0, frame = 0
 873              		@ frame_needed = 0, uses_anonymous_args = 0
 755:LWIP/Target/ethernetif.c ****   if(HAL_ETH_ReadPHYRegister(&heth, DevAddr, RegAddr, pRegVal) != HAL_OK)
 874              		.loc 1 755 1 is_stmt 0 view .LVU235
 875 0000 08B5     		push	{r3, lr}
 876              	.LCFI14:
 877              		.cfi_def_cfa_offset 8
 878              		.cfi_offset 3, -8
 879              		.cfi_offset 14, -4
 880 0002 1346     		mov	r3, r2
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 34


 756:LWIP/Target/ethernetif.c ****   {
 881              		.loc 1 756 3 is_stmt 1 view .LVU236
 756:LWIP/Target/ethernetif.c ****   {
 882              		.loc 1 756 6 is_stmt 0 view .LVU237
 883 0004 0A46     		mov	r2, r1
 884              	.LVL77:
 756:LWIP/Target/ethernetif.c ****   {
 885              		.loc 1 756 6 view .LVU238
 886 0006 0146     		mov	r1, r0
 887              	.LVL78:
 756:LWIP/Target/ethernetif.c ****   {
 888              		.loc 1 756 6 view .LVU239
 889 0008 0348     		ldr	r0, .L65
 890              	.LVL79:
 756:LWIP/Target/ethernetif.c ****   {
 891              		.loc 1 756 6 view .LVU240
 892 000a FFF7FEFF 		bl	HAL_ETH_ReadPHYRegister
 893              	.LVL80:
 756:LWIP/Target/ethernetif.c ****   {
 894              		.loc 1 756 5 discriminator 1 view .LVU241
 895 000e 00B9     		cbnz	r0, .L64
 896              	.L60:
 762:LWIP/Target/ethernetif.c **** 
 897              		.loc 1 762 1 view .LVU242
 898 0010 08BD     		pop	{r3, pc}
 899              	.L64:
 758:LWIP/Target/ethernetif.c ****   }
 900              		.loc 1 758 12 view .LVU243
 901 0012 4FF0FF30 		mov	r0, #-1
 902 0016 FBE7     		b	.L60
 903              	.L66:
 904              		.align	2
 905              	.L65:
 906 0018 00000000 		.word	heth
 907              		.cfi_endproc
 908              	.LFE188:
 910              		.section	.text.ETH_PHY_IO_WriteReg,"ax",%progbits
 911              		.align	1
 912              		.global	ETH_PHY_IO_WriteReg
 913              		.syntax unified
 914              		.thumb
 915              		.thumb_func
 917              	ETH_PHY_IO_WriteReg:
 918              	.LVL81:
 919              	.LFB189:
 772:LWIP/Target/ethernetif.c ****   if(HAL_ETH_WritePHYRegister(&heth, DevAddr, RegAddr, RegVal) != HAL_OK)
 920              		.loc 1 772 1 is_stmt 1 view -0
 921              		.cfi_startproc
 922              		@ args = 0, pretend = 0, frame = 0
 923              		@ frame_needed = 0, uses_anonymous_args = 0
 772:LWIP/Target/ethernetif.c ****   if(HAL_ETH_WritePHYRegister(&heth, DevAddr, RegAddr, RegVal) != HAL_OK)
 924              		.loc 1 772 1 is_stmt 0 view .LVU245
 925 0000 08B5     		push	{r3, lr}
 926              	.LCFI15:
 927              		.cfi_def_cfa_offset 8
 928              		.cfi_offset 3, -8
 929              		.cfi_offset 14, -4
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 35


 930 0002 1346     		mov	r3, r2
 773:LWIP/Target/ethernetif.c ****   {
 931              		.loc 1 773 3 is_stmt 1 view .LVU246
 773:LWIP/Target/ethernetif.c ****   {
 932              		.loc 1 773 6 is_stmt 0 view .LVU247
 933 0004 0A46     		mov	r2, r1
 934              	.LVL82:
 773:LWIP/Target/ethernetif.c ****   {
 935              		.loc 1 773 6 view .LVU248
 936 0006 0146     		mov	r1, r0
 937              	.LVL83:
 773:LWIP/Target/ethernetif.c ****   {
 938              		.loc 1 773 6 view .LVU249
 939 0008 0348     		ldr	r0, .L72
 940              	.LVL84:
 773:LWIP/Target/ethernetif.c ****   {
 941              		.loc 1 773 6 view .LVU250
 942 000a FFF7FEFF 		bl	HAL_ETH_WritePHYRegister
 943              	.LVL85:
 773:LWIP/Target/ethernetif.c ****   {
 944              		.loc 1 773 5 discriminator 1 view .LVU251
 945 000e 00B9     		cbnz	r0, .L71
 946              	.L67:
 779:LWIP/Target/ethernetif.c **** 
 947              		.loc 1 779 1 view .LVU252
 948 0010 08BD     		pop	{r3, pc}
 949              	.L71:
 775:LWIP/Target/ethernetif.c ****   }
 950              		.loc 1 775 12 view .LVU253
 951 0012 4FF0FF30 		mov	r0, #-1
 952 0016 FBE7     		b	.L67
 953              	.L73:
 954              		.align	2
 955              	.L72:
 956 0018 00000000 		.word	heth
 957              		.cfi_endproc
 958              	.LFE189:
 960              		.section	.text.HAL_ETH_RxCpltCallback,"ax",%progbits
 961              		.align	1
 962              		.global	HAL_ETH_RxCpltCallback
 963              		.syntax unified
 964              		.thumb
 965              		.thumb_func
 967              	HAL_ETH_RxCpltCallback:
 968              	.LVL86:
 969              	.LFB174:
 171:LWIP/Target/ethernetif.c ****   osSemaphoreRelease(RxPktSemaphore);
 970              		.loc 1 171 1 is_stmt 1 view -0
 971              		.cfi_startproc
 972              		@ args = 0, pretend = 0, frame = 0
 973              		@ frame_needed = 0, uses_anonymous_args = 0
 171:LWIP/Target/ethernetif.c ****   osSemaphoreRelease(RxPktSemaphore);
 974              		.loc 1 171 1 is_stmt 0 view .LVU255
 975 0000 08B5     		push	{r3, lr}
 976              	.LCFI16:
 977              		.cfi_def_cfa_offset 8
 978              		.cfi_offset 3, -8
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 36


 979              		.cfi_offset 14, -4
 172:LWIP/Target/ethernetif.c **** }
 980              		.loc 1 172 3 is_stmt 1 view .LVU256
 981 0002 024B     		ldr	r3, .L76
 982 0004 1868     		ldr	r0, [r3]
 983              	.LVL87:
 172:LWIP/Target/ethernetif.c **** }
 984              		.loc 1 172 3 is_stmt 0 view .LVU257
 985 0006 FFF7FEFF 		bl	osSemaphoreRelease
 986              	.LVL88:
 173:LWIP/Target/ethernetif.c **** /**
 987              		.loc 1 173 1 view .LVU258
 988 000a 08BD     		pop	{r3, pc}
 989              	.L77:
 990              		.align	2
 991              	.L76:
 992 000c 00000000 		.word	RxPktSemaphore
 993              		.cfi_endproc
 994              	.LFE174:
 996              		.section	.text.HAL_ETH_TxCpltCallback,"ax",%progbits
 997              		.align	1
 998              		.global	HAL_ETH_TxCpltCallback
 999              		.syntax unified
 1000              		.thumb
 1001              		.thumb_func
 1003              	HAL_ETH_TxCpltCallback:
 1004              	.LVL89:
 1005              	.LFB175:
 180:LWIP/Target/ethernetif.c ****   osSemaphoreRelease(TxPktSemaphore);
 1006              		.loc 1 180 1 is_stmt 1 view -0
 1007              		.cfi_startproc
 1008              		@ args = 0, pretend = 0, frame = 0
 1009              		@ frame_needed = 0, uses_anonymous_args = 0
 180:LWIP/Target/ethernetif.c ****   osSemaphoreRelease(TxPktSemaphore);
 1010              		.loc 1 180 1 is_stmt 0 view .LVU260
 1011 0000 08B5     		push	{r3, lr}
 1012              	.LCFI17:
 1013              		.cfi_def_cfa_offset 8
 1014              		.cfi_offset 3, -8
 1015              		.cfi_offset 14, -4
 181:LWIP/Target/ethernetif.c **** }
 1016              		.loc 1 181 3 is_stmt 1 view .LVU261
 1017 0002 024B     		ldr	r3, .L80
 1018 0004 1868     		ldr	r0, [r3]
 1019              	.LVL90:
 181:LWIP/Target/ethernetif.c **** }
 1020              		.loc 1 181 3 is_stmt 0 view .LVU262
 1021 0006 FFF7FEFF 		bl	osSemaphoreRelease
 1022              	.LVL91:
 182:LWIP/Target/ethernetif.c **** /**
 1023              		.loc 1 182 1 view .LVU263
 1024 000a 08BD     		pop	{r3, pc}
 1025              	.L81:
 1026              		.align	2
 1027              	.L80:
 1028 000c 00000000 		.word	TxPktSemaphore
 1029              		.cfi_endproc
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 37


 1030              	.LFE175:
 1032              		.section	.text.HAL_ETH_ErrorCallback,"ax",%progbits
 1033              		.align	1
 1034              		.global	HAL_ETH_ErrorCallback
 1035              		.syntax unified
 1036              		.thumb
 1037              		.thumb_func
 1039              	HAL_ETH_ErrorCallback:
 1040              	.LVL92:
 1041              	.LFB176:
 189:LWIP/Target/ethernetif.c ****   if((HAL_ETH_GetDMAError(handlerEth) & ETH_DMACSR_RBU) == ETH_DMACSR_RBU)
 1042              		.loc 1 189 1 is_stmt 1 view -0
 1043              		.cfi_startproc
 1044              		@ args = 0, pretend = 0, frame = 0
 1045              		@ frame_needed = 0, uses_anonymous_args = 0
 189:LWIP/Target/ethernetif.c ****   if((HAL_ETH_GetDMAError(handlerEth) & ETH_DMACSR_RBU) == ETH_DMACSR_RBU)
 1046              		.loc 1 189 1 is_stmt 0 view .LVU265
 1047 0000 08B5     		push	{r3, lr}
 1048              	.LCFI18:
 1049              		.cfi_def_cfa_offset 8
 1050              		.cfi_offset 3, -8
 1051              		.cfi_offset 14, -4
 190:LWIP/Target/ethernetif.c ****   {
 1052              		.loc 1 190 3 is_stmt 1 view .LVU266
 190:LWIP/Target/ethernetif.c ****   {
 1053              		.loc 1 190 7 is_stmt 0 view .LVU267
 1054 0002 FFF7FEFF 		bl	HAL_ETH_GetDMAError
 1055              	.LVL93:
 190:LWIP/Target/ethernetif.c ****   {
 1056              		.loc 1 190 5 discriminator 1 view .LVU268
 1057 0006 10F0800F 		tst	r0, #128
 1058 000a 00D1     		bne	.L85
 1059              	.L82:
 194:LWIP/Target/ethernetif.c **** 
 1060              		.loc 1 194 1 view .LVU269
 1061 000c 08BD     		pop	{r3, pc}
 1062              	.L85:
 192:LWIP/Target/ethernetif.c ****   }
 1063              		.loc 1 192 6 is_stmt 1 view .LVU270
 1064 000e 024B     		ldr	r3, .L86
 1065 0010 1868     		ldr	r0, [r3]
 1066 0012 FFF7FEFF 		bl	osSemaphoreRelease
 1067              	.LVL94:
 194:LWIP/Target/ethernetif.c **** 
 1068              		.loc 1 194 1 is_stmt 0 view .LVU271
 1069 0016 F9E7     		b	.L82
 1070              	.L87:
 1071              		.align	2
 1072              	.L86:
 1073 0018 00000000 		.word	RxPktSemaphore
 1074              		.cfi_endproc
 1075              	.LFE176:
 1077              		.section	.rodata.ethernetif_init.str1.4,"aMS",%progbits,1
 1078              		.align	2
 1079              	.LC1:
 1080 0000 4C574950 		.ascii	"LWIP/Target/ethernetif.c\000"
 1080      2F546172 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 38


 1080      6765742F 
 1080      65746865 
 1080      726E6574 
 1081 0019 000000   		.align	2
 1082              	.LC2:
 1083 001c 6E657469 		.ascii	"netif != NULL\000"
 1083      6620213D 
 1083      204E554C 
 1083      4C00
 1084 002a 0000     		.align	2
 1085              	.LC3:
 1086 002c 41737365 		.ascii	"Assertion \"%s\" failed at line %d in %s\012\000"
 1086      7274696F 
 1086      6E202225 
 1086      73222066 
 1086      61696C65 
 1087              		.section	.text.ethernetif_init,"ax",%progbits
 1088              		.align	1
 1089              		.global	ethernetif_init
 1090              		.syntax unified
 1091              		.thumb
 1092              		.thumb_func
 1094              	ethernetif_init:
 1095              	.LVL95:
 1096              	.LFB181:
 532:LWIP/Target/ethernetif.c ****   LWIP_ASSERT("netif != NULL", (netif != NULL));
 1097              		.loc 1 532 1 is_stmt 1 view -0
 1098              		.cfi_startproc
 1099              		@ args = 0, pretend = 0, frame = 0
 1100              		@ frame_needed = 0, uses_anonymous_args = 0
 532:LWIP/Target/ethernetif.c ****   LWIP_ASSERT("netif != NULL", (netif != NULL));
 1101              		.loc 1 532 1 is_stmt 0 view .LVU273
 1102 0000 10B5     		push	{r4, lr}
 1103              	.LCFI19:
 1104              		.cfi_def_cfa_offset 8
 1105              		.cfi_offset 4, -8
 1106              		.cfi_offset 14, -4
 533:LWIP/Target/ethernetif.c **** 
 1107              		.loc 1 533 3 is_stmt 1 view .LVU274
 533:LWIP/Target/ethernetif.c **** 
 1108              		.loc 1 533 3 view .LVU275
 1109 0002 0446     		mov	r4, r0
 1110 0004 70B1     		cbz	r0, .L91
 1111              	.LVL96:
 1112              	.L89:
 533:LWIP/Target/ethernetif.c **** 
 1113              		.loc 1 533 3 discriminator 3 view .LVU276
 533:LWIP/Target/ethernetif.c **** 
 1114              		.loc 1 533 3 discriminator 3 view .LVU277
 547:LWIP/Target/ethernetif.c ****   netif->name[1] = IFNAME1;
 1115              		.loc 1 547 3 view .LVU278
 547:LWIP/Target/ethernetif.c ****   netif->name[1] = IFNAME1;
 1116              		.loc 1 547 18 is_stmt 0 view .LVU279
 1117 0006 7323     		movs	r3, #115
 1118 0008 84F82E30 		strb	r3, [r4, #46]
 548:LWIP/Target/ethernetif.c ****   /* We directly use etharp_output() here to save a function call.
 1119              		.loc 1 548 3 is_stmt 1 view .LVU280
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 39


 548:LWIP/Target/ethernetif.c ****   /* We directly use etharp_output() here to save a function call.
 1120              		.loc 1 548 18 is_stmt 0 view .LVU281
 1121 000c 7423     		movs	r3, #116
 1122 000e 84F82F30 		strb	r3, [r4, #47]
 557:LWIP/Target/ethernetif.c **** #else
 1123              		.loc 1 557 3 is_stmt 1 view .LVU282
 557:LWIP/Target/ethernetif.c **** #else
 1124              		.loc 1 557 17 is_stmt 0 view .LVU283
 1125 0012 084B     		ldr	r3, .L92
 1126 0014 6361     		str	r3, [r4, #20]
 569:LWIP/Target/ethernetif.c **** 
 1127              		.loc 1 569 3 is_stmt 1 view .LVU284
 569:LWIP/Target/ethernetif.c **** 
 1128              		.loc 1 569 21 is_stmt 0 view .LVU285
 1129 0016 084B     		ldr	r3, .L92+4
 1130 0018 A361     		str	r3, [r4, #24]
 572:LWIP/Target/ethernetif.c **** 
 1131              		.loc 1 572 3 is_stmt 1 view .LVU286
 1132 001a 2046     		mov	r0, r4
 1133 001c FFF7FEFF 		bl	low_level_init
 1134              	.LVL97:
 574:LWIP/Target/ethernetif.c **** }
 1135              		.loc 1 574 3 view .LVU287
 575:LWIP/Target/ethernetif.c **** 
 1136              		.loc 1 575 1 is_stmt 0 view .LVU288
 1137 0020 0020     		movs	r0, #0
 1138 0022 10BD     		pop	{r4, pc}
 1139              	.LVL98:
 1140              	.L91:
 533:LWIP/Target/ethernetif.c **** 
 1141              		.loc 1 533 3 is_stmt 1 discriminator 1 view .LVU289
 533:LWIP/Target/ethernetif.c **** 
 1142              		.loc 1 533 3 discriminator 1 view .LVU290
 1143 0024 054B     		ldr	r3, .L92+8
 1144 0026 40F21522 		movw	r2, #533
 1145 002a 0549     		ldr	r1, .L92+12
 1146 002c 0548     		ldr	r0, .L92+16
 1147              	.LVL99:
 533:LWIP/Target/ethernetif.c **** 
 1148              		.loc 1 533 3 is_stmt 0 discriminator 1 view .LVU291
 1149 002e FFF7FEFF 		bl	printf
 1150              	.LVL100:
 1151 0032 E8E7     		b	.L89
 1152              	.L93:
 1153              		.align	2
 1154              	.L92:
 1155 0034 00000000 		.word	etharp_output
 1156 0038 00000000 		.word	low_level_output
 1157 003c 00000000 		.word	.LC1
 1158 0040 1C000000 		.word	.LC2
 1159 0044 2C000000 		.word	.LC3
 1160              		.cfi_endproc
 1161              	.LFE181:
 1163              		.section	.text.sys_now,"ax",%progbits
 1164              		.align	1
 1165              		.global	sys_now
 1166              		.syntax unified
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 40


 1167              		.thumb
 1168              		.thumb_func
 1170              	sys_now:
 1171              	.LFB183:
 606:LWIP/Target/ethernetif.c ****   return HAL_GetTick();
 1172              		.loc 1 606 1 is_stmt 1 view -0
 1173              		.cfi_startproc
 1174              		@ args = 0, pretend = 0, frame = 0
 1175              		@ frame_needed = 0, uses_anonymous_args = 0
 1176 0000 08B5     		push	{r3, lr}
 1177              	.LCFI20:
 1178              		.cfi_def_cfa_offset 8
 1179              		.cfi_offset 3, -8
 1180              		.cfi_offset 14, -4
 607:LWIP/Target/ethernetif.c **** }
 1181              		.loc 1 607 3 view .LVU293
 607:LWIP/Target/ethernetif.c **** }
 1182              		.loc 1 607 10 is_stmt 0 view .LVU294
 1183 0002 FFF7FEFF 		bl	HAL_GetTick
 1184              	.LVL101:
 608:LWIP/Target/ethernetif.c **** 
 1185              		.loc 1 608 1 view .LVU295
 1186 0006 08BD     		pop	{r3, pc}
 1187              		.cfi_endproc
 1188              	.LFE183:
 1190              		.section	.text.HAL_ETH_MspInit,"ax",%progbits
 1191              		.align	1
 1192              		.global	HAL_ETH_MspInit
 1193              		.syntax unified
 1194              		.thumb
 1195              		.thumb_func
 1197              	HAL_ETH_MspInit:
 1198              	.LVL102:
 1199              	.LFB184:
 619:LWIP/Target/ethernetif.c ****   GPIO_InitTypeDef GPIO_InitStruct = {0};
 1200              		.loc 1 619 1 is_stmt 1 view -0
 1201              		.cfi_startproc
 1202              		@ args = 0, pretend = 0, frame = 48
 1203              		@ frame_needed = 0, uses_anonymous_args = 0
 619:LWIP/Target/ethernetif.c ****   GPIO_InitTypeDef GPIO_InitStruct = {0};
 1204              		.loc 1 619 1 is_stmt 0 view .LVU297
 1205 0000 70B5     		push	{r4, r5, r6, lr}
 1206              	.LCFI21:
 1207              		.cfi_def_cfa_offset 16
 1208              		.cfi_offset 4, -16
 1209              		.cfi_offset 5, -12
 1210              		.cfi_offset 6, -8
 1211              		.cfi_offset 14, -4
 1212 0002 8CB0     		sub	sp, sp, #48
 1213              	.LCFI22:
 1214              		.cfi_def_cfa_offset 64
 620:LWIP/Target/ethernetif.c ****   if(ethHandle->Instance==ETH)
 1215              		.loc 1 620 3 is_stmt 1 view .LVU298
 620:LWIP/Target/ethernetif.c ****   if(ethHandle->Instance==ETH)
 1216              		.loc 1 620 20 is_stmt 0 view .LVU299
 1217 0004 0023     		movs	r3, #0
 1218 0006 0793     		str	r3, [sp, #28]
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 41


 1219 0008 0893     		str	r3, [sp, #32]
 1220 000a 0993     		str	r3, [sp, #36]
 1221 000c 0A93     		str	r3, [sp, #40]
 1222 000e 0B93     		str	r3, [sp, #44]
 621:LWIP/Target/ethernetif.c ****   {
 1223              		.loc 1 621 3 is_stmt 1 view .LVU300
 621:LWIP/Target/ethernetif.c ****   {
 1224              		.loc 1 621 15 is_stmt 0 view .LVU301
 1225 0010 0268     		ldr	r2, [r0]
 621:LWIP/Target/ethernetif.c ****   {
 1226              		.loc 1 621 5 view .LVU302
 1227 0012 3F4B     		ldr	r3, .L100
 1228 0014 9A42     		cmp	r2, r3
 1229 0016 01D0     		beq	.L99
 1230              	.LVL103:
 1231              	.L96:
 675:LWIP/Target/ethernetif.c **** 
 1232              		.loc 1 675 1 view .LVU303
 1233 0018 0CB0     		add	sp, sp, #48
 1234              	.LCFI23:
 1235              		.cfi_remember_state
 1236              		.cfi_def_cfa_offset 16
 1237              		@ sp needed
 1238 001a 70BD     		pop	{r4, r5, r6, pc}
 1239              	.LVL104:
 1240              	.L99:
 1241              	.LCFI24:
 1242              		.cfi_restore_state
 627:LWIP/Target/ethernetif.c ****     __HAL_RCC_ETH1TX_CLK_ENABLE();
 1243              		.loc 1 627 5 is_stmt 1 view .LVU304
 1244              	.LBB18:
 627:LWIP/Target/ethernetif.c ****     __HAL_RCC_ETH1TX_CLK_ENABLE();
 1245              		.loc 1 627 5 view .LVU305
 627:LWIP/Target/ethernetif.c ****     __HAL_RCC_ETH1TX_CLK_ENABLE();
 1246              		.loc 1 627 5 view .LVU306
 1247 001c 3D4B     		ldr	r3, .L100+4
 1248 001e D3F8D820 		ldr	r2, [r3, #216]
 1249 0022 42F40042 		orr	r2, r2, #32768
 1250 0026 C3F8D820 		str	r2, [r3, #216]
 627:LWIP/Target/ethernetif.c ****     __HAL_RCC_ETH1TX_CLK_ENABLE();
 1251              		.loc 1 627 5 view .LVU307
 1252 002a D3F8D820 		ldr	r2, [r3, #216]
 1253 002e 02F40042 		and	r2, r2, #32768
 1254 0032 0192     		str	r2, [sp, #4]
 627:LWIP/Target/ethernetif.c ****     __HAL_RCC_ETH1TX_CLK_ENABLE();
 1255              		.loc 1 627 5 view .LVU308
 1256 0034 019A     		ldr	r2, [sp, #4]
 1257              	.LBE18:
 627:LWIP/Target/ethernetif.c ****     __HAL_RCC_ETH1TX_CLK_ENABLE();
 1258              		.loc 1 627 5 view .LVU309
 628:LWIP/Target/ethernetif.c ****     __HAL_RCC_ETH1RX_CLK_ENABLE();
 1259              		.loc 1 628 5 view .LVU310
 1260              	.LBB19:
 628:LWIP/Target/ethernetif.c ****     __HAL_RCC_ETH1RX_CLK_ENABLE();
 1261              		.loc 1 628 5 view .LVU311
 628:LWIP/Target/ethernetif.c ****     __HAL_RCC_ETH1RX_CLK_ENABLE();
 1262              		.loc 1 628 5 view .LVU312
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 42


 1263 0036 D3F8D820 		ldr	r2, [r3, #216]
 1264 003a 42F48032 		orr	r2, r2, #65536
 1265 003e C3F8D820 		str	r2, [r3, #216]
 628:LWIP/Target/ethernetif.c ****     __HAL_RCC_ETH1RX_CLK_ENABLE();
 1266              		.loc 1 628 5 view .LVU313
 1267 0042 D3F8D820 		ldr	r2, [r3, #216]
 1268 0046 02F48032 		and	r2, r2, #65536
 1269 004a 0292     		str	r2, [sp, #8]
 628:LWIP/Target/ethernetif.c ****     __HAL_RCC_ETH1RX_CLK_ENABLE();
 1270              		.loc 1 628 5 view .LVU314
 1271 004c 029A     		ldr	r2, [sp, #8]
 1272              	.LBE19:
 628:LWIP/Target/ethernetif.c ****     __HAL_RCC_ETH1RX_CLK_ENABLE();
 1273              		.loc 1 628 5 view .LVU315
 629:LWIP/Target/ethernetif.c **** 
 1274              		.loc 1 629 5 view .LVU316
 1275              	.LBB20:
 629:LWIP/Target/ethernetif.c **** 
 1276              		.loc 1 629 5 view .LVU317
 629:LWIP/Target/ethernetif.c **** 
 1277              		.loc 1 629 5 view .LVU318
 1278 004e D3F8D820 		ldr	r2, [r3, #216]
 1279 0052 42F40032 		orr	r2, r2, #131072
 1280 0056 C3F8D820 		str	r2, [r3, #216]
 629:LWIP/Target/ethernetif.c **** 
 1281              		.loc 1 629 5 view .LVU319
 1282 005a D3F8D820 		ldr	r2, [r3, #216]
 1283 005e 02F40032 		and	r2, r2, #131072
 1284 0062 0392     		str	r2, [sp, #12]
 629:LWIP/Target/ethernetif.c **** 
 1285              		.loc 1 629 5 view .LVU320
 1286 0064 039A     		ldr	r2, [sp, #12]
 1287              	.LBE20:
 629:LWIP/Target/ethernetif.c **** 
 1288              		.loc 1 629 5 view .LVU321
 631:LWIP/Target/ethernetif.c ****     __HAL_RCC_GPIOA_CLK_ENABLE();
 1289              		.loc 1 631 5 view .LVU322
 1290              	.LBB21:
 631:LWIP/Target/ethernetif.c ****     __HAL_RCC_GPIOA_CLK_ENABLE();
 1291              		.loc 1 631 5 view .LVU323
 631:LWIP/Target/ethernetif.c ****     __HAL_RCC_GPIOA_CLK_ENABLE();
 1292              		.loc 1 631 5 view .LVU324
 1293 0066 D3F8E020 		ldr	r2, [r3, #224]
 1294 006a 42F00402 		orr	r2, r2, #4
 1295 006e C3F8E020 		str	r2, [r3, #224]
 631:LWIP/Target/ethernetif.c ****     __HAL_RCC_GPIOA_CLK_ENABLE();
 1296              		.loc 1 631 5 view .LVU325
 1297 0072 D3F8E020 		ldr	r2, [r3, #224]
 1298 0076 02F00402 		and	r2, r2, #4
 1299 007a 0492     		str	r2, [sp, #16]
 631:LWIP/Target/ethernetif.c ****     __HAL_RCC_GPIOA_CLK_ENABLE();
 1300              		.loc 1 631 5 view .LVU326
 1301 007c 049A     		ldr	r2, [sp, #16]
 1302              	.LBE21:
 631:LWIP/Target/ethernetif.c ****     __HAL_RCC_GPIOA_CLK_ENABLE();
 1303              		.loc 1 631 5 view .LVU327
 632:LWIP/Target/ethernetif.c ****     __HAL_RCC_GPIOB_CLK_ENABLE();
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 43


 1304              		.loc 1 632 5 view .LVU328
 1305              	.LBB22:
 632:LWIP/Target/ethernetif.c ****     __HAL_RCC_GPIOB_CLK_ENABLE();
 1306              		.loc 1 632 5 view .LVU329
 632:LWIP/Target/ethernetif.c ****     __HAL_RCC_GPIOB_CLK_ENABLE();
 1307              		.loc 1 632 5 view .LVU330
 1308 007e D3F8E020 		ldr	r2, [r3, #224]
 1309 0082 42F00102 		orr	r2, r2, #1
 1310 0086 C3F8E020 		str	r2, [r3, #224]
 632:LWIP/Target/ethernetif.c ****     __HAL_RCC_GPIOB_CLK_ENABLE();
 1311              		.loc 1 632 5 view .LVU331
 1312 008a D3F8E020 		ldr	r2, [r3, #224]
 1313 008e 02F00102 		and	r2, r2, #1
 1314 0092 0592     		str	r2, [sp, #20]
 632:LWIP/Target/ethernetif.c ****     __HAL_RCC_GPIOB_CLK_ENABLE();
 1315              		.loc 1 632 5 view .LVU332
 1316 0094 059A     		ldr	r2, [sp, #20]
 1317              	.LBE22:
 632:LWIP/Target/ethernetif.c ****     __HAL_RCC_GPIOB_CLK_ENABLE();
 1318              		.loc 1 632 5 view .LVU333
 633:LWIP/Target/ethernetif.c ****     /**ETH GPIO Configuration
 1319              		.loc 1 633 5 view .LVU334
 1320              	.LBB23:
 633:LWIP/Target/ethernetif.c ****     /**ETH GPIO Configuration
 1321              		.loc 1 633 5 view .LVU335
 633:LWIP/Target/ethernetif.c ****     /**ETH GPIO Configuration
 1322              		.loc 1 633 5 view .LVU336
 1323 0096 D3F8E020 		ldr	r2, [r3, #224]
 1324 009a 42F00202 		orr	r2, r2, #2
 1325 009e C3F8E020 		str	r2, [r3, #224]
 633:LWIP/Target/ethernetif.c ****     /**ETH GPIO Configuration
 1326              		.loc 1 633 5 view .LVU337
 1327 00a2 D3F8E030 		ldr	r3, [r3, #224]
 1328 00a6 03F00203 		and	r3, r3, #2
 1329 00aa 0693     		str	r3, [sp, #24]
 633:LWIP/Target/ethernetif.c ****     /**ETH GPIO Configuration
 1330              		.loc 1 633 5 view .LVU338
 1331 00ac 069B     		ldr	r3, [sp, #24]
 1332              	.LBE23:
 633:LWIP/Target/ethernetif.c ****     /**ETH GPIO Configuration
 1333              		.loc 1 633 5 view .LVU339
 645:LWIP/Target/ethernetif.c ****     GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
 1334              		.loc 1 645 5 view .LVU340
 645:LWIP/Target/ethernetif.c ****     GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
 1335              		.loc 1 645 25 is_stmt 0 view .LVU341
 1336 00ae 3223     		movs	r3, #50
 1337 00b0 0793     		str	r3, [sp, #28]
 646:LWIP/Target/ethernetif.c ****     GPIO_InitStruct.Pull = GPIO_NOPULL;
 1338              		.loc 1 646 5 is_stmt 1 view .LVU342
 646:LWIP/Target/ethernetif.c ****     GPIO_InitStruct.Pull = GPIO_NOPULL;
 1339              		.loc 1 646 26 is_stmt 0 view .LVU343
 1340 00b2 0226     		movs	r6, #2
 1341 00b4 0896     		str	r6, [sp, #32]
 647:LWIP/Target/ethernetif.c ****     GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
 1342              		.loc 1 647 5 is_stmt 1 view .LVU344
 648:LWIP/Target/ethernetif.c ****     GPIO_InitStruct.Alternate = GPIO_AF11_ETH;
 1343              		.loc 1 648 5 view .LVU345
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 44


 649:LWIP/Target/ethernetif.c ****     HAL_GPIO_Init(GPIOC, &GPIO_InitStruct);
 1344              		.loc 1 649 5 view .LVU346
 649:LWIP/Target/ethernetif.c ****     HAL_GPIO_Init(GPIOC, &GPIO_InitStruct);
 1345              		.loc 1 649 31 is_stmt 0 view .LVU347
 1346 00b6 0B25     		movs	r5, #11
 1347 00b8 0B95     		str	r5, [sp, #44]
 650:LWIP/Target/ethernetif.c **** 
 1348              		.loc 1 650 5 is_stmt 1 view .LVU348
 1349 00ba 07A9     		add	r1, sp, #28
 1350 00bc 1648     		ldr	r0, .L100+8
 1351              	.LVL105:
 650:LWIP/Target/ethernetif.c **** 
 1352              		.loc 1 650 5 is_stmt 0 view .LVU349
 1353 00be FFF7FEFF 		bl	HAL_GPIO_Init
 1354              	.LVL106:
 652:LWIP/Target/ethernetif.c ****     GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
 1355              		.loc 1 652 5 is_stmt 1 view .LVU350
 652:LWIP/Target/ethernetif.c ****     GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
 1356              		.loc 1 652 25 is_stmt 0 view .LVU351
 1357 00c2 8623     		movs	r3, #134
 1358 00c4 0793     		str	r3, [sp, #28]
 653:LWIP/Target/ethernetif.c ****     GPIO_InitStruct.Pull = GPIO_NOPULL;
 1359              		.loc 1 653 5 is_stmt 1 view .LVU352
 653:LWIP/Target/ethernetif.c ****     GPIO_InitStruct.Pull = GPIO_NOPULL;
 1360              		.loc 1 653 26 is_stmt 0 view .LVU353
 1361 00c6 0896     		str	r6, [sp, #32]
 654:LWIP/Target/ethernetif.c ****     GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
 1362              		.loc 1 654 5 is_stmt 1 view .LVU354
 654:LWIP/Target/ethernetif.c ****     GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
 1363              		.loc 1 654 26 is_stmt 0 view .LVU355
 1364 00c8 0024     		movs	r4, #0
 1365 00ca 0994     		str	r4, [sp, #36]
 655:LWIP/Target/ethernetif.c ****     GPIO_InitStruct.Alternate = GPIO_AF11_ETH;
 1366              		.loc 1 655 5 is_stmt 1 view .LVU356
 655:LWIP/Target/ethernetif.c ****     GPIO_InitStruct.Alternate = GPIO_AF11_ETH;
 1367              		.loc 1 655 27 is_stmt 0 view .LVU357
 1368 00cc 0A94     		str	r4, [sp, #40]
 656:LWIP/Target/ethernetif.c ****     HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);
 1369              		.loc 1 656 5 is_stmt 1 view .LVU358
 656:LWIP/Target/ethernetif.c ****     HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);
 1370              		.loc 1 656 31 is_stmt 0 view .LVU359
 1371 00ce 0B95     		str	r5, [sp, #44]
 657:LWIP/Target/ethernetif.c **** 
 1372              		.loc 1 657 5 is_stmt 1 view .LVU360
 1373 00d0 07A9     		add	r1, sp, #28
 1374 00d2 1248     		ldr	r0, .L100+12
 1375 00d4 FFF7FEFF 		bl	HAL_GPIO_Init
 1376              	.LVL107:
 659:LWIP/Target/ethernetif.c ****     GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
 1377              		.loc 1 659 5 view .LVU361
 659:LWIP/Target/ethernetif.c ****     GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
 1378              		.loc 1 659 25 is_stmt 0 view .LVU362
 1379 00d8 4FF46053 		mov	r3, #14336
 1380 00dc 0793     		str	r3, [sp, #28]
 660:LWIP/Target/ethernetif.c ****     GPIO_InitStruct.Pull = GPIO_NOPULL;
 1381              		.loc 1 660 5 is_stmt 1 view .LVU363
 660:LWIP/Target/ethernetif.c ****     GPIO_InitStruct.Pull = GPIO_NOPULL;
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 45


 1382              		.loc 1 660 26 is_stmt 0 view .LVU364
 1383 00de 0896     		str	r6, [sp, #32]
 661:LWIP/Target/ethernetif.c ****     GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
 1384              		.loc 1 661 5 is_stmt 1 view .LVU365
 661:LWIP/Target/ethernetif.c ****     GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
 1385              		.loc 1 661 26 is_stmt 0 view .LVU366
 1386 00e0 0994     		str	r4, [sp, #36]
 662:LWIP/Target/ethernetif.c ****     GPIO_InitStruct.Alternate = GPIO_AF11_ETH;
 1387              		.loc 1 662 5 is_stmt 1 view .LVU367
 662:LWIP/Target/ethernetif.c ****     GPIO_InitStruct.Alternate = GPIO_AF11_ETH;
 1388              		.loc 1 662 27 is_stmt 0 view .LVU368
 1389 00e2 0A94     		str	r4, [sp, #40]
 663:LWIP/Target/ethernetif.c ****     HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);
 1390              		.loc 1 663 5 is_stmt 1 view .LVU369
 663:LWIP/Target/ethernetif.c ****     HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);
 1391              		.loc 1 663 31 is_stmt 0 view .LVU370
 1392 00e4 0B95     		str	r5, [sp, #44]
 664:LWIP/Target/ethernetif.c **** 
 1393              		.loc 1 664 5 is_stmt 1 view .LVU371
 1394 00e6 07A9     		add	r1, sp, #28
 1395 00e8 0D48     		ldr	r0, .L100+16
 1396 00ea FFF7FEFF 		bl	HAL_GPIO_Init
 1397              	.LVL108:
 667:LWIP/Target/ethernetif.c ****     HAL_NVIC_EnableIRQ(ETH_IRQn);
 1398              		.loc 1 667 5 view .LVU372
 1399 00ee 2246     		mov	r2, r4
 1400 00f0 0521     		movs	r1, #5
 1401 00f2 3D20     		movs	r0, #61
 1402 00f4 FFF7FEFF 		bl	HAL_NVIC_SetPriority
 1403              	.LVL109:
 668:LWIP/Target/ethernetif.c ****     HAL_NVIC_SetPriority(ETH_WKUP_IRQn, 5, 0);
 1404              		.loc 1 668 5 view .LVU373
 1405 00f8 3D20     		movs	r0, #61
 1406 00fa FFF7FEFF 		bl	HAL_NVIC_EnableIRQ
 1407              	.LVL110:
 669:LWIP/Target/ethernetif.c ****     HAL_NVIC_EnableIRQ(ETH_WKUP_IRQn);
 1408              		.loc 1 669 5 view .LVU374
 1409 00fe 2246     		mov	r2, r4
 1410 0100 0521     		movs	r1, #5
 1411 0102 3E20     		movs	r0, #62
 1412 0104 FFF7FEFF 		bl	HAL_NVIC_SetPriority
 1413              	.LVL111:
 670:LWIP/Target/ethernetif.c ****   /* USER CODE BEGIN ETH_MspInit 1 */
 1414              		.loc 1 670 5 view .LVU375
 1415 0108 3E20     		movs	r0, #62
 1416 010a FFF7FEFF 		bl	HAL_NVIC_EnableIRQ
 1417              	.LVL112:
 675:LWIP/Target/ethernetif.c **** 
 1418              		.loc 1 675 1 is_stmt 0 view .LVU376
 1419 010e 83E7     		b	.L96
 1420              	.L101:
 1421              		.align	2
 1422              	.L100:
 1423 0110 00800240 		.word	1073905664
 1424 0114 00440258 		.word	1476543488
 1425 0118 00080258 		.word	1476528128
 1426 011c 00000258 		.word	1476526080
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 46


 1427 0120 00040258 		.word	1476527104
 1428              		.cfi_endproc
 1429              	.LFE184:
 1431              		.section	.text.HAL_ETH_MspDeInit,"ax",%progbits
 1432              		.align	1
 1433              		.global	HAL_ETH_MspDeInit
 1434              		.syntax unified
 1435              		.thumb
 1436              		.thumb_func
 1438              	HAL_ETH_MspDeInit:
 1439              	.LVL113:
 1440              	.LFB185:
 678:LWIP/Target/ethernetif.c ****   if(ethHandle->Instance==ETH)
 1441              		.loc 1 678 1 is_stmt 1 view -0
 1442              		.cfi_startproc
 1443              		@ args = 0, pretend = 0, frame = 0
 1444              		@ frame_needed = 0, uses_anonymous_args = 0
 678:LWIP/Target/ethernetif.c ****   if(ethHandle->Instance==ETH)
 1445              		.loc 1 678 1 is_stmt 0 view .LVU378
 1446 0000 08B5     		push	{r3, lr}
 1447              	.LCFI25:
 1448              		.cfi_def_cfa_offset 8
 1449              		.cfi_offset 3, -8
 1450              		.cfi_offset 14, -4
 679:LWIP/Target/ethernetif.c ****   {
 1451              		.loc 1 679 3 is_stmt 1 view .LVU379
 679:LWIP/Target/ethernetif.c ****   {
 1452              		.loc 1 679 15 is_stmt 0 view .LVU380
 1453 0002 0268     		ldr	r2, [r0]
 679:LWIP/Target/ethernetif.c ****   {
 1454              		.loc 1 679 5 view .LVU381
 1455 0004 154B     		ldr	r3, .L106
 1456 0006 9A42     		cmp	r2, r3
 1457 0008 00D0     		beq	.L105
 1458              	.LVL114:
 1459              	.L102:
 715:LWIP/Target/ethernetif.c **** 
 1460              		.loc 1 715 1 view .LVU382
 1461 000a 08BD     		pop	{r3, pc}
 1462              	.LVL115:
 1463              	.L105:
 685:LWIP/Target/ethernetif.c ****     __HAL_RCC_ETH1TX_CLK_DISABLE();
 1464              		.loc 1 685 5 is_stmt 1 view .LVU383
 1465 000c 144B     		ldr	r3, .L106+4
 1466 000e D3F8D820 		ldr	r2, [r3, #216]
 1467 0012 22F40042 		bic	r2, r2, #32768
 1468 0016 C3F8D820 		str	r2, [r3, #216]
 686:LWIP/Target/ethernetif.c ****     __HAL_RCC_ETH1RX_CLK_DISABLE();
 1469              		.loc 1 686 5 view .LVU384
 1470 001a D3F8D820 		ldr	r2, [r3, #216]
 1471 001e 22F48032 		bic	r2, r2, #65536
 1472 0022 C3F8D820 		str	r2, [r3, #216]
 687:LWIP/Target/ethernetif.c **** 
 1473              		.loc 1 687 5 view .LVU385
 1474 0026 D3F8D820 		ldr	r2, [r3, #216]
 1475 002a 22F40032 		bic	r2, r2, #131072
 1476 002e C3F8D820 		str	r2, [r3, #216]
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 47


 700:LWIP/Target/ethernetif.c **** 
 1477              		.loc 1 700 5 view .LVU386
 1478 0032 3221     		movs	r1, #50
 1479 0034 0B48     		ldr	r0, .L106+8
 1480              	.LVL116:
 700:LWIP/Target/ethernetif.c **** 
 1481              		.loc 1 700 5 is_stmt 0 view .LVU387
 1482 0036 FFF7FEFF 		bl	HAL_GPIO_DeInit
 1483              	.LVL117:
 702:LWIP/Target/ethernetif.c **** 
 1484              		.loc 1 702 5 is_stmt 1 view .LVU388
 1485 003a 8621     		movs	r1, #134
 1486 003c 0A48     		ldr	r0, .L106+12
 1487 003e FFF7FEFF 		bl	HAL_GPIO_DeInit
 1488              	.LVL118:
 704:LWIP/Target/ethernetif.c **** 
 1489              		.loc 1 704 5 view .LVU389
 1490 0042 4FF46051 		mov	r1, #14336
 1491 0046 0948     		ldr	r0, .L106+16
 1492 0048 FFF7FEFF 		bl	HAL_GPIO_DeInit
 1493              	.LVL119:
 707:LWIP/Target/ethernetif.c **** 
 1494              		.loc 1 707 5 view .LVU390
 1495 004c 3D20     		movs	r0, #61
 1496 004e FFF7FEFF 		bl	HAL_NVIC_DisableIRQ
 1497              	.LVL120:
 709:LWIP/Target/ethernetif.c **** 
 1498              		.loc 1 709 5 view .LVU391
 1499 0052 3E20     		movs	r0, #62
 1500 0054 FFF7FEFF 		bl	HAL_NVIC_DisableIRQ
 1501              	.LVL121:
 715:LWIP/Target/ethernetif.c **** 
 1502              		.loc 1 715 1 is_stmt 0 view .LVU392
 1503 0058 D7E7     		b	.L102
 1504              	.L107:
 1505 005a 00BF     		.align	2
 1506              	.L106:
 1507 005c 00800240 		.word	1073905664
 1508 0060 00440258 		.word	1476543488
 1509 0064 00080258 		.word	1476528128
 1510 0068 00000258 		.word	1476526080
 1511 006c 00040258 		.word	1476527104
 1512              		.cfi_endproc
 1513              	.LFE185:
 1515              		.section	.text.ethernet_link_thread,"ax",%progbits
 1516              		.align	1
 1517              		.global	ethernet_link_thread
 1518              		.syntax unified
 1519              		.thumb
 1520              		.thumb_func
 1522              	ethernet_link_thread:
 1523              	.LVL122:
 1524              	.LFB191:
 789:LWIP/Target/ethernetif.c **** 
 790:LWIP/Target/ethernetif.c **** /**
 791:LWIP/Target/ethernetif.c ****   * @brief  Check the ETH link state then update ETH driver and netif link accordingly.
 792:LWIP/Target/ethernetif.c ****   * @retval None
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 48


 793:LWIP/Target/ethernetif.c ****   */
 794:LWIP/Target/ethernetif.c **** void ethernet_link_thread(void* argument)
 795:LWIP/Target/ethernetif.c **** {
 1525              		.loc 1 795 1 is_stmt 1 view -0
 1526              		.cfi_startproc
 1527              		@ args = 0, pretend = 0, frame = 104
 1528              		@ frame_needed = 0, uses_anonymous_args = 0
 1529              		.loc 1 795 1 is_stmt 0 view .LVU394
 1530 0000 2DE9F041 		push	{r4, r5, r6, r7, r8, lr}
 1531              	.LCFI26:
 1532              		.cfi_def_cfa_offset 24
 1533              		.cfi_offset 4, -24
 1534              		.cfi_offset 5, -20
 1535              		.cfi_offset 6, -16
 1536              		.cfi_offset 7, -12
 1537              		.cfi_offset 8, -8
 1538              		.cfi_offset 14, -4
 1539 0004 9AB0     		sub	sp, sp, #104
 1540              	.LCFI27:
 1541              		.cfi_def_cfa_offset 128
 1542 0006 0446     		mov	r4, r0
 796:LWIP/Target/ethernetif.c ****   ETH_MACConfigTypeDef MACConf = {0};
 1543              		.loc 1 796 3 is_stmt 1 view .LVU395
 1544              		.loc 1 796 24 is_stmt 0 view .LVU396
 1545 0008 6422     		movs	r2, #100
 1546 000a 0021     		movs	r1, #0
 1547 000c 01A8     		add	r0, sp, #4
 1548              	.LVL123:
 1549              		.loc 1 796 24 view .LVU397
 1550 000e FFF7FEFF 		bl	memset
 1551              	.LVL124:
 797:LWIP/Target/ethernetif.c ****   int32_t PHYLinkState = 0;
 1552              		.loc 1 797 3 is_stmt 1 view .LVU398
 798:LWIP/Target/ethernetif.c ****   uint32_t linkchanged = 0U, speed = 0U, duplex = 0U;
 1553              		.loc 1 798 3 view .LVU399
 799:LWIP/Target/ethernetif.c **** 
 800:LWIP/Target/ethernetif.c ****   struct netif *netif = (struct netif *) argument;
 1554              		.loc 1 800 3 view .LVU400
 798:LWIP/Target/ethernetif.c ****   uint32_t linkchanged = 0U, speed = 0U, duplex = 0U;
 1555              		.loc 1 798 42 is_stmt 0 view .LVU401
 1556 0012 0025     		movs	r5, #0
 798:LWIP/Target/ethernetif.c ****   uint32_t linkchanged = 0U, speed = 0U, duplex = 0U;
 1557              		.loc 1 798 30 view .LVU402
 1558 0014 2E46     		mov	r6, r5
 798:LWIP/Target/ethernetif.c ****   uint32_t linkchanged = 0U, speed = 0U, duplex = 0U;
 1559              		.loc 1 798 12 view .LVU403
 1560 0016 2F46     		mov	r7, r5
 1561 0018 26E0     		b	.L117
 1562              	.LVL125:
 1563              	.L120:
 801:LWIP/Target/ethernetif.c **** /* USER CODE BEGIN ETH link init */
 802:LWIP/Target/ethernetif.c **** 
 803:LWIP/Target/ethernetif.c **** /* USER CODE END ETH link init */
 804:LWIP/Target/ethernetif.c **** 
 805:LWIP/Target/ethernetif.c ****   for(;;)
 806:LWIP/Target/ethernetif.c ****   {
 807:LWIP/Target/ethernetif.c ****   PHYLinkState = LAN8742_GetLinkState(&LAN8742);
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 49


 808:LWIP/Target/ethernetif.c **** 
 809:LWIP/Target/ethernetif.c ****   if(netif_is_link_up(netif) && (PHYLinkState <= LAN8742_STATUS_LINK_DOWN))
 810:LWIP/Target/ethernetif.c ****   {
 811:LWIP/Target/ethernetif.c ****     HAL_ETH_Stop_IT(&heth);
 1564              		.loc 1 811 5 is_stmt 1 view .LVU404
 1565 001a 2B48     		ldr	r0, .L121
 1566              	.LVL126:
 1567              		.loc 1 811 5 is_stmt 0 view .LVU405
 1568 001c FFF7FEFF 		bl	HAL_ETH_Stop_IT
 1569              	.LVL127:
 812:LWIP/Target/ethernetif.c ****     netif_set_down(netif);
 1570              		.loc 1 812 5 is_stmt 1 view .LVU406
 1571 0020 2046     		mov	r0, r4
 1572 0022 FFF7FEFF 		bl	netif_set_down
 1573              	.LVL128:
 813:LWIP/Target/ethernetif.c ****     netif_set_link_down(netif);
 1574              		.loc 1 813 5 view .LVU407
 1575 0026 2046     		mov	r0, r4
 1576 0028 FFF7FEFF 		bl	netif_set_link_down
 1577              	.LVL129:
 1578 002c 19E0     		b	.L110
 1579              	.LVL130:
 1580              	.L116:
 814:LWIP/Target/ethernetif.c ****   }
 815:LWIP/Target/ethernetif.c ****   else if(!netif_is_link_up(netif) && (PHYLinkState > LAN8742_STATUS_LINK_DOWN))
 816:LWIP/Target/ethernetif.c ****   {
 817:LWIP/Target/ethernetif.c **** 
 818:LWIP/Target/ethernetif.c ****     switch (PHYLinkState)
 1581              		.loc 1 818 5 is_stmt 0 view .LVU408
 1582 002e 4FF40055 		mov	r5, #8192
 1583              	.LVL131:
 1584              		.loc 1 818 5 view .LVU409
 1585 0032 4FF48046 		mov	r6, #16384
 1586              	.LVL132:
 1587              		.loc 1 818 5 view .LVU410
 1588 0036 0127     		movs	r7, #1
 1589              	.LVL133:
 1590              	.L112:
 819:LWIP/Target/ethernetif.c ****     {
 820:LWIP/Target/ethernetif.c ****     case LAN8742_STATUS_100MBITS_FULLDUPLEX:
 821:LWIP/Target/ethernetif.c ****       duplex = ETH_FULLDUPLEX_MODE;
 822:LWIP/Target/ethernetif.c ****       speed = ETH_SPEED_100M;
 823:LWIP/Target/ethernetif.c ****       linkchanged = 1;
 824:LWIP/Target/ethernetif.c ****       break;
 825:LWIP/Target/ethernetif.c ****     case LAN8742_STATUS_100MBITS_HALFDUPLEX:
 826:LWIP/Target/ethernetif.c ****       duplex = ETH_HALFDUPLEX_MODE;
 827:LWIP/Target/ethernetif.c ****       speed = ETH_SPEED_100M;
 828:LWIP/Target/ethernetif.c ****       linkchanged = 1;
 829:LWIP/Target/ethernetif.c ****       break;
 830:LWIP/Target/ethernetif.c ****     case LAN8742_STATUS_10MBITS_FULLDUPLEX:
 831:LWIP/Target/ethernetif.c ****       duplex = ETH_FULLDUPLEX_MODE;
 832:LWIP/Target/ethernetif.c ****       speed = ETH_SPEED_10M;
 833:LWIP/Target/ethernetif.c ****       linkchanged = 1;
 834:LWIP/Target/ethernetif.c ****       break;
 835:LWIP/Target/ethernetif.c ****     case LAN8742_STATUS_10MBITS_HALFDUPLEX:
 836:LWIP/Target/ethernetif.c ****       duplex = ETH_HALFDUPLEX_MODE;
 837:LWIP/Target/ethernetif.c ****       speed = ETH_SPEED_10M;
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 50


 838:LWIP/Target/ethernetif.c ****       linkchanged = 1;
 839:LWIP/Target/ethernetif.c ****       break;
 840:LWIP/Target/ethernetif.c ****     default:
 841:LWIP/Target/ethernetif.c ****       break;
 842:LWIP/Target/ethernetif.c ****     }
 843:LWIP/Target/ethernetif.c **** 
 844:LWIP/Target/ethernetif.c ****     if(linkchanged)
 845:LWIP/Target/ethernetif.c ****     {
 846:LWIP/Target/ethernetif.c ****       /* Get MAC Config MAC */
 847:LWIP/Target/ethernetif.c ****       HAL_ETH_GetMACConfig(&heth, &MACConf);
 1591              		.loc 1 847 7 is_stmt 1 view .LVU411
 1592 0038 DFF88C80 		ldr	r8, .L121
 1593 003c 01A9     		add	r1, sp, #4
 1594 003e 4046     		mov	r0, r8
 1595              	.LVL134:
 1596              		.loc 1 847 7 is_stmt 0 view .LVU412
 1597 0040 FFF7FEFF 		bl	HAL_ETH_GetMACConfig
 1598              	.LVL135:
 848:LWIP/Target/ethernetif.c ****       MACConf.DuplexMode = duplex;
 1599              		.loc 1 848 7 is_stmt 1 view .LVU413
 1600              		.loc 1 848 26 is_stmt 0 view .LVU414
 1601 0044 0795     		str	r5, [sp, #28]
 849:LWIP/Target/ethernetif.c ****       MACConf.Speed = speed;
 1602              		.loc 1 849 7 is_stmt 1 view .LVU415
 1603              		.loc 1 849 21 is_stmt 0 view .LVU416
 1604 0046 0696     		str	r6, [sp, #24]
 850:LWIP/Target/ethernetif.c ****       HAL_ETH_SetMACConfig(&heth, &MACConf);
 1605              		.loc 1 850 7 is_stmt 1 view .LVU417
 1606 0048 01A9     		add	r1, sp, #4
 1607 004a 4046     		mov	r0, r8
 1608 004c FFF7FEFF 		bl	HAL_ETH_SetMACConfig
 1609              	.LVL136:
 851:LWIP/Target/ethernetif.c ****       HAL_ETH_Start_IT(&heth);
 1610              		.loc 1 851 7 view .LVU418
 1611 0050 4046     		mov	r0, r8
 1612 0052 FFF7FEFF 		bl	HAL_ETH_Start_IT
 1613              	.LVL137:
 852:LWIP/Target/ethernetif.c ****       netif_set_up(netif);
 1614              		.loc 1 852 7 view .LVU419
 1615 0056 2046     		mov	r0, r4
 1616 0058 FFF7FEFF 		bl	netif_set_up
 1617              	.LVL138:
 853:LWIP/Target/ethernetif.c ****       netif_set_link_up(netif);
 1618              		.loc 1 853 7 view .LVU420
 1619 005c 2046     		mov	r0, r4
 1620 005e FFF7FEFF 		bl	netif_set_link_up
 1621              	.LVL139:
 1622              	.L110:
 854:LWIP/Target/ethernetif.c ****     }
 855:LWIP/Target/ethernetif.c ****   }
 856:LWIP/Target/ethernetif.c **** 
 857:LWIP/Target/ethernetif.c **** /* USER CODE BEGIN ETH link Thread core code for User BSP */
 858:LWIP/Target/ethernetif.c **** 
 859:LWIP/Target/ethernetif.c **** /* USER CODE END ETH link Thread core code for User BSP */
 860:LWIP/Target/ethernetif.c **** 
 861:LWIP/Target/ethernetif.c ****     osDelay(100);
 1623              		.loc 1 861 5 view .LVU421
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 51


 1624 0062 6420     		movs	r0, #100
 1625 0064 FFF7FEFF 		bl	osDelay
 1626              	.LVL140:
 805:LWIP/Target/ethernetif.c ****   {
 1627              		.loc 1 805 3 view .LVU422
 1628              	.L117:
 805:LWIP/Target/ethernetif.c ****   {
 1629              		.loc 1 805 3 view .LVU423
 807:LWIP/Target/ethernetif.c **** 
 1630              		.loc 1 807 3 view .LVU424
 807:LWIP/Target/ethernetif.c **** 
 1631              		.loc 1 807 18 is_stmt 0 view .LVU425
 1632 0068 1848     		ldr	r0, .L121+4
 1633 006a FFF7FEFF 		bl	LAN8742_GetLinkState
 1634              	.LVL141:
 809:LWIP/Target/ethernetif.c ****   {
 1635              		.loc 1 809 3 is_stmt 1 view .LVU426
 809:LWIP/Target/ethernetif.c ****   {
 1636              		.loc 1 809 6 is_stmt 0 view .LVU427
 1637 006e 94F82D30 		ldrb	r3, [r4, #45]	@ zero_extendqisi2
 809:LWIP/Target/ethernetif.c ****   {
 1638              		.loc 1 809 5 view .LVU428
 1639 0072 13F0040F 		tst	r3, #4
 1640 0076 01D0     		beq	.L109
 809:LWIP/Target/ethernetif.c ****   {
 1641              		.loc 1 809 30 discriminator 1 view .LVU429
 1642 0078 0128     		cmp	r0, #1
 1643 007a CEDD     		ble	.L120
 1644              	.L109:
 815:LWIP/Target/ethernetif.c ****   {
 1645              		.loc 1 815 8 is_stmt 1 view .LVU430
 815:LWIP/Target/ethernetif.c ****   {
 1646              		.loc 1 815 10 is_stmt 0 view .LVU431
 1647 007c 13F0040F 		tst	r3, #4
 1648 0080 EFD1     		bne	.L110
 815:LWIP/Target/ethernetif.c ****   {
 1649              		.loc 1 815 36 discriminator 1 view .LVU432
 1650 0082 0128     		cmp	r0, #1
 1651 0084 EDDD     		ble	.L110
 818:LWIP/Target/ethernetif.c ****     {
 1652              		.loc 1 818 5 is_stmt 1 view .LVU433
 1653 0086 0238     		subs	r0, r0, #2
 1654              	.LVL142:
 818:LWIP/Target/ethernetif.c ****     {
 1655              		.loc 1 818 5 is_stmt 0 view .LVU434
 1656 0088 0328     		cmp	r0, #3
 1657 008a 15D8     		bhi	.L111
 1658 008c 01A3     		adr	r3, .L113
 1659 008e 53F820F0 		ldr	pc, [r3, r0, lsl #2]
 1660 0092 00BF     		.p2align 2
 1661              	.L113:
 1662 0094 2F000000 		.word	.L116+1
 1663 0098 AF000000 		.word	.L115+1
 1664 009c A5000000 		.word	.L114+1
 1665 00a0 BF000000 		.word	.L118+1
 1666              		.p2align 1
 1667              	.L114:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 52


 831:LWIP/Target/ethernetif.c ****       speed = ETH_SPEED_10M;
 1668              		.loc 1 831 7 is_stmt 1 view .LVU435
 1669              	.LVL143:
 832:LWIP/Target/ethernetif.c ****       linkchanged = 1;
 1670              		.loc 1 832 7 view .LVU436
 833:LWIP/Target/ethernetif.c ****       break;
 1671              		.loc 1 833 7 view .LVU437
 834:LWIP/Target/ethernetif.c ****     case LAN8742_STATUS_10MBITS_HALFDUPLEX:
 1672              		.loc 1 834 7 view .LVU438
 844:LWIP/Target/ethernetif.c ****     {
 1673              		.loc 1 844 5 view .LVU439
 831:LWIP/Target/ethernetif.c ****       speed = ETH_SPEED_10M;
 1674              		.loc 1 831 14 is_stmt 0 view .LVU440
 1675 00a4 4FF40055 		mov	r5, #8192
 832:LWIP/Target/ethernetif.c ****       linkchanged = 1;
 1676              		.loc 1 832 13 view .LVU441
 1677 00a8 0026     		movs	r6, #0
 833:LWIP/Target/ethernetif.c ****       break;
 1678              		.loc 1 833 19 view .LVU442
 1679 00aa 0127     		movs	r7, #1
 1680 00ac C4E7     		b	.L112
 1681              	.LVL144:
 1682              	.L115:
 826:LWIP/Target/ethernetif.c ****       speed = ETH_SPEED_100M;
 1683              		.loc 1 826 7 is_stmt 1 view .LVU443
 827:LWIP/Target/ethernetif.c ****       linkchanged = 1;
 1684              		.loc 1 827 7 view .LVU444
 828:LWIP/Target/ethernetif.c ****       break;
 1685              		.loc 1 828 7 view .LVU445
 829:LWIP/Target/ethernetif.c ****     case LAN8742_STATUS_10MBITS_FULLDUPLEX:
 1686              		.loc 1 829 7 view .LVU446
 844:LWIP/Target/ethernetif.c ****     {
 1687              		.loc 1 844 5 view .LVU447
 826:LWIP/Target/ethernetif.c ****       speed = ETH_SPEED_100M;
 1688              		.loc 1 826 14 is_stmt 0 view .LVU448
 1689 00ae 0025     		movs	r5, #0
 827:LWIP/Target/ethernetif.c ****       linkchanged = 1;
 1690              		.loc 1 827 13 view .LVU449
 1691 00b0 4FF48046 		mov	r6, #16384
 828:LWIP/Target/ethernetif.c ****       break;
 1692              		.loc 1 828 19 view .LVU450
 1693 00b4 0127     		movs	r7, #1
 1694 00b6 BFE7     		b	.L112
 1695              	.LVL145:
 1696              	.L111:
 844:LWIP/Target/ethernetif.c ****     {
 1697              		.loc 1 844 5 is_stmt 1 view .LVU451
 844:LWIP/Target/ethernetif.c ****     {
 1698              		.loc 1 844 7 is_stmt 0 view .LVU452
 1699 00b8 002F     		cmp	r7, #0
 1700 00ba D2D0     		beq	.L110
 1701 00bc BCE7     		b	.L112
 1702              	.L118:
 836:LWIP/Target/ethernetif.c ****       speed = ETH_SPEED_10M;
 1703              		.loc 1 836 14 view .LVU453
 1704 00be 0025     		movs	r5, #0
 1705              	.LVL146:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 53


 837:LWIP/Target/ethernetif.c ****       linkchanged = 1;
 1706              		.loc 1 837 13 view .LVU454
 1707 00c0 2E46     		mov	r6, r5
 1708              	.LVL147:
 838:LWIP/Target/ethernetif.c ****       break;
 1709              		.loc 1 838 19 view .LVU455
 1710 00c2 0127     		movs	r7, #1
 1711              	.LVL148:
 838:LWIP/Target/ethernetif.c ****       break;
 1712              		.loc 1 838 19 view .LVU456
 1713 00c4 B8E7     		b	.L112
 1714              	.L122:
 1715 00c6 00BF     		.align	2
 1716              	.L121:
 1717 00c8 00000000 		.word	heth
 1718 00cc 00000000 		.word	LAN8742
 1719              		.cfi_endproc
 1720              	.LFE191:
 1722              		.section	.text.HAL_ETH_RxAllocateCallback,"ax",%progbits
 1723              		.align	1
 1724              		.global	HAL_ETH_RxAllocateCallback
 1725              		.syntax unified
 1726              		.thumb
 1727              		.thumb_func
 1729              	HAL_ETH_RxAllocateCallback:
 1730              	.LVL149:
 1731              	.LFB192:
 862:LWIP/Target/ethernetif.c ****   }
 863:LWIP/Target/ethernetif.c **** }
 864:LWIP/Target/ethernetif.c **** 
 865:LWIP/Target/ethernetif.c **** void HAL_ETH_RxAllocateCallback(uint8_t **buff)
 866:LWIP/Target/ethernetif.c **** {
 1732              		.loc 1 866 1 is_stmt 1 view -0
 1733              		.cfi_startproc
 1734              		@ args = 0, pretend = 0, frame = 0
 1735              		@ frame_needed = 0, uses_anonymous_args = 0
 1736              		.loc 1 866 1 is_stmt 0 view .LVU458
 1737 0000 10B5     		push	{r4, lr}
 1738              	.LCFI28:
 1739              		.cfi_def_cfa_offset 8
 1740              		.cfi_offset 4, -8
 1741              		.cfi_offset 14, -4
 1742 0002 82B0     		sub	sp, sp, #8
 1743              	.LCFI29:
 1744              		.cfi_def_cfa_offset 16
 1745 0004 0446     		mov	r4, r0
 867:LWIP/Target/ethernetif.c **** /* USER CODE BEGIN HAL ETH RxAllocateCallback */
 868:LWIP/Target/ethernetif.c ****   struct pbuf_custom *p = LWIP_MEMPOOL_ALLOC(RX_POOL);
 1746              		.loc 1 868 3 is_stmt 1 view .LVU459
 1747              		.loc 1 868 27 is_stmt 0 view .LVU460
 1748 0006 0E48     		ldr	r0, .L127
 1749              	.LVL150:
 1750              		.loc 1 868 27 view .LVU461
 1751 0008 FFF7FEFF 		bl	memp_malloc_pool
 1752              	.LVL151:
 869:LWIP/Target/ethernetif.c ****   if (p)
 1753              		.loc 1 869 3 is_stmt 1 view .LVU462
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 54


 1754              		.loc 1 869 6 is_stmt 0 view .LVU463
 1755 000c 88B1     		cbz	r0, .L124
 1756 000e 0346     		mov	r3, r0
 870:LWIP/Target/ethernetif.c ****   {
 871:LWIP/Target/ethernetif.c ****     /* Get the buff from the struct pbuf address. */
 872:LWIP/Target/ethernetif.c ****     *buff = (uint8_t *)p + offsetof(RxBuff_t, buff);
 1757              		.loc 1 872 5 is_stmt 1 view .LVU464
 1758              		.loc 1 872 26 is_stmt 0 view .LVU465
 1759 0010 00F12002 		add	r2, r0, #32
 1760              		.loc 1 872 11 view .LVU466
 1761 0014 2260     		str	r2, [r4]
 873:LWIP/Target/ethernetif.c ****     p->custom_free_function = pbuf_free_custom;
 1762              		.loc 1 873 5 is_stmt 1 view .LVU467
 1763              		.loc 1 873 29 is_stmt 0 view .LVU468
 1764 0016 0B4A     		ldr	r2, .L127+4
 1765 0018 0261     		str	r2, [r0, #16]
 874:LWIP/Target/ethernetif.c ****     /* Initialize the struct pbuf.
 875:LWIP/Target/ethernetif.c ****     * This must be performed whenever a buffer's allocated because it may be
 876:LWIP/Target/ethernetif.c ****     * changed by lwIP or the app, e.g., pbuf_free decrements ref. */
 877:LWIP/Target/ethernetif.c ****     pbuf_alloced_custom(PBUF_RAW, 0, PBUF_REF, p, *buff, ETH_RX_BUFFER_SIZE);
 1766              		.loc 1 877 5 is_stmt 1 view .LVU469
 1767 001a 4FF4C062 		mov	r2, #1536
 1768 001e 0192     		str	r2, [sp, #4]
 1769 0020 2268     		ldr	r2, [r4]
 1770 0022 0092     		str	r2, [sp]
 1771 0024 4122     		movs	r2, #65
 1772 0026 0021     		movs	r1, #0
 1773 0028 0846     		mov	r0, r1
 1774              	.LVL152:
 1775              		.loc 1 877 5 is_stmt 0 view .LVU470
 1776 002a FFF7FEFF 		bl	pbuf_alloced_custom
 1777              	.LVL153:
 1778              	.L123:
 878:LWIP/Target/ethernetif.c ****   }
 879:LWIP/Target/ethernetif.c ****   else
 880:LWIP/Target/ethernetif.c ****   {
 881:LWIP/Target/ethernetif.c ****     RxAllocStatus = RX_ALLOC_ERROR;
 882:LWIP/Target/ethernetif.c ****     *buff = NULL;
 883:LWIP/Target/ethernetif.c ****   }
 884:LWIP/Target/ethernetif.c **** /* USER CODE END HAL ETH RxAllocateCallback */
 885:LWIP/Target/ethernetif.c **** }
 1779              		.loc 1 885 1 view .LVU471
 1780 002e 02B0     		add	sp, sp, #8
 1781              	.LCFI30:
 1782              		.cfi_remember_state
 1783              		.cfi_def_cfa_offset 8
 1784              		@ sp needed
 1785 0030 10BD     		pop	{r4, pc}
 1786              	.LVL154:
 1787              	.L124:
 1788              	.LCFI31:
 1789              		.cfi_restore_state
 881:LWIP/Target/ethernetif.c ****     *buff = NULL;
 1790              		.loc 1 881 5 is_stmt 1 view .LVU472
 881:LWIP/Target/ethernetif.c ****     *buff = NULL;
 1791              		.loc 1 881 19 is_stmt 0 view .LVU473
 1792 0032 054B     		ldr	r3, .L127+8
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 55


 1793 0034 0122     		movs	r2, #1
 1794 0036 1A70     		strb	r2, [r3]
 882:LWIP/Target/ethernetif.c ****   }
 1795              		.loc 1 882 5 is_stmt 1 view .LVU474
 882:LWIP/Target/ethernetif.c ****   }
 1796              		.loc 1 882 11 is_stmt 0 view .LVU475
 1797 0038 0023     		movs	r3, #0
 1798 003a 2360     		str	r3, [r4]
 1799              		.loc 1 885 1 view .LVU476
 1800 003c F7E7     		b	.L123
 1801              	.L128:
 1802 003e 00BF     		.align	2
 1803              	.L127:
 1804 0040 00000000 		.word	memp_RX_POOL
 1805 0044 00000000 		.word	pbuf_free_custom
 1806 0048 00000000 		.word	RxAllocStatus
 1807              		.cfi_endproc
 1808              	.LFE192:
 1810              		.section	.text.HAL_ETH_RxLinkCallback,"ax",%progbits
 1811              		.align	1
 1812              		.global	HAL_ETH_RxLinkCallback
 1813              		.syntax unified
 1814              		.thumb
 1815              		.thumb_func
 1817              	HAL_ETH_RxLinkCallback:
 1818              	.LVL155:
 1819              	.LFB193:
 886:LWIP/Target/ethernetif.c **** 
 887:LWIP/Target/ethernetif.c **** void HAL_ETH_RxLinkCallback(void **pStart, void **pEnd, uint8_t *buff, uint16_t Length)
 888:LWIP/Target/ethernetif.c **** {
 1820              		.loc 1 888 1 is_stmt 1 view -0
 1821              		.cfi_startproc
 1822              		@ args = 0, pretend = 0, frame = 0
 1823              		@ frame_needed = 0, uses_anonymous_args = 0
 1824              		@ link register save eliminated.
 1825              		.loc 1 888 1 is_stmt 0 view .LVU478
 1826 0000 30B4     		push	{r4, r5}
 1827              	.LCFI32:
 1828              		.cfi_def_cfa_offset 8
 1829              		.cfi_offset 4, -8
 1830              		.cfi_offset 5, -4
 889:LWIP/Target/ethernetif.c **** /* USER CODE BEGIN HAL ETH RxLinkCallback */
 890:LWIP/Target/ethernetif.c **** 
 891:LWIP/Target/ethernetif.c ****   struct pbuf **ppStart = (struct pbuf **)pStart;
 1831              		.loc 1 891 3 is_stmt 1 view .LVU479
 1832              	.LVL156:
 892:LWIP/Target/ethernetif.c ****   struct pbuf **ppEnd = (struct pbuf **)pEnd;
 1833              		.loc 1 892 3 view .LVU480
 893:LWIP/Target/ethernetif.c ****   struct pbuf *p = NULL;
 1834              		.loc 1 893 3 view .LVU481
 894:LWIP/Target/ethernetif.c **** 
 895:LWIP/Target/ethernetif.c ****   /* Get the struct pbuf from the buff address. */
 896:LWIP/Target/ethernetif.c ****   p = (struct pbuf *)(buff - offsetof(RxBuff_t, buff));
 1835              		.loc 1 896 3 view .LVU482
 1836              		.loc 1 896 5 is_stmt 0 view .LVU483
 1837 0002 A2F12004 		sub	r4, r2, #32
 1838              	.LVL157:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 56


 897:LWIP/Target/ethernetif.c ****   p->next = NULL;
 1839              		.loc 1 897 3 is_stmt 1 view .LVU484
 1840              		.loc 1 897 11 is_stmt 0 view .LVU485
 1841 0006 0025     		movs	r5, #0
 1842 0008 42F8205C 		str	r5, [r2, #-32]
 898:LWIP/Target/ethernetif.c ****   p->tot_len = 0;
 1843              		.loc 1 898 3 is_stmt 1 view .LVU486
 1844              		.loc 1 898 14 is_stmt 0 view .LVU487
 1845 000c 22F8185C 		strh	r5, [r2, #-24]	@ movhi
 899:LWIP/Target/ethernetif.c ****   p->len = Length;
 1846              		.loc 1 899 3 is_stmt 1 view .LVU488
 1847              		.loc 1 899 10 is_stmt 0 view .LVU489
 1848 0010 22F8163C 		strh	r3, [r2, #-22]	@ movhi
 900:LWIP/Target/ethernetif.c **** 
 901:LWIP/Target/ethernetif.c ****   /* Chain the buffer. */
 902:LWIP/Target/ethernetif.c ****   if (!*ppStart)
 1849              		.loc 1 902 3 is_stmt 1 view .LVU490
 1850              		.loc 1 902 8 is_stmt 0 view .LVU491
 1851 0014 0568     		ldr	r5, [r0]
 1852              		.loc 1 902 6 view .LVU492
 1853 0016 25B1     		cbz	r5, .L137
 903:LWIP/Target/ethernetif.c ****   {
 904:LWIP/Target/ethernetif.c ****     /* The first buffer of the packet. */
 905:LWIP/Target/ethernetif.c ****     *ppStart = p;
 906:LWIP/Target/ethernetif.c ****   }
 907:LWIP/Target/ethernetif.c ****   else
 908:LWIP/Target/ethernetif.c ****   {
 909:LWIP/Target/ethernetif.c ****     /* Chain the buffer to the end of the packet. */
 910:LWIP/Target/ethernetif.c ****     (*ppEnd)->next = p;
 1854              		.loc 1 910 5 is_stmt 1 view .LVU493
 1855              		.loc 1 910 6 is_stmt 0 view .LVU494
 1856 0018 0D68     		ldr	r5, [r1]
 1857              		.loc 1 910 20 view .LVU495
 1858 001a 2C60     		str	r4, [r5]
 1859              	.L131:
 911:LWIP/Target/ethernetif.c ****   }
 912:LWIP/Target/ethernetif.c ****   *ppEnd  = p;
 1860              		.loc 1 912 3 is_stmt 1 view .LVU496
 1861              		.loc 1 912 11 is_stmt 0 view .LVU497
 1862 001c 0C60     		str	r4, [r1]
 913:LWIP/Target/ethernetif.c **** 
 914:LWIP/Target/ethernetif.c ****   /* Update the total length of all the buffers of the chain. Each pbuf in the chain should have it
 915:LWIP/Target/ethernetif.c ****    * set to its own length, plus the length of all the following pbufs in the chain. */
 916:LWIP/Target/ethernetif.c ****   for (p = *ppStart; p != NULL; p = p->next)
 1863              		.loc 1 916 3 is_stmt 1 view .LVU498
 1864              		.loc 1 916 10 is_stmt 0 view .LVU499
 1865 001e 0168     		ldr	r1, [r0]
 1866              	.LVL158:
 1867              		.loc 1 916 3 view .LVU500
 1868 0020 05E0     		b	.L132
 1869              	.LVL159:
 1870              	.L137:
 905:LWIP/Target/ethernetif.c ****   }
 1871              		.loc 1 905 5 is_stmt 1 view .LVU501
 905:LWIP/Target/ethernetif.c ****   }
 1872              		.loc 1 905 14 is_stmt 0 view .LVU502
 1873 0022 0460     		str	r4, [r0]
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 57


 1874 0024 FAE7     		b	.L131
 1875              	.LVL160:
 1876              	.L133:
 917:LWIP/Target/ethernetif.c ****   {
 918:LWIP/Target/ethernetif.c ****     p->tot_len += Length;
 1877              		.loc 1 918 5 is_stmt 1 view .LVU503
 1878              		.loc 1 918 6 is_stmt 0 view .LVU504
 1879 0026 0889     		ldrh	r0, [r1, #8]
 1880              		.loc 1 918 16 view .LVU505
 1881 0028 1844     		add	r0, r0, r3
 1882 002a 0881     		strh	r0, [r1, #8]	@ movhi
 916:LWIP/Target/ethernetif.c ****   {
 1883              		.loc 1 916 35 is_stmt 1 discriminator 3 view .LVU506
 1884 002c 0968     		ldr	r1, [r1]
 1885              	.LVL161:
 1886              	.L132:
 916:LWIP/Target/ethernetif.c ****   {
 1887              		.loc 1 916 24 discriminator 1 view .LVU507
 1888 002e 0029     		cmp	r1, #0
 1889 0030 F9D1     		bne	.L133
 919:LWIP/Target/ethernetif.c ****   }
 920:LWIP/Target/ethernetif.c **** 
 921:LWIP/Target/ethernetif.c ****   /* Invalidate data cache because Rx DMA's writing to physical memory makes it stale. */
 922:LWIP/Target/ethernetif.c ****   SCB_InvalidateDCache_by_Addr((uint32_t *)buff, Length);
 1890              		.loc 1 922 3 view .LVU508
 1891              	.LVL162:
 1892              	.LBB24:
 1893              	.LBI24:
 1894              		.file 2 "Drivers/CMSIS/Include/core_cm7.h"
   1:Drivers/CMSIS/Include/core_cm7.h **** /**************************************************************************//**
   2:Drivers/CMSIS/Include/core_cm7.h ****  * @file     core_cm7.h
   3:Drivers/CMSIS/Include/core_cm7.h ****  * @brief    CMSIS Cortex-M7 Core Peripheral Access Layer Header File
   4:Drivers/CMSIS/Include/core_cm7.h ****  * @version  V5.1.1
   5:Drivers/CMSIS/Include/core_cm7.h ****  * @date     28. March 2019
   6:Drivers/CMSIS/Include/core_cm7.h ****  ******************************************************************************/
   7:Drivers/CMSIS/Include/core_cm7.h **** /*
   8:Drivers/CMSIS/Include/core_cm7.h ****  * Copyright (c) 2009-2019 Arm Limited. All rights reserved.
   9:Drivers/CMSIS/Include/core_cm7.h ****  *
  10:Drivers/CMSIS/Include/core_cm7.h ****  * SPDX-License-Identifier: Apache-2.0
  11:Drivers/CMSIS/Include/core_cm7.h ****  *
  12:Drivers/CMSIS/Include/core_cm7.h ****  * Licensed under the Apache License, Version 2.0 (the License); you may
  13:Drivers/CMSIS/Include/core_cm7.h ****  * not use this file except in compliance with the License.
  14:Drivers/CMSIS/Include/core_cm7.h ****  * You may obtain a copy of the License at
  15:Drivers/CMSIS/Include/core_cm7.h ****  *
  16:Drivers/CMSIS/Include/core_cm7.h ****  * www.apache.org/licenses/LICENSE-2.0
  17:Drivers/CMSIS/Include/core_cm7.h ****  *
  18:Drivers/CMSIS/Include/core_cm7.h ****  * Unless required by applicable law or agreed to in writing, software
  19:Drivers/CMSIS/Include/core_cm7.h ****  * distributed under the License is distributed on an AS IS BASIS, WITHOUT
  20:Drivers/CMSIS/Include/core_cm7.h ****  * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  21:Drivers/CMSIS/Include/core_cm7.h ****  * See the License for the specific language governing permissions and
  22:Drivers/CMSIS/Include/core_cm7.h ****  * limitations under the License.
  23:Drivers/CMSIS/Include/core_cm7.h ****  */
  24:Drivers/CMSIS/Include/core_cm7.h **** 
  25:Drivers/CMSIS/Include/core_cm7.h **** #if   defined ( __ICCARM__ )
  26:Drivers/CMSIS/Include/core_cm7.h ****   #pragma system_include         /* treat file as system include file for MISRA check */
  27:Drivers/CMSIS/Include/core_cm7.h **** #elif defined (__clang__)
  28:Drivers/CMSIS/Include/core_cm7.h ****   #pragma clang system_header   /* treat file as system include file */
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 58


  29:Drivers/CMSIS/Include/core_cm7.h **** #endif
  30:Drivers/CMSIS/Include/core_cm7.h **** 
  31:Drivers/CMSIS/Include/core_cm7.h **** #ifndef __CORE_CM7_H_GENERIC
  32:Drivers/CMSIS/Include/core_cm7.h **** #define __CORE_CM7_H_GENERIC
  33:Drivers/CMSIS/Include/core_cm7.h **** 
  34:Drivers/CMSIS/Include/core_cm7.h **** #include <stdint.h>
  35:Drivers/CMSIS/Include/core_cm7.h **** 
  36:Drivers/CMSIS/Include/core_cm7.h **** #ifdef __cplusplus
  37:Drivers/CMSIS/Include/core_cm7.h ****  extern "C" {
  38:Drivers/CMSIS/Include/core_cm7.h **** #endif
  39:Drivers/CMSIS/Include/core_cm7.h **** 
  40:Drivers/CMSIS/Include/core_cm7.h **** /**
  41:Drivers/CMSIS/Include/core_cm7.h ****   \page CMSIS_MISRA_Exceptions  MISRA-C:2004 Compliance Exceptions
  42:Drivers/CMSIS/Include/core_cm7.h ****   CMSIS violates the following MISRA-C:2004 rules:
  43:Drivers/CMSIS/Include/core_cm7.h **** 
  44:Drivers/CMSIS/Include/core_cm7.h ****    \li Required Rule 8.5, object/function definition in header file.<br>
  45:Drivers/CMSIS/Include/core_cm7.h ****      Function definitions in header files are used to allow 'inlining'.
  46:Drivers/CMSIS/Include/core_cm7.h **** 
  47:Drivers/CMSIS/Include/core_cm7.h ****    \li Required Rule 18.4, declaration of union type or object of union type: '{...}'.<br>
  48:Drivers/CMSIS/Include/core_cm7.h ****      Unions are used for effective representation of core registers.
  49:Drivers/CMSIS/Include/core_cm7.h **** 
  50:Drivers/CMSIS/Include/core_cm7.h ****    \li Advisory Rule 19.7, Function-like macro defined.<br>
  51:Drivers/CMSIS/Include/core_cm7.h ****      Function-like macros are used to allow more efficient code.
  52:Drivers/CMSIS/Include/core_cm7.h ****  */
  53:Drivers/CMSIS/Include/core_cm7.h **** 
  54:Drivers/CMSIS/Include/core_cm7.h **** 
  55:Drivers/CMSIS/Include/core_cm7.h **** /*******************************************************************************
  56:Drivers/CMSIS/Include/core_cm7.h ****  *                 CMSIS definitions
  57:Drivers/CMSIS/Include/core_cm7.h ****  ******************************************************************************/
  58:Drivers/CMSIS/Include/core_cm7.h **** /**
  59:Drivers/CMSIS/Include/core_cm7.h ****   \ingroup Cortex_M7
  60:Drivers/CMSIS/Include/core_cm7.h ****   @{
  61:Drivers/CMSIS/Include/core_cm7.h ****  */
  62:Drivers/CMSIS/Include/core_cm7.h **** 
  63:Drivers/CMSIS/Include/core_cm7.h **** #include "cmsis_version.h"
  64:Drivers/CMSIS/Include/core_cm7.h **** 
  65:Drivers/CMSIS/Include/core_cm7.h **** /* CMSIS CM7 definitions */
  66:Drivers/CMSIS/Include/core_cm7.h **** #define __CM7_CMSIS_VERSION_MAIN  (__CM_CMSIS_VERSION_MAIN)                  /*!< \deprecated [31:1
  67:Drivers/CMSIS/Include/core_cm7.h **** #define __CM7_CMSIS_VERSION_SUB   ( __CM_CMSIS_VERSION_SUB)                  /*!< \deprecated [15:0
  68:Drivers/CMSIS/Include/core_cm7.h **** #define __CM7_CMSIS_VERSION       ((__CM7_CMSIS_VERSION_MAIN << 16U) | \
  69:Drivers/CMSIS/Include/core_cm7.h ****                                     __CM7_CMSIS_VERSION_SUB           )      /*!< \deprecated CMSIS
  70:Drivers/CMSIS/Include/core_cm7.h **** 
  71:Drivers/CMSIS/Include/core_cm7.h **** #define __CORTEX_M                (7U)                                       /*!< Cortex-M Core */
  72:Drivers/CMSIS/Include/core_cm7.h **** 
  73:Drivers/CMSIS/Include/core_cm7.h **** /** __FPU_USED indicates whether an FPU is used or not.
  74:Drivers/CMSIS/Include/core_cm7.h ****     For this, __FPU_PRESENT has to be checked prior to making use of FPU specific registers and fun
  75:Drivers/CMSIS/Include/core_cm7.h **** */
  76:Drivers/CMSIS/Include/core_cm7.h **** #if defined ( __CC_ARM )
  77:Drivers/CMSIS/Include/core_cm7.h ****   #if defined __TARGET_FPU_VFP
  78:Drivers/CMSIS/Include/core_cm7.h ****     #if defined (__FPU_PRESENT) && (__FPU_PRESENT == 1U)
  79:Drivers/CMSIS/Include/core_cm7.h ****       #define __FPU_USED       1U
  80:Drivers/CMSIS/Include/core_cm7.h ****     #else
  81:Drivers/CMSIS/Include/core_cm7.h ****       #error "Compiler generates FPU instructions for a device without an FPU (check __FPU_PRESENT)
  82:Drivers/CMSIS/Include/core_cm7.h ****       #define __FPU_USED       0U
  83:Drivers/CMSIS/Include/core_cm7.h ****     #endif
  84:Drivers/CMSIS/Include/core_cm7.h ****   #else
  85:Drivers/CMSIS/Include/core_cm7.h ****     #define __FPU_USED         0U
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 59


  86:Drivers/CMSIS/Include/core_cm7.h ****   #endif
  87:Drivers/CMSIS/Include/core_cm7.h **** 
  88:Drivers/CMSIS/Include/core_cm7.h **** #elif defined (__ARMCC_VERSION) && (__ARMCC_VERSION >= 6010050)
  89:Drivers/CMSIS/Include/core_cm7.h ****   #if defined __ARM_FP
  90:Drivers/CMSIS/Include/core_cm7.h ****     #if defined (__FPU_PRESENT) && (__FPU_PRESENT == 1U)
  91:Drivers/CMSIS/Include/core_cm7.h ****       #define __FPU_USED       1U
  92:Drivers/CMSIS/Include/core_cm7.h ****     #else
  93:Drivers/CMSIS/Include/core_cm7.h ****       #warning "Compiler generates FPU instructions for a device without an FPU (check __FPU_PRESEN
  94:Drivers/CMSIS/Include/core_cm7.h ****       #define __FPU_USED       0U
  95:Drivers/CMSIS/Include/core_cm7.h ****     #endif
  96:Drivers/CMSIS/Include/core_cm7.h ****   #else
  97:Drivers/CMSIS/Include/core_cm7.h ****     #define __FPU_USED         0U
  98:Drivers/CMSIS/Include/core_cm7.h ****   #endif
  99:Drivers/CMSIS/Include/core_cm7.h **** 
 100:Drivers/CMSIS/Include/core_cm7.h **** #elif defined ( __GNUC__ )
 101:Drivers/CMSIS/Include/core_cm7.h ****   #if defined (__VFP_FP__) && !defined(__SOFTFP__)
 102:Drivers/CMSIS/Include/core_cm7.h ****     #if defined (__FPU_PRESENT) && (__FPU_PRESENT == 1U)
 103:Drivers/CMSIS/Include/core_cm7.h ****       #define __FPU_USED       1U
 104:Drivers/CMSIS/Include/core_cm7.h ****     #else
 105:Drivers/CMSIS/Include/core_cm7.h ****       #error "Compiler generates FPU instructions for a device without an FPU (check __FPU_PRESENT)
 106:Drivers/CMSIS/Include/core_cm7.h ****       #define __FPU_USED       0U
 107:Drivers/CMSIS/Include/core_cm7.h ****     #endif
 108:Drivers/CMSIS/Include/core_cm7.h ****   #else
 109:Drivers/CMSIS/Include/core_cm7.h ****     #define __FPU_USED         0U
 110:Drivers/CMSIS/Include/core_cm7.h ****   #endif
 111:Drivers/CMSIS/Include/core_cm7.h **** 
 112:Drivers/CMSIS/Include/core_cm7.h **** #elif defined ( __ICCARM__ )
 113:Drivers/CMSIS/Include/core_cm7.h ****   #if defined __ARMVFP__
 114:Drivers/CMSIS/Include/core_cm7.h ****     #if defined (__FPU_PRESENT) && (__FPU_PRESENT == 1U)
 115:Drivers/CMSIS/Include/core_cm7.h ****       #define __FPU_USED       1U
 116:Drivers/CMSIS/Include/core_cm7.h ****     #else
 117:Drivers/CMSIS/Include/core_cm7.h ****       #error "Compiler generates FPU instructions for a device without an FPU (check __FPU_PRESENT)
 118:Drivers/CMSIS/Include/core_cm7.h ****       #define __FPU_USED       0U
 119:Drivers/CMSIS/Include/core_cm7.h ****     #endif
 120:Drivers/CMSIS/Include/core_cm7.h ****   #else
 121:Drivers/CMSIS/Include/core_cm7.h ****     #define __FPU_USED         0U
 122:Drivers/CMSIS/Include/core_cm7.h ****   #endif
 123:Drivers/CMSIS/Include/core_cm7.h **** 
 124:Drivers/CMSIS/Include/core_cm7.h **** #elif defined ( __TI_ARM__ )
 125:Drivers/CMSIS/Include/core_cm7.h ****   #if defined __TI_VFP_SUPPORT__
 126:Drivers/CMSIS/Include/core_cm7.h ****     #if defined (__FPU_PRESENT) && (__FPU_PRESENT == 1U)
 127:Drivers/CMSIS/Include/core_cm7.h ****       #define __FPU_USED       1U
 128:Drivers/CMSIS/Include/core_cm7.h ****     #else
 129:Drivers/CMSIS/Include/core_cm7.h ****       #error "Compiler generates FPU instructions for a device without an FPU (check __FPU_PRESENT)
 130:Drivers/CMSIS/Include/core_cm7.h ****       #define __FPU_USED       0U
 131:Drivers/CMSIS/Include/core_cm7.h ****     #endif
 132:Drivers/CMSIS/Include/core_cm7.h ****   #else
 133:Drivers/CMSIS/Include/core_cm7.h ****     #define __FPU_USED         0U
 134:Drivers/CMSIS/Include/core_cm7.h ****   #endif
 135:Drivers/CMSIS/Include/core_cm7.h **** 
 136:Drivers/CMSIS/Include/core_cm7.h **** #elif defined ( __TASKING__ )
 137:Drivers/CMSIS/Include/core_cm7.h ****   #if defined __FPU_VFP__
 138:Drivers/CMSIS/Include/core_cm7.h ****     #if defined (__FPU_PRESENT) && (__FPU_PRESENT == 1U)
 139:Drivers/CMSIS/Include/core_cm7.h ****       #define __FPU_USED       1U
 140:Drivers/CMSIS/Include/core_cm7.h ****     #else
 141:Drivers/CMSIS/Include/core_cm7.h ****       #error "Compiler generates FPU instructions for a device without an FPU (check __FPU_PRESENT)
 142:Drivers/CMSIS/Include/core_cm7.h ****       #define __FPU_USED       0U
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 60


 143:Drivers/CMSIS/Include/core_cm7.h ****     #endif
 144:Drivers/CMSIS/Include/core_cm7.h ****   #else
 145:Drivers/CMSIS/Include/core_cm7.h ****     #define __FPU_USED         0U
 146:Drivers/CMSIS/Include/core_cm7.h ****   #endif
 147:Drivers/CMSIS/Include/core_cm7.h **** 
 148:Drivers/CMSIS/Include/core_cm7.h **** #elif defined ( __CSMC__ )
 149:Drivers/CMSIS/Include/core_cm7.h ****   #if ( __CSMC__ & 0x400U)
 150:Drivers/CMSIS/Include/core_cm7.h ****     #if defined (__FPU_PRESENT) && (__FPU_PRESENT == 1U)
 151:Drivers/CMSIS/Include/core_cm7.h ****       #define __FPU_USED       1U
 152:Drivers/CMSIS/Include/core_cm7.h ****     #else
 153:Drivers/CMSIS/Include/core_cm7.h ****       #error "Compiler generates FPU instructions for a device without an FPU (check __FPU_PRESENT)
 154:Drivers/CMSIS/Include/core_cm7.h ****       #define __FPU_USED       0U
 155:Drivers/CMSIS/Include/core_cm7.h ****     #endif
 156:Drivers/CMSIS/Include/core_cm7.h ****   #else
 157:Drivers/CMSIS/Include/core_cm7.h ****     #define __FPU_USED         0U
 158:Drivers/CMSIS/Include/core_cm7.h ****   #endif
 159:Drivers/CMSIS/Include/core_cm7.h **** 
 160:Drivers/CMSIS/Include/core_cm7.h **** #endif
 161:Drivers/CMSIS/Include/core_cm7.h **** 
 162:Drivers/CMSIS/Include/core_cm7.h **** #include "cmsis_compiler.h"               /* CMSIS compiler specific defines */
 163:Drivers/CMSIS/Include/core_cm7.h **** 
 164:Drivers/CMSIS/Include/core_cm7.h **** 
 165:Drivers/CMSIS/Include/core_cm7.h **** #ifdef __cplusplus
 166:Drivers/CMSIS/Include/core_cm7.h **** }
 167:Drivers/CMSIS/Include/core_cm7.h **** #endif
 168:Drivers/CMSIS/Include/core_cm7.h **** 
 169:Drivers/CMSIS/Include/core_cm7.h **** #endif /* __CORE_CM7_H_GENERIC */
 170:Drivers/CMSIS/Include/core_cm7.h **** 
 171:Drivers/CMSIS/Include/core_cm7.h **** #ifndef __CMSIS_GENERIC
 172:Drivers/CMSIS/Include/core_cm7.h **** 
 173:Drivers/CMSIS/Include/core_cm7.h **** #ifndef __CORE_CM7_H_DEPENDANT
 174:Drivers/CMSIS/Include/core_cm7.h **** #define __CORE_CM7_H_DEPENDANT
 175:Drivers/CMSIS/Include/core_cm7.h **** 
 176:Drivers/CMSIS/Include/core_cm7.h **** #ifdef __cplusplus
 177:Drivers/CMSIS/Include/core_cm7.h ****  extern "C" {
 178:Drivers/CMSIS/Include/core_cm7.h **** #endif
 179:Drivers/CMSIS/Include/core_cm7.h **** 
 180:Drivers/CMSIS/Include/core_cm7.h **** /* check device defines and use defaults */
 181:Drivers/CMSIS/Include/core_cm7.h **** #if defined __CHECK_DEVICE_DEFINES
 182:Drivers/CMSIS/Include/core_cm7.h ****   #ifndef __CM7_REV
 183:Drivers/CMSIS/Include/core_cm7.h ****     #define __CM7_REV               0x0000U
 184:Drivers/CMSIS/Include/core_cm7.h ****     #warning "__CM7_REV not defined in device header file; using default!"
 185:Drivers/CMSIS/Include/core_cm7.h ****   #endif
 186:Drivers/CMSIS/Include/core_cm7.h **** 
 187:Drivers/CMSIS/Include/core_cm7.h ****   #ifndef __FPU_PRESENT
 188:Drivers/CMSIS/Include/core_cm7.h ****     #define __FPU_PRESENT             0U
 189:Drivers/CMSIS/Include/core_cm7.h ****     #warning "__FPU_PRESENT not defined in device header file; using default!"
 190:Drivers/CMSIS/Include/core_cm7.h ****   #endif
 191:Drivers/CMSIS/Include/core_cm7.h **** 
 192:Drivers/CMSIS/Include/core_cm7.h ****   #ifndef __MPU_PRESENT
 193:Drivers/CMSIS/Include/core_cm7.h ****     #define __MPU_PRESENT             0U
 194:Drivers/CMSIS/Include/core_cm7.h ****     #warning "__MPU_PRESENT not defined in device header file; using default!"
 195:Drivers/CMSIS/Include/core_cm7.h ****   #endif
 196:Drivers/CMSIS/Include/core_cm7.h **** 
 197:Drivers/CMSIS/Include/core_cm7.h ****   #ifndef __ICACHE_PRESENT
 198:Drivers/CMSIS/Include/core_cm7.h ****     #define __ICACHE_PRESENT          0U
 199:Drivers/CMSIS/Include/core_cm7.h ****     #warning "__ICACHE_PRESENT not defined in device header file; using default!"
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 61


 200:Drivers/CMSIS/Include/core_cm7.h ****   #endif
 201:Drivers/CMSIS/Include/core_cm7.h **** 
 202:Drivers/CMSIS/Include/core_cm7.h ****   #ifndef __DCACHE_PRESENT
 203:Drivers/CMSIS/Include/core_cm7.h ****     #define __DCACHE_PRESENT          0U
 204:Drivers/CMSIS/Include/core_cm7.h ****     #warning "__DCACHE_PRESENT not defined in device header file; using default!"
 205:Drivers/CMSIS/Include/core_cm7.h ****   #endif
 206:Drivers/CMSIS/Include/core_cm7.h **** 
 207:Drivers/CMSIS/Include/core_cm7.h ****   #ifndef __DTCM_PRESENT
 208:Drivers/CMSIS/Include/core_cm7.h ****     #define __DTCM_PRESENT            0U
 209:Drivers/CMSIS/Include/core_cm7.h ****     #warning "__DTCM_PRESENT        not defined in device header file; using default!"
 210:Drivers/CMSIS/Include/core_cm7.h ****   #endif
 211:Drivers/CMSIS/Include/core_cm7.h **** 
 212:Drivers/CMSIS/Include/core_cm7.h ****   #ifndef __NVIC_PRIO_BITS
 213:Drivers/CMSIS/Include/core_cm7.h ****     #define __NVIC_PRIO_BITS          3U
 214:Drivers/CMSIS/Include/core_cm7.h ****     #warning "__NVIC_PRIO_BITS not defined in device header file; using default!"
 215:Drivers/CMSIS/Include/core_cm7.h ****   #endif
 216:Drivers/CMSIS/Include/core_cm7.h **** 
 217:Drivers/CMSIS/Include/core_cm7.h ****   #ifndef __Vendor_SysTickConfig
 218:Drivers/CMSIS/Include/core_cm7.h ****     #define __Vendor_SysTickConfig    0U
 219:Drivers/CMSIS/Include/core_cm7.h ****     #warning "__Vendor_SysTickConfig not defined in device header file; using default!"
 220:Drivers/CMSIS/Include/core_cm7.h ****   #endif
 221:Drivers/CMSIS/Include/core_cm7.h **** #endif
 222:Drivers/CMSIS/Include/core_cm7.h **** 
 223:Drivers/CMSIS/Include/core_cm7.h **** /* IO definitions (access restrictions to peripheral registers) */
 224:Drivers/CMSIS/Include/core_cm7.h **** /**
 225:Drivers/CMSIS/Include/core_cm7.h ****     \defgroup CMSIS_glob_defs CMSIS Global Defines
 226:Drivers/CMSIS/Include/core_cm7.h **** 
 227:Drivers/CMSIS/Include/core_cm7.h ****     <strong>IO Type Qualifiers</strong> are used
 228:Drivers/CMSIS/Include/core_cm7.h ****     \li to specify the access to peripheral variables.
 229:Drivers/CMSIS/Include/core_cm7.h ****     \li for automatic generation of peripheral register debug information.
 230:Drivers/CMSIS/Include/core_cm7.h **** */
 231:Drivers/CMSIS/Include/core_cm7.h **** #ifdef __cplusplus
 232:Drivers/CMSIS/Include/core_cm7.h ****   #define   __I     volatile             /*!< Defines 'read only' permissions */
 233:Drivers/CMSIS/Include/core_cm7.h **** #else
 234:Drivers/CMSIS/Include/core_cm7.h ****   #define   __I     volatile const       /*!< Defines 'read only' permissions */
 235:Drivers/CMSIS/Include/core_cm7.h **** #endif
 236:Drivers/CMSIS/Include/core_cm7.h **** #define     __O     volatile             /*!< Defines 'write only' permissions */
 237:Drivers/CMSIS/Include/core_cm7.h **** #define     __IO    volatile             /*!< Defines 'read / write' permissions */
 238:Drivers/CMSIS/Include/core_cm7.h **** 
 239:Drivers/CMSIS/Include/core_cm7.h **** /* following defines should be used for structure members */
 240:Drivers/CMSIS/Include/core_cm7.h **** #define     __IM     volatile const      /*! Defines 'read only' structure member permissions */
 241:Drivers/CMSIS/Include/core_cm7.h **** #define     __OM     volatile            /*! Defines 'write only' structure member permissions */
 242:Drivers/CMSIS/Include/core_cm7.h **** #define     __IOM    volatile            /*! Defines 'read / write' structure member permissions */
 243:Drivers/CMSIS/Include/core_cm7.h **** 
 244:Drivers/CMSIS/Include/core_cm7.h **** /*@} end of group Cortex_M7 */
 245:Drivers/CMSIS/Include/core_cm7.h **** 
 246:Drivers/CMSIS/Include/core_cm7.h **** 
 247:Drivers/CMSIS/Include/core_cm7.h **** 
 248:Drivers/CMSIS/Include/core_cm7.h **** /*******************************************************************************
 249:Drivers/CMSIS/Include/core_cm7.h ****  *                 Register Abstraction
 250:Drivers/CMSIS/Include/core_cm7.h ****   Core Register contain:
 251:Drivers/CMSIS/Include/core_cm7.h ****   - Core Register
 252:Drivers/CMSIS/Include/core_cm7.h ****   - Core NVIC Register
 253:Drivers/CMSIS/Include/core_cm7.h ****   - Core SCB Register
 254:Drivers/CMSIS/Include/core_cm7.h ****   - Core SysTick Register
 255:Drivers/CMSIS/Include/core_cm7.h ****   - Core Debug Register
 256:Drivers/CMSIS/Include/core_cm7.h ****   - Core MPU Register
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 62


 257:Drivers/CMSIS/Include/core_cm7.h ****   - Core FPU Register
 258:Drivers/CMSIS/Include/core_cm7.h ****  ******************************************************************************/
 259:Drivers/CMSIS/Include/core_cm7.h **** /**
 260:Drivers/CMSIS/Include/core_cm7.h ****   \defgroup CMSIS_core_register Defines and Type Definitions
 261:Drivers/CMSIS/Include/core_cm7.h ****   \brief Type definitions and defines for Cortex-M processor based devices.
 262:Drivers/CMSIS/Include/core_cm7.h **** */
 263:Drivers/CMSIS/Include/core_cm7.h **** 
 264:Drivers/CMSIS/Include/core_cm7.h **** /**
 265:Drivers/CMSIS/Include/core_cm7.h ****   \ingroup    CMSIS_core_register
 266:Drivers/CMSIS/Include/core_cm7.h ****   \defgroup   CMSIS_CORE  Status and Control Registers
 267:Drivers/CMSIS/Include/core_cm7.h ****   \brief      Core Register type definitions.
 268:Drivers/CMSIS/Include/core_cm7.h ****   @{
 269:Drivers/CMSIS/Include/core_cm7.h ****  */
 270:Drivers/CMSIS/Include/core_cm7.h **** 
 271:Drivers/CMSIS/Include/core_cm7.h **** /**
 272:Drivers/CMSIS/Include/core_cm7.h ****   \brief  Union type to access the Application Program Status Register (APSR).
 273:Drivers/CMSIS/Include/core_cm7.h ****  */
 274:Drivers/CMSIS/Include/core_cm7.h **** typedef union
 275:Drivers/CMSIS/Include/core_cm7.h **** {
 276:Drivers/CMSIS/Include/core_cm7.h ****   struct
 277:Drivers/CMSIS/Include/core_cm7.h ****   {
 278:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t _reserved0:16;              /*!< bit:  0..15  Reserved */
 279:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t GE:4;                       /*!< bit: 16..19  Greater than or Equal flags */
 280:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t _reserved1:7;               /*!< bit: 20..26  Reserved */
 281:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t Q:1;                        /*!< bit:     27  Saturation condition flag */
 282:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t V:1;                        /*!< bit:     28  Overflow condition code flag */
 283:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t C:1;                        /*!< bit:     29  Carry condition code flag */
 284:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t Z:1;                        /*!< bit:     30  Zero condition code flag */
 285:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t N:1;                        /*!< bit:     31  Negative condition code flag */
 286:Drivers/CMSIS/Include/core_cm7.h ****   } b;                                   /*!< Structure used for bit  access */
 287:Drivers/CMSIS/Include/core_cm7.h ****   uint32_t w;                            /*!< Type      used for word access */
 288:Drivers/CMSIS/Include/core_cm7.h **** } APSR_Type;
 289:Drivers/CMSIS/Include/core_cm7.h **** 
 290:Drivers/CMSIS/Include/core_cm7.h **** /* APSR Register Definitions */
 291:Drivers/CMSIS/Include/core_cm7.h **** #define APSR_N_Pos                         31U                                            /*!< APSR
 292:Drivers/CMSIS/Include/core_cm7.h **** #define APSR_N_Msk                         (1UL << APSR_N_Pos)                            /*!< APSR
 293:Drivers/CMSIS/Include/core_cm7.h **** 
 294:Drivers/CMSIS/Include/core_cm7.h **** #define APSR_Z_Pos                         30U                                            /*!< APSR
 295:Drivers/CMSIS/Include/core_cm7.h **** #define APSR_Z_Msk                         (1UL << APSR_Z_Pos)                            /*!< APSR
 296:Drivers/CMSIS/Include/core_cm7.h **** 
 297:Drivers/CMSIS/Include/core_cm7.h **** #define APSR_C_Pos                         29U                                            /*!< APSR
 298:Drivers/CMSIS/Include/core_cm7.h **** #define APSR_C_Msk                         (1UL << APSR_C_Pos)                            /*!< APSR
 299:Drivers/CMSIS/Include/core_cm7.h **** 
 300:Drivers/CMSIS/Include/core_cm7.h **** #define APSR_V_Pos                         28U                                            /*!< APSR
 301:Drivers/CMSIS/Include/core_cm7.h **** #define APSR_V_Msk                         (1UL << APSR_V_Pos)                            /*!< APSR
 302:Drivers/CMSIS/Include/core_cm7.h **** 
 303:Drivers/CMSIS/Include/core_cm7.h **** #define APSR_Q_Pos                         27U                                            /*!< APSR
 304:Drivers/CMSIS/Include/core_cm7.h **** #define APSR_Q_Msk                         (1UL << APSR_Q_Pos)                            /*!< APSR
 305:Drivers/CMSIS/Include/core_cm7.h **** 
 306:Drivers/CMSIS/Include/core_cm7.h **** #define APSR_GE_Pos                        16U                                            /*!< APSR
 307:Drivers/CMSIS/Include/core_cm7.h **** #define APSR_GE_Msk                        (0xFUL << APSR_GE_Pos)                         /*!< APSR
 308:Drivers/CMSIS/Include/core_cm7.h **** 
 309:Drivers/CMSIS/Include/core_cm7.h **** 
 310:Drivers/CMSIS/Include/core_cm7.h **** /**
 311:Drivers/CMSIS/Include/core_cm7.h ****   \brief  Union type to access the Interrupt Program Status Register (IPSR).
 312:Drivers/CMSIS/Include/core_cm7.h ****  */
 313:Drivers/CMSIS/Include/core_cm7.h **** typedef union
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 63


 314:Drivers/CMSIS/Include/core_cm7.h **** {
 315:Drivers/CMSIS/Include/core_cm7.h ****   struct
 316:Drivers/CMSIS/Include/core_cm7.h ****   {
 317:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t ISR:9;                      /*!< bit:  0.. 8  Exception number */
 318:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t _reserved0:23;              /*!< bit:  9..31  Reserved */
 319:Drivers/CMSIS/Include/core_cm7.h ****   } b;                                   /*!< Structure used for bit  access */
 320:Drivers/CMSIS/Include/core_cm7.h ****   uint32_t w;                            /*!< Type      used for word access */
 321:Drivers/CMSIS/Include/core_cm7.h **** } IPSR_Type;
 322:Drivers/CMSIS/Include/core_cm7.h **** 
 323:Drivers/CMSIS/Include/core_cm7.h **** /* IPSR Register Definitions */
 324:Drivers/CMSIS/Include/core_cm7.h **** #define IPSR_ISR_Pos                        0U                                            /*!< IPSR
 325:Drivers/CMSIS/Include/core_cm7.h **** #define IPSR_ISR_Msk                       (0x1FFUL /*<< IPSR_ISR_Pos*/)                  /*!< IPSR
 326:Drivers/CMSIS/Include/core_cm7.h **** 
 327:Drivers/CMSIS/Include/core_cm7.h **** 
 328:Drivers/CMSIS/Include/core_cm7.h **** /**
 329:Drivers/CMSIS/Include/core_cm7.h ****   \brief  Union type to access the Special-Purpose Program Status Registers (xPSR).
 330:Drivers/CMSIS/Include/core_cm7.h ****  */
 331:Drivers/CMSIS/Include/core_cm7.h **** typedef union
 332:Drivers/CMSIS/Include/core_cm7.h **** {
 333:Drivers/CMSIS/Include/core_cm7.h ****   struct
 334:Drivers/CMSIS/Include/core_cm7.h ****   {
 335:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t ISR:9;                      /*!< bit:  0.. 8  Exception number */
 336:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t _reserved0:1;               /*!< bit:      9  Reserved */
 337:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t ICI_IT_1:6;                 /*!< bit: 10..15  ICI/IT part 1 */
 338:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t GE:4;                       /*!< bit: 16..19  Greater than or Equal flags */
 339:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t _reserved1:4;               /*!< bit: 20..23  Reserved */
 340:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t T:1;                        /*!< bit:     24  Thumb bit */
 341:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t ICI_IT_2:2;                 /*!< bit: 25..26  ICI/IT part 2 */
 342:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t Q:1;                        /*!< bit:     27  Saturation condition flag */
 343:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t V:1;                        /*!< bit:     28  Overflow condition code flag */
 344:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t C:1;                        /*!< bit:     29  Carry condition code flag */
 345:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t Z:1;                        /*!< bit:     30  Zero condition code flag */
 346:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t N:1;                        /*!< bit:     31  Negative condition code flag */
 347:Drivers/CMSIS/Include/core_cm7.h ****   } b;                                   /*!< Structure used for bit  access */
 348:Drivers/CMSIS/Include/core_cm7.h ****   uint32_t w;                            /*!< Type      used for word access */
 349:Drivers/CMSIS/Include/core_cm7.h **** } xPSR_Type;
 350:Drivers/CMSIS/Include/core_cm7.h **** 
 351:Drivers/CMSIS/Include/core_cm7.h **** /* xPSR Register Definitions */
 352:Drivers/CMSIS/Include/core_cm7.h **** #define xPSR_N_Pos                         31U                                            /*!< xPSR
 353:Drivers/CMSIS/Include/core_cm7.h **** #define xPSR_N_Msk                         (1UL << xPSR_N_Pos)                            /*!< xPSR
 354:Drivers/CMSIS/Include/core_cm7.h **** 
 355:Drivers/CMSIS/Include/core_cm7.h **** #define xPSR_Z_Pos                         30U                                            /*!< xPSR
 356:Drivers/CMSIS/Include/core_cm7.h **** #define xPSR_Z_Msk                         (1UL << xPSR_Z_Pos)                            /*!< xPSR
 357:Drivers/CMSIS/Include/core_cm7.h **** 
 358:Drivers/CMSIS/Include/core_cm7.h **** #define xPSR_C_Pos                         29U                                            /*!< xPSR
 359:Drivers/CMSIS/Include/core_cm7.h **** #define xPSR_C_Msk                         (1UL << xPSR_C_Pos)                            /*!< xPSR
 360:Drivers/CMSIS/Include/core_cm7.h **** 
 361:Drivers/CMSIS/Include/core_cm7.h **** #define xPSR_V_Pos                         28U                                            /*!< xPSR
 362:Drivers/CMSIS/Include/core_cm7.h **** #define xPSR_V_Msk                         (1UL << xPSR_V_Pos)                            /*!< xPSR
 363:Drivers/CMSIS/Include/core_cm7.h **** 
 364:Drivers/CMSIS/Include/core_cm7.h **** #define xPSR_Q_Pos                         27U                                            /*!< xPSR
 365:Drivers/CMSIS/Include/core_cm7.h **** #define xPSR_Q_Msk                         (1UL << xPSR_Q_Pos)                            /*!< xPSR
 366:Drivers/CMSIS/Include/core_cm7.h **** 
 367:Drivers/CMSIS/Include/core_cm7.h **** #define xPSR_ICI_IT_2_Pos                  25U                                            /*!< xPSR
 368:Drivers/CMSIS/Include/core_cm7.h **** #define xPSR_ICI_IT_2_Msk                  (3UL << xPSR_ICI_IT_2_Pos)                     /*!< xPSR
 369:Drivers/CMSIS/Include/core_cm7.h **** 
 370:Drivers/CMSIS/Include/core_cm7.h **** #define xPSR_T_Pos                         24U                                            /*!< xPSR
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 64


 371:Drivers/CMSIS/Include/core_cm7.h **** #define xPSR_T_Msk                         (1UL << xPSR_T_Pos)                            /*!< xPSR
 372:Drivers/CMSIS/Include/core_cm7.h **** 
 373:Drivers/CMSIS/Include/core_cm7.h **** #define xPSR_GE_Pos                        16U                                            /*!< xPSR
 374:Drivers/CMSIS/Include/core_cm7.h **** #define xPSR_GE_Msk                        (0xFUL << xPSR_GE_Pos)                         /*!< xPSR
 375:Drivers/CMSIS/Include/core_cm7.h **** 
 376:Drivers/CMSIS/Include/core_cm7.h **** #define xPSR_ICI_IT_1_Pos                  10U                                            /*!< xPSR
 377:Drivers/CMSIS/Include/core_cm7.h **** #define xPSR_ICI_IT_1_Msk                  (0x3FUL << xPSR_ICI_IT_1_Pos)                  /*!< xPSR
 378:Drivers/CMSIS/Include/core_cm7.h **** 
 379:Drivers/CMSIS/Include/core_cm7.h **** #define xPSR_ISR_Pos                        0U                                            /*!< xPSR
 380:Drivers/CMSIS/Include/core_cm7.h **** #define xPSR_ISR_Msk                       (0x1FFUL /*<< xPSR_ISR_Pos*/)                  /*!< xPSR
 381:Drivers/CMSIS/Include/core_cm7.h **** 
 382:Drivers/CMSIS/Include/core_cm7.h **** 
 383:Drivers/CMSIS/Include/core_cm7.h **** /**
 384:Drivers/CMSIS/Include/core_cm7.h ****   \brief  Union type to access the Control Registers (CONTROL).
 385:Drivers/CMSIS/Include/core_cm7.h ****  */
 386:Drivers/CMSIS/Include/core_cm7.h **** typedef union
 387:Drivers/CMSIS/Include/core_cm7.h **** {
 388:Drivers/CMSIS/Include/core_cm7.h ****   struct
 389:Drivers/CMSIS/Include/core_cm7.h ****   {
 390:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t nPRIV:1;                    /*!< bit:      0  Execution privilege in Thread mode */
 391:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t SPSEL:1;                    /*!< bit:      1  Stack to be used */
 392:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t FPCA:1;                     /*!< bit:      2  FP extension active flag */
 393:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t _reserved0:29;              /*!< bit:  3..31  Reserved */
 394:Drivers/CMSIS/Include/core_cm7.h ****   } b;                                   /*!< Structure used for bit  access */
 395:Drivers/CMSIS/Include/core_cm7.h ****   uint32_t w;                            /*!< Type      used for word access */
 396:Drivers/CMSIS/Include/core_cm7.h **** } CONTROL_Type;
 397:Drivers/CMSIS/Include/core_cm7.h **** 
 398:Drivers/CMSIS/Include/core_cm7.h **** /* CONTROL Register Definitions */
 399:Drivers/CMSIS/Include/core_cm7.h **** #define CONTROL_FPCA_Pos                    2U                                            /*!< CONT
 400:Drivers/CMSIS/Include/core_cm7.h **** #define CONTROL_FPCA_Msk                   (1UL << CONTROL_FPCA_Pos)                      /*!< CONT
 401:Drivers/CMSIS/Include/core_cm7.h **** 
 402:Drivers/CMSIS/Include/core_cm7.h **** #define CONTROL_SPSEL_Pos                   1U                                            /*!< CONT
 403:Drivers/CMSIS/Include/core_cm7.h **** #define CONTROL_SPSEL_Msk                  (1UL << CONTROL_SPSEL_Pos)                     /*!< CONT
 404:Drivers/CMSIS/Include/core_cm7.h **** 
 405:Drivers/CMSIS/Include/core_cm7.h **** #define CONTROL_nPRIV_Pos                   0U                                            /*!< CONT
 406:Drivers/CMSIS/Include/core_cm7.h **** #define CONTROL_nPRIV_Msk                  (1UL /*<< CONTROL_nPRIV_Pos*/)                 /*!< CONT
 407:Drivers/CMSIS/Include/core_cm7.h **** 
 408:Drivers/CMSIS/Include/core_cm7.h **** /*@} end of group CMSIS_CORE */
 409:Drivers/CMSIS/Include/core_cm7.h **** 
 410:Drivers/CMSIS/Include/core_cm7.h **** 
 411:Drivers/CMSIS/Include/core_cm7.h **** /**
 412:Drivers/CMSIS/Include/core_cm7.h ****   \ingroup    CMSIS_core_register
 413:Drivers/CMSIS/Include/core_cm7.h ****   \defgroup   CMSIS_NVIC  Nested Vectored Interrupt Controller (NVIC)
 414:Drivers/CMSIS/Include/core_cm7.h ****   \brief      Type definitions for the NVIC Registers
 415:Drivers/CMSIS/Include/core_cm7.h ****   @{
 416:Drivers/CMSIS/Include/core_cm7.h ****  */
 417:Drivers/CMSIS/Include/core_cm7.h **** 
 418:Drivers/CMSIS/Include/core_cm7.h **** /**
 419:Drivers/CMSIS/Include/core_cm7.h ****   \brief  Structure type to access the Nested Vectored Interrupt Controller (NVIC).
 420:Drivers/CMSIS/Include/core_cm7.h ****  */
 421:Drivers/CMSIS/Include/core_cm7.h **** typedef struct
 422:Drivers/CMSIS/Include/core_cm7.h **** {
 423:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t ISER[8U];               /*!< Offset: 0x000 (R/W)  Interrupt Set Enable Register */
 424:Drivers/CMSIS/Include/core_cm7.h ****         uint32_t RESERVED0[24U];
 425:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t ICER[8U];               /*!< Offset: 0x080 (R/W)  Interrupt Clear Enable Register 
 426:Drivers/CMSIS/Include/core_cm7.h ****         uint32_t RESERVED1[24U];
 427:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t ISPR[8U];               /*!< Offset: 0x100 (R/W)  Interrupt Set Pending Register *
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 65


 428:Drivers/CMSIS/Include/core_cm7.h ****         uint32_t RESERVED2[24U];
 429:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t ICPR[8U];               /*!< Offset: 0x180 (R/W)  Interrupt Clear Pending Register
 430:Drivers/CMSIS/Include/core_cm7.h ****         uint32_t RESERVED3[24U];
 431:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t IABR[8U];               /*!< Offset: 0x200 (R/W)  Interrupt Active bit Register */
 432:Drivers/CMSIS/Include/core_cm7.h ****         uint32_t RESERVED4[56U];
 433:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint8_t  IP[240U];               /*!< Offset: 0x300 (R/W)  Interrupt Priority Register (8Bi
 434:Drivers/CMSIS/Include/core_cm7.h ****         uint32_t RESERVED5[644U];
 435:Drivers/CMSIS/Include/core_cm7.h ****   __OM  uint32_t STIR;                   /*!< Offset: 0xE00 ( /W)  Software Trigger Interrupt Regis
 436:Drivers/CMSIS/Include/core_cm7.h **** }  NVIC_Type;
 437:Drivers/CMSIS/Include/core_cm7.h **** 
 438:Drivers/CMSIS/Include/core_cm7.h **** /* Software Triggered Interrupt Register Definitions */
 439:Drivers/CMSIS/Include/core_cm7.h **** #define NVIC_STIR_INTID_Pos                 0U                                         /*!< STIR: I
 440:Drivers/CMSIS/Include/core_cm7.h **** #define NVIC_STIR_INTID_Msk                (0x1FFUL /*<< NVIC_STIR_INTID_Pos*/)        /*!< STIR: I
 441:Drivers/CMSIS/Include/core_cm7.h **** 
 442:Drivers/CMSIS/Include/core_cm7.h **** /*@} end of group CMSIS_NVIC */
 443:Drivers/CMSIS/Include/core_cm7.h **** 
 444:Drivers/CMSIS/Include/core_cm7.h **** 
 445:Drivers/CMSIS/Include/core_cm7.h **** /**
 446:Drivers/CMSIS/Include/core_cm7.h ****   \ingroup  CMSIS_core_register
 447:Drivers/CMSIS/Include/core_cm7.h ****   \defgroup CMSIS_SCB     System Control Block (SCB)
 448:Drivers/CMSIS/Include/core_cm7.h ****   \brief    Type definitions for the System Control Block Registers
 449:Drivers/CMSIS/Include/core_cm7.h ****   @{
 450:Drivers/CMSIS/Include/core_cm7.h ****  */
 451:Drivers/CMSIS/Include/core_cm7.h **** 
 452:Drivers/CMSIS/Include/core_cm7.h **** /**
 453:Drivers/CMSIS/Include/core_cm7.h ****   \brief  Structure type to access the System Control Block (SCB).
 454:Drivers/CMSIS/Include/core_cm7.h ****  */
 455:Drivers/CMSIS/Include/core_cm7.h **** typedef struct
 456:Drivers/CMSIS/Include/core_cm7.h **** {
 457:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t CPUID;                  /*!< Offset: 0x000 (R/ )  CPUID Base Register */
 458:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t ICSR;                   /*!< Offset: 0x004 (R/W)  Interrupt Control and State Regi
 459:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t VTOR;                   /*!< Offset: 0x008 (R/W)  Vector Table Offset Register */
 460:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t AIRCR;                  /*!< Offset: 0x00C (R/W)  Application Interrupt and Reset 
 461:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t SCR;                    /*!< Offset: 0x010 (R/W)  System Control Register */
 462:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t CCR;                    /*!< Offset: 0x014 (R/W)  Configuration Control Register *
 463:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint8_t  SHPR[12U];              /*!< Offset: 0x018 (R/W)  System Handlers Priority Registe
 464:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t SHCSR;                  /*!< Offset: 0x024 (R/W)  System Handler Control and State
 465:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t CFSR;                   /*!< Offset: 0x028 (R/W)  Configurable Fault Status Regist
 466:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t HFSR;                   /*!< Offset: 0x02C (R/W)  HardFault Status Register */
 467:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t DFSR;                   /*!< Offset: 0x030 (R/W)  Debug Fault Status Register */
 468:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t MMFAR;                  /*!< Offset: 0x034 (R/W)  MemManage Fault Address Register
 469:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t BFAR;                   /*!< Offset: 0x038 (R/W)  BusFault Address Register */
 470:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t AFSR;                   /*!< Offset: 0x03C (R/W)  Auxiliary Fault Status Register 
 471:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t ID_PFR[2U];             /*!< Offset: 0x040 (R/ )  Processor Feature Register */
 472:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t ID_DFR;                 /*!< Offset: 0x048 (R/ )  Debug Feature Register */
 473:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t ID_AFR;                 /*!< Offset: 0x04C (R/ )  Auxiliary Feature Register */
 474:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t ID_MFR[4U];             /*!< Offset: 0x050 (R/ )  Memory Model Feature Register */
 475:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t ID_ISAR[5U];            /*!< Offset: 0x060 (R/ )  Instruction Set Attributes Regis
 476:Drivers/CMSIS/Include/core_cm7.h ****         uint32_t RESERVED0[1U];
 477:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t CLIDR;                  /*!< Offset: 0x078 (R/ )  Cache Level ID register */
 478:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t CTR;                    /*!< Offset: 0x07C (R/ )  Cache Type register */
 479:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t CCSIDR;                 /*!< Offset: 0x080 (R/ )  Cache Size ID Register */
 480:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t CSSELR;                 /*!< Offset: 0x084 (R/W)  Cache Size Selection Register */
 481:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t CPACR;                  /*!< Offset: 0x088 (R/W)  Coprocessor Access Control Regis
 482:Drivers/CMSIS/Include/core_cm7.h ****         uint32_t RESERVED3[93U];
 483:Drivers/CMSIS/Include/core_cm7.h ****   __OM  uint32_t STIR;                   /*!< Offset: 0x200 ( /W)  Software Triggered Interrupt Reg
 484:Drivers/CMSIS/Include/core_cm7.h ****         uint32_t RESERVED4[15U];
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 66


 485:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t MVFR0;                  /*!< Offset: 0x240 (R/ )  Media and VFP Feature Register 0
 486:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t MVFR1;                  /*!< Offset: 0x244 (R/ )  Media and VFP Feature Register 1
 487:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t MVFR2;                  /*!< Offset: 0x248 (R/ )  Media and VFP Feature Register 2
 488:Drivers/CMSIS/Include/core_cm7.h ****         uint32_t RESERVED5[1U];
 489:Drivers/CMSIS/Include/core_cm7.h ****   __OM  uint32_t ICIALLU;                /*!< Offset: 0x250 ( /W)  I-Cache Invalidate All to PoU */
 490:Drivers/CMSIS/Include/core_cm7.h ****         uint32_t RESERVED6[1U];
 491:Drivers/CMSIS/Include/core_cm7.h ****   __OM  uint32_t ICIMVAU;                /*!< Offset: 0x258 ( /W)  I-Cache Invalidate by MVA to PoU
 492:Drivers/CMSIS/Include/core_cm7.h ****   __OM  uint32_t DCIMVAC;                /*!< Offset: 0x25C ( /W)  D-Cache Invalidate by MVA to PoC
 493:Drivers/CMSIS/Include/core_cm7.h ****   __OM  uint32_t DCISW;                  /*!< Offset: 0x260 ( /W)  D-Cache Invalidate by Set-way */
 494:Drivers/CMSIS/Include/core_cm7.h ****   __OM  uint32_t DCCMVAU;                /*!< Offset: 0x264 ( /W)  D-Cache Clean by MVA to PoU */
 495:Drivers/CMSIS/Include/core_cm7.h ****   __OM  uint32_t DCCMVAC;                /*!< Offset: 0x268 ( /W)  D-Cache Clean by MVA to PoC */
 496:Drivers/CMSIS/Include/core_cm7.h ****   __OM  uint32_t DCCSW;                  /*!< Offset: 0x26C ( /W)  D-Cache Clean by Set-way */
 497:Drivers/CMSIS/Include/core_cm7.h ****   __OM  uint32_t DCCIMVAC;               /*!< Offset: 0x270 ( /W)  D-Cache Clean and Invalidate by 
 498:Drivers/CMSIS/Include/core_cm7.h ****   __OM  uint32_t DCCISW;                 /*!< Offset: 0x274 ( /W)  D-Cache Clean and Invalidate by 
 499:Drivers/CMSIS/Include/core_cm7.h ****         uint32_t RESERVED7[6U];
 500:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t ITCMCR;                 /*!< Offset: 0x290 (R/W)  Instruction Tightly-Coupled Memo
 501:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t DTCMCR;                 /*!< Offset: 0x294 (R/W)  Data Tightly-Coupled Memory Cont
 502:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t AHBPCR;                 /*!< Offset: 0x298 (R/W)  AHBP Control Register */
 503:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t CACR;                   /*!< Offset: 0x29C (R/W)  L1 Cache Control Register */
 504:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t AHBSCR;                 /*!< Offset: 0x2A0 (R/W)  AHB Slave Control Register */
 505:Drivers/CMSIS/Include/core_cm7.h ****         uint32_t RESERVED8[1U];
 506:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t ABFSR;                  /*!< Offset: 0x2A8 (R/W)  Auxiliary Bus Fault Status Regis
 507:Drivers/CMSIS/Include/core_cm7.h **** } SCB_Type;
 508:Drivers/CMSIS/Include/core_cm7.h **** 
 509:Drivers/CMSIS/Include/core_cm7.h **** /* SCB CPUID Register Definitions */
 510:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CPUID_IMPLEMENTER_Pos          24U                                            /*!< SCB 
 511:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CPUID_IMPLEMENTER_Msk          (0xFFUL << SCB_CPUID_IMPLEMENTER_Pos)          /*!< SCB 
 512:Drivers/CMSIS/Include/core_cm7.h **** 
 513:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CPUID_VARIANT_Pos              20U                                            /*!< SCB 
 514:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CPUID_VARIANT_Msk              (0xFUL << SCB_CPUID_VARIANT_Pos)               /*!< SCB 
 515:Drivers/CMSIS/Include/core_cm7.h **** 
 516:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CPUID_ARCHITECTURE_Pos         16U                                            /*!< SCB 
 517:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CPUID_ARCHITECTURE_Msk         (0xFUL << SCB_CPUID_ARCHITECTURE_Pos)          /*!< SCB 
 518:Drivers/CMSIS/Include/core_cm7.h **** 
 519:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CPUID_PARTNO_Pos                4U                                            /*!< SCB 
 520:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CPUID_PARTNO_Msk               (0xFFFUL << SCB_CPUID_PARTNO_Pos)              /*!< SCB 
 521:Drivers/CMSIS/Include/core_cm7.h **** 
 522:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CPUID_REVISION_Pos              0U                                            /*!< SCB 
 523:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CPUID_REVISION_Msk             (0xFUL /*<< SCB_CPUID_REVISION_Pos*/)          /*!< SCB 
 524:Drivers/CMSIS/Include/core_cm7.h **** 
 525:Drivers/CMSIS/Include/core_cm7.h **** /* SCB Interrupt Control State Register Definitions */
 526:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ICSR_NMIPENDSET_Pos            31U                                            /*!< SCB 
 527:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ICSR_NMIPENDSET_Msk            (1UL << SCB_ICSR_NMIPENDSET_Pos)               /*!< SCB 
 528:Drivers/CMSIS/Include/core_cm7.h **** 
 529:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ICSR_PENDSVSET_Pos             28U                                            /*!< SCB 
 530:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ICSR_PENDSVSET_Msk             (1UL << SCB_ICSR_PENDSVSET_Pos)                /*!< SCB 
 531:Drivers/CMSIS/Include/core_cm7.h **** 
 532:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ICSR_PENDSVCLR_Pos             27U                                            /*!< SCB 
 533:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ICSR_PENDSVCLR_Msk             (1UL << SCB_ICSR_PENDSVCLR_Pos)                /*!< SCB 
 534:Drivers/CMSIS/Include/core_cm7.h **** 
 535:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ICSR_PENDSTSET_Pos             26U                                            /*!< SCB 
 536:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ICSR_PENDSTSET_Msk             (1UL << SCB_ICSR_PENDSTSET_Pos)                /*!< SCB 
 537:Drivers/CMSIS/Include/core_cm7.h **** 
 538:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ICSR_PENDSTCLR_Pos             25U                                            /*!< SCB 
 539:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ICSR_PENDSTCLR_Msk             (1UL << SCB_ICSR_PENDSTCLR_Pos)                /*!< SCB 
 540:Drivers/CMSIS/Include/core_cm7.h **** 
 541:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ICSR_ISRPREEMPT_Pos            23U                                            /*!< SCB 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 67


 542:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ICSR_ISRPREEMPT_Msk            (1UL << SCB_ICSR_ISRPREEMPT_Pos)               /*!< SCB 
 543:Drivers/CMSIS/Include/core_cm7.h **** 
 544:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ICSR_ISRPENDING_Pos            22U                                            /*!< SCB 
 545:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ICSR_ISRPENDING_Msk            (1UL << SCB_ICSR_ISRPENDING_Pos)               /*!< SCB 
 546:Drivers/CMSIS/Include/core_cm7.h **** 
 547:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ICSR_VECTPENDING_Pos           12U                                            /*!< SCB 
 548:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ICSR_VECTPENDING_Msk           (0x1FFUL << SCB_ICSR_VECTPENDING_Pos)          /*!< SCB 
 549:Drivers/CMSIS/Include/core_cm7.h **** 
 550:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ICSR_RETTOBASE_Pos             11U                                            /*!< SCB 
 551:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ICSR_RETTOBASE_Msk             (1UL << SCB_ICSR_RETTOBASE_Pos)                /*!< SCB 
 552:Drivers/CMSIS/Include/core_cm7.h **** 
 553:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ICSR_VECTACTIVE_Pos             0U                                            /*!< SCB 
 554:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ICSR_VECTACTIVE_Msk            (0x1FFUL /*<< SCB_ICSR_VECTACTIVE_Pos*/)       /*!< SCB 
 555:Drivers/CMSIS/Include/core_cm7.h **** 
 556:Drivers/CMSIS/Include/core_cm7.h **** /* SCB Vector Table Offset Register Definitions */
 557:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_VTOR_TBLOFF_Pos                 7U                                            /*!< SCB 
 558:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_VTOR_TBLOFF_Msk                (0x1FFFFFFUL << SCB_VTOR_TBLOFF_Pos)           /*!< SCB 
 559:Drivers/CMSIS/Include/core_cm7.h **** 
 560:Drivers/CMSIS/Include/core_cm7.h **** /* SCB Application Interrupt and Reset Control Register Definitions */
 561:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_AIRCR_VECTKEY_Pos              16U                                            /*!< SCB 
 562:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_AIRCR_VECTKEY_Msk              (0xFFFFUL << SCB_AIRCR_VECTKEY_Pos)            /*!< SCB 
 563:Drivers/CMSIS/Include/core_cm7.h **** 
 564:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_AIRCR_VECTKEYSTAT_Pos          16U                                            /*!< SCB 
 565:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_AIRCR_VECTKEYSTAT_Msk          (0xFFFFUL << SCB_AIRCR_VECTKEYSTAT_Pos)        /*!< SCB 
 566:Drivers/CMSIS/Include/core_cm7.h **** 
 567:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_AIRCR_ENDIANESS_Pos            15U                                            /*!< SCB 
 568:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_AIRCR_ENDIANESS_Msk            (1UL << SCB_AIRCR_ENDIANESS_Pos)               /*!< SCB 
 569:Drivers/CMSIS/Include/core_cm7.h **** 
 570:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_AIRCR_PRIGROUP_Pos              8U                                            /*!< SCB 
 571:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_AIRCR_PRIGROUP_Msk             (7UL << SCB_AIRCR_PRIGROUP_Pos)                /*!< SCB 
 572:Drivers/CMSIS/Include/core_cm7.h **** 
 573:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_AIRCR_SYSRESETREQ_Pos           2U                                            /*!< SCB 
 574:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_AIRCR_SYSRESETREQ_Msk          (1UL << SCB_AIRCR_SYSRESETREQ_Pos)             /*!< SCB 
 575:Drivers/CMSIS/Include/core_cm7.h **** 
 576:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_AIRCR_VECTCLRACTIVE_Pos         1U                                            /*!< SCB 
 577:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_AIRCR_VECTCLRACTIVE_Msk        (1UL << SCB_AIRCR_VECTCLRACTIVE_Pos)           /*!< SCB 
 578:Drivers/CMSIS/Include/core_cm7.h **** 
 579:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_AIRCR_VECTRESET_Pos             0U                                            /*!< SCB 
 580:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_AIRCR_VECTRESET_Msk            (1UL /*<< SCB_AIRCR_VECTRESET_Pos*/)           /*!< SCB 
 581:Drivers/CMSIS/Include/core_cm7.h **** 
 582:Drivers/CMSIS/Include/core_cm7.h **** /* SCB System Control Register Definitions */
 583:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_SCR_SEVONPEND_Pos               4U                                            /*!< SCB 
 584:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_SCR_SEVONPEND_Msk              (1UL << SCB_SCR_SEVONPEND_Pos)                 /*!< SCB 
 585:Drivers/CMSIS/Include/core_cm7.h **** 
 586:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_SCR_SLEEPDEEP_Pos               2U                                            /*!< SCB 
 587:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_SCR_SLEEPDEEP_Msk              (1UL << SCB_SCR_SLEEPDEEP_Pos)                 /*!< SCB 
 588:Drivers/CMSIS/Include/core_cm7.h **** 
 589:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_SCR_SLEEPONEXIT_Pos             1U                                            /*!< SCB 
 590:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_SCR_SLEEPONEXIT_Msk            (1UL << SCB_SCR_SLEEPONEXIT_Pos)               /*!< SCB 
 591:Drivers/CMSIS/Include/core_cm7.h **** 
 592:Drivers/CMSIS/Include/core_cm7.h **** /* SCB Configuration Control Register Definitions */
 593:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CCR_BP_Pos                      18U                                           /*!< SCB 
 594:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CCR_BP_Msk                     (1UL << SCB_CCR_BP_Pos)                        /*!< SCB 
 595:Drivers/CMSIS/Include/core_cm7.h **** 
 596:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CCR_IC_Pos                      17U                                           /*!< SCB 
 597:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CCR_IC_Msk                     (1UL << SCB_CCR_IC_Pos)                        /*!< SCB 
 598:Drivers/CMSIS/Include/core_cm7.h **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 68


 599:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CCR_DC_Pos                      16U                                           /*!< SCB 
 600:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CCR_DC_Msk                     (1UL << SCB_CCR_DC_Pos)                        /*!< SCB 
 601:Drivers/CMSIS/Include/core_cm7.h **** 
 602:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CCR_STKALIGN_Pos                9U                                            /*!< SCB 
 603:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CCR_STKALIGN_Msk               (1UL << SCB_CCR_STKALIGN_Pos)                  /*!< SCB 
 604:Drivers/CMSIS/Include/core_cm7.h **** 
 605:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CCR_BFHFNMIGN_Pos               8U                                            /*!< SCB 
 606:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CCR_BFHFNMIGN_Msk              (1UL << SCB_CCR_BFHFNMIGN_Pos)                 /*!< SCB 
 607:Drivers/CMSIS/Include/core_cm7.h **** 
 608:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CCR_DIV_0_TRP_Pos               4U                                            /*!< SCB 
 609:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CCR_DIV_0_TRP_Msk              (1UL << SCB_CCR_DIV_0_TRP_Pos)                 /*!< SCB 
 610:Drivers/CMSIS/Include/core_cm7.h **** 
 611:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CCR_UNALIGN_TRP_Pos             3U                                            /*!< SCB 
 612:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CCR_UNALIGN_TRP_Msk            (1UL << SCB_CCR_UNALIGN_TRP_Pos)               /*!< SCB 
 613:Drivers/CMSIS/Include/core_cm7.h **** 
 614:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CCR_USERSETMPEND_Pos            1U                                            /*!< SCB 
 615:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CCR_USERSETMPEND_Msk           (1UL << SCB_CCR_USERSETMPEND_Pos)              /*!< SCB 
 616:Drivers/CMSIS/Include/core_cm7.h **** 
 617:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CCR_NONBASETHRDENA_Pos          0U                                            /*!< SCB 
 618:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CCR_NONBASETHRDENA_Msk         (1UL /*<< SCB_CCR_NONBASETHRDENA_Pos*/)        /*!< SCB 
 619:Drivers/CMSIS/Include/core_cm7.h **** 
 620:Drivers/CMSIS/Include/core_cm7.h **** /* SCB System Handler Control and State Register Definitions */
 621:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_SHCSR_USGFAULTENA_Pos          18U                                            /*!< SCB 
 622:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_SHCSR_USGFAULTENA_Msk          (1UL << SCB_SHCSR_USGFAULTENA_Pos)             /*!< SCB 
 623:Drivers/CMSIS/Include/core_cm7.h **** 
 624:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_SHCSR_BUSFAULTENA_Pos          17U                                            /*!< SCB 
 625:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_SHCSR_BUSFAULTENA_Msk          (1UL << SCB_SHCSR_BUSFAULTENA_Pos)             /*!< SCB 
 626:Drivers/CMSIS/Include/core_cm7.h **** 
 627:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_SHCSR_MEMFAULTENA_Pos          16U                                            /*!< SCB 
 628:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_SHCSR_MEMFAULTENA_Msk          (1UL << SCB_SHCSR_MEMFAULTENA_Pos)             /*!< SCB 
 629:Drivers/CMSIS/Include/core_cm7.h **** 
 630:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_SHCSR_SVCALLPENDED_Pos         15U                                            /*!< SCB 
 631:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_SHCSR_SVCALLPENDED_Msk         (1UL << SCB_SHCSR_SVCALLPENDED_Pos)            /*!< SCB 
 632:Drivers/CMSIS/Include/core_cm7.h **** 
 633:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_SHCSR_BUSFAULTPENDED_Pos       14U                                            /*!< SCB 
 634:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_SHCSR_BUSFAULTPENDED_Msk       (1UL << SCB_SHCSR_BUSFAULTPENDED_Pos)          /*!< SCB 
 635:Drivers/CMSIS/Include/core_cm7.h **** 
 636:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_SHCSR_MEMFAULTPENDED_Pos       13U                                            /*!< SCB 
 637:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_SHCSR_MEMFAULTPENDED_Msk       (1UL << SCB_SHCSR_MEMFAULTPENDED_Pos)          /*!< SCB 
 638:Drivers/CMSIS/Include/core_cm7.h **** 
 639:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_SHCSR_USGFAULTPENDED_Pos       12U                                            /*!< SCB 
 640:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_SHCSR_USGFAULTPENDED_Msk       (1UL << SCB_SHCSR_USGFAULTPENDED_Pos)          /*!< SCB 
 641:Drivers/CMSIS/Include/core_cm7.h **** 
 642:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_SHCSR_SYSTICKACT_Pos           11U                                            /*!< SCB 
 643:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_SHCSR_SYSTICKACT_Msk           (1UL << SCB_SHCSR_SYSTICKACT_Pos)              /*!< SCB 
 644:Drivers/CMSIS/Include/core_cm7.h **** 
 645:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_SHCSR_PENDSVACT_Pos            10U                                            /*!< SCB 
 646:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_SHCSR_PENDSVACT_Msk            (1UL << SCB_SHCSR_PENDSVACT_Pos)               /*!< SCB 
 647:Drivers/CMSIS/Include/core_cm7.h **** 
 648:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_SHCSR_MONITORACT_Pos            8U                                            /*!< SCB 
 649:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_SHCSR_MONITORACT_Msk           (1UL << SCB_SHCSR_MONITORACT_Pos)              /*!< SCB 
 650:Drivers/CMSIS/Include/core_cm7.h **** 
 651:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_SHCSR_SVCALLACT_Pos             7U                                            /*!< SCB 
 652:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_SHCSR_SVCALLACT_Msk            (1UL << SCB_SHCSR_SVCALLACT_Pos)               /*!< SCB 
 653:Drivers/CMSIS/Include/core_cm7.h **** 
 654:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_SHCSR_USGFAULTACT_Pos           3U                                            /*!< SCB 
 655:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_SHCSR_USGFAULTACT_Msk          (1UL << SCB_SHCSR_USGFAULTACT_Pos)             /*!< SCB 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 69


 656:Drivers/CMSIS/Include/core_cm7.h **** 
 657:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_SHCSR_BUSFAULTACT_Pos           1U                                            /*!< SCB 
 658:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_SHCSR_BUSFAULTACT_Msk          (1UL << SCB_SHCSR_BUSFAULTACT_Pos)             /*!< SCB 
 659:Drivers/CMSIS/Include/core_cm7.h **** 
 660:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_SHCSR_MEMFAULTACT_Pos           0U                                            /*!< SCB 
 661:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_SHCSR_MEMFAULTACT_Msk          (1UL /*<< SCB_SHCSR_MEMFAULTACT_Pos*/)         /*!< SCB 
 662:Drivers/CMSIS/Include/core_cm7.h **** 
 663:Drivers/CMSIS/Include/core_cm7.h **** /* SCB Configurable Fault Status Register Definitions */
 664:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_USGFAULTSR_Pos            16U                                            /*!< SCB 
 665:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_USGFAULTSR_Msk            (0xFFFFUL << SCB_CFSR_USGFAULTSR_Pos)          /*!< SCB 
 666:Drivers/CMSIS/Include/core_cm7.h **** 
 667:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_BUSFAULTSR_Pos             8U                                            /*!< SCB 
 668:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_BUSFAULTSR_Msk            (0xFFUL << SCB_CFSR_BUSFAULTSR_Pos)            /*!< SCB 
 669:Drivers/CMSIS/Include/core_cm7.h **** 
 670:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_MEMFAULTSR_Pos             0U                                            /*!< SCB 
 671:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_MEMFAULTSR_Msk            (0xFFUL /*<< SCB_CFSR_MEMFAULTSR_Pos*/)        /*!< SCB 
 672:Drivers/CMSIS/Include/core_cm7.h **** 
 673:Drivers/CMSIS/Include/core_cm7.h **** /* MemManage Fault Status Register (part of SCB Configurable Fault Status Register) */
 674:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_MMARVALID_Pos             (SCB_SHCSR_MEMFAULTACT_Pos + 7U)               /*!< SCB 
 675:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_MMARVALID_Msk             (1UL << SCB_CFSR_MMARVALID_Pos)                /*!< SCB 
 676:Drivers/CMSIS/Include/core_cm7.h **** 
 677:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_MLSPERR_Pos               (SCB_SHCSR_MEMFAULTACT_Pos + 5U)               /*!< SCB 
 678:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_MLSPERR_Msk               (1UL << SCB_CFSR_MLSPERR_Pos)                  /*!< SCB 
 679:Drivers/CMSIS/Include/core_cm7.h **** 
 680:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_MSTKERR_Pos               (SCB_SHCSR_MEMFAULTACT_Pos + 4U)               /*!< SCB 
 681:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_MSTKERR_Msk               (1UL << SCB_CFSR_MSTKERR_Pos)                  /*!< SCB 
 682:Drivers/CMSIS/Include/core_cm7.h **** 
 683:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_MUNSTKERR_Pos             (SCB_SHCSR_MEMFAULTACT_Pos + 3U)               /*!< SCB 
 684:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_MUNSTKERR_Msk             (1UL << SCB_CFSR_MUNSTKERR_Pos)                /*!< SCB 
 685:Drivers/CMSIS/Include/core_cm7.h **** 
 686:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_DACCVIOL_Pos              (SCB_SHCSR_MEMFAULTACT_Pos + 1U)               /*!< SCB 
 687:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_DACCVIOL_Msk              (1UL << SCB_CFSR_DACCVIOL_Pos)                 /*!< SCB 
 688:Drivers/CMSIS/Include/core_cm7.h **** 
 689:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_IACCVIOL_Pos              (SCB_SHCSR_MEMFAULTACT_Pos + 0U)               /*!< SCB 
 690:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_IACCVIOL_Msk              (1UL /*<< SCB_CFSR_IACCVIOL_Pos*/)             /*!< SCB 
 691:Drivers/CMSIS/Include/core_cm7.h **** 
 692:Drivers/CMSIS/Include/core_cm7.h **** /* BusFault Status Register (part of SCB Configurable Fault Status Register) */
 693:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_BFARVALID_Pos            (SCB_CFSR_BUSFAULTSR_Pos + 7U)                  /*!< SCB 
 694:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_BFARVALID_Msk            (1UL << SCB_CFSR_BFARVALID_Pos)                 /*!< SCB 
 695:Drivers/CMSIS/Include/core_cm7.h **** 
 696:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_LSPERR_Pos               (SCB_CFSR_BUSFAULTSR_Pos + 5U)                  /*!< SCB 
 697:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_LSPERR_Msk               (1UL << SCB_CFSR_LSPERR_Pos)                    /*!< SCB 
 698:Drivers/CMSIS/Include/core_cm7.h **** 
 699:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_STKERR_Pos               (SCB_CFSR_BUSFAULTSR_Pos + 4U)                  /*!< SCB 
 700:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_STKERR_Msk               (1UL << SCB_CFSR_STKERR_Pos)                    /*!< SCB 
 701:Drivers/CMSIS/Include/core_cm7.h **** 
 702:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_UNSTKERR_Pos             (SCB_CFSR_BUSFAULTSR_Pos + 3U)                  /*!< SCB 
 703:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_UNSTKERR_Msk             (1UL << SCB_CFSR_UNSTKERR_Pos)                  /*!< SCB 
 704:Drivers/CMSIS/Include/core_cm7.h **** 
 705:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_IMPRECISERR_Pos          (SCB_CFSR_BUSFAULTSR_Pos + 2U)                  /*!< SCB 
 706:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_IMPRECISERR_Msk          (1UL << SCB_CFSR_IMPRECISERR_Pos)               /*!< SCB 
 707:Drivers/CMSIS/Include/core_cm7.h **** 
 708:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_PRECISERR_Pos            (SCB_CFSR_BUSFAULTSR_Pos + 1U)                  /*!< SCB 
 709:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_PRECISERR_Msk            (1UL << SCB_CFSR_PRECISERR_Pos)                 /*!< SCB 
 710:Drivers/CMSIS/Include/core_cm7.h **** 
 711:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_IBUSERR_Pos              (SCB_CFSR_BUSFAULTSR_Pos + 0U)                  /*!< SCB 
 712:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_IBUSERR_Msk              (1UL << SCB_CFSR_IBUSERR_Pos)                   /*!< SCB 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 70


 713:Drivers/CMSIS/Include/core_cm7.h **** 
 714:Drivers/CMSIS/Include/core_cm7.h **** /* UsageFault Status Register (part of SCB Configurable Fault Status Register) */
 715:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_DIVBYZERO_Pos            (SCB_CFSR_USGFAULTSR_Pos + 9U)                  /*!< SCB 
 716:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_DIVBYZERO_Msk            (1UL << SCB_CFSR_DIVBYZERO_Pos)                 /*!< SCB 
 717:Drivers/CMSIS/Include/core_cm7.h **** 
 718:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_UNALIGNED_Pos            (SCB_CFSR_USGFAULTSR_Pos + 8U)                  /*!< SCB 
 719:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_UNALIGNED_Msk            (1UL << SCB_CFSR_UNALIGNED_Pos)                 /*!< SCB 
 720:Drivers/CMSIS/Include/core_cm7.h **** 
 721:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_NOCP_Pos                 (SCB_CFSR_USGFAULTSR_Pos + 3U)                  /*!< SCB 
 722:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_NOCP_Msk                 (1UL << SCB_CFSR_NOCP_Pos)                      /*!< SCB 
 723:Drivers/CMSIS/Include/core_cm7.h **** 
 724:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_INVPC_Pos                (SCB_CFSR_USGFAULTSR_Pos + 2U)                  /*!< SCB 
 725:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_INVPC_Msk                (1UL << SCB_CFSR_INVPC_Pos)                     /*!< SCB 
 726:Drivers/CMSIS/Include/core_cm7.h **** 
 727:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_INVSTATE_Pos             (SCB_CFSR_USGFAULTSR_Pos + 1U)                  /*!< SCB 
 728:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_INVSTATE_Msk             (1UL << SCB_CFSR_INVSTATE_Pos)                  /*!< SCB 
 729:Drivers/CMSIS/Include/core_cm7.h **** 
 730:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_UNDEFINSTR_Pos           (SCB_CFSR_USGFAULTSR_Pos + 0U)                  /*!< SCB 
 731:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CFSR_UNDEFINSTR_Msk           (1UL << SCB_CFSR_UNDEFINSTR_Pos)                /*!< SCB 
 732:Drivers/CMSIS/Include/core_cm7.h **** 
 733:Drivers/CMSIS/Include/core_cm7.h **** /* SCB Hard Fault Status Register Definitions */
 734:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_HFSR_DEBUGEVT_Pos              31U                                            /*!< SCB 
 735:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_HFSR_DEBUGEVT_Msk              (1UL << SCB_HFSR_DEBUGEVT_Pos)                 /*!< SCB 
 736:Drivers/CMSIS/Include/core_cm7.h **** 
 737:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_HFSR_FORCED_Pos                30U                                            /*!< SCB 
 738:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_HFSR_FORCED_Msk                (1UL << SCB_HFSR_FORCED_Pos)                   /*!< SCB 
 739:Drivers/CMSIS/Include/core_cm7.h **** 
 740:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_HFSR_VECTTBL_Pos                1U                                            /*!< SCB 
 741:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_HFSR_VECTTBL_Msk               (1UL << SCB_HFSR_VECTTBL_Pos)                  /*!< SCB 
 742:Drivers/CMSIS/Include/core_cm7.h **** 
 743:Drivers/CMSIS/Include/core_cm7.h **** /* SCB Debug Fault Status Register Definitions */
 744:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_DFSR_EXTERNAL_Pos               4U                                            /*!< SCB 
 745:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_DFSR_EXTERNAL_Msk              (1UL << SCB_DFSR_EXTERNAL_Pos)                 /*!< SCB 
 746:Drivers/CMSIS/Include/core_cm7.h **** 
 747:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_DFSR_VCATCH_Pos                 3U                                            /*!< SCB 
 748:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_DFSR_VCATCH_Msk                (1UL << SCB_DFSR_VCATCH_Pos)                   /*!< SCB 
 749:Drivers/CMSIS/Include/core_cm7.h **** 
 750:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_DFSR_DWTTRAP_Pos                2U                                            /*!< SCB 
 751:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_DFSR_DWTTRAP_Msk               (1UL << SCB_DFSR_DWTTRAP_Pos)                  /*!< SCB 
 752:Drivers/CMSIS/Include/core_cm7.h **** 
 753:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_DFSR_BKPT_Pos                   1U                                            /*!< SCB 
 754:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_DFSR_BKPT_Msk                  (1UL << SCB_DFSR_BKPT_Pos)                     /*!< SCB 
 755:Drivers/CMSIS/Include/core_cm7.h **** 
 756:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_DFSR_HALTED_Pos                 0U                                            /*!< SCB 
 757:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_DFSR_HALTED_Msk                (1UL /*<< SCB_DFSR_HALTED_Pos*/)               /*!< SCB 
 758:Drivers/CMSIS/Include/core_cm7.h **** 
 759:Drivers/CMSIS/Include/core_cm7.h **** /* SCB Cache Level ID Register Definitions */
 760:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CLIDR_LOUU_Pos                 27U                                            /*!< SCB 
 761:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CLIDR_LOUU_Msk                 (7UL << SCB_CLIDR_LOUU_Pos)                    /*!< SCB 
 762:Drivers/CMSIS/Include/core_cm7.h **** 
 763:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CLIDR_LOC_Pos                  24U                                            /*!< SCB 
 764:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CLIDR_LOC_Msk                  (7UL << SCB_CLIDR_LOC_Pos)                     /*!< SCB 
 765:Drivers/CMSIS/Include/core_cm7.h **** 
 766:Drivers/CMSIS/Include/core_cm7.h **** /* SCB Cache Type Register Definitions */
 767:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CTR_FORMAT_Pos                 29U                                            /*!< SCB 
 768:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CTR_FORMAT_Msk                 (7UL << SCB_CTR_FORMAT_Pos)                    /*!< SCB 
 769:Drivers/CMSIS/Include/core_cm7.h **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 71


 770:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CTR_CWG_Pos                    24U                                            /*!< SCB 
 771:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CTR_CWG_Msk                    (0xFUL << SCB_CTR_CWG_Pos)                     /*!< SCB 
 772:Drivers/CMSIS/Include/core_cm7.h **** 
 773:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CTR_ERG_Pos                    20U                                            /*!< SCB 
 774:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CTR_ERG_Msk                    (0xFUL << SCB_CTR_ERG_Pos)                     /*!< SCB 
 775:Drivers/CMSIS/Include/core_cm7.h **** 
 776:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CTR_DMINLINE_Pos               16U                                            /*!< SCB 
 777:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CTR_DMINLINE_Msk               (0xFUL << SCB_CTR_DMINLINE_Pos)                /*!< SCB 
 778:Drivers/CMSIS/Include/core_cm7.h **** 
 779:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CTR_IMINLINE_Pos                0U                                            /*!< SCB 
 780:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CTR_IMINLINE_Msk               (0xFUL /*<< SCB_CTR_IMINLINE_Pos*/)            /*!< SCB 
 781:Drivers/CMSIS/Include/core_cm7.h **** 
 782:Drivers/CMSIS/Include/core_cm7.h **** /* SCB Cache Size ID Register Definitions */
 783:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CCSIDR_WT_Pos                  31U                                            /*!< SCB 
 784:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CCSIDR_WT_Msk                  (1UL << SCB_CCSIDR_WT_Pos)                     /*!< SCB 
 785:Drivers/CMSIS/Include/core_cm7.h **** 
 786:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CCSIDR_WB_Pos                  30U                                            /*!< SCB 
 787:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CCSIDR_WB_Msk                  (1UL << SCB_CCSIDR_WB_Pos)                     /*!< SCB 
 788:Drivers/CMSIS/Include/core_cm7.h **** 
 789:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CCSIDR_RA_Pos                  29U                                            /*!< SCB 
 790:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CCSIDR_RA_Msk                  (1UL << SCB_CCSIDR_RA_Pos)                     /*!< SCB 
 791:Drivers/CMSIS/Include/core_cm7.h **** 
 792:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CCSIDR_WA_Pos                  28U                                            /*!< SCB 
 793:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CCSIDR_WA_Msk                  (1UL << SCB_CCSIDR_WA_Pos)                     /*!< SCB 
 794:Drivers/CMSIS/Include/core_cm7.h **** 
 795:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CCSIDR_NUMSETS_Pos             13U                                            /*!< SCB 
 796:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CCSIDR_NUMSETS_Msk             (0x7FFFUL << SCB_CCSIDR_NUMSETS_Pos)           /*!< SCB 
 797:Drivers/CMSIS/Include/core_cm7.h **** 
 798:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CCSIDR_ASSOCIATIVITY_Pos        3U                                            /*!< SCB 
 799:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CCSIDR_ASSOCIATIVITY_Msk       (0x3FFUL << SCB_CCSIDR_ASSOCIATIVITY_Pos)      /*!< SCB 
 800:Drivers/CMSIS/Include/core_cm7.h **** 
 801:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CCSIDR_LINESIZE_Pos             0U                                            /*!< SCB 
 802:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CCSIDR_LINESIZE_Msk            (7UL /*<< SCB_CCSIDR_LINESIZE_Pos*/)           /*!< SCB 
 803:Drivers/CMSIS/Include/core_cm7.h **** 
 804:Drivers/CMSIS/Include/core_cm7.h **** /* SCB Cache Size Selection Register Definitions */
 805:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CSSELR_LEVEL_Pos                1U                                            /*!< SCB 
 806:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CSSELR_LEVEL_Msk               (7UL << SCB_CSSELR_LEVEL_Pos)                  /*!< SCB 
 807:Drivers/CMSIS/Include/core_cm7.h **** 
 808:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CSSELR_IND_Pos                  0U                                            /*!< SCB 
 809:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CSSELR_IND_Msk                 (1UL /*<< SCB_CSSELR_IND_Pos*/)                /*!< SCB 
 810:Drivers/CMSIS/Include/core_cm7.h **** 
 811:Drivers/CMSIS/Include/core_cm7.h **** /* SCB Software Triggered Interrupt Register Definitions */
 812:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_STIR_INTID_Pos                  0U                                            /*!< SCB 
 813:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_STIR_INTID_Msk                 (0x1FFUL /*<< SCB_STIR_INTID_Pos*/)            /*!< SCB 
 814:Drivers/CMSIS/Include/core_cm7.h **** 
 815:Drivers/CMSIS/Include/core_cm7.h **** /* SCB D-Cache Invalidate by Set-way Register Definitions */
 816:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_DCISW_WAY_Pos                  30U                                            /*!< SCB 
 817:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_DCISW_WAY_Msk                  (3UL << SCB_DCISW_WAY_Pos)                     /*!< SCB 
 818:Drivers/CMSIS/Include/core_cm7.h **** 
 819:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_DCISW_SET_Pos                   5U                                            /*!< SCB 
 820:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_DCISW_SET_Msk                  (0x1FFUL << SCB_DCISW_SET_Pos)                 /*!< SCB 
 821:Drivers/CMSIS/Include/core_cm7.h **** 
 822:Drivers/CMSIS/Include/core_cm7.h **** /* SCB D-Cache Clean by Set-way Register Definitions */
 823:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_DCCSW_WAY_Pos                  30U                                            /*!< SCB 
 824:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_DCCSW_WAY_Msk                  (3UL << SCB_DCCSW_WAY_Pos)                     /*!< SCB 
 825:Drivers/CMSIS/Include/core_cm7.h **** 
 826:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_DCCSW_SET_Pos                   5U                                            /*!< SCB 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 72


 827:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_DCCSW_SET_Msk                  (0x1FFUL << SCB_DCCSW_SET_Pos)                 /*!< SCB 
 828:Drivers/CMSIS/Include/core_cm7.h **** 
 829:Drivers/CMSIS/Include/core_cm7.h **** /* SCB D-Cache Clean and Invalidate by Set-way Register Definitions */
 830:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_DCCISW_WAY_Pos                 30U                                            /*!< SCB 
 831:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_DCCISW_WAY_Msk                 (3UL << SCB_DCCISW_WAY_Pos)                    /*!< SCB 
 832:Drivers/CMSIS/Include/core_cm7.h **** 
 833:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_DCCISW_SET_Pos                  5U                                            /*!< SCB 
 834:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_DCCISW_SET_Msk                 (0x1FFUL << SCB_DCCISW_SET_Pos)                /*!< SCB 
 835:Drivers/CMSIS/Include/core_cm7.h **** 
 836:Drivers/CMSIS/Include/core_cm7.h **** /* Instruction Tightly-Coupled Memory Control Register Definitions */
 837:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ITCMCR_SZ_Pos                   3U                                            /*!< SCB 
 838:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ITCMCR_SZ_Msk                  (0xFUL << SCB_ITCMCR_SZ_Pos)                   /*!< SCB 
 839:Drivers/CMSIS/Include/core_cm7.h **** 
 840:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ITCMCR_RETEN_Pos                2U                                            /*!< SCB 
 841:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ITCMCR_RETEN_Msk               (1UL << SCB_ITCMCR_RETEN_Pos)                  /*!< SCB 
 842:Drivers/CMSIS/Include/core_cm7.h **** 
 843:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ITCMCR_RMW_Pos                  1U                                            /*!< SCB 
 844:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ITCMCR_RMW_Msk                 (1UL << SCB_ITCMCR_RMW_Pos)                    /*!< SCB 
 845:Drivers/CMSIS/Include/core_cm7.h **** 
 846:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ITCMCR_EN_Pos                   0U                                            /*!< SCB 
 847:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ITCMCR_EN_Msk                  (1UL /*<< SCB_ITCMCR_EN_Pos*/)                 /*!< SCB 
 848:Drivers/CMSIS/Include/core_cm7.h **** 
 849:Drivers/CMSIS/Include/core_cm7.h **** /* Data Tightly-Coupled Memory Control Register Definitions */
 850:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_DTCMCR_SZ_Pos                   3U                                            /*!< SCB 
 851:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_DTCMCR_SZ_Msk                  (0xFUL << SCB_DTCMCR_SZ_Pos)                   /*!< SCB 
 852:Drivers/CMSIS/Include/core_cm7.h **** 
 853:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_DTCMCR_RETEN_Pos                2U                                            /*!< SCB 
 854:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_DTCMCR_RETEN_Msk               (1UL << SCB_DTCMCR_RETEN_Pos)                   /*!< SCB
 855:Drivers/CMSIS/Include/core_cm7.h **** 
 856:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_DTCMCR_RMW_Pos                  1U                                            /*!< SCB 
 857:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_DTCMCR_RMW_Msk                 (1UL << SCB_DTCMCR_RMW_Pos)                    /*!< SCB 
 858:Drivers/CMSIS/Include/core_cm7.h **** 
 859:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_DTCMCR_EN_Pos                   0U                                            /*!< SCB 
 860:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_DTCMCR_EN_Msk                  (1UL /*<< SCB_DTCMCR_EN_Pos*/)                 /*!< SCB 
 861:Drivers/CMSIS/Include/core_cm7.h **** 
 862:Drivers/CMSIS/Include/core_cm7.h **** /* AHBP Control Register Definitions */
 863:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_AHBPCR_SZ_Pos                   1U                                            /*!< SCB 
 864:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_AHBPCR_SZ_Msk                  (7UL << SCB_AHBPCR_SZ_Pos)                     /*!< SCB 
 865:Drivers/CMSIS/Include/core_cm7.h **** 
 866:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_AHBPCR_EN_Pos                   0U                                            /*!< SCB 
 867:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_AHBPCR_EN_Msk                  (1UL /*<< SCB_AHBPCR_EN_Pos*/)                 /*!< SCB 
 868:Drivers/CMSIS/Include/core_cm7.h **** 
 869:Drivers/CMSIS/Include/core_cm7.h **** /* L1 Cache Control Register Definitions */
 870:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CACR_FORCEWT_Pos                2U                                            /*!< SCB 
 871:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CACR_FORCEWT_Msk               (1UL << SCB_CACR_FORCEWT_Pos)                  /*!< SCB 
 872:Drivers/CMSIS/Include/core_cm7.h **** 
 873:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CACR_ECCEN_Pos                  1U                                            /*!< SCB 
 874:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CACR_ECCEN_Msk                 (1UL << SCB_CACR_ECCEN_Pos)                    /*!< SCB 
 875:Drivers/CMSIS/Include/core_cm7.h **** 
 876:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CACR_SIWT_Pos                   0U                                            /*!< SCB 
 877:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_CACR_SIWT_Msk                  (1UL /*<< SCB_CACR_SIWT_Pos*/)                 /*!< SCB 
 878:Drivers/CMSIS/Include/core_cm7.h **** 
 879:Drivers/CMSIS/Include/core_cm7.h **** /* AHBS Control Register Definitions */
 880:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_AHBSCR_INITCOUNT_Pos           11U                                            /*!< SCB 
 881:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_AHBSCR_INITCOUNT_Msk           (0x1FUL << SCB_AHBPCR_INITCOUNT_Pos)           /*!< SCB 
 882:Drivers/CMSIS/Include/core_cm7.h **** 
 883:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_AHBSCR_TPRI_Pos                 2U                                            /*!< SCB 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 73


 884:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_AHBSCR_TPRI_Msk                (0x1FFUL << SCB_AHBPCR_TPRI_Pos)               /*!< SCB 
 885:Drivers/CMSIS/Include/core_cm7.h **** 
 886:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_AHBSCR_CTL_Pos                  0U                                            /*!< SCB 
 887:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_AHBSCR_CTL_Msk                 (3UL /*<< SCB_AHBPCR_CTL_Pos*/)                /*!< SCB 
 888:Drivers/CMSIS/Include/core_cm7.h **** 
 889:Drivers/CMSIS/Include/core_cm7.h **** /* Auxiliary Bus Fault Status Register Definitions */
 890:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ABFSR_AXIMTYPE_Pos              8U                                            /*!< SCB 
 891:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ABFSR_AXIMTYPE_Msk             (3UL << SCB_ABFSR_AXIMTYPE_Pos)                /*!< SCB 
 892:Drivers/CMSIS/Include/core_cm7.h **** 
 893:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ABFSR_EPPB_Pos                  4U                                            /*!< SCB 
 894:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ABFSR_EPPB_Msk                 (1UL << SCB_ABFSR_EPPB_Pos)                    /*!< SCB 
 895:Drivers/CMSIS/Include/core_cm7.h **** 
 896:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ABFSR_AXIM_Pos                  3U                                            /*!< SCB 
 897:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ABFSR_AXIM_Msk                 (1UL << SCB_ABFSR_AXIM_Pos)                    /*!< SCB 
 898:Drivers/CMSIS/Include/core_cm7.h **** 
 899:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ABFSR_AHBP_Pos                  2U                                            /*!< SCB 
 900:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ABFSR_AHBP_Msk                 (1UL << SCB_ABFSR_AHBP_Pos)                    /*!< SCB 
 901:Drivers/CMSIS/Include/core_cm7.h **** 
 902:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ABFSR_DTCM_Pos                  1U                                            /*!< SCB 
 903:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ABFSR_DTCM_Msk                 (1UL << SCB_ABFSR_DTCM_Pos)                    /*!< SCB 
 904:Drivers/CMSIS/Include/core_cm7.h **** 
 905:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ABFSR_ITCM_Pos                  0U                                            /*!< SCB 
 906:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_ABFSR_ITCM_Msk                 (1UL /*<< SCB_ABFSR_ITCM_Pos*/)                /*!< SCB 
 907:Drivers/CMSIS/Include/core_cm7.h **** 
 908:Drivers/CMSIS/Include/core_cm7.h **** /*@} end of group CMSIS_SCB */
 909:Drivers/CMSIS/Include/core_cm7.h **** 
 910:Drivers/CMSIS/Include/core_cm7.h **** 
 911:Drivers/CMSIS/Include/core_cm7.h **** /**
 912:Drivers/CMSIS/Include/core_cm7.h ****   \ingroup  CMSIS_core_register
 913:Drivers/CMSIS/Include/core_cm7.h ****   \defgroup CMSIS_SCnSCB System Controls not in SCB (SCnSCB)
 914:Drivers/CMSIS/Include/core_cm7.h ****   \brief    Type definitions for the System Control and ID Register not in the SCB
 915:Drivers/CMSIS/Include/core_cm7.h ****   @{
 916:Drivers/CMSIS/Include/core_cm7.h ****  */
 917:Drivers/CMSIS/Include/core_cm7.h **** 
 918:Drivers/CMSIS/Include/core_cm7.h **** /**
 919:Drivers/CMSIS/Include/core_cm7.h ****   \brief  Structure type to access the System Control and ID Register not in the SCB.
 920:Drivers/CMSIS/Include/core_cm7.h ****  */
 921:Drivers/CMSIS/Include/core_cm7.h **** typedef struct
 922:Drivers/CMSIS/Include/core_cm7.h **** {
 923:Drivers/CMSIS/Include/core_cm7.h ****         uint32_t RESERVED0[1U];
 924:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t ICTR;                   /*!< Offset: 0x004 (R/ )  Interrupt Controller Type Regist
 925:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t ACTLR;                  /*!< Offset: 0x008 (R/W)  Auxiliary Control Register */
 926:Drivers/CMSIS/Include/core_cm7.h **** } SCnSCB_Type;
 927:Drivers/CMSIS/Include/core_cm7.h **** 
 928:Drivers/CMSIS/Include/core_cm7.h **** /* Interrupt Controller Type Register Definitions */
 929:Drivers/CMSIS/Include/core_cm7.h **** #define SCnSCB_ICTR_INTLINESNUM_Pos         0U                                         /*!< ICTR: I
 930:Drivers/CMSIS/Include/core_cm7.h **** #define SCnSCB_ICTR_INTLINESNUM_Msk        (0xFUL /*<< SCnSCB_ICTR_INTLINESNUM_Pos*/)  /*!< ICTR: I
 931:Drivers/CMSIS/Include/core_cm7.h **** 
 932:Drivers/CMSIS/Include/core_cm7.h **** /* Auxiliary Control Register Definitions */
 933:Drivers/CMSIS/Include/core_cm7.h **** #define SCnSCB_ACTLR_DISDYNADD_Pos         26U                                         /*!< ACTLR: 
 934:Drivers/CMSIS/Include/core_cm7.h **** #define SCnSCB_ACTLR_DISDYNADD_Msk         (1UL << SCnSCB_ACTLR_DISDYNADD_Pos)         /*!< ACTLR: 
 935:Drivers/CMSIS/Include/core_cm7.h **** 
 936:Drivers/CMSIS/Include/core_cm7.h **** #define SCnSCB_ACTLR_DISISSCH1_Pos         21U                                         /*!< ACTLR: 
 937:Drivers/CMSIS/Include/core_cm7.h **** #define SCnSCB_ACTLR_DISISSCH1_Msk         (0x1FUL << SCnSCB_ACTLR_DISISSCH1_Pos)      /*!< ACTLR: 
 938:Drivers/CMSIS/Include/core_cm7.h **** 
 939:Drivers/CMSIS/Include/core_cm7.h **** #define SCnSCB_ACTLR_DISDI_Pos             16U                                         /*!< ACTLR: 
 940:Drivers/CMSIS/Include/core_cm7.h **** #define SCnSCB_ACTLR_DISDI_Msk             (0x1FUL << SCnSCB_ACTLR_DISDI_Pos)          /*!< ACTLR: 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 74


 941:Drivers/CMSIS/Include/core_cm7.h **** 
 942:Drivers/CMSIS/Include/core_cm7.h **** #define SCnSCB_ACTLR_DISCRITAXIRUR_Pos     15U                                         /*!< ACTLR: 
 943:Drivers/CMSIS/Include/core_cm7.h **** #define SCnSCB_ACTLR_DISCRITAXIRUR_Msk     (1UL << SCnSCB_ACTLR_DISCRITAXIRUR_Pos)     /*!< ACTLR: 
 944:Drivers/CMSIS/Include/core_cm7.h **** 
 945:Drivers/CMSIS/Include/core_cm7.h **** #define SCnSCB_ACTLR_DISBTACALLOC_Pos      14U                                         /*!< ACTLR: 
 946:Drivers/CMSIS/Include/core_cm7.h **** #define SCnSCB_ACTLR_DISBTACALLOC_Msk      (1UL << SCnSCB_ACTLR_DISBTACALLOC_Pos)      /*!< ACTLR: 
 947:Drivers/CMSIS/Include/core_cm7.h **** 
 948:Drivers/CMSIS/Include/core_cm7.h **** #define SCnSCB_ACTLR_DISBTACREAD_Pos       13U                                         /*!< ACTLR: 
 949:Drivers/CMSIS/Include/core_cm7.h **** #define SCnSCB_ACTLR_DISBTACREAD_Msk       (1UL << SCnSCB_ACTLR_DISBTACREAD_Pos)       /*!< ACTLR: 
 950:Drivers/CMSIS/Include/core_cm7.h **** 
 951:Drivers/CMSIS/Include/core_cm7.h **** #define SCnSCB_ACTLR_DISITMATBFLUSH_Pos    12U                                         /*!< ACTLR: 
 952:Drivers/CMSIS/Include/core_cm7.h **** #define SCnSCB_ACTLR_DISITMATBFLUSH_Msk    (1UL << SCnSCB_ACTLR_DISITMATBFLUSH_Pos)    /*!< ACTLR: 
 953:Drivers/CMSIS/Include/core_cm7.h **** 
 954:Drivers/CMSIS/Include/core_cm7.h **** #define SCnSCB_ACTLR_DISRAMODE_Pos         11U                                         /*!< ACTLR: 
 955:Drivers/CMSIS/Include/core_cm7.h **** #define SCnSCB_ACTLR_DISRAMODE_Msk         (1UL << SCnSCB_ACTLR_DISRAMODE_Pos)         /*!< ACTLR: 
 956:Drivers/CMSIS/Include/core_cm7.h **** 
 957:Drivers/CMSIS/Include/core_cm7.h **** #define SCnSCB_ACTLR_FPEXCODIS_Pos         10U                                         /*!< ACTLR: 
 958:Drivers/CMSIS/Include/core_cm7.h **** #define SCnSCB_ACTLR_FPEXCODIS_Msk         (1UL << SCnSCB_ACTLR_FPEXCODIS_Pos)         /*!< ACTLR: 
 959:Drivers/CMSIS/Include/core_cm7.h **** 
 960:Drivers/CMSIS/Include/core_cm7.h **** #define SCnSCB_ACTLR_DISFOLD_Pos            2U                                         /*!< ACTLR: 
 961:Drivers/CMSIS/Include/core_cm7.h **** #define SCnSCB_ACTLR_DISFOLD_Msk           (1UL << SCnSCB_ACTLR_DISFOLD_Pos)           /*!< ACTLR: 
 962:Drivers/CMSIS/Include/core_cm7.h **** 
 963:Drivers/CMSIS/Include/core_cm7.h **** #define SCnSCB_ACTLR_DISMCYCINT_Pos         0U                                         /*!< ACTLR: 
 964:Drivers/CMSIS/Include/core_cm7.h **** #define SCnSCB_ACTLR_DISMCYCINT_Msk        (1UL /*<< SCnSCB_ACTLR_DISMCYCINT_Pos*/)    /*!< ACTLR: 
 965:Drivers/CMSIS/Include/core_cm7.h **** 
 966:Drivers/CMSIS/Include/core_cm7.h **** /*@} end of group CMSIS_SCnotSCB */
 967:Drivers/CMSIS/Include/core_cm7.h **** 
 968:Drivers/CMSIS/Include/core_cm7.h **** 
 969:Drivers/CMSIS/Include/core_cm7.h **** /**
 970:Drivers/CMSIS/Include/core_cm7.h ****   \ingroup  CMSIS_core_register
 971:Drivers/CMSIS/Include/core_cm7.h ****   \defgroup CMSIS_SysTick     System Tick Timer (SysTick)
 972:Drivers/CMSIS/Include/core_cm7.h ****   \brief    Type definitions for the System Timer Registers.
 973:Drivers/CMSIS/Include/core_cm7.h ****   @{
 974:Drivers/CMSIS/Include/core_cm7.h ****  */
 975:Drivers/CMSIS/Include/core_cm7.h **** 
 976:Drivers/CMSIS/Include/core_cm7.h **** /**
 977:Drivers/CMSIS/Include/core_cm7.h ****   \brief  Structure type to access the System Timer (SysTick).
 978:Drivers/CMSIS/Include/core_cm7.h ****  */
 979:Drivers/CMSIS/Include/core_cm7.h **** typedef struct
 980:Drivers/CMSIS/Include/core_cm7.h **** {
 981:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t CTRL;                   /*!< Offset: 0x000 (R/W)  SysTick Control and Status Regis
 982:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t LOAD;                   /*!< Offset: 0x004 (R/W)  SysTick Reload Value Register */
 983:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t VAL;                    /*!< Offset: 0x008 (R/W)  SysTick Current Value Register *
 984:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t CALIB;                  /*!< Offset: 0x00C (R/ )  SysTick Calibration Register */
 985:Drivers/CMSIS/Include/core_cm7.h **** } SysTick_Type;
 986:Drivers/CMSIS/Include/core_cm7.h **** 
 987:Drivers/CMSIS/Include/core_cm7.h **** /* SysTick Control / Status Register Definitions */
 988:Drivers/CMSIS/Include/core_cm7.h **** #define SysTick_CTRL_COUNTFLAG_Pos         16U                                            /*!< SysT
 989:Drivers/CMSIS/Include/core_cm7.h **** #define SysTick_CTRL_COUNTFLAG_Msk         (1UL << SysTick_CTRL_COUNTFLAG_Pos)            /*!< SysT
 990:Drivers/CMSIS/Include/core_cm7.h **** 
 991:Drivers/CMSIS/Include/core_cm7.h **** #define SysTick_CTRL_CLKSOURCE_Pos          2U                                            /*!< SysT
 992:Drivers/CMSIS/Include/core_cm7.h **** #define SysTick_CTRL_CLKSOURCE_Msk         (1UL << SysTick_CTRL_CLKSOURCE_Pos)            /*!< SysT
 993:Drivers/CMSIS/Include/core_cm7.h **** 
 994:Drivers/CMSIS/Include/core_cm7.h **** #define SysTick_CTRL_TICKINT_Pos            1U                                            /*!< SysT
 995:Drivers/CMSIS/Include/core_cm7.h **** #define SysTick_CTRL_TICKINT_Msk           (1UL << SysTick_CTRL_TICKINT_Pos)              /*!< SysT
 996:Drivers/CMSIS/Include/core_cm7.h **** 
 997:Drivers/CMSIS/Include/core_cm7.h **** #define SysTick_CTRL_ENABLE_Pos             0U                                            /*!< SysT
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 75


 998:Drivers/CMSIS/Include/core_cm7.h **** #define SysTick_CTRL_ENABLE_Msk            (1UL /*<< SysTick_CTRL_ENABLE_Pos*/)           /*!< SysT
 999:Drivers/CMSIS/Include/core_cm7.h **** 
1000:Drivers/CMSIS/Include/core_cm7.h **** /* SysTick Reload Register Definitions */
1001:Drivers/CMSIS/Include/core_cm7.h **** #define SysTick_LOAD_RELOAD_Pos             0U                                            /*!< SysT
1002:Drivers/CMSIS/Include/core_cm7.h **** #define SysTick_LOAD_RELOAD_Msk            (0xFFFFFFUL /*<< SysTick_LOAD_RELOAD_Pos*/)    /*!< SysT
1003:Drivers/CMSIS/Include/core_cm7.h **** 
1004:Drivers/CMSIS/Include/core_cm7.h **** /* SysTick Current Register Definitions */
1005:Drivers/CMSIS/Include/core_cm7.h **** #define SysTick_VAL_CURRENT_Pos             0U                                            /*!< SysT
1006:Drivers/CMSIS/Include/core_cm7.h **** #define SysTick_VAL_CURRENT_Msk            (0xFFFFFFUL /*<< SysTick_VAL_CURRENT_Pos*/)    /*!< SysT
1007:Drivers/CMSIS/Include/core_cm7.h **** 
1008:Drivers/CMSIS/Include/core_cm7.h **** /* SysTick Calibration Register Definitions */
1009:Drivers/CMSIS/Include/core_cm7.h **** #define SysTick_CALIB_NOREF_Pos            31U                                            /*!< SysT
1010:Drivers/CMSIS/Include/core_cm7.h **** #define SysTick_CALIB_NOREF_Msk            (1UL << SysTick_CALIB_NOREF_Pos)               /*!< SysT
1011:Drivers/CMSIS/Include/core_cm7.h **** 
1012:Drivers/CMSIS/Include/core_cm7.h **** #define SysTick_CALIB_SKEW_Pos             30U                                            /*!< SysT
1013:Drivers/CMSIS/Include/core_cm7.h **** #define SysTick_CALIB_SKEW_Msk             (1UL << SysTick_CALIB_SKEW_Pos)                /*!< SysT
1014:Drivers/CMSIS/Include/core_cm7.h **** 
1015:Drivers/CMSIS/Include/core_cm7.h **** #define SysTick_CALIB_TENMS_Pos             0U                                            /*!< SysT
1016:Drivers/CMSIS/Include/core_cm7.h **** #define SysTick_CALIB_TENMS_Msk            (0xFFFFFFUL /*<< SysTick_CALIB_TENMS_Pos*/)    /*!< SysT
1017:Drivers/CMSIS/Include/core_cm7.h **** 
1018:Drivers/CMSIS/Include/core_cm7.h **** /*@} end of group CMSIS_SysTick */
1019:Drivers/CMSIS/Include/core_cm7.h **** 
1020:Drivers/CMSIS/Include/core_cm7.h **** 
1021:Drivers/CMSIS/Include/core_cm7.h **** /**
1022:Drivers/CMSIS/Include/core_cm7.h ****   \ingroup  CMSIS_core_register
1023:Drivers/CMSIS/Include/core_cm7.h ****   \defgroup CMSIS_ITM     Instrumentation Trace Macrocell (ITM)
1024:Drivers/CMSIS/Include/core_cm7.h ****   \brief    Type definitions for the Instrumentation Trace Macrocell (ITM)
1025:Drivers/CMSIS/Include/core_cm7.h ****   @{
1026:Drivers/CMSIS/Include/core_cm7.h ****  */
1027:Drivers/CMSIS/Include/core_cm7.h **** 
1028:Drivers/CMSIS/Include/core_cm7.h **** /**
1029:Drivers/CMSIS/Include/core_cm7.h ****   \brief  Structure type to access the Instrumentation Trace Macrocell Register (ITM).
1030:Drivers/CMSIS/Include/core_cm7.h ****  */
1031:Drivers/CMSIS/Include/core_cm7.h **** typedef struct
1032:Drivers/CMSIS/Include/core_cm7.h **** {
1033:Drivers/CMSIS/Include/core_cm7.h ****   __OM  union
1034:Drivers/CMSIS/Include/core_cm7.h ****   {
1035:Drivers/CMSIS/Include/core_cm7.h ****     __OM  uint8_t    u8;                 /*!< Offset: 0x000 ( /W)  ITM Stimulus Port 8-bit */
1036:Drivers/CMSIS/Include/core_cm7.h ****     __OM  uint16_t   u16;                /*!< Offset: 0x000 ( /W)  ITM Stimulus Port 16-bit */
1037:Drivers/CMSIS/Include/core_cm7.h ****     __OM  uint32_t   u32;                /*!< Offset: 0x000 ( /W)  ITM Stimulus Port 32-bit */
1038:Drivers/CMSIS/Include/core_cm7.h ****   }  PORT [32U];                         /*!< Offset: 0x000 ( /W)  ITM Stimulus Port Registers */
1039:Drivers/CMSIS/Include/core_cm7.h ****         uint32_t RESERVED0[864U];
1040:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t TER;                    /*!< Offset: 0xE00 (R/W)  ITM Trace Enable Register */
1041:Drivers/CMSIS/Include/core_cm7.h ****         uint32_t RESERVED1[15U];
1042:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t TPR;                    /*!< Offset: 0xE40 (R/W)  ITM Trace Privilege Register */
1043:Drivers/CMSIS/Include/core_cm7.h ****         uint32_t RESERVED2[15U];
1044:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t TCR;                    /*!< Offset: 0xE80 (R/W)  ITM Trace Control Register */
1045:Drivers/CMSIS/Include/core_cm7.h ****         uint32_t RESERVED3[32U];
1046:Drivers/CMSIS/Include/core_cm7.h ****         uint32_t RESERVED4[43U];
1047:Drivers/CMSIS/Include/core_cm7.h ****   __OM  uint32_t LAR;                    /*!< Offset: 0xFB0 ( /W)  ITM Lock Access Register */
1048:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t LSR;                    /*!< Offset: 0xFB4 (R/ )  ITM Lock Status Register */
1049:Drivers/CMSIS/Include/core_cm7.h ****         uint32_t RESERVED5[6U];
1050:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t PID4;                   /*!< Offset: 0xFD0 (R/ )  ITM Peripheral Identification Re
1051:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t PID5;                   /*!< Offset: 0xFD4 (R/ )  ITM Peripheral Identification Re
1052:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t PID6;                   /*!< Offset: 0xFD8 (R/ )  ITM Peripheral Identification Re
1053:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t PID7;                   /*!< Offset: 0xFDC (R/ )  ITM Peripheral Identification Re
1054:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t PID0;                   /*!< Offset: 0xFE0 (R/ )  ITM Peripheral Identification Re
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 76


1055:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t PID1;                   /*!< Offset: 0xFE4 (R/ )  ITM Peripheral Identification Re
1056:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t PID2;                   /*!< Offset: 0xFE8 (R/ )  ITM Peripheral Identification Re
1057:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t PID3;                   /*!< Offset: 0xFEC (R/ )  ITM Peripheral Identification Re
1058:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t CID0;                   /*!< Offset: 0xFF0 (R/ )  ITM Component  Identification Re
1059:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t CID1;                   /*!< Offset: 0xFF4 (R/ )  ITM Component  Identification Re
1060:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t CID2;                   /*!< Offset: 0xFF8 (R/ )  ITM Component  Identification Re
1061:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t CID3;                   /*!< Offset: 0xFFC (R/ )  ITM Component  Identification Re
1062:Drivers/CMSIS/Include/core_cm7.h **** } ITM_Type;
1063:Drivers/CMSIS/Include/core_cm7.h **** 
1064:Drivers/CMSIS/Include/core_cm7.h **** /* ITM Trace Privilege Register Definitions */
1065:Drivers/CMSIS/Include/core_cm7.h **** #define ITM_TPR_PRIVMASK_Pos                0U                                            /*!< ITM 
1066:Drivers/CMSIS/Include/core_cm7.h **** #define ITM_TPR_PRIVMASK_Msk               (0xFFFFFFFFUL /*<< ITM_TPR_PRIVMASK_Pos*/)     /*!< ITM 
1067:Drivers/CMSIS/Include/core_cm7.h **** 
1068:Drivers/CMSIS/Include/core_cm7.h **** /* ITM Trace Control Register Definitions */
1069:Drivers/CMSIS/Include/core_cm7.h **** #define ITM_TCR_BUSY_Pos                   23U                                            /*!< ITM 
1070:Drivers/CMSIS/Include/core_cm7.h **** #define ITM_TCR_BUSY_Msk                   (1UL << ITM_TCR_BUSY_Pos)                      /*!< ITM 
1071:Drivers/CMSIS/Include/core_cm7.h **** 
1072:Drivers/CMSIS/Include/core_cm7.h **** #define ITM_TCR_TraceBusID_Pos             16U                                            /*!< ITM 
1073:Drivers/CMSIS/Include/core_cm7.h **** #define ITM_TCR_TraceBusID_Msk             (0x7FUL << ITM_TCR_TraceBusID_Pos)             /*!< ITM 
1074:Drivers/CMSIS/Include/core_cm7.h **** 
1075:Drivers/CMSIS/Include/core_cm7.h **** #define ITM_TCR_GTSFREQ_Pos                10U                                            /*!< ITM 
1076:Drivers/CMSIS/Include/core_cm7.h **** #define ITM_TCR_GTSFREQ_Msk                (3UL << ITM_TCR_GTSFREQ_Pos)                   /*!< ITM 
1077:Drivers/CMSIS/Include/core_cm7.h **** 
1078:Drivers/CMSIS/Include/core_cm7.h **** #define ITM_TCR_TSPrescale_Pos              8U                                            /*!< ITM 
1079:Drivers/CMSIS/Include/core_cm7.h **** #define ITM_TCR_TSPrescale_Msk             (3UL << ITM_TCR_TSPrescale_Pos)                /*!< ITM 
1080:Drivers/CMSIS/Include/core_cm7.h **** 
1081:Drivers/CMSIS/Include/core_cm7.h **** #define ITM_TCR_SWOENA_Pos                  4U                                            /*!< ITM 
1082:Drivers/CMSIS/Include/core_cm7.h **** #define ITM_TCR_SWOENA_Msk                 (1UL << ITM_TCR_SWOENA_Pos)                    /*!< ITM 
1083:Drivers/CMSIS/Include/core_cm7.h **** 
1084:Drivers/CMSIS/Include/core_cm7.h **** #define ITM_TCR_DWTENA_Pos                  3U                                            /*!< ITM 
1085:Drivers/CMSIS/Include/core_cm7.h **** #define ITM_TCR_DWTENA_Msk                 (1UL << ITM_TCR_DWTENA_Pos)                    /*!< ITM 
1086:Drivers/CMSIS/Include/core_cm7.h **** 
1087:Drivers/CMSIS/Include/core_cm7.h **** #define ITM_TCR_SYNCENA_Pos                 2U                                            /*!< ITM 
1088:Drivers/CMSIS/Include/core_cm7.h **** #define ITM_TCR_SYNCENA_Msk                (1UL << ITM_TCR_SYNCENA_Pos)                   /*!< ITM 
1089:Drivers/CMSIS/Include/core_cm7.h **** 
1090:Drivers/CMSIS/Include/core_cm7.h **** #define ITM_TCR_TSENA_Pos                   1U                                            /*!< ITM 
1091:Drivers/CMSIS/Include/core_cm7.h **** #define ITM_TCR_TSENA_Msk                  (1UL << ITM_TCR_TSENA_Pos)                     /*!< ITM 
1092:Drivers/CMSIS/Include/core_cm7.h **** 
1093:Drivers/CMSIS/Include/core_cm7.h **** #define ITM_TCR_ITMENA_Pos                  0U                                            /*!< ITM 
1094:Drivers/CMSIS/Include/core_cm7.h **** #define ITM_TCR_ITMENA_Msk                 (1UL /*<< ITM_TCR_ITMENA_Pos*/)                /*!< ITM 
1095:Drivers/CMSIS/Include/core_cm7.h **** 
1096:Drivers/CMSIS/Include/core_cm7.h **** /* ITM Lock Status Register Definitions */
1097:Drivers/CMSIS/Include/core_cm7.h **** #define ITM_LSR_ByteAcc_Pos                 2U                                            /*!< ITM 
1098:Drivers/CMSIS/Include/core_cm7.h **** #define ITM_LSR_ByteAcc_Msk                (1UL << ITM_LSR_ByteAcc_Pos)                   /*!< ITM 
1099:Drivers/CMSIS/Include/core_cm7.h **** 
1100:Drivers/CMSIS/Include/core_cm7.h **** #define ITM_LSR_Access_Pos                  1U                                            /*!< ITM 
1101:Drivers/CMSIS/Include/core_cm7.h **** #define ITM_LSR_Access_Msk                 (1UL << ITM_LSR_Access_Pos)                    /*!< ITM 
1102:Drivers/CMSIS/Include/core_cm7.h **** 
1103:Drivers/CMSIS/Include/core_cm7.h **** #define ITM_LSR_Present_Pos                 0U                                            /*!< ITM 
1104:Drivers/CMSIS/Include/core_cm7.h **** #define ITM_LSR_Present_Msk                (1UL /*<< ITM_LSR_Present_Pos*/)               /*!< ITM 
1105:Drivers/CMSIS/Include/core_cm7.h **** 
1106:Drivers/CMSIS/Include/core_cm7.h **** /*@}*/ /* end of group CMSIS_ITM */
1107:Drivers/CMSIS/Include/core_cm7.h **** 
1108:Drivers/CMSIS/Include/core_cm7.h **** 
1109:Drivers/CMSIS/Include/core_cm7.h **** /**
1110:Drivers/CMSIS/Include/core_cm7.h ****   \ingroup  CMSIS_core_register
1111:Drivers/CMSIS/Include/core_cm7.h ****   \defgroup CMSIS_DWT     Data Watchpoint and Trace (DWT)
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 77


1112:Drivers/CMSIS/Include/core_cm7.h ****   \brief    Type definitions for the Data Watchpoint and Trace (DWT)
1113:Drivers/CMSIS/Include/core_cm7.h ****   @{
1114:Drivers/CMSIS/Include/core_cm7.h ****  */
1115:Drivers/CMSIS/Include/core_cm7.h **** 
1116:Drivers/CMSIS/Include/core_cm7.h **** /**
1117:Drivers/CMSIS/Include/core_cm7.h ****   \brief  Structure type to access the Data Watchpoint and Trace Register (DWT).
1118:Drivers/CMSIS/Include/core_cm7.h ****  */
1119:Drivers/CMSIS/Include/core_cm7.h **** typedef struct
1120:Drivers/CMSIS/Include/core_cm7.h **** {
1121:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t CTRL;                   /*!< Offset: 0x000 (R/W)  Control Register */
1122:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t CYCCNT;                 /*!< Offset: 0x004 (R/W)  Cycle Count Register */
1123:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t CPICNT;                 /*!< Offset: 0x008 (R/W)  CPI Count Register */
1124:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t EXCCNT;                 /*!< Offset: 0x00C (R/W)  Exception Overhead Count Registe
1125:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t SLEEPCNT;               /*!< Offset: 0x010 (R/W)  Sleep Count Register */
1126:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t LSUCNT;                 /*!< Offset: 0x014 (R/W)  LSU Count Register */
1127:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t FOLDCNT;                /*!< Offset: 0x018 (R/W)  Folded-instruction Count Registe
1128:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t PCSR;                   /*!< Offset: 0x01C (R/ )  Program Counter Sample Register 
1129:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t COMP0;                  /*!< Offset: 0x020 (R/W)  Comparator Register 0 */
1130:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t MASK0;                  /*!< Offset: 0x024 (R/W)  Mask Register 0 */
1131:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t FUNCTION0;              /*!< Offset: 0x028 (R/W)  Function Register 0 */
1132:Drivers/CMSIS/Include/core_cm7.h ****         uint32_t RESERVED0[1U];
1133:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t COMP1;                  /*!< Offset: 0x030 (R/W)  Comparator Register 1 */
1134:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t MASK1;                  /*!< Offset: 0x034 (R/W)  Mask Register 1 */
1135:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t FUNCTION1;              /*!< Offset: 0x038 (R/W)  Function Register 1 */
1136:Drivers/CMSIS/Include/core_cm7.h ****         uint32_t RESERVED1[1U];
1137:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t COMP2;                  /*!< Offset: 0x040 (R/W)  Comparator Register 2 */
1138:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t MASK2;                  /*!< Offset: 0x044 (R/W)  Mask Register 2 */
1139:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t FUNCTION2;              /*!< Offset: 0x048 (R/W)  Function Register 2 */
1140:Drivers/CMSIS/Include/core_cm7.h ****         uint32_t RESERVED2[1U];
1141:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t COMP3;                  /*!< Offset: 0x050 (R/W)  Comparator Register 3 */
1142:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t MASK3;                  /*!< Offset: 0x054 (R/W)  Mask Register 3 */
1143:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t FUNCTION3;              /*!< Offset: 0x058 (R/W)  Function Register 3 */
1144:Drivers/CMSIS/Include/core_cm7.h ****         uint32_t RESERVED3[981U];
1145:Drivers/CMSIS/Include/core_cm7.h ****   __OM  uint32_t LAR;                    /*!< Offset: 0xFB0 (  W)  Lock Access Register */
1146:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t LSR;                    /*!< Offset: 0xFB4 (R  )  Lock Status Register */
1147:Drivers/CMSIS/Include/core_cm7.h **** } DWT_Type;
1148:Drivers/CMSIS/Include/core_cm7.h **** 
1149:Drivers/CMSIS/Include/core_cm7.h **** /* DWT Control Register Definitions */
1150:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_CTRL_NUMCOMP_Pos               28U                                         /*!< DWT CTR
1151:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_CTRL_NUMCOMP_Msk               (0xFUL << DWT_CTRL_NUMCOMP_Pos)             /*!< DWT CTR
1152:Drivers/CMSIS/Include/core_cm7.h **** 
1153:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_CTRL_NOTRCPKT_Pos              27U                                         /*!< DWT CTR
1154:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_CTRL_NOTRCPKT_Msk              (0x1UL << DWT_CTRL_NOTRCPKT_Pos)            /*!< DWT CTR
1155:Drivers/CMSIS/Include/core_cm7.h **** 
1156:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_CTRL_NOEXTTRIG_Pos             26U                                         /*!< DWT CTR
1157:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_CTRL_NOEXTTRIG_Msk             (0x1UL << DWT_CTRL_NOEXTTRIG_Pos)           /*!< DWT CTR
1158:Drivers/CMSIS/Include/core_cm7.h **** 
1159:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_CTRL_NOCYCCNT_Pos              25U                                         /*!< DWT CTR
1160:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_CTRL_NOCYCCNT_Msk              (0x1UL << DWT_CTRL_NOCYCCNT_Pos)            /*!< DWT CTR
1161:Drivers/CMSIS/Include/core_cm7.h **** 
1162:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_CTRL_NOPRFCNT_Pos              24U                                         /*!< DWT CTR
1163:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_CTRL_NOPRFCNT_Msk              (0x1UL << DWT_CTRL_NOPRFCNT_Pos)            /*!< DWT CTR
1164:Drivers/CMSIS/Include/core_cm7.h **** 
1165:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_CTRL_CYCEVTENA_Pos             22U                                         /*!< DWT CTR
1166:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_CTRL_CYCEVTENA_Msk             (0x1UL << DWT_CTRL_CYCEVTENA_Pos)           /*!< DWT CTR
1167:Drivers/CMSIS/Include/core_cm7.h **** 
1168:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_CTRL_FOLDEVTENA_Pos            21U                                         /*!< DWT CTR
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 78


1169:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_CTRL_FOLDEVTENA_Msk            (0x1UL << DWT_CTRL_FOLDEVTENA_Pos)          /*!< DWT CTR
1170:Drivers/CMSIS/Include/core_cm7.h **** 
1171:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_CTRL_LSUEVTENA_Pos             20U                                         /*!< DWT CTR
1172:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_CTRL_LSUEVTENA_Msk             (0x1UL << DWT_CTRL_LSUEVTENA_Pos)           /*!< DWT CTR
1173:Drivers/CMSIS/Include/core_cm7.h **** 
1174:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_CTRL_SLEEPEVTENA_Pos           19U                                         /*!< DWT CTR
1175:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_CTRL_SLEEPEVTENA_Msk           (0x1UL << DWT_CTRL_SLEEPEVTENA_Pos)         /*!< DWT CTR
1176:Drivers/CMSIS/Include/core_cm7.h **** 
1177:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_CTRL_EXCEVTENA_Pos             18U                                         /*!< DWT CTR
1178:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_CTRL_EXCEVTENA_Msk             (0x1UL << DWT_CTRL_EXCEVTENA_Pos)           /*!< DWT CTR
1179:Drivers/CMSIS/Include/core_cm7.h **** 
1180:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_CTRL_CPIEVTENA_Pos             17U                                         /*!< DWT CTR
1181:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_CTRL_CPIEVTENA_Msk             (0x1UL << DWT_CTRL_CPIEVTENA_Pos)           /*!< DWT CTR
1182:Drivers/CMSIS/Include/core_cm7.h **** 
1183:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_CTRL_EXCTRCENA_Pos             16U                                         /*!< DWT CTR
1184:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_CTRL_EXCTRCENA_Msk             (0x1UL << DWT_CTRL_EXCTRCENA_Pos)           /*!< DWT CTR
1185:Drivers/CMSIS/Include/core_cm7.h **** 
1186:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_CTRL_PCSAMPLENA_Pos            12U                                         /*!< DWT CTR
1187:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_CTRL_PCSAMPLENA_Msk            (0x1UL << DWT_CTRL_PCSAMPLENA_Pos)          /*!< DWT CTR
1188:Drivers/CMSIS/Include/core_cm7.h **** 
1189:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_CTRL_SYNCTAP_Pos               10U                                         /*!< DWT CTR
1190:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_CTRL_SYNCTAP_Msk               (0x3UL << DWT_CTRL_SYNCTAP_Pos)             /*!< DWT CTR
1191:Drivers/CMSIS/Include/core_cm7.h **** 
1192:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_CTRL_CYCTAP_Pos                 9U                                         /*!< DWT CTR
1193:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_CTRL_CYCTAP_Msk                (0x1UL << DWT_CTRL_CYCTAP_Pos)              /*!< DWT CTR
1194:Drivers/CMSIS/Include/core_cm7.h **** 
1195:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_CTRL_POSTINIT_Pos               5U                                         /*!< DWT CTR
1196:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_CTRL_POSTINIT_Msk              (0xFUL << DWT_CTRL_POSTINIT_Pos)            /*!< DWT CTR
1197:Drivers/CMSIS/Include/core_cm7.h **** 
1198:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_CTRL_POSTPRESET_Pos             1U                                         /*!< DWT CTR
1199:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_CTRL_POSTPRESET_Msk            (0xFUL << DWT_CTRL_POSTPRESET_Pos)          /*!< DWT CTR
1200:Drivers/CMSIS/Include/core_cm7.h **** 
1201:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_CTRL_CYCCNTENA_Pos              0U                                         /*!< DWT CTR
1202:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_CTRL_CYCCNTENA_Msk             (0x1UL /*<< DWT_CTRL_CYCCNTENA_Pos*/)       /*!< DWT CTR
1203:Drivers/CMSIS/Include/core_cm7.h **** 
1204:Drivers/CMSIS/Include/core_cm7.h **** /* DWT CPI Count Register Definitions */
1205:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_CPICNT_CPICNT_Pos               0U                                         /*!< DWT CPI
1206:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_CPICNT_CPICNT_Msk              (0xFFUL /*<< DWT_CPICNT_CPICNT_Pos*/)       /*!< DWT CPI
1207:Drivers/CMSIS/Include/core_cm7.h **** 
1208:Drivers/CMSIS/Include/core_cm7.h **** /* DWT Exception Overhead Count Register Definitions */
1209:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_EXCCNT_EXCCNT_Pos               0U                                         /*!< DWT EXC
1210:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_EXCCNT_EXCCNT_Msk              (0xFFUL /*<< DWT_EXCCNT_EXCCNT_Pos*/)       /*!< DWT EXC
1211:Drivers/CMSIS/Include/core_cm7.h **** 
1212:Drivers/CMSIS/Include/core_cm7.h **** /* DWT Sleep Count Register Definitions */
1213:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_SLEEPCNT_SLEEPCNT_Pos           0U                                         /*!< DWT SLE
1214:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_SLEEPCNT_SLEEPCNT_Msk          (0xFFUL /*<< DWT_SLEEPCNT_SLEEPCNT_Pos*/)   /*!< DWT SLE
1215:Drivers/CMSIS/Include/core_cm7.h **** 
1216:Drivers/CMSIS/Include/core_cm7.h **** /* DWT LSU Count Register Definitions */
1217:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_LSUCNT_LSUCNT_Pos               0U                                         /*!< DWT LSU
1218:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_LSUCNT_LSUCNT_Msk              (0xFFUL /*<< DWT_LSUCNT_LSUCNT_Pos*/)       /*!< DWT LSU
1219:Drivers/CMSIS/Include/core_cm7.h **** 
1220:Drivers/CMSIS/Include/core_cm7.h **** /* DWT Folded-instruction Count Register Definitions */
1221:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_FOLDCNT_FOLDCNT_Pos             0U                                         /*!< DWT FOL
1222:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_FOLDCNT_FOLDCNT_Msk            (0xFFUL /*<< DWT_FOLDCNT_FOLDCNT_Pos*/)     /*!< DWT FOL
1223:Drivers/CMSIS/Include/core_cm7.h **** 
1224:Drivers/CMSIS/Include/core_cm7.h **** /* DWT Comparator Mask Register Definitions */
1225:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_MASK_MASK_Pos                   0U                                         /*!< DWT MAS
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 79


1226:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_MASK_MASK_Msk                  (0x1FUL /*<< DWT_MASK_MASK_Pos*/)           /*!< DWT MAS
1227:Drivers/CMSIS/Include/core_cm7.h **** 
1228:Drivers/CMSIS/Include/core_cm7.h **** /* DWT Comparator Function Register Definitions */
1229:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_FUNCTION_MATCHED_Pos           24U                                         /*!< DWT FUN
1230:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_FUNCTION_MATCHED_Msk           (0x1UL << DWT_FUNCTION_MATCHED_Pos)         /*!< DWT FUN
1231:Drivers/CMSIS/Include/core_cm7.h **** 
1232:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_FUNCTION_DATAVADDR1_Pos        16U                                         /*!< DWT FUN
1233:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_FUNCTION_DATAVADDR1_Msk        (0xFUL << DWT_FUNCTION_DATAVADDR1_Pos)      /*!< DWT FUN
1234:Drivers/CMSIS/Include/core_cm7.h **** 
1235:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_FUNCTION_DATAVADDR0_Pos        12U                                         /*!< DWT FUN
1236:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_FUNCTION_DATAVADDR0_Msk        (0xFUL << DWT_FUNCTION_DATAVADDR0_Pos)      /*!< DWT FUN
1237:Drivers/CMSIS/Include/core_cm7.h **** 
1238:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_FUNCTION_DATAVSIZE_Pos         10U                                         /*!< DWT FUN
1239:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_FUNCTION_DATAVSIZE_Msk         (0x3UL << DWT_FUNCTION_DATAVSIZE_Pos)       /*!< DWT FUN
1240:Drivers/CMSIS/Include/core_cm7.h **** 
1241:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_FUNCTION_LNK1ENA_Pos            9U                                         /*!< DWT FUN
1242:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_FUNCTION_LNK1ENA_Msk           (0x1UL << DWT_FUNCTION_LNK1ENA_Pos)         /*!< DWT FUN
1243:Drivers/CMSIS/Include/core_cm7.h **** 
1244:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_FUNCTION_DATAVMATCH_Pos         8U                                         /*!< DWT FUN
1245:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_FUNCTION_DATAVMATCH_Msk        (0x1UL << DWT_FUNCTION_DATAVMATCH_Pos)      /*!< DWT FUN
1246:Drivers/CMSIS/Include/core_cm7.h **** 
1247:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_FUNCTION_CYCMATCH_Pos           7U                                         /*!< DWT FUN
1248:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_FUNCTION_CYCMATCH_Msk          (0x1UL << DWT_FUNCTION_CYCMATCH_Pos)        /*!< DWT FUN
1249:Drivers/CMSIS/Include/core_cm7.h **** 
1250:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_FUNCTION_EMITRANGE_Pos          5U                                         /*!< DWT FUN
1251:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_FUNCTION_EMITRANGE_Msk         (0x1UL << DWT_FUNCTION_EMITRANGE_Pos)       /*!< DWT FUN
1252:Drivers/CMSIS/Include/core_cm7.h **** 
1253:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_FUNCTION_FUNCTION_Pos           0U                                         /*!< DWT FUN
1254:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_FUNCTION_FUNCTION_Msk          (0xFUL /*<< DWT_FUNCTION_FUNCTION_Pos*/)    /*!< DWT FUN
1255:Drivers/CMSIS/Include/core_cm7.h **** 
1256:Drivers/CMSIS/Include/core_cm7.h **** /*@}*/ /* end of group CMSIS_DWT */
1257:Drivers/CMSIS/Include/core_cm7.h **** 
1258:Drivers/CMSIS/Include/core_cm7.h **** 
1259:Drivers/CMSIS/Include/core_cm7.h **** /**
1260:Drivers/CMSIS/Include/core_cm7.h ****   \ingroup  CMSIS_core_register
1261:Drivers/CMSIS/Include/core_cm7.h ****   \defgroup CMSIS_TPI     Trace Port Interface (TPI)
1262:Drivers/CMSIS/Include/core_cm7.h ****   \brief    Type definitions for the Trace Port Interface (TPI)
1263:Drivers/CMSIS/Include/core_cm7.h ****   @{
1264:Drivers/CMSIS/Include/core_cm7.h ****  */
1265:Drivers/CMSIS/Include/core_cm7.h **** 
1266:Drivers/CMSIS/Include/core_cm7.h **** /**
1267:Drivers/CMSIS/Include/core_cm7.h ****   \brief  Structure type to access the Trace Port Interface Register (TPI).
1268:Drivers/CMSIS/Include/core_cm7.h ****  */
1269:Drivers/CMSIS/Include/core_cm7.h **** typedef struct
1270:Drivers/CMSIS/Include/core_cm7.h **** {
1271:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t SSPSR;                  /*!< Offset: 0x000 (R/ )  Supported Parallel Port Size Reg
1272:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t CSPSR;                  /*!< Offset: 0x004 (R/W)  Current Parallel Port Size Regis
1273:Drivers/CMSIS/Include/core_cm7.h ****         uint32_t RESERVED0[2U];
1274:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t ACPR;                   /*!< Offset: 0x010 (R/W)  Asynchronous Clock Prescaler Reg
1275:Drivers/CMSIS/Include/core_cm7.h ****         uint32_t RESERVED1[55U];
1276:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t SPPR;                   /*!< Offset: 0x0F0 (R/W)  Selected Pin Protocol Register *
1277:Drivers/CMSIS/Include/core_cm7.h ****         uint32_t RESERVED2[131U];
1278:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t FFSR;                   /*!< Offset: 0x300 (R/ )  Formatter and Flush Status Regis
1279:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t FFCR;                   /*!< Offset: 0x304 (R/W)  Formatter and Flush Control Regi
1280:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t FSCR;                   /*!< Offset: 0x308 (R/ )  Formatter Synchronization Counte
1281:Drivers/CMSIS/Include/core_cm7.h ****         uint32_t RESERVED3[759U];
1282:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t TRIGGER;                /*!< Offset: 0xEE8 (R/ )  TRIGGER Register */
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 80


1283:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t FIFO0;                  /*!< Offset: 0xEEC (R/ )  Integration ETM Data */
1284:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t ITATBCTR2;              /*!< Offset: 0xEF0 (R/ )  ITATBCTR2 */
1285:Drivers/CMSIS/Include/core_cm7.h ****         uint32_t RESERVED4[1U];
1286:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t ITATBCTR0;              /*!< Offset: 0xEF8 (R/ )  ITATBCTR0 */
1287:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t FIFO1;                  /*!< Offset: 0xEFC (R/ )  Integration ITM Data */
1288:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t ITCTRL;                 /*!< Offset: 0xF00 (R/W)  Integration Mode Control */
1289:Drivers/CMSIS/Include/core_cm7.h ****         uint32_t RESERVED5[39U];
1290:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t CLAIMSET;               /*!< Offset: 0xFA0 (R/W)  Claim tag set */
1291:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t CLAIMCLR;               /*!< Offset: 0xFA4 (R/W)  Claim tag clear */
1292:Drivers/CMSIS/Include/core_cm7.h ****         uint32_t RESERVED7[8U];
1293:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t DEVID;                  /*!< Offset: 0xFC8 (R/ )  TPIU_DEVID */
1294:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t DEVTYPE;                /*!< Offset: 0xFCC (R/ )  TPIU_DEVTYPE */
1295:Drivers/CMSIS/Include/core_cm7.h **** } TPI_Type;
1296:Drivers/CMSIS/Include/core_cm7.h **** 
1297:Drivers/CMSIS/Include/core_cm7.h **** /* TPI Asynchronous Clock Prescaler Register Definitions */
1298:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_ACPR_PRESCALER_Pos              0U                                         /*!< TPI ACP
1299:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_ACPR_PRESCALER_Msk             (0x1FFFUL /*<< TPI_ACPR_PRESCALER_Pos*/)    /*!< TPI ACP
1300:Drivers/CMSIS/Include/core_cm7.h **** 
1301:Drivers/CMSIS/Include/core_cm7.h **** /* TPI Selected Pin Protocol Register Definitions */
1302:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_SPPR_TXMODE_Pos                 0U                                         /*!< TPI SPP
1303:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_SPPR_TXMODE_Msk                (0x3UL /*<< TPI_SPPR_TXMODE_Pos*/)          /*!< TPI SPP
1304:Drivers/CMSIS/Include/core_cm7.h **** 
1305:Drivers/CMSIS/Include/core_cm7.h **** /* TPI Formatter and Flush Status Register Definitions */
1306:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FFSR_FtNonStop_Pos              3U                                         /*!< TPI FFS
1307:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FFSR_FtNonStop_Msk             (0x1UL << TPI_FFSR_FtNonStop_Pos)           /*!< TPI FFS
1308:Drivers/CMSIS/Include/core_cm7.h **** 
1309:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FFSR_TCPresent_Pos              2U                                         /*!< TPI FFS
1310:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FFSR_TCPresent_Msk             (0x1UL << TPI_FFSR_TCPresent_Pos)           /*!< TPI FFS
1311:Drivers/CMSIS/Include/core_cm7.h **** 
1312:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FFSR_FtStopped_Pos              1U                                         /*!< TPI FFS
1313:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FFSR_FtStopped_Msk             (0x1UL << TPI_FFSR_FtStopped_Pos)           /*!< TPI FFS
1314:Drivers/CMSIS/Include/core_cm7.h **** 
1315:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FFSR_FlInProg_Pos               0U                                         /*!< TPI FFS
1316:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FFSR_FlInProg_Msk              (0x1UL /*<< TPI_FFSR_FlInProg_Pos*/)        /*!< TPI FFS
1317:Drivers/CMSIS/Include/core_cm7.h **** 
1318:Drivers/CMSIS/Include/core_cm7.h **** /* TPI Formatter and Flush Control Register Definitions */
1319:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FFCR_TrigIn_Pos                 8U                                         /*!< TPI FFC
1320:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FFCR_TrigIn_Msk                (0x1UL << TPI_FFCR_TrigIn_Pos)              /*!< TPI FFC
1321:Drivers/CMSIS/Include/core_cm7.h **** 
1322:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FFCR_EnFCont_Pos                1U                                         /*!< TPI FFC
1323:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FFCR_EnFCont_Msk               (0x1UL << TPI_FFCR_EnFCont_Pos)             /*!< TPI FFC
1324:Drivers/CMSIS/Include/core_cm7.h **** 
1325:Drivers/CMSIS/Include/core_cm7.h **** /* TPI TRIGGER Register Definitions */
1326:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_TRIGGER_TRIGGER_Pos             0U                                         /*!< TPI TRI
1327:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_TRIGGER_TRIGGER_Msk            (0x1UL /*<< TPI_TRIGGER_TRIGGER_Pos*/)      /*!< TPI TRI
1328:Drivers/CMSIS/Include/core_cm7.h **** 
1329:Drivers/CMSIS/Include/core_cm7.h **** /* TPI Integration ETM Data Register Definitions (FIFO0) */
1330:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FIFO0_ITM_ATVALID_Pos          29U                                         /*!< TPI FIF
1331:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FIFO0_ITM_ATVALID_Msk          (0x1UL << TPI_FIFO0_ITM_ATVALID_Pos)        /*!< TPI FIF
1332:Drivers/CMSIS/Include/core_cm7.h **** 
1333:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FIFO0_ITM_bytecount_Pos        27U                                         /*!< TPI FIF
1334:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FIFO0_ITM_bytecount_Msk        (0x3UL << TPI_FIFO0_ITM_bytecount_Pos)      /*!< TPI FIF
1335:Drivers/CMSIS/Include/core_cm7.h **** 
1336:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FIFO0_ETM_ATVALID_Pos          26U                                         /*!< TPI FIF
1337:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FIFO0_ETM_ATVALID_Msk          (0x1UL << TPI_FIFO0_ETM_ATVALID_Pos)        /*!< TPI FIF
1338:Drivers/CMSIS/Include/core_cm7.h **** 
1339:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FIFO0_ETM_bytecount_Pos        24U                                         /*!< TPI FIF
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 81


1340:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FIFO0_ETM_bytecount_Msk        (0x3UL << TPI_FIFO0_ETM_bytecount_Pos)      /*!< TPI FIF
1341:Drivers/CMSIS/Include/core_cm7.h **** 
1342:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FIFO0_ETM2_Pos                 16U                                         /*!< TPI FIF
1343:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FIFO0_ETM2_Msk                 (0xFFUL << TPI_FIFO0_ETM2_Pos)              /*!< TPI FIF
1344:Drivers/CMSIS/Include/core_cm7.h **** 
1345:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FIFO0_ETM1_Pos                  8U                                         /*!< TPI FIF
1346:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FIFO0_ETM1_Msk                 (0xFFUL << TPI_FIFO0_ETM1_Pos)              /*!< TPI FIF
1347:Drivers/CMSIS/Include/core_cm7.h **** 
1348:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FIFO0_ETM0_Pos                  0U                                         /*!< TPI FIF
1349:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FIFO0_ETM0_Msk                 (0xFFUL /*<< TPI_FIFO0_ETM0_Pos*/)          /*!< TPI FIF
1350:Drivers/CMSIS/Include/core_cm7.h **** 
1351:Drivers/CMSIS/Include/core_cm7.h **** /* TPI ITATBCTR2 Register Definitions */
1352:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_ITATBCTR2_ATREADY2_Pos          0U                                         /*!< TPI ITA
1353:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_ITATBCTR2_ATREADY2_Msk         (0x1UL /*<< TPI_ITATBCTR2_ATREADY2_Pos*/)   /*!< TPI ITA
1354:Drivers/CMSIS/Include/core_cm7.h **** 
1355:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_ITATBCTR2_ATREADY1_Pos          0U                                         /*!< TPI ITA
1356:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_ITATBCTR2_ATREADY1_Msk         (0x1UL /*<< TPI_ITATBCTR2_ATREADY1_Pos*/)   /*!< TPI ITA
1357:Drivers/CMSIS/Include/core_cm7.h **** 
1358:Drivers/CMSIS/Include/core_cm7.h **** /* TPI Integration ITM Data Register Definitions (FIFO1) */
1359:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FIFO1_ITM_ATVALID_Pos          29U                                         /*!< TPI FIF
1360:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FIFO1_ITM_ATVALID_Msk          (0x1UL << TPI_FIFO1_ITM_ATVALID_Pos)        /*!< TPI FIF
1361:Drivers/CMSIS/Include/core_cm7.h **** 
1362:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FIFO1_ITM_bytecount_Pos        27U                                         /*!< TPI FIF
1363:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FIFO1_ITM_bytecount_Msk        (0x3UL << TPI_FIFO1_ITM_bytecount_Pos)      /*!< TPI FIF
1364:Drivers/CMSIS/Include/core_cm7.h **** 
1365:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FIFO1_ETM_ATVALID_Pos          26U                                         /*!< TPI FIF
1366:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FIFO1_ETM_ATVALID_Msk          (0x1UL << TPI_FIFO1_ETM_ATVALID_Pos)        /*!< TPI FIF
1367:Drivers/CMSIS/Include/core_cm7.h **** 
1368:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FIFO1_ETM_bytecount_Pos        24U                                         /*!< TPI FIF
1369:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FIFO1_ETM_bytecount_Msk        (0x3UL << TPI_FIFO1_ETM_bytecount_Pos)      /*!< TPI FIF
1370:Drivers/CMSIS/Include/core_cm7.h **** 
1371:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FIFO1_ITM2_Pos                 16U                                         /*!< TPI FIF
1372:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FIFO1_ITM2_Msk                 (0xFFUL << TPI_FIFO1_ITM2_Pos)              /*!< TPI FIF
1373:Drivers/CMSIS/Include/core_cm7.h **** 
1374:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FIFO1_ITM1_Pos                  8U                                         /*!< TPI FIF
1375:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FIFO1_ITM1_Msk                 (0xFFUL << TPI_FIFO1_ITM1_Pos)              /*!< TPI FIF
1376:Drivers/CMSIS/Include/core_cm7.h **** 
1377:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FIFO1_ITM0_Pos                  0U                                         /*!< TPI FIF
1378:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_FIFO1_ITM0_Msk                 (0xFFUL /*<< TPI_FIFO1_ITM0_Pos*/)          /*!< TPI FIF
1379:Drivers/CMSIS/Include/core_cm7.h **** 
1380:Drivers/CMSIS/Include/core_cm7.h **** /* TPI ITATBCTR0 Register Definitions */
1381:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_ITATBCTR0_ATREADY2_Pos          0U                                         /*!< TPI ITA
1382:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_ITATBCTR0_ATREADY2_Msk         (0x1UL /*<< TPI_ITATBCTR0_ATREADY2_Pos*/)   /*!< TPI ITA
1383:Drivers/CMSIS/Include/core_cm7.h **** 
1384:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_ITATBCTR0_ATREADY1_Pos          0U                                         /*!< TPI ITA
1385:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_ITATBCTR0_ATREADY1_Msk         (0x1UL /*<< TPI_ITATBCTR0_ATREADY1_Pos*/)   /*!< TPI ITA
1386:Drivers/CMSIS/Include/core_cm7.h **** 
1387:Drivers/CMSIS/Include/core_cm7.h **** /* TPI Integration Mode Control Register Definitions */
1388:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_ITCTRL_Mode_Pos                 0U                                         /*!< TPI ITC
1389:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_ITCTRL_Mode_Msk                (0x3UL /*<< TPI_ITCTRL_Mode_Pos*/)          /*!< TPI ITC
1390:Drivers/CMSIS/Include/core_cm7.h **** 
1391:Drivers/CMSIS/Include/core_cm7.h **** /* TPI DEVID Register Definitions */
1392:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_DEVID_NRZVALID_Pos             11U                                         /*!< TPI DEV
1393:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_DEVID_NRZVALID_Msk             (0x1UL << TPI_DEVID_NRZVALID_Pos)           /*!< TPI DEV
1394:Drivers/CMSIS/Include/core_cm7.h **** 
1395:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_DEVID_MANCVALID_Pos            10U                                         /*!< TPI DEV
1396:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_DEVID_MANCVALID_Msk            (0x1UL << TPI_DEVID_MANCVALID_Pos)          /*!< TPI DEV
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 82


1397:Drivers/CMSIS/Include/core_cm7.h **** 
1398:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_DEVID_PTINVALID_Pos             9U                                         /*!< TPI DEV
1399:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_DEVID_PTINVALID_Msk            (0x1UL << TPI_DEVID_PTINVALID_Pos)          /*!< TPI DEV
1400:Drivers/CMSIS/Include/core_cm7.h **** 
1401:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_DEVID_MinBufSz_Pos              6U                                         /*!< TPI DEV
1402:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_DEVID_MinBufSz_Msk             (0x7UL << TPI_DEVID_MinBufSz_Pos)           /*!< TPI DEV
1403:Drivers/CMSIS/Include/core_cm7.h **** 
1404:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_DEVID_AsynClkIn_Pos             5U                                         /*!< TPI DEV
1405:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_DEVID_AsynClkIn_Msk            (0x1UL << TPI_DEVID_AsynClkIn_Pos)          /*!< TPI DEV
1406:Drivers/CMSIS/Include/core_cm7.h **** 
1407:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_DEVID_NrTraceInput_Pos          0U                                         /*!< TPI DEV
1408:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_DEVID_NrTraceInput_Msk         (0x1FUL /*<< TPI_DEVID_NrTraceInput_Pos*/)  /*!< TPI DEV
1409:Drivers/CMSIS/Include/core_cm7.h **** 
1410:Drivers/CMSIS/Include/core_cm7.h **** /* TPI DEVTYPE Register Definitions */
1411:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_DEVTYPE_SubType_Pos             4U                                         /*!< TPI DEV
1412:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_DEVTYPE_SubType_Msk            (0xFUL /*<< TPI_DEVTYPE_SubType_Pos*/)      /*!< TPI DEV
1413:Drivers/CMSIS/Include/core_cm7.h **** 
1414:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_DEVTYPE_MajorType_Pos           0U                                         /*!< TPI DEV
1415:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_DEVTYPE_MajorType_Msk          (0xFUL << TPI_DEVTYPE_MajorType_Pos)        /*!< TPI DEV
1416:Drivers/CMSIS/Include/core_cm7.h **** 
1417:Drivers/CMSIS/Include/core_cm7.h **** /*@}*/ /* end of group CMSIS_TPI */
1418:Drivers/CMSIS/Include/core_cm7.h **** 
1419:Drivers/CMSIS/Include/core_cm7.h **** 
1420:Drivers/CMSIS/Include/core_cm7.h **** #if defined (__MPU_PRESENT) && (__MPU_PRESENT == 1U)
1421:Drivers/CMSIS/Include/core_cm7.h **** /**
1422:Drivers/CMSIS/Include/core_cm7.h ****   \ingroup  CMSIS_core_register
1423:Drivers/CMSIS/Include/core_cm7.h ****   \defgroup CMSIS_MPU     Memory Protection Unit (MPU)
1424:Drivers/CMSIS/Include/core_cm7.h ****   \brief    Type definitions for the Memory Protection Unit (MPU)
1425:Drivers/CMSIS/Include/core_cm7.h ****   @{
1426:Drivers/CMSIS/Include/core_cm7.h ****  */
1427:Drivers/CMSIS/Include/core_cm7.h **** 
1428:Drivers/CMSIS/Include/core_cm7.h **** /**
1429:Drivers/CMSIS/Include/core_cm7.h ****   \brief  Structure type to access the Memory Protection Unit (MPU).
1430:Drivers/CMSIS/Include/core_cm7.h ****  */
1431:Drivers/CMSIS/Include/core_cm7.h **** typedef struct
1432:Drivers/CMSIS/Include/core_cm7.h **** {
1433:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t TYPE;                   /*!< Offset: 0x000 (R/ )  MPU Type Register */
1434:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t CTRL;                   /*!< Offset: 0x004 (R/W)  MPU Control Register */
1435:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t RNR;                    /*!< Offset: 0x008 (R/W)  MPU Region RNRber Register */
1436:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t RBAR;                   /*!< Offset: 0x00C (R/W)  MPU Region Base Address Register
1437:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t RASR;                   /*!< Offset: 0x010 (R/W)  MPU Region Attribute and Size Re
1438:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t RBAR_A1;                /*!< Offset: 0x014 (R/W)  MPU Alias 1 Region Base Address 
1439:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t RASR_A1;                /*!< Offset: 0x018 (R/W)  MPU Alias 1 Region Attribute and
1440:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t RBAR_A2;                /*!< Offset: 0x01C (R/W)  MPU Alias 2 Region Base Address 
1441:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t RASR_A2;                /*!< Offset: 0x020 (R/W)  MPU Alias 2 Region Attribute and
1442:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t RBAR_A3;                /*!< Offset: 0x024 (R/W)  MPU Alias 3 Region Base Address 
1443:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t RASR_A3;                /*!< Offset: 0x028 (R/W)  MPU Alias 3 Region Attribute and
1444:Drivers/CMSIS/Include/core_cm7.h **** } MPU_Type;
1445:Drivers/CMSIS/Include/core_cm7.h **** 
1446:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_TYPE_RALIASES                  4U
1447:Drivers/CMSIS/Include/core_cm7.h **** 
1448:Drivers/CMSIS/Include/core_cm7.h **** /* MPU Type Register Definitions */
1449:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_TYPE_IREGION_Pos               16U                                            /*!< MPU 
1450:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_TYPE_IREGION_Msk               (0xFFUL << MPU_TYPE_IREGION_Pos)               /*!< MPU 
1451:Drivers/CMSIS/Include/core_cm7.h **** 
1452:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_TYPE_DREGION_Pos                8U                                            /*!< MPU 
1453:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_TYPE_DREGION_Msk               (0xFFUL << MPU_TYPE_DREGION_Pos)               /*!< MPU 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 83


1454:Drivers/CMSIS/Include/core_cm7.h **** 
1455:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_TYPE_SEPARATE_Pos               0U                                            /*!< MPU 
1456:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_TYPE_SEPARATE_Msk              (1UL /*<< MPU_TYPE_SEPARATE_Pos*/)             /*!< MPU 
1457:Drivers/CMSIS/Include/core_cm7.h **** 
1458:Drivers/CMSIS/Include/core_cm7.h **** /* MPU Control Register Definitions */
1459:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_CTRL_PRIVDEFENA_Pos             2U                                            /*!< MPU 
1460:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_CTRL_PRIVDEFENA_Msk            (1UL << MPU_CTRL_PRIVDEFENA_Pos)               /*!< MPU 
1461:Drivers/CMSIS/Include/core_cm7.h **** 
1462:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_CTRL_HFNMIENA_Pos               1U                                            /*!< MPU 
1463:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_CTRL_HFNMIENA_Msk              (1UL << MPU_CTRL_HFNMIENA_Pos)                 /*!< MPU 
1464:Drivers/CMSIS/Include/core_cm7.h **** 
1465:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_CTRL_ENABLE_Pos                 0U                                            /*!< MPU 
1466:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_CTRL_ENABLE_Msk                (1UL /*<< MPU_CTRL_ENABLE_Pos*/)               /*!< MPU 
1467:Drivers/CMSIS/Include/core_cm7.h **** 
1468:Drivers/CMSIS/Include/core_cm7.h **** /* MPU Region Number Register Definitions */
1469:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_RNR_REGION_Pos                  0U                                            /*!< MPU 
1470:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_RNR_REGION_Msk                 (0xFFUL /*<< MPU_RNR_REGION_Pos*/)             /*!< MPU 
1471:Drivers/CMSIS/Include/core_cm7.h **** 
1472:Drivers/CMSIS/Include/core_cm7.h **** /* MPU Region Base Address Register Definitions */
1473:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_RBAR_ADDR_Pos                   5U                                            /*!< MPU 
1474:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_RBAR_ADDR_Msk                  (0x7FFFFFFUL << MPU_RBAR_ADDR_Pos)             /*!< MPU 
1475:Drivers/CMSIS/Include/core_cm7.h **** 
1476:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_RBAR_VALID_Pos                  4U                                            /*!< MPU 
1477:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_RBAR_VALID_Msk                 (1UL << MPU_RBAR_VALID_Pos)                    /*!< MPU 
1478:Drivers/CMSIS/Include/core_cm7.h **** 
1479:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_RBAR_REGION_Pos                 0U                                            /*!< MPU 
1480:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_RBAR_REGION_Msk                (0xFUL /*<< MPU_RBAR_REGION_Pos*/)             /*!< MPU 
1481:Drivers/CMSIS/Include/core_cm7.h **** 
1482:Drivers/CMSIS/Include/core_cm7.h **** /* MPU Region Attribute and Size Register Definitions */
1483:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_RASR_ATTRS_Pos                 16U                                            /*!< MPU 
1484:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_RASR_ATTRS_Msk                 (0xFFFFUL << MPU_RASR_ATTRS_Pos)               /*!< MPU 
1485:Drivers/CMSIS/Include/core_cm7.h **** 
1486:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_RASR_XN_Pos                    28U                                            /*!< MPU 
1487:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_RASR_XN_Msk                    (1UL << MPU_RASR_XN_Pos)                       /*!< MPU 
1488:Drivers/CMSIS/Include/core_cm7.h **** 
1489:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_RASR_AP_Pos                    24U                                            /*!< MPU 
1490:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_RASR_AP_Msk                    (0x7UL << MPU_RASR_AP_Pos)                     /*!< MPU 
1491:Drivers/CMSIS/Include/core_cm7.h **** 
1492:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_RASR_TEX_Pos                   19U                                            /*!< MPU 
1493:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_RASR_TEX_Msk                   (0x7UL << MPU_RASR_TEX_Pos)                    /*!< MPU 
1494:Drivers/CMSIS/Include/core_cm7.h **** 
1495:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_RASR_S_Pos                     18U                                            /*!< MPU 
1496:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_RASR_S_Msk                     (1UL << MPU_RASR_S_Pos)                        /*!< MPU 
1497:Drivers/CMSIS/Include/core_cm7.h **** 
1498:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_RASR_C_Pos                     17U                                            /*!< MPU 
1499:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_RASR_C_Msk                     (1UL << MPU_RASR_C_Pos)                        /*!< MPU 
1500:Drivers/CMSIS/Include/core_cm7.h **** 
1501:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_RASR_B_Pos                     16U                                            /*!< MPU 
1502:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_RASR_B_Msk                     (1UL << MPU_RASR_B_Pos)                        /*!< MPU 
1503:Drivers/CMSIS/Include/core_cm7.h **** 
1504:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_RASR_SRD_Pos                    8U                                            /*!< MPU 
1505:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_RASR_SRD_Msk                   (0xFFUL << MPU_RASR_SRD_Pos)                   /*!< MPU 
1506:Drivers/CMSIS/Include/core_cm7.h **** 
1507:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_RASR_SIZE_Pos                   1U                                            /*!< MPU 
1508:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_RASR_SIZE_Msk                  (0x1FUL << MPU_RASR_SIZE_Pos)                  /*!< MPU 
1509:Drivers/CMSIS/Include/core_cm7.h **** 
1510:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_RASR_ENABLE_Pos                 0U                                            /*!< MPU 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 84


1511:Drivers/CMSIS/Include/core_cm7.h **** #define MPU_RASR_ENABLE_Msk                (1UL /*<< MPU_RASR_ENABLE_Pos*/)               /*!< MPU 
1512:Drivers/CMSIS/Include/core_cm7.h **** 
1513:Drivers/CMSIS/Include/core_cm7.h **** /*@} end of group CMSIS_MPU */
1514:Drivers/CMSIS/Include/core_cm7.h **** #endif /* defined (__MPU_PRESENT) && (__MPU_PRESENT == 1U) */
1515:Drivers/CMSIS/Include/core_cm7.h **** 
1516:Drivers/CMSIS/Include/core_cm7.h **** 
1517:Drivers/CMSIS/Include/core_cm7.h **** /**
1518:Drivers/CMSIS/Include/core_cm7.h ****   \ingroup  CMSIS_core_register
1519:Drivers/CMSIS/Include/core_cm7.h ****   \defgroup CMSIS_FPU     Floating Point Unit (FPU)
1520:Drivers/CMSIS/Include/core_cm7.h ****   \brief    Type definitions for the Floating Point Unit (FPU)
1521:Drivers/CMSIS/Include/core_cm7.h ****   @{
1522:Drivers/CMSIS/Include/core_cm7.h ****  */
1523:Drivers/CMSIS/Include/core_cm7.h **** 
1524:Drivers/CMSIS/Include/core_cm7.h **** /**
1525:Drivers/CMSIS/Include/core_cm7.h ****   \brief  Structure type to access the Floating Point Unit (FPU).
1526:Drivers/CMSIS/Include/core_cm7.h ****  */
1527:Drivers/CMSIS/Include/core_cm7.h **** typedef struct
1528:Drivers/CMSIS/Include/core_cm7.h **** {
1529:Drivers/CMSIS/Include/core_cm7.h ****         uint32_t RESERVED0[1U];
1530:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t FPCCR;                  /*!< Offset: 0x004 (R/W)  Floating-Point Context Control R
1531:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t FPCAR;                  /*!< Offset: 0x008 (R/W)  Floating-Point Context Address R
1532:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t FPDSCR;                 /*!< Offset: 0x00C (R/W)  Floating-Point Default Status Co
1533:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t MVFR0;                  /*!< Offset: 0x010 (R/ )  Media and FP Feature Register 0 
1534:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t MVFR1;                  /*!< Offset: 0x014 (R/ )  Media and FP Feature Register 1 
1535:Drivers/CMSIS/Include/core_cm7.h ****   __IM  uint32_t MVFR2;                  /*!< Offset: 0x018 (R/ )  Media and FP Feature Register 2 
1536:Drivers/CMSIS/Include/core_cm7.h **** } FPU_Type;
1537:Drivers/CMSIS/Include/core_cm7.h **** 
1538:Drivers/CMSIS/Include/core_cm7.h **** /* Floating-Point Context Control Register Definitions */
1539:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_FPCCR_ASPEN_Pos                31U                                            /*!< FPCC
1540:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_FPCCR_ASPEN_Msk                (1UL << FPU_FPCCR_ASPEN_Pos)                   /*!< FPCC
1541:Drivers/CMSIS/Include/core_cm7.h **** 
1542:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_FPCCR_LSPEN_Pos                30U                                            /*!< FPCC
1543:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_FPCCR_LSPEN_Msk                (1UL << FPU_FPCCR_LSPEN_Pos)                   /*!< FPCC
1544:Drivers/CMSIS/Include/core_cm7.h **** 
1545:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_FPCCR_MONRDY_Pos                8U                                            /*!< FPCC
1546:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_FPCCR_MONRDY_Msk               (1UL << FPU_FPCCR_MONRDY_Pos)                  /*!< FPCC
1547:Drivers/CMSIS/Include/core_cm7.h **** 
1548:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_FPCCR_BFRDY_Pos                 6U                                            /*!< FPCC
1549:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_FPCCR_BFRDY_Msk                (1UL << FPU_FPCCR_BFRDY_Pos)                   /*!< FPCC
1550:Drivers/CMSIS/Include/core_cm7.h **** 
1551:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_FPCCR_MMRDY_Pos                 5U                                            /*!< FPCC
1552:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_FPCCR_MMRDY_Msk                (1UL << FPU_FPCCR_MMRDY_Pos)                   /*!< FPCC
1553:Drivers/CMSIS/Include/core_cm7.h **** 
1554:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_FPCCR_HFRDY_Pos                 4U                                            /*!< FPCC
1555:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_FPCCR_HFRDY_Msk                (1UL << FPU_FPCCR_HFRDY_Pos)                   /*!< FPCC
1556:Drivers/CMSIS/Include/core_cm7.h **** 
1557:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_FPCCR_THREAD_Pos                3U                                            /*!< FPCC
1558:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_FPCCR_THREAD_Msk               (1UL << FPU_FPCCR_THREAD_Pos)                  /*!< FPCC
1559:Drivers/CMSIS/Include/core_cm7.h **** 
1560:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_FPCCR_USER_Pos                  1U                                            /*!< FPCC
1561:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_FPCCR_USER_Msk                 (1UL << FPU_FPCCR_USER_Pos)                    /*!< FPCC
1562:Drivers/CMSIS/Include/core_cm7.h **** 
1563:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_FPCCR_LSPACT_Pos                0U                                            /*!< FPCC
1564:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_FPCCR_LSPACT_Msk               (1UL /*<< FPU_FPCCR_LSPACT_Pos*/)              /*!< FPCC
1565:Drivers/CMSIS/Include/core_cm7.h **** 
1566:Drivers/CMSIS/Include/core_cm7.h **** /* Floating-Point Context Address Register Definitions */
1567:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_FPCAR_ADDRESS_Pos               3U                                            /*!< FPCA
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 85


1568:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_FPCAR_ADDRESS_Msk              (0x1FFFFFFFUL << FPU_FPCAR_ADDRESS_Pos)        /*!< FPCA
1569:Drivers/CMSIS/Include/core_cm7.h **** 
1570:Drivers/CMSIS/Include/core_cm7.h **** /* Floating-Point Default Status Control Register Definitions */
1571:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_FPDSCR_AHP_Pos                 26U                                            /*!< FPDS
1572:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_FPDSCR_AHP_Msk                 (1UL << FPU_FPDSCR_AHP_Pos)                    /*!< FPDS
1573:Drivers/CMSIS/Include/core_cm7.h **** 
1574:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_FPDSCR_DN_Pos                  25U                                            /*!< FPDS
1575:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_FPDSCR_DN_Msk                  (1UL << FPU_FPDSCR_DN_Pos)                     /*!< FPDS
1576:Drivers/CMSIS/Include/core_cm7.h **** 
1577:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_FPDSCR_FZ_Pos                  24U                                            /*!< FPDS
1578:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_FPDSCR_FZ_Msk                  (1UL << FPU_FPDSCR_FZ_Pos)                     /*!< FPDS
1579:Drivers/CMSIS/Include/core_cm7.h **** 
1580:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_FPDSCR_RMode_Pos               22U                                            /*!< FPDS
1581:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_FPDSCR_RMode_Msk               (3UL << FPU_FPDSCR_RMode_Pos)                  /*!< FPDS
1582:Drivers/CMSIS/Include/core_cm7.h **** 
1583:Drivers/CMSIS/Include/core_cm7.h **** /* Media and FP Feature Register 0 Definitions */
1584:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_MVFR0_FP_rounding_modes_Pos    28U                                            /*!< MVFR
1585:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_MVFR0_FP_rounding_modes_Msk    (0xFUL << FPU_MVFR0_FP_rounding_modes_Pos)     /*!< MVFR
1586:Drivers/CMSIS/Include/core_cm7.h **** 
1587:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_MVFR0_Short_vectors_Pos        24U                                            /*!< MVFR
1588:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_MVFR0_Short_vectors_Msk        (0xFUL << FPU_MVFR0_Short_vectors_Pos)         /*!< MVFR
1589:Drivers/CMSIS/Include/core_cm7.h **** 
1590:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_MVFR0_Square_root_Pos          20U                                            /*!< MVFR
1591:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_MVFR0_Square_root_Msk          (0xFUL << FPU_MVFR0_Square_root_Pos)           /*!< MVFR
1592:Drivers/CMSIS/Include/core_cm7.h **** 
1593:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_MVFR0_Divide_Pos               16U                                            /*!< MVFR
1594:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_MVFR0_Divide_Msk               (0xFUL << FPU_MVFR0_Divide_Pos)                /*!< MVFR
1595:Drivers/CMSIS/Include/core_cm7.h **** 
1596:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_MVFR0_FP_excep_trapping_Pos    12U                                            /*!< MVFR
1597:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_MVFR0_FP_excep_trapping_Msk    (0xFUL << FPU_MVFR0_FP_excep_trapping_Pos)     /*!< MVFR
1598:Drivers/CMSIS/Include/core_cm7.h **** 
1599:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_MVFR0_Double_precision_Pos      8U                                            /*!< MVFR
1600:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_MVFR0_Double_precision_Msk     (0xFUL << FPU_MVFR0_Double_precision_Pos)      /*!< MVFR
1601:Drivers/CMSIS/Include/core_cm7.h **** 
1602:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_MVFR0_Single_precision_Pos      4U                                            /*!< MVFR
1603:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_MVFR0_Single_precision_Msk     (0xFUL << FPU_MVFR0_Single_precision_Pos)      /*!< MVFR
1604:Drivers/CMSIS/Include/core_cm7.h **** 
1605:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_MVFR0_A_SIMD_registers_Pos      0U                                            /*!< MVFR
1606:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_MVFR0_A_SIMD_registers_Msk     (0xFUL /*<< FPU_MVFR0_A_SIMD_registers_Pos*/)  /*!< MVFR
1607:Drivers/CMSIS/Include/core_cm7.h **** 
1608:Drivers/CMSIS/Include/core_cm7.h **** /* Media and FP Feature Register 1 Definitions */
1609:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_MVFR1_FP_fused_MAC_Pos         28U                                            /*!< MVFR
1610:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_MVFR1_FP_fused_MAC_Msk         (0xFUL << FPU_MVFR1_FP_fused_MAC_Pos)          /*!< MVFR
1611:Drivers/CMSIS/Include/core_cm7.h **** 
1612:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_MVFR1_FP_HPFP_Pos              24U                                            /*!< MVFR
1613:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_MVFR1_FP_HPFP_Msk              (0xFUL << FPU_MVFR1_FP_HPFP_Pos)               /*!< MVFR
1614:Drivers/CMSIS/Include/core_cm7.h **** 
1615:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_MVFR1_D_NaN_mode_Pos            4U                                            /*!< MVFR
1616:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_MVFR1_D_NaN_mode_Msk           (0xFUL << FPU_MVFR1_D_NaN_mode_Pos)            /*!< MVFR
1617:Drivers/CMSIS/Include/core_cm7.h **** 
1618:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_MVFR1_FtZ_mode_Pos              0U                                            /*!< MVFR
1619:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_MVFR1_FtZ_mode_Msk             (0xFUL /*<< FPU_MVFR1_FtZ_mode_Pos*/)          /*!< MVFR
1620:Drivers/CMSIS/Include/core_cm7.h **** 
1621:Drivers/CMSIS/Include/core_cm7.h **** /* Media and FP Feature Register 2 Definitions */
1622:Drivers/CMSIS/Include/core_cm7.h **** 
1623:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_MVFR2_VFP_Misc_Pos              4U                                            /*!< MVFR
1624:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_MVFR2_VFP_Misc_Msk             (0xFUL << FPU_MVFR2_VFP_Misc_Pos)              /*!< MVFR
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 86


1625:Drivers/CMSIS/Include/core_cm7.h **** 
1626:Drivers/CMSIS/Include/core_cm7.h **** /*@} end of group CMSIS_FPU */
1627:Drivers/CMSIS/Include/core_cm7.h **** 
1628:Drivers/CMSIS/Include/core_cm7.h **** 
1629:Drivers/CMSIS/Include/core_cm7.h **** /**
1630:Drivers/CMSIS/Include/core_cm7.h ****   \ingroup  CMSIS_core_register
1631:Drivers/CMSIS/Include/core_cm7.h ****   \defgroup CMSIS_CoreDebug       Core Debug Registers (CoreDebug)
1632:Drivers/CMSIS/Include/core_cm7.h ****   \brief    Type definitions for the Core Debug Registers
1633:Drivers/CMSIS/Include/core_cm7.h ****   @{
1634:Drivers/CMSIS/Include/core_cm7.h ****  */
1635:Drivers/CMSIS/Include/core_cm7.h **** 
1636:Drivers/CMSIS/Include/core_cm7.h **** /**
1637:Drivers/CMSIS/Include/core_cm7.h ****   \brief  Structure type to access the Core Debug Register (CoreDebug).
1638:Drivers/CMSIS/Include/core_cm7.h ****  */
1639:Drivers/CMSIS/Include/core_cm7.h **** typedef struct
1640:Drivers/CMSIS/Include/core_cm7.h **** {
1641:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t DHCSR;                  /*!< Offset: 0x000 (R/W)  Debug Halting Control and Status
1642:Drivers/CMSIS/Include/core_cm7.h ****   __OM  uint32_t DCRSR;                  /*!< Offset: 0x004 ( /W)  Debug Core Register Selector Reg
1643:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t DCRDR;                  /*!< Offset: 0x008 (R/W)  Debug Core Register Data Registe
1644:Drivers/CMSIS/Include/core_cm7.h ****   __IOM uint32_t DEMCR;                  /*!< Offset: 0x00C (R/W)  Debug Exception and Monitor Cont
1645:Drivers/CMSIS/Include/core_cm7.h **** } CoreDebug_Type;
1646:Drivers/CMSIS/Include/core_cm7.h **** 
1647:Drivers/CMSIS/Include/core_cm7.h **** /* Debug Halting Control and Status Register Definitions */
1648:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DHCSR_DBGKEY_Pos         16U                                            /*!< Core
1649:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DHCSR_DBGKEY_Msk         (0xFFFFUL << CoreDebug_DHCSR_DBGKEY_Pos)       /*!< Core
1650:Drivers/CMSIS/Include/core_cm7.h **** 
1651:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DHCSR_S_RESET_ST_Pos     25U                                            /*!< Core
1652:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DHCSR_S_RESET_ST_Msk     (1UL << CoreDebug_DHCSR_S_RESET_ST_Pos)        /*!< Core
1653:Drivers/CMSIS/Include/core_cm7.h **** 
1654:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DHCSR_S_RETIRE_ST_Pos    24U                                            /*!< Core
1655:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DHCSR_S_RETIRE_ST_Msk    (1UL << CoreDebug_DHCSR_S_RETIRE_ST_Pos)       /*!< Core
1656:Drivers/CMSIS/Include/core_cm7.h **** 
1657:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DHCSR_S_LOCKUP_Pos       19U                                            /*!< Core
1658:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DHCSR_S_LOCKUP_Msk       (1UL << CoreDebug_DHCSR_S_LOCKUP_Pos)          /*!< Core
1659:Drivers/CMSIS/Include/core_cm7.h **** 
1660:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DHCSR_S_SLEEP_Pos        18U                                            /*!< Core
1661:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DHCSR_S_SLEEP_Msk        (1UL << CoreDebug_DHCSR_S_SLEEP_Pos)           /*!< Core
1662:Drivers/CMSIS/Include/core_cm7.h **** 
1663:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DHCSR_S_HALT_Pos         17U                                            /*!< Core
1664:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DHCSR_S_HALT_Msk         (1UL << CoreDebug_DHCSR_S_HALT_Pos)            /*!< Core
1665:Drivers/CMSIS/Include/core_cm7.h **** 
1666:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DHCSR_S_REGRDY_Pos       16U                                            /*!< Core
1667:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DHCSR_S_REGRDY_Msk       (1UL << CoreDebug_DHCSR_S_REGRDY_Pos)          /*!< Core
1668:Drivers/CMSIS/Include/core_cm7.h **** 
1669:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DHCSR_C_SNAPSTALL_Pos     5U                                            /*!< Core
1670:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DHCSR_C_SNAPSTALL_Msk    (1UL << CoreDebug_DHCSR_C_SNAPSTALL_Pos)       /*!< Core
1671:Drivers/CMSIS/Include/core_cm7.h **** 
1672:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DHCSR_C_MASKINTS_Pos      3U                                            /*!< Core
1673:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DHCSR_C_MASKINTS_Msk     (1UL << CoreDebug_DHCSR_C_MASKINTS_Pos)        /*!< Core
1674:Drivers/CMSIS/Include/core_cm7.h **** 
1675:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DHCSR_C_STEP_Pos          2U                                            /*!< Core
1676:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DHCSR_C_STEP_Msk         (1UL << CoreDebug_DHCSR_C_STEP_Pos)            /*!< Core
1677:Drivers/CMSIS/Include/core_cm7.h **** 
1678:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DHCSR_C_HALT_Pos          1U                                            /*!< Core
1679:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DHCSR_C_HALT_Msk         (1UL << CoreDebug_DHCSR_C_HALT_Pos)            /*!< Core
1680:Drivers/CMSIS/Include/core_cm7.h **** 
1681:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DHCSR_C_DEBUGEN_Pos       0U                                            /*!< Core
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 87


1682:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DHCSR_C_DEBUGEN_Msk      (1UL /*<< CoreDebug_DHCSR_C_DEBUGEN_Pos*/)     /*!< Core
1683:Drivers/CMSIS/Include/core_cm7.h **** 
1684:Drivers/CMSIS/Include/core_cm7.h **** /* Debug Core Register Selector Register Definitions */
1685:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DCRSR_REGWnR_Pos         16U                                            /*!< Core
1686:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DCRSR_REGWnR_Msk         (1UL << CoreDebug_DCRSR_REGWnR_Pos)            /*!< Core
1687:Drivers/CMSIS/Include/core_cm7.h **** 
1688:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DCRSR_REGSEL_Pos          0U                                            /*!< Core
1689:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DCRSR_REGSEL_Msk         (0x1FUL /*<< CoreDebug_DCRSR_REGSEL_Pos*/)     /*!< Core
1690:Drivers/CMSIS/Include/core_cm7.h **** 
1691:Drivers/CMSIS/Include/core_cm7.h **** /* Debug Exception and Monitor Control Register Definitions */
1692:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DEMCR_TRCENA_Pos         24U                                            /*!< Core
1693:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DEMCR_TRCENA_Msk         (1UL << CoreDebug_DEMCR_TRCENA_Pos)            /*!< Core
1694:Drivers/CMSIS/Include/core_cm7.h **** 
1695:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DEMCR_MON_REQ_Pos        19U                                            /*!< Core
1696:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DEMCR_MON_REQ_Msk        (1UL << CoreDebug_DEMCR_MON_REQ_Pos)           /*!< Core
1697:Drivers/CMSIS/Include/core_cm7.h **** 
1698:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DEMCR_MON_STEP_Pos       18U                                            /*!< Core
1699:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DEMCR_MON_STEP_Msk       (1UL << CoreDebug_DEMCR_MON_STEP_Pos)          /*!< Core
1700:Drivers/CMSIS/Include/core_cm7.h **** 
1701:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DEMCR_MON_PEND_Pos       17U                                            /*!< Core
1702:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DEMCR_MON_PEND_Msk       (1UL << CoreDebug_DEMCR_MON_PEND_Pos)          /*!< Core
1703:Drivers/CMSIS/Include/core_cm7.h **** 
1704:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DEMCR_MON_EN_Pos         16U                                            /*!< Core
1705:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DEMCR_MON_EN_Msk         (1UL << CoreDebug_DEMCR_MON_EN_Pos)            /*!< Core
1706:Drivers/CMSIS/Include/core_cm7.h **** 
1707:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DEMCR_VC_HARDERR_Pos     10U                                            /*!< Core
1708:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DEMCR_VC_HARDERR_Msk     (1UL << CoreDebug_DEMCR_VC_HARDERR_Pos)        /*!< Core
1709:Drivers/CMSIS/Include/core_cm7.h **** 
1710:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DEMCR_VC_INTERR_Pos       9U                                            /*!< Core
1711:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DEMCR_VC_INTERR_Msk      (1UL << CoreDebug_DEMCR_VC_INTERR_Pos)         /*!< Core
1712:Drivers/CMSIS/Include/core_cm7.h **** 
1713:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DEMCR_VC_BUSERR_Pos       8U                                            /*!< Core
1714:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DEMCR_VC_BUSERR_Msk      (1UL << CoreDebug_DEMCR_VC_BUSERR_Pos)         /*!< Core
1715:Drivers/CMSIS/Include/core_cm7.h **** 
1716:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DEMCR_VC_STATERR_Pos      7U                                            /*!< Core
1717:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DEMCR_VC_STATERR_Msk     (1UL << CoreDebug_DEMCR_VC_STATERR_Pos)        /*!< Core
1718:Drivers/CMSIS/Include/core_cm7.h **** 
1719:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DEMCR_VC_CHKERR_Pos       6U                                            /*!< Core
1720:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DEMCR_VC_CHKERR_Msk      (1UL << CoreDebug_DEMCR_VC_CHKERR_Pos)         /*!< Core
1721:Drivers/CMSIS/Include/core_cm7.h **** 
1722:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DEMCR_VC_NOCPERR_Pos      5U                                            /*!< Core
1723:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DEMCR_VC_NOCPERR_Msk     (1UL << CoreDebug_DEMCR_VC_NOCPERR_Pos)        /*!< Core
1724:Drivers/CMSIS/Include/core_cm7.h **** 
1725:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DEMCR_VC_MMERR_Pos        4U                                            /*!< Core
1726:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DEMCR_VC_MMERR_Msk       (1UL << CoreDebug_DEMCR_VC_MMERR_Pos)          /*!< Core
1727:Drivers/CMSIS/Include/core_cm7.h **** 
1728:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DEMCR_VC_CORERESET_Pos    0U                                            /*!< Core
1729:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_DEMCR_VC_CORERESET_Msk   (1UL /*<< CoreDebug_DEMCR_VC_CORERESET_Pos*/)  /*!< Core
1730:Drivers/CMSIS/Include/core_cm7.h **** 
1731:Drivers/CMSIS/Include/core_cm7.h **** /*@} end of group CMSIS_CoreDebug */
1732:Drivers/CMSIS/Include/core_cm7.h **** 
1733:Drivers/CMSIS/Include/core_cm7.h **** 
1734:Drivers/CMSIS/Include/core_cm7.h **** /**
1735:Drivers/CMSIS/Include/core_cm7.h ****   \ingroup    CMSIS_core_register
1736:Drivers/CMSIS/Include/core_cm7.h ****   \defgroup   CMSIS_core_bitfield     Core register bit field macros
1737:Drivers/CMSIS/Include/core_cm7.h ****   \brief      Macros for use with bit field definitions (xxx_Pos, xxx_Msk).
1738:Drivers/CMSIS/Include/core_cm7.h ****   @{
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 88


1739:Drivers/CMSIS/Include/core_cm7.h ****  */
1740:Drivers/CMSIS/Include/core_cm7.h **** 
1741:Drivers/CMSIS/Include/core_cm7.h **** /**
1742:Drivers/CMSIS/Include/core_cm7.h ****   \brief   Mask and shift a bit field value for use in a register bit range.
1743:Drivers/CMSIS/Include/core_cm7.h ****   \param[in] field  Name of the register bit field.
1744:Drivers/CMSIS/Include/core_cm7.h ****   \param[in] value  Value of the bit field. This parameter is interpreted as an uint32_t type.
1745:Drivers/CMSIS/Include/core_cm7.h ****   \return           Masked and shifted value.
1746:Drivers/CMSIS/Include/core_cm7.h **** */
1747:Drivers/CMSIS/Include/core_cm7.h **** #define _VAL2FLD(field, value)    (((uint32_t)(value) << field ## _Pos) & field ## _Msk)
1748:Drivers/CMSIS/Include/core_cm7.h **** 
1749:Drivers/CMSIS/Include/core_cm7.h **** /**
1750:Drivers/CMSIS/Include/core_cm7.h ****   \brief     Mask and shift a register value to extract a bit filed value.
1751:Drivers/CMSIS/Include/core_cm7.h ****   \param[in] field  Name of the register bit field.
1752:Drivers/CMSIS/Include/core_cm7.h ****   \param[in] value  Value of register. This parameter is interpreted as an uint32_t type.
1753:Drivers/CMSIS/Include/core_cm7.h ****   \return           Masked and shifted bit field value.
1754:Drivers/CMSIS/Include/core_cm7.h **** */
1755:Drivers/CMSIS/Include/core_cm7.h **** #define _FLD2VAL(field, value)    (((uint32_t)(value) & field ## _Msk) >> field ## _Pos)
1756:Drivers/CMSIS/Include/core_cm7.h **** 
1757:Drivers/CMSIS/Include/core_cm7.h **** /*@} end of group CMSIS_core_bitfield */
1758:Drivers/CMSIS/Include/core_cm7.h **** 
1759:Drivers/CMSIS/Include/core_cm7.h **** 
1760:Drivers/CMSIS/Include/core_cm7.h **** /**
1761:Drivers/CMSIS/Include/core_cm7.h ****   \ingroup    CMSIS_core_register
1762:Drivers/CMSIS/Include/core_cm7.h ****   \defgroup   CMSIS_core_base     Core Definitions
1763:Drivers/CMSIS/Include/core_cm7.h ****   \brief      Definitions for base addresses, unions, and structures.
1764:Drivers/CMSIS/Include/core_cm7.h ****   @{
1765:Drivers/CMSIS/Include/core_cm7.h ****  */
1766:Drivers/CMSIS/Include/core_cm7.h **** 
1767:Drivers/CMSIS/Include/core_cm7.h **** /* Memory mapping of Core Hardware */
1768:Drivers/CMSIS/Include/core_cm7.h **** #define SCS_BASE            (0xE000E000UL)                            /*!< System Control Space Bas
1769:Drivers/CMSIS/Include/core_cm7.h **** #define ITM_BASE            (0xE0000000UL)                            /*!< ITM Base Address */
1770:Drivers/CMSIS/Include/core_cm7.h **** #define DWT_BASE            (0xE0001000UL)                            /*!< DWT Base Address */
1771:Drivers/CMSIS/Include/core_cm7.h **** #define TPI_BASE            (0xE0040000UL)                            /*!< TPI Base Address */
1772:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug_BASE      (0xE000EDF0UL)                            /*!< Core Debug Base Address 
1773:Drivers/CMSIS/Include/core_cm7.h **** #define SysTick_BASE        (SCS_BASE +  0x0010UL)                    /*!< SysTick Base Address */
1774:Drivers/CMSIS/Include/core_cm7.h **** #define NVIC_BASE           (SCS_BASE +  0x0100UL)                    /*!< NVIC Base Address */
1775:Drivers/CMSIS/Include/core_cm7.h **** #define SCB_BASE            (SCS_BASE +  0x0D00UL)                    /*!< System Control Block Bas
1776:Drivers/CMSIS/Include/core_cm7.h **** 
1777:Drivers/CMSIS/Include/core_cm7.h **** #define SCnSCB              ((SCnSCB_Type    *)     SCS_BASE      )   /*!< System control Register 
1778:Drivers/CMSIS/Include/core_cm7.h **** #define SCB                 ((SCB_Type       *)     SCB_BASE      )   /*!< SCB configuration struct
1779:Drivers/CMSIS/Include/core_cm7.h **** #define SysTick             ((SysTick_Type   *)     SysTick_BASE  )   /*!< SysTick configuration st
1780:Drivers/CMSIS/Include/core_cm7.h **** #define NVIC                ((NVIC_Type      *)     NVIC_BASE     )   /*!< NVIC configuration struc
1781:Drivers/CMSIS/Include/core_cm7.h **** #define ITM                 ((ITM_Type       *)     ITM_BASE      )   /*!< ITM configuration struct
1782:Drivers/CMSIS/Include/core_cm7.h **** #define DWT                 ((DWT_Type       *)     DWT_BASE      )   /*!< DWT configuration struct
1783:Drivers/CMSIS/Include/core_cm7.h **** #define TPI                 ((TPI_Type       *)     TPI_BASE      )   /*!< TPI configuration struct
1784:Drivers/CMSIS/Include/core_cm7.h **** #define CoreDebug           ((CoreDebug_Type *)     CoreDebug_BASE)   /*!< Core Debug configuration
1785:Drivers/CMSIS/Include/core_cm7.h **** 
1786:Drivers/CMSIS/Include/core_cm7.h **** #if defined (__MPU_PRESENT) && (__MPU_PRESENT == 1U)
1787:Drivers/CMSIS/Include/core_cm7.h ****   #define MPU_BASE          (SCS_BASE +  0x0D90UL)                    /*!< Memory Protection Unit *
1788:Drivers/CMSIS/Include/core_cm7.h ****   #define MPU               ((MPU_Type       *)     MPU_BASE      )   /*!< Memory Protection Unit *
1789:Drivers/CMSIS/Include/core_cm7.h **** #endif
1790:Drivers/CMSIS/Include/core_cm7.h **** 
1791:Drivers/CMSIS/Include/core_cm7.h **** #define FPU_BASE            (SCS_BASE +  0x0F30UL)                    /*!< Floating Point Unit */
1792:Drivers/CMSIS/Include/core_cm7.h **** #define FPU                 ((FPU_Type       *)     FPU_BASE      )   /*!< Floating Point Unit */
1793:Drivers/CMSIS/Include/core_cm7.h **** 
1794:Drivers/CMSIS/Include/core_cm7.h **** /*@} */
1795:Drivers/CMSIS/Include/core_cm7.h **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 89


1796:Drivers/CMSIS/Include/core_cm7.h **** 
1797:Drivers/CMSIS/Include/core_cm7.h **** 
1798:Drivers/CMSIS/Include/core_cm7.h **** /*******************************************************************************
1799:Drivers/CMSIS/Include/core_cm7.h ****  *                Hardware Abstraction Layer
1800:Drivers/CMSIS/Include/core_cm7.h ****   Core Function Interface contains:
1801:Drivers/CMSIS/Include/core_cm7.h ****   - Core NVIC Functions
1802:Drivers/CMSIS/Include/core_cm7.h ****   - Core SysTick Functions
1803:Drivers/CMSIS/Include/core_cm7.h ****   - Core Debug Functions
1804:Drivers/CMSIS/Include/core_cm7.h ****   - Core Register Access Functions
1805:Drivers/CMSIS/Include/core_cm7.h ****  ******************************************************************************/
1806:Drivers/CMSIS/Include/core_cm7.h **** /**
1807:Drivers/CMSIS/Include/core_cm7.h ****   \defgroup CMSIS_Core_FunctionInterface Functions and Instructions Reference
1808:Drivers/CMSIS/Include/core_cm7.h **** */
1809:Drivers/CMSIS/Include/core_cm7.h **** 
1810:Drivers/CMSIS/Include/core_cm7.h **** 
1811:Drivers/CMSIS/Include/core_cm7.h **** 
1812:Drivers/CMSIS/Include/core_cm7.h **** /* ##########################   NVIC functions  #################################### */
1813:Drivers/CMSIS/Include/core_cm7.h **** /**
1814:Drivers/CMSIS/Include/core_cm7.h ****   \ingroup  CMSIS_Core_FunctionInterface
1815:Drivers/CMSIS/Include/core_cm7.h ****   \defgroup CMSIS_Core_NVICFunctions NVIC Functions
1816:Drivers/CMSIS/Include/core_cm7.h ****   \brief    Functions that manage interrupts and exceptions via the NVIC.
1817:Drivers/CMSIS/Include/core_cm7.h ****   @{
1818:Drivers/CMSIS/Include/core_cm7.h ****  */
1819:Drivers/CMSIS/Include/core_cm7.h **** 
1820:Drivers/CMSIS/Include/core_cm7.h **** #ifdef CMSIS_NVIC_VIRTUAL
1821:Drivers/CMSIS/Include/core_cm7.h ****   #ifndef CMSIS_NVIC_VIRTUAL_HEADER_FILE
1822:Drivers/CMSIS/Include/core_cm7.h ****     #define CMSIS_NVIC_VIRTUAL_HEADER_FILE "cmsis_nvic_virtual.h"
1823:Drivers/CMSIS/Include/core_cm7.h ****   #endif
1824:Drivers/CMSIS/Include/core_cm7.h ****   #include CMSIS_NVIC_VIRTUAL_HEADER_FILE
1825:Drivers/CMSIS/Include/core_cm7.h **** #else
1826:Drivers/CMSIS/Include/core_cm7.h ****   #define NVIC_SetPriorityGrouping    __NVIC_SetPriorityGrouping
1827:Drivers/CMSIS/Include/core_cm7.h ****   #define NVIC_GetPriorityGrouping    __NVIC_GetPriorityGrouping
1828:Drivers/CMSIS/Include/core_cm7.h ****   #define NVIC_EnableIRQ              __NVIC_EnableIRQ
1829:Drivers/CMSIS/Include/core_cm7.h ****   #define NVIC_GetEnableIRQ           __NVIC_GetEnableIRQ
1830:Drivers/CMSIS/Include/core_cm7.h ****   #define NVIC_DisableIRQ             __NVIC_DisableIRQ
1831:Drivers/CMSIS/Include/core_cm7.h ****   #define NVIC_GetPendingIRQ          __NVIC_GetPendingIRQ
1832:Drivers/CMSIS/Include/core_cm7.h ****   #define NVIC_SetPendingIRQ          __NVIC_SetPendingIRQ
1833:Drivers/CMSIS/Include/core_cm7.h ****   #define NVIC_ClearPendingIRQ        __NVIC_ClearPendingIRQ
1834:Drivers/CMSIS/Include/core_cm7.h ****   #define NVIC_GetActive              __NVIC_GetActive
1835:Drivers/CMSIS/Include/core_cm7.h ****   #define NVIC_SetPriority            __NVIC_SetPriority
1836:Drivers/CMSIS/Include/core_cm7.h ****   #define NVIC_GetPriority            __NVIC_GetPriority
1837:Drivers/CMSIS/Include/core_cm7.h ****   #define NVIC_SystemReset            __NVIC_SystemReset
1838:Drivers/CMSIS/Include/core_cm7.h **** #endif /* CMSIS_NVIC_VIRTUAL */
1839:Drivers/CMSIS/Include/core_cm7.h **** 
1840:Drivers/CMSIS/Include/core_cm7.h **** #ifdef CMSIS_VECTAB_VIRTUAL
1841:Drivers/CMSIS/Include/core_cm7.h ****   #ifndef CMSIS_VECTAB_VIRTUAL_HEADER_FILE
1842:Drivers/CMSIS/Include/core_cm7.h ****     #define CMSIS_VECTAB_VIRTUAL_HEADER_FILE "cmsis_vectab_virtual.h"
1843:Drivers/CMSIS/Include/core_cm7.h ****   #endif
1844:Drivers/CMSIS/Include/core_cm7.h ****   #include CMSIS_VECTAB_VIRTUAL_HEADER_FILE
1845:Drivers/CMSIS/Include/core_cm7.h **** #else
1846:Drivers/CMSIS/Include/core_cm7.h ****   #define NVIC_SetVector              __NVIC_SetVector
1847:Drivers/CMSIS/Include/core_cm7.h ****   #define NVIC_GetVector              __NVIC_GetVector
1848:Drivers/CMSIS/Include/core_cm7.h **** #endif  /* (CMSIS_VECTAB_VIRTUAL) */
1849:Drivers/CMSIS/Include/core_cm7.h **** 
1850:Drivers/CMSIS/Include/core_cm7.h **** #define NVIC_USER_IRQ_OFFSET          16
1851:Drivers/CMSIS/Include/core_cm7.h **** 
1852:Drivers/CMSIS/Include/core_cm7.h **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 90


1853:Drivers/CMSIS/Include/core_cm7.h **** /* The following EXC_RETURN values are saved the LR on exception entry */
1854:Drivers/CMSIS/Include/core_cm7.h **** #define EXC_RETURN_HANDLER         (0xFFFFFFF1UL)     /* return to Handler mode, uses MSP after ret
1855:Drivers/CMSIS/Include/core_cm7.h **** #define EXC_RETURN_THREAD_MSP      (0xFFFFFFF9UL)     /* return to Thread mode, uses MSP after retu
1856:Drivers/CMSIS/Include/core_cm7.h **** #define EXC_RETURN_THREAD_PSP      (0xFFFFFFFDUL)     /* return to Thread mode, uses PSP after retu
1857:Drivers/CMSIS/Include/core_cm7.h **** #define EXC_RETURN_HANDLER_FPU     (0xFFFFFFE1UL)     /* return to Handler mode, uses MSP after ret
1858:Drivers/CMSIS/Include/core_cm7.h **** #define EXC_RETURN_THREAD_MSP_FPU  (0xFFFFFFE9UL)     /* return to Thread mode, uses MSP after retu
1859:Drivers/CMSIS/Include/core_cm7.h **** #define EXC_RETURN_THREAD_PSP_FPU  (0xFFFFFFEDUL)     /* return to Thread mode, uses PSP after retu
1860:Drivers/CMSIS/Include/core_cm7.h **** 
1861:Drivers/CMSIS/Include/core_cm7.h **** 
1862:Drivers/CMSIS/Include/core_cm7.h **** /**
1863:Drivers/CMSIS/Include/core_cm7.h ****   \brief   Set Priority Grouping
1864:Drivers/CMSIS/Include/core_cm7.h ****   \details Sets the priority grouping field using the required unlock sequence.
1865:Drivers/CMSIS/Include/core_cm7.h ****            The parameter PriorityGroup is assigned to the field SCB->AIRCR [10:8] PRIGROUP field.
1866:Drivers/CMSIS/Include/core_cm7.h ****            Only values from 0..7 are used.
1867:Drivers/CMSIS/Include/core_cm7.h ****            In case of a conflict between priority grouping and available
1868:Drivers/CMSIS/Include/core_cm7.h ****            priority bits (__NVIC_PRIO_BITS), the smallest possible priority group is set.
1869:Drivers/CMSIS/Include/core_cm7.h ****   \param [in]      PriorityGroup  Priority grouping field.
1870:Drivers/CMSIS/Include/core_cm7.h ****  */
1871:Drivers/CMSIS/Include/core_cm7.h **** __STATIC_INLINE void __NVIC_SetPriorityGrouping(uint32_t PriorityGroup)
1872:Drivers/CMSIS/Include/core_cm7.h **** {
1873:Drivers/CMSIS/Include/core_cm7.h ****   uint32_t reg_value;
1874:Drivers/CMSIS/Include/core_cm7.h ****   uint32_t PriorityGroupTmp = (PriorityGroup & (uint32_t)0x07UL);             /* only values 0..7 a
1875:Drivers/CMSIS/Include/core_cm7.h **** 
1876:Drivers/CMSIS/Include/core_cm7.h ****   reg_value  =  SCB->AIRCR;                                                   /* read old register 
1877:Drivers/CMSIS/Include/core_cm7.h ****   reg_value &= ~((uint32_t)(SCB_AIRCR_VECTKEY_Msk | SCB_AIRCR_PRIGROUP_Msk)); /* clear bits to chan
1878:Drivers/CMSIS/Include/core_cm7.h ****   reg_value  =  (reg_value                                   |
1879:Drivers/CMSIS/Include/core_cm7.h ****                 ((uint32_t)0x5FAUL << SCB_AIRCR_VECTKEY_Pos) |
1880:Drivers/CMSIS/Include/core_cm7.h ****                 (PriorityGroupTmp << SCB_AIRCR_PRIGROUP_Pos)  );              /* Insert write key a
1881:Drivers/CMSIS/Include/core_cm7.h ****   SCB->AIRCR =  reg_value;
1882:Drivers/CMSIS/Include/core_cm7.h **** }
1883:Drivers/CMSIS/Include/core_cm7.h **** 
1884:Drivers/CMSIS/Include/core_cm7.h **** 
1885:Drivers/CMSIS/Include/core_cm7.h **** /**
1886:Drivers/CMSIS/Include/core_cm7.h ****   \brief   Get Priority Grouping
1887:Drivers/CMSIS/Include/core_cm7.h ****   \details Reads the priority grouping field from the NVIC Interrupt Controller.
1888:Drivers/CMSIS/Include/core_cm7.h ****   \return                Priority grouping field (SCB->AIRCR [10:8] PRIGROUP field).
1889:Drivers/CMSIS/Include/core_cm7.h ****  */
1890:Drivers/CMSIS/Include/core_cm7.h **** __STATIC_INLINE uint32_t __NVIC_GetPriorityGrouping(void)
1891:Drivers/CMSIS/Include/core_cm7.h **** {
1892:Drivers/CMSIS/Include/core_cm7.h ****   return ((uint32_t)((SCB->AIRCR & SCB_AIRCR_PRIGROUP_Msk) >> SCB_AIRCR_PRIGROUP_Pos));
1893:Drivers/CMSIS/Include/core_cm7.h **** }
1894:Drivers/CMSIS/Include/core_cm7.h **** 
1895:Drivers/CMSIS/Include/core_cm7.h **** 
1896:Drivers/CMSIS/Include/core_cm7.h **** /**
1897:Drivers/CMSIS/Include/core_cm7.h ****   \brief   Enable Interrupt
1898:Drivers/CMSIS/Include/core_cm7.h ****   \details Enables a device specific interrupt in the NVIC interrupt controller.
1899:Drivers/CMSIS/Include/core_cm7.h ****   \param [in]      IRQn  Device specific interrupt number.
1900:Drivers/CMSIS/Include/core_cm7.h ****   \note    IRQn must not be negative.
1901:Drivers/CMSIS/Include/core_cm7.h ****  */
1902:Drivers/CMSIS/Include/core_cm7.h **** __STATIC_INLINE void __NVIC_EnableIRQ(IRQn_Type IRQn)
1903:Drivers/CMSIS/Include/core_cm7.h **** {
1904:Drivers/CMSIS/Include/core_cm7.h ****   if ((int32_t)(IRQn) >= 0)
1905:Drivers/CMSIS/Include/core_cm7.h ****   {
1906:Drivers/CMSIS/Include/core_cm7.h ****     __COMPILER_BARRIER();
1907:Drivers/CMSIS/Include/core_cm7.h ****     NVIC->ISER[(((uint32_t)IRQn) >> 5UL)] = (uint32_t)(1UL << (((uint32_t)IRQn) & 0x1FUL));
1908:Drivers/CMSIS/Include/core_cm7.h ****     __COMPILER_BARRIER();
1909:Drivers/CMSIS/Include/core_cm7.h ****   }
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 91


1910:Drivers/CMSIS/Include/core_cm7.h **** }
1911:Drivers/CMSIS/Include/core_cm7.h **** 
1912:Drivers/CMSIS/Include/core_cm7.h **** 
1913:Drivers/CMSIS/Include/core_cm7.h **** /**
1914:Drivers/CMSIS/Include/core_cm7.h ****   \brief   Get Interrupt Enable status
1915:Drivers/CMSIS/Include/core_cm7.h ****   \details Returns a device specific interrupt enable status from the NVIC interrupt controller.
1916:Drivers/CMSIS/Include/core_cm7.h ****   \param [in]      IRQn  Device specific interrupt number.
1917:Drivers/CMSIS/Include/core_cm7.h ****   \return             0  Interrupt is not enabled.
1918:Drivers/CMSIS/Include/core_cm7.h ****   \return             1  Interrupt is enabled.
1919:Drivers/CMSIS/Include/core_cm7.h ****   \note    IRQn must not be negative.
1920:Drivers/CMSIS/Include/core_cm7.h ****  */
1921:Drivers/CMSIS/Include/core_cm7.h **** __STATIC_INLINE uint32_t __NVIC_GetEnableIRQ(IRQn_Type IRQn)
1922:Drivers/CMSIS/Include/core_cm7.h **** {
1923:Drivers/CMSIS/Include/core_cm7.h ****   if ((int32_t)(IRQn) >= 0)
1924:Drivers/CMSIS/Include/core_cm7.h ****   {
1925:Drivers/CMSIS/Include/core_cm7.h ****     return((uint32_t)(((NVIC->ISER[(((uint32_t)IRQn) >> 5UL)] & (1UL << (((uint32_t)IRQn) & 0x1FUL)
1926:Drivers/CMSIS/Include/core_cm7.h ****   }
1927:Drivers/CMSIS/Include/core_cm7.h ****   else
1928:Drivers/CMSIS/Include/core_cm7.h ****   {
1929:Drivers/CMSIS/Include/core_cm7.h ****     return(0U);
1930:Drivers/CMSIS/Include/core_cm7.h ****   }
1931:Drivers/CMSIS/Include/core_cm7.h **** }
1932:Drivers/CMSIS/Include/core_cm7.h **** 
1933:Drivers/CMSIS/Include/core_cm7.h **** 
1934:Drivers/CMSIS/Include/core_cm7.h **** /**
1935:Drivers/CMSIS/Include/core_cm7.h ****   \brief   Disable Interrupt
1936:Drivers/CMSIS/Include/core_cm7.h ****   \details Disables a device specific interrupt in the NVIC interrupt controller.
1937:Drivers/CMSIS/Include/core_cm7.h ****   \param [in]      IRQn  Device specific interrupt number.
1938:Drivers/CMSIS/Include/core_cm7.h ****   \note    IRQn must not be negative.
1939:Drivers/CMSIS/Include/core_cm7.h ****  */
1940:Drivers/CMSIS/Include/core_cm7.h **** __STATIC_INLINE void __NVIC_DisableIRQ(IRQn_Type IRQn)
1941:Drivers/CMSIS/Include/core_cm7.h **** {
1942:Drivers/CMSIS/Include/core_cm7.h ****   if ((int32_t)(IRQn) >= 0)
1943:Drivers/CMSIS/Include/core_cm7.h ****   {
1944:Drivers/CMSIS/Include/core_cm7.h ****     NVIC->ICER[(((uint32_t)IRQn) >> 5UL)] = (uint32_t)(1UL << (((uint32_t)IRQn) & 0x1FUL));
1945:Drivers/CMSIS/Include/core_cm7.h ****     __DSB();
1946:Drivers/CMSIS/Include/core_cm7.h ****     __ISB();
1947:Drivers/CMSIS/Include/core_cm7.h ****   }
1948:Drivers/CMSIS/Include/core_cm7.h **** }
1949:Drivers/CMSIS/Include/core_cm7.h **** 
1950:Drivers/CMSIS/Include/core_cm7.h **** 
1951:Drivers/CMSIS/Include/core_cm7.h **** /**
1952:Drivers/CMSIS/Include/core_cm7.h ****   \brief   Get Pending Interrupt
1953:Drivers/CMSIS/Include/core_cm7.h ****   \details Reads the NVIC pending register and returns the pending bit for the specified device spe
1954:Drivers/CMSIS/Include/core_cm7.h ****   \param [in]      IRQn  Device specific interrupt number.
1955:Drivers/CMSIS/Include/core_cm7.h ****   \return             0  Interrupt status is not pending.
1956:Drivers/CMSIS/Include/core_cm7.h ****   \return             1  Interrupt status is pending.
1957:Drivers/CMSIS/Include/core_cm7.h ****   \note    IRQn must not be negative.
1958:Drivers/CMSIS/Include/core_cm7.h ****  */
1959:Drivers/CMSIS/Include/core_cm7.h **** __STATIC_INLINE uint32_t __NVIC_GetPendingIRQ(IRQn_Type IRQn)
1960:Drivers/CMSIS/Include/core_cm7.h **** {
1961:Drivers/CMSIS/Include/core_cm7.h ****   if ((int32_t)(IRQn) >= 0)
1962:Drivers/CMSIS/Include/core_cm7.h ****   {
1963:Drivers/CMSIS/Include/core_cm7.h ****     return((uint32_t)(((NVIC->ISPR[(((uint32_t)IRQn) >> 5UL)] & (1UL << (((uint32_t)IRQn) & 0x1FUL)
1964:Drivers/CMSIS/Include/core_cm7.h ****   }
1965:Drivers/CMSIS/Include/core_cm7.h ****   else
1966:Drivers/CMSIS/Include/core_cm7.h ****   {
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 92


1967:Drivers/CMSIS/Include/core_cm7.h ****     return(0U);
1968:Drivers/CMSIS/Include/core_cm7.h ****   }
1969:Drivers/CMSIS/Include/core_cm7.h **** }
1970:Drivers/CMSIS/Include/core_cm7.h **** 
1971:Drivers/CMSIS/Include/core_cm7.h **** 
1972:Drivers/CMSIS/Include/core_cm7.h **** /**
1973:Drivers/CMSIS/Include/core_cm7.h ****   \brief   Set Pending Interrupt
1974:Drivers/CMSIS/Include/core_cm7.h ****   \details Sets the pending bit of a device specific interrupt in the NVIC pending register.
1975:Drivers/CMSIS/Include/core_cm7.h ****   \param [in]      IRQn  Device specific interrupt number.
1976:Drivers/CMSIS/Include/core_cm7.h ****   \note    IRQn must not be negative.
1977:Drivers/CMSIS/Include/core_cm7.h ****  */
1978:Drivers/CMSIS/Include/core_cm7.h **** __STATIC_INLINE void __NVIC_SetPendingIRQ(IRQn_Type IRQn)
1979:Drivers/CMSIS/Include/core_cm7.h **** {
1980:Drivers/CMSIS/Include/core_cm7.h ****   if ((int32_t)(IRQn) >= 0)
1981:Drivers/CMSIS/Include/core_cm7.h ****   {
1982:Drivers/CMSIS/Include/core_cm7.h ****     NVIC->ISPR[(((uint32_t)IRQn) >> 5UL)] = (uint32_t)(1UL << (((uint32_t)IRQn) & 0x1FUL));
1983:Drivers/CMSIS/Include/core_cm7.h ****   }
1984:Drivers/CMSIS/Include/core_cm7.h **** }
1985:Drivers/CMSIS/Include/core_cm7.h **** 
1986:Drivers/CMSIS/Include/core_cm7.h **** 
1987:Drivers/CMSIS/Include/core_cm7.h **** /**
1988:Drivers/CMSIS/Include/core_cm7.h ****   \brief   Clear Pending Interrupt
1989:Drivers/CMSIS/Include/core_cm7.h ****   \details Clears the pending bit of a device specific interrupt in the NVIC pending register.
1990:Drivers/CMSIS/Include/core_cm7.h ****   \param [in]      IRQn  Device specific interrupt number.
1991:Drivers/CMSIS/Include/core_cm7.h ****   \note    IRQn must not be negative.
1992:Drivers/CMSIS/Include/core_cm7.h ****  */
1993:Drivers/CMSIS/Include/core_cm7.h **** __STATIC_INLINE void __NVIC_ClearPendingIRQ(IRQn_Type IRQn)
1994:Drivers/CMSIS/Include/core_cm7.h **** {
1995:Drivers/CMSIS/Include/core_cm7.h ****   if ((int32_t)(IRQn) >= 0)
1996:Drivers/CMSIS/Include/core_cm7.h ****   {
1997:Drivers/CMSIS/Include/core_cm7.h ****     NVIC->ICPR[(((uint32_t)IRQn) >> 5UL)] = (uint32_t)(1UL << (((uint32_t)IRQn) & 0x1FUL));
1998:Drivers/CMSIS/Include/core_cm7.h ****   }
1999:Drivers/CMSIS/Include/core_cm7.h **** }
2000:Drivers/CMSIS/Include/core_cm7.h **** 
2001:Drivers/CMSIS/Include/core_cm7.h **** 
2002:Drivers/CMSIS/Include/core_cm7.h **** /**
2003:Drivers/CMSIS/Include/core_cm7.h ****   \brief   Get Active Interrupt
2004:Drivers/CMSIS/Include/core_cm7.h ****   \details Reads the active register in the NVIC and returns the active bit for the device specific
2005:Drivers/CMSIS/Include/core_cm7.h ****   \param [in]      IRQn  Device specific interrupt number.
2006:Drivers/CMSIS/Include/core_cm7.h ****   \return             0  Interrupt status is not active.
2007:Drivers/CMSIS/Include/core_cm7.h ****   \return             1  Interrupt status is active.
2008:Drivers/CMSIS/Include/core_cm7.h ****   \note    IRQn must not be negative.
2009:Drivers/CMSIS/Include/core_cm7.h ****  */
2010:Drivers/CMSIS/Include/core_cm7.h **** __STATIC_INLINE uint32_t __NVIC_GetActive(IRQn_Type IRQn)
2011:Drivers/CMSIS/Include/core_cm7.h **** {
2012:Drivers/CMSIS/Include/core_cm7.h ****   if ((int32_t)(IRQn) >= 0)
2013:Drivers/CMSIS/Include/core_cm7.h ****   {
2014:Drivers/CMSIS/Include/core_cm7.h ****     return((uint32_t)(((NVIC->IABR[(((uint32_t)IRQn) >> 5UL)] & (1UL << (((uint32_t)IRQn) & 0x1FUL)
2015:Drivers/CMSIS/Include/core_cm7.h ****   }
2016:Drivers/CMSIS/Include/core_cm7.h ****   else
2017:Drivers/CMSIS/Include/core_cm7.h ****   {
2018:Drivers/CMSIS/Include/core_cm7.h ****     return(0U);
2019:Drivers/CMSIS/Include/core_cm7.h ****   }
2020:Drivers/CMSIS/Include/core_cm7.h **** }
2021:Drivers/CMSIS/Include/core_cm7.h **** 
2022:Drivers/CMSIS/Include/core_cm7.h **** 
2023:Drivers/CMSIS/Include/core_cm7.h **** /**
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 93


2024:Drivers/CMSIS/Include/core_cm7.h ****   \brief   Set Interrupt Priority
2025:Drivers/CMSIS/Include/core_cm7.h ****   \details Sets the priority of a device specific interrupt or a processor exception.
2026:Drivers/CMSIS/Include/core_cm7.h ****            The interrupt number can be positive to specify a device specific interrupt,
2027:Drivers/CMSIS/Include/core_cm7.h ****            or negative to specify a processor exception.
2028:Drivers/CMSIS/Include/core_cm7.h ****   \param [in]      IRQn  Interrupt number.
2029:Drivers/CMSIS/Include/core_cm7.h ****   \param [in]  priority  Priority to set.
2030:Drivers/CMSIS/Include/core_cm7.h ****   \note    The priority cannot be set for every processor exception.
2031:Drivers/CMSIS/Include/core_cm7.h ****  */
2032:Drivers/CMSIS/Include/core_cm7.h **** __STATIC_INLINE void __NVIC_SetPriority(IRQn_Type IRQn, uint32_t priority)
2033:Drivers/CMSIS/Include/core_cm7.h **** {
2034:Drivers/CMSIS/Include/core_cm7.h ****   if ((int32_t)(IRQn) >= 0)
2035:Drivers/CMSIS/Include/core_cm7.h ****   {
2036:Drivers/CMSIS/Include/core_cm7.h ****     NVIC->IP[((uint32_t)IRQn)]                = (uint8_t)((priority << (8U - __NVIC_PRIO_BITS)) & (
2037:Drivers/CMSIS/Include/core_cm7.h ****   }
2038:Drivers/CMSIS/Include/core_cm7.h ****   else
2039:Drivers/CMSIS/Include/core_cm7.h ****   {
2040:Drivers/CMSIS/Include/core_cm7.h ****     SCB->SHPR[(((uint32_t)IRQn) & 0xFUL)-4UL] = (uint8_t)((priority << (8U - __NVIC_PRIO_BITS)) & (
2041:Drivers/CMSIS/Include/core_cm7.h ****   }
2042:Drivers/CMSIS/Include/core_cm7.h **** }
2043:Drivers/CMSIS/Include/core_cm7.h **** 
2044:Drivers/CMSIS/Include/core_cm7.h **** 
2045:Drivers/CMSIS/Include/core_cm7.h **** /**
2046:Drivers/CMSIS/Include/core_cm7.h ****   \brief   Get Interrupt Priority
2047:Drivers/CMSIS/Include/core_cm7.h ****   \details Reads the priority of a device specific interrupt or a processor exception.
2048:Drivers/CMSIS/Include/core_cm7.h ****            The interrupt number can be positive to specify a device specific interrupt,
2049:Drivers/CMSIS/Include/core_cm7.h ****            or negative to specify a processor exception.
2050:Drivers/CMSIS/Include/core_cm7.h ****   \param [in]   IRQn  Interrupt number.
2051:Drivers/CMSIS/Include/core_cm7.h ****   \return             Interrupt Priority.
2052:Drivers/CMSIS/Include/core_cm7.h ****                       Value is aligned automatically to the implemented priority bits of the microc
2053:Drivers/CMSIS/Include/core_cm7.h ****  */
2054:Drivers/CMSIS/Include/core_cm7.h **** __STATIC_INLINE uint32_t __NVIC_GetPriority(IRQn_Type IRQn)
2055:Drivers/CMSIS/Include/core_cm7.h **** {
2056:Drivers/CMSIS/Include/core_cm7.h **** 
2057:Drivers/CMSIS/Include/core_cm7.h ****   if ((int32_t)(IRQn) >= 0)
2058:Drivers/CMSIS/Include/core_cm7.h ****   {
2059:Drivers/CMSIS/Include/core_cm7.h ****     return(((uint32_t)NVIC->IP[((uint32_t)IRQn)]                >> (8U - __NVIC_PRIO_BITS)));
2060:Drivers/CMSIS/Include/core_cm7.h ****   }
2061:Drivers/CMSIS/Include/core_cm7.h ****   else
2062:Drivers/CMSIS/Include/core_cm7.h ****   {
2063:Drivers/CMSIS/Include/core_cm7.h ****     return(((uint32_t)SCB->SHPR[(((uint32_t)IRQn) & 0xFUL)-4UL] >> (8U - __NVIC_PRIO_BITS)));
2064:Drivers/CMSIS/Include/core_cm7.h ****   }
2065:Drivers/CMSIS/Include/core_cm7.h **** }
2066:Drivers/CMSIS/Include/core_cm7.h **** 
2067:Drivers/CMSIS/Include/core_cm7.h **** 
2068:Drivers/CMSIS/Include/core_cm7.h **** /**
2069:Drivers/CMSIS/Include/core_cm7.h ****   \brief   Encode Priority
2070:Drivers/CMSIS/Include/core_cm7.h ****   \details Encodes the priority for an interrupt with the given priority group,
2071:Drivers/CMSIS/Include/core_cm7.h ****            preemptive priority value, and subpriority value.
2072:Drivers/CMSIS/Include/core_cm7.h ****            In case of a conflict between priority grouping and available
2073:Drivers/CMSIS/Include/core_cm7.h ****            priority bits (__NVIC_PRIO_BITS), the smallest possible priority group is set.
2074:Drivers/CMSIS/Include/core_cm7.h ****   \param [in]     PriorityGroup  Used priority group.
2075:Drivers/CMSIS/Include/core_cm7.h ****   \param [in]   PreemptPriority  Preemptive priority value (starting from 0).
2076:Drivers/CMSIS/Include/core_cm7.h ****   \param [in]       SubPriority  Subpriority value (starting from 0).
2077:Drivers/CMSIS/Include/core_cm7.h ****   \return                        Encoded priority. Value can be used in the function \ref NVIC_SetP
2078:Drivers/CMSIS/Include/core_cm7.h ****  */
2079:Drivers/CMSIS/Include/core_cm7.h **** __STATIC_INLINE uint32_t NVIC_EncodePriority (uint32_t PriorityGroup, uint32_t PreemptPriority, uin
2080:Drivers/CMSIS/Include/core_cm7.h **** {
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 94


2081:Drivers/CMSIS/Include/core_cm7.h ****   uint32_t PriorityGroupTmp = (PriorityGroup & (uint32_t)0x07UL);   /* only values 0..7 are used   
2082:Drivers/CMSIS/Include/core_cm7.h ****   uint32_t PreemptPriorityBits;
2083:Drivers/CMSIS/Include/core_cm7.h ****   uint32_t SubPriorityBits;
2084:Drivers/CMSIS/Include/core_cm7.h **** 
2085:Drivers/CMSIS/Include/core_cm7.h ****   PreemptPriorityBits = ((7UL - PriorityGroupTmp) > (uint32_t)(__NVIC_PRIO_BITS)) ? (uint32_t)(__NV
2086:Drivers/CMSIS/Include/core_cm7.h ****   SubPriorityBits     = ((PriorityGroupTmp + (uint32_t)(__NVIC_PRIO_BITS)) < (uint32_t)7UL) ? (uint
2087:Drivers/CMSIS/Include/core_cm7.h **** 
2088:Drivers/CMSIS/Include/core_cm7.h ****   return (
2089:Drivers/CMSIS/Include/core_cm7.h ****            ((PreemptPriority & (uint32_t)((1UL << (PreemptPriorityBits)) - 1UL)) << SubPriorityBits
2090:Drivers/CMSIS/Include/core_cm7.h ****            ((SubPriority     & (uint32_t)((1UL << (SubPriorityBits    )) - 1UL)))
2091:Drivers/CMSIS/Include/core_cm7.h ****          );
2092:Drivers/CMSIS/Include/core_cm7.h **** }
2093:Drivers/CMSIS/Include/core_cm7.h **** 
2094:Drivers/CMSIS/Include/core_cm7.h **** 
2095:Drivers/CMSIS/Include/core_cm7.h **** /**
2096:Drivers/CMSIS/Include/core_cm7.h ****   \brief   Decode Priority
2097:Drivers/CMSIS/Include/core_cm7.h ****   \details Decodes an interrupt priority value with a given priority group to
2098:Drivers/CMSIS/Include/core_cm7.h ****            preemptive priority value and subpriority value.
2099:Drivers/CMSIS/Include/core_cm7.h ****            In case of a conflict between priority grouping and available
2100:Drivers/CMSIS/Include/core_cm7.h ****            priority bits (__NVIC_PRIO_BITS) the smallest possible priority group is set.
2101:Drivers/CMSIS/Include/core_cm7.h ****   \param [in]         Priority   Priority value, which can be retrieved with the function \ref NVIC
2102:Drivers/CMSIS/Include/core_cm7.h ****   \param [in]     PriorityGroup  Used priority group.
2103:Drivers/CMSIS/Include/core_cm7.h ****   \param [out] pPreemptPriority  Preemptive priority value (starting from 0).
2104:Drivers/CMSIS/Include/core_cm7.h ****   \param [out]     pSubPriority  Subpriority value (starting from 0).
2105:Drivers/CMSIS/Include/core_cm7.h ****  */
2106:Drivers/CMSIS/Include/core_cm7.h **** __STATIC_INLINE void NVIC_DecodePriority (uint32_t Priority, uint32_t PriorityGroup, uint32_t* cons
2107:Drivers/CMSIS/Include/core_cm7.h **** {
2108:Drivers/CMSIS/Include/core_cm7.h ****   uint32_t PriorityGroupTmp = (PriorityGroup & (uint32_t)0x07UL);   /* only values 0..7 are used   
2109:Drivers/CMSIS/Include/core_cm7.h ****   uint32_t PreemptPriorityBits;
2110:Drivers/CMSIS/Include/core_cm7.h ****   uint32_t SubPriorityBits;
2111:Drivers/CMSIS/Include/core_cm7.h **** 
2112:Drivers/CMSIS/Include/core_cm7.h ****   PreemptPriorityBits = ((7UL - PriorityGroupTmp) > (uint32_t)(__NVIC_PRIO_BITS)) ? (uint32_t)(__NV
2113:Drivers/CMSIS/Include/core_cm7.h ****   SubPriorityBits     = ((PriorityGroupTmp + (uint32_t)(__NVIC_PRIO_BITS)) < (uint32_t)7UL) ? (uint
2114:Drivers/CMSIS/Include/core_cm7.h **** 
2115:Drivers/CMSIS/Include/core_cm7.h ****   *pPreemptPriority = (Priority >> SubPriorityBits) & (uint32_t)((1UL << (PreemptPriorityBits)) - 1
2116:Drivers/CMSIS/Include/core_cm7.h ****   *pSubPriority     = (Priority                   ) & (uint32_t)((1UL << (SubPriorityBits    )) - 1
2117:Drivers/CMSIS/Include/core_cm7.h **** }
2118:Drivers/CMSIS/Include/core_cm7.h **** 
2119:Drivers/CMSIS/Include/core_cm7.h **** 
2120:Drivers/CMSIS/Include/core_cm7.h **** /**
2121:Drivers/CMSIS/Include/core_cm7.h ****   \brief   Set Interrupt Vector
2122:Drivers/CMSIS/Include/core_cm7.h ****   \details Sets an interrupt vector in SRAM based interrupt vector table.
2123:Drivers/CMSIS/Include/core_cm7.h ****            The interrupt number can be positive to specify a device specific interrupt,
2124:Drivers/CMSIS/Include/core_cm7.h ****            or negative to specify a processor exception.
2125:Drivers/CMSIS/Include/core_cm7.h ****            VTOR must been relocated to SRAM before.
2126:Drivers/CMSIS/Include/core_cm7.h ****   \param [in]   IRQn      Interrupt number
2127:Drivers/CMSIS/Include/core_cm7.h ****   \param [in]   vector    Address of interrupt handler function
2128:Drivers/CMSIS/Include/core_cm7.h ****  */
2129:Drivers/CMSIS/Include/core_cm7.h **** __STATIC_INLINE void __NVIC_SetVector(IRQn_Type IRQn, uint32_t vector)
2130:Drivers/CMSIS/Include/core_cm7.h **** {
2131:Drivers/CMSIS/Include/core_cm7.h ****   uint32_t vectors = (uint32_t )SCB->VTOR;
2132:Drivers/CMSIS/Include/core_cm7.h ****   (* (int *) (vectors + ((int32_t)IRQn + NVIC_USER_IRQ_OFFSET) * 4)) = vector;
2133:Drivers/CMSIS/Include/core_cm7.h ****   __DSB();
2134:Drivers/CMSIS/Include/core_cm7.h **** }
2135:Drivers/CMSIS/Include/core_cm7.h **** 
2136:Drivers/CMSIS/Include/core_cm7.h **** 
2137:Drivers/CMSIS/Include/core_cm7.h **** /**
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 95


2138:Drivers/CMSIS/Include/core_cm7.h ****   \brief   Get Interrupt Vector
2139:Drivers/CMSIS/Include/core_cm7.h ****   \details Reads an interrupt vector from interrupt vector table.
2140:Drivers/CMSIS/Include/core_cm7.h ****            The interrupt number can be positive to specify a device specific interrupt,
2141:Drivers/CMSIS/Include/core_cm7.h ****            or negative to specify a processor exception.
2142:Drivers/CMSIS/Include/core_cm7.h ****   \param [in]   IRQn      Interrupt number.
2143:Drivers/CMSIS/Include/core_cm7.h ****   \return                 Address of interrupt handler function
2144:Drivers/CMSIS/Include/core_cm7.h ****  */
2145:Drivers/CMSIS/Include/core_cm7.h **** __STATIC_INLINE uint32_t __NVIC_GetVector(IRQn_Type IRQn)
2146:Drivers/CMSIS/Include/core_cm7.h **** {
2147:Drivers/CMSIS/Include/core_cm7.h ****   uint32_t vectors = (uint32_t )SCB->VTOR;
2148:Drivers/CMSIS/Include/core_cm7.h ****   return (uint32_t)(* (int *) (vectors + ((int32_t)IRQn + NVIC_USER_IRQ_OFFSET) * 4));
2149:Drivers/CMSIS/Include/core_cm7.h **** }
2150:Drivers/CMSIS/Include/core_cm7.h **** 
2151:Drivers/CMSIS/Include/core_cm7.h **** 
2152:Drivers/CMSIS/Include/core_cm7.h **** /**
2153:Drivers/CMSIS/Include/core_cm7.h ****   \brief   System Reset
2154:Drivers/CMSIS/Include/core_cm7.h ****   \details Initiates a system reset request to reset the MCU.
2155:Drivers/CMSIS/Include/core_cm7.h ****  */
2156:Drivers/CMSIS/Include/core_cm7.h **** __NO_RETURN __STATIC_INLINE void __NVIC_SystemReset(void)
2157:Drivers/CMSIS/Include/core_cm7.h **** {
2158:Drivers/CMSIS/Include/core_cm7.h ****   __DSB();                                                          /* Ensure all outstanding memor
2159:Drivers/CMSIS/Include/core_cm7.h ****                                                                        buffered write are completed
2160:Drivers/CMSIS/Include/core_cm7.h ****   SCB->AIRCR  = (uint32_t)((0x5FAUL << SCB_AIRCR_VECTKEY_Pos)    |
2161:Drivers/CMSIS/Include/core_cm7.h ****                            (SCB->AIRCR & SCB_AIRCR_PRIGROUP_Msk) |
2162:Drivers/CMSIS/Include/core_cm7.h ****                             SCB_AIRCR_SYSRESETREQ_Msk    );         /* Keep priority group unchange
2163:Drivers/CMSIS/Include/core_cm7.h ****   __DSB();                                                          /* Ensure completion of memory 
2164:Drivers/CMSIS/Include/core_cm7.h **** 
2165:Drivers/CMSIS/Include/core_cm7.h ****   for(;;)                                                           /* wait until reset */
2166:Drivers/CMSIS/Include/core_cm7.h ****   {
2167:Drivers/CMSIS/Include/core_cm7.h ****     __NOP();
2168:Drivers/CMSIS/Include/core_cm7.h ****   }
2169:Drivers/CMSIS/Include/core_cm7.h **** }
2170:Drivers/CMSIS/Include/core_cm7.h **** 
2171:Drivers/CMSIS/Include/core_cm7.h **** /*@} end of CMSIS_Core_NVICFunctions */
2172:Drivers/CMSIS/Include/core_cm7.h **** 
2173:Drivers/CMSIS/Include/core_cm7.h **** 
2174:Drivers/CMSIS/Include/core_cm7.h **** /* ##########################  MPU functions  #################################### */
2175:Drivers/CMSIS/Include/core_cm7.h **** 
2176:Drivers/CMSIS/Include/core_cm7.h **** #if defined (__MPU_PRESENT) && (__MPU_PRESENT == 1U)
2177:Drivers/CMSIS/Include/core_cm7.h **** 
2178:Drivers/CMSIS/Include/core_cm7.h **** #include "mpu_armv7.h"
2179:Drivers/CMSIS/Include/core_cm7.h **** 
2180:Drivers/CMSIS/Include/core_cm7.h **** #endif
2181:Drivers/CMSIS/Include/core_cm7.h **** 
2182:Drivers/CMSIS/Include/core_cm7.h **** 
2183:Drivers/CMSIS/Include/core_cm7.h **** /* ##########################  FPU functions  #################################### */
2184:Drivers/CMSIS/Include/core_cm7.h **** /**
2185:Drivers/CMSIS/Include/core_cm7.h ****   \ingroup  CMSIS_Core_FunctionInterface
2186:Drivers/CMSIS/Include/core_cm7.h ****   \defgroup CMSIS_Core_FpuFunctions FPU Functions
2187:Drivers/CMSIS/Include/core_cm7.h ****   \brief    Function that provides FPU type.
2188:Drivers/CMSIS/Include/core_cm7.h ****   @{
2189:Drivers/CMSIS/Include/core_cm7.h ****  */
2190:Drivers/CMSIS/Include/core_cm7.h **** 
2191:Drivers/CMSIS/Include/core_cm7.h **** /**
2192:Drivers/CMSIS/Include/core_cm7.h ****   \brief   get FPU type
2193:Drivers/CMSIS/Include/core_cm7.h ****   \details returns the FPU type
2194:Drivers/CMSIS/Include/core_cm7.h ****   \returns
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 96


2195:Drivers/CMSIS/Include/core_cm7.h ****    - \b  0: No FPU
2196:Drivers/CMSIS/Include/core_cm7.h ****    - \b  1: Single precision FPU
2197:Drivers/CMSIS/Include/core_cm7.h ****    - \b  2: Double + Single precision FPU
2198:Drivers/CMSIS/Include/core_cm7.h ****  */
2199:Drivers/CMSIS/Include/core_cm7.h **** __STATIC_INLINE uint32_t SCB_GetFPUType(void)
2200:Drivers/CMSIS/Include/core_cm7.h **** {
2201:Drivers/CMSIS/Include/core_cm7.h ****   uint32_t mvfr0;
2202:Drivers/CMSIS/Include/core_cm7.h **** 
2203:Drivers/CMSIS/Include/core_cm7.h ****   mvfr0 = SCB->MVFR0;
2204:Drivers/CMSIS/Include/core_cm7.h ****   if      ((mvfr0 & (FPU_MVFR0_Single_precision_Msk | FPU_MVFR0_Double_precision_Msk)) == 0x220U)
2205:Drivers/CMSIS/Include/core_cm7.h ****   {
2206:Drivers/CMSIS/Include/core_cm7.h ****     return 2U;           /* Double + Single precision FPU */
2207:Drivers/CMSIS/Include/core_cm7.h ****   }
2208:Drivers/CMSIS/Include/core_cm7.h ****   else if ((mvfr0 & (FPU_MVFR0_Single_precision_Msk | FPU_MVFR0_Double_precision_Msk)) == 0x020U)
2209:Drivers/CMSIS/Include/core_cm7.h ****   {
2210:Drivers/CMSIS/Include/core_cm7.h ****     return 1U;           /* Single precision FPU */
2211:Drivers/CMSIS/Include/core_cm7.h ****   }
2212:Drivers/CMSIS/Include/core_cm7.h ****   else
2213:Drivers/CMSIS/Include/core_cm7.h ****   {
2214:Drivers/CMSIS/Include/core_cm7.h ****     return 0U;           /* No FPU */
2215:Drivers/CMSIS/Include/core_cm7.h ****   }
2216:Drivers/CMSIS/Include/core_cm7.h **** }
2217:Drivers/CMSIS/Include/core_cm7.h **** 
2218:Drivers/CMSIS/Include/core_cm7.h **** /*@} end of CMSIS_Core_FpuFunctions */
2219:Drivers/CMSIS/Include/core_cm7.h **** 
2220:Drivers/CMSIS/Include/core_cm7.h **** 
2221:Drivers/CMSIS/Include/core_cm7.h **** 
2222:Drivers/CMSIS/Include/core_cm7.h **** /* ##########################  Cache functions  #################################### */
2223:Drivers/CMSIS/Include/core_cm7.h **** /**
2224:Drivers/CMSIS/Include/core_cm7.h ****   \ingroup  CMSIS_Core_FunctionInterface
2225:Drivers/CMSIS/Include/core_cm7.h ****   \defgroup CMSIS_Core_CacheFunctions Cache Functions
2226:Drivers/CMSIS/Include/core_cm7.h ****   \brief    Functions that configure Instruction and Data cache.
2227:Drivers/CMSIS/Include/core_cm7.h ****   @{
2228:Drivers/CMSIS/Include/core_cm7.h ****  */
2229:Drivers/CMSIS/Include/core_cm7.h **** 
2230:Drivers/CMSIS/Include/core_cm7.h **** /* Cache Size ID Register Macros */
2231:Drivers/CMSIS/Include/core_cm7.h **** #define CCSIDR_WAYS(x)         (((x) & SCB_CCSIDR_ASSOCIATIVITY_Msk) >> SCB_CCSIDR_ASSOCIATIVITY_Po
2232:Drivers/CMSIS/Include/core_cm7.h **** #define CCSIDR_SETS(x)         (((x) & SCB_CCSIDR_NUMSETS_Msk      ) >> SCB_CCSIDR_NUMSETS_Pos     
2233:Drivers/CMSIS/Include/core_cm7.h **** 
2234:Drivers/CMSIS/Include/core_cm7.h **** #define __SCB_DCACHE_LINE_SIZE  32U /*!< Cortex-M7 cache line size is fixed to 32 bytes (8 words). 
2235:Drivers/CMSIS/Include/core_cm7.h **** #define __SCB_ICACHE_LINE_SIZE  32U /*!< Cortex-M7 cache line size is fixed to 32 bytes (8 words). 
2236:Drivers/CMSIS/Include/core_cm7.h **** 
2237:Drivers/CMSIS/Include/core_cm7.h **** /**
2238:Drivers/CMSIS/Include/core_cm7.h ****   \brief   Enable I-Cache
2239:Drivers/CMSIS/Include/core_cm7.h ****   \details Turns on I-Cache
2240:Drivers/CMSIS/Include/core_cm7.h ****   */
2241:Drivers/CMSIS/Include/core_cm7.h **** __STATIC_FORCEINLINE void SCB_EnableICache (void)
2242:Drivers/CMSIS/Include/core_cm7.h **** {
2243:Drivers/CMSIS/Include/core_cm7.h ****   #if defined (__ICACHE_PRESENT) && (__ICACHE_PRESENT == 1U)
2244:Drivers/CMSIS/Include/core_cm7.h ****     if (SCB->CCR & SCB_CCR_IC_Msk) return;  /* return if ICache is already enabled */
2245:Drivers/CMSIS/Include/core_cm7.h **** 
2246:Drivers/CMSIS/Include/core_cm7.h ****     __DSB();
2247:Drivers/CMSIS/Include/core_cm7.h ****     __ISB();
2248:Drivers/CMSIS/Include/core_cm7.h ****     SCB->ICIALLU = 0UL;                     /* invalidate I-Cache */
2249:Drivers/CMSIS/Include/core_cm7.h ****     __DSB();
2250:Drivers/CMSIS/Include/core_cm7.h ****     __ISB();
2251:Drivers/CMSIS/Include/core_cm7.h ****     SCB->CCR |=  (uint32_t)SCB_CCR_IC_Msk;  /* enable I-Cache */
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 97


2252:Drivers/CMSIS/Include/core_cm7.h ****     __DSB();
2253:Drivers/CMSIS/Include/core_cm7.h ****     __ISB();
2254:Drivers/CMSIS/Include/core_cm7.h ****   #endif
2255:Drivers/CMSIS/Include/core_cm7.h **** }
2256:Drivers/CMSIS/Include/core_cm7.h **** 
2257:Drivers/CMSIS/Include/core_cm7.h **** 
2258:Drivers/CMSIS/Include/core_cm7.h **** /**
2259:Drivers/CMSIS/Include/core_cm7.h ****   \brief   Disable I-Cache
2260:Drivers/CMSIS/Include/core_cm7.h ****   \details Turns off I-Cache
2261:Drivers/CMSIS/Include/core_cm7.h ****   */
2262:Drivers/CMSIS/Include/core_cm7.h **** __STATIC_FORCEINLINE void SCB_DisableICache (void)
2263:Drivers/CMSIS/Include/core_cm7.h **** {
2264:Drivers/CMSIS/Include/core_cm7.h ****   #if defined (__ICACHE_PRESENT) && (__ICACHE_PRESENT == 1U)
2265:Drivers/CMSIS/Include/core_cm7.h ****     __DSB();
2266:Drivers/CMSIS/Include/core_cm7.h ****     __ISB();
2267:Drivers/CMSIS/Include/core_cm7.h ****     SCB->CCR &= ~(uint32_t)SCB_CCR_IC_Msk;  /* disable I-Cache */
2268:Drivers/CMSIS/Include/core_cm7.h ****     SCB->ICIALLU = 0UL;                     /* invalidate I-Cache */
2269:Drivers/CMSIS/Include/core_cm7.h ****     __DSB();
2270:Drivers/CMSIS/Include/core_cm7.h ****     __ISB();
2271:Drivers/CMSIS/Include/core_cm7.h ****   #endif
2272:Drivers/CMSIS/Include/core_cm7.h **** }
2273:Drivers/CMSIS/Include/core_cm7.h **** 
2274:Drivers/CMSIS/Include/core_cm7.h **** 
2275:Drivers/CMSIS/Include/core_cm7.h **** /**
2276:Drivers/CMSIS/Include/core_cm7.h ****   \brief   Invalidate I-Cache
2277:Drivers/CMSIS/Include/core_cm7.h ****   \details Invalidates I-Cache
2278:Drivers/CMSIS/Include/core_cm7.h ****   */
2279:Drivers/CMSIS/Include/core_cm7.h **** __STATIC_FORCEINLINE void SCB_InvalidateICache (void)
2280:Drivers/CMSIS/Include/core_cm7.h **** {
2281:Drivers/CMSIS/Include/core_cm7.h ****   #if defined (__ICACHE_PRESENT) && (__ICACHE_PRESENT == 1U)
2282:Drivers/CMSIS/Include/core_cm7.h ****     __DSB();
2283:Drivers/CMSIS/Include/core_cm7.h ****     __ISB();
2284:Drivers/CMSIS/Include/core_cm7.h ****     SCB->ICIALLU = 0UL;
2285:Drivers/CMSIS/Include/core_cm7.h ****     __DSB();
2286:Drivers/CMSIS/Include/core_cm7.h ****     __ISB();
2287:Drivers/CMSIS/Include/core_cm7.h ****   #endif
2288:Drivers/CMSIS/Include/core_cm7.h **** }
2289:Drivers/CMSIS/Include/core_cm7.h **** 
2290:Drivers/CMSIS/Include/core_cm7.h **** 
2291:Drivers/CMSIS/Include/core_cm7.h **** /**
2292:Drivers/CMSIS/Include/core_cm7.h ****   \brief   I-Cache Invalidate by address
2293:Drivers/CMSIS/Include/core_cm7.h ****   \details Invalidates I-Cache for the given address.
2294:Drivers/CMSIS/Include/core_cm7.h ****            I-Cache is invalidated starting from a 32 byte aligned address in 32 byte granularity.
2295:Drivers/CMSIS/Include/core_cm7.h ****            I-Cache memory blocks which are part of given address + given size are invalidated.
2296:Drivers/CMSIS/Include/core_cm7.h ****   \param[in]   addr    address
2297:Drivers/CMSIS/Include/core_cm7.h ****   \param[in]   isize   size of memory block (in number of bytes)
2298:Drivers/CMSIS/Include/core_cm7.h **** */
2299:Drivers/CMSIS/Include/core_cm7.h **** __STATIC_FORCEINLINE void SCB_InvalidateICache_by_Addr (void *addr, int32_t isize)
2300:Drivers/CMSIS/Include/core_cm7.h **** {
2301:Drivers/CMSIS/Include/core_cm7.h ****   #if defined (__ICACHE_PRESENT) && (__ICACHE_PRESENT == 1U)
2302:Drivers/CMSIS/Include/core_cm7.h ****     if ( isize > 0 ) {
2303:Drivers/CMSIS/Include/core_cm7.h ****        int32_t op_size = isize + (((uint32_t)addr) & (__SCB_ICACHE_LINE_SIZE - 1U));
2304:Drivers/CMSIS/Include/core_cm7.h ****       uint32_t op_addr = (uint32_t)addr /* & ~(__SCB_ICACHE_LINE_SIZE - 1U) */;
2305:Drivers/CMSIS/Include/core_cm7.h **** 
2306:Drivers/CMSIS/Include/core_cm7.h ****       __DSB();
2307:Drivers/CMSIS/Include/core_cm7.h **** 
2308:Drivers/CMSIS/Include/core_cm7.h ****       do {
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 98


2309:Drivers/CMSIS/Include/core_cm7.h ****         SCB->ICIMVAU = op_addr;             /* register accepts only 32byte aligned values, only bi
2310:Drivers/CMSIS/Include/core_cm7.h ****         op_addr += __SCB_ICACHE_LINE_SIZE;
2311:Drivers/CMSIS/Include/core_cm7.h ****         op_size -= __SCB_ICACHE_LINE_SIZE;
2312:Drivers/CMSIS/Include/core_cm7.h ****       } while ( op_size > 0 );
2313:Drivers/CMSIS/Include/core_cm7.h **** 
2314:Drivers/CMSIS/Include/core_cm7.h ****       __DSB();
2315:Drivers/CMSIS/Include/core_cm7.h ****       __ISB();
2316:Drivers/CMSIS/Include/core_cm7.h ****     }
2317:Drivers/CMSIS/Include/core_cm7.h ****   #endif
2318:Drivers/CMSIS/Include/core_cm7.h **** }
2319:Drivers/CMSIS/Include/core_cm7.h **** 
2320:Drivers/CMSIS/Include/core_cm7.h **** 
2321:Drivers/CMSIS/Include/core_cm7.h **** /**
2322:Drivers/CMSIS/Include/core_cm7.h ****   \brief   Enable D-Cache
2323:Drivers/CMSIS/Include/core_cm7.h ****   \details Turns on D-Cache
2324:Drivers/CMSIS/Include/core_cm7.h ****   */
2325:Drivers/CMSIS/Include/core_cm7.h **** __STATIC_FORCEINLINE void SCB_EnableDCache (void)
2326:Drivers/CMSIS/Include/core_cm7.h **** {
2327:Drivers/CMSIS/Include/core_cm7.h ****   #if defined (__DCACHE_PRESENT) && (__DCACHE_PRESENT == 1U)
2328:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t ccsidr;
2329:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t sets;
2330:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t ways;
2331:Drivers/CMSIS/Include/core_cm7.h **** 
2332:Drivers/CMSIS/Include/core_cm7.h ****     if (SCB->CCR & SCB_CCR_DC_Msk) return;  /* return if DCache is already enabled */
2333:Drivers/CMSIS/Include/core_cm7.h **** 
2334:Drivers/CMSIS/Include/core_cm7.h ****     SCB->CSSELR = 0U;                       /* select Level 1 data cache */
2335:Drivers/CMSIS/Include/core_cm7.h ****     __DSB();
2336:Drivers/CMSIS/Include/core_cm7.h **** 
2337:Drivers/CMSIS/Include/core_cm7.h ****     ccsidr = SCB->CCSIDR;
2338:Drivers/CMSIS/Include/core_cm7.h **** 
2339:Drivers/CMSIS/Include/core_cm7.h ****                                             /* invalidate D-Cache */
2340:Drivers/CMSIS/Include/core_cm7.h ****     sets = (uint32_t)(CCSIDR_SETS(ccsidr));
2341:Drivers/CMSIS/Include/core_cm7.h ****     do {
2342:Drivers/CMSIS/Include/core_cm7.h ****       ways = (uint32_t)(CCSIDR_WAYS(ccsidr));
2343:Drivers/CMSIS/Include/core_cm7.h ****       do {
2344:Drivers/CMSIS/Include/core_cm7.h ****         SCB->DCISW = (((sets << SCB_DCISW_SET_Pos) & SCB_DCISW_SET_Msk) |
2345:Drivers/CMSIS/Include/core_cm7.h ****                       ((ways << SCB_DCISW_WAY_Pos) & SCB_DCISW_WAY_Msk)  );
2346:Drivers/CMSIS/Include/core_cm7.h ****         #if defined ( __CC_ARM )
2347:Drivers/CMSIS/Include/core_cm7.h ****           __schedule_barrier();
2348:Drivers/CMSIS/Include/core_cm7.h ****         #endif
2349:Drivers/CMSIS/Include/core_cm7.h ****       } while (ways-- != 0U);
2350:Drivers/CMSIS/Include/core_cm7.h ****     } while(sets-- != 0U);
2351:Drivers/CMSIS/Include/core_cm7.h ****     __DSB();
2352:Drivers/CMSIS/Include/core_cm7.h **** 
2353:Drivers/CMSIS/Include/core_cm7.h ****     SCB->CCR |=  (uint32_t)SCB_CCR_DC_Msk;  /* enable D-Cache */
2354:Drivers/CMSIS/Include/core_cm7.h **** 
2355:Drivers/CMSIS/Include/core_cm7.h ****     __DSB();
2356:Drivers/CMSIS/Include/core_cm7.h ****     __ISB();
2357:Drivers/CMSIS/Include/core_cm7.h ****   #endif
2358:Drivers/CMSIS/Include/core_cm7.h **** }
2359:Drivers/CMSIS/Include/core_cm7.h **** 
2360:Drivers/CMSIS/Include/core_cm7.h **** 
2361:Drivers/CMSIS/Include/core_cm7.h **** /**
2362:Drivers/CMSIS/Include/core_cm7.h ****   \brief   Disable D-Cache
2363:Drivers/CMSIS/Include/core_cm7.h ****   \details Turns off D-Cache
2364:Drivers/CMSIS/Include/core_cm7.h ****   */
2365:Drivers/CMSIS/Include/core_cm7.h **** __STATIC_FORCEINLINE void SCB_DisableDCache (void)
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 99


2366:Drivers/CMSIS/Include/core_cm7.h **** {
2367:Drivers/CMSIS/Include/core_cm7.h ****   #if defined (__DCACHE_PRESENT) && (__DCACHE_PRESENT == 1U)
2368:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t ccsidr;
2369:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t sets;
2370:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t ways;
2371:Drivers/CMSIS/Include/core_cm7.h **** 
2372:Drivers/CMSIS/Include/core_cm7.h ****     SCB->CSSELR = 0U;                       /* select Level 1 data cache */
2373:Drivers/CMSIS/Include/core_cm7.h ****     __DSB();
2374:Drivers/CMSIS/Include/core_cm7.h **** 
2375:Drivers/CMSIS/Include/core_cm7.h ****     SCB->CCR &= ~(uint32_t)SCB_CCR_DC_Msk;  /* disable D-Cache */
2376:Drivers/CMSIS/Include/core_cm7.h ****     __DSB();
2377:Drivers/CMSIS/Include/core_cm7.h **** 
2378:Drivers/CMSIS/Include/core_cm7.h ****     ccsidr = SCB->CCSIDR;
2379:Drivers/CMSIS/Include/core_cm7.h **** 
2380:Drivers/CMSIS/Include/core_cm7.h ****                                             /* clean & invalidate D-Cache */
2381:Drivers/CMSIS/Include/core_cm7.h ****     sets = (uint32_t)(CCSIDR_SETS(ccsidr));
2382:Drivers/CMSIS/Include/core_cm7.h ****     do {
2383:Drivers/CMSIS/Include/core_cm7.h ****       ways = (uint32_t)(CCSIDR_WAYS(ccsidr));
2384:Drivers/CMSIS/Include/core_cm7.h ****       do {
2385:Drivers/CMSIS/Include/core_cm7.h ****         SCB->DCCISW = (((sets << SCB_DCCISW_SET_Pos) & SCB_DCCISW_SET_Msk) |
2386:Drivers/CMSIS/Include/core_cm7.h ****                        ((ways << SCB_DCCISW_WAY_Pos) & SCB_DCCISW_WAY_Msk)  );
2387:Drivers/CMSIS/Include/core_cm7.h ****         #if defined ( __CC_ARM )
2388:Drivers/CMSIS/Include/core_cm7.h ****           __schedule_barrier();
2389:Drivers/CMSIS/Include/core_cm7.h ****         #endif
2390:Drivers/CMSIS/Include/core_cm7.h ****       } while (ways-- != 0U);
2391:Drivers/CMSIS/Include/core_cm7.h ****     } while(sets-- != 0U);
2392:Drivers/CMSIS/Include/core_cm7.h **** 
2393:Drivers/CMSIS/Include/core_cm7.h ****     __DSB();
2394:Drivers/CMSIS/Include/core_cm7.h ****     __ISB();
2395:Drivers/CMSIS/Include/core_cm7.h ****   #endif
2396:Drivers/CMSIS/Include/core_cm7.h **** }
2397:Drivers/CMSIS/Include/core_cm7.h **** 
2398:Drivers/CMSIS/Include/core_cm7.h **** 
2399:Drivers/CMSIS/Include/core_cm7.h **** /**
2400:Drivers/CMSIS/Include/core_cm7.h ****   \brief   Invalidate D-Cache
2401:Drivers/CMSIS/Include/core_cm7.h ****   \details Invalidates D-Cache
2402:Drivers/CMSIS/Include/core_cm7.h ****   */
2403:Drivers/CMSIS/Include/core_cm7.h **** __STATIC_FORCEINLINE void SCB_InvalidateDCache (void)
2404:Drivers/CMSIS/Include/core_cm7.h **** {
2405:Drivers/CMSIS/Include/core_cm7.h ****   #if defined (__DCACHE_PRESENT) && (__DCACHE_PRESENT == 1U)
2406:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t ccsidr;
2407:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t sets;
2408:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t ways;
2409:Drivers/CMSIS/Include/core_cm7.h **** 
2410:Drivers/CMSIS/Include/core_cm7.h ****     SCB->CSSELR = 0U;                       /* select Level 1 data cache */
2411:Drivers/CMSIS/Include/core_cm7.h ****     __DSB();
2412:Drivers/CMSIS/Include/core_cm7.h **** 
2413:Drivers/CMSIS/Include/core_cm7.h ****     ccsidr = SCB->CCSIDR;
2414:Drivers/CMSIS/Include/core_cm7.h **** 
2415:Drivers/CMSIS/Include/core_cm7.h ****                                             /* invalidate D-Cache */
2416:Drivers/CMSIS/Include/core_cm7.h ****     sets = (uint32_t)(CCSIDR_SETS(ccsidr));
2417:Drivers/CMSIS/Include/core_cm7.h ****     do {
2418:Drivers/CMSIS/Include/core_cm7.h ****       ways = (uint32_t)(CCSIDR_WAYS(ccsidr));
2419:Drivers/CMSIS/Include/core_cm7.h ****       do {
2420:Drivers/CMSIS/Include/core_cm7.h ****         SCB->DCISW = (((sets << SCB_DCISW_SET_Pos) & SCB_DCISW_SET_Msk) |
2421:Drivers/CMSIS/Include/core_cm7.h ****                       ((ways << SCB_DCISW_WAY_Pos) & SCB_DCISW_WAY_Msk)  );
2422:Drivers/CMSIS/Include/core_cm7.h ****         #if defined ( __CC_ARM )
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 100


2423:Drivers/CMSIS/Include/core_cm7.h ****           __schedule_barrier();
2424:Drivers/CMSIS/Include/core_cm7.h ****         #endif
2425:Drivers/CMSIS/Include/core_cm7.h ****       } while (ways-- != 0U);
2426:Drivers/CMSIS/Include/core_cm7.h ****     } while(sets-- != 0U);
2427:Drivers/CMSIS/Include/core_cm7.h **** 
2428:Drivers/CMSIS/Include/core_cm7.h ****     __DSB();
2429:Drivers/CMSIS/Include/core_cm7.h ****     __ISB();
2430:Drivers/CMSIS/Include/core_cm7.h ****   #endif
2431:Drivers/CMSIS/Include/core_cm7.h **** }
2432:Drivers/CMSIS/Include/core_cm7.h **** 
2433:Drivers/CMSIS/Include/core_cm7.h **** 
2434:Drivers/CMSIS/Include/core_cm7.h **** /**
2435:Drivers/CMSIS/Include/core_cm7.h ****   \brief   Clean D-Cache
2436:Drivers/CMSIS/Include/core_cm7.h ****   \details Cleans D-Cache
2437:Drivers/CMSIS/Include/core_cm7.h ****   */
2438:Drivers/CMSIS/Include/core_cm7.h **** __STATIC_FORCEINLINE void SCB_CleanDCache (void)
2439:Drivers/CMSIS/Include/core_cm7.h **** {
2440:Drivers/CMSIS/Include/core_cm7.h ****   #if defined (__DCACHE_PRESENT) && (__DCACHE_PRESENT == 1U)
2441:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t ccsidr;
2442:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t sets;
2443:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t ways;
2444:Drivers/CMSIS/Include/core_cm7.h **** 
2445:Drivers/CMSIS/Include/core_cm7.h ****     SCB->CSSELR = 0U;                       /* select Level 1 data cache */
2446:Drivers/CMSIS/Include/core_cm7.h ****     __DSB();
2447:Drivers/CMSIS/Include/core_cm7.h **** 
2448:Drivers/CMSIS/Include/core_cm7.h ****     ccsidr = SCB->CCSIDR;
2449:Drivers/CMSIS/Include/core_cm7.h **** 
2450:Drivers/CMSIS/Include/core_cm7.h ****                                             /* clean D-Cache */
2451:Drivers/CMSIS/Include/core_cm7.h ****     sets = (uint32_t)(CCSIDR_SETS(ccsidr));
2452:Drivers/CMSIS/Include/core_cm7.h ****     do {
2453:Drivers/CMSIS/Include/core_cm7.h ****       ways = (uint32_t)(CCSIDR_WAYS(ccsidr));
2454:Drivers/CMSIS/Include/core_cm7.h ****       do {
2455:Drivers/CMSIS/Include/core_cm7.h ****         SCB->DCCSW = (((sets << SCB_DCCSW_SET_Pos) & SCB_DCCSW_SET_Msk) |
2456:Drivers/CMSIS/Include/core_cm7.h ****                       ((ways << SCB_DCCSW_WAY_Pos) & SCB_DCCSW_WAY_Msk)  );
2457:Drivers/CMSIS/Include/core_cm7.h ****         #if defined ( __CC_ARM )
2458:Drivers/CMSIS/Include/core_cm7.h ****           __schedule_barrier();
2459:Drivers/CMSIS/Include/core_cm7.h ****         #endif
2460:Drivers/CMSIS/Include/core_cm7.h ****       } while (ways-- != 0U);
2461:Drivers/CMSIS/Include/core_cm7.h ****     } while(sets-- != 0U);
2462:Drivers/CMSIS/Include/core_cm7.h **** 
2463:Drivers/CMSIS/Include/core_cm7.h ****     __DSB();
2464:Drivers/CMSIS/Include/core_cm7.h ****     __ISB();
2465:Drivers/CMSIS/Include/core_cm7.h ****   #endif
2466:Drivers/CMSIS/Include/core_cm7.h **** }
2467:Drivers/CMSIS/Include/core_cm7.h **** 
2468:Drivers/CMSIS/Include/core_cm7.h **** 
2469:Drivers/CMSIS/Include/core_cm7.h **** /**
2470:Drivers/CMSIS/Include/core_cm7.h ****   \brief   Clean & Invalidate D-Cache
2471:Drivers/CMSIS/Include/core_cm7.h ****   \details Cleans and Invalidates D-Cache
2472:Drivers/CMSIS/Include/core_cm7.h ****   */
2473:Drivers/CMSIS/Include/core_cm7.h **** __STATIC_FORCEINLINE void SCB_CleanInvalidateDCache (void)
2474:Drivers/CMSIS/Include/core_cm7.h **** {
2475:Drivers/CMSIS/Include/core_cm7.h ****   #if defined (__DCACHE_PRESENT) && (__DCACHE_PRESENT == 1U)
2476:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t ccsidr;
2477:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t sets;
2478:Drivers/CMSIS/Include/core_cm7.h ****     uint32_t ways;
2479:Drivers/CMSIS/Include/core_cm7.h **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 101


2480:Drivers/CMSIS/Include/core_cm7.h ****     SCB->CSSELR = 0U;                       /* select Level 1 data cache */
2481:Drivers/CMSIS/Include/core_cm7.h ****     __DSB();
2482:Drivers/CMSIS/Include/core_cm7.h **** 
2483:Drivers/CMSIS/Include/core_cm7.h ****     ccsidr = SCB->CCSIDR;
2484:Drivers/CMSIS/Include/core_cm7.h **** 
2485:Drivers/CMSIS/Include/core_cm7.h ****                                             /* clean & invalidate D-Cache */
2486:Drivers/CMSIS/Include/core_cm7.h ****     sets = (uint32_t)(CCSIDR_SETS(ccsidr));
2487:Drivers/CMSIS/Include/core_cm7.h ****     do {
2488:Drivers/CMSIS/Include/core_cm7.h ****       ways = (uint32_t)(CCSIDR_WAYS(ccsidr));
2489:Drivers/CMSIS/Include/core_cm7.h ****       do {
2490:Drivers/CMSIS/Include/core_cm7.h ****         SCB->DCCISW = (((sets << SCB_DCCISW_SET_Pos) & SCB_DCCISW_SET_Msk) |
2491:Drivers/CMSIS/Include/core_cm7.h ****                        ((ways << SCB_DCCISW_WAY_Pos) & SCB_DCCISW_WAY_Msk)  );
2492:Drivers/CMSIS/Include/core_cm7.h ****         #if defined ( __CC_ARM )
2493:Drivers/CMSIS/Include/core_cm7.h ****           __schedule_barrier();
2494:Drivers/CMSIS/Include/core_cm7.h ****         #endif
2495:Drivers/CMSIS/Include/core_cm7.h ****       } while (ways-- != 0U);
2496:Drivers/CMSIS/Include/core_cm7.h ****     } while(sets-- != 0U);
2497:Drivers/CMSIS/Include/core_cm7.h **** 
2498:Drivers/CMSIS/Include/core_cm7.h ****     __DSB();
2499:Drivers/CMSIS/Include/core_cm7.h ****     __ISB();
2500:Drivers/CMSIS/Include/core_cm7.h ****   #endif
2501:Drivers/CMSIS/Include/core_cm7.h **** }
2502:Drivers/CMSIS/Include/core_cm7.h **** 
2503:Drivers/CMSIS/Include/core_cm7.h **** 
2504:Drivers/CMSIS/Include/core_cm7.h **** /**
2505:Drivers/CMSIS/Include/core_cm7.h ****   \brief   D-Cache Invalidate by address
2506:Drivers/CMSIS/Include/core_cm7.h ****   \details Invalidates D-Cache for the given address.
2507:Drivers/CMSIS/Include/core_cm7.h ****            D-Cache is invalidated starting from a 32 byte aligned address in 32 byte granularity.
2508:Drivers/CMSIS/Include/core_cm7.h ****            D-Cache memory blocks which are part of given address + given size are invalidated.
2509:Drivers/CMSIS/Include/core_cm7.h ****   \param[in]   addr    address
2510:Drivers/CMSIS/Include/core_cm7.h ****   \param[in]   dsize   size of memory block (in number of bytes)
2511:Drivers/CMSIS/Include/core_cm7.h **** */
2512:Drivers/CMSIS/Include/core_cm7.h **** __STATIC_FORCEINLINE void SCB_InvalidateDCache_by_Addr (void *addr, int32_t dsize)
 1895              		.loc 2 2512 27 view .LVU509
 1896              	.LBB25:
2513:Drivers/CMSIS/Include/core_cm7.h **** {
2514:Drivers/CMSIS/Include/core_cm7.h ****   #if defined (__DCACHE_PRESENT) && (__DCACHE_PRESENT == 1U)
2515:Drivers/CMSIS/Include/core_cm7.h ****     if ( dsize > 0 ) { 
 1897              		.loc 2 2515 5 view .LVU510
 1898              		.loc 2 2515 8 is_stmt 0 view .LVU511
 1899 0032 83B1     		cbz	r3, .L129
 1900              	.LBB26:
2516:Drivers/CMSIS/Include/core_cm7.h ****        int32_t op_size = dsize + (((uint32_t)addr) & (__SCB_DCACHE_LINE_SIZE - 1U));
 1901              		.loc 2 2516 8 is_stmt 1 view .LVU512
 1902              		.loc 2 2516 36 is_stmt 0 view .LVU513
 1903 0034 1146     		mov	r1, r2
 1904              	.LVL163:
 1905              		.loc 2 2516 52 view .LVU514
 1906 0036 02F01F02 		and	r2, r2, #31
 1907              	.LVL164:
 1908              		.loc 2 2516 32 view .LVU515
 1909 003a 1344     		add	r3, r3, r2
 1910              	.LVL165:
2517:Drivers/CMSIS/Include/core_cm7.h ****       uint32_t op_addr = (uint32_t)addr /* & ~(__SCB_DCACHE_LINE_SIZE - 1U) */;
 1911              		.loc 2 2517 7 is_stmt 1 view .LVU516
2518:Drivers/CMSIS/Include/core_cm7.h ****     
2519:Drivers/CMSIS/Include/core_cm7.h ****       __DSB();
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 102


 1912              		.loc 2 2519 7 view .LVU517
 1913              	.LBB27:
 1914              	.LBI27:
 1915              		.file 3 "Drivers/CMSIS/Include/cmsis_gcc.h"
   1:Drivers/CMSIS/Include/cmsis_gcc.h **** /**************************************************************************//**
   2:Drivers/CMSIS/Include/cmsis_gcc.h ****  * @file     cmsis_gcc.h
   3:Drivers/CMSIS/Include/cmsis_gcc.h ****  * @brief    CMSIS compiler GCC header file
   4:Drivers/CMSIS/Include/cmsis_gcc.h ****  * @version  V5.2.0
   5:Drivers/CMSIS/Include/cmsis_gcc.h ****  * @date     08. May 2019
   6:Drivers/CMSIS/Include/cmsis_gcc.h ****  ******************************************************************************/
   7:Drivers/CMSIS/Include/cmsis_gcc.h **** /*
   8:Drivers/CMSIS/Include/cmsis_gcc.h ****  * Copyright (c) 2009-2019 Arm Limited. All rights reserved.
   9:Drivers/CMSIS/Include/cmsis_gcc.h ****  *
  10:Drivers/CMSIS/Include/cmsis_gcc.h ****  * SPDX-License-Identifier: Apache-2.0
  11:Drivers/CMSIS/Include/cmsis_gcc.h ****  *
  12:Drivers/CMSIS/Include/cmsis_gcc.h ****  * Licensed under the Apache License, Version 2.0 (the License); you may
  13:Drivers/CMSIS/Include/cmsis_gcc.h ****  * not use this file except in compliance with the License.
  14:Drivers/CMSIS/Include/cmsis_gcc.h ****  * You may obtain a copy of the License at
  15:Drivers/CMSIS/Include/cmsis_gcc.h ****  *
  16:Drivers/CMSIS/Include/cmsis_gcc.h ****  * www.apache.org/licenses/LICENSE-2.0
  17:Drivers/CMSIS/Include/cmsis_gcc.h ****  *
  18:Drivers/CMSIS/Include/cmsis_gcc.h ****  * Unless required by applicable law or agreed to in writing, software
  19:Drivers/CMSIS/Include/cmsis_gcc.h ****  * distributed under the License is distributed on an AS IS BASIS, WITHOUT
  20:Drivers/CMSIS/Include/cmsis_gcc.h ****  * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  21:Drivers/CMSIS/Include/cmsis_gcc.h ****  * See the License for the specific language governing permissions and
  22:Drivers/CMSIS/Include/cmsis_gcc.h ****  * limitations under the License.
  23:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
  24:Drivers/CMSIS/Include/cmsis_gcc.h **** 
  25:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef __CMSIS_GCC_H
  26:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __CMSIS_GCC_H
  27:Drivers/CMSIS/Include/cmsis_gcc.h **** 
  28:Drivers/CMSIS/Include/cmsis_gcc.h **** /* ignore some GCC warnings */
  29:Drivers/CMSIS/Include/cmsis_gcc.h **** #pragma GCC diagnostic push
  30:Drivers/CMSIS/Include/cmsis_gcc.h **** #pragma GCC diagnostic ignored "-Wsign-conversion"
  31:Drivers/CMSIS/Include/cmsis_gcc.h **** #pragma GCC diagnostic ignored "-Wconversion"
  32:Drivers/CMSIS/Include/cmsis_gcc.h **** #pragma GCC diagnostic ignored "-Wunused-parameter"
  33:Drivers/CMSIS/Include/cmsis_gcc.h **** 
  34:Drivers/CMSIS/Include/cmsis_gcc.h **** /* Fallback for __has_builtin */
  35:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef __has_builtin
  36:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __has_builtin(x) (0)
  37:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  38:Drivers/CMSIS/Include/cmsis_gcc.h **** 
  39:Drivers/CMSIS/Include/cmsis_gcc.h **** /* CMSIS compiler specific defines */
  40:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __ASM
  41:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __ASM                                  __asm
  42:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  43:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __INLINE
  44:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __INLINE                               inline
  45:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  46:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __STATIC_INLINE
  47:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __STATIC_INLINE                        static inline
  48:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  49:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __STATIC_FORCEINLINE                 
  50:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __STATIC_FORCEINLINE                   __attribute__((always_inline)) static inline
  51:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif                                           
  52:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __NO_RETURN
  53:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __NO_RETURN                            __attribute__((__noreturn__))
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 103


  54:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  55:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __USED
  56:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __USED                                 __attribute__((used))
  57:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  58:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __WEAK
  59:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __WEAK                                 __attribute__((weak))
  60:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  61:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __PACKED
  62:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __PACKED                               __attribute__((packed, aligned(1)))
  63:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  64:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __PACKED_STRUCT
  65:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __PACKED_STRUCT                        struct __attribute__((packed, aligned(1)))
  66:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  67:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __PACKED_UNION
  68:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __PACKED_UNION                         union __attribute__((packed, aligned(1)))
  69:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  70:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __UNALIGNED_UINT32        /* deprecated */
  71:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic push
  72:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic ignored "-Wpacked"
  73:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic ignored "-Wattributes"
  74:Drivers/CMSIS/Include/cmsis_gcc.h ****   struct __attribute__((packed)) T_UINT32 { uint32_t v; };
  75:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic pop
  76:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __UNALIGNED_UINT32(x)                  (((struct T_UINT32 *)(x))->v)
  77:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  78:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __UNALIGNED_UINT16_WRITE
  79:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic push
  80:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic ignored "-Wpacked"
  81:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic ignored "-Wattributes"
  82:Drivers/CMSIS/Include/cmsis_gcc.h ****   __PACKED_STRUCT T_UINT16_WRITE { uint16_t v; };
  83:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic pop
  84:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __UNALIGNED_UINT16_WRITE(addr, val)    (void)((((struct T_UINT16_WRITE *)(void *)(addr))-
  85:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  86:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __UNALIGNED_UINT16_READ
  87:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic push
  88:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic ignored "-Wpacked"
  89:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic ignored "-Wattributes"
  90:Drivers/CMSIS/Include/cmsis_gcc.h ****   __PACKED_STRUCT T_UINT16_READ { uint16_t v; };
  91:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic pop
  92:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __UNALIGNED_UINT16_READ(addr)          (((const struct T_UINT16_READ *)(const void *)(add
  93:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  94:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __UNALIGNED_UINT32_WRITE
  95:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic push
  96:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic ignored "-Wpacked"
  97:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic ignored "-Wattributes"
  98:Drivers/CMSIS/Include/cmsis_gcc.h ****   __PACKED_STRUCT T_UINT32_WRITE { uint32_t v; };
  99:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic pop
 100:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __UNALIGNED_UINT32_WRITE(addr, val)    (void)((((struct T_UINT32_WRITE *)(void *)(addr))-
 101:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 102:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __UNALIGNED_UINT32_READ
 103:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic push
 104:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic ignored "-Wpacked"
 105:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic ignored "-Wattributes"
 106:Drivers/CMSIS/Include/cmsis_gcc.h ****   __PACKED_STRUCT T_UINT32_READ { uint32_t v; };
 107:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic pop
 108:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __UNALIGNED_UINT32_READ(addr)          (((const struct T_UINT32_READ *)(const void *)(add
 109:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 110:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __ALIGNED
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 104


 111:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __ALIGNED(x)                           __attribute__((aligned(x)))
 112:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 113:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __RESTRICT
 114:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __RESTRICT                             __restrict
 115:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 116:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __COMPILER_BARRIER
 117:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __COMPILER_BARRIER()                   __ASM volatile("":::"memory")
 118:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 119:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 120:Drivers/CMSIS/Include/cmsis_gcc.h **** /* #########################  Startup and Lowlevel Init  ######################## */
 121:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 122:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef __PROGRAM_START
 123:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 124:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 125:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Initializes data and bss sections
 126:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details This default implementations initialized all data and additional bss
 127:Drivers/CMSIS/Include/cmsis_gcc.h ****            sections relying on .copy.table and .zero.table specified properly
 128:Drivers/CMSIS/Include/cmsis_gcc.h ****            in the used linker script.
 129:Drivers/CMSIS/Include/cmsis_gcc.h ****   
 130:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 131:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE __NO_RETURN void __cmsis_start(void)
 132:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 133:Drivers/CMSIS/Include/cmsis_gcc.h ****   extern void _start(void) __NO_RETURN;
 134:Drivers/CMSIS/Include/cmsis_gcc.h ****   
 135:Drivers/CMSIS/Include/cmsis_gcc.h ****   typedef struct {
 136:Drivers/CMSIS/Include/cmsis_gcc.h ****     uint32_t const* src;
 137:Drivers/CMSIS/Include/cmsis_gcc.h ****     uint32_t* dest;
 138:Drivers/CMSIS/Include/cmsis_gcc.h ****     uint32_t  wlen;
 139:Drivers/CMSIS/Include/cmsis_gcc.h ****   } __copy_table_t;
 140:Drivers/CMSIS/Include/cmsis_gcc.h ****   
 141:Drivers/CMSIS/Include/cmsis_gcc.h ****   typedef struct {
 142:Drivers/CMSIS/Include/cmsis_gcc.h ****     uint32_t* dest;
 143:Drivers/CMSIS/Include/cmsis_gcc.h ****     uint32_t  wlen;
 144:Drivers/CMSIS/Include/cmsis_gcc.h ****   } __zero_table_t;
 145:Drivers/CMSIS/Include/cmsis_gcc.h ****   
 146:Drivers/CMSIS/Include/cmsis_gcc.h ****   extern const __copy_table_t __copy_table_start__;
 147:Drivers/CMSIS/Include/cmsis_gcc.h ****   extern const __copy_table_t __copy_table_end__;
 148:Drivers/CMSIS/Include/cmsis_gcc.h ****   extern const __zero_table_t __zero_table_start__;
 149:Drivers/CMSIS/Include/cmsis_gcc.h ****   extern const __zero_table_t __zero_table_end__;
 150:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 151:Drivers/CMSIS/Include/cmsis_gcc.h ****   for (__copy_table_t const* pTable = &__copy_table_start__; pTable < &__copy_table_end__; ++pTable
 152:Drivers/CMSIS/Include/cmsis_gcc.h ****     for(uint32_t i=0u; i<pTable->wlen; ++i) {
 153:Drivers/CMSIS/Include/cmsis_gcc.h ****       pTable->dest[i] = pTable->src[i];
 154:Drivers/CMSIS/Include/cmsis_gcc.h ****     }
 155:Drivers/CMSIS/Include/cmsis_gcc.h ****   }
 156:Drivers/CMSIS/Include/cmsis_gcc.h ****  
 157:Drivers/CMSIS/Include/cmsis_gcc.h ****   for (__zero_table_t const* pTable = &__zero_table_start__; pTable < &__zero_table_end__; ++pTable
 158:Drivers/CMSIS/Include/cmsis_gcc.h ****     for(uint32_t i=0u; i<pTable->wlen; ++i) {
 159:Drivers/CMSIS/Include/cmsis_gcc.h ****       pTable->dest[i] = 0u;
 160:Drivers/CMSIS/Include/cmsis_gcc.h ****     }
 161:Drivers/CMSIS/Include/cmsis_gcc.h ****   }
 162:Drivers/CMSIS/Include/cmsis_gcc.h ****  
 163:Drivers/CMSIS/Include/cmsis_gcc.h ****   _start();
 164:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 165:Drivers/CMSIS/Include/cmsis_gcc.h ****   
 166:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __PROGRAM_START           __cmsis_start
 167:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 105


 168:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 169:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef __INITIAL_SP
 170:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __INITIAL_SP              __StackTop
 171:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 172:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 173:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef __STACK_LIMIT
 174:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __STACK_LIMIT             __StackLimit
 175:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 176:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 177:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef __VECTOR_TABLE
 178:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __VECTOR_TABLE            __Vectors
 179:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 180:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 181:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef __VECTOR_TABLE_ATTRIBUTE
 182:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __VECTOR_TABLE_ATTRIBUTE  __attribute((used, section(".vectors")))
 183:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 184:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 185:Drivers/CMSIS/Include/cmsis_gcc.h **** /* ###########################  Core Function Access  ########################### */
 186:Drivers/CMSIS/Include/cmsis_gcc.h **** /** \ingroup  CMSIS_Core_FunctionInterface
 187:Drivers/CMSIS/Include/cmsis_gcc.h ****     \defgroup CMSIS_Core_RegAccFunctions CMSIS Core Register Access Functions
 188:Drivers/CMSIS/Include/cmsis_gcc.h ****   @{
 189:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 190:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 191:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 192:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Enable IRQ Interrupts
 193:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Enables IRQ interrupts by clearing the I-bit in the CPSR.
 194:Drivers/CMSIS/Include/cmsis_gcc.h ****            Can only be executed in Privileged modes.
 195:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 196:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __enable_irq(void)
 197:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 198:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("cpsie i" : : : "memory");
 199:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 200:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 201:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 202:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 203:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Disable IRQ Interrupts
 204:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Disables IRQ interrupts by setting the I-bit in the CPSR.
 205:Drivers/CMSIS/Include/cmsis_gcc.h ****            Can only be executed in Privileged modes.
 206:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 207:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __disable_irq(void)
 208:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 209:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("cpsid i" : : : "memory");
 210:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 211:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 212:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 213:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 214:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get Control Register
 215:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the content of the Control Register.
 216:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               Control Register value
 217:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 218:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __get_CONTROL(void)
 219:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 220:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 221:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 222:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, control" : "=r" (result) );
 223:Drivers/CMSIS/Include/cmsis_gcc.h ****   return(result);
 224:Drivers/CMSIS/Include/cmsis_gcc.h **** }
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 106


 225:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 226:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 227:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (defined (__ARM_FEATURE_CMSE ) && (__ARM_FEATURE_CMSE == 3))
 228:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 229:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get Control Register (non-secure)
 230:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the content of the non-secure Control Register when in secure mode.
 231:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               non-secure Control Register value
 232:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 233:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __TZ_get_CONTROL_NS(void)
 234:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 235:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 236:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 237:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, control_ns" : "=r" (result) );
 238:Drivers/CMSIS/Include/cmsis_gcc.h ****   return(result);
 239:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 240:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 241:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 242:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 243:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 244:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Set Control Register
 245:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Writes the given value to the Control Register.
 246:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    control  Control Register value to set
 247:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 248:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __set_CONTROL(uint32_t control)
 249:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 250:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MSR control, %0" : : "r" (control) : "memory");
 251:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 252:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 253:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 254:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (defined (__ARM_FEATURE_CMSE ) && (__ARM_FEATURE_CMSE == 3))
 255:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 256:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Set Control Register (non-secure)
 257:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Writes the given value to the non-secure Control Register when in secure state.
 258:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    control  Control Register value to set
 259:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 260:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __TZ_set_CONTROL_NS(uint32_t control)
 261:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 262:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MSR control_ns, %0" : : "r" (control) : "memory");
 263:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 264:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 265:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 266:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 267:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 268:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get IPSR Register
 269:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the content of the IPSR Register.
 270:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               IPSR Register value
 271:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 272:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __get_IPSR(void)
 273:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 274:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 275:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 276:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, ipsr" : "=r" (result) );
 277:Drivers/CMSIS/Include/cmsis_gcc.h ****   return(result);
 278:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 279:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 280:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 281:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 107


 282:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get APSR Register
 283:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the content of the APSR Register.
 284:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               APSR Register value
 285:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 286:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __get_APSR(void)
 287:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 288:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 289:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 290:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, apsr" : "=r" (result) );
 291:Drivers/CMSIS/Include/cmsis_gcc.h ****   return(result);
 292:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 293:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 294:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 295:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 296:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get xPSR Register
 297:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the content of the xPSR Register.
 298:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               xPSR Register value
 299:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 300:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __get_xPSR(void)
 301:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 302:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 303:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 304:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, xpsr" : "=r" (result) );
 305:Drivers/CMSIS/Include/cmsis_gcc.h ****   return(result);
 306:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 307:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 308:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 309:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 310:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get Process Stack Pointer
 311:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the current value of the Process Stack Pointer (PSP).
 312:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               PSP Register value
 313:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 314:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __get_PSP(void)
 315:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 316:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 317:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 318:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, psp"  : "=r" (result) );
 319:Drivers/CMSIS/Include/cmsis_gcc.h ****   return(result);
 320:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 321:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 322:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 323:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (defined (__ARM_FEATURE_CMSE ) && (__ARM_FEATURE_CMSE == 3))
 324:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 325:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get Process Stack Pointer (non-secure)
 326:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the current value of the non-secure Process Stack Pointer (PSP) when in secure s
 327:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               PSP Register value
 328:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 329:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __TZ_get_PSP_NS(void)
 330:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 331:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 332:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 333:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, psp_ns"  : "=r" (result) );
 334:Drivers/CMSIS/Include/cmsis_gcc.h ****   return(result);
 335:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 336:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 337:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 338:Drivers/CMSIS/Include/cmsis_gcc.h **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 108


 339:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 340:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Set Process Stack Pointer
 341:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Assigns the given value to the Process Stack Pointer (PSP).
 342:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    topOfProcStack  Process Stack Pointer value to set
 343:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 344:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __set_PSP(uint32_t topOfProcStack)
 345:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 346:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MSR psp, %0" : : "r" (topOfProcStack) : );
 347:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 348:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 349:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 350:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (defined (__ARM_FEATURE_CMSE ) && (__ARM_FEATURE_CMSE == 3))
 351:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 352:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Set Process Stack Pointer (non-secure)
 353:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Assigns the given value to the non-secure Process Stack Pointer (PSP) when in secure sta
 354:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    topOfProcStack  Process Stack Pointer value to set
 355:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 356:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __TZ_set_PSP_NS(uint32_t topOfProcStack)
 357:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 358:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MSR psp_ns, %0" : : "r" (topOfProcStack) : );
 359:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 360:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 361:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 362:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 363:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 364:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get Main Stack Pointer
 365:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the current value of the Main Stack Pointer (MSP).
 366:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               MSP Register value
 367:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 368:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __get_MSP(void)
 369:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 370:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 371:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 372:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, msp" : "=r" (result) );
 373:Drivers/CMSIS/Include/cmsis_gcc.h ****   return(result);
 374:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 375:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 376:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 377:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (defined (__ARM_FEATURE_CMSE ) && (__ARM_FEATURE_CMSE == 3))
 378:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 379:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get Main Stack Pointer (non-secure)
 380:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the current value of the non-secure Main Stack Pointer (MSP) when in secure stat
 381:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               MSP Register value
 382:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 383:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __TZ_get_MSP_NS(void)
 384:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 385:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 386:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 387:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, msp_ns" : "=r" (result) );
 388:Drivers/CMSIS/Include/cmsis_gcc.h ****   return(result);
 389:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 390:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 391:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 392:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 393:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 394:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Set Main Stack Pointer
 395:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Assigns the given value to the Main Stack Pointer (MSP).
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 109


 396:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    topOfMainStack  Main Stack Pointer value to set
 397:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 398:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __set_MSP(uint32_t topOfMainStack)
 399:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 400:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MSR msp, %0" : : "r" (topOfMainStack) : );
 401:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 402:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 403:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 404:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (defined (__ARM_FEATURE_CMSE ) && (__ARM_FEATURE_CMSE == 3))
 405:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 406:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Set Main Stack Pointer (non-secure)
 407:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Assigns the given value to the non-secure Main Stack Pointer (MSP) when in secure state.
 408:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    topOfMainStack  Main Stack Pointer value to set
 409:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 410:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __TZ_set_MSP_NS(uint32_t topOfMainStack)
 411:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 412:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MSR msp_ns, %0" : : "r" (topOfMainStack) : );
 413:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 414:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 415:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 416:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 417:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (defined (__ARM_FEATURE_CMSE ) && (__ARM_FEATURE_CMSE == 3))
 418:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 419:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get Stack Pointer (non-secure)
 420:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the current value of the non-secure Stack Pointer (SP) when in secure state.
 421:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               SP Register value
 422:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 423:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __TZ_get_SP_NS(void)
 424:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 425:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 426:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 427:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, sp_ns" : "=r" (result) );
 428:Drivers/CMSIS/Include/cmsis_gcc.h ****   return(result);
 429:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 430:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 431:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 432:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 433:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Set Stack Pointer (non-secure)
 434:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Assigns the given value to the non-secure Stack Pointer (SP) when in secure state.
 435:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    topOfStack  Stack Pointer value to set
 436:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 437:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __TZ_set_SP_NS(uint32_t topOfStack)
 438:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 439:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MSR sp_ns, %0" : : "r" (topOfStack) : );
 440:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 441:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 442:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 443:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 444:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 445:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get Priority Mask
 446:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the current state of the priority mask bit from the Priority Mask Register.
 447:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               Priority Mask value
 448:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 449:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __get_PRIMASK(void)
 450:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 451:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 452:Drivers/CMSIS/Include/cmsis_gcc.h **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 110


 453:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, primask" : "=r" (result) :: "memory");
 454:Drivers/CMSIS/Include/cmsis_gcc.h ****   return(result);
 455:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 456:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 457:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 458:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (defined (__ARM_FEATURE_CMSE ) && (__ARM_FEATURE_CMSE == 3))
 459:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 460:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get Priority Mask (non-secure)
 461:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the current state of the non-secure priority mask bit from the Priority Mask Reg
 462:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               Priority Mask value
 463:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 464:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __TZ_get_PRIMASK_NS(void)
 465:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 466:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 467:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 468:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, primask_ns" : "=r" (result) :: "memory");
 469:Drivers/CMSIS/Include/cmsis_gcc.h ****   return(result);
 470:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 471:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 472:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 473:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 474:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 475:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Set Priority Mask
 476:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Assigns the given value to the Priority Mask Register.
 477:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    priMask  Priority Mask
 478:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 479:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __set_PRIMASK(uint32_t priMask)
 480:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 481:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MSR primask, %0" : : "r" (priMask) : "memory");
 482:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 483:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 484:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 485:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (defined (__ARM_FEATURE_CMSE ) && (__ARM_FEATURE_CMSE == 3))
 486:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 487:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Set Priority Mask (non-secure)
 488:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Assigns the given value to the non-secure Priority Mask Register when in secure state.
 489:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    priMask  Priority Mask
 490:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 491:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __TZ_set_PRIMASK_NS(uint32_t priMask)
 492:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 493:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MSR primask_ns, %0" : : "r" (priMask) : "memory");
 494:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 495:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 496:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 497:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 498:Drivers/CMSIS/Include/cmsis_gcc.h **** #if ((defined (__ARM_ARCH_7M__      ) && (__ARM_ARCH_7M__      == 1)) || \
 499:Drivers/CMSIS/Include/cmsis_gcc.h ****      (defined (__ARM_ARCH_7EM__     ) && (__ARM_ARCH_7EM__     == 1)) || \
 500:Drivers/CMSIS/Include/cmsis_gcc.h ****      (defined (__ARM_ARCH_8M_MAIN__ ) && (__ARM_ARCH_8M_MAIN__ == 1))    )
 501:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 502:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Enable FIQ
 503:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Enables FIQ interrupts by clearing the F-bit in the CPSR.
 504:Drivers/CMSIS/Include/cmsis_gcc.h ****            Can only be executed in Privileged modes.
 505:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 506:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __enable_fault_irq(void)
 507:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 508:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("cpsie f" : : : "memory");
 509:Drivers/CMSIS/Include/cmsis_gcc.h **** }
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 111


 510:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 511:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 512:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 513:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Disable FIQ
 514:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Disables FIQ interrupts by setting the F-bit in the CPSR.
 515:Drivers/CMSIS/Include/cmsis_gcc.h ****            Can only be executed in Privileged modes.
 516:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 517:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __disable_fault_irq(void)
 518:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 519:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("cpsid f" : : : "memory");
 520:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 521:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 522:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 523:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 524:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get Base Priority
 525:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the current value of the Base Priority register.
 526:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               Base Priority register value
 527:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 528:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __get_BASEPRI(void)
 529:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 530:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 531:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 532:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, basepri" : "=r" (result) );
 533:Drivers/CMSIS/Include/cmsis_gcc.h ****   return(result);
 534:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 535:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 536:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 537:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (defined (__ARM_FEATURE_CMSE ) && (__ARM_FEATURE_CMSE == 3))
 538:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 539:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get Base Priority (non-secure)
 540:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the current value of the non-secure Base Priority register when in secure state.
 541:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               Base Priority register value
 542:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 543:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __TZ_get_BASEPRI_NS(void)
 544:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 545:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 546:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 547:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, basepri_ns" : "=r" (result) );
 548:Drivers/CMSIS/Include/cmsis_gcc.h ****   return(result);
 549:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 550:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 551:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 552:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 553:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 554:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Set Base Priority
 555:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Assigns the given value to the Base Priority register.
 556:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    basePri  Base Priority value to set
 557:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 558:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __set_BASEPRI(uint32_t basePri)
 559:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 560:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MSR basepri, %0" : : "r" (basePri) : "memory");
 561:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 562:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 563:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 564:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (defined (__ARM_FEATURE_CMSE ) && (__ARM_FEATURE_CMSE == 3))
 565:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 566:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Set Base Priority (non-secure)
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 112


 567:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Assigns the given value to the non-secure Base Priority register when in secure state.
 568:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    basePri  Base Priority value to set
 569:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 570:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __TZ_set_BASEPRI_NS(uint32_t basePri)
 571:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 572:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MSR basepri_ns, %0" : : "r" (basePri) : "memory");
 573:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 574:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 575:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 576:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 577:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 578:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Set Base Priority with condition
 579:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Assigns the given value to the Base Priority register only if BASEPRI masking is disable
 580:Drivers/CMSIS/Include/cmsis_gcc.h ****            or the new value increases the BASEPRI priority level.
 581:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    basePri  Base Priority value to set
 582:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 583:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __set_BASEPRI_MAX(uint32_t basePri)
 584:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 585:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MSR basepri_max, %0" : : "r" (basePri) : "memory");
 586:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 587:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 588:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 589:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 590:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get Fault Mask
 591:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the current value of the Fault Mask register.
 592:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               Fault Mask register value
 593:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 594:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __get_FAULTMASK(void)
 595:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 596:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 597:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 598:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, faultmask" : "=r" (result) );
 599:Drivers/CMSIS/Include/cmsis_gcc.h ****   return(result);
 600:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 601:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 602:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 603:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (defined (__ARM_FEATURE_CMSE ) && (__ARM_FEATURE_CMSE == 3))
 604:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 605:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get Fault Mask (non-secure)
 606:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the current value of the non-secure Fault Mask register when in secure state.
 607:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               Fault Mask register value
 608:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 609:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __TZ_get_FAULTMASK_NS(void)
 610:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 611:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 612:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 613:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, faultmask_ns" : "=r" (result) );
 614:Drivers/CMSIS/Include/cmsis_gcc.h ****   return(result);
 615:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 616:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 617:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 618:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 619:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 620:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Set Fault Mask
 621:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Assigns the given value to the Fault Mask register.
 622:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    faultMask  Fault Mask value to set
 623:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 113


 624:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __set_FAULTMASK(uint32_t faultMask)
 625:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 626:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MSR faultmask, %0" : : "r" (faultMask) : "memory");
 627:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 628:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 629:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 630:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (defined (__ARM_FEATURE_CMSE ) && (__ARM_FEATURE_CMSE == 3))
 631:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 632:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Set Fault Mask (non-secure)
 633:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Assigns the given value to the non-secure Fault Mask register when in secure state.
 634:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    faultMask  Fault Mask value to set
 635:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 636:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __TZ_set_FAULTMASK_NS(uint32_t faultMask)
 637:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 638:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MSR faultmask_ns, %0" : : "r" (faultMask) : "memory");
 639:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 640:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 641:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 642:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif /* ((defined (__ARM_ARCH_7M__      ) && (__ARM_ARCH_7M__      == 1)) || \
 643:Drivers/CMSIS/Include/cmsis_gcc.h ****            (defined (__ARM_ARCH_7EM__     ) && (__ARM_ARCH_7EM__     == 1)) || \
 644:Drivers/CMSIS/Include/cmsis_gcc.h ****            (defined (__ARM_ARCH_8M_MAIN__ ) && (__ARM_ARCH_8M_MAIN__ == 1))    ) */
 645:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 646:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 647:Drivers/CMSIS/Include/cmsis_gcc.h **** #if ((defined (__ARM_ARCH_8M_MAIN__ ) && (__ARM_ARCH_8M_MAIN__ == 1)) || \
 648:Drivers/CMSIS/Include/cmsis_gcc.h ****      (defined (__ARM_ARCH_8M_BASE__ ) && (__ARM_ARCH_8M_BASE__ == 1))    )
 649:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 650:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 651:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get Process Stack Pointer Limit
 652:Drivers/CMSIS/Include/cmsis_gcc.h ****   Devices without ARMv8-M Main Extensions (i.e. Cortex-M23) lack the non-secure
 653:Drivers/CMSIS/Include/cmsis_gcc.h ****   Stack Pointer Limit register hence zero is returned always in non-secure
 654:Drivers/CMSIS/Include/cmsis_gcc.h ****   mode.
 655:Drivers/CMSIS/Include/cmsis_gcc.h ****   
 656:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the current value of the Process Stack Pointer Limit (PSPLIM).
 657:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               PSPLIM Register value
 658:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 659:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __get_PSPLIM(void)
 660:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 661:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (!(defined (__ARM_ARCH_8M_MAIN__ ) && (__ARM_ARCH_8M_MAIN__ == 1)) && \
 662:Drivers/CMSIS/Include/cmsis_gcc.h ****     (!defined (__ARM_FEATURE_CMSE) || (__ARM_FEATURE_CMSE < 3)))
 663:Drivers/CMSIS/Include/cmsis_gcc.h ****     // without main extensions, the non-secure PSPLIM is RAZ/WI
 664:Drivers/CMSIS/Include/cmsis_gcc.h ****   return 0U;
 665:Drivers/CMSIS/Include/cmsis_gcc.h **** #else
 666:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 667:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, psplim"  : "=r" (result) );
 668:Drivers/CMSIS/Include/cmsis_gcc.h ****   return result;
 669:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 670:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 671:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 672:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (defined (__ARM_FEATURE_CMSE) && (__ARM_FEATURE_CMSE == 3))
 673:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 674:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get Process Stack Pointer Limit (non-secure)
 675:Drivers/CMSIS/Include/cmsis_gcc.h ****   Devices without ARMv8-M Main Extensions (i.e. Cortex-M23) lack the non-secure
 676:Drivers/CMSIS/Include/cmsis_gcc.h ****   Stack Pointer Limit register hence zero is returned always.
 677:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 678:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the current value of the non-secure Process Stack Pointer Limit (PSPLIM) when in
 679:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               PSPLIM Register value
 680:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 114


 681:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __TZ_get_PSPLIM_NS(void)
 682:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 683:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (!(defined (__ARM_ARCH_8M_MAIN__ ) && (__ARM_ARCH_8M_MAIN__ == 1)))
 684:Drivers/CMSIS/Include/cmsis_gcc.h ****   // without main extensions, the non-secure PSPLIM is RAZ/WI
 685:Drivers/CMSIS/Include/cmsis_gcc.h ****   return 0U;
 686:Drivers/CMSIS/Include/cmsis_gcc.h **** #else
 687:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 688:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, psplim_ns"  : "=r" (result) );
 689:Drivers/CMSIS/Include/cmsis_gcc.h ****   return result;
 690:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 691:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 692:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 693:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 694:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 695:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 696:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Set Process Stack Pointer Limit
 697:Drivers/CMSIS/Include/cmsis_gcc.h ****   Devices without ARMv8-M Main Extensions (i.e. Cortex-M23) lack the non-secure
 698:Drivers/CMSIS/Include/cmsis_gcc.h ****   Stack Pointer Limit register hence the write is silently ignored in non-secure
 699:Drivers/CMSIS/Include/cmsis_gcc.h ****   mode.
 700:Drivers/CMSIS/Include/cmsis_gcc.h ****   
 701:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Assigns the given value to the Process Stack Pointer Limit (PSPLIM).
 702:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    ProcStackPtrLimit  Process Stack Pointer Limit value to set
 703:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 704:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __set_PSPLIM(uint32_t ProcStackPtrLimit)
 705:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 706:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (!(defined (__ARM_ARCH_8M_MAIN__ ) && (__ARM_ARCH_8M_MAIN__ == 1)) && \
 707:Drivers/CMSIS/Include/cmsis_gcc.h ****     (!defined (__ARM_FEATURE_CMSE) || (__ARM_FEATURE_CMSE < 3)))
 708:Drivers/CMSIS/Include/cmsis_gcc.h ****   // without main extensions, the non-secure PSPLIM is RAZ/WI
 709:Drivers/CMSIS/Include/cmsis_gcc.h ****   (void)ProcStackPtrLimit;
 710:Drivers/CMSIS/Include/cmsis_gcc.h **** #else
 711:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MSR psplim, %0" : : "r" (ProcStackPtrLimit));
 712:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 713:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 714:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 715:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 716:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (defined (__ARM_FEATURE_CMSE  ) && (__ARM_FEATURE_CMSE   == 3))
 717:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 718:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Set Process Stack Pointer (non-secure)
 719:Drivers/CMSIS/Include/cmsis_gcc.h ****   Devices without ARMv8-M Main Extensions (i.e. Cortex-M23) lack the non-secure
 720:Drivers/CMSIS/Include/cmsis_gcc.h ****   Stack Pointer Limit register hence the write is silently ignored.
 721:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 722:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Assigns the given value to the non-secure Process Stack Pointer Limit (PSPLIM) when in s
 723:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    ProcStackPtrLimit  Process Stack Pointer Limit value to set
 724:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 725:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __TZ_set_PSPLIM_NS(uint32_t ProcStackPtrLimit)
 726:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 727:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (!(defined (__ARM_ARCH_8M_MAIN__ ) && (__ARM_ARCH_8M_MAIN__ == 1)))
 728:Drivers/CMSIS/Include/cmsis_gcc.h ****   // without main extensions, the non-secure PSPLIM is RAZ/WI
 729:Drivers/CMSIS/Include/cmsis_gcc.h ****   (void)ProcStackPtrLimit;
 730:Drivers/CMSIS/Include/cmsis_gcc.h **** #else
 731:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MSR psplim_ns, %0\n" : : "r" (ProcStackPtrLimit));
 732:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 733:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 734:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 735:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 736:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 737:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 115


 738:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get Main Stack Pointer Limit
 739:Drivers/CMSIS/Include/cmsis_gcc.h ****   Devices without ARMv8-M Main Extensions (i.e. Cortex-M23) lack the non-secure
 740:Drivers/CMSIS/Include/cmsis_gcc.h ****   Stack Pointer Limit register hence zero is returned always in non-secure
 741:Drivers/CMSIS/Include/cmsis_gcc.h ****   mode.
 742:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 743:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the current value of the Main Stack Pointer Limit (MSPLIM).
 744:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               MSPLIM Register value
 745:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 746:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __get_MSPLIM(void)
 747:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 748:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (!(defined (__ARM_ARCH_8M_MAIN__ ) && (__ARM_ARCH_8M_MAIN__ == 1)) && \
 749:Drivers/CMSIS/Include/cmsis_gcc.h ****     (!defined (__ARM_FEATURE_CMSE) || (__ARM_FEATURE_CMSE < 3)))
 750:Drivers/CMSIS/Include/cmsis_gcc.h ****   // without main extensions, the non-secure MSPLIM is RAZ/WI
 751:Drivers/CMSIS/Include/cmsis_gcc.h ****   return 0U;
 752:Drivers/CMSIS/Include/cmsis_gcc.h **** #else
 753:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 754:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, msplim" : "=r" (result) );
 755:Drivers/CMSIS/Include/cmsis_gcc.h ****   return result;
 756:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 757:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 758:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 759:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 760:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (defined (__ARM_FEATURE_CMSE  ) && (__ARM_FEATURE_CMSE   == 3))
 761:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 762:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get Main Stack Pointer Limit (non-secure)
 763:Drivers/CMSIS/Include/cmsis_gcc.h ****   Devices without ARMv8-M Main Extensions (i.e. Cortex-M23) lack the non-secure
 764:Drivers/CMSIS/Include/cmsis_gcc.h ****   Stack Pointer Limit register hence zero is returned always.
 765:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 766:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the current value of the non-secure Main Stack Pointer Limit(MSPLIM) when in sec
 767:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               MSPLIM Register value
 768:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 769:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __TZ_get_MSPLIM_NS(void)
 770:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 771:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (!(defined (__ARM_ARCH_8M_MAIN__ ) && (__ARM_ARCH_8M_MAIN__ == 1)))
 772:Drivers/CMSIS/Include/cmsis_gcc.h ****   // without main extensions, the non-secure MSPLIM is RAZ/WI
 773:Drivers/CMSIS/Include/cmsis_gcc.h ****   return 0U;
 774:Drivers/CMSIS/Include/cmsis_gcc.h **** #else
 775:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 776:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, msplim_ns" : "=r" (result) );
 777:Drivers/CMSIS/Include/cmsis_gcc.h ****   return result;
 778:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 779:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 780:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 781:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 782:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 783:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 784:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Set Main Stack Pointer Limit
 785:Drivers/CMSIS/Include/cmsis_gcc.h ****   Devices without ARMv8-M Main Extensions (i.e. Cortex-M23) lack the non-secure
 786:Drivers/CMSIS/Include/cmsis_gcc.h ****   Stack Pointer Limit register hence the write is silently ignored in non-secure
 787:Drivers/CMSIS/Include/cmsis_gcc.h ****   mode.
 788:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 789:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Assigns the given value to the Main Stack Pointer Limit (MSPLIM).
 790:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    MainStackPtrLimit  Main Stack Pointer Limit value to set
 791:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 792:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __set_MSPLIM(uint32_t MainStackPtrLimit)
 793:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 794:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (!(defined (__ARM_ARCH_8M_MAIN__ ) && (__ARM_ARCH_8M_MAIN__ == 1)) && \
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 116


 795:Drivers/CMSIS/Include/cmsis_gcc.h ****     (!defined (__ARM_FEATURE_CMSE) || (__ARM_FEATURE_CMSE < 3)))
 796:Drivers/CMSIS/Include/cmsis_gcc.h ****   // without main extensions, the non-secure MSPLIM is RAZ/WI
 797:Drivers/CMSIS/Include/cmsis_gcc.h ****   (void)MainStackPtrLimit;
 798:Drivers/CMSIS/Include/cmsis_gcc.h **** #else
 799:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MSR msplim, %0" : : "r" (MainStackPtrLimit));
 800:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 801:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 802:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 803:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 804:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (defined (__ARM_FEATURE_CMSE  ) && (__ARM_FEATURE_CMSE   == 3))
 805:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 806:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Set Main Stack Pointer Limit (non-secure)
 807:Drivers/CMSIS/Include/cmsis_gcc.h ****   Devices without ARMv8-M Main Extensions (i.e. Cortex-M23) lack the non-secure
 808:Drivers/CMSIS/Include/cmsis_gcc.h ****   Stack Pointer Limit register hence the write is silently ignored.
 809:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 810:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Assigns the given value to the non-secure Main Stack Pointer Limit (MSPLIM) when in secu
 811:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    MainStackPtrLimit  Main Stack Pointer value to set
 812:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 813:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __TZ_set_MSPLIM_NS(uint32_t MainStackPtrLimit)
 814:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 815:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (!(defined (__ARM_ARCH_8M_MAIN__ ) && (__ARM_ARCH_8M_MAIN__ == 1)))
 816:Drivers/CMSIS/Include/cmsis_gcc.h ****   // without main extensions, the non-secure MSPLIM is RAZ/WI
 817:Drivers/CMSIS/Include/cmsis_gcc.h ****   (void)MainStackPtrLimit;
 818:Drivers/CMSIS/Include/cmsis_gcc.h **** #else
 819:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MSR msplim_ns, %0" : : "r" (MainStackPtrLimit));
 820:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 821:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 822:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 823:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 824:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif /* ((defined (__ARM_ARCH_8M_MAIN__ ) && (__ARM_ARCH_8M_MAIN__ == 1)) || \
 825:Drivers/CMSIS/Include/cmsis_gcc.h ****            (defined (__ARM_ARCH_8M_BASE__ ) && (__ARM_ARCH_8M_BASE__ == 1))    ) */
 826:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 827:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 828:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 829:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get FPSCR
 830:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the current value of the Floating Point Status/Control register.
 831:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               Floating Point Status/Control register value
 832:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 833:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __get_FPSCR(void)
 834:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 835:Drivers/CMSIS/Include/cmsis_gcc.h **** #if ((defined (__FPU_PRESENT) && (__FPU_PRESENT == 1U)) && \
 836:Drivers/CMSIS/Include/cmsis_gcc.h ****      (defined (__FPU_USED   ) && (__FPU_USED    == 1U))     )
 837:Drivers/CMSIS/Include/cmsis_gcc.h **** #if __has_builtin(__builtin_arm_get_fpscr) 
 838:Drivers/CMSIS/Include/cmsis_gcc.h **** // Re-enable using built-in when GCC has been fixed
 839:Drivers/CMSIS/Include/cmsis_gcc.h **** // || (__GNUC__ > 7) || (__GNUC__ == 7 && __GNUC_MINOR__ >= 2)
 840:Drivers/CMSIS/Include/cmsis_gcc.h ****   /* see https://gcc.gnu.org/ml/gcc-patches/2017-04/msg00443.html */
 841:Drivers/CMSIS/Include/cmsis_gcc.h ****   return __builtin_arm_get_fpscr();
 842:Drivers/CMSIS/Include/cmsis_gcc.h **** #else
 843:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 844:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 845:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("VMRS %0, fpscr" : "=r" (result) );
 846:Drivers/CMSIS/Include/cmsis_gcc.h ****   return(result);
 847:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 848:Drivers/CMSIS/Include/cmsis_gcc.h **** #else
 849:Drivers/CMSIS/Include/cmsis_gcc.h ****   return(0U);
 850:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 851:Drivers/CMSIS/Include/cmsis_gcc.h **** }
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 117


 852:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 853:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 854:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 855:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Set FPSCR
 856:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Assigns the given value to the Floating Point Status/Control register.
 857:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    fpscr  Floating Point Status/Control value to set
 858:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 859:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __set_FPSCR(uint32_t fpscr)
 860:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 861:Drivers/CMSIS/Include/cmsis_gcc.h **** #if ((defined (__FPU_PRESENT) && (__FPU_PRESENT == 1U)) && \
 862:Drivers/CMSIS/Include/cmsis_gcc.h ****      (defined (__FPU_USED   ) && (__FPU_USED    == 1U))     )
 863:Drivers/CMSIS/Include/cmsis_gcc.h **** #if __has_builtin(__builtin_arm_set_fpscr)
 864:Drivers/CMSIS/Include/cmsis_gcc.h **** // Re-enable using built-in when GCC has been fixed
 865:Drivers/CMSIS/Include/cmsis_gcc.h **** // || (__GNUC__ > 7) || (__GNUC__ == 7 && __GNUC_MINOR__ >= 2)
 866:Drivers/CMSIS/Include/cmsis_gcc.h ****   /* see https://gcc.gnu.org/ml/gcc-patches/2017-04/msg00443.html */
 867:Drivers/CMSIS/Include/cmsis_gcc.h ****   __builtin_arm_set_fpscr(fpscr);
 868:Drivers/CMSIS/Include/cmsis_gcc.h **** #else
 869:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("VMSR fpscr, %0" : : "r" (fpscr) : "vfpcc", "memory");
 870:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 871:Drivers/CMSIS/Include/cmsis_gcc.h **** #else
 872:Drivers/CMSIS/Include/cmsis_gcc.h ****   (void)fpscr;
 873:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 874:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 875:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 876:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 877:Drivers/CMSIS/Include/cmsis_gcc.h **** /*@} end of CMSIS_Core_RegAccFunctions */
 878:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 879:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 880:Drivers/CMSIS/Include/cmsis_gcc.h **** /* ##########################  Core Instruction Access  ######################### */
 881:Drivers/CMSIS/Include/cmsis_gcc.h **** /** \defgroup CMSIS_Core_InstructionInterface CMSIS Core Instruction Interface
 882:Drivers/CMSIS/Include/cmsis_gcc.h ****   Access to dedicated instructions
 883:Drivers/CMSIS/Include/cmsis_gcc.h ****   @{
 884:Drivers/CMSIS/Include/cmsis_gcc.h **** */
 885:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 886:Drivers/CMSIS/Include/cmsis_gcc.h **** /* Define macros for porting to both thumb1 and thumb2.
 887:Drivers/CMSIS/Include/cmsis_gcc.h ****  * For thumb1, use low register (r0-r7), specified by constraint "l"
 888:Drivers/CMSIS/Include/cmsis_gcc.h ****  * Otherwise, use general registers, specified by constraint "r" */
 889:Drivers/CMSIS/Include/cmsis_gcc.h **** #if defined (__thumb__) && !defined (__thumb2__)
 890:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __CMSIS_GCC_OUT_REG(r) "=l" (r)
 891:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __CMSIS_GCC_RW_REG(r) "+l" (r)
 892:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __CMSIS_GCC_USE_REG(r) "l" (r)
 893:Drivers/CMSIS/Include/cmsis_gcc.h **** #else
 894:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __CMSIS_GCC_OUT_REG(r) "=r" (r)
 895:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __CMSIS_GCC_RW_REG(r) "+r" (r)
 896:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __CMSIS_GCC_USE_REG(r) "r" (r)
 897:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 898:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 899:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 900:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   No Operation
 901:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details No Operation does nothing. This instruction can be used for code alignment purposes.
 902:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 903:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __NOP()                             __ASM volatile ("nop")
 904:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 905:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 906:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Wait For Interrupt
 907:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Wait For Interrupt is a hint instruction that suspends execution until one of a number o
 908:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 118


 909:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __WFI()                             __ASM volatile ("wfi")
 910:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 911:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 912:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 913:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Wait For Event
 914:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Wait For Event is a hint instruction that permits the processor to enter
 915:Drivers/CMSIS/Include/cmsis_gcc.h ****            a low-power state until one of a number of events occurs.
 916:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 917:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __WFE()                             __ASM volatile ("wfe")
 918:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 919:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 920:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 921:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Send Event
 922:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Send Event is a hint instruction. It causes an event to be signaled to the CPU.
 923:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 924:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __SEV()                             __ASM volatile ("sev")
 925:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 926:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 927:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 928:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Instruction Synchronization Barrier
 929:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Instruction Synchronization Barrier flushes the pipeline in the processor,
 930:Drivers/CMSIS/Include/cmsis_gcc.h ****            so that all instructions following the ISB are fetched from cache or memory,
 931:Drivers/CMSIS/Include/cmsis_gcc.h ****            after the instruction has been completed.
 932:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 933:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __ISB(void)
 934:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 935:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("isb 0xF":::"memory");
 936:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 937:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 938:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 939:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 940:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Data Synchronization Barrier
 941:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Acts as a special kind of Data Memory Barrier.
 942:Drivers/CMSIS/Include/cmsis_gcc.h ****            It completes when all explicit memory accesses before this instruction complete.
 943:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 944:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __DSB(void)
 1916              		.loc 3 944 27 view .LVU518
 1917              	.LBB28:
 945:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 946:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("dsb 0xF":::"memory");
 1918              		.loc 3 946 3 view .LVU519
 1919              		.syntax unified
 1920              	@ 946 "Drivers/CMSIS/Include/cmsis_gcc.h" 1
 1921 003c BFF34F8F 		dsb 0xF
 1922              	@ 0 "" 2
 1923              	.LVL166:
 1924              		.thumb
 1925              		.syntax unified
 1926              	.L135:
 1927              		.loc 3 946 3 is_stmt 0 view .LVU520
 1928              	.LBE28:
 1929              	.LBE27:
2520:Drivers/CMSIS/Include/core_cm7.h **** 
2521:Drivers/CMSIS/Include/core_cm7.h ****       do {
 1930              		.loc 2 2521 7 is_stmt 1 view .LVU521
2522:Drivers/CMSIS/Include/core_cm7.h ****         SCB->DCIMVAC = op_addr;             /* register accepts only 32byte aligned values, only bi
 1931              		.loc 2 2522 9 view .LVU522
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 119


 1932              		.loc 2 2522 22 is_stmt 0 view .LVU523
 1933 0040 064A     		ldr	r2, .L138
 1934 0042 C2F85C12 		str	r1, [r2, #604]
2523:Drivers/CMSIS/Include/core_cm7.h ****         op_addr += __SCB_DCACHE_LINE_SIZE;
 1935              		.loc 2 2523 9 is_stmt 1 view .LVU524
 1936              		.loc 2 2523 17 is_stmt 0 view .LVU525
 1937 0046 2031     		adds	r1, r1, #32
 1938              	.LVL167:
2524:Drivers/CMSIS/Include/core_cm7.h ****         op_size -= __SCB_DCACHE_LINE_SIZE;
 1939              		.loc 2 2524 9 is_stmt 1 view .LVU526
 1940              		.loc 2 2524 17 is_stmt 0 view .LVU527
 1941 0048 203B     		subs	r3, r3, #32
 1942              	.LVL168:
2525:Drivers/CMSIS/Include/core_cm7.h ****       } while ( op_size > 0 );
 1943              		.loc 2 2525 25 is_stmt 1 discriminator 1 view .LVU528
 1944 004a 002B     		cmp	r3, #0
 1945 004c F8DC     		bgt	.L135
2526:Drivers/CMSIS/Include/core_cm7.h **** 
2527:Drivers/CMSIS/Include/core_cm7.h ****       __DSB();
 1946              		.loc 2 2527 7 view .LVU529
 1947              	.LBB29:
 1948              	.LBI29:
 944:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 1949              		.loc 3 944 27 view .LVU530
 1950              	.LBB30:
 1951              		.loc 3 946 3 view .LVU531
 1952              		.syntax unified
 1953              	@ 946 "Drivers/CMSIS/Include/cmsis_gcc.h" 1
 1954 004e BFF34F8F 		dsb 0xF
 1955              	@ 0 "" 2
 1956              		.thumb
 1957              		.syntax unified
 1958              	.LBE30:
 1959              	.LBE29:
2528:Drivers/CMSIS/Include/core_cm7.h ****       __ISB();
 1960              		.loc 2 2528 7 view .LVU532
 1961              	.LBB31:
 1962              	.LBI31:
 933:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 1963              		.loc 3 933 27 view .LVU533
 1964              	.LBB32:
 935:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 1965              		.loc 3 935 3 view .LVU534
 1966              		.syntax unified
 1967              	@ 935 "Drivers/CMSIS/Include/cmsis_gcc.h" 1
 1968 0052 BFF36F8F 		isb 0xF
 1969              	@ 0 "" 2
 1970              	.LVL169:
 1971              		.thumb
 1972              		.syntax unified
 1973              	.L129:
 935:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 1974              		.loc 3 935 3 is_stmt 0 view .LVU535
 1975              	.LBE32:
 1976              	.LBE31:
 1977              	.LBE26:
 1978              	.LBE25:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 120


 1979              	.LBE24:
 923:LWIP/Target/ethernetif.c **** 
 924:LWIP/Target/ethernetif.c **** /* USER CODE END HAL ETH RxLinkCallback */
 925:LWIP/Target/ethernetif.c **** }
 1980              		.loc 1 925 1 view .LVU536
 1981 0056 30BC     		pop	{r4, r5}
 1982              	.LCFI33:
 1983              		.cfi_restore 5
 1984              		.cfi_restore 4
 1985              		.cfi_def_cfa_offset 0
 1986              	.LVL170:
 1987              		.loc 1 925 1 view .LVU537
 1988 0058 7047     		bx	lr
 1989              	.L139:
 1990 005a 00BF     		.align	2
 1991              	.L138:
 1992 005c 00ED00E0 		.word	-*********
 1993              		.cfi_endproc
 1994              	.LFE193:
 1996              		.section	.text.HAL_ETH_TxFreeCallback,"ax",%progbits
 1997              		.align	1
 1998              		.global	HAL_ETH_TxFreeCallback
 1999              		.syntax unified
 2000              		.thumb
 2001              		.thumb_func
 2003              	HAL_ETH_TxFreeCallback:
 2004              	.LVL171:
 2005              	.LFB194:
 926:LWIP/Target/ethernetif.c **** 
 927:LWIP/Target/ethernetif.c **** void HAL_ETH_TxFreeCallback(uint32_t * buff)
 928:LWIP/Target/ethernetif.c **** {
 2006              		.loc 1 928 1 is_stmt 1 view -0
 2007              		.cfi_startproc
 2008              		@ args = 0, pretend = 0, frame = 0
 2009              		@ frame_needed = 0, uses_anonymous_args = 0
 2010              		.loc 1 928 1 is_stmt 0 view .LVU539
 2011 0000 08B5     		push	{r3, lr}
 2012              	.LCFI34:
 2013              		.cfi_def_cfa_offset 8
 2014              		.cfi_offset 3, -8
 2015              		.cfi_offset 14, -4
 929:LWIP/Target/ethernetif.c **** /* USER CODE BEGIN HAL ETH TxFreeCallback */
 930:LWIP/Target/ethernetif.c **** 
 931:LWIP/Target/ethernetif.c ****   pbuf_free((struct pbuf *)buff);
 2016              		.loc 1 931 3 is_stmt 1 view .LVU540
 2017 0002 FFF7FEFF 		bl	pbuf_free
 2018              	.LVL172:
 932:LWIP/Target/ethernetif.c **** 
 933:LWIP/Target/ethernetif.c **** /* USER CODE END HAL ETH TxFreeCallback */
 934:LWIP/Target/ethernetif.c **** }
 2019              		.loc 1 934 1 is_stmt 0 view .LVU541
 2020 0006 08BD     		pop	{r3, pc}
 2021              		.cfi_endproc
 2022              	.LFE194:
 2024              		.global	LAN8742_IOCtx
 2025              		.section	.data.LAN8742_IOCtx,"aw"
 2026              		.align	2
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 121


 2029              	LAN8742_IOCtx:
 2030 0000 00000000 		.word	ETH_PHY_IO_Init
 2031 0004 00000000 		.word	ETH_PHY_IO_DeInit
 2032 0008 00000000 		.word	ETH_PHY_IO_WriteReg
 2033 000c 00000000 		.word	ETH_PHY_IO_ReadReg
 2034 0010 00000000 		.word	ETH_PHY_IO_GetTick
 2035              		.global	LAN8742
 2036              		.section	.bss.LAN8742,"aw",%nobits
 2037              		.align	2
 2040              	LAN8742:
 2041 0000 00000000 		.space	32
 2041      00000000 
 2041      00000000 
 2041      00000000 
 2041      00000000 
 2042              		.global	TxConfig
 2043              		.section	.bss.TxConfig,"aw",%nobits
 2044              		.align	2
 2047              	TxConfig:
 2048 0000 00000000 		.space	56
 2048      00000000 
 2048      00000000 
 2048      00000000 
 2048      00000000 
 2049              		.global	heth
 2050              		.section	.bss.heth,"aw",%nobits
 2051              		.align	2
 2054              	heth:
 2055 0000 00000000 		.space	176
 2055      00000000 
 2055      00000000 
 2055      00000000 
 2055      00000000 
 2056              		.global	TxPktSemaphore
 2057              		.section	.bss.TxPktSemaphore,"aw",%nobits
 2058              		.align	2
 2061              	TxPktSemaphore:
 2062 0000 00000000 		.space	4
 2063              		.global	RxPktSemaphore
 2064              		.section	.bss.RxPktSemaphore,"aw",%nobits
 2065              		.align	2
 2068              	RxPktSemaphore:
 2069 0000 00000000 		.space	4
 2070              		.global	DMATxDscrTab
 2071              		.section	.TxDecripSection,"aw"
 2072              		.align	2
 2075              	DMATxDscrTab:
 2076 0000 00000000 		.space	96
 2076      00000000 
 2076      00000000 
 2076      00000000 
 2076      00000000 
 2077              		.global	DMARxDscrTab
 2078              		.section	.RxDecripSection,"aw"
 2079              		.align	2
 2082              	DMARxDscrTab:
 2083 0000 00000000 		.space	96
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 122


 2083      00000000 
 2083      00000000 
 2083      00000000 
 2083      00000000 
 2084              		.section	.bss.RxAllocStatus,"aw",%nobits
 2087              	RxAllocStatus:
 2088 0000 00       		.space	1
 2089              		.global	memp_RX_POOL
 2090              		.section	.rodata.memp_RX_POOL,"a"
 2091              		.align	2
 2094              	memp_RX_POOL:
 2095 0000 2006     		.short	1568
 2096 0002 0C00     		.short	12
 2097 0004 00000000 		.word	memp_memory_RX_POOL_base
 2098 0008 00000000 		.word	memp_tab_RX_POOL
 2099              		.section	.bss.memp_tab_RX_POOL,"aw",%nobits
 2100              		.align	2
 2103              	memp_tab_RX_POOL:
 2104 0000 00000000 		.space	4
 2105              		.global	memp_memory_RX_POOL_base
 2106              		.section	.Rx_PoolSection,"aw"
 2107              		.align	2
 2110              	memp_memory_RX_POOL_base:
 2111 0000 00000000 		.space	18819
 2111      00000000 
 2111      00000000 
 2111      00000000 
 2111      00000000 
 2112              		.text
 2113              	.Letext0:
 2114              		.file 4 "Drivers/CMSIS/Device/ST/STM32H7xx/Include/stm32h723xx.h"
 2115              		.file 5 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin/../lib/gcc/arm-none-eabi/13.3.1/../
 2116              		.file 6 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin/../lib/gcc/arm-none-eabi/13.3.1/../
 2117              		.file 7 "Drivers/CMSIS/Device/ST/STM32H7xx/Include/stm32h7xx.h"
 2118              		.file 8 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin/../lib/gcc/arm-none-eabi/13.3.1/inc
 2119              		.file 9 "Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_def.h"
 2120              		.file 10 "Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_gpio.h"
 2121              		.file 11 "Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_eth.h"
 2122              		.file 12 "Middlewares/Third_Party/LwIP/src/include/lwip/arch.h"
 2123              		.file 13 "Middlewares/Third_Party/LwIP/src/include/lwip/err.h"
 2124              		.file 14 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h"
 2125              		.file 15 "Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.h"
 2126              		.file 16 "Middlewares/Third_Party/LwIP/src/include/lwip/pbuf.h"
 2127              		.file 17 "Middlewares/Third_Party/LwIP/src/include/lwip/ip4_addr.h"
 2128              		.file 18 "Middlewares/Third_Party/LwIP/src/include/lwip/ip_addr.h"
 2129              		.file 19 "Middlewares/Third_Party/LwIP/src/include/lwip/memp.h"
 2130              		.file 20 "Middlewares/Third_Party/LwIP/src/include/lwip/priv/memp_priv.h"
 2131              		.file 21 "Middlewares/Third_Party/LwIP/src/include/lwip/netif.h"
 2132              		.file 22 "Drivers/BSP/Components/lan8742/lan8742.h"
 2133              		.file 23 "Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_cortex.h"
 2134              		.file 24 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin/../lib/gcc/arm-none-eabi/13.3.1/..
 2135              		.file 25 "Middlewares/Third_Party/LwIP/src/include/lwip/etharp.h"
 2136              		.file 26 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin/../lib/gcc/arm-none-eabi/13.3.1/..
 2137              		.file 27 "Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal.h"
 2138              		.file 28 "LWIP/Target/ethernetif.h"
 2139              		.file 29 "<built-in>"
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 123


DEFINED SYMBOLS
                            *ABS*:00000000 ethernetif.c
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:20     .text.ETH_PHY_IO_DeInit:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:26     .text.ETH_PHY_IO_DeInit:00000000 ETH_PHY_IO_DeInit
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:41     .text.low_level_input:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:46     .text.low_level_input:00000000 low_level_input
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:97     .text.low_level_input:00000020 $d
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:2087   .bss.RxAllocStatus:00000000 RxAllocStatus
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:2054   .bss.heth:00000000 heth
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:103    .text.ethernetif_input:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:109    .text.ethernetif_input:00000000 ethernetif_input
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:177    .text.ethernetif_input:00000038 $d
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:2068   .bss.RxPktSemaphore:00000000 RxPktSemaphore
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:182    .text.low_level_output:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:187    .text.low_level_output:00000000 low_level_output
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:383    .text.low_level_output:000000d8 $d
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:2047   .bss.TxConfig:00000000 TxConfig
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:2061   .bss.TxPktSemaphore:00000000 TxPktSemaphore
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:390    .rodata.low_level_init.str1.4:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:394    .text.low_level_init:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:399    .text.low_level_init:00000000 low_level_init
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:724    .text.low_level_init:00000190 $d
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:2075   .TxDecripSection:00000000 DMATxDscrTab
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:2082   .RxDecripSection:00000000 DMARxDscrTab
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:2094   .rodata.memp_RX_POOL:00000000 memp_RX_POOL
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:2040   .bss.LAN8742:00000000 LAN8742
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:2029   .data.LAN8742_IOCtx:00000000 LAN8742_IOCtx
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:740    .text.pbuf_free_custom:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:746    .text.pbuf_free_custom:00000000 pbuf_free_custom
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:794    .text.pbuf_free_custom:00000024 $d
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:801    .text.ETH_PHY_IO_GetTick:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:807    .text.ETH_PHY_IO_GetTick:00000000 ETH_PHY_IO_GetTick
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:828    .text.ETH_PHY_IO_Init:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:834    .text.ETH_PHY_IO_Init:00000000 ETH_PHY_IO_Init
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:856    .text.ETH_PHY_IO_Init:0000000c $d
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:861    .text.ETH_PHY_IO_ReadReg:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:867    .text.ETH_PHY_IO_ReadReg:00000000 ETH_PHY_IO_ReadReg
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:906    .text.ETH_PHY_IO_ReadReg:00000018 $d
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:911    .text.ETH_PHY_IO_WriteReg:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:917    .text.ETH_PHY_IO_WriteReg:00000000 ETH_PHY_IO_WriteReg
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:956    .text.ETH_PHY_IO_WriteReg:00000018 $d
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:961    .text.HAL_ETH_RxCpltCallback:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:967    .text.HAL_ETH_RxCpltCallback:00000000 HAL_ETH_RxCpltCallback
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:992    .text.HAL_ETH_RxCpltCallback:0000000c $d
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:997    .text.HAL_ETH_TxCpltCallback:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:1003   .text.HAL_ETH_TxCpltCallback:00000000 HAL_ETH_TxCpltCallback
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:1028   .text.HAL_ETH_TxCpltCallback:0000000c $d
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:1033   .text.HAL_ETH_ErrorCallback:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:1039   .text.HAL_ETH_ErrorCallback:00000000 HAL_ETH_ErrorCallback
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:1073   .text.HAL_ETH_ErrorCallback:00000018 $d
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:1078   .rodata.ethernetif_init.str1.4:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:1088   .text.ethernetif_init:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:1094   .text.ethernetif_init:00000000 ethernetif_init
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:1155   .text.ethernetif_init:00000034 $d
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:1164   .text.sys_now:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:1170   .text.sys_now:00000000 sys_now
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:1191   .text.HAL_ETH_MspInit:00000000 $t
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 124


C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:1197   .text.HAL_ETH_MspInit:00000000 HAL_ETH_MspInit
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:1423   .text.HAL_ETH_MspInit:00000110 $d
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:1432   .text.HAL_ETH_MspDeInit:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:1438   .text.HAL_ETH_MspDeInit:00000000 HAL_ETH_MspDeInit
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:1507   .text.HAL_ETH_MspDeInit:0000005c $d
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:1516   .text.ethernet_link_thread:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:1522   .text.ethernet_link_thread:00000000 ethernet_link_thread
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:1662   .text.ethernet_link_thread:00000094 $d
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:1666   .text.ethernet_link_thread:000000a4 $t
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:1717   .text.ethernet_link_thread:000000c8 $d
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:1723   .text.HAL_ETH_RxAllocateCallback:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:1729   .text.HAL_ETH_RxAllocateCallback:00000000 HAL_ETH_RxAllocateCallback
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:1804   .text.HAL_ETH_RxAllocateCallback:00000040 $d
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:1811   .text.HAL_ETH_RxLinkCallback:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:1817   .text.HAL_ETH_RxLinkCallback:00000000 HAL_ETH_RxLinkCallback
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:1992   .text.HAL_ETH_RxLinkCallback:0000005c $d
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:1997   .text.HAL_ETH_TxFreeCallback:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:2003   .text.HAL_ETH_TxFreeCallback:00000000 HAL_ETH_TxFreeCallback
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:2026   .data.LAN8742_IOCtx:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:2037   .bss.LAN8742:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:2044   .bss.TxConfig:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:2051   .bss.heth:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:2058   .bss.TxPktSemaphore:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:2065   .bss.RxPktSemaphore:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:2072   .TxDecripSection:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:2079   .RxDecripSection:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:2088   .bss.RxAllocStatus:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:2091   .rodata.memp_RX_POOL:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:2110   .Rx_PoolSection:00000000 memp_memory_RX_POOL_base
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:2103   .bss.memp_tab_RX_POOL:00000000 memp_tab_RX_POOL
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:2100   .bss.memp_tab_RX_POOL:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s:2107   .Rx_PoolSection:00000000 $d

UNDEFINED SYMBOLS
HAL_ETH_ReadData
osSemaphoreAcquire
pbuf_free
memset
pbuf_ref
HAL_ETH_GetError
HAL_ETH_ReleaseTxPacket
HAL_ETH_Transmit_IT
HAL_ETH_Init
memp_init_pool
osSemaphoreNew
osThreadNew
LAN8742_RegisterBusIO
LAN8742_Init
LAN8742_GetLinkState
HAL_ETH_GetMACConfig
HAL_ETH_SetMACConfig
HAL_ETH_Start_IT
netif_set_up
netif_set_link_up
netif_set_link_down
netif_set_down
Error_Handler
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccVFRclU.s 			page 125


memp_free_pool
osSemaphoreRelease
HAL_GetTick
HAL_ETH_SetMDIOClockRange
HAL_ETH_ReadPHYRegister
HAL_ETH_WritePHYRegister
HAL_ETH_GetDMAError
printf
etharp_output
HAL_GPIO_Init
HAL_NVIC_SetPriority
HAL_NVIC_EnableIRQ
HAL_GPIO_DeInit
HAL_NVIC_DisableIRQ
HAL_ETH_Stop_IT
osDelay
memp_malloc_pool
pbuf_alloced_custom
