ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc2bj8wg.s 			page 1


   1              		.cpu cortex-m7
   2              		.arch armv7e-m
   3              		.fpu fpv5-d16
   4              		.eabi_attribute 28, 1
   5              		.eabi_attribute 20, 1
   6              		.eabi_attribute 21, 1
   7              		.eabi_attribute 23, 3
   8              		.eabi_attribute 24, 1
   9              		.eabi_attribute 25, 1
  10              		.eabi_attribute 26, 1
  11              		.eabi_attribute 30, 1
  12              		.eabi_attribute 34, 1
  13              		.eabi_attribute 18, 4
  14              		.file	"udp_transport.c"
  15              		.text
  16              	.Ltext0:
  17              		.cfi_sections	.debug_frame
  18              		.file 1 "micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c"
  19              		.section	.text.cubemx_transport_open,"ax",%progbits
  20              		.align	1
  21              		.global	cubemx_transport_open
  22              		.syntax unified
  23              		.thumb
  24              		.thumb_func
  26              	cubemx_transport_open:
  27              	.LVL0:
  28              	.LFB174:
   1:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c **** #include <uxr/client/transport.h>
   2:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c **** 
   3:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c **** #include <rmw_microxrcedds_c/config.h>
   4:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c **** 
   5:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c **** #include "main.h"
   6:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c **** #include "cmsis_os.h"
   7:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c **** 
   8:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c **** #include <unistd.h>
   9:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c **** #include <stdio.h>
  10:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c **** #include <string.h>
  11:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c **** #include <stdbool.h>
  12:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c **** 
  13:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c **** // --- LWIP ---
  14:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c **** #include "lwip/opt.h"
  15:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c **** #include "lwip/sys.h"
  16:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c **** #include "lwip/api.h"
  17:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c **** #include <lwip/sockets.h>
  18:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c **** 
  19:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c **** #ifdef RMW_UXRCE_TRANSPORT_CUSTOM
  20:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c **** 
  21:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c **** // --- micro-ROS Transports ---
  22:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c **** #define UDP_PORT        8888
  23:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c **** static int sock_fd = -1;
  24:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c **** 
  25:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c **** bool cubemx_transport_open(struct uxrCustomTransport * transport){
  29              		.loc 1 25 66 view -0
  30              		.cfi_startproc
  31              		@ args = 0, pretend = 0, frame = 16
  32              		@ frame_needed = 0, uses_anonymous_args = 0
  33              		.loc 1 25 66 is_stmt 0 view .LVU1
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc2bj8wg.s 			page 2


  34 0000 10B5     		push	{r4, lr}
  35              	.LCFI0:
  36              		.cfi_def_cfa_offset 8
  37              		.cfi_offset 4, -8
  38              		.cfi_offset 14, -4
  39 0002 84B0     		sub	sp, sp, #16
  40              	.LCFI1:
  41              		.cfi_def_cfa_offset 24
  26:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c ****     sock_fd = socket(AF_INET, SOCK_DGRAM, 0);
  42              		.loc 1 26 5 is_stmt 1 view .LVU2
  43              		.loc 1 26 15 is_stmt 0 view .LVU3
  44 0004 0022     		movs	r2, #0
  45 0006 0221     		movs	r1, #2
  46 0008 0846     		mov	r0, r1
  47              	.LVL1:
  48              		.loc 1 26 15 view .LVU4
  49 000a FFF7FEFF 		bl	lwip_socket
  50              	.LVL2:
  51              		.loc 1 26 13 discriminator 1 view .LVU5
  52 000e 0E4C     		ldr	r4, .L5
  53 0010 2060     		str	r0, [r4]
  27:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c ****     struct sockaddr_in addr;
  54              		.loc 1 27 5 is_stmt 1 view .LVU6
  28:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c ****     addr.sin_family = AF_INET;
  55              		.loc 1 28 5 view .LVU7
  56              		.loc 1 28 21 is_stmt 0 view .LVU8
  57 0012 0223     		movs	r3, #2
  58 0014 8DF80130 		strb	r3, [sp, #1]
  29:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c ****     addr.sin_port = htons(UDP_PORT);
  59              		.loc 1 29 5 is_stmt 1 view .LVU9
  60              		.loc 1 29 21 is_stmt 0 view .LVU10
  61 0018 42F2B820 		movw	r0, #8888
  62 001c FFF7FEFF 		bl	lwip_htons
  63              	.LVL3:
  64              		.loc 1 29 19 discriminator 1 view .LVU11
  65 0020 ADF80200 		strh	r0, [sp, #2]	@ movhi
  30:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c ****     addr.sin_addr.s_addr = htonl(INADDR_ANY);
  66              		.loc 1 30 5 is_stmt 1 view .LVU12
  67              		.loc 1 30 28 is_stmt 0 view .LVU13
  68 0024 0020     		movs	r0, #0
  69 0026 FFF7FEFF 		bl	lwip_htonl
  70              	.LVL4:
  71              		.loc 1 30 26 discriminator 1 view .LVU14
  72 002a 0190     		str	r0, [sp, #4]
  31:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c ****     
  32:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c ****     if (bind(sock_fd, (struct sockaddr *)&addr, sizeof(addr)) == -1)
  73              		.loc 1 32 5 is_stmt 1 view .LVU15
  74              		.loc 1 32 9 is_stmt 0 view .LVU16
  75 002c 1022     		movs	r2, #16
  76 002e 6946     		mov	r1, sp
  77 0030 2068     		ldr	r0, [r4]
  78 0032 FFF7FEFF 		bl	lwip_bind
  79              	.LVL5:
  80              		.loc 1 32 8 discriminator 1 view .LVU17
  81 0036 B0F1FF3F 		cmp	r0, #-1
  82 003a 02D0     		beq	.L3
  33:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c ****     {
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc2bj8wg.s 			page 3


  34:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c ****         return false;
  35:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c ****     }
  36:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c **** 
  37:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c ****     return true;
  83              		.loc 1 37 12 view .LVU18
  84 003c 0120     		movs	r0, #1
  85              	.L2:
  38:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c **** }
  86              		.loc 1 38 1 view .LVU19
  87 003e 04B0     		add	sp, sp, #16
  88              	.LCFI2:
  89              		.cfi_remember_state
  90              		.cfi_def_cfa_offset 8
  91              		@ sp needed
  92 0040 10BD     		pop	{r4, pc}
  93              	.L3:
  94              	.LCFI3:
  95              		.cfi_restore_state
  34:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c ****     }
  96              		.loc 1 34 16 view .LVU20
  97 0042 0020     		movs	r0, #0
  98 0044 FBE7     		b	.L2
  99              	.L6:
 100 0046 00BF     		.align	2
 101              	.L5:
 102 0048 00000000 		.word	sock_fd
 103              		.cfi_endproc
 104              	.LFE174:
 106              		.section	.text.cubemx_transport_close,"ax",%progbits
 107              		.align	1
 108              		.global	cubemx_transport_close
 109              		.syntax unified
 110              		.thumb
 111              		.thumb_func
 113              	cubemx_transport_close:
 114              	.LVL6:
 115              	.LFB175:
  39:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c **** 
  40:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c **** bool cubemx_transport_close(struct uxrCustomTransport * transport){
 116              		.loc 1 40 67 is_stmt 1 view -0
 117              		.cfi_startproc
 118              		@ args = 0, pretend = 0, frame = 0
 119              		@ frame_needed = 0, uses_anonymous_args = 0
 120              		.loc 1 40 67 is_stmt 0 view .LVU22
 121 0000 08B5     		push	{r3, lr}
 122              	.LCFI4:
 123              		.cfi_def_cfa_offset 8
 124              		.cfi_offset 3, -8
 125              		.cfi_offset 14, -4
  41:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c ****     if (sock_fd != -1)
 126              		.loc 1 41 5 is_stmt 1 view .LVU23
 127              		.loc 1 41 17 is_stmt 0 view .LVU24
 128 0002 074B     		ldr	r3, .L11
 129 0004 1868     		ldr	r0, [r3]
 130              	.LVL7:
 131              		.loc 1 41 8 view .LVU25
 132 0006 B0F1FF3F 		cmp	r0, #-1
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc2bj8wg.s 			page 4


 133 000a 01D1     		bne	.L10
 134              	.L8:
  42:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c ****     {
  43:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c ****         closesocket(sock_fd);
  44:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c ****         sock_fd = -1;
  45:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c ****     }
  46:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c ****     return true;
 135              		.loc 1 46 5 is_stmt 1 view .LVU26
  47:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c **** }
 136              		.loc 1 47 1 is_stmt 0 view .LVU27
 137 000c 0120     		movs	r0, #1
 138 000e 08BD     		pop	{r3, pc}
 139              	.L10:
  43:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c ****         sock_fd = -1;
 140              		.loc 1 43 9 is_stmt 1 view .LVU28
 141 0010 FFF7FEFF 		bl	lwip_close
 142              	.LVL8:
  44:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c ****     }
 143              		.loc 1 44 9 view .LVU29
  44:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c ****     }
 144              		.loc 1 44 17 is_stmt 0 view .LVU30
 145 0014 024B     		ldr	r3, .L11
 146 0016 4FF0FF32 		mov	r2, #-1
 147 001a 1A60     		str	r2, [r3]
 148 001c F6E7     		b	.L8
 149              	.L12:
 150 001e 00BF     		.align	2
 151              	.L11:
 152 0020 00000000 		.word	sock_fd
 153              		.cfi_endproc
 154              	.LFE175:
 156              		.section	.text.cubemx_transport_write,"ax",%progbits
 157              		.align	1
 158              		.global	cubemx_transport_write
 159              		.syntax unified
 160              		.thumb
 161              		.thumb_func
 163              	cubemx_transport_write:
 164              	.LVL9:
 165              	.LFB176:
  48:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c **** 
  49:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c **** size_t cubemx_transport_write(struct uxrCustomTransport* transport, uint8_t * buf, size_t len, uint
 166              		.loc 1 49 110 is_stmt 1 view -0
 167              		.cfi_startproc
 168              		@ args = 0, pretend = 0, frame = 16
 169              		@ frame_needed = 0, uses_anonymous_args = 0
  50:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c ****     if (sock_fd == -1)
 170              		.loc 1 50 5 view .LVU32
 171              		.loc 1 50 17 is_stmt 0 view .LVU33
 172 0000 144B     		ldr	r3, .L20
 173              	.LVL10:
 174              		.loc 1 50 17 view .LVU34
 175 0002 1B68     		ldr	r3, [r3]
 176              		.loc 1 50 8 view .LVU35
 177 0004 B3F1FF3F 		cmp	r3, #-1
 178 0008 21D0     		beq	.L15
  49:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c ****     if (sock_fd == -1)
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc2bj8wg.s 			page 5


 179              		.loc 1 49 110 view .LVU36
 180 000a 70B5     		push	{r4, r5, r6, lr}
 181              	.LCFI5:
 182              		.cfi_def_cfa_offset 16
 183              		.cfi_offset 4, -16
 184              		.cfi_offset 5, -12
 185              		.cfi_offset 6, -8
 186              		.cfi_offset 14, -4
 187 000c 86B0     		sub	sp, sp, #24
 188              	.LCFI6:
 189              		.cfi_def_cfa_offset 40
 190 000e 0D46     		mov	r5, r1
 191 0010 1446     		mov	r4, r2
  51:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c ****     {
  52:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c ****         return 0;
  53:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c ****     }
  54:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c ****     const char * ip_addr = (const char*) transport->args;
 192              		.loc 1 54 5 is_stmt 1 view .LVU37
 193              		.loc 1 54 18 is_stmt 0 view .LVU38
 194 0012 D0F88C62 		ldr	r6, [r0, #652]
 195              	.LVL11:
  55:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c ****     struct sockaddr_in addr;
 196              		.loc 1 55 5 is_stmt 1 view .LVU39
  56:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c ****     addr.sin_family = AF_INET;
 197              		.loc 1 56 5 view .LVU40
 198              		.loc 1 56 21 is_stmt 0 view .LVU41
 199 0016 0223     		movs	r3, #2
 200 0018 8DF80930 		strb	r3, [sp, #9]
  57:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c ****     addr.sin_port = htons(UDP_PORT);
 201              		.loc 1 57 5 is_stmt 1 view .LVU42
 202              		.loc 1 57 21 is_stmt 0 view .LVU43
 203 001c 42F2B820 		movw	r0, #8888
 204              	.LVL12:
 205              		.loc 1 57 21 view .LVU44
 206 0020 FFF7FEFF 		bl	lwip_htons
 207              	.LVL13:
 208              		.loc 1 57 19 discriminator 1 view .LVU45
 209 0024 ADF80A00 		strh	r0, [sp, #10]	@ movhi
  58:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c ****     addr.sin_addr.s_addr = inet_addr(ip_addr);
 210              		.loc 1 58 5 is_stmt 1 view .LVU46
 211              		.loc 1 58 28 is_stmt 0 view .LVU47
 212 0028 3046     		mov	r0, r6
 213 002a FFF7FEFF 		bl	ipaddr_addr
 214              	.LVL14:
 215              		.loc 1 58 26 discriminator 1 view .LVU48
 216 002e 0390     		str	r0, [sp, #12]
  59:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c ****     int ret = 0;
 217              		.loc 1 59 5 is_stmt 1 view .LVU49
 218              	.LVL15:
  60:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c ****     ret = sendto(sock_fd, buf, len, 0, (struct sockaddr *)&addr, sizeof(addr));
 219              		.loc 1 60 5 view .LVU50
 220              		.loc 1 60 11 is_stmt 0 view .LVU51
 221 0030 1023     		movs	r3, #16
 222 0032 0193     		str	r3, [sp, #4]
 223 0034 02AB     		add	r3, sp, #8
 224 0036 0093     		str	r3, [sp]
 225 0038 0023     		movs	r3, #0
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc2bj8wg.s 			page 6


 226 003a 2246     		mov	r2, r4
 227 003c 2946     		mov	r1, r5
 228 003e 0548     		ldr	r0, .L20
 229 0040 0068     		ldr	r0, [r0]
 230 0042 FFF7FEFF 		bl	lwip_sendto
 231              	.LVL16:
  61:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c ****     size_t writed = ret>0? ret:0;
 232              		.loc 1 61 5 is_stmt 1 view .LVU52
 233              		.loc 1 61 31 is_stmt 0 view .LVU53
 234 0046 20EAE070 		bic	r0, r0, r0, asr #31
 235              	.LVL17:
  62:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c **** 
  63:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c ****     return writed;
 236              		.loc 1 63 5 is_stmt 1 view .LVU54
  64:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c **** }
 237              		.loc 1 64 1 is_stmt 0 view .LVU55
 238 004a 06B0     		add	sp, sp, #24
 239              	.LCFI7:
 240              		.cfi_def_cfa_offset 16
 241              		@ sp needed
 242 004c 70BD     		pop	{r4, r5, r6, pc}
 243              	.LVL18:
 244              	.L15:
 245              	.LCFI8:
 246              		.cfi_def_cfa_offset 0
 247              		.cfi_restore 4
 248              		.cfi_restore 5
 249              		.cfi_restore 6
 250              		.cfi_restore 14
  52:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c ****     }
 251              		.loc 1 52 16 view .LVU56
 252 004e 0020     		movs	r0, #0
 253              	.LVL19:
 254              		.loc 1 64 1 view .LVU57
 255 0050 7047     		bx	lr
 256              	.L21:
 257 0052 00BF     		.align	2
 258              	.L20:
 259 0054 00000000 		.word	sock_fd
 260              		.cfi_endproc
 261              	.LFE176:
 263              		.section	.text.cubemx_transport_read,"ax",%progbits
 264              		.align	1
 265              		.global	cubemx_transport_read
 266              		.syntax unified
 267              		.thumb
 268              		.thumb_func
 270              	cubemx_transport_read:
 271              	.LVL20:
 272              	.LFB177:
  65:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c **** 
  66:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c **** size_t cubemx_transport_read(struct uxrCustomTransport* transport, uint8_t* buf, size_t len, int ti
 273              		.loc 1 66 120 is_stmt 1 view -0
 274              		.cfi_startproc
 275              		@ args = 4, pretend = 0, frame = 16
 276              		@ frame_needed = 0, uses_anonymous_args = 0
 277              		.loc 1 66 120 is_stmt 0 view .LVU59
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc2bj8wg.s 			page 7


 278 0000 70B5     		push	{r4, r5, r6, lr}
 279              	.LCFI9:
 280              		.cfi_def_cfa_offset 16
 281              		.cfi_offset 4, -16
 282              		.cfi_offset 5, -12
 283              		.cfi_offset 6, -8
 284              		.cfi_offset 14, -4
 285 0002 86B0     		sub	sp, sp, #24
 286              	.LCFI10:
 287              		.cfi_def_cfa_offset 40
 288 0004 0C46     		mov	r4, r1
 289 0006 1546     		mov	r5, r2
  67:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c **** 
  68:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c ****     int ret = 0;
 290              		.loc 1 68 5 is_stmt 1 view .LVU60
 291              	.LVL21:
  69:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c ****     //set timeout
  70:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c ****     struct timeval tv_out;
 292              		.loc 1 70 5 view .LVU61
  71:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c ****     tv_out.tv_sec = timeout / 1000;
 293              		.loc 1 71 5 view .LVU62
 294              		.loc 1 71 29 is_stmt 0 view .LVU63
 295 0008 124A     		ldr	r2, .L24
 296              	.LVL22:
 297              		.loc 1 71 29 view .LVU64
 298 000a 82FB0312 		smull	r1, r2, r2, r3
 299              	.LVL23:
 300              		.loc 1 71 29 view .LVU65
 301 000e D817     		asrs	r0, r3, #31
 302              	.LVL24:
 303              		.loc 1 71 29 view .LVU66
 304 0010 C0EBA210 		rsb	r0, r0, r2, asr #6
 305 0014 C217     		asrs	r2, r0, #31
 306              		.loc 1 71 19 view .LVU67
 307 0016 0290     		str	r0, [sp, #8]
 308 0018 0392     		str	r2, [sp, #12]
  72:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c ****     tv_out.tv_usec = (timeout % 1000) * 1000;
 309              		.loc 1 72 5 is_stmt 1 view .LVU68
 310              		.loc 1 72 31 is_stmt 0 view .LVU69
 311 001a 4FF47A72 		mov	r2, #1000
 312 001e 02FB1033 		mls	r3, r2, r0, r3
 313              	.LVL25:
 314              		.loc 1 72 39 view .LVU70
 315 0022 02FB03F3 		mul	r3, r2, r3
 316              		.loc 1 72 20 view .LVU71
 317 0026 0493     		str	r3, [sp, #16]
  73:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c ****     setsockopt(sock_fd, SOL_SOCKET, SO_RCVTIMEO,&tv_out, sizeof(tv_out));
 318              		.loc 1 73 5 is_stmt 1 view .LVU72
 319 0028 0B4E     		ldr	r6, .L24+4
 320 002a 1023     		movs	r3, #16
 321 002c 0093     		str	r3, [sp]
 322 002e 02AB     		add	r3, sp, #8
 323 0030 41F20602 		movw	r2, #4102
 324 0034 40F6FF71 		movw	r1, #4095
 325 0038 3068     		ldr	r0, [r6]
 326 003a FFF7FEFF 		bl	lwip_setsockopt
 327              	.LVL26:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc2bj8wg.s 			page 8


  74:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c ****     ret = recv(sock_fd, buf, len, MSG_WAITALL);
 328              		.loc 1 74 5 view .LVU73
 329              		.loc 1 74 11 is_stmt 0 view .LVU74
 330 003e 0223     		movs	r3, #2
 331 0040 2A46     		mov	r2, r5
 332 0042 2146     		mov	r1, r4
 333 0044 3068     		ldr	r0, [r6]
 334 0046 FFF7FEFF 		bl	lwip_recv
 335              	.LVL27:
  75:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c ****     size_t readed = ret > 0 ? ret : 0;
 336              		.loc 1 75 5 is_stmt 1 view .LVU75
  76:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c ****     return readed;
 337              		.loc 1 76 5 view .LVU76
  77:micro_ros_stm32cubemx_utils/extra_sources/microros_transports/udp_transport.c **** }
 338              		.loc 1 77 1 is_stmt 0 view .LVU77
 339 004a 20EAE070 		bic	r0, r0, r0, asr #31
 340              	.LVL28:
 341              		.loc 1 77 1 view .LVU78
 342 004e 06B0     		add	sp, sp, #24
 343              	.LCFI11:
 344              		.cfi_def_cfa_offset 16
 345              		@ sp needed
 346 0050 70BD     		pop	{r4, r5, r6, pc}
 347              	.LVL29:
 348              	.L25:
 349              		.loc 1 77 1 view .LVU79
 350 0052 00BF     		.align	2
 351              	.L24:
 352 0054 D34D6210 		.word	274877907
 353 0058 00000000 		.word	sock_fd
 354              		.cfi_endproc
 355              	.LFE177:
 357              		.section	.data.sock_fd,"aw"
 358              		.align	2
 361              	sock_fd:
 362 0000 FFFFFFFF 		.word	-1
 363              		.text
 364              	.Letext0:
 365              		.file 2 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\../lib/gcc/arm-none-eabi/13.3.1/..
 366              		.file 3 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\../lib/gcc/arm-none-eabi/13.3.1/..
 367              		.file 4 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\../lib/gcc/arm-none-eabi/13.3.1/in
 368              		.file 5 "micro_ros_stm32cubemx_utils/microros_static_library/libmicroros/microros_include/uxr/clie
 369              		.file 6 "micro_ros_stm32cubemx_utils/microros_static_library/libmicroros/microros_include/uxr/clie
 370              		.file 7 "micro_ros_stm32cubemx_utils/microros_static_library/libmicroros/microros_include/uxr/clie
 371              		.file 8 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\../lib/gcc/arm-none-eabi/13.3.1/..
 372              		.file 9 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\../lib/gcc/arm-none-eabi/13.3.1/..
 373              		.file 10 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\../lib/gcc/arm-none-eabi/13.3.1/.
 374              		.file 11 "Middlewares/Third_Party/LwIP/src/include/lwip/arch.h"
 375              		.file 12 "Middlewares/Third_Party/LwIP/src/include/lwip/inet.h"
 376              		.file 13 "Middlewares/Third_Party/LwIP/src/include/lwip/sockets.h"
 377              		.file 14 "Middlewares/Third_Party/LwIP/src/include/lwip/ip4_addr.h"
 378              		.file 15 "Middlewares/Third_Party/LwIP/src/include/lwip/def.h"
 379              		.file 16 "Middlewares/Third_Party/LwIP/src/include/lwip/memp.h"
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc2bj8wg.s 			page 9


DEFINED SYMBOLS
                            *ABS*:00000000 udp_transport.c
C:\Users\<USER>\AppData\Local\Temp\cc2bj8wg.s:20     .text.cubemx_transport_open:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cc2bj8wg.s:26     .text.cubemx_transport_open:00000000 cubemx_transport_open
C:\Users\<USER>\AppData\Local\Temp\cc2bj8wg.s:102    .text.cubemx_transport_open:00000048 $d
C:\Users\<USER>\AppData\Local\Temp\cc2bj8wg.s:361    .data.sock_fd:00000000 sock_fd
C:\Users\<USER>\AppData\Local\Temp\cc2bj8wg.s:107    .text.cubemx_transport_close:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cc2bj8wg.s:113    .text.cubemx_transport_close:00000000 cubemx_transport_close
C:\Users\<USER>\AppData\Local\Temp\cc2bj8wg.s:152    .text.cubemx_transport_close:00000020 $d
C:\Users\<USER>\AppData\Local\Temp\cc2bj8wg.s:157    .text.cubemx_transport_write:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cc2bj8wg.s:163    .text.cubemx_transport_write:00000000 cubemx_transport_write
C:\Users\<USER>\AppData\Local\Temp\cc2bj8wg.s:259    .text.cubemx_transport_write:00000054 $d
C:\Users\<USER>\AppData\Local\Temp\cc2bj8wg.s:264    .text.cubemx_transport_read:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cc2bj8wg.s:270    .text.cubemx_transport_read:00000000 cubemx_transport_read
C:\Users\<USER>\AppData\Local\Temp\cc2bj8wg.s:352    .text.cubemx_transport_read:00000054 $d
C:\Users\<USER>\AppData\Local\Temp\cc2bj8wg.s:358    .data.sock_fd:00000000 $d

UNDEFINED SYMBOLS
lwip_socket
lwip_htons
lwip_htonl
lwip_bind
lwip_close
ipaddr_addr
lwip_sendto
lwip_setsockopt
lwip_recv
