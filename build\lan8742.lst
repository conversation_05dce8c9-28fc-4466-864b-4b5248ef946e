ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5YGIqW.s 			page 1


   1              		.cpu cortex-m7
   2              		.arch armv7e-m
   3              		.fpu fpv5-d16
   4              		.eabi_attribute 28, 1
   5              		.eabi_attribute 20, 1
   6              		.eabi_attribute 21, 1
   7              		.eabi_attribute 23, 3
   8              		.eabi_attribute 24, 1
   9              		.eabi_attribute 25, 1
  10              		.eabi_attribute 26, 1
  11              		.eabi_attribute 30, 1
  12              		.eabi_attribute 34, 1
  13              		.eabi_attribute 18, 4
  14              		.file	"lan8742.c"
  15              		.text
  16              	.Ltext0:
  17              		.cfi_sections	.debug_frame
  18              		.file 1 "Drivers/BSP/Components/lan8742/lan8742.c"
  19              		.section	.text.LAN8742_RegisterBusIO,"ax",%progbits
  20              		.align	1
  21              		.global	LAN8742_RegisterBusIO
  22              		.syntax unified
  23              		.thumb
  24              		.thumb_func
  26              	LAN8742_RegisterBusIO:
  27              	.LVL0:
  28              	.LFB0:
   1:Drivers/BSP/Components/lan8742/lan8742.c **** /**
   2:Drivers/BSP/Components/lan8742/lan8742.c ****   ******************************************************************************
   3:Drivers/BSP/Components/lan8742/lan8742.c ****   * @file    lan8742.c
   4:Drivers/BSP/Components/lan8742/lan8742.c ****   * <AUTHOR> Application Team
   5:Drivers/BSP/Components/lan8742/lan8742.c ****   * @brief   This file provides a set of functions needed to manage the LAN742
   6:Drivers/BSP/Components/lan8742/lan8742.c ****   *          PHY devices.
   7:Drivers/BSP/Components/lan8742/lan8742.c ****   ******************************************************************************
   8:Drivers/BSP/Components/lan8742/lan8742.c ****   * @attention
   9:Drivers/BSP/Components/lan8742/lan8742.c ****   *
  10:Drivers/BSP/Components/lan8742/lan8742.c ****   * Copyright (c) 2017 STMicroelectronics.
  11:Drivers/BSP/Components/lan8742/lan8742.c ****   * All rights reserved.
  12:Drivers/BSP/Components/lan8742/lan8742.c ****   *
  13:Drivers/BSP/Components/lan8742/lan8742.c ****   * This software is licensed under terms that can be found in the LICENSE file
  14:Drivers/BSP/Components/lan8742/lan8742.c ****   * in the root directory of this software component.
  15:Drivers/BSP/Components/lan8742/lan8742.c ****   * If no LICENSE file comes with this software, it is provided AS-IS.
  16:Drivers/BSP/Components/lan8742/lan8742.c ****   *
  17:Drivers/BSP/Components/lan8742/lan8742.c ****   ******************************************************************************
  18:Drivers/BSP/Components/lan8742/lan8742.c ****   */
  19:Drivers/BSP/Components/lan8742/lan8742.c **** 
  20:Drivers/BSP/Components/lan8742/lan8742.c **** /* Includes ------------------------------------------------------------------*/
  21:Drivers/BSP/Components/lan8742/lan8742.c **** #include "lan8742.h"
  22:Drivers/BSP/Components/lan8742/lan8742.c **** 
  23:Drivers/BSP/Components/lan8742/lan8742.c **** /** @addtogroup BSP
  24:Drivers/BSP/Components/lan8742/lan8742.c ****   * @{
  25:Drivers/BSP/Components/lan8742/lan8742.c ****   */
  26:Drivers/BSP/Components/lan8742/lan8742.c **** 
  27:Drivers/BSP/Components/lan8742/lan8742.c **** /** @addtogroup Component
  28:Drivers/BSP/Components/lan8742/lan8742.c ****   * @{
  29:Drivers/BSP/Components/lan8742/lan8742.c ****   */
  30:Drivers/BSP/Components/lan8742/lan8742.c **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5YGIqW.s 			page 2


  31:Drivers/BSP/Components/lan8742/lan8742.c **** /** @defgroup LAN8742 LAN8742
  32:Drivers/BSP/Components/lan8742/lan8742.c ****   * @{
  33:Drivers/BSP/Components/lan8742/lan8742.c ****   */
  34:Drivers/BSP/Components/lan8742/lan8742.c **** 
  35:Drivers/BSP/Components/lan8742/lan8742.c **** /* Private typedef -----------------------------------------------------------*/
  36:Drivers/BSP/Components/lan8742/lan8742.c **** /* Private define ------------------------------------------------------------*/
  37:Drivers/BSP/Components/lan8742/lan8742.c **** /** @defgroup LAN8742_Private_Defines LAN8742 Private Defines
  38:Drivers/BSP/Components/lan8742/lan8742.c ****   * @{
  39:Drivers/BSP/Components/lan8742/lan8742.c ****   */
  40:Drivers/BSP/Components/lan8742/lan8742.c **** #define LAN8742_MAX_DEV_ADDR   ((uint32_t)31U)
  41:Drivers/BSP/Components/lan8742/lan8742.c **** /**
  42:Drivers/BSP/Components/lan8742/lan8742.c ****   * @}
  43:Drivers/BSP/Components/lan8742/lan8742.c ****   */
  44:Drivers/BSP/Components/lan8742/lan8742.c **** 
  45:Drivers/BSP/Components/lan8742/lan8742.c **** /* Private macro -------------------------------------------------------------*/
  46:Drivers/BSP/Components/lan8742/lan8742.c **** /* Private variables ---------------------------------------------------------*/
  47:Drivers/BSP/Components/lan8742/lan8742.c **** /* Private function prototypes -----------------------------------------------*/
  48:Drivers/BSP/Components/lan8742/lan8742.c **** /* Private functions ---------------------------------------------------------*/
  49:Drivers/BSP/Components/lan8742/lan8742.c **** /** @defgroup LAN8742_Private_Functions LAN8742 Private Functions
  50:Drivers/BSP/Components/lan8742/lan8742.c ****   * @{
  51:Drivers/BSP/Components/lan8742/lan8742.c ****   */
  52:Drivers/BSP/Components/lan8742/lan8742.c **** 
  53:Drivers/BSP/Components/lan8742/lan8742.c **** /**
  54:Drivers/BSP/Components/lan8742/lan8742.c ****   * @brief  Register IO functions to component object
  55:Drivers/BSP/Components/lan8742/lan8742.c ****   * @param  pObj: device object  of LAN8742_Object_t.
  56:Drivers/BSP/Components/lan8742/lan8742.c ****   * @param  ioctx: holds device IO functions.
  57:Drivers/BSP/Components/lan8742/lan8742.c ****   * @retval LAN8742_STATUS_OK  if OK
  58:Drivers/BSP/Components/lan8742/lan8742.c ****   *         LAN8742_STATUS_ERROR if missing mandatory function
  59:Drivers/BSP/Components/lan8742/lan8742.c ****   */
  60:Drivers/BSP/Components/lan8742/lan8742.c **** int32_t  LAN8742_RegisterBusIO(lan8742_Object_t *pObj, lan8742_IOCtx_t *ioctx)
  61:Drivers/BSP/Components/lan8742/lan8742.c **** {
  29              		.loc 1 61 1 view -0
  30              		.cfi_startproc
  31              		@ args = 0, pretend = 0, frame = 0
  32              		@ frame_needed = 0, uses_anonymous_args = 0
  33              		@ link register save eliminated.
  62:Drivers/BSP/Components/lan8742/lan8742.c ****   if(!pObj || !ioctx->ReadReg || !ioctx->WriteReg || !ioctx->GetTick)
  34              		.loc 1 62 3 view .LVU1
  35              		.loc 1 62 5 is_stmt 0 view .LVU2
  36 0000 88B1     		cbz	r0, .L3
  37              		.loc 1 62 21 discriminator 1 view .LVU3
  38 0002 CA68     		ldr	r2, [r1, #12]
  39              		.loc 1 62 12 discriminator 1 view .LVU4
  40 0004 92B1     		cbz	r2, .L4
  41              		.loc 1 62 40 discriminator 2 view .LVU5
  42 0006 8A68     		ldr	r2, [r1, #8]
  43              		.loc 1 62 31 discriminator 2 view .LVU6
  44 0008 9AB1     		cbz	r2, .L5
  45              		.loc 1 62 60 discriminator 3 view .LVU7
  46 000a 0A69     		ldr	r2, [r1, #16]
  47              		.loc 1 62 51 discriminator 3 view .LVU8
  48 000c A2B1     		cbz	r2, .L6
  63:Drivers/BSP/Components/lan8742/lan8742.c ****   {
  64:Drivers/BSP/Components/lan8742/lan8742.c ****     return LAN8742_STATUS_ERROR;
  65:Drivers/BSP/Components/lan8742/lan8742.c ****   }
  66:Drivers/BSP/Components/lan8742/lan8742.c **** 
  67:Drivers/BSP/Components/lan8742/lan8742.c ****   pObj->IO.Init = ioctx->Init;
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5YGIqW.s 			page 3


  49              		.loc 1 67 3 is_stmt 1 view .LVU9
  50              		.loc 1 67 24 is_stmt 0 view .LVU10
  51 000e 0A68     		ldr	r2, [r1]
  52              		.loc 1 67 17 view .LVU11
  53 0010 8260     		str	r2, [r0, #8]
  68:Drivers/BSP/Components/lan8742/lan8742.c ****   pObj->IO.DeInit = ioctx->DeInit;
  54              		.loc 1 68 3 is_stmt 1 view .LVU12
  55              		.loc 1 68 26 is_stmt 0 view .LVU13
  56 0012 4A68     		ldr	r2, [r1, #4]
  57              		.loc 1 68 19 view .LVU14
  58 0014 C260     		str	r2, [r0, #12]
  69:Drivers/BSP/Components/lan8742/lan8742.c ****   pObj->IO.ReadReg = ioctx->ReadReg;
  59              		.loc 1 69 3 is_stmt 1 view .LVU15
  60              		.loc 1 69 27 is_stmt 0 view .LVU16
  61 0016 CA68     		ldr	r2, [r1, #12]
  62              		.loc 1 69 20 view .LVU17
  63 0018 4261     		str	r2, [r0, #20]
  70:Drivers/BSP/Components/lan8742/lan8742.c ****   pObj->IO.WriteReg = ioctx->WriteReg;
  64              		.loc 1 70 3 is_stmt 1 view .LVU18
  65              		.loc 1 70 28 is_stmt 0 view .LVU19
  66 001a 8A68     		ldr	r2, [r1, #8]
  67              		.loc 1 70 21 view .LVU20
  68 001c 0261     		str	r2, [r0, #16]
  71:Drivers/BSP/Components/lan8742/lan8742.c ****   pObj->IO.GetTick = ioctx->GetTick;
  69              		.loc 1 71 3 is_stmt 1 view .LVU21
  70              		.loc 1 71 27 is_stmt 0 view .LVU22
  71 001e 0A69     		ldr	r2, [r1, #16]
  72              		.loc 1 71 20 view .LVU23
  73 0020 8261     		str	r2, [r0, #24]
  72:Drivers/BSP/Components/lan8742/lan8742.c **** 
  73:Drivers/BSP/Components/lan8742/lan8742.c ****   return LAN8742_STATUS_OK;
  74              		.loc 1 73 3 is_stmt 1 view .LVU24
  75              		.loc 1 73 10 is_stmt 0 view .LVU25
  76 0022 0020     		movs	r0, #0
  77              	.LVL1:
  78              		.loc 1 73 10 view .LVU26
  79 0024 7047     		bx	lr
  80              	.LVL2:
  81              	.L3:
  64:Drivers/BSP/Components/lan8742/lan8742.c ****   }
  82              		.loc 1 64 12 view .LVU27
  83 0026 4FF0FF30 		mov	r0, #-1
  84              	.LVL3:
  64:Drivers/BSP/Components/lan8742/lan8742.c ****   }
  85              		.loc 1 64 12 view .LVU28
  86 002a 7047     		bx	lr
  87              	.LVL4:
  88              	.L4:
  64:Drivers/BSP/Components/lan8742/lan8742.c ****   }
  89              		.loc 1 64 12 view .LVU29
  90 002c 4FF0FF30 		mov	r0, #-1
  91              	.LVL5:
  64:Drivers/BSP/Components/lan8742/lan8742.c ****   }
  92              		.loc 1 64 12 view .LVU30
  93 0030 7047     		bx	lr
  94              	.LVL6:
  95              	.L5:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5YGIqW.s 			page 4


  64:Drivers/BSP/Components/lan8742/lan8742.c ****   }
  96              		.loc 1 64 12 view .LVU31
  97 0032 4FF0FF30 		mov	r0, #-1
  98              	.LVL7:
  64:Drivers/BSP/Components/lan8742/lan8742.c ****   }
  99              		.loc 1 64 12 view .LVU32
 100 0036 7047     		bx	lr
 101              	.LVL8:
 102              	.L6:
  64:Drivers/BSP/Components/lan8742/lan8742.c ****   }
 103              		.loc 1 64 12 view .LVU33
 104 0038 4FF0FF30 		mov	r0, #-1
 105              	.LVL9:
  74:Drivers/BSP/Components/lan8742/lan8742.c **** }
 106              		.loc 1 74 1 view .LVU34
 107 003c 7047     		bx	lr
 108              		.cfi_endproc
 109              	.LFE0:
 111              		.section	.text.LAN8742_Init,"ax",%progbits
 112              		.align	1
 113              		.global	LAN8742_Init
 114              		.syntax unified
 115              		.thumb
 116              		.thumb_func
 118              	LAN8742_Init:
 119              	.LVL10:
 120              	.LFB1:
  75:Drivers/BSP/Components/lan8742/lan8742.c **** 
  76:Drivers/BSP/Components/lan8742/lan8742.c **** /**
  77:Drivers/BSP/Components/lan8742/lan8742.c ****   * @brief  Initialize the lan8742 and configure the needed hardware resources
  78:Drivers/BSP/Components/lan8742/lan8742.c ****   * @param  pObj: device object LAN8742_Object_t.
  79:Drivers/BSP/Components/lan8742/lan8742.c ****   * @retval LAN8742_STATUS_OK  if OK
  80:Drivers/BSP/Components/lan8742/lan8742.c ****   *         LAN8742_STATUS_ADDRESS_ERROR if cannot find device address
  81:Drivers/BSP/Components/lan8742/lan8742.c ****   *         LAN8742_STATUS_READ_ERROR if cannot read register
  82:Drivers/BSP/Components/lan8742/lan8742.c ****   */
  83:Drivers/BSP/Components/lan8742/lan8742.c ****  int32_t LAN8742_Init(lan8742_Object_t *pObj)
  84:Drivers/BSP/Components/lan8742/lan8742.c ****  {
 121              		.loc 1 84 2 is_stmt 1 view -0
 122              		.cfi_startproc
 123              		@ args = 0, pretend = 0, frame = 8
 124              		@ frame_needed = 0, uses_anonymous_args = 0
 125              		.loc 1 84 2 is_stmt 0 view .LVU36
 126 0000 70B5     		push	{r4, r5, r6, lr}
 127              	.LCFI0:
 128              		.cfi_def_cfa_offset 16
 129              		.cfi_offset 4, -16
 130              		.cfi_offset 5, -12
 131              		.cfi_offset 6, -8
 132              		.cfi_offset 14, -4
 133 0002 82B0     		sub	sp, sp, #8
 134              	.LCFI1:
 135              		.cfi_def_cfa_offset 24
  85:Drivers/BSP/Components/lan8742/lan8742.c ****    uint32_t regvalue = 0, addr = 0;
 136              		.loc 1 85 4 is_stmt 1 view .LVU37
 137              		.loc 1 85 13 is_stmt 0 view .LVU38
 138 0004 0023     		movs	r3, #0
 139 0006 0193     		str	r3, [sp, #4]
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5YGIqW.s 			page 5


 140              	.LVL11:
  86:Drivers/BSP/Components/lan8742/lan8742.c ****    int32_t status = LAN8742_STATUS_OK;
 141              		.loc 1 86 4 is_stmt 1 view .LVU39
  87:Drivers/BSP/Components/lan8742/lan8742.c **** 
  88:Drivers/BSP/Components/lan8742/lan8742.c ****    if(pObj->Is_Initialized == 0)
 142              		.loc 1 88 4 view .LVU40
 143              		.loc 1 88 11 is_stmt 0 view .LVU41
 144 0008 4468     		ldr	r4, [r0, #4]
 145              		.loc 1 88 6 view .LVU42
 146 000a 0CBB     		cbnz	r4, .L14
 147 000c 0546     		mov	r5, r0
  89:Drivers/BSP/Components/lan8742/lan8742.c ****    {
  90:Drivers/BSP/Components/lan8742/lan8742.c ****      if(pObj->IO.Init != 0)
 148              		.loc 1 90 6 is_stmt 1 view .LVU43
 149              		.loc 1 90 17 is_stmt 0 view .LVU44
 150 000e 8368     		ldr	r3, [r0, #8]
 151              		.loc 1 90 8 view .LVU45
 152 0010 03B1     		cbz	r3, .L9
  91:Drivers/BSP/Components/lan8742/lan8742.c ****      {
  92:Drivers/BSP/Components/lan8742/lan8742.c ****        /* GPIO and Clocks initialization */
  93:Drivers/BSP/Components/lan8742/lan8742.c ****        pObj->IO.Init();
 153              		.loc 1 93 8 is_stmt 1 view .LVU46
 154 0012 9847     		blx	r3
 155              	.LVL12:
 156              	.L9:
  94:Drivers/BSP/Components/lan8742/lan8742.c ****      }
  95:Drivers/BSP/Components/lan8742/lan8742.c **** 
  96:Drivers/BSP/Components/lan8742/lan8742.c ****      /* for later check */
  97:Drivers/BSP/Components/lan8742/lan8742.c ****      pObj->DevAddr = LAN8742_MAX_DEV_ADDR + 1;
 157              		.loc 1 97 6 view .LVU47
 158              		.loc 1 97 20 is_stmt 0 view .LVU48
 159 0014 2023     		movs	r3, #32
 160 0016 2B60     		str	r3, [r5]
  98:Drivers/BSP/Components/lan8742/lan8742.c **** 
  99:Drivers/BSP/Components/lan8742/lan8742.c ****      /* Get the device address from special mode register */
 100:Drivers/BSP/Components/lan8742/lan8742.c ****      for(addr = 0; addr <= LAN8742_MAX_DEV_ADDR; addr ++)
 161              		.loc 1 100 6 is_stmt 1 view .LVU49
  86:Drivers/BSP/Components/lan8742/lan8742.c **** 
 162              		.loc 1 86 12 is_stmt 0 view .LVU50
 163 0018 0026     		movs	r6, #0
 164              		.loc 1 100 6 view .LVU51
 165 001a 02E0     		b	.L10
 166              	.LVL13:
 167              	.L15:
 101:Drivers/BSP/Components/lan8742/lan8742.c ****      {
 102:Drivers/BSP/Components/lan8742/lan8742.c ****        if(pObj->IO.ReadReg(addr, LAN8742_SMR, &regvalue) < 0)
 103:Drivers/BSP/Components/lan8742/lan8742.c ****        {
 104:Drivers/BSP/Components/lan8742/lan8742.c ****          status = LAN8742_STATUS_READ_ERROR;
 168              		.loc 1 104 17 view .LVU52
 169 001c 6FF00406 		mvn	r6, #4
 170              	.LVL14:
 171              	.L11:
 100:Drivers/BSP/Components/lan8742/lan8742.c ****      {
 172              		.loc 1 100 55 is_stmt 1 discriminator 2 view .LVU53
 173 0020 0134     		adds	r4, r4, #1
 174              	.LVL15:
 175              	.L10:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5YGIqW.s 			page 6


 100:Drivers/BSP/Components/lan8742/lan8742.c ****      {
 176              		.loc 1 100 25 discriminator 1 view .LVU54
 177 0022 1F2C     		cmp	r4, #31
 178 0024 0DD8     		bhi	.L12
 102:Drivers/BSP/Components/lan8742/lan8742.c ****        {
 179              		.loc 1 102 8 view .LVU55
 102:Drivers/BSP/Components/lan8742/lan8742.c ****        {
 180              		.loc 1 102 19 is_stmt 0 view .LVU56
 181 0026 6B69     		ldr	r3, [r5, #20]
 102:Drivers/BSP/Components/lan8742/lan8742.c ****        {
 182              		.loc 1 102 11 view .LVU57
 183 0028 01AA     		add	r2, sp, #4
 184 002a 1221     		movs	r1, #18
 185 002c 2046     		mov	r0, r4
 186 002e 9847     		blx	r3
 187              	.LVL16:
 102:Drivers/BSP/Components/lan8742/lan8742.c ****        {
 188              		.loc 1 102 10 discriminator 1 view .LVU58
 189 0030 0028     		cmp	r0, #0
 190 0032 F3DB     		blt	.L15
 105:Drivers/BSP/Components/lan8742/lan8742.c ****          /* Can't read from this device address
 106:Drivers/BSP/Components/lan8742/lan8742.c ****             continue with next address */
 107:Drivers/BSP/Components/lan8742/lan8742.c ****          continue;
 108:Drivers/BSP/Components/lan8742/lan8742.c ****        }
 109:Drivers/BSP/Components/lan8742/lan8742.c **** 
 110:Drivers/BSP/Components/lan8742/lan8742.c ****        if((regvalue & LAN8742_SMR_PHY_ADDR) == addr)
 191              		.loc 1 110 8 is_stmt 1 view .LVU59
 192              		.loc 1 110 21 is_stmt 0 view .LVU60
 193 0034 019B     		ldr	r3, [sp, #4]
 194 0036 03F01F03 		and	r3, r3, #31
 195              		.loc 1 110 10 view .LVU61
 196 003a A342     		cmp	r3, r4
 197 003c F0D1     		bne	.L11
 111:Drivers/BSP/Components/lan8742/lan8742.c ****        {
 112:Drivers/BSP/Components/lan8742/lan8742.c ****          pObj->DevAddr = addr;
 198              		.loc 1 112 10 is_stmt 1 view .LVU62
 199              		.loc 1 112 24 is_stmt 0 view .LVU63
 200 003e 2C60     		str	r4, [r5]
 113:Drivers/BSP/Components/lan8742/lan8742.c ****          status = LAN8742_STATUS_OK;
 201              		.loc 1 113 10 is_stmt 1 view .LVU64
 202              	.LVL17:
 114:Drivers/BSP/Components/lan8742/lan8742.c ****          break;
 203              		.loc 1 114 10 view .LVU65
 113:Drivers/BSP/Components/lan8742/lan8742.c ****          status = LAN8742_STATUS_OK;
 204              		.loc 1 113 17 is_stmt 0 view .LVU66
 205 0040 0026     		movs	r6, #0
 206              	.LVL18:
 207              	.L12:
 115:Drivers/BSP/Components/lan8742/lan8742.c ****        }
 116:Drivers/BSP/Components/lan8742/lan8742.c ****      }
 117:Drivers/BSP/Components/lan8742/lan8742.c **** 
 118:Drivers/BSP/Components/lan8742/lan8742.c ****      if(pObj->DevAddr > LAN8742_MAX_DEV_ADDR)
 208              		.loc 1 118 6 is_stmt 1 view .LVU67
 209              		.loc 1 118 13 is_stmt 0 view .LVU68
 210 0042 2B68     		ldr	r3, [r5]
 211              		.loc 1 118 8 view .LVU69
 212 0044 1F2B     		cmp	r3, #31
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5YGIqW.s 			page 7


 213 0046 07D8     		bhi	.L16
 119:Drivers/BSP/Components/lan8742/lan8742.c ****      {
 120:Drivers/BSP/Components/lan8742/lan8742.c ****        status = LAN8742_STATUS_ADDRESS_ERROR;
 121:Drivers/BSP/Components/lan8742/lan8742.c ****      }
 122:Drivers/BSP/Components/lan8742/lan8742.c **** 
 123:Drivers/BSP/Components/lan8742/lan8742.c ****      /* if device address is matched */
 124:Drivers/BSP/Components/lan8742/lan8742.c ****      if(status == LAN8742_STATUS_OK)
 214              		.loc 1 124 6 is_stmt 1 view .LVU70
 215              		.loc 1 124 8 is_stmt 0 view .LVU71
 216 0048 1EB9     		cbnz	r6, .L7
 125:Drivers/BSP/Components/lan8742/lan8742.c ****      {
 126:Drivers/BSP/Components/lan8742/lan8742.c ****        pObj->Is_Initialized = 1;
 217              		.loc 1 126 8 is_stmt 1 view .LVU72
 218              		.loc 1 126 29 is_stmt 0 view .LVU73
 219 004a 0123     		movs	r3, #1
 220 004c 6B60     		str	r3, [r5, #4]
 221 004e 00E0     		b	.L7
 222              	.LVL19:
 223              	.L14:
  86:Drivers/BSP/Components/lan8742/lan8742.c **** 
 224              		.loc 1 86 12 view .LVU74
 225 0050 0026     		movs	r6, #0
 226              	.LVL20:
 227              	.L7:
 127:Drivers/BSP/Components/lan8742/lan8742.c ****      }
 128:Drivers/BSP/Components/lan8742/lan8742.c ****    }
 129:Drivers/BSP/Components/lan8742/lan8742.c **** 
 130:Drivers/BSP/Components/lan8742/lan8742.c ****    return status;
 131:Drivers/BSP/Components/lan8742/lan8742.c ****  }
 228              		.loc 1 131 2 view .LVU75
 229 0052 3046     		mov	r0, r6
 230 0054 02B0     		add	sp, sp, #8
 231              	.LCFI2:
 232              		.cfi_remember_state
 233              		.cfi_def_cfa_offset 16
 234              		@ sp needed
 235 0056 70BD     		pop	{r4, r5, r6, pc}
 236              	.LVL21:
 237              	.L16:
 238              	.LCFI3:
 239              		.cfi_restore_state
 120:Drivers/BSP/Components/lan8742/lan8742.c ****      }
 240              		.loc 1 120 15 view .LVU76
 241 0058 6FF00206 		mvn	r6, #2
 242              	.LVL22:
 130:Drivers/BSP/Components/lan8742/lan8742.c ****  }
 243              		.loc 1 130 4 is_stmt 1 view .LVU77
 130:Drivers/BSP/Components/lan8742/lan8742.c ****  }
 244              		.loc 1 130 11 is_stmt 0 view .LVU78
 245 005c F9E7     		b	.L7
 246              		.cfi_endproc
 247              	.LFE1:
 249              		.section	.text.LAN8742_DeInit,"ax",%progbits
 250              		.align	1
 251              		.global	LAN8742_DeInit
 252              		.syntax unified
 253              		.thumb
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5YGIqW.s 			page 8


 254              		.thumb_func
 256              	LAN8742_DeInit:
 257              	.LVL23:
 258              	.LFB2:
 132:Drivers/BSP/Components/lan8742/lan8742.c **** 
 133:Drivers/BSP/Components/lan8742/lan8742.c **** /**
 134:Drivers/BSP/Components/lan8742/lan8742.c ****   * @brief  De-Initialize the lan8742 and it's hardware resources
 135:Drivers/BSP/Components/lan8742/lan8742.c ****   * @param  pObj: device object LAN8742_Object_t.
 136:Drivers/BSP/Components/lan8742/lan8742.c ****   * @retval None
 137:Drivers/BSP/Components/lan8742/lan8742.c ****   */
 138:Drivers/BSP/Components/lan8742/lan8742.c **** int32_t LAN8742_DeInit(lan8742_Object_t *pObj)
 139:Drivers/BSP/Components/lan8742/lan8742.c **** {
 259              		.loc 1 139 1 is_stmt 1 view -0
 260              		.cfi_startproc
 261              		@ args = 0, pretend = 0, frame = 0
 262              		@ frame_needed = 0, uses_anonymous_args = 0
 140:Drivers/BSP/Components/lan8742/lan8742.c ****   if(pObj->Is_Initialized)
 263              		.loc 1 140 3 view .LVU80
 264              		.loc 1 140 10 is_stmt 0 view .LVU81
 265 0000 4368     		ldr	r3, [r0, #4]
 266              		.loc 1 140 5 view .LVU82
 267 0002 4BB1     		cbz	r3, .L21
 139:Drivers/BSP/Components/lan8742/lan8742.c ****   if(pObj->Is_Initialized)
 268              		.loc 1 139 1 view .LVU83
 269 0004 10B5     		push	{r4, lr}
 270              	.LCFI4:
 271              		.cfi_def_cfa_offset 8
 272              		.cfi_offset 4, -8
 273              		.cfi_offset 14, -4
 274 0006 0446     		mov	r4, r0
 141:Drivers/BSP/Components/lan8742/lan8742.c ****   {
 142:Drivers/BSP/Components/lan8742/lan8742.c ****     if(pObj->IO.DeInit != 0)
 275              		.loc 1 142 5 is_stmt 1 view .LVU84
 276              		.loc 1 142 16 is_stmt 0 view .LVU85
 277 0008 C368     		ldr	r3, [r0, #12]
 278              		.loc 1 142 7 view .LVU86
 279 000a 13B1     		cbz	r3, .L20
 143:Drivers/BSP/Components/lan8742/lan8742.c ****     {
 144:Drivers/BSP/Components/lan8742/lan8742.c ****       if(pObj->IO.DeInit() < 0)
 280              		.loc 1 144 7 is_stmt 1 view .LVU87
 281              		.loc 1 144 10 is_stmt 0 view .LVU88
 282 000c 9847     		blx	r3
 283              	.LVL24:
 284              		.loc 1 144 9 discriminator 1 view .LVU89
 285 000e 0028     		cmp	r0, #0
 286 0010 04DB     		blt	.L22
 287              	.L20:
 145:Drivers/BSP/Components/lan8742/lan8742.c ****       {
 146:Drivers/BSP/Components/lan8742/lan8742.c ****         return LAN8742_STATUS_ERROR;
 147:Drivers/BSP/Components/lan8742/lan8742.c ****       }
 148:Drivers/BSP/Components/lan8742/lan8742.c ****     }
 149:Drivers/BSP/Components/lan8742/lan8742.c **** 
 150:Drivers/BSP/Components/lan8742/lan8742.c ****     pObj->Is_Initialized = 0;
 288              		.loc 1 150 5 is_stmt 1 view .LVU90
 289              		.loc 1 150 26 is_stmt 0 view .LVU91
 290 0012 0020     		movs	r0, #0
 291 0014 6060     		str	r0, [r4, #4]
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5YGIqW.s 			page 9


 292              	.L18:
 151:Drivers/BSP/Components/lan8742/lan8742.c ****   }
 152:Drivers/BSP/Components/lan8742/lan8742.c **** 
 153:Drivers/BSP/Components/lan8742/lan8742.c ****   return LAN8742_STATUS_OK;
 154:Drivers/BSP/Components/lan8742/lan8742.c **** }
 293              		.loc 1 154 1 view .LVU92
 294 0016 10BD     		pop	{r4, pc}
 295              	.LVL25:
 296              	.L21:
 297              	.LCFI5:
 298              		.cfi_def_cfa_offset 0
 299              		.cfi_restore 4
 300              		.cfi_restore 14
 153:Drivers/BSP/Components/lan8742/lan8742.c **** }
 301              		.loc 1 153 10 view .LVU93
 302 0018 0020     		movs	r0, #0
 303              	.LVL26:
 304              		.loc 1 154 1 view .LVU94
 305 001a 7047     		bx	lr
 306              	.LVL27:
 307              	.L22:
 308              	.LCFI6:
 309              		.cfi_def_cfa_offset 8
 310              		.cfi_offset 4, -8
 311              		.cfi_offset 14, -4
 146:Drivers/BSP/Components/lan8742/lan8742.c ****       }
 312              		.loc 1 146 16 view .LVU95
 313 001c 4FF0FF30 		mov	r0, #-1
 314 0020 F9E7     		b	.L18
 315              		.cfi_endproc
 316              	.LFE2:
 318              		.section	.text.LAN8742_DisablePowerDownMode,"ax",%progbits
 319              		.align	1
 320              		.global	LAN8742_DisablePowerDownMode
 321              		.syntax unified
 322              		.thumb
 323              		.thumb_func
 325              	LAN8742_DisablePowerDownMode:
 326              	.LVL28:
 327              	.LFB3:
 155:Drivers/BSP/Components/lan8742/lan8742.c **** 
 156:Drivers/BSP/Components/lan8742/lan8742.c **** /**
 157:Drivers/BSP/Components/lan8742/lan8742.c ****   * @brief  Disable the LAN8742 power down mode.
 158:Drivers/BSP/Components/lan8742/lan8742.c ****   * @param  pObj: device object LAN8742_Object_t.
 159:Drivers/BSP/Components/lan8742/lan8742.c ****   * @retval LAN8742_STATUS_OK  if OK
 160:Drivers/BSP/Components/lan8742/lan8742.c ****   *         LAN8742_STATUS_READ_ERROR if cannot read register
 161:Drivers/BSP/Components/lan8742/lan8742.c ****   *         LAN8742_STATUS_WRITE_ERROR if cannot write to register
 162:Drivers/BSP/Components/lan8742/lan8742.c ****   */
 163:Drivers/BSP/Components/lan8742/lan8742.c **** int32_t LAN8742_DisablePowerDownMode(lan8742_Object_t *pObj)
 164:Drivers/BSP/Components/lan8742/lan8742.c **** {
 328              		.loc 1 164 1 is_stmt 1 view -0
 329              		.cfi_startproc
 330              		@ args = 0, pretend = 0, frame = 8
 331              		@ frame_needed = 0, uses_anonymous_args = 0
 332              		.loc 1 164 1 is_stmt 0 view .LVU97
 333 0000 10B5     		push	{r4, lr}
 334              	.LCFI7:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5YGIqW.s 			page 10


 335              		.cfi_def_cfa_offset 8
 336              		.cfi_offset 4, -8
 337              		.cfi_offset 14, -4
 338 0002 82B0     		sub	sp, sp, #8
 339              	.LCFI8:
 340              		.cfi_def_cfa_offset 16
 341 0004 0446     		mov	r4, r0
 165:Drivers/BSP/Components/lan8742/lan8742.c ****   uint32_t readval = 0;
 342              		.loc 1 165 3 is_stmt 1 view .LVU98
 343              		.loc 1 165 12 is_stmt 0 view .LVU99
 344 0006 0021     		movs	r1, #0
 345 0008 0191     		str	r1, [sp, #4]
 166:Drivers/BSP/Components/lan8742/lan8742.c ****   int32_t status = LAN8742_STATUS_OK;
 346              		.loc 1 166 3 is_stmt 1 view .LVU100
 347              	.LVL29:
 167:Drivers/BSP/Components/lan8742/lan8742.c **** 
 168:Drivers/BSP/Components/lan8742/lan8742.c ****   if(pObj->IO.ReadReg(pObj->DevAddr, LAN8742_BCR, &readval) >= 0)
 348              		.loc 1 168 3 view .LVU101
 349              		.loc 1 168 14 is_stmt 0 view .LVU102
 350 000a 4369     		ldr	r3, [r0, #20]
 351              		.loc 1 168 6 view .LVU103
 352 000c 01AA     		add	r2, sp, #4
 353 000e 0068     		ldr	r0, [r0]
 354              	.LVL30:
 355              		.loc 1 168 6 view .LVU104
 356 0010 9847     		blx	r3
 357              	.LVL31:
 358              		.loc 1 168 5 discriminator 1 view .LVU105
 359 0012 0028     		cmp	r0, #0
 360 0014 0CDB     		blt	.L29
 169:Drivers/BSP/Components/lan8742/lan8742.c ****   {
 170:Drivers/BSP/Components/lan8742/lan8742.c ****     readval &= ~LAN8742_BCR_POWER_DOWN;
 361              		.loc 1 170 5 is_stmt 1 view .LVU106
 362              		.loc 1 170 13 is_stmt 0 view .LVU107
 363 0016 019A     		ldr	r2, [sp, #4]
 364 0018 22F40062 		bic	r2, r2, #2048
 365 001c 0192     		str	r2, [sp, #4]
 171:Drivers/BSP/Components/lan8742/lan8742.c **** 
 172:Drivers/BSP/Components/lan8742/lan8742.c ****     /* Apply configuration */
 173:Drivers/BSP/Components/lan8742/lan8742.c ****     if(pObj->IO.WriteReg(pObj->DevAddr, LAN8742_BCR, readval) < 0)
 366              		.loc 1 173 5 is_stmt 1 view .LVU108
 367              		.loc 1 173 16 is_stmt 0 view .LVU109
 368 001e 2369     		ldr	r3, [r4, #16]
 369              		.loc 1 173 8 view .LVU110
 370 0020 0021     		movs	r1, #0
 371 0022 2068     		ldr	r0, [r4]
 372 0024 9847     		blx	r3
 373              	.LVL32:
 374              		.loc 1 173 7 discriminator 1 view .LVU111
 375 0026 0028     		cmp	r0, #0
 376 0028 05DB     		blt	.L30
 166:Drivers/BSP/Components/lan8742/lan8742.c **** 
 377              		.loc 1 166 11 view .LVU112
 378 002a 0020     		movs	r0, #0
 379              	.LVL33:
 380              	.L27:
 174:Drivers/BSP/Components/lan8742/lan8742.c ****     {
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5YGIqW.s 			page 11


 175:Drivers/BSP/Components/lan8742/lan8742.c ****       status =  LAN8742_STATUS_WRITE_ERROR;
 176:Drivers/BSP/Components/lan8742/lan8742.c ****     }
 177:Drivers/BSP/Components/lan8742/lan8742.c ****   }
 178:Drivers/BSP/Components/lan8742/lan8742.c ****   else
 179:Drivers/BSP/Components/lan8742/lan8742.c ****   {
 180:Drivers/BSP/Components/lan8742/lan8742.c ****     status = LAN8742_STATUS_READ_ERROR;
 181:Drivers/BSP/Components/lan8742/lan8742.c ****   }
 182:Drivers/BSP/Components/lan8742/lan8742.c **** 
 183:Drivers/BSP/Components/lan8742/lan8742.c ****   return status;
 184:Drivers/BSP/Components/lan8742/lan8742.c **** }
 381              		.loc 1 184 1 view .LVU113
 382 002c 02B0     		add	sp, sp, #8
 383              	.LCFI9:
 384              		.cfi_remember_state
 385              		.cfi_def_cfa_offset 8
 386              		@ sp needed
 387 002e 10BD     		pop	{r4, pc}
 388              	.LVL34:
 389              	.L29:
 390              	.LCFI10:
 391              		.cfi_restore_state
 180:Drivers/BSP/Components/lan8742/lan8742.c ****   }
 392              		.loc 1 180 12 view .LVU114
 393 0030 6FF00400 		mvn	r0, #4
 394 0034 FAE7     		b	.L27
 395              	.L30:
 175:Drivers/BSP/Components/lan8742/lan8742.c ****     }
 396              		.loc 1 175 14 view .LVU115
 397 0036 6FF00300 		mvn	r0, #3
 398              	.LVL35:
 183:Drivers/BSP/Components/lan8742/lan8742.c **** }
 399              		.loc 1 183 3 is_stmt 1 view .LVU116
 183:Drivers/BSP/Components/lan8742/lan8742.c **** }
 400              		.loc 1 183 10 is_stmt 0 view .LVU117
 401 003a F7E7     		b	.L27
 402              		.cfi_endproc
 403              	.LFE3:
 405              		.section	.text.LAN8742_EnablePowerDownMode,"ax",%progbits
 406              		.align	1
 407              		.global	LAN8742_EnablePowerDownMode
 408              		.syntax unified
 409              		.thumb
 410              		.thumb_func
 412              	LAN8742_EnablePowerDownMode:
 413              	.LVL36:
 414              	.LFB4:
 185:Drivers/BSP/Components/lan8742/lan8742.c **** 
 186:Drivers/BSP/Components/lan8742/lan8742.c **** /**
 187:Drivers/BSP/Components/lan8742/lan8742.c ****   * @brief  Enable the LAN8742 power down mode.
 188:Drivers/BSP/Components/lan8742/lan8742.c ****   * @param  pObj: device object LAN8742_Object_t.
 189:Drivers/BSP/Components/lan8742/lan8742.c ****   * @retval LAN8742_STATUS_OK  if OK
 190:Drivers/BSP/Components/lan8742/lan8742.c ****   *         LAN8742_STATUS_READ_ERROR if cannot read register
 191:Drivers/BSP/Components/lan8742/lan8742.c ****   *         LAN8742_STATUS_WRITE_ERROR if cannot write to register
 192:Drivers/BSP/Components/lan8742/lan8742.c ****   */
 193:Drivers/BSP/Components/lan8742/lan8742.c **** int32_t LAN8742_EnablePowerDownMode(lan8742_Object_t *pObj)
 194:Drivers/BSP/Components/lan8742/lan8742.c **** {
 415              		.loc 1 194 1 is_stmt 1 view -0
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5YGIqW.s 			page 12


 416              		.cfi_startproc
 417              		@ args = 0, pretend = 0, frame = 8
 418              		@ frame_needed = 0, uses_anonymous_args = 0
 419              		.loc 1 194 1 is_stmt 0 view .LVU119
 420 0000 10B5     		push	{r4, lr}
 421              	.LCFI11:
 422              		.cfi_def_cfa_offset 8
 423              		.cfi_offset 4, -8
 424              		.cfi_offset 14, -4
 425 0002 82B0     		sub	sp, sp, #8
 426              	.LCFI12:
 427              		.cfi_def_cfa_offset 16
 428 0004 0446     		mov	r4, r0
 195:Drivers/BSP/Components/lan8742/lan8742.c ****   uint32_t readval = 0;
 429              		.loc 1 195 3 is_stmt 1 view .LVU120
 430              		.loc 1 195 12 is_stmt 0 view .LVU121
 431 0006 0021     		movs	r1, #0
 432 0008 0191     		str	r1, [sp, #4]
 196:Drivers/BSP/Components/lan8742/lan8742.c ****   int32_t status = LAN8742_STATUS_OK;
 433              		.loc 1 196 3 is_stmt 1 view .LVU122
 434              	.LVL37:
 197:Drivers/BSP/Components/lan8742/lan8742.c **** 
 198:Drivers/BSP/Components/lan8742/lan8742.c ****   if(pObj->IO.ReadReg(pObj->DevAddr, LAN8742_BCR, &readval) >= 0)
 435              		.loc 1 198 3 view .LVU123
 436              		.loc 1 198 14 is_stmt 0 view .LVU124
 437 000a 4369     		ldr	r3, [r0, #20]
 438              		.loc 1 198 6 view .LVU125
 439 000c 01AA     		add	r2, sp, #4
 440 000e 0068     		ldr	r0, [r0]
 441              	.LVL38:
 442              		.loc 1 198 6 view .LVU126
 443 0010 9847     		blx	r3
 444              	.LVL39:
 445              		.loc 1 198 5 discriminator 1 view .LVU127
 446 0012 0028     		cmp	r0, #0
 447 0014 0CDB     		blt	.L34
 199:Drivers/BSP/Components/lan8742/lan8742.c ****   {
 200:Drivers/BSP/Components/lan8742/lan8742.c ****     readval |= LAN8742_BCR_POWER_DOWN;
 448              		.loc 1 200 5 is_stmt 1 view .LVU128
 449              		.loc 1 200 13 is_stmt 0 view .LVU129
 450 0016 019A     		ldr	r2, [sp, #4]
 451 0018 42F40062 		orr	r2, r2, #2048
 452 001c 0192     		str	r2, [sp, #4]
 201:Drivers/BSP/Components/lan8742/lan8742.c **** 
 202:Drivers/BSP/Components/lan8742/lan8742.c ****     /* Apply configuration */
 203:Drivers/BSP/Components/lan8742/lan8742.c ****     if(pObj->IO.WriteReg(pObj->DevAddr, LAN8742_BCR, readval) < 0)
 453              		.loc 1 203 5 is_stmt 1 view .LVU130
 454              		.loc 1 203 16 is_stmt 0 view .LVU131
 455 001e 2369     		ldr	r3, [r4, #16]
 456              		.loc 1 203 8 view .LVU132
 457 0020 0021     		movs	r1, #0
 458 0022 2068     		ldr	r0, [r4]
 459 0024 9847     		blx	r3
 460              	.LVL40:
 461              		.loc 1 203 7 discriminator 1 view .LVU133
 462 0026 0028     		cmp	r0, #0
 463 0028 05DB     		blt	.L35
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5YGIqW.s 			page 13


 196:Drivers/BSP/Components/lan8742/lan8742.c **** 
 464              		.loc 1 196 11 view .LVU134
 465 002a 0020     		movs	r0, #0
 466              	.LVL41:
 467              	.L32:
 204:Drivers/BSP/Components/lan8742/lan8742.c ****     {
 205:Drivers/BSP/Components/lan8742/lan8742.c ****       status =  LAN8742_STATUS_WRITE_ERROR;
 206:Drivers/BSP/Components/lan8742/lan8742.c ****     }
 207:Drivers/BSP/Components/lan8742/lan8742.c ****   }
 208:Drivers/BSP/Components/lan8742/lan8742.c ****   else
 209:Drivers/BSP/Components/lan8742/lan8742.c ****   {
 210:Drivers/BSP/Components/lan8742/lan8742.c ****     status = LAN8742_STATUS_READ_ERROR;
 211:Drivers/BSP/Components/lan8742/lan8742.c ****   }
 212:Drivers/BSP/Components/lan8742/lan8742.c **** 
 213:Drivers/BSP/Components/lan8742/lan8742.c ****   return status;
 214:Drivers/BSP/Components/lan8742/lan8742.c **** }
 468              		.loc 1 214 1 view .LVU135
 469 002c 02B0     		add	sp, sp, #8
 470              	.LCFI13:
 471              		.cfi_remember_state
 472              		.cfi_def_cfa_offset 8
 473              		@ sp needed
 474 002e 10BD     		pop	{r4, pc}
 475              	.LVL42:
 476              	.L34:
 477              	.LCFI14:
 478              		.cfi_restore_state
 210:Drivers/BSP/Components/lan8742/lan8742.c ****   }
 479              		.loc 1 210 12 view .LVU136
 480 0030 6FF00400 		mvn	r0, #4
 481 0034 FAE7     		b	.L32
 482              	.L35:
 205:Drivers/BSP/Components/lan8742/lan8742.c ****     }
 483              		.loc 1 205 14 view .LVU137
 484 0036 6FF00300 		mvn	r0, #3
 485              	.LVL43:
 213:Drivers/BSP/Components/lan8742/lan8742.c **** }
 486              		.loc 1 213 3 is_stmt 1 view .LVU138
 213:Drivers/BSP/Components/lan8742/lan8742.c **** }
 487              		.loc 1 213 10 is_stmt 0 view .LVU139
 488 003a F7E7     		b	.L32
 489              		.cfi_endproc
 490              	.LFE4:
 492              		.section	.text.LAN8742_StartAutoNego,"ax",%progbits
 493              		.align	1
 494              		.global	LAN8742_StartAutoNego
 495              		.syntax unified
 496              		.thumb
 497              		.thumb_func
 499              	LAN8742_StartAutoNego:
 500              	.LVL44:
 501              	.LFB5:
 215:Drivers/BSP/Components/lan8742/lan8742.c **** 
 216:Drivers/BSP/Components/lan8742/lan8742.c **** /**
 217:Drivers/BSP/Components/lan8742/lan8742.c ****   * @brief  Start the auto negotiation process.
 218:Drivers/BSP/Components/lan8742/lan8742.c ****   * @param  pObj: device object LAN8742_Object_t.
 219:Drivers/BSP/Components/lan8742/lan8742.c ****   * @retval LAN8742_STATUS_OK  if OK
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5YGIqW.s 			page 14


 220:Drivers/BSP/Components/lan8742/lan8742.c ****   *         LAN8742_STATUS_READ_ERROR if cannot read register
 221:Drivers/BSP/Components/lan8742/lan8742.c ****   *         LAN8742_STATUS_WRITE_ERROR if cannot write to register
 222:Drivers/BSP/Components/lan8742/lan8742.c ****   */
 223:Drivers/BSP/Components/lan8742/lan8742.c **** int32_t LAN8742_StartAutoNego(lan8742_Object_t *pObj)
 224:Drivers/BSP/Components/lan8742/lan8742.c **** {
 502              		.loc 1 224 1 is_stmt 1 view -0
 503              		.cfi_startproc
 504              		@ args = 0, pretend = 0, frame = 8
 505              		@ frame_needed = 0, uses_anonymous_args = 0
 506              		.loc 1 224 1 is_stmt 0 view .LVU141
 507 0000 10B5     		push	{r4, lr}
 508              	.LCFI15:
 509              		.cfi_def_cfa_offset 8
 510              		.cfi_offset 4, -8
 511              		.cfi_offset 14, -4
 512 0002 82B0     		sub	sp, sp, #8
 513              	.LCFI16:
 514              		.cfi_def_cfa_offset 16
 515 0004 0446     		mov	r4, r0
 225:Drivers/BSP/Components/lan8742/lan8742.c ****   uint32_t readval = 0;
 516              		.loc 1 225 3 is_stmt 1 view .LVU142
 517              		.loc 1 225 12 is_stmt 0 view .LVU143
 518 0006 0021     		movs	r1, #0
 519 0008 0191     		str	r1, [sp, #4]
 226:Drivers/BSP/Components/lan8742/lan8742.c ****   int32_t status = LAN8742_STATUS_OK;
 520              		.loc 1 226 3 is_stmt 1 view .LVU144
 521              	.LVL45:
 227:Drivers/BSP/Components/lan8742/lan8742.c **** 
 228:Drivers/BSP/Components/lan8742/lan8742.c ****   if(pObj->IO.ReadReg(pObj->DevAddr, LAN8742_BCR, &readval) >= 0)
 522              		.loc 1 228 3 view .LVU145
 523              		.loc 1 228 14 is_stmt 0 view .LVU146
 524 000a 4369     		ldr	r3, [r0, #20]
 525              		.loc 1 228 6 view .LVU147
 526 000c 01AA     		add	r2, sp, #4
 527 000e 0068     		ldr	r0, [r0]
 528              	.LVL46:
 529              		.loc 1 228 6 view .LVU148
 530 0010 9847     		blx	r3
 531              	.LVL47:
 532              		.loc 1 228 5 discriminator 1 view .LVU149
 533 0012 0028     		cmp	r0, #0
 534 0014 0CDB     		blt	.L39
 229:Drivers/BSP/Components/lan8742/lan8742.c ****   {
 230:Drivers/BSP/Components/lan8742/lan8742.c ****     readval |= LAN8742_BCR_AUTONEGO_EN;
 535              		.loc 1 230 5 is_stmt 1 view .LVU150
 536              		.loc 1 230 13 is_stmt 0 view .LVU151
 537 0016 019A     		ldr	r2, [sp, #4]
 538 0018 42F48052 		orr	r2, r2, #4096
 539 001c 0192     		str	r2, [sp, #4]
 231:Drivers/BSP/Components/lan8742/lan8742.c **** 
 232:Drivers/BSP/Components/lan8742/lan8742.c ****     /* Apply configuration */
 233:Drivers/BSP/Components/lan8742/lan8742.c ****     if(pObj->IO.WriteReg(pObj->DevAddr, LAN8742_BCR, readval) < 0)
 540              		.loc 1 233 5 is_stmt 1 view .LVU152
 541              		.loc 1 233 16 is_stmt 0 view .LVU153
 542 001e 2369     		ldr	r3, [r4, #16]
 543              		.loc 1 233 8 view .LVU154
 544 0020 0021     		movs	r1, #0
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5YGIqW.s 			page 15


 545 0022 2068     		ldr	r0, [r4]
 546 0024 9847     		blx	r3
 547              	.LVL48:
 548              		.loc 1 233 7 discriminator 1 view .LVU155
 549 0026 0028     		cmp	r0, #0
 550 0028 05DB     		blt	.L40
 226:Drivers/BSP/Components/lan8742/lan8742.c **** 
 551              		.loc 1 226 11 view .LVU156
 552 002a 0020     		movs	r0, #0
 553              	.LVL49:
 554              	.L37:
 234:Drivers/BSP/Components/lan8742/lan8742.c ****     {
 235:Drivers/BSP/Components/lan8742/lan8742.c ****       status =  LAN8742_STATUS_WRITE_ERROR;
 236:Drivers/BSP/Components/lan8742/lan8742.c ****     }
 237:Drivers/BSP/Components/lan8742/lan8742.c ****   }
 238:Drivers/BSP/Components/lan8742/lan8742.c ****   else
 239:Drivers/BSP/Components/lan8742/lan8742.c ****   {
 240:Drivers/BSP/Components/lan8742/lan8742.c ****     status = LAN8742_STATUS_READ_ERROR;
 241:Drivers/BSP/Components/lan8742/lan8742.c ****   }
 242:Drivers/BSP/Components/lan8742/lan8742.c **** 
 243:Drivers/BSP/Components/lan8742/lan8742.c ****   return status;
 244:Drivers/BSP/Components/lan8742/lan8742.c **** }
 555              		.loc 1 244 1 view .LVU157
 556 002c 02B0     		add	sp, sp, #8
 557              	.LCFI17:
 558              		.cfi_remember_state
 559              		.cfi_def_cfa_offset 8
 560              		@ sp needed
 561 002e 10BD     		pop	{r4, pc}
 562              	.LVL50:
 563              	.L39:
 564              	.LCFI18:
 565              		.cfi_restore_state
 240:Drivers/BSP/Components/lan8742/lan8742.c ****   }
 566              		.loc 1 240 12 view .LVU158
 567 0030 6FF00400 		mvn	r0, #4
 568 0034 FAE7     		b	.L37
 569              	.L40:
 235:Drivers/BSP/Components/lan8742/lan8742.c ****     }
 570              		.loc 1 235 14 view .LVU159
 571 0036 6FF00300 		mvn	r0, #3
 572              	.LVL51:
 243:Drivers/BSP/Components/lan8742/lan8742.c **** }
 573              		.loc 1 243 3 is_stmt 1 view .LVU160
 243:Drivers/BSP/Components/lan8742/lan8742.c **** }
 574              		.loc 1 243 10 is_stmt 0 view .LVU161
 575 003a F7E7     		b	.L37
 576              		.cfi_endproc
 577              	.LFE5:
 579              		.section	.text.LAN8742_GetLinkState,"ax",%progbits
 580              		.align	1
 581              		.global	LAN8742_GetLinkState
 582              		.syntax unified
 583              		.thumb
 584              		.thumb_func
 586              	LAN8742_GetLinkState:
 587              	.LVL52:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5YGIqW.s 			page 16


 588              	.LFB6:
 245:Drivers/BSP/Components/lan8742/lan8742.c **** 
 246:Drivers/BSP/Components/lan8742/lan8742.c **** /**
 247:Drivers/BSP/Components/lan8742/lan8742.c ****   * @brief  Get the link state of LAN8742 device.
 248:Drivers/BSP/Components/lan8742/lan8742.c ****   * @param  pObj: Pointer to device object.
 249:Drivers/BSP/Components/lan8742/lan8742.c ****   * @param  pLinkState: Pointer to link state
 250:Drivers/BSP/Components/lan8742/lan8742.c ****   * @retval LAN8742_STATUS_LINK_DOWN  if link is down
 251:Drivers/BSP/Components/lan8742/lan8742.c ****   *         LAN8742_STATUS_AUTONEGO_NOTDONE if Auto nego not completed
 252:Drivers/BSP/Components/lan8742/lan8742.c ****   *         LAN8742_STATUS_100MBITS_FULLDUPLEX if 100Mb/s FD
 253:Drivers/BSP/Components/lan8742/lan8742.c ****   *         LAN8742_STATUS_100MBITS_HALFDUPLEX if 100Mb/s HD
 254:Drivers/BSP/Components/lan8742/lan8742.c ****   *         LAN8742_STATUS_10MBITS_FULLDUPLEX  if 10Mb/s FD
 255:Drivers/BSP/Components/lan8742/lan8742.c ****   *         LAN8742_STATUS_10MBITS_HALFDUPLEX  if 10Mb/s HD
 256:Drivers/BSP/Components/lan8742/lan8742.c ****   *         LAN8742_STATUS_READ_ERROR if cannot read register
 257:Drivers/BSP/Components/lan8742/lan8742.c ****   *         LAN8742_STATUS_WRITE_ERROR if cannot write to register
 258:Drivers/BSP/Components/lan8742/lan8742.c ****   */
 259:Drivers/BSP/Components/lan8742/lan8742.c **** int32_t LAN8742_GetLinkState(lan8742_Object_t *pObj)
 260:Drivers/BSP/Components/lan8742/lan8742.c **** {
 589              		.loc 1 260 1 is_stmt 1 view -0
 590              		.cfi_startproc
 591              		@ args = 0, pretend = 0, frame = 8
 592              		@ frame_needed = 0, uses_anonymous_args = 0
 593              		.loc 1 260 1 is_stmt 0 view .LVU163
 594 0000 10B5     		push	{r4, lr}
 595              	.LCFI19:
 596              		.cfi_def_cfa_offset 8
 597              		.cfi_offset 4, -8
 598              		.cfi_offset 14, -4
 599 0002 82B0     		sub	sp, sp, #8
 600              	.LCFI20:
 601              		.cfi_def_cfa_offset 16
 602 0004 0446     		mov	r4, r0
 261:Drivers/BSP/Components/lan8742/lan8742.c ****   uint32_t readval = 0;
 603              		.loc 1 261 3 is_stmt 1 view .LVU164
 604              		.loc 1 261 12 is_stmt 0 view .LVU165
 605 0006 0023     		movs	r3, #0
 606 0008 0193     		str	r3, [sp, #4]
 262:Drivers/BSP/Components/lan8742/lan8742.c **** 
 263:Drivers/BSP/Components/lan8742/lan8742.c ****   /* Read Status register  */
 264:Drivers/BSP/Components/lan8742/lan8742.c ****   if(pObj->IO.ReadReg(pObj->DevAddr, LAN8742_BSR, &readval) < 0)
 607              		.loc 1 264 3 is_stmt 1 view .LVU166
 608              		.loc 1 264 14 is_stmt 0 view .LVU167
 609 000a 4369     		ldr	r3, [r0, #20]
 610              		.loc 1 264 6 view .LVU168
 611 000c 01AA     		add	r2, sp, #4
 612 000e 0121     		movs	r1, #1
 613 0010 0068     		ldr	r0, [r0]
 614              	.LVL53:
 615              		.loc 1 264 6 view .LVU169
 616 0012 9847     		blx	r3
 617              	.LVL54:
 618              		.loc 1 264 5 discriminator 1 view .LVU170
 619 0014 0028     		cmp	r0, #0
 620 0016 39DB     		blt	.L45
 265:Drivers/BSP/Components/lan8742/lan8742.c ****   {
 266:Drivers/BSP/Components/lan8742/lan8742.c ****     return LAN8742_STATUS_READ_ERROR;
 267:Drivers/BSP/Components/lan8742/lan8742.c ****   }
 268:Drivers/BSP/Components/lan8742/lan8742.c **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5YGIqW.s 			page 17


 269:Drivers/BSP/Components/lan8742/lan8742.c ****   /* Read Status register again */
 270:Drivers/BSP/Components/lan8742/lan8742.c ****   if(pObj->IO.ReadReg(pObj->DevAddr, LAN8742_BSR, &readval) < 0)
 621              		.loc 1 270 3 is_stmt 1 view .LVU171
 622              		.loc 1 270 14 is_stmt 0 view .LVU172
 623 0018 6369     		ldr	r3, [r4, #20]
 624              		.loc 1 270 6 view .LVU173
 625 001a 01AA     		add	r2, sp, #4
 626 001c 0121     		movs	r1, #1
 627 001e 2068     		ldr	r0, [r4]
 628 0020 9847     		blx	r3
 629              	.LVL55:
 630              		.loc 1 270 5 discriminator 1 view .LVU174
 631 0022 0028     		cmp	r0, #0
 632 0024 35DB     		blt	.L46
 271:Drivers/BSP/Components/lan8742/lan8742.c ****   {
 272:Drivers/BSP/Components/lan8742/lan8742.c ****     return LAN8742_STATUS_READ_ERROR;
 273:Drivers/BSP/Components/lan8742/lan8742.c ****   }
 274:Drivers/BSP/Components/lan8742/lan8742.c **** 
 275:Drivers/BSP/Components/lan8742/lan8742.c ****   if((readval & LAN8742_BSR_LINK_STATUS) == 0)
 633              		.loc 1 275 3 is_stmt 1 view .LVU175
 634              		.loc 1 275 5 is_stmt 0 view .LVU176
 635 0026 019B     		ldr	r3, [sp, #4]
 636 0028 13F0040F 		tst	r3, #4
 637 002c 34D0     		beq	.L47
 276:Drivers/BSP/Components/lan8742/lan8742.c ****   {
 277:Drivers/BSP/Components/lan8742/lan8742.c ****     /* Return Link Down status */
 278:Drivers/BSP/Components/lan8742/lan8742.c ****     return LAN8742_STATUS_LINK_DOWN;
 279:Drivers/BSP/Components/lan8742/lan8742.c ****   }
 280:Drivers/BSP/Components/lan8742/lan8742.c **** 
 281:Drivers/BSP/Components/lan8742/lan8742.c ****   /* Check Auto negotiation */
 282:Drivers/BSP/Components/lan8742/lan8742.c ****   if(pObj->IO.ReadReg(pObj->DevAddr, LAN8742_BCR, &readval) < 0)
 638              		.loc 1 282 3 is_stmt 1 view .LVU177
 639              		.loc 1 282 14 is_stmt 0 view .LVU178
 640 002e 6369     		ldr	r3, [r4, #20]
 641              		.loc 1 282 6 view .LVU179
 642 0030 01AA     		add	r2, sp, #4
 643 0032 0021     		movs	r1, #0
 644 0034 2068     		ldr	r0, [r4]
 645 0036 9847     		blx	r3
 646              	.LVL56:
 647              		.loc 1 282 5 discriminator 1 view .LVU180
 648 0038 0028     		cmp	r0, #0
 649 003a 30DB     		blt	.L48
 283:Drivers/BSP/Components/lan8742/lan8742.c ****   {
 284:Drivers/BSP/Components/lan8742/lan8742.c ****     return LAN8742_STATUS_READ_ERROR;
 285:Drivers/BSP/Components/lan8742/lan8742.c ****   }
 286:Drivers/BSP/Components/lan8742/lan8742.c **** 
 287:Drivers/BSP/Components/lan8742/lan8742.c ****   if((readval & LAN8742_BCR_AUTONEGO_EN) != LAN8742_BCR_AUTONEGO_EN)
 650              		.loc 1 287 3 is_stmt 1 view .LVU181
 651              		.loc 1 287 15 is_stmt 0 view .LVU182
 652 003c 019B     		ldr	r3, [sp, #4]
 653              		.loc 1 287 5 view .LVU183
 654 003e 13F4805F 		tst	r3, #4096
 655 0042 0CD1     		bne	.L44
 288:Drivers/BSP/Components/lan8742/lan8742.c ****   {
 289:Drivers/BSP/Components/lan8742/lan8742.c ****     if(((readval & LAN8742_BCR_SPEED_SELECT) == LAN8742_BCR_SPEED_SELECT) && ((readval & LAN8742_BC
 656              		.loc 1 289 5 is_stmt 1 view .LVU184
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5YGIqW.s 			page 18


 657              		.loc 1 289 75 is_stmt 0 view .LVU185
 658 0044 03F40452 		and	r2, r3, #8448
 659              		.loc 1 289 7 view .LVU186
 660 0048 B2F5045F 		cmp	r2, #8448
 661 004c 2AD0     		beq	.L49
 290:Drivers/BSP/Components/lan8742/lan8742.c ****     {
 291:Drivers/BSP/Components/lan8742/lan8742.c ****       return LAN8742_STATUS_100MBITS_FULLDUPLEX;
 292:Drivers/BSP/Components/lan8742/lan8742.c ****     }
 293:Drivers/BSP/Components/lan8742/lan8742.c ****     else if ((readval & LAN8742_BCR_SPEED_SELECT) == LAN8742_BCR_SPEED_SELECT)
 662              		.loc 1 293 10 is_stmt 1 view .LVU187
 663              		.loc 1 293 13 is_stmt 0 view .LVU188
 664 004e 13F4005F 		tst	r3, #8192
 665 0052 29D1     		bne	.L50
 294:Drivers/BSP/Components/lan8742/lan8742.c ****     {
 295:Drivers/BSP/Components/lan8742/lan8742.c ****       return LAN8742_STATUS_100MBITS_HALFDUPLEX;
 296:Drivers/BSP/Components/lan8742/lan8742.c ****     }
 297:Drivers/BSP/Components/lan8742/lan8742.c ****     else if ((readval & LAN8742_BCR_DUPLEX_MODE) == LAN8742_BCR_DUPLEX_MODE)
 666              		.loc 1 297 10 is_stmt 1 view .LVU189
 667              		.loc 1 297 13 is_stmt 0 view .LVU190
 668 0054 13F4807F 		tst	r3, #256
 669 0058 28D0     		beq	.L51
 298:Drivers/BSP/Components/lan8742/lan8742.c ****     {
 299:Drivers/BSP/Components/lan8742/lan8742.c ****       return LAN8742_STATUS_10MBITS_FULLDUPLEX;
 670              		.loc 1 299 14 view .LVU191
 671 005a 0420     		movs	r0, #4
 672 005c 1DE0     		b	.L42
 673              	.L44:
 300:Drivers/BSP/Components/lan8742/lan8742.c ****     }
 301:Drivers/BSP/Components/lan8742/lan8742.c ****     else
 302:Drivers/BSP/Components/lan8742/lan8742.c ****     {
 303:Drivers/BSP/Components/lan8742/lan8742.c ****       return LAN8742_STATUS_10MBITS_HALFDUPLEX;
 304:Drivers/BSP/Components/lan8742/lan8742.c ****     }
 305:Drivers/BSP/Components/lan8742/lan8742.c ****   }
 306:Drivers/BSP/Components/lan8742/lan8742.c ****   else /* Auto Nego enabled */
 307:Drivers/BSP/Components/lan8742/lan8742.c ****   {
 308:Drivers/BSP/Components/lan8742/lan8742.c ****     if(pObj->IO.ReadReg(pObj->DevAddr, LAN8742_PHYSCSR, &readval) < 0)
 674              		.loc 1 308 5 is_stmt 1 view .LVU192
 675              		.loc 1 308 16 is_stmt 0 view .LVU193
 676 005e 6369     		ldr	r3, [r4, #20]
 677              		.loc 1 308 8 view .LVU194
 678 0060 01AA     		add	r2, sp, #4
 679 0062 1F21     		movs	r1, #31
 680 0064 2068     		ldr	r0, [r4]
 681 0066 9847     		blx	r3
 682              	.LVL57:
 683              		.loc 1 308 7 discriminator 1 view .LVU195
 684 0068 0028     		cmp	r0, #0
 685 006a 21DB     		blt	.L52
 309:Drivers/BSP/Components/lan8742/lan8742.c ****     {
 310:Drivers/BSP/Components/lan8742/lan8742.c ****       return LAN8742_STATUS_READ_ERROR;
 311:Drivers/BSP/Components/lan8742/lan8742.c ****     }
 312:Drivers/BSP/Components/lan8742/lan8742.c **** 
 313:Drivers/BSP/Components/lan8742/lan8742.c ****     /* Check if auto nego not done */
 314:Drivers/BSP/Components/lan8742/lan8742.c ****     if((readval & LAN8742_PHYSCSR_AUTONEGO_DONE) == 0)
 686              		.loc 1 314 5 is_stmt 1 view .LVU196
 687              		.loc 1 314 17 is_stmt 0 view .LVU197
 688 006c 019B     		ldr	r3, [sp, #4]
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5YGIqW.s 			page 19


 689              		.loc 1 314 7 view .LVU198
 690 006e 13F4805F 		tst	r3, #4096
 691 0072 20D0     		beq	.L53
 315:Drivers/BSP/Components/lan8742/lan8742.c ****     {
 316:Drivers/BSP/Components/lan8742/lan8742.c ****       return LAN8742_STATUS_AUTONEGO_NOTDONE;
 317:Drivers/BSP/Components/lan8742/lan8742.c ****     }
 318:Drivers/BSP/Components/lan8742/lan8742.c **** 
 319:Drivers/BSP/Components/lan8742/lan8742.c ****     if((readval & LAN8742_PHYSCSR_HCDSPEEDMASK) == LAN8742_PHYSCSR_100BTX_FD)
 692              		.loc 1 319 5 is_stmt 1 view .LVU199
 693              		.loc 1 319 17 is_stmt 0 view .LVU200
 694 0074 03F01C03 		and	r3, r3, #28
 695              		.loc 1 319 7 view .LVU201
 696 0078 182B     		cmp	r3, #24
 697 007a 1ED0     		beq	.L54
 320:Drivers/BSP/Components/lan8742/lan8742.c ****     {
 321:Drivers/BSP/Components/lan8742/lan8742.c ****       return LAN8742_STATUS_100MBITS_FULLDUPLEX;
 322:Drivers/BSP/Components/lan8742/lan8742.c ****     }
 323:Drivers/BSP/Components/lan8742/lan8742.c ****     else if ((readval & LAN8742_PHYSCSR_HCDSPEEDMASK) == LAN8742_PHYSCSR_100BTX_HD)
 698              		.loc 1 323 10 is_stmt 1 view .LVU202
 699              		.loc 1 323 13 is_stmt 0 view .LVU203
 700 007c 082B     		cmp	r3, #8
 701 007e 1ED0     		beq	.L55
 324:Drivers/BSP/Components/lan8742/lan8742.c ****     {
 325:Drivers/BSP/Components/lan8742/lan8742.c ****       return LAN8742_STATUS_100MBITS_HALFDUPLEX;
 326:Drivers/BSP/Components/lan8742/lan8742.c ****     }
 327:Drivers/BSP/Components/lan8742/lan8742.c ****     else if ((readval & LAN8742_PHYSCSR_HCDSPEEDMASK) == LAN8742_PHYSCSR_10BT_FD)
 702              		.loc 1 327 10 is_stmt 1 view .LVU204
 703              		.loc 1 327 13 is_stmt 0 view .LVU205
 704 0080 142B     		cmp	r3, #20
 705 0082 01D0     		beq	.L58
 328:Drivers/BSP/Components/lan8742/lan8742.c ****     {
 329:Drivers/BSP/Components/lan8742/lan8742.c ****       return LAN8742_STATUS_10MBITS_FULLDUPLEX;
 330:Drivers/BSP/Components/lan8742/lan8742.c ****     }
 331:Drivers/BSP/Components/lan8742/lan8742.c ****     else
 332:Drivers/BSP/Components/lan8742/lan8742.c ****     {
 333:Drivers/BSP/Components/lan8742/lan8742.c ****       return LAN8742_STATUS_10MBITS_HALFDUPLEX;
 706              		.loc 1 333 14 view .LVU206
 707 0084 0520     		movs	r0, #5
 708 0086 08E0     		b	.L42
 709              	.L58:
 329:Drivers/BSP/Components/lan8742/lan8742.c ****     }
 710              		.loc 1 329 14 view .LVU207
 711 0088 0420     		movs	r0, #4
 712 008a 06E0     		b	.L42
 713              	.L45:
 266:Drivers/BSP/Components/lan8742/lan8742.c ****   }
 714              		.loc 1 266 12 view .LVU208
 715 008c 6FF00400 		mvn	r0, #4
 716 0090 03E0     		b	.L42
 717              	.L46:
 272:Drivers/BSP/Components/lan8742/lan8742.c ****   }
 718              		.loc 1 272 12 view .LVU209
 719 0092 6FF00400 		mvn	r0, #4
 720 0096 00E0     		b	.L42
 721              	.L47:
 278:Drivers/BSP/Components/lan8742/lan8742.c ****   }
 722              		.loc 1 278 12 view .LVU210
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5YGIqW.s 			page 20


 723 0098 0120     		movs	r0, #1
 724              	.L42:
 334:Drivers/BSP/Components/lan8742/lan8742.c ****     }
 335:Drivers/BSP/Components/lan8742/lan8742.c ****   }
 336:Drivers/BSP/Components/lan8742/lan8742.c **** }
 725              		.loc 1 336 1 view .LVU211
 726 009a 02B0     		add	sp, sp, #8
 727              	.LCFI21:
 728              		.cfi_remember_state
 729              		.cfi_def_cfa_offset 8
 730              		@ sp needed
 731 009c 10BD     		pop	{r4, pc}
 732              	.LVL58:
 733              	.L48:
 734              	.LCFI22:
 735              		.cfi_restore_state
 284:Drivers/BSP/Components/lan8742/lan8742.c ****   }
 736              		.loc 1 284 12 view .LVU212
 737 009e 6FF00400 		mvn	r0, #4
 738 00a2 FAE7     		b	.L42
 739              	.L49:
 291:Drivers/BSP/Components/lan8742/lan8742.c ****     }
 740              		.loc 1 291 14 view .LVU213
 741 00a4 0220     		movs	r0, #2
 742 00a6 F8E7     		b	.L42
 743              	.L50:
 295:Drivers/BSP/Components/lan8742/lan8742.c ****     }
 744              		.loc 1 295 14 view .LVU214
 745 00a8 0320     		movs	r0, #3
 746 00aa F6E7     		b	.L42
 747              	.L51:
 303:Drivers/BSP/Components/lan8742/lan8742.c ****     }
 748              		.loc 1 303 14 view .LVU215
 749 00ac 0520     		movs	r0, #5
 750 00ae F4E7     		b	.L42
 751              	.L52:
 310:Drivers/BSP/Components/lan8742/lan8742.c ****     }
 752              		.loc 1 310 14 view .LVU216
 753 00b0 6FF00400 		mvn	r0, #4
 754 00b4 F1E7     		b	.L42
 755              	.L53:
 316:Drivers/BSP/Components/lan8742/lan8742.c ****     }
 756              		.loc 1 316 14 view .LVU217
 757 00b6 0620     		movs	r0, #6
 758 00b8 EFE7     		b	.L42
 759              	.L54:
 321:Drivers/BSP/Components/lan8742/lan8742.c ****     }
 760              		.loc 1 321 14 view .LVU218
 761 00ba 0220     		movs	r0, #2
 762 00bc EDE7     		b	.L42
 763              	.L55:
 325:Drivers/BSP/Components/lan8742/lan8742.c ****     }
 764              		.loc 1 325 14 view .LVU219
 765 00be 0320     		movs	r0, #3
 766 00c0 EBE7     		b	.L42
 767              		.cfi_endproc
 768              	.LFE6:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5YGIqW.s 			page 21


 770              		.section	.text.LAN8742_SetLinkState,"ax",%progbits
 771              		.align	1
 772              		.global	LAN8742_SetLinkState
 773              		.syntax unified
 774              		.thumb
 775              		.thumb_func
 777              	LAN8742_SetLinkState:
 778              	.LVL59:
 779              	.LFB7:
 337:Drivers/BSP/Components/lan8742/lan8742.c **** 
 338:Drivers/BSP/Components/lan8742/lan8742.c **** /**
 339:Drivers/BSP/Components/lan8742/lan8742.c ****   * @brief  Set the link state of LAN8742 device.
 340:Drivers/BSP/Components/lan8742/lan8742.c ****   * @param  pObj: Pointer to device object.
 341:Drivers/BSP/Components/lan8742/lan8742.c ****   * @param  pLinkState: link state can be one of the following
 342:Drivers/BSP/Components/lan8742/lan8742.c ****   *         LAN8742_STATUS_100MBITS_FULLDUPLEX if 100Mb/s FD
 343:Drivers/BSP/Components/lan8742/lan8742.c ****   *         LAN8742_STATUS_100MBITS_HALFDUPLEX if 100Mb/s HD
 344:Drivers/BSP/Components/lan8742/lan8742.c ****   *         LAN8742_STATUS_10MBITS_FULLDUPLEX  if 10Mb/s FD
 345:Drivers/BSP/Components/lan8742/lan8742.c ****   *         LAN8742_STATUS_10MBITS_HALFDUPLEX  if 10Mb/s HD
 346:Drivers/BSP/Components/lan8742/lan8742.c ****   * @retval LAN8742_STATUS_OK  if OK
 347:Drivers/BSP/Components/lan8742/lan8742.c ****   *         LAN8742_STATUS_ERROR  if parameter error
 348:Drivers/BSP/Components/lan8742/lan8742.c ****   *         LAN8742_STATUS_READ_ERROR if cannot read register
 349:Drivers/BSP/Components/lan8742/lan8742.c ****   *         LAN8742_STATUS_WRITE_ERROR if cannot write to register
 350:Drivers/BSP/Components/lan8742/lan8742.c ****   */
 351:Drivers/BSP/Components/lan8742/lan8742.c **** int32_t LAN8742_SetLinkState(lan8742_Object_t *pObj, uint32_t LinkState)
 352:Drivers/BSP/Components/lan8742/lan8742.c **** {
 780              		.loc 1 352 1 is_stmt 1 view -0
 781              		.cfi_startproc
 782              		@ args = 0, pretend = 0, frame = 8
 783              		@ frame_needed = 0, uses_anonymous_args = 0
 784              		.loc 1 352 1 is_stmt 0 view .LVU221
 785 0000 30B5     		push	{r4, r5, lr}
 786              	.LCFI23:
 787              		.cfi_def_cfa_offset 12
 788              		.cfi_offset 4, -12
 789              		.cfi_offset 5, -8
 790              		.cfi_offset 14, -4
 791 0002 83B0     		sub	sp, sp, #12
 792              	.LCFI24:
 793              		.cfi_def_cfa_offset 24
 794 0004 0446     		mov	r4, r0
 795 0006 0D46     		mov	r5, r1
 353:Drivers/BSP/Components/lan8742/lan8742.c ****   uint32_t bcrvalue = 0;
 796              		.loc 1 353 3 is_stmt 1 view .LVU222
 797              		.loc 1 353 12 is_stmt 0 view .LVU223
 798 0008 0021     		movs	r1, #0
 799              	.LVL60:
 800              		.loc 1 353 12 view .LVU224
 801 000a 0191     		str	r1, [sp, #4]
 354:Drivers/BSP/Components/lan8742/lan8742.c ****   int32_t status = LAN8742_STATUS_OK;
 802              		.loc 1 354 3 is_stmt 1 view .LVU225
 803              	.LVL61:
 355:Drivers/BSP/Components/lan8742/lan8742.c **** 
 356:Drivers/BSP/Components/lan8742/lan8742.c ****   if(pObj->IO.ReadReg(pObj->DevAddr, LAN8742_BCR, &bcrvalue) >= 0)
 804              		.loc 1 356 3 view .LVU226
 805              		.loc 1 356 14 is_stmt 0 view .LVU227
 806 000c 4369     		ldr	r3, [r0, #20]
 807              		.loc 1 356 6 view .LVU228
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5YGIqW.s 			page 22


 808 000e 01AA     		add	r2, sp, #4
 809 0010 0068     		ldr	r0, [r0]
 810              	.LVL62:
 811              		.loc 1 356 6 view .LVU229
 812 0012 9847     		blx	r3
 813              	.LVL63:
 814              		.loc 1 356 5 discriminator 1 view .LVU230
 815 0014 0028     		cmp	r0, #0
 816 0016 1EDB     		blt	.L64
 357:Drivers/BSP/Components/lan8742/lan8742.c ****   {
 358:Drivers/BSP/Components/lan8742/lan8742.c ****     /* Disable link config (Auto nego, speed and duplex) */
 359:Drivers/BSP/Components/lan8742/lan8742.c ****     bcrvalue &= ~(LAN8742_BCR_AUTONEGO_EN | LAN8742_BCR_SPEED_SELECT | LAN8742_BCR_DUPLEX_MODE);
 817              		.loc 1 359 5 is_stmt 1 view .LVU231
 818              		.loc 1 359 14 is_stmt 0 view .LVU232
 819 0018 019B     		ldr	r3, [sp, #4]
 820 001a 23F44453 		bic	r3, r3, #12544
 821 001e 0193     		str	r3, [sp, #4]
 360:Drivers/BSP/Components/lan8742/lan8742.c **** 
 361:Drivers/BSP/Components/lan8742/lan8742.c ****     if(LinkState == LAN8742_STATUS_100MBITS_FULLDUPLEX)
 822              		.loc 1 361 5 is_stmt 1 view .LVU233
 823              		.loc 1 361 7 is_stmt 0 view .LVU234
 824 0020 022D     		cmp	r5, #2
 825 0022 10D0     		beq	.L68
 362:Drivers/BSP/Components/lan8742/lan8742.c ****     {
 363:Drivers/BSP/Components/lan8742/lan8742.c ****       bcrvalue |= (LAN8742_BCR_SPEED_SELECT | LAN8742_BCR_DUPLEX_MODE);
 364:Drivers/BSP/Components/lan8742/lan8742.c ****     }
 365:Drivers/BSP/Components/lan8742/lan8742.c ****     else if (LinkState == LAN8742_STATUS_100MBITS_HALFDUPLEX)
 826              		.loc 1 365 10 is_stmt 1 view .LVU235
 827              		.loc 1 365 13 is_stmt 0 view .LVU236
 828 0024 032D     		cmp	r5, #3
 829 0026 12D0     		beq	.L69
 366:Drivers/BSP/Components/lan8742/lan8742.c ****     {
 367:Drivers/BSP/Components/lan8742/lan8742.c ****       bcrvalue |= LAN8742_BCR_SPEED_SELECT;
 368:Drivers/BSP/Components/lan8742/lan8742.c ****     }
 369:Drivers/BSP/Components/lan8742/lan8742.c ****     else if (LinkState == LAN8742_STATUS_10MBITS_FULLDUPLEX)
 830              		.loc 1 369 10 is_stmt 1 view .LVU237
 831              		.loc 1 369 13 is_stmt 0 view .LVU238
 832 0028 042D     		cmp	r5, #4
 833 002a 17D1     		bne	.L65
 370:Drivers/BSP/Components/lan8742/lan8742.c ****     {
 371:Drivers/BSP/Components/lan8742/lan8742.c ****       bcrvalue |= LAN8742_BCR_DUPLEX_MODE;
 834              		.loc 1 371 7 is_stmt 1 view .LVU239
 835              		.loc 1 371 16 is_stmt 0 view .LVU240
 836 002c 43F48073 		orr	r3, r3, #256
 837 0030 0193     		str	r3, [sp, #4]
 372:Drivers/BSP/Components/lan8742/lan8742.c ****     }
 373:Drivers/BSP/Components/lan8742/lan8742.c ****     else
 374:Drivers/BSP/Components/lan8742/lan8742.c ****     {
 375:Drivers/BSP/Components/lan8742/lan8742.c ****       /* Wrong link status parameter */
 376:Drivers/BSP/Components/lan8742/lan8742.c ****       status = LAN8742_STATUS_ERROR;
 377:Drivers/BSP/Components/lan8742/lan8742.c ****     }
 378:Drivers/BSP/Components/lan8742/lan8742.c ****   }
 379:Drivers/BSP/Components/lan8742/lan8742.c ****   else
 380:Drivers/BSP/Components/lan8742/lan8742.c ****   {
 381:Drivers/BSP/Components/lan8742/lan8742.c ****     status = LAN8742_STATUS_READ_ERROR;
 382:Drivers/BSP/Components/lan8742/lan8742.c ****   }
 383:Drivers/BSP/Components/lan8742/lan8742.c **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5YGIqW.s 			page 23


 384:Drivers/BSP/Components/lan8742/lan8742.c ****   if(status == LAN8742_STATUS_OK)
 838              		.loc 1 384 3 is_stmt 1 view .LVU241
 839              	.L62:
 385:Drivers/BSP/Components/lan8742/lan8742.c ****   {
 386:Drivers/BSP/Components/lan8742/lan8742.c ****     /* Apply configuration */
 387:Drivers/BSP/Components/lan8742/lan8742.c ****     if(pObj->IO.WriteReg(pObj->DevAddr, LAN8742_BCR, bcrvalue) < 0)
 840              		.loc 1 387 5 view .LVU242
 841              		.loc 1 387 16 is_stmt 0 view .LVU243
 842 0032 2369     		ldr	r3, [r4, #16]
 843              		.loc 1 387 8 view .LVU244
 844 0034 019A     		ldr	r2, [sp, #4]
 845 0036 0021     		movs	r1, #0
 846 0038 2068     		ldr	r0, [r4]
 847 003a 9847     		blx	r3
 848              	.LVL64:
 849              		.loc 1 387 7 discriminator 1 view .LVU245
 850 003c 0028     		cmp	r0, #0
 851 003e 10DB     		blt	.L66
 852 0040 0020     		movs	r0, #0
 853              	.LVL65:
 854              	.L59:
 388:Drivers/BSP/Components/lan8742/lan8742.c ****     {
 389:Drivers/BSP/Components/lan8742/lan8742.c ****       status = LAN8742_STATUS_WRITE_ERROR;
 390:Drivers/BSP/Components/lan8742/lan8742.c ****     }
 391:Drivers/BSP/Components/lan8742/lan8742.c ****   }
 392:Drivers/BSP/Components/lan8742/lan8742.c **** 
 393:Drivers/BSP/Components/lan8742/lan8742.c ****   return status;
 394:Drivers/BSP/Components/lan8742/lan8742.c **** }
 855              		.loc 1 394 1 view .LVU246
 856 0042 03B0     		add	sp, sp, #12
 857              	.LCFI25:
 858              		.cfi_remember_state
 859              		.cfi_def_cfa_offset 12
 860              		@ sp needed
 861 0044 30BD     		pop	{r4, r5, pc}
 862              	.LVL66:
 863              	.L68:
 864              	.LCFI26:
 865              		.cfi_restore_state
 363:Drivers/BSP/Components/lan8742/lan8742.c ****     }
 866              		.loc 1 363 7 is_stmt 1 view .LVU247
 363:Drivers/BSP/Components/lan8742/lan8742.c ****     }
 867              		.loc 1 363 16 is_stmt 0 view .LVU248
 868 0046 43F40453 		orr	r3, r3, #8448
 869 004a 0193     		str	r3, [sp, #4]
 384:Drivers/BSP/Components/lan8742/lan8742.c ****   {
 870              		.loc 1 384 3 is_stmt 1 view .LVU249
 871 004c F1E7     		b	.L62
 872              	.L69:
 367:Drivers/BSP/Components/lan8742/lan8742.c ****     }
 873              		.loc 1 367 7 view .LVU250
 367:Drivers/BSP/Components/lan8742/lan8742.c ****     }
 874              		.loc 1 367 16 is_stmt 0 view .LVU251
 875 004e 43F40053 		orr	r3, r3, #8192
 876 0052 0193     		str	r3, [sp, #4]
 384:Drivers/BSP/Components/lan8742/lan8742.c ****   {
 877              		.loc 1 384 3 is_stmt 1 view .LVU252
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5YGIqW.s 			page 24


 878 0054 EDE7     		b	.L62
 879              	.L64:
 381:Drivers/BSP/Components/lan8742/lan8742.c ****   }
 880              		.loc 1 381 12 is_stmt 0 view .LVU253
 881 0056 6FF00400 		mvn	r0, #4
 882 005a F2E7     		b	.L59
 883              	.L65:
 376:Drivers/BSP/Components/lan8742/lan8742.c ****     }
 884              		.loc 1 376 14 view .LVU254
 885 005c 4FF0FF30 		mov	r0, #-1
 886 0060 EFE7     		b	.L59
 887              	.L66:
 389:Drivers/BSP/Components/lan8742/lan8742.c ****     }
 888              		.loc 1 389 14 view .LVU255
 889 0062 6FF00300 		mvn	r0, #3
 890              	.LVL67:
 393:Drivers/BSP/Components/lan8742/lan8742.c **** }
 891              		.loc 1 393 3 is_stmt 1 view .LVU256
 393:Drivers/BSP/Components/lan8742/lan8742.c **** }
 892              		.loc 1 393 10 is_stmt 0 view .LVU257
 893 0066 ECE7     		b	.L59
 894              		.cfi_endproc
 895              	.LFE7:
 897              		.section	.text.LAN8742_EnableLoopbackMode,"ax",%progbits
 898              		.align	1
 899              		.global	LAN8742_EnableLoopbackMode
 900              		.syntax unified
 901              		.thumb
 902              		.thumb_func
 904              	LAN8742_EnableLoopbackMode:
 905              	.LVL68:
 906              	.LFB8:
 395:Drivers/BSP/Components/lan8742/lan8742.c **** 
 396:Drivers/BSP/Components/lan8742/lan8742.c **** /**
 397:Drivers/BSP/Components/lan8742/lan8742.c ****   * @brief  Enable loopback mode.
 398:Drivers/BSP/Components/lan8742/lan8742.c ****   * @param  pObj: Pointer to device object.
 399:Drivers/BSP/Components/lan8742/lan8742.c ****   * @retval LAN8742_STATUS_OK  if OK
 400:Drivers/BSP/Components/lan8742/lan8742.c ****   *         LAN8742_STATUS_READ_ERROR if cannot read register
 401:Drivers/BSP/Components/lan8742/lan8742.c ****   *         LAN8742_STATUS_WRITE_ERROR if cannot write to register
 402:Drivers/BSP/Components/lan8742/lan8742.c ****   */
 403:Drivers/BSP/Components/lan8742/lan8742.c **** int32_t LAN8742_EnableLoopbackMode(lan8742_Object_t *pObj)
 404:Drivers/BSP/Components/lan8742/lan8742.c **** {
 907              		.loc 1 404 1 is_stmt 1 view -0
 908              		.cfi_startproc
 909              		@ args = 0, pretend = 0, frame = 8
 910              		@ frame_needed = 0, uses_anonymous_args = 0
 911              		.loc 1 404 1 is_stmt 0 view .LVU259
 912 0000 10B5     		push	{r4, lr}
 913              	.LCFI27:
 914              		.cfi_def_cfa_offset 8
 915              		.cfi_offset 4, -8
 916              		.cfi_offset 14, -4
 917 0002 82B0     		sub	sp, sp, #8
 918              	.LCFI28:
 919              		.cfi_def_cfa_offset 16
 920 0004 0446     		mov	r4, r0
 405:Drivers/BSP/Components/lan8742/lan8742.c ****   uint32_t readval = 0;
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5YGIqW.s 			page 25


 921              		.loc 1 405 3 is_stmt 1 view .LVU260
 922              		.loc 1 405 12 is_stmt 0 view .LVU261
 923 0006 0021     		movs	r1, #0
 924 0008 0191     		str	r1, [sp, #4]
 406:Drivers/BSP/Components/lan8742/lan8742.c ****   int32_t status = LAN8742_STATUS_OK;
 925              		.loc 1 406 3 is_stmt 1 view .LVU262
 926              	.LVL69:
 407:Drivers/BSP/Components/lan8742/lan8742.c **** 
 408:Drivers/BSP/Components/lan8742/lan8742.c ****   if(pObj->IO.ReadReg(pObj->DevAddr, LAN8742_BCR, &readval) >= 0)
 927              		.loc 1 408 3 view .LVU263
 928              		.loc 1 408 14 is_stmt 0 view .LVU264
 929 000a 4369     		ldr	r3, [r0, #20]
 930              		.loc 1 408 6 view .LVU265
 931 000c 01AA     		add	r2, sp, #4
 932 000e 0068     		ldr	r0, [r0]
 933              	.LVL70:
 934              		.loc 1 408 6 view .LVU266
 935 0010 9847     		blx	r3
 936              	.LVL71:
 937              		.loc 1 408 5 discriminator 1 view .LVU267
 938 0012 0028     		cmp	r0, #0
 939 0014 0CDB     		blt	.L72
 409:Drivers/BSP/Components/lan8742/lan8742.c ****   {
 410:Drivers/BSP/Components/lan8742/lan8742.c ****     readval |= LAN8742_BCR_LOOPBACK;
 940              		.loc 1 410 5 is_stmt 1 view .LVU268
 941              		.loc 1 410 13 is_stmt 0 view .LVU269
 942 0016 019A     		ldr	r2, [sp, #4]
 943 0018 42F48042 		orr	r2, r2, #16384
 944 001c 0192     		str	r2, [sp, #4]
 411:Drivers/BSP/Components/lan8742/lan8742.c **** 
 412:Drivers/BSP/Components/lan8742/lan8742.c ****     /* Apply configuration */
 413:Drivers/BSP/Components/lan8742/lan8742.c ****     if(pObj->IO.WriteReg(pObj->DevAddr, LAN8742_BCR, readval) < 0)
 945              		.loc 1 413 5 is_stmt 1 view .LVU270
 946              		.loc 1 413 16 is_stmt 0 view .LVU271
 947 001e 2369     		ldr	r3, [r4, #16]
 948              		.loc 1 413 8 view .LVU272
 949 0020 0021     		movs	r1, #0
 950 0022 2068     		ldr	r0, [r4]
 951 0024 9847     		blx	r3
 952              	.LVL72:
 953              		.loc 1 413 7 discriminator 1 view .LVU273
 954 0026 0028     		cmp	r0, #0
 955 0028 05DB     		blt	.L73
 406:Drivers/BSP/Components/lan8742/lan8742.c **** 
 956              		.loc 1 406 11 view .LVU274
 957 002a 0020     		movs	r0, #0
 958              	.LVL73:
 959              	.L70:
 414:Drivers/BSP/Components/lan8742/lan8742.c ****     {
 415:Drivers/BSP/Components/lan8742/lan8742.c ****       status = LAN8742_STATUS_WRITE_ERROR;
 416:Drivers/BSP/Components/lan8742/lan8742.c ****     }
 417:Drivers/BSP/Components/lan8742/lan8742.c ****   }
 418:Drivers/BSP/Components/lan8742/lan8742.c ****   else
 419:Drivers/BSP/Components/lan8742/lan8742.c ****   {
 420:Drivers/BSP/Components/lan8742/lan8742.c ****     status = LAN8742_STATUS_READ_ERROR;
 421:Drivers/BSP/Components/lan8742/lan8742.c ****   }
 422:Drivers/BSP/Components/lan8742/lan8742.c **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5YGIqW.s 			page 26


 423:Drivers/BSP/Components/lan8742/lan8742.c ****   return status;
 424:Drivers/BSP/Components/lan8742/lan8742.c **** }
 960              		.loc 1 424 1 view .LVU275
 961 002c 02B0     		add	sp, sp, #8
 962              	.LCFI29:
 963              		.cfi_remember_state
 964              		.cfi_def_cfa_offset 8
 965              		@ sp needed
 966 002e 10BD     		pop	{r4, pc}
 967              	.LVL74:
 968              	.L72:
 969              	.LCFI30:
 970              		.cfi_restore_state
 420:Drivers/BSP/Components/lan8742/lan8742.c ****   }
 971              		.loc 1 420 12 view .LVU276
 972 0030 6FF00400 		mvn	r0, #4
 973 0034 FAE7     		b	.L70
 974              	.L73:
 415:Drivers/BSP/Components/lan8742/lan8742.c ****     }
 975              		.loc 1 415 14 view .LVU277
 976 0036 6FF00300 		mvn	r0, #3
 977              	.LVL75:
 423:Drivers/BSP/Components/lan8742/lan8742.c **** }
 978              		.loc 1 423 3 is_stmt 1 view .LVU278
 423:Drivers/BSP/Components/lan8742/lan8742.c **** }
 979              		.loc 1 423 10 is_stmt 0 view .LVU279
 980 003a F7E7     		b	.L70
 981              		.cfi_endproc
 982              	.LFE8:
 984              		.section	.text.LAN8742_DisableLoopbackMode,"ax",%progbits
 985              		.align	1
 986              		.global	LAN8742_DisableLoopbackMode
 987              		.syntax unified
 988              		.thumb
 989              		.thumb_func
 991              	LAN8742_DisableLoopbackMode:
 992              	.LVL76:
 993              	.LFB9:
 425:Drivers/BSP/Components/lan8742/lan8742.c **** 
 426:Drivers/BSP/Components/lan8742/lan8742.c **** /**
 427:Drivers/BSP/Components/lan8742/lan8742.c ****   * @brief  Disable loopback mode.
 428:Drivers/BSP/Components/lan8742/lan8742.c ****   * @param  pObj: Pointer to device object.
 429:Drivers/BSP/Components/lan8742/lan8742.c ****   * @retval LAN8742_STATUS_OK  if OK
 430:Drivers/BSP/Components/lan8742/lan8742.c ****   *         LAN8742_STATUS_READ_ERROR if cannot read register
 431:Drivers/BSP/Components/lan8742/lan8742.c ****   *         LAN8742_STATUS_WRITE_ERROR if cannot write to register
 432:Drivers/BSP/Components/lan8742/lan8742.c ****   */
 433:Drivers/BSP/Components/lan8742/lan8742.c **** int32_t LAN8742_DisableLoopbackMode(lan8742_Object_t *pObj)
 434:Drivers/BSP/Components/lan8742/lan8742.c **** {
 994              		.loc 1 434 1 is_stmt 1 view -0
 995              		.cfi_startproc
 996              		@ args = 0, pretend = 0, frame = 8
 997              		@ frame_needed = 0, uses_anonymous_args = 0
 998              		.loc 1 434 1 is_stmt 0 view .LVU281
 999 0000 10B5     		push	{r4, lr}
 1000              	.LCFI31:
 1001              		.cfi_def_cfa_offset 8
 1002              		.cfi_offset 4, -8
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5YGIqW.s 			page 27


 1003              		.cfi_offset 14, -4
 1004 0002 82B0     		sub	sp, sp, #8
 1005              	.LCFI32:
 1006              		.cfi_def_cfa_offset 16
 1007 0004 0446     		mov	r4, r0
 435:Drivers/BSP/Components/lan8742/lan8742.c ****   uint32_t readval = 0;
 1008              		.loc 1 435 3 is_stmt 1 view .LVU282
 1009              		.loc 1 435 12 is_stmt 0 view .LVU283
 1010 0006 0021     		movs	r1, #0
 1011 0008 0191     		str	r1, [sp, #4]
 436:Drivers/BSP/Components/lan8742/lan8742.c ****   int32_t status = LAN8742_STATUS_OK;
 1012              		.loc 1 436 3 is_stmt 1 view .LVU284
 1013              	.LVL77:
 437:Drivers/BSP/Components/lan8742/lan8742.c **** 
 438:Drivers/BSP/Components/lan8742/lan8742.c ****   if(pObj->IO.ReadReg(pObj->DevAddr, LAN8742_BCR, &readval) >= 0)
 1014              		.loc 1 438 3 view .LVU285
 1015              		.loc 1 438 14 is_stmt 0 view .LVU286
 1016 000a 4369     		ldr	r3, [r0, #20]
 1017              		.loc 1 438 6 view .LVU287
 1018 000c 01AA     		add	r2, sp, #4
 1019 000e 0068     		ldr	r0, [r0]
 1020              	.LVL78:
 1021              		.loc 1 438 6 view .LVU288
 1022 0010 9847     		blx	r3
 1023              	.LVL79:
 1024              		.loc 1 438 5 discriminator 1 view .LVU289
 1025 0012 0028     		cmp	r0, #0
 1026 0014 0CDB     		blt	.L77
 439:Drivers/BSP/Components/lan8742/lan8742.c ****   {
 440:Drivers/BSP/Components/lan8742/lan8742.c ****     readval &= ~LAN8742_BCR_LOOPBACK;
 1027              		.loc 1 440 5 is_stmt 1 view .LVU290
 1028              		.loc 1 440 13 is_stmt 0 view .LVU291
 1029 0016 019A     		ldr	r2, [sp, #4]
 1030 0018 22F48042 		bic	r2, r2, #16384
 1031 001c 0192     		str	r2, [sp, #4]
 441:Drivers/BSP/Components/lan8742/lan8742.c **** 
 442:Drivers/BSP/Components/lan8742/lan8742.c ****     /* Apply configuration */
 443:Drivers/BSP/Components/lan8742/lan8742.c ****     if(pObj->IO.WriteReg(pObj->DevAddr, LAN8742_BCR, readval) < 0)
 1032              		.loc 1 443 5 is_stmt 1 view .LVU292
 1033              		.loc 1 443 16 is_stmt 0 view .LVU293
 1034 001e 2369     		ldr	r3, [r4, #16]
 1035              		.loc 1 443 8 view .LVU294
 1036 0020 0021     		movs	r1, #0
 1037 0022 2068     		ldr	r0, [r4]
 1038 0024 9847     		blx	r3
 1039              	.LVL80:
 1040              		.loc 1 443 7 discriminator 1 view .LVU295
 1041 0026 0028     		cmp	r0, #0
 1042 0028 05DB     		blt	.L78
 436:Drivers/BSP/Components/lan8742/lan8742.c **** 
 1043              		.loc 1 436 11 view .LVU296
 1044 002a 0020     		movs	r0, #0
 1045              	.LVL81:
 1046              	.L75:
 444:Drivers/BSP/Components/lan8742/lan8742.c ****     {
 445:Drivers/BSP/Components/lan8742/lan8742.c ****       status =  LAN8742_STATUS_WRITE_ERROR;
 446:Drivers/BSP/Components/lan8742/lan8742.c ****     }
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5YGIqW.s 			page 28


 447:Drivers/BSP/Components/lan8742/lan8742.c ****   }
 448:Drivers/BSP/Components/lan8742/lan8742.c ****   else
 449:Drivers/BSP/Components/lan8742/lan8742.c ****   {
 450:Drivers/BSP/Components/lan8742/lan8742.c ****     status = LAN8742_STATUS_READ_ERROR;
 451:Drivers/BSP/Components/lan8742/lan8742.c ****   }
 452:Drivers/BSP/Components/lan8742/lan8742.c **** 
 453:Drivers/BSP/Components/lan8742/lan8742.c ****   return status;
 454:Drivers/BSP/Components/lan8742/lan8742.c **** }
 1047              		.loc 1 454 1 view .LVU297
 1048 002c 02B0     		add	sp, sp, #8
 1049              	.LCFI33:
 1050              		.cfi_remember_state
 1051              		.cfi_def_cfa_offset 8
 1052              		@ sp needed
 1053 002e 10BD     		pop	{r4, pc}
 1054              	.LVL82:
 1055              	.L77:
 1056              	.LCFI34:
 1057              		.cfi_restore_state
 450:Drivers/BSP/Components/lan8742/lan8742.c ****   }
 1058              		.loc 1 450 12 view .LVU298
 1059 0030 6FF00400 		mvn	r0, #4
 1060 0034 FAE7     		b	.L75
 1061              	.L78:
 445:Drivers/BSP/Components/lan8742/lan8742.c ****     }
 1062              		.loc 1 445 14 view .LVU299
 1063 0036 6FF00300 		mvn	r0, #3
 1064              	.LVL83:
 453:Drivers/BSP/Components/lan8742/lan8742.c **** }
 1065              		.loc 1 453 3 is_stmt 1 view .LVU300
 453:Drivers/BSP/Components/lan8742/lan8742.c **** }
 1066              		.loc 1 453 10 is_stmt 0 view .LVU301
 1067 003a F7E7     		b	.L75
 1068              		.cfi_endproc
 1069              	.LFE9:
 1071              		.section	.text.LAN8742_EnableIT,"ax",%progbits
 1072              		.align	1
 1073              		.global	LAN8742_EnableIT
 1074              		.syntax unified
 1075              		.thumb
 1076              		.thumb_func
 1078              	LAN8742_EnableIT:
 1079              	.LVL84:
 1080              	.LFB10:
 455:Drivers/BSP/Components/lan8742/lan8742.c **** 
 456:Drivers/BSP/Components/lan8742/lan8742.c **** /**
 457:Drivers/BSP/Components/lan8742/lan8742.c ****   * @brief  Enable IT source.
 458:Drivers/BSP/Components/lan8742/lan8742.c ****   * @param  pObj: Pointer to device object.
 459:Drivers/BSP/Components/lan8742/lan8742.c ****   * @param  Interrupt: IT source to be enabled
 460:Drivers/BSP/Components/lan8742/lan8742.c ****   *         should be a value or a combination of the following:
 461:Drivers/BSP/Components/lan8742/lan8742.c ****   *         LAN8742_WOL_IT
 462:Drivers/BSP/Components/lan8742/lan8742.c ****   *         LAN8742_ENERGYON_IT
 463:Drivers/BSP/Components/lan8742/lan8742.c ****   *         LAN8742_AUTONEGO_COMPLETE_IT
 464:Drivers/BSP/Components/lan8742/lan8742.c ****   *         LAN8742_REMOTE_FAULT_IT
 465:Drivers/BSP/Components/lan8742/lan8742.c ****   *         LAN8742_LINK_DOWN_IT
 466:Drivers/BSP/Components/lan8742/lan8742.c ****   *         LAN8742_AUTONEGO_LP_ACK_IT
 467:Drivers/BSP/Components/lan8742/lan8742.c ****   *         LAN8742_PARALLEL_DETECTION_FAULT_IT
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5YGIqW.s 			page 29


 468:Drivers/BSP/Components/lan8742/lan8742.c ****   *         LAN8742_AUTONEGO_PAGE_RECEIVED_IT
 469:Drivers/BSP/Components/lan8742/lan8742.c ****   * @retval LAN8742_STATUS_OK  if OK
 470:Drivers/BSP/Components/lan8742/lan8742.c ****   *         LAN8742_STATUS_READ_ERROR if cannot read register
 471:Drivers/BSP/Components/lan8742/lan8742.c ****   *         LAN8742_STATUS_WRITE_ERROR if cannot write to register
 472:Drivers/BSP/Components/lan8742/lan8742.c ****   */
 473:Drivers/BSP/Components/lan8742/lan8742.c **** int32_t LAN8742_EnableIT(lan8742_Object_t *pObj, uint32_t Interrupt)
 474:Drivers/BSP/Components/lan8742/lan8742.c **** {
 1081              		.loc 1 474 1 is_stmt 1 view -0
 1082              		.cfi_startproc
 1083              		@ args = 0, pretend = 0, frame = 8
 1084              		@ frame_needed = 0, uses_anonymous_args = 0
 1085              		.loc 1 474 1 is_stmt 0 view .LVU303
 1086 0000 30B5     		push	{r4, r5, lr}
 1087              	.LCFI35:
 1088              		.cfi_def_cfa_offset 12
 1089              		.cfi_offset 4, -12
 1090              		.cfi_offset 5, -8
 1091              		.cfi_offset 14, -4
 1092 0002 83B0     		sub	sp, sp, #12
 1093              	.LCFI36:
 1094              		.cfi_def_cfa_offset 24
 1095 0004 0446     		mov	r4, r0
 1096 0006 0D46     		mov	r5, r1
 475:Drivers/BSP/Components/lan8742/lan8742.c ****   uint32_t readval = 0;
 1097              		.loc 1 475 3 is_stmt 1 view .LVU304
 1098              		.loc 1 475 12 is_stmt 0 view .LVU305
 1099 0008 0023     		movs	r3, #0
 1100 000a 0193     		str	r3, [sp, #4]
 476:Drivers/BSP/Components/lan8742/lan8742.c ****   int32_t status = LAN8742_STATUS_OK;
 1101              		.loc 1 476 3 is_stmt 1 view .LVU306
 1102              	.LVL85:
 477:Drivers/BSP/Components/lan8742/lan8742.c **** 
 478:Drivers/BSP/Components/lan8742/lan8742.c ****   if(pObj->IO.ReadReg(pObj->DevAddr, LAN8742_IMR, &readval) >= 0)
 1103              		.loc 1 478 3 view .LVU307
 1104              		.loc 1 478 14 is_stmt 0 view .LVU308
 1105 000c 4369     		ldr	r3, [r0, #20]
 1106              		.loc 1 478 6 view .LVU309
 1107 000e 01AA     		add	r2, sp, #4
 1108 0010 1E21     		movs	r1, #30
 1109              	.LVL86:
 1110              		.loc 1 478 6 view .LVU310
 1111 0012 0068     		ldr	r0, [r0]
 1112              	.LVL87:
 1113              		.loc 1 478 6 view .LVU311
 1114 0014 9847     		blx	r3
 1115              	.LVL88:
 1116              		.loc 1 478 5 discriminator 1 view .LVU312
 1117 0016 0028     		cmp	r0, #0
 1118 0018 0CDB     		blt	.L82
 479:Drivers/BSP/Components/lan8742/lan8742.c ****   {
 480:Drivers/BSP/Components/lan8742/lan8742.c ****     readval |= Interrupt;
 1119              		.loc 1 480 5 is_stmt 1 view .LVU313
 1120              		.loc 1 480 13 is_stmt 0 view .LVU314
 1121 001a 019B     		ldr	r3, [sp, #4]
 1122 001c 45EA0302 		orr	r2, r5, r3
 1123 0020 0192     		str	r2, [sp, #4]
 481:Drivers/BSP/Components/lan8742/lan8742.c **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5YGIqW.s 			page 30


 482:Drivers/BSP/Components/lan8742/lan8742.c ****     /* Apply configuration */
 483:Drivers/BSP/Components/lan8742/lan8742.c ****     if(pObj->IO.WriteReg(pObj->DevAddr, LAN8742_IMR, readval) < 0)
 1124              		.loc 1 483 5 is_stmt 1 view .LVU315
 1125              		.loc 1 483 16 is_stmt 0 view .LVU316
 1126 0022 2369     		ldr	r3, [r4, #16]
 1127              		.loc 1 483 8 view .LVU317
 1128 0024 1E21     		movs	r1, #30
 1129 0026 2068     		ldr	r0, [r4]
 1130 0028 9847     		blx	r3
 1131              	.LVL89:
 1132              		.loc 1 483 7 discriminator 1 view .LVU318
 1133 002a 0028     		cmp	r0, #0
 1134 002c 05DB     		blt	.L83
 476:Drivers/BSP/Components/lan8742/lan8742.c **** 
 1135              		.loc 1 476 11 view .LVU319
 1136 002e 0020     		movs	r0, #0
 1137              	.LVL90:
 1138              	.L80:
 484:Drivers/BSP/Components/lan8742/lan8742.c ****     {
 485:Drivers/BSP/Components/lan8742/lan8742.c ****       status =  LAN8742_STATUS_WRITE_ERROR;
 486:Drivers/BSP/Components/lan8742/lan8742.c ****     }
 487:Drivers/BSP/Components/lan8742/lan8742.c ****   }
 488:Drivers/BSP/Components/lan8742/lan8742.c ****   else
 489:Drivers/BSP/Components/lan8742/lan8742.c ****   {
 490:Drivers/BSP/Components/lan8742/lan8742.c ****     status = LAN8742_STATUS_READ_ERROR;
 491:Drivers/BSP/Components/lan8742/lan8742.c ****   }
 492:Drivers/BSP/Components/lan8742/lan8742.c **** 
 493:Drivers/BSP/Components/lan8742/lan8742.c ****   return status;
 494:Drivers/BSP/Components/lan8742/lan8742.c **** }
 1139              		.loc 1 494 1 view .LVU320
 1140 0030 03B0     		add	sp, sp, #12
 1141              	.LCFI37:
 1142              		.cfi_remember_state
 1143              		.cfi_def_cfa_offset 12
 1144              		@ sp needed
 1145 0032 30BD     		pop	{r4, r5, pc}
 1146              	.LVL91:
 1147              	.L82:
 1148              	.LCFI38:
 1149              		.cfi_restore_state
 490:Drivers/BSP/Components/lan8742/lan8742.c ****   }
 1150              		.loc 1 490 12 view .LVU321
 1151 0034 6FF00400 		mvn	r0, #4
 1152 0038 FAE7     		b	.L80
 1153              	.L83:
 485:Drivers/BSP/Components/lan8742/lan8742.c ****     }
 1154              		.loc 1 485 14 view .LVU322
 1155 003a 6FF00300 		mvn	r0, #3
 1156              	.LVL92:
 493:Drivers/BSP/Components/lan8742/lan8742.c **** }
 1157              		.loc 1 493 3 is_stmt 1 view .LVU323
 493:Drivers/BSP/Components/lan8742/lan8742.c **** }
 1158              		.loc 1 493 10 is_stmt 0 view .LVU324
 1159 003e F7E7     		b	.L80
 1160              		.cfi_endproc
 1161              	.LFE10:
 1163              		.section	.text.LAN8742_DisableIT,"ax",%progbits
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5YGIqW.s 			page 31


 1164              		.align	1
 1165              		.global	LAN8742_DisableIT
 1166              		.syntax unified
 1167              		.thumb
 1168              		.thumb_func
 1170              	LAN8742_DisableIT:
 1171              	.LVL93:
 1172              	.LFB11:
 495:Drivers/BSP/Components/lan8742/lan8742.c **** 
 496:Drivers/BSP/Components/lan8742/lan8742.c **** /**
 497:Drivers/BSP/Components/lan8742/lan8742.c ****   * @brief  Disable IT source.
 498:Drivers/BSP/Components/lan8742/lan8742.c ****   * @param  pObj: Pointer to device object.
 499:Drivers/BSP/Components/lan8742/lan8742.c ****   * @param  Interrupt: IT source to be disabled
 500:Drivers/BSP/Components/lan8742/lan8742.c ****   *         should be a value or a combination of the following:
 501:Drivers/BSP/Components/lan8742/lan8742.c ****   *         LAN8742_WOL_IT
 502:Drivers/BSP/Components/lan8742/lan8742.c ****   *         LAN8742_ENERGYON_IT
 503:Drivers/BSP/Components/lan8742/lan8742.c ****   *         LAN8742_AUTONEGO_COMPLETE_IT
 504:Drivers/BSP/Components/lan8742/lan8742.c ****   *         LAN8742_REMOTE_FAULT_IT
 505:Drivers/BSP/Components/lan8742/lan8742.c ****   *         LAN8742_LINK_DOWN_IT
 506:Drivers/BSP/Components/lan8742/lan8742.c ****   *         LAN8742_AUTONEGO_LP_ACK_IT
 507:Drivers/BSP/Components/lan8742/lan8742.c ****   *         LAN8742_PARALLEL_DETECTION_FAULT_IT
 508:Drivers/BSP/Components/lan8742/lan8742.c ****   *         LAN8742_AUTONEGO_PAGE_RECEIVED_IT
 509:Drivers/BSP/Components/lan8742/lan8742.c ****   * @retval LAN8742_STATUS_OK  if OK
 510:Drivers/BSP/Components/lan8742/lan8742.c ****   *         LAN8742_STATUS_READ_ERROR if cannot read register
 511:Drivers/BSP/Components/lan8742/lan8742.c ****   *         LAN8742_STATUS_WRITE_ERROR if cannot write to register
 512:Drivers/BSP/Components/lan8742/lan8742.c ****   */
 513:Drivers/BSP/Components/lan8742/lan8742.c **** int32_t LAN8742_DisableIT(lan8742_Object_t *pObj, uint32_t Interrupt)
 514:Drivers/BSP/Components/lan8742/lan8742.c **** {
 1173              		.loc 1 514 1 is_stmt 1 view -0
 1174              		.cfi_startproc
 1175              		@ args = 0, pretend = 0, frame = 8
 1176              		@ frame_needed = 0, uses_anonymous_args = 0
 1177              		.loc 1 514 1 is_stmt 0 view .LVU326
 1178 0000 30B5     		push	{r4, r5, lr}
 1179              	.LCFI39:
 1180              		.cfi_def_cfa_offset 12
 1181              		.cfi_offset 4, -12
 1182              		.cfi_offset 5, -8
 1183              		.cfi_offset 14, -4
 1184 0002 83B0     		sub	sp, sp, #12
 1185              	.LCFI40:
 1186              		.cfi_def_cfa_offset 24
 1187 0004 0446     		mov	r4, r0
 1188 0006 0D46     		mov	r5, r1
 515:Drivers/BSP/Components/lan8742/lan8742.c ****   uint32_t readval = 0;
 1189              		.loc 1 515 3 is_stmt 1 view .LVU327
 1190              		.loc 1 515 12 is_stmt 0 view .LVU328
 1191 0008 0023     		movs	r3, #0
 1192 000a 0193     		str	r3, [sp, #4]
 516:Drivers/BSP/Components/lan8742/lan8742.c ****   int32_t status = LAN8742_STATUS_OK;
 1193              		.loc 1 516 3 is_stmt 1 view .LVU329
 1194              	.LVL94:
 517:Drivers/BSP/Components/lan8742/lan8742.c **** 
 518:Drivers/BSP/Components/lan8742/lan8742.c ****   if(pObj->IO.ReadReg(pObj->DevAddr, LAN8742_IMR, &readval) >= 0)
 1195              		.loc 1 518 3 view .LVU330
 1196              		.loc 1 518 14 is_stmt 0 view .LVU331
 1197 000c 4369     		ldr	r3, [r0, #20]
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5YGIqW.s 			page 32


 1198              		.loc 1 518 6 view .LVU332
 1199 000e 01AA     		add	r2, sp, #4
 1200 0010 1E21     		movs	r1, #30
 1201              	.LVL95:
 1202              		.loc 1 518 6 view .LVU333
 1203 0012 0068     		ldr	r0, [r0]
 1204              	.LVL96:
 1205              		.loc 1 518 6 view .LVU334
 1206 0014 9847     		blx	r3
 1207              	.LVL97:
 1208              		.loc 1 518 5 discriminator 1 view .LVU335
 1209 0016 0028     		cmp	r0, #0
 1210 0018 0CDB     		blt	.L87
 519:Drivers/BSP/Components/lan8742/lan8742.c ****   {
 520:Drivers/BSP/Components/lan8742/lan8742.c ****     readval &= ~Interrupt;
 1211              		.loc 1 520 5 is_stmt 1 view .LVU336
 1212              		.loc 1 520 13 is_stmt 0 view .LVU337
 1213 001a 019A     		ldr	r2, [sp, #4]
 1214 001c 22EA0502 		bic	r2, r2, r5
 1215 0020 0192     		str	r2, [sp, #4]
 521:Drivers/BSP/Components/lan8742/lan8742.c **** 
 522:Drivers/BSP/Components/lan8742/lan8742.c ****     /* Apply configuration */
 523:Drivers/BSP/Components/lan8742/lan8742.c ****     if(pObj->IO.WriteReg(pObj->DevAddr, LAN8742_IMR, readval) < 0)
 1216              		.loc 1 523 5 is_stmt 1 view .LVU338
 1217              		.loc 1 523 16 is_stmt 0 view .LVU339
 1218 0022 2369     		ldr	r3, [r4, #16]
 1219              		.loc 1 523 8 view .LVU340
 1220 0024 1E21     		movs	r1, #30
 1221 0026 2068     		ldr	r0, [r4]
 1222 0028 9847     		blx	r3
 1223              	.LVL98:
 1224              		.loc 1 523 7 discriminator 1 view .LVU341
 1225 002a 0028     		cmp	r0, #0
 1226 002c 05DB     		blt	.L88
 516:Drivers/BSP/Components/lan8742/lan8742.c **** 
 1227              		.loc 1 516 11 view .LVU342
 1228 002e 0020     		movs	r0, #0
 1229              	.LVL99:
 1230              	.L85:
 524:Drivers/BSP/Components/lan8742/lan8742.c ****     {
 525:Drivers/BSP/Components/lan8742/lan8742.c ****       status = LAN8742_STATUS_WRITE_ERROR;
 526:Drivers/BSP/Components/lan8742/lan8742.c ****     }
 527:Drivers/BSP/Components/lan8742/lan8742.c ****   }
 528:Drivers/BSP/Components/lan8742/lan8742.c ****   else
 529:Drivers/BSP/Components/lan8742/lan8742.c ****   {
 530:Drivers/BSP/Components/lan8742/lan8742.c ****     status = LAN8742_STATUS_READ_ERROR;
 531:Drivers/BSP/Components/lan8742/lan8742.c ****   }
 532:Drivers/BSP/Components/lan8742/lan8742.c **** 
 533:Drivers/BSP/Components/lan8742/lan8742.c ****   return status;
 534:Drivers/BSP/Components/lan8742/lan8742.c **** }
 1231              		.loc 1 534 1 view .LVU343
 1232 0030 03B0     		add	sp, sp, #12
 1233              	.LCFI41:
 1234              		.cfi_remember_state
 1235              		.cfi_def_cfa_offset 12
 1236              		@ sp needed
 1237 0032 30BD     		pop	{r4, r5, pc}
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5YGIqW.s 			page 33


 1238              	.LVL100:
 1239              	.L87:
 1240              	.LCFI42:
 1241              		.cfi_restore_state
 530:Drivers/BSP/Components/lan8742/lan8742.c ****   }
 1242              		.loc 1 530 12 view .LVU344
 1243 0034 6FF00400 		mvn	r0, #4
 1244 0038 FAE7     		b	.L85
 1245              	.L88:
 525:Drivers/BSP/Components/lan8742/lan8742.c ****     }
 1246              		.loc 1 525 14 view .LVU345
 1247 003a 6FF00300 		mvn	r0, #3
 1248              	.LVL101:
 533:Drivers/BSP/Components/lan8742/lan8742.c **** }
 1249              		.loc 1 533 3 is_stmt 1 view .LVU346
 533:Drivers/BSP/Components/lan8742/lan8742.c **** }
 1250              		.loc 1 533 10 is_stmt 0 view .LVU347
 1251 003e F7E7     		b	.L85
 1252              		.cfi_endproc
 1253              	.LFE11:
 1255              		.section	.text.LAN8742_ClearIT,"ax",%progbits
 1256              		.align	1
 1257              		.global	LAN8742_ClearIT
 1258              		.syntax unified
 1259              		.thumb
 1260              		.thumb_func
 1262              	LAN8742_ClearIT:
 1263              	.LVL102:
 1264              	.LFB12:
 535:Drivers/BSP/Components/lan8742/lan8742.c **** 
 536:Drivers/BSP/Components/lan8742/lan8742.c **** /**
 537:Drivers/BSP/Components/lan8742/lan8742.c ****   * @brief  Clear IT flag.
 538:Drivers/BSP/Components/lan8742/lan8742.c ****   * @param  pObj: Pointer to device object.
 539:Drivers/BSP/Components/lan8742/lan8742.c ****   * @param  Interrupt: IT flag to be cleared
 540:Drivers/BSP/Components/lan8742/lan8742.c ****   *         should be a value or a combination of the following:
 541:Drivers/BSP/Components/lan8742/lan8742.c ****   *         LAN8742_WOL_IT
 542:Drivers/BSP/Components/lan8742/lan8742.c ****   *         LAN8742_ENERGYON_IT
 543:Drivers/BSP/Components/lan8742/lan8742.c ****   *         LAN8742_AUTONEGO_COMPLETE_IT
 544:Drivers/BSP/Components/lan8742/lan8742.c ****   *         LAN8742_REMOTE_FAULT_IT
 545:Drivers/BSP/Components/lan8742/lan8742.c ****   *         LAN8742_LINK_DOWN_IT
 546:Drivers/BSP/Components/lan8742/lan8742.c ****   *         LAN8742_AUTONEGO_LP_ACK_IT
 547:Drivers/BSP/Components/lan8742/lan8742.c ****   *         LAN8742_PARALLEL_DETECTION_FAULT_IT
 548:Drivers/BSP/Components/lan8742/lan8742.c ****   *         LAN8742_AUTONEGO_PAGE_RECEIVED_IT
 549:Drivers/BSP/Components/lan8742/lan8742.c ****   * @retval LAN8742_STATUS_OK  if OK
 550:Drivers/BSP/Components/lan8742/lan8742.c ****   *         LAN8742_STATUS_READ_ERROR if cannot read register
 551:Drivers/BSP/Components/lan8742/lan8742.c ****   */
 552:Drivers/BSP/Components/lan8742/lan8742.c **** int32_t  LAN8742_ClearIT(lan8742_Object_t *pObj, uint32_t Interrupt)
 553:Drivers/BSP/Components/lan8742/lan8742.c **** {
 1265              		.loc 1 553 1 is_stmt 1 view -0
 1266              		.cfi_startproc
 1267              		@ args = 0, pretend = 0, frame = 8
 1268              		@ frame_needed = 0, uses_anonymous_args = 0
 1269              		.loc 1 553 1 is_stmt 0 view .LVU349
 1270 0000 00B5     		push	{lr}
 1271              	.LCFI43:
 1272              		.cfi_def_cfa_offset 4
 1273              		.cfi_offset 14, -4
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5YGIqW.s 			page 34


 1274 0002 83B0     		sub	sp, sp, #12
 1275              	.LCFI44:
 1276              		.cfi_def_cfa_offset 16
 554:Drivers/BSP/Components/lan8742/lan8742.c ****   uint32_t readval = 0;
 1277              		.loc 1 554 3 is_stmt 1 view .LVU350
 1278              		.loc 1 554 12 is_stmt 0 view .LVU351
 1279 0004 0023     		movs	r3, #0
 1280 0006 0193     		str	r3, [sp, #4]
 555:Drivers/BSP/Components/lan8742/lan8742.c ****   int32_t status = LAN8742_STATUS_OK;
 1281              		.loc 1 555 3 is_stmt 1 view .LVU352
 1282              	.LVL103:
 556:Drivers/BSP/Components/lan8742/lan8742.c **** 
 557:Drivers/BSP/Components/lan8742/lan8742.c ****   if(pObj->IO.ReadReg(pObj->DevAddr, LAN8742_ISFR, &readval) < 0)
 1283              		.loc 1 557 3 view .LVU353
 1284              		.loc 1 557 14 is_stmt 0 view .LVU354
 1285 0008 4369     		ldr	r3, [r0, #20]
 1286              		.loc 1 557 6 view .LVU355
 1287 000a 01AA     		add	r2, sp, #4
 1288 000c 1D21     		movs	r1, #29
 1289              	.LVL104:
 1290              		.loc 1 557 6 view .LVU356
 1291 000e 0068     		ldr	r0, [r0]
 1292              	.LVL105:
 1293              		.loc 1 557 6 view .LVU357
 1294 0010 9847     		blx	r3
 1295              	.LVL106:
 1296              		.loc 1 557 5 discriminator 1 view .LVU358
 1297 0012 0028     		cmp	r0, #0
 1298 0014 03DB     		blt	.L92
 555:Drivers/BSP/Components/lan8742/lan8742.c ****   int32_t status = LAN8742_STATUS_OK;
 1299              		.loc 1 555 11 view .LVU359
 1300 0016 0020     		movs	r0, #0
 1301              	.LVL107:
 1302              	.L90:
 558:Drivers/BSP/Components/lan8742/lan8742.c ****   {
 559:Drivers/BSP/Components/lan8742/lan8742.c ****     status =  LAN8742_STATUS_READ_ERROR;
 560:Drivers/BSP/Components/lan8742/lan8742.c ****   }
 561:Drivers/BSP/Components/lan8742/lan8742.c **** 
 562:Drivers/BSP/Components/lan8742/lan8742.c ****   return status;
 563:Drivers/BSP/Components/lan8742/lan8742.c **** }
 1303              		.loc 1 563 1 view .LVU360
 1304 0018 03B0     		add	sp, sp, #12
 1305              	.LCFI45:
 1306              		.cfi_remember_state
 1307              		.cfi_def_cfa_offset 4
 1308              		@ sp needed
 1309 001a 5DF804FB 		ldr	pc, [sp], #4
 1310              	.LVL108:
 1311              	.L92:
 1312              	.LCFI46:
 1313              		.cfi_restore_state
 559:Drivers/BSP/Components/lan8742/lan8742.c ****   }
 1314              		.loc 1 559 12 view .LVU361
 1315 001e 6FF00400 		mvn	r0, #4
 1316              	.LVL109:
 562:Drivers/BSP/Components/lan8742/lan8742.c **** }
 1317              		.loc 1 562 3 is_stmt 1 view .LVU362
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5YGIqW.s 			page 35


 562:Drivers/BSP/Components/lan8742/lan8742.c **** }
 1318              		.loc 1 562 10 is_stmt 0 view .LVU363
 1319 0022 F9E7     		b	.L90
 1320              		.cfi_endproc
 1321              	.LFE12:
 1323              		.section	.text.LAN8742_GetITStatus,"ax",%progbits
 1324              		.align	1
 1325              		.global	LAN8742_GetITStatus
 1326              		.syntax unified
 1327              		.thumb
 1328              		.thumb_func
 1330              	LAN8742_GetITStatus:
 1331              	.LVL110:
 1332              	.LFB13:
 564:Drivers/BSP/Components/lan8742/lan8742.c **** 
 565:Drivers/BSP/Components/lan8742/lan8742.c **** /**
 566:Drivers/BSP/Components/lan8742/lan8742.c ****   * @brief  Get IT Flag status.
 567:Drivers/BSP/Components/lan8742/lan8742.c ****   * @param  pObj: Pointer to device object.
 568:Drivers/BSP/Components/lan8742/lan8742.c ****   * @param  Interrupt: IT Flag to be checked,
 569:Drivers/BSP/Components/lan8742/lan8742.c ****   *         should be a value or a combination of the following:
 570:Drivers/BSP/Components/lan8742/lan8742.c ****   *         LAN8742_WOL_IT
 571:Drivers/BSP/Components/lan8742/lan8742.c ****   *         LAN8742_ENERGYON_IT
 572:Drivers/BSP/Components/lan8742/lan8742.c ****   *         LAN8742_AUTONEGO_COMPLETE_IT
 573:Drivers/BSP/Components/lan8742/lan8742.c ****   *         LAN8742_REMOTE_FAULT_IT
 574:Drivers/BSP/Components/lan8742/lan8742.c ****   *         LAN8742_LINK_DOWN_IT
 575:Drivers/BSP/Components/lan8742/lan8742.c ****   *         LAN8742_AUTONEGO_LP_ACK_IT
 576:Drivers/BSP/Components/lan8742/lan8742.c ****   *         LAN8742_PARALLEL_DETECTION_FAULT_IT
 577:Drivers/BSP/Components/lan8742/lan8742.c ****   *         LAN8742_AUTONEGO_PAGE_RECEIVED_IT
 578:Drivers/BSP/Components/lan8742/lan8742.c ****   * @retval 1 IT flag is SET
 579:Drivers/BSP/Components/lan8742/lan8742.c ****   *         0 IT flag is RESET
 580:Drivers/BSP/Components/lan8742/lan8742.c ****   *         LAN8742_STATUS_READ_ERROR if cannot read register
 581:Drivers/BSP/Components/lan8742/lan8742.c ****   */
 582:Drivers/BSP/Components/lan8742/lan8742.c **** int32_t LAN8742_GetITStatus(lan8742_Object_t *pObj, uint32_t Interrupt)
 583:Drivers/BSP/Components/lan8742/lan8742.c **** {
 1333              		.loc 1 583 1 is_stmt 1 view -0
 1334              		.cfi_startproc
 1335              		@ args = 0, pretend = 0, frame = 8
 1336              		@ frame_needed = 0, uses_anonymous_args = 0
 1337              		.loc 1 583 1 is_stmt 0 view .LVU365
 1338 0000 10B5     		push	{r4, lr}
 1339              	.LCFI47:
 1340              		.cfi_def_cfa_offset 8
 1341              		.cfi_offset 4, -8
 1342              		.cfi_offset 14, -4
 1343 0002 82B0     		sub	sp, sp, #8
 1344              	.LCFI48:
 1345              		.cfi_def_cfa_offset 16
 1346 0004 0C46     		mov	r4, r1
 584:Drivers/BSP/Components/lan8742/lan8742.c ****   uint32_t readval = 0;
 1347              		.loc 1 584 3 is_stmt 1 view .LVU366
 1348              		.loc 1 584 12 is_stmt 0 view .LVU367
 1349 0006 0023     		movs	r3, #0
 1350 0008 0193     		str	r3, [sp, #4]
 585:Drivers/BSP/Components/lan8742/lan8742.c ****   int32_t status = 0;
 1351              		.loc 1 585 3 is_stmt 1 view .LVU368
 1352              	.LVL111:
 586:Drivers/BSP/Components/lan8742/lan8742.c **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5YGIqW.s 			page 36


 587:Drivers/BSP/Components/lan8742/lan8742.c ****   if(pObj->IO.ReadReg(pObj->DevAddr, LAN8742_ISFR, &readval) >= 0)
 1353              		.loc 1 587 3 view .LVU369
 1354              		.loc 1 587 14 is_stmt 0 view .LVU370
 1355 000a 4369     		ldr	r3, [r0, #20]
 1356              		.loc 1 587 6 view .LVU371
 1357 000c 01AA     		add	r2, sp, #4
 1358 000e 1D21     		movs	r1, #29
 1359              	.LVL112:
 1360              		.loc 1 587 6 view .LVU372
 1361 0010 0068     		ldr	r0, [r0]
 1362              	.LVL113:
 1363              		.loc 1 587 6 view .LVU373
 1364 0012 9847     		blx	r3
 1365              	.LVL114:
 1366              		.loc 1 587 5 discriminator 1 view .LVU374
 1367 0014 0028     		cmp	r0, #0
 1368 0016 07DB     		blt	.L96
 588:Drivers/BSP/Components/lan8742/lan8742.c ****   {
 589:Drivers/BSP/Components/lan8742/lan8742.c ****     status = ((readval & Interrupt) == Interrupt);
 1369              		.loc 1 589 5 is_stmt 1 view .LVU375
 1370              		.loc 1 589 37 is_stmt 0 view .LVU376
 1371 0018 019B     		ldr	r3, [sp, #4]
 1372 001a 34EA0301 		bics	r1, r4, r3
 1373 001e 0CBF     		ite	eq
 1374 0020 0120     		moveq	r0, #1
 1375 0022 0020     		movne	r0, #0
 1376              	.LVL115:
 1377              	.L94:
 590:Drivers/BSP/Components/lan8742/lan8742.c ****   }
 591:Drivers/BSP/Components/lan8742/lan8742.c ****   else
 592:Drivers/BSP/Components/lan8742/lan8742.c ****   {
 593:Drivers/BSP/Components/lan8742/lan8742.c ****     status = LAN8742_STATUS_READ_ERROR;
 594:Drivers/BSP/Components/lan8742/lan8742.c ****   }
 595:Drivers/BSP/Components/lan8742/lan8742.c **** 
 596:Drivers/BSP/Components/lan8742/lan8742.c ****   return status;
 597:Drivers/BSP/Components/lan8742/lan8742.c **** }
 1378              		.loc 1 597 1 view .LVU377
 1379 0024 02B0     		add	sp, sp, #8
 1380              	.LCFI49:
 1381              		.cfi_remember_state
 1382              		.cfi_def_cfa_offset 8
 1383              		@ sp needed
 1384 0026 10BD     		pop	{r4, pc}
 1385              	.LVL116:
 1386              	.L96:
 1387              	.LCFI50:
 1388              		.cfi_restore_state
 593:Drivers/BSP/Components/lan8742/lan8742.c ****   }
 1389              		.loc 1 593 12 view .LVU378
 1390 0028 6FF00400 		mvn	r0, #4
 1391              	.LVL117:
 596:Drivers/BSP/Components/lan8742/lan8742.c **** }
 1392              		.loc 1 596 3 is_stmt 1 view .LVU379
 596:Drivers/BSP/Components/lan8742/lan8742.c **** }
 1393              		.loc 1 596 10 is_stmt 0 view .LVU380
 1394 002c FAE7     		b	.L94
 1395              		.cfi_endproc
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5YGIqW.s 			page 37


 1396              	.LFE13:
 1398              		.text
 1399              	.Letext0:
 1400              		.file 2 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin/../lib/gcc/arm-none-eabi/13.3.1/../
 1401              		.file 3 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin/../lib/gcc/arm-none-eabi/13.3.1/../
 1402              		.file 4 "Drivers/BSP/Components/lan8742/lan8742.h"
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cc5YGIqW.s 			page 38


DEFINED SYMBOLS
                            *ABS*:00000000 lan8742.c
C:\Users\<USER>\AppData\Local\Temp\cc5YGIqW.s:20     .text.LAN8742_RegisterBusIO:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cc5YGIqW.s:26     .text.LAN8742_RegisterBusIO:00000000 LAN8742_RegisterBusIO
C:\Users\<USER>\AppData\Local\Temp\cc5YGIqW.s:112    .text.LAN8742_Init:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cc5YGIqW.s:118    .text.LAN8742_Init:00000000 LAN8742_Init
C:\Users\<USER>\AppData\Local\Temp\cc5YGIqW.s:250    .text.LAN8742_DeInit:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cc5YGIqW.s:256    .text.LAN8742_DeInit:00000000 LAN8742_DeInit
C:\Users\<USER>\AppData\Local\Temp\cc5YGIqW.s:319    .text.LAN8742_DisablePowerDownMode:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cc5YGIqW.s:325    .text.LAN8742_DisablePowerDownMode:00000000 LAN8742_DisablePowerDownMode
C:\Users\<USER>\AppData\Local\Temp\cc5YGIqW.s:406    .text.LAN8742_EnablePowerDownMode:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cc5YGIqW.s:412    .text.LAN8742_EnablePowerDownMode:00000000 LAN8742_EnablePowerDownMode
C:\Users\<USER>\AppData\Local\Temp\cc5YGIqW.s:493    .text.LAN8742_StartAutoNego:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cc5YGIqW.s:499    .text.LAN8742_StartAutoNego:00000000 LAN8742_StartAutoNego
C:\Users\<USER>\AppData\Local\Temp\cc5YGIqW.s:580    .text.LAN8742_GetLinkState:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cc5YGIqW.s:586    .text.LAN8742_GetLinkState:00000000 LAN8742_GetLinkState
C:\Users\<USER>\AppData\Local\Temp\cc5YGIqW.s:771    .text.LAN8742_SetLinkState:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cc5YGIqW.s:777    .text.LAN8742_SetLinkState:00000000 LAN8742_SetLinkState
C:\Users\<USER>\AppData\Local\Temp\cc5YGIqW.s:898    .text.LAN8742_EnableLoopbackMode:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cc5YGIqW.s:904    .text.LAN8742_EnableLoopbackMode:00000000 LAN8742_EnableLoopbackMode
C:\Users\<USER>\AppData\Local\Temp\cc5YGIqW.s:985    .text.LAN8742_DisableLoopbackMode:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cc5YGIqW.s:991    .text.LAN8742_DisableLoopbackMode:00000000 LAN8742_DisableLoopbackMode
C:\Users\<USER>\AppData\Local\Temp\cc5YGIqW.s:1072   .text.LAN8742_EnableIT:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cc5YGIqW.s:1078   .text.LAN8742_EnableIT:00000000 LAN8742_EnableIT
C:\Users\<USER>\AppData\Local\Temp\cc5YGIqW.s:1164   .text.LAN8742_DisableIT:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cc5YGIqW.s:1170   .text.LAN8742_DisableIT:00000000 LAN8742_DisableIT
C:\Users\<USER>\AppData\Local\Temp\cc5YGIqW.s:1256   .text.LAN8742_ClearIT:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cc5YGIqW.s:1262   .text.LAN8742_ClearIT:00000000 LAN8742_ClearIT
C:\Users\<USER>\AppData\Local\Temp\cc5YGIqW.s:1324   .text.LAN8742_GetITStatus:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cc5YGIqW.s:1330   .text.LAN8742_GetITStatus:00000000 LAN8742_GetITStatus

NO UNDEFINED SYMBOLS
