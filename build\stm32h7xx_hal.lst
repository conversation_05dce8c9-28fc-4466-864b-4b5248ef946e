ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s 			page 1


   1              		.cpu cortex-m7
   2              		.arch armv7e-m
   3              		.fpu fpv5-d16
   4              		.eabi_attribute 28, 1
   5              		.eabi_attribute 20, 1
   6              		.eabi_attribute 21, 1
   7              		.eabi_attribute 23, 3
   8              		.eabi_attribute 24, 1
   9              		.eabi_attribute 25, 1
  10              		.eabi_attribute 26, 1
  11              		.eabi_attribute 30, 1
  12              		.eabi_attribute 34, 1
  13              		.eabi_attribute 18, 4
  14              		.file	"stm32h7xx_hal.c"
  15              		.text
  16              	.Ltext0:
  17              		.cfi_sections	.debug_frame
  18              		.file 1 "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c"
  19              		.section	.text.HAL_MspInit,"ax",%progbits
  20              		.align	1
  21              		.weak	HAL_MspInit
  22              		.syntax unified
  23              		.thumb
  24              		.thumb_func
  26              	HAL_MspInit:
  27              	.LFB146:
   1:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /**
   2:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   ******************************************************************************
   3:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @file    stm32h7xx_hal.c
   4:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * <AUTHOR> Application Team
   5:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @brief   HAL module driver.
   6:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *          This is the common part of the HAL initialization
   7:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *
   8:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   ******************************************************************************
   9:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @attention
  10:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *
  11:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * Copyright (c) 2017 STMicroelectronics.
  12:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * All rights reserved.
  13:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *
  14:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * This software is licensed under terms that can be found in the LICENSE file
  15:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * in the root directory of this software component.
  16:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * If no LICENSE file comes with this software, it is provided AS-IS.
  17:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *
  18:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   ******************************************************************************
  19:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   @verbatim
  20:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   ==============================================================================
  21:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****                      ##### How to use this driver #####
  22:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   ==============================================================================
  23:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     [..]
  24:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     The common HAL driver contains a set of generic and common APIs that can be
  25:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     used by the PPP peripheral drivers and the user to start using the HAL.
  26:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     [..]
  27:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     The HAL contains two APIs' categories:
  28:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****          (+) Common HAL APIs
  29:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****          (+) Services HAL APIs
  30:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
  31:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   @endverbatim
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s 			page 2


  32:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   ******************************************************************************
  33:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   */
  34:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
  35:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /* Includes ------------------------------------------------------------------*/
  36:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** #include "stm32h7xx_hal.h"
  37:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
  38:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /** @addtogroup STM32H7xx_HAL_Driver
  39:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @{
  40:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   */
  41:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
  42:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /** @defgroup HAL  HAL
  43:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @brief HAL module driver.
  44:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @{
  45:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   */
  46:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
  47:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /* Private typedef -----------------------------------------------------------*/
  48:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /* Private define ------------------------------------------------------------*/
  49:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /**
  50:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****  * @brief STM32H7xx HAL Driver version number
  51:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****    */
  52:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** #define __STM32H7xx_HAL_VERSION_MAIN   (0x01UL) /*!< [31:24] main version */
  53:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** #define __STM32H7xx_HAL_VERSION_SUB1   (0x0BUL) /*!< [23:16] sub1 version */
  54:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** #define __STM32H7xx_HAL_VERSION_SUB2   (0x05UL) /*!< [15:8]  sub2 version */
  55:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** #define __STM32H7xx_HAL_VERSION_RC     (0x00UL) /*!< [7:0]  release candidate */
  56:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** #define __STM32H7xx_HAL_VERSION         ((__STM32H7xx_HAL_VERSION_MAIN << 24)\
  57:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****                                         |(__STM32H7xx_HAL_VERSION_SUB1 << 16)\
  58:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****                                         |(__STM32H7xx_HAL_VERSION_SUB2 << 8 )\
  59:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****                                         |(__STM32H7xx_HAL_VERSION_RC))
  60:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
  61:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** #define IDCODE_DEVID_MASK    ((uint32_t)0x00000FFF)
  62:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** #define VREFBUF_TIMEOUT_VALUE     (uint32_t)10   /* 10 ms  */
  63:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
  64:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /* Private macro -------------------------------------------------------------*/
  65:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /* Private variables ---------------------------------------------------------*/
  66:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /* Exported variables --------------------------------------------------------*/
  67:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
  68:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /** @defgroup HAL_Exported_Variables HAL Exported Variables
  69:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @{
  70:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   */
  71:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** __IO uint32_t uwTick;
  72:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** uint32_t uwTickPrio   = (1UL << __NVIC_PRIO_BITS); /* Invalid PRIO */
  73:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** HAL_TickFreqTypeDef uwTickFreq = HAL_TICK_FREQ_DEFAULT;  /* 1KHz */
  74:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /**
  75:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @}
  76:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   */
  77:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
  78:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /* Private function prototypes -----------------------------------------------*/
  79:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /* Private functions ---------------------------------------------------------*/
  80:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
  81:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /** @addtogroup HAL_Exported_Functions
  82:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @{
  83:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   */
  84:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
  85:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /** @addtogroup HAL_Group1
  86:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****  *  @brief    Initialization and de-initialization functions
  87:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****  *
  88:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** @verbatim
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s 			page 3


  89:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****  ===============================================================================
  90:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****               ##### Initialization and de-initialization functions #####
  91:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****  ===============================================================================
  92:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     [..]  This section provides functions allowing to:
  93:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****       (+) Initializes the Flash interface the NVIC allocation and initial clock
  94:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****           configuration. It initializes the systick also when timeout is needed
  95:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****           and the backup domain when enabled.
  96:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****       (+) De-Initializes common part of the HAL.
  97:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****       (+) Configure The time base source to have 1ms time base with a dedicated
  98:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****           Tick interrupt priority.
  99:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****         (++) SysTick timer is used by default as source of time base, but user
 100:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****              can eventually implement his proper time base source (a general purpose
 101:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****              timer for example or other time source), keeping in mind that Time base
 102:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****              duration should be kept 1ms since PPP_TIMEOUT_VALUEs are defined and
 103:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****              handled in milliseconds basis.
 104:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****         (++) Time base configuration function (HAL_InitTick ()) is called automatically
 105:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****              at the beginning of the program after reset by HAL_Init() or at any time
 106:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****              when clock is configured, by HAL_RCC_ClockConfig().
 107:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****         (++) Source of time base is configured  to generate interrupts at regular
 108:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****              time intervals. Care must be taken if HAL_Delay() is called from a
 109:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****              peripheral ISR process, the Tick interrupt line must have higher priority
 110:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****             (numerically lower) than the peripheral interrupt. Otherwise the caller
 111:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****             ISR process will be blocked.
 112:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****        (++) functions affecting time base configurations are declared as __weak
 113:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****              to make  override possible  in case of other  implementations in user file.
 114:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** @endverbatim
 115:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @{
 116:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   */
 117:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 118:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /**
 119:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @brief  This function is used to initialize the HAL Library; it must be the first
 120:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *         instruction to be executed in the main program (before to call any other
 121:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *         HAL function), it performs the following:
 122:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *           Configures the SysTick to generate an interrupt each 1 millisecond,
 123:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *           which is clocked by the HSI (at this stage, the clock is not yet
 124:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *           configured and thus the system is running from the internal HSI at 16 MHz).
 125:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *           Set NVIC Group Priority to 4.
 126:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *           Calls the HAL_MspInit() callback function defined in user file
 127:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *           "stm32h7xx_hal_msp.c" to do the global low level hardware initialization
 128:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *
 129:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @note   SysTick is used as time base for the HAL_Delay() function, the application
 130:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *         need to ensure that the SysTick time base is always set to 1 millisecond
 131:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *         to have correct HAL operation.
 132:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @retval HAL status
 133:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   */
 134:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** HAL_StatusTypeDef HAL_Init(void)
 135:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** {
 136:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 137:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** uint32_t common_system_clock;
 138:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 139:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** #if defined(DUAL_CORE) && defined(CORE_CM4)
 140:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****    /* Configure Cortex-M4 Instruction cache through ART accelerator */
 141:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****    __HAL_RCC_ART_CLK_ENABLE();                   /* Enable the Cortex-M4 ART Clock */
 142:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****    __HAL_ART_CONFIG_BASE_ADDRESS(0x08100000UL);  /* Configure the Cortex-M4 ART Base address to the
 143:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****    __HAL_ART_ENABLE();                           /* Enable the Cortex-M4 ART */
 144:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** #endif /* DUAL_CORE &&  CORE_CM4 */
 145:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s 			page 4


 146:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   /* Set Interrupt Group Priority */
 147:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   HAL_NVIC_SetPriorityGrouping(NVIC_PRIORITYGROUP_4);
 148:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 149:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   /* Update the SystemCoreClock global variable */
 150:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** #if defined(RCC_D1CFGR_D1CPRE)
 151:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   common_system_clock = HAL_RCC_GetSysClockFreq() >> ((D1CorePrescTable[(RCC->D1CFGR & RCC_D1CFGR_D
 152:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** #else
 153:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   common_system_clock = HAL_RCC_GetSysClockFreq() >> ((D1CorePrescTable[(RCC->CDCFGR1 & RCC_CDCFGR1
 154:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** #endif
 155:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 156:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   /* Update the SystemD2Clock global variable */
 157:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** #if defined(RCC_D1CFGR_HPRE)
 158:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   SystemD2Clock = (common_system_clock >> ((D1CorePrescTable[(RCC->D1CFGR & RCC_D1CFGR_HPRE)>> RCC_
 159:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** #else
 160:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   SystemD2Clock = (common_system_clock >> ((D1CorePrescTable[(RCC->CDCFGR1 & RCC_CDCFGR1_HPRE)>> RC
 161:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** #endif
 162:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 163:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** #if defined(DUAL_CORE) && defined(CORE_CM4)
 164:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   SystemCoreClock = SystemD2Clock;
 165:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** #else
 166:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   SystemCoreClock = common_system_clock;
 167:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** #endif /* DUAL_CORE && CORE_CM4 */
 168:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 169:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   /* Use systick as time base source and configure 1ms tick (default clock after Reset is HSI) */
 170:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   if(HAL_InitTick(TICK_INT_PRIORITY) != HAL_OK)
 171:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   {
 172:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     return HAL_ERROR;
 173:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   }
 174:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 175:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   /* Init the low level hardware */
 176:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   HAL_MspInit();
 177:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 178:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   /* Return function status */
 179:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   return HAL_OK;
 180:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** }
 181:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 182:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /**
 183:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @brief  This function de-Initializes common part of the HAL and stops the systick.
 184:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *         This function is optional.
 185:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @retval HAL status
 186:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   */
 187:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** HAL_StatusTypeDef HAL_DeInit(void)
 188:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** {
 189:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   /* Reset of all peripherals */
 190:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   __HAL_RCC_AHB3_FORCE_RESET();
 191:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   __HAL_RCC_AHB3_RELEASE_RESET();
 192:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 193:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   __HAL_RCC_AHB1_FORCE_RESET();
 194:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   __HAL_RCC_AHB1_RELEASE_RESET();
 195:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 196:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   __HAL_RCC_AHB2_FORCE_RESET();
 197:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   __HAL_RCC_AHB2_RELEASE_RESET();
 198:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 199:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   __HAL_RCC_AHB4_FORCE_RESET();
 200:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****  __HAL_RCC_AHB4_RELEASE_RESET();
 201:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 202:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   __HAL_RCC_APB3_FORCE_RESET();
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s 			page 5


 203:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   __HAL_RCC_APB3_RELEASE_RESET();
 204:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 205:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   __HAL_RCC_APB1L_FORCE_RESET();
 206:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   __HAL_RCC_APB1L_RELEASE_RESET();
 207:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 208:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   __HAL_RCC_APB1H_FORCE_RESET();
 209:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   __HAL_RCC_APB1H_RELEASE_RESET();
 210:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 211:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****    __HAL_RCC_APB2_FORCE_RESET();
 212:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****    __HAL_RCC_APB2_RELEASE_RESET();
 213:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 214:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   __HAL_RCC_APB4_FORCE_RESET();
 215:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   __HAL_RCC_APB4_RELEASE_RESET();
 216:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 217:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   /* De-Init the low level hardware */
 218:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   HAL_MspDeInit();
 219:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 220:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   /* Return function status */
 221:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   return HAL_OK;
 222:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** }
 223:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 224:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /**
 225:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @brief  Initializes the MSP.
 226:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @retval None
 227:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   */
 228:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** __weak void HAL_MspInit(void)
 229:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** {
  28              		.loc 1 229 1 view -0
  29              		.cfi_startproc
  30              		@ args = 0, pretend = 0, frame = 0
  31              		@ frame_needed = 0, uses_anonymous_args = 0
  32              		@ link register save eliminated.
 230:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   /* NOTE : This function Should not be modified, when the callback is needed,
 231:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****             the HAL_MspInit could be implemented in the user file
 232:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****    */
 233:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** }
  33              		.loc 1 233 1 view .LVU1
  34 0000 7047     		bx	lr
  35              		.cfi_endproc
  36              	.LFE146:
  38              		.section	.text.HAL_MspDeInit,"ax",%progbits
  39              		.align	1
  40              		.weak	HAL_MspDeInit
  41              		.syntax unified
  42              		.thumb
  43              		.thumb_func
  45              	HAL_MspDeInit:
  46              	.LFB147:
 234:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 235:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /**
 236:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @brief  DeInitializes the MSP.
 237:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @retval None
 238:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   */
 239:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** __weak void HAL_MspDeInit(void)
 240:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** {
  47              		.loc 1 240 1 view -0
  48              		.cfi_startproc
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s 			page 6


  49              		@ args = 0, pretend = 0, frame = 0
  50              		@ frame_needed = 0, uses_anonymous_args = 0
  51              		@ link register save eliminated.
 241:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   /* NOTE : This function Should not be modified, when the callback is needed,
 242:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****             the HAL_MspDeInit could be implemented in the user file
 243:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****    */
 244:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** }
  52              		.loc 1 244 1 view .LVU3
  53 0000 7047     		bx	lr
  54              		.cfi_endproc
  55              	.LFE147:
  57              		.section	.text.HAL_DeInit,"ax",%progbits
  58              		.align	1
  59              		.global	HAL_DeInit
  60              		.syntax unified
  61              		.thumb
  62              		.thumb_func
  64              	HAL_DeInit:
  65              	.LFB145:
 188:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   /* Reset of all peripherals */
  66              		.loc 1 188 1 view -0
  67              		.cfi_startproc
  68              		@ args = 0, pretend = 0, frame = 0
  69              		@ frame_needed = 0, uses_anonymous_args = 0
  70 0000 10B5     		push	{r4, lr}
  71              	.LCFI0:
  72              		.cfi_def_cfa_offset 8
  73              		.cfi_offset 4, -8
  74              		.cfi_offset 14, -4
 190:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   __HAL_RCC_AHB3_RELEASE_RESET();
  75              		.loc 1 190 3 view .LVU5
  76 0002 1B4B     		ldr	r3, .L5
  77 0004 1B4A     		ldr	r2, .L5+4
  78 0006 DA67     		str	r2, [r3, #124]
 191:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
  79              		.loc 1 191 3 view .LVU6
  80 0008 0024     		movs	r4, #0
  81 000a DC67     		str	r4, [r3, #124]
 193:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   __HAL_RCC_AHB1_RELEASE_RESET();
  82              		.loc 1 193 3 view .LVU7
  83 000c 1A4A     		ldr	r2, .L5+8
  84 000e C3F88020 		str	r2, [r3, #128]
 194:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
  85              		.loc 1 194 3 view .LVU8
  86 0012 C3F88040 		str	r4, [r3, #128]
 196:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   __HAL_RCC_AHB2_RELEASE_RESET();
  87              		.loc 1 196 3 view .LVU9
  88 0016 194A     		ldr	r2, .L5+12
  89 0018 C3F88420 		str	r2, [r3, #132]
 197:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
  90              		.loc 1 197 3 view .LVU10
  91 001c C3F88440 		str	r4, [r3, #132]
 199:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****  __HAL_RCC_AHB4_RELEASE_RESET();
  92              		.loc 1 199 3 view .LVU11
  93 0020 174A     		ldr	r2, .L5+16
  94 0022 C3F88820 		str	r2, [r3, #136]
 200:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s 			page 7


  95              		.loc 1 200 2 view .LVU12
  96 0026 C3F88840 		str	r4, [r3, #136]
 202:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   __HAL_RCC_APB3_RELEASE_RESET();
  97              		.loc 1 202 3 view .LVU13
  98 002a 0822     		movs	r2, #8
  99 002c C3F88C20 		str	r2, [r3, #140]
 203:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 100              		.loc 1 203 3 view .LVU14
 101 0030 C3F88C40 		str	r4, [r3, #140]
 205:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   __HAL_RCC_APB1L_RELEASE_RESET();
 102              		.loc 1 205 3 view .LVU15
 103 0034 134A     		ldr	r2, .L5+20
 104 0036 C3F89020 		str	r2, [r3, #144]
 206:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 105              		.loc 1 206 3 view .LVU16
 106 003a C3F89040 		str	r4, [r3, #144]
 208:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   __HAL_RCC_APB1H_RELEASE_RESET();
 107              		.loc 1 208 3 view .LVU17
 108 003e 124A     		ldr	r2, .L5+24
 109 0040 C3F89420 		str	r2, [r3, #148]
 209:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 110              		.loc 1 209 3 view .LVU18
 111 0044 C3F89440 		str	r4, [r3, #148]
 211:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****    __HAL_RCC_APB2_RELEASE_RESET();
 112              		.loc 1 211 4 view .LVU19
 113 0048 02F17552 		add	r2, r2, #1027604480
 114 004c 02F5B912 		add	r2, r2, #1515520
 115 0050 02F6BD72 		addw	r2, r2, #4029
 116 0054 C3F89820 		str	r2, [r3, #152]
 212:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 117              		.loc 1 212 4 view .LVU20
 118 0058 C3F89840 		str	r4, [r3, #152]
 214:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   __HAL_RCC_APB4_RELEASE_RESET();
 119              		.loc 1 214 3 view .LVU21
 120 005c 0B4A     		ldr	r2, .L5+28
 121 005e C3F89C20 		str	r2, [r3, #156]
 215:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 122              		.loc 1 215 3 view .LVU22
 123 0062 C3F89C40 		str	r4, [r3, #156]
 218:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 124              		.loc 1 218 3 view .LVU23
 125 0066 FFF7FEFF 		bl	HAL_MspDeInit
 126              	.LVL0:
 221:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** }
 127              		.loc 1 221 3 view .LVU24
 222:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 128              		.loc 1 222 1 is_stmt 0 view .LVU25
 129 006a 2046     		mov	r0, r4
 130 006c 10BD     		pop	{r4, pc}
 131              	.L6:
 132 006e 00BF     		.align	2
 133              	.L5:
 134 0070 00440258 		.word	1476543488
 135 0074 1150E900 		.word	15290385
 136 0078 23800002 		.word	33587235
 137 007c 71020300 		.word	197233
 138 0080 FF062803 		.word	52954879
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s 			page 8


 139 0084 FFC3FFEA 		.word	-*********
 140 0088 36010003 		.word	50331958
 141 008c AADE2004 		.word	69263018
 142              		.cfi_endproc
 143              	.LFE145:
 145              		.section	.text.HAL_InitTick,"ax",%progbits
 146              		.align	1
 147              		.weak	HAL_InitTick
 148              		.syntax unified
 149              		.thumb
 150              		.thumb_func
 152              	HAL_InitTick:
 153              	.LVL1:
 154              	.LFB148:
 245:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 246:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /**
 247:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @brief This function configures the source of the time base.
 248:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *        The time source is configured  to have 1ms time base with a dedicated
 249:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *        Tick interrupt priority.
 250:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @note This function is called  automatically at the beginning of program after
 251:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *       reset by HAL_Init() or at any time when clock is reconfigured  by HAL_RCC_ClockConfig().
 252:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @note In the default implementation, SysTick timer is the source of time base.
 253:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *       It is used to generate interrupts at regular time intervals.
 254:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *       Care must be taken if HAL_Delay() is called from a peripheral ISR process,
 255:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *       the SysTick interrupt must have higher priority (numerically lower)
 256:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *       than the peripheral interrupt. Otherwise the caller ISR process will be blocked.
 257:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *       The function is declared as __weak  to be overwritten  in case of other
 258:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *       implementation  in user file.
 259:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @param TickPriority: Tick interrupt priority.
 260:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @retval HAL status
 261:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   */
 262:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** __weak HAL_StatusTypeDef HAL_InitTick(uint32_t TickPriority)
 263:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** {
 155              		.loc 1 263 1 is_stmt 1 view -0
 156              		.cfi_startproc
 157              		@ args = 0, pretend = 0, frame = 0
 158              		@ frame_needed = 0, uses_anonymous_args = 0
 264:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   /* Check uwTickFreq for MisraC 2012 (even if uwTickFreq is a enum type that don't take the value 
 265:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   if((uint32_t)uwTickFreq == 0UL)
 159              		.loc 1 265 3 view .LVU27
 160              		.loc 1 265 27 is_stmt 0 view .LVU28
 161 0000 104B     		ldr	r3, .L18
 162 0002 1B78     		ldrb	r3, [r3]	@ zero_extendqisi2
 163              		.loc 1 265 5 view .LVU29
 164 0004 0BB9     		cbnz	r3, .L16
 266:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   {
 267:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     return HAL_ERROR;
 165              		.loc 1 267 12 view .LVU30
 166 0006 0120     		movs	r0, #1
 167              	.LVL2:
 268:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   }
 269:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 270:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     /* Configure the SysTick to have interrupt in 1ms time basis*/
 271:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     if (HAL_SYSTICK_Config(SystemCoreClock / (1000UL / (uint32_t)uwTickFreq)) > 0U)
 272:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     {
 273:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****       return HAL_ERROR;
 274:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     }
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s 			page 9


 275:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 276:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   /* Configure the SysTick IRQ priority */
 277:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   if (TickPriority < (1UL << __NVIC_PRIO_BITS))
 278:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   {
 279:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     HAL_NVIC_SetPriority(SysTick_IRQn, TickPriority, 0U);
 280:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     uwTickPrio = TickPriority;
 281:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   }
 282:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   else
 283:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   {
 284:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     return HAL_ERROR;
 285:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   }
 286:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 287:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   /* Return function status */
 288:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   return HAL_OK;
 289:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** }
 168              		.loc 1 289 1 view .LVU31
 169 0008 7047     		bx	lr
 170              	.LVL3:
 171              	.L16:
 263:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   /* Check uwTickFreq for MisraC 2012 (even if uwTickFreq is a enum type that don't take the value 
 172              		.loc 1 263 1 view .LVU32
 173 000a 10B5     		push	{r4, lr}
 174              	.LCFI1:
 175              		.cfi_def_cfa_offset 8
 176              		.cfi_offset 4, -8
 177              		.cfi_offset 14, -4
 178 000c 0446     		mov	r4, r0
 271:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     {
 179              		.loc 1 271 5 is_stmt 1 view .LVU33
 271:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     {
 180              		.loc 1 271 54 is_stmt 0 view .LVU34
 181 000e 4FF47A70 		mov	r0, #1000
 182              	.LVL4:
 271:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     {
 183              		.loc 1 271 54 view .LVU35
 184 0012 B0FBF3F3 		udiv	r3, r0, r3
 271:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     {
 185              		.loc 1 271 9 view .LVU36
 186 0016 0C4A     		ldr	r2, .L18+4
 187 0018 1068     		ldr	r0, [r2]
 188 001a B0FBF3F0 		udiv	r0, r0, r3
 189 001e FFF7FEFF 		bl	HAL_SYSTICK_Config
 190              	.LVL5:
 271:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     {
 191              		.loc 1 271 8 discriminator 1 view .LVU37
 192 0022 68B9     		cbnz	r0, .L10
 277:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   {
 193              		.loc 1 277 3 is_stmt 1 view .LVU38
 277:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   {
 194              		.loc 1 277 6 is_stmt 0 view .LVU39
 195 0024 0F2C     		cmp	r4, #15
 196 0026 01D9     		bls	.L17
 284:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   }
 197              		.loc 1 284 12 view .LVU40
 198 0028 0120     		movs	r0, #1
 199 002a 0AE0     		b	.L8
 200              	.L17:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s 			page 10


 279:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     uwTickPrio = TickPriority;
 201              		.loc 1 279 5 is_stmt 1 view .LVU41
 202 002c 0022     		movs	r2, #0
 203 002e 2146     		mov	r1, r4
 204 0030 4FF0FF30 		mov	r0, #-1
 205 0034 FFF7FEFF 		bl	HAL_NVIC_SetPriority
 206              	.LVL6:
 280:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   }
 207              		.loc 1 280 5 view .LVU42
 280:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   }
 208              		.loc 1 280 16 is_stmt 0 view .LVU43
 209 0038 044B     		ldr	r3, .L18+8
 210 003a 1C60     		str	r4, [r3]
 288:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** }
 211              		.loc 1 288 3 is_stmt 1 view .LVU44
 288:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** }
 212              		.loc 1 288 10 is_stmt 0 view .LVU45
 213 003c 0020     		movs	r0, #0
 214 003e 00E0     		b	.L8
 215              	.L10:
 273:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     }
 216              		.loc 1 273 14 view .LVU46
 217 0040 0120     		movs	r0, #1
 218              	.L8:
 219              		.loc 1 289 1 view .LVU47
 220 0042 10BD     		pop	{r4, pc}
 221              	.LVL7:
 222              	.L19:
 223              		.loc 1 289 1 view .LVU48
 224              		.align	2
 225              	.L18:
 226 0044 00000000 		.word	uwTickFreq
 227 0048 00000000 		.word	SystemCoreClock
 228 004c 00000000 		.word	uwTickPrio
 229              		.cfi_endproc
 230              	.LFE148:
 232              		.section	.text.HAL_Init,"ax",%progbits
 233              		.align	1
 234              		.global	HAL_Init
 235              		.syntax unified
 236              		.thumb
 237              		.thumb_func
 239              	HAL_Init:
 240              	.LFB144:
 135:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 241              		.loc 1 135 1 is_stmt 1 view -0
 242              		.cfi_startproc
 243              		@ args = 0, pretend = 0, frame = 0
 244              		@ frame_needed = 0, uses_anonymous_args = 0
 245 0000 10B5     		push	{r4, lr}
 246              	.LCFI2:
 247              		.cfi_def_cfa_offset 8
 248              		.cfi_offset 4, -8
 249              		.cfi_offset 14, -4
 137:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 250              		.loc 1 137 1 view .LVU50
 147:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s 			page 11


 251              		.loc 1 147 3 view .LVU51
 252 0002 0320     		movs	r0, #3
 253 0004 FFF7FEFF 		bl	HAL_NVIC_SetPriorityGrouping
 254              	.LVL8:
 151:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** #else
 255              		.loc 1 151 3 view .LVU52
 151:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** #else
 256              		.loc 1 151 25 is_stmt 0 view .LVU53
 257 0008 FFF7FEFF 		bl	HAL_RCC_GetSysClockFreq
 258              	.LVL9:
 151:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** #else
 259              		.loc 1 151 77 discriminator 1 view .LVU54
 260 000c 0F49     		ldr	r1, .L25
 261 000e 8B69     		ldr	r3, [r1, #24]
 151:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** #else
 262              		.loc 1 151 106 discriminator 1 view .LVU55
 263 0010 C3F30323 		ubfx	r3, r3, #8, #4
 151:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** #else
 264              		.loc 1 151 72 discriminator 1 view .LVU56
 265 0014 0E4A     		ldr	r2, .L25+4
 266 0016 D35C     		ldrb	r3, [r2, r3]	@ zero_extendqisi2
 151:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** #else
 267              		.loc 1 151 133 discriminator 1 view .LVU57
 268 0018 03F01F03 		and	r3, r3, #31
 151:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** #else
 269              		.loc 1 151 23 discriminator 1 view .LVU58
 270 001c D840     		lsrs	r0, r0, r3
 271              	.LVL10:
 158:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** #else
 272              		.loc 1 158 3 is_stmt 1 view .LVU59
 158:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** #else
 273              		.loc 1 158 66 is_stmt 0 view .LVU60
 274 001e 8B69     		ldr	r3, [r1, #24]
 158:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** #else
 275              		.loc 1 158 93 view .LVU61
 276 0020 03F00F03 		and	r3, r3, #15
 158:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** #else
 277              		.loc 1 158 61 view .LVU62
 278 0024 D35C     		ldrb	r3, [r2, r3]	@ zero_extendqisi2
 158:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** #else
 279              		.loc 1 158 118 view .LVU63
 280 0026 03F01F03 		and	r3, r3, #31
 158:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** #else
 281              		.loc 1 158 40 view .LVU64
 282 002a 20FA03F3 		lsr	r3, r0, r3
 158:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** #else
 283              		.loc 1 158 17 view .LVU65
 284 002e 094A     		ldr	r2, .L25+8
 285 0030 1360     		str	r3, [r2]
 166:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** #endif /* DUAL_CORE && CORE_CM4 */
 286              		.loc 1 166 3 is_stmt 1 view .LVU66
 166:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** #endif /* DUAL_CORE && CORE_CM4 */
 287              		.loc 1 166 19 is_stmt 0 view .LVU67
 288 0032 094B     		ldr	r3, .L25+12
 289 0034 1860     		str	r0, [r3]
 170:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   {
 290              		.loc 1 170 3 is_stmt 1 view .LVU68
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s 			page 12


 170:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   {
 291              		.loc 1 170 6 is_stmt 0 view .LVU69
 292 0036 0F20     		movs	r0, #15
 293              	.LVL11:
 170:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   {
 294              		.loc 1 170 6 view .LVU70
 295 0038 FFF7FEFF 		bl	HAL_InitTick
 296              	.LVL12:
 170:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   {
 297              		.loc 1 170 5 discriminator 1 view .LVU71
 298 003c 10B1     		cbz	r0, .L24
 172:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   }
 299              		.loc 1 172 12 view .LVU72
 300 003e 0124     		movs	r4, #1
 301              	.L21:
 180:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 302              		.loc 1 180 1 view .LVU73
 303 0040 2046     		mov	r0, r4
 304 0042 10BD     		pop	{r4, pc}
 305              	.L24:
 306 0044 0446     		mov	r4, r0
 176:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 307              		.loc 1 176 3 is_stmt 1 view .LVU74
 308 0046 FFF7FEFF 		bl	HAL_MspInit
 309              	.LVL13:
 179:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** }
 310              		.loc 1 179 3 view .LVU75
 179:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** }
 311              		.loc 1 179 10 is_stmt 0 view .LVU76
 312 004a F9E7     		b	.L21
 313              	.L26:
 314              		.align	2
 315              	.L25:
 316 004c 00440258 		.word	1476543488
 317 0050 00000000 		.word	D1CorePrescTable
 318 0054 00000000 		.word	SystemD2Clock
 319 0058 00000000 		.word	SystemCoreClock
 320              		.cfi_endproc
 321              	.LFE144:
 323              		.section	.text.HAL_IncTick,"ax",%progbits
 324              		.align	1
 325              		.weak	HAL_IncTick
 326              		.syntax unified
 327              		.thumb
 328              		.thumb_func
 330              	HAL_IncTick:
 331              	.LFB149:
 290:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 291:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /**
 292:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @}
 293:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   */
 294:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 295:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /** @addtogroup HAL_Group2
 296:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****  *  @brief    HAL Control functions
 297:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****  *
 298:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** @verbatim
 299:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****  ===============================================================================
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s 			page 13


 300:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****                       ##### HAL Control functions #####
 301:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****  ===============================================================================
 302:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     [..]  This section provides functions allowing to:
 303:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****       (+) Provide a tick value in millisecond
 304:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****       (+) Provide a blocking delay in millisecond
 305:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****       (+) Suspend the time base source interrupt
 306:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****       (+) Resume the time base source interrupt
 307:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****       (+) Get the HAL API driver version
 308:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****       (+) Get the device identifier
 309:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****       (+) Get the device revision identifier
 310:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****       (+) Enable/Disable Debug module during SLEEP mode
 311:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****       (+) Enable/Disable Debug module during STOP mode
 312:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****       (+) Enable/Disable Debug module during STANDBY mode
 313:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 314:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** @endverbatim
 315:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @{
 316:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   */
 317:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 318:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /**
 319:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @brief This function is called to increment  a global variable "uwTick"
 320:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *        used as application time base.
 321:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @note In the default implementation, this variable is incremented each 1ms
 322:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *       in Systick ISR.
 323:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****  * @note This function is declared as __weak to be overwritten in case of other
 324:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *      implementations in user file.
 325:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @retval None
 326:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   */
 327:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** __weak void HAL_IncTick(void)
 328:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** {
 332              		.loc 1 328 1 is_stmt 1 view -0
 333              		.cfi_startproc
 334              		@ args = 0, pretend = 0, frame = 0
 335              		@ frame_needed = 0, uses_anonymous_args = 0
 336              		@ link register save eliminated.
 329:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   uwTick += (uint32_t)uwTickFreq;
 337              		.loc 1 329 3 view .LVU78
 338              		.loc 1 329 13 is_stmt 0 view .LVU79
 339 0000 034B     		ldr	r3, .L28
 340 0002 1B78     		ldrb	r3, [r3]	@ zero_extendqisi2
 341              		.loc 1 329 10 view .LVU80
 342 0004 034A     		ldr	r2, .L28+4
 343 0006 1168     		ldr	r1, [r2]
 344 0008 0B44     		add	r3, r3, r1
 345 000a 1360     		str	r3, [r2]
 330:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** }
 346              		.loc 1 330 1 view .LVU81
 347 000c 7047     		bx	lr
 348              	.L29:
 349 000e 00BF     		.align	2
 350              	.L28:
 351 0010 00000000 		.word	uwTickFreq
 352 0014 00000000 		.word	uwTick
 353              		.cfi_endproc
 354              	.LFE149:
 356              		.section	.text.HAL_GetTick,"ax",%progbits
 357              		.align	1
 358              		.weak	HAL_GetTick
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s 			page 14


 359              		.syntax unified
 360              		.thumb
 361              		.thumb_func
 363              	HAL_GetTick:
 364              	.LFB150:
 331:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 332:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /**
 333:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @brief Provides a tick value in millisecond.
 334:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @note This function is declared as __weak to be overwritten in case of other
 335:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *       implementations in user file.
 336:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @retval tick value
 337:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   */
 338:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** __weak uint32_t HAL_GetTick(void)
 339:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** {
 365              		.loc 1 339 1 is_stmt 1 view -0
 366              		.cfi_startproc
 367              		@ args = 0, pretend = 0, frame = 0
 368              		@ frame_needed = 0, uses_anonymous_args = 0
 369              		@ link register save eliminated.
 340:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   return uwTick;
 370              		.loc 1 340 3 view .LVU83
 371              		.loc 1 340 10 is_stmt 0 view .LVU84
 372 0000 014B     		ldr	r3, .L31
 373 0002 1868     		ldr	r0, [r3]
 341:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** }
 374              		.loc 1 341 1 view .LVU85
 375 0004 7047     		bx	lr
 376              	.L32:
 377 0006 00BF     		.align	2
 378              	.L31:
 379 0008 00000000 		.word	uwTick
 380              		.cfi_endproc
 381              	.LFE150:
 383              		.section	.text.HAL_GetTickPrio,"ax",%progbits
 384              		.align	1
 385              		.global	HAL_GetTickPrio
 386              		.syntax unified
 387              		.thumb
 388              		.thumb_func
 390              	HAL_GetTickPrio:
 391              	.LFB151:
 342:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 343:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /**
 344:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @brief This function returns a tick priority.
 345:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @retval tick priority
 346:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   */
 347:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** uint32_t HAL_GetTickPrio(void)
 348:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** {
 392              		.loc 1 348 1 is_stmt 1 view -0
 393              		.cfi_startproc
 394              		@ args = 0, pretend = 0, frame = 0
 395              		@ frame_needed = 0, uses_anonymous_args = 0
 396              		@ link register save eliminated.
 349:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   return uwTickPrio;
 397              		.loc 1 349 3 view .LVU87
 350:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** }
 398              		.loc 1 350 1 is_stmt 0 view .LVU88
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s 			page 15


 399 0000 014B     		ldr	r3, .L34
 400 0002 1868     		ldr	r0, [r3]
 401 0004 7047     		bx	lr
 402              	.L35:
 403 0006 00BF     		.align	2
 404              	.L34:
 405 0008 00000000 		.word	uwTickPrio
 406              		.cfi_endproc
 407              	.LFE151:
 409              		.section	.text.HAL_SetTickFreq,"ax",%progbits
 410              		.align	1
 411              		.global	HAL_SetTickFreq
 412              		.syntax unified
 413              		.thumb
 414              		.thumb_func
 416              	HAL_SetTickFreq:
 417              	.LVL14:
 418              	.LFB152:
 351:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 352:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /**
 353:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @brief Set new tick Freq.
 354:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @retval Status
 355:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   */
 356:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** HAL_StatusTypeDef HAL_SetTickFreq(HAL_TickFreqTypeDef Freq)
 357:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** {
 419              		.loc 1 357 1 is_stmt 1 view -0
 420              		.cfi_startproc
 421              		@ args = 0, pretend = 0, frame = 0
 422              		@ frame_needed = 0, uses_anonymous_args = 0
 423              		.loc 1 357 1 is_stmt 0 view .LVU90
 424 0000 10B5     		push	{r4, lr}
 425              	.LCFI3:
 426              		.cfi_def_cfa_offset 8
 427              		.cfi_offset 4, -8
 428              		.cfi_offset 14, -4
 358:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   HAL_StatusTypeDef status  = HAL_OK;
 429              		.loc 1 358 3 is_stmt 1 view .LVU91
 430              	.LVL15:
 359:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   HAL_TickFreqTypeDef prevTickFreq;
 431              		.loc 1 359 3 view .LVU92
 360:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 361:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   assert_param(IS_TICKFREQ(Freq));
 432              		.loc 1 361 3 view .LVU93
 362:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 363:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   if (uwTickFreq != Freq)
 433              		.loc 1 363 3 view .LVU94
 434              		.loc 1 363 18 is_stmt 0 view .LVU95
 435 0002 084B     		ldr	r3, .L41
 436 0004 1C78     		ldrb	r4, [r3]	@ zero_extendqisi2
 437              		.loc 1 363 6 view .LVU96
 438 0006 8442     		cmp	r4, r0
 439 0008 01D1     		bne	.L40
 358:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   HAL_TickFreqTypeDef prevTickFreq;
 440              		.loc 1 358 21 view .LVU97
 441 000a 0020     		movs	r0, #0
 442              	.LVL16:
 443              	.L37:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s 			page 16


 364:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   {
 365:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 366:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     /* Back up uwTickFreq frequency */
 367:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     prevTickFreq = uwTickFreq;
 368:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 369:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     /* Update uwTickFreq global variable used by HAL_InitTick() */
 370:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     uwTickFreq = Freq;
 371:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 372:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     /* Apply the new tick Freq  */
 373:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     status = HAL_InitTick(uwTickPrio);
 374:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     if (status != HAL_OK)
 375:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     {
 376:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****       /* Restore previous tick frequency */
 377:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****       uwTickFreq = prevTickFreq;
 378:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     }
 379:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   }
 380:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 381:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   return status;
 444              		.loc 1 381 3 is_stmt 1 view .LVU98
 382:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** }
 445              		.loc 1 382 1 is_stmt 0 view .LVU99
 446 000c 10BD     		pop	{r4, pc}
 447              	.LVL17:
 448              	.L40:
 367:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 449              		.loc 1 367 5 is_stmt 1 view .LVU100
 370:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 450              		.loc 1 370 5 view .LVU101
 370:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 451              		.loc 1 370 16 is_stmt 0 view .LVU102
 452 000e 1870     		strb	r0, [r3]
 373:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     if (status != HAL_OK)
 453              		.loc 1 373 5 is_stmt 1 view .LVU103
 373:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     if (status != HAL_OK)
 454              		.loc 1 373 14 is_stmt 0 view .LVU104
 455 0010 054B     		ldr	r3, .L41+4
 456 0012 1868     		ldr	r0, [r3]
 457              	.LVL18:
 373:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     if (status != HAL_OK)
 458              		.loc 1 373 14 view .LVU105
 459 0014 FFF7FEFF 		bl	HAL_InitTick
 460              	.LVL19:
 374:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     {
 461              		.loc 1 374 5 is_stmt 1 view .LVU106
 374:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     {
 462              		.loc 1 374 8 is_stmt 0 view .LVU107
 463 0018 0028     		cmp	r0, #0
 464 001a F7D0     		beq	.L37
 377:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     }
 465              		.loc 1 377 7 is_stmt 1 view .LVU108
 377:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     }
 466              		.loc 1 377 18 is_stmt 0 view .LVU109
 467 001c 014B     		ldr	r3, .L41
 468 001e 1C70     		strb	r4, [r3]
 469 0020 F4E7     		b	.L37
 470              	.L42:
 471 0022 00BF     		.align	2
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s 			page 17


 472              	.L41:
 473 0024 00000000 		.word	uwTickFreq
 474 0028 00000000 		.word	uwTickPrio
 475              		.cfi_endproc
 476              	.LFE152:
 478              		.section	.text.HAL_GetTickFreq,"ax",%progbits
 479              		.align	1
 480              		.global	HAL_GetTickFreq
 481              		.syntax unified
 482              		.thumb
 483              		.thumb_func
 485              	HAL_GetTickFreq:
 486              	.LFB153:
 383:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 384:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /**
 385:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @brief Return tick frequency.
 386:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @retval Tick frequency.
 387:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *         Value of @ref HAL_TickFreqTypeDef.
 388:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   */
 389:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** HAL_TickFreqTypeDef HAL_GetTickFreq(void)
 390:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** {
 487              		.loc 1 390 1 is_stmt 1 view -0
 488              		.cfi_startproc
 489              		@ args = 0, pretend = 0, frame = 0
 490              		@ frame_needed = 0, uses_anonymous_args = 0
 491              		@ link register save eliminated.
 391:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   return uwTickFreq;
 492              		.loc 1 391 3 view .LVU111
 392:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** }
 493              		.loc 1 392 1 is_stmt 0 view .LVU112
 494 0000 014B     		ldr	r3, .L44
 495 0002 1878     		ldrb	r0, [r3]	@ zero_extendqisi2
 496 0004 7047     		bx	lr
 497              	.L45:
 498 0006 00BF     		.align	2
 499              	.L44:
 500 0008 00000000 		.word	uwTickFreq
 501              		.cfi_endproc
 502              	.LFE153:
 504              		.section	.text.HAL_Delay,"ax",%progbits
 505              		.align	1
 506              		.weak	HAL_Delay
 507              		.syntax unified
 508              		.thumb
 509              		.thumb_func
 511              	HAL_Delay:
 512              	.LVL20:
 513              	.LFB154:
 393:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 394:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /**
 395:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @brief This function provides minimum delay (in milliseconds) based
 396:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *        on variable incremented.
 397:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @note In the default implementation , SysTick timer is the source of time base.
 398:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *       It is used to generate interrupts at regular time intervals where uwTick
 399:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *       is incremented.
 400:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @note This function is declared as __weak to be overwritten in case of other
 401:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *       implementations in user file.
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s 			page 18


 402:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @param Delay  specifies the delay time length, in milliseconds.
 403:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @retval None
 404:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   */
 405:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** __weak void HAL_Delay(uint32_t Delay)
 406:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** {
 514              		.loc 1 406 1 is_stmt 1 view -0
 515              		.cfi_startproc
 516              		@ args = 0, pretend = 0, frame = 0
 517              		@ frame_needed = 0, uses_anonymous_args = 0
 518              		.loc 1 406 1 is_stmt 0 view .LVU114
 519 0000 38B5     		push	{r3, r4, r5, lr}
 520              	.LCFI4:
 521              		.cfi_def_cfa_offset 16
 522              		.cfi_offset 3, -16
 523              		.cfi_offset 4, -12
 524              		.cfi_offset 5, -8
 525              		.cfi_offset 14, -4
 526 0002 0446     		mov	r4, r0
 407:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   uint32_t tickstart = HAL_GetTick();
 527              		.loc 1 407 3 is_stmt 1 view .LVU115
 528              		.loc 1 407 24 is_stmt 0 view .LVU116
 529 0004 FFF7FEFF 		bl	HAL_GetTick
 530              	.LVL21:
 531              		.loc 1 407 24 view .LVU117
 532 0008 0546     		mov	r5, r0
 533              	.LVL22:
 408:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   uint32_t wait = Delay;
 534              		.loc 1 408 3 is_stmt 1 view .LVU118
 409:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 410:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   /* Add a freq to guarantee minimum wait */
 411:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   if (wait < HAL_MAX_DELAY)
 535              		.loc 1 411 3 view .LVU119
 536              		.loc 1 411 6 is_stmt 0 view .LVU120
 537 000a B4F1FF3F 		cmp	r4, #-1
 538 000e 02D0     		beq	.L48
 412:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   {
 413:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     wait += (uint32_t)(uwTickFreq);
 539              		.loc 1 413 5 is_stmt 1 view .LVU121
 540              		.loc 1 413 13 is_stmt 0 view .LVU122
 541 0010 044B     		ldr	r3, .L50
 542 0012 1B78     		ldrb	r3, [r3]	@ zero_extendqisi2
 543              		.loc 1 413 10 view .LVU123
 544 0014 1C44     		add	r4, r4, r3
 545              	.LVL23:
 546              	.L48:
 414:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   }
 415:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 416:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   while ((HAL_GetTick() - tickstart) < wait)
 417:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   {
 418:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   }
 547              		.loc 1 418 3 is_stmt 1 view .LVU124
 416:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   {
 548              		.loc 1 416 38 discriminator 1 view .LVU125
 416:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   {
 549              		.loc 1 416 11 is_stmt 0 discriminator 1 view .LVU126
 550 0016 FFF7FEFF 		bl	HAL_GetTick
 551              	.LVL24:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s 			page 19


 416:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   {
 552              		.loc 1 416 25 discriminator 1 view .LVU127
 553 001a 401B     		subs	r0, r0, r5
 416:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   {
 554              		.loc 1 416 38 discriminator 1 view .LVU128
 555 001c A042     		cmp	r0, r4
 556 001e FAD3     		bcc	.L48
 419:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** }
 557              		.loc 1 419 1 view .LVU129
 558 0020 38BD     		pop	{r3, r4, r5, pc}
 559              	.LVL25:
 560              	.L51:
 561              		.loc 1 419 1 view .LVU130
 562 0022 00BF     		.align	2
 563              	.L50:
 564 0024 00000000 		.word	uwTickFreq
 565              		.cfi_endproc
 566              	.LFE154:
 568              		.section	.text.HAL_SuspendTick,"ax",%progbits
 569              		.align	1
 570              		.weak	HAL_SuspendTick
 571              		.syntax unified
 572              		.thumb
 573              		.thumb_func
 575              	HAL_SuspendTick:
 576              	.LFB155:
 420:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 421:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /**
 422:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @brief Suspend Tick increment.
 423:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @note In the default implementation , SysTick timer is the source of time base. It is
 424:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *       used to generate interrupts at regular time intervals. Once HAL_SuspendTick()
 425:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *       is called, the SysTick interrupt will be disabled and so Tick increment
 426:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *       is suspended.
 427:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @note This function is declared as __weak to be overwritten in case of other
 428:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *       implementations in user file.
 429:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @retval None
 430:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   */
 431:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** __weak void HAL_SuspendTick(void)
 432:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** {
 577              		.loc 1 432 1 is_stmt 1 view -0
 578              		.cfi_startproc
 579              		@ args = 0, pretend = 0, frame = 0
 580              		@ frame_needed = 0, uses_anonymous_args = 0
 581              		@ link register save eliminated.
 433:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   /* Disable SysTick Interrupt */
 434:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   SysTick->CTRL &= ~SysTick_CTRL_TICKINT_Msk;
 582              		.loc 1 434 3 view .LVU132
 583              		.loc 1 434 10 is_stmt 0 view .LVU133
 584 0000 4FF0E022 		mov	r2, #-536813568
 585 0004 1369     		ldr	r3, [r2, #16]
 586              		.loc 1 434 17 view .LVU134
 587 0006 23F00203 		bic	r3, r3, #2
 588 000a 1361     		str	r3, [r2, #16]
 435:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** }
 589              		.loc 1 435 1 view .LVU135
 590 000c 7047     		bx	lr
 591              		.cfi_endproc
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s 			page 20


 592              	.LFE155:
 594              		.section	.text.HAL_ResumeTick,"ax",%progbits
 595              		.align	1
 596              		.weak	HAL_ResumeTick
 597              		.syntax unified
 598              		.thumb
 599              		.thumb_func
 601              	HAL_ResumeTick:
 602              	.LFB156:
 436:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 437:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /**
 438:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @brief Resume Tick increment.
 439:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @note In the default implementation , SysTick timer is the source of time base. It is
 440:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *       used to generate interrupts at regular time intervals. Once HAL_ResumeTick()
 441:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *       is called, the SysTick interrupt will be enabled and so Tick increment
 442:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *       is resumed.
 443:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @note This function is declared as __weak to be overwritten in case of other
 444:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *       implementations in user file.
 445:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @retval None
 446:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   */
 447:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** __weak void HAL_ResumeTick(void)
 448:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** {
 603              		.loc 1 448 1 is_stmt 1 view -0
 604              		.cfi_startproc
 605              		@ args = 0, pretend = 0, frame = 0
 606              		@ frame_needed = 0, uses_anonymous_args = 0
 607              		@ link register save eliminated.
 449:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   /* Enable SysTick Interrupt */
 450:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   SysTick->CTRL  |= SysTick_CTRL_TICKINT_Msk;
 608              		.loc 1 450 3 view .LVU137
 609              		.loc 1 450 10 is_stmt 0 view .LVU138
 610 0000 4FF0E022 		mov	r2, #-536813568
 611 0004 1369     		ldr	r3, [r2, #16]
 612              		.loc 1 450 18 view .LVU139
 613 0006 43F00203 		orr	r3, r3, #2
 614 000a 1361     		str	r3, [r2, #16]
 451:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** }
 615              		.loc 1 451 1 view .LVU140
 616 000c 7047     		bx	lr
 617              		.cfi_endproc
 618              	.LFE156:
 620              		.section	.text.HAL_GetHalVersion,"ax",%progbits
 621              		.align	1
 622              		.global	HAL_GetHalVersion
 623              		.syntax unified
 624              		.thumb
 625              		.thumb_func
 627              	HAL_GetHalVersion:
 628              	.LFB157:
 452:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 453:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /**
 454:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @brief  Returns the HAL revision
 455:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @retval version : 0xXYZR (8bits for each decimal, R for RC)
 456:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   */
 457:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** uint32_t HAL_GetHalVersion(void)
 458:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** {
 629              		.loc 1 458 1 is_stmt 1 view -0
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s 			page 21


 630              		.cfi_startproc
 631              		@ args = 0, pretend = 0, frame = 0
 632              		@ frame_needed = 0, uses_anonymous_args = 0
 633              		@ link register save eliminated.
 459:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****  return __STM32H7xx_HAL_VERSION;
 634              		.loc 1 459 2 view .LVU142
 460:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** }
 635              		.loc 1 460 1 is_stmt 0 view .LVU143
 636 0000 0048     		ldr	r0, .L55
 637 0002 7047     		bx	lr
 638              	.L56:
 639              		.align	2
 640              	.L55:
 641 0004 00050B01 		.word	17499392
 642              		.cfi_endproc
 643              	.LFE157:
 645              		.section	.text.HAL_GetREVID,"ax",%progbits
 646              		.align	1
 647              		.global	HAL_GetREVID
 648              		.syntax unified
 649              		.thumb
 650              		.thumb_func
 652              	HAL_GetREVID:
 653              	.LFB158:
 461:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 462:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /**
 463:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @brief  Returns the device revision identifier.
 464:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @retval Device revision identifier
 465:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   */
 466:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** uint32_t HAL_GetREVID(void)
 467:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** {
 654              		.loc 1 467 1 is_stmt 1 view -0
 655              		.cfi_startproc
 656              		@ args = 0, pretend = 0, frame = 0
 657              		@ frame_needed = 0, uses_anonymous_args = 0
 658              		@ link register save eliminated.
 468:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****    return((DBGMCU->IDCODE) >> 16);
 659              		.loc 1 468 4 view .LVU145
 660              		.loc 1 468 18 is_stmt 0 view .LVU146
 661 0000 014B     		ldr	r3, .L58
 662 0002 1868     		ldr	r0, [r3]
 469:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** }
 663              		.loc 1 469 1 view .LVU147
 664 0004 000C     		lsrs	r0, r0, #16
 665 0006 7047     		bx	lr
 666              	.L59:
 667              		.align	2
 668              	.L58:
 669 0008 0010005C 		.word	1543507968
 670              		.cfi_endproc
 671              	.LFE158:
 673              		.section	.text.HAL_GetDEVID,"ax",%progbits
 674              		.align	1
 675              		.global	HAL_GetDEVID
 676              		.syntax unified
 677              		.thumb
 678              		.thumb_func
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s 			page 22


 680              	HAL_GetDEVID:
 681              	.LFB159:
 470:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 471:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /**
 472:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @brief  Returns the device identifier.
 473:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @retval Device identifier
 474:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   */
 475:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** uint32_t HAL_GetDEVID(void)
 476:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** {
 682              		.loc 1 476 1 is_stmt 1 view -0
 683              		.cfi_startproc
 684              		@ args = 0, pretend = 0, frame = 0
 685              		@ frame_needed = 0, uses_anonymous_args = 0
 686              		@ link register save eliminated.
 477:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****    return((DBGMCU->IDCODE) & IDCODE_DEVID_MASK);
 687              		.loc 1 477 4 view .LVU149
 688              		.loc 1 477 18 is_stmt 0 view .LVU150
 689 0000 024B     		ldr	r3, .L61
 690 0002 1868     		ldr	r0, [r3]
 478:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** }
 691              		.loc 1 478 1 view .LVU151
 692 0004 C0F30B00 		ubfx	r0, r0, #0, #12
 693 0008 7047     		bx	lr
 694              	.L62:
 695 000a 00BF     		.align	2
 696              	.L61:
 697 000c 0010005C 		.word	1543507968
 698              		.cfi_endproc
 699              	.LFE159:
 701              		.section	.text.HAL_GetUIDw0,"ax",%progbits
 702              		.align	1
 703              		.global	HAL_GetUIDw0
 704              		.syntax unified
 705              		.thumb
 706              		.thumb_func
 708              	HAL_GetUIDw0:
 709              	.LFB160:
 479:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 480:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /**
 481:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @brief  Return the first word of the unique device identifier (UID based on 96 bits)
 482:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @retval Device identifier
 483:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   */
 484:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** uint32_t HAL_GetUIDw0(void)
 485:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** {
 710              		.loc 1 485 1 is_stmt 1 view -0
 711              		.cfi_startproc
 712              		@ args = 0, pretend = 0, frame = 0
 713              		@ frame_needed = 0, uses_anonymous_args = 0
 714              		@ link register save eliminated.
 486:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   return(READ_REG(*((uint32_t *)UID_BASE)));
 715              		.loc 1 486 3 view .LVU153
 487:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** }
 716              		.loc 1 487 1 is_stmt 0 view .LVU154
 717 0000 014B     		ldr	r3, .L64
 718 0002 D3F80008 		ldr	r0, [r3, #2048]
 719 0006 7047     		bx	lr
 720              	.L65:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s 			page 23


 721              		.align	2
 722              	.L64:
 723 0008 00E0F11F 		.word	*********
 724              		.cfi_endproc
 725              	.LFE160:
 727              		.section	.text.HAL_GetUIDw1,"ax",%progbits
 728              		.align	1
 729              		.global	HAL_GetUIDw1
 730              		.syntax unified
 731              		.thumb
 732              		.thumb_func
 734              	HAL_GetUIDw1:
 735              	.LFB161:
 488:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 489:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /**
 490:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @brief  Return the second word of the unique device identifier (UID based on 96 bits)
 491:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @retval Device identifier
 492:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   */
 493:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** uint32_t HAL_GetUIDw1(void)
 494:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** {
 736              		.loc 1 494 1 is_stmt 1 view -0
 737              		.cfi_startproc
 738              		@ args = 0, pretend = 0, frame = 0
 739              		@ frame_needed = 0, uses_anonymous_args = 0
 740              		@ link register save eliminated.
 495:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   return(READ_REG(*((uint32_t *)(UID_BASE + 4U))));
 741              		.loc 1 495 3 view .LVU156
 496:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** }
 742              		.loc 1 496 1 is_stmt 0 view .LVU157
 743 0000 014B     		ldr	r3, .L67
 744 0002 D3F80408 		ldr	r0, [r3, #2052]
 745 0006 7047     		bx	lr
 746              	.L68:
 747              		.align	2
 748              	.L67:
 749 0008 00E0F11F 		.word	*********
 750              		.cfi_endproc
 751              	.LFE161:
 753              		.section	.text.HAL_GetUIDw2,"ax",%progbits
 754              		.align	1
 755              		.global	HAL_GetUIDw2
 756              		.syntax unified
 757              		.thumb
 758              		.thumb_func
 760              	HAL_GetUIDw2:
 761              	.LFB162:
 497:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 498:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /**
 499:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @brief  Return the third word of the unique device identifier (UID based on 96 bits)
 500:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @retval Device identifier
 501:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   */
 502:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** uint32_t HAL_GetUIDw2(void)
 503:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** {
 762              		.loc 1 503 1 is_stmt 1 view -0
 763              		.cfi_startproc
 764              		@ args = 0, pretend = 0, frame = 0
 765              		@ frame_needed = 0, uses_anonymous_args = 0
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s 			page 24


 766              		@ link register save eliminated.
 504:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   return(READ_REG(*((uint32_t *)(UID_BASE + 8U))));
 767              		.loc 1 504 3 view .LVU159
 505:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** }
 768              		.loc 1 505 1 is_stmt 0 view .LVU160
 769 0000 014B     		ldr	r3, .L70
 770 0002 D3F80808 		ldr	r0, [r3, #2056]
 771 0006 7047     		bx	lr
 772              	.L71:
 773              		.align	2
 774              	.L70:
 775 0008 00E0F11F 		.word	*********
 776              		.cfi_endproc
 777              	.LFE162:
 779              		.section	.text.HAL_SYSCFG_VREFBUF_VoltageScalingConfig,"ax",%progbits
 780              		.align	1
 781              		.global	HAL_SYSCFG_VREFBUF_VoltageScalingConfig
 782              		.syntax unified
 783              		.thumb
 784              		.thumb_func
 786              	HAL_SYSCFG_VREFBUF_VoltageScalingConfig:
 787              	.LVL26:
 788              	.LFB163:
 506:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 507:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /**
 508:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @brief Configure the internal voltage reference buffer voltage scale.
 509:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @param VoltageScaling  specifies the output voltage to achieve
 510:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *          This parameter can be one of the following values:
 511:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *            @arg SYSCFG_VREFBUF_VOLTAGE_SCALE0: VREF_OUT1 around 2.5 V.
 512:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *                                                This requires VDDA equal to or higher than 2.8 V
 513:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *            @arg SYSCFG_VREFBUF_VOLTAGE_SCALE1: VREF_OUT2 around 2.048 V.
 514:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *                                                This requires VDDA equal to or higher than 2.4 V
 515:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *            @arg SYSCFG_VREFBUF_VOLTAGE_SCALE2: VREF_OUT3 around 1.8 V.
 516:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *                                                This requires VDDA equal to or higher than 2.1 V
 517:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *            @arg SYSCFG_VREFBUF_VOLTAGE_SCALE3: VREF_OUT4 around 1.5 V.
 518:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *                                                This requires VDDA equal to or higher than 1.8 V
 519:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @retval None
 520:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   */
 521:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** void HAL_SYSCFG_VREFBUF_VoltageScalingConfig(uint32_t VoltageScaling)
 522:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** {
 789              		.loc 1 522 1 is_stmt 1 view -0
 790              		.cfi_startproc
 791              		@ args = 0, pretend = 0, frame = 0
 792              		@ frame_needed = 0, uses_anonymous_args = 0
 793              		@ link register save eliminated.
 523:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   /* Check the parameters */
 524:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   assert_param(IS_SYSCFG_VREFBUF_VOLTAGE_SCALE(VoltageScaling));
 794              		.loc 1 524 3 view .LVU162
 525:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 526:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   MODIFY_REG(VREFBUF->CSR, VREFBUF_CSR_VRS, VoltageScaling);
 795              		.loc 1 526 3 view .LVU163
 796 0000 034A     		ldr	r2, .L73
 797 0002 1368     		ldr	r3, [r2]
 798 0004 23F07003 		bic	r3, r3, #112
 799 0008 0343     		orrs	r3, r3, r0
 800 000a 1360     		str	r3, [r2]
 527:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** }
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s 			page 25


 801              		.loc 1 527 1 is_stmt 0 view .LVU164
 802 000c 7047     		bx	lr
 803              	.L74:
 804 000e 00BF     		.align	2
 805              	.L73:
 806 0010 003C0058 		.word	1476410368
 807              		.cfi_endproc
 808              	.LFE163:
 810              		.section	.text.HAL_SYSCFG_VREFBUF_HighImpedanceConfig,"ax",%progbits
 811              		.align	1
 812              		.global	HAL_SYSCFG_VREFBUF_HighImpedanceConfig
 813              		.syntax unified
 814              		.thumb
 815              		.thumb_func
 817              	HAL_SYSCFG_VREFBUF_HighImpedanceConfig:
 818              	.LVL27:
 819              	.LFB164:
 528:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 529:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /**
 530:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @brief Configure the internal voltage reference buffer high impedance mode.
 531:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @param Mode  specifies the high impedance mode
 532:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *          This parameter can be one of the following values:
 533:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *            @arg SYSCFG_VREFBUF_HIGH_IMPEDANCE_DISABLE: VREF+ pin is internally connect to VREFI
 534:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *            @arg SYSCFG_VREFBUF_HIGH_IMPEDANCE_ENABLE: VREF+ pin is high impedance.
 535:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @retval None
 536:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   */
 537:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** void HAL_SYSCFG_VREFBUF_HighImpedanceConfig(uint32_t Mode)
 538:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** {
 820              		.loc 1 538 1 is_stmt 1 view -0
 821              		.cfi_startproc
 822              		@ args = 0, pretend = 0, frame = 0
 823              		@ frame_needed = 0, uses_anonymous_args = 0
 824              		@ link register save eliminated.
 539:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   /* Check the parameters */
 540:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   assert_param(IS_SYSCFG_VREFBUF_HIGH_IMPEDANCE(Mode));
 825              		.loc 1 540 3 view .LVU166
 541:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 542:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   MODIFY_REG(VREFBUF->CSR, VREFBUF_CSR_HIZ, Mode);
 826              		.loc 1 542 3 view .LVU167
 827 0000 034A     		ldr	r2, .L76
 828 0002 1368     		ldr	r3, [r2]
 829 0004 23F00203 		bic	r3, r3, #2
 830 0008 0343     		orrs	r3, r3, r0
 831 000a 1360     		str	r3, [r2]
 543:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** }
 832              		.loc 1 543 1 is_stmt 0 view .LVU168
 833 000c 7047     		bx	lr
 834              	.L77:
 835 000e 00BF     		.align	2
 836              	.L76:
 837 0010 003C0058 		.word	1476410368
 838              		.cfi_endproc
 839              	.LFE164:
 841              		.section	.text.HAL_SYSCFG_VREFBUF_TrimmingConfig,"ax",%progbits
 842              		.align	1
 843              		.global	HAL_SYSCFG_VREFBUF_TrimmingConfig
 844              		.syntax unified
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s 			page 26


 845              		.thumb
 846              		.thumb_func
 848              	HAL_SYSCFG_VREFBUF_TrimmingConfig:
 849              	.LVL28:
 850              	.LFB165:
 544:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 545:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /**
 546:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @brief  Tune the Internal Voltage Reference buffer (VREFBUF).
 547:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @retval None
 548:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   */
 549:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** void HAL_SYSCFG_VREFBUF_TrimmingConfig(uint32_t TrimmingValue)
 550:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** {
 851              		.loc 1 550 1 is_stmt 1 view -0
 852              		.cfi_startproc
 853              		@ args = 0, pretend = 0, frame = 0
 854              		@ frame_needed = 0, uses_anonymous_args = 0
 855              		@ link register save eliminated.
 551:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   /* Check the parameters */
 552:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   assert_param(IS_SYSCFG_VREFBUF_TRIMMING(TrimmingValue));
 856              		.loc 1 552 3 view .LVU170
 553:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 554:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   MODIFY_REG(VREFBUF->CCR, VREFBUF_CCR_TRIM, TrimmingValue);
 857              		.loc 1 554 3 view .LVU171
 858 0000 034A     		ldr	r2, .L79
 859 0002 5368     		ldr	r3, [r2, #4]
 860 0004 23F03F03 		bic	r3, r3, #63
 861 0008 0343     		orrs	r3, r3, r0
 862 000a 5360     		str	r3, [r2, #4]
 555:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** }
 863              		.loc 1 555 1 is_stmt 0 view .LVU172
 864 000c 7047     		bx	lr
 865              	.L80:
 866 000e 00BF     		.align	2
 867              	.L79:
 868 0010 003C0058 		.word	1476410368
 869              		.cfi_endproc
 870              	.LFE165:
 872              		.section	.text.HAL_SYSCFG_EnableVREFBUF,"ax",%progbits
 873              		.align	1
 874              		.global	HAL_SYSCFG_EnableVREFBUF
 875              		.syntax unified
 876              		.thumb
 877              		.thumb_func
 879              	HAL_SYSCFG_EnableVREFBUF:
 880              	.LFB166:
 556:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 557:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /**
 558:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @brief  Enable the Internal Voltage Reference buffer (VREFBUF).
 559:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @retval HAL_OK/HAL_TIMEOUT
 560:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   */
 561:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** HAL_StatusTypeDef HAL_SYSCFG_EnableVREFBUF(void)
 562:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** {
 881              		.loc 1 562 1 is_stmt 1 view -0
 882              		.cfi_startproc
 883              		@ args = 0, pretend = 0, frame = 0
 884              		@ frame_needed = 0, uses_anonymous_args = 0
 885 0000 10B5     		push	{r4, lr}
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s 			page 27


 886              	.LCFI5:
 887              		.cfi_def_cfa_offset 8
 888              		.cfi_offset 4, -8
 889              		.cfi_offset 14, -4
 563:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   uint32_t  tickstart;
 890              		.loc 1 563 3 view .LVU174
 564:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 565:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   SET_BIT(VREFBUF->CSR, VREFBUF_CSR_ENVR);
 891              		.loc 1 565 3 view .LVU175
 892 0002 0B4A     		ldr	r2, .L88
 893 0004 1368     		ldr	r3, [r2]
 894 0006 43F00103 		orr	r3, r3, #1
 895 000a 1360     		str	r3, [r2]
 566:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 567:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   /* Get Start Tick*/
 568:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   tickstart = HAL_GetTick();
 896              		.loc 1 568 3 view .LVU176
 897              		.loc 1 568 15 is_stmt 0 view .LVU177
 898 000c FFF7FEFF 		bl	HAL_GetTick
 899              	.LVL29:
 900 0010 0446     		mov	r4, r0
 901              	.LVL30:
 569:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 570:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   /* Wait for VRR bit  */
 571:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   while(READ_BIT(VREFBUF->CSR, VREFBUF_CSR_VRR) == 0UL)
 902              		.loc 1 571 3 is_stmt 1 view .LVU178
 903              	.L82:
 904              		.loc 1 571 49 view .LVU179
 905              		.loc 1 571 9 is_stmt 0 view .LVU180
 906 0012 074B     		ldr	r3, .L88
 907 0014 1B68     		ldr	r3, [r3]
 908              		.loc 1 571 49 view .LVU181
 909 0016 13F0080F 		tst	r3, #8
 910 001a 06D1     		bne	.L87
 572:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   {
 573:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     if((HAL_GetTick() - tickstart) > VREFBUF_TIMEOUT_VALUE)
 911              		.loc 1 573 5 is_stmt 1 view .LVU182
 912              		.loc 1 573 9 is_stmt 0 view .LVU183
 913 001c FFF7FEFF 		bl	HAL_GetTick
 914              	.LVL31:
 915              		.loc 1 573 23 discriminator 1 view .LVU184
 916 0020 001B     		subs	r0, r0, r4
 917              		.loc 1 573 7 discriminator 1 view .LVU185
 918 0022 0A28     		cmp	r0, #10
 919 0024 F5D9     		bls	.L82
 574:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     {
 575:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****       return HAL_TIMEOUT;
 920              		.loc 1 575 14 view .LVU186
 921 0026 0320     		movs	r0, #3
 922 0028 00E0     		b	.L83
 923              	.L87:
 576:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     }
 577:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   }
 578:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 579:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   return HAL_OK;
 924              		.loc 1 579 10 view .LVU187
 925 002a 0020     		movs	r0, #0
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s 			page 28


 926              	.L83:
 580:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** }
 927              		.loc 1 580 1 view .LVU188
 928 002c 10BD     		pop	{r4, pc}
 929              	.LVL32:
 930              	.L89:
 931              		.loc 1 580 1 view .LVU189
 932 002e 00BF     		.align	2
 933              	.L88:
 934 0030 003C0058 		.word	1476410368
 935              		.cfi_endproc
 936              	.LFE166:
 938              		.section	.text.HAL_SYSCFG_DisableVREFBUF,"ax",%progbits
 939              		.align	1
 940              		.global	HAL_SYSCFG_DisableVREFBUF
 941              		.syntax unified
 942              		.thumb
 943              		.thumb_func
 945              	HAL_SYSCFG_DisableVREFBUF:
 946              	.LFB167:
 581:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 582:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /**
 583:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @brief  Disable the Internal Voltage Reference buffer (VREFBUF).
 584:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *
 585:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @retval None
 586:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   */
 587:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** void HAL_SYSCFG_DisableVREFBUF(void)
 588:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** {
 947              		.loc 1 588 1 is_stmt 1 view -0
 948              		.cfi_startproc
 949              		@ args = 0, pretend = 0, frame = 0
 950              		@ frame_needed = 0, uses_anonymous_args = 0
 951              		@ link register save eliminated.
 589:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   CLEAR_BIT(VREFBUF->CSR, VREFBUF_CSR_ENVR);
 952              		.loc 1 589 3 view .LVU191
 953 0000 024A     		ldr	r2, .L91
 954 0002 1368     		ldr	r3, [r2]
 955 0004 23F00103 		bic	r3, r3, #1
 956 0008 1360     		str	r3, [r2]
 590:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** }
 957              		.loc 1 590 1 is_stmt 0 view .LVU192
 958 000a 7047     		bx	lr
 959              	.L92:
 960              		.align	2
 961              	.L91:
 962 000c 003C0058 		.word	1476410368
 963              		.cfi_endproc
 964              	.LFE167:
 966              		.section	.text.HAL_SYSCFG_ETHInterfaceSelect,"ax",%progbits
 967              		.align	1
 968              		.global	HAL_SYSCFG_ETHInterfaceSelect
 969              		.syntax unified
 970              		.thumb
 971              		.thumb_func
 973              	HAL_SYSCFG_ETHInterfaceSelect:
 974              	.LVL33:
 975              	.LFB168:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s 			page 29


 591:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 592:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** #if defined(SYSCFG_PMCR_EPIS_SEL)
 593:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /**
 594:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @brief  Ethernet PHY Interface Selection either MII or RMII
 595:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @param  SYSCFG_ETHInterface: Selects the Ethernet PHY interface
 596:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *   This parameter can be one of the following values:
 597:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *   @arg SYSCFG_ETH_MII : Select the Media Independent Interface
 598:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *   @arg SYSCFG_ETH_RMII: Select the Reduced Media Independent Interface
 599:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @retval None
 600:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   */
 601:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** void HAL_SYSCFG_ETHInterfaceSelect(uint32_t SYSCFG_ETHInterface)
 602:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** {
 976              		.loc 1 602 1 is_stmt 1 view -0
 977              		.cfi_startproc
 978              		@ args = 0, pretend = 0, frame = 0
 979              		@ frame_needed = 0, uses_anonymous_args = 0
 980              		@ link register save eliminated.
 603:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   /* Check the parameter */
 604:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   assert_param(IS_SYSCFG_ETHERNET_CONFIG(SYSCFG_ETHInterface));
 981              		.loc 1 604 3 view .LVU194
 605:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 606:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   MODIFY_REG(SYSCFG->PMCR, SYSCFG_PMCR_EPIS_SEL, (uint32_t)(SYSCFG_ETHInterface));
 982              		.loc 1 606 3 view .LVU195
 983 0000 034A     		ldr	r2, .L94
 984 0002 5368     		ldr	r3, [r2, #4]
 985 0004 23F46003 		bic	r3, r3, #14680064
 986 0008 0343     		orrs	r3, r3, r0
 987 000a 5360     		str	r3, [r2, #4]
 607:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** }
 988              		.loc 1 607 1 is_stmt 0 view .LVU196
 989 000c 7047     		bx	lr
 990              	.L95:
 991 000e 00BF     		.align	2
 992              	.L94:
 993 0010 00040058 		.word	1476396032
 994              		.cfi_endproc
 995              	.LFE168:
 997              		.section	.text.HAL_SYSCFG_AnalogSwitchConfig,"ax",%progbits
 998              		.align	1
 999              		.global	HAL_SYSCFG_AnalogSwitchConfig
 1000              		.syntax unified
 1001              		.thumb
 1002              		.thumb_func
 1004              	HAL_SYSCFG_AnalogSwitchConfig:
 1005              	.LVL34:
 1006              	.LFB169:
 608:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** #endif /* SYSCFG_PMCR_EPIS_SEL */
 609:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 610:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /**
 611:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @brief  Analog Switch control for dual analog pads.
 612:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @param  SYSCFG_AnalogSwitch: Selects the analog pad
 613:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *   This parameter can be one or a combination of the following values:
 614:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *   @arg SYSCFG_SWITCH_PA0 : Select PA0 analog switch
 615:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *   @arg SYSCFG_SWITCH_PA1:  Select PA1 analog switch
 616:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *   @arg SYSCFG_SWITCH_PC2 : Select PC2 analog switch
 617:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *   @arg SYSCFG_SWITCH_PC3:  Select PC3 analog switch
 618:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @param  SYSCFG_SwitchState: Open or Close the analog switch between dual pads (PXn and PXn_C)
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s 			page 30


 619:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *   This parameter can be one or a combination of the following values:
 620:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *   @arg SYSCFG_SWITCH_PA0_OPEN
 621:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *   @arg SYSCFG_SWITCH_PA0_CLOSE
 622:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *   @arg SYSCFG_SWITCH_PA1_OPEN
 623:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *   @arg SYSCFG_SWITCH_PA1_CLOSE
 624:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *   @arg SYSCFG_SWITCH_PC2_OPEN
 625:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *   @arg SYSCFG_SWITCH_PC2_CLOSE
 626:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *   @arg SYSCFG_SWITCH_PC3_OPEN
 627:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *   @arg SYSCFG_SWITCH_PC3_CLOSE
 628:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @retval None
 629:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   */
 630:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 631:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** void HAL_SYSCFG_AnalogSwitchConfig(uint32_t SYSCFG_AnalogSwitch , uint32_t SYSCFG_SwitchState )
 632:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** {
 1007              		.loc 1 632 1 is_stmt 1 view -0
 1008              		.cfi_startproc
 1009              		@ args = 0, pretend = 0, frame = 0
 1010              		@ frame_needed = 0, uses_anonymous_args = 0
 1011              		@ link register save eliminated.
 633:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   /* Check the parameter */
 634:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   assert_param(IS_SYSCFG_ANALOG_SWITCH(SYSCFG_AnalogSwitch));
 1012              		.loc 1 634 3 view .LVU198
 635:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   assert_param(IS_SYSCFG_SWITCH_STATE(SYSCFG_SwitchState));
 1013              		.loc 1 635 3 view .LVU199
 636:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 637:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   MODIFY_REG(SYSCFG->PMCR, (uint32_t) SYSCFG_AnalogSwitch, (uint32_t)(SYSCFG_SwitchState));
 1014              		.loc 1 637 3 view .LVU200
 1015 0000 034A     		ldr	r2, .L97
 1016 0002 5368     		ldr	r3, [r2, #4]
 1017 0004 23EA0003 		bic	r3, r3, r0
 1018 0008 0B43     		orrs	r3, r3, r1
 1019 000a 5360     		str	r3, [r2, #4]
 638:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** }
 1020              		.loc 1 638 1 is_stmt 0 view .LVU201
 1021 000c 7047     		bx	lr
 1022              	.L98:
 1023 000e 00BF     		.align	2
 1024              	.L97:
 1025 0010 00040058 		.word	1476396032
 1026              		.cfi_endproc
 1027              	.LFE169:
 1029              		.section	.text.HAL_SYSCFG_EnableBOOST,"ax",%progbits
 1030              		.align	1
 1031              		.global	HAL_SYSCFG_EnableBOOST
 1032              		.syntax unified
 1033              		.thumb
 1034              		.thumb_func
 1036              	HAL_SYSCFG_EnableBOOST:
 1037              	.LFB170:
 639:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 640:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** #if defined(SYSCFG_PMCR_BOOSTEN)
 641:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /**
 642:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @brief  Enables the booster to reduce the total harmonic distortion of the analog
 643:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *         switch when the supply voltage is lower than 2.7 V.
 644:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @note   Activating the booster allows to guaranty the analog switch AC performance
 645:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *         when the supply voltage is below 2.7 V: in this case, the analog switch
 646:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *         performance is the same on the full voltage range
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s 			page 31


 647:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @retval None
 648:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   */
 649:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** void HAL_SYSCFG_EnableBOOST(void)
 650:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** {
 1038              		.loc 1 650 1 is_stmt 1 view -0
 1039              		.cfi_startproc
 1040              		@ args = 0, pretend = 0, frame = 0
 1041              		@ frame_needed = 0, uses_anonymous_args = 0
 1042              		@ link register save eliminated.
 651:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****  SET_BIT(SYSCFG->PMCR, SYSCFG_PMCR_BOOSTEN) ;
 1043              		.loc 1 651 2 view .LVU203
 1044 0000 024A     		ldr	r2, .L100
 1045 0002 5368     		ldr	r3, [r2, #4]
 1046 0004 43F48073 		orr	r3, r3, #256
 1047 0008 5360     		str	r3, [r2, #4]
 652:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** }
 1048              		.loc 1 652 1 is_stmt 0 view .LVU204
 1049 000a 7047     		bx	lr
 1050              	.L101:
 1051              		.align	2
 1052              	.L100:
 1053 000c 00040058 		.word	1476396032
 1054              		.cfi_endproc
 1055              	.LFE170:
 1057              		.section	.text.HAL_SYSCFG_DisableBOOST,"ax",%progbits
 1058              		.align	1
 1059              		.global	HAL_SYSCFG_DisableBOOST
 1060              		.syntax unified
 1061              		.thumb
 1062              		.thumb_func
 1064              	HAL_SYSCFG_DisableBOOST:
 1065              	.LFB171:
 653:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 654:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /**
 655:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @brief  Disables the booster
 656:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @note   Activating the booster allows to guaranty the analog switch AC performance
 657:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *         when the supply voltage is below 2.7 V: in this case, the analog switch
 658:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *         performance is the same on the full voltage range
 659:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @retval None
 660:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   */
 661:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** void HAL_SYSCFG_DisableBOOST(void)
 662:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** {
 1066              		.loc 1 662 1 is_stmt 1 view -0
 1067              		.cfi_startproc
 1068              		@ args = 0, pretend = 0, frame = 0
 1069              		@ frame_needed = 0, uses_anonymous_args = 0
 1070              		@ link register save eliminated.
 663:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****  CLEAR_BIT(SYSCFG->PMCR, SYSCFG_PMCR_BOOSTEN) ;
 1071              		.loc 1 663 2 view .LVU206
 1072 0000 024A     		ldr	r2, .L103
 1073 0002 5368     		ldr	r3, [r2, #4]
 1074 0004 23F48073 		bic	r3, r3, #256
 1075 0008 5360     		str	r3, [r2, #4]
 664:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** }
 1076              		.loc 1 664 1 is_stmt 0 view .LVU207
 1077 000a 7047     		bx	lr
 1078              	.L104:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s 			page 32


 1079              		.align	2
 1080              	.L103:
 1081 000c 00040058 		.word	1476396032
 1082              		.cfi_endproc
 1083              	.LFE171:
 1085              		.section	.text.HAL_SYSCFG_CM7BootAddConfig,"ax",%progbits
 1086              		.align	1
 1087              		.global	HAL_SYSCFG_CM7BootAddConfig
 1088              		.syntax unified
 1089              		.thumb
 1090              		.thumb_func
 1092              	HAL_SYSCFG_CM7BootAddConfig:
 1093              	.LVL35:
 1094              	.LFB172:
 665:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** #endif /* SYSCFG_PMCR_BOOSTEN */
 666:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 667:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** #if defined (SYSCFG_UR2_BOOT_ADD0) ||  defined (SYSCFG_UR2_BCM7_ADD0)
 668:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /**
 669:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @brief  BootCM7 address 0 configuration
 670:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @param  BootRegister :Specifies the Boot Address register (Address0 or Address1)
 671:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *   This parameter can be one of the following values:
 672:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *   @arg SYSCFG_BOOT_ADDR0 : Select the boot address0
 673:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *   @arg SYSCFG_BOOT_ADDR1:  Select the boot address1
 674:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @param  BootAddress :Specifies the CM7 Boot Address to be loaded in Address0 or Address1
 675:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @retval None
 676:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   */
 677:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** void HAL_SYSCFG_CM7BootAddConfig(uint32_t BootRegister, uint32_t BootAddress)
 678:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** {
 1095              		.loc 1 678 1 is_stmt 1 view -0
 1096              		.cfi_startproc
 1097              		@ args = 0, pretend = 0, frame = 0
 1098              		@ frame_needed = 0, uses_anonymous_args = 0
 1099              		@ link register save eliminated.
 679:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   /* Check the parameters */
 680:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   assert_param(IS_SYSCFG_BOOT_REGISTER(BootRegister));
 1100              		.loc 1 680 3 view .LVU209
 681:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   assert_param(IS_SYSCFG_BOOT_ADDRESS(BootAddress));
 1101              		.loc 1 681 3 view .LVU210
 682:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   if ( BootRegister == SYSCFG_BOOT_ADDR0 )
 1102              		.loc 1 682 3 view .LVU211
 1103              		.loc 1 682 6 is_stmt 0 view .LVU212
 1104 0000 48B9     		cbnz	r0, .L106
 683:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   {
 684:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     /* Configure CM7 BOOT ADD0 */
 685:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** #if defined(DUAL_CORE)
 686:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     MODIFY_REG(SYSCFG->UR2, SYSCFG_UR2_BCM7_ADD0, ((BootAddress >> 16) << SYSCFG_UR2_BCM7_ADD0_Pos)
 687:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** #else
 688:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     MODIFY_REG(SYSCFG->UR2, SYSCFG_UR2_BOOT_ADD0, ((BootAddress >> 16) << SYSCFG_UR2_BOOT_ADD0_Pos)
 1105              		.loc 1 688 5 is_stmt 1 view .LVU213
 1106 0002 0A4A     		ldr	r2, .L108
 1107 0004 D2F80833 		ldr	r3, [r2, #776]
 1108 0008 9BB2     		uxth	r3, r3
 1109 000a 6FF30F01 		bfc	r1, #0, #16
 1110              	.LVL36:
 1111              		.loc 1 688 5 is_stmt 0 view .LVU214
 1112 000e 0B43     		orrs	r3, r3, r1
 1113 0010 C2F80833 		str	r3, [r2, #776]
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s 			page 33


 1114 0014 7047     		bx	lr
 1115              	.LVL37:
 1116              	.L106:
 689:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** #endif /*DUAL_CORE*/
 690:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   }
 691:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   else
 692:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   {
 693:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     /* Configure CM7 BOOT ADD1 */
 694:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** #if defined(DUAL_CORE)
 695:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     MODIFY_REG(SYSCFG->UR3, SYSCFG_UR3_BCM7_ADD1, (BootAddress >> 16));
 696:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** #else
 697:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     MODIFY_REG(SYSCFG->UR3, SYSCFG_UR3_BOOT_ADD1, (BootAddress >> 16));
 1117              		.loc 1 697 5 is_stmt 1 view .LVU215
 1118 0016 054A     		ldr	r2, .L108
 1119 0018 D2F80C33 		ldr	r3, [r2, #780]
 1120 001c 6FF30F03 		bfc	r3, #0, #16
 1121 0020 43EA1143 		orr	r3, r3, r1, lsr #16
 1122 0024 C2F80C33 		str	r3, [r2, #780]
 698:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** #endif /*DUAL_CORE*/
 699:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   }
 700:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** }
 1123              		.loc 1 700 1 is_stmt 0 view .LVU216
 1124 0028 7047     		bx	lr
 1125              	.L109:
 1126 002a 00BF     		.align	2
 1127              	.L108:
 1128 002c 00040058 		.word	1476396032
 1129              		.cfi_endproc
 1130              	.LFE172:
 1132              		.section	.text.HAL_EnableCompensationCell,"ax",%progbits
 1133              		.align	1
 1134              		.global	HAL_EnableCompensationCell
 1135              		.syntax unified
 1136              		.thumb
 1137              		.thumb_func
 1139              	HAL_EnableCompensationCell:
 1140              	.LFB173:
 701:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** #endif /* SYSCFG_UR2_BOOT_ADD0 || SYSCFG_UR2_BCM7_ADD0 */
 702:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 703:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** #if defined(DUAL_CORE)
 704:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /**
 705:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @brief  BootCM4 address 0 configuration
 706:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @param  BootRegister :Specifies the Boot Address register (Address0 or Address1)
 707:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *   This parameter can be one of the following values:
 708:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *   @arg SYSCFG_BOOT_ADDR0 : Select the boot address0
 709:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *   @arg SYSCFG_BOOT_ADDR1:  Select the boot address1
 710:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @param  BootAddress :Specifies the CM4 Boot Address to be loaded in Address0 or Address1
 711:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @retval None
 712:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   */
 713:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** void HAL_SYSCFG_CM4BootAddConfig(uint32_t BootRegister, uint32_t BootAddress)
 714:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** {
 715:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   /* Check the parameters */
 716:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   assert_param(IS_SYSCFG_BOOT_REGISTER(BootRegister));
 717:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   assert_param(IS_SYSCFG_BOOT_ADDRESS(BootAddress));
 718:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 719:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   if ( BootRegister == SYSCFG_BOOT_ADDR0 )
 720:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   {
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s 			page 34


 721:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     /* Configure CM4 BOOT ADD0 */
 722:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     MODIFY_REG(SYSCFG->UR3, SYSCFG_UR3_BCM4_ADD0, ((BootAddress >> 16)<< SYSCFG_UR3_BCM4_ADD0_Pos))
 723:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   }
 724:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 725:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   else
 726:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   {
 727:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     /* Configure CM4 BOOT ADD1 */
 728:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     MODIFY_REG(SYSCFG->UR4, SYSCFG_UR4_BCM4_ADD1, (BootAddress >> 16));
 729:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   }
 730:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** }
 731:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 732:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /**
 733:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @brief  Enables the Cortex-M7 boot
 734:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @retval None
 735:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   */
 736:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** void HAL_SYSCFG_EnableCM7BOOT(void)
 737:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** {
 738:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****  SET_BIT(SYSCFG->UR1, SYSCFG_UR1_BCM7);
 739:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** }
 740:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 741:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /**
 742:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @brief  Disables the Cortex-M7 boot
 743:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @note   Disabling the boot will gate the CPU clock
 744:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @retval None
 745:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   */
 746:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** void HAL_SYSCFG_DisableCM7BOOT(void)
 747:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** {
 748:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****  CLEAR_BIT(SYSCFG->UR1, SYSCFG_UR1_BCM7) ;
 749:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** }
 750:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 751:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /**
 752:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @brief  Enables the Cortex-M4 boot
 753:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @retval None
 754:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   */
 755:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** void HAL_SYSCFG_EnableCM4BOOT(void)
 756:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** {
 757:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****  SET_BIT(SYSCFG->UR1, SYSCFG_UR1_BCM4);
 758:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** }
 759:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 760:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /**
 761:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @brief  Disables the Cortex-M4 boot
 762:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @note   Disabling the boot will gate the CPU clock
 763:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @retval None
 764:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   */
 765:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** void HAL_SYSCFG_DisableCM4BOOT(void)
 766:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** {
 767:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   CLEAR_BIT(SYSCFG->UR1, SYSCFG_UR1_BCM4);
 768:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** }
 769:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** #endif /*DUAL_CORE*/
 770:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /**
 771:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @brief  Enables the I/O Compensation Cell.
 772:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @note   The I/O compensation cell can be used only when the device supply
 773:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *         voltage ranges from 1.62 to 2.0 V and from 2.7 to 3.6 V.
 774:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @retval None
 775:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   */
 776:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** void HAL_EnableCompensationCell(void)
 777:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** {
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s 			page 35


 1141              		.loc 1 777 1 is_stmt 1 view -0
 1142              		.cfi_startproc
 1143              		@ args = 0, pretend = 0, frame = 0
 1144              		@ frame_needed = 0, uses_anonymous_args = 0
 1145              		@ link register save eliminated.
 778:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   SET_BIT(SYSCFG->CCCSR, SYSCFG_CCCSR_EN) ;
 1146              		.loc 1 778 3 view .LVU218
 1147 0000 024A     		ldr	r2, .L111
 1148 0002 136A     		ldr	r3, [r2, #32]
 1149 0004 43F00103 		orr	r3, r3, #1
 1150 0008 1362     		str	r3, [r2, #32]
 779:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** }
 1151              		.loc 1 779 1 is_stmt 0 view .LVU219
 1152 000a 7047     		bx	lr
 1153              	.L112:
 1154              		.align	2
 1155              	.L111:
 1156 000c 00040058 		.word	1476396032
 1157              		.cfi_endproc
 1158              	.LFE173:
 1160              		.section	.text.HAL_DisableCompensationCell,"ax",%progbits
 1161              		.align	1
 1162              		.global	HAL_DisableCompensationCell
 1163              		.syntax unified
 1164              		.thumb
 1165              		.thumb_func
 1167              	HAL_DisableCompensationCell:
 1168              	.LFB174:
 780:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 781:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /**
 782:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @brief  Power-down the I/O Compensation Cell.
 783:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @note   The I/O compensation cell can be used only when the device supply
 784:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *         voltage ranges from 1.62 to 2.0 V and from 2.7 to 3.6 V.
 785:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @retval None
 786:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   */
 787:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** void HAL_DisableCompensationCell(void)
 788:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** {
 1169              		.loc 1 788 1 is_stmt 1 view -0
 1170              		.cfi_startproc
 1171              		@ args = 0, pretend = 0, frame = 0
 1172              		@ frame_needed = 0, uses_anonymous_args = 0
 1173              		@ link register save eliminated.
 789:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   CLEAR_BIT(SYSCFG->CCCSR, SYSCFG_CCCSR_EN);
 1174              		.loc 1 789 3 view .LVU221
 1175 0000 024A     		ldr	r2, .L114
 1176 0002 136A     		ldr	r3, [r2, #32]
 1177 0004 23F00103 		bic	r3, r3, #1
 1178 0008 1362     		str	r3, [r2, #32]
 790:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** }
 1179              		.loc 1 790 1 is_stmt 0 view .LVU222
 1180 000a 7047     		bx	lr
 1181              	.L115:
 1182              		.align	2
 1183              	.L114:
 1184 000c 00040058 		.word	1476396032
 1185              		.cfi_endproc
 1186              	.LFE174:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s 			page 36


 1188              		.section	.text.HAL_SYSCFG_EnableIOSpeedOptimize,"ax",%progbits
 1189              		.align	1
 1190              		.global	HAL_SYSCFG_EnableIOSpeedOptimize
 1191              		.syntax unified
 1192              		.thumb
 1193              		.thumb_func
 1195              	HAL_SYSCFG_EnableIOSpeedOptimize:
 1196              	.LFB175:
 791:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 792:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 793:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /**
 794:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @brief  To Enable optimize the I/O speed when the product voltage is low.
 795:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @note   This bit is active only if PRODUCT_BELOW_25V user option bit is set. It must be
 796:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *         used only if the product supply voltage is below 2.5 V. Setting this bit when VDD is
 797:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *         higher than 2.5 V might be destructive.
 798:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @retval None
 799:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   */
 800:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** void HAL_SYSCFG_EnableIOSpeedOptimize(void)
 801:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** {
 1197              		.loc 1 801 1 is_stmt 1 view -0
 1198              		.cfi_startproc
 1199              		@ args = 0, pretend = 0, frame = 0
 1200              		@ frame_needed = 0, uses_anonymous_args = 0
 1201              		@ link register save eliminated.
 802:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** #if defined(SYSCFG_CCCSR_HSLV)
 803:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   SET_BIT(SYSCFG->CCCSR, SYSCFG_CCCSR_HSLV);
 1202              		.loc 1 803 3 view .LVU224
 1203 0000 024A     		ldr	r2, .L117
 1204 0002 136A     		ldr	r3, [r2, #32]
 1205 0004 43F48033 		orr	r3, r3, #65536
 1206 0008 1362     		str	r3, [r2, #32]
 804:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** #else
 805:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   SET_BIT(SYSCFG->CCCSR, (SYSCFG_CCCSR_HSLV0| SYSCFG_CCCSR_HSLV1 | SYSCFG_CCCSR_HSLV2  | SYSCFG_CCC
 806:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** #endif   /* SYSCFG_CCCSR_HSLV */
 807:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** }
 1207              		.loc 1 807 1 is_stmt 0 view .LVU225
 1208 000a 7047     		bx	lr
 1209              	.L118:
 1210              		.align	2
 1211              	.L117:
 1212 000c 00040058 		.word	1476396032
 1213              		.cfi_endproc
 1214              	.LFE175:
 1216              		.section	.text.HAL_SYSCFG_DisableIOSpeedOptimize,"ax",%progbits
 1217              		.align	1
 1218              		.global	HAL_SYSCFG_DisableIOSpeedOptimize
 1219              		.syntax unified
 1220              		.thumb
 1221              		.thumb_func
 1223              	HAL_SYSCFG_DisableIOSpeedOptimize:
 1224              	.LFB176:
 808:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 809:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /**
 810:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @brief  To Disable optimize the I/O speed when the product voltage is low.
 811:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @note   This bit is active only if PRODUCT_BELOW_25V user option bit is set. It must be
 812:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *         used only if the product supply voltage is below 2.5 V. Setting this bit when VDD is
 813:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *         higher than 2.5 V might be destructive.
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s 			page 37


 814:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @retval None
 815:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   */
 816:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** void HAL_SYSCFG_DisableIOSpeedOptimize(void)
 817:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** {
 1225              		.loc 1 817 1 is_stmt 1 view -0
 1226              		.cfi_startproc
 1227              		@ args = 0, pretend = 0, frame = 0
 1228              		@ frame_needed = 0, uses_anonymous_args = 0
 1229              		@ link register save eliminated.
 818:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** #if defined(SYSCFG_CCCSR_HSLV)
 819:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   CLEAR_BIT(SYSCFG->CCCSR, SYSCFG_CCCSR_HSLV);
 1230              		.loc 1 819 3 view .LVU227
 1231 0000 024A     		ldr	r2, .L120
 1232 0002 136A     		ldr	r3, [r2, #32]
 1233 0004 23F48033 		bic	r3, r3, #65536
 1234 0008 1362     		str	r3, [r2, #32]
 820:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** #else
 821:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   CLEAR_BIT(SYSCFG->CCCSR, (SYSCFG_CCCSR_HSLV0| SYSCFG_CCCSR_HSLV1 | SYSCFG_CCCSR_HSLV2  | SYSCFG_C
 822:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** #endif   /* SYSCFG_CCCSR_HSLV */
 823:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** }
 1235              		.loc 1 823 1 is_stmt 0 view .LVU228
 1236 000a 7047     		bx	lr
 1237              	.L121:
 1238              		.align	2
 1239              	.L120:
 1240 000c 00040058 		.word	1476396032
 1241              		.cfi_endproc
 1242              	.LFE176:
 1244              		.section	.text.HAL_SYSCFG_CompensationCodeSelect,"ax",%progbits
 1245              		.align	1
 1246              		.global	HAL_SYSCFG_CompensationCodeSelect
 1247              		.syntax unified
 1248              		.thumb
 1249              		.thumb_func
 1251              	HAL_SYSCFG_CompensationCodeSelect:
 1252              	.LVL38:
 1253              	.LFB177:
 824:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 825:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /**
 826:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @brief  Code selection for the I/O Compensation cell
 827:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @param  SYSCFG_CompCode: Selects the code to be applied for the I/O compensation cell
 828:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *   This parameter can be one of the following values:
 829:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *   @arg SYSCFG_CELL_CODE : Select Code from the cell (available in the SYSCFG_CCVR)
 830:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *   @arg SYSCFG_REGISTER_CODE: Select Code from the SYSCFG compensation cell code register (SYSCF
 831:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @retval None
 832:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   */
 833:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** void HAL_SYSCFG_CompensationCodeSelect(uint32_t SYSCFG_CompCode)
 834:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** {
 1254              		.loc 1 834 1 is_stmt 1 view -0
 1255              		.cfi_startproc
 1256              		@ args = 0, pretend = 0, frame = 0
 1257              		@ frame_needed = 0, uses_anonymous_args = 0
 1258              		@ link register save eliminated.
 835:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   /* Check the parameter */
 836:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   assert_param(IS_SYSCFG_CODE_SELECT(SYSCFG_CompCode));
 1259              		.loc 1 836 3 view .LVU230
 837:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   MODIFY_REG(SYSCFG->CCCSR, SYSCFG_CCCSR_CS, (uint32_t)(SYSCFG_CompCode));
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s 			page 38


 1260              		.loc 1 837 3 view .LVU231
 1261 0000 034A     		ldr	r2, .L123
 1262 0002 136A     		ldr	r3, [r2, #32]
 1263 0004 23F00203 		bic	r3, r3, #2
 1264 0008 0343     		orrs	r3, r3, r0
 1265 000a 1362     		str	r3, [r2, #32]
 838:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** }
 1266              		.loc 1 838 1 is_stmt 0 view .LVU232
 1267 000c 7047     		bx	lr
 1268              	.L124:
 1269 000e 00BF     		.align	2
 1270              	.L123:
 1271 0010 00040058 		.word	1476396032
 1272              		.cfi_endproc
 1273              	.LFE177:
 1275              		.section	.text.HAL_SYSCFG_CompensationCodeConfig,"ax",%progbits
 1276              		.align	1
 1277              		.global	HAL_SYSCFG_CompensationCodeConfig
 1278              		.syntax unified
 1279              		.thumb
 1280              		.thumb_func
 1282              	HAL_SYSCFG_CompensationCodeConfig:
 1283              	.LVL39:
 1284              	.LFB178:
 839:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 840:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /**
 841:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @brief  Code selection for the I/O Compensation cell
 842:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @param  SYSCFG_PMOSCode: PMOS compensation code
 843:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *         This code is applied to the I/O compensation cell when the CS bit of the
 844:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *          SYSCFG_CMPCR is set
 845:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @param  SYSCFG_NMOSCode: NMOS compensation code
 846:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *         This code is applied to the I/O compensation cell when the CS bit of the
 847:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *          SYSCFG_CMPCR is set
 848:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @retval None
 849:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   */
 850:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** void HAL_SYSCFG_CompensationCodeConfig(uint32_t SYSCFG_PMOSCode, uint32_t SYSCFG_NMOSCode )
 851:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** {
 1285              		.loc 1 851 1 is_stmt 1 view -0
 1286              		.cfi_startproc
 1287              		@ args = 0, pretend = 0, frame = 0
 1288              		@ frame_needed = 0, uses_anonymous_args = 0
 1289              		@ link register save eliminated.
 852:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   /* Check the parameter */
 853:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   assert_param(IS_SYSCFG_CODE_CONFIG(SYSCFG_PMOSCode));
 1290              		.loc 1 853 3 view .LVU234
 854:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   assert_param(IS_SYSCFG_CODE_CONFIG(SYSCFG_NMOSCode));
 1291              		.loc 1 854 3 view .LVU235
 855:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   MODIFY_REG(SYSCFG->CCCR, SYSCFG_CCCR_NCC|SYSCFG_CCCR_PCC, (((uint32_t)(SYSCFG_PMOSCode)<< 4)|(uin
 1292              		.loc 1 855 3 view .LVU236
 1293 0000 044A     		ldr	r2, .L126
 1294 0002 936A     		ldr	r3, [r2, #40]
 1295 0004 23F0FF03 		bic	r3, r3, #255
 1296 0008 41EA0011 		orr	r1, r1, r0, lsl #4
 1297              	.LVL40:
 1298              		.loc 1 855 3 is_stmt 0 view .LVU237
 1299 000c 0B43     		orrs	r3, r3, r1
 1300 000e 9362     		str	r3, [r2, #40]
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s 			page 39


 856:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** }
 1301              		.loc 1 856 1 view .LVU238
 1302 0010 7047     		bx	lr
 1303              	.L127:
 1304 0012 00BF     		.align	2
 1305              	.L126:
 1306 0014 00040058 		.word	1476396032
 1307              		.cfi_endproc
 1308              	.LFE178:
 1310              		.section	.text.HAL_SYSCFG_ADC2ALT_Rout0Config,"ax",%progbits
 1311              		.align	1
 1312              		.global	HAL_SYSCFG_ADC2ALT_Rout0Config
 1313              		.syntax unified
 1314              		.thumb
 1315              		.thumb_func
 1317              	HAL_SYSCFG_ADC2ALT_Rout0Config:
 1318              	.LVL41:
 1319              	.LFB179:
 857:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 858:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** #if defined(SYSCFG_CCCR_NCC_MMC)
 859:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /**
 860:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @brief  Code selection for the I/O Compensation cell
 861:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @param  SYSCFG_PMOSCode: VDDMMC PMOS compensation code
 862:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *         This code is applied to the I/O compensation cell when the CS bit of the
 863:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *          SYSCFG_CMPCR is set
 864:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @param  SYSCFG_NMOSCode: VDDMMC NMOS compensation code
 865:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *         This code is applied to the I/O compensation cell when the CS bit of the
 866:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *          SYSCFG_CMPCR is set
 867:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @retval None
 868:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   */
 869:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** void HAL_SYSCFG_VDDMMC_CompensationCodeConfig(uint32_t SYSCFG_PMOSCode, uint32_t SYSCFG_NMOSCode )
 870:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** {
 871:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   /* Check the parameter */
 872:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   assert_param(IS_SYSCFG_CODE_CONFIG(SYSCFG_PMOSCode));
 873:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   assert_param(IS_SYSCFG_CODE_CONFIG(SYSCFG_NMOSCode));
 874:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   MODIFY_REG(SYSCFG->CCCR, (SYSCFG_CCCR_NCC_MMC | SYSCFG_CCCR_PCC_MMC), (((uint32_t)(SYSCFG_PMOSCod
 875:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** }
 876:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** #endif /* SYSCFG_CCCR_NCC_MMC */
 877:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 878:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** #if defined(SYSCFG_ADC2ALT_ADC2_ROUT0)
 879:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /** @brief  SYSCFG ADC2 internal input alternate connection macros
 880:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @param Adc2AltRout0 This parameter can be a value of :
 881:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *     @arg @ref SYSCFG_ADC2_ROUT0_DAC1_1   DAC1_out1 connected to ADC2 VINP[16]
 882:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *     @arg @ref SYSCFG_ADC2_ROUT0_VBAT4    VBAT/4 connected to ADC2 VINP[16]
 883:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   */
 884:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** void HAL_SYSCFG_ADC2ALT_Rout0Config(uint32_t Adc2AltRout0)
 885:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** {
 1320              		.loc 1 885 1 is_stmt 1 view -0
 1321              		.cfi_startproc
 1322              		@ args = 0, pretend = 0, frame = 0
 1323              		@ frame_needed = 0, uses_anonymous_args = 0
 1324              		@ link register save eliminated.
 886:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   /* Check the parameters */
 887:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   assert_param(IS_SYSCFG_ADC2ALT_ROUT0(Adc2AltRout0));
 1325              		.loc 1 887 3 view .LVU240
 888:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 889:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   MODIFY_REG(SYSCFG->ADC2ALT, SYSCFG_ADC2ALT_ADC2_ROUT0, Adc2AltRout0);
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s 			page 40


 1326              		.loc 1 889 3 view .LVU241
 1327 0000 034A     		ldr	r2, .L129
 1328 0002 136B     		ldr	r3, [r2, #48]
 1329 0004 23F00103 		bic	r3, r3, #1
 1330 0008 0343     		orrs	r3, r3, r0
 1331 000a 1363     		str	r3, [r2, #48]
 890:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** }
 1332              		.loc 1 890 1 is_stmt 0 view .LVU242
 1333 000c 7047     		bx	lr
 1334              	.L130:
 1335 000e 00BF     		.align	2
 1336              	.L129:
 1337 0010 00040058 		.word	1476396032
 1338              		.cfi_endproc
 1339              	.LFE179:
 1341              		.section	.text.HAL_SYSCFG_ADC2ALT_Rout1Config,"ax",%progbits
 1342              		.align	1
 1343              		.global	HAL_SYSCFG_ADC2ALT_Rout1Config
 1344              		.syntax unified
 1345              		.thumb
 1346              		.thumb_func
 1348              	HAL_SYSCFG_ADC2ALT_Rout1Config:
 1349              	.LVL42:
 1350              	.LFB180:
 891:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** #endif /*SYSCFG_ADC2ALT_ADC2_ROUT0*/
 892:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 893:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** #if defined(SYSCFG_ADC2ALT_ADC2_ROUT1)
 894:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /** @brief  SYSCFG ADC2 internal input alternate connection macros
 895:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @param Adc2AltRout1  This parameter can be a value of :
 896:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *     @arg @ref SYSCFG_ADC2_ROUT1_DAC1_2   DAC1_out2 connected to ADC2 VINP[17]
 897:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *     @arg @ref SYSCFG_ADC2_ROUT1_VREFINT  VREFINT connected to ADC2 VINP[17]
 898:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   */
 899:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** void HAL_SYSCFG_ADC2ALT_Rout1Config(uint32_t Adc2AltRout1)
 900:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** {
 1351              		.loc 1 900 1 is_stmt 1 view -0
 1352              		.cfi_startproc
 1353              		@ args = 0, pretend = 0, frame = 0
 1354              		@ frame_needed = 0, uses_anonymous_args = 0
 1355              		@ link register save eliminated.
 901:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   /* Check the parameters */
 902:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   assert_param(IS_SYSCFG_ADC2ALT_ROUT1(Adc2AltRout1));
 1356              		.loc 1 902 3 view .LVU244
 903:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 904:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   MODIFY_REG(SYSCFG->ADC2ALT, SYSCFG_ADC2ALT_ADC2_ROUT1, Adc2AltRout1);
 1357              		.loc 1 904 3 view .LVU245
 1358 0000 034A     		ldr	r2, .L132
 1359 0002 136B     		ldr	r3, [r2, #48]
 1360 0004 23F00203 		bic	r3, r3, #2
 1361 0008 0343     		orrs	r3, r3, r0
 1362 000a 1363     		str	r3, [r2, #48]
 905:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** }
 1363              		.loc 1 905 1 is_stmt 0 view .LVU246
 1364 000c 7047     		bx	lr
 1365              	.L133:
 1366 000e 00BF     		.align	2
 1367              	.L132:
 1368 0010 00040058 		.word	1476396032
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s 			page 41


 1369              		.cfi_endproc
 1370              	.LFE180:
 1372              		.section	.text.HAL_DBGMCU_EnableDBGSleepMode,"ax",%progbits
 1373              		.align	1
 1374              		.global	HAL_DBGMCU_EnableDBGSleepMode
 1375              		.syntax unified
 1376              		.thumb
 1377              		.thumb_func
 1379              	HAL_DBGMCU_EnableDBGSleepMode:
 1380              	.LFB181:
 906:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** #endif /*SYSCFG_ADC2ALT_ADC2_ROUT1*/
 907:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 908:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /**
 909:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @brief  Enable the Debug Module during Domain1/CDomain SLEEP mode
 910:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @retval None
 911:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   */
 912:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** void HAL_DBGMCU_EnableDBGSleepMode(void)
 913:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** {
 1381              		.loc 1 913 1 is_stmt 1 view -0
 1382              		.cfi_startproc
 1383              		@ args = 0, pretend = 0, frame = 0
 1384              		@ frame_needed = 0, uses_anonymous_args = 0
 1385              		@ link register save eliminated.
 914:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   SET_BIT(DBGMCU->CR, DBGMCU_CR_DBG_SLEEPD1);
 1386              		.loc 1 914 3 view .LVU248
 1387 0000 024A     		ldr	r2, .L135
 1388 0002 5368     		ldr	r3, [r2, #4]
 1389 0004 43F00103 		orr	r3, r3, #1
 1390 0008 5360     		str	r3, [r2, #4]
 915:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** }
 1391              		.loc 1 915 1 is_stmt 0 view .LVU249
 1392 000a 7047     		bx	lr
 1393              	.L136:
 1394              		.align	2
 1395              	.L135:
 1396 000c 0010005C 		.word	1543507968
 1397              		.cfi_endproc
 1398              	.LFE181:
 1400              		.section	.text.HAL_DBGMCU_DisableDBGSleepMode,"ax",%progbits
 1401              		.align	1
 1402              		.global	HAL_DBGMCU_DisableDBGSleepMode
 1403              		.syntax unified
 1404              		.thumb
 1405              		.thumb_func
 1407              	HAL_DBGMCU_DisableDBGSleepMode:
 1408              	.LFB182:
 916:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 917:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /**
 918:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @brief  Disable the Debug Module during Domain1/CDomain SLEEP mode
 919:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @retval None
 920:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   */
 921:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** void HAL_DBGMCU_DisableDBGSleepMode(void)
 922:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** {
 1409              		.loc 1 922 1 is_stmt 1 view -0
 1410              		.cfi_startproc
 1411              		@ args = 0, pretend = 0, frame = 0
 1412              		@ frame_needed = 0, uses_anonymous_args = 0
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s 			page 42


 1413              		@ link register save eliminated.
 923:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   CLEAR_BIT(DBGMCU->CR, DBGMCU_CR_DBG_SLEEPD1);
 1414              		.loc 1 923 3 view .LVU251
 1415 0000 024A     		ldr	r2, .L138
 1416 0002 5368     		ldr	r3, [r2, #4]
 1417 0004 23F00103 		bic	r3, r3, #1
 1418 0008 5360     		str	r3, [r2, #4]
 924:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** }
 1419              		.loc 1 924 1 is_stmt 0 view .LVU252
 1420 000a 7047     		bx	lr
 1421              	.L139:
 1422              		.align	2
 1423              	.L138:
 1424 000c 0010005C 		.word	1543507968
 1425              		.cfi_endproc
 1426              	.LFE182:
 1428              		.section	.text.HAL_DBGMCU_EnableDBGStopMode,"ax",%progbits
 1429              		.align	1
 1430              		.global	HAL_DBGMCU_EnableDBGStopMode
 1431              		.syntax unified
 1432              		.thumb
 1433              		.thumb_func
 1435              	HAL_DBGMCU_EnableDBGStopMode:
 1436              	.LFB183:
 925:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 926:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 927:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /**
 928:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @brief  Enable the Debug Module during Domain1/CDomain STOP mode
 929:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @retval None
 930:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   */
 931:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** void HAL_DBGMCU_EnableDBGStopMode(void)
 932:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** {
 1437              		.loc 1 932 1 is_stmt 1 view -0
 1438              		.cfi_startproc
 1439              		@ args = 0, pretend = 0, frame = 0
 1440              		@ frame_needed = 0, uses_anonymous_args = 0
 1441              		@ link register save eliminated.
 933:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   SET_BIT(DBGMCU->CR, DBGMCU_CR_DBG_STOPD1);
 1442              		.loc 1 933 3 view .LVU254
 1443 0000 024A     		ldr	r2, .L141
 1444 0002 5368     		ldr	r3, [r2, #4]
 1445 0004 43F00203 		orr	r3, r3, #2
 1446 0008 5360     		str	r3, [r2, #4]
 934:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** }
 1447              		.loc 1 934 1 is_stmt 0 view .LVU255
 1448 000a 7047     		bx	lr
 1449              	.L142:
 1450              		.align	2
 1451              	.L141:
 1452 000c 0010005C 		.word	1543507968
 1453              		.cfi_endproc
 1454              	.LFE183:
 1456              		.section	.text.HAL_DBGMCU_DisableDBGStopMode,"ax",%progbits
 1457              		.align	1
 1458              		.global	HAL_DBGMCU_DisableDBGStopMode
 1459              		.syntax unified
 1460              		.thumb
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s 			page 43


 1461              		.thumb_func
 1463              	HAL_DBGMCU_DisableDBGStopMode:
 1464              	.LFB184:
 935:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 936:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /**
 937:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @brief  Disable the Debug Module during Domain1/CDomain STOP mode
 938:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @retval None
 939:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   */
 940:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** void HAL_DBGMCU_DisableDBGStopMode(void)
 941:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** {
 1465              		.loc 1 941 1 is_stmt 1 view -0
 1466              		.cfi_startproc
 1467              		@ args = 0, pretend = 0, frame = 0
 1468              		@ frame_needed = 0, uses_anonymous_args = 0
 1469              		@ link register save eliminated.
 942:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   CLEAR_BIT(DBGMCU->CR, DBGMCU_CR_DBG_STOPD1);
 1470              		.loc 1 942 3 view .LVU257
 1471 0000 024A     		ldr	r2, .L144
 1472 0002 5368     		ldr	r3, [r2, #4]
 1473 0004 23F00203 		bic	r3, r3, #2
 1474 0008 5360     		str	r3, [r2, #4]
 943:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** }
 1475              		.loc 1 943 1 is_stmt 0 view .LVU258
 1476 000a 7047     		bx	lr
 1477              	.L145:
 1478              		.align	2
 1479              	.L144:
 1480 000c 0010005C 		.word	1543507968
 1481              		.cfi_endproc
 1482              	.LFE184:
 1484              		.section	.text.HAL_DBGMCU_EnableDBGStandbyMode,"ax",%progbits
 1485              		.align	1
 1486              		.global	HAL_DBGMCU_EnableDBGStandbyMode
 1487              		.syntax unified
 1488              		.thumb
 1489              		.thumb_func
 1491              	HAL_DBGMCU_EnableDBGStandbyMode:
 1492              	.LFB185:
 944:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 945:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /**
 946:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @brief  Enable the Debug Module during Domain1/CDomain STANDBY mode
 947:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @retval None
 948:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   */
 949:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** void HAL_DBGMCU_EnableDBGStandbyMode(void)
 950:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** {
 1493              		.loc 1 950 1 is_stmt 1 view -0
 1494              		.cfi_startproc
 1495              		@ args = 0, pretend = 0, frame = 0
 1496              		@ frame_needed = 0, uses_anonymous_args = 0
 1497              		@ link register save eliminated.
 951:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   SET_BIT(DBGMCU->CR, DBGMCU_CR_DBG_STANDBYD1);
 1498              		.loc 1 951 3 view .LVU260
 1499 0000 024A     		ldr	r2, .L147
 1500 0002 5368     		ldr	r3, [r2, #4]
 1501 0004 43F00403 		orr	r3, r3, #4
 1502 0008 5360     		str	r3, [r2, #4]
 952:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** }
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s 			page 44


 1503              		.loc 1 952 1 is_stmt 0 view .LVU261
 1504 000a 7047     		bx	lr
 1505              	.L148:
 1506              		.align	2
 1507              	.L147:
 1508 000c 0010005C 		.word	1543507968
 1509              		.cfi_endproc
 1510              	.LFE185:
 1512              		.section	.text.HAL_DBGMCU_DisableDBGStandbyMode,"ax",%progbits
 1513              		.align	1
 1514              		.global	HAL_DBGMCU_DisableDBGStandbyMode
 1515              		.syntax unified
 1516              		.thumb
 1517              		.thumb_func
 1519              	HAL_DBGMCU_DisableDBGStandbyMode:
 1520              	.LFB186:
 953:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 954:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /**
 955:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @brief  Disable the Debug Module during Domain1/CDomain STANDBY mode
 956:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @retval None
 957:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   */
 958:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** void HAL_DBGMCU_DisableDBGStandbyMode(void)
 959:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** {
 1521              		.loc 1 959 1 is_stmt 1 view -0
 1522              		.cfi_startproc
 1523              		@ args = 0, pretend = 0, frame = 0
 1524              		@ frame_needed = 0, uses_anonymous_args = 0
 1525              		@ link register save eliminated.
 960:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   CLEAR_BIT(DBGMCU->CR, DBGMCU_CR_DBG_STANDBYD1);
 1526              		.loc 1 960 3 view .LVU263
 1527 0000 024A     		ldr	r2, .L150
 1528 0002 5368     		ldr	r3, [r2, #4]
 1529 0004 23F00403 		bic	r3, r3, #4
 1530 0008 5360     		str	r3, [r2, #4]
 961:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** }
 1531              		.loc 1 961 1 is_stmt 0 view .LVU264
 1532 000a 7047     		bx	lr
 1533              	.L151:
 1534              		.align	2
 1535              	.L150:
 1536 000c 0010005C 		.word	1543507968
 1537              		.cfi_endproc
 1538              	.LFE186:
 1540              		.section	.text.HAL_EnableDomain3DBGStopMode,"ax",%progbits
 1541              		.align	1
 1542              		.global	HAL_EnableDomain3DBGStopMode
 1543              		.syntax unified
 1544              		.thumb
 1545              		.thumb_func
 1547              	HAL_EnableDomain3DBGStopMode:
 1548              	.LFB187:
 962:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 963:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** #if defined(DUAL_CORE)
 964:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /**
 965:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @brief  Enable the Debug Module during Domain1 SLEEP mode
 966:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @retval None
 967:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   */
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s 			page 45


 968:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** void HAL_EnableDomain2DBGSleepMode(void)
 969:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** {
 970:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   SET_BIT(DBGMCU->CR, DBGMCU_CR_DBG_SLEEPD2);
 971:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** }
 972:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 973:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /**
 974:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @brief  Disable the Debug Module during Domain2 SLEEP mode
 975:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @retval None
 976:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   */
 977:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** void HAL_DisableDomain2DBGSleepMode(void)
 978:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** {
 979:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   CLEAR_BIT(DBGMCU->CR, DBGMCU_CR_DBG_SLEEPD2);
 980:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** }
 981:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 982:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /**
 983:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @brief  Enable the Debug Module during Domain2 STOP mode
 984:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @retval None
 985:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   */
 986:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** void HAL_EnableDomain2DBGStopMode(void)
 987:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** {
 988:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   SET_BIT(DBGMCU->CR, DBGMCU_CR_DBG_STOPD2);
 989:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** }
 990:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
 991:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /**
 992:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @brief  Disable the Debug Module during Domain2 STOP mode
 993:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @retval None
 994:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   */
 995:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** void HAL_DisableDomain2DBGStopMode(void)
 996:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** {
 997:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   CLEAR_BIT(DBGMCU->CR, DBGMCU_CR_DBG_STOPD2);
 998:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** }
 999:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
1000:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /**
1001:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @brief  Enable the Debug Module during Domain2 STANDBY mode
1002:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @retval None
1003:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   */
1004:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** void HAL_EnableDomain2DBGStandbyMode(void)
1005:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** {
1006:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   SET_BIT(DBGMCU->CR, DBGMCU_CR_DBG_STANDBYD2);
1007:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** }
1008:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
1009:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /**
1010:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @brief  Disable the Debug Module during Domain2 STANDBY mode
1011:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @retval None
1012:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   */
1013:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** void HAL_DisableDomain2DBGStandbyMode(void)
1014:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** {
1015:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   CLEAR_BIT(DBGMCU->CR, DBGMCU_CR_DBG_STANDBYD2);
1016:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** }
1017:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** #endif /*DUAL_CORE*/
1018:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
1019:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** #if defined(DBGMCU_CR_DBG_STOPD3)
1020:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /**
1021:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @brief  Enable the Debug Module during Domain3/SRDomain STOP mode
1022:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @retval None
1023:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   */
1024:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** void HAL_EnableDomain3DBGStopMode(void)
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s 			page 46


1025:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** {
 1549              		.loc 1 1025 1 is_stmt 1 view -0
 1550              		.cfi_startproc
 1551              		@ args = 0, pretend = 0, frame = 0
 1552              		@ frame_needed = 0, uses_anonymous_args = 0
 1553              		@ link register save eliminated.
1026:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   SET_BIT(DBGMCU->CR, DBGMCU_CR_DBG_STOPD3);
 1554              		.loc 1 1026 3 view .LVU266
 1555 0000 024A     		ldr	r2, .L153
 1556 0002 5368     		ldr	r3, [r2, #4]
 1557 0004 43F08003 		orr	r3, r3, #128
 1558 0008 5360     		str	r3, [r2, #4]
1027:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** }
 1559              		.loc 1 1027 1 is_stmt 0 view .LVU267
 1560 000a 7047     		bx	lr
 1561              	.L154:
 1562              		.align	2
 1563              	.L153:
 1564 000c 0010005C 		.word	1543507968
 1565              		.cfi_endproc
 1566              	.LFE187:
 1568              		.section	.text.HAL_DisableDomain3DBGStopMode,"ax",%progbits
 1569              		.align	1
 1570              		.global	HAL_DisableDomain3DBGStopMode
 1571              		.syntax unified
 1572              		.thumb
 1573              		.thumb_func
 1575              	HAL_DisableDomain3DBGStopMode:
 1576              	.LFB188:
1028:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
1029:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /**
1030:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @brief  Disable the Debug Module during Domain3/SRDomain STOP mode
1031:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @retval None
1032:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   */
1033:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** void HAL_DisableDomain3DBGStopMode(void)
1034:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** {
 1577              		.loc 1 1034 1 is_stmt 1 view -0
 1578              		.cfi_startproc
 1579              		@ args = 0, pretend = 0, frame = 0
 1580              		@ frame_needed = 0, uses_anonymous_args = 0
 1581              		@ link register save eliminated.
1035:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   CLEAR_BIT(DBGMCU->CR, DBGMCU_CR_DBG_STOPD3);
 1582              		.loc 1 1035 3 view .LVU269
 1583 0000 024A     		ldr	r2, .L156
 1584 0002 5368     		ldr	r3, [r2, #4]
 1585 0004 23F08003 		bic	r3, r3, #128
 1586 0008 5360     		str	r3, [r2, #4]
1036:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** }
 1587              		.loc 1 1036 1 is_stmt 0 view .LVU270
 1588 000a 7047     		bx	lr
 1589              	.L157:
 1590              		.align	2
 1591              	.L156:
 1592 000c 0010005C 		.word	1543507968
 1593              		.cfi_endproc
 1594              	.LFE188:
 1596              		.section	.text.HAL_EnableDomain3DBGStandbyMode,"ax",%progbits
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s 			page 47


 1597              		.align	1
 1598              		.global	HAL_EnableDomain3DBGStandbyMode
 1599              		.syntax unified
 1600              		.thumb
 1601              		.thumb_func
 1603              	HAL_EnableDomain3DBGStandbyMode:
 1604              	.LFB189:
1037:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** #endif /*DBGMCU_CR_DBG_STOPD3*/
1038:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
1039:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** #if defined(DBGMCU_CR_DBG_STANDBYD3)
1040:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /**
1041:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @brief  Enable the Debug Module during Domain3/SRDomain STANDBY mode
1042:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @retval None
1043:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   */
1044:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** void HAL_EnableDomain3DBGStandbyMode(void)
1045:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** {
 1605              		.loc 1 1045 1 is_stmt 1 view -0
 1606              		.cfi_startproc
 1607              		@ args = 0, pretend = 0, frame = 0
 1608              		@ frame_needed = 0, uses_anonymous_args = 0
 1609              		@ link register save eliminated.
1046:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   SET_BIT(DBGMCU->CR, DBGMCU_CR_DBG_STANDBYD3);
 1610              		.loc 1 1046 3 view .LVU272
 1611 0000 024A     		ldr	r2, .L159
 1612 0002 5368     		ldr	r3, [r2, #4]
 1613 0004 43F48073 		orr	r3, r3, #256
 1614 0008 5360     		str	r3, [r2, #4]
1047:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** }
 1615              		.loc 1 1047 1 is_stmt 0 view .LVU273
 1616 000a 7047     		bx	lr
 1617              	.L160:
 1618              		.align	2
 1619              	.L159:
 1620 000c 0010005C 		.word	1543507968
 1621              		.cfi_endproc
 1622              	.LFE189:
 1624              		.section	.text.HAL_DisableDomain3DBGStandbyMode,"ax",%progbits
 1625              		.align	1
 1626              		.global	HAL_DisableDomain3DBGStandbyMode
 1627              		.syntax unified
 1628              		.thumb
 1629              		.thumb_func
 1631              	HAL_DisableDomain3DBGStandbyMode:
 1632              	.LFB190:
1048:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
1049:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /**
1050:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @brief  Disable the Debug Module during Domain3/SRDomain STANDBY mode
1051:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @retval None
1052:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   */
1053:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** void HAL_DisableDomain3DBGStandbyMode(void)
1054:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** {
 1633              		.loc 1 1054 1 is_stmt 1 view -0
 1634              		.cfi_startproc
 1635              		@ args = 0, pretend = 0, frame = 0
 1636              		@ frame_needed = 0, uses_anonymous_args = 0
 1637              		@ link register save eliminated.
1055:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   CLEAR_BIT(DBGMCU->CR, DBGMCU_CR_DBG_STANDBYD3);
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s 			page 48


 1638              		.loc 1 1055 3 view .LVU275
 1639 0000 024A     		ldr	r2, .L162
 1640 0002 5368     		ldr	r3, [r2, #4]
 1641 0004 23F48073 		bic	r3, r3, #256
 1642 0008 5360     		str	r3, [r2, #4]
1056:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** }
 1643              		.loc 1 1056 1 is_stmt 0 view .LVU276
 1644 000a 7047     		bx	lr
 1645              	.L163:
 1646              		.align	2
 1647              	.L162:
 1648 000c 0010005C 		.word	1543507968
 1649              		.cfi_endproc
 1650              	.LFE190:
 1652              		.section	.text.HAL_SetFMCMemorySwappingConfig,"ax",%progbits
 1653              		.align	1
 1654              		.global	HAL_SetFMCMemorySwappingConfig
 1655              		.syntax unified
 1656              		.thumb
 1657              		.thumb_func
 1659              	HAL_SetFMCMemorySwappingConfig:
 1660              	.LVL43:
 1661              	.LFB191:
1057:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** #endif /*DBGMCU_CR_DBG_STANDBYD3*/
1058:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
1059:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /**
1060:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @brief  Set the FMC Memory Mapping Swapping config.
1061:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @param  BankMapConfig: Defines the FMC Bank mapping configuration. This parameter can be
1062:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****             FMC_SWAPBMAP_DISABLE, FMC_SWAPBMAP_SDRAM_SRAM, FMC_SWAPBMAP_SDRAMB2
1063:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @retval HAL state
1064:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   */
1065:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** void HAL_SetFMCMemorySwappingConfig(uint32_t BankMapConfig)
1066:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** {
 1662              		.loc 1 1066 1 is_stmt 1 view -0
 1663              		.cfi_startproc
 1664              		@ args = 0, pretend = 0, frame = 0
 1665              		@ frame_needed = 0, uses_anonymous_args = 0
 1666              		@ link register save eliminated.
1067:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   /* Check the parameter */
1068:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   assert_param(IS_FMC_SWAPBMAP_MODE(BankMapConfig));
 1667              		.loc 1 1068 3 view .LVU278
1069:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   MODIFY_REG(FMC_Bank1_R->BTCR[0], FMC_BCR1_BMAP, BankMapConfig);
 1668              		.loc 1 1069 3 view .LVU279
 1669 0000 034A     		ldr	r2, .L165
 1670 0002 1368     		ldr	r3, [r2]
 1671 0004 23F04073 		bic	r3, r3, #********
 1672 0008 0343     		orrs	r3, r3, r0
 1673 000a 1360     		str	r3, [r2]
1070:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** }
 1674              		.loc 1 1070 1 is_stmt 0 view .LVU280
 1675 000c 7047     		bx	lr
 1676              	.L166:
 1677 000e 00BF     		.align	2
 1678              	.L165:
 1679 0010 ******** 		.word	**********
 1680              		.cfi_endproc
 1681              	.LFE191:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s 			page 49


 1683              		.section	.text.HAL_GetFMCMemorySwappingConfig,"ax",%progbits
 1684              		.align	1
 1685              		.global	HAL_GetFMCMemorySwappingConfig
 1686              		.syntax unified
 1687              		.thumb
 1688              		.thumb_func
 1690              	HAL_GetFMCMemorySwappingConfig:
 1691              	.LFB192:
1071:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
1072:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /**
1073:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @brief  Get FMC Bank mapping mode.
1074:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @retval The FMC Bank mapping mode. This parameter can be
1075:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****             FMC_SWAPBMAP_DISABLE, FMC_SWAPBMAP_SDRAM_SRAM, FMC_SWAPBMAP_SDRAMB2
1076:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** */
1077:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** uint32_t HAL_GetFMCMemorySwappingConfig(void)
1078:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** {
 1692              		.loc 1 1078 1 is_stmt 1 view -0
 1693              		.cfi_startproc
 1694              		@ args = 0, pretend = 0, frame = 0
 1695              		@ frame_needed = 0, uses_anonymous_args = 0
 1696              		@ link register save eliminated.
1079:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   return READ_BIT(FMC_Bank1_R->BTCR[0], FMC_BCR1_BMAP);
 1697              		.loc 1 1079 3 view .LVU282
 1698              		.loc 1 1079 10 is_stmt 0 view .LVU283
 1699 0000 024B     		ldr	r3, .L168
 1700 0002 1868     		ldr	r0, [r3]
1080:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** }
 1701              		.loc 1 1080 1 view .LVU284
 1702 0004 00F04070 		and	r0, r0, #********
 1703 0008 7047     		bx	lr
 1704              	.L169:
 1705 000a 00BF     		.align	2
 1706              	.L168:
 1707 000c ******** 		.word	**********
 1708              		.cfi_endproc
 1709              	.LFE192:
 1711              		.section	.text.HAL_EXTI_EdgeConfig,"ax",%progbits
 1712              		.align	1
 1713              		.global	HAL_EXTI_EdgeConfig
 1714              		.syntax unified
 1715              		.thumb
 1716              		.thumb_func
 1718              	HAL_EXTI_EdgeConfig:
 1719              	.LVL44:
 1720              	.LFB193:
1081:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
1082:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /**
1083:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @brief  Configure the EXTI input event line edge
1084:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @note    No edge configuration for direct lines but for configurable lines:(EXTI_LINE0..EXTI_LI
1085:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *          EXTI_LINE49,EXTI_LINE51,EXTI_LINE82,EXTI_LINE84,EXTI_LINE85 and EXTI_LINE86.
1086:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @param   EXTI_Line: Specifies the EXTI LINE, it can be one of the following values,
1087:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *         (EXTI_LINE0....EXTI_LINE87)excluding :line45, line81,line83 which are reserved
1088:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @param   EXTI_Edge: Specifies  EXTI line Edge used.
1089:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *          This parameter can be one of the following values :
1090:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *   @arg EXTI_RISING_EDGE : Configurable line, with Rising edge trigger detection
1091:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *   @arg EXTI_FALLING_EDGE: Configurable line, with Falling edge trigger detection
1092:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @retval None
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s 			page 50


1093:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   */
1094:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** void HAL_EXTI_EdgeConfig(uint32_t EXTI_Line , uint32_t EXTI_Edge )
1095:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** {
 1721              		.loc 1 1095 1 is_stmt 1 view -0
 1722              		.cfi_startproc
 1723              		@ args = 0, pretend = 0, frame = 0
 1724              		@ frame_needed = 0, uses_anonymous_args = 0
 1725              		@ link register save eliminated.
 1726              		.loc 1 1095 1 is_stmt 0 view .LVU286
 1727 0000 30B4     		push	{r4, r5}
 1728              	.LCFI6:
 1729              		.cfi_def_cfa_offset 8
 1730              		.cfi_offset 4, -8
 1731              		.cfi_offset 5, -4
1096:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   /* Check the parameter */
1097:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   assert_param(IS_HAL_EXTI_CONFIG_LINE(EXTI_Line));
 1732              		.loc 1 1097 3 is_stmt 1 view .LVU287
1098:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   assert_param(IS_EXTI_EDGE_LINE(EXTI_Edge));
 1733              		.loc 1 1098 3 view .LVU288
1099:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
1100:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   /* Clear Rising Falling edge configuration */
1101:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   CLEAR_BIT(*(__IO uint32_t *) (((uint32_t) &(EXTI->FTSR1)) + ((EXTI_Line >> 5 ) * 0x20UL)), (uint3
 1734              		.loc 1 1101 3 view .LVU289
 1735 0002 4309     		lsrs	r3, r0, #5
 1736 0004 20F01F0C 		bic	ip, r0, #31
 1737 0008 104C     		ldr	r4, .L174
 1738 000a 5CF80450 		ldr	r5, [ip, r4]
 1739 000e 00F01F00 		and	r0, r0, #31
 1740              	.LVL45:
 1741              		.loc 1 1101 3 is_stmt 0 view .LVU290
 1742 0012 0122     		movs	r2, #1
 1743 0014 8240     		lsls	r2, r2, r0
 1744 0016 25EA0205 		bic	r5, r5, r2
 1745 001a 4CF80450 		str	r5, [ip, r4]
1102:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   CLEAR_BIT( *(__IO uint32_t *) (((uint32_t) &(EXTI->RTSR1)) + ((EXTI_Line >> 5 ) * 0x20UL)), (uint
 1746              		.loc 1 1102 3 is_stmt 1 view .LVU291
 1747 001e 03F13073 		add	r3, r3, #46137344
 1748 0022 5B01     		lsls	r3, r3, #5
 1749 0024 1868     		ldr	r0, [r3]
 1750 0026 20EA0200 		bic	r0, r0, r2
 1751 002a 1860     		str	r0, [r3]
1103:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
1104:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   if( (EXTI_Edge & EXTI_RISING_EDGE) == EXTI_RISING_EDGE)
 1752              		.loc 1 1104 3 view .LVU292
 1753              		.loc 1 1104 5 is_stmt 0 view .LVU293
 1754 002c 11F4801F 		tst	r1, #1048576
 1755 0030 02D0     		beq	.L171
1105:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   {
1106:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****    SET_BIT( *(__IO uint32_t *) (((uint32_t) &(EXTI->RTSR1)) + ((EXTI_Line >> 5 ) * 0x20UL)), (uint3
 1756              		.loc 1 1106 4 is_stmt 1 view .LVU294
 1757 0032 1868     		ldr	r0, [r3]
 1758 0034 1043     		orrs	r0, r0, r2
 1759 0036 1860     		str	r0, [r3]
 1760              	.L171:
1107:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   }
1108:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   if( (EXTI_Edge & EXTI_FALLING_EDGE) == EXTI_FALLING_EDGE)
 1761              		.loc 1 1108 3 view .LVU295
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s 			page 51


 1762              		.loc 1 1108 5 is_stmt 0 view .LVU296
 1763 0038 11F4001F 		tst	r1, #2097152
 1764 003c 04D0     		beq	.L170
1109:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   {
1110:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****    SET_BIT(*(__IO uint32_t *) (((uint32_t) &(EXTI->FTSR1)) + ((EXTI_Line >> 5 ) * 0x20UL)), (uint32
 1765              		.loc 1 1110 4 is_stmt 1 view .LVU297
 1766 003e 5CF80430 		ldr	r3, [ip, r4]
 1767 0042 1A43     		orrs	r2, r2, r3
 1768 0044 4CF80420 		str	r2, [ip, r4]
 1769              	.L170:
1111:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   }
1112:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** }
 1770              		.loc 1 1112 1 is_stmt 0 view .LVU298
 1771 0048 30BC     		pop	{r4, r5}
 1772              	.LCFI7:
 1773              		.cfi_restore 5
 1774              		.cfi_restore 4
 1775              		.cfi_def_cfa_offset 0
 1776 004a 7047     		bx	lr
 1777              	.L175:
 1778              		.align	2
 1779              	.L174:
 1780 004c 04000058 		.word	1476395012
 1781              		.cfi_endproc
 1782              	.LFE193:
 1784              		.section	.text.HAL_EXTI_GenerateSWInterrupt,"ax",%progbits
 1785              		.align	1
 1786              		.global	HAL_EXTI_GenerateSWInterrupt
 1787              		.syntax unified
 1788              		.thumb
 1789              		.thumb_func
 1791              	HAL_EXTI_GenerateSWInterrupt:
 1792              	.LVL46:
 1793              	.LFB194:
1113:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
1114:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /**
1115:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @brief  Generates a Software interrupt on selected EXTI line.
1116:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @param   EXTI_Line: Specifies the EXTI LINE, it can be one of the following values,
1117:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *          (EXTI_LINE0..EXTI_LINE21),EXTI_LINE49,EXTI_LINE51,EXTI_LINE82,EXTI_LINE84,EXTI_LINE85 
1118:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @retval None
1119:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   */
1120:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** void HAL_EXTI_GenerateSWInterrupt(uint32_t EXTI_Line)
1121:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** {
 1794              		.loc 1 1121 1 is_stmt 1 view -0
 1795              		.cfi_startproc
 1796              		@ args = 0, pretend = 0, frame = 0
 1797              		@ frame_needed = 0, uses_anonymous_args = 0
 1798              		@ link register save eliminated.
1122:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   /* Check the parameters */
1123:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   assert_param(IS_HAL_EXTI_CONFIG_LINE(EXTI_Line));
 1799              		.loc 1 1123 3 view .LVU300
1124:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
1125:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   SET_BIT(*(__IO uint32_t *) (((uint32_t) &(EXTI->SWIER1)) + ((EXTI_Line >> 5 ) * 0x20UL)), (uint32
 1800              		.loc 1 1125 3 view .LVU301
 1801 0000 20F01F0C 		bic	ip, r0, #31
 1802 0004 0549     		ldr	r1, .L177
 1803 0006 5CF80130 		ldr	r3, [ip, r1]
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s 			page 52


 1804 000a 00F01F00 		and	r0, r0, #31
 1805              	.LVL47:
 1806              		.loc 1 1125 3 is_stmt 0 view .LVU302
 1807 000e 0122     		movs	r2, #1
 1808 0010 8240     		lsls	r2, r2, r0
 1809 0012 1343     		orrs	r3, r3, r2
 1810 0014 4CF80130 		str	r3, [ip, r1]
1126:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** }
 1811              		.loc 1 1126 1 view .LVU303
 1812 0018 7047     		bx	lr
 1813              	.L178:
 1814 001a 00BF     		.align	2
 1815              	.L177:
 1816 001c 08000058 		.word	1476395016
 1817              		.cfi_endproc
 1818              	.LFE194:
 1820              		.section	.text.HAL_EXTI_D1_ClearFlag,"ax",%progbits
 1821              		.align	1
 1822              		.global	HAL_EXTI_D1_ClearFlag
 1823              		.syntax unified
 1824              		.thumb
 1825              		.thumb_func
 1827              	HAL_EXTI_D1_ClearFlag:
 1828              	.LVL48:
 1829              	.LFB195:
1127:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
1128:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
1129:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /**
1130:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @brief  Clears the EXTI's line pending flags for Domain D1
1131:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @param   EXTI_Line: Specifies the EXTI LINE, it can be one of the following values,
1132:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *         (EXTI_LINE0....EXTI_LINE87)excluding :line45, line81,line83 which are reserved
1133:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @retval None
1134:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   */
1135:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** void HAL_EXTI_D1_ClearFlag(uint32_t EXTI_Line)
1136:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** {
 1830              		.loc 1 1136 1 is_stmt 1 view -0
 1831              		.cfi_startproc
 1832              		@ args = 0, pretend = 0, frame = 0
 1833              		@ frame_needed = 0, uses_anonymous_args = 0
 1834              		@ link register save eliminated.
1137:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   /* Check the parameters */
1138:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****  assert_param(IS_EXTI_D1_LINE(EXTI_Line));
 1835              		.loc 1 1138 2 view .LVU305
1139:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****  WRITE_REG(*(__IO uint32_t *) (((uint32_t) &(EXTI_D1->PR1)) + ((EXTI_Line >> 5 ) * 0x10UL)), (uint3
 1836              		.loc 1 1139 2 view .LVU306
 1837 0000 00F01F02 		and	r2, r0, #31
 1838 0004 4009     		lsrs	r0, r0, #5
 1839              	.LVL49:
 1840              		.loc 1 1139 2 is_stmt 0 view .LVU307
 1841 0006 0001     		lsls	r0, r0, #4
 1842 0008 0123     		movs	r3, #1
 1843 000a 9340     		lsls	r3, r3, r2
 1844 000c 014A     		ldr	r2, .L180
 1845 000e 8350     		str	r3, [r0, r2]
1140:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
1141:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** }
 1846              		.loc 1 1141 1 view .LVU308
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s 			page 53


 1847 0010 7047     		bx	lr
 1848              	.L181:
 1849 0012 00BF     		.align	2
 1850              	.L180:
 1851 0014 88000058 		.word	1476395144
 1852              		.cfi_endproc
 1853              	.LFE195:
 1855              		.section	.text.HAL_EXTI_D1_EventInputConfig,"ax",%progbits
 1856              		.align	1
 1857              		.global	HAL_EXTI_D1_EventInputConfig
 1858              		.syntax unified
 1859              		.thumb
 1860              		.thumb_func
 1862              	HAL_EXTI_D1_EventInputConfig:
 1863              	.LVL50:
 1864              	.LFB196:
1142:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
1143:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** #if defined(DUAL_CORE)
1144:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /**
1145:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @brief  Clears the EXTI's line pending flags for Domain D2
1146:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @param   EXTI_Line: Specifies the EXTI LINE, it can be one of the following values,
1147:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *         (EXTI_LINE0....EXTI_LINE87)excluding :line45, line81,line83 which are reserved
1148:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @retval None
1149:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   */
1150:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** void HAL_EXTI_D2_ClearFlag(uint32_t EXTI_Line)
1151:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** {
1152:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   /* Check the parameters */
1153:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****  assert_param(IS_EXTI_D2_LINE(EXTI_Line));
1154:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****  WRITE_REG(*(__IO uint32_t *) (((uint32_t) &(EXTI_D2->PR1)) + ((EXTI_Line >> 5 ) * 0x10UL)), (uint3
1155:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** }
1156:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
1157:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** #endif /*DUAL_CORE*/
1158:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /**
1159:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @brief  Configure the EXTI input event line for Domain D1
1160:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @param   EXTI_Line: Specifies the EXTI LINE, it can be one of the following values,
1161:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *         (EXTI_LINE0....EXTI_LINE87)excluding :line45, line81,line83 which are reserved
1162:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @param   EXTI_Mode: Specifies which EXTI line is used as interrupt or an event.
1163:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *          This parameter can be one or a combination of the following values :
1164:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *   @arg EXTI_MODE_IT :  Interrupt Mode selected
1165:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *   @arg EXTI_MODE_EVT : Event Mode selected
1166:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @param   EXTI_LineCmd controls (Enable/Disable) the EXTI line.
1167:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
1168:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @retval None
1169:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   */
1170:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** void HAL_EXTI_D1_EventInputConfig(uint32_t EXTI_Line , uint32_t EXTI_Mode,  uint32_t EXTI_LineCmd )
1171:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** {
 1865              		.loc 1 1171 1 is_stmt 1 view -0
 1866              		.cfi_startproc
 1867              		@ args = 0, pretend = 0, frame = 0
 1868              		@ frame_needed = 0, uses_anonymous_args = 0
 1869              		.loc 1 1171 1 is_stmt 0 view .LVU310
 1870 0000 10B5     		push	{r4, lr}
 1871              	.LCFI8:
 1872              		.cfi_def_cfa_offset 8
 1873              		.cfi_offset 4, -8
 1874              		.cfi_offset 14, -4
1172:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   /* Check the parameter */
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s 			page 54


1173:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   assert_param(IS_EXTI_D1_LINE(EXTI_Line));
 1875              		.loc 1 1173 3 is_stmt 1 view .LVU311
1174:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   assert_param(IS_EXTI_MODE_LINE(EXTI_Mode));
 1876              		.loc 1 1174 3 view .LVU312
1175:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
1176:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   if( (EXTI_Mode & EXTI_MODE_IT) == EXTI_MODE_IT)
 1877              		.loc 1 1176 3 view .LVU313
 1878              		.loc 1 1176 5 is_stmt 0 view .LVU314
 1879 0002 11F4803F 		tst	r1, #65536
 1880 0006 0ED0     		beq	.L183
1177:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   {
1178:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****      if( EXTI_LineCmd == 0UL)
 1881              		.loc 1 1178 6 is_stmt 1 view .LVU315
 1882              		.loc 1 1178 8 is_stmt 0 view .LVU316
 1883 0008 EAB9     		cbnz	r2, .L184
1179:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****      {
1180:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****        /* Clear EXTI line configuration */
1181:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****         CLEAR_BIT(*(__IO uint32_t *) (((uint32_t) &(EXTI_D1->IMR1)) + ((EXTI_Line >> 5 ) * 0x10UL))
 1884              		.loc 1 1181 9 is_stmt 1 view .LVU317
 1885 000a 1C4B     		ldr	r3, .L188
 1886 000c 03EB5013 		add	r3, r3, r0, lsr #5
 1887 0010 1B01     		lsls	r3, r3, #4
 1888 0012 1C68     		ldr	r4, [r3]
 1889 0014 00F01F0E 		and	lr, r0, #31
 1890 0018 4FF0010C 		mov	ip, #1
 1891 001c 0CFA0EFC 		lsl	ip, ip, lr
 1892 0020 24EA0C04 		bic	r4, r4, ip
 1893 0024 1C60     		str	r4, [r3]
 1894              	.L183:
1182:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****      }
1183:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****      else
1184:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****      {
1185:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****         SET_BIT(*(__IO uint32_t *) (((uint32_t) &(EXTI_D1->IMR1)) + ((EXTI_Line >> 5 ) * 0x10UL)), 
1186:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****      }
1187:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   }
1188:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
1189:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   if( (EXTI_Mode & EXTI_MODE_EVT) == EXTI_MODE_EVT)
 1895              		.loc 1 1189 3 view .LVU318
 1896              		.loc 1 1189 5 is_stmt 0 view .LVU319
 1897 0026 11F4003F 		tst	r1, #131072
 1898 002a 0BD0     		beq	.L182
1190:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   {
1191:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     if( EXTI_LineCmd == 0UL)
 1899              		.loc 1 1191 5 is_stmt 1 view .LVU320
 1900              		.loc 1 1191 7 is_stmt 0 view .LVU321
 1901 002c D2B9     		cbnz	r2, .L186
1192:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     {
1193:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****       /* Clear EXTI line configuration */
1194:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****       CLEAR_BIT(  *(__IO uint32_t *) (((uint32_t) &(EXTI_D1->EMR1)) + ((EXTI_Line >> 5 ) * 0x10UL))
 1902              		.loc 1 1194 7 is_stmt 1 view .LVU322
 1903 002e 4309     		lsrs	r3, r0, #5
 1904 0030 1B01     		lsls	r3, r3, #4
 1905 0032 134C     		ldr	r4, .L188+4
 1906 0034 1A59     		ldr	r2, [r3, r4]
 1907              	.LVL51:
 1908              		.loc 1 1194 7 is_stmt 0 view .LVU323
 1909 0036 00F01F00 		and	r0, r0, #31
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s 			page 55


 1910              	.LVL52:
 1911              		.loc 1 1194 7 view .LVU324
 1912 003a 0121     		movs	r1, #1
 1913              	.LVL53:
 1914              		.loc 1 1194 7 view .LVU325
 1915 003c 8140     		lsls	r1, r1, r0
 1916 003e 22EA0102 		bic	r2, r2, r1
 1917 0042 1A51     		str	r2, [r3, r4]
 1918              	.L182:
1195:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     }
1196:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     else
1197:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     {
1198:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****       SET_BIT(  *(__IO uint32_t *) (((uint32_t) &(EXTI_D1->EMR1)) + ((EXTI_Line >> 5 ) * 0x10UL)), 
1199:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     }
1200:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   }
1201:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** }
 1919              		.loc 1 1201 1 view .LVU326
 1920 0044 10BD     		pop	{r4, pc}
 1921              	.LVL54:
 1922              	.L184:
1185:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****      }
 1923              		.loc 1 1185 9 is_stmt 1 view .LVU327
 1924 0046 0D4B     		ldr	r3, .L188
 1925 0048 03EB5013 		add	r3, r3, r0, lsr #5
 1926 004c 1B01     		lsls	r3, r3, #4
 1927 004e 1C68     		ldr	r4, [r3]
 1928 0050 00F01F0E 		and	lr, r0, #31
 1929 0054 4FF0010C 		mov	ip, #1
 1930 0058 0CFA0EFC 		lsl	ip, ip, lr
 1931 005c 44EA0C04 		orr	r4, r4, ip
 1932 0060 1C60     		str	r4, [r3]
 1933 0062 E0E7     		b	.L183
 1934              	.L186:
1198:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     }
 1935              		.loc 1 1198 7 view .LVU328
 1936 0064 4309     		lsrs	r3, r0, #5
 1937 0066 1B01     		lsls	r3, r3, #4
 1938 0068 054C     		ldr	r4, .L188+4
 1939 006a 1A59     		ldr	r2, [r3, r4]
 1940              	.LVL55:
1198:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     }
 1941              		.loc 1 1198 7 is_stmt 0 view .LVU329
 1942 006c 00F01F00 		and	r0, r0, #31
 1943              	.LVL56:
1198:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     }
 1944              		.loc 1 1198 7 view .LVU330
 1945 0070 0121     		movs	r1, #1
 1946              	.LVL57:
1198:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     }
 1947              		.loc 1 1198 7 view .LVU331
 1948 0072 8140     		lsls	r1, r1, r0
 1949 0074 0A43     		orrs	r2, r2, r1
 1950 0076 1A51     		str	r2, [r3, r4]
 1951              		.loc 1 1201 1 view .LVU332
 1952 0078 E4E7     		b	.L182
 1953              	.L189:
 1954 007a 00BF     		.align	2
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s 			page 56


 1955              	.L188:
 1956 007c 08008005 		.word	92274696
 1957 0080 84000058 		.word	1476395140
 1958              		.cfi_endproc
 1959              	.LFE196:
 1961              		.section	.text.HAL_EXTI_D3_EventInputConfig,"ax",%progbits
 1962              		.align	1
 1963              		.global	HAL_EXTI_D3_EventInputConfig
 1964              		.syntax unified
 1965              		.thumb
 1966              		.thumb_func
 1968              	HAL_EXTI_D3_EventInputConfig:
 1969              	.LVL58:
 1970              	.LFB197:
1202:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
1203:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** #if defined(DUAL_CORE)
1204:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /**
1205:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @brief  Configure the EXTI input event line for Domain D2
1206:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @param   EXTI_Line: Specifies the EXTI LINE, it can be one of the following values,
1207:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *         (EXTI_LINE0....EXTI_LINE87)excluding :line45, line81,line83 which are reserved
1208:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @param   EXTI_Mode: Specifies which EXTI line is used as interrupt or an event.
1209:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *          This parameter can be one or a combination of the following values :
1210:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *   @arg EXTI_MODE_IT :  Interrupt Mode selected
1211:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *   @arg EXTI_MODE_EVT : Event Mode selected
1212:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @param   EXTI_LineCmd controls (Enable/Disable) the EXTI line.
1213:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
1214:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @retval None
1215:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   */
1216:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** void HAL_EXTI_D2_EventInputConfig(uint32_t EXTI_Line , uint32_t EXTI_Mode,  uint32_t EXTI_LineCmd )
1217:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** {
1218:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   /* Check the parameter */
1219:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   assert_param(IS_EXTI_D2_LINE(EXTI_Line));
1220:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   assert_param(IS_EXTI_MODE_LINE(EXTI_Mode));
1221:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
1222:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   if( (EXTI_Mode & EXTI_MODE_IT) == EXTI_MODE_IT)
1223:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   {
1224:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     if( EXTI_LineCmd == 0UL)
1225:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     {
1226:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     /* Clear EXTI line configuration */
1227:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****      CLEAR_BIT(*(__IO uint32_t *) (((uint32_t) &(EXTI_D2->IMR1)) + ((EXTI_Line >> 5 ) * 0x10UL)),(u
1228:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     }
1229:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     else
1230:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     {
1231:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****      SET_BIT(*(__IO uint32_t *) (((uint32_t) &(EXTI_D2->IMR1)) + ((EXTI_Line >> 5 ) * 0x10UL)), (ui
1232:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     }
1233:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   }
1234:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
1235:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   if( (EXTI_Mode & EXTI_MODE_EVT) == EXTI_MODE_EVT)
1236:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   {
1237:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     if( EXTI_LineCmd == 0UL)
1238:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     {
1239:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****       /* Clear EXTI line configuration */
1240:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****       CLEAR_BIT(  *(__IO uint32_t *) (((uint32_t) &(EXTI_D2->EMR1)) + ((EXTI_Line >> 5 ) * 0x10UL))
1241:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     }
1242:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     else
1243:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     {
1244:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****       SET_BIT(  *(__IO uint32_t *) (((uint32_t) &(EXTI_D2->EMR1)) + ((EXTI_Line >> 5 ) * 0x10UL)), 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s 			page 57


1245:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     }
1246:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   }
1247:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** }
1248:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** #endif /*DUAL_CORE*/
1249:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
1250:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** /**
1251:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @brief  Configure the EXTI input event line for Domain D3
1252:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @param   EXTI_Line: Specifies the EXTI LINE, it can be one of the following values,
1253:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *         (EXTI_LINE0...EXTI_LINE15),(EXTI_LINE19...EXTI_LINE21),EXTI_LINE25, EXTI_LINE34,
1254:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *          EXTI_LINE35,EXTI_LINE41,(EXTI_LINE48...EXTI_LINE53)
1255:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @param   EXTI_LineCmd controls (Enable/Disable) the EXTI line.
1256:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @param   EXTI_ClearSrc: Specifies the clear source of D3 pending event.
1257:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *          This parameter can be one of the following values :
1258:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *   @arg BDMA_CH6_CLEAR : BDMA ch6 event selected as D3 domain pendclear source
1259:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *   @arg BDMA_CH7_CLEAR : BDMA ch7 event selected as D3 domain pendclear source
1260:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *   @arg LPTIM4_OUT_CLEAR : LPTIM4 out selected as D3 domain pendclear source
1261:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   *   @arg LPTIM5_OUT_CLEAR : LPTIM5 out selected as D3 domain pendclear source
1262:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   * @retval None
1263:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   */
1264:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** void HAL_EXTI_D3_EventInputConfig(uint32_t EXTI_Line, uint32_t EXTI_LineCmd , uint32_t EXTI_ClearSr
1265:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** {
 1971              		.loc 1 1265 1 is_stmt 1 view -0
 1972              		.cfi_startproc
 1973              		@ args = 0, pretend = 0, frame = 0
 1974              		@ frame_needed = 0, uses_anonymous_args = 0
 1975              		.loc 1 1265 1 is_stmt 0 view .LVU334
 1976 0000 10B5     		push	{r4, lr}
 1977              	.LCFI9:
 1978              		.cfi_def_cfa_offset 8
 1979              		.cfi_offset 4, -8
 1980              		.cfi_offset 14, -4
1266:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   __IO uint32_t *pRegv;
 1981              		.loc 1 1266 3 is_stmt 1 view .LVU335
1267:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
1268:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   /* Check the parameter */
1269:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   assert_param(IS_EXTI_D3_LINE(EXTI_Line));
 1982              		.loc 1 1269 3 view .LVU336
1270:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   assert_param(IS_EXTI_D3_CLEAR(EXTI_ClearSrc));
 1983              		.loc 1 1270 3 view .LVU337
1271:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
1272:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   if( EXTI_LineCmd == 0UL)
 1984              		.loc 1 1272 3 view .LVU338
 1985              		.loc 1 1272 5 is_stmt 0 view .LVU339
 1986 0002 09BB     		cbnz	r1, .L191
1273:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   {
1274:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     /* Clear EXTI line configuration */
1275:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     CLEAR_BIT(*(__IO uint32_t *) (((uint32_t) &(EXTI->D3PMR1)) + ((EXTI_Line >> 5 ) * 0x20UL)),(uin
 1987              		.loc 1 1275 5 is_stmt 1 view .LVU340
 1988 0004 20F01F03 		bic	r3, r0, #31
 1989 0008 194C     		ldr	r4, .L196
 1990 000a 1959     		ldr	r1, [r3, r4]
 1991              	.LVL59:
 1992              		.loc 1 1275 5 is_stmt 0 view .LVU341
 1993 000c 00F01F0E 		and	lr, r0, #31
 1994 0010 4FF0010C 		mov	ip, #1
 1995 0014 0CFA0EFC 		lsl	ip, ip, lr
 1996 0018 21EA0C01 		bic	r1, r1, ip
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s 			page 58


 1997 001c 1951     		str	r1, [r3, r4]
 1998              	.L192:
1276:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   }
1277:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   else
1278:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   {
1279:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     SET_BIT(*(__IO uint32_t *) (((uint32_t) &(EXTI->D3PMR1)) +((EXTI_Line >> 5 ) * 0x20UL)), (uint3
1280:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   }
1281:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
1282:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   if(((EXTI_Line>>4)%2UL) == 0UL)
 1999              		.loc 1 1282 3 is_stmt 1 view .LVU342
 2000              		.loc 1 1282 5 is_stmt 0 view .LVU343
 2001 001e 10F0100F 		tst	r0, #16
 2002 0022 1FD1     		bne	.L193
1283:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   {
1284:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     pRegv = (__IO uint32_t *) (((uint32_t) &(EXTI->D3PCR1L)) + ((EXTI_Line >> 5 ) * 0x20UL));
 2003              		.loc 1 1284 5 is_stmt 1 view .LVU344
 2004              		.loc 1 1284 83 is_stmt 0 view .LVU345
 2005 0024 20F01F03 		bic	r3, r0, #31
 2006              		.loc 1 1284 62 view .LVU346
 2007 0028 1249     		ldr	r1, .L196+4
 2008 002a 1944     		add	r1, r1, r3
 2009              	.LVL60:
 2010              	.L194:
1285:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   }
1286:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   else
1287:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   {
1288:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****     pRegv = (__IO uint32_t *) (((uint32_t) &(EXTI->D3PCR1H)) + ((EXTI_Line >> 5 ) * 0x20UL));
1289:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   }
1290:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   MODIFY_REG(*pRegv, (uint32_t)(3UL << ((EXTI_Line*2UL) & 0x1FUL)), (uint32_t)(EXTI_ClearSrc << ((E
 2011              		.loc 1 1290 3 is_stmt 1 view .LVU347
 2012 002c 0B68     		ldr	r3, [r1]
 2013 002e 4000     		lsls	r0, r0, #1
 2014              	.LVL61:
 2015              		.loc 1 1290 3 is_stmt 0 view .LVU348
 2016 0030 00F01E00 		and	r0, r0, #30
 2017 0034 4FF0030C 		mov	ip, #3
 2018 0038 0CFA00FC 		lsl	ip, ip, r0
 2019 003c 23EA0C03 		bic	r3, r3, ip
 2020 0040 8240     		lsls	r2, r2, r0
 2021              	.LVL62:
 2022              		.loc 1 1290 3 view .LVU349
 2023 0042 1A43     		orrs	r2, r2, r3
 2024 0044 0A60     		str	r2, [r1]
1291:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** 
1292:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c **** }
 2025              		.loc 1 1292 1 view .LVU350
 2026 0046 10BD     		pop	{r4, pc}
 2027              	.LVL63:
 2028              	.L191:
1279:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   }
 2029              		.loc 1 1279 5 is_stmt 1 view .LVU351
 2030 0048 20F01F03 		bic	r3, r0, #31
 2031 004c 084C     		ldr	r4, .L196
 2032 004e 1959     		ldr	r1, [r3, r4]
 2033              	.LVL64:
1279:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   }
 2034              		.loc 1 1279 5 is_stmt 0 view .LVU352
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s 			page 59


 2035 0050 00F01F0E 		and	lr, r0, #31
 2036 0054 4FF0010C 		mov	ip, #1
 2037 0058 0CFA0EFC 		lsl	ip, ip, lr
 2038 005c 41EA0C01 		orr	r1, r1, ip
 2039 0060 1951     		str	r1, [r3, r4]
 2040 0062 DCE7     		b	.L192
 2041              	.L193:
1288:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   }
 2042              		.loc 1 1288 5 is_stmt 1 view .LVU353
1288:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   }
 2043              		.loc 1 1288 83 is_stmt 0 view .LVU354
 2044 0064 20F01F03 		bic	r3, r0, #31
1288:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   }
 2045              		.loc 1 1288 62 view .LVU355
 2046 0068 0349     		ldr	r1, .L196+8
 2047 006a 1944     		add	r1, r1, r3
 2048              	.LVL65:
1288:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c ****   }
 2049              		.loc 1 1288 62 view .LVU356
 2050 006c DEE7     		b	.L194
 2051              	.L197:
 2052 006e 00BF     		.align	2
 2053              	.L196:
 2054 0070 0C000058 		.word	1476395020
 2055 0074 10000058 		.word	1476395024
 2056 0078 14000058 		.word	1476395028
 2057              		.cfi_endproc
 2058              	.LFE197:
 2060              		.global	uwTickFreq
 2061              		.section	.data.uwTickFreq,"aw"
 2064              	uwTickFreq:
 2065 0000 01       		.byte	1
 2066              		.global	uwTickPrio
 2067              		.section	.data.uwTickPrio,"aw"
 2068              		.align	2
 2071              	uwTickPrio:
 2072 0000 10000000 		.word	16
 2073              		.global	uwTick
 2074              		.section	.bss.uwTick,"aw",%nobits
 2075              		.align	2
 2078              	uwTick:
 2079 0000 00000000 		.space	4
 2080              		.text
 2081              	.Letext0:
 2082              		.file 2 "Drivers/CMSIS/Device/ST/STM32H7xx/Include/stm32h723xx.h"
 2083              		.file 3 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin/../lib/gcc/arm-none-eabi/13.3.1/../
 2084              		.file 4 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin/../lib/gcc/arm-none-eabi/13.3.1/../
 2085              		.file 5 "Drivers/CMSIS/Include/core_cm7.h"
 2086              		.file 6 "Drivers/CMSIS/Device/ST/STM32H7xx/Include/system_stm32h7xx.h"
 2087              		.file 7 "Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_def.h"
 2088              		.file 8 "Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal.h"
 2089              		.file 9 "Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_cortex.h"
 2090              		.file 10 "Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_rcc.h"
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s 			page 60


DEFINED SYMBOLS
                            *ABS*:00000000 stm32h7xx_hal.c
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:20     .text.HAL_MspInit:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:26     .text.HAL_MspInit:00000000 HAL_MspInit
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:39     .text.HAL_MspDeInit:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:45     .text.HAL_MspDeInit:00000000 HAL_MspDeInit
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:58     .text.HAL_DeInit:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:64     .text.HAL_DeInit:00000000 HAL_DeInit
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:134    .text.HAL_DeInit:00000070 $d
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:146    .text.HAL_InitTick:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:152    .text.HAL_InitTick:00000000 HAL_InitTick
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:226    .text.HAL_InitTick:00000044 $d
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:2064   .data.uwTickFreq:00000000 uwTickFreq
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:2071   .data.uwTickPrio:00000000 uwTickPrio
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:233    .text.HAL_Init:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:239    .text.HAL_Init:00000000 HAL_Init
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:316    .text.HAL_Init:0000004c $d
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:324    .text.HAL_IncTick:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:330    .text.HAL_IncTick:00000000 HAL_IncTick
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:351    .text.HAL_IncTick:00000010 $d
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:2078   .bss.uwTick:00000000 uwTick
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:357    .text.HAL_GetTick:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:363    .text.HAL_GetTick:00000000 HAL_GetTick
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:379    .text.HAL_GetTick:00000008 $d
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:384    .text.HAL_GetTickPrio:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:390    .text.HAL_GetTickPrio:00000000 HAL_GetTickPrio
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:405    .text.HAL_GetTickPrio:00000008 $d
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:410    .text.HAL_SetTickFreq:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:416    .text.HAL_SetTickFreq:00000000 HAL_SetTickFreq
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:473    .text.HAL_SetTickFreq:00000024 $d
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:479    .text.HAL_GetTickFreq:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:485    .text.HAL_GetTickFreq:00000000 HAL_GetTickFreq
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:500    .text.HAL_GetTickFreq:00000008 $d
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:505    .text.HAL_Delay:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:511    .text.HAL_Delay:00000000 HAL_Delay
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:564    .text.HAL_Delay:00000024 $d
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:569    .text.HAL_SuspendTick:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:575    .text.HAL_SuspendTick:00000000 HAL_SuspendTick
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:595    .text.HAL_ResumeTick:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:601    .text.HAL_ResumeTick:00000000 HAL_ResumeTick
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:621    .text.HAL_GetHalVersion:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:627    .text.HAL_GetHalVersion:00000000 HAL_GetHalVersion
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:641    .text.HAL_GetHalVersion:00000004 $d
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:646    .text.HAL_GetREVID:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:652    .text.HAL_GetREVID:00000000 HAL_GetREVID
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:669    .text.HAL_GetREVID:00000008 $d
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:674    .text.HAL_GetDEVID:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:680    .text.HAL_GetDEVID:00000000 HAL_GetDEVID
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:697    .text.HAL_GetDEVID:0000000c $d
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:702    .text.HAL_GetUIDw0:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:708    .text.HAL_GetUIDw0:00000000 HAL_GetUIDw0
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:723    .text.HAL_GetUIDw0:00000008 $d
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:728    .text.HAL_GetUIDw1:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:734    .text.HAL_GetUIDw1:00000000 HAL_GetUIDw1
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:749    .text.HAL_GetUIDw1:00000008 $d
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:754    .text.HAL_GetUIDw2:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:760    .text.HAL_GetUIDw2:00000000 HAL_GetUIDw2
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s 			page 61


C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:775    .text.HAL_GetUIDw2:00000008 $d
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:780    .text.HAL_SYSCFG_VREFBUF_VoltageScalingConfig:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:786    .text.HAL_SYSCFG_VREFBUF_VoltageScalingConfig:00000000 HAL_SYSCFG_VREFBUF_VoltageScalingConfig
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:806    .text.HAL_SYSCFG_VREFBUF_VoltageScalingConfig:00000010 $d
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:811    .text.HAL_SYSCFG_VREFBUF_HighImpedanceConfig:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:817    .text.HAL_SYSCFG_VREFBUF_HighImpedanceConfig:00000000 HAL_SYSCFG_VREFBUF_HighImpedanceConfig
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:837    .text.HAL_SYSCFG_VREFBUF_HighImpedanceConfig:00000010 $d
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:842    .text.HAL_SYSCFG_VREFBUF_TrimmingConfig:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:848    .text.HAL_SYSCFG_VREFBUF_TrimmingConfig:00000000 HAL_SYSCFG_VREFBUF_TrimmingConfig
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:868    .text.HAL_SYSCFG_VREFBUF_TrimmingConfig:00000010 $d
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:873    .text.HAL_SYSCFG_EnableVREFBUF:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:879    .text.HAL_SYSCFG_EnableVREFBUF:00000000 HAL_SYSCFG_EnableVREFBUF
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:934    .text.HAL_SYSCFG_EnableVREFBUF:00000030 $d
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:939    .text.HAL_SYSCFG_DisableVREFBUF:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:945    .text.HAL_SYSCFG_DisableVREFBUF:00000000 HAL_SYSCFG_DisableVREFBUF
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:962    .text.HAL_SYSCFG_DisableVREFBUF:0000000c $d
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:967    .text.HAL_SYSCFG_ETHInterfaceSelect:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:973    .text.HAL_SYSCFG_ETHInterfaceSelect:00000000 HAL_SYSCFG_ETHInterfaceSelect
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:993    .text.HAL_SYSCFG_ETHInterfaceSelect:00000010 $d
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:998    .text.HAL_SYSCFG_AnalogSwitchConfig:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1004   .text.HAL_SYSCFG_AnalogSwitchConfig:00000000 HAL_SYSCFG_AnalogSwitchConfig
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1025   .text.HAL_SYSCFG_AnalogSwitchConfig:00000010 $d
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1030   .text.HAL_SYSCFG_EnableBOOST:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1036   .text.HAL_SYSCFG_EnableBOOST:00000000 HAL_SYSCFG_EnableBOOST
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1053   .text.HAL_SYSCFG_EnableBOOST:0000000c $d
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1058   .text.HAL_SYSCFG_DisableBOOST:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1064   .text.HAL_SYSCFG_DisableBOOST:00000000 HAL_SYSCFG_DisableBOOST
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1081   .text.HAL_SYSCFG_DisableBOOST:0000000c $d
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1086   .text.HAL_SYSCFG_CM7BootAddConfig:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1092   .text.HAL_SYSCFG_CM7BootAddConfig:00000000 HAL_SYSCFG_CM7BootAddConfig
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1128   .text.HAL_SYSCFG_CM7BootAddConfig:0000002c $d
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1133   .text.HAL_EnableCompensationCell:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1139   .text.HAL_EnableCompensationCell:00000000 HAL_EnableCompensationCell
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1156   .text.HAL_EnableCompensationCell:0000000c $d
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1161   .text.HAL_DisableCompensationCell:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1167   .text.HAL_DisableCompensationCell:00000000 HAL_DisableCompensationCell
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1184   .text.HAL_DisableCompensationCell:0000000c $d
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1189   .text.HAL_SYSCFG_EnableIOSpeedOptimize:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1195   .text.HAL_SYSCFG_EnableIOSpeedOptimize:00000000 HAL_SYSCFG_EnableIOSpeedOptimize
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1212   .text.HAL_SYSCFG_EnableIOSpeedOptimize:0000000c $d
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1217   .text.HAL_SYSCFG_DisableIOSpeedOptimize:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1223   .text.HAL_SYSCFG_DisableIOSpeedOptimize:00000000 HAL_SYSCFG_DisableIOSpeedOptimize
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1240   .text.HAL_SYSCFG_DisableIOSpeedOptimize:0000000c $d
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1245   .text.HAL_SYSCFG_CompensationCodeSelect:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1251   .text.HAL_SYSCFG_CompensationCodeSelect:00000000 HAL_SYSCFG_CompensationCodeSelect
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1271   .text.HAL_SYSCFG_CompensationCodeSelect:00000010 $d
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1276   .text.HAL_SYSCFG_CompensationCodeConfig:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1282   .text.HAL_SYSCFG_CompensationCodeConfig:00000000 HAL_SYSCFG_CompensationCodeConfig
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1306   .text.HAL_SYSCFG_CompensationCodeConfig:00000014 $d
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1311   .text.HAL_SYSCFG_ADC2ALT_Rout0Config:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1317   .text.HAL_SYSCFG_ADC2ALT_Rout0Config:00000000 HAL_SYSCFG_ADC2ALT_Rout0Config
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1337   .text.HAL_SYSCFG_ADC2ALT_Rout0Config:00000010 $d
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1342   .text.HAL_SYSCFG_ADC2ALT_Rout1Config:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1348   .text.HAL_SYSCFG_ADC2ALT_Rout1Config:00000000 HAL_SYSCFG_ADC2ALT_Rout1Config
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1368   .text.HAL_SYSCFG_ADC2ALT_Rout1Config:00000010 $d
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1373   .text.HAL_DBGMCU_EnableDBGSleepMode:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1379   .text.HAL_DBGMCU_EnableDBGSleepMode:00000000 HAL_DBGMCU_EnableDBGSleepMode
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s 			page 62


C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1396   .text.HAL_DBGMCU_EnableDBGSleepMode:0000000c $d
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1401   .text.HAL_DBGMCU_DisableDBGSleepMode:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1407   .text.HAL_DBGMCU_DisableDBGSleepMode:00000000 HAL_DBGMCU_DisableDBGSleepMode
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1424   .text.HAL_DBGMCU_DisableDBGSleepMode:0000000c $d
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1429   .text.HAL_DBGMCU_EnableDBGStopMode:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1435   .text.HAL_DBGMCU_EnableDBGStopMode:00000000 HAL_DBGMCU_EnableDBGStopMode
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1452   .text.HAL_DBGMCU_EnableDBGStopMode:0000000c $d
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1457   .text.HAL_DBGMCU_DisableDBGStopMode:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1463   .text.HAL_DBGMCU_DisableDBGStopMode:00000000 HAL_DBGMCU_DisableDBGStopMode
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1480   .text.HAL_DBGMCU_DisableDBGStopMode:0000000c $d
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1485   .text.HAL_DBGMCU_EnableDBGStandbyMode:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1491   .text.HAL_DBGMCU_EnableDBGStandbyMode:00000000 HAL_DBGMCU_EnableDBGStandbyMode
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1508   .text.HAL_DBGMCU_EnableDBGStandbyMode:0000000c $d
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1513   .text.HAL_DBGMCU_DisableDBGStandbyMode:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1519   .text.HAL_DBGMCU_DisableDBGStandbyMode:00000000 HAL_DBGMCU_DisableDBGStandbyMode
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1536   .text.HAL_DBGMCU_DisableDBGStandbyMode:0000000c $d
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1541   .text.HAL_EnableDomain3DBGStopMode:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1547   .text.HAL_EnableDomain3DBGStopMode:00000000 HAL_EnableDomain3DBGStopMode
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1564   .text.HAL_EnableDomain3DBGStopMode:0000000c $d
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1569   .text.HAL_DisableDomain3DBGStopMode:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1575   .text.HAL_DisableDomain3DBGStopMode:00000000 HAL_DisableDomain3DBGStopMode
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1592   .text.HAL_DisableDomain3DBGStopMode:0000000c $d
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1597   .text.HAL_EnableDomain3DBGStandbyMode:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1603   .text.HAL_EnableDomain3DBGStandbyMode:00000000 HAL_EnableDomain3DBGStandbyMode
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1620   .text.HAL_EnableDomain3DBGStandbyMode:0000000c $d
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1625   .text.HAL_DisableDomain3DBGStandbyMode:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1631   .text.HAL_DisableDomain3DBGStandbyMode:00000000 HAL_DisableDomain3DBGStandbyMode
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1648   .text.HAL_DisableDomain3DBGStandbyMode:0000000c $d
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1653   .text.HAL_SetFMCMemorySwappingConfig:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1659   .text.HAL_SetFMCMemorySwappingConfig:00000000 HAL_SetFMCMemorySwappingConfig
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1679   .text.HAL_SetFMCMemorySwappingConfig:00000010 $d
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1684   .text.HAL_GetFMCMemorySwappingConfig:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1690   .text.HAL_GetFMCMemorySwappingConfig:00000000 HAL_GetFMCMemorySwappingConfig
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1707   .text.HAL_GetFMCMemorySwappingConfig:0000000c $d
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1712   .text.HAL_EXTI_EdgeConfig:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1718   .text.HAL_EXTI_EdgeConfig:00000000 HAL_EXTI_EdgeConfig
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1780   .text.HAL_EXTI_EdgeConfig:0000004c $d
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1785   .text.HAL_EXTI_GenerateSWInterrupt:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1791   .text.HAL_EXTI_GenerateSWInterrupt:00000000 HAL_EXTI_GenerateSWInterrupt
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1816   .text.HAL_EXTI_GenerateSWInterrupt:0000001c $d
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1821   .text.HAL_EXTI_D1_ClearFlag:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1827   .text.HAL_EXTI_D1_ClearFlag:00000000 HAL_EXTI_D1_ClearFlag
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1851   .text.HAL_EXTI_D1_ClearFlag:00000014 $d
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1856   .text.HAL_EXTI_D1_EventInputConfig:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1862   .text.HAL_EXTI_D1_EventInputConfig:00000000 HAL_EXTI_D1_EventInputConfig
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1956   .text.HAL_EXTI_D1_EventInputConfig:0000007c $d
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1962   .text.HAL_EXTI_D3_EventInputConfig:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:1968   .text.HAL_EXTI_D3_EventInputConfig:00000000 HAL_EXTI_D3_EventInputConfig
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:2054   .text.HAL_EXTI_D3_EventInputConfig:00000070 $d
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:2068   .data.uwTickPrio:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s:2075   .bss.uwTick:00000000 $d

UNDEFINED SYMBOLS
HAL_SYSTICK_Config
HAL_NVIC_SetPriority
SystemCoreClock
HAL_NVIC_SetPriorityGrouping
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccu4xDUz.s 			page 63


HAL_RCC_GetSysClockFreq
D1CorePrescTable
SystemD2Clock
