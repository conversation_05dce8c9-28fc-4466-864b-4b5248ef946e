ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s 			page 1


   1              		.cpu cortex-m7
   2              		.arch armv7e-m
   3              		.fpu fpv5-d16
   4              		.eabi_attribute 28, 1
   5              		.eabi_attribute 20, 1
   6              		.eabi_attribute 21, 1
   7              		.eabi_attribute 23, 3
   8              		.eabi_attribute 24, 1
   9              		.eabi_attribute 25, 1
  10              		.eabi_attribute 26, 1
  11              		.eabi_attribute 30, 1
  12              		.eabi_attribute 34, 1
  13              		.eabi_attribute 18, 4
  14              		.file	"ip4_frag.c"
  15              		.text
  16              	.Ltext0:
  17              		.cfi_sections	.debug_frame
  18              		.file 1 "Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c"
  19              		.section	.rodata.ip_reass_dequeue_datagram.str1.4,"aMS",%progbits,1
  20              		.align	2
  21              	.LC0:
  22 0000 4D696464 		.ascii	"Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag"
  22      6C657761 
  22      7265732F 
  22      54686972 
  22      645F5061 
  23 0033 2E6300   		.ascii	".c\000"
  24 0036 0000     		.align	2
  25              	.LC1:
  26 0038 73616E69 		.ascii	"sanity check linked list\000"
  26      74792063 
  26      6865636B 
  26      206C696E 
  26      6B656420 
  27 0051 000000   		.align	2
  28              	.LC2:
  29 0054 41737365 		.ascii	"Assertion \"%s\" failed at line %d in %s\012\000"
  29      7274696F 
  29      6E202225 
  29      73222066 
  29      61696C65 
  30              		.section	.text.ip_reass_dequeue_datagram,"ax",%progbits
  31              		.align	1
  32              		.syntax unified
  33              		.thumb
  34              		.thumb_func
  36              	ip_reass_dequeue_datagram:
  37              	.LVL0:
  38              	.LFB174:
   1:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** /**
   2:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  * @file
   3:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  * This is the IPv4 packet segmentation and reassembly implementation.
   4:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  *
   5:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  */
   6:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
   7:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** /*
   8:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  * Copyright (c) 2001-2004 Swedish Institute of Computer Science.
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s 			page 2


   9:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  * All rights reserved.
  10:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  *
  11:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  * Redistribution and use in source and binary forms, with or without modification,
  12:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  * are permitted provided that the following conditions are met:
  13:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  *
  14:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  * 1. Redistributions of source code must retain the above copyright notice,
  15:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  *    this list of conditions and the following disclaimer.
  16:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  * 2. Redistributions in binary form must reproduce the above copyright notice,
  17:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  *    this list of conditions and the following disclaimer in the documentation
  18:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  *    and/or other materials provided with the distribution.
  19:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  * 3. The name of the author may not be used to endorse or promote products
  20:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  *    derived from this software without specific prior written permission.
  21:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  *
  22:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  * THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR IMPLIED
  23:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
  24:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  * MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT
  25:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  * SHALL THE AUTHOR BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
  26:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  * EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT
  27:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  * OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
  28:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
  29:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
  30:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY
  31:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  * OF SUCH DAMAGE.
  32:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  *
  33:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  * This file is part of the lwIP TCP/IP stack.
  34:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  *
  35:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  * Author: Jani Monoses <<EMAIL>>
  36:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  *         Simon Goldschmidt
  37:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  * original reassembly code by Adam Dunkels <<EMAIL>>
  38:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  *
  39:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  */
  40:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
  41:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #include "lwip/opt.h"
  42:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
  43:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #if LWIP_IPV4
  44:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
  45:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #include "lwip/ip4_frag.h"
  46:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #include "lwip/def.h"
  47:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #include "lwip/inet_chksum.h"
  48:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #include "lwip/netif.h"
  49:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #include "lwip/stats.h"
  50:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #include "lwip/icmp.h"
  51:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
  52:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #include <string.h>
  53:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
  54:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #if IP_REASSEMBLY
  55:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** /**
  56:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  * The IP reassembly code currently has the following limitations:
  57:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  * - IP header options are not supported
  58:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  * - fragments must not overlap (e.g. due to different routes),
  59:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  *   currently, overlapping or duplicate fragments are thrown away
  60:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  *   if IP_REASS_CHECK_OVERLAP=1 (the default)!
  61:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  *
  62:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  * @todo: work with IP header options
  63:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  */
  64:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
  65:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** /** Setting this to 0, you can turn off checking the fragments for overlapping
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s 			page 3


  66:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  * regions. The code gets a little smaller. Only use this if you know that
  67:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  * overlapping won't occur on your network! */
  68:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #ifndef IP_REASS_CHECK_OVERLAP
  69:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #define IP_REASS_CHECK_OVERLAP 1
  70:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #endif /* IP_REASS_CHECK_OVERLAP */
  71:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
  72:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** /** Set to 0 to prevent freeing the oldest datagram when the reassembly buffer is
  73:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  * full (IP_REASS_MAX_PBUFS pbufs are enqueued). The code gets a little smaller.
  74:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  * Datagrams will be freed by timeout only. Especially useful when MEMP_NUM_REASSDATA
  75:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  * is set to 1, so one datagram can be reassembled at a time, only. */
  76:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #ifndef IP_REASS_FREE_OLDEST
  77:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #define IP_REASS_FREE_OLDEST 1
  78:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #endif /* IP_REASS_FREE_OLDEST */
  79:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
  80:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #define IP_REASS_FLAG_LASTFRAG 0x01
  81:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
  82:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #define IP_REASS_VALIDATE_TELEGRAM_FINISHED  1
  83:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #define IP_REASS_VALIDATE_PBUF_QUEUED        0
  84:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #define IP_REASS_VALIDATE_PBUF_DROPPED       -1
  85:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
  86:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** /** This is a helper struct which holds the starting
  87:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  * offset and the ending offset of this fragment to
  88:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  * easily chain the fragments.
  89:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  * It has the same packing requirements as the IP header, since it replaces
  90:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  * the IP header in memory in incoming fragments (after copying it) to keep
  91:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  * track of the various fragments. (-> If the IP header doesn't need packing,
  92:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  * this struct doesn't need packing, too.)
  93:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  */
  94:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #ifdef PACK_STRUCT_USE_INCLUDES
  95:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #  include "arch/bpstruct.h"
  96:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #endif
  97:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** PACK_STRUCT_BEGIN
  98:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** struct ip_reass_helper {
  99:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   PACK_STRUCT_FIELD(struct pbuf *next_pbuf);
 100:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   PACK_STRUCT_FIELD(u16_t start);
 101:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   PACK_STRUCT_FIELD(u16_t end);
 102:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** } PACK_STRUCT_STRUCT;
 103:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** PACK_STRUCT_END
 104:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #ifdef PACK_STRUCT_USE_INCLUDES
 105:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #  include "arch/epstruct.h"
 106:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #endif
 107:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 108:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #define IP_ADDRESSES_AND_ID_MATCH(iphdrA, iphdrB)  \
 109:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   (ip4_addr_cmp(&(iphdrA)->src, &(iphdrB)->src) && \
 110:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****    ip4_addr_cmp(&(iphdrA)->dest, &(iphdrB)->dest) && \
 111:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****    IPH_ID(iphdrA) == IPH_ID(iphdrB)) ? 1 : 0
 112:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 113:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** /* global variables */
 114:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** static struct ip_reassdata *reassdatagrams;
 115:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** static u16_t ip_reass_pbufcount;
 116:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 117:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** /* function prototypes */
 118:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** static void ip_reass_dequeue_datagram(struct ip_reassdata *ipr, struct ip_reassdata *prev);
 119:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** static int ip_reass_free_complete_datagram(struct ip_reassdata *ipr, struct ip_reassdata *prev);
 120:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 121:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** /**
 122:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  * Reassembly timer base function
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s 			page 4


 123:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  * for both NO_SYS == 0 and 1 (!).
 124:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  *
 125:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  * Should be called every 1000 msec (defined by IP_TMR_INTERVAL).
 126:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  */
 127:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** void
 128:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** ip_reass_tmr(void)
 129:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** {
 130:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   struct ip_reassdata *r, *prev = NULL;
 131:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 132:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   r = reassdatagrams;
 133:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   while (r != NULL) {
 134:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     /* Decrement the timer. Once it reaches 0,
 135:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****      * clean up the incomplete fragment assembly */
 136:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     if (r->timer > 0) {
 137:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       r->timer--;
 138:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       LWIP_DEBUGF(IP_REASS_DEBUG, ("ip_reass_tmr: timer dec %"U16_F"\n", (u16_t)r->timer));
 139:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       prev = r;
 140:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       r = r->next;
 141:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     } else {
 142:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       /* reassembly timed out */
 143:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       struct ip_reassdata *tmp;
 144:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       LWIP_DEBUGF(IP_REASS_DEBUG, ("ip_reass_tmr: timer timed out\n"));
 145:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       tmp = r;
 146:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       /* get the next pointer before freeing */
 147:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       r = r->next;
 148:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       /* free the helper struct and all enqueued pbufs */
 149:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       ip_reass_free_complete_datagram(tmp, prev);
 150:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     }
 151:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   }
 152:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** }
 153:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 154:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** /**
 155:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  * Free a datagram (struct ip_reassdata) and all its pbufs.
 156:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  * Updates the total count of enqueued pbufs (ip_reass_pbufcount),
 157:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  * SNMP counters and sends an ICMP time exceeded packet.
 158:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  *
 159:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  * @param ipr datagram to free
 160:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  * @param prev the previous datagram in the linked list
 161:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  * @return the number of pbufs freed
 162:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  */
 163:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** static int
 164:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** ip_reass_free_complete_datagram(struct ip_reassdata *ipr, struct ip_reassdata *prev)
 165:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** {
 166:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   u16_t pbufs_freed = 0;
 167:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   u16_t clen;
 168:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   struct pbuf *p;
 169:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   struct ip_reass_helper *iprh;
 170:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 171:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   LWIP_ASSERT("prev != ipr", prev != ipr);
 172:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   if (prev != NULL) {
 173:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     LWIP_ASSERT("prev->next == ipr", prev->next == ipr);
 174:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   }
 175:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 176:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   MIB2_STATS_INC(mib2.ipreasmfails);
 177:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #if LWIP_ICMP
 178:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   iprh = (struct ip_reass_helper *)ipr->p->payload;
 179:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   if (iprh->start == 0) {
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s 			page 5


 180:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     /* The first fragment was received, send ICMP time exceeded. */
 181:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     /* First, de-queue the first pbuf from r->p. */
 182:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     p = ipr->p;
 183:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     ipr->p = iprh->next_pbuf;
 184:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     /* Then, copy the original header into it. */
 185:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     SMEMCPY(p->payload, &ipr->iphdr, IP_HLEN);
 186:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     icmp_time_exceeded(p, ICMP_TE_FRAG);
 187:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     clen = pbuf_clen(p);
 188:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     LWIP_ASSERT("pbufs_freed + clen <= 0xffff", pbufs_freed + clen <= 0xffff);
 189:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     pbufs_freed = (u16_t)(pbufs_freed + clen);
 190:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     pbuf_free(p);
 191:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   }
 192:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #endif /* LWIP_ICMP */
 193:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 194:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   /* First, free all received pbufs.  The individual pbufs need to be released
 195:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****      separately as they have not yet been chained */
 196:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   p = ipr->p;
 197:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   while (p != NULL) {
 198:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     struct pbuf *pcur;
 199:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     iprh = (struct ip_reass_helper *)p->payload;
 200:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     pcur = p;
 201:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     /* get the next pointer before freeing */
 202:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     p = iprh->next_pbuf;
 203:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     clen = pbuf_clen(pcur);
 204:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     LWIP_ASSERT("pbufs_freed + clen <= 0xffff", pbufs_freed + clen <= 0xffff);
 205:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     pbufs_freed = (u16_t)(pbufs_freed + clen);
 206:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     pbuf_free(pcur);
 207:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   }
 208:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   /* Then, unchain the struct ip_reassdata from the list and free it. */
 209:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   ip_reass_dequeue_datagram(ipr, prev);
 210:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   LWIP_ASSERT("ip_reass_pbufcount >= pbufs_freed", ip_reass_pbufcount >= pbufs_freed);
 211:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   ip_reass_pbufcount = (u16_t)(ip_reass_pbufcount - pbufs_freed);
 212:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 213:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   return pbufs_freed;
 214:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** }
 215:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 216:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #if IP_REASS_FREE_OLDEST
 217:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** /**
 218:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  * Free the oldest datagram to make room for enqueueing new fragments.
 219:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  * The datagram 'fraghdr' belongs to is not freed!
 220:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  *
 221:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  * @param fraghdr IP header of the current fragment
 222:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  * @param pbufs_needed number of pbufs needed to enqueue
 223:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  *        (used for freeing other datagrams if not enough space)
 224:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  * @return the number of pbufs freed
 225:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  */
 226:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** static int
 227:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** ip_reass_remove_oldest_datagram(struct ip_hdr *fraghdr, int pbufs_needed)
 228:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** {
 229:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   /* @todo Can't we simply remove the last datagram in the
 230:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****    *       linked list behind reassdatagrams?
 231:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****    */
 232:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   struct ip_reassdata *r, *oldest, *prev, *oldest_prev;
 233:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   int pbufs_freed = 0, pbufs_freed_current;
 234:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   int other_datagrams;
 235:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 236:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   /* Free datagrams until being allowed to enqueue 'pbufs_needed' pbufs,
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s 			page 6


 237:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****    * but don't free the datagram that 'fraghdr' belongs to! */
 238:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   do {
 239:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     oldest = NULL;
 240:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     prev = NULL;
 241:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     oldest_prev = NULL;
 242:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     other_datagrams = 0;
 243:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     r = reassdatagrams;
 244:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     while (r != NULL) {
 245:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       if (!IP_ADDRESSES_AND_ID_MATCH(&r->iphdr, fraghdr)) {
 246:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         /* Not the same datagram as fraghdr */
 247:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         other_datagrams++;
 248:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         if (oldest == NULL) {
 249:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****           oldest = r;
 250:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****           oldest_prev = prev;
 251:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         } else if (r->timer <= oldest->timer) {
 252:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****           /* older than the previous oldest */
 253:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****           oldest = r;
 254:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****           oldest_prev = prev;
 255:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         }
 256:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       }
 257:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       if (r->next != NULL) {
 258:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         prev = r;
 259:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       }
 260:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       r = r->next;
 261:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     }
 262:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     if (oldest != NULL) {
 263:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       pbufs_freed_current = ip_reass_free_complete_datagram(oldest, oldest_prev);
 264:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       pbufs_freed += pbufs_freed_current;
 265:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     }
 266:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   } while ((pbufs_freed < pbufs_needed) && (other_datagrams > 1));
 267:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   return pbufs_freed;
 268:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** }
 269:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #endif /* IP_REASS_FREE_OLDEST */
 270:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 271:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** /**
 272:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  * Enqueues a new fragment into the fragment queue
 273:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  * @param fraghdr points to the new fragments IP hdr
 274:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  * @param clen number of pbufs needed to enqueue (used for freeing other datagrams if not enough sp
 275:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  * @return A pointer to the queue location into which the fragment was enqueued
 276:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  */
 277:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** static struct ip_reassdata *
 278:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** ip_reass_enqueue_new_datagram(struct ip_hdr *fraghdr, int clen)
 279:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** {
 280:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   struct ip_reassdata *ipr;
 281:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #if ! IP_REASS_FREE_OLDEST
 282:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   LWIP_UNUSED_ARG(clen);
 283:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #endif
 284:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 285:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   /* No matching previous fragment found, allocate a new reassdata struct */
 286:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   ipr = (struct ip_reassdata *)memp_malloc(MEMP_REASSDATA);
 287:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   if (ipr == NULL) {
 288:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #if IP_REASS_FREE_OLDEST
 289:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     if (ip_reass_remove_oldest_datagram(fraghdr, clen) >= clen) {
 290:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       ipr = (struct ip_reassdata *)memp_malloc(MEMP_REASSDATA);
 291:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     }
 292:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     if (ipr == NULL)
 293:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #endif /* IP_REASS_FREE_OLDEST */
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s 			page 7


 294:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     {
 295:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       IPFRAG_STATS_INC(ip_frag.memerr);
 296:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       LWIP_DEBUGF(IP_REASS_DEBUG, ("Failed to alloc reassdata struct\n"));
 297:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       return NULL;
 298:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     }
 299:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   }
 300:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   memset(ipr, 0, sizeof(struct ip_reassdata));
 301:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   ipr->timer = IP_REASS_MAXAGE;
 302:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 303:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   /* enqueue the new structure to the front of the list */
 304:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   ipr->next = reassdatagrams;
 305:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   reassdatagrams = ipr;
 306:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   /* copy the ip header for later tests and input */
 307:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   /* @todo: no ip options supported? */
 308:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   SMEMCPY(&(ipr->iphdr), fraghdr, IP_HLEN);
 309:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   return ipr;
 310:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** }
 311:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 312:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** /**
 313:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  * Dequeues a datagram from the datagram queue. Doesn't deallocate the pbufs.
 314:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  * @param ipr points to the queue entry to dequeue
 315:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  */
 316:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** static void
 317:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** ip_reass_dequeue_datagram(struct ip_reassdata *ipr, struct ip_reassdata *prev)
 318:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** {
  39              		.loc 1 318 1 view -0
  40              		.cfi_startproc
  41              		@ args = 0, pretend = 0, frame = 0
  42              		@ frame_needed = 0, uses_anonymous_args = 0
  43              		.loc 1 318 1 is_stmt 0 view .LVU1
  44 0000 38B5     		push	{r3, r4, r5, lr}
  45              	.LCFI0:
  46              		.cfi_def_cfa_offset 16
  47              		.cfi_offset 3, -16
  48              		.cfi_offset 4, -12
  49              		.cfi_offset 5, -8
  50              		.cfi_offset 14, -4
  51 0002 0446     		mov	r4, r0
 319:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   /* dequeue the reass struct  */
 320:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   if (reassdatagrams == ipr) {
  52              		.loc 1 320 3 is_stmt 1 view .LVU2
  53              		.loc 1 320 22 is_stmt 0 view .LVU3
  54 0004 0C4B     		ldr	r3, .L8
  55 0006 1B68     		ldr	r3, [r3]
  56              		.loc 1 320 6 view .LVU4
  57 0008 8342     		cmp	r3, r0
  58 000a 08D0     		beq	.L6
  59 000c 0D46     		mov	r5, r1
 321:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     /* it was the first in the list */
 322:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     reassdatagrams = ipr->next;
 323:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   } else {
 324:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     /* it wasn't the first, so it must have a valid 'prev' */
 325:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     LWIP_ASSERT("sanity check linked list", prev != NULL);
  60              		.loc 1 325 5 is_stmt 1 view .LVU5
  61              		.loc 1 325 5 view .LVU6
  62 000e 51B1     		cbz	r1, .L7
  63              	.LVL1:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s 			page 8


  64              	.L4:
  65              		.loc 1 325 5 discriminator 3 view .LVU7
  66              		.loc 1 325 5 discriminator 3 view .LVU8
 326:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     prev->next = ipr->next;
  67              		.loc 1 326 5 view .LVU9
  68              		.loc 1 326 21 is_stmt 0 view .LVU10
  69 0010 2368     		ldr	r3, [r4]
  70              		.loc 1 326 16 view .LVU11
  71 0012 2B60     		str	r3, [r5]
  72              	.LVL2:
  73              	.L3:
 327:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   }
 328:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 329:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   /* now we can free the ip_reassdata struct */
 330:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   memp_free(MEMP_REASSDATA, ipr);
  74              		.loc 1 330 3 is_stmt 1 view .LVU12
  75 0014 2146     		mov	r1, r4
  76 0016 0420     		movs	r0, #4
  77 0018 FFF7FEFF 		bl	memp_free
  78              	.LVL3:
 331:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** }
  79              		.loc 1 331 1 is_stmt 0 view .LVU13
  80 001c 38BD     		pop	{r3, r4, r5, pc}
  81              	.LVL4:
  82              	.L6:
 322:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   } else {
  83              		.loc 1 322 5 is_stmt 1 view .LVU14
 322:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   } else {
  84              		.loc 1 322 25 is_stmt 0 view .LVU15
  85 001e 0268     		ldr	r2, [r0]
 322:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   } else {
  86              		.loc 1 322 20 view .LVU16
  87 0020 054B     		ldr	r3, .L8
  88 0022 1A60     		str	r2, [r3]
  89 0024 F6E7     		b	.L3
  90              	.L7:
 325:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     prev->next = ipr->next;
  91              		.loc 1 325 5 is_stmt 1 discriminator 1 view .LVU17
 325:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     prev->next = ipr->next;
  92              		.loc 1 325 5 discriminator 1 view .LVU18
  93 0026 054B     		ldr	r3, .L8+4
  94 0028 40F24512 		movw	r2, #325
  95 002c 0449     		ldr	r1, .L8+8
  96              	.LVL5:
 325:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     prev->next = ipr->next;
  97              		.loc 1 325 5 is_stmt 0 discriminator 1 view .LVU19
  98 002e 0548     		ldr	r0, .L8+12
  99              	.LVL6:
 325:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     prev->next = ipr->next;
 100              		.loc 1 325 5 discriminator 1 view .LVU20
 101 0030 FFF7FEFF 		bl	printf
 102              	.LVL7:
 103 0034 ECE7     		b	.L4
 104              	.L9:
 105 0036 00BF     		.align	2
 106              	.L8:
 107 0038 00000000 		.word	reassdatagrams
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s 			page 9


 108 003c 00000000 		.word	.LC0
 109 0040 38000000 		.word	.LC1
 110 0044 54000000 		.word	.LC2
 111              		.cfi_endproc
 112              	.LFE174:
 114              		.section	.rodata.ip_reass_free_complete_datagram.str1.4,"aMS",%progbits,1
 115              		.align	2
 116              	.LC3:
 117 0000 70726576 		.ascii	"prev != ipr\000"
 117      20213D20 
 117      69707200 
 118              		.align	2
 119              	.LC4:
 120 000c 70726576 		.ascii	"prev->next == ipr\000"
 120      2D3E6E65 
 120      7874203D 
 120      3D206970 
 120      7200
 121 001e 0000     		.align	2
 122              	.LC5:
 123 0020 70627566 		.ascii	"pbufs_freed + clen <= 0xffff\000"
 123      735F6672 
 123      65656420 
 123      2B20636C 
 123      656E203C 
 124 003d 000000   		.align	2
 125              	.LC6:
 126 0040 69705F72 		.ascii	"ip_reass_pbufcount >= pbufs_freed\000"
 126      65617373 
 126      5F706275 
 126      66636F75 
 126      6E74203E 
 127              		.section	.text.ip_reass_free_complete_datagram,"ax",%progbits
 128              		.align	1
 129              		.syntax unified
 130              		.thumb
 131              		.thumb_func
 133              	ip_reass_free_complete_datagram:
 134              	.LVL8:
 135              	.LFB171:
 165:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   u16_t pbufs_freed = 0;
 136              		.loc 1 165 1 is_stmt 1 view -0
 137              		.cfi_startproc
 138              		@ args = 0, pretend = 0, frame = 0
 139              		@ frame_needed = 0, uses_anonymous_args = 0
 165:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   u16_t pbufs_freed = 0;
 140              		.loc 1 165 1 is_stmt 0 view .LVU22
 141 0000 2DE9F843 		push	{r3, r4, r5, r6, r7, r8, r9, lr}
 142              	.LCFI1:
 143              		.cfi_def_cfa_offset 32
 144              		.cfi_offset 3, -32
 145              		.cfi_offset 4, -28
 146              		.cfi_offset 5, -24
 147              		.cfi_offset 6, -20
 148              		.cfi_offset 7, -16
 149              		.cfi_offset 8, -12
 150              		.cfi_offset 9, -8
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s 			page 10


 151              		.cfi_offset 14, -4
 152 0004 8046     		mov	r8, r0
 153 0006 8946     		mov	r9, r1
 166:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   u16_t clen;
 154              		.loc 1 166 3 is_stmt 1 view .LVU23
 155              	.LVL9:
 167:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   struct pbuf *p;
 156              		.loc 1 167 3 view .LVU24
 168:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   struct ip_reass_helper *iprh;
 157              		.loc 1 168 3 view .LVU25
 169:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 158              		.loc 1 169 3 view .LVU26
 171:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   if (prev != NULL) {
 159              		.loc 1 171 3 view .LVU27
 171:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   if (prev != NULL) {
 160              		.loc 1 171 3 view .LVU28
 161 0008 8142     		cmp	r1, r0
 162 000a 15D0     		beq	.L20
 163              	.LVL10:
 164              	.L11:
 171:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   if (prev != NULL) {
 165              		.loc 1 171 3 discriminator 3 view .LVU29
 171:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   if (prev != NULL) {
 166              		.loc 1 171 3 discriminator 3 view .LVU30
 172:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     LWIP_ASSERT("prev->next == ipr", prev->next == ipr);
 167              		.loc 1 172 3 view .LVU31
 172:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     LWIP_ASSERT("prev->next == ipr", prev->next == ipr);
 168              		.loc 1 172 6 is_stmt 0 view .LVU32
 169 000c B9F1000F 		cmp	r9, #0
 170 0010 09D0     		beq	.L12
 173:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   }
 171              		.loc 1 173 5 is_stmt 1 view .LVU33
 173:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   }
 172              		.loc 1 173 5 view .LVU34
 173 0012 D9F80030 		ldr	r3, [r9]
 174 0016 4345     		cmp	r3, r8
 175 0018 05D0     		beq	.L12
 173:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   }
 176              		.loc 1 173 5 discriminator 1 view .LVU35
 173:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   }
 177              		.loc 1 173 5 discriminator 1 view .LVU36
 178 001a 314B     		ldr	r3, .L24
 179 001c AD22     		movs	r2, #173
 180 001e 3149     		ldr	r1, .L24+4
 181 0020 3148     		ldr	r0, .L24+8
 182 0022 FFF7FEFF 		bl	printf
 183              	.LVL11:
 184              	.L12:
 173:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   }
 185              		.loc 1 173 5 discriminator 3 view .LVU37
 173:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   }
 186              		.loc 1 173 5 discriminator 3 view .LVU38
 176:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #if LWIP_ICMP
 187              		.loc 1 176 36 view .LVU39
 178:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   if (iprh->start == 0) {
 188              		.loc 1 178 3 view .LVU40
 178:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   if (iprh->start == 0) {
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s 			page 11


 189              		.loc 1 178 39 is_stmt 0 view .LVU41
 190 0026 D8F80440 		ldr	r4, [r8, #4]
 178:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   if (iprh->start == 0) {
 191              		.loc 1 178 8 view .LVU42
 192 002a 6368     		ldr	r3, [r4, #4]
 193              	.LVL12:
 179:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     /* The first fragment was received, send ICMP time exceeded. */
 194              		.loc 1 179 3 is_stmt 1 view .LVU43
 179:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     /* The first fragment was received, send ICMP time exceeded. */
 195              		.loc 1 179 11 is_stmt 0 view .LVU44
 196 002c 9A88     		ldrh	r2, [r3, #4]	@ unaligned
 179:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     /* The first fragment was received, send ICMP time exceeded. */
 197              		.loc 1 179 6 view .LVU45
 198 002e 52B1     		cbz	r2, .L21
 166:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   u16_t clen;
 199              		.loc 1 166 9 view .LVU46
 200 0030 0026     		movs	r6, #0
 201              	.LVL13:
 202              	.L13:
 196:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   while (p != NULL) {
 203              		.loc 1 196 3 is_stmt 1 view .LVU47
 196:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   while (p != NULL) {
 204              		.loc 1 196 5 is_stmt 0 view .LVU48
 205 0032 D8F80450 		ldr	r5, [r8, #4]
 206              	.LVL14:
 197:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     struct pbuf *pcur;
 207              		.loc 1 197 3 is_stmt 1 view .LVU49
 197:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     struct pbuf *pcur;
 208              		.loc 1 197 9 is_stmt 0 view .LVU50
 209 0036 2BE0     		b	.L14
 210              	.LVL15:
 211              	.L20:
 171:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   if (prev != NULL) {
 212              		.loc 1 171 3 is_stmt 1 discriminator 1 view .LVU51
 171:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   if (prev != NULL) {
 213              		.loc 1 171 3 discriminator 1 view .LVU52
 214 0038 294B     		ldr	r3, .L24
 215 003a AB22     		movs	r2, #171
 216 003c 2B49     		ldr	r1, .L24+12
 217              	.LVL16:
 171:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   if (prev != NULL) {
 218              		.loc 1 171 3 is_stmt 0 discriminator 1 view .LVU53
 219 003e 2A48     		ldr	r0, .L24+8
 220              	.LVL17:
 171:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   if (prev != NULL) {
 221              		.loc 1 171 3 discriminator 1 view .LVU54
 222 0040 FFF7FEFF 		bl	printf
 223              	.LVL18:
 224 0044 E2E7     		b	.L11
 225              	.LVL19:
 226              	.L21:
 182:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     ipr->p = iprh->next_pbuf;
 227              		.loc 1 182 5 is_stmt 1 view .LVU55
 183:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     /* Then, copy the original header into it. */
 228              		.loc 1 183 5 view .LVU56
 183:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     /* Then, copy the original header into it. */
 229              		.loc 1 183 18 is_stmt 0 view .LVU57
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s 			page 12


 230 0046 1B68     		ldr	r3, [r3]	@ unaligned
 231              	.LVL20:
 183:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     /* Then, copy the original header into it. */
 232              		.loc 1 183 12 view .LVU58
 233 0048 C8F80430 		str	r3, [r8, #4]
 234              	.LVL21:
 185:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     icmp_time_exceeded(p, ICMP_TE_FRAG);
 235              		.loc 1 185 5 is_stmt 1 view .LVU59
 236 004c 6368     		ldr	r3, [r4, #4]
 237 004e D8F80820 		ldr	r2, [r8, #8]	@ unaligned
 238 0052 D8F80C50 		ldr	r5, [r8, #12]	@ unaligned
 239 0056 D8F81000 		ldr	r0, [r8, #16]	@ unaligned
 240 005a D8F81410 		ldr	r1, [r8, #20]	@ unaligned
 241 005e 1A60     		str	r2, [r3]	@ unaligned
 242 0060 5D60     		str	r5, [r3, #4]	@ unaligned
 243 0062 9860     		str	r0, [r3, #8]	@ unaligned
 244 0064 D960     		str	r1, [r3, #12]	@ unaligned
 245 0066 D8F81820 		ldr	r2, [r8, #24]	@ unaligned
 246 006a 1A61     		str	r2, [r3, #16]	@ unaligned
 186:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     clen = pbuf_clen(p);
 247              		.loc 1 186 5 view .LVU60
 248 006c 0121     		movs	r1, #1
 249 006e 2046     		mov	r0, r4
 250 0070 FFF7FEFF 		bl	icmp_time_exceeded
 251              	.LVL22:
 187:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     LWIP_ASSERT("pbufs_freed + clen <= 0xffff", pbufs_freed + clen <= 0xffff);
 252              		.loc 1 187 5 view .LVU61
 187:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     LWIP_ASSERT("pbufs_freed + clen <= 0xffff", pbufs_freed + clen <= 0xffff);
 253              		.loc 1 187 12 is_stmt 0 view .LVU62
 254 0074 2046     		mov	r0, r4
 255 0076 FFF7FEFF 		bl	pbuf_clen
 256              	.LVL23:
 257 007a 0646     		mov	r6, r0
 258              	.LVL24:
 188:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     pbufs_freed = (u16_t)(pbufs_freed + clen);
 259              		.loc 1 188 5 is_stmt 1 view .LVU63
 188:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     pbufs_freed = (u16_t)(pbufs_freed + clen);
 260              		.loc 1 188 5 view .LVU64
 188:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     pbufs_freed = (u16_t)(pbufs_freed + clen);
 261              		.loc 1 188 5 discriminator 3 view .LVU65
 188:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     pbufs_freed = (u16_t)(pbufs_freed + clen);
 262              		.loc 1 188 5 discriminator 3 view .LVU66
 189:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     pbuf_free(p);
 263              		.loc 1 189 5 view .LVU67
 190:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   }
 264              		.loc 1 190 5 view .LVU68
 265 007c 2046     		mov	r0, r4
 266 007e FFF7FEFF 		bl	pbuf_free
 267              	.LVL25:
 268 0082 D6E7     		b	.L13
 269              	.LVL26:
 270              	.L15:
 271              	.LBB2:
 204:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     pbufs_freed = (u16_t)(pbufs_freed + clen);
 272              		.loc 1 204 5 discriminator 3 view .LVU69
 204:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     pbufs_freed = (u16_t)(pbufs_freed + clen);
 273              		.loc 1 204 5 discriminator 3 view .LVU70
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s 			page 13


 205:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     pbuf_free(pcur);
 274              		.loc 1 205 5 view .LVU71
 205:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     pbuf_free(pcur);
 275              		.loc 1 205 17 is_stmt 0 view .LVU72
 276 0084 3444     		add	r4, r4, r6
 277 0086 A6B2     		uxth	r6, r4
 278              	.LVL27:
 206:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   }
 279              		.loc 1 206 5 is_stmt 1 view .LVU73
 280 0088 2846     		mov	r0, r5
 281 008a FFF7FEFF 		bl	pbuf_free
 282              	.LVL28:
 202:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     clen = pbuf_clen(pcur);
 283              		.loc 1 202 7 is_stmt 0 view .LVU74
 284 008e 3D46     		mov	r5, r7
 285              	.LVL29:
 286              	.L14:
 202:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     clen = pbuf_clen(pcur);
 287              		.loc 1 202 7 view .LVU75
 288              	.LBE2:
 197:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     struct pbuf *pcur;
 289              		.loc 1 197 12 is_stmt 1 view .LVU76
 290 0090 85B1     		cbz	r5, .L22
 291              	.LBB3:
 198:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     iprh = (struct ip_reass_helper *)p->payload;
 292              		.loc 1 198 5 view .LVU77
 199:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     pcur = p;
 293              		.loc 1 199 5 view .LVU78
 199:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     pcur = p;
 294              		.loc 1 199 10 is_stmt 0 view .LVU79
 295 0092 6B68     		ldr	r3, [r5, #4]
 296              	.LVL30:
 200:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     /* get the next pointer before freeing */
 297              		.loc 1 200 5 is_stmt 1 view .LVU80
 202:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     clen = pbuf_clen(pcur);
 298              		.loc 1 202 5 view .LVU81
 202:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     clen = pbuf_clen(pcur);
 299              		.loc 1 202 7 is_stmt 0 view .LVU82
 300 0094 1F68     		ldr	r7, [r3]	@ unaligned
 301              	.LVL31:
 203:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     LWIP_ASSERT("pbufs_freed + clen <= 0xffff", pbufs_freed + clen <= 0xffff);
 302              		.loc 1 203 5 is_stmt 1 view .LVU83
 203:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     LWIP_ASSERT("pbufs_freed + clen <= 0xffff", pbufs_freed + clen <= 0xffff);
 303              		.loc 1 203 12 is_stmt 0 view .LVU84
 304 0096 2846     		mov	r0, r5
 305 0098 FFF7FEFF 		bl	pbuf_clen
 306              	.LVL32:
 203:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     LWIP_ASSERT("pbufs_freed + clen <= 0xffff", pbufs_freed + clen <= 0xffff);
 307              		.loc 1 203 12 view .LVU85
 308 009c 0446     		mov	r4, r0
 309              	.LVL33:
 204:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     pbufs_freed = (u16_t)(pbufs_freed + clen);
 310              		.loc 1 204 5 is_stmt 1 view .LVU86
 204:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     pbufs_freed = (u16_t)(pbufs_freed + clen);
 311              		.loc 1 204 5 view .LVU87
 312 009e 3318     		adds	r3, r6, r0
 313 00a0 B3F5803F 		cmp	r3, #65536
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s 			page 14


 314 00a4 EEDB     		blt	.L15
 204:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     pbufs_freed = (u16_t)(pbufs_freed + clen);
 315              		.loc 1 204 5 discriminator 1 view .LVU88
 204:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     pbufs_freed = (u16_t)(pbufs_freed + clen);
 316              		.loc 1 204 5 discriminator 1 view .LVU89
 317 00a6 0E4B     		ldr	r3, .L24
 318 00a8 CC22     		movs	r2, #204
 319 00aa 1149     		ldr	r1, .L24+16
 320 00ac 0E48     		ldr	r0, .L24+8
 321              	.LVL34:
 204:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     pbufs_freed = (u16_t)(pbufs_freed + clen);
 322              		.loc 1 204 5 is_stmt 0 discriminator 1 view .LVU90
 323 00ae FFF7FEFF 		bl	printf
 324              	.LVL35:
 325 00b2 E7E7     		b	.L15
 326              	.LVL36:
 327              	.L22:
 204:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     pbufs_freed = (u16_t)(pbufs_freed + clen);
 328              		.loc 1 204 5 discriminator 1 view .LVU91
 329              	.LBE3:
 209:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   LWIP_ASSERT("ip_reass_pbufcount >= pbufs_freed", ip_reass_pbufcount >= pbufs_freed);
 330              		.loc 1 209 3 is_stmt 1 view .LVU92
 331 00b4 4946     		mov	r1, r9
 332 00b6 4046     		mov	r0, r8
 333 00b8 FFF7FEFF 		bl	ip_reass_dequeue_datagram
 334              	.LVL37:
 210:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   ip_reass_pbufcount = (u16_t)(ip_reass_pbufcount - pbufs_freed);
 335              		.loc 1 210 3 view .LVU93
 210:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   ip_reass_pbufcount = (u16_t)(ip_reass_pbufcount - pbufs_freed);
 336              		.loc 1 210 3 view .LVU94
 337 00bc 0D4B     		ldr	r3, .L24+20
 338 00be 1B88     		ldrh	r3, [r3]
 339 00c0 B342     		cmp	r3, r6
 340 00c2 06D3     		bcc	.L23
 341              	.L17:
 210:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   ip_reass_pbufcount = (u16_t)(ip_reass_pbufcount - pbufs_freed);
 342              		.loc 1 210 3 discriminator 3 view .LVU95
 210:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   ip_reass_pbufcount = (u16_t)(ip_reass_pbufcount - pbufs_freed);
 343              		.loc 1 210 3 discriminator 3 view .LVU96
 211:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 344              		.loc 1 211 3 view .LVU97
 211:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 345              		.loc 1 211 24 is_stmt 0 view .LVU98
 346 00c4 0B4A     		ldr	r2, .L24+20
 347 00c6 1388     		ldrh	r3, [r2]
 348 00c8 9B1B     		subs	r3, r3, r6
 211:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 349              		.loc 1 211 22 view .LVU99
 350 00ca 1380     		strh	r3, [r2]	@ movhi
 213:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** }
 351              		.loc 1 213 3 is_stmt 1 view .LVU100
 214:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 352              		.loc 1 214 1 is_stmt 0 view .LVU101
 353 00cc 3046     		mov	r0, r6
 354 00ce BDE8F883 		pop	{r3, r4, r5, r6, r7, r8, r9, pc}
 355              	.LVL38:
 356              	.L23:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s 			page 15


 210:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   ip_reass_pbufcount = (u16_t)(ip_reass_pbufcount - pbufs_freed);
 357              		.loc 1 210 3 is_stmt 1 discriminator 1 view .LVU102
 210:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   ip_reass_pbufcount = (u16_t)(ip_reass_pbufcount - pbufs_freed);
 358              		.loc 1 210 3 discriminator 1 view .LVU103
 359 00d2 034B     		ldr	r3, .L24
 360 00d4 D222     		movs	r2, #210
 361 00d6 0849     		ldr	r1, .L24+24
 362 00d8 0348     		ldr	r0, .L24+8
 363 00da FFF7FEFF 		bl	printf
 364              	.LVL39:
 365 00de F1E7     		b	.L17
 366              	.L25:
 367              		.align	2
 368              	.L24:
 369 00e0 00000000 		.word	.LC0
 370 00e4 0C000000 		.word	.LC4
 371 00e8 54000000 		.word	.LC2
 372 00ec 00000000 		.word	.LC3
 373 00f0 20000000 		.word	.LC5
 374 00f4 00000000 		.word	ip_reass_pbufcount
 375 00f8 40000000 		.word	.LC6
 376              		.cfi_endproc
 377              	.LFE171:
 379              		.section	.text.ip_reass_remove_oldest_datagram,"ax",%progbits
 380              		.align	1
 381              		.syntax unified
 382              		.thumb
 383              		.thumb_func
 385              	ip_reass_remove_oldest_datagram:
 386              	.LVL40:
 387              	.LFB172:
 228:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   /* @todo Can't we simply remove the last datagram in the
 388              		.loc 1 228 1 view -0
 389              		.cfi_startproc
 390              		@ args = 0, pretend = 0, frame = 0
 391              		@ frame_needed = 0, uses_anonymous_args = 0
 228:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   /* @todo Can't we simply remove the last datagram in the
 392              		.loc 1 228 1 is_stmt 0 view .LVU105
 393 0000 F8B5     		push	{r3, r4, r5, r6, r7, lr}
 394              	.LCFI2:
 395              		.cfi_def_cfa_offset 24
 396              		.cfi_offset 3, -24
 397              		.cfi_offset 4, -20
 398              		.cfi_offset 5, -16
 399              		.cfi_offset 6, -12
 400              		.cfi_offset 7, -8
 401              		.cfi_offset 14, -4
 402 0002 0546     		mov	r5, r0
 403 0004 0F46     		mov	r7, r1
 232:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   int pbufs_freed = 0, pbufs_freed_current;
 404              		.loc 1 232 3 is_stmt 1 view .LVU106
 233:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   int other_datagrams;
 405              		.loc 1 233 3 view .LVU107
 406              	.LVL41:
 233:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   int other_datagrams;
 407              		.loc 1 233 7 is_stmt 0 view .LVU108
 408 0006 0026     		movs	r6, #0
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s 			page 16


 409 0008 2EE0     		b	.L33
 410              	.LVL42:
 411              	.L40:
 245:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         /* Not the same datagram as fraghdr */
 412              		.loc 1 245 12 discriminator 1 view .LVU109
 413 000a 9A69     		ldr	r2, [r3, #24]
 414 000c D5F810E0 		ldr	lr, [r5, #16]	@ unaligned
 415 0010 7245     		cmp	r2, lr
 416 0012 11D1     		bne	.L28
 245:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         /* Not the same datagram as fraghdr */
 417              		.loc 1 245 12 discriminator 2 view .LVU110
 418 0014 B3F80CE0 		ldrh	lr, [r3, #12]
 419 0018 AA88     		ldrh	r2, [r5, #4]	@ unaligned
 245:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         /* Not the same datagram as fraghdr */
 420              		.loc 1 245 11 discriminator 2 view .LVU111
 421 001a 9645     		cmp	lr, r2
 422 001c 0CD1     		bne	.L28
 423 001e 01E0     		b	.L29
 424              	.L35:
 250:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         } else if (r->timer <= oldest->timer) {
 425              		.loc 1 250 23 view .LVU112
 426 0020 6146     		mov	r1, ip
 427              	.LVL43:
 249:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****           oldest_prev = prev;
 428              		.loc 1 249 18 view .LVU113
 429 0022 1846     		mov	r0, r3
 430              	.LVL44:
 431              	.L29:
 257:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         prev = r;
 432              		.loc 1 257 7 is_stmt 1 view .LVU114
 257:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         prev = r;
 433              		.loc 1 257 12 is_stmt 0 view .LVU115
 434 0024 1A68     		ldr	r2, [r3]
 257:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         prev = r;
 435              		.loc 1 257 10 view .LVU116
 436 0026 92B1     		cbz	r2, .L38
 437              	.LVL45:
 438              	.L30:
 257:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         prev = r;
 439              		.loc 1 257 10 view .LVU117
 440 0028 9C46     		mov	ip, r3
 441 002a 1346     		mov	r3, r2
 442              	.LVL46:
 443              	.L27:
 244:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       if (!IP_ADDRESSES_AND_ID_MATCH(&r->iphdr, fraghdr)) {
 444              		.loc 1 244 14 is_stmt 1 view .LVU118
 445 002c 8BB1     		cbz	r3, .L39
 245:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         /* Not the same datagram as fraghdr */
 446              		.loc 1 245 7 view .LVU119
 245:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         /* Not the same datagram as fraghdr */
 447              		.loc 1 245 12 is_stmt 0 view .LVU120
 448 002e 5A69     		ldr	r2, [r3, #20]
 449 0030 D5F80CE0 		ldr	lr, [r5, #12]	@ unaligned
 245:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         /* Not the same datagram as fraghdr */
 450              		.loc 1 245 10 view .LVU121
 451 0034 7245     		cmp	r2, lr
 452 0036 E8D0     		beq	.L40
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s 			page 17


 453              	.L28:
 247:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         if (oldest == NULL) {
 454              		.loc 1 247 9 is_stmt 1 view .LVU122
 247:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         if (oldest == NULL) {
 455              		.loc 1 247 24 is_stmt 0 view .LVU123
 456 0038 0134     		adds	r4, r4, #1
 457              	.LVL47:
 248:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****           oldest = r;
 458              		.loc 1 248 9 is_stmt 1 view .LVU124
 248:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****           oldest = r;
 459              		.loc 1 248 12 is_stmt 0 view .LVU125
 460 003a 0028     		cmp	r0, #0
 461 003c F0D0     		beq	.L35
 251:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****           /* older than the previous oldest */
 462              		.loc 1 251 16 is_stmt 1 view .LVU126
 251:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****           /* older than the previous oldest */
 463              		.loc 1 251 21 is_stmt 0 view .LVU127
 464 003e 93F81FE0 		ldrb	lr, [r3, #31]	@ zero_extendqisi2
 251:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****           /* older than the previous oldest */
 465              		.loc 1 251 38 view .LVU128
 466 0042 C27F     		ldrb	r2, [r0, #31]	@ zero_extendqisi2
 251:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****           /* older than the previous oldest */
 467              		.loc 1 251 19 view .LVU129
 468 0044 9645     		cmp	lr, r2
 469 0046 EDD8     		bhi	.L29
 254:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         }
 470              		.loc 1 254 23 view .LVU130
 471 0048 6146     		mov	r1, ip
 472              	.LVL48:
 253:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****           oldest_prev = prev;
 473              		.loc 1 253 18 view .LVU131
 474 004a 1846     		mov	r0, r3
 475              	.LVL49:
 253:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****           oldest_prev = prev;
 476              		.loc 1 253 18 view .LVU132
 477 004c EAE7     		b	.L29
 478              	.LVL50:
 479              	.L38:
 257:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         prev = r;
 480              		.loc 1 257 10 view .LVU133
 481 004e 6346     		mov	r3, ip
 482              	.LVL51:
 257:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         prev = r;
 483              		.loc 1 257 10 view .LVU134
 484 0050 EAE7     		b	.L30
 485              	.LVL52:
 486              	.L39:
 262:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       pbufs_freed_current = ip_reass_free_complete_datagram(oldest, oldest_prev);
 487              		.loc 1 262 5 is_stmt 1 view .LVU135
 262:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       pbufs_freed_current = ip_reass_free_complete_datagram(oldest, oldest_prev);
 488              		.loc 1 262 8 is_stmt 0 view .LVU136
 489 0052 10B1     		cbz	r0, .L32
 263:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       pbufs_freed += pbufs_freed_current;
 490              		.loc 1 263 7 is_stmt 1 view .LVU137
 263:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       pbufs_freed += pbufs_freed_current;
 491              		.loc 1 263 29 is_stmt 0 view .LVU138
 492 0054 FFF7FEFF 		bl	ip_reass_free_complete_datagram
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s 			page 18


 493              	.LVL53:
 264:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     }
 494              		.loc 1 264 7 is_stmt 1 view .LVU139
 264:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     }
 495              		.loc 1 264 19 is_stmt 0 view .LVU140
 496 0058 0644     		add	r6, r6, r0
 497              	.LVL54:
 498              	.L32:
 266:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   return pbufs_freed;
 499              		.loc 1 266 41 is_stmt 1 view .LVU141
 266:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   return pbufs_freed;
 500              		.loc 1 266 61 is_stmt 0 view .LVU142
 501 005a 012C     		cmp	r4, #1
 502 005c D4BF     		ite	le
 503 005e 0024     		movle	r4, #0
 504              	.LVL55:
 266:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   return pbufs_freed;
 505              		.loc 1 266 61 view .LVU143
 506 0060 0124     		movgt	r4, #1
 266:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   return pbufs_freed;
 507              		.loc 1 266 41 view .LVU144
 508 0062 BE42     		cmp	r6, r7
 509 0064 07DA     		bge	.L26
 266:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   return pbufs_freed;
 510              		.loc 1 266 41 view .LVU145
 511 0066 34B1     		cbz	r4, .L26
 512              	.LVL56:
 513              	.L33:
 234:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 514              		.loc 1 234 3 is_stmt 1 view .LVU146
 238:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     oldest = NULL;
 515              		.loc 1 238 3 view .LVU147
 239:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     prev = NULL;
 516              		.loc 1 239 5 view .LVU148
 240:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     oldest_prev = NULL;
 517              		.loc 1 240 5 view .LVU149
 241:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     other_datagrams = 0;
 518              		.loc 1 241 5 view .LVU150
 242:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     r = reassdatagrams;
 519              		.loc 1 242 5 view .LVU151
 243:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     while (r != NULL) {
 520              		.loc 1 243 5 view .LVU152
 243:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     while (r != NULL) {
 521              		.loc 1 243 7 is_stmt 0 view .LVU153
 522 0068 044B     		ldr	r3, .L41
 523 006a 1B68     		ldr	r3, [r3]
 524              	.LVL57:
 244:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       if (!IP_ADDRESSES_AND_ID_MATCH(&r->iphdr, fraghdr)) {
 525              		.loc 1 244 5 is_stmt 1 view .LVU154
 242:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     r = reassdatagrams;
 526              		.loc 1 242 21 is_stmt 0 view .LVU155
 527 006c 0024     		movs	r4, #0
 241:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     other_datagrams = 0;
 528              		.loc 1 241 17 view .LVU156
 529 006e 2146     		mov	r1, r4
 240:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     oldest_prev = NULL;
 530              		.loc 1 240 10 view .LVU157
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s 			page 19


 531 0070 A446     		mov	ip, r4
 239:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     prev = NULL;
 532              		.loc 1 239 12 view .LVU158
 533 0072 2046     		mov	r0, r4
 244:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       if (!IP_ADDRESSES_AND_ID_MATCH(&r->iphdr, fraghdr)) {
 534              		.loc 1 244 11 view .LVU159
 535 0074 DAE7     		b	.L27
 536              	.LVL58:
 537              	.L26:
 268:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #endif /* IP_REASS_FREE_OLDEST */
 538              		.loc 1 268 1 view .LVU160
 539 0076 3046     		mov	r0, r6
 540 0078 F8BD     		pop	{r3, r4, r5, r6, r7, pc}
 541              	.LVL59:
 542              	.L42:
 268:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #endif /* IP_REASS_FREE_OLDEST */
 543              		.loc 1 268 1 view .LVU161
 544 007a 00BF     		.align	2
 545              	.L41:
 546 007c 00000000 		.word	reassdatagrams
 547              		.cfi_endproc
 548              	.LFE172:
 550              		.section	.rodata.ip_frag_free_pbuf_custom_ref.str1.4,"aMS",%progbits,1
 551              		.align	2
 552              	.LC7:
 553 0000 7020213D 		.ascii	"p != NULL\000"
 553      204E554C 
 553      4C00
 554              		.section	.text.ip_frag_free_pbuf_custom_ref,"ax",%progbits
 555              		.align	1
 556              		.syntax unified
 557              		.thumb
 558              		.thumb_func
 560              	ip_frag_free_pbuf_custom_ref:
 561              	.LVL60:
 562              	.LFB178:
 332:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 333:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** /**
 334:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  * Chain a new pbuf into the pbuf list that composes the datagram.  The pbuf list
 335:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  * will grow over time as  new pbufs are rx.
 336:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  * Also checks that the datagram passes basic continuity checks (if the last
 337:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  * fragment was received at least once).
 338:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  * @param ipr points to the reassembly state
 339:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  * @param new_p points to the pbuf for the current fragment
 340:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  * @param is_last is 1 if this pbuf has MF==0 (ipr->flags not updated yet)
 341:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  * @return see IP_REASS_VALIDATE_* defines
 342:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  */
 343:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** static int
 344:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** ip_reass_chain_frag_into_datagram_and_validate(struct ip_reassdata *ipr, struct pbuf *new_p, int is
 345:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** {
 346:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   struct ip_reass_helper *iprh, *iprh_tmp, *iprh_prev = NULL;
 347:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   struct pbuf *q;
 348:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   u16_t offset, len;
 349:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   u8_t hlen;
 350:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   struct ip_hdr *fraghdr;
 351:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   int valid = 1;
 352:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s 			page 20


 353:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   /* Extract length and fragment offset from current fragment */
 354:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   fraghdr = (struct ip_hdr *)new_p->payload;
 355:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   len = lwip_ntohs(IPH_LEN(fraghdr));
 356:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   hlen = IPH_HL_BYTES(fraghdr);
 357:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   if (hlen > len) {
 358:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     /* invalid datagram */
 359:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     return IP_REASS_VALIDATE_PBUF_DROPPED;
 360:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   }
 361:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   len = (u16_t)(len - hlen);
 362:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   offset = IPH_OFFSET_BYTES(fraghdr);
 363:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 364:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   /* overwrite the fragment's ip header from the pbuf with our helper struct,
 365:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****    * and setup the embedded helper structure. */
 366:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   /* make sure the struct ip_reass_helper fits into the IP header */
 367:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   LWIP_ASSERT("sizeof(struct ip_reass_helper) <= IP_HLEN",
 368:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****               sizeof(struct ip_reass_helper) <= IP_HLEN);
 369:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   iprh = (struct ip_reass_helper *)new_p->payload;
 370:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   iprh->next_pbuf = NULL;
 371:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   iprh->start = offset;
 372:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   iprh->end = (u16_t)(offset + len);
 373:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   if (iprh->end < offset) {
 374:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     /* u16_t overflow, cannot handle this */
 375:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     return IP_REASS_VALIDATE_PBUF_DROPPED;
 376:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   }
 377:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 378:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   /* Iterate through until we either get to the end of the list (append),
 379:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****    * or we find one with a larger offset (insert). */
 380:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   for (q = ipr->p; q != NULL;) {
 381:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     iprh_tmp = (struct ip_reass_helper *)q->payload;
 382:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     if (iprh->start < iprh_tmp->start) {
 383:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       /* the new pbuf should be inserted before this */
 384:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       iprh->next_pbuf = q;
 385:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       if (iprh_prev != NULL) {
 386:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         /* not the fragment with the lowest offset */
 387:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #if IP_REASS_CHECK_OVERLAP
 388:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         if ((iprh->start < iprh_prev->end) || (iprh->end > iprh_tmp->start)) {
 389:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****           /* fragment overlaps with previous or following, throw away */
 390:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****           return IP_REASS_VALIDATE_PBUF_DROPPED;
 391:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         }
 392:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #endif /* IP_REASS_CHECK_OVERLAP */
 393:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         iprh_prev->next_pbuf = new_p;
 394:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         if (iprh_prev->end != iprh->start) {
 395:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****           /* There is a fragment missing between the current
 396:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****            * and the previous fragment */
 397:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****           valid = 0;
 398:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         }
 399:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       } else {
 400:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #if IP_REASS_CHECK_OVERLAP
 401:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         if (iprh->end > iprh_tmp->start) {
 402:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****           /* fragment overlaps with following, throw away */
 403:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****           return IP_REASS_VALIDATE_PBUF_DROPPED;
 404:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         }
 405:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #endif /* IP_REASS_CHECK_OVERLAP */
 406:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         /* fragment with the lowest offset */
 407:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         ipr->p = new_p;
 408:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       }
 409:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       break;
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s 			page 21


 410:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     } else if (iprh->start == iprh_tmp->start) {
 411:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       /* received the same datagram twice: no need to keep the datagram */
 412:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       return IP_REASS_VALIDATE_PBUF_DROPPED;
 413:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #if IP_REASS_CHECK_OVERLAP
 414:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     } else if (iprh->start < iprh_tmp->end) {
 415:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       /* overlap: no need to keep the new datagram */
 416:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       return IP_REASS_VALIDATE_PBUF_DROPPED;
 417:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #endif /* IP_REASS_CHECK_OVERLAP */
 418:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     } else {
 419:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       /* Check if the fragments received so far have no holes. */
 420:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       if (iprh_prev != NULL) {
 421:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         if (iprh_prev->end != iprh_tmp->start) {
 422:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****           /* There is a fragment missing between the current
 423:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****            * and the previous fragment */
 424:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****           valid = 0;
 425:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         }
 426:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       }
 427:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     }
 428:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     q = iprh_tmp->next_pbuf;
 429:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     iprh_prev = iprh_tmp;
 430:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   }
 431:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 432:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   /* If q is NULL, then we made it to the end of the list. Determine what to do now */
 433:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   if (q == NULL) {
 434:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     if (iprh_prev != NULL) {
 435:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       /* this is (for now), the fragment with the highest offset:
 436:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****        * chain it to the last fragment */
 437:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #if IP_REASS_CHECK_OVERLAP
 438:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       LWIP_ASSERT("check fragments don't overlap", iprh_prev->end <= iprh->start);
 439:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #endif /* IP_REASS_CHECK_OVERLAP */
 440:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       iprh_prev->next_pbuf = new_p;
 441:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       if (iprh_prev->end != iprh->start) {
 442:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         valid = 0;
 443:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       }
 444:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     } else {
 445:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #if IP_REASS_CHECK_OVERLAP
 446:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       LWIP_ASSERT("no previous fragment, this must be the first fragment!",
 447:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****                   ipr->p == NULL);
 448:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #endif /* IP_REASS_CHECK_OVERLAP */
 449:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       /* this is the first fragment we ever received for this ip datagram */
 450:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       ipr->p = new_p;
 451:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     }
 452:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   }
 453:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 454:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   /* At this point, the validation part begins: */
 455:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   /* If we already received the last fragment */
 456:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   if (is_last || ((ipr->flags & IP_REASS_FLAG_LASTFRAG) != 0)) {
 457:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     /* and had no holes so far */
 458:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     if (valid) {
 459:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       /* then check if the rest of the fragments is here */
 460:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       /* Check if the queue starts with the first datagram */
 461:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       if ((ipr->p == NULL) || (((struct ip_reass_helper *)ipr->p->payload)->start != 0)) {
 462:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         valid = 0;
 463:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       } else {
 464:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         /* and check that there are no holes after this datagram */
 465:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         iprh_prev = iprh;
 466:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         q = iprh->next_pbuf;
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s 			page 22


 467:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         while (q != NULL) {
 468:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****           iprh = (struct ip_reass_helper *)q->payload;
 469:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****           if (iprh_prev->end != iprh->start) {
 470:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****             valid = 0;
 471:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****             break;
 472:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****           }
 473:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****           iprh_prev = iprh;
 474:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****           q = iprh->next_pbuf;
 475:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         }
 476:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         /* if still valid, all fragments are received
 477:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****          * (because to the MF==0 already arrived */
 478:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         if (valid) {
 479:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****           LWIP_ASSERT("sanity check", ipr->p != NULL);
 480:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****           LWIP_ASSERT("sanity check",
 481:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****                       ((struct ip_reass_helper *)ipr->p->payload) != iprh);
 482:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****           LWIP_ASSERT("validate_datagram:next_pbuf!=NULL",
 483:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****                       iprh->next_pbuf == NULL);
 484:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         }
 485:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       }
 486:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     }
 487:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     /* If valid is 0 here, there are some fragments missing in the middle
 488:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****      * (since MF == 0 has already arrived). Such datagrams simply time out if
 489:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****      * no more fragments are received... */
 490:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     return valid ? IP_REASS_VALIDATE_TELEGRAM_FINISHED : IP_REASS_VALIDATE_PBUF_QUEUED;
 491:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   }
 492:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   /* If we come here, not all fragments were received, yet! */
 493:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   return IP_REASS_VALIDATE_PBUF_QUEUED; /* not yet valid! */
 494:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** }
 495:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 496:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** /**
 497:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  * Reassembles incoming IP fragments into an IP datagram.
 498:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  *
 499:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  * @param p points to a pbuf chain of the fragment
 500:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  * @return NULL if reassembly is incomplete, ? otherwise
 501:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  */
 502:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** struct pbuf *
 503:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** ip4_reass(struct pbuf *p)
 504:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** {
 505:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   struct pbuf *r;
 506:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   struct ip_hdr *fraghdr;
 507:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   struct ip_reassdata *ipr;
 508:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   struct ip_reass_helper *iprh;
 509:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   u16_t offset, len, clen;
 510:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   u8_t hlen;
 511:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   int valid;
 512:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   int is_last;
 513:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 514:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   IPFRAG_STATS_INC(ip_frag.recv);
 515:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   MIB2_STATS_INC(mib2.ipreasmreqds);
 516:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 517:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   fraghdr = (struct ip_hdr *)p->payload;
 518:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 519:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   if (IPH_HL_BYTES(fraghdr) != IP_HLEN) {
 520:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     LWIP_DEBUGF(IP_REASS_DEBUG, ("ip4_reass: IP options currently not supported!\n"));
 521:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     IPFRAG_STATS_INC(ip_frag.err);
 522:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     goto nullreturn;
 523:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   }
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s 			page 23


 524:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 525:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   offset = IPH_OFFSET_BYTES(fraghdr);
 526:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   len = lwip_ntohs(IPH_LEN(fraghdr));
 527:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   hlen = IPH_HL_BYTES(fraghdr);
 528:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   if (hlen > len) {
 529:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     /* invalid datagram */
 530:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     goto nullreturn;
 531:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   }
 532:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   len = (u16_t)(len - hlen);
 533:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 534:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   /* Check if we are allowed to enqueue more datagrams. */
 535:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   clen = pbuf_clen(p);
 536:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   if ((ip_reass_pbufcount + clen) > IP_REASS_MAX_PBUFS) {
 537:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #if IP_REASS_FREE_OLDEST
 538:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     if (!ip_reass_remove_oldest_datagram(fraghdr, clen) ||
 539:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         ((ip_reass_pbufcount + clen) > IP_REASS_MAX_PBUFS))
 540:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #endif /* IP_REASS_FREE_OLDEST */
 541:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     {
 542:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       /* No datagram could be freed and still too many pbufs enqueued */
 543:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       LWIP_DEBUGF(IP_REASS_DEBUG, ("ip4_reass: Overflow condition: pbufct=%d, clen=%d, MAX=%d\n",
 544:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****                                    ip_reass_pbufcount, clen, IP_REASS_MAX_PBUFS));
 545:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       IPFRAG_STATS_INC(ip_frag.memerr);
 546:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       /* @todo: send ICMP time exceeded here? */
 547:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       /* drop this pbuf */
 548:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       goto nullreturn;
 549:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     }
 550:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   }
 551:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 552:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   /* Look for the datagram the fragment belongs to in the current datagram queue,
 553:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****    * remembering the previous in the queue for later dequeueing. */
 554:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   for (ipr = reassdatagrams; ipr != NULL; ipr = ipr->next) {
 555:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     /* Check if the incoming fragment matches the one currently present
 556:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****        in the reassembly buffer. If so, we proceed with copying the
 557:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****        fragment into the buffer. */
 558:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     if (IP_ADDRESSES_AND_ID_MATCH(&ipr->iphdr, fraghdr)) {
 559:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       LWIP_DEBUGF(IP_REASS_DEBUG, ("ip4_reass: matching previous fragment ID=%"X16_F"\n",
 560:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****                                    lwip_ntohs(IPH_ID(fraghdr))));
 561:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       IPFRAG_STATS_INC(ip_frag.cachehit);
 562:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       break;
 563:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     }
 564:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   }
 565:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 566:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   if (ipr == NULL) {
 567:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     /* Enqueue a new datagram into the datagram queue */
 568:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     ipr = ip_reass_enqueue_new_datagram(fraghdr, clen);
 569:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     /* Bail if unable to enqueue */
 570:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     if (ipr == NULL) {
 571:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       goto nullreturn;
 572:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     }
 573:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   } else {
 574:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     if (((lwip_ntohs(IPH_OFFSET(fraghdr)) & IP_OFFMASK) == 0) &&
 575:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         ((lwip_ntohs(IPH_OFFSET(&ipr->iphdr)) & IP_OFFMASK) != 0)) {
 576:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       /* ipr->iphdr is not the header from the first fragment, but fraghdr is
 577:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****        * -> copy fraghdr into ipr->iphdr since we want to have the header
 578:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****        * of the first fragment (for ICMP time exceeded and later, for copying
 579:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****        * all options, if supported)*/
 580:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       SMEMCPY(&ipr->iphdr, fraghdr, IP_HLEN);
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s 			page 24


 581:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     }
 582:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   }
 583:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 584:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   /* At this point, we have either created a new entry or pointing
 585:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****    * to an existing one */
 586:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 587:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   /* check for 'no more fragments', and update queue entry*/
 588:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   is_last = (IPH_OFFSET(fraghdr) & PP_NTOHS(IP_MF)) == 0;
 589:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   if (is_last) {
 590:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     u16_t datagram_len = (u16_t)(offset + len);
 591:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     if ((datagram_len < offset) || (datagram_len > (0xFFFF - IP_HLEN))) {
 592:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       /* u16_t overflow, cannot handle this */
 593:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       goto nullreturn_ipr;
 594:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     }
 595:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   }
 596:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   /* find the right place to insert this pbuf */
 597:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   /* @todo: trim pbufs if fragments are overlapping */
 598:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   valid = ip_reass_chain_frag_into_datagram_and_validate(ipr, p, is_last);
 599:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   if (valid == IP_REASS_VALIDATE_PBUF_DROPPED) {
 600:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     goto nullreturn_ipr;
 601:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   }
 602:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   /* if we come here, the pbuf has been enqueued */
 603:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 604:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   /* Track the current number of pbufs current 'in-flight', in order to limit
 605:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****      the number of fragments that may be enqueued at any one time
 606:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****      (overflow checked by testing against IP_REASS_MAX_PBUFS) */
 607:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   ip_reass_pbufcount = (u16_t)(ip_reass_pbufcount + clen);
 608:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   if (is_last) {
 609:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     u16_t datagram_len = (u16_t)(offset + len);
 610:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     ipr->datagram_len = datagram_len;
 611:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     ipr->flags |= IP_REASS_FLAG_LASTFRAG;
 612:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     LWIP_DEBUGF(IP_REASS_DEBUG,
 613:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****                 ("ip4_reass: last fragment seen, total len %"S16_F"\n",
 614:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****                  ipr->datagram_len));
 615:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   }
 616:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 617:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   if (valid == IP_REASS_VALIDATE_TELEGRAM_FINISHED) {
 618:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     struct ip_reassdata *ipr_prev;
 619:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     /* the totally last fragment (flag more fragments = 0) was received at least
 620:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****      * once AND all fragments are received */
 621:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     u16_t datagram_len = (u16_t)(ipr->datagram_len + IP_HLEN);
 622:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 623:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     /* save the second pbuf before copying the header over the pointer */
 624:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     r = ((struct ip_reass_helper *)ipr->p->payload)->next_pbuf;
 625:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 626:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     /* copy the original ip header back to the first pbuf */
 627:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     fraghdr = (struct ip_hdr *)(ipr->p->payload);
 628:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     SMEMCPY(fraghdr, &ipr->iphdr, IP_HLEN);
 629:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     IPH_LEN_SET(fraghdr, lwip_htons(datagram_len));
 630:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     IPH_OFFSET_SET(fraghdr, 0);
 631:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     IPH_CHKSUM_SET(fraghdr, 0);
 632:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     /* @todo: do we need to set/calculate the correct checksum? */
 633:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #if CHECKSUM_GEN_IP
 634:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     IF__NETIF_CHECKSUM_ENABLED(ip_current_input_netif(), NETIF_CHECKSUM_GEN_IP) {
 635:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       IPH_CHKSUM_SET(fraghdr, inet_chksum(fraghdr, IP_HLEN));
 636:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     }
 637:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #endif /* CHECKSUM_GEN_IP */
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s 			page 25


 638:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 639:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     p = ipr->p;
 640:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 641:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     /* chain together the pbufs contained within the reass_data list. */
 642:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     while (r != NULL) {
 643:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       iprh = (struct ip_reass_helper *)r->payload;
 644:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 645:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       /* hide the ip header for every succeeding fragment */
 646:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       pbuf_remove_header(r, IP_HLEN);
 647:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       pbuf_cat(p, r);
 648:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       r = iprh->next_pbuf;
 649:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     }
 650:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 651:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     /* find the previous entry in the linked list */
 652:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     if (ipr == reassdatagrams) {
 653:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       ipr_prev = NULL;
 654:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     } else {
 655:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       for (ipr_prev = reassdatagrams; ipr_prev != NULL; ipr_prev = ipr_prev->next) {
 656:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         if (ipr_prev->next == ipr) {
 657:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****           break;
 658:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         }
 659:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       }
 660:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     }
 661:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 662:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     /* release the sources allocate for the fragment queue entry */
 663:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     ip_reass_dequeue_datagram(ipr, ipr_prev);
 664:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 665:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     /* and adjust the number of pbufs currently queued for reassembly. */
 666:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     clen = pbuf_clen(p);
 667:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     LWIP_ASSERT("ip_reass_pbufcount >= clen", ip_reass_pbufcount >= clen);
 668:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     ip_reass_pbufcount = (u16_t)(ip_reass_pbufcount - clen);
 669:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 670:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     MIB2_STATS_INC(mib2.ipreasmoks);
 671:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 672:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     /* Return the pbuf chain */
 673:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     return p;
 674:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   }
 675:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   /* the datagram is not (yet?) reassembled completely */
 676:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   LWIP_DEBUGF(IP_REASS_DEBUG, ("ip_reass_pbufcount: %d out\n", ip_reass_pbufcount));
 677:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   return NULL;
 678:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 679:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** nullreturn_ipr:
 680:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   LWIP_ASSERT("ipr != NULL", ipr != NULL);
 681:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   if (ipr->p == NULL) {
 682:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     /* dropped pbuf after creating a new datagram entry: remove the entry, too */
 683:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     LWIP_ASSERT("not firstalthough just enqueued", ipr == reassdatagrams);
 684:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     ip_reass_dequeue_datagram(ipr, NULL);
 685:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   }
 686:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 687:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** nullreturn:
 688:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   LWIP_DEBUGF(IP_REASS_DEBUG, ("ip4_reass: nullreturn\n"));
 689:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   IPFRAG_STATS_INC(ip_frag.drop);
 690:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   pbuf_free(p);
 691:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   return NULL;
 692:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** }
 693:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #endif /* IP_REASSEMBLY */
 694:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s 			page 26


 695:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #if IP_FRAG
 696:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #if !LWIP_NETIF_TX_SINGLE_PBUF
 697:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** /** Allocate a new struct pbuf_custom_ref */
 698:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** static struct pbuf_custom_ref *
 699:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** ip_frag_alloc_pbuf_custom_ref(void)
 700:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** {
 701:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   return (struct pbuf_custom_ref *)memp_malloc(MEMP_FRAG_PBUF);
 702:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** }
 703:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 704:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** /** Free a struct pbuf_custom_ref */
 705:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** static void
 706:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** ip_frag_free_pbuf_custom_ref(struct pbuf_custom_ref *p)
 707:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** {
 563              		.loc 1 707 1 is_stmt 1 view -0
 564              		.cfi_startproc
 565              		@ args = 0, pretend = 0, frame = 0
 566              		@ frame_needed = 0, uses_anonymous_args = 0
 567              		.loc 1 707 1 is_stmt 0 view .LVU163
 568 0000 10B5     		push	{r4, lr}
 569              	.LCFI3:
 570              		.cfi_def_cfa_offset 8
 571              		.cfi_offset 4, -8
 572              		.cfi_offset 14, -4
 708:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   LWIP_ASSERT("p != NULL", p != NULL);
 573              		.loc 1 708 3 is_stmt 1 view .LVU164
 574              		.loc 1 708 3 view .LVU165
 575 0002 0446     		mov	r4, r0
 576 0004 20B1     		cbz	r0, .L46
 577              	.LVL61:
 578              	.L44:
 579              		.loc 1 708 3 discriminator 3 view .LVU166
 580              		.loc 1 708 3 discriminator 3 view .LVU167
 709:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   memp_free(MEMP_FRAG_PBUF, p);
 581              		.loc 1 709 3 view .LVU168
 582 0006 2146     		mov	r1, r4
 583 0008 0520     		movs	r0, #5
 584 000a FFF7FEFF 		bl	memp_free
 585              	.LVL62:
 710:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** }
 586              		.loc 1 710 1 is_stmt 0 view .LVU169
 587 000e 10BD     		pop	{r4, pc}
 588              	.LVL63:
 589              	.L46:
 708:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   LWIP_ASSERT("p != NULL", p != NULL);
 590              		.loc 1 708 3 is_stmt 1 discriminator 1 view .LVU170
 708:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   LWIP_ASSERT("p != NULL", p != NULL);
 591              		.loc 1 708 3 discriminator 1 view .LVU171
 592 0010 034B     		ldr	r3, .L47
 593 0012 4FF43172 		mov	r2, #708
 594 0016 0349     		ldr	r1, .L47+4
 595 0018 0348     		ldr	r0, .L47+8
 596              	.LVL64:
 708:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   LWIP_ASSERT("p != NULL", p != NULL);
 597              		.loc 1 708 3 is_stmt 0 discriminator 1 view .LVU172
 598 001a FFF7FEFF 		bl	printf
 599              	.LVL65:
 600 001e F2E7     		b	.L44
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s 			page 27


 601              	.L48:
 602              		.align	2
 603              	.L47:
 604 0020 00000000 		.word	.LC0
 605 0024 00000000 		.word	.LC7
 606 0028 54000000 		.word	.LC2
 607              		.cfi_endproc
 608              	.LFE178:
 610              		.section	.rodata.ipfrag_free_pbuf_custom.str1.4,"aMS",%progbits,1
 611              		.align	2
 612              	.LC8:
 613 0000 70637220 		.ascii	"pcr != NULL\000"
 613      213D204E 
 613      554C4C00 
 614              		.section	.text.ipfrag_free_pbuf_custom,"ax",%progbits
 615              		.align	1
 616              		.syntax unified
 617              		.thumb
 618              		.thumb_func
 620              	ipfrag_free_pbuf_custom:
 621              	.LVL66:
 622              	.LFB179:
 711:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 712:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** /** Free-callback function to free a 'struct pbuf_custom_ref', called by
 713:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  * pbuf_free. */
 714:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** static void
 715:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** ipfrag_free_pbuf_custom(struct pbuf *p)
 716:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** {
 623              		.loc 1 716 1 is_stmt 1 view -0
 624              		.cfi_startproc
 625              		@ args = 0, pretend = 0, frame = 0
 626              		@ frame_needed = 0, uses_anonymous_args = 0
 627              		.loc 1 716 1 is_stmt 0 view .LVU174
 628 0000 10B5     		push	{r4, lr}
 629              	.LCFI4:
 630              		.cfi_def_cfa_offset 8
 631              		.cfi_offset 4, -8
 632              		.cfi_offset 14, -4
 717:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   struct pbuf_custom_ref *pcr = (struct pbuf_custom_ref *)p;
 633              		.loc 1 717 3 is_stmt 1 view .LVU175
 634              	.LVL67:
 718:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   LWIP_ASSERT("pcr != NULL", pcr != NULL);
 635              		.loc 1 718 3 view .LVU176
 636              		.loc 1 718 3 view .LVU177
 637 0002 0446     		mov	r4, r0
 638 0004 38B1     		cbz	r0, .L53
 639              	.LVL68:
 640              	.L50:
 641              		.loc 1 718 3 discriminator 3 view .LVU178
 642              		.loc 1 718 3 discriminator 3 view .LVU179
 719:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   LWIP_ASSERT("pcr == p", (void *)pcr == (void *)p);
 643              		.loc 1 719 3 view .LVU180
 644              		.loc 1 719 3 view .LVU181
 645              		.loc 1 719 3 discriminator 3 view .LVU182
 646              		.loc 1 719 3 discriminator 3 view .LVU183
 720:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   if (pcr->original != NULL) {
 647              		.loc 1 720 3 view .LVU184
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s 			page 28


 648              		.loc 1 720 10 is_stmt 0 view .LVU185
 649 0006 6069     		ldr	r0, [r4, #20]
 650              		.loc 1 720 6 view .LVU186
 651 0008 08B1     		cbz	r0, .L51
 721:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     pbuf_free(pcr->original);
 652              		.loc 1 721 5 is_stmt 1 view .LVU187
 653 000a FFF7FEFF 		bl	pbuf_free
 654              	.LVL69:
 655              	.L51:
 722:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   }
 723:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   ip_frag_free_pbuf_custom_ref(pcr);
 656              		.loc 1 723 3 view .LVU188
 657 000e 2046     		mov	r0, r4
 658 0010 FFF7FEFF 		bl	ip_frag_free_pbuf_custom_ref
 659              	.LVL70:
 724:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** }
 660              		.loc 1 724 1 is_stmt 0 view .LVU189
 661 0014 10BD     		pop	{r4, pc}
 662              	.LVL71:
 663              	.L53:
 718:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   LWIP_ASSERT("pcr == p", (void *)pcr == (void *)p);
 664              		.loc 1 718 3 is_stmt 1 discriminator 1 view .LVU190
 718:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   LWIP_ASSERT("pcr == p", (void *)pcr == (void *)p);
 665              		.loc 1 718 3 discriminator 1 view .LVU191
 666 0016 044B     		ldr	r3, .L54
 667 0018 40F2CE22 		movw	r2, #718
 668 001c 0349     		ldr	r1, .L54+4
 669 001e 0448     		ldr	r0, .L54+8
 670              	.LVL72:
 718:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   LWIP_ASSERT("pcr == p", (void *)pcr == (void *)p);
 671              		.loc 1 718 3 is_stmt 0 discriminator 1 view .LVU192
 672 0020 FFF7FEFF 		bl	printf
 673              	.LVL73:
 674 0024 EFE7     		b	.L50
 675              	.L55:
 676 0026 00BF     		.align	2
 677              	.L54:
 678 0028 00000000 		.word	.LC0
 679 002c 00000000 		.word	.LC8
 680 0030 54000000 		.word	.LC2
 681              		.cfi_endproc
 682              	.LFE179:
 684              		.section	.rodata.ip_reass_chain_frag_into_datagram_and_validate.str1.4,"aMS",%progbits,1
 685              		.align	2
 686              	.LC9:
 687 0000 63686563 		.ascii	"check fragments don't overlap\000"
 687      6B206672 
 687      61676D65 
 687      6E747320 
 687      646F6E27 
 688 001e 0000     		.align	2
 689              	.LC10:
 690 0020 6E6F2070 		.ascii	"no previous fragment, this must be the first fragme"
 690      72657669 
 690      6F757320 
 690      66726167 
 690      6D656E74 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s 			page 29


 691 0053 6E742100 		.ascii	"nt!\000"
 692 0057 00       		.align	2
 693              	.LC11:
 694 0058 73616E69 		.ascii	"sanity check\000"
 694      74792063 
 694      6865636B 
 694      00
 695 0065 000000   		.align	2
 696              	.LC12:
 697 0068 76616C69 		.ascii	"validate_datagram:next_pbuf!=NULL\000"
 697      64617465 
 697      5F646174 
 697      61677261 
 697      6D3A6E65 
 698              		.section	.text.ip_reass_chain_frag_into_datagram_and_validate,"ax",%progbits
 699              		.align	1
 700              		.syntax unified
 701              		.thumb
 702              		.thumb_func
 704              	ip_reass_chain_frag_into_datagram_and_validate:
 705              	.LVL74:
 706              	.LFB175:
 345:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   struct ip_reass_helper *iprh, *iprh_tmp, *iprh_prev = NULL;
 707              		.loc 1 345 1 is_stmt 1 view -0
 708              		.cfi_startproc
 709              		@ args = 0, pretend = 0, frame = 0
 710              		@ frame_needed = 0, uses_anonymous_args = 0
 345:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   struct ip_reass_helper *iprh, *iprh_tmp, *iprh_prev = NULL;
 711              		.loc 1 345 1 is_stmt 0 view .LVU194
 712 0000 2DE9F843 		push	{r3, r4, r5, r6, r7, r8, r9, lr}
 713              	.LCFI5:
 714              		.cfi_def_cfa_offset 32
 715              		.cfi_offset 3, -32
 716              		.cfi_offset 4, -28
 717              		.cfi_offset 5, -24
 718              		.cfi_offset 6, -20
 719              		.cfi_offset 7, -16
 720              		.cfi_offset 8, -12
 721              		.cfi_offset 9, -8
 722              		.cfi_offset 14, -4
 723 0004 8046     		mov	r8, r0
 724 0006 0D46     		mov	r5, r1
 725 0008 9146     		mov	r9, r2
 346:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   struct pbuf *q;
 726              		.loc 1 346 3 is_stmt 1 view .LVU195
 727              	.LVL75:
 347:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   u16_t offset, len;
 728              		.loc 1 347 3 view .LVU196
 348:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   u8_t hlen;
 729              		.loc 1 348 3 view .LVU197
 349:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   struct ip_hdr *fraghdr;
 730              		.loc 1 349 3 view .LVU198
 350:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   int valid = 1;
 731              		.loc 1 350 3 view .LVU199
 351:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 732              		.loc 1 351 3 view .LVU200
 354:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   len = lwip_ntohs(IPH_LEN(fraghdr));
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s 			page 30


 733              		.loc 1 354 3 view .LVU201
 354:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   len = lwip_ntohs(IPH_LEN(fraghdr));
 734              		.loc 1 354 11 is_stmt 0 view .LVU202
 735 000a 4C68     		ldr	r4, [r1, #4]
 736              	.LVL76:
 355:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   hlen = IPH_HL_BYTES(fraghdr);
 737              		.loc 1 355 3 is_stmt 1 view .LVU203
 355:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   hlen = IPH_HL_BYTES(fraghdr);
 738              		.loc 1 355 9 is_stmt 0 view .LVU204
 739 000c 6088     		ldrh	r0, [r4, #2]	@ unaligned
 740              	.LVL77:
 355:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   hlen = IPH_HL_BYTES(fraghdr);
 741              		.loc 1 355 9 view .LVU205
 742 000e FFF7FEFF 		bl	lwip_htons
 743              	.LVL78:
 356:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   if (hlen > len) {
 744              		.loc 1 356 3 is_stmt 1 view .LVU206
 356:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   if (hlen > len) {
 745              		.loc 1 356 10 is_stmt 0 view .LVU207
 746 0012 2378     		ldrb	r3, [r4]	@ zero_extendqisi2
 747 0014 03F00F03 		and	r3, r3, #15
 748              	.LVL79:
 357:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     /* invalid datagram */
 749              		.loc 1 357 3 is_stmt 1 view .LVU208
 357:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     /* invalid datagram */
 750              		.loc 1 357 6 is_stmt 0 view .LVU209
 751 0018 B0EB830F 		cmp	r0, r3, lsl #2
 752 001c 03D2     		bcs	.L89
 359:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   }
 753              		.loc 1 359 12 view .LVU210
 754 001e 4FF0FF30 		mov	r0, #-1
 755              	.LVL80:
 756              	.L56:
 494:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 757              		.loc 1 494 1 view .LVU211
 758 0022 BDE8F883 		pop	{r3, r4, r5, r6, r7, r8, r9, pc}
 759              	.LVL81:
 760              	.L89:
 494:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 761              		.loc 1 494 1 view .LVU212
 762 0026 9A00     		lsls	r2, r3, #2
 361:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   offset = IPH_OFFSET_BYTES(fraghdr);
 763              		.loc 1 361 3 is_stmt 1 view .LVU213
 361:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   offset = IPH_OFFSET_BYTES(fraghdr);
 764              		.loc 1 361 7 is_stmt 0 view .LVU214
 765 0028 801A     		subs	r0, r0, r2
 766              	.LVL82:
 361:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   offset = IPH_OFFSET_BYTES(fraghdr);
 767              		.loc 1 361 7 view .LVU215
 768 002a 87B2     		uxth	r7, r0
 769              	.LVL83:
 362:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 770              		.loc 1 362 3 is_stmt 1 view .LVU216
 362:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 771              		.loc 1 362 12 is_stmt 0 view .LVU217
 772 002c E088     		ldrh	r0, [r4, #6]	@ unaligned
 773 002e FFF7FEFF 		bl	lwip_htons
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s 			page 31


 774              	.LVL84:
 362:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 775              		.loc 1 362 12 discriminator 1 view .LVU218
 776 0032 C0F30C0C 		ubfx	ip, r0, #0, #13
 362:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 777              		.loc 1 362 10 discriminator 1 view .LVU219
 778 0036 4FEACC0C 		lsl	ip, ip, #3
 779              	.LVL85:
 367:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****               sizeof(struct ip_reass_helper) <= IP_HLEN);
 780              		.loc 1 367 3 is_stmt 1 view .LVU220
 367:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****               sizeof(struct ip_reass_helper) <= IP_HLEN);
 781              		.loc 1 367 3 view .LVU221
 367:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****               sizeof(struct ip_reass_helper) <= IP_HLEN);
 782              		.loc 1 367 3 discriminator 3 view .LVU222
 367:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****               sizeof(struct ip_reass_helper) <= IP_HLEN);
 783              		.loc 1 367 3 discriminator 3 view .LVU223
 369:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   iprh->next_pbuf = NULL;
 784              		.loc 1 369 3 view .LVU224
 369:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   iprh->next_pbuf = NULL;
 785              		.loc 1 369 8 is_stmt 0 view .LVU225
 786 003a 6E68     		ldr	r6, [r5, #4]
 787              	.LVL86:
 370:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   iprh->start = offset;
 788              		.loc 1 370 3 is_stmt 1 view .LVU226
 370:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   iprh->start = offset;
 789              		.loc 1 370 19 is_stmt 0 view .LVU227
 790 003c 0023     		movs	r3, #0
 791 003e 3370     		strb	r3, [r6]
 792 0040 7370     		strb	r3, [r6, #1]
 793 0042 B370     		strb	r3, [r6, #2]
 794 0044 F370     		strb	r3, [r6, #3]
 371:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   iprh->end = (u16_t)(offset + len);
 795              		.loc 1 371 3 is_stmt 1 view .LVU228
 371:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   iprh->end = (u16_t)(offset + len);
 796              		.loc 1 371 15 is_stmt 0 view .LVU229
 797 0046 A6F804C0 		strh	ip, [r6, #4]	@ unaligned
 372:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   if (iprh->end < offset) {
 798              		.loc 1 372 3 is_stmt 1 view .LVU230
 372:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   if (iprh->end < offset) {
 799              		.loc 1 372 15 is_stmt 0 view .LVU231
 800 004a 07EB0C00 		add	r0, r7, ip
 801 004e 1FFA80FE 		uxth	lr, r0
 372:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   if (iprh->end < offset) {
 802              		.loc 1 372 13 view .LVU232
 803 0052 F080     		strh	r0, [r6, #6]	@ unaligned
 373:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     /* u16_t overflow, cannot handle this */
 804              		.loc 1 373 3 is_stmt 1 view .LVU233
 373:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     /* u16_t overflow, cannot handle this */
 805              		.loc 1 373 6 is_stmt 0 view .LVU234
 806 0054 E645     		cmp	lr, ip
 807 0056 C0F08580 		bcc	.L75
 380:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     iprh_tmp = (struct ip_reass_helper *)q->payload;
 808              		.loc 1 380 3 is_stmt 1 view .LVU235
 380:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     iprh_tmp = (struct ip_reass_helper *)q->payload;
 809              		.loc 1 380 10 is_stmt 0 view .LVU236
 810 005a D8F80410 		ldr	r1, [r8, #4]
 811              	.LVL87:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s 			page 32


 351:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 812              		.loc 1 351 7 view .LVU237
 813 005e 0127     		movs	r7, #1
 814              	.LVL88:
 346:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   struct pbuf *q;
 815              		.loc 1 346 45 view .LVU238
 816 0060 1C46     		mov	r4, r3
 817              	.LVL89:
 380:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     iprh_tmp = (struct ip_reass_helper *)q->payload;
 818              		.loc 1 380 3 view .LVU239
 819 0062 1FE0     		b	.L58
 820              	.LVL90:
 821              	.L91:
 384:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       if (iprh_prev != NULL) {
 822              		.loc 1 384 7 is_stmt 1 view .LVU240
 384:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       if (iprh_prev != NULL) {
 823              		.loc 1 384 23 is_stmt 0 view .LVU241
 824 0064 3160     		str	r1, [r6]	@ unaligned
 385:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         /* not the fragment with the lowest offset */
 825              		.loc 1 385 7 is_stmt 1 view .LVU242
 385:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         /* not the fragment with the lowest offset */
 826              		.loc 1 385 10 is_stmt 0 view .LVU243
 827 0066 ACB1     		cbz	r4, .L60
 388:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****           /* fragment overlaps with previous or following, throw away */
 828              		.loc 1 388 9 is_stmt 1 view .LVU244
 388:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****           /* fragment overlaps with previous or following, throw away */
 829              		.loc 1 388 37 is_stmt 0 view .LVU245
 830 0068 E288     		ldrh	r2, [r4, #6]	@ unaligned
 388:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****           /* fragment overlaps with previous or following, throw away */
 831              		.loc 1 388 12 view .LVU246
 832 006a 6245     		cmp	r2, ip
 833 006c 7DD8     		bhi	.L76
 388:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****           /* fragment overlaps with previous or following, throw away */
 834              		.loc 1 388 68 discriminator 1 view .LVU247
 835 006e 9B88     		ldrh	r3, [r3, #4]	@ unaligned
 836              	.LVL91:
 388:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****           /* fragment overlaps with previous or following, throw away */
 837              		.loc 1 388 44 discriminator 1 view .LVU248
 838 0070 9E45     		cmp	lr, r3
 839 0072 7DD8     		bhi	.L77
 393:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         if (iprh_prev->end != iprh->start) {
 840              		.loc 1 393 9 is_stmt 1 view .LVU249
 393:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         if (iprh_prev->end != iprh->start) {
 841              		.loc 1 393 30 is_stmt 0 view .LVU250
 842 0074 2560     		str	r5, [r4]	@ unaligned
 394:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****           /* There is a fragment missing between the current
 843              		.loc 1 394 9 is_stmt 1 view .LVU251
 394:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****           /* There is a fragment missing between the current
 844              		.loc 1 394 35 is_stmt 0 view .LVU252
 845 0076 B388     		ldrh	r3, [r6, #4]	@ unaligned
 394:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****           /* There is a fragment missing between the current
 846              		.loc 1 394 12 view .LVU253
 847 0078 9A42     		cmp	r2, r3
 848 007a 00D0     		beq	.L61
 397:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         }
 849              		.loc 1 397 17 view .LVU254
 850 007c 0027     		movs	r7, #0
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s 			page 33


 851              	.LVL92:
 852              	.L61:
 433:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     if (iprh_prev != NULL) {
 853              		.loc 1 433 3 is_stmt 1 view .LVU255
 433:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     if (iprh_prev != NULL) {
 854              		.loc 1 433 6 is_stmt 0 view .LVU256
 855 007e 19B3     		cbz	r1, .L90
 856              	.LVL93:
 857              	.L64:
 456:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     /* and had no holes so far */
 858              		.loc 1 456 3 is_stmt 1 view .LVU257
 456:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     /* and had no holes so far */
 859              		.loc 1 456 6 is_stmt 0 view .LVU258
 860 0080 B9F1000F 		cmp	r9, #0
 861 0084 41D1     		bne	.L68
 456:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     /* and had no holes so far */
 862              		.loc 1 456 23 discriminator 1 view .LVU259
 863 0086 98F81E30 		ldrb	r3, [r8, #30]	@ zero_extendqisi2
 456:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     /* and had no holes so far */
 864              		.loc 1 456 15 discriminator 1 view .LVU260
 865 008a 13F0010F 		tst	r3, #1
 866 008e 3CD1     		bne	.L68
 493:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** }
 867              		.loc 1 493 10 view .LVU261
 868 0090 4846     		mov	r0, r9
 869 0092 C6E7     		b	.L56
 870              	.LVL94:
 871              	.L60:
 401:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****           /* fragment overlaps with following, throw away */
 872              		.loc 1 401 9 is_stmt 1 view .LVU262
 401:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****           /* fragment overlaps with following, throw away */
 873              		.loc 1 401 33 is_stmt 0 view .LVU263
 874 0094 9B88     		ldrh	r3, [r3, #4]	@ unaligned
 875              	.LVL95:
 401:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****           /* fragment overlaps with following, throw away */
 876              		.loc 1 401 12 view .LVU264
 877 0096 9E45     		cmp	lr, r3
 878 0098 6DD8     		bhi	.L79
 407:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       }
 879              		.loc 1 407 9 is_stmt 1 view .LVU265
 407:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       }
 880              		.loc 1 407 16 is_stmt 0 view .LVU266
 881 009a C8F80450 		str	r5, [r8, #4]
 882 009e EEE7     		b	.L61
 883              	.LVL96:
 884              	.L62:
 428:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     iprh_prev = iprh_tmp;
 885              		.loc 1 428 5 is_stmt 1 view .LVU267
 428:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     iprh_prev = iprh_tmp;
 886              		.loc 1 428 7 is_stmt 0 view .LVU268
 887 00a0 1968     		ldr	r1, [r3]	@ unaligned
 888              	.LVL97:
 429:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   }
 889              		.loc 1 429 5 is_stmt 1 view .LVU269
 429:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   }
 890              		.loc 1 429 15 is_stmt 0 view .LVU270
 891 00a2 1C46     		mov	r4, r3
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s 			page 34


 892              	.LVL98:
 893              	.L58:
 380:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     iprh_tmp = (struct ip_reass_helper *)q->payload;
 894              		.loc 1 380 22 is_stmt 1 discriminator 1 view .LVU271
 895 00a4 0029     		cmp	r1, #0
 896 00a6 EAD0     		beq	.L61
 381:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     if (iprh->start < iprh_tmp->start) {
 897              		.loc 1 381 5 view .LVU272
 381:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     if (iprh->start < iprh_tmp->start) {
 898              		.loc 1 381 14 is_stmt 0 view .LVU273
 899 00a8 4B68     		ldr	r3, [r1, #4]
 900              	.LVL99:
 382:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       /* the new pbuf should be inserted before this */
 901              		.loc 1 382 5 is_stmt 1 view .LVU274
 382:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       /* the new pbuf should be inserted before this */
 902              		.loc 1 382 31 is_stmt 0 view .LVU275
 903 00aa 9A88     		ldrh	r2, [r3, #4]	@ unaligned
 382:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       /* the new pbuf should be inserted before this */
 904              		.loc 1 382 8 view .LVU276
 905 00ac 6245     		cmp	r2, ip
 906 00ae D9D8     		bhi	.L91
 410:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       /* received the same datagram twice: no need to keep the datagram */
 907              		.loc 1 410 12 is_stmt 1 view .LVU277
 410:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       /* received the same datagram twice: no need to keep the datagram */
 908              		.loc 1 410 15 is_stmt 0 view .LVU278
 909 00b0 6245     		cmp	r2, ip
 910 00b2 63D0     		beq	.L80
 414:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       /* overlap: no need to keep the new datagram */
 911              		.loc 1 414 12 is_stmt 1 view .LVU279
 414:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       /* overlap: no need to keep the new datagram */
 912              		.loc 1 414 38 is_stmt 0 view .LVU280
 913 00b4 D988     		ldrh	r1, [r3, #6]	@ unaligned
 914              	.LVL100:
 414:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       /* overlap: no need to keep the new datagram */
 915              		.loc 1 414 15 view .LVU281
 916 00b6 6145     		cmp	r1, ip
 917 00b8 63D8     		bhi	.L81
 420:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         if (iprh_prev->end != iprh_tmp->start) {
 918              		.loc 1 420 7 is_stmt 1 view .LVU282
 420:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         if (iprh_prev->end != iprh_tmp->start) {
 919              		.loc 1 420 10 is_stmt 0 view .LVU283
 920 00ba 002C     		cmp	r4, #0
 921 00bc F0D0     		beq	.L62
 421:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****           /* There is a fragment missing between the current
 922              		.loc 1 421 9 is_stmt 1 view .LVU284
 421:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****           /* There is a fragment missing between the current
 923              		.loc 1 421 22 is_stmt 0 view .LVU285
 924 00be E188     		ldrh	r1, [r4, #6]	@ unaligned
 421:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****           /* There is a fragment missing between the current
 925              		.loc 1 421 12 view .LVU286
 926 00c0 8A42     		cmp	r2, r1
 927 00c2 EDD0     		beq	.L62
 424:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         }
 928              		.loc 1 424 17 view .LVU287
 929 00c4 0027     		movs	r7, #0
 930              	.LVL101:
 424:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         }
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s 			page 35


 931              		.loc 1 424 17 view .LVU288
 932 00c6 EBE7     		b	.L62
 933              	.LVL102:
 934              	.L90:
 434:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       /* this is (for now), the fragment with the highest offset:
 935              		.loc 1 434 5 is_stmt 1 view .LVU289
 434:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       /* this is (for now), the fragment with the highest offset:
 936              		.loc 1 434 8 is_stmt 0 view .LVU290
 937 00c8 94B1     		cbz	r4, .L65
 438:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #endif /* IP_REASS_CHECK_OVERLAP */
 938              		.loc 1 438 7 is_stmt 1 view .LVU291
 438:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #endif /* IP_REASS_CHECK_OVERLAP */
 939              		.loc 1 438 7 view .LVU292
 940 00ca E288     		ldrh	r2, [r4, #6]	@ unaligned
 941 00cc B388     		ldrh	r3, [r6, #4]	@ unaligned
 942 00ce 9A42     		cmp	r2, r3
 943 00d0 06D8     		bhi	.L92
 944              	.LVL103:
 945              	.L66:
 438:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #endif /* IP_REASS_CHECK_OVERLAP */
 946              		.loc 1 438 7 discriminator 3 view .LVU293
 438:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #endif /* IP_REASS_CHECK_OVERLAP */
 947              		.loc 1 438 7 discriminator 3 view .LVU294
 440:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       if (iprh_prev->end != iprh->start) {
 948              		.loc 1 440 7 view .LVU295
 440:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       if (iprh_prev->end != iprh->start) {
 949              		.loc 1 440 28 is_stmt 0 view .LVU296
 950 00d2 2560     		str	r5, [r4]	@ unaligned
 441:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         valid = 0;
 951              		.loc 1 441 7 is_stmt 1 view .LVU297
 441:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         valid = 0;
 952              		.loc 1 441 20 is_stmt 0 view .LVU298
 953 00d4 E288     		ldrh	r2, [r4, #6]	@ unaligned
 441:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         valid = 0;
 954              		.loc 1 441 33 view .LVU299
 955 00d6 B388     		ldrh	r3, [r6, #4]	@ unaligned
 441:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         valid = 0;
 956              		.loc 1 441 10 view .LVU300
 957 00d8 9A42     		cmp	r2, r3
 958 00da D1D0     		beq	.L64
 442:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       }
 959              		.loc 1 442 15 view .LVU301
 960 00dc 0027     		movs	r7, #0
 961              	.LVL104:
 442:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       }
 962              		.loc 1 442 15 view .LVU302
 963 00de CFE7     		b	.L64
 964              	.LVL105:
 965              	.L92:
 438:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #endif /* IP_REASS_CHECK_OVERLAP */
 966              		.loc 1 438 7 is_stmt 1 discriminator 1 view .LVU303
 438:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #endif /* IP_REASS_CHECK_OVERLAP */
 967              		.loc 1 438 7 discriminator 1 view .LVU304
 968 00e0 294B     		ldr	r3, .L94
 969 00e2 4FF4DB72 		mov	r2, #438
 970 00e6 2949     		ldr	r1, .L94+4
 971              	.LVL106:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s 			page 36


 438:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #endif /* IP_REASS_CHECK_OVERLAP */
 972              		.loc 1 438 7 is_stmt 0 discriminator 1 view .LVU305
 973 00e8 2948     		ldr	r0, .L94+8
 974 00ea FFF7FEFF 		bl	printf
 975              	.LVL107:
 438:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #endif /* IP_REASS_CHECK_OVERLAP */
 976              		.loc 1 438 7 discriminator 1 view .LVU306
 977 00ee F0E7     		b	.L66
 978              	.LVL108:
 979              	.L65:
 446:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****                   ipr->p == NULL);
 980              		.loc 1 446 7 is_stmt 1 view .LVU307
 446:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****                   ipr->p == NULL);
 981              		.loc 1 446 7 view .LVU308
 982 00f0 D8F80430 		ldr	r3, [r8, #4]
 983 00f4 33B1     		cbz	r3, .L67
 446:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****                   ipr->p == NULL);
 984              		.loc 1 446 7 discriminator 1 view .LVU309
 446:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****                   ipr->p == NULL);
 985              		.loc 1 446 7 discriminator 1 view .LVU310
 986 00f6 244B     		ldr	r3, .L94
 987 00f8 4FF4DF72 		mov	r2, #446
 988 00fc 2549     		ldr	r1, .L94+12
 989              	.LVL109:
 446:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****                   ipr->p == NULL);
 990              		.loc 1 446 7 is_stmt 0 discriminator 1 view .LVU311
 991 00fe 2448     		ldr	r0, .L94+8
 992 0100 FFF7FEFF 		bl	printf
 993              	.LVL110:
 994              	.L67:
 446:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****                   ipr->p == NULL);
 995              		.loc 1 446 7 is_stmt 1 discriminator 3 view .LVU312
 446:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****                   ipr->p == NULL);
 996              		.loc 1 446 7 discriminator 3 view .LVU313
 450:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     }
 997              		.loc 1 450 7 view .LVU314
 450:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     }
 998              		.loc 1 450 14 is_stmt 0 view .LVU315
 999 0104 C8F80450 		str	r5, [r8, #4]
 1000 0108 BAE7     		b	.L64
 1001              	.L68:
 458:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       /* then check if the rest of the fragments is here */
 1002              		.loc 1 458 5 is_stmt 1 view .LVU316
 458:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       /* then check if the rest of the fragments is here */
 1003              		.loc 1 458 8 is_stmt 0 view .LVU317
 1004 010a 4FB3     		cbz	r7, .L69
 461:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         valid = 0;
 1005              		.loc 1 461 7 is_stmt 1 view .LVU318
 461:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         valid = 0;
 1006              		.loc 1 461 15 is_stmt 0 view .LVU319
 1007 010c D8F80430 		ldr	r3, [r8, #4]
 461:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         valid = 0;
 1008              		.loc 1 461 10 view .LVU320
 1009 0110 1BB3     		cbz	r3, .L85
 461:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         valid = 0;
 1010              		.loc 1 461 65 discriminator 1 view .LVU321
 1011 0112 5868     		ldr	r0, [r3, #4]
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s 			page 37


 461:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         valid = 0;
 1012              		.loc 1 461 75 discriminator 1 view .LVU322
 1013 0114 8388     		ldrh	r3, [r0, #4]	@ unaligned
 461:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         valid = 0;
 1014              		.loc 1 461 28 discriminator 1 view .LVU323
 1015 0116 13BB     		cbnz	r3, .L86
 465:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         q = iprh->next_pbuf;
 1016              		.loc 1 465 9 is_stmt 1 view .LVU324
 1017              	.LVL111:
 466:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         while (q != NULL) {
 1018              		.loc 1 466 9 view .LVU325
 466:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         while (q != NULL) {
 1019              		.loc 1 466 11 is_stmt 0 view .LVU326
 1020 0118 3268     		ldr	r2, [r6]	@ unaligned
 1021              	.LVL112:
 467:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****           iprh = (struct ip_reass_helper *)q->payload;
 1022              		.loc 1 467 9 is_stmt 1 view .LVU327
 1023              	.L70:
 467:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****           iprh = (struct ip_reass_helper *)q->payload;
 1024              		.loc 1 467 18 view .LVU328
 1025 011a 4AB1     		cbz	r2, .L71
 468:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****           if (iprh_prev->end != iprh->start) {
 1026              		.loc 1 468 11 view .LVU329
 468:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****           if (iprh_prev->end != iprh->start) {
 1027              		.loc 1 468 16 is_stmt 0 view .LVU330
 1028 011c 5368     		ldr	r3, [r2, #4]
 1029              	.LVL113:
 469:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****             valid = 0;
 1030              		.loc 1 469 11 is_stmt 1 view .LVU331
 469:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****             valid = 0;
 1031              		.loc 1 469 24 is_stmt 0 view .LVU332
 1032 011e F188     		ldrh	r1, [r6, #6]	@ unaligned
 469:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****             valid = 0;
 1033              		.loc 1 469 37 view .LVU333
 1034 0120 9A88     		ldrh	r2, [r3, #4]	@ unaligned
 1035              	.LVL114:
 469:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****             valid = 0;
 1036              		.loc 1 469 14 view .LVU334
 1037 0122 9142     		cmp	r1, r2
 1038 0124 02D1     		bne	.L87
 473:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****           q = iprh->next_pbuf;
 1039              		.loc 1 473 11 is_stmt 1 view .LVU335
 1040              	.LVL115:
 474:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         }
 1041              		.loc 1 474 11 view .LVU336
 474:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         }
 1042              		.loc 1 474 13 is_stmt 0 view .LVU337
 1043 0126 1A68     		ldr	r2, [r3]	@ unaligned
 1044              	.LVL116:
 468:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****           if (iprh_prev->end != iprh->start) {
 1045              		.loc 1 468 16 view .LVU338
 1046 0128 1E46     		mov	r6, r3
 1047 012a F6E7     		b	.L70
 1048              	.LVL117:
 1049              	.L87:
 468:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****           if (iprh_prev->end != iprh->start) {
 1050              		.loc 1 468 16 view .LVU339
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s 			page 38


 1051 012c 1E46     		mov	r6, r3
 1052              	.LVL118:
 470:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****             break;
 1053              		.loc 1 470 19 view .LVU340
 1054 012e 0027     		movs	r7, #0
 1055              	.LVL119:
 1056              	.L71:
 478:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****           LWIP_ASSERT("sanity check", ipr->p != NULL);
 1057              		.loc 1 478 9 is_stmt 1 view .LVU341
 478:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****           LWIP_ASSERT("sanity check", ipr->p != NULL);
 1058              		.loc 1 478 12 is_stmt 0 view .LVU342
 1059 0130 B7B1     		cbz	r7, .L69
 479:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****           LWIP_ASSERT("sanity check",
 1060              		.loc 1 479 11 is_stmt 1 view .LVU343
 479:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****           LWIP_ASSERT("sanity check",
 1061              		.loc 1 479 11 view .LVU344
 479:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****           LWIP_ASSERT("sanity check",
 1062              		.loc 1 479 11 discriminator 3 view .LVU345
 479:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****           LWIP_ASSERT("sanity check",
 1063              		.loc 1 479 11 discriminator 3 view .LVU346
 480:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****                       ((struct ip_reass_helper *)ipr->p->payload) != iprh);
 1064              		.loc 1 480 11 view .LVU347
 480:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****                       ((struct ip_reass_helper *)ipr->p->payload) != iprh);
 1065              		.loc 1 480 11 view .LVU348
 1066 0132 B042     		cmp	r0, r6
 1067 0134 09D0     		beq	.L93
 1068              	.L73:
 480:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****                       ((struct ip_reass_helper *)ipr->p->payload) != iprh);
 1069              		.loc 1 480 11 discriminator 3 view .LVU349
 480:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****                       ((struct ip_reass_helper *)ipr->p->payload) != iprh);
 1070              		.loc 1 480 11 discriminator 3 view .LVU350
 482:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****                       iprh->next_pbuf == NULL);
 1071              		.loc 1 482 11 view .LVU351
 482:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****                       iprh->next_pbuf == NULL);
 1072              		.loc 1 482 11 view .LVU352
 1073 0136 3368     		ldr	r3, [r6]	@ unaligned
 1074 0138 93B1     		cbz	r3, .L69
 482:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****                       iprh->next_pbuf == NULL);
 1075              		.loc 1 482 11 discriminator 1 view .LVU353
 482:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****                       iprh->next_pbuf == NULL);
 1076              		.loc 1 482 11 discriminator 1 view .LVU354
 1077 013a 134B     		ldr	r3, .L94
 1078 013c 4FF4F172 		mov	r2, #482
 1079 0140 1549     		ldr	r1, .L94+16
 1080 0142 1348     		ldr	r0, .L94+8
 1081 0144 FFF7FEFF 		bl	printf
 1082              	.LVL120:
 1083 0148 0AE0     		b	.L69
 1084              	.L93:
 480:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****                       ((struct ip_reass_helper *)ipr->p->payload) != iprh);
 1085              		.loc 1 480 11 discriminator 1 view .LVU355
 480:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****                       ((struct ip_reass_helper *)ipr->p->payload) != iprh);
 1086              		.loc 1 480 11 discriminator 1 view .LVU356
 1087 014a 0F4B     		ldr	r3, .L94
 1088 014c 4FF4F072 		mov	r2, #480
 1089 0150 1249     		ldr	r1, .L94+20
 1090 0152 0F48     		ldr	r0, .L94+8
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s 			page 39


 1091 0154 FFF7FEFF 		bl	printf
 1092              	.LVL121:
 1093 0158 EDE7     		b	.L73
 1094              	.LVL122:
 1095              	.L85:
 462:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       } else {
 1096              		.loc 1 462 15 is_stmt 0 view .LVU357
 1097 015a 0027     		movs	r7, #0
 1098              	.LVL123:
 462:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       } else {
 1099              		.loc 1 462 15 view .LVU358
 1100 015c 00E0     		b	.L69
 1101              	.LVL124:
 1102              	.L86:
 462:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       } else {
 1103              		.loc 1 462 15 view .LVU359
 1104 015e 0027     		movs	r7, #0
 1105              	.LVL125:
 1106              	.L69:
 482:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****                       iprh->next_pbuf == NULL);
 1107              		.loc 1 482 11 is_stmt 1 discriminator 3 view .LVU360
 482:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****                       iprh->next_pbuf == NULL);
 1108              		.loc 1 482 11 discriminator 3 view .LVU361
 490:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   }
 1109              		.loc 1 490 5 view .LVU362
 490:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   }
 1110              		.loc 1 490 56 is_stmt 0 view .LVU363
 1111 0160 3846     		mov	r0, r7
 1112 0162 5EE7     		b	.L56
 1113              	.LVL126:
 1114              	.L75:
 375:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   }
 1115              		.loc 1 375 12 view .LVU364
 1116 0164 4FF0FF30 		mov	r0, #-1
 1117 0168 5BE7     		b	.L56
 1118              	.LVL127:
 1119              	.L76:
 390:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         }
 1120              		.loc 1 390 18 view .LVU365
 1121 016a 4FF0FF30 		mov	r0, #-1
 1122 016e 58E7     		b	.L56
 1123              	.LVL128:
 1124              	.L77:
 390:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         }
 1125              		.loc 1 390 18 view .LVU366
 1126 0170 4FF0FF30 		mov	r0, #-1
 1127 0174 55E7     		b	.L56
 1128              	.L79:
 403:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         }
 1129              		.loc 1 403 18 view .LVU367
 1130 0176 4FF0FF30 		mov	r0, #-1
 1131 017a 52E7     		b	.L56
 1132              	.LVL129:
 1133              	.L80:
 412:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #if IP_REASS_CHECK_OVERLAP
 1134              		.loc 1 412 14 view .LVU368
 1135 017c 4FF0FF30 		mov	r0, #-1
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s 			page 40


 1136 0180 4FE7     		b	.L56
 1137              	.LVL130:
 1138              	.L81:
 416:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #endif /* IP_REASS_CHECK_OVERLAP */
 1139              		.loc 1 416 14 view .LVU369
 1140 0182 4FF0FF30 		mov	r0, #-1
 1141 0186 4CE7     		b	.L56
 1142              	.L95:
 1143              		.align	2
 1144              	.L94:
 1145 0188 00000000 		.word	.LC0
 1146 018c 00000000 		.word	.LC9
 1147 0190 54000000 		.word	.LC2
 1148 0194 20000000 		.word	.LC10
 1149 0198 68000000 		.word	.LC12
 1150 019c 58000000 		.word	.LC11
 1151              		.cfi_endproc
 1152              	.LFE175:
 1154              		.section	.text.ip_frag_alloc_pbuf_custom_ref,"ax",%progbits
 1155              		.align	1
 1156              		.syntax unified
 1157              		.thumb
 1158              		.thumb_func
 1160              	ip_frag_alloc_pbuf_custom_ref:
 1161              	.LFB177:
 700:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   return (struct pbuf_custom_ref *)memp_malloc(MEMP_FRAG_PBUF);
 1162              		.loc 1 700 1 is_stmt 1 view -0
 1163              		.cfi_startproc
 1164              		@ args = 0, pretend = 0, frame = 0
 1165              		@ frame_needed = 0, uses_anonymous_args = 0
 1166 0000 08B5     		push	{r3, lr}
 1167              	.LCFI6:
 1168              		.cfi_def_cfa_offset 8
 1169              		.cfi_offset 3, -8
 1170              		.cfi_offset 14, -4
 701:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** }
 1171              		.loc 1 701 3 view .LVU371
 701:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** }
 1172              		.loc 1 701 36 is_stmt 0 view .LVU372
 1173 0002 0520     		movs	r0, #5
 1174 0004 FFF7FEFF 		bl	memp_malloc
 1175              	.LVL131:
 702:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 1176              		.loc 1 702 1 view .LVU373
 1177 0008 08BD     		pop	{r3, pc}
 1178              		.cfi_endproc
 1179              	.LFE177:
 1181              		.section	.text.ip_reass_enqueue_new_datagram,"ax",%progbits
 1182              		.align	1
 1183              		.syntax unified
 1184              		.thumb
 1185              		.thumb_func
 1187              	ip_reass_enqueue_new_datagram:
 1188              	.LVL132:
 1189              	.LFB173:
 279:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   struct ip_reassdata *ipr;
 1190              		.loc 1 279 1 is_stmt 1 view -0
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s 			page 41


 1191              		.cfi_startproc
 1192              		@ args = 0, pretend = 0, frame = 0
 1193              		@ frame_needed = 0, uses_anonymous_args = 0
 279:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   struct ip_reassdata *ipr;
 1194              		.loc 1 279 1 is_stmt 0 view .LVU375
 1195 0000 70B5     		push	{r4, r5, r6, lr}
 1196              	.LCFI7:
 1197              		.cfi_def_cfa_offset 16
 1198              		.cfi_offset 4, -16
 1199              		.cfi_offset 5, -12
 1200              		.cfi_offset 6, -8
 1201              		.cfi_offset 14, -4
 1202 0002 0546     		mov	r5, r0
 1203 0004 0E46     		mov	r6, r1
 280:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #if ! IP_REASS_FREE_OLDEST
 1204              		.loc 1 280 3 is_stmt 1 view .LVU376
 286:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   if (ipr == NULL) {
 1205              		.loc 1 286 3 view .LVU377
 286:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   if (ipr == NULL) {
 1206              		.loc 1 286 32 is_stmt 0 view .LVU378
 1207 0006 0420     		movs	r0, #4
 1208              	.LVL133:
 286:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   if (ipr == NULL) {
 1209              		.loc 1 286 32 view .LVU379
 1210 0008 FFF7FEFF 		bl	memp_malloc
 1211              	.LVL134:
 287:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #if IP_REASS_FREE_OLDEST
 1212              		.loc 1 287 3 is_stmt 1 view .LVU380
 287:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #if IP_REASS_FREE_OLDEST
 1213              		.loc 1 287 6 is_stmt 0 view .LVU381
 1214 000c 0446     		mov	r4, r0
 1215 000e B0B1     		cbz	r0, .L103
 1216              	.LVL135:
 1217              	.L99:
 300:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   ipr->timer = IP_REASS_MAXAGE;
 1218              		.loc 1 300 3 is_stmt 1 view .LVU382
 1219 0010 2022     		movs	r2, #32
 1220 0012 0021     		movs	r1, #0
 1221 0014 2046     		mov	r0, r4
 1222 0016 FFF7FEFF 		bl	memset
 1223              	.LVL136:
 301:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 1224              		.loc 1 301 3 view .LVU383
 301:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 1225              		.loc 1 301 14 is_stmt 0 view .LVU384
 1226 001a 0F23     		movs	r3, #15
 1227 001c E377     		strb	r3, [r4, #31]
 304:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   reassdatagrams = ipr;
 1228              		.loc 1 304 3 is_stmt 1 view .LVU385
 304:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   reassdatagrams = ipr;
 1229              		.loc 1 304 13 is_stmt 0 view .LVU386
 1230 001e 0F4B     		ldr	r3, .L105
 1231 0020 1A68     		ldr	r2, [r3]
 1232 0022 2260     		str	r2, [r4]
 305:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   /* copy the ip header for later tests and input */
 1233              		.loc 1 305 3 is_stmt 1 view .LVU387
 305:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   /* copy the ip header for later tests and input */
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s 			page 42


 1234              		.loc 1 305 18 is_stmt 0 view .LVU388
 1235 0024 1C60     		str	r4, [r3]
 308:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   return ipr;
 1236              		.loc 1 308 3 is_stmt 1 view .LVU389
 1237 0026 2B68     		ldr	r3, [r5]	@ unaligned
 1238 0028 6868     		ldr	r0, [r5, #4]	@ unaligned
 1239 002a A968     		ldr	r1, [r5, #8]	@ unaligned
 1240 002c EA68     		ldr	r2, [r5, #12]	@ unaligned
 1241 002e A360     		str	r3, [r4, #8]	@ unaligned
 1242 0030 E060     		str	r0, [r4, #12]	@ unaligned
 1243 0032 2161     		str	r1, [r4, #16]	@ unaligned
 1244 0034 6261     		str	r2, [r4, #20]	@ unaligned
 1245 0036 2B69     		ldr	r3, [r5, #16]	@ unaligned
 1246 0038 A361     		str	r3, [r4, #24]	@ unaligned
 309:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** }
 1247              		.loc 1 309 3 view .LVU390
 1248              	.L98:
 310:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 1249              		.loc 1 310 1 is_stmt 0 view .LVU391
 1250 003a 2046     		mov	r0, r4
 1251 003c 70BD     		pop	{r4, r5, r6, pc}
 1252              	.LVL137:
 1253              	.L103:
 289:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       ipr = (struct ip_reassdata *)memp_malloc(MEMP_REASSDATA);
 1254              		.loc 1 289 5 is_stmt 1 view .LVU392
 289:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       ipr = (struct ip_reassdata *)memp_malloc(MEMP_REASSDATA);
 1255              		.loc 1 289 9 is_stmt 0 view .LVU393
 1256 003e 3146     		mov	r1, r6
 1257 0040 2846     		mov	r0, r5
 1258              	.LVL138:
 289:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       ipr = (struct ip_reassdata *)memp_malloc(MEMP_REASSDATA);
 1259              		.loc 1 289 9 view .LVU394
 1260 0042 FFF7FEFF 		bl	ip_reass_remove_oldest_datagram
 1261              	.LVL139:
 289:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       ipr = (struct ip_reassdata *)memp_malloc(MEMP_REASSDATA);
 1262              		.loc 1 289 8 discriminator 1 view .LVU395
 1263 0046 B042     		cmp	r0, r6
 1264 0048 02DA     		bge	.L104
 1265              	.L100:
 292:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #endif /* IP_REASS_FREE_OLDEST */
 1266              		.loc 1 292 5 is_stmt 1 view .LVU396
 292:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #endif /* IP_REASS_FREE_OLDEST */
 1267              		.loc 1 292 8 is_stmt 0 view .LVU397
 1268 004a 002C     		cmp	r4, #0
 1269 004c E0D1     		bne	.L99
 292:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #endif /* IP_REASS_FREE_OLDEST */
 1270              		.loc 1 292 8 view .LVU398
 1271 004e F4E7     		b	.L98
 1272              	.L104:
 290:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     }
 1273              		.loc 1 290 7 is_stmt 1 view .LVU399
 290:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     }
 1274              		.loc 1 290 36 is_stmt 0 view .LVU400
 1275 0050 0420     		movs	r0, #4
 1276 0052 FFF7FEFF 		bl	memp_malloc
 1277              	.LVL140:
 1278 0056 0446     		mov	r4, r0
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s 			page 43


 1279              	.LVL141:
 290:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     }
 1280              		.loc 1 290 36 view .LVU401
 1281 0058 F7E7     		b	.L100
 1282              	.L106:
 1283 005a 00BF     		.align	2
 1284              	.L105:
 1285 005c 00000000 		.word	reassdatagrams
 1286              		.cfi_endproc
 1287              	.LFE173:
 1289              		.section	.text.ip_reass_tmr,"ax",%progbits
 1290              		.align	1
 1291              		.global	ip_reass_tmr
 1292              		.syntax unified
 1293              		.thumb
 1294              		.thumb_func
 1296              	ip_reass_tmr:
 1297              	.LFB170:
 129:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   struct ip_reassdata *r, *prev = NULL;
 1298              		.loc 1 129 1 is_stmt 1 view -0
 1299              		.cfi_startproc
 1300              		@ args = 0, pretend = 0, frame = 0
 1301              		@ frame_needed = 0, uses_anonymous_args = 0
 1302 0000 38B5     		push	{r3, r4, r5, lr}
 1303              	.LCFI8:
 1304              		.cfi_def_cfa_offset 16
 1305              		.cfi_offset 3, -16
 1306              		.cfi_offset 4, -12
 1307              		.cfi_offset 5, -8
 1308              		.cfi_offset 14, -4
 130:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 1309              		.loc 1 130 3 view .LVU403
 1310              	.LVL142:
 132:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   while (r != NULL) {
 1311              		.loc 1 132 3 view .LVU404
 132:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   while (r != NULL) {
 1312              		.loc 1 132 5 is_stmt 0 view .LVU405
 1313 0002 094B     		ldr	r3, .L115
 1314 0004 1868     		ldr	r0, [r3]
 1315              	.LVL143:
 133:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     /* Decrement the timer. Once it reaches 0,
 1316              		.loc 1 133 3 is_stmt 1 view .LVU406
 130:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 1317              		.loc 1 130 28 is_stmt 0 view .LVU407
 1318 0006 0024     		movs	r4, #0
 133:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     /* Decrement the timer. Once it reaches 0,
 1319              		.loc 1 133 9 view .LVU408
 1320 0008 03E0     		b	.L108
 1321              	.LVL144:
 1322              	.L114:
 137:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       LWIP_DEBUGF(IP_REASS_DEBUG, ("ip_reass_tmr: timer dec %"U16_F"\n", (u16_t)r->timer));
 1323              		.loc 1 137 7 is_stmt 1 view .LVU409
 137:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       LWIP_DEBUGF(IP_REASS_DEBUG, ("ip_reass_tmr: timer dec %"U16_F"\n", (u16_t)r->timer));
 1324              		.loc 1 137 15 is_stmt 0 view .LVU410
 1325 000a 013B     		subs	r3, r3, #1
 1326 000c C377     		strb	r3, [r0, #31]
 138:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       prev = r;
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s 			page 44


 1327              		.loc 1 138 91 is_stmt 1 view .LVU411
 139:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       r = r->next;
 1328              		.loc 1 139 7 view .LVU412
 1329              	.LVL145:
 140:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     } else {
 1330              		.loc 1 140 7 view .LVU413
 139:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       r = r->next;
 1331              		.loc 1 139 12 is_stmt 0 view .LVU414
 1332 000e 0446     		mov	r4, r0
 140:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     } else {
 1333              		.loc 1 140 9 view .LVU415
 1334 0010 0068     		ldr	r0, [r0]
 1335              	.LVL146:
 1336              	.L108:
 133:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     /* Decrement the timer. Once it reaches 0,
 1337              		.loc 1 133 12 is_stmt 1 view .LVU416
 1338 0012 40B1     		cbz	r0, .L113
 136:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       r->timer--;
 1339              		.loc 1 136 5 view .LVU417
 136:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       r->timer--;
 1340              		.loc 1 136 10 is_stmt 0 view .LVU418
 1341 0014 C37F     		ldrb	r3, [r0, #31]	@ zero_extendqisi2
 136:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       r->timer--;
 1342              		.loc 1 136 8 view .LVU419
 1343 0016 002B     		cmp	r3, #0
 1344 0018 F7D1     		bne	.L114
 1345              	.LBB4:
 143:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       LWIP_DEBUGF(IP_REASS_DEBUG, ("ip_reass_tmr: timer timed out\n"));
 1346              		.loc 1 143 7 is_stmt 1 view .LVU420
 144:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       tmp = r;
 1347              		.loc 1 144 71 view .LVU421
 145:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       /* get the next pointer before freeing */
 1348              		.loc 1 145 7 view .LVU422
 1349              	.LVL147:
 147:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       /* free the helper struct and all enqueued pbufs */
 1350              		.loc 1 147 7 view .LVU423
 147:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       /* free the helper struct and all enqueued pbufs */
 1351              		.loc 1 147 9 is_stmt 0 view .LVU424
 1352 001a 0568     		ldr	r5, [r0]
 1353              	.LVL148:
 149:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     }
 1354              		.loc 1 149 7 is_stmt 1 view .LVU425
 1355 001c 2146     		mov	r1, r4
 1356 001e FFF7FEFF 		bl	ip_reass_free_complete_datagram
 1357              	.LVL149:
 147:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       /* free the helper struct and all enqueued pbufs */
 1358              		.loc 1 147 9 is_stmt 0 view .LVU426
 1359 0022 2846     		mov	r0, r5
 1360 0024 F5E7     		b	.L108
 1361              	.LVL150:
 1362              	.L113:
 147:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       /* free the helper struct and all enqueued pbufs */
 1363              		.loc 1 147 9 view .LVU427
 1364              	.LBE4:
 152:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 1365              		.loc 1 152 1 view .LVU428
 1366 0026 38BD     		pop	{r3, r4, r5, pc}
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s 			page 45


 1367              	.LVL151:
 1368              	.L116:
 152:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 1369              		.loc 1 152 1 view .LVU429
 1370              		.align	2
 1371              	.L115:
 1372 0028 00000000 		.word	reassdatagrams
 1373              		.cfi_endproc
 1374              	.LFE170:
 1376              		.section	.rodata.ip4_reass.str1.4,"aMS",%progbits,1
 1377              		.align	2
 1378              	.LC13:
 1379 0000 69705F72 		.ascii	"ip_reass_pbufcount >= clen\000"
 1379      65617373 
 1379      5F706275 
 1379      66636F75 
 1379      6E74203E 
 1380 001b 00       		.align	2
 1381              	.LC14:
 1382 001c 69707220 		.ascii	"ipr != NULL\000"
 1382      213D204E 
 1382      554C4C00 
 1383              		.align	2
 1384              	.LC15:
 1385 0028 6E6F7420 		.ascii	"not firstalthough just enqueued\000"
 1385      66697273 
 1385      74616C74 
 1385      686F7567 
 1385      68206A75 
 1386              		.section	.text.ip4_reass,"ax",%progbits
 1387              		.align	1
 1388              		.global	ip4_reass
 1389              		.syntax unified
 1390              		.thumb
 1391              		.thumb_func
 1393              	ip4_reass:
 1394              	.LVL152:
 1395              	.LFB176:
 504:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   struct pbuf *r;
 1396              		.loc 1 504 1 is_stmt 1 view -0
 1397              		.cfi_startproc
 1398              		@ args = 0, pretend = 0, frame = 0
 1399              		@ frame_needed = 0, uses_anonymous_args = 0
 504:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   struct pbuf *r;
 1400              		.loc 1 504 1 is_stmt 0 view .LVU431
 1401 0000 2DE9F843 		push	{r3, r4, r5, r6, r7, r8, r9, lr}
 1402              	.LCFI9:
 1403              		.cfi_def_cfa_offset 32
 1404              		.cfi_offset 3, -32
 1405              		.cfi_offset 4, -28
 1406              		.cfi_offset 5, -24
 1407              		.cfi_offset 6, -20
 1408              		.cfi_offset 7, -16
 1409              		.cfi_offset 8, -12
 1410              		.cfi_offset 9, -8
 1411              		.cfi_offset 14, -4
 1412 0004 0646     		mov	r6, r0
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s 			page 46


 505:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   struct ip_hdr *fraghdr;
 1413              		.loc 1 505 3 is_stmt 1 view .LVU432
 506:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   struct ip_reassdata *ipr;
 1414              		.loc 1 506 3 view .LVU433
 507:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   struct ip_reass_helper *iprh;
 1415              		.loc 1 507 3 view .LVU434
 508:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   u16_t offset, len, clen;
 1416              		.loc 1 508 3 view .LVU435
 509:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   u8_t hlen;
 1417              		.loc 1 509 3 view .LVU436
 510:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   int valid;
 1418              		.loc 1 510 3 view .LVU437
 511:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   int is_last;
 1419              		.loc 1 511 3 view .LVU438
 512:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 1420              		.loc 1 512 3 view .LVU439
 514:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   MIB2_STATS_INC(mib2.ipreasmreqds);
 1421              		.loc 1 514 33 view .LVU440
 515:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 1422              		.loc 1 515 36 view .LVU441
 517:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 1423              		.loc 1 517 3 view .LVU442
 517:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 1424              		.loc 1 517 11 is_stmt 0 view .LVU443
 1425 0006 4568     		ldr	r5, [r0, #4]
 1426              	.LVL153:
 519:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     LWIP_DEBUGF(IP_REASS_DEBUG, ("ip4_reass: IP options currently not supported!\n"));
 1427              		.loc 1 519 3 is_stmt 1 view .LVU444
 519:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     LWIP_DEBUGF(IP_REASS_DEBUG, ("ip4_reass: IP options currently not supported!\n"));
 1428              		.loc 1 519 7 is_stmt 0 view .LVU445
 1429 0008 2B78     		ldrb	r3, [r5]	@ zero_extendqisi2
 1430 000a 03F00F03 		and	r3, r3, #15
 519:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     LWIP_DEBUGF(IP_REASS_DEBUG, ("ip4_reass: IP options currently not supported!\n"));
 1431              		.loc 1 519 6 view .LVU446
 1432 000e 052B     		cmp	r3, #5
 1433 0010 40F0D180 		bne	.L118
 525:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   len = lwip_ntohs(IPH_LEN(fraghdr));
 1434              		.loc 1 525 3 is_stmt 1 view .LVU447
 525:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   len = lwip_ntohs(IPH_LEN(fraghdr));
 1435              		.loc 1 525 12 is_stmt 0 view .LVU448
 1436 0014 E888     		ldrh	r0, [r5, #6]	@ unaligned
 1437              	.LVL154:
 525:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   len = lwip_ntohs(IPH_LEN(fraghdr));
 1438              		.loc 1 525 12 view .LVU449
 1439 0016 FFF7FEFF 		bl	lwip_htons
 1440              	.LVL155:
 525:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   len = lwip_ntohs(IPH_LEN(fraghdr));
 1441              		.loc 1 525 12 discriminator 1 view .LVU450
 1442 001a C0F30C08 		ubfx	r8, r0, #0, #13
 525:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   len = lwip_ntohs(IPH_LEN(fraghdr));
 1443              		.loc 1 525 10 discriminator 1 view .LVU451
 1444 001e 4FEAC808 		lsl	r8, r8, #3
 1445              	.LVL156:
 526:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   hlen = IPH_HL_BYTES(fraghdr);
 1446              		.loc 1 526 3 is_stmt 1 view .LVU452
 526:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   hlen = IPH_HL_BYTES(fraghdr);
 1447              		.loc 1 526 9 is_stmt 0 view .LVU453
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s 			page 47


 1448 0022 6888     		ldrh	r0, [r5, #2]	@ unaligned
 1449 0024 FFF7FEFF 		bl	lwip_htons
 1450              	.LVL157:
 527:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   if (hlen > len) {
 1451              		.loc 1 527 3 is_stmt 1 view .LVU454
 527:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   if (hlen > len) {
 1452              		.loc 1 527 10 is_stmt 0 view .LVU455
 1453 0028 2B78     		ldrb	r3, [r5]	@ zero_extendqisi2
 1454 002a 03F00F03 		and	r3, r3, #15
 527:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   if (hlen > len) {
 1455              		.loc 1 527 8 view .LVU456
 1456 002e 9A00     		lsls	r2, r3, #2
 1457              	.LVL158:
 528:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     /* invalid datagram */
 1458              		.loc 1 528 3 is_stmt 1 view .LVU457
 528:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     /* invalid datagram */
 1459              		.loc 1 528 6 is_stmt 0 view .LVU458
 1460 0030 B0EB830F 		cmp	r0, r3, lsl #2
 1461 0034 C0F0BF80 		bcc	.L118
 532:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 1462              		.loc 1 532 3 is_stmt 1 view .LVU459
 532:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 1463              		.loc 1 532 7 is_stmt 0 view .LVU460
 1464 0038 801A     		subs	r0, r0, r2
 1465              	.LVL159:
 532:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 1466              		.loc 1 532 7 view .LVU461
 1467 003a 87B2     		uxth	r7, r0
 1468              	.LVL160:
 535:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   if ((ip_reass_pbufcount + clen) > IP_REASS_MAX_PBUFS) {
 1469              		.loc 1 535 3 is_stmt 1 view .LVU462
 535:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   if ((ip_reass_pbufcount + clen) > IP_REASS_MAX_PBUFS) {
 1470              		.loc 1 535 10 is_stmt 0 view .LVU463
 1471 003c 3046     		mov	r0, r6
 1472 003e FFF7FEFF 		bl	pbuf_clen
 1473              	.LVL161:
 535:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   if ((ip_reass_pbufcount + clen) > IP_REASS_MAX_PBUFS) {
 1474              		.loc 1 535 10 view .LVU464
 1475 0042 8146     		mov	r9, r0
 1476              	.LVL162:
 536:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #if IP_REASS_FREE_OLDEST
 1477              		.loc 1 536 3 is_stmt 1 view .LVU465
 536:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #if IP_REASS_FREE_OLDEST
 1478              		.loc 1 536 27 is_stmt 0 view .LVU466
 1479 0044 6B4B     		ldr	r3, .L149
 1480 0046 1B88     		ldrh	r3, [r3]
 1481 0048 0344     		add	r3, r3, r0
 536:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #if IP_REASS_FREE_OLDEST
 1482              		.loc 1 536 6 view .LVU467
 1483 004a 0A2B     		cmp	r3, #10
 1484 004c 02DC     		bgt	.L141
 1485              	.LVL163:
 1486              	.L119:
 554:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     /* Check if the incoming fragment matches the one currently present
 1487              		.loc 1 554 3 is_stmt 1 view .LVU468
 554:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     /* Check if the incoming fragment matches the one currently present
 1488              		.loc 1 554 12 is_stmt 0 view .LVU469
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s 			page 48


 1489 004e 6A4B     		ldr	r3, .L149+4
 1490 0050 1C68     		ldr	r4, [r3]
 1491              	.LVL164:
 554:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     /* Check if the incoming fragment matches the one currently present
 1492              		.loc 1 554 3 view .LVU470
 1493 0052 0DE0     		b	.L120
 1494              	.LVL165:
 1495              	.L141:
 538:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         ((ip_reass_pbufcount + clen) > IP_REASS_MAX_PBUFS))
 1496              		.loc 1 538 5 is_stmt 1 view .LVU471
 538:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         ((ip_reass_pbufcount + clen) > IP_REASS_MAX_PBUFS))
 1497              		.loc 1 538 10 is_stmt 0 view .LVU472
 1498 0054 0146     		mov	r1, r0
 1499 0056 2846     		mov	r0, r5
 1500              	.LVL166:
 538:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         ((ip_reass_pbufcount + clen) > IP_REASS_MAX_PBUFS))
 1501              		.loc 1 538 10 view .LVU473
 1502 0058 FFF7FEFF 		bl	ip_reass_remove_oldest_datagram
 1503              	.LVL167:
 538:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         ((ip_reass_pbufcount + clen) > IP_REASS_MAX_PBUFS))
 1504              		.loc 1 538 8 discriminator 1 view .LVU474
 1505 005c 0028     		cmp	r0, #0
 1506 005e 00F0AA80 		beq	.L118
 539:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #endif /* IP_REASS_FREE_OLDEST */
 1507              		.loc 1 539 30 view .LVU475
 1508 0062 644B     		ldr	r3, .L149
 1509 0064 1B88     		ldrh	r3, [r3]
 1510 0066 4B44     		add	r3, r3, r9
 538:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         ((ip_reass_pbufcount + clen) > IP_REASS_MAX_PBUFS))
 1511              		.loc 1 538 57 discriminator 1 view .LVU476
 1512 0068 0A2B     		cmp	r3, #10
 1513 006a F0DD     		ble	.L119
 1514 006c A3E0     		b	.L118
 1515              	.LVL168:
 1516              	.L121:
 554:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     /* Check if the incoming fragment matches the one currently present
 1517              		.loc 1 554 47 is_stmt 1 discriminator 2 view .LVU477
 1518 006e 2468     		ldr	r4, [r4]
 1519              	.LVL169:
 1520              	.L120:
 554:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     /* Check if the incoming fragment matches the one currently present
 1521              		.loc 1 554 34 discriminator 1 view .LVU478
 1522 0070 5CB1     		cbz	r4, .L122
 558:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       LWIP_DEBUGF(IP_REASS_DEBUG, ("ip4_reass: matching previous fragment ID=%"X16_F"\n",
 1523              		.loc 1 558 5 view .LVU479
 558:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       LWIP_DEBUGF(IP_REASS_DEBUG, ("ip4_reass: matching previous fragment ID=%"X16_F"\n",
 1524              		.loc 1 558 9 is_stmt 0 view .LVU480
 1525 0072 6269     		ldr	r2, [r4, #20]
 1526 0074 EB68     		ldr	r3, [r5, #12]	@ unaligned
 558:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       LWIP_DEBUGF(IP_REASS_DEBUG, ("ip4_reass: matching previous fragment ID=%"X16_F"\n",
 1527              		.loc 1 558 8 view .LVU481
 1528 0076 9A42     		cmp	r2, r3
 1529 0078 F9D1     		bne	.L121
 558:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       LWIP_DEBUGF(IP_REASS_DEBUG, ("ip4_reass: matching previous fragment ID=%"X16_F"\n",
 1530              		.loc 1 558 9 discriminator 1 view .LVU482
 1531 007a A269     		ldr	r2, [r4, #24]
 1532 007c 2B69     		ldr	r3, [r5, #16]	@ unaligned
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s 			page 49


 1533 007e 9A42     		cmp	r2, r3
 1534 0080 F5D1     		bne	.L121
 558:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       LWIP_DEBUGF(IP_REASS_DEBUG, ("ip4_reass: matching previous fragment ID=%"X16_F"\n",
 1535              		.loc 1 558 9 discriminator 2 view .LVU483
 1536 0082 A289     		ldrh	r2, [r4, #12]
 1537 0084 AB88     		ldrh	r3, [r5, #4]	@ unaligned
 1538 0086 9A42     		cmp	r2, r3
 1539 0088 F1D1     		bne	.L121
 1540              	.L122:
 566:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     /* Enqueue a new datagram into the datagram queue */
 1541              		.loc 1 566 3 is_stmt 1 view .LVU484
 566:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     /* Enqueue a new datagram into the datagram queue */
 1542              		.loc 1 566 6 is_stmt 0 view .LVU485
 1543 008a 74B3     		cbz	r4, .L142
 574:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         ((lwip_ntohs(IPH_OFFSET(&ipr->iphdr)) & IP_OFFMASK) != 0)) {
 1544              		.loc 1 574 5 is_stmt 1 view .LVU486
 574:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         ((lwip_ntohs(IPH_OFFSET(&ipr->iphdr)) & IP_OFFMASK) != 0)) {
 1545              		.loc 1 574 11 is_stmt 0 view .LVU487
 1546 008c E888     		ldrh	r0, [r5, #6]	@ unaligned
 1547 008e FFF7FEFF 		bl	lwip_htons
 1548              	.LVL170:
 574:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         ((lwip_ntohs(IPH_OFFSET(&ipr->iphdr)) & IP_OFFMASK) != 0)) {
 1549              		.loc 1 574 8 discriminator 1 view .LVU488
 1550 0092 C0F30C00 		ubfx	r0, r0, #0, #13
 1551 0096 80B3     		cbz	r0, .L143
 1552              	.L125:
 588:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   if (is_last) {
 1553              		.loc 1 588 3 is_stmt 1 view .LVU489
 588:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   if (is_last) {
 1554              		.loc 1 588 14 is_stmt 0 view .LVU490
 1555 0098 ED88     		ldrh	r5, [r5, #6]	@ unaligned
 1556              	.LVL171:
 588:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   if (is_last) {
 1557              		.loc 1 588 14 view .LVU491
 1558 009a 05F02005 		and	r5, r5, #32
 588:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   if (is_last) {
 1559              		.loc 1 588 53 view .LVU492
 1560 009e 002D     		cmp	r5, #0
 1561 00a0 0CBF     		ite	eq
 1562 00a2 0122     		moveq	r2, #1
 1563 00a4 0022     		movne	r2, #0
 1564              	.LVL172:
 589:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     u16_t datagram_len = (u16_t)(offset + len);
 1565              		.loc 1 589 3 is_stmt 1 view .LVU493
 589:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     u16_t datagram_len = (u16_t)(offset + len);
 1566              		.loc 1 589 6 is_stmt 0 view .LVU494
 1567 00a6 08D1     		bne	.L126
 1568              	.LBB5:
 590:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     if ((datagram_len < offset) || (datagram_len > (0xFFFF - IP_HLEN))) {
 1569              		.loc 1 590 5 is_stmt 1 view .LVU495
 590:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     if ((datagram_len < offset) || (datagram_len > (0xFFFF - IP_HLEN))) {
 1570              		.loc 1 590 11 is_stmt 0 view .LVU496
 1571 00a8 08EB0703 		add	r3, r8, r7
 1572 00ac 9BB2     		uxth	r3, r3
 1573              	.LVL173:
 591:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       /* u16_t overflow, cannot handle this */
 1574              		.loc 1 591 5 is_stmt 1 view .LVU497
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s 			page 50


 591:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       /* u16_t overflow, cannot handle this */
 1575              		.loc 1 591 8 is_stmt 0 view .LVU498
 1576 00ae 4FF6EB71 		movw	r1, #65515
 1577 00b2 8B42     		cmp	r3, r1
 1578 00b4 98BF     		it	ls
 1579 00b6 9845     		cmpls	r8, r3
 1580 00b8 7AD8     		bhi	.L127
 1581              	.LVL174:
 1582              	.L126:
 591:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       /* u16_t overflow, cannot handle this */
 1583              		.loc 1 591 8 view .LVU499
 1584              	.LBE5:
 598:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   if (valid == IP_REASS_VALIDATE_PBUF_DROPPED) {
 1585              		.loc 1 598 3 is_stmt 1 view .LVU500
 598:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   if (valid == IP_REASS_VALIDATE_PBUF_DROPPED) {
 1586              		.loc 1 598 11 is_stmt 0 view .LVU501
 1587 00ba 3146     		mov	r1, r6
 1588 00bc 2046     		mov	r0, r4
 1589 00be FFF7FEFF 		bl	ip_reass_chain_frag_into_datagram_and_validate
 1590              	.LVL175:
 599:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     goto nullreturn_ipr;
 1591              		.loc 1 599 3 is_stmt 1 view .LVU502
 599:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     goto nullreturn_ipr;
 1592              		.loc 1 599 6 is_stmt 0 view .LVU503
 1593 00c2 B0F1FF3F 		cmp	r0, #-1
 1594 00c6 73D0     		beq	.L127
 607:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   if (is_last) {
 1595              		.loc 1 607 3 is_stmt 1 view .LVU504
 607:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   if (is_last) {
 1596              		.loc 1 607 24 is_stmt 0 view .LVU505
 1597 00c8 4A4B     		ldr	r3, .L149
 1598 00ca 1A88     		ldrh	r2, [r3]
 1599 00cc 9144     		add	r9, r9, r2
 607:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   if (is_last) {
 1600              		.loc 1 607 22 view .LVU506
 1601 00ce A3F80090 		strh	r9, [r3]	@ movhi
 608:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     u16_t datagram_len = (u16_t)(offset + len);
 1602              		.loc 1 608 3 is_stmt 1 view .LVU507
 608:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     u16_t datagram_len = (u16_t)(offset + len);
 1603              		.loc 1 608 6 is_stmt 0 view .LVU508
 1604 00d2 35B9     		cbnz	r5, .L128
 1605              	.LBB6:
 609:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     ipr->datagram_len = datagram_len;
 1606              		.loc 1 609 5 is_stmt 1 view .LVU509
 609:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     ipr->datagram_len = datagram_len;
 1607              		.loc 1 609 11 is_stmt 0 view .LVU510
 1608 00d4 B844     		add	r8, r8, r7
 1609              	.LVL176:
 610:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     ipr->flags |= IP_REASS_FLAG_LASTFRAG;
 1610              		.loc 1 610 5 is_stmt 1 view .LVU511
 610:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     ipr->flags |= IP_REASS_FLAG_LASTFRAG;
 1611              		.loc 1 610 23 is_stmt 0 view .LVU512
 1612 00d6 A4F81C80 		strh	r8, [r4, #28]	@ movhi
 611:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     LWIP_DEBUGF(IP_REASS_DEBUG,
 1613              		.loc 1 611 5 is_stmt 1 view .LVU513
 611:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     LWIP_DEBUGF(IP_REASS_DEBUG,
 1614              		.loc 1 611 8 is_stmt 0 view .LVU514
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s 			page 51


 1615 00da A37F     		ldrb	r3, [r4, #30]	@ zero_extendqisi2
 611:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     LWIP_DEBUGF(IP_REASS_DEBUG,
 1616              		.loc 1 611 16 view .LVU515
 1617 00dc 43F00103 		orr	r3, r3, #1
 1618 00e0 A377     		strb	r3, [r4, #30]
 1619              	.LVL177:
 1620              	.L128:
 614:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   }
 1621              		.loc 1 614 37 is_stmt 1 view .LVU516
 1622              	.LBE6:
 617:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     struct ip_reassdata *ipr_prev;
 1623              		.loc 1 617 3 view .LVU517
 617:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     struct ip_reassdata *ipr_prev;
 1624              		.loc 1 617 6 is_stmt 0 view .LVU518
 1625 00e2 0128     		cmp	r0, #1
 1626 00e4 1BD0     		beq	.L144
 677:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 1627              		.loc 1 677 10 view .LVU519
 1628 00e6 0026     		movs	r6, #0
 1629              	.LVL178:
 677:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 1630              		.loc 1 677 10 view .LVU520
 1631 00e8 69E0     		b	.L117
 1632              	.LVL179:
 1633              	.L142:
 568:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     /* Bail if unable to enqueue */
 1634              		.loc 1 568 5 is_stmt 1 view .LVU521
 568:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     /* Bail if unable to enqueue */
 1635              		.loc 1 568 11 is_stmt 0 view .LVU522
 1636 00ea 4946     		mov	r1, r9
 1637 00ec 2846     		mov	r0, r5
 1638 00ee FFF7FEFF 		bl	ip_reass_enqueue_new_datagram
 1639              	.LVL180:
 570:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       goto nullreturn;
 1640              		.loc 1 570 5 is_stmt 1 view .LVU523
 570:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       goto nullreturn;
 1641              		.loc 1 570 8 is_stmt 0 view .LVU524
 1642 00f2 0446     		mov	r4, r0
 1643 00f4 0028     		cmp	r0, #0
 1644 00f6 CFD1     		bne	.L125
 1645 00f8 5DE0     		b	.L118
 1646              	.LVL181:
 1647              	.L143:
 575:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       /* ipr->iphdr is not the header from the first fragment, but fraghdr is
 1648              		.loc 1 575 11 view .LVU525
 1649 00fa E089     		ldrh	r0, [r4, #14]
 1650 00fc FFF7FEFF 		bl	lwip_htons
 1651              	.LVL182:
 574:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         ((lwip_ntohs(IPH_OFFSET(&ipr->iphdr)) & IP_OFFMASK) != 0)) {
 1652              		.loc 1 574 63 discriminator 1 view .LVU526
 1653 0100 C0F30C00 		ubfx	r0, r0, #0, #13
 1654 0104 0028     		cmp	r0, #0
 1655 0106 C7D0     		beq	.L125
 580:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     }
 1656              		.loc 1 580 7 is_stmt 1 view .LVU527
 1657 0108 2B68     		ldr	r3, [r5]	@ unaligned
 1658 010a 6868     		ldr	r0, [r5, #4]	@ unaligned
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s 			page 52


 1659 010c A968     		ldr	r1, [r5, #8]	@ unaligned
 1660 010e EA68     		ldr	r2, [r5, #12]	@ unaligned
 1661 0110 A360     		str	r3, [r4, #8]	@ unaligned
 1662 0112 E060     		str	r0, [r4, #12]	@ unaligned
 1663 0114 2161     		str	r1, [r4, #16]	@ unaligned
 1664 0116 6261     		str	r2, [r4, #20]	@ unaligned
 1665 0118 2B69     		ldr	r3, [r5, #16]	@ unaligned
 1666 011a A361     		str	r3, [r4, #24]	@ unaligned
 1667 011c BCE7     		b	.L125
 1668              	.LVL183:
 1669              	.L144:
 1670              	.LBB7:
 618:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     /* the totally last fragment (flag more fragments = 0) was received at least
 1671              		.loc 1 618 5 view .LVU528
 621:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 1672              		.loc 1 621 5 view .LVU529
 621:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 1673              		.loc 1 621 37 is_stmt 0 view .LVU530
 1674 011e A08B     		ldrh	r0, [r4, #28]
 1675              	.LVL184:
 621:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 1676              		.loc 1 621 11 view .LVU531
 1677 0120 1430     		adds	r0, r0, #20
 1678              	.LVL185:
 624:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 1679              		.loc 1 624 5 is_stmt 1 view .LVU532
 624:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 1680              		.loc 1 624 39 is_stmt 0 view .LVU533
 1681 0122 6368     		ldr	r3, [r4, #4]
 624:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 1682              		.loc 1 624 42 view .LVU534
 1683 0124 5E68     		ldr	r6, [r3, #4]
 1684              	.LVL186:
 624:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 1685              		.loc 1 624 7 view .LVU535
 1686 0126 3568     		ldr	r5, [r6]	@ unaligned
 1687              	.LVL187:
 627:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     SMEMCPY(fraghdr, &ipr->iphdr, IP_HLEN);
 1688              		.loc 1 627 5 is_stmt 1 view .LVU536
 628:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     IPH_LEN_SET(fraghdr, lwip_htons(datagram_len));
 1689              		.loc 1 628 5 view .LVU537
 1690 0128 A368     		ldr	r3, [r4, #8]	@ unaligned
 1691 012a E768     		ldr	r7, [r4, #12]	@ unaligned
 1692              	.LVL188:
 628:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     IPH_LEN_SET(fraghdr, lwip_htons(datagram_len));
 1693              		.loc 1 628 5 is_stmt 0 view .LVU538
 1694 012c 2169     		ldr	r1, [r4, #16]	@ unaligned
 1695 012e 6269     		ldr	r2, [r4, #20]	@ unaligned
 1696 0130 3360     		str	r3, [r6]	@ unaligned
 1697 0132 7760     		str	r7, [r6, #4]	@ unaligned
 1698 0134 B160     		str	r1, [r6, #8]	@ unaligned
 1699 0136 F260     		str	r2, [r6, #12]	@ unaligned
 1700 0138 A369     		ldr	r3, [r4, #24]	@ unaligned
 1701 013a 3361     		str	r3, [r6, #16]	@ unaligned
 629:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     IPH_OFFSET_SET(fraghdr, 0);
 1702              		.loc 1 629 5 is_stmt 1 view .LVU539
 1703 013c 80B2     		uxth	r0, r0
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s 			page 53


 629:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     IPH_OFFSET_SET(fraghdr, 0);
 1704              		.loc 1 629 5 is_stmt 0 view .LVU540
 1705 013e FFF7FEFF 		bl	lwip_htons
 1706              	.LVL189:
 629:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     IPH_OFFSET_SET(fraghdr, 0);
 1707              		.loc 1 629 5 discriminator 1 view .LVU541
 1708 0142 7080     		strh	r0, [r6, #2]	@ unaligned
 630:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     IPH_CHKSUM_SET(fraghdr, 0);
 1709              		.loc 1 630 5 is_stmt 1 view .LVU542
 1710 0144 0023     		movs	r3, #0
 1711 0146 B371     		strb	r3, [r6, #6]
 1712 0148 F371     		strb	r3, [r6, #7]
 631:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     /* @todo: do we need to set/calculate the correct checksum? */
 1713              		.loc 1 631 5 view .LVU543
 1714 014a B372     		strb	r3, [r6, #10]
 1715 014c F372     		strb	r3, [r6, #11]
 639:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 1716              		.loc 1 639 5 view .LVU544
 639:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 1717              		.loc 1 639 7 is_stmt 0 view .LVU545
 1718 014e 6668     		ldr	r6, [r4, #4]
 1719              	.LVL190:
 642:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       iprh = (struct ip_reass_helper *)r->payload;
 1720              		.loc 1 642 5 is_stmt 1 view .LVU546
 642:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       iprh = (struct ip_reass_helper *)r->payload;
 1721              		.loc 1 642 11 is_stmt 0 view .LVU547
 1722 0150 09E0     		b	.L130
 1723              	.L131:
 643:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 1724              		.loc 1 643 7 is_stmt 1 view .LVU548
 643:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 1725              		.loc 1 643 12 is_stmt 0 view .LVU549
 1726 0152 6F68     		ldr	r7, [r5, #4]
 1727              	.LVL191:
 646:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       pbuf_cat(p, r);
 1728              		.loc 1 646 7 is_stmt 1 view .LVU550
 1729 0154 1421     		movs	r1, #20
 1730 0156 2846     		mov	r0, r5
 1731 0158 FFF7FEFF 		bl	pbuf_remove_header
 1732              	.LVL192:
 647:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       r = iprh->next_pbuf;
 1733              		.loc 1 647 7 view .LVU551
 1734 015c 2946     		mov	r1, r5
 1735 015e 3046     		mov	r0, r6
 1736 0160 FFF7FEFF 		bl	pbuf_cat
 1737              	.LVL193:
 648:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     }
 1738              		.loc 1 648 7 view .LVU552
 648:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     }
 1739              		.loc 1 648 9 is_stmt 0 view .LVU553
 1740 0164 3D68     		ldr	r5, [r7]	@ unaligned
 1741              	.LVL194:
 1742              	.L130:
 642:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       iprh = (struct ip_reass_helper *)r->payload;
 1743              		.loc 1 642 14 is_stmt 1 view .LVU554
 1744 0166 002D     		cmp	r5, #0
 1745 0168 F3D1     		bne	.L131
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s 			page 54


 652:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       ipr_prev = NULL;
 1746              		.loc 1 652 5 view .LVU555
 652:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       ipr_prev = NULL;
 1747              		.loc 1 652 13 is_stmt 0 view .LVU556
 1748 016a 234B     		ldr	r3, .L149+4
 1749 016c 1968     		ldr	r1, [r3]
 652:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       ipr_prev = NULL;
 1750              		.loc 1 652 8 view .LVU557
 1751 016e A142     		cmp	r1, r4
 1752 0170 05D0     		beq	.L145
 1753              	.L133:
 1754              	.LVL195:
 655:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         if (ipr_prev->next == ipr) {
 1755              		.loc 1 655 48 is_stmt 1 discriminator 1 view .LVU558
 1756 0172 29B1     		cbz	r1, .L132
 656:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****           break;
 1757              		.loc 1 656 9 view .LVU559
 656:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****           break;
 1758              		.loc 1 656 21 is_stmt 0 view .LVU560
 1759 0174 0B68     		ldr	r3, [r1]
 656:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****           break;
 1760              		.loc 1 656 12 view .LVU561
 1761 0176 A342     		cmp	r3, r4
 1762 0178 02D0     		beq	.L132
 655:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         if (ipr_prev->next == ipr) {
 1763              		.loc 1 655 66 discriminator 2 view .LVU562
 1764 017a 1946     		mov	r1, r3
 1765              	.LVL196:
 655:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         if (ipr_prev->next == ipr) {
 1766              		.loc 1 655 66 discriminator 2 view .LVU563
 1767 017c F9E7     		b	.L133
 1768              	.LVL197:
 1769              	.L145:
 653:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     } else {
 1770              		.loc 1 653 16 view .LVU564
 1771 017e 2946     		mov	r1, r5
 1772              	.L132:
 1773              	.LVL198:
 663:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 1774              		.loc 1 663 5 is_stmt 1 view .LVU565
 1775 0180 2046     		mov	r0, r4
 1776 0182 FFF7FEFF 		bl	ip_reass_dequeue_datagram
 1777              	.LVL199:
 666:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     LWIP_ASSERT("ip_reass_pbufcount >= clen", ip_reass_pbufcount >= clen);
 1778              		.loc 1 666 5 view .LVU566
 666:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     LWIP_ASSERT("ip_reass_pbufcount >= clen", ip_reass_pbufcount >= clen);
 1779              		.loc 1 666 12 is_stmt 0 view .LVU567
 1780 0186 3046     		mov	r0, r6
 1781 0188 FFF7FEFF 		bl	pbuf_clen
 1782              	.LVL200:
 1783 018c 0446     		mov	r4, r0
 1784              	.LVL201:
 667:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     ip_reass_pbufcount = (u16_t)(ip_reass_pbufcount - clen);
 1785              		.loc 1 667 5 is_stmt 1 view .LVU568
 667:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     ip_reass_pbufcount = (u16_t)(ip_reass_pbufcount - clen);
 1786              		.loc 1 667 5 view .LVU569
 1787 018e 194B     		ldr	r3, .L149
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s 			page 55


 1788 0190 1B88     		ldrh	r3, [r3]
 1789 0192 8342     		cmp	r3, r0
 1790 0194 04D3     		bcc	.L146
 1791              	.LVL202:
 1792              	.L135:
 667:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     ip_reass_pbufcount = (u16_t)(ip_reass_pbufcount - clen);
 1793              		.loc 1 667 5 discriminator 3 view .LVU570
 667:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     ip_reass_pbufcount = (u16_t)(ip_reass_pbufcount - clen);
 1794              		.loc 1 667 5 discriminator 3 view .LVU571
 668:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 1795              		.loc 1 668 5 view .LVU572
 668:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 1796              		.loc 1 668 26 is_stmt 0 view .LVU573
 1797 0196 174A     		ldr	r2, .L149
 1798 0198 1388     		ldrh	r3, [r2]
 1799 019a 1B1B     		subs	r3, r3, r4
 668:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 1800              		.loc 1 668 24 view .LVU574
 1801 019c 1380     		strh	r3, [r2]	@ movhi
 670:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 1802              		.loc 1 670 36 is_stmt 1 view .LVU575
 673:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   }
 1803              		.loc 1 673 5 view .LVU576
 673:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   }
 1804              		.loc 1 673 12 is_stmt 0 view .LVU577
 1805 019e 0EE0     		b	.L117
 1806              	.LVL203:
 1807              	.L146:
 667:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     ip_reass_pbufcount = (u16_t)(ip_reass_pbufcount - clen);
 1808              		.loc 1 667 5 is_stmt 1 discriminator 1 view .LVU578
 667:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     ip_reass_pbufcount = (u16_t)(ip_reass_pbufcount - clen);
 1809              		.loc 1 667 5 discriminator 1 view .LVU579
 1810 01a0 164B     		ldr	r3, .L149+8
 1811 01a2 40F29B22 		movw	r2, #667
 1812 01a6 1649     		ldr	r1, .L149+12
 1813 01a8 1648     		ldr	r0, .L149+16
 1814              	.LVL204:
 667:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     ip_reass_pbufcount = (u16_t)(ip_reass_pbufcount - clen);
 1815              		.loc 1 667 5 is_stmt 0 discriminator 1 view .LVU580
 1816 01aa FFF7FEFF 		bl	printf
 1817              	.LVL205:
 1818 01ae F2E7     		b	.L135
 1819              	.LVL206:
 1820              	.L127:
 667:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     ip_reass_pbufcount = (u16_t)(ip_reass_pbufcount - clen);
 1821              		.loc 1 667 5 discriminator 1 view .LVU581
 1822              	.LBE7:
 680:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   if (ipr->p == NULL) {
 1823              		.loc 1 680 3 is_stmt 1 view .LVU582
 680:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   if (ipr->p == NULL) {
 1824              		.loc 1 680 3 view .LVU583
 1825 01b0 44B1     		cbz	r4, .L147
 1826              	.L136:
 680:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   if (ipr->p == NULL) {
 1827              		.loc 1 680 3 discriminator 3 view .LVU584
 680:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   if (ipr->p == NULL) {
 1828              		.loc 1 680 3 discriminator 3 view .LVU585
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s 			page 56


 681:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     /* dropped pbuf after creating a new datagram entry: remove the entry, too */
 1829              		.loc 1 681 3 view .LVU586
 681:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     /* dropped pbuf after creating a new datagram entry: remove the entry, too */
 1830              		.loc 1 681 10 is_stmt 0 view .LVU587
 1831 01b2 6368     		ldr	r3, [r4, #4]
 681:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     /* dropped pbuf after creating a new datagram entry: remove the entry, too */
 1832              		.loc 1 681 6 view .LVU588
 1833 01b4 73B1     		cbz	r3, .L148
 1834              	.LVL207:
 1835              	.L118:
 688:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   IPFRAG_STATS_INC(ip_frag.drop);
 1836              		.loc 1 688 59 is_stmt 1 view .LVU589
 689:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   pbuf_free(p);
 1837              		.loc 1 689 33 view .LVU590
 690:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   return NULL;
 1838              		.loc 1 690 3 view .LVU591
 1839 01b6 3046     		mov	r0, r6
 1840 01b8 FFF7FEFF 		bl	pbuf_free
 1841              	.LVL208:
 691:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** }
 1842              		.loc 1 691 3 view .LVU592
 691:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** }
 1843              		.loc 1 691 10 is_stmt 0 view .LVU593
 1844 01bc 0026     		movs	r6, #0
 1845              	.LVL209:
 1846              	.L117:
 692:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #endif /* IP_REASSEMBLY */
 1847              		.loc 1 692 1 view .LVU594
 1848 01be 3046     		mov	r0, r6
 1849 01c0 BDE8F883 		pop	{r3, r4, r5, r6, r7, r8, r9, pc}
 1850              	.LVL210:
 1851              	.L147:
 680:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   if (ipr->p == NULL) {
 1852              		.loc 1 680 3 is_stmt 1 discriminator 1 view .LVU595
 680:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   if (ipr->p == NULL) {
 1853              		.loc 1 680 3 discriminator 1 view .LVU596
 1854 01c4 0D4B     		ldr	r3, .L149+8
 1855 01c6 4FF42A72 		mov	r2, #680
 1856 01ca 0F49     		ldr	r1, .L149+20
 1857 01cc 0D48     		ldr	r0, .L149+16
 1858 01ce FFF7FEFF 		bl	printf
 1859              	.LVL211:
 1860 01d2 EEE7     		b	.L136
 1861              	.L148:
 683:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     ip_reass_dequeue_datagram(ipr, NULL);
 1862              		.loc 1 683 5 view .LVU597
 683:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     ip_reass_dequeue_datagram(ipr, NULL);
 1863              		.loc 1 683 5 view .LVU598
 1864 01d4 084B     		ldr	r3, .L149+4
 1865 01d6 1B68     		ldr	r3, [r3]
 1866 01d8 A342     		cmp	r3, r4
 1867 01da 06D0     		beq	.L137
 683:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     ip_reass_dequeue_datagram(ipr, NULL);
 1868              		.loc 1 683 5 discriminator 1 view .LVU599
 683:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     ip_reass_dequeue_datagram(ipr, NULL);
 1869              		.loc 1 683 5 discriminator 1 view .LVU600
 1870 01dc 074B     		ldr	r3, .L149+8
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s 			page 57


 1871 01de 40F2AB22 		movw	r2, #683
 1872 01e2 0A49     		ldr	r1, .L149+24
 1873 01e4 0748     		ldr	r0, .L149+16
 1874 01e6 FFF7FEFF 		bl	printf
 1875              	.LVL212:
 1876              	.L137:
 683:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     ip_reass_dequeue_datagram(ipr, NULL);
 1877              		.loc 1 683 5 discriminator 3 view .LVU601
 683:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     ip_reass_dequeue_datagram(ipr, NULL);
 1878              		.loc 1 683 5 discriminator 3 view .LVU602
 684:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   }
 1879              		.loc 1 684 5 view .LVU603
 1880 01ea 0021     		movs	r1, #0
 1881 01ec 2046     		mov	r0, r4
 1882 01ee FFF7FEFF 		bl	ip_reass_dequeue_datagram
 1883              	.LVL213:
 1884 01f2 E0E7     		b	.L118
 1885              	.L150:
 1886              		.align	2
 1887              	.L149:
 1888 01f4 00000000 		.word	ip_reass_pbufcount
 1889 01f8 00000000 		.word	reassdatagrams
 1890 01fc 00000000 		.word	.LC0
 1891 0200 00000000 		.word	.LC13
 1892 0204 54000000 		.word	.LC2
 1893 0208 1C000000 		.word	.LC14
 1894 020c 28000000 		.word	.LC15
 1895              		.cfi_endproc
 1896              	.LFE176:
 1898              		.section	.rodata.ip4_frag.str1.4,"aMS",%progbits,1
 1899              		.align	2
 1900              	.LC16:
 1901 0000 6970345F 		.ascii	"ip4_frag(): pbuf too short\000"
 1901      66726167 
 1901      28293A20 
 1901      70627566 
 1901      20746F6F 
 1902 001b 00       		.align	2
 1903              	.LC17:
 1904 001c 74686973 		.ascii	"this needs a pbuf in one piece!\000"
 1904      206E6565 
 1904      64732061 
 1904      20706275 
 1904      6620696E 
 1905              		.align	2
 1906              	.LC18:
 1907 003c 702D3E6C 		.ascii	"p->len >= poff\000"
 1907      656E203E 
 1907      3D20706F 
 1907      666600
 1908              		.section	.text.ip4_frag,"ax",%progbits
 1909              		.align	1
 1910              		.global	ip4_frag
 1911              		.syntax unified
 1912              		.thumb
 1913              		.thumb_func
 1915              	ip4_frag:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s 			page 58


 1916              	.LVL214:
 1917              	.LFB180:
 725:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #endif /* !LWIP_NETIF_TX_SINGLE_PBUF */
 726:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 727:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** /**
 728:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  * Fragment an IP datagram if too large for the netif.
 729:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  *
 730:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  * Chop the datagram in MTU sized chunks and send them in order
 731:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  * by pointing PBUF_REFs into p.
 732:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  *
 733:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  * @param p ip packet to send
 734:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  * @param netif the netif on which to send
 735:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  * @param dest destination ip address to which to send
 736:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  *
 737:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  * @return ERR_OK if sent successfully, err_t otherwise
 738:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****  */
 739:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** err_t
 740:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** ip4_frag(struct pbuf *p, struct netif *netif, const ip4_addr_t *dest)
 741:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** {
 1918              		.loc 1 741 1 view -0
 1919              		.cfi_startproc
 1920              		@ args = 0, pretend = 0, frame = 32
 1921              		@ frame_needed = 0, uses_anonymous_args = 0
 1922              		.loc 1 741 1 is_stmt 0 view .LVU605
 1923 0000 2DE9F04F 		push	{r4, r5, r6, r7, r8, r9, r10, fp, lr}
 1924              	.LCFI10:
 1925              		.cfi_def_cfa_offset 36
 1926              		.cfi_offset 4, -36
 1927              		.cfi_offset 5, -32
 1928              		.cfi_offset 6, -28
 1929              		.cfi_offset 7, -24
 1930              		.cfi_offset 8, -20
 1931              		.cfi_offset 9, -16
 1932              		.cfi_offset 10, -12
 1933              		.cfi_offset 11, -8
 1934              		.cfi_offset 14, -4
 1935 0004 8BB0     		sub	sp, sp, #44
 1936              	.LCFI11:
 1937              		.cfi_def_cfa_offset 80
 1938 0006 0546     		mov	r5, r0
 1939 0008 0691     		str	r1, [sp, #24]
 1940 000a 0892     		str	r2, [sp, #32]
 742:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   struct pbuf *rambuf;
 1941              		.loc 1 742 3 is_stmt 1 view .LVU606
 743:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #if !LWIP_NETIF_TX_SINGLE_PBUF
 744:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   struct pbuf *newpbuf;
 1942              		.loc 1 744 3 view .LVU607
 745:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   u16_t newpbuflen = 0;
 1943              		.loc 1 745 3 view .LVU608
 1944              	.LVL215:
 746:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   u16_t left_to_copy;
 1945              		.loc 1 746 3 view .LVU609
 747:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #endif
 748:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   struct ip_hdr *original_iphdr;
 1946              		.loc 1 748 3 view .LVU610
 749:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   struct ip_hdr *iphdr;
 1947              		.loc 1 749 3 view .LVU611
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s 			page 59


 750:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   const u16_t nfb = (u16_t)((netif->mtu - IP_HLEN) / 8);
 1948              		.loc 1 750 3 view .LVU612
 1949              		.loc 1 750 35 is_stmt 0 view .LVU613
 1950 000c 8B8C     		ldrh	r3, [r1, #36]
 1951              		.loc 1 750 52 view .LVU614
 1952 000e 143B     		subs	r3, r3, #20
 1953 0010 21D4     		bmi	.L170
 1954              	.L152:
 1955              		.loc 1 750 15 view .LVU615
 1956 0012 C3F3CF03 		ubfx	r3, r3, #3, #16
 1957 0016 0593     		str	r3, [sp, #20]
 1958              	.LVL216:
 751:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   u16_t left, fragsize;
 1959              		.loc 1 751 3 is_stmt 1 view .LVU616
 752:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   u16_t ofo;
 1960              		.loc 1 752 3 view .LVU617
 753:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   int last;
 1961              		.loc 1 753 3 view .LVU618
 754:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   u16_t poff = IP_HLEN;
 1962              		.loc 1 754 3 view .LVU619
 755:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   u16_t tmp;
 1963              		.loc 1 755 3 view .LVU620
 756:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   int mf_set;
 1964              		.loc 1 756 3 view .LVU621
 757:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 758:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   original_iphdr = (struct ip_hdr *)p->payload;
 1965              		.loc 1 758 3 view .LVU622
 1966              		.loc 1 758 18 is_stmt 0 view .LVU623
 1967 0018 D5F80480 		ldr	r8, [r5, #4]
 1968              	.LVL217:
 759:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   iphdr = original_iphdr;
 1969              		.loc 1 759 3 is_stmt 1 view .LVU624
 760:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   if (IPH_HL_BYTES(iphdr) != IP_HLEN) {
 1970              		.loc 1 760 3 view .LVU625
 1971              		.loc 1 760 7 is_stmt 0 view .LVU626
 1972 001c 98F80030 		ldrb	r3, [r8]	@ zero_extendqisi2
 1973 0020 03F00F03 		and	r3, r3, #15
 1974              		.loc 1 760 6 view .LVU627
 1975 0024 052B     		cmp	r3, #5
 1976 0026 40F0D880 		bne	.L167
 761:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     /* ip4_frag() does not support IP options */
 762:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     return ERR_VAL;
 763:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   }
 764:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   LWIP_ERROR("ip4_frag(): pbuf too short", p->len >= IP_HLEN, return ERR_VAL);
 1977              		.loc 1 764 3 is_stmt 1 view .LVU628
 1978              		.loc 1 764 3 view .LVU629
 1979 002a 6B89     		ldrh	r3, [r5, #10]
 1980 002c 132B     		cmp	r3, #19
 1981 002e 14D9     		bls	.L171
 1982              		.loc 1 764 3 discriminator 2 view .LVU630
 765:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 766:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   /* Save original offset */
 767:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   tmp = lwip_ntohs(IPH_OFFSET(iphdr));
 1983              		.loc 1 767 3 view .LVU631
 1984              		.loc 1 767 9 is_stmt 0 view .LVU632
 1985 0030 B8F80600 		ldrh	r0, [r8, #6]	@ unaligned
 1986              	.LVL218:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s 			page 60


 1987              		.loc 1 767 9 view .LVU633
 1988 0034 FFF7FEFF 		bl	lwip_htons
 1989              	.LVL219:
 768:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   ofo = tmp & IP_OFFMASK;
 1990              		.loc 1 768 3 is_stmt 1 view .LVU634
 1991              		.loc 1 768 7 is_stmt 0 view .LVU635
 1992 0038 C0F30C03 		ubfx	r3, r0, #0, #13
 1993 003c 0493     		str	r3, [sp, #16]
 1994              	.LVL220:
 769:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   /* already fragmented? if so, the last fragment we create must have MF, too */
 770:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   mf_set = tmp & IP_MF;
 1995              		.loc 1 770 3 is_stmt 1 view .LVU636
 1996 003e 00F40053 		and	r3, r0, #8192
 1997 0042 0993     		str	r3, [sp, #36]
 1998              	.LVL221:
 771:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 772:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   left = (u16_t)(p->tot_len - IP_HLEN);
 1999              		.loc 1 772 3 view .LVU637
 2000              		.loc 1 772 19 is_stmt 0 view .LVU638
 2001 0044 2B89     		ldrh	r3, [r5, #8]
 2002              		.loc 1 772 8 view .LVU639
 2003 0046 143B     		subs	r3, r3, #20
 2004 0048 1FFA83FB 		uxth	fp, r3
 2005              	.LVL222:
 773:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 774:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   while (left) {
 2006              		.loc 1 774 3 is_stmt 1 view .LVU640
 754:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   u16_t tmp;
 2007              		.loc 1 754 9 is_stmt 0 view .LVU641
 2008 004c 1427     		movs	r7, #20
 745:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   u16_t left_to_copy;
 2009              		.loc 1 745 9 view .LVU642
 2010 004e 0024     		movs	r4, #0
 2011 0050 CDF808B0 		str	fp, [sp, #8]
 2012              		.loc 1 774 9 view .LVU643
 2013 0054 92E0     		b	.L155
 2014              	.LVL223:
 2015              	.L170:
 750:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   u16_t left, fragsize;
 2016              		.loc 1 750 52 view .LVU644
 2017 0056 0733     		adds	r3, r3, #7
 2018 0058 DBE7     		b	.L152
 2019              	.LVL224:
 2020              	.L171:
 764:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 2021              		.loc 1 764 3 is_stmt 1 discriminator 1 view .LVU645
 764:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 2022              		.loc 1 764 3 discriminator 1 view .LVU646
 2023 005a 634B     		ldr	r3, .L177
 2024 005c 4FF43F72 		mov	r2, #764
 2025              	.LVL225:
 764:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 2026              		.loc 1 764 3 is_stmt 0 discriminator 1 view .LVU647
 2027 0060 6249     		ldr	r1, .L177+4
 2028              	.LVL226:
 764:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 2029              		.loc 1 764 3 discriminator 1 view .LVU648
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s 			page 61


 2030 0062 6348     		ldr	r0, .L177+8
 2031              	.LVL227:
 764:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 2032              		.loc 1 764 3 discriminator 1 view .LVU649
 2033 0064 FFF7FEFF 		bl	printf
 2034              	.LVL228:
 764:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 2035              		.loc 1 764 3 is_stmt 1 discriminator 1 view .LVU650
 764:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 2036              		.loc 1 764 3 discriminator 1 view .LVU651
 2037 0068 6FF00500 		mvn	r0, #5
 764:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 2038              		.loc 1 764 3 is_stmt 0 view .LVU652
 2039 006c 42E0     		b	.L153
 2040              	.LVL229:
 2041              	.L176:
 775:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     /* Fill this fragment */
 776:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     fragsize = LWIP_MIN(left, (u16_t)(nfb * 8));
 777:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 778:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #if LWIP_NETIF_TX_SINGLE_PBUF
 779:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     rambuf = pbuf_alloc(PBUF_IP, fragsize, PBUF_RAM);
 780:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     if (rambuf == NULL) {
 781:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       goto memerr;
 782:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     }
 783:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     LWIP_ASSERT("this needs a pbuf in one piece!",
 784:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****                 (rambuf->len == rambuf->tot_len) && (rambuf->next == NULL));
 785:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     poff += pbuf_copy_partial(p, rambuf->payload, fragsize, poff);
 786:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     /* make room for the IP header */
 787:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     if (pbuf_add_header(rambuf, IP_HLEN)) {
 788:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       pbuf_free(rambuf);
 789:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       goto memerr;
 790:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     }
 791:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     /* fill in the IP header */
 792:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     SMEMCPY(rambuf->payload, original_iphdr, IP_HLEN);
 793:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     iphdr = (struct ip_hdr *)rambuf->payload;
 794:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #else /* LWIP_NETIF_TX_SINGLE_PBUF */
 795:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     /* When not using a static buffer, create a chain of pbufs.
 796:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****      * The first will be a PBUF_RAM holding the link and IP header.
 797:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****      * The rest will be PBUF_REFs mirroring the pbuf chain to be fragged,
 798:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****      * but limited to the size of an mtu.
 799:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****      */
 800:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     rambuf = pbuf_alloc(PBUF_LINK, IP_HLEN, PBUF_RAM);
 801:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     if (rambuf == NULL) {
 802:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       goto memerr;
 803:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     }
 804:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     LWIP_ASSERT("this needs a pbuf in one piece!",
 2042              		.loc 1 804 5 is_stmt 1 discriminator 1 view .LVU653
 2043              		.loc 1 804 5 discriminator 1 view .LVU654
 2044 006e 5E4B     		ldr	r3, .L177
 2045 0070 4FF44972 		mov	r2, #804
 2046 0074 5F49     		ldr	r1, .L177+12
 2047 0076 5E48     		ldr	r0, .L177+8
 2048              	.LVL230:
 2049              		.loc 1 804 5 is_stmt 0 discriminator 1 view .LVU655
 2050 0078 FFF7FEFF 		bl	printf
 2051              	.LVL231:
 2052 007c 94E0     		b	.L156
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s 			page 62


 2053              	.LVL232:
 2054              	.L174:
 2055              	.LBB8:
 805:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****                 (rambuf->len >= (IP_HLEN)));
 806:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     SMEMCPY(rambuf->payload, original_iphdr, IP_HLEN);
 807:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     iphdr = (struct ip_hdr *)rambuf->payload;
 808:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 809:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     left_to_copy = fragsize;
 810:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     while (left_to_copy) {
 811:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       struct pbuf_custom_ref *pcr;
 812:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       u16_t plen = (u16_t)(p->len - poff);
 813:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       LWIP_ASSERT("p->len >= poff", p->len >= poff);
 2056              		.loc 1 813 7 is_stmt 1 discriminator 1 view .LVU656
 2057              		.loc 1 813 7 discriminator 1 view .LVU657
 2058 007e 5A4B     		ldr	r3, .L177
 2059 0080 40F22D32 		movw	r2, #813
 2060 0084 5C49     		ldr	r1, .L177+16
 2061 0086 5A48     		ldr	r0, .L177+8
 2062 0088 FFF7FEFF 		bl	printf
 2063              	.LVL233:
 2064 008c 26E0     		b	.L158
 2065              	.LVL234:
 2066              	.L159:
 814:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       newpbuflen = LWIP_MIN(left_to_copy, plen);
 815:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       /* Is this pbuf already empty? */
 816:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       if (!newpbuflen) {
 817:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         poff = 0;
 818:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         p = p->next;
 819:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         continue;
 820:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       }
 821:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       pcr = ip_frag_alloc_pbuf_custom_ref();
 2067              		.loc 1 821 7 view .LVU658
 2068              		.loc 1 821 13 is_stmt 0 view .LVU659
 2069 008e FFF7FEFF 		bl	ip_frag_alloc_pbuf_custom_ref
 2070              	.LVL235:
 822:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       if (pcr == NULL) {
 2071              		.loc 1 822 7 is_stmt 1 view .LVU660
 2072              		.loc 1 822 10 is_stmt 0 view .LVU661
 2073 0092 8346     		mov	fp, r0
 2074 0094 48B3     		cbz	r0, .L172
 823:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         pbuf_free(rambuf);
 824:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         goto memerr;
 825:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       }
 826:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       /* Mirror this pbuf, although we might not need all of it. */
 827:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       newpbuf = pbuf_alloced_custom(PBUF_RAW, newpbuflen, PBUF_REF, &pcr->pc,
 2075              		.loc 1 827 7 is_stmt 1 view .LVU662
 828:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****                                     (u8_t *)p->payload + poff, newpbuflen);
 2076              		.loc 1 828 46 is_stmt 0 view .LVU663
 2077 0096 6B68     		ldr	r3, [r5, #4]
 827:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****                                     (u8_t *)p->payload + poff, newpbuflen);
 2078              		.loc 1 827 17 view .LVU664
 2079 0098 0194     		str	r4, [sp, #4]
 2080 009a 3B44     		add	r3, r3, r7
 2081 009c 0093     		str	r3, [sp]
 2082 009e 0346     		mov	r3, r0
 2083 00a0 4122     		movs	r2, #65
 2084 00a2 2146     		mov	r1, r4
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s 			page 63


 2085 00a4 0020     		movs	r0, #0
 2086              	.LVL236:
 827:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****                                     (u8_t *)p->payload + poff, newpbuflen);
 2087              		.loc 1 827 17 view .LVU665
 2088 00a6 FFF7FEFF 		bl	pbuf_alloced_custom
 2089              	.LVL237:
 829:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       if (newpbuf == NULL) {
 2090              		.loc 1 829 7 is_stmt 1 view .LVU666
 2091              		.loc 1 829 10 is_stmt 0 view .LVU667
 2092 00aa 8046     		mov	r8, r0
 2093 00ac 28B3     		cbz	r0, .L173
 830:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         ip_frag_free_pbuf_custom_ref(pcr);
 831:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         pbuf_free(rambuf);
 832:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         goto memerr;
 833:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       }
 834:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       pbuf_ref(p);
 2094              		.loc 1 834 7 is_stmt 1 view .LVU668
 2095 00ae 2846     		mov	r0, r5
 2096              	.LVL238:
 2097              		.loc 1 834 7 is_stmt 0 view .LVU669
 2098 00b0 FFF7FEFF 		bl	pbuf_ref
 2099              	.LVL239:
 835:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       pcr->original = p;
 2100              		.loc 1 835 7 is_stmt 1 view .LVU670
 2101              		.loc 1 835 21 is_stmt 0 view .LVU671
 2102 00b4 CBF81450 		str	r5, [fp, #20]
 836:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       pcr->pc.custom_free_function = ipfrag_free_pbuf_custom;
 2103              		.loc 1 836 7 is_stmt 1 view .LVU672
 2104              		.loc 1 836 36 is_stmt 0 view .LVU673
 2105 00b8 504B     		ldr	r3, .L177+20
 2106 00ba CBF81030 		str	r3, [fp, #16]
 837:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 838:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       /* Add it to end of rambuf's chain, but using pbuf_cat, not pbuf_chain
 839:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****        * so that it is removed when pbuf_dechain is later called on rambuf.
 840:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****        */
 841:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       pbuf_cat(rambuf, newpbuf);
 2107              		.loc 1 841 7 is_stmt 1 view .LVU674
 2108 00be 4146     		mov	r1, r8
 2109 00c0 4846     		mov	r0, r9
 2110 00c2 FFF7FEFF 		bl	pbuf_cat
 2111              	.LVL240:
 842:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       left_to_copy = (u16_t)(left_to_copy - newpbuflen);
 2112              		.loc 1 842 7 view .LVU675
 2113              		.loc 1 842 20 is_stmt 0 view .LVU676
 2114 00c6 361B     		subs	r6, r6, r4
 2115              	.LVL241:
 2116              		.loc 1 842 20 view .LVU677
 2117 00c8 B6B2     		uxth	r6, r6
 2118              	.LVL242:
 843:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       if (left_to_copy) {
 2119              		.loc 1 843 7 is_stmt 1 view .LVU678
 2120              		.loc 1 843 10 is_stmt 0 view .LVU679
 2121 00ca FEB1     		cbz	r6, .L163
 844:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         poff = 0;
 2122              		.loc 1 844 9 is_stmt 1 view .LVU680
 2123              	.LVL243:
 845:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         p = p->next;
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s 			page 64


 2124              		.loc 1 845 9 view .LVU681
 2125              		.loc 1 845 11 is_stmt 0 view .LVU682
 2126 00cc 2D68     		ldr	r5, [r5]
 2127              	.LVL244:
 2128              	.L160:
 2129              		.loc 1 845 11 view .LVU683
 2130              	.LBE8:
 741:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   struct pbuf *rambuf;
 2131              		.loc 1 741 1 view .LVU684
 2132 00ce 0027     		movs	r7, #0
 2133              	.LVL245:
 2134              	.L157:
 810:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       struct pbuf_custom_ref *pcr;
 2135              		.loc 1 810 12 is_stmt 1 view .LVU685
 2136 00d0 E6B1     		cbz	r6, .L163
 2137              	.LBB9:
 811:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       u16_t plen = (u16_t)(p->len - poff);
 2138              		.loc 1 811 7 view .LVU686
 812:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       LWIP_ASSERT("p->len >= poff", p->len >= poff);
 2139              		.loc 1 812 7 view .LVU687
 812:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       LWIP_ASSERT("p->len >= poff", p->len >= poff);
 2140              		.loc 1 812 29 is_stmt 0 view .LVU688
 2141 00d2 6B89     		ldrh	r3, [r5, #10]
 812:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       LWIP_ASSERT("p->len >= poff", p->len >= poff);
 2142              		.loc 1 812 13 view .LVU689
 2143 00d4 DC1B     		subs	r4, r3, r7
 2144              	.LVL246:
 812:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       LWIP_ASSERT("p->len >= poff", p->len >= poff);
 2145              		.loc 1 812 13 view .LVU690
 2146 00d6 A4B2     		uxth	r4, r4
 2147              	.LVL247:
 813:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       newpbuflen = LWIP_MIN(left_to_copy, plen);
 2148              		.loc 1 813 7 is_stmt 1 view .LVU691
 813:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       newpbuflen = LWIP_MIN(left_to_copy, plen);
 2149              		.loc 1 813 7 view .LVU692
 2150 00d8 BB42     		cmp	r3, r7
 2151 00da D0D3     		bcc	.L174
 2152              	.L158:
 813:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       newpbuflen = LWIP_MIN(left_to_copy, plen);
 2153              		.loc 1 813 7 discriminator 3 view .LVU693
 813:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       newpbuflen = LWIP_MIN(left_to_copy, plen);
 2154              		.loc 1 813 7 discriminator 3 view .LVU694
 814:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       /* Is this pbuf already empty? */
 2155              		.loc 1 814 7 view .LVU695
 814:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       /* Is this pbuf already empty? */
 2156              		.loc 1 814 18 is_stmt 0 view .LVU696
 2157 00dc B442     		cmp	r4, r6
 2158 00de 28BF     		it	cs
 2159 00e0 3446     		movcs	r4, r6
 2160              	.LVL248:
 816:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         poff = 0;
 2161              		.loc 1 816 7 is_stmt 1 view .LVU697
 816:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         poff = 0;
 2162              		.loc 1 816 10 is_stmt 0 view .LVU698
 2163 00e2 002C     		cmp	r4, #0
 2164 00e4 D3D1     		bne	.L159
 817:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         p = p->next;
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s 			page 65


 2165              		.loc 1 817 9 is_stmt 1 view .LVU699
 2166              	.LVL249:
 818:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         continue;
 2167              		.loc 1 818 9 view .LVU700
 818:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         continue;
 2168              		.loc 1 818 11 is_stmt 0 view .LVU701
 2169 00e6 2D68     		ldr	r5, [r5]
 2170              	.LVL250:
 819:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       }
 2171              		.loc 1 819 9 is_stmt 1 view .LVU702
 2172 00e8 F1E7     		b	.L160
 2173              	.LVL251:
 2174              	.L172:
 823:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         goto memerr;
 2175              		.loc 1 823 9 view .LVU703
 2176 00ea 4846     		mov	r0, r9
 2177              	.LVL252:
 823:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         goto memerr;
 2178              		.loc 1 823 9 is_stmt 0 view .LVU704
 2179 00ec FFF7FEFF 		bl	pbuf_free
 2180              	.LVL253:
 824:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       }
 2181              		.loc 1 824 9 is_stmt 1 view .LVU705
 2182              	.LBE9:
 846:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       }
 847:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     }
 848:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     poff = (u16_t)(poff + newpbuflen);
 849:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #endif /* LWIP_NETIF_TX_SINGLE_PBUF */
 850:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 851:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     /* Correct header */
 852:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     last = (left <= netif->mtu - IP_HLEN);
 853:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 854:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     /* Set new offset and MF flag */
 855:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     tmp = (IP_OFFMASK & (ofo));
 856:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     if (!last || mf_set) {
 857:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       /* the last fragment has MF set if the input frame had it */
 858:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       tmp = tmp | IP_MF;
 859:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     }
 860:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     IPH_OFFSET_SET(iphdr, lwip_htons(tmp));
 861:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     IPH_LEN_SET(iphdr, lwip_htons((u16_t)(fragsize + IP_HLEN)));
 862:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     IPH_CHKSUM_SET(iphdr, 0);
 863:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #if CHECKSUM_GEN_IP
 864:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     IF__NETIF_CHECKSUM_ENABLED(netif, NETIF_CHECKSUM_GEN_IP) {
 865:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       IPH_CHKSUM_SET(iphdr, inet_chksum(iphdr, IP_HLEN));
 866:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     }
 867:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #endif /* CHECKSUM_GEN_IP */
 868:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 869:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     /* No need for separate header pbuf - we allowed room for it in rambuf
 870:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****      * when allocated.
 871:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****      */
 872:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     netif->output(netif, rambuf, dest);
 873:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     IPFRAG_STATS_INC(ip_frag.xmit);
 874:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 875:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     /* Unfortunately we can't reuse rambuf - the hardware may still be
 876:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****      * using the buffer. Instead we free it (and the ensuing chain) and
 877:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****      * recreate it next time round the loop. If we're lucky the hardware
 878:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****      * will have already sent the packet, the free will really free, and
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s 			page 66


 879:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****      * there will be zero memory penalty.
 880:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****      */
 881:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 882:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     pbuf_free(rambuf);
 883:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     left = (u16_t)(left - fragsize);
 884:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     ofo = (u16_t)(ofo + nfb);
 885:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   }
 886:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   MIB2_STATS_INC(mib2.ipfragoks);
 887:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   return ERR_OK;
 888:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** memerr:
 889:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   MIB2_STATS_INC(mib2.ipfragfails);
 890:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   return ERR_MEM;
 2183              		.loc 1 890 10 is_stmt 0 view .LVU706
 2184 00f0 4FF0FF30 		mov	r0, #-1
 2185              	.LVL254:
 2186              	.L153:
 891:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** }
 2187              		.loc 1 891 1 view .LVU707
 2188 00f4 0BB0     		add	sp, sp, #44
 2189              	.LCFI12:
 2190              		.cfi_remember_state
 2191              		.cfi_def_cfa_offset 36
 2192              		@ sp needed
 2193 00f6 BDE8F08F 		pop	{r4, r5, r6, r7, r8, r9, r10, fp, pc}
 2194              	.LVL255:
 2195              	.L173:
 2196              	.LCFI13:
 2197              		.cfi_restore_state
 2198              	.LBB10:
 830:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         pbuf_free(rambuf);
 2199              		.loc 1 830 9 is_stmt 1 view .LVU708
 2200 00fa 5846     		mov	r0, fp
 2201              	.LVL256:
 830:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         pbuf_free(rambuf);
 2202              		.loc 1 830 9 is_stmt 0 view .LVU709
 2203 00fc FFF7FEFF 		bl	ip_frag_free_pbuf_custom_ref
 2204              	.LVL257:
 831:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****         goto memerr;
 2205              		.loc 1 831 9 is_stmt 1 view .LVU710
 2206 0100 4846     		mov	r0, r9
 2207 0102 FFF7FEFF 		bl	pbuf_free
 2208              	.LVL258:
 832:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       }
 2209              		.loc 1 832 9 view .LVU711
 2210              	.LBE10:
 890:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** }
 2211              		.loc 1 890 10 is_stmt 0 view .LVU712
 2212 0106 4FF0FF30 		mov	r0, #-1
 2213              	.LBB11:
 832:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       }
 2214              		.loc 1 832 9 view .LVU713
 2215 010a F3E7     		b	.L153
 2216              	.LVL259:
 2217              	.L163:
 832:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       }
 2218              		.loc 1 832 9 view .LVU714
 2219              	.LBE11:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s 			page 67


 848:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #endif /* LWIP_NETIF_TX_SINGLE_PBUF */
 2220              		.loc 1 848 10 view .LVU715
 2221 010c DDF81C80 		ldr	r8, [sp, #28]
 2222              	.LVL260:
 848:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #endif /* LWIP_NETIF_TX_SINGLE_PBUF */
 2223              		.loc 1 848 5 is_stmt 1 view .LVU716
 848:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #endif /* LWIP_NETIF_TX_SINGLE_PBUF */
 2224              		.loc 1 848 10 is_stmt 0 view .LVU717
 2225 0110 2744     		add	r7, r7, r4
 2226 0112 BFB2     		uxth	r7, r7
 2227              	.LVL261:
 852:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 2228              		.loc 1 852 5 is_stmt 1 view .LVU718
 852:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 2229              		.loc 1 852 26 is_stmt 0 view .LVU719
 2230 0114 069B     		ldr	r3, [sp, #24]
 2231 0116 9A8C     		ldrh	r2, [r3, #36]
 852:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 2232              		.loc 1 852 18 view .LVU720
 2233 0118 133A     		subs	r2, r2, #19
 2234              	.LVL262:
 855:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     if (!last || mf_set) {
 2235              		.loc 1 855 5 is_stmt 1 view .LVU721
 855:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     if (!last || mf_set) {
 2236              		.loc 1 855 9 is_stmt 0 view .LVU722
 2237 011a 049B     		ldr	r3, [sp, #16]
 2238 011c C3F30C00 		ubfx	r0, r3, #0, #13
 2239              	.LVL263:
 856:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       /* the last fragment has MF set if the input frame had it */
 2240              		.loc 1 856 5 is_stmt 1 view .LVU723
 856:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       /* the last fragment has MF set if the input frame had it */
 2241              		.loc 1 856 15 is_stmt 0 view .LVU724
 2242 0120 099B     		ldr	r3, [sp, #36]
 2243 0122 003B     		subs	r3, r3, #0
 2244 0124 18BF     		it	ne
 2245 0126 0123     		movne	r3, #1
 2246 0128 0299     		ldr	r1, [sp, #8]
 2247 012a 8A42     		cmp	r2, r1
 2248 012c D8BF     		it	le
 2249 012e 43F00103 		orrle	r3, r3, #1
 856:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       /* the last fragment has MF set if the input frame had it */
 2250              		.loc 1 856 8 view .LVU725
 2251 0132 0BB1     		cbz	r3, .L165
 858:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     }
 2252              		.loc 1 858 7 is_stmt 1 view .LVU726
 858:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     }
 2253              		.loc 1 858 11 is_stmt 0 view .LVU727
 2254 0134 40F40050 		orr	r0, r0, #8192
 2255              	.LVL264:
 2256              	.L165:
 860:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     IPH_LEN_SET(iphdr, lwip_htons((u16_t)(fragsize + IP_HLEN)));
 2257              		.loc 1 860 5 is_stmt 1 view .LVU728
 2258 0138 FFF7FEFF 		bl	lwip_htons
 2259              	.LVL265:
 860:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     IPH_LEN_SET(iphdr, lwip_htons((u16_t)(fragsize + IP_HLEN)));
 2260              		.loc 1 860 5 is_stmt 0 discriminator 1 view .LVU729
 2261 013c AAF80600 		strh	r0, [r10, #6]	@ unaligned
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s 			page 68


 861:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     IPH_CHKSUM_SET(iphdr, 0);
 2262              		.loc 1 861 5 is_stmt 1 view .LVU730
 2263 0140 039E     		ldr	r6, [sp, #12]
 2264 0142 06F11400 		add	r0, r6, #20
 2265 0146 80B2     		uxth	r0, r0
 2266 0148 FFF7FEFF 		bl	lwip_htons
 2267              	.LVL266:
 861:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     IPH_CHKSUM_SET(iphdr, 0);
 2268              		.loc 1 861 5 is_stmt 0 discriminator 1 view .LVU731
 2269 014c AAF80200 		strh	r0, [r10, #2]	@ unaligned
 862:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** #if CHECKSUM_GEN_IP
 2270              		.loc 1 862 5 is_stmt 1 view .LVU732
 2271 0150 0023     		movs	r3, #0
 2272 0152 8AF80A30 		strb	r3, [r10, #10]
 2273 0156 8AF80B30 		strb	r3, [r10, #11]
 872:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     IPFRAG_STATS_INC(ip_frag.xmit);
 2274              		.loc 1 872 5 view .LVU733
 872:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     IPFRAG_STATS_INC(ip_frag.xmit);
 2275              		.loc 1 872 10 is_stmt 0 view .LVU734
 2276 015a 0698     		ldr	r0, [sp, #24]
 2277 015c 4369     		ldr	r3, [r0, #20]
 872:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     IPFRAG_STATS_INC(ip_frag.xmit);
 2278              		.loc 1 872 5 view .LVU735
 2279 015e 089A     		ldr	r2, [sp, #32]
 2280 0160 4946     		mov	r1, r9
 2281 0162 9847     		blx	r3
 2282              	.LVL267:
 873:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 2283              		.loc 1 873 35 is_stmt 1 view .LVU736
 882:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     left = (u16_t)(left - fragsize);
 2284              		.loc 1 882 5 view .LVU737
 2285 0164 4846     		mov	r0, r9
 2286 0166 FFF7FEFF 		bl	pbuf_free
 2287              	.LVL268:
 883:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     ofo = (u16_t)(ofo + nfb);
 2288              		.loc 1 883 5 view .LVU738
 883:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     ofo = (u16_t)(ofo + nfb);
 2289              		.loc 1 883 10 is_stmt 0 view .LVU739
 2290 016a 029B     		ldr	r3, [sp, #8]
 2291 016c 9B1B     		subs	r3, r3, r6
 2292 016e 9BB2     		uxth	r3, r3
 2293 0170 0293     		str	r3, [sp, #8]
 2294              	.LVL269:
 884:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   }
 2295              		.loc 1 884 5 is_stmt 1 view .LVU740
 884:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   }
 2296              		.loc 1 884 9 is_stmt 0 view .LVU741
 2297 0172 049B     		ldr	r3, [sp, #16]
 2298 0174 059A     		ldr	r2, [sp, #20]
 2299 0176 1344     		add	r3, r3, r2
 2300 0178 9BB2     		uxth	r3, r3
 2301 017a 0493     		str	r3, [sp, #16]
 2302              	.LVL270:
 2303              	.L155:
 774:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     /* Fill this fragment */
 2304              		.loc 1 774 10 is_stmt 1 view .LVU742
 2305 017c 029B     		ldr	r3, [sp, #8]
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s 			page 69


 2306 017e 53B3     		cbz	r3, .L175
 776:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 2307              		.loc 1 776 5 view .LVU743
 776:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 2308              		.loc 1 776 16 is_stmt 0 view .LVU744
 2309 0180 059B     		ldr	r3, [sp, #20]
 2310 0182 DB00     		lsls	r3, r3, #3
 2311 0184 9BB2     		uxth	r3, r3
 776:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 2312              		.loc 1 776 14 view .LVU745
 2313 0186 029A     		ldr	r2, [sp, #8]
 2314 0188 9342     		cmp	r3, r2
 2315 018a 28BF     		it	cs
 2316 018c 1346     		movcs	r3, r2
 2317 018e 0393     		str	r3, [sp, #12]
 2318              	.LVL271:
 800:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     if (rambuf == NULL) {
 2319              		.loc 1 800 5 is_stmt 1 view .LVU746
 800:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     if (rambuf == NULL) {
 2320              		.loc 1 800 14 is_stmt 0 view .LVU747
 2321 0190 4FF42072 		mov	r2, #640
 2322 0194 1421     		movs	r1, #20
 2323 0196 0E20     		movs	r0, #14
 2324 0198 FFF7FEFF 		bl	pbuf_alloc
 2325              	.LVL272:
 801:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       goto memerr;
 2326              		.loc 1 801 5 is_stmt 1 view .LVU748
 801:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       goto memerr;
 2327              		.loc 1 801 8 is_stmt 0 view .LVU749
 2328 019c 8146     		mov	r9, r0
 2329 019e F8B1     		cbz	r0, .L168
 804:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****                 (rambuf->len >= (IP_HLEN)));
 2330              		.loc 1 804 5 is_stmt 1 view .LVU750
 804:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****                 (rambuf->len >= (IP_HLEN)));
 2331              		.loc 1 804 5 view .LVU751
 2332 01a0 4389     		ldrh	r3, [r0, #10]
 2333 01a2 132B     		cmp	r3, #19
 2334 01a4 7FF663AF 		bls	.L176
 2335              	.LVL273:
 2336              	.L156:
 804:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****                 (rambuf->len >= (IP_HLEN)));
 2337              		.loc 1 804 5 discriminator 3 view .LVU752
 804:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****                 (rambuf->len >= (IP_HLEN)));
 2338              		.loc 1 804 5 discriminator 3 view .LVU753
 806:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     iphdr = (struct ip_hdr *)rambuf->payload;
 2339              		.loc 1 806 5 view .LVU754
 2340 01a8 D9F80430 		ldr	r3, [r9, #4]
 2341 01ac D8F80020 		ldr	r2, [r8]	@ unaligned
 2342 01b0 D8F80460 		ldr	r6, [r8, #4]	@ unaligned
 2343 01b4 D8F80800 		ldr	r0, [r8, #8]	@ unaligned
 2344 01b8 D8F80C10 		ldr	r1, [r8, #12]	@ unaligned
 2345 01bc 1A60     		str	r2, [r3]	@ unaligned
 2346 01be 5E60     		str	r6, [r3, #4]	@ unaligned
 2347 01c0 9860     		str	r0, [r3, #8]	@ unaligned
 2348 01c2 D960     		str	r1, [r3, #12]	@ unaligned
 2349 01c4 D8F81020 		ldr	r2, [r8, #16]	@ unaligned
 2350 01c8 1A61     		str	r2, [r3, #16]	@ unaligned
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s 			page 70


 807:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 2351              		.loc 1 807 5 view .LVU755
 807:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** 
 2352              		.loc 1 807 11 is_stmt 0 view .LVU756
 2353 01ca D9F804A0 		ldr	r10, [r9, #4]
 2354              	.LVL274:
 809:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     while (left_to_copy) {
 2355              		.loc 1 809 5 is_stmt 1 view .LVU757
 810:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       struct pbuf_custom_ref *pcr;
 2356              		.loc 1 810 5 view .LVU758
 809:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****     while (left_to_copy) {
 2357              		.loc 1 809 18 is_stmt 0 view .LVU759
 2358 01ce 039E     		ldr	r6, [sp, #12]
 2359 01d0 CDF81C80 		str	r8, [sp, #28]
 810:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****       struct pbuf_custom_ref *pcr;
 2360              		.loc 1 810 11 view .LVU760
 2361 01d4 7CE7     		b	.L157
 2362              	.LVL275:
 2363              	.L175:
 887:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** memerr:
 2364              		.loc 1 887 10 view .LVU761
 2365 01d6 0020     		movs	r0, #0
 2366 01d8 8CE7     		b	.L153
 2367              	.LVL276:
 2368              	.L167:
 762:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   }
 2369              		.loc 1 762 12 view .LVU762
 2370 01da 6FF00500 		mvn	r0, #5
 2371              	.LVL277:
 762:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c ****   }
 2372              		.loc 1 762 12 view .LVU763
 2373 01de 89E7     		b	.L153
 2374              	.LVL278:
 2375              	.L168:
 890:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** }
 2376              		.loc 1 890 10 view .LVU764
 2377 01e0 4FF0FF30 		mov	r0, #-1
 2378              	.LVL279:
 890:Middlewares/Third_Party/LwIP/src/core/ipv4/ip4_frag.c **** }
 2379              		.loc 1 890 10 view .LVU765
 2380 01e4 86E7     		b	.L153
 2381              	.L178:
 2382 01e6 00BF     		.align	2
 2383              	.L177:
 2384 01e8 00000000 		.word	.LC0
 2385 01ec 00000000 		.word	.LC16
 2386 01f0 54000000 		.word	.LC2
 2387 01f4 1C000000 		.word	.LC17
 2388 01f8 3C000000 		.word	.LC18
 2389 01fc 00000000 		.word	ipfrag_free_pbuf_custom
 2390              		.cfi_endproc
 2391              	.LFE180:
 2393              		.section	.bss.ip_reass_pbufcount,"aw",%nobits
 2394              		.align	1
 2397              	ip_reass_pbufcount:
 2398 0000 0000     		.space	2
 2399              		.section	.bss.reassdatagrams,"aw",%nobits
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s 			page 71


 2400              		.align	2
 2403              	reassdatagrams:
 2404 0000 00000000 		.space	4
 2405              		.text
 2406              	.Letext0:
 2407              		.file 2 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin/../lib/gcc/arm-none-eabi/13.3.1/../
 2408              		.file 3 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin/../lib/gcc/arm-none-eabi/13.3.1/../
 2409              		.file 4 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin/../lib/gcc/arm-none-eabi/13.3.1/inc
 2410              		.file 5 "Middlewares/Third_Party/LwIP/src/include/lwip/arch.h"
 2411              		.file 6 "Middlewares/Third_Party/LwIP/src/include/lwip/err.h"
 2412              		.file 7 "Middlewares/Third_Party/LwIP/src/include/lwip/pbuf.h"
 2413              		.file 8 "Middlewares/Third_Party/LwIP/src/include/lwip/ip4_addr.h"
 2414              		.file 9 "Middlewares/Third_Party/LwIP/src/include/lwip/ip_addr.h"
 2415              		.file 10 "Middlewares/Third_Party/LwIP/src/include/lwip/memp.h"
 2416              		.file 11 "Middlewares/Third_Party/LwIP/src/include/lwip/netif.h"
 2417              		.file 12 "Middlewares/Third_Party/LwIP/src/include/lwip/prot/ip4.h"
 2418              		.file 13 "Middlewares/Third_Party/LwIP/src/include/lwip/ip4_frag.h"
 2419              		.file 14 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin/../lib/gcc/arm-none-eabi/13.3.1/..
 2420              		.file 15 "Middlewares/Third_Party/LwIP/src/include/lwip/def.h"
 2421              		.file 16 "Middlewares/Third_Party/LwIP/src/include/lwip/icmp.h"
 2422              		.file 17 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin/../lib/gcc/arm-none-eabi/13.3.1/..
 2423              		.file 18 "<built-in>"
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s 			page 72


DEFINED SYMBOLS
                            *ABS*:00000000 ip4_frag.c
C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s:20     .rodata.ip_reass_dequeue_datagram.str1.4:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s:31     .text.ip_reass_dequeue_datagram:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s:36     .text.ip_reass_dequeue_datagram:00000000 ip_reass_dequeue_datagram
C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s:107    .text.ip_reass_dequeue_datagram:00000038 $d
C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s:2403   .bss.reassdatagrams:00000000 reassdatagrams
C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s:115    .rodata.ip_reass_free_complete_datagram.str1.4:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s:128    .text.ip_reass_free_complete_datagram:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s:133    .text.ip_reass_free_complete_datagram:00000000 ip_reass_free_complete_datagram
C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s:369    .text.ip_reass_free_complete_datagram:000000e0 $d
C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s:2397   .bss.ip_reass_pbufcount:00000000 ip_reass_pbufcount
C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s:380    .text.ip_reass_remove_oldest_datagram:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s:385    .text.ip_reass_remove_oldest_datagram:00000000 ip_reass_remove_oldest_datagram
C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s:546    .text.ip_reass_remove_oldest_datagram:0000007c $d
C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s:551    .rodata.ip_frag_free_pbuf_custom_ref.str1.4:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s:555    .text.ip_frag_free_pbuf_custom_ref:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s:560    .text.ip_frag_free_pbuf_custom_ref:00000000 ip_frag_free_pbuf_custom_ref
C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s:604    .text.ip_frag_free_pbuf_custom_ref:00000020 $d
C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s:611    .rodata.ipfrag_free_pbuf_custom.str1.4:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s:615    .text.ipfrag_free_pbuf_custom:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s:620    .text.ipfrag_free_pbuf_custom:00000000 ipfrag_free_pbuf_custom
C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s:678    .text.ipfrag_free_pbuf_custom:00000028 $d
C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s:685    .rodata.ip_reass_chain_frag_into_datagram_and_validate.str1.4:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s:699    .text.ip_reass_chain_frag_into_datagram_and_validate:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s:704    .text.ip_reass_chain_frag_into_datagram_and_validate:00000000 ip_reass_chain_frag_into_datagram_and_validate
C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s:1145   .text.ip_reass_chain_frag_into_datagram_and_validate:00000188 $d
C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s:1155   .text.ip_frag_alloc_pbuf_custom_ref:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s:1160   .text.ip_frag_alloc_pbuf_custom_ref:00000000 ip_frag_alloc_pbuf_custom_ref
C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s:1182   .text.ip_reass_enqueue_new_datagram:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s:1187   .text.ip_reass_enqueue_new_datagram:00000000 ip_reass_enqueue_new_datagram
C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s:1285   .text.ip_reass_enqueue_new_datagram:0000005c $d
C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s:1290   .text.ip_reass_tmr:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s:1296   .text.ip_reass_tmr:00000000 ip_reass_tmr
C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s:1372   .text.ip_reass_tmr:00000028 $d
C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s:1377   .rodata.ip4_reass.str1.4:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s:1387   .text.ip4_reass:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s:1393   .text.ip4_reass:00000000 ip4_reass
C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s:1888   .text.ip4_reass:000001f4 $d
C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s:1899   .rodata.ip4_frag.str1.4:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s:1909   .text.ip4_frag:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s:1915   .text.ip4_frag:00000000 ip4_frag
C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s:2384   .text.ip4_frag:000001e8 $d
C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s:2394   .bss.ip_reass_pbufcount:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s:2400   .bss.reassdatagrams:00000000 $d

UNDEFINED SYMBOLS
memp_free
printf
icmp_time_exceeded
pbuf_clen
pbuf_free
lwip_htons
memp_malloc
memset
pbuf_remove_header
pbuf_cat
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWnL6.s 			page 73


pbuf_alloced_custom
pbuf_ref
pbuf_alloc
