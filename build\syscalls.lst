ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccik3IuW.s 			page 1


   1              		.cpu cortex-m7
   2              		.arch armv7e-m
   3              		.fpu fpv5-d16
   4              		.eabi_attribute 28, 1
   5              		.eabi_attribute 20, 1
   6              		.eabi_attribute 21, 1
   7              		.eabi_attribute 23, 3
   8              		.eabi_attribute 24, 1
   9              		.eabi_attribute 25, 1
  10              		.eabi_attribute 26, 1
  11              		.eabi_attribute 30, 1
  12              		.eabi_attribute 34, 1
  13              		.eabi_attribute 18, 4
  14              		.file	"syscalls.c"
  15              		.text
  16              	.Ltext0:
  17              		.cfi_sections	.debug_frame
  18              		.file 1 "Core/Src/syscalls.c"
  19              		.section	.text.initialise_monitor_handles,"ax",%progbits
  20              		.align	1
  21              		.global	initialise_monitor_handles
  22              		.syntax unified
  23              		.thumb
  24              		.thumb_func
  26              	initialise_monitor_handles:
  27              	.LFB170:
   1:Core/Src/syscalls.c **** /**
   2:Core/Src/syscalls.c ****  ******************************************************************************
   3:Core/Src/syscalls.c ****  * @file      syscalls.c
   4:Core/Src/syscalls.c ****  * <AUTHOR> by STM32CubeMX
   5:Core/Src/syscalls.c ****  * @brief     Minimal System calls file
   6:Core/Src/syscalls.c ****  *
   7:Core/Src/syscalls.c ****  *            For more information about which c-functions
   8:Core/Src/syscalls.c ****  *            need which of these lowlevel functions
   9:Core/Src/syscalls.c ****  *            please consult the Newlib or Picolibc libc-manual
  10:Core/Src/syscalls.c ****  ******************************************************************************
  11:Core/Src/syscalls.c ****  * @attention
  12:Core/Src/syscalls.c ****  *
  13:Core/Src/syscalls.c ****  * Copyright (c) 2020-2025 STMicroelectronics.
  14:Core/Src/syscalls.c ****  * All rights reserved.
  15:Core/Src/syscalls.c ****  *
  16:Core/Src/syscalls.c ****  * This software is licensed under terms that can be found in the LICENSE file
  17:Core/Src/syscalls.c ****  * in the root directory of this software component.
  18:Core/Src/syscalls.c ****  * If no LICENSE file comes with this software, it is provided AS-IS.
  19:Core/Src/syscalls.c ****  *
  20:Core/Src/syscalls.c ****  ******************************************************************************
  21:Core/Src/syscalls.c ****  */
  22:Core/Src/syscalls.c **** 
  23:Core/Src/syscalls.c **** /* Includes */
  24:Core/Src/syscalls.c **** #include <sys/stat.h>
  25:Core/Src/syscalls.c **** #include <stdlib.h>
  26:Core/Src/syscalls.c **** #include <errno.h>
  27:Core/Src/syscalls.c **** #include <stdio.h>
  28:Core/Src/syscalls.c **** #include <signal.h>
  29:Core/Src/syscalls.c **** #include <time.h>
  30:Core/Src/syscalls.c **** #include <sys/time.h>
  31:Core/Src/syscalls.c **** #include <sys/times.h>
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccik3IuW.s 			page 2


  32:Core/Src/syscalls.c **** 
  33:Core/Src/syscalls.c **** 
  34:Core/Src/syscalls.c **** /* Variables */
  35:Core/Src/syscalls.c **** extern int __io_putchar(int ch) __attribute__((weak));
  36:Core/Src/syscalls.c **** extern int __io_getchar(void) __attribute__((weak));
  37:Core/Src/syscalls.c **** 
  38:Core/Src/syscalls.c **** 
  39:Core/Src/syscalls.c **** char *__env[1] = { 0 };
  40:Core/Src/syscalls.c **** char **environ = __env;
  41:Core/Src/syscalls.c **** 
  42:Core/Src/syscalls.c **** 
  43:Core/Src/syscalls.c **** /* Functions */
  44:Core/Src/syscalls.c **** void initialise_monitor_handles()
  45:Core/Src/syscalls.c **** {
  28              		.loc 1 45 1 view -0
  29              		.cfi_startproc
  30              		@ args = 0, pretend = 0, frame = 0
  31              		@ frame_needed = 0, uses_anonymous_args = 0
  32              		@ link register save eliminated.
  46:Core/Src/syscalls.c **** }
  33              		.loc 1 46 1 view .LVU1
  34 0000 7047     		bx	lr
  35              		.cfi_endproc
  36              	.LFE170:
  38              		.section	.text._getpid,"ax",%progbits
  39              		.align	1
  40              		.global	_getpid
  41              		.syntax unified
  42              		.thumb
  43              		.thumb_func
  45              	_getpid:
  46              	.LFB171:
  47:Core/Src/syscalls.c **** 
  48:Core/Src/syscalls.c **** int _getpid(void)
  49:Core/Src/syscalls.c **** {
  47              		.loc 1 49 1 view -0
  48              		.cfi_startproc
  49              		@ args = 0, pretend = 0, frame = 0
  50              		@ frame_needed = 0, uses_anonymous_args = 0
  51              		@ link register save eliminated.
  50:Core/Src/syscalls.c ****   return 1;
  52              		.loc 1 50 3 view .LVU3
  51:Core/Src/syscalls.c **** }
  53              		.loc 1 51 1 is_stmt 0 view .LVU4
  54 0000 0120     		movs	r0, #1
  55 0002 7047     		bx	lr
  56              		.cfi_endproc
  57              	.LFE171:
  59              		.section	.text._kill,"ax",%progbits
  60              		.align	1
  61              		.global	_kill
  62              		.syntax unified
  63              		.thumb
  64              		.thumb_func
  66              	_kill:
  67              	.LVL0:
  68              	.LFB172:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccik3IuW.s 			page 3


  52:Core/Src/syscalls.c **** 
  53:Core/Src/syscalls.c **** int _kill(int pid, int sig)
  54:Core/Src/syscalls.c **** {
  69              		.loc 1 54 1 is_stmt 1 view -0
  70              		.cfi_startproc
  71              		@ args = 0, pretend = 0, frame = 0
  72              		@ frame_needed = 0, uses_anonymous_args = 0
  73              		@ link register save eliminated.
  55:Core/Src/syscalls.c ****   (void)pid;
  74              		.loc 1 55 3 view .LVU6
  56:Core/Src/syscalls.c ****   (void)sig;
  75              		.loc 1 56 3 view .LVU7
  57:Core/Src/syscalls.c ****   errno = EINVAL;
  76              		.loc 1 57 3 view .LVU8
  77              		.loc 1 57 9 is_stmt 0 view .LVU9
  78 0000 024B     		ldr	r3, .L4
  79 0002 1622     		movs	r2, #22
  80 0004 1A60     		str	r2, [r3]
  58:Core/Src/syscalls.c ****   return -1;
  81              		.loc 1 58 3 is_stmt 1 view .LVU10
  59:Core/Src/syscalls.c **** }
  82              		.loc 1 59 1 is_stmt 0 view .LVU11
  83 0006 4FF0FF30 		mov	r0, #-1
  84              	.LVL1:
  85              		.loc 1 59 1 view .LVU12
  86 000a 7047     		bx	lr
  87              	.L5:
  88              		.align	2
  89              	.L4:
  90 000c 00000000 		.word	errno
  91              		.cfi_endproc
  92              	.LFE172:
  94              		.section	.text._exit,"ax",%progbits
  95              		.align	1
  96              		.global	_exit
  97              		.syntax unified
  98              		.thumb
  99              		.thumb_func
 101              	_exit:
 102              	.LVL2:
 103              	.LFB173:
  60:Core/Src/syscalls.c **** 
  61:Core/Src/syscalls.c **** void _exit (int status)
  62:Core/Src/syscalls.c **** {
 104              		.loc 1 62 1 is_stmt 1 view -0
 105              		.cfi_startproc
 106              		@ Volatile: function does not return.
 107              		@ args = 0, pretend = 0, frame = 0
 108              		@ frame_needed = 0, uses_anonymous_args = 0
 109              		.loc 1 62 1 is_stmt 0 view .LVU14
 110 0000 08B5     		push	{r3, lr}
 111              	.LCFI0:
 112              		.cfi_def_cfa_offset 8
 113              		.cfi_offset 3, -8
 114              		.cfi_offset 14, -4
  63:Core/Src/syscalls.c ****   _kill(status, -1);
 115              		.loc 1 63 3 is_stmt 1 view .LVU15
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccik3IuW.s 			page 4


 116 0002 4FF0FF31 		mov	r1, #-1
 117 0006 FFF7FEFF 		bl	_kill
 118              	.LVL3:
 119              	.L7:
  64:Core/Src/syscalls.c ****   while (1) {}    /* Make sure we hang here */
 120              		.loc 1 64 3 view .LVU16
 121              		.loc 1 64 14 view .LVU17
 122              		.loc 1 64 9 view .LVU18
 123 000a FEE7     		b	.L7
 124              		.cfi_endproc
 125              	.LFE173:
 127              		.section	.text._read,"ax",%progbits
 128              		.align	1
 129              		.weak	_read
 130              		.syntax unified
 131              		.thumb
 132              		.thumb_func
 134              	_read:
 135              	.LVL4:
 136              	.LFB174:
  65:Core/Src/syscalls.c **** }
  66:Core/Src/syscalls.c **** 
  67:Core/Src/syscalls.c **** __attribute__((weak)) int _read(int file, char *ptr, int len)
  68:Core/Src/syscalls.c **** {
 137              		.loc 1 68 1 view -0
 138              		.cfi_startproc
 139              		@ args = 0, pretend = 0, frame = 0
 140              		@ frame_needed = 0, uses_anonymous_args = 0
 141              		.loc 1 68 1 is_stmt 0 view .LVU20
 142 0000 70B5     		push	{r4, r5, r6, lr}
 143              	.LCFI1:
 144              		.cfi_def_cfa_offset 16
 145              		.cfi_offset 4, -16
 146              		.cfi_offset 5, -12
 147              		.cfi_offset 6, -8
 148              		.cfi_offset 14, -4
 149 0002 0C46     		mov	r4, r1
 150 0004 1646     		mov	r6, r2
  69:Core/Src/syscalls.c ****   (void)file;
 151              		.loc 1 69 3 is_stmt 1 view .LVU21
  70:Core/Src/syscalls.c ****   int DataIdx;
 152              		.loc 1 70 3 view .LVU22
  71:Core/Src/syscalls.c **** 
  72:Core/Src/syscalls.c ****   for (DataIdx = 0; DataIdx < len; DataIdx++)
 153              		.loc 1 72 3 view .LVU23
 154              	.LVL5:
 155              		.loc 1 72 16 is_stmt 0 view .LVU24
 156 0006 0025     		movs	r5, #0
 157              		.loc 1 72 3 view .LVU25
 158 0008 06E0     		b	.L10
 159              	.LVL6:
 160              	.L11:
  73:Core/Src/syscalls.c ****   {
  74:Core/Src/syscalls.c ****     *ptr++ = __io_getchar();
 161              		.loc 1 74 5 is_stmt 1 view .LVU26
 162              		.loc 1 74 14 is_stmt 0 view .LVU27
 163 000a FFF7FEFF 		bl	__io_getchar
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccik3IuW.s 			page 5


 164              	.LVL7:
 165              		.loc 1 74 9 discriminator 1 view .LVU28
 166 000e 2146     		mov	r1, r4
 167              	.LVL8:
 168              		.loc 1 74 12 discriminator 1 view .LVU29
 169 0010 01F8010B 		strb	r0, [r1], #1
 170              	.LVL9:
  72:Core/Src/syscalls.c ****   {
 171              		.loc 1 72 43 is_stmt 1 discriminator 3 view .LVU30
 172 0014 0135     		adds	r5, r5, #1
 173              	.LVL10:
 174              		.loc 1 74 9 is_stmt 0 discriminator 1 view .LVU31
 175 0016 0C46     		mov	r4, r1
 176              	.LVL11:
 177              	.L10:
  72:Core/Src/syscalls.c ****   {
 178              		.loc 1 72 29 is_stmt 1 discriminator 1 view .LVU32
 179 0018 B542     		cmp	r5, r6
 180 001a F6DB     		blt	.L11
  75:Core/Src/syscalls.c ****   }
  76:Core/Src/syscalls.c **** 
  77:Core/Src/syscalls.c ****   return len;
 181              		.loc 1 77 3 view .LVU33
  78:Core/Src/syscalls.c **** }
 182              		.loc 1 78 1 is_stmt 0 view .LVU34
 183 001c 3046     		mov	r0, r6
 184 001e 70BD     		pop	{r4, r5, r6, pc}
 185              		.loc 1 78 1 view .LVU35
 186              		.cfi_endproc
 187              	.LFE174:
 189              		.section	.text._write,"ax",%progbits
 190              		.align	1
 191              		.weak	_write
 192              		.syntax unified
 193              		.thumb
 194              		.thumb_func
 196              	_write:
 197              	.LVL12:
 198              	.LFB175:
  79:Core/Src/syscalls.c **** 
  80:Core/Src/syscalls.c **** __attribute__((weak)) int _write(int file, char *ptr, int len)
  81:Core/Src/syscalls.c **** {
 199              		.loc 1 81 1 is_stmt 1 view -0
 200              		.cfi_startproc
 201              		@ args = 0, pretend = 0, frame = 0
 202              		@ frame_needed = 0, uses_anonymous_args = 0
 203              		.loc 1 81 1 is_stmt 0 view .LVU37
 204 0000 70B5     		push	{r4, r5, r6, lr}
 205              	.LCFI2:
 206              		.cfi_def_cfa_offset 16
 207              		.cfi_offset 4, -16
 208              		.cfi_offset 5, -12
 209              		.cfi_offset 6, -8
 210              		.cfi_offset 14, -4
 211 0002 0C46     		mov	r4, r1
 212 0004 1646     		mov	r6, r2
  82:Core/Src/syscalls.c ****   (void)file;
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccik3IuW.s 			page 6


 213              		.loc 1 82 3 is_stmt 1 view .LVU38
  83:Core/Src/syscalls.c ****   int DataIdx;
 214              		.loc 1 83 3 view .LVU39
  84:Core/Src/syscalls.c **** 
  85:Core/Src/syscalls.c ****   for (DataIdx = 0; DataIdx < len; DataIdx++)
 215              		.loc 1 85 3 view .LVU40
 216              	.LVL13:
 217              		.loc 1 85 16 is_stmt 0 view .LVU41
 218 0006 0025     		movs	r5, #0
 219              		.loc 1 85 3 view .LVU42
 220 0008 04E0     		b	.L14
 221              	.LVL14:
 222              	.L15:
  86:Core/Src/syscalls.c ****   {
  87:Core/Src/syscalls.c ****     __io_putchar(*ptr++);
 223              		.loc 1 87 5 is_stmt 1 view .LVU43
 224              		.loc 1 87 5 is_stmt 0 view .LVU44
 225 000a 14F8010B 		ldrb	r0, [r4], #1	@ zero_extendqisi2
 226              	.LVL15:
 227              		.loc 1 87 5 view .LVU45
 228 000e FFF7FEFF 		bl	__io_putchar
 229              	.LVL16:
  85:Core/Src/syscalls.c ****   {
 230              		.loc 1 85 43 is_stmt 1 discriminator 3 view .LVU46
 231 0012 0135     		adds	r5, r5, #1
 232              	.LVL17:
 233              	.L14:
  85:Core/Src/syscalls.c ****   {
 234              		.loc 1 85 29 discriminator 1 view .LVU47
 235 0014 B542     		cmp	r5, r6
 236 0016 F8DB     		blt	.L15
  88:Core/Src/syscalls.c ****   }
  89:Core/Src/syscalls.c ****   return len;
 237              		.loc 1 89 3 view .LVU48
  90:Core/Src/syscalls.c **** }
 238              		.loc 1 90 1 is_stmt 0 view .LVU49
 239 0018 3046     		mov	r0, r6
 240 001a 70BD     		pop	{r4, r5, r6, pc}
 241              		.loc 1 90 1 view .LVU50
 242              		.cfi_endproc
 243              	.LFE175:
 245              		.section	.text._close,"ax",%progbits
 246              		.align	1
 247              		.global	_close
 248              		.syntax unified
 249              		.thumb
 250              		.thumb_func
 252              	_close:
 253              	.LVL18:
 254              	.LFB176:
  91:Core/Src/syscalls.c **** 
  92:Core/Src/syscalls.c **** int _close(int file)
  93:Core/Src/syscalls.c **** {
 255              		.loc 1 93 1 is_stmt 1 view -0
 256              		.cfi_startproc
 257              		@ args = 0, pretend = 0, frame = 0
 258              		@ frame_needed = 0, uses_anonymous_args = 0
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccik3IuW.s 			page 7


 259              		@ link register save eliminated.
  94:Core/Src/syscalls.c ****   (void)file;
 260              		.loc 1 94 3 view .LVU52
  95:Core/Src/syscalls.c ****   return -1;
 261              		.loc 1 95 3 view .LVU53
  96:Core/Src/syscalls.c **** }
 262              		.loc 1 96 1 is_stmt 0 view .LVU54
 263 0000 4FF0FF30 		mov	r0, #-1
 264              	.LVL19:
 265              		.loc 1 96 1 view .LVU55
 266 0004 7047     		bx	lr
 267              		.cfi_endproc
 268              	.LFE176:
 270              		.section	.text._fstat,"ax",%progbits
 271              		.align	1
 272              		.global	_fstat
 273              		.syntax unified
 274              		.thumb
 275              		.thumb_func
 277              	_fstat:
 278              	.LVL20:
 279              	.LFB177:
  97:Core/Src/syscalls.c **** 
  98:Core/Src/syscalls.c **** 
  99:Core/Src/syscalls.c **** int _fstat(int file, struct stat *st)
 100:Core/Src/syscalls.c **** {
 280              		.loc 1 100 1 is_stmt 1 view -0
 281              		.cfi_startproc
 282              		@ args = 0, pretend = 0, frame = 0
 283              		@ frame_needed = 0, uses_anonymous_args = 0
 284              		@ link register save eliminated.
 101:Core/Src/syscalls.c ****   (void)file;
 285              		.loc 1 101 3 view .LVU57
 102:Core/Src/syscalls.c ****   st->st_mode = S_IFCHR;
 286              		.loc 1 102 3 view .LVU58
 287              		.loc 1 102 15 is_stmt 0 view .LVU59
 288 0000 4FF40053 		mov	r3, #8192
 289 0004 4B60     		str	r3, [r1, #4]
 103:Core/Src/syscalls.c ****   return 0;
 290              		.loc 1 103 3 is_stmt 1 view .LVU60
 104:Core/Src/syscalls.c **** }
 291              		.loc 1 104 1 is_stmt 0 view .LVU61
 292 0006 0020     		movs	r0, #0
 293              	.LVL21:
 294              		.loc 1 104 1 view .LVU62
 295 0008 7047     		bx	lr
 296              		.cfi_endproc
 297              	.LFE177:
 299              		.section	.text._isatty,"ax",%progbits
 300              		.align	1
 301              		.global	_isatty
 302              		.syntax unified
 303              		.thumb
 304              		.thumb_func
 306              	_isatty:
 307              	.LVL22:
 308              	.LFB178:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccik3IuW.s 			page 8


 105:Core/Src/syscalls.c **** 
 106:Core/Src/syscalls.c **** int _isatty(int file)
 107:Core/Src/syscalls.c **** {
 309              		.loc 1 107 1 is_stmt 1 view -0
 310              		.cfi_startproc
 311              		@ args = 0, pretend = 0, frame = 0
 312              		@ frame_needed = 0, uses_anonymous_args = 0
 313              		@ link register save eliminated.
 108:Core/Src/syscalls.c ****   (void)file;
 314              		.loc 1 108 3 view .LVU64
 109:Core/Src/syscalls.c ****   return 1;
 315              		.loc 1 109 3 view .LVU65
 110:Core/Src/syscalls.c **** }
 316              		.loc 1 110 1 is_stmt 0 view .LVU66
 317 0000 0120     		movs	r0, #1
 318              	.LVL23:
 319              		.loc 1 110 1 view .LVU67
 320 0002 7047     		bx	lr
 321              		.cfi_endproc
 322              	.LFE178:
 324              		.section	.text._lseek,"ax",%progbits
 325              		.align	1
 326              		.global	_lseek
 327              		.syntax unified
 328              		.thumb
 329              		.thumb_func
 331              	_lseek:
 332              	.LVL24:
 333              	.LFB179:
 111:Core/Src/syscalls.c **** 
 112:Core/Src/syscalls.c **** int _lseek(int file, int ptr, int dir)
 113:Core/Src/syscalls.c **** {
 334              		.loc 1 113 1 is_stmt 1 view -0
 335              		.cfi_startproc
 336              		@ args = 0, pretend = 0, frame = 0
 337              		@ frame_needed = 0, uses_anonymous_args = 0
 338              		@ link register save eliminated.
 114:Core/Src/syscalls.c ****   (void)file;
 339              		.loc 1 114 3 view .LVU69
 115:Core/Src/syscalls.c ****   (void)ptr;
 340              		.loc 1 115 3 view .LVU70
 116:Core/Src/syscalls.c ****   (void)dir;
 341              		.loc 1 116 3 view .LVU71
 117:Core/Src/syscalls.c ****   return 0;
 342              		.loc 1 117 3 view .LVU72
 118:Core/Src/syscalls.c **** }
 343              		.loc 1 118 1 is_stmt 0 view .LVU73
 344 0000 0020     		movs	r0, #0
 345              	.LVL25:
 346              		.loc 1 118 1 view .LVU74
 347 0002 7047     		bx	lr
 348              		.cfi_endproc
 349              	.LFE179:
 351              		.section	.text._open,"ax",%progbits
 352              		.align	1
 353              		.global	_open
 354              		.syntax unified
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccik3IuW.s 			page 9


 355              		.thumb
 356              		.thumb_func
 358              	_open:
 359              	.LVL26:
 360              	.LFB180:
 119:Core/Src/syscalls.c **** 
 120:Core/Src/syscalls.c **** int _open(char *path, int flags, ...)
 121:Core/Src/syscalls.c **** {
 361              		.loc 1 121 1 is_stmt 1 view -0
 362              		.cfi_startproc
 363              		@ args = 4, pretend = 12, frame = 0
 364              		@ frame_needed = 0, uses_anonymous_args = 1
 365              		@ link register save eliminated.
 366              		.loc 1 121 1 is_stmt 0 view .LVU76
 367 0000 0EB4     		push	{r1, r2, r3}
 368              	.LCFI3:
 369              		.cfi_def_cfa_offset 12
 370              		.cfi_offset 1, -12
 371              		.cfi_offset 2, -8
 372              		.cfi_offset 3, -4
 122:Core/Src/syscalls.c ****   (void)path;
 373              		.loc 1 122 3 is_stmt 1 view .LVU77
 123:Core/Src/syscalls.c ****   (void)flags;
 374              		.loc 1 123 3 view .LVU78
 124:Core/Src/syscalls.c ****   /* Pretend like we always fail */
 125:Core/Src/syscalls.c ****   return -1;
 375              		.loc 1 125 3 view .LVU79
 126:Core/Src/syscalls.c **** }
 376              		.loc 1 126 1 is_stmt 0 view .LVU80
 377 0002 4FF0FF30 		mov	r0, #-1
 378              	.LVL27:
 379              		.loc 1 126 1 view .LVU81
 380 0006 03B0     		add	sp, sp, #12
 381              	.LCFI4:
 382              		.cfi_restore 3
 383              		.cfi_restore 2
 384              		.cfi_restore 1
 385              		.cfi_def_cfa_offset 0
 386 0008 7047     		bx	lr
 387              		.cfi_endproc
 388              	.LFE180:
 390              		.section	.text._wait,"ax",%progbits
 391              		.align	1
 392              		.global	_wait
 393              		.syntax unified
 394              		.thumb
 395              		.thumb_func
 397              	_wait:
 398              	.LVL28:
 399              	.LFB181:
 127:Core/Src/syscalls.c **** 
 128:Core/Src/syscalls.c **** int _wait(int *status)
 129:Core/Src/syscalls.c **** {
 400              		.loc 1 129 1 is_stmt 1 view -0
 401              		.cfi_startproc
 402              		@ args = 0, pretend = 0, frame = 0
 403              		@ frame_needed = 0, uses_anonymous_args = 0
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccik3IuW.s 			page 10


 404              		@ link register save eliminated.
 130:Core/Src/syscalls.c ****   (void)status;
 405              		.loc 1 130 3 view .LVU83
 131:Core/Src/syscalls.c ****   errno = ECHILD;
 406              		.loc 1 131 3 view .LVU84
 407              		.loc 1 131 9 is_stmt 0 view .LVU85
 408 0000 024B     		ldr	r3, .L24
 409 0002 0A22     		movs	r2, #10
 410 0004 1A60     		str	r2, [r3]
 132:Core/Src/syscalls.c ****   return -1;
 411              		.loc 1 132 3 is_stmt 1 view .LVU86
 133:Core/Src/syscalls.c **** }
 412              		.loc 1 133 1 is_stmt 0 view .LVU87
 413 0006 4FF0FF30 		mov	r0, #-1
 414              	.LVL29:
 415              		.loc 1 133 1 view .LVU88
 416 000a 7047     		bx	lr
 417              	.L25:
 418              		.align	2
 419              	.L24:
 420 000c 00000000 		.word	errno
 421              		.cfi_endproc
 422              	.LFE181:
 424              		.section	.text._unlink,"ax",%progbits
 425              		.align	1
 426              		.global	_unlink
 427              		.syntax unified
 428              		.thumb
 429              		.thumb_func
 431              	_unlink:
 432              	.LVL30:
 433              	.LFB182:
 134:Core/Src/syscalls.c **** 
 135:Core/Src/syscalls.c **** int _unlink(char *name)
 136:Core/Src/syscalls.c **** {
 434              		.loc 1 136 1 is_stmt 1 view -0
 435              		.cfi_startproc
 436              		@ args = 0, pretend = 0, frame = 0
 437              		@ frame_needed = 0, uses_anonymous_args = 0
 438              		@ link register save eliminated.
 137:Core/Src/syscalls.c ****   (void)name;
 439              		.loc 1 137 3 view .LVU90
 138:Core/Src/syscalls.c ****   errno = ENOENT;
 440              		.loc 1 138 3 view .LVU91
 441              		.loc 1 138 9 is_stmt 0 view .LVU92
 442 0000 024B     		ldr	r3, .L27
 443 0002 0222     		movs	r2, #2
 444 0004 1A60     		str	r2, [r3]
 139:Core/Src/syscalls.c ****   return -1;
 445              		.loc 1 139 3 is_stmt 1 view .LVU93
 140:Core/Src/syscalls.c **** }
 446              		.loc 1 140 1 is_stmt 0 view .LVU94
 447 0006 4FF0FF30 		mov	r0, #-1
 448              	.LVL31:
 449              		.loc 1 140 1 view .LVU95
 450 000a 7047     		bx	lr
 451              	.L28:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccik3IuW.s 			page 11


 452              		.align	2
 453              	.L27:
 454 000c 00000000 		.word	errno
 455              		.cfi_endproc
 456              	.LFE182:
 458              		.section	.text._times,"ax",%progbits
 459              		.align	1
 460              		.global	_times
 461              		.syntax unified
 462              		.thumb
 463              		.thumb_func
 465              	_times:
 466              	.LVL32:
 467              	.LFB183:
 141:Core/Src/syscalls.c **** 
 142:Core/Src/syscalls.c **** clock_t _times(struct tms *buf)
 143:Core/Src/syscalls.c **** {
 468              		.loc 1 143 1 is_stmt 1 view -0
 469              		.cfi_startproc
 470              		@ args = 0, pretend = 0, frame = 0
 471              		@ frame_needed = 0, uses_anonymous_args = 0
 472              		@ link register save eliminated.
 144:Core/Src/syscalls.c ****   (void)buf;
 473              		.loc 1 144 3 view .LVU97
 145:Core/Src/syscalls.c ****   return -1;
 474              		.loc 1 145 3 view .LVU98
 146:Core/Src/syscalls.c **** }
 475              		.loc 1 146 1 is_stmt 0 view .LVU99
 476 0000 4FF0FF30 		mov	r0, #-1
 477              	.LVL33:
 478              		.loc 1 146 1 view .LVU100
 479 0004 7047     		bx	lr
 480              		.cfi_endproc
 481              	.LFE183:
 483              		.section	.text._stat,"ax",%progbits
 484              		.align	1
 485              		.global	_stat
 486              		.syntax unified
 487              		.thumb
 488              		.thumb_func
 490              	_stat:
 491              	.LVL34:
 492              	.LFB184:
 147:Core/Src/syscalls.c **** 
 148:Core/Src/syscalls.c **** int _stat(const char *file, struct stat *st)
 149:Core/Src/syscalls.c **** {
 493              		.loc 1 149 1 is_stmt 1 view -0
 494              		.cfi_startproc
 495              		@ args = 0, pretend = 0, frame = 0
 496              		@ frame_needed = 0, uses_anonymous_args = 0
 497              		@ link register save eliminated.
 150:Core/Src/syscalls.c ****   (void)file;
 498              		.loc 1 150 3 view .LVU102
 151:Core/Src/syscalls.c ****   st->st_mode = S_IFCHR;
 499              		.loc 1 151 3 view .LVU103
 500              		.loc 1 151 15 is_stmt 0 view .LVU104
 501 0000 4FF40053 		mov	r3, #8192
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccik3IuW.s 			page 12


 502 0004 4B60     		str	r3, [r1, #4]
 152:Core/Src/syscalls.c ****   return 0;
 503              		.loc 1 152 3 is_stmt 1 view .LVU105
 153:Core/Src/syscalls.c **** }
 504              		.loc 1 153 1 is_stmt 0 view .LVU106
 505 0006 0020     		movs	r0, #0
 506              	.LVL35:
 507              		.loc 1 153 1 view .LVU107
 508 0008 7047     		bx	lr
 509              		.cfi_endproc
 510              	.LFE184:
 512              		.section	.text._link,"ax",%progbits
 513              		.align	1
 514              		.global	_link
 515              		.syntax unified
 516              		.thumb
 517              		.thumb_func
 519              	_link:
 520              	.LVL36:
 521              	.LFB185:
 154:Core/Src/syscalls.c **** 
 155:Core/Src/syscalls.c **** int _link(char *old, char *new)
 156:Core/Src/syscalls.c **** {
 522              		.loc 1 156 1 is_stmt 1 view -0
 523              		.cfi_startproc
 524              		@ args = 0, pretend = 0, frame = 0
 525              		@ frame_needed = 0, uses_anonymous_args = 0
 526              		@ link register save eliminated.
 157:Core/Src/syscalls.c ****   (void)old;
 527              		.loc 1 157 3 view .LVU109
 158:Core/Src/syscalls.c ****   (void)new;
 528              		.loc 1 158 3 view .LVU110
 159:Core/Src/syscalls.c ****   errno = EMLINK;
 529              		.loc 1 159 3 view .LVU111
 530              		.loc 1 159 9 is_stmt 0 view .LVU112
 531 0000 024B     		ldr	r3, .L32
 532 0002 1F22     		movs	r2, #31
 533 0004 1A60     		str	r2, [r3]
 160:Core/Src/syscalls.c ****   return -1;
 534              		.loc 1 160 3 is_stmt 1 view .LVU113
 161:Core/Src/syscalls.c **** }
 535              		.loc 1 161 1 is_stmt 0 view .LVU114
 536 0006 4FF0FF30 		mov	r0, #-1
 537              	.LVL37:
 538              		.loc 1 161 1 view .LVU115
 539 000a 7047     		bx	lr
 540              	.L33:
 541              		.align	2
 542              	.L32:
 543 000c 00000000 		.word	errno
 544              		.cfi_endproc
 545              	.LFE185:
 547              		.section	.text._fork,"ax",%progbits
 548              		.align	1
 549              		.global	_fork
 550              		.syntax unified
 551              		.thumb
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccik3IuW.s 			page 13


 552              		.thumb_func
 554              	_fork:
 555              	.LFB186:
 162:Core/Src/syscalls.c **** 
 163:Core/Src/syscalls.c **** int _fork(void)
 164:Core/Src/syscalls.c **** {
 556              		.loc 1 164 1 is_stmt 1 view -0
 557              		.cfi_startproc
 558              		@ args = 0, pretend = 0, frame = 0
 559              		@ frame_needed = 0, uses_anonymous_args = 0
 560              		@ link register save eliminated.
 165:Core/Src/syscalls.c ****   errno = EAGAIN;
 561              		.loc 1 165 3 view .LVU117
 562              		.loc 1 165 9 is_stmt 0 view .LVU118
 563 0000 024B     		ldr	r3, .L35
 564 0002 0B22     		movs	r2, #11
 565 0004 1A60     		str	r2, [r3]
 166:Core/Src/syscalls.c ****   return -1;
 566              		.loc 1 166 3 is_stmt 1 view .LVU119
 167:Core/Src/syscalls.c **** }
 567              		.loc 1 167 1 is_stmt 0 view .LVU120
 568 0006 4FF0FF30 		mov	r0, #-1
 569 000a 7047     		bx	lr
 570              	.L36:
 571              		.align	2
 572              	.L35:
 573 000c 00000000 		.word	errno
 574              		.cfi_endproc
 575              	.LFE186:
 577              		.section	.text._execve,"ax",%progbits
 578              		.align	1
 579              		.global	_execve
 580              		.syntax unified
 581              		.thumb
 582              		.thumb_func
 584              	_execve:
 585              	.LVL38:
 586              	.LFB187:
 168:Core/Src/syscalls.c **** 
 169:Core/Src/syscalls.c **** int _execve(char *name, char **argv, char **env)
 170:Core/Src/syscalls.c **** {
 587              		.loc 1 170 1 is_stmt 1 view -0
 588              		.cfi_startproc
 589              		@ args = 0, pretend = 0, frame = 0
 590              		@ frame_needed = 0, uses_anonymous_args = 0
 591              		@ link register save eliminated.
 171:Core/Src/syscalls.c ****   (void)name;
 592              		.loc 1 171 3 view .LVU122
 172:Core/Src/syscalls.c ****   (void)argv;
 593              		.loc 1 172 3 view .LVU123
 173:Core/Src/syscalls.c ****   (void)env;
 594              		.loc 1 173 3 view .LVU124
 174:Core/Src/syscalls.c ****   errno = ENOMEM;
 595              		.loc 1 174 3 view .LVU125
 596              		.loc 1 174 9 is_stmt 0 view .LVU126
 597 0000 024B     		ldr	r3, .L38
 598 0002 0C22     		movs	r2, #12
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccik3IuW.s 			page 14


 599              	.LVL39:
 600              		.loc 1 174 9 view .LVU127
 601 0004 1A60     		str	r2, [r3]
 175:Core/Src/syscalls.c ****   return -1;
 602              		.loc 1 175 3 is_stmt 1 view .LVU128
 176:Core/Src/syscalls.c **** }
 603              		.loc 1 176 1 is_stmt 0 view .LVU129
 604 0006 4FF0FF30 		mov	r0, #-1
 605              	.LVL40:
 606              		.loc 1 176 1 view .LVU130
 607 000a 7047     		bx	lr
 608              	.L39:
 609              		.align	2
 610              	.L38:
 611 000c 00000000 		.word	errno
 612              		.cfi_endproc
 613              	.LFE187:
 615              		.global	environ
 616              		.section	.data.environ,"aw"
 617              		.align	2
 620              	environ:
 621 0000 00000000 		.word	__env
 622              		.global	__env
 623              		.section	.bss.__env,"aw",%nobits
 624              		.align	2
 627              	__env:
 628 0000 00000000 		.space	4
 629              		.weak	__io_putchar
 630              		.weak	__io_getchar
 631              		.text
 632              	.Letext0:
 633              		.file 2 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin/../lib/gcc/arm-none-eabi/13.3.1/../
 634              		.file 3 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin/../lib/gcc/arm-none-eabi/13.3.1/../
 635              		.file 4 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin/../lib/gcc/arm-none-eabi/13.3.1/../
 636              		.file 5 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin/../lib/gcc/arm-none-eabi/13.3.1/../
 637              		.file 6 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin/../lib/gcc/arm-none-eabi/13.3.1/../
 638              		.file 7 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin/../lib/gcc/arm-none-eabi/13.3.1/../
 639              		.file 8 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin/../lib/gcc/arm-none-eabi/13.3.1/../
 640              		.file 9 "Middlewares/Third_Party/LwIP/src/include/lwip/errno.h"
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccik3IuW.s 			page 15


DEFINED SYMBOLS
                            *ABS*:00000000 syscalls.c
C:\Users\<USER>\AppData\Local\Temp\ccik3IuW.s:20     .text.initialise_monitor_handles:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccik3IuW.s:26     .text.initialise_monitor_handles:00000000 initialise_monitor_handles
C:\Users\<USER>\AppData\Local\Temp\ccik3IuW.s:39     .text._getpid:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccik3IuW.s:45     .text._getpid:00000000 _getpid
C:\Users\<USER>\AppData\Local\Temp\ccik3IuW.s:60     .text._kill:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccik3IuW.s:66     .text._kill:00000000 _kill
C:\Users\<USER>\AppData\Local\Temp\ccik3IuW.s:90     .text._kill:0000000c $d
C:\Users\<USER>\AppData\Local\Temp\ccik3IuW.s:95     .text._exit:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccik3IuW.s:101    .text._exit:00000000 _exit
C:\Users\<USER>\AppData\Local\Temp\ccik3IuW.s:128    .text._read:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccik3IuW.s:134    .text._read:00000000 _read
C:\Users\<USER>\AppData\Local\Temp\ccik3IuW.s:190    .text._write:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccik3IuW.s:196    .text._write:00000000 _write
C:\Users\<USER>\AppData\Local\Temp\ccik3IuW.s:246    .text._close:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccik3IuW.s:252    .text._close:00000000 _close
C:\Users\<USER>\AppData\Local\Temp\ccik3IuW.s:271    .text._fstat:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccik3IuW.s:277    .text._fstat:00000000 _fstat
C:\Users\<USER>\AppData\Local\Temp\ccik3IuW.s:300    .text._isatty:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccik3IuW.s:306    .text._isatty:00000000 _isatty
C:\Users\<USER>\AppData\Local\Temp\ccik3IuW.s:325    .text._lseek:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccik3IuW.s:331    .text._lseek:00000000 _lseek
C:\Users\<USER>\AppData\Local\Temp\ccik3IuW.s:352    .text._open:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccik3IuW.s:358    .text._open:00000000 _open
C:\Users\<USER>\AppData\Local\Temp\ccik3IuW.s:391    .text._wait:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccik3IuW.s:397    .text._wait:00000000 _wait
C:\Users\<USER>\AppData\Local\Temp\ccik3IuW.s:420    .text._wait:0000000c $d
C:\Users\<USER>\AppData\Local\Temp\ccik3IuW.s:425    .text._unlink:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccik3IuW.s:431    .text._unlink:00000000 _unlink
C:\Users\<USER>\AppData\Local\Temp\ccik3IuW.s:454    .text._unlink:0000000c $d
C:\Users\<USER>\AppData\Local\Temp\ccik3IuW.s:459    .text._times:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccik3IuW.s:465    .text._times:00000000 _times
C:\Users\<USER>\AppData\Local\Temp\ccik3IuW.s:484    .text._stat:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccik3IuW.s:490    .text._stat:00000000 _stat
C:\Users\<USER>\AppData\Local\Temp\ccik3IuW.s:513    .text._link:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccik3IuW.s:519    .text._link:00000000 _link
C:\Users\<USER>\AppData\Local\Temp\ccik3IuW.s:543    .text._link:0000000c $d
C:\Users\<USER>\AppData\Local\Temp\ccik3IuW.s:548    .text._fork:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccik3IuW.s:554    .text._fork:00000000 _fork
C:\Users\<USER>\AppData\Local\Temp\ccik3IuW.s:573    .text._fork:0000000c $d
C:\Users\<USER>\AppData\Local\Temp\ccik3IuW.s:578    .text._execve:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccik3IuW.s:584    .text._execve:00000000 _execve
C:\Users\<USER>\AppData\Local\Temp\ccik3IuW.s:611    .text._execve:0000000c $d
C:\Users\<USER>\AppData\Local\Temp\ccik3IuW.s:620    .data.environ:00000000 environ
C:\Users\<USER>\AppData\Local\Temp\ccik3IuW.s:617    .data.environ:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\ccik3IuW.s:627    .bss.__env:00000000 __env
C:\Users\<USER>\AppData\Local\Temp\ccik3IuW.s:624    .bss.__env:00000000 $d

UNDEFINED SYMBOLS
errno
__io_getchar
__io_putchar
