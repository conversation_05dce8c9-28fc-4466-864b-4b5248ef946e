ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 1


   1              		.cpu cortex-m7
   2              		.arch armv7e-m
   3              		.fpu fpv5-d16
   4              		.eabi_attribute 28, 1
   5              		.eabi_attribute 20, 1
   6              		.eabi_attribute 21, 1
   7              		.eabi_attribute 23, 3
   8              		.eabi_attribute 24, 1
   9              		.eabi_attribute 25, 1
  10              		.eabi_attribute 26, 1
  11              		.eabi_attribute 30, 1
  12              		.eabi_attribute 34, 1
  13              		.eabi_attribute 18, 4
  14              		.file	"stm32h7xx_hal_flash_ex.c"
  15              		.text
  16              	.Ltext0:
  17              		.cfi_sections	.debug_frame
  18              		.file 1 "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c"
  19              		.section	.text.FLASH_MassErase,"ax",%progbits
  20              		.align	1
  21              		.syntax unified
  22              		.thumb
  23              		.thumb_func
  25              	FLASH_MassErase:
  26              	.LVL0:
  27              	.LFB151:
   1:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /**
   2:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   ******************************************************************************
   3:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @file    stm32h7xx_hal_flash_ex.c
   4:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * <AUTHOR> Application Team
   5:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @brief   Extended FLASH HAL module driver.
   6:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *          This file provides firmware functions to manage the following
   7:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *          functionalities of the FLASH extension peripheral:
   8:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *           + Extended programming operations functions
   9:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *
  10:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****  @verbatim
  11:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****  ==============================================================================
  12:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****                    ##### Flash Extension features #####
  13:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   ==============================================================================
  14:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
  15:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   [..] Comparing to other previous devices, the FLASH interface for STM32H7xx
  16:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****        devices contains the following additional features
  17:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
  18:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****        (+) Capacity up to 2 Mbyte with dual bank architecture supporting read-while-write
  19:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****            capability (RWW)
  20:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****        (+) Dual bank memory organization
  21:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****        (+) PCROP protection for all banks
  22:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****        (+) Global readout protection (RDP)
  23:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****        (+) Write protection
  24:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****        (+) Secure access only protection
  25:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****        (+) Bank / register swapping (when Dual-Bank)
  26:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****        (+) Cyclic Redundancy Check (CRC)
  27:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
  28:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****                         ##### How to use this driver #####
  29:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****  ==============================================================================
  30:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   [..] This driver provides functions to configure and program the FLASH memory
  31:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****        of all STM32H7xx devices. It includes
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 2


  32:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       (#) FLASH Memory Erase functions:
  33:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****            (++) Lock and Unlock the FLASH interface using HAL_FLASH_Unlock() and
  34:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****                 HAL_FLASH_Lock() functions
  35:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****            (++) Erase function: Sector erase, bank erase and dual-bank mass erase
  36:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****            (++) There are two modes of erase :
  37:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****              (+++) Polling Mode using HAL_FLASHEx_Erase()
  38:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****              (+++) Interrupt Mode using HAL_FLASHEx_Erase_IT()
  39:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
  40:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       (#) Option Bytes Programming functions: Use HAL_FLASHEx_OBProgram() to:
  41:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         (++) Set/Reset the write protection per bank
  42:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         (++) Set the Read protection Level
  43:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         (++) Set the BOR level
  44:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         (++) Program the user Option Bytes
  45:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         (++) PCROP protection configuration and control per bank
  46:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         (++) Secure area configuration and control per bank
  47:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         (++) Core Boot address configuration
  48:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         (++) TCM / AXI shared RAM configuration
  49:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         (++) CPU Frequency Boost configuration
  50:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
  51:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       (#) FLASH Memory Lock and unlock per Bank: HAL_FLASHEx_Lock_Bank1(), HAL_FLASHEx_Unlock_Bank1
  52:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****           HAL_FLASHEx_Lock_Bank2() and HAL_FLASHEx_Unlock_Bank2() functions
  53:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
  54:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       (#) FLASH CRC computation function: Use HAL_FLASHEx_ComputeCRC() to:
  55:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****           (++) Enable CRC feature
  56:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****           (++) Program the desired burst size
  57:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****           (++) Define the user Flash Area on which the CRC has be computed
  58:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****           (++) Perform the CRC computation
  59:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****           (++) Disable CRC feature
  60:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
  61:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       (#) Error correction code error functions:
  62:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****           (++) Use the HAL_FLASHEx_EnableEccCorrectionInterrupt() and HAL_FLASHEx_DisableEccCorrect
  63:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****                functions to enable and disable the FLASH ECC correction interruption.
  64:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****           (++) Use the HAL_FLASHEx_EnableEccDetectionInterrupt() and HAL_FLASHEx_DisableEccDetectio
  65:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****                functions to enable and disable the FLASH ECC Detection interruption.
  66:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****           (++) Handle ECCD interrupt by calling HAL_FLASHEx_BusFault_IRQHandler()
  67:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****           (++) Use HAL_FLASHEx_BusFault_IRQHandler() function called under BusFault_IRQHandler() in
  68:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****                to handle the ECCD interrupt.
  69:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****           (++) Use HAL_FLASHEx_GetEccInfo() function to get the flash ECC fail information.
  70:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
  71:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****  @endverbatim
  72:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   ******************************************************************************
  73:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @attention
  74:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *
  75:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * Copyright (c) 2017 STMicroelectronics.
  76:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * All rights reserved.
  77:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *
  78:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * This software is licensed under terms that can be found in the LICENSE file in
  79:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * the root directory of this software component.
  80:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * If no LICENSE file comes with this software, it is provided AS-IS.
  81:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   ******************************************************************************
  82:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   */
  83:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
  84:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /* Includes ------------------------------------------------------------------*/
  85:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #include "stm32h7xx_hal.h"
  86:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
  87:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /** @addtogroup STM32H7xx_HAL_Driver
  88:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @{
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 3


  89:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   */
  90:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
  91:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /** @defgroup FLASHEx  FLASHEx
  92:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @brief FLASH HAL Extension module driver
  93:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @{
  94:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   */
  95:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
  96:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #ifdef HAL_FLASH_MODULE_ENABLED
  97:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
  98:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /* Private typedef -----------------------------------------------------------*/
  99:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /* Private define ------------------------------------------------------------*/
 100:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /** @addtogroup FLASHEx_Private_Constants
 101:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @{
 102:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   */
 103:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #define FLASH_TIMEOUT_VALUE       50000U /* 50 s */
 104:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 105:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /**
 106:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @}
 107:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   */
 108:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /* Private macro -------------------------------------------------------------*/
 109:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /* Private variables ---------------------------------------------------------*/
 110:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /* Private function prototypes -----------------------------------------------*/
 111:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /** @defgroup FLASHEx_Private_Functions FLASHEx Private Functions
 112:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @{
 113:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   */
 114:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** static void FLASH_MassErase(uint32_t VoltageRange, uint32_t Banks);
 115:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** static void FLASH_OB_EnableWRP(uint32_t WRPSector, uint32_t Banks);
 116:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** static void FLASH_OB_DisableWRP(uint32_t WRPSector, uint32_t Bank);
 117:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** static void FLASH_OB_GetWRP(uint32_t *WRPState, uint32_t *WRPSector, uint32_t Bank);
 118:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** static void FLASH_OB_RDPConfig(uint32_t RDPLevel);
 119:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** static uint32_t FLASH_OB_GetRDP(void);
 120:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** static void FLASH_OB_PCROPConfig(uint32_t PCROConfigRDP, uint32_t PCROPStartAddr, uint32_t PCROPEnd
 121:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** static void FLASH_OB_GetPCROP(uint32_t *PCROPConfig, uint32_t *PCROPStartAddr,uint32_t *PCROPEndAdd
 122:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** static void FLASH_OB_BOR_LevelConfig(uint32_t Level);
 123:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** static uint32_t FLASH_OB_GetBOR(void);
 124:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** static void FLASH_OB_UserConfig(uint32_t UserType, uint32_t UserConfig);
 125:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** static uint32_t FLASH_OB_GetUser(void);
 126:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** static void FLASH_OB_BootAddConfig(uint32_t BootOption, uint32_t BootAddress0, uint32_t BootAddress
 127:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** static void FLASH_OB_GetBootAdd(uint32_t *BootAddress0, uint32_t *BootAddress1);
 128:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** static void FLASH_OB_SecureAreaConfig(uint32_t SecureAreaConfig, uint32_t SecureAreaStartAddr, uint
 129:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** static void FLASH_OB_GetSecureArea(uint32_t *SecureAreaConfig, uint32_t *SecureAreaStartAddr, uint3
 130:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** static void FLASH_CRC_AddSector(uint32_t Sector, uint32_t Bank);
 131:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** static void FLASH_CRC_SelectAddress(uint32_t CRCStartAddr, uint32_t CRCEndAddr, uint32_t Bank);
 132:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 133:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #if defined (DUAL_CORE)
 134:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** static void FLASH_OB_CM4BootAddConfig(uint32_t BootOption, uint32_t BootAddress0, uint32_t BootAddr
 135:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** static void FLASH_OB_GetCM4BootAdd(uint32_t *BootAddress0, uint32_t *BootAddress1);
 136:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /*DUAL_CORE*/
 137:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 138:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #if defined (FLASH_OTPBL_LOCKBL)
 139:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** static void FLASH_OB_OTP_LockConfig(uint32_t OTP_Block);
 140:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** static uint32_t FLASH_OB_OTP_GetLock(void);
 141:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /* FLASH_OTPBL_LOCKBL */
 142:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 143:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #if defined (FLASH_OPTSR2_TCM_AXI_SHARED)
 144:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** static void FLASH_OB_SharedRAM_Config(uint32_t SharedRamConfig);
 145:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** static uint32_t FLASH_OB_SharedRAM_GetConfig(void);
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 4


 146:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /* FLASH_OPTSR2_TCM_AXI_SHARED */
 147:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 148:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #if defined (FLASH_OPTSR2_CPUFREQ_BOOST)
 149:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** static void FLASH_OB_CPUFreq_BoostConfig(uint32_t FreqBoost);
 150:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** static uint32_t FLASH_OB_CPUFreq_GetBoost(void);
 151:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /* FLASH_OPTSR2_CPUFREQ_BOOST */
 152:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /**
 153:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @}
 154:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   */
 155:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 156:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /* Exported functions ---------------------------------------------------------*/
 157:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /** @defgroup FLASHEx_Exported_Functions FLASHEx Exported Functions
 158:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @{
 159:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   */
 160:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 161:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /** @defgroup FLASHEx_Exported_Functions_Group1 Extended IO operation functions
 162:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****  *  @brief   Extended IO operation functions
 163:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****  *
 164:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** @verbatim
 165:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****  ===============================================================================
 166:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****                 ##### Extended programming operation functions #####
 167:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****  ===============================================================================
 168:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     [..]
 169:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     This subsection provides a set of functions allowing to manage the Extension FLASH
 170:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     programming operations Operations.
 171:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 172:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** @endverbatim
 173:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @{
 174:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   */
 175:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /**
 176:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @brief  Perform a mass erase or erase the specified FLASH memory sectors
 177:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @param[in]  pEraseInit pointer to an FLASH_EraseInitTypeDef structure that
 178:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *         contains the configuration information for the erasing.
 179:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *
 180:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @param[out]  SectorError pointer to variable  that contains the configuration
 181:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *          information on faulty sector in case of error (0xFFFFFFFF means that all
 182:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *          the sectors have been correctly erased)
 183:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *
 184:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @retval HAL Status
 185:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   */
 186:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** HAL_StatusTypeDef HAL_FLASHEx_Erase(FLASH_EraseInitTypeDef *pEraseInit, uint32_t *SectorError)
 187:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** {
 188:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   HAL_StatusTypeDef status = HAL_OK;
 189:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   uint32_t sector_index;
 190:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 191:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   /* Check the parameters */
 192:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   assert_param(IS_FLASH_TYPEERASE(pEraseInit->TypeErase));
 193:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   assert_param(IS_FLASH_BANK(pEraseInit->Banks));
 194:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 195:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   /* Process Locked */
 196:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   __HAL_LOCK(&pFlash);
 197:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 198:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   /* Reset error code */
 199:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   pFlash.ErrorCode = HAL_FLASH_ERROR_NONE;
 200:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 201:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   /* Wait for last operation to be completed on Bank1 */
 202:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   if((pEraseInit->Banks & FLASH_BANK_1) == FLASH_BANK_1)
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 5


 203:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
 204:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     if(FLASH_WaitForLastOperation((uint32_t)FLASH_TIMEOUT_VALUE, FLASH_BANK_1) != HAL_OK)
 205:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 206:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       status = HAL_ERROR;
 207:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     }
 208:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
 209:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 210:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #if defined (DUAL_BANK)
 211:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   /* Wait for last operation to be completed on Bank2 */
 212:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   if((pEraseInit->Banks & FLASH_BANK_2) == FLASH_BANK_2)
 213:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
 214:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     if(FLASH_WaitForLastOperation((uint32_t)FLASH_TIMEOUT_VALUE, FLASH_BANK_2) != HAL_OK)
 215:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 216:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       status = HAL_ERROR;
 217:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     }
 218:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
 219:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /* DUAL_BANK */
 220:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 221:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   if(status == HAL_OK)
 222:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
 223:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     if(pEraseInit->TypeErase == FLASH_TYPEERASE_MASSERASE)
 224:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 225:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       /* Mass erase to be done */
 226:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       FLASH_MassErase(pEraseInit->VoltageRange, pEraseInit->Banks);
 227:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 228:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       /* Wait for last operation to be completed on Bank 1 */
 229:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       if((pEraseInit->Banks & FLASH_BANK_1) == FLASH_BANK_1)
 230:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       {
 231:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         if(FLASH_WaitForLastOperation((uint32_t)FLASH_TIMEOUT_VALUE, FLASH_BANK_1) != HAL_OK)
 232:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         {
 233:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****           status = HAL_ERROR;
 234:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         }
 235:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         /* if the erase operation is completed, disable the Bank1 BER Bit */
 236:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         FLASH->CR1 &= (~FLASH_CR_BER);
 237:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       }
 238:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #if defined (DUAL_BANK)
 239:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       /* Wait for last operation to be completed on Bank 2 */
 240:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       if((pEraseInit->Banks & FLASH_BANK_2) == FLASH_BANK_2)
 241:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       {
 242:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         if(FLASH_WaitForLastOperation((uint32_t)FLASH_TIMEOUT_VALUE, FLASH_BANK_2) != HAL_OK)
 243:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         {
 244:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****           status = HAL_ERROR;
 245:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         }
 246:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         /* if the erase operation is completed, disable the Bank2 BER Bit */
 247:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         FLASH->CR2 &= (~FLASH_CR_BER);
 248:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       }
 249:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /* DUAL_BANK */
 250:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     }
 251:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     else
 252:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 253:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       /*Initialization of SectorError variable*/
 254:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       *SectorError = 0xFFFFFFFFU;
 255:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 256:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       /* Erase by sector by sector to be done*/
 257:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       for(sector_index = pEraseInit->Sector; sector_index < (pEraseInit->NbSectors + pEraseInit->Se
 258:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       {
 259:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         FLASH_Erase_Sector(sector_index, pEraseInit->Banks, pEraseInit->VoltageRange);
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 6


 260:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 261:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         if((pEraseInit->Banks & FLASH_BANK_1) == FLASH_BANK_1)
 262:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         {
 263:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****           /* Wait for last operation to be completed */
 264:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****           status = FLASH_WaitForLastOperation((uint32_t)FLASH_TIMEOUT_VALUE, FLASH_BANK_1);
 265:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 266:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****           /* If the erase operation is completed, disable the SER Bit */
 267:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****           FLASH->CR1 &= (~(FLASH_CR_SER | FLASH_CR_SNB));
 268:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         }
 269:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #if defined (DUAL_BANK)
 270:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         if((pEraseInit->Banks & FLASH_BANK_2) == FLASH_BANK_2)
 271:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         {
 272:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****           /* Wait for last operation to be completed */
 273:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****           status = FLASH_WaitForLastOperation((uint32_t)FLASH_TIMEOUT_VALUE, FLASH_BANK_2);
 274:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 275:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****           /* If the erase operation is completed, disable the SER Bit */
 276:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****           FLASH->CR2 &= (~(FLASH_CR_SER | FLASH_CR_SNB));
 277:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         }
 278:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /* DUAL_BANK */
 279:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 280:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         if(status != HAL_OK)
 281:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         {
 282:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****           /* In case of error, stop erase procedure and return the faulty sector */
 283:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****           *SectorError = sector_index;
 284:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****           break;
 285:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         }
 286:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       }
 287:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     }
 288:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
 289:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 290:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   /* Process Unlocked */
 291:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   __HAL_UNLOCK(&pFlash);
 292:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 293:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   return status;
 294:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** }
 295:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 296:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /**
 297:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @brief  Perform a mass erase or erase the specified FLASH memory sectors with interrupt enabled
 298:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @param  pEraseInit pointer to an FLASH_EraseInitTypeDef structure that
 299:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *         contains the configuration information for the erasing.
 300:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *
 301:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @retval HAL Status
 302:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   */
 303:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** HAL_StatusTypeDef HAL_FLASHEx_Erase_IT(FLASH_EraseInitTypeDef *pEraseInit)
 304:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** {
 305:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   HAL_StatusTypeDef status = HAL_OK;
 306:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 307:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   /* Check the parameters */
 308:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   assert_param(IS_FLASH_TYPEERASE(pEraseInit->TypeErase));
 309:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   assert_param(IS_FLASH_BANK(pEraseInit->Banks));
 310:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 311:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   /* Process Locked */
 312:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   __HAL_LOCK(&pFlash);
 313:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 314:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   /* Reset error code */
 315:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   pFlash.ErrorCode = HAL_FLASH_ERROR_NONE;
 316:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 7


 317:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   /* Wait for last operation to be completed on Bank 1 */
 318:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   if((pEraseInit->Banks & FLASH_BANK_1) == FLASH_BANK_1)
 319:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
 320:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     if(FLASH_WaitForLastOperation((uint32_t)FLASH_TIMEOUT_VALUE, FLASH_BANK_1) != HAL_OK)
 321:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 322:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       status = HAL_ERROR;
 323:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     }
 324:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
 325:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 326:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #if defined (DUAL_BANK)
 327:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   /* Wait for last operation to be completed on Bank 2 */
 328:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   if((pEraseInit->Banks & FLASH_BANK_2) == FLASH_BANK_2)
 329:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
 330:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     if(FLASH_WaitForLastOperation((uint32_t)FLASH_TIMEOUT_VALUE, FLASH_BANK_2) != HAL_OK)
 331:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 332:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       status = HAL_ERROR;
 333:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     }
 334:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
 335:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /* DUAL_BANK */
 336:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 337:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   if (status != HAL_OK)
 338:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
 339:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* Process Unlocked */
 340:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     __HAL_UNLOCK(&pFlash);
 341:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
 342:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   else
 343:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
 344:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     if((pEraseInit->Banks & FLASH_BANK_1) == FLASH_BANK_1)
 345:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 346:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       /* Enable End of Operation and Error interrupts for Bank 1 */
 347:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #if defined (FLASH_CR_OPERRIE)
 348:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       __HAL_FLASH_ENABLE_IT_BANK1(FLASH_IT_EOP_BANK1     | FLASH_IT_WRPERR_BANK1 | FLASH_IT_PGSERR_
 349:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****                                   FLASH_IT_STRBERR_BANK1 | FLASH_IT_INCERR_BANK1 | FLASH_IT_OPERR_B
 350:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #else
 351:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       __HAL_FLASH_ENABLE_IT_BANK1(FLASH_IT_EOP_BANK1     | FLASH_IT_WRPERR_BANK1 | FLASH_IT_PGSERR_
 352:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****                                   FLASH_IT_STRBERR_BANK1 | FLASH_IT_INCERR_BANK1);
 353:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /* FLASH_CR_OPERRIE */
 354:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     }
 355:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #if defined (DUAL_BANK)
 356:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     if((pEraseInit->Banks & FLASH_BANK_2) == FLASH_BANK_2)
 357:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 358:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       /* Enable End of Operation and Error interrupts for Bank 2 */
 359:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #if defined (FLASH_CR_OPERRIE)
 360:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       __HAL_FLASH_ENABLE_IT_BANK2(FLASH_IT_EOP_BANK2     | FLASH_IT_WRPERR_BANK2 | FLASH_IT_PGSERR_
 361:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****                                   FLASH_IT_STRBERR_BANK2 | FLASH_IT_INCERR_BANK2 | FLASH_IT_OPERR_B
 362:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #else
 363:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       __HAL_FLASH_ENABLE_IT_BANK2(FLASH_IT_EOP_BANK2     | FLASH_IT_WRPERR_BANK2 | FLASH_IT_PGSERR_
 364:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****                                   FLASH_IT_STRBERR_BANK2 | FLASH_IT_INCERR_BANK2);
 365:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /* FLASH_CR_OPERRIE */
 366:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     }
 367:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /* DUAL_BANK */
 368:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 369:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     if(pEraseInit->TypeErase == FLASH_TYPEERASE_MASSERASE)
 370:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 371:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       /*Mass erase to be done*/
 372:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       if(pEraseInit->Banks == FLASH_BANK_1)
 373:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       {
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 8


 374:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         pFlash.ProcedureOnGoing = FLASH_PROC_MASSERASE_BANK1;
 375:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       }
 376:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #if defined (DUAL_BANK)
 377:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       else if(pEraseInit->Banks == FLASH_BANK_2)
 378:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       {
 379:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         pFlash.ProcedureOnGoing = FLASH_PROC_MASSERASE_BANK2;
 380:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       }
 381:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /* DUAL_BANK */
 382:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       else
 383:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       {
 384:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         pFlash.ProcedureOnGoing = FLASH_PROC_ALLBANK_MASSERASE;
 385:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       }
 386:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 387:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       FLASH_MassErase(pEraseInit->VoltageRange, pEraseInit->Banks);
 388:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     }
 389:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     else
 390:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 391:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       /* Erase by sector to be done */
 392:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #if defined (DUAL_BANK)
 393:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       if(pEraseInit->Banks == FLASH_BANK_1)
 394:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       {
 395:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         pFlash.ProcedureOnGoing = FLASH_PROC_SECTERASE_BANK1;
 396:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       }
 397:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       else
 398:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       {
 399:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         pFlash.ProcedureOnGoing = FLASH_PROC_SECTERASE_BANK2;
 400:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       }
 401:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #else
 402:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       pFlash.ProcedureOnGoing = FLASH_PROC_SECTERASE_BANK1;
 403:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /* DUAL_BANK */
 404:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 405:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       pFlash.NbSectorsToErase = pEraseInit->NbSectors;
 406:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       pFlash.Sector = pEraseInit->Sector;
 407:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       pFlash.VoltageForErase = pEraseInit->VoltageRange;
 408:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 409:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       /* Erase first sector and wait for IT */
 410:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       FLASH_Erase_Sector(pEraseInit->Sector, pEraseInit->Banks, pEraseInit->VoltageRange);
 411:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     }
 412:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
 413:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 414:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   return status;
 415:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** }
 416:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 417:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /**
 418:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @brief  Program option bytes
 419:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @param  pOBInit pointer to an FLASH_OBProgramInitTypeDef structure that
 420:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *         contains the configuration information for the programming.
 421:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *
 422:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @retval HAL Status
 423:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   */
 424:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** HAL_StatusTypeDef HAL_FLASHEx_OBProgram(FLASH_OBProgramInitTypeDef *pOBInit)
 425:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** {
 426:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   HAL_StatusTypeDef status;
 427:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 428:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   /* Check the parameters */
 429:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   assert_param(IS_OPTIONBYTE(pOBInit->OptionType));
 430:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 9


 431:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   /* Process Locked */
 432:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   __HAL_LOCK(&pFlash);
 433:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 434:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   /* Reset Error Code */
 435:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   pFlash.ErrorCode = HAL_FLASH_ERROR_NONE;
 436:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 437:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   /* Wait for last operation to be completed */
 438:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   if(FLASH_WaitForLastOperation((uint32_t)FLASH_TIMEOUT_VALUE, FLASH_BANK_1) != HAL_OK)
 439:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
 440:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     status = HAL_ERROR;
 441:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
 442:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #if defined (DUAL_BANK)
 443:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   else if(FLASH_WaitForLastOperation((uint32_t)FLASH_TIMEOUT_VALUE, FLASH_BANK_2) != HAL_OK)
 444:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
 445:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     status = HAL_ERROR;
 446:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
 447:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /* DUAL_BANK */
 448:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   else
 449:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
 450:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     status = HAL_OK;
 451:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
 452:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 453:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   if(status == HAL_OK)
 454:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
 455:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /*Write protection configuration*/
 456:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     if((pOBInit->OptionType & OPTIONBYTE_WRP) == OPTIONBYTE_WRP)
 457:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 458:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       assert_param(IS_WRPSTATE(pOBInit->WRPState));
 459:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 460:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       if(pOBInit->WRPState == OB_WRPSTATE_ENABLE)
 461:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       {
 462:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         /*Enable of Write protection on the selected Sector*/
 463:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         FLASH_OB_EnableWRP(pOBInit->WRPSector,pOBInit->Banks);
 464:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       }
 465:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       else
 466:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       {
 467:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         /*Disable of Write protection on the selected Sector*/
 468:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         FLASH_OB_DisableWRP(pOBInit->WRPSector, pOBInit->Banks);
 469:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       }
 470:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     }
 471:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 472:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* Read protection configuration */
 473:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     if((pOBInit->OptionType & OPTIONBYTE_RDP) != 0U)
 474:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 475:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       /* Configure the Read protection level */
 476:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       FLASH_OB_RDPConfig(pOBInit->RDPLevel);
 477:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     }
 478:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 479:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* User Configuration */
 480:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     if((pOBInit->OptionType & OPTIONBYTE_USER) != 0U)
 481:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 482:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       /* Configure the user option bytes */
 483:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       FLASH_OB_UserConfig(pOBInit->USERType, pOBInit->USERConfig);
 484:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     }
 485:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 486:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* PCROP Configuration */
 487:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     if((pOBInit->OptionType & OPTIONBYTE_PCROP) != 0U)
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 10


 488:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 489:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       assert_param(IS_FLASH_BANK(pOBInit->Banks));
 490:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 491:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       /*Configure the Proprietary code readout protection */
 492:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       FLASH_OB_PCROPConfig(pOBInit->PCROPConfig, pOBInit->PCROPStartAddr, pOBInit->PCROPEndAddr, pO
 493:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     }
 494:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 495:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* BOR Level configuration */
 496:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     if((pOBInit->OptionType & OPTIONBYTE_BOR) == OPTIONBYTE_BOR)
 497:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 498:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       FLASH_OB_BOR_LevelConfig(pOBInit->BORLevel);
 499:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     }
 500:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 501:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #if defined(DUAL_CORE)
 502:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* CM7 Boot Address  configuration */
 503:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     if((pOBInit->OptionType & OPTIONBYTE_CM7_BOOTADD) == OPTIONBYTE_CM7_BOOTADD)
 504:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 505:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       FLASH_OB_BootAddConfig(pOBInit->BootConfig, pOBInit->BootAddr0, pOBInit->BootAddr1);
 506:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     }
 507:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 508:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* CM4 Boot Address  configuration */
 509:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     if((pOBInit->OptionType & OPTIONBYTE_CM4_BOOTADD) == OPTIONBYTE_CM4_BOOTADD)
 510:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 511:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       FLASH_OB_CM4BootAddConfig(pOBInit->CM4BootConfig, pOBInit->CM4BootAddr0, pOBInit->CM4BootAddr
 512:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     }
 513:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #else /* Single Core*/
 514:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* Boot Address  configuration */
 515:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     if((pOBInit->OptionType & OPTIONBYTE_BOOTADD) == OPTIONBYTE_BOOTADD)
 516:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 517:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       FLASH_OB_BootAddConfig(pOBInit->BootConfig, pOBInit->BootAddr0, pOBInit->BootAddr1);
 518:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     }
 519:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /*DUAL_CORE*/
 520:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 521:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* Secure area configuration */
 522:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     if((pOBInit->OptionType & OPTIONBYTE_SECURE_AREA) == OPTIONBYTE_SECURE_AREA)
 523:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 524:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       FLASH_OB_SecureAreaConfig(pOBInit->SecureAreaConfig, pOBInit->SecureAreaStartAddr, pOBInit->S
 525:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     }
 526:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 527:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #if defined(FLASH_OTPBL_LOCKBL)
 528:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* OTP Block Lock configuration */
 529:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     if((pOBInit->OptionType & OPTIONBYTE_OTP_LOCK) == OPTIONBYTE_OTP_LOCK)
 530:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 531:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       FLASH_OB_OTP_LockConfig(pOBInit->OTPBlockLock);
 532:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     }
 533:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /* FLASH_OTPBL_LOCKBL */
 534:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 535:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #if defined(FLASH_OPTSR2_TCM_AXI_SHARED)
 536:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* TCM / AXI Shared RAM configuration */
 537:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     if((pOBInit->OptionType & OPTIONBYTE_SHARED_RAM) == OPTIONBYTE_SHARED_RAM)
 538:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 539:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       FLASH_OB_SharedRAM_Config(pOBInit->SharedRamConfig);
 540:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     }
 541:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /* FLASH_OPTSR2_TCM_AXI_SHARED */
 542:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 543:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #if defined(FLASH_OPTSR2_CPUFREQ_BOOST)
 544:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* CPU Frequency Boost configuration */
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 11


 545:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     if((pOBInit->OptionType & OPTIONBYTE_FREQ_BOOST) == OPTIONBYTE_FREQ_BOOST)
 546:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 547:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       FLASH_OB_CPUFreq_BoostConfig(pOBInit->FreqBoostState);
 548:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     }
 549:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /* FLASH_OPTSR2_CPUFREQ_BOOST */
 550:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
 551:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 552:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   /* Process Unlocked */
 553:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   __HAL_UNLOCK(&pFlash);
 554:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 555:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   return status;
 556:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** }
 557:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 558:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /**
 559:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @brief Get the Option byte configuration
 560:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @param  pOBInit pointer to an FLASH_OBProgramInitTypeDef structure that
 561:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *         contains the configuration information for the programming.
 562:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @note   The parameter Banks of the pOBInit structure must be set exclusively to FLASH_BANK_1 or
 563:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *         as this parameter is use to get the given Bank WRP, PCROP and secured area configuratio
 564:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *
 565:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @retval None
 566:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   */
 567:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** void HAL_FLASHEx_OBGetConfig(FLASH_OBProgramInitTypeDef *pOBInit)
 568:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** {
 569:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   pOBInit->OptionType = (OPTIONBYTE_USER | OPTIONBYTE_RDP | OPTIONBYTE_BOR);
 570:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 571:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   /* Get Read protection level */
 572:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   pOBInit->RDPLevel = FLASH_OB_GetRDP();
 573:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 574:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   /* Get the user option bytes */
 575:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   pOBInit->USERConfig = FLASH_OB_GetUser();
 576:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 577:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   /*Get BOR Level*/
 578:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   pOBInit->BORLevel = FLASH_OB_GetBOR();
 579:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 580:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #if defined (DUAL_BANK)
 581:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   if ((pOBInit->Banks == FLASH_BANK_1) || (pOBInit->Banks == FLASH_BANK_2))
 582:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #else
 583:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   if (pOBInit->Banks == FLASH_BANK_1)
 584:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /* DUAL_BANK */
 585:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
 586:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     pOBInit->OptionType |= (OPTIONBYTE_WRP | OPTIONBYTE_PCROP | OPTIONBYTE_SECURE_AREA);
 587:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 588:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* Get write protection on the selected area */
 589:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     FLASH_OB_GetWRP(&(pOBInit->WRPState), &(pOBInit->WRPSector), pOBInit->Banks);
 590:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 591:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* Get the Proprietary code readout protection */
 592:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     FLASH_OB_GetPCROP(&(pOBInit->PCROPConfig), &(pOBInit->PCROPStartAddr), &(pOBInit->PCROPEndAddr)
 593:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 594:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /*Get Bank Secure area*/
 595:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     FLASH_OB_GetSecureArea(&(pOBInit->SecureAreaConfig), &(pOBInit->SecureAreaStartAddr), &(pOBInit
 596:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
 597:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 598:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   /*Get Boot Address*/
 599:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   FLASH_OB_GetBootAdd(&(pOBInit->BootAddr0), &(pOBInit->BootAddr1));
 600:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #if defined(DUAL_CORE)
 601:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   pOBInit->OptionType |= OPTIONBYTE_CM7_BOOTADD | OPTIONBYTE_CM4_BOOTADD;
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 12


 602:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 603:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   /*Get CM4 Boot Address*/
 604:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   FLASH_OB_GetCM4BootAdd(&(pOBInit->CM4BootAddr0), &(pOBInit->CM4BootAddr1));
 605:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #else
 606:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   pOBInit->OptionType |= OPTIONBYTE_BOOTADD;
 607:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /*DUAL_CORE*/
 608:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 609:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #if defined (FLASH_OTPBL_LOCKBL)
 610:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   pOBInit->OptionType |= OPTIONBYTE_OTP_LOCK;
 611:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 612:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   /* Get OTP Block Lock */
 613:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   pOBInit->OTPBlockLock = FLASH_OB_OTP_GetLock();
 614:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /* FLASH_OTPBL_LOCKBL */
 615:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 616:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #if defined (FLASH_OPTSR2_TCM_AXI_SHARED)
 617:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   pOBInit->OptionType |= OPTIONBYTE_SHARED_RAM;
 618:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 619:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   /* Get TCM / AXI Shared RAM */
 620:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   pOBInit->SharedRamConfig = FLASH_OB_SharedRAM_GetConfig();
 621:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /* FLASH_OPTSR2_TCM_AXI_SHARED */
 622:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 623:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #if defined (FLASH_OPTSR2_CPUFREQ_BOOST)
 624:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   pOBInit->OptionType |= OPTIONBYTE_FREQ_BOOST;
 625:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 626:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   /* Get CPU Frequency Boost */
 627:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   pOBInit->FreqBoostState = FLASH_OB_CPUFreq_GetBoost();
 628:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /* FLASH_OPTSR2_CPUFREQ_BOOST */
 629:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** }
 630:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 631:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /**
 632:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @brief  Unlock the FLASH Bank1 control registers access
 633:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @retval HAL Status
 634:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   */
 635:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** HAL_StatusTypeDef HAL_FLASHEx_Unlock_Bank1(void)
 636:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** {
 637:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   if(READ_BIT(FLASH->CR1, FLASH_CR_LOCK) != 0U)
 638:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
 639:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* Authorize the FLASH Bank1 Registers access */
 640:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     WRITE_REG(FLASH->KEYR1, FLASH_KEY1);
 641:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     WRITE_REG(FLASH->KEYR1, FLASH_KEY2);
 642:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 643:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* Verify Flash Bank1 is unlocked */
 644:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     if (READ_BIT(FLASH->CR1, FLASH_CR_LOCK) != 0U)
 645:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 646:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       return HAL_ERROR;
 647:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     }
 648:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
 649:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 650:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   return HAL_OK;
 651:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** }
 652:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 653:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /**
 654:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @brief  Locks the FLASH Bank1 control registers access
 655:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @retval HAL Status
 656:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   */
 657:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** HAL_StatusTypeDef HAL_FLASHEx_Lock_Bank1(void)
 658:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** {
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 13


 659:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   /* Set the LOCK Bit to lock the FLASH Bank1 Registers access */
 660:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   SET_BIT(FLASH->CR1, FLASH_CR_LOCK);
 661:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   return HAL_OK;
 662:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** }
 663:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 664:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #if defined (DUAL_BANK)
 665:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /**
 666:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @brief  Unlock the FLASH Bank2 control registers access
 667:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @retval HAL Status
 668:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   */
 669:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** HAL_StatusTypeDef HAL_FLASHEx_Unlock_Bank2(void)
 670:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** {
 671:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   if(READ_BIT(FLASH->CR2, FLASH_CR_LOCK) != 0U)
 672:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
 673:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* Authorize the FLASH Bank2 Registers access */
 674:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     WRITE_REG(FLASH->KEYR2, FLASH_KEY1);
 675:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     WRITE_REG(FLASH->KEYR2, FLASH_KEY2);
 676:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 677:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* Verify Flash Bank1 is unlocked */
 678:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     if (READ_BIT(FLASH->CR2, FLASH_CR_LOCK) != 0U)
 679:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 680:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       return HAL_ERROR;
 681:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     }
 682:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
 683:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 684:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   return HAL_OK;
 685:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** }
 686:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 687:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /**
 688:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @brief  Locks the FLASH Bank2 control registers access
 689:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @retval HAL Status
 690:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   */
 691:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** HAL_StatusTypeDef HAL_FLASHEx_Lock_Bank2(void)
 692:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** {
 693:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   /* Set the LOCK Bit to lock the FLASH Bank2 Registers access */
 694:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   SET_BIT(FLASH->CR2, FLASH_CR_LOCK);
 695:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   return HAL_OK;
 696:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** }
 697:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /* DUAL_BANK */
 698:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 699:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /*
 700:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @brief  Perform a CRC computation on the specified FLASH memory area
 701:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @param  pCRCInit pointer to an FLASH_CRCInitTypeDef structure that
 702:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *         contains the configuration information for the CRC computation.
 703:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @note   CRC computation uses CRC-32 (Ethernet) polynomial 0x4C11DB7
 704:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @note   The application should avoid running a CRC on PCROP or secure-only
 705:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *         user Flash memory area since it may alter the expected CRC value.
 706:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *         A special error flag (CRC read error: CRCRDERR) can be used to
 707:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *         detect such a case.
 708:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @retval HAL Status
 709:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** */
 710:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** HAL_StatusTypeDef HAL_FLASHEx_ComputeCRC(FLASH_CRCInitTypeDef *pCRCInit, uint32_t *CRC_Result)
 711:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** {
 712:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   HAL_StatusTypeDef status;
 713:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   uint32_t sector_index;
 714:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 715:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   /* Check the parameters */
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 14


 716:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   assert_param(IS_FLASH_BANK_EXCLUSIVE(pCRCInit->Bank));
 717:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   assert_param(IS_FLASH_TYPECRC(pCRCInit->TypeCRC));
 718:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 719:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   /* Wait for OB change operation to be completed */
 720:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   status = FLASH_OB_WaitForLastOperation((uint32_t)FLASH_TIMEOUT_VALUE);
 721:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 722:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   if (status == HAL_OK)
 723:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
 724:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     if (pCRCInit->Bank == FLASH_BANK_1)
 725:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 726:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       /* Enable CRC feature */
 727:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       FLASH->CR1 |= FLASH_CR_CRC_EN;
 728:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 729:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       /* Clear CRC flags in Status Register: CRC end of calculation and CRC read error */
 730:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       FLASH->CCR1 |= (FLASH_CCR_CLR_CRCEND | FLASH_CCR_CLR_CRCRDERR);
 731:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 732:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       /* Clear current CRC result, program burst size and define memory area on which CRC has to be
 733:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       FLASH->CRCCR1 |= FLASH_CRCCR_CLEAN_CRC | pCRCInit->BurstSize | pCRCInit->TypeCRC;
 734:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 735:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       if (pCRCInit->TypeCRC == FLASH_CRC_SECTORS)
 736:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       {
 737:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         /* Clear sectors list */
 738:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         FLASH->CRCCR1 |= FLASH_CRCCR_CLEAN_SECT;
 739:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 740:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         /* Select CRC sectors */
 741:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         for(sector_index = pCRCInit->Sector; sector_index < (pCRCInit->NbSectors + pCRCInit->Sector
 742:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         {
 743:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****           FLASH_CRC_AddSector(sector_index, FLASH_BANK_1);
 744:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         }
 745:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       }
 746:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       else if (pCRCInit->TypeCRC == FLASH_CRC_BANK)
 747:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       {
 748:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         /* Enable Bank 1 CRC select bit */
 749:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         FLASH->CRCCR1 |= FLASH_CRCCR_ALL_BANK;
 750:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       }
 751:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       else
 752:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       {
 753:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         /* Select CRC start and end addresses */
 754:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         FLASH_CRC_SelectAddress(pCRCInit->CRCStartAddr, pCRCInit->CRCEndAddr, FLASH_BANK_1);
 755:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       }
 756:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 757:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       /* Start the CRC calculation */
 758:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       FLASH->CRCCR1 |= FLASH_CRCCR_START_CRC;
 759:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 760:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       /* Wait on CRC busy flag */
 761:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       status = FLASH_CRC_WaitForLastOperation((uint32_t)FLASH_TIMEOUT_VALUE, FLASH_BANK_1);
 762:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 763:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       /* Return CRC result */
 764:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       (*CRC_Result) = FLASH->CRCDATA;
 765:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 766:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       /* Disable CRC feature */
 767:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       FLASH->CR1 &= (~FLASH_CR_CRC_EN);
 768:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 769:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       /* Clear CRC flags */
 770:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       __HAL_FLASH_CLEAR_FLAG_BANK1(FLASH_FLAG_CRCEND_BANK1 | FLASH_FLAG_CRCRDERR_BANK1);
 771:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     }
 772:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #if defined (DUAL_BANK)
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 15


 773:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     else
 774:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 775:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       /* Enable CRC feature */
 776:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       FLASH->CR2 |= FLASH_CR_CRC_EN;
 777:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 778:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       /* Clear CRC flags in Status Register: CRC end of calculation and CRC read error */
 779:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       FLASH->CCR2 |= (FLASH_CCR_CLR_CRCEND | FLASH_CCR_CLR_CRCRDERR);
 780:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 781:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       /* Clear current CRC result, program burst size and define memory area on which CRC has to be
 782:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       FLASH->CRCCR2 |= FLASH_CRCCR_CLEAN_CRC | pCRCInit->BurstSize | pCRCInit->TypeCRC;
 783:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 784:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       if (pCRCInit->TypeCRC == FLASH_CRC_SECTORS)
 785:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       {
 786:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         /* Clear sectors list */
 787:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         FLASH->CRCCR2 |= FLASH_CRCCR_CLEAN_SECT;
 788:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 789:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         /* Add CRC sectors */
 790:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         for(sector_index = pCRCInit->Sector; sector_index < (pCRCInit->NbSectors + pCRCInit->Sector
 791:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         {
 792:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****           FLASH_CRC_AddSector(sector_index, FLASH_BANK_2);
 793:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         }
 794:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       }
 795:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       else if (pCRCInit->TypeCRC == FLASH_CRC_BANK)
 796:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       {
 797:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         /* Enable Bank 2 CRC select bit */
 798:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         FLASH->CRCCR2 |= FLASH_CRCCR_ALL_BANK;
 799:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       }
 800:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       else
 801:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       {
 802:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         /* Select CRC start and end addresses */
 803:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         FLASH_CRC_SelectAddress(pCRCInit->CRCStartAddr, pCRCInit->CRCEndAddr, FLASH_BANK_2);
 804:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       }
 805:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 806:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       /* Start the CRC calculation */
 807:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       FLASH->CRCCR2 |= FLASH_CRCCR_START_CRC;
 808:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 809:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       /* Wait on CRC busy flag */
 810:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       status = FLASH_CRC_WaitForLastOperation((uint32_t)FLASH_TIMEOUT_VALUE, FLASH_BANK_2);
 811:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 812:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       /* Return CRC result */
 813:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       (*CRC_Result) = FLASH->CRCDATA;
 814:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 815:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       /* Disable CRC feature */
 816:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       FLASH->CR2 &= (~FLASH_CR_CRC_EN);
 817:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 818:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       /* Clear CRC flags */
 819:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       __HAL_FLASH_CLEAR_FLAG_BANK2(FLASH_FLAG_CRCEND_BANK2 | FLASH_FLAG_CRCRDERR_BANK2);
 820:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     }
 821:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /* DUAL_BANK */
 822:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
 823:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 824:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   return status;
 825:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** }
 826:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 827:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /**
 828:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @}
 829:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   */
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 16


 830:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 831:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #if (USE_FLASH_ECC == 1U)
 832:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /** @defgroup FLASHEx_Exported_Functions_Group2 Extended ECC operation functions
 833:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *  @brief   Extended ECC operation functions
 834:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *
 835:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** @verbatim
 836:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****  ===============================================================================
 837:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****                   ##### Extended ECC operation functions #####
 838:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****  ===============================================================================
 839:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     [..]
 840:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     This subsection provides a set of functions allowing to manage the Extended FLASH
 841:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     ECC Operations.
 842:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 843:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** @endverbatim
 844:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @{
 845:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   */
 846:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 847:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /**
 848:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @brief  Enable ECC correction interrupts on FLASH BANK1 and BANK2.
 849:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @param  None
 850:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @retval None
 851:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   */
 852:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** void HAL_FLASHEx_EnableEccCorrectionInterrupt(void)
 853:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** {
 854:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   __HAL_FLASH_ENABLE_IT(FLASH_IT_SNECCERR_BANK1);
 855:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 856:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #if defined (DUAL_BANK)
 857:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   __HAL_FLASH_ENABLE_IT(FLASH_IT_SNECCERR_BANK2);
 858:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /* DUAL_BANK */
 859:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** }
 860:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 861:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /**
 862:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @brief  Disable ECC correction interrupts on FLASH BANK1 and BANK2.
 863:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @param  None
 864:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @retval None
 865:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   */
 866:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** void HAL_FLASHEx_DisableEccCorrectionInterrupt(void)
 867:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** {
 868:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   __HAL_FLASH_DISABLE_IT(FLASH_IT_SNECCERR_BANK1);
 869:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 870:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #if defined (DUAL_BANK)
 871:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   __HAL_FLASH_DISABLE_IT(FLASH_IT_SNECCERR_BANK2);
 872:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /* DUAL_BANK */
 873:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** }
 874:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 875:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /**
 876:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @brief  Enable ECC correction interrupt on FLASH BANK1.
 877:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @param  None
 878:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @retval None
 879:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   */
 880:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** void HAL_FLASHEx_EnableEccCorrectionInterrupt_Bank1(void)
 881:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** {
 882:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   __HAL_FLASH_ENABLE_IT(FLASH_IT_SNECCERR_BANK1);
 883:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** }
 884:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 885:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /**
 886:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @brief  Disable ECC correction interrupt on FLASH BANK1.
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 17


 887:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @param  None
 888:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @retval None
 889:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   */
 890:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** void HAL_FLASHEx_DisableEccCorrectionInterrupt_Bank1(void)
 891:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** {
 892:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   __HAL_FLASH_DISABLE_IT(FLASH_IT_SNECCERR_BANK1);
 893:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** }
 894:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 895:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #if defined (DUAL_BANK)
 896:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /**
 897:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @brief  Enable ECC correction interrupt on FLASH BANK2.
 898:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @param  None
 899:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @retval None
 900:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   */
 901:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** void HAL_FLASHEx_EnableEccCorrectionInterrupt_Bank2(void)
 902:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** {
 903:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   __HAL_FLASH_ENABLE_IT(FLASH_IT_SNECCERR_BANK2);
 904:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** }
 905:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 906:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /**
 907:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @brief  Disable ECC correction interrupt on FLASH BANK2.
 908:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @param  None
 909:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @retval None
 910:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   */
 911:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** void HAL_FLASHEx_DisableEccCorrectionInterrupt_Bank2(void)
 912:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** {
 913:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   __HAL_FLASH_DISABLE_IT(FLASH_IT_SNECCERR_BANK2);
 914:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** }
 915:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /* DUAL_BANK */
 916:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 917:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /**
 918:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @brief  Enable ECC Detection interrupts on FLASH BANK1 and BANK2.
 919:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @param  None
 920:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @retval None
 921:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   */
 922:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** void HAL_FLASHEx_EnableEccDetectionInterrupt(void)
 923:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** {
 924:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   __HAL_FLASH_ENABLE_IT(FLASH_IT_DBECCERR_BANK1);
 925:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 926:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #if defined (DUAL_BANK)
 927:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   __HAL_FLASH_ENABLE_IT(FLASH_IT_DBECCERR_BANK2);
 928:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /* DUAL_BANK */
 929:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** }
 930:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 931:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /**
 932:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @brief  Disable ECC Detection interrupts on FLASH BANK1 and BANK2.
 933:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @param  None
 934:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @retval None
 935:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   */
 936:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** void HAL_FLASHEx_DisableEccDetectionInterrupt(void)
 937:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** {
 938:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   __HAL_FLASH_DISABLE_IT(FLASH_IT_DBECCERR_BANK1);
 939:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 940:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #if defined (DUAL_BANK)
 941:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   __HAL_FLASH_DISABLE_IT(FLASH_IT_DBECCERR_BANK2);
 942:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /* DUAL_BANK */
 943:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** }
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 18


 944:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 945:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /**
 946:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @brief  Enable ECC Detection interrupt on FLASH BANK1.
 947:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @param  None
 948:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @retval None
 949:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   */
 950:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** void HAL_FLASHEx_EnableEccDetectionInterrupt_Bank1(void)
 951:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** {
 952:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   __HAL_FLASH_ENABLE_IT(FLASH_IT_DBECCERR_BANK1);
 953:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** }
 954:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 955:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /**
 956:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @brief  Disable ECC correction interrupt on FLASH BANK1.
 957:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @param  None
 958:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @retval None
 959:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   */
 960:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** void HAL_FLASHEx_DisableEccDetectionInterrupt_Bank1(void)
 961:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** {
 962:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   __HAL_FLASH_DISABLE_IT(FLASH_IT_DBECCERR_BANK1);
 963:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** }
 964:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 965:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #if defined (DUAL_BANK)
 966:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /**
 967:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @brief  Enable ECC Detection interrupt on FLASH BANK2.
 968:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @param  None
 969:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @retval None
 970:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   */
 971:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** void HAL_FLASHEx_EnableEccDetectionInterrupt_Bank2(void)
 972:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** {
 973:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   __HAL_FLASH_ENABLE_IT(FLASH_IT_DBECCERR_BANK2);
 974:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** }
 975:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 976:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /**
 977:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @brief  Disable ECC Detection interrupt on FLASH BANK2.
 978:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @param  None
 979:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @retval None
 980:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   */
 981:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** void HAL_FLASHEx_DisableEccDetectionInterrupt_Bank2(void)
 982:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** {
 983:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   __HAL_FLASH_DISABLE_IT(FLASH_IT_DBECCERR_BANK2);
 984:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** }
 985:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /* DUAL_BANK */
 986:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 987:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /**
 988:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @brief  Get the ECC error information.
 989:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @param  pData Pointer to an FLASH_EccInfoTypeDef structure that contains the
 990:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *         ECC error information.
 991:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @note   This function should be called before ECC bit is cleared
 992:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *         (in callback function)
 993:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @retval None
 994:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   */
 995:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** void HAL_FLASHEx_GetEccInfo(FLASH_EccInfoTypeDef *pData)
 996:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** {
 997:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   uint32_t errorflag;
 998:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 999:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   /* Check FLASH Bank1 ECC single correction and double detection error flags */
1000:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   errorflag = FLASH->SR1 & (FLASH_FLAG_SNECCERR_BANK1 | FLASH_FLAG_DBECCERR_BANK1);
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 19


1001:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   if(errorflag != 0U)
1002:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
1003:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     pData->Area = FLASH_ECC_AREA_USER_BANK1;
1004:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     pData->Address = ((((FLASH->ECC_FA1 & FLASH_ECC_FA_FAIL_ECC_ADDR))* FLASH_NB_32BITWORD_IN_FLASH
1005:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
1006:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #if defined (DUAL_BANK)
1007:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   /* Check FLASH Bank2 ECC single correction and double detection error flags */
1008:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   errorflag = FLASH->SR2 & (FLASH_FLAG_SNECCERR_BANK2 | FLASH_FLAG_DBECCERR_BANK2);
1009:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   if(errorflag != 0U)
1010:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
1011:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     pData->Area = FLASH_ECC_AREA_USER_BANK2;
1012:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     pData->Address = ((((FLASH->ECC_FA2 & FLASH_ECC_FA_FAIL_ECC_ADDR))* FLASH_NB_32BITWORD_IN_FLASH
1013:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
1014:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /* DUAL_BANK */
1015:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** }
1016:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1017:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /**
1018:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @brief Handle Flash ECC Detection interrupt request.
1019:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @retval None
1020:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   */
1021:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** void HAL_FLASHEx_BusFault_IRQHandler(void)
1022:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** {
1023:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   /* Check if the ECC double error occurred*/
1024:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   if ((FLASH->SR1 & FLASH_FLAG_DBECCERR_BANK1)  != 0)
1025:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
1026:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* FLASH ECC detection user callback */
1027:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     HAL_FLASHEx_EccDetectionCallback();
1028:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1029:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* Clear Bank 1 ECC double detection error flag
1030:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     note : this step will clear all the information related to the flash ECC detection
1031:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     */
1032:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     __HAL_FLASH_CLEAR_FLAG_BANK1(FLASH_FLAG_DBECCERR_BANK1);
1033:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
1034:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #if defined (DUAL_BANK)
1035:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   /* Check if the ECC double error occurred*/
1036:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   if ((FLASH->SR2 & FLASH_FLAG_DBECCERR_BANK2)  != 0)
1037:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
1038:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* FLASH ECC detection user callback */
1039:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     HAL_FLASHEx_EccDetectionCallback();
1040:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1041:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* Clear Bank 2 ECC double detection error flag
1042:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     note : this step will clear all the information related to the flash ECC detection
1043:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     */
1044:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     __HAL_FLASH_CLEAR_FLAG_BANK2(FLASH_FLAG_DBECCERR_BANK2);
1045:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
1046:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /* DUAL_BANK */
1047:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** }
1048:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1049:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /**
1050:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @brief  FLASH ECC Correction interrupt callback.
1051:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @retval None
1052:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   */
1053:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** __weak void HAL_FLASHEx_EccCorrectionCallback(void)
1054:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** {
1055:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   /* NOTE : This function should not be modified, when the callback is needed,
1056:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****             the HAL_FLASHEx_EccCorrectionCallback could be implemented in the user file
1057:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****    */
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 20


1058:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** }
1059:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1060:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /**
1061:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @brief  FLASH ECC Detection interrupt callback.
1062:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @retval None
1063:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   */
1064:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** __weak void HAL_FLASHEx_EccDetectionCallback(void)
1065:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** {
1066:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   /* NOTE : This function should not be modified, when the callback is needed,
1067:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****             the HAL_FLASHEx_EccDetectionCallback could be implemented in the user file
1068:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****    */
1069:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** }
1070:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1071:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /**
1072:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @}
1073:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   */
1074:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /* USE_FLASH_ECC */
1075:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1076:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /**
1077:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @}
1078:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   */
1079:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1080:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /* Private functions ---------------------------------------------------------*/
1081:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1082:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /** @addtogroup FLASHEx_Private_Functions
1083:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @{
1084:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   */
1085:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1086:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /**
1087:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @brief  Mass erase of FLASH memory
1088:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @param  VoltageRange The device program/erase parallelism.
1089:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *          This parameter can be one of the following values:
1090:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *            @arg FLASH_VOLTAGE_RANGE_1 : Flash program/erase by 8 bits
1091:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *            @arg FLASH_VOLTAGE_RANGE_2 : Flash program/erase by 16 bits
1092:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *            @arg FLASH_VOLTAGE_RANGE_3 : Flash program/erase by 32 bits
1093:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *            @arg FLASH_VOLTAGE_RANGE_4 : Flash program/erase by 64 bits
1094:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *
1095:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @param  Banks Banks to be erased
1096:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *          This parameter can be one of the following values:
1097:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *            @arg FLASH_BANK_1: Bank1 to be erased
1098:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *            @arg FLASH_BANK_2: Bank2 to be erased
1099:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *            @arg FLASH_BANK_BOTH: Bank1 and Bank2 to be erased
1100:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *
1101:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @retval HAL Status
1102:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   */
1103:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** static void FLASH_MassErase(uint32_t VoltageRange, uint32_t Banks)
1104:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** {
  28              		.loc 1 1104 1 view -0
  29              		.cfi_startproc
  30              		@ args = 0, pretend = 0, frame = 0
  31              		@ frame_needed = 0, uses_anonymous_args = 0
  32              		@ link register save eliminated.
1105:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   /* Check the parameters */
1106:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #if defined (FLASH_CR_PSIZE)
1107:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   assert_param(IS_VOLTAGERANGE(VoltageRange));
  33              		.loc 1 1107 3 view .LVU1
1108:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #else
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 21


1109:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   UNUSED(VoltageRange);
1110:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /* FLASH_CR_PSIZE */
1111:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   assert_param(IS_FLASH_BANK(Banks));
  34              		.loc 1 1111 3 view .LVU2
1112:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1113:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #if defined (DUAL_BANK)
1114:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   /* Flash Mass Erase */
1115:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   if((Banks & FLASH_BANK_BOTH) == FLASH_BANK_BOTH)
1116:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
1117:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #if defined (FLASH_CR_PSIZE)
1118:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* Reset Program/erase VoltageRange for Bank1 and Bank2 */
1119:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     FLASH->CR1 &= (~FLASH_CR_PSIZE);
1120:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     FLASH->CR2 &= (~FLASH_CR_PSIZE);
1121:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1122:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* Set voltage range */
1123:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     FLASH->CR1 |= VoltageRange;
1124:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     FLASH->CR2 |= VoltageRange;
1125:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /* FLASH_CR_PSIZE */
1126:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1127:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* Set Mass Erase Bit */
1128:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     FLASH->OPTCR |= FLASH_OPTCR_MER;
1129:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
1130:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   else
1131:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /* DUAL_BANK */
1132:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
1133:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* Proceed to erase Flash Bank  */
1134:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     if((Banks & FLASH_BANK_1) == FLASH_BANK_1)
  35              		.loc 1 1134 5 view .LVU3
  36              		.loc 1 1134 7 is_stmt 0 view .LVU4
  37 0000 11F0010F 		tst	r1, #1
  38 0004 0BD0     		beq	.L1
1135:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
1136:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #if defined (FLASH_CR_PSIZE)
1137:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       /* Set Program/erase VoltageRange for Bank1 */
1138:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       FLASH->CR1 &= (~FLASH_CR_PSIZE);
  39              		.loc 1 1138 7 is_stmt 1 view .LVU5
  40              		.loc 1 1138 12 is_stmt 0 view .LVU6
  41 0006 064B     		ldr	r3, .L3
  42 0008 DA68     		ldr	r2, [r3, #12]
  43              		.loc 1 1138 18 view .LVU7
  44 000a 22F03002 		bic	r2, r2, #48
  45 000e DA60     		str	r2, [r3, #12]
1139:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       FLASH->CR1 |=  VoltageRange;
  46              		.loc 1 1139 7 is_stmt 1 view .LVU8
  47              		.loc 1 1139 12 is_stmt 0 view .LVU9
  48 0010 DA68     		ldr	r2, [r3, #12]
  49              		.loc 1 1139 18 view .LVU10
  50 0012 0243     		orrs	r2, r2, r0
  51 0014 DA60     		str	r2, [r3, #12]
1140:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /* FLASH_CR_PSIZE */
1141:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1142:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       /* Erase Bank1 */
1143:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       FLASH->CR1 |= (FLASH_CR_BER | FLASH_CR_START);
  52              		.loc 1 1143 7 is_stmt 1 view .LVU11
  53              		.loc 1 1143 12 is_stmt 0 view .LVU12
  54 0016 DA68     		ldr	r2, [r3, #12]
  55              		.loc 1 1143 18 view .LVU13
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 22


  56 0018 42F08802 		orr	r2, r2, #136
  57 001c DA60     		str	r2, [r3, #12]
  58              	.L1:
1144:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     }
1145:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1146:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #if defined (DUAL_BANK)
1147:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     if((Banks & FLASH_BANK_2) == FLASH_BANK_2)
1148:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
1149:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #if defined (FLASH_CR_PSIZE)
1150:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       /* Set Program/erase VoltageRange for Bank2 */
1151:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       FLASH->CR2 &= (~FLASH_CR_PSIZE);
1152:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       FLASH->CR2 |= VoltageRange;
1153:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /* FLASH_CR_PSIZE */
1154:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1155:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       /* Erase Bank2 */
1156:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       FLASH->CR2 |= (FLASH_CR_BER | FLASH_CR_START);
1157:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     }
1158:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /* DUAL_BANK */
1159:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
1160:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** }
  59              		.loc 1 1160 1 view .LVU14
  60 001e 7047     		bx	lr
  61              	.L4:
  62              		.align	2
  63              	.L3:
  64 0020 ******** 		.word	**********
  65              		.cfi_endproc
  66              	.LFE151:
  68              		.section	.text.FLASH_OB_EnableWRP,"ax",%progbits
  69              		.align	1
  70              		.syntax unified
  71              		.thumb
  72              		.thumb_func
  74              	FLASH_OB_EnableWRP:
  75              	.LVL1:
  76              	.LFB153:
1161:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1162:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /**
1163:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @brief  Erase the specified FLASH memory sector
1164:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @param  Sector FLASH sector to erase
1165:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *          This parameter can be a value of @ref FLASH_Sectors
1166:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @param  Banks Banks to be erased
1167:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *          This parameter can be one of the following values:
1168:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *            @arg FLASH_BANK_1: Bank1 to be erased
1169:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *            @arg FLASH_BANK_2: Bank2 to be erased
1170:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *            @arg FLASH_BANK_BOTH: Bank1 and Bank2 to be erased
1171:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @param  VoltageRange The device program/erase parallelism.
1172:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *          This parameter can be one of the following values:
1173:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *            @arg FLASH_VOLTAGE_RANGE_1 : Flash program/erase by 8 bits
1174:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *            @arg FLASH_VOLTAGE_RANGE_2 : Flash program/erase by 16 bits
1175:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *            @arg FLASH_VOLTAGE_RANGE_3 : Flash program/erase by 32 bits
1176:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *            @arg FLASH_VOLTAGE_RANGE_4 : Flash program/erase by 64 bits
1177:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *
1178:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @retval None
1179:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   */
1180:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** void FLASH_Erase_Sector(uint32_t Sector, uint32_t Banks, uint32_t VoltageRange)
1181:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** {
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 23


1182:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   assert_param(IS_FLASH_SECTOR(Sector));
1183:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   assert_param(IS_FLASH_BANK_EXCLUSIVE(Banks));
1184:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #if defined (FLASH_CR_PSIZE)
1185:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   assert_param(IS_VOLTAGERANGE(VoltageRange));
1186:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #else
1187:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   UNUSED(VoltageRange);
1188:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /* FLASH_CR_PSIZE */
1189:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1190:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   if((Banks & FLASH_BANK_1) == FLASH_BANK_1)
1191:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
1192:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #if defined (FLASH_CR_PSIZE)
1193:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* Reset Program/erase VoltageRange and Sector Number for Bank1 */
1194:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     FLASH->CR1 &= ~(FLASH_CR_PSIZE | FLASH_CR_SNB);
1195:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1196:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     FLASH->CR1 |= (FLASH_CR_SER | VoltageRange | (Sector << FLASH_CR_SNB_Pos) | FLASH_CR_START);
1197:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #else
1198:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* Reset Sector Number for Bank1 */
1199:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     FLASH->CR1 &= ~(FLASH_CR_SNB);
1200:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1201:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     FLASH->CR1 |= (FLASH_CR_SER | (Sector << FLASH_CR_SNB_Pos) | FLASH_CR_START);
1202:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /* FLASH_CR_PSIZE */
1203:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
1204:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1205:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #if defined (DUAL_BANK)
1206:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   if((Banks & FLASH_BANK_2) == FLASH_BANK_2)
1207:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
1208:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #if defined (FLASH_CR_PSIZE)
1209:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* Reset Program/erase VoltageRange and Sector Number for Bank2 */
1210:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     FLASH->CR2 &= ~(FLASH_CR_PSIZE | FLASH_CR_SNB);
1211:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1212:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     FLASH->CR2 |= (FLASH_CR_SER | VoltageRange  | (Sector << FLASH_CR_SNB_Pos) | FLASH_CR_START);
1213:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #else
1214:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* Reset Sector Number for Bank2 */
1215:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     FLASH->CR2 &= ~(FLASH_CR_SNB);
1216:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1217:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     FLASH->CR2 |= (FLASH_CR_SER | (Sector << FLASH_CR_SNB_Pos) | FLASH_CR_START);
1218:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /* FLASH_CR_PSIZE */
1219:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
1220:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /* DUAL_BANK */
1221:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** }
1222:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1223:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /**
1224:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @brief  Enable the write protection of the desired bank1 or bank 2 sectors
1225:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @param  WRPSector specifies the sector(s) to be write protected.
1226:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *          This parameter can be one of the following values:
1227:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *            @arg WRPSector:  A combination of OB_WRP_SECTOR_0 to OB_WRP_SECTOR_7 or OB_WRP_SECTO
1228:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *
1229:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @param  Banks the specific bank to apply WRP sectors
1230:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *          This parameter can be one of the following values:
1231:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *            @arg FLASH_BANK_1: enable WRP on specified bank1 sectors
1232:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *            @arg FLASH_BANK_2: enable WRP on specified bank2 sectors
1233:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *            @arg FLASH_BANK_BOTH: enable WRP on both bank1 and bank2 specified sectors
1234:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *
1235:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @retval HAL FLASH State
1236:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   */
1237:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** static void FLASH_OB_EnableWRP(uint32_t WRPSector, uint32_t Banks)
1238:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** {
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 24


  77              		.loc 1 1238 1 is_stmt 1 view -0
  78              		.cfi_startproc
  79              		@ args = 0, pretend = 0, frame = 0
  80              		@ frame_needed = 0, uses_anonymous_args = 0
  81              		@ link register save eliminated.
1239:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   /* Check the parameters */
1240:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   assert_param(IS_OB_WRP_SECTOR(WRPSector));
  82              		.loc 1 1240 3 view .LVU16
1241:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   assert_param(IS_FLASH_BANK(Banks));
  83              		.loc 1 1241 3 view .LVU17
1242:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1243:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   if((Banks & FLASH_BANK_1) == FLASH_BANK_1)
  84              		.loc 1 1243 3 view .LVU18
  85              		.loc 1 1243 5 is_stmt 0 view .LVU19
  86 0000 11F0010F 		tst	r1, #1
  87 0004 05D0     		beq	.L5
1244:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
1245:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* Enable Write Protection for bank 1 */
1246:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     FLASH->WPSN_PRG1 &= (~(WRPSector & FLASH_WPSN_WRPSN));
  88              		.loc 1 1246 5 is_stmt 1 view .LVU20
  89              		.loc 1 1246 10 is_stmt 0 view .LVU21
  90 0006 034A     		ldr	r2, .L7
  91 0008 D36B     		ldr	r3, [r2, #60]
  92              		.loc 1 1246 38 view .LVU22
  93 000a C0B2     		uxtb	r0, r0
  94              	.LVL2:
  95              		.loc 1 1246 22 view .LVU23
  96 000c 23EA0003 		bic	r3, r3, r0
  97 0010 D363     		str	r3, [r2, #60]
  98              	.L5:
1247:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
1248:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1249:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #if defined (DUAL_BANK)
1250:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   if((Banks & FLASH_BANK_2) == FLASH_BANK_2)
1251:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
1252:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* Enable Write Protection for bank 2 */
1253:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     FLASH->WPSN_PRG2 &= (~(WRPSector & FLASH_WPSN_WRPSN));
1254:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
1255:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /* DUAL_BANK */
1256:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** }
  99              		.loc 1 1256 1 view .LVU24
 100 0012 7047     		bx	lr
 101              	.L8:
 102              		.align	2
 103              	.L7:
 104 0014 ******** 		.word	**********
 105              		.cfi_endproc
 106              	.LFE153:
 108              		.section	.text.FLASH_OB_DisableWRP,"ax",%progbits
 109              		.align	1
 110              		.syntax unified
 111              		.thumb
 112              		.thumb_func
 114              	FLASH_OB_DisableWRP:
 115              	.LVL3:
 116              	.LFB154:
1257:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 25


1258:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /**
1259:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @brief  Disable the write protection of the desired bank1 or bank 2 sectors
1260:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @param  WRPSector specifies the sector(s) to disable write protection.
1261:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *          This parameter can be one of the following values:
1262:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *            @arg WRPSector:  A combination of FLASH_OB_WRP_SECTOR_0 to FLASH_OB_WRP_SECTOR_7 or 
1263:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *
1264:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @param  Banks the specific bank to apply WRP sectors
1265:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *          This parameter can be one of the following values:
1266:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *            @arg FLASH_BANK_1: disable WRP on specified bank1 sectors
1267:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *            @arg FLASH_BANK_2: disable WRP on specified bank2 sectors
1268:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *            @arg FLASH_BANK_BOTH: disable WRP on both bank1 and bank2 specified sectors
1269:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *
1270:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @retval HAL FLASH State
1271:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   */
1272:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** static void FLASH_OB_DisableWRP(uint32_t WRPSector, uint32_t Banks)
1273:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** {
 117              		.loc 1 1273 1 is_stmt 1 view -0
 118              		.cfi_startproc
 119              		@ args = 0, pretend = 0, frame = 0
 120              		@ frame_needed = 0, uses_anonymous_args = 0
 121              		@ link register save eliminated.
1274:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   /* Check the parameters */
1275:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   assert_param(IS_OB_WRP_SECTOR(WRPSector));
 122              		.loc 1 1275 3 view .LVU26
1276:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   assert_param(IS_FLASH_BANK(Banks));
 123              		.loc 1 1276 3 view .LVU27
1277:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1278:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   if((Banks & FLASH_BANK_1) == FLASH_BANK_1)
 124              		.loc 1 1278 3 view .LVU28
 125              		.loc 1 1278 5 is_stmt 0 view .LVU29
 126 0000 11F0010F 		tst	r1, #1
 127 0004 04D0     		beq	.L9
1279:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
1280:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* Disable Write Protection for bank 1 */
1281:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     FLASH->WPSN_PRG1 |= (WRPSector & FLASH_WPSN_WRPSN);
 128              		.loc 1 1281 5 is_stmt 1 view .LVU30
 129              		.loc 1 1281 10 is_stmt 0 view .LVU31
 130 0006 034A     		ldr	r2, .L11
 131 0008 D36B     		ldr	r3, [r2, #60]
 132              		.loc 1 1281 36 view .LVU32
 133 000a C0B2     		uxtb	r0, r0
 134              	.LVL4:
 135              		.loc 1 1281 22 view .LVU33
 136 000c 0343     		orrs	r3, r3, r0
 137 000e D363     		str	r3, [r2, #60]
 138              	.L9:
1282:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
1283:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1284:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #if defined (DUAL_BANK)
1285:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   if((Banks & FLASH_BANK_2) == FLASH_BANK_2)
1286:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
1287:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* Disable Write Protection for bank 2 */
1288:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     FLASH->WPSN_PRG2 |= (WRPSector & FLASH_WPSN_WRPSN);
1289:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
1290:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /* DUAL_BANK */
1291:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** }
 139              		.loc 1 1291 1 view .LVU34
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 26


 140 0010 7047     		bx	lr
 141              	.L12:
 142 0012 00BF     		.align	2
 143              	.L11:
 144 0014 ******** 		.word	**********
 145              		.cfi_endproc
 146              	.LFE154:
 148              		.section	.text.FLASH_OB_GetWRP,"ax",%progbits
 149              		.align	1
 150              		.syntax unified
 151              		.thumb
 152              		.thumb_func
 154              	FLASH_OB_GetWRP:
 155              	.LVL5:
 156              	.LFB155:
1292:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1293:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /**
1294:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @brief  Get the write protection of the given bank 1 or bank 2 sectors
1295:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @param  WRPState gives the write protection state on the given bank.
1296:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *          This parameter can be one of the following values:
1297:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *          @arg WRPState: OB_WRPSTATE_DISABLE or OB_WRPSTATE_ENABLE
1298:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1299:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @param  WRPSector gives the write protected sector(s) on the given bank .
1300:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *          This parameter can be one of the following values:
1301:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *          @arg WRPSector: A combination of FLASH_OB_WRP_SECTOR_0 to FLASH_OB_WRP_SECTOR_7 or FLA
1302:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *
1303:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @param  Bank the specific bank to apply WRP sectors
1304:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *          This parameter can be exclusively one of the following values:
1305:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *            @arg FLASH_BANK_1: Get bank1 WRP sectors
1306:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *            @arg FLASH_BANK_2: Get bank2 WRP sectors
1307:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *            @arg FLASH_BANK_BOTH: note allowed in this functions
1308:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *
1309:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @retval HAL FLASH State
1310:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   */
1311:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** static void FLASH_OB_GetWRP(uint32_t *WRPState, uint32_t *WRPSector, uint32_t Bank)
1312:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** {
 157              		.loc 1 1312 1 is_stmt 1 view -0
 158              		.cfi_startproc
 159              		@ args = 0, pretend = 0, frame = 0
 160              		@ frame_needed = 0, uses_anonymous_args = 0
 161              		@ link register save eliminated.
1313:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   uint32_t regvalue = 0U;
 162              		.loc 1 1313 3 view .LVU36
1314:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1315:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   if(Bank == FLASH_BANK_1)
 163              		.loc 1 1315 3 view .LVU37
 164              		.loc 1 1315 5 is_stmt 0 view .LVU38
 165 0000 012A     		cmp	r2, #1
 166 0002 06D0     		beq	.L18
1313:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   uint32_t regvalue = 0U;
 167              		.loc 1 1313 12 view .LVU39
 168 0004 0023     		movs	r3, #0
 169              	.LVL6:
 170              	.L14:
1316:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
1317:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     regvalue = FLASH->WPSN_CUR1;
1318:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 27


1319:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1320:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #if defined (DUAL_BANK)
1321:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   if(Bank == FLASH_BANK_2)
1322:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
1323:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     regvalue = FLASH->WPSN_CUR2;
1324:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
1325:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /* DUAL_BANK */
1326:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1327:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   (*WRPSector) = (~regvalue) & FLASH_WPSN_WRPSN;
 171              		.loc 1 1327 3 is_stmt 1 view .LVU40
 172              		.loc 1 1327 19 is_stmt 0 view .LVU41
 173 0006 DB43     		mvns	r3, r3
 174              	.LVL7:
 175              		.loc 1 1327 30 view .LVU42
 176 0008 DBB2     		uxtb	r3, r3
 177              	.LVL8:
 178              		.loc 1 1327 16 view .LVU43
 179 000a 0B60     		str	r3, [r1]
1328:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1329:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   if(*WRPSector == 0U)
 180              		.loc 1 1329 3 is_stmt 1 view .LVU44
 181              		.loc 1 1329 5 is_stmt 0 view .LVU45
 182 000c 23B9     		cbnz	r3, .L15
1330:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
1331:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     (*WRPState) = OB_WRPSTATE_DISABLE;
 183              		.loc 1 1331 5 is_stmt 1 view .LVU46
 184              		.loc 1 1331 17 is_stmt 0 view .LVU47
 185 000e 0360     		str	r3, [r0]
 186 0010 7047     		bx	lr
 187              	.LVL9:
 188              	.L18:
1317:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
 189              		.loc 1 1317 5 is_stmt 1 view .LVU48
1317:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
 190              		.loc 1 1317 14 is_stmt 0 view .LVU49
 191 0012 034B     		ldr	r3, .L19
 192 0014 9B6B     		ldr	r3, [r3, #56]
 193              	.LVL10:
1317:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
 194              		.loc 1 1317 14 view .LVU50
 195 0016 F6E7     		b	.L14
 196              	.LVL11:
 197              	.L15:
1332:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
1333:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   else
1334:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
1335:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     (*WRPState) = OB_WRPSTATE_ENABLE;
 198              		.loc 1 1335 5 is_stmt 1 view .LVU51
 199              		.loc 1 1335 17 is_stmt 0 view .LVU52
 200 0018 0123     		movs	r3, #1
 201 001a 0360     		str	r3, [r0]
1336:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
1337:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** }
 202              		.loc 1 1337 1 view .LVU53
 203 001c 7047     		bx	lr
 204              	.L20:
 205 001e 00BF     		.align	2
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 28


 206              	.L19:
 207 0020 ******** 		.word	**********
 208              		.cfi_endproc
 209              	.LFE155:
 211              		.section	.text.FLASH_OB_RDPConfig,"ax",%progbits
 212              		.align	1
 213              		.syntax unified
 214              		.thumb
 215              		.thumb_func
 217              	FLASH_OB_RDPConfig:
 218              	.LVL12:
 219              	.LFB156:
1338:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1339:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /**
1340:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @brief  Set the read protection level.
1341:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *
1342:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @note   To configure the RDP level, the option lock bit OPTLOCK must be
1343:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *         cleared with the call of the HAL_FLASH_OB_Unlock() function.
1344:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @note   To validate the RDP level, the option bytes must be reloaded
1345:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *         through the call of the HAL_FLASH_OB_Launch() function.
1346:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @note   !!! Warning : When enabling OB_RDP level 2 it's no more possible
1347:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *         to go back to level 1 or 0 !!!
1348:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *
1349:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @param  RDPLevel specifies the read protection level.
1350:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *         This parameter can be one of the following values:
1351:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *            @arg OB_RDP_LEVEL_0: No protection
1352:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *            @arg OB_RDP_LEVEL_1: Read protection of the memory
1353:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *            @arg OB_RDP_LEVEL_2: Full chip protection
1354:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *
1355:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @retval HAL status
1356:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   */
1357:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** static void FLASH_OB_RDPConfig(uint32_t RDPLevel)
1358:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** {
 220              		.loc 1 1358 1 is_stmt 1 view -0
 221              		.cfi_startproc
 222              		@ args = 0, pretend = 0, frame = 0
 223              		@ frame_needed = 0, uses_anonymous_args = 0
 224              		@ link register save eliminated.
1359:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   /* Check the parameters */
1360:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   assert_param(IS_OB_RDP_LEVEL(RDPLevel));
 225              		.loc 1 1360 3 view .LVU55
1361:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1362:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   /* Configure the RDP level in the option bytes register */
1363:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   MODIFY_REG(FLASH->OPTSR_PRG, FLASH_OPTSR_RDP, RDPLevel);
 226              		.loc 1 1363 3 view .LVU56
 227 0000 034A     		ldr	r2, .L22
 228 0002 136A     		ldr	r3, [r2, #32]
 229 0004 23F47F43 		bic	r3, r3, #65280
 230 0008 0343     		orrs	r3, r3, r0
 231 000a 1362     		str	r3, [r2, #32]
1364:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** }
 232              		.loc 1 1364 1 is_stmt 0 view .LVU57
 233 000c 7047     		bx	lr
 234              	.L23:
 235 000e 00BF     		.align	2
 236              	.L22:
 237 0010 ******** 		.word	**********
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 29


 238              		.cfi_endproc
 239              	.LFE156:
 241              		.section	.text.FLASH_OB_GetRDP,"ax",%progbits
 242              		.align	1
 243              		.syntax unified
 244              		.thumb
 245              		.thumb_func
 247              	FLASH_OB_GetRDP:
 248              	.LFB157:
1365:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1366:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /**
1367:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @brief  Get the read protection level.
1368:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @retval RDPLevel specifies the read protection level.
1369:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *         This return value can be one of the following values:
1370:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *            @arg OB_RDP_LEVEL_0: No protection
1371:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *            @arg OB_RDP_LEVEL_1: Read protection of the memory
1372:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *            @arg OB_RDP_LEVEL_2: Full chip protection
1373:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   */
1374:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** static uint32_t FLASH_OB_GetRDP(void)
1375:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** {
 249              		.loc 1 1375 1 is_stmt 1 view -0
 250              		.cfi_startproc
 251              		@ args = 0, pretend = 0, frame = 0
 252              		@ frame_needed = 0, uses_anonymous_args = 0
 253              		@ link register save eliminated.
1376:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   uint32_t rdp_level = READ_BIT(FLASH->OPTSR_CUR, FLASH_OPTSR_RDP);
 254              		.loc 1 1376 3 view .LVU59
 255              		.loc 1 1376 24 is_stmt 0 view .LVU60
 256 0000 064B     		ldr	r3, .L27
 257 0002 D869     		ldr	r0, [r3, #28]
 258              		.loc 1 1376 12 view .LVU61
 259 0004 00F47F40 		and	r0, r0, #65280
 260              	.LVL13:
1377:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   
1378:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   if ((rdp_level != OB_RDP_LEVEL_0) && (rdp_level != OB_RDP_LEVEL_2))
 261              		.loc 1 1378 3 is_stmt 1 view .LVU62
 262              		.loc 1 1378 6 is_stmt 0 view .LVU63
 263 0008 B0F52A4F 		cmp	r0, #43520
 264 000c 18BF     		it	ne
 265 000e B0F54C4F 		cmpne	r0, #52224
 266 0012 00D1     		bne	.L26
 267              	.LVL14:
 268              	.L24:
1379:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
1380:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     return (OB_RDP_LEVEL_1);
1381:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
1382:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   else
1383:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
1384:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     return rdp_level;
1385:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
1386:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** }
 269              		.loc 1 1386 1 view .LVU64
 270 0014 7047     		bx	lr
 271              	.LVL15:
 272              	.L26:
1380:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
 273              		.loc 1 1380 12 view .LVU65
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 30


 274 0016 4FF4AA40 		mov	r0, #21760
 275              	.LVL16:
1380:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
 276              		.loc 1 1380 12 view .LVU66
 277 001a FBE7     		b	.L24
 278              	.L28:
 279              		.align	2
 280              	.L27:
 281 001c ******** 		.word	**********
 282              		.cfi_endproc
 283              	.LFE157:
 285              		.section	.text.FLASH_OB_UserConfig,"ax",%progbits
 286              		.align	1
 287              		.syntax unified
 288              		.thumb
 289              		.thumb_func
 291              	FLASH_OB_UserConfig:
 292              	.LVL17:
 293              	.LFB158:
1387:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1388:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #if defined(DUAL_CORE)
1389:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /**
1390:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @brief  Program the FLASH User Option Byte.
1391:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *
1392:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @note   To configure the user option bytes, the option lock bit OPTLOCK must
1393:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *         be cleared with the call of the HAL_FLASH_OB_Unlock() function.
1394:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *
1395:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @note   To validate the user option bytes, the option bytes must be reloaded
1396:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *         through the call of the HAL_FLASH_OB_Launch() function.
1397:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *
1398:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @param  UserType The FLASH User Option Bytes to be modified :
1399:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *                   a combination of @ref FLASHEx_OB_USER_Type
1400:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *
1401:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @param  UserConfig The FLASH User Option Bytes values:
1402:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *         IWDG1_SW(Bit4), IWDG2_SW(Bit 5), nRST_STOP_D1(Bit 6), nRST_STDY_D1(Bit 7),
1403:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *         FZ_IWDG_STOP(Bit 17), FZ_IWDG_SDBY(Bit 18), ST_RAM_SIZE(Bit[19:20]),
1404:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *         SECURITY(Bit 21), BCM4(Bit 22), BCM7(Bit 23), nRST_STOP_D2(Bit 24),
1405:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *         nRST_STDY_D2(Bit 25), IO_HSLV (Bit 29) and SWAP_BANK_OPT(Bit 31).
1406:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *
1407:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @retval HAL status
1408:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   */
1409:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #else
1410:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /**
1411:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @brief  Program the FLASH User Option Byte.
1412:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *
1413:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @note   To configure the user option bytes, the option lock bit OPTLOCK must
1414:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *         be cleared with the call of the HAL_FLASH_OB_Unlock() function.
1415:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *
1416:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @note   To validate the user option bytes, the option bytes must be reloaded
1417:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *         through the call of the HAL_FLASH_OB_Launch() function.
1418:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *
1419:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @param  UserType The FLASH User Option Bytes to be modified :
1420:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *                   a combination of @arg FLASHEx_OB_USER_Type
1421:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *
1422:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @param  UserConfig The FLASH User Option Bytes values:
1423:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *         IWDG_SW(Bit4), nRST_STOP_D1(Bit 6), nRST_STDY_D1(Bit 7),
1424:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *         FZ_IWDG_STOP(Bit 17), FZ_IWDG_SDBY(Bit 18), ST_RAM_SIZE(Bit[19:20]),
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 31


1425:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *         SECURITY(Bit 21), IO_HSLV (Bit 29) and SWAP_BANK_OPT(Bit 31).
1426:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *
1427:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @retval HAL status
1428:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   */
1429:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /*DUAL_CORE*/
1430:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** static void FLASH_OB_UserConfig(uint32_t UserType, uint32_t UserConfig)
1431:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** {
 294              		.loc 1 1431 1 is_stmt 1 view -0
 295              		.cfi_startproc
 296              		@ args = 0, pretend = 0, frame = 0
 297              		@ frame_needed = 0, uses_anonymous_args = 0
 298              		@ link register save eliminated.
1432:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   uint32_t optr_reg_val = 0;
 299              		.loc 1 1432 3 view .LVU68
1433:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   uint32_t optr_reg_mask = 0;
 300              		.loc 1 1433 3 view .LVU69
1434:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1435:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   /* Check the parameters */
1436:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   assert_param(IS_OB_USER_TYPE(UserType));
 301              		.loc 1 1436 3 view .LVU70
1437:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1438:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   if((UserType & OB_USER_IWDG1_SW) != 0U)
 302              		.loc 1 1438 3 view .LVU71
 303              		.loc 1 1438 5 is_stmt 0 view .LVU72
 304 0000 10F00103 		ands	r3, r0, #1
 305 0004 59D0     		beq	.L40
1439:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
1440:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* IWDG_HW option byte should be modified */
1441:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     assert_param(IS_OB_IWDG1_SOURCE(UserConfig & FLASH_OPTSR_IWDG1_SW));
 306              		.loc 1 1441 5 is_stmt 1 view .LVU73
1442:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1443:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* Set value and mask for IWDG_HW option byte */
1444:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     optr_reg_val |= (UserConfig & FLASH_OPTSR_IWDG1_SW);
 307              		.loc 1 1444 5 view .LVU74
 308              		.loc 1 1444 33 is_stmt 0 view .LVU75
 309 0006 01F01003 		and	r3, r1, #16
 310              	.LVL18:
1445:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     optr_reg_mask |= FLASH_OPTSR_IWDG1_SW;
 311              		.loc 1 1445 5 is_stmt 1 view .LVU76
 312              		.loc 1 1445 19 is_stmt 0 view .LVU77
 313 000a 1022     		movs	r2, #16
 314              	.LVL19:
 315              	.L30:
1446:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
1447:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #if defined(DUAL_CORE)
1448:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   if((UserType & OB_USER_IWDG2_SW) != 0U)
1449:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
1450:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* IWDG2_SW option byte should be modified */
1451:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     assert_param(IS_OB_IWDG2_SOURCE(UserConfig & FLASH_OPTSR_IWDG2_SW));
1452:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1453:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* Set value and mask for IWDG2_SW option byte */
1454:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     optr_reg_val |= (UserConfig & FLASH_OPTSR_IWDG2_SW);
1455:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     optr_reg_mask |= FLASH_OPTSR_IWDG2_SW;
1456:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
1457:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /*DUAL_CORE*/
1458:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   if((UserType & OB_USER_NRST_STOP_D1) != 0U)
 316              		.loc 1 1458 3 is_stmt 1 view .LVU78
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 32


 317              		.loc 1 1458 5 is_stmt 0 view .LVU79
 318 000c 10F0020F 		tst	r0, #2
 319 0010 05D0     		beq	.L31
1459:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
1460:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* NRST_STOP option byte should be modified */
1461:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     assert_param(IS_OB_STOP_D1_RESET(UserConfig & FLASH_OPTSR_NRST_STOP_D1));
 320              		.loc 1 1461 5 is_stmt 1 view .LVU80
1462:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1463:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* Set value and mask for NRST_STOP option byte */
1464:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     optr_reg_val |= (UserConfig & FLASH_OPTSR_NRST_STOP_D1);
 321              		.loc 1 1464 5 view .LVU81
 322              		.loc 1 1464 33 is_stmt 0 view .LVU82
 323 0012 01F0400C 		and	ip, r1, #64
 324              		.loc 1 1464 18 view .LVU83
 325 0016 43EA0C03 		orr	r3, r3, ip
 326              	.LVL20:
1465:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     optr_reg_mask |= FLASH_OPTSR_NRST_STOP_D1;
 327              		.loc 1 1465 5 is_stmt 1 view .LVU84
 328              		.loc 1 1465 19 is_stmt 0 view .LVU85
 329 001a 42F04002 		orr	r2, r2, #64
 330              	.LVL21:
 331              	.L31:
1466:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
1467:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1468:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   if((UserType & OB_USER_NRST_STDBY_D1) != 0U)
 332              		.loc 1 1468 3 is_stmt 1 view .LVU86
 333              		.loc 1 1468 5 is_stmt 0 view .LVU87
 334 001e 10F0040F 		tst	r0, #4
 335 0022 05D0     		beq	.L32
1469:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
1470:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* NRST_STDBY option byte should be modified */
1471:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     assert_param(IS_OB_STDBY_D1_RESET(UserConfig & FLASH_OPTSR_NRST_STBY_D1));
 336              		.loc 1 1471 5 is_stmt 1 view .LVU88
1472:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1473:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* Set value and mask for NRST_STDBY option byte */
1474:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     optr_reg_val |= (UserConfig & FLASH_OPTSR_NRST_STBY_D1);
 337              		.loc 1 1474 5 view .LVU89
 338              		.loc 1 1474 33 is_stmt 0 view .LVU90
 339 0024 01F0800C 		and	ip, r1, #128
 340              		.loc 1 1474 18 view .LVU91
 341 0028 43EA0C03 		orr	r3, r3, ip
 342              	.LVL22:
1475:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     optr_reg_mask |= FLASH_OPTSR_NRST_STBY_D1;
 343              		.loc 1 1475 5 is_stmt 1 view .LVU92
 344              		.loc 1 1475 19 is_stmt 0 view .LVU93
 345 002c 42F08002 		orr	r2, r2, #128
 346              	.LVL23:
 347              	.L32:
1476:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
1477:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1478:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   if((UserType & OB_USER_IWDG_STOP) != 0U)
 348              		.loc 1 1478 3 is_stmt 1 view .LVU94
 349              		.loc 1 1478 5 is_stmt 0 view .LVU95
 350 0030 10F0080F 		tst	r0, #8
 351 0034 05D0     		beq	.L33
1479:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
1480:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* IWDG_STOP option byte should be modified */
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 33


1481:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     assert_param(IS_OB_USER_IWDG_STOP(UserConfig & FLASH_OPTSR_FZ_IWDG_STOP));
 352              		.loc 1 1481 5 is_stmt 1 view .LVU96
1482:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1483:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* Set value and mask for IWDG_STOP option byte */
1484:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     optr_reg_val |= (UserConfig & FLASH_OPTSR_FZ_IWDG_STOP);
 353              		.loc 1 1484 5 view .LVU97
 354              		.loc 1 1484 33 is_stmt 0 view .LVU98
 355 0036 01F4003C 		and	ip, r1, #131072
 356              		.loc 1 1484 18 view .LVU99
 357 003a 43EA0C03 		orr	r3, r3, ip
 358              	.LVL24:
1485:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     optr_reg_mask |= FLASH_OPTSR_FZ_IWDG_STOP;
 359              		.loc 1 1485 5 is_stmt 1 view .LVU100
 360              		.loc 1 1485 19 is_stmt 0 view .LVU101
 361 003e 42F40032 		orr	r2, r2, #131072
 362              	.LVL25:
 363              	.L33:
1486:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
1487:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1488:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   if((UserType & OB_USER_IWDG_STDBY) != 0U)
 364              		.loc 1 1488 3 is_stmt 1 view .LVU102
 365              		.loc 1 1488 5 is_stmt 0 view .LVU103
 366 0042 10F0100F 		tst	r0, #16
 367 0046 05D0     		beq	.L34
1489:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
1490:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* IWDG_STDBY option byte should be modified */
1491:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     assert_param(IS_OB_USER_IWDG_STDBY(UserConfig & FLASH_OPTSR_FZ_IWDG_SDBY));
 368              		.loc 1 1491 5 is_stmt 1 view .LVU104
1492:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1493:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* Set value and mask for IWDG_STDBY option byte */
1494:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     optr_reg_val |= (UserConfig & FLASH_OPTSR_FZ_IWDG_SDBY);
 369              		.loc 1 1494 5 view .LVU105
 370              		.loc 1 1494 33 is_stmt 0 view .LVU106
 371 0048 01F4802C 		and	ip, r1, #262144
 372              		.loc 1 1494 18 view .LVU107
 373 004c 43EA0C03 		orr	r3, r3, ip
 374              	.LVL26:
1495:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     optr_reg_mask |= FLASH_OPTSR_FZ_IWDG_SDBY;
 375              		.loc 1 1495 5 is_stmt 1 view .LVU108
 376              		.loc 1 1495 19 is_stmt 0 view .LVU109
 377 0050 42F48022 		orr	r2, r2, #262144
 378              	.LVL27:
 379              	.L34:
1496:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
1497:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1498:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   if((UserType & OB_USER_ST_RAM_SIZE) != 0U)
 380              		.loc 1 1498 3 is_stmt 1 view .LVU110
 381              		.loc 1 1498 5 is_stmt 0 view .LVU111
 382 0054 10F0200F 		tst	r0, #32
 383 0058 05D0     		beq	.L35
1499:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
1500:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* ST_RAM_SIZE option byte should be modified */
1501:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     assert_param(IS_OB_USER_ST_RAM_SIZE(UserConfig & FLASH_OPTSR_ST_RAM_SIZE));
 384              		.loc 1 1501 5 is_stmt 1 view .LVU112
1502:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1503:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* Set value and mask for ST_RAM_SIZE option byte */
1504:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     optr_reg_val |= (UserConfig & FLASH_OPTSR_ST_RAM_SIZE);
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 34


 385              		.loc 1 1504 5 view .LVU113
 386              		.loc 1 1504 33 is_stmt 0 view .LVU114
 387 005a 01F4C01C 		and	ip, r1, #1572864
 388              		.loc 1 1504 18 view .LVU115
 389 005e 43EA0C03 		orr	r3, r3, ip
 390              	.LVL28:
1505:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     optr_reg_mask |= FLASH_OPTSR_ST_RAM_SIZE;
 391              		.loc 1 1505 5 is_stmt 1 view .LVU116
 392              		.loc 1 1505 19 is_stmt 0 view .LVU117
 393 0062 42F4C012 		orr	r2, r2, #1572864
 394              	.LVL29:
 395              	.L35:
1506:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
1507:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1508:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   if((UserType & OB_USER_SECURITY) != 0U)
 396              		.loc 1 1508 3 is_stmt 1 view .LVU118
 397              		.loc 1 1508 5 is_stmt 0 view .LVU119
 398 0066 10F0400F 		tst	r0, #64
 399 006a 05D0     		beq	.L36
1509:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
1510:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* SECURITY option byte should be modified */
1511:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     assert_param(IS_OB_USER_SECURITY(UserConfig & FLASH_OPTSR_SECURITY));
 400              		.loc 1 1511 5 is_stmt 1 view .LVU120
1512:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1513:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* Set value and mask for SECURITY option byte */
1514:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     optr_reg_val |= (UserConfig & FLASH_OPTSR_SECURITY);
 401              		.loc 1 1514 5 view .LVU121
 402              		.loc 1 1514 33 is_stmt 0 view .LVU122
 403 006c 01F4001C 		and	ip, r1, #2097152
 404              		.loc 1 1514 18 view .LVU123
 405 0070 43EA0C03 		orr	r3, r3, ip
 406              	.LVL30:
1515:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     optr_reg_mask |= FLASH_OPTSR_SECURITY;
 407              		.loc 1 1515 5 is_stmt 1 view .LVU124
 408              		.loc 1 1515 19 is_stmt 0 view .LVU125
 409 0074 42F40012 		orr	r2, r2, #2097152
 410              	.LVL31:
 411              	.L36:
1516:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
1517:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1518:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #if defined(DUAL_CORE)
1519:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   if((UserType & OB_USER_BCM4) != 0U)
1520:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
1521:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* BCM4 option byte should be modified */
1522:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     assert_param(IS_OB_USER_BCM4(UserConfig & FLASH_OPTSR_BCM4));
1523:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1524:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* Set value and mask for BCM4 option byte */
1525:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     optr_reg_val |= (UserConfig & FLASH_OPTSR_BCM4);
1526:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     optr_reg_mask |= FLASH_OPTSR_BCM4;
1527:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
1528:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1529:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   if((UserType & OB_USER_BCM7) != 0U)
1530:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
1531:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* BCM7 option byte should be modified */
1532:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     assert_param(IS_OB_USER_BCM7(UserConfig & FLASH_OPTSR_BCM7));
1533:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1534:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* Set value and mask for BCM7 option byte */
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 35


1535:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     optr_reg_val |= (UserConfig & FLASH_OPTSR_BCM7);
1536:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     optr_reg_mask |= FLASH_OPTSR_BCM7;
1537:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
1538:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /* DUAL_CORE */
1539:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1540:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #if defined (FLASH_OPTSR_NRST_STOP_D2)
1541:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   if((UserType & OB_USER_NRST_STOP_D2) != 0U)
 412              		.loc 1 1541 3 is_stmt 1 view .LVU126
 413              		.loc 1 1541 5 is_stmt 0 view .LVU127
 414 0078 10F4805F 		tst	r0, #4096
 415 007c 05D0     		beq	.L37
1542:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
1543:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* NRST_STOP option byte should be modified */
1544:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     assert_param(IS_OB_STOP_D2_RESET(UserConfig & FLASH_OPTSR_NRST_STOP_D2));
 416              		.loc 1 1544 5 is_stmt 1 view .LVU128
1545:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1546:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* Set value and mask for NRST_STOP option byte */
1547:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     optr_reg_val |= (UserConfig & FLASH_OPTSR_NRST_STOP_D2);
 417              		.loc 1 1547 5 view .LVU129
 418              		.loc 1 1547 33 is_stmt 0 view .LVU130
 419 007e 01F0807C 		and	ip, r1, #16777216
 420              		.loc 1 1547 18 view .LVU131
 421 0082 43EA0C03 		orr	r3, r3, ip
 422              	.LVL32:
1548:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     optr_reg_mask |= FLASH_OPTSR_NRST_STOP_D2;
 423              		.loc 1 1548 5 is_stmt 1 view .LVU132
 424              		.loc 1 1548 19 is_stmt 0 view .LVU133
 425 0086 42F08072 		orr	r2, r2, #16777216
 426              	.LVL33:
 427              	.L37:
1549:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
1550:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1551:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   if((UserType & OB_USER_NRST_STDBY_D2) != 0U)
 428              		.loc 1 1551 3 is_stmt 1 view .LVU134
 429              		.loc 1 1551 5 is_stmt 0 view .LVU135
 430 008a 10F4005F 		tst	r0, #8192
 431 008e 05D0     		beq	.L38
1552:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
1553:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* NRST_STDBY option byte should be modified */
1554:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     assert_param(IS_OB_STDBY_D2_RESET(UserConfig & FLASH_OPTSR_NRST_STBY_D2));
 432              		.loc 1 1554 5 is_stmt 1 view .LVU136
1555:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1556:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* Set value and mask for NRST_STDBY option byte */
1557:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     optr_reg_val |= (UserConfig & FLASH_OPTSR_NRST_STBY_D2);
 433              		.loc 1 1557 5 view .LVU137
 434              		.loc 1 1557 33 is_stmt 0 view .LVU138
 435 0090 01F0007C 		and	ip, r1, #********
 436              		.loc 1 1557 18 view .LVU139
 437 0094 43EA0C03 		orr	r3, r3, ip
 438              	.LVL34:
1558:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     optr_reg_mask |= FLASH_OPTSR_NRST_STBY_D2;
 439              		.loc 1 1558 5 is_stmt 1 view .LVU140
 440              		.loc 1 1558 19 is_stmt 0 view .LVU141
 441 0098 42F00072 		orr	r2, r2, #********
 442              	.LVL35:
 443              	.L38:
1559:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 36


1560:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /* FLASH_OPTSR_NRST_STOP_D2 */
1561:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1562:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #if defined (DUAL_BANK)
1563:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   if((UserType & OB_USER_SWAP_BANK) != 0U)
1564:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
1565:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* SWAP_BANK_OPT option byte should be modified */
1566:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     assert_param(IS_OB_USER_SWAP_BANK(UserConfig & FLASH_OPTSR_SWAP_BANK_OPT));
1567:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1568:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* Set value and mask for SWAP_BANK_OPT option byte */
1569:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     optr_reg_val |= (UserConfig & FLASH_OPTSR_SWAP_BANK_OPT);
1570:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     optr_reg_mask |= FLASH_OPTSR_SWAP_BANK_OPT;
1571:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
1572:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /* DUAL_BANK */
1573:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1574:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   if((UserType & OB_USER_IOHSLV) != 0U)
 444              		.loc 1 1574 3 is_stmt 1 view .LVU142
 445              		.loc 1 1574 5 is_stmt 0 view .LVU143
 446 009c 10F0800F 		tst	r0, #128
 447 00a0 04D0     		beq	.L39
1575:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
1576:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* IOHSLV_OPT option byte should be modified */
1577:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     assert_param(IS_OB_USER_IOHSLV(UserConfig & FLASH_OPTSR_IO_HSLV));
 448              		.loc 1 1577 5 is_stmt 1 view .LVU144
1578:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1579:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* Set value and mask for IOHSLV_OPT option byte */
1580:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     optr_reg_val |= (UserConfig & FLASH_OPTSR_IO_HSLV);
 449              		.loc 1 1580 5 view .LVU145
 450              		.loc 1 1580 33 is_stmt 0 view .LVU146
 451 00a2 01F00051 		and	r1, r1, #536870912
 452              	.LVL36:
 453              		.loc 1 1580 18 view .LVU147
 454 00a6 0B43     		orrs	r3, r3, r1
 455              	.LVL37:
1581:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     optr_reg_mask |= FLASH_OPTSR_IO_HSLV;
 456              		.loc 1 1581 5 is_stmt 1 view .LVU148
 457              		.loc 1 1581 19 is_stmt 0 view .LVU149
 458 00a8 42F00052 		orr	r2, r2, #536870912
 459              	.LVL38:
 460              	.L39:
1582:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
1583:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1584:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #if defined (FLASH_OPTSR_VDDMMC_HSLV)
1585:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   if((UserType & OB_USER_VDDMMC_HSLV) != 0U)
1586:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
1587:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* VDDMMC_HSLV option byte should be modified */
1588:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     assert_param(IS_OB_USER_VDDMMC_HSLV(UserConfig & FLASH_OPTSR_VDDMMC_HSLV));
1589:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1590:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* Set value and mask for VDDMMC_HSLV option byte */
1591:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     optr_reg_val |= (UserConfig & FLASH_OPTSR_VDDMMC_HSLV);
1592:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     optr_reg_mask |= FLASH_OPTSR_VDDMMC_HSLV;
1593:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
1594:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /* FLASH_OPTSR_VDDMMC_HSLV */
1595:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1596:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   /* Configure the option bytes register */
1597:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   MODIFY_REG(FLASH->OPTSR_PRG, optr_reg_mask, optr_reg_val);
 461              		.loc 1 1597 3 is_stmt 1 view .LVU150
 462 00ac 0448     		ldr	r0, .L41
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 37


 463              	.LVL39:
 464              		.loc 1 1597 3 is_stmt 0 view .LVU151
 465 00ae 016A     		ldr	r1, [r0, #32]
 466 00b0 21EA0202 		bic	r2, r1, r2
 467              	.LVL40:
 468              		.loc 1 1597 3 view .LVU152
 469 00b4 1343     		orrs	r3, r3, r2
 470              	.LVL41:
 471              		.loc 1 1597 3 view .LVU153
 472 00b6 0362     		str	r3, [r0, #32]
1598:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** }
 473              		.loc 1 1598 1 view .LVU154
 474 00b8 7047     		bx	lr
 475              	.LVL42:
 476              	.L40:
1433:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 477              		.loc 1 1433 12 view .LVU155
 478 00ba 1A46     		mov	r2, r3
 479 00bc A6E7     		b	.L30
 480              	.L42:
 481 00be 00BF     		.align	2
 482              	.L41:
 483 00c0 ******** 		.word	**********
 484              		.cfi_endproc
 485              	.LFE158:
 487              		.section	.text.FLASH_OB_GetUser,"ax",%progbits
 488              		.align	1
 489              		.syntax unified
 490              		.thumb
 491              		.thumb_func
 493              	FLASH_OB_GetUser:
 494              	.LFB159:
1599:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1600:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #if defined(DUAL_CORE)
1601:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /**
1602:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @brief  Return the FLASH User Option Byte value.
1603:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @retval The FLASH User Option Bytes values
1604:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *         IWDG1_SW(Bit4), IWDG2_SW(Bit 5), nRST_STOP_D1(Bit 6), nRST_STDY_D1(Bit 7),
1605:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *         FZ_IWDG_STOP(Bit 17), FZ_IWDG_SDBY(Bit 18), ST_RAM_SIZE(Bit[19:20]),
1606:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *         SECURITY(Bit 21), BCM4(Bit 22), BCM7(Bit 23), nRST_STOP_D2(Bit 24),
1607:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *         nRST_STDY_D2(Bit 25), IO_HSLV (Bit 29) and SWAP_BANK_OPT(Bit 31).
1608:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   */
1609:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #else
1610:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /**
1611:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @brief  Return the FLASH User Option Byte value.
1612:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @retval The FLASH User Option Bytes values
1613:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *         IWDG_SW(Bit4), nRST_STOP_D1(Bit 6), nRST_STDY_D1(Bit 7),
1614:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *         FZ_IWDG_STOP(Bit 17), FZ_IWDG_SDBY(Bit 18), ST_RAM_SIZE(Bit[19:20]),
1615:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *         SECURITY(Bit 21), IO_HSLV (Bit 29) and SWAP_BANK_OPT(Bit 31).
1616:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   */
1617:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /*DUAL_CORE*/
1618:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** static uint32_t FLASH_OB_GetUser(void)
1619:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** {
 495              		.loc 1 1619 1 is_stmt 1 view -0
 496              		.cfi_startproc
 497              		@ args = 0, pretend = 0, frame = 0
 498              		@ frame_needed = 0, uses_anonymous_args = 0
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 38


 499              		@ link register save eliminated.
1620:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   uint32_t userConfig = READ_REG(FLASH->OPTSR_CUR);
 500              		.loc 1 1620 3 view .LVU157
 501              		.loc 1 1620 12 is_stmt 0 view .LVU158
 502 0000 024B     		ldr	r3, .L44
 503 0002 DB69     		ldr	r3, [r3, #28]
 504              	.LVL43:
1621:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   userConfig &= (~(FLASH_OPTSR_BOR_LEV | FLASH_OPTSR_RDP));
 505              		.loc 1 1621 3 is_stmt 1 view .LVU159
1622:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1623:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   return userConfig;
 506              		.loc 1 1623 3 view .LVU160
1624:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** }
 507              		.loc 1 1624 1 is_stmt 0 view .LVU161
 508 0004 0248     		ldr	r0, .L44+4
 509 0006 1840     		ands	r0, r0, r3
 510              	.LVL44:
 511              		.loc 1 1624 1 view .LVU162
 512 0008 7047     		bx	lr
 513              	.L45:
 514 000a 00BF     		.align	2
 515              	.L44:
 516 000c ******** 		.word	**********
 517 0010 F300FFFF 		.word	-65293
 518              		.cfi_endproc
 519              	.LFE159:
 521              		.section	.text.FLASH_OB_PCROPConfig,"ax",%progbits
 522              		.align	1
 523              		.syntax unified
 524              		.thumb
 525              		.thumb_func
 527              	FLASH_OB_PCROPConfig:
 528              	.LVL45:
 529              	.LFB160:
1625:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1626:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /**
1627:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @brief  Configure the Proprietary code readout protection of the desired addresses
1628:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *
1629:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @note   To configure the PCROP options, the option lock bit OPTLOCK must be
1630:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *         cleared with the call of the HAL_FLASH_OB_Unlock() function.
1631:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @note   To validate the PCROP options, the option bytes must be reloaded
1632:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *         through the call of the HAL_FLASH_OB_Launch() function.
1633:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *
1634:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @param  PCROPConfig specifies if the PCROP area for the given Bank shall be erased or not
1635:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *         when RDP level decreased from Level 1 to Level 0, or after a bank erase with protection
1636:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *         This parameter must be a value of @arg FLASHEx_OB_PCROP_RDP enumeration
1637:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *
1638:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @param  PCROPStartAddr specifies the start address of the Proprietary code readout protection
1639:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *          This parameter can be an address between begin and end of the bank
1640:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *
1641:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @param  PCROPEndAddr specifies the end address of the Proprietary code readout protection
1642:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *          This parameter can be an address between PCROPStartAddr and end of the bank
1643:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *
1644:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @param  Banks the specific bank to apply PCROP protection
1645:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *          This parameter can be one of the following values:
1646:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *            @arg FLASH_BANK_1: PCROP on specified bank1 area
1647:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *            @arg FLASH_BANK_2: PCROP on specified bank2 area
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 39


1648:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *            @arg FLASH_BANK_BOTH: PCROP on specified bank1 and bank2 area (same config will be a
1649:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *
1650:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @retval None
1651:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   */
1652:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** static void FLASH_OB_PCROPConfig(uint32_t PCROPConfig, uint32_t PCROPStartAddr, uint32_t PCROPEndAd
1653:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** {
 530              		.loc 1 1653 1 is_stmt 1 view -0
 531              		.cfi_startproc
 532              		@ args = 0, pretend = 0, frame = 0
 533              		@ frame_needed = 0, uses_anonymous_args = 0
 534              		@ link register save eliminated.
1654:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   /* Check the parameters */
1655:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   assert_param(IS_FLASH_BANK(Banks));
 535              		.loc 1 1655 3 view .LVU164
1656:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   assert_param(IS_OB_PCROP_RDP(PCROPConfig));
 536              		.loc 1 1656 3 view .LVU165
1657:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1658:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   if((Banks & FLASH_BANK_1) == FLASH_BANK_1)
 537              		.loc 1 1658 3 view .LVU166
 538              		.loc 1 1658 5 is_stmt 0 view .LVU167
 539 0000 13F0010F 		tst	r3, #1
 540 0004 0AD0     		beq	.L46
1659:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
1660:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     assert_param(IS_FLASH_PROGRAM_ADDRESS_BANK1(PCROPStartAddr));
 541              		.loc 1 1660 5 is_stmt 1 view .LVU168
1661:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     assert_param(IS_FLASH_PROGRAM_ADDRESS_BANK1(PCROPEndAddr));
 542              		.loc 1 1661 5 view .LVU169
1662:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1663:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* Configure the Proprietary code readout protection */
1664:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     FLASH->PRAR_PRG1 = ((PCROPStartAddr - FLASH_BANK1_BASE) >> 8)                                 |
 543              		.loc 1 1664 5 view .LVU170
 544              		.loc 1 1664 41 is_stmt 0 view .LVU171
 545 0006 01F17841 		add	r1, r1, #-*********
 546              	.LVL46:
1665:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****                        (((PCROPEndAddr - FLASH_BANK1_BASE) >> 8) << FLASH_PRAR_PROT_AREA_END_Pos) |
 547              		.loc 1 1665 40 view .LVU172
 548 000a 02F17842 		add	r2, r2, #-*********
 549              	.LVL47:
 550              		.loc 1 1665 60 view .LVU173
 551 000e 120A     		lsrs	r2, r2, #8
 552              	.LVL48:
 553              		.loc 1 1665 66 view .LVU174
 554 0010 1204     		lsls	r2, r2, #16
1664:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****                        (((PCROPEndAddr - FLASH_BANK1_BASE) >> 8) << FLASH_PRAR_PROT_AREA_END_Pos) |
 555              		.loc 1 1664 99 view .LVU175
 556 0012 42EA1122 		orr	r2, r2, r1, lsr #8
 557              		.loc 1 1665 99 view .LVU176
 558 0016 1043     		orrs	r0, r0, r2
 559              	.LVL49:
1664:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****                        (((PCROPEndAddr - FLASH_BANK1_BASE) >> 8) << FLASH_PRAR_PROT_AREA_END_Pos) |
 560              		.loc 1 1664 22 view .LVU177
 561 0018 014B     		ldr	r3, .L48
 562              	.LVL50:
1664:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****                        (((PCROPEndAddr - FLASH_BANK1_BASE) >> 8) << FLASH_PRAR_PROT_AREA_END_Pos) |
 563              		.loc 1 1664 22 view .LVU178
 564 001a D862     		str	r0, [r3, #44]
 565              	.LVL51:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 40


 566              	.L46:
1666:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****                        PCROPConfig;
1667:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
1668:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1669:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #if defined (DUAL_BANK)
1670:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   if((Banks & FLASH_BANK_2) == FLASH_BANK_2)
1671:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
1672:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     assert_param(IS_FLASH_PROGRAM_ADDRESS_BANK2(PCROPStartAddr));
1673:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     assert_param(IS_FLASH_PROGRAM_ADDRESS_BANK2(PCROPEndAddr));
1674:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1675:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* Configure the Proprietary code readout protection */
1676:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     FLASH->PRAR_PRG2 = ((PCROPStartAddr - FLASH_BANK2_BASE) >> 8)                                 |
1677:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****                        (((PCROPEndAddr - FLASH_BANK2_BASE) >> 8) << FLASH_PRAR_PROT_AREA_END_Pos) |
1678:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****                        PCROPConfig;
1679:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
1680:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /* DUAL_BANK */
1681:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** }
 567              		.loc 1 1681 1 view .LVU179
 568 001c 7047     		bx	lr
 569              	.L49:
 570 001e 00BF     		.align	2
 571              	.L48:
 572 0020 ******** 		.word	**********
 573              		.cfi_endproc
 574              	.LFE160:
 576              		.section	.text.FLASH_OB_GetPCROP,"ax",%progbits
 577              		.align	1
 578              		.syntax unified
 579              		.thumb
 580              		.thumb_func
 582              	FLASH_OB_GetPCROP:
 583              	.LVL52:
 584              	.LFB161:
1682:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1683:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /**
1684:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @brief  Get the Proprietary code readout protection configuration on a given Bank
1685:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *
1686:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @param  PCROPConfig indicates if the PCROP area for the given Bank shall be erased or not
1687:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *         when RDP level decreased from Level 1 to Level 0 or after a bank erase with protection 
1688:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *
1689:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @param  PCROPStartAddr gives the start address of the Proprietary code readout protection of th
1690:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *
1691:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @param  PCROPEndAddr gives the end address of the Proprietary code readout protection of the ba
1692:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *
1693:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @param  Bank the specific bank to apply PCROP protection
1694:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *          This parameter can be exclusively one of the following values:
1695:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *            @arg FLASH_BANK_1: PCROP on specified bank1 area
1696:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *            @arg FLASH_BANK_2: PCROP on specified bank2 area
1697:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *            @arg FLASH_BANK_BOTH: is  not allowed here
1698:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *
1699:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @retval None
1700:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   */
1701:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** static void FLASH_OB_GetPCROP(uint32_t *PCROPConfig, uint32_t *PCROPStartAddr, uint32_t *PCROPEndAd
1702:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** {
 585              		.loc 1 1702 1 is_stmt 1 view -0
 586              		.cfi_startproc
 587              		@ args = 0, pretend = 0, frame = 0
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 41


 588              		@ frame_needed = 0, uses_anonymous_args = 0
 589              		@ link register save eliminated.
 590              		.loc 1 1702 1 is_stmt 0 view .LVU181
 591 0000 10B4     		push	{r4}
 592              	.LCFI0:
 593              		.cfi_def_cfa_offset 4
 594              		.cfi_offset 4, -4
1703:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   uint32_t regvalue = 0;
 595              		.loc 1 1703 3 is_stmt 1 view .LVU182
 596              	.LVL53:
1704:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   uint32_t bankBase = 0;
 597              		.loc 1 1704 3 view .LVU183
1705:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1706:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   if(Bank == FLASH_BANK_1)
 598              		.loc 1 1706 3 view .LVU184
 599              		.loc 1 1706 5 is_stmt 0 view .LVU185
 600 0002 012B     		cmp	r3, #1
 601 0004 13D0     		beq	.L54
1704:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   uint32_t bankBase = 0;
 602              		.loc 1 1704 12 view .LVU186
 603 0006 4FF0000C 		mov	ip, #0
1703:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   uint32_t bankBase = 0;
 604              		.loc 1 1703 12 view .LVU187
 605 000a 6346     		mov	r3, ip
 606              	.LVL54:
 607              	.L51:
1707:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
1708:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     regvalue = FLASH->PRAR_CUR1;
1709:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     bankBase = FLASH_BANK1_BASE;
1710:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
1711:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1712:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #if defined (DUAL_BANK)
1713:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   if(Bank == FLASH_BANK_2)
1714:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
1715:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     regvalue = FLASH->PRAR_CUR2;
1716:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     bankBase = FLASH_BANK2_BASE;
1717:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
1718:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /* DUAL_BANK */
1719:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1720:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   (*PCROPConfig) =  (regvalue & FLASH_PRAR_DMEP);
 608              		.loc 1 1720 3 is_stmt 1 view .LVU188
 609              		.loc 1 1720 31 is_stmt 0 view .LVU189
 610 000c 03F00044 		and	r4, r3, #-**********
 611              		.loc 1 1720 18 view .LVU190
 612 0010 0460     		str	r4, [r0]
1721:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1722:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   (*PCROPStartAddr) = ((regvalue & FLASH_PRAR_PROT_AREA_START) << 8) + bankBase;
 613              		.loc 1 1722 3 is_stmt 1 view .LVU191
 614              		.loc 1 1722 64 is_stmt 0 view .LVU192
 615 0012 0948     		ldr	r0, .L55
 616              	.LVL55:
 617              		.loc 1 1722 64 view .LVU193
 618 0014 00EA0320 		and	r0, r0, r3, lsl #8
 619              		.loc 1 1722 70 view .LVU194
 620 0018 6044     		add	r0, r0, ip
 621              		.loc 1 1722 21 view .LVU195
 622 001a 0860     		str	r0, [r1]
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 42


1723:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   (*PCROPEndAddr) = (regvalue & FLASH_PRAR_PROT_AREA_END) >> FLASH_PRAR_PROT_AREA_END_Pos;
 623              		.loc 1 1723 3 is_stmt 1 view .LVU196
 624              		.loc 1 1723 59 is_stmt 0 view .LVU197
 625 001c C3F30B43 		ubfx	r3, r3, #16, #12
 626              	.LVL56:
 627              		.loc 1 1723 19 view .LVU198
 628 0020 1360     		str	r3, [r2]
1724:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   (*PCROPEndAddr) = ((*PCROPEndAddr) << 8) + bankBase;
 629              		.loc 1 1724 3 is_stmt 1 view .LVU199
 630              		.loc 1 1724 44 is_stmt 0 view .LVU200
 631 0022 0CEB0323 		add	r3, ip, r3, lsl #8
 632              		.loc 1 1724 19 view .LVU201
 633 0026 1360     		str	r3, [r2]
1725:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** }
 634              		.loc 1 1725 1 view .LVU202
 635 0028 5DF8044B 		ldr	r4, [sp], #4
 636              	.LCFI1:
 637              		.cfi_remember_state
 638              		.cfi_restore 4
 639              		.cfi_def_cfa_offset 0
 640 002c 7047     		bx	lr
 641              	.LVL57:
 642              	.L54:
 643              	.LCFI2:
 644              		.cfi_restore_state
1708:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     bankBase = FLASH_BANK1_BASE;
 645              		.loc 1 1708 5 is_stmt 1 view .LVU203
1708:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     bankBase = FLASH_BANK1_BASE;
 646              		.loc 1 1708 14 is_stmt 0 view .LVU204
 647 002e 034B     		ldr	r3, .L55+4
 648              	.LVL58:
1708:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     bankBase = FLASH_BANK1_BASE;
 649              		.loc 1 1708 14 view .LVU205
 650 0030 9B6A     		ldr	r3, [r3, #40]
 651              	.LVL59:
1709:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
 652              		.loc 1 1709 5 is_stmt 1 view .LVU206
1709:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
 653              		.loc 1 1709 14 is_stmt 0 view .LVU207
 654 0032 4FF0006C 		mov	ip, #*********
 655 0036 E9E7     		b	.L51
 656              	.L56:
 657              		.align	2
 658              	.L55:
 659 0038 00FF0F00 		.word	1048320
 660 003c ******** 		.word	**********
 661              		.cfi_endproc
 662              	.LFE161:
 664              		.section	.text.FLASH_OB_BOR_LevelConfig,"ax",%progbits
 665              		.align	1
 666              		.syntax unified
 667              		.thumb
 668              		.thumb_func
 670              	FLASH_OB_BOR_LevelConfig:
 671              	.LVL60:
 672              	.LFB162:
1726:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 43


1727:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /**
1728:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @brief  Set the BOR Level.
1729:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @param  Level specifies the Option Bytes BOR Reset Level.
1730:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *          This parameter can be one of the following values:
1731:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *            @arg OB_BOR_LEVEL0: Reset level threshold is set to 1.6V
1732:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *            @arg OB_BOR_LEVEL1: Reset level threshold is set to 2.1V
1733:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *            @arg OB_BOR_LEVEL2: Reset level threshold is set to 2.4V
1734:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *            @arg OB_BOR_LEVEL3: Reset level threshold is set to 2.7V
1735:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @retval None
1736:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   */
1737:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** static void FLASH_OB_BOR_LevelConfig(uint32_t Level)
1738:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** {
 673              		.loc 1 1738 1 is_stmt 1 view -0
 674              		.cfi_startproc
 675              		@ args = 0, pretend = 0, frame = 0
 676              		@ frame_needed = 0, uses_anonymous_args = 0
 677              		@ link register save eliminated.
1739:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   assert_param(IS_OB_BOR_LEVEL(Level));
 678              		.loc 1 1739 3 view .LVU209
1740:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1741:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   /* Configure BOR_LEV option byte */
1742:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   MODIFY_REG(FLASH->OPTSR_PRG, FLASH_OPTSR_BOR_LEV, Level);
 679              		.loc 1 1742 3 view .LVU210
 680 0000 034A     		ldr	r2, .L58
 681 0002 136A     		ldr	r3, [r2, #32]
 682 0004 23F00C03 		bic	r3, r3, #12
 683 0008 0343     		orrs	r3, r3, r0
 684 000a 1362     		str	r3, [r2, #32]
1743:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** }
 685              		.loc 1 1743 1 is_stmt 0 view .LVU211
 686 000c 7047     		bx	lr
 687              	.L59:
 688 000e 00BF     		.align	2
 689              	.L58:
 690 0010 ******** 		.word	**********
 691              		.cfi_endproc
 692              	.LFE162:
 694              		.section	.text.FLASH_OB_GetBOR,"ax",%progbits
 695              		.align	1
 696              		.syntax unified
 697              		.thumb
 698              		.thumb_func
 700              	FLASH_OB_GetBOR:
 701              	.LFB163:
1744:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1745:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /**
1746:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @brief  Get the BOR Level.
1747:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @retval The Option Bytes BOR Reset Level.
1748:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *            This parameter can be one of the following values:
1749:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *            @arg OB_BOR_LEVEL0: Reset level threshold is set to 1.6V
1750:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *            @arg OB_BOR_LEVEL1: Reset level threshold is set to 2.1V
1751:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *            @arg OB_BOR_LEVEL2: Reset level threshold is set to 2.4V
1752:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *            @arg OB_BOR_LEVEL3: Reset level threshold is set to 2.7V
1753:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   */
1754:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** static uint32_t FLASH_OB_GetBOR(void)
1755:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** {
 702              		.loc 1 1755 1 is_stmt 1 view -0
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 44


 703              		.cfi_startproc
 704              		@ args = 0, pretend = 0, frame = 0
 705              		@ frame_needed = 0, uses_anonymous_args = 0
 706              		@ link register save eliminated.
1756:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   return (FLASH->OPTSR_CUR & FLASH_OPTSR_BOR_LEV);
 707              		.loc 1 1756 3 view .LVU213
 708              		.loc 1 1756 16 is_stmt 0 view .LVU214
 709 0000 024B     		ldr	r3, .L61
 710 0002 D869     		ldr	r0, [r3, #28]
1757:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** }
 711              		.loc 1 1757 1 view .LVU215
 712 0004 00F00C00 		and	r0, r0, #12
 713 0008 7047     		bx	lr
 714              	.L62:
 715 000a 00BF     		.align	2
 716              	.L61:
 717 000c ******** 		.word	**********
 718              		.cfi_endproc
 719              	.LFE163:
 721              		.section	.text.FLASH_OB_BootAddConfig,"ax",%progbits
 722              		.align	1
 723              		.syntax unified
 724              		.thumb
 725              		.thumb_func
 727              	FLASH_OB_BootAddConfig:
 728              	.LVL61:
 729              	.LFB164:
1758:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1759:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /**
1760:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @brief  Set Boot address
1761:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @param  BootOption Boot address option byte to be programmed,
1762:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *                     This parameter must be a value of @ref FLASHEx_OB_BOOT_OPTION
1763:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****                         (OB_BOOT_ADD0, OB_BOOT_ADD1 or OB_BOOT_ADD_BOTH)
1764:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *
1765:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @param  BootAddress0 Specifies the Boot Address 0
1766:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @param  BootAddress1 Specifies the Boot Address 1
1767:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @retval HAL Status
1768:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   */
1769:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** static void FLASH_OB_BootAddConfig(uint32_t BootOption, uint32_t BootAddress0, uint32_t BootAddress
1770:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** {
 730              		.loc 1 1770 1 is_stmt 1 view -0
 731              		.cfi_startproc
 732              		@ args = 0, pretend = 0, frame = 0
 733              		@ frame_needed = 0, uses_anonymous_args = 0
 734              		@ link register save eliminated.
 735              		.loc 1 1770 1 is_stmt 0 view .LVU217
 736 0000 10B4     		push	{r4}
 737              	.LCFI3:
 738              		.cfi_def_cfa_offset 4
 739              		.cfi_offset 4, -4
1771:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   /* Check the parameters */
1772:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   assert_param(IS_OB_BOOT_ADD_OPTION(BootOption));
 740              		.loc 1 1772 3 is_stmt 1 view .LVU218
1773:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1774:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   if((BootOption & OB_BOOT_ADD0) == OB_BOOT_ADD0)
 741              		.loc 1 1774 3 view .LVU219
 742              		.loc 1 1774 5 is_stmt 0 view .LVU220
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 45


 743 0002 10F0010F 		tst	r0, #1
 744 0006 06D0     		beq	.L64
1775:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
1776:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* Check the parameters */
1777:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     assert_param(IS_BOOT_ADDRESS(BootAddress0));
 745              		.loc 1 1777 5 is_stmt 1 view .LVU221
1778:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1779:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* Configure CM7 BOOT ADD0 */
1780:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #if defined(DUAL_CORE)
1781:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     MODIFY_REG(FLASH->BOOT7_PRG, FLASH_BOOT7_BCM7_ADD0, (BootAddress0 >> 16));
1782:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #else /* Single Core*/
1783:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     MODIFY_REG(FLASH->BOOT_PRG, FLASH_BOOT_ADD0, (BootAddress0 >> 16));
 746              		.loc 1 1783 5 view .LVU222
 747 0008 084C     		ldr	r4, .L67
 748 000a 636C     		ldr	r3, [r4, #68]
 749 000c 6FF30F03 		bfc	r3, #0, #16
 750 0010 43EA1143 		orr	r3, r3, r1, lsr #16
 751 0014 6364     		str	r3, [r4, #68]
 752              	.L64:
1784:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /* DUAL_CORE */
1785:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
1786:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1787:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   if((BootOption & OB_BOOT_ADD1) == OB_BOOT_ADD1)
 753              		.loc 1 1787 3 view .LVU223
 754              		.loc 1 1787 5 is_stmt 0 view .LVU224
 755 0016 10F0020F 		tst	r0, #2
 756 001a 04D0     		beq	.L63
1788:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
1789:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* Check the parameters */
1790:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     assert_param(IS_BOOT_ADDRESS(BootAddress1));
 757              		.loc 1 1790 5 is_stmt 1 view .LVU225
1791:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1792:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* Configure CM7 BOOT ADD1 */
1793:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #if defined(DUAL_CORE)
1794:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     MODIFY_REG(FLASH->BOOT7_PRG, FLASH_BOOT7_BCM7_ADD1, BootAddress1);
1795:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #else /* Single Core*/
1796:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     MODIFY_REG(FLASH->BOOT_PRG, FLASH_BOOT_ADD1, BootAddress1);
 758              		.loc 1 1796 5 view .LVU226
 759 001c 0349     		ldr	r1, .L67
 760              	.LVL62:
 761              		.loc 1 1796 5 is_stmt 0 view .LVU227
 762 001e 4B6C     		ldr	r3, [r1, #68]
 763 0020 9BB2     		uxth	r3, r3
 764 0022 1343     		orrs	r3, r3, r2
 765 0024 4B64     		str	r3, [r1, #68]
 766              	.L63:
1797:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /* DUAL_CORE */
1798:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
1799:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** }
 767              		.loc 1 1799 1 view .LVU228
 768 0026 5DF8044B 		ldr	r4, [sp], #4
 769              	.LCFI4:
 770              		.cfi_restore 4
 771              		.cfi_def_cfa_offset 0
 772 002a 7047     		bx	lr
 773              	.L68:
 774              		.align	2
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 46


 775              	.L67:
 776 002c ******** 		.word	**********
 777              		.cfi_endproc
 778              	.LFE164:
 780              		.section	.text.FLASH_OB_GetBootAdd,"ax",%progbits
 781              		.align	1
 782              		.syntax unified
 783              		.thumb
 784              		.thumb_func
 786              	FLASH_OB_GetBootAdd:
 787              	.LVL63:
 788              	.LFB165:
1800:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1801:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /**
1802:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @brief  Get Boot address
1803:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @param  BootAddress0 Specifies the Boot Address 0.
1804:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @param  BootAddress1 Specifies the Boot Address 1.
1805:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @retval HAL Status
1806:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   */
1807:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** static void FLASH_OB_GetBootAdd(uint32_t *BootAddress0, uint32_t *BootAddress1)
1808:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** {
 789              		.loc 1 1808 1 is_stmt 1 view -0
 790              		.cfi_startproc
 791              		@ args = 0, pretend = 0, frame = 0
 792              		@ frame_needed = 0, uses_anonymous_args = 0
 793              		@ link register save eliminated.
1809:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   uint32_t regvalue;
 794              		.loc 1 1809 3 view .LVU230
1810:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1811:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #if defined(DUAL_CORE)
1812:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   regvalue = FLASH->BOOT7_CUR;
1813:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1814:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   (*BootAddress0) = (regvalue & FLASH_BOOT7_BCM7_ADD0) << 16;
1815:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   (*BootAddress1) = (regvalue & FLASH_BOOT7_BCM7_ADD1);
1816:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #else /* Single Core */
1817:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   regvalue = FLASH->BOOT_CUR;
 795              		.loc 1 1817 3 view .LVU231
 796              		.loc 1 1817 12 is_stmt 0 view .LVU232
 797 0000 034B     		ldr	r3, .L70
 798 0002 1B6C     		ldr	r3, [r3, #64]
 799              	.LVL64:
1818:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1819:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   (*BootAddress0) = (regvalue & FLASH_BOOT_ADD0) << 16;
 800              		.loc 1 1819 3 is_stmt 1 view .LVU233
 801              		.loc 1 1819 50 is_stmt 0 view .LVU234
 802 0004 1A04     		lsls	r2, r3, #16
 803              		.loc 1 1819 19 view .LVU235
 804 0006 0260     		str	r2, [r0]
1820:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   (*BootAddress1) = (regvalue & FLASH_BOOT_ADD1);
 805              		.loc 1 1820 3 is_stmt 1 view .LVU236
 806              		.loc 1 1820 31 is_stmt 0 view .LVU237
 807 0008 6FF30F03 		bfc	r3, #0, #16
 808              	.LVL65:
 809              		.loc 1 1820 19 view .LVU238
 810 000c 0B60     		str	r3, [r1]
1821:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /* DUAL_CORE */
1822:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** }
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 47


 811              		.loc 1 1822 1 view .LVU239
 812 000e 7047     		bx	lr
 813              	.L71:
 814              		.align	2
 815              	.L70:
 816 0010 ******** 		.word	**********
 817              		.cfi_endproc
 818              	.LFE165:
 820              		.section	.text.FLASH_OB_SecureAreaConfig,"ax",%progbits
 821              		.align	1
 822              		.syntax unified
 823              		.thumb
 824              		.thumb_func
 826              	FLASH_OB_SecureAreaConfig:
 827              	.LVL66:
 828              	.LFB166:
1823:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1824:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #if defined(DUAL_CORE)
1825:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /**
1826:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @brief  Set CM4 Boot address
1827:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @param  BootOption Boot address option byte to be programmed,
1828:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *                     This parameter must be a value of @ref FLASHEx_OB_BOOT_OPTION
1829:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****                         (OB_BOOT_ADD0, OB_BOOT_ADD1 or OB_BOOT_ADD_BOTH)
1830:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *
1831:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @param  BootAddress0 Specifies the CM4 Boot Address 0.
1832:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @param  BootAddress1 Specifies the CM4 Boot Address 1.
1833:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @retval HAL Status
1834:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   */
1835:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** static void FLASH_OB_CM4BootAddConfig(uint32_t BootOption, uint32_t BootAddress0, uint32_t BootAddr
1836:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** {
1837:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   /* Check the parameters */
1838:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   assert_param(IS_OB_BOOT_ADD_OPTION(BootOption));
1839:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1840:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   if((BootOption & OB_BOOT_ADD0) == OB_BOOT_ADD0)
1841:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
1842:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* Check the parameters */
1843:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     assert_param(IS_BOOT_ADDRESS(BootAddress0));
1844:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1845:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* Configure CM4 BOOT ADD0 */
1846:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     MODIFY_REG(FLASH->BOOT4_PRG, FLASH_BOOT4_BCM4_ADD0, (BootAddress0 >> 16));
1847:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1848:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
1849:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1850:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   if((BootOption & OB_BOOT_ADD1) == OB_BOOT_ADD1)
1851:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
1852:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* Check the parameters */
1853:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     assert_param(IS_BOOT_ADDRESS(BootAddress1));
1854:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1855:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* Configure CM4 BOOT ADD1 */
1856:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     MODIFY_REG(FLASH->BOOT4_PRG, FLASH_BOOT4_BCM4_ADD1, BootAddress1);
1857:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
1858:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** }
1859:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1860:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /**
1861:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @brief  Get CM4 Boot address
1862:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @param  BootAddress0 Specifies the CM4 Boot Address 0.
1863:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @param  BootAddress1 Specifies the CM4 Boot Address 1.
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 48


1864:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @retval HAL Status
1865:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   */
1866:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** static void FLASH_OB_GetCM4BootAdd(uint32_t *BootAddress0, uint32_t *BootAddress1)
1867:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** {
1868:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   uint32_t regvalue;
1869:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1870:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   regvalue = FLASH->BOOT4_CUR;
1871:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1872:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   (*BootAddress0) = (regvalue & FLASH_BOOT4_BCM4_ADD0) << 16;
1873:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   (*BootAddress1) = (regvalue & FLASH_BOOT4_BCM4_ADD1);
1874:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** }
1875:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /*DUAL_CORE*/
1876:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1877:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /**
1878:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @brief  Set secure area configuration
1879:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @param  SecureAreaConfig specify if the secure area will be deleted or not
1880:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *         when RDP level decreased from Level 1 to Level 0 or during a mass erase.
1881:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *
1882:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @param  SecureAreaStartAddr Specifies the secure area start address
1883:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @param  SecureAreaEndAddr Specifies the secure area end address
1884:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @param  Banks the specific bank to apply Security protection
1885:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *          This parameter can be one of the following values:
1886:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *            @arg FLASH_BANK_1: Secure area on specified bank1 area
1887:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *            @arg FLASH_BANK_2: Secure area on specified bank2 area
1888:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *            @arg FLASH_BANK_BOTH: Secure area on specified bank1 and bank2 area (same config wil
1889:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @retval None
1890:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   */
1891:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** static void FLASH_OB_SecureAreaConfig(uint32_t SecureAreaConfig, uint32_t SecureAreaStartAddr, uint
1892:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** {
 829              		.loc 1 1892 1 is_stmt 1 view -0
 830              		.cfi_startproc
 831              		@ args = 0, pretend = 0, frame = 0
 832              		@ frame_needed = 0, uses_anonymous_args = 0
 833              		@ link register save eliminated.
1893:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   /* Check the parameters */
1894:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   assert_param(IS_FLASH_BANK(Banks));
 834              		.loc 1 1894 3 view .LVU241
1895:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   assert_param(IS_OB_SECURE_RDP(SecureAreaConfig));
 835              		.loc 1 1895 3 view .LVU242
1896:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1897:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   if((Banks & FLASH_BANK_1) == FLASH_BANK_1)
 836              		.loc 1 1897 3 view .LVU243
 837              		.loc 1 1897 5 is_stmt 0 view .LVU244
 838 0000 13F0010F 		tst	r3, #1
 839 0004 0CD0     		beq	.L72
1898:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
1899:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* Check the parameters */
1900:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     assert_param(IS_FLASH_PROGRAM_ADDRESS_BANK1(SecureAreaStartAddr));
 840              		.loc 1 1900 5 is_stmt 1 view .LVU245
1901:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     assert_param(IS_FLASH_PROGRAM_ADDRESS_BANK1(SecureAreaEndAddr));
 841              		.loc 1 1901 5 view .LVU246
1902:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1903:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* Configure the secure area */
1904:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     FLASH->SCAR_PRG1 = ((SecureAreaStartAddr - FLASH_BANK1_BASE) >> 8)                             
 842              		.loc 1 1904 5 view .LVU247
 843              		.loc 1 1904 46 is_stmt 0 view .LVU248
 844 0006 01F17841 		add	r1, r1, #-*********
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 49


 845              	.LVL67:
1905:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****                        (((SecureAreaEndAddr - FLASH_BANK1_BASE) >> 8) << FLASH_SCAR_SEC_AREA_END_Po
 846              		.loc 1 1905 45 view .LVU249
 847 000a 02F17843 		add	r3, r2, #-*********
 848              	.LVL68:
 849              		.loc 1 1905 65 view .LVU250
 850 000e 1B0A     		lsrs	r3, r3, #8
 851              		.loc 1 1905 71 view .LVU251
 852 0010 1B04     		lsls	r3, r3, #16
1904:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****                        (((SecureAreaEndAddr - FLASH_BANK1_BASE) >> 8) << FLASH_SCAR_SEC_AREA_END_Po
 853              		.loc 1 1904 103 view .LVU252
 854 0012 43EA1123 		orr	r3, r3, r1, lsr #8
1906:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****                        (SecureAreaConfig & FLASH_SCAR_DMES);
 855              		.loc 1 1906 42 view .LVU253
 856 0016 00F00040 		and	r0, r0, #-**********
 857              	.LVL69:
1905:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****                        (((SecureAreaEndAddr - FLASH_BANK1_BASE) >> 8) << FLASH_SCAR_SEC_AREA_END_Po
 858              		.loc 1 1905 103 view .LVU254
 859 001a 0343     		orrs	r3, r3, r0
1904:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****                        (((SecureAreaEndAddr - FLASH_BANK1_BASE) >> 8) << FLASH_SCAR_SEC_AREA_END_Po
 860              		.loc 1 1904 22 view .LVU255
 861 001c 014A     		ldr	r2, .L74
 862              	.LVL70:
1904:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****                        (((SecureAreaEndAddr - FLASH_BANK1_BASE) >> 8) << FLASH_SCAR_SEC_AREA_END_Po
 863              		.loc 1 1904 22 view .LVU256
 864 001e 5363     		str	r3, [r2, #52]
 865              	.LVL71:
 866              	.L72:
1907:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
1908:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1909:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #if defined (DUAL_BANK)
1910:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   if((Banks & FLASH_BANK_2) == FLASH_BANK_2)
1911:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
1912:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* Check the parameters */
1913:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     assert_param(IS_FLASH_PROGRAM_ADDRESS_BANK2(SecureAreaStartAddr));
1914:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     assert_param(IS_FLASH_PROGRAM_ADDRESS_BANK2(SecureAreaEndAddr));
1915:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1916:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* Configure the secure area */
1917:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     FLASH->SCAR_PRG2 = ((SecureAreaStartAddr - FLASH_BANK2_BASE) >> 8)                             
1918:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****                        (((SecureAreaEndAddr - FLASH_BANK2_BASE) >> 8) << FLASH_SCAR_SEC_AREA_END_Po
1919:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****                        (SecureAreaConfig & FLASH_SCAR_DMES);
1920:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
1921:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /* DUAL_BANK */
1922:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** }
 867              		.loc 1 1922 1 view .LVU257
 868 0020 7047     		bx	lr
 869              	.L75:
 870 0022 00BF     		.align	2
 871              	.L74:
 872 0024 ******** 		.word	**********
 873              		.cfi_endproc
 874              	.LFE166:
 876              		.section	.text.FLASH_OB_GetSecureArea,"ax",%progbits
 877              		.align	1
 878              		.syntax unified
 879              		.thumb
 880              		.thumb_func
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 50


 882              	FLASH_OB_GetSecureArea:
 883              	.LVL72:
 884              	.LFB167:
1923:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1924:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /**
1925:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @brief  Get secure area configuration
1926:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @param  SecureAreaConfig indicates if the secure area will be deleted or not
1927:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *         when RDP level decreased from Level 1 to Level 0 or during a mass erase.
1928:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @param  SecureAreaStartAddr gives the secure area start address
1929:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @param  SecureAreaEndAddr gives the secure area end address
1930:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @param  Bank Specifies the Bank
1931:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @retval None
1932:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   */
1933:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** static void FLASH_OB_GetSecureArea(uint32_t *SecureAreaConfig, uint32_t *SecureAreaStartAddr, uint3
1934:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** {
 885              		.loc 1 1934 1 is_stmt 1 view -0
 886              		.cfi_startproc
 887              		@ args = 0, pretend = 0, frame = 0
 888              		@ frame_needed = 0, uses_anonymous_args = 0
 889              		@ link register save eliminated.
 890              		.loc 1 1934 1 is_stmt 0 view .LVU259
 891 0000 10B4     		push	{r4}
 892              	.LCFI5:
 893              		.cfi_def_cfa_offset 4
 894              		.cfi_offset 4, -4
1935:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   uint32_t regvalue = 0;
 895              		.loc 1 1935 3 is_stmt 1 view .LVU260
 896              	.LVL73:
1936:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   uint32_t bankBase = 0;
 897              		.loc 1 1936 3 view .LVU261
1937:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1938:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   /* Check Bank parameter value */
1939:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   if(Bank == FLASH_BANK_1)
 898              		.loc 1 1939 3 view .LVU262
 899              		.loc 1 1939 5 is_stmt 0 view .LVU263
 900 0002 012B     		cmp	r3, #1
 901 0004 13D0     		beq	.L80
1936:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   uint32_t bankBase = 0;
 902              		.loc 1 1936 12 view .LVU264
 903 0006 4FF0000C 		mov	ip, #0
1935:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   uint32_t bankBase = 0;
 904              		.loc 1 1935 12 view .LVU265
 905 000a 6346     		mov	r3, ip
 906              	.LVL74:
 907              	.L77:
1940:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
1941:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     regvalue = FLASH->SCAR_CUR1;
1942:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     bankBase = FLASH_BANK1_BASE;
1943:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
1944:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1945:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #if defined (DUAL_BANK)
1946:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   if(Bank == FLASH_BANK_2)
1947:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
1948:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     regvalue = FLASH->SCAR_CUR2;
1949:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     bankBase = FLASH_BANK2_BASE;
1950:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
1951:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /* DUAL_BANK */
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 51


1952:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1953:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   /* Get the secure area settings */
1954:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   (*SecureAreaConfig) = (regvalue & FLASH_SCAR_DMES);
 908              		.loc 1 1954 3 is_stmt 1 view .LVU266
 909              		.loc 1 1954 35 is_stmt 0 view .LVU267
 910 000c 03F00044 		and	r4, r3, #-**********
 911              		.loc 1 1954 23 view .LVU268
 912 0010 0460     		str	r4, [r0]
1955:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   (*SecureAreaStartAddr) = ((regvalue & FLASH_SCAR_SEC_AREA_START) << 8) + bankBase;
 913              		.loc 1 1955 3 is_stmt 1 view .LVU269
 914              		.loc 1 1955 68 is_stmt 0 view .LVU270
 915 0012 0948     		ldr	r0, .L81
 916              	.LVL75:
 917              		.loc 1 1955 68 view .LVU271
 918 0014 00EA0320 		and	r0, r0, r3, lsl #8
 919              		.loc 1 1955 74 view .LVU272
 920 0018 6044     		add	r0, r0, ip
 921              		.loc 1 1955 26 view .LVU273
 922 001a 0860     		str	r0, [r1]
1956:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   (*SecureAreaEndAddr) = (regvalue & FLASH_SCAR_SEC_AREA_END) >> FLASH_SCAR_SEC_AREA_END_Pos;
 923              		.loc 1 1956 3 is_stmt 1 view .LVU274
 924              		.loc 1 1956 63 is_stmt 0 view .LVU275
 925 001c C3F30B43 		ubfx	r3, r3, #16, #12
 926              	.LVL76:
 927              		.loc 1 1956 24 view .LVU276
 928 0020 1360     		str	r3, [r2]
1957:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   (*SecureAreaEndAddr) = ((*SecureAreaEndAddr) << 8) + bankBase;
 929              		.loc 1 1957 3 is_stmt 1 view .LVU277
 930              		.loc 1 1957 54 is_stmt 0 view .LVU278
 931 0022 0CEB0323 		add	r3, ip, r3, lsl #8
 932              		.loc 1 1957 24 view .LVU279
 933 0026 1360     		str	r3, [r2]
1958:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** }
 934              		.loc 1 1958 1 view .LVU280
 935 0028 5DF8044B 		ldr	r4, [sp], #4
 936              	.LCFI6:
 937              		.cfi_remember_state
 938              		.cfi_restore 4
 939              		.cfi_def_cfa_offset 0
 940 002c 7047     		bx	lr
 941              	.LVL77:
 942              	.L80:
 943              	.LCFI7:
 944              		.cfi_restore_state
1941:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     bankBase = FLASH_BANK1_BASE;
 945              		.loc 1 1941 5 is_stmt 1 view .LVU281
1941:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     bankBase = FLASH_BANK1_BASE;
 946              		.loc 1 1941 14 is_stmt 0 view .LVU282
 947 002e 034B     		ldr	r3, .L81+4
 948              	.LVL78:
1941:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     bankBase = FLASH_BANK1_BASE;
 949              		.loc 1 1941 14 view .LVU283
 950 0030 1B6B     		ldr	r3, [r3, #48]
 951              	.LVL79:
1942:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
 952              		.loc 1 1942 5 is_stmt 1 view .LVU284
1942:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 52


 953              		.loc 1 1942 14 is_stmt 0 view .LVU285
 954 0032 4FF0006C 		mov	ip, #*********
 955 0036 E9E7     		b	.L77
 956              	.L82:
 957              		.align	2
 958              	.L81:
 959 0038 00FF0F00 		.word	1048320
 960 003c ******** 		.word	**********
 961              		.cfi_endproc
 962              	.LFE167:
 964              		.section	.text.FLASH_CRC_AddSector,"ax",%progbits
 965              		.align	1
 966              		.syntax unified
 967              		.thumb
 968              		.thumb_func
 970              	FLASH_CRC_AddSector:
 971              	.LVL80:
 972              	.LFB168:
1959:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1960:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /**
1961:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @brief  Add a CRC sector to the list of sectors on which the CRC will be calculated
1962:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @param  Sector Specifies the CRC sector number
1963:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @param  Bank Specifies the Bank
1964:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @retval None
1965:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   */
1966:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** static void FLASH_CRC_AddSector(uint32_t Sector, uint32_t Bank)
1967:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** {
 973              		.loc 1 1967 1 is_stmt 1 view -0
 974              		.cfi_startproc
 975              		@ args = 0, pretend = 0, frame = 0
 976              		@ frame_needed = 0, uses_anonymous_args = 0
 977              		@ link register save eliminated.
1968:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   /* Check the parameters */
1969:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   assert_param(IS_FLASH_SECTOR(Sector));
 978              		.loc 1 1969 3 view .LVU287
1970:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1971:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   if (Bank == FLASH_BANK_1)
 979              		.loc 1 1971 3 view .LVU288
 980              		.loc 1 1971 6 is_stmt 0 view .LVU289
 981 0000 0129     		cmp	r1, #1
 982 0002 00D0     		beq	.L85
 983              	.L83:
1972:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
1973:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* Clear CRC sector */
1974:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     FLASH->CRCCR1 &= (~FLASH_CRCCR_CRC_SECT);
1975:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1976:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* Select CRC Sector and activate ADD_SECT bit */
1977:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     FLASH->CRCCR1 |= Sector | FLASH_CRCCR_ADD_SECT;
1978:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
1979:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #if defined (DUAL_BANK)
1980:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   else
1981:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
1982:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* Clear CRC sector */
1983:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     FLASH->CRCCR2 &= (~FLASH_CRCCR_CRC_SECT);
1984:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1985:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* Select CRC Sector and activate ADD_SECT bit */
1986:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     FLASH->CRCCR2 |= Sector | FLASH_CRCCR_ADD_SECT;
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 53


1987:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
1988:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /* DUAL_BANK */
1989:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** }
 984              		.loc 1 1989 1 view .LVU290
 985 0004 7047     		bx	lr
 986              	.L85:
1974:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 987              		.loc 1 1974 5 is_stmt 1 view .LVU291
1974:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 988              		.loc 1 1974 10 is_stmt 0 view .LVU292
 989 0006 054A     		ldr	r2, .L86
 990 0008 136D     		ldr	r3, [r2, #80]
1974:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 991              		.loc 1 1974 19 view .LVU293
 992 000a 23F00703 		bic	r3, r3, #7
 993 000e 1365     		str	r3, [r2, #80]
1977:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
 994              		.loc 1 1977 5 is_stmt 1 view .LVU294
1977:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
 995              		.loc 1 1977 10 is_stmt 0 view .LVU295
 996 0010 136D     		ldr	r3, [r2, #80]
1977:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
 997              		.loc 1 1977 19 view .LVU296
 998 0012 0343     		orrs	r3, r3, r0
 999 0014 43F40073 		orr	r3, r3, #512
 1000 0018 1365     		str	r3, [r2, #80]
 1001              		.loc 1 1989 1 view .LVU297
 1002 001a F3E7     		b	.L83
 1003              	.L87:
 1004              		.align	2
 1005              	.L86:
 1006 001c ******** 		.word	**********
 1007              		.cfi_endproc
 1008              	.LFE168:
 1010              		.section	.text.FLASH_CRC_SelectAddress,"ax",%progbits
 1011              		.align	1
 1012              		.syntax unified
 1013              		.thumb
 1014              		.thumb_func
 1016              	FLASH_CRC_SelectAddress:
 1017              	.LVL81:
 1018              	.LFB169:
1990:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
1991:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /**
1992:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @brief  Select CRC start and end memory addresses on which the CRC will be calculated
1993:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @param  CRCStartAddr Specifies the CRC start address
1994:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @param  CRCEndAddr Specifies the CRC end address
1995:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @param  Bank Specifies the Bank
1996:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @retval None
1997:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   */
1998:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** static void FLASH_CRC_SelectAddress(uint32_t CRCStartAddr, uint32_t CRCEndAddr, uint32_t Bank)
1999:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** {
 1019              		.loc 1 1999 1 is_stmt 1 view -0
 1020              		.cfi_startproc
 1021              		@ args = 0, pretend = 0, frame = 0
 1022              		@ frame_needed = 0, uses_anonymous_args = 0
 1023              		@ link register save eliminated.
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 54


2000:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   if (Bank == FLASH_BANK_1)
 1024              		.loc 1 2000 3 view .LVU299
 1025              		.loc 1 2000 6 is_stmt 0 view .LVU300
 1026 0000 012A     		cmp	r2, #1
 1027 0002 00D0     		beq	.L90
 1028              	.L88:
2001:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
2002:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     assert_param(IS_FLASH_PROGRAM_ADDRESS_BANK1(CRCStartAddr));
2003:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     assert_param(IS_FLASH_PROGRAM_ADDRESS_BANK1(CRCEndAddr));
2004:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
2005:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* Write CRC Start and End addresses */
2006:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     FLASH->CRCSADD1 = CRCStartAddr;
2007:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     FLASH->CRCEADD1 = CRCEndAddr;
2008:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
2009:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #if defined (DUAL_BANK)
2010:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   else
2011:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
2012:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     assert_param(IS_FLASH_PROGRAM_ADDRESS_BANK2(CRCStartAddr));
2013:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     assert_param(IS_FLASH_PROGRAM_ADDRESS_BANK2(CRCEndAddr));
2014:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
2015:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     /* Write CRC Start and End addresses */
2016:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     FLASH->CRCSADD2 = CRCStartAddr;
2017:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     FLASH->CRCEADD2 = CRCEndAddr;
2018:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
2019:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /* DUAL_BANK */
2020:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** }
 1029              		.loc 1 2020 1 view .LVU301
 1030 0004 7047     		bx	lr
 1031              	.L90:
2002:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     assert_param(IS_FLASH_PROGRAM_ADDRESS_BANK1(CRCEndAddr));
 1032              		.loc 1 2002 5 is_stmt 1 view .LVU302
2003:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1033              		.loc 1 2003 5 view .LVU303
2006:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     FLASH->CRCEADD1 = CRCEndAddr;
 1034              		.loc 1 2006 5 view .LVU304
2006:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     FLASH->CRCEADD1 = CRCEndAddr;
 1035              		.loc 1 2006 21 is_stmt 0 view .LVU305
 1036 0006 024B     		ldr	r3, .L91
 1037 0008 5865     		str	r0, [r3, #84]
2007:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
 1038              		.loc 1 2007 5 is_stmt 1 view .LVU306
2007:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
 1039              		.loc 1 2007 21 is_stmt 0 view .LVU307
 1040 000a 9965     		str	r1, [r3, #88]
 1041              		.loc 1 2020 1 view .LVU308
 1042 000c FAE7     		b	.L88
 1043              	.L92:
 1044 000e 00BF     		.align	2
 1045              	.L91:
 1046 0010 ******** 		.word	**********
 1047              		.cfi_endproc
 1048              	.LFE169:
 1050              		.section	.text.FLASH_OB_SharedRAM_Config,"ax",%progbits
 1051              		.align	1
 1052              		.syntax unified
 1053              		.thumb
 1054              		.thumb_func
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 55


 1056              	FLASH_OB_SharedRAM_Config:
 1057              	.LVL82:
 1058              	.LFB170:
2021:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /**
2022:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @}
2023:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   */
2024:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
2025:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #if defined (FLASH_OTPBL_LOCKBL)
2026:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /**
2027:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @brief  Configure the OTP Block Lock.
2028:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @param  OTP_Block specifies the OTP Block to lock.
2029:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *         This parameter can be a value of @ref FLASHEx_OTP_Blocks
2030:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @retval None
2031:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   */
2032:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** static void FLASH_OB_OTP_LockConfig(uint32_t OTP_Block)
2033:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** {
2034:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   /* Check the parameters */
2035:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   assert_param(IS_OTP_BLOCK(OTP_Block));
2036:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
2037:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   /* Configure the OTP Block lock in the option bytes register */
2038:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   FLASH->OTPBL_PRG |= (OTP_Block & FLASH_OTPBL_LOCKBL);
2039:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** }
2040:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
2041:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /**
2042:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @brief  Get the OTP Block Lock.
2043:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @retval OTP_Block specifies the OTP Block to lock.
2044:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *         This return value can be a value of @ref FLASHEx_OTP_Blocks
2045:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   */
2046:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** static uint32_t FLASH_OB_OTP_GetLock(void)
2047:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** {
2048:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   return (FLASH->OTPBL_CUR);
2049:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** }
2050:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /* FLASH_OTPBL_LOCKBL */
2051:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
2052:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #if defined (FLASH_OPTSR2_TCM_AXI_SHARED)
2053:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /**
2054:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @brief  Configure the TCM / AXI Shared RAM.
2055:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @param  SharedRamConfig specifies the Shared RAM configuration.
2056:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *         This parameter can be a value of @ref FLASHEx_OB_TCM_AXI_SHARED
2057:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @retval None
2058:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   */
2059:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** static void FLASH_OB_SharedRAM_Config(uint32_t SharedRamConfig)
2060:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** {
 1059              		.loc 1 2060 1 is_stmt 1 view -0
 1060              		.cfi_startproc
 1061              		@ args = 0, pretend = 0, frame = 0
 1062              		@ frame_needed = 0, uses_anonymous_args = 0
 1063              		@ link register save eliminated.
2061:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   /* Check the parameters */
2062:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   assert_param(IS_OB_USER_TCM_AXI_SHARED(SharedRamConfig));
 1064              		.loc 1 2062 3 view .LVU310
2063:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
2064:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   /* Configure the TCM / AXI Shared RAM in the option bytes register */
2065:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   MODIFY_REG(FLASH->OPTSR2_PRG, FLASH_OPTSR2_TCM_AXI_SHARED, SharedRamConfig);
 1065              		.loc 1 2065 3 view .LVU311
 1066 0000 034A     		ldr	r2, .L94
 1067 0002 536F     		ldr	r3, [r2, #116]
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 56


 1068 0004 23F00303 		bic	r3, r3, #3
 1069 0008 0343     		orrs	r3, r3, r0
 1070 000a 5367     		str	r3, [r2, #116]
2066:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** }
 1071              		.loc 1 2066 1 is_stmt 0 view .LVU312
 1072 000c 7047     		bx	lr
 1073              	.L95:
 1074 000e 00BF     		.align	2
 1075              	.L94:
 1076 0010 ******** 		.word	**********
 1077              		.cfi_endproc
 1078              	.LFE170:
 1080              		.section	.text.FLASH_OB_SharedRAM_GetConfig,"ax",%progbits
 1081              		.align	1
 1082              		.syntax unified
 1083              		.thumb
 1084              		.thumb_func
 1086              	FLASH_OB_SharedRAM_GetConfig:
 1087              	.LFB171:
2067:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
2068:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /**
2069:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @brief  Get the TCM / AXI Shared RAM configuration.
2070:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @retval SharedRamConfig returns the TCM / AXI Shared RAM configuration.
2071:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *         This return value can be a value of @ref FLASHEx_OB_TCM_AXI_SHARED
2072:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   */
2073:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** static uint32_t FLASH_OB_SharedRAM_GetConfig(void)
2074:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** {
 1088              		.loc 1 2074 1 is_stmt 1 view -0
 1089              		.cfi_startproc
 1090              		@ args = 0, pretend = 0, frame = 0
 1091              		@ frame_needed = 0, uses_anonymous_args = 0
 1092              		@ link register save eliminated.
2075:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   return (FLASH->OPTSR2_CUR & FLASH_OPTSR2_TCM_AXI_SHARED);
 1093              		.loc 1 2075 3 view .LVU314
 1094              		.loc 1 2075 16 is_stmt 0 view .LVU315
 1095 0000 024B     		ldr	r3, .L97
 1096 0002 186F     		ldr	r0, [r3, #112]
2076:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** }
 1097              		.loc 1 2076 1 view .LVU316
 1098 0004 00F00300 		and	r0, r0, #3
 1099 0008 7047     		bx	lr
 1100              	.L98:
 1101 000a 00BF     		.align	2
 1102              	.L97:
 1103 000c ******** 		.word	**********
 1104              		.cfi_endproc
 1105              	.LFE171:
 1107              		.section	.text.FLASH_OB_CPUFreq_BoostConfig,"ax",%progbits
 1108              		.align	1
 1109              		.syntax unified
 1110              		.thumb
 1111              		.thumb_func
 1113              	FLASH_OB_CPUFreq_BoostConfig:
 1114              	.LVL83:
 1115              	.LFB172:
2077:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /* FLASH_OPTSR2_TCM_AXI_SHARED */
2078:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 57


2079:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #if defined (FLASH_OPTSR2_CPUFREQ_BOOST)
2080:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /**
2081:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @brief  Configure the CPU Frequency Boost.
2082:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @param  FreqBoost specifies the CPU Frequency Boost state.
2083:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *         This parameter can be a value of @ref FLASHEx_OB_CPUFREQ_BOOST
2084:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @retval None
2085:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   */
2086:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** static void FLASH_OB_CPUFreq_BoostConfig(uint32_t FreqBoost)
2087:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** {
 1116              		.loc 1 2087 1 is_stmt 1 view -0
 1117              		.cfi_startproc
 1118              		@ args = 0, pretend = 0, frame = 0
 1119              		@ frame_needed = 0, uses_anonymous_args = 0
 1120              		@ link register save eliminated.
2088:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   /* Check the parameters */
2089:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   assert_param(IS_OB_USER_CPUFREQ_BOOST(FreqBoost));
 1121              		.loc 1 2089 3 view .LVU318
2090:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
2091:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   /* Configure the CPU Frequency Boost in the option bytes register */
2092:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   MODIFY_REG(FLASH->OPTSR2_PRG, FLASH_OPTSR2_CPUFREQ_BOOST, FreqBoost);
 1122              		.loc 1 2092 3 view .LVU319
 1123 0000 034A     		ldr	r2, .L100
 1124 0002 536F     		ldr	r3, [r2, #116]
 1125 0004 23F00403 		bic	r3, r3, #4
 1126 0008 0343     		orrs	r3, r3, r0
 1127 000a 5367     		str	r3, [r2, #116]
2093:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** }
 1128              		.loc 1 2093 1 is_stmt 0 view .LVU320
 1129 000c 7047     		bx	lr
 1130              	.L101:
 1131 000e 00BF     		.align	2
 1132              	.L100:
 1133 0010 ******** 		.word	**********
 1134              		.cfi_endproc
 1135              	.LFE172:
 1137              		.section	.text.FLASH_OB_CPUFreq_GetBoost,"ax",%progbits
 1138              		.align	1
 1139              		.syntax unified
 1140              		.thumb
 1141              		.thumb_func
 1143              	FLASH_OB_CPUFreq_GetBoost:
 1144              	.LFB173:
2094:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
2095:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** /**
2096:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @brief  Get the CPU Frequency Boost state.
2097:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   * @retval FreqBoost returns the CPU Frequency Boost state.
2098:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   *         This return value can be a value of @ref FLASHEx_OB_CPUFREQ_BOOST
2099:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   */
2100:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** static uint32_t FLASH_OB_CPUFreq_GetBoost(void)
2101:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** {
 1145              		.loc 1 2101 1 is_stmt 1 view -0
 1146              		.cfi_startproc
 1147              		@ args = 0, pretend = 0, frame = 0
 1148              		@ frame_needed = 0, uses_anonymous_args = 0
 1149              		@ link register save eliminated.
2102:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   return (FLASH->OPTSR2_CUR & FLASH_OPTSR2_CPUFREQ_BOOST);
 1150              		.loc 1 2102 3 view .LVU322
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 58


 1151              		.loc 1 2102 16 is_stmt 0 view .LVU323
 1152 0000 024B     		ldr	r3, .L103
 1153 0002 186F     		ldr	r0, [r3, #112]
2103:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** }
 1154              		.loc 1 2103 1 view .LVU324
 1155 0004 00F00400 		and	r0, r0, #4
 1156 0008 7047     		bx	lr
 1157              	.L104:
 1158 000a 00BF     		.align	2
 1159              	.L103:
 1160 000c ******** 		.word	**********
 1161              		.cfi_endproc
 1162              	.LFE173:
 1164              		.section	.text.HAL_FLASHEx_OBProgram,"ax",%progbits
 1165              		.align	1
 1166              		.global	HAL_FLASHEx_OBProgram
 1167              		.syntax unified
 1168              		.thumb
 1169              		.thumb_func
 1171              	HAL_FLASHEx_OBProgram:
 1172              	.LVL84:
 1173              	.LFB146:
 425:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   HAL_StatusTypeDef status;
 1174              		.loc 1 425 1 is_stmt 1 view -0
 1175              		.cfi_startproc
 1176              		@ args = 0, pretend = 0, frame = 0
 1177              		@ frame_needed = 0, uses_anonymous_args = 0
 425:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   HAL_StatusTypeDef status;
 1178              		.loc 1 425 1 is_stmt 0 view .LVU326
 1179 0000 38B5     		push	{r3, r4, r5, lr}
 1180              	.LCFI8:
 1181              		.cfi_def_cfa_offset 16
 1182              		.cfi_offset 3, -16
 1183              		.cfi_offset 4, -12
 1184              		.cfi_offset 5, -8
 1185              		.cfi_offset 14, -4
 426:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1186              		.loc 1 426 3 is_stmt 1 view .LVU327
 429:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1187              		.loc 1 429 3 view .LVU328
 432:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1188              		.loc 1 432 3 view .LVU329
 432:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1189              		.loc 1 432 3 view .LVU330
 1190 0002 394B     		ldr	r3, .L128
 1191 0004 1B7D     		ldrb	r3, [r3, #20]	@ zero_extendqisi2
 1192 0006 012B     		cmp	r3, #1
 1193 0008 6BD0     		beq	.L117
 1194 000a 0446     		mov	r4, r0
 432:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1195              		.loc 1 432 3 discriminator 2 view .LVU331
 1196 000c 364B     		ldr	r3, .L128
 1197 000e 0121     		movs	r1, #1
 1198 0010 1975     		strb	r1, [r3, #20]
 432:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1199              		.loc 1 432 3 discriminator 2 view .LVU332
 435:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 59


 1200              		.loc 1 435 3 view .LVU333
 435:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1201              		.loc 1 435 20 is_stmt 0 view .LVU334
 1202 0012 0022     		movs	r2, #0
 1203 0014 9A61     		str	r2, [r3, #24]
 438:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
 1204              		.loc 1 438 3 is_stmt 1 view .LVU335
 438:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
 1205              		.loc 1 438 6 is_stmt 0 view .LVU336
 1206 0016 4CF25030 		movw	r0, #50000
 1207              	.LVL85:
 438:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
 1208              		.loc 1 438 6 view .LVU337
 1209 001a FFF7FEFF 		bl	FLASH_WaitForLastOperation
 1210              	.LVL86:
 438:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
 1211              		.loc 1 438 5 discriminator 1 view .LVU338
 1212 001e 0546     		mov	r5, r0
 1213 0020 0028     		cmp	r0, #0
 1214 0022 58D1     		bne	.L118
 450:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
 1215              		.loc 1 450 5 is_stmt 1 view .LVU339
 1216              	.LVL87:
 453:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
 1217              		.loc 1 453 3 view .LVU340
 456:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 1218              		.loc 1 456 5 view .LVU341
 456:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 1219              		.loc 1 456 16 is_stmt 0 view .LVU342
 1220 0024 2368     		ldr	r3, [r4]
 456:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 1221              		.loc 1 456 7 view .LVU343
 1222 0026 13F0010F 		tst	r3, #1
 1223 002a 06D0     		beq	.L108
 458:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1224              		.loc 1 458 7 is_stmt 1 view .LVU344
 460:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       {
 1225              		.loc 1 460 7 view .LVU345
 460:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       {
 1226              		.loc 1 460 17 is_stmt 0 view .LVU346
 1227 002c 6368     		ldr	r3, [r4, #4]
 460:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       {
 1228              		.loc 1 460 9 view .LVU347
 1229 002e 012B     		cmp	r3, #1
 1230 0030 27D0     		beq	.L120
 468:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       }
 1231              		.loc 1 468 9 is_stmt 1 view .LVU348
 1232 0032 E169     		ldr	r1, [r4, #28]
 1233 0034 A068     		ldr	r0, [r4, #8]
 1234 0036 FFF7FEFF 		bl	FLASH_OB_DisableWRP
 1235              	.LVL88:
 1236              	.L108:
 473:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 1237              		.loc 1 473 5 view .LVU349
 473:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 1238              		.loc 1 473 16 is_stmt 0 view .LVU350
 1239 003a 2368     		ldr	r3, [r4]
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 60


 473:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 1240              		.loc 1 473 7 view .LVU351
 1241 003c 13F0020F 		tst	r3, #2
 1242 0040 24D1     		bne	.L121
 1243              	.L110:
 480:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 1244              		.loc 1 480 5 is_stmt 1 view .LVU352
 480:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 1245              		.loc 1 480 16 is_stmt 0 view .LVU353
 1246 0042 2368     		ldr	r3, [r4]
 480:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 1247              		.loc 1 480 7 view .LVU354
 1248 0044 13F0040F 		tst	r3, #4
 1249 0048 24D1     		bne	.L122
 1250              	.L111:
 487:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 1251              		.loc 1 487 5 is_stmt 1 view .LVU355
 487:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 1252              		.loc 1 487 16 is_stmt 0 view .LVU356
 1253 004a 2368     		ldr	r3, [r4]
 487:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 1254              		.loc 1 487 7 view .LVU357
 1255 004c 13F0080F 		tst	r3, #8
 1256 0050 25D1     		bne	.L123
 1257              	.L112:
 496:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 1258              		.loc 1 496 5 is_stmt 1 view .LVU358
 496:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 1259              		.loc 1 496 16 is_stmt 0 view .LVU359
 1260 0052 2368     		ldr	r3, [r4]
 496:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 1261              		.loc 1 496 7 view .LVU360
 1262 0054 13F0100F 		tst	r3, #16
 1263 0058 28D1     		bne	.L124
 1264              	.L113:
 515:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 1265              		.loc 1 515 5 is_stmt 1 view .LVU361
 515:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 1266              		.loc 1 515 16 is_stmt 0 view .LVU362
 1267 005a 2368     		ldr	r3, [r4]
 515:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 1268              		.loc 1 515 7 view .LVU363
 1269 005c 13F0400F 		tst	r3, #64
 1270 0060 28D1     		bne	.L125
 1271              	.L114:
 522:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 1272              		.loc 1 522 5 is_stmt 1 view .LVU364
 522:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 1273              		.loc 1 522 16 is_stmt 0 view .LVU365
 1274 0062 2368     		ldr	r3, [r4]
 522:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 1275              		.loc 1 522 7 view .LVU366
 1276 0064 13F0200F 		tst	r3, #32
 1277 0068 2AD1     		bne	.L126
 1278              	.L115:
 537:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 1279              		.loc 1 537 5 is_stmt 1 view .LVU367
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 61


 537:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 1280              		.loc 1 537 16 is_stmt 0 view .LVU368
 1281 006a 2368     		ldr	r3, [r4]
 537:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 1282              		.loc 1 537 7 view .LVU369
 1283 006c 13F4807F 		tst	r3, #256
 1284 0070 2DD1     		bne	.L127
 1285              	.L116:
 545:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 1286              		.loc 1 545 5 is_stmt 1 view .LVU370
 545:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 1287              		.loc 1 545 16 is_stmt 0 view .LVU371
 1288 0072 2368     		ldr	r3, [r4]
 545:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 1289              		.loc 1 545 7 view .LVU372
 1290 0074 13F4007F 		tst	r3, #512
 1291 0078 2ED0     		beq	.L107
 547:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     }
 1292              		.loc 1 547 7 is_stmt 1 view .LVU373
 1293 007a A06C     		ldr	r0, [r4, #72]
 1294 007c FFF7FEFF 		bl	FLASH_OB_CPUFreq_BoostConfig
 1295              	.LVL89:
 1296 0080 2AE0     		b	.L107
 1297              	.L120:
 463:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       }
 1298              		.loc 1 463 9 view .LVU374
 1299 0082 E169     		ldr	r1, [r4, #28]
 1300 0084 A068     		ldr	r0, [r4, #8]
 1301 0086 FFF7FEFF 		bl	FLASH_OB_EnableWRP
 1302              	.LVL90:
 1303 008a D6E7     		b	.L108
 1304              	.L121:
 476:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     }
 1305              		.loc 1 476 7 view .LVU375
 1306 008c E068     		ldr	r0, [r4, #12]
 1307 008e FFF7FEFF 		bl	FLASH_OB_RDPConfig
 1308              	.LVL91:
 1309 0092 D6E7     		b	.L110
 1310              	.L122:
 483:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     }
 1311              		.loc 1 483 7 view .LVU376
 1312 0094 A169     		ldr	r1, [r4, #24]
 1313 0096 6069     		ldr	r0, [r4, #20]
 1314 0098 FFF7FEFF 		bl	FLASH_OB_UserConfig
 1315              	.LVL92:
 1316 009c D5E7     		b	.L111
 1317              	.L123:
 489:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1318              		.loc 1 489 7 view .LVU377
 492:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     }
 1319              		.loc 1 492 7 view .LVU378
 1320 009e E369     		ldr	r3, [r4, #28]
 1321 00a0 A26A     		ldr	r2, [r4, #40]
 1322 00a2 616A     		ldr	r1, [r4, #36]
 1323 00a4 206A     		ldr	r0, [r4, #32]
 1324 00a6 FFF7FEFF 		bl	FLASH_OB_PCROPConfig
 1325              	.LVL93:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 62


 1326 00aa D2E7     		b	.L112
 1327              	.L124:
 498:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     }
 1328              		.loc 1 498 7 view .LVU379
 1329 00ac 2069     		ldr	r0, [r4, #16]
 1330 00ae FFF7FEFF 		bl	FLASH_OB_BOR_LevelConfig
 1331              	.LVL94:
 1332 00b2 D2E7     		b	.L113
 1333              	.L125:
 517:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     }
 1334              		.loc 1 517 7 view .LVU380
 1335 00b4 626B     		ldr	r2, [r4, #52]
 1336 00b6 216B     		ldr	r1, [r4, #48]
 1337 00b8 E06A     		ldr	r0, [r4, #44]
 1338 00ba FFF7FEFF 		bl	FLASH_OB_BootAddConfig
 1339              	.LVL95:
 1340 00be D0E7     		b	.L114
 1341              	.L126:
 524:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     }
 1342              		.loc 1 524 7 view .LVU381
 1343 00c0 E369     		ldr	r3, [r4, #28]
 1344 00c2 226C     		ldr	r2, [r4, #64]
 1345 00c4 E16B     		ldr	r1, [r4, #60]
 1346 00c6 A06B     		ldr	r0, [r4, #56]
 1347 00c8 FFF7FEFF 		bl	FLASH_OB_SecureAreaConfig
 1348              	.LVL96:
 1349 00cc CDE7     		b	.L115
 1350              	.L127:
 539:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     }
 1351              		.loc 1 539 7 view .LVU382
 1352 00ce 606C     		ldr	r0, [r4, #68]
 1353 00d0 FFF7FEFF 		bl	FLASH_OB_SharedRAM_Config
 1354              	.LVL97:
 1355 00d4 CDE7     		b	.L116
 1356              	.LVL98:
 1357              	.L118:
 440:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
 1358              		.loc 1 440 12 is_stmt 0 view .LVU383
 1359 00d6 0125     		movs	r5, #1
 1360              	.L107:
 553:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1361              		.loc 1 553 3 is_stmt 1 view .LVU384
 553:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1362              		.loc 1 553 3 view .LVU385
 1363 00d8 034B     		ldr	r3, .L128
 1364 00da 0022     		movs	r2, #0
 1365 00dc 1A75     		strb	r2, [r3, #20]
 553:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1366              		.loc 1 553 3 view .LVU386
 555:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** }
 1367              		.loc 1 555 3 view .LVU387
 1368              	.LVL99:
 1369              	.L106:
 556:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1370              		.loc 1 556 1 is_stmt 0 view .LVU388
 1371 00de 2846     		mov	r0, r5
 1372 00e0 38BD     		pop	{r3, r4, r5, pc}
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 63


 1373              	.LVL100:
 1374              	.L117:
 432:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1375              		.loc 1 432 3 discriminator 1 view .LVU389
 1376 00e2 0225     		movs	r5, #2
 1377 00e4 FBE7     		b	.L106
 1378              	.L129:
 1379 00e6 00BF     		.align	2
 1380              	.L128:
 1381 00e8 ******** 		.word	pFlash
 1382              		.cfi_endproc
 1383              	.LFE146:
 1385              		.section	.text.HAL_FLASHEx_OBGetConfig,"ax",%progbits
 1386              		.align	1
 1387              		.global	HAL_FLASHEx_OBGetConfig
 1388              		.syntax unified
 1389              		.thumb
 1390              		.thumb_func
 1392              	HAL_FLASHEx_OBGetConfig:
 1393              	.LVL101:
 1394              	.LFB147:
 568:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   pOBInit->OptionType = (OPTIONBYTE_USER | OPTIONBYTE_RDP | OPTIONBYTE_BOR);
 1395              		.loc 1 568 1 is_stmt 1 view -0
 1396              		.cfi_startproc
 1397              		@ args = 0, pretend = 0, frame = 0
 1398              		@ frame_needed = 0, uses_anonymous_args = 0
 568:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   pOBInit->OptionType = (OPTIONBYTE_USER | OPTIONBYTE_RDP | OPTIONBYTE_BOR);
 1399              		.loc 1 568 1 is_stmt 0 view .LVU391
 1400 0000 10B5     		push	{r4, lr}
 1401              	.LCFI9:
 1402              		.cfi_def_cfa_offset 8
 1403              		.cfi_offset 4, -8
 1404              		.cfi_offset 14, -4
 1405 0002 0446     		mov	r4, r0
 569:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1406              		.loc 1 569 3 is_stmt 1 view .LVU392
 569:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1407              		.loc 1 569 23 is_stmt 0 view .LVU393
 1408 0004 1623     		movs	r3, #22
 1409 0006 0360     		str	r3, [r0]
 572:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1410              		.loc 1 572 3 is_stmt 1 view .LVU394
 572:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1411              		.loc 1 572 23 is_stmt 0 view .LVU395
 1412 0008 FFF7FEFF 		bl	FLASH_OB_GetRDP
 1413              	.LVL102:
 572:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1414              		.loc 1 572 21 discriminator 1 view .LVU396
 1415 000c E060     		str	r0, [r4, #12]
 575:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1416              		.loc 1 575 3 is_stmt 1 view .LVU397
 575:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1417              		.loc 1 575 25 is_stmt 0 view .LVU398
 1418 000e FFF7FEFF 		bl	FLASH_OB_GetUser
 1419              	.LVL103:
 575:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1420              		.loc 1 575 23 discriminator 1 view .LVU399
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 64


 1421 0012 A061     		str	r0, [r4, #24]
 578:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1422              		.loc 1 578 3 is_stmt 1 view .LVU400
 578:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1423              		.loc 1 578 23 is_stmt 0 view .LVU401
 1424 0014 FFF7FEFF 		bl	FLASH_OB_GetBOR
 1425              	.LVL104:
 578:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1426              		.loc 1 578 21 discriminator 1 view .LVU402
 1427 0018 2061     		str	r0, [r4, #16]
 583:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /* DUAL_BANK */
 1428              		.loc 1 583 3 is_stmt 1 view .LVU403
 583:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /* DUAL_BANK */
 1429              		.loc 1 583 14 is_stmt 0 view .LVU404
 1430 001a E269     		ldr	r2, [r4, #28]
 583:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /* DUAL_BANK */
 1431              		.loc 1 583 6 view .LVU405
 1432 001c 012A     		cmp	r2, #1
 1433 001e 17D0     		beq	.L133
 1434              	.L131:
 599:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #if defined(DUAL_CORE)
 1435              		.loc 1 599 3 is_stmt 1 view .LVU406
 1436 0020 04F13401 		add	r1, r4, #52
 1437 0024 04F13000 		add	r0, r4, #48
 1438 0028 FFF7FEFF 		bl	FLASH_OB_GetBootAdd
 1439              	.LVL105:
 606:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /*DUAL_CORE*/
 1440              		.loc 1 606 3 view .LVU407
 606:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /*DUAL_CORE*/
 1441              		.loc 1 606 10 is_stmt 0 view .LVU408
 1442 002c 2368     		ldr	r3, [r4]
 606:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /*DUAL_CORE*/
 1443              		.loc 1 606 23 view .LVU409
 1444 002e 43F04002 		orr	r2, r3, #64
 1445 0032 2260     		str	r2, [r4]
 617:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1446              		.loc 1 617 3 is_stmt 1 view .LVU410
 617:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1447              		.loc 1 617 23 is_stmt 0 view .LVU411
 1448 0034 43F4A073 		orr	r3, r3, #320
 1449 0038 2360     		str	r3, [r4]
 620:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /* FLASH_OPTSR2_TCM_AXI_SHARED */
 1450              		.loc 1 620 3 is_stmt 1 view .LVU412
 620:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /* FLASH_OPTSR2_TCM_AXI_SHARED */
 1451              		.loc 1 620 30 is_stmt 0 view .LVU413
 1452 003a FFF7FEFF 		bl	FLASH_OB_SharedRAM_GetConfig
 1453              	.LVL106:
 620:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /* FLASH_OPTSR2_TCM_AXI_SHARED */
 1454              		.loc 1 620 28 discriminator 1 view .LVU414
 1455 003e 6064     		str	r0, [r4, #68]
 624:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1456              		.loc 1 624 3 is_stmt 1 view .LVU415
 624:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1457              		.loc 1 624 10 is_stmt 0 view .LVU416
 1458 0040 2368     		ldr	r3, [r4]
 624:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1459              		.loc 1 624 23 view .LVU417
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 65


 1460 0042 43F40073 		orr	r3, r3, #512
 1461 0046 2360     		str	r3, [r4]
 627:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /* FLASH_OPTSR2_CPUFREQ_BOOST */
 1462              		.loc 1 627 3 is_stmt 1 view .LVU418
 627:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /* FLASH_OPTSR2_CPUFREQ_BOOST */
 1463              		.loc 1 627 29 is_stmt 0 view .LVU419
 1464 0048 FFF7FEFF 		bl	FLASH_OB_CPUFreq_GetBoost
 1465              	.LVL107:
 627:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /* FLASH_OPTSR2_CPUFREQ_BOOST */
 1466              		.loc 1 627 27 discriminator 1 view .LVU420
 1467 004c A064     		str	r0, [r4, #72]
 629:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1468              		.loc 1 629 1 view .LVU421
 1469 004e 10BD     		pop	{r4, pc}
 1470              	.LVL108:
 1471              	.L133:
 586:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1472              		.loc 1 586 5 is_stmt 1 view .LVU422
 586:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1473              		.loc 1 586 12 is_stmt 0 view .LVU423
 1474 0050 2368     		ldr	r3, [r4]
 586:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1475              		.loc 1 586 25 view .LVU424
 1476 0052 43F02903 		orr	r3, r3, #41
 1477 0056 2146     		mov	r1, r4
 1478 0058 41F8083B 		str	r3, [r1], #8
 589:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1479              		.loc 1 589 5 is_stmt 1 view .LVU425
 1480 005c 201D     		adds	r0, r4, #4
 1481 005e FFF7FEFF 		bl	FLASH_OB_GetWRP
 1482              	.LVL109:
 592:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1483              		.loc 1 592 5 view .LVU426
 1484 0062 E369     		ldr	r3, [r4, #28]
 1485 0064 04F12802 		add	r2, r4, #40
 1486 0068 04F12401 		add	r1, r4, #36
 1487 006c 04F12000 		add	r0, r4, #32
 1488 0070 FFF7FEFF 		bl	FLASH_OB_GetPCROP
 1489              	.LVL110:
 595:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
 1490              		.loc 1 595 5 view .LVU427
 1491 0074 E369     		ldr	r3, [r4, #28]
 1492 0076 04F14002 		add	r2, r4, #64
 1493 007a 04F13C01 		add	r1, r4, #60
 1494 007e 04F13800 		add	r0, r4, #56
 1495 0082 FFF7FEFF 		bl	FLASH_OB_GetSecureArea
 1496              	.LVL111:
 1497 0086 CBE7     		b	.L131
 1498              		.cfi_endproc
 1499              	.LFE147:
 1501              		.section	.text.HAL_FLASHEx_Unlock_Bank1,"ax",%progbits
 1502              		.align	1
 1503              		.global	HAL_FLASHEx_Unlock_Bank1
 1504              		.syntax unified
 1505              		.thumb
 1506              		.thumb_func
 1508              	HAL_FLASHEx_Unlock_Bank1:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 66


 1509              	.LFB148:
 636:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   if(READ_BIT(FLASH->CR1, FLASH_CR_LOCK) != 0U)
 1510              		.loc 1 636 1 view -0
 1511              		.cfi_startproc
 1512              		@ args = 0, pretend = 0, frame = 0
 1513              		@ frame_needed = 0, uses_anonymous_args = 0
 1514              		@ link register save eliminated.
 637:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
 1515              		.loc 1 637 3 view .LVU429
 637:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
 1516              		.loc 1 637 6 is_stmt 0 view .LVU430
 1517 0000 0A4B     		ldr	r3, .L138
 1518 0002 DB68     		ldr	r3, [r3, #12]
 637:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
 1519              		.loc 1 637 5 view .LVU431
 1520 0004 13F0010F 		tst	r3, #1
 1521 0008 0BD0     		beq	.L136
 640:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     WRITE_REG(FLASH->KEYR1, FLASH_KEY2);
 1522              		.loc 1 640 5 is_stmt 1 view .LVU432
 1523 000a 084B     		ldr	r3, .L138
 1524 000c 084A     		ldr	r2, .L138+4
 1525 000e 5A60     		str	r2, [r3, #4]
 641:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1526              		.loc 1 641 5 view .LVU433
 1527 0010 02F18832 		add	r2, r2, #-2004318072
 1528 0014 5A60     		str	r2, [r3, #4]
 644:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 1529              		.loc 1 644 5 view .LVU434
 644:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 1530              		.loc 1 644 9 is_stmt 0 view .LVU435
 1531 0016 DB68     		ldr	r3, [r3, #12]
 644:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 1532              		.loc 1 644 8 view .LVU436
 1533 0018 13F0010F 		tst	r3, #1
 1534 001c 03D1     		bne	.L137
 650:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** }
 1535              		.loc 1 650 10 view .LVU437
 1536 001e 0020     		movs	r0, #0
 1537 0020 7047     		bx	lr
 1538              	.L136:
 1539 0022 0020     		movs	r0, #0
 1540 0024 7047     		bx	lr
 1541              	.L137:
 646:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     }
 1542              		.loc 1 646 14 view .LVU438
 1543 0026 0120     		movs	r0, #1
 651:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1544              		.loc 1 651 1 view .LVU439
 1545 0028 7047     		bx	lr
 1546              	.L139:
 1547 002a 00BF     		.align	2
 1548              	.L138:
 1549 002c ******** 		.word	**********
 1550 0030 ******** 		.word	**********
 1551              		.cfi_endproc
 1552              	.LFE148:
 1554              		.section	.text.HAL_FLASHEx_Lock_Bank1,"ax",%progbits
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 67


 1555              		.align	1
 1556              		.global	HAL_FLASHEx_Lock_Bank1
 1557              		.syntax unified
 1558              		.thumb
 1559              		.thumb_func
 1561              	HAL_FLASHEx_Lock_Bank1:
 1562              	.LFB149:
 658:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   /* Set the LOCK Bit to lock the FLASH Bank1 Registers access */
 1563              		.loc 1 658 1 is_stmt 1 view -0
 1564              		.cfi_startproc
 1565              		@ args = 0, pretend = 0, frame = 0
 1566              		@ frame_needed = 0, uses_anonymous_args = 0
 1567              		@ link register save eliminated.
 660:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   return HAL_OK;
 1568              		.loc 1 660 3 view .LVU441
 1569 0000 034A     		ldr	r2, .L141
 1570 0002 D368     		ldr	r3, [r2, #12]
 1571 0004 43F00103 		orr	r3, r3, #1
 1572 0008 D360     		str	r3, [r2, #12]
 661:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** }
 1573              		.loc 1 661 3 view .LVU442
 662:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1574              		.loc 1 662 1 is_stmt 0 view .LVU443
 1575 000a 0020     		movs	r0, #0
 1576 000c 7047     		bx	lr
 1577              	.L142:
 1578 000e 00BF     		.align	2
 1579              	.L141:
 1580 0010 ******** 		.word	**********
 1581              		.cfi_endproc
 1582              	.LFE149:
 1584              		.section	.text.HAL_FLASHEx_ComputeCRC,"ax",%progbits
 1585              		.align	1
 1586              		.global	HAL_FLASHEx_ComputeCRC
 1587              		.syntax unified
 1588              		.thumb
 1589              		.thumb_func
 1591              	HAL_FLASHEx_ComputeCRC:
 1592              	.LVL112:
 1593              	.LFB150:
 711:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   HAL_StatusTypeDef status;
 1594              		.loc 1 711 1 is_stmt 1 view -0
 1595              		.cfi_startproc
 1596              		@ args = 0, pretend = 0, frame = 0
 1597              		@ frame_needed = 0, uses_anonymous_args = 0
 711:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   HAL_StatusTypeDef status;
 1598              		.loc 1 711 1 is_stmt 0 view .LVU445
 1599 0000 70B5     		push	{r4, r5, r6, lr}
 1600              	.LCFI10:
 1601              		.cfi_def_cfa_offset 16
 1602              		.cfi_offset 4, -16
 1603              		.cfi_offset 5, -12
 1604              		.cfi_offset 6, -8
 1605              		.cfi_offset 14, -4
 1606 0002 0446     		mov	r4, r0
 1607 0004 0D46     		mov	r5, r1
 712:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   uint32_t sector_index;
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 68


 1608              		.loc 1 712 3 is_stmt 1 view .LVU446
 713:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1609              		.loc 1 713 3 view .LVU447
 716:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   assert_param(IS_FLASH_TYPECRC(pCRCInit->TypeCRC));
 1610              		.loc 1 716 3 view .LVU448
 717:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1611              		.loc 1 717 3 view .LVU449
 720:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1612              		.loc 1 720 3 view .LVU450
 720:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1613              		.loc 1 720 12 is_stmt 0 view .LVU451
 1614 0006 4CF25030 		movw	r0, #50000
 1615              	.LVL113:
 720:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1616              		.loc 1 720 12 view .LVU452
 1617 000a FFF7FEFF 		bl	FLASH_OB_WaitForLastOperation
 1618              	.LVL114:
 722:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
 1619              		.loc 1 722 3 is_stmt 1 view .LVU453
 722:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
 1620              		.loc 1 722 6 is_stmt 0 view .LVU454
 1621 000e 10B9     		cbnz	r0, .L144
 724:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 1622              		.loc 1 724 5 is_stmt 1 view .LVU455
 724:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 1623              		.loc 1 724 17 is_stmt 0 view .LVU456
 1624 0010 A368     		ldr	r3, [r4, #8]
 724:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 1625              		.loc 1 724 8 view .LVU457
 1626 0012 012B     		cmp	r3, #1
 1627 0014 00D0     		beq	.L151
 1628              	.LVL115:
 1629              	.L144:
 824:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** }
 1630              		.loc 1 824 3 is_stmt 1 view .LVU458
 825:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1631              		.loc 1 825 1 is_stmt 0 view .LVU459
 1632 0016 70BD     		pop	{r4, r5, r6, pc}
 1633              	.LVL116:
 1634              	.L151:
 727:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1635              		.loc 1 727 7 is_stmt 1 view .LVU460
 727:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1636              		.loc 1 727 12 is_stmt 0 view .LVU461
 1637 0018 244B     		ldr	r3, .L153
 1638 001a DA68     		ldr	r2, [r3, #12]
 727:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1639              		.loc 1 727 18 view .LVU462
 1640 001c 42F40042 		orr	r2, r2, #32768
 1641 0020 DA60     		str	r2, [r3, #12]
 730:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1642              		.loc 1 730 7 is_stmt 1 view .LVU463
 730:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1643              		.loc 1 730 12 is_stmt 0 view .LVU464
 1644 0022 5A69     		ldr	r2, [r3, #20]
 730:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1645              		.loc 1 730 19 view .LVU465
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 69


 1646 0024 42F0C052 		orr	r2, r2, #402653184
 1647 0028 5A61     		str	r2, [r3, #20]
 733:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1648              		.loc 1 733 7 is_stmt 1 view .LVU466
 733:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1649              		.loc 1 733 12 is_stmt 0 view .LVU467
 1650 002a 1A6D     		ldr	r2, [r3, #80]
 733:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1651              		.loc 1 733 56 view .LVU468
 1652 002c 6168     		ldr	r1, [r4, #4]
 733:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1653              		.loc 1 733 78 view .LVU469
 1654 002e 2068     		ldr	r0, [r4]
 733:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1655              		.loc 1 733 68 view .LVU470
 1656 0030 0143     		orrs	r1, r1, r0
 733:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1657              		.loc 1 733 21 view .LVU471
 1658 0032 0A43     		orrs	r2, r2, r1
 1659 0034 42F40032 		orr	r2, r2, #131072
 1660 0038 1A65     		str	r2, [r3, #80]
 735:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       {
 1661              		.loc 1 735 7 is_stmt 1 view .LVU472
 735:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       {
 1662              		.loc 1 735 19 is_stmt 0 view .LVU473
 1663 003a 2368     		ldr	r3, [r4]
 735:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       {
 1664              		.loc 1 735 10 view .LVU474
 1665 003c B3F5807F 		cmp	r3, #256
 1666 0040 08D0     		beq	.L152
 746:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       {
 1667              		.loc 1 746 12 is_stmt 1 view .LVU475
 746:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       {
 1668              		.loc 1 746 15 is_stmt 0 view .LVU476
 1669 0042 1B4A     		ldr	r2, .L153+4
 1670 0044 9342     		cmp	r3, r2
 1671 0046 2AD1     		bne	.L149
 749:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       }
 1672              		.loc 1 749 9 is_stmt 1 view .LVU477
 749:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       }
 1673              		.loc 1 749 14 is_stmt 0 view .LVU478
 1674 0048 184A     		ldr	r2, .L153
 1675 004a 136D     		ldr	r3, [r2, #80]
 749:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       }
 1676              		.loc 1 749 23 view .LVU479
 1677 004c 43F48003 		orr	r3, r3, #4194304
 1678 0050 1365     		str	r3, [r2, #80]
 1679 0052 10E0     		b	.L148
 1680              	.L152:
 738:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1681              		.loc 1 738 9 is_stmt 1 view .LVU480
 738:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1682              		.loc 1 738 14 is_stmt 0 view .LVU481
 1683 0054 154A     		ldr	r2, .L153
 1684 0056 136D     		ldr	r3, [r2, #80]
 738:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1685              		.loc 1 738 23 view .LVU482
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 70


 1686 0058 43F48063 		orr	r3, r3, #1024
 1687 005c 1365     		str	r3, [r2, #80]
 741:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         {
 1688              		.loc 1 741 9 is_stmt 1 view .LVU483
 741:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         {
 1689              		.loc 1 741 26 is_stmt 0 view .LVU484
 1690 005e E668     		ldr	r6, [r4, #12]
 1691              	.LVL117:
 741:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         {
 1692              		.loc 1 741 9 view .LVU485
 1693 0060 04E0     		b	.L146
 1694              	.L147:
 743:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         }
 1695              		.loc 1 743 11 is_stmt 1 view .LVU486
 1696 0062 0121     		movs	r1, #1
 1697 0064 3046     		mov	r0, r6
 1698 0066 FFF7FEFF 		bl	FLASH_CRC_AddSector
 1699              	.LVL118:
 741:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         {
 1700              		.loc 1 741 115 discriminator 3 view .LVU487
 1701 006a 0136     		adds	r6, r6, #1
 1702              	.LVL119:
 1703              	.L146:
 741:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         {
 1704              		.loc 1 741 59 discriminator 1 view .LVU488
 741:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         {
 1705              		.loc 1 741 70 is_stmt 0 discriminator 1 view .LVU489
 1706 006c 2369     		ldr	r3, [r4, #16]
 741:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         {
 1707              		.loc 1 741 92 discriminator 1 view .LVU490
 1708 006e E268     		ldr	r2, [r4, #12]
 741:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         {
 1709              		.loc 1 741 82 discriminator 1 view .LVU491
 1710 0070 1344     		add	r3, r3, r2
 741:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         {
 1711              		.loc 1 741 59 discriminator 1 view .LVU492
 1712 0072 B342     		cmp	r3, r6
 1713 0074 F5D8     		bhi	.L147
 1714              	.LVL120:
 1715              	.L148:
 758:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1716              		.loc 1 758 7 is_stmt 1 view .LVU493
 758:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1717              		.loc 1 758 12 is_stmt 0 view .LVU494
 1718 0076 0D4C     		ldr	r4, .L153
 1719              	.LVL121:
 758:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1720              		.loc 1 758 12 view .LVU495
 1721 0078 236D     		ldr	r3, [r4, #80]
 758:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1722              		.loc 1 758 21 view .LVU496
 1723 007a 43F48033 		orr	r3, r3, #65536
 1724 007e 2365     		str	r3, [r4, #80]
 761:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1725              		.loc 1 761 7 is_stmt 1 view .LVU497
 761:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1726              		.loc 1 761 16 is_stmt 0 view .LVU498
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 71


 1727 0080 0121     		movs	r1, #1
 1728 0082 4CF25030 		movw	r0, #50000
 1729 0086 FFF7FEFF 		bl	FLASH_CRC_WaitForLastOperation
 1730              	.LVL122:
 764:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1731              		.loc 1 764 7 is_stmt 1 view .LVU499
 764:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1732              		.loc 1 764 28 is_stmt 0 view .LVU500
 1733 008a E36D     		ldr	r3, [r4, #92]
 764:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1734              		.loc 1 764 21 view .LVU501
 1735 008c 2B60     		str	r3, [r5]
 767:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1736              		.loc 1 767 7 is_stmt 1 view .LVU502
 767:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1737              		.loc 1 767 12 is_stmt 0 view .LVU503
 1738 008e E368     		ldr	r3, [r4, #12]
 767:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1739              		.loc 1 767 18 view .LVU504
 1740 0090 23F40043 		bic	r3, r3, #32768
 1741 0094 E360     		str	r3, [r4, #12]
 770:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     }
 1742              		.loc 1 770 7 is_stmt 1 view .LVU505
 1743 0096 4FF0C053 		mov	r3, #402653184
 1744 009a 6361     		str	r3, [r4, #20]
 1745 009c BBE7     		b	.L144
 1746              	.LVL123:
 1747              	.L149:
 754:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       }
 1748              		.loc 1 754 9 view .LVU506
 1749 009e 0122     		movs	r2, #1
 1750 00a0 A169     		ldr	r1, [r4, #24]
 1751 00a2 6069     		ldr	r0, [r4, #20]
 1752 00a4 FFF7FEFF 		bl	FLASH_CRC_SelectAddress
 1753              	.LVL124:
 1754 00a8 E5E7     		b	.L148
 1755              	.L154:
 1756 00aa 00BF     		.align	2
 1757              	.L153:
 1758 00ac ******** 		.word	**********
 1759 00b0 00014000 		.word	4194560
 1760              		.cfi_endproc
 1761              	.LFE150:
 1763              		.section	.text.FLASH_Erase_Sector,"ax",%progbits
 1764              		.align	1
 1765              		.global	FLASH_Erase_Sector
 1766              		.syntax unified
 1767              		.thumb
 1768              		.thumb_func
 1770              	FLASH_Erase_Sector:
 1771              	.LVL125:
 1772              	.LFB152:
1181:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   assert_param(IS_FLASH_SECTOR(Sector));
 1773              		.loc 1 1181 1 view -0
 1774              		.cfi_startproc
 1775              		@ args = 0, pretend = 0, frame = 0
 1776              		@ frame_needed = 0, uses_anonymous_args = 0
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 72


 1777              		@ link register save eliminated.
1182:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   assert_param(IS_FLASH_BANK_EXCLUSIVE(Banks));
 1778              		.loc 1 1182 3 view .LVU508
1183:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #if defined (FLASH_CR_PSIZE)
 1779              		.loc 1 1183 3 view .LVU509
1185:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #else
 1780              		.loc 1 1185 3 view .LVU510
1190:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
 1781              		.loc 1 1190 3 view .LVU511
1190:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
 1782              		.loc 1 1190 5 is_stmt 0 view .LVU512
 1783 0000 11F0010F 		tst	r1, #1
 1784 0004 0BD0     		beq	.L155
1194:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1785              		.loc 1 1194 5 is_stmt 1 view .LVU513
1194:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1786              		.loc 1 1194 10 is_stmt 0 view .LVU514
 1787 0006 0649     		ldr	r1, .L157
 1788              	.LVL126:
1194:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1789              		.loc 1 1194 10 view .LVU515
 1790 0008 CB68     		ldr	r3, [r1, #12]
1194:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1791              		.loc 1 1194 16 view .LVU516
 1792 000a 23F4E663 		bic	r3, r3, #1840
 1793 000e CB60     		str	r3, [r1, #12]
1196:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #else
 1794              		.loc 1 1196 5 is_stmt 1 view .LVU517
1196:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #else
 1795              		.loc 1 1196 10 is_stmt 0 view .LVU518
 1796 0010 CB68     		ldr	r3, [r1, #12]
1196:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #else
 1797              		.loc 1 1196 48 view .LVU519
 1798 0012 42EA0022 		orr	r2, r2, r0, lsl #8
 1799              	.LVL127:
1196:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #else
 1800              		.loc 1 1196 16 view .LVU520
 1801 0016 1343     		orrs	r3, r3, r2
 1802 0018 43F08403 		orr	r3, r3, #132
 1803 001c CB60     		str	r3, [r1, #12]
 1804              	.L155:
1221:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1805              		.loc 1 1221 1 view .LVU521
 1806 001e 7047     		bx	lr
 1807              	.L158:
 1808              		.align	2
 1809              	.L157:
 1810 0020 ******** 		.word	**********
 1811              		.cfi_endproc
 1812              	.LFE152:
 1814              		.section	.text.HAL_FLASHEx_Erase,"ax",%progbits
 1815              		.align	1
 1816              		.global	HAL_FLASHEx_Erase
 1817              		.syntax unified
 1818              		.thumb
 1819              		.thumb_func
 1821              	HAL_FLASHEx_Erase:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 73


 1822              	.LVL128:
 1823              	.LFB144:
 187:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   HAL_StatusTypeDef status = HAL_OK;
 1824              		.loc 1 187 1 is_stmt 1 view -0
 1825              		.cfi_startproc
 1826              		@ args = 0, pretend = 0, frame = 0
 1827              		@ frame_needed = 0, uses_anonymous_args = 0
 187:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   HAL_StatusTypeDef status = HAL_OK;
 1828              		.loc 1 187 1 is_stmt 0 view .LVU523
 1829 0000 F8B5     		push	{r3, r4, r5, r6, r7, lr}
 1830              	.LCFI11:
 1831              		.cfi_def_cfa_offset 24
 1832              		.cfi_offset 3, -24
 1833              		.cfi_offset 4, -20
 1834              		.cfi_offset 5, -16
 1835              		.cfi_offset 6, -12
 1836              		.cfi_offset 7, -8
 1837              		.cfi_offset 14, -4
 188:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   uint32_t sector_index;
 1838              		.loc 1 188 3 is_stmt 1 view .LVU524
 1839              	.LVL129:
 189:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1840              		.loc 1 189 3 view .LVU525
 192:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   assert_param(IS_FLASH_BANK(pEraseInit->Banks));
 1841              		.loc 1 192 3 view .LVU526
 193:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1842              		.loc 1 193 3 view .LVU527
 196:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1843              		.loc 1 196 3 view .LVU528
 196:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1844              		.loc 1 196 3 view .LVU529
 1845 0002 2E4B     		ldr	r3, .L178
 1846 0004 1B7D     		ldrb	r3, [r3, #20]	@ zero_extendqisi2
 1847 0006 012B     		cmp	r3, #1
 1848 0008 56D0     		beq	.L169
 1849 000a 0446     		mov	r4, r0
 1850 000c 0F46     		mov	r7, r1
 196:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1851              		.loc 1 196 3 discriminator 2 view .LVU530
 1852 000e 2B4B     		ldr	r3, .L178
 1853 0010 0122     		movs	r2, #1
 1854 0012 1A75     		strb	r2, [r3, #20]
 196:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1855              		.loc 1 196 3 discriminator 2 view .LVU531
 199:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1856              		.loc 1 199 3 view .LVU532
 199:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1857              		.loc 1 199 20 is_stmt 0 view .LVU533
 1858 0014 0022     		movs	r2, #0
 1859 0016 9A61     		str	r2, [r3, #24]
 202:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
 1860              		.loc 1 202 3 is_stmt 1 view .LVU534
 202:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
 1861              		.loc 1 202 17 is_stmt 0 view .LVU535
 1862 0018 4368     		ldr	r3, [r0, #4]
 202:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
 1863              		.loc 1 202 5 view .LVU536
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 74


 1864 001a 13F0010F 		tst	r3, #1
 1865 001e 08D1     		bne	.L174
 1866              	.LVL130:
 1867              	.L161:
 223:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 1868              		.loc 1 223 5 is_stmt 1 view .LVU537
 223:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 1869              		.loc 1 223 18 is_stmt 0 view .LVU538
 1870 0020 2368     		ldr	r3, [r4]
 223:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 1871              		.loc 1 223 7 view .LVU539
 1872 0022 012B     		cmp	r3, #1
 1873 0024 0ED0     		beq	.L175
 254:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1874              		.loc 1 254 7 is_stmt 1 view .LVU540
 254:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1875              		.loc 1 254 20 is_stmt 0 view .LVU541
 1876 0026 4FF0FF33 		mov	r3, #-1
 1877 002a 3B60     		str	r3, [r7]
 257:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       {
 1878              		.loc 1 257 7 is_stmt 1 view .LVU542
 257:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       {
 1879              		.loc 1 257 24 is_stmt 0 view .LVU543
 1880 002c A568     		ldr	r5, [r4, #8]
 1881              	.LVL131:
 257:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       {
 1882              		.loc 1 257 7 view .LVU544
 1883 002e 0026     		movs	r6, #0
 1884 0030 22E0     		b	.L165
 1885              	.LVL132:
 1886              	.L174:
 204:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 1887              		.loc 1 204 5 is_stmt 1 view .LVU545
 204:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 1888              		.loc 1 204 8 is_stmt 0 view .LVU546
 1889 0032 0121     		movs	r1, #1
 1890              	.LVL133:
 204:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 1891              		.loc 1 204 8 view .LVU547
 1892 0034 4CF25030 		movw	r0, #50000
 1893              	.LVL134:
 204:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 1894              		.loc 1 204 8 view .LVU548
 1895 0038 FFF7FEFF 		bl	FLASH_WaitForLastOperation
 1896              	.LVL135:
 204:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 1897              		.loc 1 204 7 discriminator 1 view .LVU549
 1898 003c 0028     		cmp	r0, #0
 1899 003e EFD0     		beq	.L161
 206:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     }
 1900              		.loc 1 206 14 view .LVU550
 1901 0040 0126     		movs	r6, #1
 1902 0042 34E0     		b	.L162
 1903              	.LVL136:
 1904              	.L175:
 226:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1905              		.loc 1 226 7 is_stmt 1 view .LVU551
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 75


 1906 0044 6168     		ldr	r1, [r4, #4]
 1907 0046 2069     		ldr	r0, [r4, #16]
 1908 0048 FFF7FEFF 		bl	FLASH_MassErase
 1909              	.LVL137:
 229:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       {
 1910              		.loc 1 229 7 view .LVU552
 229:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       {
 1911              		.loc 1 229 21 is_stmt 0 view .LVU553
 1912 004c 6368     		ldr	r3, [r4, #4]
 229:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       {
 1913              		.loc 1 229 9 view .LVU554
 1914 004e 13F0010F 		tst	r3, #1
 1915 0052 01D1     		bne	.L176
 1916 0054 0026     		movs	r6, #0
 1917 0056 2AE0     		b	.L162
 1918              	.L176:
 231:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         {
 1919              		.loc 1 231 9 is_stmt 1 view .LVU555
 231:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         {
 1920              		.loc 1 231 12 is_stmt 0 view .LVU556
 1921 0058 0121     		movs	r1, #1
 1922 005a 4CF25030 		movw	r0, #50000
 1923 005e FFF7FEFF 		bl	FLASH_WaitForLastOperation
 1924              	.LVL138:
 231:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         {
 1925              		.loc 1 231 11 discriminator 1 view .LVU557
 1926 0062 0646     		mov	r6, r0
 1927 0064 00B1     		cbz	r0, .L164
 233:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         }
 1928              		.loc 1 233 18 view .LVU558
 1929 0066 0126     		movs	r6, #1
 1930              	.L164:
 1931              	.LVL139:
 236:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       }
 1932              		.loc 1 236 9 is_stmt 1 view .LVU559
 236:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       }
 1933              		.loc 1 236 14 is_stmt 0 view .LVU560
 1934 0068 154A     		ldr	r2, .L178+4
 1935 006a D368     		ldr	r3, [r2, #12]
 236:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       }
 1936              		.loc 1 236 20 view .LVU561
 1937 006c 23F00803 		bic	r3, r3, #8
 1938 0070 D360     		str	r3, [r2, #12]
 1939 0072 1CE0     		b	.L162
 1940              	.LVL140:
 1941              	.L166:
 280:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         {
 1942              		.loc 1 280 9 is_stmt 1 view .LVU562
 280:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         {
 1943              		.loc 1 280 11 is_stmt 0 view .LVU563
 1944 0074 D6B9     		cbnz	r6, .L177
 257:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       {
 1945              		.loc 1 257 119 is_stmt 1 discriminator 2 view .LVU564
 1946 0076 0135     		adds	r5, r5, #1
 1947              	.LVL141:
 1948              	.L165:
 257:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       {
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 76


 1949              		.loc 1 257 59 discriminator 1 view .LVU565
 257:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       {
 1950              		.loc 1 257 72 is_stmt 0 discriminator 1 view .LVU566
 1951 0078 E368     		ldr	r3, [r4, #12]
 257:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       {
 1952              		.loc 1 257 96 discriminator 1 view .LVU567
 1953 007a A268     		ldr	r2, [r4, #8]
 257:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       {
 1954              		.loc 1 257 84 discriminator 1 view .LVU568
 1955 007c 1344     		add	r3, r3, r2
 257:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       {
 1956              		.loc 1 257 59 discriminator 1 view .LVU569
 1957 007e AB42     		cmp	r3, r5
 1958 0080 15D9     		bls	.L162
 259:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1959              		.loc 1 259 9 is_stmt 1 view .LVU570
 1960 0082 2269     		ldr	r2, [r4, #16]
 1961 0084 6168     		ldr	r1, [r4, #4]
 1962 0086 2846     		mov	r0, r5
 1963 0088 FFF7FEFF 		bl	FLASH_Erase_Sector
 1964              	.LVL142:
 261:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         {
 1965              		.loc 1 261 9 view .LVU571
 261:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         {
 1966              		.loc 1 261 23 is_stmt 0 view .LVU572
 1967 008c 6368     		ldr	r3, [r4, #4]
 261:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         {
 1968              		.loc 1 261 11 view .LVU573
 1969 008e 13F0010F 		tst	r3, #1
 1970 0092 EFD0     		beq	.L166
 264:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1971              		.loc 1 264 11 is_stmt 1 view .LVU574
 264:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1972              		.loc 1 264 20 is_stmt 0 view .LVU575
 1973 0094 0121     		movs	r1, #1
 1974 0096 4CF25030 		movw	r0, #50000
 1975 009a FFF7FEFF 		bl	FLASH_WaitForLastOperation
 1976              	.LVL143:
 1977 009e 0646     		mov	r6, r0
 1978              	.LVL144:
 267:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         }
 1979              		.loc 1 267 11 is_stmt 1 view .LVU576
 267:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         }
 1980              		.loc 1 267 16 is_stmt 0 view .LVU577
 1981 00a0 074A     		ldr	r2, .L178+4
 1982 00a2 D168     		ldr	r1, [r2, #12]
 267:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         }
 1983              		.loc 1 267 22 view .LVU578
 1984 00a4 074B     		ldr	r3, .L178+8
 1985 00a6 0B40     		ands	r3, r3, r1
 1986 00a8 D360     		str	r3, [r2, #12]
 1987 00aa E3E7     		b	.L166
 1988              	.L177:
 283:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****           break;
 1989              		.loc 1 283 11 is_stmt 1 view .LVU579
 283:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****           break;
 1990              		.loc 1 283 24 is_stmt 0 view .LVU580
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 77


 1991 00ac 3D60     		str	r5, [r7]
 284:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****         }
 1992              		.loc 1 284 11 is_stmt 1 view .LVU581
 1993              	.LVL145:
 1994              	.L162:
 291:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1995              		.loc 1 291 3 view .LVU582
 291:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 1996              		.loc 1 291 3 view .LVU583
 1997 00ae 034B     		ldr	r3, .L178
 1998 00b0 0022     		movs	r2, #0
 1999 00b2 1A75     		strb	r2, [r3, #20]
 291:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 2000              		.loc 1 291 3 view .LVU584
 293:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** }
 2001              		.loc 1 293 3 view .LVU585
 2002              	.LVL146:
 2003              	.L160:
 294:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 2004              		.loc 1 294 1 is_stmt 0 view .LVU586
 2005 00b4 3046     		mov	r0, r6
 2006 00b6 F8BD     		pop	{r3, r4, r5, r6, r7, pc}
 2007              	.LVL147:
 2008              	.L169:
 196:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 2009              		.loc 1 196 3 discriminator 1 view .LVU587
 2010 00b8 0226     		movs	r6, #2
 2011 00ba FBE7     		b	.L160
 2012              	.L179:
 2013              		.align	2
 2014              	.L178:
 2015 00bc ******** 		.word	pFlash
 2016 00c0 ******** 		.word	**********
 2017 00c4 FBF8FFFF 		.word	-1797
 2018              		.cfi_endproc
 2019              	.LFE144:
 2021              		.section	.text.HAL_FLASHEx_Erase_IT,"ax",%progbits
 2022              		.align	1
 2023              		.global	HAL_FLASHEx_Erase_IT
 2024              		.syntax unified
 2025              		.thumb
 2026              		.thumb_func
 2028              	HAL_FLASHEx_Erase_IT:
 2029              	.LVL148:
 2030              	.LFB145:
 304:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   HAL_StatusTypeDef status = HAL_OK;
 2031              		.loc 1 304 1 is_stmt 1 view -0
 2032              		.cfi_startproc
 2033              		@ args = 0, pretend = 0, frame = 0
 2034              		@ frame_needed = 0, uses_anonymous_args = 0
 305:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 2035              		.loc 1 305 3 view .LVU589
 308:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   assert_param(IS_FLASH_BANK(pEraseInit->Banks));
 2036              		.loc 1 308 3 view .LVU590
 309:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 2037              		.loc 1 309 3 view .LVU591
 312:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 78


 2038              		.loc 1 312 3 view .LVU592
 312:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 2039              		.loc 1 312 3 view .LVU593
 2040 0000 234B     		ldr	r3, .L194
 2041 0002 1B7D     		ldrb	r3, [r3, #20]	@ zero_extendqisi2
 2042 0004 012B     		cmp	r3, #1
 2043 0006 40D0     		beq	.L187
 304:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   HAL_StatusTypeDef status = HAL_OK;
 2044              		.loc 1 304 1 is_stmt 0 view .LVU594
 2045 0008 10B5     		push	{r4, lr}
 2046              	.LCFI12:
 2047              		.cfi_def_cfa_offset 8
 2048              		.cfi_offset 4, -8
 2049              		.cfi_offset 14, -4
 2050 000a 0446     		mov	r4, r0
 312:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 2051              		.loc 1 312 3 is_stmt 1 discriminator 2 view .LVU595
 2052 000c 204B     		ldr	r3, .L194
 2053 000e 0122     		movs	r2, #1
 2054 0010 1A75     		strb	r2, [r3, #20]
 312:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 2055              		.loc 1 312 3 discriminator 2 view .LVU596
 315:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 2056              		.loc 1 315 3 view .LVU597
 315:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 2057              		.loc 1 315 20 is_stmt 0 view .LVU598
 2058 0012 0022     		movs	r2, #0
 2059 0014 9A61     		str	r2, [r3, #24]
 318:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
 2060              		.loc 1 318 3 is_stmt 1 view .LVU599
 318:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
 2061              		.loc 1 318 17 is_stmt 0 view .LVU600
 2062 0016 4368     		ldr	r3, [r0, #4]
 318:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
 2063              		.loc 1 318 5 view .LVU601
 2064 0018 13F0010F 		tst	r3, #1
 2065 001c 16D1     		bne	.L192
 2066              	.LVL149:
 2067              	.L182:
 344:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 2068              		.loc 1 344 5 is_stmt 1 view .LVU602
 344:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 2069              		.loc 1 344 19 is_stmt 0 view .LVU603
 2070 001e 6368     		ldr	r3, [r4, #4]
 344:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 2071              		.loc 1 344 7 view .LVU604
 2072 0020 13F0010F 		tst	r3, #1
 2073 0024 04D0     		beq	.L183
 348:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****                                   FLASH_IT_STRBERR_BANK1 | FLASH_IT_INCERR_BANK1 | FLASH_IT_OPERR_B
 2074              		.loc 1 348 7 is_stmt 1 view .LVU605
 2075 0026 1B4A     		ldr	r2, .L194+4
 2076 0028 D368     		ldr	r3, [r2, #12]
 2077 002a 43F4DE03 		orr	r3, r3, #7274496
 2078 002e D360     		str	r3, [r2, #12]
 2079              	.L183:
 369:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 2080              		.loc 1 369 5 view .LVU606
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 79


 369:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 2081              		.loc 1 369 18 is_stmt 0 view .LVU607
 2082 0030 2368     		ldr	r3, [r4]
 369:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 2083              		.loc 1 369 7 view .LVU608
 2084 0032 012B     		cmp	r3, #1
 2085 0034 1AD1     		bne	.L184
 372:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       {
 2086              		.loc 1 372 7 is_stmt 1 view .LVU609
 372:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       {
 2087              		.loc 1 372 20 is_stmt 0 view .LVU610
 2088 0036 6168     		ldr	r1, [r4, #4]
 372:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       {
 2089              		.loc 1 372 9 view .LVU611
 2090 0038 0129     		cmp	r1, #1
 2091 003a 13D0     		beq	.L193
 384:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       }
 2092              		.loc 1 384 9 is_stmt 1 view .LVU612
 384:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       }
 2093              		.loc 1 384 33 is_stmt 0 view .LVU613
 2094 003c 144B     		ldr	r3, .L194
 2095 003e 0722     		movs	r2, #7
 2096 0040 1A70     		strb	r2, [r3]
 2097              	.L186:
 387:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     }
 2098              		.loc 1 387 7 is_stmt 1 view .LVU614
 2099 0042 2069     		ldr	r0, [r4, #16]
 2100 0044 FFF7FEFF 		bl	FLASH_MassErase
 2101              	.LVL150:
 2102 0048 0020     		movs	r0, #0
 2103              	.L181:
 415:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 2104              		.loc 1 415 1 is_stmt 0 view .LVU615
 2105 004a 10BD     		pop	{r4, pc}
 2106              	.LVL151:
 2107              	.L192:
 320:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 2108              		.loc 1 320 5 is_stmt 1 view .LVU616
 320:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 2109              		.loc 1 320 8 is_stmt 0 view .LVU617
 2110 004c 0121     		movs	r1, #1
 2111 004e 4CF25030 		movw	r0, #50000
 2112              	.LVL152:
 320:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 2113              		.loc 1 320 8 view .LVU618
 2114 0052 FFF7FEFF 		bl	FLASH_WaitForLastOperation
 2115              	.LVL153:
 320:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     {
 2116              		.loc 1 320 7 discriminator 1 view .LVU619
 2117 0056 0028     		cmp	r0, #0
 2118 0058 E1D0     		beq	.L182
 322:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     }
 2119              		.loc 1 322 7 is_stmt 1 view .LVU620
 2120              	.LVL154:
 337:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   {
 2121              		.loc 1 337 3 view .LVU621
 340:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 80


 2122              		.loc 1 340 5 view .LVU622
 340:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
 2123              		.loc 1 340 5 view .LVU623
 2124 005a 0D4B     		ldr	r3, .L194
 2125 005c 0022     		movs	r2, #0
 2126 005e 1A75     		strb	r2, [r3, #20]
 340:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****   }
 2127              		.loc 1 340 5 view .LVU624
 322:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     }
 2128              		.loc 1 322 14 is_stmt 0 view .LVU625
 2129 0060 0120     		movs	r0, #1
 2130 0062 F2E7     		b	.L181
 2131              	.LVL155:
 2132              	.L193:
 374:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       }
 2133              		.loc 1 374 9 is_stmt 1 view .LVU626
 374:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       }
 2134              		.loc 1 374 33 is_stmt 0 view .LVU627
 2135 0064 0A4B     		ldr	r3, .L194
 2136 0066 0222     		movs	r2, #2
 2137 0068 1A70     		strb	r2, [r3]
 2138 006a EAE7     		b	.L186
 2139              	.L184:
 402:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /* DUAL_BANK */
 2140              		.loc 1 402 7 is_stmt 1 view .LVU628
 402:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** #endif /* DUAL_BANK */
 2141              		.loc 1 402 31 is_stmt 0 view .LVU629
 2142 006c 084B     		ldr	r3, .L194
 2143 006e 0122     		movs	r2, #1
 2144 0070 1A70     		strb	r2, [r3]
 405:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       pFlash.Sector = pEraseInit->Sector;
 2145              		.loc 1 405 7 is_stmt 1 view .LVU630
 405:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       pFlash.Sector = pEraseInit->Sector;
 2146              		.loc 1 405 43 is_stmt 0 view .LVU631
 2147 0072 E268     		ldr	r2, [r4, #12]
 405:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       pFlash.Sector = pEraseInit->Sector;
 2148              		.loc 1 405 31 view .LVU632
 2149 0074 5A60     		str	r2, [r3, #4]
 406:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       pFlash.VoltageForErase = pEraseInit->VoltageRange;
 2150              		.loc 1 406 7 is_stmt 1 view .LVU633
 406:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       pFlash.VoltageForErase = pEraseInit->VoltageRange;
 2151              		.loc 1 406 33 is_stmt 0 view .LVU634
 2152 0076 A268     		ldr	r2, [r4, #8]
 406:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****       pFlash.VoltageForErase = pEraseInit->VoltageRange;
 2153              		.loc 1 406 21 view .LVU635
 2154 0078 DA60     		str	r2, [r3, #12]
 407:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 2155              		.loc 1 407 7 is_stmt 1 view .LVU636
 407:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 2156              		.loc 1 407 42 is_stmt 0 view .LVU637
 2157 007a 2269     		ldr	r2, [r4, #16]
 407:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 2158              		.loc 1 407 30 view .LVU638
 2159 007c 9A60     		str	r2, [r3, #8]
 410:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c ****     }
 2160              		.loc 1 410 7 is_stmt 1 view .LVU639
 2161 007e 6168     		ldr	r1, [r4, #4]
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 81


 2162 0080 A068     		ldr	r0, [r4, #8]
 2163 0082 FFF7FEFF 		bl	FLASH_Erase_Sector
 2164              	.LVL156:
 2165 0086 0020     		movs	r0, #0
 2166 0088 DFE7     		b	.L181
 2167              	.LVL157:
 2168              	.L187:
 2169              	.LCFI13:
 2170              		.cfi_def_cfa_offset 0
 2171              		.cfi_restore 4
 2172              		.cfi_restore 14
 312:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 2173              		.loc 1 312 3 is_stmt 0 discriminator 1 view .LVU640
 2174 008a 0220     		movs	r0, #2
 2175              	.LVL158:
 415:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c **** 
 2176              		.loc 1 415 1 view .LVU641
 2177 008c 7047     		bx	lr
 2178              	.L195:
 2179 008e 00BF     		.align	2
 2180              	.L194:
 2181 0090 ******** 		.word	pFlash
 2182 0094 ******** 		.word	**********
 2183              		.cfi_endproc
 2184              	.LFE145:
 2186              		.text
 2187              	.Letext0:
 2188              		.file 2 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin/../lib/gcc/arm-none-eabi/13.3.1/../
 2189              		.file 3 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin/../lib/gcc/arm-none-eabi/13.3.1/../
 2190              		.file 4 "Drivers/CMSIS/Device/ST/STM32H7xx/Include/stm32h723xx.h"
 2191              		.file 5 "Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_def.h"
 2192              		.file 6 "Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_flash.h"
 2193              		.file 7 "Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_flash_ex.h"
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 82


DEFINED SYMBOLS
                            *ABS*:******** stm32h7xx_hal_flash_ex.c
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:20     .text.FLASH_MassErase:******** $t
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:25     .text.FLASH_MassErase:******** FLASH_MassErase
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:64     .text.FLASH_MassErase:******** $d
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:69     .text.FLASH_OB_EnableWRP:******** $t
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:74     .text.FLASH_OB_EnableWRP:******** FLASH_OB_EnableWRP
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:104    .text.FLASH_OB_EnableWRP:00000014 $d
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:109    .text.FLASH_OB_DisableWRP:******** $t
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:114    .text.FLASH_OB_DisableWRP:******** FLASH_OB_DisableWRP
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:144    .text.FLASH_OB_DisableWRP:00000014 $d
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:149    .text.FLASH_OB_GetWRP:******** $t
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:154    .text.FLASH_OB_GetWRP:******** FLASH_OB_GetWRP
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:207    .text.FLASH_OB_GetWRP:******** $d
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:212    .text.FLASH_OB_RDPConfig:******** $t
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:217    .text.FLASH_OB_RDPConfig:******** FLASH_OB_RDPConfig
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:237    .text.FLASH_OB_RDPConfig:******** $d
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:242    .text.FLASH_OB_GetRDP:******** $t
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:247    .text.FLASH_OB_GetRDP:******** FLASH_OB_GetRDP
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:281    .text.FLASH_OB_GetRDP:0000001c $d
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:286    .text.FLASH_OB_UserConfig:******** $t
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:291    .text.FLASH_OB_UserConfig:******** FLASH_OB_UserConfig
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:483    .text.FLASH_OB_UserConfig:000000c0 $d
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:488    .text.FLASH_OB_GetUser:******** $t
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:493    .text.FLASH_OB_GetUser:******** FLASH_OB_GetUser
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:516    .text.FLASH_OB_GetUser:0000000c $d
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:522    .text.FLASH_OB_PCROPConfig:******** $t
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:527    .text.FLASH_OB_PCROPConfig:******** FLASH_OB_PCROPConfig
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:572    .text.FLASH_OB_PCROPConfig:******** $d
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:577    .text.FLASH_OB_GetPCROP:******** $t
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:582    .text.FLASH_OB_GetPCROP:******** FLASH_OB_GetPCROP
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:659    .text.FLASH_OB_GetPCROP:00000038 $d
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:665    .text.FLASH_OB_BOR_LevelConfig:******** $t
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:670    .text.FLASH_OB_BOR_LevelConfig:******** FLASH_OB_BOR_LevelConfig
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:690    .text.FLASH_OB_BOR_LevelConfig:******** $d
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:695    .text.FLASH_OB_GetBOR:******** $t
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:700    .text.FLASH_OB_GetBOR:******** FLASH_OB_GetBOR
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:717    .text.FLASH_OB_GetBOR:0000000c $d
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:722    .text.FLASH_OB_BootAddConfig:******** $t
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:727    .text.FLASH_OB_BootAddConfig:******** FLASH_OB_BootAddConfig
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:776    .text.FLASH_OB_BootAddConfig:0000002c $d
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:781    .text.FLASH_OB_GetBootAdd:******** $t
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:786    .text.FLASH_OB_GetBootAdd:******** FLASH_OB_GetBootAdd
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:816    .text.FLASH_OB_GetBootAdd:******** $d
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:821    .text.FLASH_OB_SecureAreaConfig:******** $t
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:826    .text.FLASH_OB_SecureAreaConfig:******** FLASH_OB_SecureAreaConfig
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:872    .text.FLASH_OB_SecureAreaConfig:00000024 $d
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:877    .text.FLASH_OB_GetSecureArea:******** $t
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:882    .text.FLASH_OB_GetSecureArea:******** FLASH_OB_GetSecureArea
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:959    .text.FLASH_OB_GetSecureArea:00000038 $d
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:965    .text.FLASH_CRC_AddSector:******** $t
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:970    .text.FLASH_CRC_AddSector:******** FLASH_CRC_AddSector
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:1006   .text.FLASH_CRC_AddSector:0000001c $d
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:1011   .text.FLASH_CRC_SelectAddress:******** $t
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:1016   .text.FLASH_CRC_SelectAddress:******** FLASH_CRC_SelectAddress
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:1046   .text.FLASH_CRC_SelectAddress:******** $d
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:1051   .text.FLASH_OB_SharedRAM_Config:******** $t
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s 			page 83


C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:1056   .text.FLASH_OB_SharedRAM_Config:******** FLASH_OB_SharedRAM_Config
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:1076   .text.FLASH_OB_SharedRAM_Config:******** $d
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:1081   .text.FLASH_OB_SharedRAM_GetConfig:******** $t
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:1086   .text.FLASH_OB_SharedRAM_GetConfig:******** FLASH_OB_SharedRAM_GetConfig
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:1103   .text.FLASH_OB_SharedRAM_GetConfig:0000000c $d
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:1108   .text.FLASH_OB_CPUFreq_BoostConfig:******** $t
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:1113   .text.FLASH_OB_CPUFreq_BoostConfig:******** FLASH_OB_CPUFreq_BoostConfig
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:1133   .text.FLASH_OB_CPUFreq_BoostConfig:******** $d
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:1138   .text.FLASH_OB_CPUFreq_GetBoost:******** $t
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:1143   .text.FLASH_OB_CPUFreq_GetBoost:******** FLASH_OB_CPUFreq_GetBoost
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:1160   .text.FLASH_OB_CPUFreq_GetBoost:0000000c $d
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:1165   .text.HAL_FLASHEx_OBProgram:******** $t
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:1171   .text.HAL_FLASHEx_OBProgram:******** HAL_FLASHEx_OBProgram
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:1381   .text.HAL_FLASHEx_OBProgram:000000e8 $d
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:1386   .text.HAL_FLASHEx_OBGetConfig:******** $t
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:1392   .text.HAL_FLASHEx_OBGetConfig:******** HAL_FLASHEx_OBGetConfig
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:1502   .text.HAL_FLASHEx_Unlock_Bank1:******** $t
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:1508   .text.HAL_FLASHEx_Unlock_Bank1:******** HAL_FLASHEx_Unlock_Bank1
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:1549   .text.HAL_FLASHEx_Unlock_Bank1:0000002c $d
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:1555   .text.HAL_FLASHEx_Lock_Bank1:******** $t
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:1561   .text.HAL_FLASHEx_Lock_Bank1:******** HAL_FLASHEx_Lock_Bank1
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:1580   .text.HAL_FLASHEx_Lock_Bank1:******** $d
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:1585   .text.HAL_FLASHEx_ComputeCRC:******** $t
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:1591   .text.HAL_FLASHEx_ComputeCRC:******** HAL_FLASHEx_ComputeCRC
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:1758   .text.HAL_FLASHEx_ComputeCRC:000000ac $d
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:1764   .text.FLASH_Erase_Sector:******** $t
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:1770   .text.FLASH_Erase_Sector:******** FLASH_Erase_Sector
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:1810   .text.FLASH_Erase_Sector:******** $d
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:1815   .text.HAL_FLASHEx_Erase:******** $t
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:1821   .text.HAL_FLASHEx_Erase:******** HAL_FLASHEx_Erase
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:2015   .text.HAL_FLASHEx_Erase:000000bc $d
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:2022   .text.HAL_FLASHEx_Erase_IT:******** $t
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:2028   .text.HAL_FLASHEx_Erase_IT:******** HAL_FLASHEx_Erase_IT
C:\Users\<USER>\AppData\Local\Temp\ccs0NM3G.s:2181   .text.HAL_FLASHEx_Erase_IT:00000090 $d

UNDEFINED SYMBOLS
FLASH_WaitForLastOperation
pFlash
FLASH_OB_WaitForLastOperation
FLASH_CRC_WaitForLastOperation
