// generated from rosidl_generator_c/resource/idl.h.em
// with input from example_interfaces:msg/Float64.idl
// generated code does not contain a copyright notice

#ifndef EXAMPLE_INTERFACES__MSG__FLOAT64_H_
#define EXAMPLE_INTERFACES__MSG__FLOAT64_H_

#include "example_interfaces/msg/detail/float64__struct.h"
#include "example_interfaces/msg/detail/float64__functions.h"
#include "example_interfaces/msg/detail/float64__type_support.h"

#endif  // EXAMPLE_INTERFACES__MSG__FLOAT64_H_
