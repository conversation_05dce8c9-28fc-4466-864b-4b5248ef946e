ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s 			page 1


   1              		.cpu cortex-m7
   2              		.arch armv7e-m
   3              		.fpu fpv5-d16
   4              		.eabi_attribute 28, 1
   5              		.eabi_attribute 20, 1
   6              		.eabi_attribute 21, 1
   7              		.eabi_attribute 23, 3
   8              		.eabi_attribute 24, 1
   9              		.eabi_attribute 25, 1
  10              		.eabi_attribute 26, 1
  11              		.eabi_attribute 30, 1
  12              		.eabi_attribute 34, 1
  13              		.eabi_attribute 18, 4
  14              		.file	"stm32h7xx_hal_dma_ex.c"
  15              		.text
  16              	.Ltext0:
  17              		.cfi_sections	.debug_frame
  18              		.file 1 "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c"
  19              		.section	.text.DMA_MultiBufferSetConfig,"ax",%progbits
  20              		.align	1
  21              		.syntax unified
  22              		.thumb
  23              		.thumb_func
  25              	DMA_MultiBufferSetConfig:
  26              	.LVL0:
  27              	.LFB152:
   1:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** /**
   2:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   ******************************************************************************
   3:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   * @file    stm32h7xx_hal_dma_ex.c
   4:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   * <AUTHOR> Application Team
   5:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   * @brief   DMA Extension HAL module driver
   6:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   *          This file provides firmware functions to manage the following
   7:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   *          functionalities of the DMA Extension peripheral:
   8:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   *           + Extended features functions
   9:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   *
  10:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   @verbatim
  11:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   ==============================================================================
  12:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****                         ##### How to use this driver #####
  13:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   ==============================================================================
  14:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   [..]
  15:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   The DMA Extension HAL driver can be used as follows:
  16:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****    (+) Start a multi buffer transfer using the HAL_DMA_MultiBufferStart() function
  17:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****        for polling mode or HAL_DMA_MultiBufferStart_IT() for interrupt mode.
  18:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
  19:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****    (+) Configure the DMA_MUX Synchronization Block using HAL_DMAEx_ConfigMuxSync function.
  20:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****    (+) Configure the DMA_MUX Request Generator Block using HAL_DMAEx_ConfigMuxRequestGenerator func
  21:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****        Functions HAL_DMAEx_EnableMuxRequestGenerator and HAL_DMAEx_DisableMuxRequestGenerator can t
  22:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****        to respectively enable/disable the request generator.
  23:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
  24:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****    (+) To handle the DMAMUX Interrupts, the function  HAL_DMAEx_MUX_IRQHandler should be called fro
  25:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****        the DMAMUX IRQ handler i.e DMAMUX1_OVR_IRQHandler or DMAMUX2_OVR_IRQHandler .
  26:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****        As only one interrupt line is available for all DMAMUX channels and request generators , HAL
  27:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****        called with, as parameter, the appropriate DMA handle as many as used DMAs in the user proje
  28:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       (exception done if a given DMA is not using the DMAMUX SYNC block neither a request generator
  29:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
  30:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****      -@-  In Memory-to-Memory transfer mode, Multi (Double) Buffer mode is not allowed.
  31:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****      -@-  When Multi (Double) Buffer mode is enabled, the transfer is circular by default.
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s 			page 2


  32:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****      -@-  In Multi (Double) buffer mode, it is possible to update the base address for
  33:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****           the AHB memory port on the fly (DMA_SxM0AR or DMA_SxM1AR) when the stream is enabled.
  34:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****      -@-  Multi (Double) buffer mode is possible with DMA and BDMA instances.
  35:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
  36:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   @endverbatim
  37:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   ******************************************************************************
  38:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   * @attention
  39:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   *
  40:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   * Copyright (c) 2017 STMicroelectronics.
  41:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   * All rights reserved.
  42:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   *
  43:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   * This software is licensed under terms that can be found in the LICENSE file
  44:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   * in the root directory of this software component.
  45:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   * If no LICENSE file comes with this software, it is provided AS-IS.
  46:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   *
  47:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   ******************************************************************************
  48:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   */
  49:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
  50:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** /* Includes ------------------------------------------------------------------*/
  51:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** #include "stm32h7xx_hal.h"
  52:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
  53:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** /** @addtogroup STM32H7xx_HAL_Driver
  54:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   * @{
  55:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   */
  56:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
  57:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** /** @defgroup DMAEx DMAEx
  58:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   * @brief DMA Extended HAL module driver
  59:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   * @{
  60:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   */
  61:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
  62:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** #ifdef HAL_DMA_MODULE_ENABLED
  63:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
  64:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** /* Private types -------------------------------------------------------------*/
  65:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** /* Private variables ---------------------------------------------------------*/
  66:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** /* Private Constants ---------------------------------------------------------*/
  67:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** /* Private macros ------------------------------------------------------------*/
  68:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** /* Private functions ---------------------------------------------------------*/
  69:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** /** @addtogroup DMAEx_Private_Functions
  70:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   * @{
  71:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   */
  72:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
  73:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** static void DMA_MultiBufferSetConfig(DMA_HandleTypeDef *hdma, uint32_t SrcAddress, uint32_t DstAddr
  74:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
  75:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** /**
  76:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   * @}
  77:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   */
  78:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
  79:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** /* Exported functions ---------------------------------------------------------*/
  80:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
  81:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** /** @addtogroup DMAEx_Exported_Functions
  82:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   * @{
  83:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   */
  84:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
  85:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
  86:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** /** @addtogroup DMAEx_Exported_Functions_Group1
  87:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   *
  88:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** @verbatim
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s 			page 3


  89:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****  ===============================================================================
  90:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****                 #####  Extended features functions  #####
  91:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****  ===============================================================================
  92:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     [..]  This section provides functions allowing to:
  93:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       (+) Configure the source, destination address and data length and
  94:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****           Start MultiBuffer DMA transfer
  95:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       (+) Configure the source, destination address and data length and
  96:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****           Start MultiBuffer DMA transfer with interrupt
  97:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       (+) Change on the fly the memory0 or memory1 address.
  98:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       (+) Configure the DMA_MUX Synchronization Block using HAL_DMAEx_ConfigMuxSync function.
  99:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       (+) Configure the DMA_MUX Request Generator Block using HAL_DMAEx_ConfigMuxRequestGenerator f
 100:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       (+) Functions HAL_DMAEx_EnableMuxRequestGenerator and HAL_DMAEx_DisableMuxRequestGenerator ca
 101:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****           to respectively enable/disable the request generator.
 102:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       (+) Handle DMAMUX interrupts using HAL_DMAEx_MUX_IRQHandler : should be called from
 103:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****           the DMAMUX IRQ handler i.e DMAMUX1_OVR_IRQHandler or DMAMUX2_OVR_IRQHandler
 104:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 105:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** @endverbatim
 106:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   * @{
 107:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   */
 108:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 109:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 110:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** /**
 111:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   * @brief  Starts the multi_buffer DMA Transfer.
 112:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   * @param  hdma      : pointer to a DMA_HandleTypeDef structure that contains
 113:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   *                     the configuration information for the specified DMA Stream.
 114:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   * @param  SrcAddress: The source memory Buffer address
 115:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   * @param  DstAddress: The destination memory Buffer address
 116:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   * @param  SecondMemAddress: The second memory Buffer address in case of multi buffer Transfer
 117:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   * @param  DataLength: The length of data to be transferred from source to destination
 118:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   * @retval HAL status
 119:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   */
 120:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** HAL_StatusTypeDef HAL_DMAEx_MultiBufferStart(DMA_HandleTypeDef *hdma, uint32_t SrcAddress, uint32_t
 121:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** {
 122:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   HAL_StatusTypeDef status = HAL_OK;
 123:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   __IO uint32_t *ifcRegister_Base; /* DMA Stream Interrupt Clear register */
 124:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 125:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   /* Check the parameters */
 126:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   assert_param(IS_DMA_BUFFER_SIZE(DataLength));
 127:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   assert_param(IS_DMA_ALL_INSTANCE(hdma->Instance));
 128:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 129:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   /* Memory-to-memory transfer not supported in double buffering mode */
 130:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   if (hdma->Init.Direction == DMA_MEMORY_TO_MEMORY)
 131:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
 132:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     hdma->ErrorCode = HAL_DMA_ERROR_NOT_SUPPORTED;
 133:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     status = HAL_ERROR;
 134:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   }
 135:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   else
 136:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
 137:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     /* Process Locked */
 138:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     __HAL_LOCK(hdma);
 139:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 140:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     if(HAL_DMA_STATE_READY == hdma->State)
 141:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 142:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       /* Change DMA peripheral state */
 143:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       hdma->State = HAL_DMA_STATE_BUSY;
 144:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 145:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       /* Initialize the error code */
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s 			page 4


 146:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       hdma->ErrorCode = HAL_DMA_ERROR_NONE;
 147:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 148:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       if(IS_DMA_STREAM_INSTANCE(hdma->Instance) != 0U) /* DMA1 or DMA2 instance */
 149:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       {
 150:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****         /* Enable the Double buffer mode */
 151:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****         ((DMA_Stream_TypeDef   *)hdma->Instance)->CR |= DMA_SxCR_DBM;
 152:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 153:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****         /* Configure DMA Stream destination address */
 154:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****         ((DMA_Stream_TypeDef   *)hdma->Instance)->M1AR = SecondMemAddress;
 155:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 156:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****         /* Calculate the interrupt clear flag register (IFCR) base address  */
 157:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****         ifcRegister_Base = (uint32_t *)((uint32_t)(hdma->StreamBaseAddress + 8U));
 158:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 159:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****         /* Clear all flags */
 160:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****         *ifcRegister_Base = 0x3FUL << (hdma->StreamIndex & 0x1FU);
 161:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       }
 162:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       else /* BDMA instance(s) */
 163:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       {
 164:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****         /* Enable the Double buffer mode */
 165:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****         ((BDMA_Channel_TypeDef   *)hdma->Instance)->CCR |= (BDMA_CCR_DBM | BDMA_CCR_CIRC);
 166:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 167:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****         /* Configure DMA Stream destination address */
 168:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****         ((BDMA_Channel_TypeDef   *)hdma->Instance)->CM1AR = SecondMemAddress;
 169:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 170:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****         /* Calculate the interrupt clear flag register (IFCR) base address  */
 171:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****         ifcRegister_Base = (uint32_t *)((uint32_t)(hdma->StreamBaseAddress + 4U));
 172:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 173:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****         /* Clear all flags */
 174:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****         *ifcRegister_Base = (BDMA_ISR_GIF0) << (hdma->StreamIndex & 0x1FU);
 175:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       }
 176:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 177:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       if(IS_DMA_DMAMUX_ALL_INSTANCE(hdma->Instance) != 0U) /* No DMAMUX available for BDMA1 */
 178:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       {
 179:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****         /* Configure the source, destination address and the data length */
 180:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****         DMA_MultiBufferSetConfig(hdma, SrcAddress, DstAddress, DataLength);
 181:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 182:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****         /* Clear the DMAMUX synchro overrun flag */
 183:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****         hdma->DMAmuxChannelStatus->CFR = hdma->DMAmuxChannelStatusMask;
 184:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 185:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****         if(hdma->DMAmuxRequestGen != 0U)
 186:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****         {
 187:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****           /* Clear the DMAMUX request generator overrun flag */
 188:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****           hdma->DMAmuxRequestGenStatus->RGCFR = hdma->DMAmuxRequestGenStatusMask;
 189:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****         }
 190:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       }
 191:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 192:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       /* Enable the peripheral */
 193:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       __HAL_DMA_ENABLE(hdma);
 194:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     }
 195:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     else
 196:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 197:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       /* Set the error code to busy */
 198:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       hdma->ErrorCode = HAL_DMA_ERROR_BUSY;
 199:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 200:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       /* Return error status */
 201:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       status = HAL_ERROR;
 202:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     }
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s 			page 5


 203:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   }
 204:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   return status;
 205:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** }
 206:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 207:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** /**
 208:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   * @brief  Starts the multi_buffer DMA Transfer with interrupt enabled.
 209:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   * @param  hdma:       pointer to a DMA_HandleTypeDef structure that contains
 210:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   *                     the configuration information for the specified DMA Stream.
 211:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   * @param  SrcAddress: The source memory Buffer address
 212:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   * @param  DstAddress: The destination memory Buffer address
 213:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   * @param  SecondMemAddress: The second memory Buffer address in case of multi buffer Transfer
 214:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   * @param  DataLength: The length of data to be transferred from source to destination
 215:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   * @retval HAL status
 216:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   */
 217:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** HAL_StatusTypeDef HAL_DMAEx_MultiBufferStart_IT(DMA_HandleTypeDef *hdma, uint32_t SrcAddress, uint3
 218:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** {
 219:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   HAL_StatusTypeDef status = HAL_OK;
 220:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   __IO uint32_t *ifcRegister_Base; /* DMA Stream Interrupt Clear register */
 221:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 222:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   /* Check the parameters */
 223:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   assert_param(IS_DMA_BUFFER_SIZE(DataLength));
 224:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   assert_param(IS_DMA_ALL_INSTANCE(hdma->Instance));
 225:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 226:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   /* Memory-to-memory transfer not supported in double buffering mode */
 227:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   if(hdma->Init.Direction == DMA_MEMORY_TO_MEMORY)
 228:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
 229:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     hdma->ErrorCode = HAL_DMA_ERROR_NOT_SUPPORTED;
 230:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     return HAL_ERROR;
 231:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   }
 232:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 233:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   /* Process locked */
 234:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   __HAL_LOCK(hdma);
 235:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 236:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   if(HAL_DMA_STATE_READY == hdma->State)
 237:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
 238:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     /* Change DMA peripheral state */
 239:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     hdma->State = HAL_DMA_STATE_BUSY;
 240:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 241:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     /* Initialize the error code */
 242:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     hdma->ErrorCode = HAL_DMA_ERROR_NONE;
 243:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 244:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     if(IS_DMA_STREAM_INSTANCE(hdma->Instance) != 0U) /* DMA1 or DMA2 instance */
 245:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 246:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       /* Enable the Double buffer mode */
 247:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       ((DMA_Stream_TypeDef   *)hdma->Instance)->CR |= DMA_SxCR_DBM;
 248:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 249:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       /* Configure DMA Stream destination address */
 250:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       ((DMA_Stream_TypeDef   *)hdma->Instance)->M1AR = SecondMemAddress;
 251:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 252:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       /* Calculate the interrupt clear flag register (IFCR) base address  */
 253:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       ifcRegister_Base = (uint32_t *)((uint32_t)(hdma->StreamBaseAddress + 8U));
 254:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 255:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       /* Clear all flags */
 256:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       *ifcRegister_Base = 0x3FUL << (hdma->StreamIndex & 0x1FU);
 257:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     }
 258:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     else /* BDMA instance(s) */
 259:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s 			page 6


 260:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       /* Enable the Double buffer mode */
 261:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       ((BDMA_Channel_TypeDef   *)hdma->Instance)->CCR |= (BDMA_CCR_DBM | BDMA_CCR_CIRC);
 262:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 263:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       /* Configure DMA Stream destination address */
 264:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       ((BDMA_Channel_TypeDef   *)hdma->Instance)->CM1AR = SecondMemAddress;
 265:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 266:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       /* Calculate the interrupt clear flag register (IFCR) base address  */
 267:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       ifcRegister_Base = (uint32_t *)((uint32_t)(hdma->StreamBaseAddress + 4U));
 268:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 269:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       /* Clear all flags */
 270:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       *ifcRegister_Base = (BDMA_ISR_GIF0) << (hdma->StreamIndex & 0x1FU);
 271:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     }
 272:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 273:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     /* Configure the source, destination address and the data length */
 274:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     DMA_MultiBufferSetConfig(hdma, SrcAddress, DstAddress, DataLength);
 275:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 276:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     if(IS_DMA_DMAMUX_ALL_INSTANCE(hdma->Instance) != 0U) /* No DMAMUX available for BDMA1 */
 277:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 278:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       /* Clear the DMAMUX synchro overrun flag */
 279:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       hdma->DMAmuxChannelStatus->CFR = hdma->DMAmuxChannelStatusMask;
 280:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 281:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       if(hdma->DMAmuxRequestGen != 0U)
 282:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       {
 283:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****         /* Clear the DMAMUX request generator overrun flag */
 284:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****         hdma->DMAmuxRequestGenStatus->RGCFR = hdma->DMAmuxRequestGenStatusMask;
 285:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       }
 286:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     }
 287:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 288:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     if(IS_DMA_STREAM_INSTANCE(hdma->Instance) != 0U) /* DMA1 or DMA2 instance */
 289:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 290:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       /* Enable Common interrupts*/
 291:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       MODIFY_REG(((DMA_Stream_TypeDef   *)hdma->Instance)->CR, (DMA_IT_TC | DMA_IT_TE | DMA_IT_DME 
 292:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       ((DMA_Stream_TypeDef   *)hdma->Instance)->FCR |= DMA_IT_FE;
 293:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 294:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       if((hdma->XferHalfCpltCallback != NULL) || (hdma->XferM1HalfCpltCallback != NULL))
 295:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       {
 296:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****         /*Enable Half Transfer IT if corresponding Callback is set*/
 297:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****         ((DMA_Stream_TypeDef   *)hdma->Instance)->CR  |= DMA_IT_HT;
 298:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       }
 299:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     }
 300:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     else /* BDMA instance(s) */
 301:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 302:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       /* Enable Common interrupts*/
 303:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       MODIFY_REG(((BDMA_Channel_TypeDef   *)hdma->Instance)->CCR, (BDMA_CCR_TCIE | BDMA_CCR_HTIE | 
 304:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 305:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       if((hdma->XferHalfCpltCallback != NULL) || (hdma->XferM1HalfCpltCallback != NULL))
 306:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       {
 307:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****         /*Enable Half Transfer IT if corresponding Callback is set*/
 308:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****         ((BDMA_Channel_TypeDef   *)hdma->Instance)->CCR  |= BDMA_CCR_HTIE;
 309:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       }
 310:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     }
 311:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 312:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     if(IS_DMA_DMAMUX_ALL_INSTANCE(hdma->Instance) != 0U) /* No DMAMUX available for BDMA1 */
 313:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 314:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       /* Check if DMAMUX Synchronization is enabled*/
 315:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       if((hdma->DMAmuxChannel->CCR & DMAMUX_CxCR_SE) != 0U)
 316:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       {
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s 			page 7


 317:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****         /* Enable DMAMUX sync overrun IT*/
 318:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****         hdma->DMAmuxChannel->CCR |= DMAMUX_CxCR_SOIE;
 319:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       }
 320:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 321:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       if(hdma->DMAmuxRequestGen != 0U)
 322:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       {
 323:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****         /* if using DMAMUX request generator, enable the DMAMUX request generator overrun IT*/
 324:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****         /* enable the request gen overrun IT*/
 325:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****         hdma->DMAmuxRequestGen->RGCR |= DMAMUX_RGxCR_OIE;
 326:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       }
 327:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     }
 328:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 329:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     /* Enable the peripheral */
 330:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     __HAL_DMA_ENABLE(hdma);
 331:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   }
 332:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   else
 333:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
 334:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     /* Set the error code to busy */
 335:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     hdma->ErrorCode = HAL_DMA_ERROR_BUSY;
 336:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 337:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     /* Return error status */
 338:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     status = HAL_ERROR;
 339:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   }
 340:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   return status;
 341:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** }
 342:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 343:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** /**
 344:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   * @brief  Change the memory0 or memory1 address on the fly.
 345:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   * @param  hdma:       pointer to a DMA_HandleTypeDef structure that contains
 346:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   *                     the configuration information for the specified DMA Stream.
 347:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   * @param  Address:    The new address
 348:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   * @param  memory:     the memory to be changed, This parameter can be one of
 349:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   *                     the following values:
 350:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   *                      MEMORY0 /
 351:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   *                      MEMORY1
 352:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   * @note   The MEMORY0 address can be changed only when the current transfer use
 353:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   *         MEMORY1 and the MEMORY1 address can be changed only when the current
 354:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   *         transfer use MEMORY0.
 355:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   * @retval HAL status
 356:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   */
 357:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** HAL_StatusTypeDef HAL_DMAEx_ChangeMemory(DMA_HandleTypeDef *hdma, uint32_t Address, HAL_DMA_MemoryT
 358:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** {
 359:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   if(IS_DMA_STREAM_INSTANCE(hdma->Instance) != 0U) /* DMA1 or DMA2 instance */
 360:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
 361:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     if(memory == MEMORY0)
 362:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 363:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       /* change the memory0 address */
 364:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       ((DMA_Stream_TypeDef   *)hdma->Instance)->M0AR = Address;
 365:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     }
 366:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     else
 367:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 368:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       /* change the memory1 address */
 369:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       ((DMA_Stream_TypeDef   *)hdma->Instance)->M1AR = Address;
 370:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     }
 371:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   }
 372:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   else /* BDMA instance(s) */
 373:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s 			page 8


 374:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     if(memory == MEMORY0)
 375:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 376:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       /* change the memory0 address */
 377:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       ((BDMA_Channel_TypeDef   *)hdma->Instance)->CM0AR = Address;
 378:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     }
 379:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     else
 380:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 381:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       /* change the memory1 address */
 382:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       ((BDMA_Channel_TypeDef   *)hdma->Instance)->CM1AR = Address;
 383:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     }
 384:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   }
 385:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 386:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   return HAL_OK;
 387:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** }
 388:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 389:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** /**
 390:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   * @brief  Configure the DMAMUX synchronization parameters for a given DMA stream (instance).
 391:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   * @param  hdma:       pointer to a DMA_HandleTypeDef structure that contains
 392:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   *                     the configuration information for the specified DMA Stream.
 393:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   * @param  pSyncConfig : pointer to HAL_DMA_MuxSyncConfigTypeDef : contains the DMAMUX synchroniza
 394:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   * @retval HAL status
 395:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   */
 396:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** HAL_StatusTypeDef HAL_DMAEx_ConfigMuxSync(DMA_HandleTypeDef *hdma, HAL_DMA_MuxSyncConfigTypeDef *pS
 397:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** {
 398:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   uint32_t syncSignalID = 0;
 399:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   uint32_t syncPolarity = 0;
 400:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 401:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   /* Check the parameters */
 402:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   assert_param(IS_DMA_DMAMUX_ALL_INSTANCE(hdma->Instance));
 403:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   assert_param(IS_DMAMUX_SYNC_STATE(pSyncConfig->SyncEnable));
 404:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   assert_param(IS_DMAMUX_SYNC_EVENT(pSyncConfig->EventEnable));
 405:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   assert_param(IS_DMAMUX_SYNC_REQUEST_NUMBER(pSyncConfig->RequestNumber));
 406:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 407:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   if(pSyncConfig->SyncEnable == ENABLE)
 408:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
 409:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     assert_param(IS_DMAMUX_SYNC_POLARITY(pSyncConfig->SyncPolarity));
 410:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 411:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     if(IS_DMA_STREAM_INSTANCE(hdma->Instance) != 0U) /* DMA1 or DMA2 instance */
 412:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 413:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       assert_param(IS_DMA_DMAMUX_SYNC_SIGNAL_ID(pSyncConfig->SyncSignalID));
 414:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     }
 415:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     else
 416:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 417:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       assert_param(IS_BDMA_DMAMUX_SYNC_SIGNAL_ID(pSyncConfig->SyncSignalID));
 418:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     }
 419:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     syncSignalID = pSyncConfig->SyncSignalID;
 420:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     syncPolarity = pSyncConfig->SyncPolarity;
 421:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   }
 422:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 423:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   /*Check if the DMA state is ready */
 424:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   if(hdma->State == HAL_DMA_STATE_READY)
 425:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
 426:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     /* Process Locked */
 427:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     __HAL_LOCK(hdma);
 428:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 429:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     /* Disable the synchronization and event generation before applying a new config */
 430:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     CLEAR_BIT(hdma->DMAmuxChannel->CCR,(DMAMUX_CxCR_SE | DMAMUX_CxCR_EGE));
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s 			page 9


 431:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 432:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     /* Set the new synchronization parameters (and keep the request ID filled during the Init)*/
 433:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     MODIFY_REG( hdma->DMAmuxChannel->CCR, \
 434:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****                (~DMAMUX_CxCR_DMAREQ_ID) , \
 435:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****                (syncSignalID << DMAMUX_CxCR_SYNC_ID_Pos)       | \
 436:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****                ((pSyncConfig->RequestNumber - 1U) << DMAMUX_CxCR_NBREQ_Pos) | \
 437:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****                syncPolarity | ((uint32_t)pSyncConfig->SyncEnable << DMAMUX_CxCR_SE_Pos)    | \
 438:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****                ((uint32_t)pSyncConfig->EventEnable << DMAMUX_CxCR_EGE_Pos));
 439:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 440:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       /* Process Locked */
 441:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     __HAL_UNLOCK(hdma);
 442:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 443:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     return HAL_OK;
 444:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   }
 445:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   else
 446:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
 447:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     /* Set the error code to busy */
 448:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     hdma->ErrorCode = HAL_DMA_ERROR_BUSY;
 449:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 450:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     /* Return error status */
 451:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     return HAL_ERROR;
 452:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   }
 453:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** }
 454:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 455:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** /**
 456:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   * @brief  Configure the DMAMUX request generator block used by the given DMA stream (instance).
 457:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   * @param  hdma:       pointer to a DMA_HandleTypeDef structure that contains
 458:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   *                     the configuration information for the specified DMA Stream.
 459:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   * @param  pRequestGeneratorConfig : pointer to HAL_DMA_MuxRequestGeneratorConfigTypeDef :
 460:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   *         contains the request generator parameters.
 461:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   *
 462:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   * @retval HAL status
 463:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   */
 464:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** HAL_StatusTypeDef HAL_DMAEx_ConfigMuxRequestGenerator (DMA_HandleTypeDef *hdma, HAL_DMA_MuxRequestG
 465:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** {
 466:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   HAL_StatusTypeDef status;
 467:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   HAL_DMA_StateTypeDef temp_state = hdma->State;
 468:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 469:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   /* Check the parameters */
 470:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   assert_param(IS_DMA_DMAMUX_ALL_INSTANCE(hdma->Instance));
 471:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 472:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   if(IS_DMA_STREAM_INSTANCE(hdma->Instance) != 0U) /* DMA1 or DMA2 instance */
 473:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
 474:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     assert_param(IS_DMA_DMAMUX_REQUEST_GEN_SIGNAL_ID(pRequestGeneratorConfig->SignalID));
 475:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   }
 476:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   else
 477:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
 478:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     assert_param(IS_BDMA_DMAMUX_REQUEST_GEN_SIGNAL_ID(pRequestGeneratorConfig->SignalID));
 479:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   }
 480:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 481:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 482:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   assert_param(IS_DMAMUX_REQUEST_GEN_POLARITY(pRequestGeneratorConfig->Polarity));
 483:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   assert_param(IS_DMAMUX_REQUEST_GEN_REQUEST_NUMBER(pRequestGeneratorConfig->RequestNumber));
 484:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 485:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   /* check if the DMA state is ready
 486:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****      and DMA is using a DMAMUX request generator block
 487:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   */
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s 			page 10


 488:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   if(hdma->DMAmuxRequestGen == 0U)
 489:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
 490:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     /* Set the error code to busy */
 491:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     hdma->ErrorCode = HAL_DMA_ERROR_PARAM;
 492:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 493:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     /* error status */
 494:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     status = HAL_ERROR;
 495:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   }
 496:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   else if(((hdma->DMAmuxRequestGen->RGCR & DMAMUX_RGxCR_GE) == 0U) && (temp_state == HAL_DMA_STATE_
 497:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
 498:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     /* RequestGenerator must be disable prior to the configuration i.e GE bit is 0 */
 499:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 500:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     /* Process Locked */
 501:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     __HAL_LOCK(hdma);
 502:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 503:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     /* Set the request generator new parameters */
 504:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     hdma->DMAmuxRequestGen->RGCR = pRequestGeneratorConfig->SignalID | \
 505:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****                                   ((pRequestGeneratorConfig->RequestNumber - 1U) << DMAMUX_RGxCR_GN
 506:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****                                   pRequestGeneratorConfig->Polarity;
 507:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     /* Process Locked */
 508:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     __HAL_UNLOCK(hdma);
 509:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 510:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     return HAL_OK;
 511:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   }
 512:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   else
 513:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
 514:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     /* Set the error code to busy */
 515:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     hdma->ErrorCode = HAL_DMA_ERROR_BUSY;
 516:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 517:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     /* error status */
 518:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     status = HAL_ERROR;
 519:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   }
 520:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 521:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   return status;
 522:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** }
 523:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 524:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** /**
 525:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   * @brief  Enable the DMAMUX request generator block used by the given DMA stream (instance).
 526:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   * @param  hdma:       pointer to a DMA_HandleTypeDef structure that contains
 527:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   *                     the configuration information for the specified DMA Stream.
 528:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   * @retval HAL status
 529:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   */
 530:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** HAL_StatusTypeDef HAL_DMAEx_EnableMuxRequestGenerator (DMA_HandleTypeDef *hdma)
 531:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** {
 532:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   /* Check the parameters */
 533:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   assert_param(IS_DMA_DMAMUX_ALL_INSTANCE(hdma->Instance));
 534:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 535:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   /* check if the DMA state is ready
 536:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****      and DMA is using a DMAMUX request generator block */
 537:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   if((hdma->State != HAL_DMA_STATE_RESET) && (hdma->DMAmuxRequestGen != 0U))
 538:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
 539:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     /* Enable the request generator*/
 540:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     hdma->DMAmuxRequestGen->RGCR |= DMAMUX_RGxCR_GE;
 541:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 542:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****    return HAL_OK;
 543:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****  }
 544:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****  else
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s 			page 11


 545:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****  {
 546:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****    return HAL_ERROR;
 547:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****  }
 548:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** }
 549:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 550:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** /**
 551:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   * @brief  Disable the DMAMUX request generator block used by the given DMA stream (instance).
 552:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   * @param  hdma:       pointer to a DMA_HandleTypeDef structure that contains
 553:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   *                     the configuration information for the specified DMA Stream.
 554:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   * @retval HAL status
 555:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   */
 556:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** HAL_StatusTypeDef HAL_DMAEx_DisableMuxRequestGenerator (DMA_HandleTypeDef *hdma)
 557:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** {
 558:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   /* Check the parameters */
 559:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   assert_param(IS_DMA_DMAMUX_ALL_INSTANCE(hdma->Instance));
 560:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 561:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   /* check if the DMA state is ready
 562:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****      and DMA is using a DMAMUX request generator block */
 563:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   if((hdma->State != HAL_DMA_STATE_RESET) && (hdma->DMAmuxRequestGen != 0U))
 564:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
 565:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     /* Disable the request generator*/
 566:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     hdma->DMAmuxRequestGen->RGCR &= ~DMAMUX_RGxCR_GE;
 567:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 568:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****    return HAL_OK;
 569:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****  }
 570:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****  else
 571:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****  {
 572:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****    return HAL_ERROR;
 573:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****  }
 574:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** }
 575:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 576:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** /**
 577:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   * @brief  Handles DMAMUX interrupt request.
 578:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   * @param  hdma: pointer to a DMA_HandleTypeDef structure that contains
 579:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   *               the configuration information for the specified DMA Stream.
 580:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   * @retval None
 581:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   */
 582:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** void HAL_DMAEx_MUX_IRQHandler(DMA_HandleTypeDef *hdma)
 583:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** {
 584:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   /* Check for DMAMUX Synchronization overrun */
 585:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   if((hdma->DMAmuxChannelStatus->CSR & hdma->DMAmuxChannelStatusMask) != 0U)
 586:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
 587:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     /* Disable the synchro overrun interrupt */
 588:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     hdma->DMAmuxChannel->CCR &= ~DMAMUX_CxCR_SOIE;
 589:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 590:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     /* Clear the DMAMUX synchro overrun flag */
 591:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     hdma->DMAmuxChannelStatus->CFR = hdma->DMAmuxChannelStatusMask;
 592:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 593:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     /* Update error code */
 594:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     hdma->ErrorCode |= HAL_DMA_ERROR_SYNC;
 595:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 596:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     if(hdma->XferErrorCallback != NULL)
 597:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 598:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       /* Transfer error callback */
 599:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       hdma->XferErrorCallback(hdma);
 600:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     }
 601:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   }
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s 			page 12


 602:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 603:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   if(hdma->DMAmuxRequestGen != 0)
 604:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
 605:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****    /* if using a DMAMUX request generator block Check for DMAMUX request generator overrun */
 606:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     if((hdma->DMAmuxRequestGenStatus->RGSR & hdma->DMAmuxRequestGenStatusMask) != 0U)
 607:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 608:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       /* Disable the request gen overrun interrupt */
 609:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       hdma->DMAmuxRequestGen->RGCR &= ~DMAMUX_RGxCR_OIE;
 610:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 611:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       /* Clear the DMAMUX request generator overrun flag */
 612:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       hdma->DMAmuxRequestGenStatus->RGCFR = hdma->DMAmuxRequestGenStatusMask;
 613:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 614:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       /* Update error code */
 615:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       hdma->ErrorCode |= HAL_DMA_ERROR_REQGEN;
 616:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 617:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       if(hdma->XferErrorCallback != NULL)
 618:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       {
 619:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****         /* Transfer error callback */
 620:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****         hdma->XferErrorCallback(hdma);
 621:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       }
 622:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     }
 623:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   }
 624:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** }
 625:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 626:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 627:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** /**
 628:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   * @}
 629:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   */
 630:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 631:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** /**
 632:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   * @}
 633:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   */
 634:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 635:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** /** @addtogroup DMAEx_Private_Functions
 636:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   * @{
 637:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   */
 638:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 639:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** /**
 640:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   * @brief  Set the DMA Transfer parameter.
 641:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   * @param  hdma:       pointer to a DMA_HandleTypeDef structure that contains
 642:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   *                     the configuration information for the specified DMA Stream.
 643:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   * @param  SrcAddress: The source memory Buffer address
 644:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   * @param  DstAddress: The destination memory Buffer address
 645:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   * @param  DataLength: The length of data to be transferred from source to destination
 646:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   * @retval HAL status
 647:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   */
 648:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** static void DMA_MultiBufferSetConfig(DMA_HandleTypeDef *hdma, uint32_t SrcAddress, uint32_t DstAddr
 649:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** {
  28              		.loc 1 649 1 view -0
  29              		.cfi_startproc
  30              		@ args = 0, pretend = 0, frame = 0
  31              		@ frame_needed = 0, uses_anonymous_args = 0
  32              		@ link register save eliminated.
  33              		.loc 1 649 1 is_stmt 0 view .LVU1
  34 0000 70B4     		push	{r4, r5, r6}
  35              	.LCFI0:
  36              		.cfi_def_cfa_offset 12
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s 			page 13


  37              		.cfi_offset 4, -12
  38              		.cfi_offset 5, -8
  39              		.cfi_offset 6, -4
 650:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   if(IS_DMA_STREAM_INSTANCE(hdma->Instance) != 0U) /* DMA1 or DMA2 instance */
  40              		.loc 1 650 3 is_stmt 1 view .LVU2
  41              		.loc 1 650 6 is_stmt 0 view .LVU3
  42 0002 0468     		ldr	r4, [r0]
  43 0004 264E     		ldr	r6, .L11
  44 0006 274D     		ldr	r5, .L11+4
  45 0008 AC42     		cmp	r4, r5
  46 000a 18BF     		it	ne
  47 000c B442     		cmpne	r4, r6
  48 000e 33D0     		beq	.L2
  49              		.loc 1 650 6 discriminator 2 view .LVU4
  50 0010 1835     		adds	r5, r5, #24
  51 0012 AC42     		cmp	r4, r5
  52 0014 30D0     		beq	.L2
  53              		.loc 1 650 6 discriminator 4 view .LVU5
  54 0016 1835     		adds	r5, r5, #24
  55 0018 AC42     		cmp	r4, r5
  56 001a 2DD0     		beq	.L2
  57              		.loc 1 650 6 discriminator 6 view .LVU6
  58 001c 1835     		adds	r5, r5, #24
  59 001e AC42     		cmp	r4, r5
  60 0020 2AD0     		beq	.L2
  61              		.loc 1 650 6 discriminator 8 view .LVU7
  62 0022 1835     		adds	r5, r5, #24
  63 0024 AC42     		cmp	r4, r5
  64 0026 27D0     		beq	.L2
  65              		.loc 1 650 6 discriminator 10 view .LVU8
  66 0028 1835     		adds	r5, r5, #24
  67 002a AC42     		cmp	r4, r5
  68 002c 24D0     		beq	.L2
  69              		.loc 1 650 6 discriminator 12 view .LVU9
  70 002e 1835     		adds	r5, r5, #24
  71 0030 AC42     		cmp	r4, r5
  72 0032 21D0     		beq	.L2
  73              		.loc 1 650 6 discriminator 14 view .LVU10
  74 0034 05F55675 		add	r5, r5, #856
  75 0038 AC42     		cmp	r4, r5
  76 003a 1DD0     		beq	.L2
  77              		.loc 1 650 6 discriminator 16 view .LVU11
  78 003c 1835     		adds	r5, r5, #24
  79 003e AC42     		cmp	r4, r5
  80 0040 1AD0     		beq	.L2
  81              		.loc 1 650 6 discriminator 18 view .LVU12
  82 0042 1835     		adds	r5, r5, #24
  83 0044 AC42     		cmp	r4, r5
  84 0046 17D0     		beq	.L2
  85              		.loc 1 650 6 discriminator 20 view .LVU13
  86 0048 1835     		adds	r5, r5, #24
  87 004a AC42     		cmp	r4, r5
  88 004c 14D0     		beq	.L2
  89              		.loc 1 650 6 discriminator 22 view .LVU14
  90 004e 1835     		adds	r5, r5, #24
  91 0050 AC42     		cmp	r4, r5
  92 0052 11D0     		beq	.L2
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s 			page 14


  93              		.loc 1 650 6 discriminator 24 view .LVU15
  94 0054 1835     		adds	r5, r5, #24
  95 0056 AC42     		cmp	r4, r5
  96 0058 0ED0     		beq	.L2
  97              		.loc 1 650 6 discriminator 26 view .LVU16
  98 005a 1835     		adds	r5, r5, #24
  99 005c AC42     		cmp	r4, r5
 100 005e 0BD0     		beq	.L2
 101              		.loc 1 650 6 discriminator 28 view .LVU17
 102 0060 1835     		adds	r5, r5, #24
 103 0062 AC42     		cmp	r4, r5
 104 0064 08D0     		beq	.L2
 651:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
 652:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     /* Configure DMA Stream data length */
 653:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     ((DMA_Stream_TypeDef   *)hdma->Instance)->NDTR = DataLength;
 654:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 655:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     /* Peripheral to Memory */
 656:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     if((hdma->Init.Direction) == DMA_MEMORY_TO_PERIPH)
 657:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 658:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       /* Configure DMA Stream destination address */
 659:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       ((DMA_Stream_TypeDef   *)hdma->Instance)->PAR = DstAddress;
 660:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 661:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       /* Configure DMA Stream source address */
 662:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       ((DMA_Stream_TypeDef   *)hdma->Instance)->M0AR = SrcAddress;
 663:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     }
 664:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     /* Memory to Peripheral */
 665:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     else
 666:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 667:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       /* Configure DMA Stream source address */
 668:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       ((DMA_Stream_TypeDef   *)hdma->Instance)->PAR = SrcAddress;
 669:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 670:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       /* Configure DMA Stream destination address */
 671:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       ((DMA_Stream_TypeDef   *)hdma->Instance)->M0AR = DstAddress;
 672:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     }
 673:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   }
 674:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   else /* BDMA instance(s) */
 675:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
 676:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     /* Configure DMA Stream data length */
 677:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     ((BDMA_Channel_TypeDef   *)hdma->Instance)->CNDTR = DataLength;
 105              		.loc 1 677 5 is_stmt 1 view .LVU18
 106              		.loc 1 677 55 is_stmt 0 view .LVU19
 107 0066 6360     		str	r3, [r4, #4]
 678:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 679:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     /* Peripheral to Memory */
 680:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     if((hdma->Init.Direction) == DMA_MEMORY_TO_PERIPH)
 108              		.loc 1 680 5 is_stmt 1 view .LVU20
 109              		.loc 1 680 19 is_stmt 0 view .LVU21
 110 0068 8368     		ldr	r3, [r0, #8]
 111              	.LVL1:
 112              		.loc 1 680 7 view .LVU22
 113 006a 402B     		cmp	r3, #64
 114 006c 13D0     		beq	.L9
 681:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 682:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       /* Configure DMA Stream destination address */
 683:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       ((BDMA_Channel_TypeDef   *)hdma->Instance)->CPAR = DstAddress;
 684:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 685:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       /* Configure DMA Stream source address */
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s 			page 15


 686:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       ((BDMA_Channel_TypeDef   *)hdma->Instance)->CM0AR = SrcAddress;
 687:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     }
 688:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     /* Memory to Peripheral */
 689:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     else
 690:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 691:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       /* Configure DMA Stream source address */
 692:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       ((BDMA_Channel_TypeDef   *)hdma->Instance)->CPAR = SrcAddress;
 115              		.loc 1 692 7 is_stmt 1 view .LVU23
 116              		.loc 1 692 38 is_stmt 0 view .LVU24
 117 006e 0368     		ldr	r3, [r0]
 118              		.loc 1 692 56 view .LVU25
 119 0070 9960     		str	r1, [r3, #8]
 120              	.LVL2:
 693:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 694:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       /* Configure DMA Stream destination address */
 695:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       ((BDMA_Channel_TypeDef   *)hdma->Instance)->CM0AR = DstAddress;
 121              		.loc 1 695 7 is_stmt 1 view .LVU26
 122              		.loc 1 695 38 is_stmt 0 view .LVU27
 123 0072 0368     		ldr	r3, [r0]
 124              		.loc 1 695 57 view .LVU28
 125 0074 DA60     		str	r2, [r3, #12]
 696:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     }
 697:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   }
 698:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** }
 126              		.loc 1 698 1 view .LVU29
 127 0076 07E0     		b	.L1
 128              	.LVL3:
 129              	.L2:
 653:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 130              		.loc 1 653 5 is_stmt 1 view .LVU30
 653:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 131              		.loc 1 653 52 is_stmt 0 view .LVU31
 132 0078 6360     		str	r3, [r4, #4]
 656:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 133              		.loc 1 656 5 is_stmt 1 view .LVU32
 656:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 134              		.loc 1 656 19 is_stmt 0 view .LVU33
 135 007a 8368     		ldr	r3, [r0, #8]
 136              	.LVL4:
 656:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 137              		.loc 1 656 7 view .LVU34
 138 007c 402B     		cmp	r3, #64
 139 007e 05D0     		beq	.L10
 668:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 140              		.loc 1 668 7 is_stmt 1 view .LVU35
 668:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 141              		.loc 1 668 36 is_stmt 0 view .LVU36
 142 0080 0368     		ldr	r3, [r0]
 668:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 143              		.loc 1 668 53 view .LVU37
 144 0082 9960     		str	r1, [r3, #8]
 145              	.LVL5:
 671:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     }
 146              		.loc 1 671 7 is_stmt 1 view .LVU38
 671:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     }
 147              		.loc 1 671 36 is_stmt 0 view .LVU39
 148 0084 0368     		ldr	r3, [r0]
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s 			page 16


 671:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     }
 149              		.loc 1 671 54 view .LVU40
 150 0086 DA60     		str	r2, [r3, #12]
 151              	.L1:
 152              		.loc 1 698 1 view .LVU41
 153 0088 70BC     		pop	{r4, r5, r6}
 154              	.LCFI1:
 155              		.cfi_remember_state
 156              		.cfi_restore 6
 157              		.cfi_restore 5
 158              		.cfi_restore 4
 159              		.cfi_def_cfa_offset 0
 160 008a 7047     		bx	lr
 161              	.LVL6:
 162              	.L10:
 163              	.LCFI2:
 164              		.cfi_restore_state
 659:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 165              		.loc 1 659 7 is_stmt 1 view .LVU42
 659:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 166              		.loc 1 659 36 is_stmt 0 view .LVU43
 167 008c 0368     		ldr	r3, [r0]
 659:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 168              		.loc 1 659 53 view .LVU44
 169 008e 9A60     		str	r2, [r3, #8]
 170              	.LVL7:
 662:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     }
 171              		.loc 1 662 7 is_stmt 1 view .LVU45
 662:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     }
 172              		.loc 1 662 36 is_stmt 0 view .LVU46
 173 0090 0368     		ldr	r3, [r0]
 662:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     }
 174              		.loc 1 662 54 view .LVU47
 175 0092 D960     		str	r1, [r3, #12]
 176 0094 F8E7     		b	.L1
 177              	.LVL8:
 178              	.L9:
 683:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 179              		.loc 1 683 7 is_stmt 1 view .LVU48
 683:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 180              		.loc 1 683 38 is_stmt 0 view .LVU49
 181 0096 0368     		ldr	r3, [r0]
 683:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 182              		.loc 1 683 56 view .LVU50
 183 0098 9A60     		str	r2, [r3, #8]
 184              	.LVL9:
 686:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     }
 185              		.loc 1 686 7 is_stmt 1 view .LVU51
 686:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     }
 186              		.loc 1 686 38 is_stmt 0 view .LVU52
 187 009a 0368     		ldr	r3, [r0]
 686:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     }
 188              		.loc 1 686 57 view .LVU53
 189 009c D960     		str	r1, [r3, #12]
 190 009e F3E7     		b	.L1
 191              	.L12:
 192              		.align	2
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s 			page 17


 193              	.L11:
 194 00a0 10000240 		.word	1073872912
 195 00a4 28000240 		.word	1073872936
 196              		.cfi_endproc
 197              	.LFE152:
 199              		.section	.text.HAL_DMAEx_MultiBufferStart,"ax",%progbits
 200              		.align	1
 201              		.global	HAL_DMAEx_MultiBufferStart
 202              		.syntax unified
 203              		.thumb
 204              		.thumb_func
 206              	HAL_DMAEx_MultiBufferStart:
 207              	.LVL10:
 208              	.LFB144:
 121:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   HAL_StatusTypeDef status = HAL_OK;
 209              		.loc 1 121 1 is_stmt 1 view -0
 210              		.cfi_startproc
 211              		@ args = 4, pretend = 0, frame = 0
 212              		@ frame_needed = 0, uses_anonymous_args = 0
 121:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   HAL_StatusTypeDef status = HAL_OK;
 213              		.loc 1 121 1 is_stmt 0 view .LVU55
 214 0000 70B5     		push	{r4, r5, r6, lr}
 215              	.LCFI3:
 216              		.cfi_def_cfa_offset 16
 217              		.cfi_offset 4, -16
 218              		.cfi_offset 5, -12
 219              		.cfi_offset 6, -8
 220              		.cfi_offset 14, -4
 221 0002 0446     		mov	r4, r0
 122:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   __IO uint32_t *ifcRegister_Base; /* DMA Stream Interrupt Clear register */
 222              		.loc 1 122 3 is_stmt 1 view .LVU56
 223              	.LVL11:
 123:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 224              		.loc 1 123 3 view .LVU57
 126:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   assert_param(IS_DMA_ALL_INSTANCE(hdma->Instance));
 225              		.loc 1 126 3 view .LVU58
 127:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 226              		.loc 1 127 3 view .LVU59
 130:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
 227              		.loc 1 130 3 view .LVU60
 130:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
 228              		.loc 1 130 17 is_stmt 0 view .LVU61
 229 0004 8068     		ldr	r0, [r0, #8]
 230              	.LVL12:
 130:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
 231              		.loc 1 130 6 view .LVU62
 232 0006 8028     		cmp	r0, #128
 233 0008 11D0     		beq	.L25
 138:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 234              		.loc 1 138 5 is_stmt 1 view .LVU63
 138:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 235              		.loc 1 138 5 view .LVU64
 236 000a 94F83400 		ldrb	r0, [r4, #52]	@ zero_extendqisi2
 237 000e 0128     		cmp	r0, #1
 238 0010 00F0F980 		beq	.L23
 138:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 239              		.loc 1 138 5 discriminator 2 view .LVU65
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s 			page 18


 240 0014 0120     		movs	r0, #1
 241 0016 84F83400 		strb	r0, [r4, #52]
 138:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 242              		.loc 1 138 5 discriminator 2 view .LVU66
 140:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 243              		.loc 1 140 5 view .LVU67
 140:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 244              		.loc 1 140 35 is_stmt 0 view .LVU68
 245 001a 94F83500 		ldrb	r0, [r4, #53]	@ zero_extendqisi2
 246 001e C0B2     		uxtb	r0, r0
 140:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 247              		.loc 1 140 7 view .LVU69
 248 0020 0128     		cmp	r0, #1
 249 0022 09D0     		beq	.L26
 198:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 250              		.loc 1 198 7 is_stmt 1 view .LVU70
 198:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 251              		.loc 1 198 23 is_stmt 0 view .LVU71
 252 0024 4FF40063 		mov	r3, #2048
 253              	.LVL13:
 198:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 254              		.loc 1 198 23 view .LVU72
 255 0028 6365     		str	r3, [r4, #84]
 201:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     }
 256              		.loc 1 201 7 is_stmt 1 view .LVU73
 257              	.LVL14:
 201:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     }
 258              		.loc 1 201 14 is_stmt 0 view .LVU74
 259 002a 0120     		movs	r0, #1
 260              	.LVL15:
 261              	.L15:
 205:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 262              		.loc 1 205 1 view .LVU75
 263 002c 70BD     		pop	{r4, r5, r6, pc}
 264              	.LVL16:
 265              	.L25:
 132:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     status = HAL_ERROR;
 266              		.loc 1 132 5 is_stmt 1 view .LVU76
 132:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     status = HAL_ERROR;
 267              		.loc 1 132 21 is_stmt 0 view .LVU77
 268 002e 4FF48073 		mov	r3, #256
 269              	.LVL17:
 132:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     status = HAL_ERROR;
 270              		.loc 1 132 21 view .LVU78
 271 0032 6365     		str	r3, [r4, #84]
 133:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   }
 272              		.loc 1 133 5 is_stmt 1 view .LVU79
 273              	.LVL18:
 133:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   }
 274              		.loc 1 133 12 is_stmt 0 view .LVU80
 275 0034 0120     		movs	r0, #1
 276 0036 F9E7     		b	.L15
 277              	.LVL19:
 278              	.L26:
 143:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 279              		.loc 1 143 7 is_stmt 1 view .LVU81
 143:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s 			page 19


 280              		.loc 1 143 19 is_stmt 0 view .LVU82
 281 0038 0220     		movs	r0, #2
 282 003a 84F83500 		strb	r0, [r4, #53]
 146:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 283              		.loc 1 146 7 is_stmt 1 view .LVU83
 146:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 284              		.loc 1 146 23 is_stmt 0 view .LVU84
 285 003e 0020     		movs	r0, #0
 286 0040 6065     		str	r0, [r4, #84]
 148:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       {
 287              		.loc 1 148 7 is_stmt 1 view .LVU85
 148:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       {
 288              		.loc 1 148 10 is_stmt 0 view .LVU86
 289 0042 2068     		ldr	r0, [r4]
 290 0044 714E     		ldr	r6, .L27
 291 0046 724D     		ldr	r5, .L27+4
 292 0048 A842     		cmp	r0, r5
 293 004a 18BF     		it	ne
 294 004c B042     		cmpne	r0, r6
 295 004e 39D0     		beq	.L17
 148:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       {
 296              		.loc 1 148 10 discriminator 2 view .LVU87
 297 0050 1835     		adds	r5, r5, #24
 298 0052 A842     		cmp	r0, r5
 299 0054 36D0     		beq	.L17
 148:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       {
 300              		.loc 1 148 10 discriminator 4 view .LVU88
 301 0056 1835     		adds	r5, r5, #24
 302 0058 A842     		cmp	r0, r5
 303 005a 33D0     		beq	.L17
 148:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       {
 304              		.loc 1 148 10 discriminator 6 view .LVU89
 305 005c 1835     		adds	r5, r5, #24
 306 005e A842     		cmp	r0, r5
 307 0060 30D0     		beq	.L17
 148:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       {
 308              		.loc 1 148 10 discriminator 8 view .LVU90
 309 0062 1835     		adds	r5, r5, #24
 310 0064 A842     		cmp	r0, r5
 311 0066 2DD0     		beq	.L17
 148:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       {
 312              		.loc 1 148 10 discriminator 10 view .LVU91
 313 0068 1835     		adds	r5, r5, #24
 314 006a A842     		cmp	r0, r5
 315 006c 2AD0     		beq	.L17
 148:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       {
 316              		.loc 1 148 10 discriminator 12 view .LVU92
 317 006e 1835     		adds	r5, r5, #24
 318 0070 A842     		cmp	r0, r5
 319 0072 27D0     		beq	.L17
 148:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       {
 320              		.loc 1 148 10 discriminator 14 view .LVU93
 321 0074 05F55675 		add	r5, r5, #856
 322 0078 A842     		cmp	r0, r5
 323 007a 23D0     		beq	.L17
 148:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       {
 324              		.loc 1 148 10 discriminator 16 view .LVU94
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s 			page 20


 325 007c 1835     		adds	r5, r5, #24
 326 007e A842     		cmp	r0, r5
 327 0080 20D0     		beq	.L17
 148:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       {
 328              		.loc 1 148 10 discriminator 18 view .LVU95
 329 0082 1835     		adds	r5, r5, #24
 330 0084 A842     		cmp	r0, r5
 331 0086 1DD0     		beq	.L17
 148:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       {
 332              		.loc 1 148 10 discriminator 20 view .LVU96
 333 0088 1835     		adds	r5, r5, #24
 334 008a A842     		cmp	r0, r5
 335 008c 1AD0     		beq	.L17
 148:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       {
 336              		.loc 1 148 10 discriminator 22 view .LVU97
 337 008e 1835     		adds	r5, r5, #24
 338 0090 A842     		cmp	r0, r5
 339 0092 17D0     		beq	.L17
 148:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       {
 340              		.loc 1 148 10 discriminator 24 view .LVU98
 341 0094 1835     		adds	r5, r5, #24
 342 0096 A842     		cmp	r0, r5
 343 0098 14D0     		beq	.L17
 148:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       {
 344              		.loc 1 148 10 discriminator 26 view .LVU99
 345 009a 1835     		adds	r5, r5, #24
 346 009c A842     		cmp	r0, r5
 347 009e 11D0     		beq	.L17
 148:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       {
 348              		.loc 1 148 10 discriminator 28 view .LVU100
 349 00a0 1835     		adds	r5, r5, #24
 350 00a2 A842     		cmp	r0, r5
 351 00a4 0ED0     		beq	.L17
 165:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 352              		.loc 1 165 9 is_stmt 1 view .LVU101
 165:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 353              		.loc 1 165 51 is_stmt 0 view .LVU102
 354 00a6 0668     		ldr	r6, [r0]
 165:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 355              		.loc 1 165 57 view .LVU103
 356 00a8 48F22005 		movw	r5, #32800
 357 00ac 3543     		orrs	r5, r5, r6
 358 00ae 0560     		str	r5, [r0]
 168:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 359              		.loc 1 168 9 is_stmt 1 view .LVU104
 168:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 360              		.loc 1 168 40 is_stmt 0 view .LVU105
 361 00b0 2068     		ldr	r0, [r4]
 168:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 362              		.loc 1 168 59 view .LVU106
 363 00b2 0361     		str	r3, [r0, #16]
 171:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 364              		.loc 1 171 9 is_stmt 1 view .LVU107
 171:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 365              		.loc 1 171 56 is_stmt 0 view .LVU108
 366 00b4 A56D     		ldr	r5, [r4, #88]
 367              	.LVL20:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s 			page 21


 174:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       }
 368              		.loc 1 174 9 is_stmt 1 view .LVU109
 174:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       }
 369              		.loc 1 174 53 is_stmt 0 view .LVU110
 370 00b6 E36D     		ldr	r3, [r4, #92]
 371              	.LVL21:
 174:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       }
 372              		.loc 1 174 67 view .LVU111
 373 00b8 03F01F00 		and	r0, r3, #31
 374              	.LVL22:
 174:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       }
 375              		.loc 1 174 45 view .LVU112
 376 00bc 0123     		movs	r3, #1
 377 00be 8340     		lsls	r3, r3, r0
 174:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       }
 378              		.loc 1 174 27 view .LVU113
 379 00c0 6B60     		str	r3, [r5, #4]
 380 00c2 0CE0     		b	.L18
 381              	.LVL23:
 382              	.L17:
 151:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 383              		.loc 1 151 9 is_stmt 1 view .LVU114
 151:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 384              		.loc 1 151 49 is_stmt 0 view .LVU115
 385 00c4 0568     		ldr	r5, [r0]
 151:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 386              		.loc 1 151 54 view .LVU116
 387 00c6 45F48025 		orr	r5, r5, #262144
 388 00ca 0560     		str	r5, [r0]
 154:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 389              		.loc 1 154 9 is_stmt 1 view .LVU117
 154:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 390              		.loc 1 154 38 is_stmt 0 view .LVU118
 391 00cc 2068     		ldr	r0, [r4]
 154:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 392              		.loc 1 154 56 view .LVU119
 393 00ce 0361     		str	r3, [r0, #16]
 157:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 394              		.loc 1 157 9 is_stmt 1 view .LVU120
 157:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 395              		.loc 1 157 56 is_stmt 0 view .LVU121
 396 00d0 A56D     		ldr	r5, [r4, #88]
 397              	.LVL24:
 160:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       }
 398              		.loc 1 160 9 is_stmt 1 view .LVU122
 160:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       }
 399              		.loc 1 160 44 is_stmt 0 view .LVU123
 400 00d2 E36D     		ldr	r3, [r4, #92]
 401              	.LVL25:
 160:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       }
 402              		.loc 1 160 58 view .LVU124
 403 00d4 03F01F00 		and	r0, r3, #31
 404              	.LVL26:
 160:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       }
 405              		.loc 1 160 36 view .LVU125
 406 00d8 3F23     		movs	r3, #63
 407 00da 8340     		lsls	r3, r3, r0
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s 			page 22


 160:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       }
 408              		.loc 1 160 27 view .LVU126
 409 00dc AB60     		str	r3, [r5, #8]
 410              	.LVL27:
 411              	.L18:
 177:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       {
 412              		.loc 1 177 7 is_stmt 1 view .LVU127
 177:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       {
 413              		.loc 1 177 10 is_stmt 0 view .LVU128
 414 00de 2368     		ldr	r3, [r4]
 415 00e0 4A4D     		ldr	r5, .L27
 416 00e2 4B48     		ldr	r0, .L27+4
 417 00e4 8342     		cmp	r3, r0
 418 00e6 18BF     		it	ne
 419 00e8 AB42     		cmpne	r3, r5
 420 00ea 42D0     		beq	.L19
 177:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       {
 421              		.loc 1 177 10 discriminator 2 view .LVU129
 422 00ec 1830     		adds	r0, r0, #24
 423 00ee 8342     		cmp	r3, r0
 424 00f0 3FD0     		beq	.L19
 177:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       {
 425              		.loc 1 177 10 discriminator 4 view .LVU130
 426 00f2 1830     		adds	r0, r0, #24
 427 00f4 8342     		cmp	r3, r0
 428 00f6 3CD0     		beq	.L19
 177:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       {
 429              		.loc 1 177 10 discriminator 6 view .LVU131
 430 00f8 1830     		adds	r0, r0, #24
 431 00fa 8342     		cmp	r3, r0
 432 00fc 39D0     		beq	.L19
 177:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       {
 433              		.loc 1 177 10 discriminator 8 view .LVU132
 434 00fe 1830     		adds	r0, r0, #24
 435 0100 8342     		cmp	r3, r0
 436 0102 36D0     		beq	.L19
 177:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       {
 437              		.loc 1 177 10 discriminator 10 view .LVU133
 438 0104 1830     		adds	r0, r0, #24
 439 0106 8342     		cmp	r3, r0
 440 0108 33D0     		beq	.L19
 177:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       {
 441              		.loc 1 177 10 discriminator 12 view .LVU134
 442 010a 1830     		adds	r0, r0, #24
 443 010c 8342     		cmp	r3, r0
 444 010e 30D0     		beq	.L19
 177:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       {
 445              		.loc 1 177 10 discriminator 14 view .LVU135
 446 0110 00F55670 		add	r0, r0, #856
 447 0114 8342     		cmp	r3, r0
 448 0116 2CD0     		beq	.L19
 177:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       {
 449              		.loc 1 177 10 discriminator 16 view .LVU136
 450 0118 1830     		adds	r0, r0, #24
 451 011a 8342     		cmp	r3, r0
 452 011c 29D0     		beq	.L19
 177:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       {
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s 			page 23


 453              		.loc 1 177 10 discriminator 18 view .LVU137
 454 011e 1830     		adds	r0, r0, #24
 455 0120 8342     		cmp	r3, r0
 456 0122 26D0     		beq	.L19
 177:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       {
 457              		.loc 1 177 10 discriminator 20 view .LVU138
 458 0124 1830     		adds	r0, r0, #24
 459 0126 8342     		cmp	r3, r0
 460 0128 23D0     		beq	.L19
 177:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       {
 461              		.loc 1 177 10 discriminator 22 view .LVU139
 462 012a 1830     		adds	r0, r0, #24
 463 012c 8342     		cmp	r3, r0
 464 012e 20D0     		beq	.L19
 177:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       {
 465              		.loc 1 177 10 discriminator 24 view .LVU140
 466 0130 1830     		adds	r0, r0, #24
 467 0132 8342     		cmp	r3, r0
 468 0134 1DD0     		beq	.L19
 177:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       {
 469              		.loc 1 177 10 discriminator 26 view .LVU141
 470 0136 1830     		adds	r0, r0, #24
 471 0138 8342     		cmp	r3, r0
 472 013a 1AD0     		beq	.L19
 177:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       {
 473              		.loc 1 177 10 discriminator 28 view .LVU142
 474 013c 1830     		adds	r0, r0, #24
 475 013e 8342     		cmp	r3, r0
 476 0140 17D0     		beq	.L19
 177:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       {
 477              		.loc 1 177 10 discriminator 30 view .LVU143
 478 0142 3448     		ldr	r0, .L27+8
 479 0144 8342     		cmp	r3, r0
 480 0146 14D0     		beq	.L19
 177:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       {
 481              		.loc 1 177 10 discriminator 32 view .LVU144
 482 0148 1430     		adds	r0, r0, #20
 483 014a 8342     		cmp	r3, r0
 484 014c 11D0     		beq	.L19
 177:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       {
 485              		.loc 1 177 10 discriminator 34 view .LVU145
 486 014e 1430     		adds	r0, r0, #20
 487 0150 8342     		cmp	r3, r0
 488 0152 0ED0     		beq	.L19
 177:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       {
 489              		.loc 1 177 10 discriminator 36 view .LVU146
 490 0154 1430     		adds	r0, r0, #20
 491 0156 8342     		cmp	r3, r0
 492 0158 0BD0     		beq	.L19
 177:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       {
 493              		.loc 1 177 10 discriminator 38 view .LVU147
 494 015a 1430     		adds	r0, r0, #20
 495 015c 8342     		cmp	r3, r0
 496 015e 08D0     		beq	.L19
 177:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       {
 497              		.loc 1 177 10 discriminator 40 view .LVU148
 498 0160 1430     		adds	r0, r0, #20
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s 			page 24


 499 0162 8342     		cmp	r3, r0
 500 0164 05D0     		beq	.L19
 177:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       {
 501              		.loc 1 177 10 discriminator 42 view .LVU149
 502 0166 1430     		adds	r0, r0, #20
 503 0168 8342     		cmp	r3, r0
 504 016a 02D0     		beq	.L19
 177:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       {
 505              		.loc 1 177 10 discriminator 44 view .LVU150
 506 016c 1430     		adds	r0, r0, #20
 507 016e 8342     		cmp	r3, r0
 508 0170 0BD1     		bne	.L20
 509              	.L19:
 180:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 510              		.loc 1 180 9 is_stmt 1 view .LVU151
 511 0172 049B     		ldr	r3, [sp, #16]
 512 0174 2046     		mov	r0, r4
 513 0176 FFF7FEFF 		bl	DMA_MultiBufferSetConfig
 514              	.LVL28:
 183:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 515              		.loc 1 183 9 view .LVU152
 183:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 516              		.loc 1 183 13 is_stmt 0 view .LVU153
 517 017a 636E     		ldr	r3, [r4, #100]
 183:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 518              		.loc 1 183 46 view .LVU154
 519 017c A26E     		ldr	r2, [r4, #104]
 183:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 520              		.loc 1 183 40 view .LVU155
 521 017e 5A60     		str	r2, [r3, #4]
 185:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****         {
 522              		.loc 1 185 9 is_stmt 1 view .LVU156
 185:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****         {
 523              		.loc 1 185 16 is_stmt 0 view .LVU157
 524 0180 E36E     		ldr	r3, [r4, #108]
 185:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****         {
 525              		.loc 1 185 11 view .LVU158
 526 0182 13B1     		cbz	r3, .L20
 188:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****         }
 527              		.loc 1 188 11 is_stmt 1 view .LVU159
 188:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****         }
 528              		.loc 1 188 15 is_stmt 0 view .LVU160
 529 0184 236F     		ldr	r3, [r4, #112]
 188:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****         }
 530              		.loc 1 188 53 view .LVU161
 531 0186 626F     		ldr	r2, [r4, #116]
 188:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****         }
 532              		.loc 1 188 47 view .LVU162
 533 0188 5A60     		str	r2, [r3, #4]
 534              	.L20:
 193:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     }
 535              		.loc 1 193 7 is_stmt 1 view .LVU163
 536 018a 2368     		ldr	r3, [r4]
 537 018c 1F49     		ldr	r1, .L27
 538 018e 204A     		ldr	r2, .L27+4
 539 0190 9342     		cmp	r3, r2
 540 0192 18BF     		it	ne
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s 			page 25


 541 0194 8B42     		cmpne	r3, r1
 542 0196 30D0     		beq	.L21
 193:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     }
 543              		.loc 1 193 7 is_stmt 0 discriminator 2 view .LVU164
 544 0198 1832     		adds	r2, r2, #24
 545 019a 9342     		cmp	r3, r2
 546 019c 2DD0     		beq	.L21
 193:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     }
 547              		.loc 1 193 7 discriminator 4 view .LVU165
 548 019e 1832     		adds	r2, r2, #24
 549 01a0 9342     		cmp	r3, r2
 550 01a2 2AD0     		beq	.L21
 193:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     }
 551              		.loc 1 193 7 discriminator 6 view .LVU166
 552 01a4 1832     		adds	r2, r2, #24
 553 01a6 9342     		cmp	r3, r2
 554 01a8 27D0     		beq	.L21
 193:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     }
 555              		.loc 1 193 7 discriminator 8 view .LVU167
 556 01aa 1832     		adds	r2, r2, #24
 557 01ac 9342     		cmp	r3, r2
 558 01ae 24D0     		beq	.L21
 193:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     }
 559              		.loc 1 193 7 discriminator 10 view .LVU168
 560 01b0 1832     		adds	r2, r2, #24
 561 01b2 9342     		cmp	r3, r2
 562 01b4 21D0     		beq	.L21
 193:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     }
 563              		.loc 1 193 7 discriminator 12 view .LVU169
 564 01b6 1832     		adds	r2, r2, #24
 565 01b8 9342     		cmp	r3, r2
 566 01ba 1ED0     		beq	.L21
 193:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     }
 567              		.loc 1 193 7 discriminator 14 view .LVU170
 568 01bc 02F55672 		add	r2, r2, #856
 569 01c0 9342     		cmp	r3, r2
 570 01c2 1AD0     		beq	.L21
 193:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     }
 571              		.loc 1 193 7 discriminator 16 view .LVU171
 572 01c4 1832     		adds	r2, r2, #24
 573 01c6 9342     		cmp	r3, r2
 574 01c8 17D0     		beq	.L21
 193:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     }
 575              		.loc 1 193 7 discriminator 18 view .LVU172
 576 01ca 1832     		adds	r2, r2, #24
 577 01cc 9342     		cmp	r3, r2
 578 01ce 14D0     		beq	.L21
 193:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     }
 579              		.loc 1 193 7 discriminator 20 view .LVU173
 580 01d0 1832     		adds	r2, r2, #24
 581 01d2 9342     		cmp	r3, r2
 582 01d4 11D0     		beq	.L21
 193:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     }
 583              		.loc 1 193 7 discriminator 22 view .LVU174
 584 01d6 1832     		adds	r2, r2, #24
 585 01d8 9342     		cmp	r3, r2
 586 01da 0ED0     		beq	.L21
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s 			page 26


 193:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     }
 587              		.loc 1 193 7 discriminator 24 view .LVU175
 588 01dc 1832     		adds	r2, r2, #24
 589 01de 9342     		cmp	r3, r2
 590 01e0 0BD0     		beq	.L21
 193:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     }
 591              		.loc 1 193 7 discriminator 26 view .LVU176
 592 01e2 1832     		adds	r2, r2, #24
 593 01e4 9342     		cmp	r3, r2
 594 01e6 08D0     		beq	.L21
 193:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     }
 595              		.loc 1 193 7 discriminator 28 view .LVU177
 596 01e8 1832     		adds	r2, r2, #24
 597 01ea 9342     		cmp	r3, r2
 598 01ec 05D0     		beq	.L21
 193:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     }
 599              		.loc 1 193 7 discriminator 30 view .LVU178
 600 01ee 1A68     		ldr	r2, [r3]
 601 01f0 42F00102 		orr	r2, r2, #1
 602 01f4 1A60     		str	r2, [r3]
 122:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   __IO uint32_t *ifcRegister_Base; /* DMA Stream Interrupt Clear register */
 603              		.loc 1 122 21 view .LVU179
 604 01f6 0020     		movs	r0, #0
 605 01f8 18E7     		b	.L15
 606              	.L21:
 193:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     }
 607              		.loc 1 193 7 discriminator 29 view .LVU180
 608 01fa 1A68     		ldr	r2, [r3]
 609 01fc 42F00102 		orr	r2, r2, #1
 610 0200 1A60     		str	r2, [r3]
 122:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   __IO uint32_t *ifcRegister_Base; /* DMA Stream Interrupt Clear register */
 611              		.loc 1 122 21 view .LVU181
 612 0202 0020     		movs	r0, #0
 193:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     }
 613              		.loc 1 193 7 view .LVU182
 614 0204 12E7     		b	.L15
 615              	.LVL29:
 616              	.L23:
 138:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 617              		.loc 1 138 5 discriminator 1 view .LVU183
 618 0206 0220     		movs	r0, #2
 619 0208 10E7     		b	.L15
 620              	.L28:
 621 020a 00BF     		.align	2
 622              	.L27:
 623 020c 10000240 		.word	1073872912
 624 0210 28000240 		.word	1073872936
 625 0214 08540258 		.word	1476547592
 626              		.cfi_endproc
 627              	.LFE144:
 629              		.section	.text.HAL_DMAEx_MultiBufferStart_IT,"ax",%progbits
 630              		.align	1
 631              		.global	HAL_DMAEx_MultiBufferStart_IT
 632              		.syntax unified
 633              		.thumb
 634              		.thumb_func
 636              	HAL_DMAEx_MultiBufferStart_IT:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s 			page 27


 637              	.LVL30:
 638              	.LFB145:
 218:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   HAL_StatusTypeDef status = HAL_OK;
 639              		.loc 1 218 1 is_stmt 1 view -0
 640              		.cfi_startproc
 641              		@ args = 4, pretend = 0, frame = 0
 642              		@ frame_needed = 0, uses_anonymous_args = 0
 218:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   HAL_StatusTypeDef status = HAL_OK;
 643              		.loc 1 218 1 is_stmt 0 view .LVU185
 644 0000 70B5     		push	{r4, r5, r6, lr}
 645              	.LCFI4:
 646              		.cfi_def_cfa_offset 16
 647              		.cfi_offset 4, -16
 648              		.cfi_offset 5, -12
 649              		.cfi_offset 6, -8
 650              		.cfi_offset 14, -4
 651 0002 0446     		mov	r4, r0
 219:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   __IO uint32_t *ifcRegister_Base; /* DMA Stream Interrupt Clear register */
 652              		.loc 1 219 3 is_stmt 1 view .LVU186
 653              	.LVL31:
 220:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 654              		.loc 1 220 3 view .LVU187
 223:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   assert_param(IS_DMA_ALL_INSTANCE(hdma->Instance));
 655              		.loc 1 223 3 view .LVU188
 224:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 656              		.loc 1 224 3 view .LVU189
 227:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
 657              		.loc 1 227 3 view .LVU190
 227:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
 658              		.loc 1 227 16 is_stmt 0 view .LVU191
 659 0004 8068     		ldr	r0, [r0, #8]
 660              	.LVL32:
 227:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
 661              		.loc 1 227 5 view .LVU192
 662 0006 8028     		cmp	r0, #128
 663 0008 11D0     		beq	.L50
 234:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 664              		.loc 1 234 3 is_stmt 1 view .LVU193
 234:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 665              		.loc 1 234 3 view .LVU194
 666 000a 94F83400 		ldrb	r0, [r4, #52]	@ zero_extendqisi2
 667 000e 0128     		cmp	r0, #1
 668 0010 00F0B981 		beq	.L47
 234:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 669              		.loc 1 234 3 discriminator 2 view .LVU195
 670 0014 0120     		movs	r0, #1
 671 0016 84F83400 		strb	r0, [r4, #52]
 234:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 672              		.loc 1 234 3 discriminator 2 view .LVU196
 236:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
 673              		.loc 1 236 3 view .LVU197
 236:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
 674              		.loc 1 236 33 is_stmt 0 view .LVU198
 675 001a 94F83500 		ldrb	r0, [r4, #53]	@ zero_extendqisi2
 676 001e C0B2     		uxtb	r0, r0
 236:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
 677              		.loc 1 236 5 view .LVU199
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s 			page 28


 678 0020 0128     		cmp	r0, #1
 679 0022 09D0     		beq	.L51
 335:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 680              		.loc 1 335 5 is_stmt 1 view .LVU200
 335:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 681              		.loc 1 335 21 is_stmt 0 view .LVU201
 682 0024 4FF40063 		mov	r3, #2048
 683              	.LVL33:
 335:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 684              		.loc 1 335 21 view .LVU202
 685 0028 6365     		str	r3, [r4, #84]
 338:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   }
 686              		.loc 1 338 5 is_stmt 1 view .LVU203
 687              	.LVL34:
 338:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   }
 688              		.loc 1 338 12 is_stmt 0 view .LVU204
 689 002a 0120     		movs	r0, #1
 690              	.LVL35:
 691              	.L31:
 341:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 692              		.loc 1 341 1 view .LVU205
 693 002c 70BD     		pop	{r4, r5, r6, pc}
 694              	.LVL36:
 695              	.L50:
 229:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     return HAL_ERROR;
 696              		.loc 1 229 5 is_stmt 1 view .LVU206
 229:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     return HAL_ERROR;
 697              		.loc 1 229 21 is_stmt 0 view .LVU207
 698 002e 4FF48073 		mov	r3, #256
 699              	.LVL37:
 229:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     return HAL_ERROR;
 700              		.loc 1 229 21 view .LVU208
 701 0032 6365     		str	r3, [r4, #84]
 230:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   }
 702              		.loc 1 230 5 is_stmt 1 view .LVU209
 230:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   }
 703              		.loc 1 230 12 is_stmt 0 view .LVU210
 704 0034 0120     		movs	r0, #1
 705 0036 F9E7     		b	.L31
 706              	.LVL38:
 707              	.L51:
 239:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 708              		.loc 1 239 5 is_stmt 1 view .LVU211
 239:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 709              		.loc 1 239 17 is_stmt 0 view .LVU212
 710 0038 0220     		movs	r0, #2
 711 003a 84F83500 		strb	r0, [r4, #53]
 242:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 712              		.loc 1 242 5 is_stmt 1 view .LVU213
 242:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 713              		.loc 1 242 21 is_stmt 0 view .LVU214
 714 003e 0020     		movs	r0, #0
 715 0040 6065     		str	r0, [r4, #84]
 244:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 716              		.loc 1 244 5 is_stmt 1 view .LVU215
 244:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 717              		.loc 1 244 8 is_stmt 0 view .LVU216
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s 			page 29


 718 0042 2068     		ldr	r0, [r4]
 719 0044 724E     		ldr	r6, .L54
 720 0046 734D     		ldr	r5, .L54+4
 721 0048 A842     		cmp	r0, r5
 722 004a 18BF     		it	ne
 723 004c B042     		cmpne	r0, r6
 724 004e 39D0     		beq	.L33
 244:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 725              		.loc 1 244 8 discriminator 2 view .LVU217
 726 0050 1835     		adds	r5, r5, #24
 727 0052 A842     		cmp	r0, r5
 728 0054 36D0     		beq	.L33
 244:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 729              		.loc 1 244 8 discriminator 4 view .LVU218
 730 0056 1835     		adds	r5, r5, #24
 731 0058 A842     		cmp	r0, r5
 732 005a 33D0     		beq	.L33
 244:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 733              		.loc 1 244 8 discriminator 6 view .LVU219
 734 005c 1835     		adds	r5, r5, #24
 735 005e A842     		cmp	r0, r5
 736 0060 30D0     		beq	.L33
 244:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 737              		.loc 1 244 8 discriminator 8 view .LVU220
 738 0062 1835     		adds	r5, r5, #24
 739 0064 A842     		cmp	r0, r5
 740 0066 2DD0     		beq	.L33
 244:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 741              		.loc 1 244 8 discriminator 10 view .LVU221
 742 0068 1835     		adds	r5, r5, #24
 743 006a A842     		cmp	r0, r5
 744 006c 2AD0     		beq	.L33
 244:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 745              		.loc 1 244 8 discriminator 12 view .LVU222
 746 006e 1835     		adds	r5, r5, #24
 747 0070 A842     		cmp	r0, r5
 748 0072 27D0     		beq	.L33
 244:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 749              		.loc 1 244 8 discriminator 14 view .LVU223
 750 0074 05F55675 		add	r5, r5, #856
 751 0078 A842     		cmp	r0, r5
 752 007a 23D0     		beq	.L33
 244:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 753              		.loc 1 244 8 discriminator 16 view .LVU224
 754 007c 1835     		adds	r5, r5, #24
 755 007e A842     		cmp	r0, r5
 756 0080 20D0     		beq	.L33
 244:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 757              		.loc 1 244 8 discriminator 18 view .LVU225
 758 0082 1835     		adds	r5, r5, #24
 759 0084 A842     		cmp	r0, r5
 760 0086 1DD0     		beq	.L33
 244:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 761              		.loc 1 244 8 discriminator 20 view .LVU226
 762 0088 1835     		adds	r5, r5, #24
 763 008a A842     		cmp	r0, r5
 764 008c 1AD0     		beq	.L33
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s 			page 30


 244:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 765              		.loc 1 244 8 discriminator 22 view .LVU227
 766 008e 1835     		adds	r5, r5, #24
 767 0090 A842     		cmp	r0, r5
 768 0092 17D0     		beq	.L33
 244:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 769              		.loc 1 244 8 discriminator 24 view .LVU228
 770 0094 1835     		adds	r5, r5, #24
 771 0096 A842     		cmp	r0, r5
 772 0098 14D0     		beq	.L33
 244:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 773              		.loc 1 244 8 discriminator 26 view .LVU229
 774 009a 1835     		adds	r5, r5, #24
 775 009c A842     		cmp	r0, r5
 776 009e 11D0     		beq	.L33
 244:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 777              		.loc 1 244 8 discriminator 28 view .LVU230
 778 00a0 1835     		adds	r5, r5, #24
 779 00a2 A842     		cmp	r0, r5
 780 00a4 0ED0     		beq	.L33
 261:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 781              		.loc 1 261 7 is_stmt 1 view .LVU231
 261:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 782              		.loc 1 261 49 is_stmt 0 view .LVU232
 783 00a6 0668     		ldr	r6, [r0]
 261:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 784              		.loc 1 261 55 view .LVU233
 785 00a8 48F22005 		movw	r5, #32800
 786 00ac 3543     		orrs	r5, r5, r6
 787 00ae 0560     		str	r5, [r0]
 264:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 788              		.loc 1 264 7 is_stmt 1 view .LVU234
 264:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 789              		.loc 1 264 38 is_stmt 0 view .LVU235
 790 00b0 2068     		ldr	r0, [r4]
 264:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 791              		.loc 1 264 57 view .LVU236
 792 00b2 0361     		str	r3, [r0, #16]
 267:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 793              		.loc 1 267 7 is_stmt 1 view .LVU237
 267:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 794              		.loc 1 267 54 is_stmt 0 view .LVU238
 795 00b4 A56D     		ldr	r5, [r4, #88]
 796              	.LVL39:
 270:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     }
 797              		.loc 1 270 7 is_stmt 1 view .LVU239
 270:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     }
 798              		.loc 1 270 51 is_stmt 0 view .LVU240
 799 00b6 E36D     		ldr	r3, [r4, #92]
 800              	.LVL40:
 270:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     }
 801              		.loc 1 270 65 view .LVU241
 802 00b8 03F01F00 		and	r0, r3, #31
 803              	.LVL41:
 270:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     }
 804              		.loc 1 270 43 view .LVU242
 805 00bc 0123     		movs	r3, #1
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s 			page 31


 806 00be 8340     		lsls	r3, r3, r0
 270:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     }
 807              		.loc 1 270 25 view .LVU243
 808 00c0 6B60     		str	r3, [r5, #4]
 809 00c2 0CE0     		b	.L34
 810              	.LVL42:
 811              	.L33:
 247:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 812              		.loc 1 247 7 is_stmt 1 view .LVU244
 247:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 813              		.loc 1 247 47 is_stmt 0 view .LVU245
 814 00c4 0568     		ldr	r5, [r0]
 247:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 815              		.loc 1 247 52 view .LVU246
 816 00c6 45F48025 		orr	r5, r5, #262144
 817 00ca 0560     		str	r5, [r0]
 250:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 818              		.loc 1 250 7 is_stmt 1 view .LVU247
 250:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 819              		.loc 1 250 36 is_stmt 0 view .LVU248
 820 00cc 2068     		ldr	r0, [r4]
 250:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 821              		.loc 1 250 54 view .LVU249
 822 00ce 0361     		str	r3, [r0, #16]
 253:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 823              		.loc 1 253 7 is_stmt 1 view .LVU250
 253:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 824              		.loc 1 253 54 is_stmt 0 view .LVU251
 825 00d0 A56D     		ldr	r5, [r4, #88]
 826              	.LVL43:
 256:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     }
 827              		.loc 1 256 7 is_stmt 1 view .LVU252
 256:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     }
 828              		.loc 1 256 42 is_stmt 0 view .LVU253
 829 00d2 E36D     		ldr	r3, [r4, #92]
 830              	.LVL44:
 256:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     }
 831              		.loc 1 256 56 view .LVU254
 832 00d4 03F01F00 		and	r0, r3, #31
 833              	.LVL45:
 256:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     }
 834              		.loc 1 256 34 view .LVU255
 835 00d8 3F23     		movs	r3, #63
 836 00da 8340     		lsls	r3, r3, r0
 256:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     }
 837              		.loc 1 256 25 view .LVU256
 838 00dc AB60     		str	r3, [r5, #8]
 839              	.LVL46:
 840              	.L34:
 274:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 841              		.loc 1 274 5 is_stmt 1 view .LVU257
 842 00de 049B     		ldr	r3, [sp, #16]
 843 00e0 2046     		mov	r0, r4
 844 00e2 FFF7FEFF 		bl	DMA_MultiBufferSetConfig
 845              	.LVL47:
 276:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 846              		.loc 1 276 5 view .LVU258
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s 			page 32


 276:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 847              		.loc 1 276 8 is_stmt 0 view .LVU259
 848 00e6 2368     		ldr	r3, [r4]
 849 00e8 4949     		ldr	r1, .L54
 850 00ea 4A4A     		ldr	r2, .L54+4
 851 00ec 9342     		cmp	r3, r2
 852 00ee 18BF     		it	ne
 853 00f0 8B42     		cmpne	r3, r1
 854 00f2 42D0     		beq	.L35
 276:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 855              		.loc 1 276 8 discriminator 2 view .LVU260
 856 00f4 1832     		adds	r2, r2, #24
 857 00f6 9342     		cmp	r3, r2
 858 00f8 3FD0     		beq	.L35
 276:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 859              		.loc 1 276 8 discriminator 4 view .LVU261
 860 00fa 1832     		adds	r2, r2, #24
 861 00fc 9342     		cmp	r3, r2
 862 00fe 3CD0     		beq	.L35
 276:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 863              		.loc 1 276 8 discriminator 6 view .LVU262
 864 0100 1832     		adds	r2, r2, #24
 865 0102 9342     		cmp	r3, r2
 866 0104 39D0     		beq	.L35
 276:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 867              		.loc 1 276 8 discriminator 8 view .LVU263
 868 0106 1832     		adds	r2, r2, #24
 869 0108 9342     		cmp	r3, r2
 870 010a 36D0     		beq	.L35
 276:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 871              		.loc 1 276 8 discriminator 10 view .LVU264
 872 010c 1832     		adds	r2, r2, #24
 873 010e 9342     		cmp	r3, r2
 874 0110 33D0     		beq	.L35
 276:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 875              		.loc 1 276 8 discriminator 12 view .LVU265
 876 0112 1832     		adds	r2, r2, #24
 877 0114 9342     		cmp	r3, r2
 878 0116 30D0     		beq	.L35
 276:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 879              		.loc 1 276 8 discriminator 14 view .LVU266
 880 0118 02F55672 		add	r2, r2, #856
 881 011c 9342     		cmp	r3, r2
 882 011e 2CD0     		beq	.L35
 276:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 883              		.loc 1 276 8 discriminator 16 view .LVU267
 884 0120 1832     		adds	r2, r2, #24
 885 0122 9342     		cmp	r3, r2
 886 0124 29D0     		beq	.L35
 276:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 887              		.loc 1 276 8 discriminator 18 view .LVU268
 888 0126 1832     		adds	r2, r2, #24
 889 0128 9342     		cmp	r3, r2
 890 012a 26D0     		beq	.L35
 276:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 891              		.loc 1 276 8 discriminator 20 view .LVU269
 892 012c 1832     		adds	r2, r2, #24
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s 			page 33


 893 012e 9342     		cmp	r3, r2
 894 0130 23D0     		beq	.L35
 276:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 895              		.loc 1 276 8 discriminator 22 view .LVU270
 896 0132 1832     		adds	r2, r2, #24
 897 0134 9342     		cmp	r3, r2
 898 0136 20D0     		beq	.L35
 276:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 899              		.loc 1 276 8 discriminator 24 view .LVU271
 900 0138 1832     		adds	r2, r2, #24
 901 013a 9342     		cmp	r3, r2
 902 013c 1DD0     		beq	.L35
 276:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 903              		.loc 1 276 8 discriminator 26 view .LVU272
 904 013e 1832     		adds	r2, r2, #24
 905 0140 9342     		cmp	r3, r2
 906 0142 1AD0     		beq	.L35
 276:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 907              		.loc 1 276 8 discriminator 28 view .LVU273
 908 0144 1832     		adds	r2, r2, #24
 909 0146 9342     		cmp	r3, r2
 910 0148 17D0     		beq	.L35
 276:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 911              		.loc 1 276 8 discriminator 30 view .LVU274
 912 014a 334A     		ldr	r2, .L54+8
 913 014c 9342     		cmp	r3, r2
 914 014e 14D0     		beq	.L35
 276:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 915              		.loc 1 276 8 discriminator 32 view .LVU275
 916 0150 1432     		adds	r2, r2, #20
 917 0152 9342     		cmp	r3, r2
 918 0154 11D0     		beq	.L35
 276:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 919              		.loc 1 276 8 discriminator 34 view .LVU276
 920 0156 1432     		adds	r2, r2, #20
 921 0158 9342     		cmp	r3, r2
 922 015a 0ED0     		beq	.L35
 276:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 923              		.loc 1 276 8 discriminator 36 view .LVU277
 924 015c 1432     		adds	r2, r2, #20
 925 015e 9342     		cmp	r3, r2
 926 0160 0BD0     		beq	.L35
 276:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 927              		.loc 1 276 8 discriminator 38 view .LVU278
 928 0162 1432     		adds	r2, r2, #20
 929 0164 9342     		cmp	r3, r2
 930 0166 08D0     		beq	.L35
 276:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 931              		.loc 1 276 8 discriminator 40 view .LVU279
 932 0168 1432     		adds	r2, r2, #20
 933 016a 9342     		cmp	r3, r2
 934 016c 05D0     		beq	.L35
 276:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 935              		.loc 1 276 8 discriminator 42 view .LVU280
 936 016e 1432     		adds	r2, r2, #20
 937 0170 9342     		cmp	r3, r2
 938 0172 02D0     		beq	.L35
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s 			page 34


 276:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 939              		.loc 1 276 8 discriminator 44 view .LVU281
 940 0174 1432     		adds	r2, r2, #20
 941 0176 9342     		cmp	r3, r2
 942 0178 07D1     		bne	.L36
 943              	.L35:
 279:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 944              		.loc 1 279 7 is_stmt 1 view .LVU282
 279:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 945              		.loc 1 279 11 is_stmt 0 view .LVU283
 946 017a 636E     		ldr	r3, [r4, #100]
 279:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 947              		.loc 1 279 44 view .LVU284
 948 017c A26E     		ldr	r2, [r4, #104]
 279:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 949              		.loc 1 279 38 view .LVU285
 950 017e 5A60     		str	r2, [r3, #4]
 281:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       {
 951              		.loc 1 281 7 is_stmt 1 view .LVU286
 281:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       {
 952              		.loc 1 281 14 is_stmt 0 view .LVU287
 953 0180 E36E     		ldr	r3, [r4, #108]
 281:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       {
 954              		.loc 1 281 9 view .LVU288
 955 0182 13B1     		cbz	r3, .L36
 284:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       }
 956              		.loc 1 284 9 is_stmt 1 view .LVU289
 284:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       }
 957              		.loc 1 284 13 is_stmt 0 view .LVU290
 958 0184 236F     		ldr	r3, [r4, #112]
 284:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       }
 959              		.loc 1 284 51 view .LVU291
 960 0186 626F     		ldr	r2, [r4, #116]
 284:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       }
 961              		.loc 1 284 45 view .LVU292
 962 0188 5A60     		str	r2, [r3, #4]
 963              	.L36:
 288:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 964              		.loc 1 288 5 is_stmt 1 view .LVU293
 288:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 965              		.loc 1 288 8 is_stmt 0 view .LVU294
 966 018a 2368     		ldr	r3, [r4]
 967 018c 2049     		ldr	r1, .L54
 968 018e 214A     		ldr	r2, .L54+4
 969 0190 9342     		cmp	r3, r2
 970 0192 18BF     		it	ne
 971 0194 8B42     		cmpne	r3, r1
 972 0196 41D0     		beq	.L37
 288:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 973              		.loc 1 288 8 discriminator 2 view .LVU295
 974 0198 1832     		adds	r2, r2, #24
 975 019a 9342     		cmp	r3, r2
 976 019c 3ED0     		beq	.L37
 288:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 977              		.loc 1 288 8 discriminator 4 view .LVU296
 978 019e 1832     		adds	r2, r2, #24
 979 01a0 9342     		cmp	r3, r2
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s 			page 35


 980 01a2 3BD0     		beq	.L37
 288:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 981              		.loc 1 288 8 discriminator 6 view .LVU297
 982 01a4 1832     		adds	r2, r2, #24
 983 01a6 9342     		cmp	r3, r2
 984 01a8 38D0     		beq	.L37
 288:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 985              		.loc 1 288 8 discriminator 8 view .LVU298
 986 01aa 1832     		adds	r2, r2, #24
 987 01ac 9342     		cmp	r3, r2
 988 01ae 35D0     		beq	.L37
 288:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 989              		.loc 1 288 8 discriminator 10 view .LVU299
 990 01b0 1832     		adds	r2, r2, #24
 991 01b2 9342     		cmp	r3, r2
 992 01b4 32D0     		beq	.L37
 288:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 993              		.loc 1 288 8 discriminator 12 view .LVU300
 994 01b6 1832     		adds	r2, r2, #24
 995 01b8 9342     		cmp	r3, r2
 996 01ba 2FD0     		beq	.L37
 288:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 997              		.loc 1 288 8 discriminator 14 view .LVU301
 998 01bc 02F55672 		add	r2, r2, #856
 999 01c0 9342     		cmp	r3, r2
 1000 01c2 2BD0     		beq	.L37
 288:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 1001              		.loc 1 288 8 discriminator 16 view .LVU302
 1002 01c4 1832     		adds	r2, r2, #24
 1003 01c6 9342     		cmp	r3, r2
 1004 01c8 28D0     		beq	.L37
 288:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 1005              		.loc 1 288 8 discriminator 18 view .LVU303
 1006 01ca 1832     		adds	r2, r2, #24
 1007 01cc 9342     		cmp	r3, r2
 1008 01ce 25D0     		beq	.L37
 288:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 1009              		.loc 1 288 8 discriminator 20 view .LVU304
 1010 01d0 1832     		adds	r2, r2, #24
 1011 01d2 9342     		cmp	r3, r2
 1012 01d4 22D0     		beq	.L37
 288:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 1013              		.loc 1 288 8 discriminator 22 view .LVU305
 1014 01d6 1832     		adds	r2, r2, #24
 1015 01d8 9342     		cmp	r3, r2
 1016 01da 1FD0     		beq	.L37
 288:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 1017              		.loc 1 288 8 discriminator 24 view .LVU306
 1018 01dc 1832     		adds	r2, r2, #24
 1019 01de 9342     		cmp	r3, r2
 1020 01e0 1CD0     		beq	.L37
 288:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 1021              		.loc 1 288 8 discriminator 26 view .LVU307
 1022 01e2 1832     		adds	r2, r2, #24
 1023 01e4 9342     		cmp	r3, r2
 1024 01e6 19D0     		beq	.L37
 288:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s 			page 36


 1025              		.loc 1 288 8 discriminator 28 view .LVU308
 1026 01e8 1832     		adds	r2, r2, #24
 1027 01ea 9342     		cmp	r3, r2
 1028 01ec 16D0     		beq	.L37
 303:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1029              		.loc 1 303 7 is_stmt 1 view .LVU309
 1030 01ee 1A68     		ldr	r2, [r3]
 1031 01f0 22F00E02 		bic	r2, r2, #14
 1032 01f4 42F00A02 		orr	r2, r2, #10
 1033 01f8 1A60     		str	r2, [r3]
 305:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       {
 1034              		.loc 1 305 7 view .LVU310
 305:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       {
 1035              		.loc 1 305 15 is_stmt 0 view .LVU311
 1036 01fa 236C     		ldr	r3, [r4, #64]
 305:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       {
 1037              		.loc 1 305 9 view .LVU312
 1038 01fc 002B     		cmp	r3, #0
 1039 01fe 00F0B780 		beq	.L52
 1040              	.L38:
 308:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       }
 1041              		.loc 1 308 9 is_stmt 1 view .LVU313
 308:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       }
 1042              		.loc 1 308 40 is_stmt 0 view .LVU314
 1043 0202 2268     		ldr	r2, [r4]
 308:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       }
 1044              		.loc 1 308 51 view .LVU315
 1045 0204 1368     		ldr	r3, [r2]
 308:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       }
 1046              		.loc 1 308 58 view .LVU316
 1047 0206 43F00403 		orr	r3, r3, #4
 1048 020a 1360     		str	r3, [r2]
 1049 020c 1AE0     		b	.L41
 1050              	.L55:
 1051 020e 00BF     		.align	2
 1052              	.L54:
 1053 0210 10000240 		.word	1073872912
 1054 0214 28000240 		.word	1073872936
 1055 0218 08540258 		.word	1476547592
 1056              	.L37:
 291:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       ((DMA_Stream_TypeDef   *)hdma->Instance)->FCR |= DMA_IT_FE;
 1057              		.loc 1 291 7 is_stmt 1 view .LVU317
 1058 021c 1A68     		ldr	r2, [r3]
 1059 021e 22F01E02 		bic	r2, r2, #30
 1060 0222 42F01602 		orr	r2, r2, #22
 1061 0226 1A60     		str	r2, [r3]
 292:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1062              		.loc 1 292 7 view .LVU318
 292:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1063              		.loc 1 292 36 is_stmt 0 view .LVU319
 1064 0228 2268     		ldr	r2, [r4]
 292:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1065              		.loc 1 292 47 view .LVU320
 1066 022a 5369     		ldr	r3, [r2, #20]
 292:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1067              		.loc 1 292 53 view .LVU321
 1068 022c 43F08003 		orr	r3, r3, #128
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s 			page 37


 1069 0230 5361     		str	r3, [r2, #20]
 294:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       {
 1070              		.loc 1 294 7 is_stmt 1 view .LVU322
 294:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       {
 1071              		.loc 1 294 15 is_stmt 0 view .LVU323
 1072 0232 236C     		ldr	r3, [r4, #64]
 294:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       {
 1073              		.loc 1 294 9 view .LVU324
 1074 0234 002B     		cmp	r3, #0
 1075 0236 00F09680 		beq	.L53
 1076              	.L40:
 297:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       }
 1077              		.loc 1 297 9 is_stmt 1 view .LVU325
 297:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       }
 1078              		.loc 1 297 38 is_stmt 0 view .LVU326
 1079 023a 2268     		ldr	r2, [r4]
 297:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       }
 1080              		.loc 1 297 49 view .LVU327
 1081 023c 1368     		ldr	r3, [r2]
 297:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       }
 1082              		.loc 1 297 55 view .LVU328
 1083 023e 43F00803 		orr	r3, r3, #8
 1084 0242 1360     		str	r3, [r2]
 1085              	.L41:
 312:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 1086              		.loc 1 312 5 is_stmt 1 view .LVU329
 312:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 1087              		.loc 1 312 8 is_stmt 0 view .LVU330
 1088 0244 2368     		ldr	r3, [r4]
 1089 0246 5149     		ldr	r1, .L56
 1090 0248 514A     		ldr	r2, .L56+4
 1091 024a 9342     		cmp	r3, r2
 1092 024c 18BF     		it	ne
 1093 024e 8B42     		cmpne	r3, r1
 1094 0250 42D0     		beq	.L42
 312:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 1095              		.loc 1 312 8 discriminator 2 view .LVU331
 1096 0252 1832     		adds	r2, r2, #24
 1097 0254 9342     		cmp	r3, r2
 1098 0256 3FD0     		beq	.L42
 312:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 1099              		.loc 1 312 8 discriminator 4 view .LVU332
 1100 0258 1832     		adds	r2, r2, #24
 1101 025a 9342     		cmp	r3, r2
 1102 025c 3CD0     		beq	.L42
 312:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 1103              		.loc 1 312 8 discriminator 6 view .LVU333
 1104 025e 1832     		adds	r2, r2, #24
 1105 0260 9342     		cmp	r3, r2
 1106 0262 39D0     		beq	.L42
 312:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 1107              		.loc 1 312 8 discriminator 8 view .LVU334
 1108 0264 1832     		adds	r2, r2, #24
 1109 0266 9342     		cmp	r3, r2
 1110 0268 36D0     		beq	.L42
 312:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 1111              		.loc 1 312 8 discriminator 10 view .LVU335
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s 			page 38


 1112 026a 1832     		adds	r2, r2, #24
 1113 026c 9342     		cmp	r3, r2
 1114 026e 33D0     		beq	.L42
 312:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 1115              		.loc 1 312 8 discriminator 12 view .LVU336
 1116 0270 1832     		adds	r2, r2, #24
 1117 0272 9342     		cmp	r3, r2
 1118 0274 30D0     		beq	.L42
 312:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 1119              		.loc 1 312 8 discriminator 14 view .LVU337
 1120 0276 02F55672 		add	r2, r2, #856
 1121 027a 9342     		cmp	r3, r2
 1122 027c 2CD0     		beq	.L42
 312:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 1123              		.loc 1 312 8 discriminator 16 view .LVU338
 1124 027e 1832     		adds	r2, r2, #24
 1125 0280 9342     		cmp	r3, r2
 1126 0282 29D0     		beq	.L42
 312:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 1127              		.loc 1 312 8 discriminator 18 view .LVU339
 1128 0284 1832     		adds	r2, r2, #24
 1129 0286 9342     		cmp	r3, r2
 1130 0288 26D0     		beq	.L42
 312:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 1131              		.loc 1 312 8 discriminator 20 view .LVU340
 1132 028a 1832     		adds	r2, r2, #24
 1133 028c 9342     		cmp	r3, r2
 1134 028e 23D0     		beq	.L42
 312:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 1135              		.loc 1 312 8 discriminator 22 view .LVU341
 1136 0290 1832     		adds	r2, r2, #24
 1137 0292 9342     		cmp	r3, r2
 1138 0294 20D0     		beq	.L42
 312:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 1139              		.loc 1 312 8 discriminator 24 view .LVU342
 1140 0296 1832     		adds	r2, r2, #24
 1141 0298 9342     		cmp	r3, r2
 1142 029a 1DD0     		beq	.L42
 312:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 1143              		.loc 1 312 8 discriminator 26 view .LVU343
 1144 029c 1832     		adds	r2, r2, #24
 1145 029e 9342     		cmp	r3, r2
 1146 02a0 1AD0     		beq	.L42
 312:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 1147              		.loc 1 312 8 discriminator 28 view .LVU344
 1148 02a2 1832     		adds	r2, r2, #24
 1149 02a4 9342     		cmp	r3, r2
 1150 02a6 17D0     		beq	.L42
 312:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 1151              		.loc 1 312 8 discriminator 30 view .LVU345
 1152 02a8 3A4A     		ldr	r2, .L56+8
 1153 02aa 9342     		cmp	r3, r2
 1154 02ac 14D0     		beq	.L42
 312:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 1155              		.loc 1 312 8 discriminator 32 view .LVU346
 1156 02ae 1432     		adds	r2, r2, #20
 1157 02b0 9342     		cmp	r3, r2
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s 			page 39


 1158 02b2 11D0     		beq	.L42
 312:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 1159              		.loc 1 312 8 discriminator 34 view .LVU347
 1160 02b4 1432     		adds	r2, r2, #20
 1161 02b6 9342     		cmp	r3, r2
 1162 02b8 0ED0     		beq	.L42
 312:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 1163              		.loc 1 312 8 discriminator 36 view .LVU348
 1164 02ba 1432     		adds	r2, r2, #20
 1165 02bc 9342     		cmp	r3, r2
 1166 02be 0BD0     		beq	.L42
 312:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 1167              		.loc 1 312 8 discriminator 38 view .LVU349
 1168 02c0 1432     		adds	r2, r2, #20
 1169 02c2 9342     		cmp	r3, r2
 1170 02c4 08D0     		beq	.L42
 312:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 1171              		.loc 1 312 8 discriminator 40 view .LVU350
 1172 02c6 1432     		adds	r2, r2, #20
 1173 02c8 9342     		cmp	r3, r2
 1174 02ca 05D0     		beq	.L42
 312:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 1175              		.loc 1 312 8 discriminator 42 view .LVU351
 1176 02cc 1432     		adds	r2, r2, #20
 1177 02ce 9342     		cmp	r3, r2
 1178 02d0 02D0     		beq	.L42
 312:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 1179              		.loc 1 312 8 discriminator 44 view .LVU352
 1180 02d2 1432     		adds	r2, r2, #20
 1181 02d4 9342     		cmp	r3, r2
 1182 02d6 0ED1     		bne	.L43
 1183              	.L42:
 315:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       {
 1184              		.loc 1 315 7 is_stmt 1 view .LVU353
 315:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       {
 1185              		.loc 1 315 15 is_stmt 0 view .LVU354
 1186 02d8 236E     		ldr	r3, [r4, #96]
 315:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       {
 1187              		.loc 1 315 30 view .LVU355
 1188 02da 1A68     		ldr	r2, [r3]
 315:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       {
 1189              		.loc 1 315 9 view .LVU356
 1190 02dc 12F4803F 		tst	r2, #65536
 1191 02e0 03D0     		beq	.L44
 318:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       }
 1192              		.loc 1 318 9 is_stmt 1 view .LVU357
 318:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       }
 1193              		.loc 1 318 28 is_stmt 0 view .LVU358
 1194 02e2 1A68     		ldr	r2, [r3]
 318:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       }
 1195              		.loc 1 318 34 view .LVU359
 1196 02e4 42F48072 		orr	r2, r2, #256
 1197 02e8 1A60     		str	r2, [r3]
 1198              	.L44:
 321:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       {
 1199              		.loc 1 321 7 is_stmt 1 view .LVU360
 321:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       {
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s 			page 40


 1200              		.loc 1 321 14 is_stmt 0 view .LVU361
 1201 02ea E36E     		ldr	r3, [r4, #108]
 321:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       {
 1202              		.loc 1 321 9 view .LVU362
 1203 02ec 1BB1     		cbz	r3, .L43
 325:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       }
 1204              		.loc 1 325 9 is_stmt 1 view .LVU363
 325:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       }
 1205              		.loc 1 325 31 is_stmt 0 view .LVU364
 1206 02ee 1A68     		ldr	r2, [r3]
 325:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       }
 1207              		.loc 1 325 38 view .LVU365
 1208 02f0 42F48072 		orr	r2, r2, #256
 1209 02f4 1A60     		str	r2, [r3]
 1210              	.L43:
 330:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   }
 1211              		.loc 1 330 5 is_stmt 1 view .LVU366
 1212 02f6 2368     		ldr	r3, [r4]
 1213 02f8 2449     		ldr	r1, .L56
 1214 02fa 254A     		ldr	r2, .L56+4
 1215 02fc 9342     		cmp	r3, r2
 1216 02fe 18BF     		it	ne
 1217 0300 8B42     		cmpne	r3, r1
 1218 0302 3AD0     		beq	.L45
 330:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   }
 1219              		.loc 1 330 5 is_stmt 0 discriminator 2 view .LVU367
 1220 0304 1832     		adds	r2, r2, #24
 1221 0306 9342     		cmp	r3, r2
 1222 0308 37D0     		beq	.L45
 330:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   }
 1223              		.loc 1 330 5 discriminator 4 view .LVU368
 1224 030a 1832     		adds	r2, r2, #24
 1225 030c 9342     		cmp	r3, r2
 1226 030e 34D0     		beq	.L45
 330:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   }
 1227              		.loc 1 330 5 discriminator 6 view .LVU369
 1228 0310 1832     		adds	r2, r2, #24
 1229 0312 9342     		cmp	r3, r2
 1230 0314 31D0     		beq	.L45
 330:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   }
 1231              		.loc 1 330 5 discriminator 8 view .LVU370
 1232 0316 1832     		adds	r2, r2, #24
 1233 0318 9342     		cmp	r3, r2
 1234 031a 2ED0     		beq	.L45
 330:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   }
 1235              		.loc 1 330 5 discriminator 10 view .LVU371
 1236 031c 1832     		adds	r2, r2, #24
 1237 031e 9342     		cmp	r3, r2
 1238 0320 2BD0     		beq	.L45
 330:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   }
 1239              		.loc 1 330 5 discriminator 12 view .LVU372
 1240 0322 1832     		adds	r2, r2, #24
 1241 0324 9342     		cmp	r3, r2
 1242 0326 28D0     		beq	.L45
 330:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   }
 1243              		.loc 1 330 5 discriminator 14 view .LVU373
 1244 0328 02F55672 		add	r2, r2, #856
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s 			page 41


 1245 032c 9342     		cmp	r3, r2
 1246 032e 24D0     		beq	.L45
 330:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   }
 1247              		.loc 1 330 5 discriminator 16 view .LVU374
 1248 0330 1832     		adds	r2, r2, #24
 1249 0332 9342     		cmp	r3, r2
 1250 0334 21D0     		beq	.L45
 330:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   }
 1251              		.loc 1 330 5 discriminator 18 view .LVU375
 1252 0336 1832     		adds	r2, r2, #24
 1253 0338 9342     		cmp	r3, r2
 1254 033a 1ED0     		beq	.L45
 330:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   }
 1255              		.loc 1 330 5 discriminator 20 view .LVU376
 1256 033c 1832     		adds	r2, r2, #24
 1257 033e 9342     		cmp	r3, r2
 1258 0340 1BD0     		beq	.L45
 330:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   }
 1259              		.loc 1 330 5 discriminator 22 view .LVU377
 1260 0342 1832     		adds	r2, r2, #24
 1261 0344 9342     		cmp	r3, r2
 1262 0346 18D0     		beq	.L45
 330:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   }
 1263              		.loc 1 330 5 discriminator 24 view .LVU378
 1264 0348 1832     		adds	r2, r2, #24
 1265 034a 9342     		cmp	r3, r2
 1266 034c 15D0     		beq	.L45
 330:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   }
 1267              		.loc 1 330 5 discriminator 26 view .LVU379
 1268 034e 1832     		adds	r2, r2, #24
 1269 0350 9342     		cmp	r3, r2
 1270 0352 12D0     		beq	.L45
 330:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   }
 1271              		.loc 1 330 5 discriminator 28 view .LVU380
 1272 0354 1832     		adds	r2, r2, #24
 1273 0356 9342     		cmp	r3, r2
 1274 0358 0FD0     		beq	.L45
 330:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   }
 1275              		.loc 1 330 5 discriminator 30 view .LVU381
 1276 035a 1A68     		ldr	r2, [r3]
 1277 035c 42F00102 		orr	r2, r2, #1
 1278 0360 1A60     		str	r2, [r3]
 219:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   __IO uint32_t *ifcRegister_Base; /* DMA Stream Interrupt Clear register */
 1279              		.loc 1 219 21 view .LVU382
 1280 0362 0020     		movs	r0, #0
 1281 0364 62E6     		b	.L31
 1282              	.L53:
 294:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       {
 1283              		.loc 1 294 55 discriminator 1 view .LVU383
 1284 0366 A36C     		ldr	r3, [r4, #72]
 294:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       {
 1285              		.loc 1 294 47 discriminator 1 view .LVU384
 1286 0368 002B     		cmp	r3, #0
 1287 036a 7FF466AF 		bne	.L40
 1288 036e 69E7     		b	.L41
 1289              	.L52:
 305:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       {
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s 			page 42


 1290              		.loc 1 305 55 discriminator 1 view .LVU385
 1291 0370 A36C     		ldr	r3, [r4, #72]
 305:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       {
 1292              		.loc 1 305 47 discriminator 1 view .LVU386
 1293 0372 002B     		cmp	r3, #0
 1294 0374 7FF445AF 		bne	.L38
 1295 0378 64E7     		b	.L41
 1296              	.L45:
 330:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   }
 1297              		.loc 1 330 5 discriminator 29 view .LVU387
 1298 037a 1A68     		ldr	r2, [r3]
 1299 037c 42F00102 		orr	r2, r2, #1
 1300 0380 1A60     		str	r2, [r3]
 219:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   __IO uint32_t *ifcRegister_Base; /* DMA Stream Interrupt Clear register */
 1301              		.loc 1 219 21 view .LVU388
 1302 0382 0020     		movs	r0, #0
 330:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   }
 1303              		.loc 1 330 5 view .LVU389
 1304 0384 52E6     		b	.L31
 1305              	.LVL48:
 1306              	.L47:
 234:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1307              		.loc 1 234 3 discriminator 1 view .LVU390
 1308 0386 0220     		movs	r0, #2
 1309 0388 50E6     		b	.L31
 1310              	.L57:
 1311 038a 00BF     		.align	2
 1312              	.L56:
 1313 038c 10000240 		.word	1073872912
 1314 0390 28000240 		.word	1073872936
 1315 0394 08540258 		.word	1476547592
 1316              		.cfi_endproc
 1317              	.LFE145:
 1319              		.section	.text.HAL_DMAEx_ChangeMemory,"ax",%progbits
 1320              		.align	1
 1321              		.global	HAL_DMAEx_ChangeMemory
 1322              		.syntax unified
 1323              		.thumb
 1324              		.thumb_func
 1326              	HAL_DMAEx_ChangeMemory:
 1327              	.LVL49:
 1328              	.LFB146:
 358:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   if(IS_DMA_STREAM_INSTANCE(hdma->Instance) != 0U) /* DMA1 or DMA2 instance */
 1329              		.loc 1 358 1 is_stmt 1 view -0
 1330              		.cfi_startproc
 1331              		@ args = 0, pretend = 0, frame = 0
 1332              		@ frame_needed = 0, uses_anonymous_args = 0
 1333              		@ link register save eliminated.
 358:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   if(IS_DMA_STREAM_INSTANCE(hdma->Instance) != 0U) /* DMA1 or DMA2 instance */
 1334              		.loc 1 358 1 is_stmt 0 view .LVU392
 1335 0000 10B4     		push	{r4}
 1336              	.LCFI5:
 1337              		.cfi_def_cfa_offset 4
 1338              		.cfi_offset 4, -4
 359:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
 1339              		.loc 1 359 3 is_stmt 1 view .LVU393
 359:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s 			page 43


 1340              		.loc 1 359 6 is_stmt 0 view .LVU394
 1341 0002 0368     		ldr	r3, [r0]
 1342 0004 1E4C     		ldr	r4, .L66
 1343 0006 1F48     		ldr	r0, .L66+4
 1344              	.LVL50:
 359:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
 1345              		.loc 1 359 6 view .LVU395
 1346 0008 8342     		cmp	r3, r0
 1347 000a 18BF     		it	ne
 1348 000c A342     		cmpne	r3, r4
 1349 000e 2DD0     		beq	.L59
 359:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
 1350              		.loc 1 359 6 discriminator 2 view .LVU396
 1351 0010 1830     		adds	r0, r0, #24
 1352 0012 8342     		cmp	r3, r0
 1353 0014 2AD0     		beq	.L59
 359:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
 1354              		.loc 1 359 6 discriminator 4 view .LVU397
 1355 0016 1830     		adds	r0, r0, #24
 1356 0018 8342     		cmp	r3, r0
 1357 001a 27D0     		beq	.L59
 359:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
 1358              		.loc 1 359 6 discriminator 6 view .LVU398
 1359 001c 1830     		adds	r0, r0, #24
 1360 001e 8342     		cmp	r3, r0
 1361 0020 24D0     		beq	.L59
 359:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
 1362              		.loc 1 359 6 discriminator 8 view .LVU399
 1363 0022 1830     		adds	r0, r0, #24
 1364 0024 8342     		cmp	r3, r0
 1365 0026 21D0     		beq	.L59
 359:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
 1366              		.loc 1 359 6 discriminator 10 view .LVU400
 1367 0028 1830     		adds	r0, r0, #24
 1368 002a 8342     		cmp	r3, r0
 1369 002c 1ED0     		beq	.L59
 359:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
 1370              		.loc 1 359 6 discriminator 12 view .LVU401
 1371 002e 1830     		adds	r0, r0, #24
 1372 0030 8342     		cmp	r3, r0
 1373 0032 1BD0     		beq	.L59
 359:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
 1374              		.loc 1 359 6 discriminator 14 view .LVU402
 1375 0034 00F55670 		add	r0, r0, #856
 1376 0038 8342     		cmp	r3, r0
 1377 003a 17D0     		beq	.L59
 359:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
 1378              		.loc 1 359 6 discriminator 16 view .LVU403
 1379 003c 1830     		adds	r0, r0, #24
 1380 003e 8342     		cmp	r3, r0
 1381 0040 14D0     		beq	.L59
 359:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
 1382              		.loc 1 359 6 discriminator 18 view .LVU404
 1383 0042 1830     		adds	r0, r0, #24
 1384 0044 8342     		cmp	r3, r0
 1385 0046 11D0     		beq	.L59
 359:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s 			page 44


 1386              		.loc 1 359 6 discriminator 20 view .LVU405
 1387 0048 1830     		adds	r0, r0, #24
 1388 004a 8342     		cmp	r3, r0
 1389 004c 0ED0     		beq	.L59
 359:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
 1390              		.loc 1 359 6 discriminator 22 view .LVU406
 1391 004e 1830     		adds	r0, r0, #24
 1392 0050 8342     		cmp	r3, r0
 1393 0052 0BD0     		beq	.L59
 359:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
 1394              		.loc 1 359 6 discriminator 24 view .LVU407
 1395 0054 1830     		adds	r0, r0, #24
 1396 0056 8342     		cmp	r3, r0
 1397 0058 08D0     		beq	.L59
 359:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
 1398              		.loc 1 359 6 discriminator 26 view .LVU408
 1399 005a 1830     		adds	r0, r0, #24
 1400 005c 8342     		cmp	r3, r0
 1401 005e 05D0     		beq	.L59
 359:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
 1402              		.loc 1 359 6 discriminator 28 view .LVU409
 1403 0060 1830     		adds	r0, r0, #24
 1404 0062 8342     		cmp	r3, r0
 1405 0064 02D0     		beq	.L59
 374:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 1406              		.loc 1 374 5 is_stmt 1 view .LVU410
 374:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 1407              		.loc 1 374 7 is_stmt 0 view .LVU411
 1408 0066 4AB1     		cbz	r2, .L60
 382:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     }
 1409              		.loc 1 382 7 is_stmt 1 view .LVU412
 382:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     }
 1410              		.loc 1 382 57 is_stmt 0 view .LVU413
 1411 0068 1961     		str	r1, [r3, #16]
 1412 006a 01E0     		b	.L63
 1413              	.L59:
 361:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 1414              		.loc 1 361 5 is_stmt 1 view .LVU414
 361:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 1415              		.loc 1 361 7 is_stmt 0 view .LVU415
 1416 006c 22B9     		cbnz	r2, .L62
 364:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     }
 1417              		.loc 1 364 7 is_stmt 1 view .LVU416
 364:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     }
 1418              		.loc 1 364 54 is_stmt 0 view .LVU417
 1419 006e D960     		str	r1, [r3, #12]
 1420              	.L63:
 386:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** }
 1421              		.loc 1 386 3 is_stmt 1 view .LVU418
 387:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1422              		.loc 1 387 1 is_stmt 0 view .LVU419
 1423 0070 0020     		movs	r0, #0
 1424 0072 5DF8044B 		ldr	r4, [sp], #4
 1425              	.LCFI6:
 1426              		.cfi_remember_state
 1427              		.cfi_restore 4
 1428              		.cfi_def_cfa_offset 0
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s 			page 45


 1429 0076 7047     		bx	lr
 1430              	.L62:
 1431              	.LCFI7:
 1432              		.cfi_restore_state
 369:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     }
 1433              		.loc 1 369 7 is_stmt 1 view .LVU420
 369:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     }
 1434              		.loc 1 369 54 is_stmt 0 view .LVU421
 1435 0078 1961     		str	r1, [r3, #16]
 1436 007a F9E7     		b	.L63
 1437              	.L60:
 377:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     }
 1438              		.loc 1 377 7 is_stmt 1 view .LVU422
 377:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     }
 1439              		.loc 1 377 57 is_stmt 0 view .LVU423
 1440 007c D960     		str	r1, [r3, #12]
 1441 007e F7E7     		b	.L63
 1442              	.L67:
 1443              		.align	2
 1444              	.L66:
 1445 0080 10000240 		.word	1073872912
 1446 0084 28000240 		.word	1073872936
 1447              		.cfi_endproc
 1448              	.LFE146:
 1450              		.section	.text.HAL_DMAEx_ConfigMuxSync,"ax",%progbits
 1451              		.align	1
 1452              		.global	HAL_DMAEx_ConfigMuxSync
 1453              		.syntax unified
 1454              		.thumb
 1455              		.thumb_func
 1457              	HAL_DMAEx_ConfigMuxSync:
 1458              	.LVL51:
 1459              	.LFB147:
 397:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   uint32_t syncSignalID = 0;
 1460              		.loc 1 397 1 is_stmt 1 view -0
 1461              		.cfi_startproc
 1462              		@ args = 0, pretend = 0, frame = 0
 1463              		@ frame_needed = 0, uses_anonymous_args = 0
 1464              		@ link register save eliminated.
 397:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   uint32_t syncSignalID = 0;
 1465              		.loc 1 397 1 is_stmt 0 view .LVU425
 1466 0000 70B4     		push	{r4, r5, r6}
 1467              	.LCFI8:
 1468              		.cfi_def_cfa_offset 12
 1469              		.cfi_offset 4, -12
 1470              		.cfi_offset 5, -8
 1471              		.cfi_offset 6, -4
 398:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   uint32_t syncPolarity = 0;
 1472              		.loc 1 398 3 is_stmt 1 view .LVU426
 1473              	.LVL52:
 399:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1474              		.loc 1 399 3 view .LVU427
 402:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   assert_param(IS_DMAMUX_SYNC_STATE(pSyncConfig->SyncEnable));
 1475              		.loc 1 402 3 view .LVU428
 403:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   assert_param(IS_DMAMUX_SYNC_EVENT(pSyncConfig->EventEnable));
 1476              		.loc 1 403 3 view .LVU429
 404:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   assert_param(IS_DMAMUX_SYNC_REQUEST_NUMBER(pSyncConfig->RequestNumber));
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s 			page 46


 1477              		.loc 1 404 3 view .LVU430
 405:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1478              		.loc 1 405 3 view .LVU431
 407:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
 1479              		.loc 1 407 3 view .LVU432
 407:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
 1480              		.loc 1 407 17 is_stmt 0 view .LVU433
 1481 0002 0B7A     		ldrb	r3, [r1, #8]	@ zero_extendqisi2
 407:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
 1482              		.loc 1 407 5 view .LVU434
 1483 0004 012B     		cmp	r3, #1
 1484 0006 28D0     		beq	.L75
 399:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1485              		.loc 1 399 12 view .LVU435
 1486 0008 0024     		movs	r4, #0
 398:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   uint32_t syncPolarity = 0;
 1487              		.loc 1 398 12 view .LVU436
 1488 000a 2546     		mov	r5, r4
 1489              	.LVL53:
 1490              	.L69:
 424:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
 1491              		.loc 1 424 3 is_stmt 1 view .LVU437
 424:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
 1492              		.loc 1 424 10 is_stmt 0 view .LVU438
 1493 000c 90F83530 		ldrb	r3, [r0, #53]	@ zero_extendqisi2
 1494 0010 DBB2     		uxtb	r3, r3
 424:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
 1495              		.loc 1 424 5 view .LVU439
 1496 0012 012B     		cmp	r3, #1
 1497 0014 24D1     		bne	.L70
 427:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1498              		.loc 1 427 5 is_stmt 1 view .LVU440
 427:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1499              		.loc 1 427 5 view .LVU441
 1500 0016 90F83430 		ldrb	r3, [r0, #52]	@ zero_extendqisi2
 1501 001a 012B     		cmp	r3, #1
 1502 001c 26D0     		beq	.L73
 427:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1503              		.loc 1 427 5 discriminator 2 view .LVU442
 1504 001e 0123     		movs	r3, #1
 1505 0020 80F83430 		strb	r3, [r0, #52]
 427:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1506              		.loc 1 427 5 discriminator 2 view .LVU443
 430:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1507              		.loc 1 430 5 view .LVU444
 1508 0024 026E     		ldr	r2, [r0, #96]
 1509 0026 1368     		ldr	r3, [r2]
 1510 0028 23F48133 		bic	r3, r3, #66048
 1511 002c 1360     		str	r3, [r2]
 433:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****                (~DMAMUX_CxCR_DMAREQ_ID) , \
 1512              		.loc 1 433 5 view .LVU445
 1513 002e 066E     		ldr	r6, [r0, #96]
 1514 0030 3268     		ldr	r2, [r6]
 1515 0032 D2B2     		uxtb	r2, r2
 1516 0034 CB68     		ldr	r3, [r1, #12]
 1517 0036 013B     		subs	r3, r3, #1
 1518 0038 DB04     		lsls	r3, r3, #19
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s 			page 47


 1519 003a 43EA0563 		orr	r3, r3, r5, lsl #24
 1520 003e 2343     		orrs	r3, r3, r4
 1521 0040 0C7A     		ldrb	r4, [r1, #8]	@ zero_extendqisi2
 1522              	.LVL54:
 433:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****                (~DMAMUX_CxCR_DMAREQ_ID) , \
 1523              		.loc 1 433 5 is_stmt 0 view .LVU446
 1524 0042 43EA0443 		orr	r3, r3, r4, lsl #16
 1525 0046 497A     		ldrb	r1, [r1, #9]	@ zero_extendqisi2
 1526              	.LVL55:
 433:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****                (~DMAMUX_CxCR_DMAREQ_ID) , \
 1527              		.loc 1 433 5 view .LVU447
 1528 0048 43EA4123 		orr	r3, r3, r1, lsl #9
 1529 004c 1343     		orrs	r3, r3, r2
 1530 004e 3360     		str	r3, [r6]
 441:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1531              		.loc 1 441 5 is_stmt 1 view .LVU448
 441:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1532              		.loc 1 441 5 view .LVU449
 1533 0050 0023     		movs	r3, #0
 1534 0052 80F83430 		strb	r3, [r0, #52]
 441:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1535              		.loc 1 441 5 view .LVU450
 443:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   }
 1536              		.loc 1 443 5 view .LVU451
 443:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   }
 1537              		.loc 1 443 12 is_stmt 0 view .LVU452
 1538 0056 1846     		mov	r0, r3
 1539              	.LVL56:
 443:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   }
 1540              		.loc 1 443 12 view .LVU453
 1541 0058 06E0     		b	.L71
 1542              	.LVL57:
 1543              	.L75:
 409:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1544              		.loc 1 409 5 is_stmt 1 view .LVU454
 411:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 1545              		.loc 1 411 5 view .LVU455
 413:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     }
 1546              		.loc 1 413 7 view .LVU456
 417:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     }
 1547              		.loc 1 417 7 view .LVU457
 419:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     syncPolarity = pSyncConfig->SyncPolarity;
 1548              		.loc 1 419 5 view .LVU458
 419:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     syncPolarity = pSyncConfig->SyncPolarity;
 1549              		.loc 1 419 18 is_stmt 0 view .LVU459
 1550 005a 0D68     		ldr	r5, [r1]
 1551              	.LVL58:
 420:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   }
 1552              		.loc 1 420 5 is_stmt 1 view .LVU460
 420:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   }
 1553              		.loc 1 420 18 is_stmt 0 view .LVU461
 1554 005c 4C68     		ldr	r4, [r1, #4]
 1555              	.LVL59:
 420:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   }
 1556              		.loc 1 420 18 view .LVU462
 1557 005e D5E7     		b	.L69
 1558              	.L70:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s 			page 48


 448:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1559              		.loc 1 448 5 is_stmt 1 view .LVU463
 448:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1560              		.loc 1 448 21 is_stmt 0 view .LVU464
 1561 0060 4FF40063 		mov	r3, #2048
 1562 0064 4365     		str	r3, [r0, #84]
 451:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   }
 1563              		.loc 1 451 5 is_stmt 1 view .LVU465
 451:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   }
 1564              		.loc 1 451 12 is_stmt 0 view .LVU466
 1565 0066 0120     		movs	r0, #1
 1566              	.LVL60:
 1567              	.L71:
 453:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1568              		.loc 1 453 1 view .LVU467
 1569 0068 70BC     		pop	{r4, r5, r6}
 1570              	.LCFI9:
 1571              		.cfi_remember_state
 1572              		.cfi_restore 6
 1573              		.cfi_restore 5
 1574              		.cfi_restore 4
 1575              		.cfi_def_cfa_offset 0
 1576              	.LVL61:
 453:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1577              		.loc 1 453 1 view .LVU468
 1578 006a 7047     		bx	lr
 1579              	.LVL62:
 1580              	.L73:
 1581              	.LCFI10:
 1582              		.cfi_restore_state
 427:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1583              		.loc 1 427 5 discriminator 1 view .LVU469
 1584 006c 0220     		movs	r0, #2
 1585              	.LVL63:
 427:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1586              		.loc 1 427 5 discriminator 1 view .LVU470
 1587 006e FBE7     		b	.L71
 1588              		.cfi_endproc
 1589              	.LFE147:
 1591              		.section	.text.HAL_DMAEx_ConfigMuxRequestGenerator,"ax",%progbits
 1592              		.align	1
 1593              		.global	HAL_DMAEx_ConfigMuxRequestGenerator
 1594              		.syntax unified
 1595              		.thumb
 1596              		.thumb_func
 1598              	HAL_DMAEx_ConfigMuxRequestGenerator:
 1599              	.LVL64:
 1600              	.LFB148:
 465:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   HAL_StatusTypeDef status;
 1601              		.loc 1 465 1 is_stmt 1 view -0
 1602              		.cfi_startproc
 1603              		@ args = 0, pretend = 0, frame = 0
 1604              		@ frame_needed = 0, uses_anonymous_args = 0
 1605              		@ link register save eliminated.
 466:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   HAL_DMA_StateTypeDef temp_state = hdma->State;
 1606              		.loc 1 466 3 view .LVU472
 467:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s 			page 49


 1607              		.loc 1 467 3 view .LVU473
 467:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1608              		.loc 1 467 24 is_stmt 0 view .LVU474
 1609 0000 90F83530 		ldrb	r3, [r0, #53]	@ zero_extendqisi2
 1610              	.LVL65:
 470:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1611              		.loc 1 470 3 is_stmt 1 view .LVU475
 472:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
 1612              		.loc 1 472 3 view .LVU476
 474:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   }
 1613              		.loc 1 474 5 view .LVU477
 478:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   }
 1614              		.loc 1 478 5 view .LVU478
 482:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   assert_param(IS_DMAMUX_REQUEST_GEN_REQUEST_NUMBER(pRequestGeneratorConfig->RequestNumber));
 1615              		.loc 1 482 3 view .LVU479
 483:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1616              		.loc 1 483 3 view .LVU480
 488:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
 1617              		.loc 1 488 3 view .LVU481
 488:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
 1618              		.loc 1 488 10 is_stmt 0 view .LVU482
 1619 0004 C26E     		ldr	r2, [r0, #108]
 488:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
 1620              		.loc 1 488 5 view .LVU483
 1621 0006 72B1     		cbz	r2, .L84
 465:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   HAL_StatusTypeDef status;
 1622              		.loc 1 465 1 view .LVU484
 1623 0008 10B4     		push	{r4}
 1624              	.LCFI11:
 1625              		.cfi_def_cfa_offset 4
 1626              		.cfi_offset 4, -4
 1627 000a DBB2     		uxtb	r3, r3
 496:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
 1628              		.loc 1 496 8 is_stmt 1 view .LVU485
 496:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
 1629              		.loc 1 496 35 is_stmt 0 view .LVU486
 1630 000c 1468     		ldr	r4, [r2]
 496:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
 1631              		.loc 1 496 10 view .LVU487
 1632 000e 14F4803F 		tst	r4, #65536
 1633 0012 01D1     		bne	.L79
 496:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
 1634              		.loc 1 496 68 discriminator 1 view .LVU488
 1635 0014 012B     		cmp	r3, #1
 1636 0016 0AD0     		beq	.L85
 1637              	.L79:
 515:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1638              		.loc 1 515 5 is_stmt 1 view .LVU489
 515:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1639              		.loc 1 515 21 is_stmt 0 view .LVU490
 1640 0018 4FF40063 		mov	r3, #2048
 1641              	.LVL66:
 515:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1642              		.loc 1 515 21 view .LVU491
 1643 001c 4365     		str	r3, [r0, #84]
 518:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   }
 1644              		.loc 1 518 5 is_stmt 1 view .LVU492
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s 			page 50


 1645              	.LVL67:
 521:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** }
 1646              		.loc 1 521 10 is_stmt 0 view .LVU493
 1647 001e 0120     		movs	r0, #1
 1648              	.LVL68:
 1649              	.L78:
 522:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1650              		.loc 1 522 1 view .LVU494
 1651 0020 5DF8044B 		ldr	r4, [sp], #4
 1652              	.LCFI12:
 1653              		.cfi_restore 4
 1654              		.cfi_def_cfa_offset 0
 1655 0024 7047     		bx	lr
 1656              	.LVL69:
 1657              	.L84:
 491:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1658              		.loc 1 491 5 is_stmt 1 view .LVU495
 491:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1659              		.loc 1 491 21 is_stmt 0 view .LVU496
 1660 0026 4023     		movs	r3, #64
 1661              	.LVL70:
 491:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1662              		.loc 1 491 21 view .LVU497
 1663 0028 4365     		str	r3, [r0, #84]
 494:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   }
 1664              		.loc 1 494 5 is_stmt 1 view .LVU498
 1665              	.LVL71:
 521:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** }
 1666              		.loc 1 521 10 is_stmt 0 view .LVU499
 1667 002a 0120     		movs	r0, #1
 1668              	.LVL72:
 522:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1669              		.loc 1 522 1 view .LVU500
 1670 002c 7047     		bx	lr
 1671              	.LVL73:
 1672              	.L85:
 1673              	.LCFI13:
 1674              		.cfi_def_cfa_offset 4
 1675              		.cfi_offset 4, -4
 501:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1676              		.loc 1 501 5 is_stmt 1 view .LVU501
 501:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1677              		.loc 1 501 5 view .LVU502
 1678 002e 90F83430 		ldrb	r3, [r0, #52]	@ zero_extendqisi2
 1679              	.LVL74:
 501:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1680              		.loc 1 501 5 is_stmt 0 view .LVU503
 1681 0032 012B     		cmp	r3, #1
 1682 0034 0FD0     		beq	.L80
 501:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1683              		.loc 1 501 5 is_stmt 1 discriminator 2 view .LVU504
 1684 0036 0123     		movs	r3, #1
 1685 0038 80F83430 		strb	r3, [r0, #52]
 501:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1686              		.loc 1 501 5 discriminator 2 view .LVU505
 504:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****                                   ((pRequestGeneratorConfig->RequestNumber - 1U) << DMAMUX_RGxCR_GN
 1687              		.loc 1 504 5 view .LVU506
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s 			page 51


 504:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****                                   ((pRequestGeneratorConfig->RequestNumber - 1U) << DMAMUX_RGxCR_GN
 1688              		.loc 1 504 59 is_stmt 0 view .LVU507
 1689 003c 0B68     		ldr	r3, [r1]
 505:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****                                   pRequestGeneratorConfig->Polarity;
 1690              		.loc 1 505 60 view .LVU508
 1691 003e 8C68     		ldr	r4, [r1, #8]
 505:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****                                   pRequestGeneratorConfig->Polarity;
 1692              		.loc 1 505 76 view .LVU509
 1693 0040 013C     		subs	r4, r4, #1
 504:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****                                   ((pRequestGeneratorConfig->RequestNumber - 1U) << DMAMUX_RGxCR_GN
 1694              		.loc 1 504 70 view .LVU510
 1695 0042 43EAC443 		orr	r3, r3, r4, lsl #19
 506:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     /* Process Locked */
 1696              		.loc 1 506 58 view .LVU511
 1697 0046 4968     		ldr	r1, [r1, #4]
 1698              	.LVL75:
 505:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****                                   pRequestGeneratorConfig->Polarity;
 1699              		.loc 1 505 109 view .LVU512
 1700 0048 0B43     		orrs	r3, r3, r1
 504:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****                                   ((pRequestGeneratorConfig->RequestNumber - 1U) << DMAMUX_RGxCR_GN
 1701              		.loc 1 504 34 view .LVU513
 1702 004a 1360     		str	r3, [r2]
 508:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1703              		.loc 1 508 5 is_stmt 1 view .LVU514
 508:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1704              		.loc 1 508 5 view .LVU515
 1705 004c 0023     		movs	r3, #0
 1706 004e 80F83430 		strb	r3, [r0, #52]
 508:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1707              		.loc 1 508 5 view .LVU516
 510:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   }
 1708              		.loc 1 510 5 view .LVU517
 510:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   }
 1709              		.loc 1 510 12 is_stmt 0 view .LVU518
 1710 0052 1846     		mov	r0, r3
 1711              	.LVL76:
 510:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   }
 1712              		.loc 1 510 12 view .LVU519
 1713 0054 E4E7     		b	.L78
 1714              	.LVL77:
 1715              	.L80:
 501:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1716              		.loc 1 501 5 discriminator 1 view .LVU520
 1717 0056 0220     		movs	r0, #2
 1718              	.LVL78:
 501:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1719              		.loc 1 501 5 discriminator 1 view .LVU521
 1720 0058 E2E7     		b	.L78
 1721              		.cfi_endproc
 1722              	.LFE148:
 1724              		.section	.text.HAL_DMAEx_EnableMuxRequestGenerator,"ax",%progbits
 1725              		.align	1
 1726              		.global	HAL_DMAEx_EnableMuxRequestGenerator
 1727              		.syntax unified
 1728              		.thumb
 1729              		.thumb_func
 1731              	HAL_DMAEx_EnableMuxRequestGenerator:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s 			page 52


 1732              	.LVL79:
 1733              	.LFB149:
 531:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   /* Check the parameters */
 1734              		.loc 1 531 1 is_stmt 1 view -0
 1735              		.cfi_startproc
 1736              		@ args = 0, pretend = 0, frame = 0
 1737              		@ frame_needed = 0, uses_anonymous_args = 0
 1738              		@ link register save eliminated.
 533:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1739              		.loc 1 533 3 view .LVU523
 537:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
 1740              		.loc 1 537 3 view .LVU524
 537:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
 1741              		.loc 1 537 11 is_stmt 0 view .LVU525
 1742 0000 90F83530 		ldrb	r3, [r0, #53]	@ zero_extendqisi2
 537:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
 1743              		.loc 1 537 5 view .LVU526
 1744 0004 3BB1     		cbz	r3, .L88
 537:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
 1745              		.loc 1 537 51 discriminator 1 view .LVU527
 1746 0006 C36E     		ldr	r3, [r0, #108]
 537:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
 1747              		.loc 1 537 43 discriminator 1 view .LVU528
 1748 0008 3BB1     		cbz	r3, .L89
 540:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1749              		.loc 1 540 5 is_stmt 1 view .LVU529
 540:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1750              		.loc 1 540 27 is_stmt 0 view .LVU530
 1751 000a 1A68     		ldr	r2, [r3]
 540:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1752              		.loc 1 540 34 view .LVU531
 1753 000c 42F48032 		orr	r2, r2, #65536
 1754 0010 1A60     		str	r2, [r3]
 542:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****  }
 1755              		.loc 1 542 4 is_stmt 1 view .LVU532
 542:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****  }
 1756              		.loc 1 542 11 is_stmt 0 view .LVU533
 1757 0012 0020     		movs	r0, #0
 1758              	.LVL80:
 542:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****  }
 1759              		.loc 1 542 11 view .LVU534
 1760 0014 7047     		bx	lr
 1761              	.LVL81:
 1762              	.L88:
 546:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****  }
 1763              		.loc 1 546 11 view .LVU535
 1764 0016 0120     		movs	r0, #1
 1765              	.LVL82:
 546:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****  }
 1766              		.loc 1 546 11 view .LVU536
 1767 0018 7047     		bx	lr
 1768              	.LVL83:
 1769              	.L89:
 546:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****  }
 1770              		.loc 1 546 11 view .LVU537
 1771 001a 0120     		movs	r0, #1
 1772              	.LVL84:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s 			page 53


 548:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1773              		.loc 1 548 1 view .LVU538
 1774 001c 7047     		bx	lr
 1775              		.cfi_endproc
 1776              	.LFE149:
 1778              		.section	.text.HAL_DMAEx_DisableMuxRequestGenerator,"ax",%progbits
 1779              		.align	1
 1780              		.global	HAL_DMAEx_DisableMuxRequestGenerator
 1781              		.syntax unified
 1782              		.thumb
 1783              		.thumb_func
 1785              	HAL_DMAEx_DisableMuxRequestGenerator:
 1786              	.LVL85:
 1787              	.LFB150:
 557:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   /* Check the parameters */
 1788              		.loc 1 557 1 is_stmt 1 view -0
 1789              		.cfi_startproc
 1790              		@ args = 0, pretend = 0, frame = 0
 1791              		@ frame_needed = 0, uses_anonymous_args = 0
 1792              		@ link register save eliminated.
 559:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1793              		.loc 1 559 3 view .LVU540
 563:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
 1794              		.loc 1 563 3 view .LVU541
 563:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
 1795              		.loc 1 563 11 is_stmt 0 view .LVU542
 1796 0000 90F83530 		ldrb	r3, [r0, #53]	@ zero_extendqisi2
 563:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
 1797              		.loc 1 563 5 view .LVU543
 1798 0004 3BB1     		cbz	r3, .L92
 563:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
 1799              		.loc 1 563 51 discriminator 1 view .LVU544
 1800 0006 C36E     		ldr	r3, [r0, #108]
 563:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
 1801              		.loc 1 563 43 discriminator 1 view .LVU545
 1802 0008 3BB1     		cbz	r3, .L93
 566:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1803              		.loc 1 566 5 is_stmt 1 view .LVU546
 566:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1804              		.loc 1 566 27 is_stmt 0 view .LVU547
 1805 000a 1A68     		ldr	r2, [r3]
 566:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1806              		.loc 1 566 34 view .LVU548
 1807 000c 22F48032 		bic	r2, r2, #65536
 1808 0010 1A60     		str	r2, [r3]
 568:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****  }
 1809              		.loc 1 568 4 is_stmt 1 view .LVU549
 568:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****  }
 1810              		.loc 1 568 11 is_stmt 0 view .LVU550
 1811 0012 0020     		movs	r0, #0
 1812              	.LVL86:
 568:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****  }
 1813              		.loc 1 568 11 view .LVU551
 1814 0014 7047     		bx	lr
 1815              	.LVL87:
 1816              	.L92:
 572:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****  }
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s 			page 54


 1817              		.loc 1 572 11 view .LVU552
 1818 0016 0120     		movs	r0, #1
 1819              	.LVL88:
 572:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****  }
 1820              		.loc 1 572 11 view .LVU553
 1821 0018 7047     		bx	lr
 1822              	.LVL89:
 1823              	.L93:
 572:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****  }
 1824              		.loc 1 572 11 view .LVU554
 1825 001a 0120     		movs	r0, #1
 1826              	.LVL90:
 574:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1827              		.loc 1 574 1 view .LVU555
 1828 001c 7047     		bx	lr
 1829              		.cfi_endproc
 1830              	.LFE150:
 1832              		.section	.text.HAL_DMAEx_MUX_IRQHandler,"ax",%progbits
 1833              		.align	1
 1834              		.global	HAL_DMAEx_MUX_IRQHandler
 1835              		.syntax unified
 1836              		.thumb
 1837              		.thumb_func
 1839              	HAL_DMAEx_MUX_IRQHandler:
 1840              	.LVL91:
 1841              	.LFB151:
 583:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   /* Check for DMAMUX Synchronization overrun */
 1842              		.loc 1 583 1 is_stmt 1 view -0
 1843              		.cfi_startproc
 1844              		@ args = 0, pretend = 0, frame = 0
 1845              		@ frame_needed = 0, uses_anonymous_args = 0
 583:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   /* Check for DMAMUX Synchronization overrun */
 1846              		.loc 1 583 1 is_stmt 0 view .LVU557
 1847 0000 10B5     		push	{r4, lr}
 1848              	.LCFI14:
 1849              		.cfi_def_cfa_offset 8
 1850              		.cfi_offset 4, -8
 1851              		.cfi_offset 14, -4
 1852 0002 0446     		mov	r4, r0
 585:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
 1853              		.loc 1 585 3 is_stmt 1 view .LVU558
 585:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
 1854              		.loc 1 585 11 is_stmt 0 view .LVU559
 1855 0004 436E     		ldr	r3, [r0, #100]
 585:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
 1856              		.loc 1 585 32 view .LVU560
 1857 0006 1A68     		ldr	r2, [r3]
 585:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
 1858              		.loc 1 585 44 view .LVU561
 1859 0008 836E     		ldr	r3, [r0, #104]
 585:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
 1860              		.loc 1 585 5 view .LVU562
 1861 000a 1A42     		tst	r2, r3
 1862 000c 0ED0     		beq	.L95
 588:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1863              		.loc 1 588 5 is_stmt 1 view .LVU563
 588:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s 			page 55


 1864              		.loc 1 588 9 is_stmt 0 view .LVU564
 1865 000e 026E     		ldr	r2, [r0, #96]
 588:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1866              		.loc 1 588 24 view .LVU565
 1867 0010 1368     		ldr	r3, [r2]
 588:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1868              		.loc 1 588 30 view .LVU566
 1869 0012 23F48073 		bic	r3, r3, #256
 1870 0016 1360     		str	r3, [r2]
 591:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1871              		.loc 1 591 5 is_stmt 1 view .LVU567
 591:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1872              		.loc 1 591 9 is_stmt 0 view .LVU568
 1873 0018 436E     		ldr	r3, [r0, #100]
 591:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1874              		.loc 1 591 42 view .LVU569
 1875 001a 826E     		ldr	r2, [r0, #104]
 591:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1876              		.loc 1 591 36 view .LVU570
 1877 001c 5A60     		str	r2, [r3, #4]
 594:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1878              		.loc 1 594 5 is_stmt 1 view .LVU571
 594:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1879              		.loc 1 594 9 is_stmt 0 view .LVU572
 1880 001e 436D     		ldr	r3, [r0, #84]
 594:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1881              		.loc 1 594 21 view .LVU573
 1882 0020 43F40073 		orr	r3, r3, #512
 1883 0024 4365     		str	r3, [r0, #84]
 596:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 1884              		.loc 1 596 5 is_stmt 1 view .LVU574
 596:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 1885              		.loc 1 596 12 is_stmt 0 view .LVU575
 1886 0026 C36C     		ldr	r3, [r0, #76]
 596:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 1887              		.loc 1 596 7 view .LVU576
 1888 0028 03B1     		cbz	r3, .L95
 599:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     }
 1889              		.loc 1 599 7 is_stmt 1 view .LVU577
 1890 002a 9847     		blx	r3
 1891              	.LVL92:
 1892              	.L95:
 603:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
 1893              		.loc 1 603 3 view .LVU578
 603:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
 1894              		.loc 1 603 10 is_stmt 0 view .LVU579
 1895 002c E36E     		ldr	r3, [r4, #108]
 603:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****   {
 1896              		.loc 1 603 5 view .LVU580
 1897 002e 9BB1     		cbz	r3, .L94
 606:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 1898              		.loc 1 606 5 is_stmt 1 view .LVU581
 606:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 1899              		.loc 1 606 13 is_stmt 0 view .LVU582
 1900 0030 226F     		ldr	r2, [r4, #112]
 606:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 1901              		.loc 1 606 37 view .LVU583
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s 			page 56


 1902 0032 1168     		ldr	r1, [r2]
 606:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 1903              		.loc 1 606 50 view .LVU584
 1904 0034 626F     		ldr	r2, [r4, #116]
 606:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****     {
 1905              		.loc 1 606 7 view .LVU585
 1906 0036 1142     		tst	r1, r2
 1907 0038 0ED0     		beq	.L94
 609:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1908              		.loc 1 609 7 is_stmt 1 view .LVU586
 609:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1909              		.loc 1 609 29 is_stmt 0 view .LVU587
 1910 003a 1A68     		ldr	r2, [r3]
 609:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1911              		.loc 1 609 36 view .LVU588
 1912 003c 22F48072 		bic	r2, r2, #256
 1913 0040 1A60     		str	r2, [r3]
 612:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1914              		.loc 1 612 7 is_stmt 1 view .LVU589
 612:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1915              		.loc 1 612 11 is_stmt 0 view .LVU590
 1916 0042 236F     		ldr	r3, [r4, #112]
 612:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1917              		.loc 1 612 49 view .LVU591
 1918 0044 626F     		ldr	r2, [r4, #116]
 612:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1919              		.loc 1 612 43 view .LVU592
 1920 0046 5A60     		str	r2, [r3, #4]
 615:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1921              		.loc 1 615 7 is_stmt 1 view .LVU593
 615:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1922              		.loc 1 615 11 is_stmt 0 view .LVU594
 1923 0048 636D     		ldr	r3, [r4, #84]
 615:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1924              		.loc 1 615 23 view .LVU595
 1925 004a 43F48063 		orr	r3, r3, #1024
 1926 004e 6365     		str	r3, [r4, #84]
 617:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       {
 1927              		.loc 1 617 7 is_stmt 1 view .LVU596
 617:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       {
 1928              		.loc 1 617 14 is_stmt 0 view .LVU597
 1929 0050 E36C     		ldr	r3, [r4, #76]
 617:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       {
 1930              		.loc 1 617 9 view .LVU598
 1931 0052 0BB1     		cbz	r3, .L94
 620:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c ****       }
 1932              		.loc 1 620 9 is_stmt 1 view .LVU599
 1933 0054 2046     		mov	r0, r4
 1934 0056 9847     		blx	r3
 1935              	.LVL93:
 1936              	.L94:
 624:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1937              		.loc 1 624 1 is_stmt 0 view .LVU600
 1938 0058 10BD     		pop	{r4, pc}
 624:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c **** 
 1939              		.loc 1 624 1 view .LVU601
 1940              		.cfi_endproc
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s 			page 57


 1941              	.LFE151:
 1943              		.text
 1944              	.Letext0:
 1945              		.file 2 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin/../lib/gcc/arm-none-eabi/13.3.1/../
 1946              		.file 3 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin/../lib/gcc/arm-none-eabi/13.3.1/../
 1947              		.file 4 "Drivers/CMSIS/Device/ST/STM32H7xx/Include/stm32h723xx.h"
 1948              		.file 5 "Drivers/CMSIS/Device/ST/STM32H7xx/Include/stm32h7xx.h"
 1949              		.file 6 "Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_def.h"
 1950              		.file 7 "Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_dma.h"
 1951              		.file 8 "Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_dma_ex.h"
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s 			page 58


DEFINED SYMBOLS
                            *ABS*:00000000 stm32h7xx_hal_dma_ex.c
C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s:20     .text.DMA_MultiBufferSetConfig:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s:25     .text.DMA_MultiBufferSetConfig:00000000 DMA_MultiBufferSetConfig
C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s:194    .text.DMA_MultiBufferSetConfig:000000a0 $d
C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s:200    .text.HAL_DMAEx_MultiBufferStart:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s:206    .text.HAL_DMAEx_MultiBufferStart:00000000 HAL_DMAEx_MultiBufferStart
C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s:623    .text.HAL_DMAEx_MultiBufferStart:0000020c $d
C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s:630    .text.HAL_DMAEx_MultiBufferStart_IT:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s:636    .text.HAL_DMAEx_MultiBufferStart_IT:00000000 HAL_DMAEx_MultiBufferStart_IT
C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s:1053   .text.HAL_DMAEx_MultiBufferStart_IT:00000210 $d
C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s:1058   .text.HAL_DMAEx_MultiBufferStart_IT:0000021c $t
C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s:1313   .text.HAL_DMAEx_MultiBufferStart_IT:0000038c $d
C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s:1320   .text.HAL_DMAEx_ChangeMemory:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s:1326   .text.HAL_DMAEx_ChangeMemory:00000000 HAL_DMAEx_ChangeMemory
C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s:1445   .text.HAL_DMAEx_ChangeMemory:00000080 $d
C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s:1451   .text.HAL_DMAEx_ConfigMuxSync:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s:1457   .text.HAL_DMAEx_ConfigMuxSync:00000000 HAL_DMAEx_ConfigMuxSync
C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s:1592   .text.HAL_DMAEx_ConfigMuxRequestGenerator:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s:1598   .text.HAL_DMAEx_ConfigMuxRequestGenerator:00000000 HAL_DMAEx_ConfigMuxRequestGenerator
C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s:1725   .text.HAL_DMAEx_EnableMuxRequestGenerator:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s:1731   .text.HAL_DMAEx_EnableMuxRequestGenerator:00000000 HAL_DMAEx_EnableMuxRequestGenerator
C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s:1779   .text.HAL_DMAEx_DisableMuxRequestGenerator:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s:1785   .text.HAL_DMAEx_DisableMuxRequestGenerator:00000000 HAL_DMAEx_DisableMuxRequestGenerator
C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s:1833   .text.HAL_DMAEx_MUX_IRQHandler:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cchmMTud.s:1839   .text.HAL_DMAEx_MUX_IRQHandler:00000000 HAL_DMAEx_MUX_IRQHandler

NO UNDEFINED SYMBOLS
