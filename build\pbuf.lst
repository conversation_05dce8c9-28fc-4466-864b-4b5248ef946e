ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 1


   1              		.cpu cortex-m7
   2              		.arch armv7e-m
   3              		.fpu fpv5-d16
   4              		.eabi_attribute 28, 1
   5              		.eabi_attribute 20, 1
   6              		.eabi_attribute 21, 1
   7              		.eabi_attribute 23, 3
   8              		.eabi_attribute 24, 1
   9              		.eabi_attribute 25, 1
  10              		.eabi_attribute 26, 1
  11              		.eabi_attribute 30, 1
  12              		.eabi_attribute 34, 1
  13              		.eabi_attribute 18, 4
  14              		.file	"pbuf.c"
  15              		.text
  16              	.Ltext0:
  17              		.cfi_sections	.debug_frame
  18              		.file 1 "Middlewares/Third_Party/LwIP/src/core/pbuf.c"
  19              		.section	.text.pbuf_init_alloced_pbuf,"ax",%progbits
  20              		.align	1
  21              		.syntax unified
  22              		.thumb
  23              		.thumb_func
  25              	pbuf_init_alloced_pbuf:
  26              	.LVL0:
  27              	.LFB177:
   1:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** /**
   2:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @file
   3:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * Packet buffer management
   4:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  */
   5:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
   6:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** /**
   7:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @defgroup pbuf Packet buffers (PBUF)
   8:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @ingroup infrastructure
   9:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
  10:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * Packets are built from the pbuf data structure. It supports dynamic
  11:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * memory allocation for packet contents or can reference externally
  12:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * managed packet contents both in RAM and ROM. Quick allocation for
  13:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * incoming packets is provided through pools with fixed sized pbufs.
  14:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
  15:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * A packet may span over multiple pbufs, chained as a singly linked
  16:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * list. This is called a "pbuf chain".
  17:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
  18:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * Multiple packets may be queued, also using this singly linked list.
  19:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * This is called a "packet queue".
  20:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
  21:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * So, a packet queue consists of one or more pbuf chains, each of
  22:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * which consist of one or more pbufs. CURRENTLY, PACKET QUEUES ARE
  23:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * NOT SUPPORTED!!! Use helper structs to queue multiple packets.
  24:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
  25:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * The differences between a pbuf chain and a packet queue are very
  26:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * precise but subtle.
  27:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
  28:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * The last pbuf of a packet has a ->tot_len field that equals the
  29:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * ->len field. It can be found by traversing the list. If the last
  30:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * pbuf of a packet has a ->next field other than NULL, more packets
  31:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * are on the queue.
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 2


  32:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
  33:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * Therefore, looping through a pbuf of a single packet, has an
  34:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * loop end condition (tot_len == p->len), NOT (next == NULL).
  35:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
  36:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * Example of custom pbuf usage: @ref zerocopyrx
  37:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  */
  38:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
  39:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** /*
  40:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * Copyright (c) 2001-2004 Swedish Institute of Computer Science.
  41:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * All rights reserved.
  42:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
  43:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * Redistribution and use in source and binary forms, with or without modification,
  44:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * are permitted provided that the following conditions are met:
  45:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
  46:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * 1. Redistributions of source code must retain the above copyright notice,
  47:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *    this list of conditions and the following disclaimer.
  48:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * 2. Redistributions in binary form must reproduce the above copyright notice,
  49:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *    this list of conditions and the following disclaimer in the documentation
  50:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *    and/or other materials provided with the distribution.
  51:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * 3. The name of the author may not be used to endorse or promote products
  52:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *    derived from this software without specific prior written permission.
  53:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
  54:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR IMPLIED
  55:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
  56:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT
  57:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * SHALL THE AUTHOR BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
  58:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT
  59:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
  60:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
  61:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
  62:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY
  63:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * OF SUCH DAMAGE.
  64:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
  65:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * This file is part of the lwIP TCP/IP stack.
  66:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
  67:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * Author: Adam Dunkels <<EMAIL>>
  68:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
  69:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  */
  70:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
  71:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** #include "lwip/opt.h"
  72:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
  73:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** #include "lwip/pbuf.h"
  74:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** #include "lwip/stats.h"
  75:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** #include "lwip/def.h"
  76:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** #include "lwip/mem.h"
  77:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** #include "lwip/memp.h"
  78:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** #include "lwip/sys.h"
  79:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** #include "lwip/netif.h"
  80:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** #if LWIP_TCP && TCP_QUEUE_OOSEQ
  81:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** #include "lwip/priv/tcp_priv.h"
  82:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** #endif
  83:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** #if LWIP_CHECKSUM_ON_COPY
  84:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** #include "lwip/inet_chksum.h"
  85:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** #endif
  86:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
  87:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** #include <string.h>
  88:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 3


  89:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** #define SIZEOF_STRUCT_PBUF        LWIP_MEM_ALIGN_SIZE(sizeof(struct pbuf))
  90:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** /* Since the pool is created in memp, PBUF_POOL_BUFSIZE will be automatically
  91:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****    aligned there. Therefore, PBUF_POOL_BUFSIZE_ALIGNED can be used here. */
  92:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** #define PBUF_POOL_BUFSIZE_ALIGNED LWIP_MEM_ALIGN_SIZE(PBUF_POOL_BUFSIZE)
  93:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
  94:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** static const struct pbuf *
  95:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** pbuf_skip_const(const struct pbuf *in, u16_t in_offset, u16_t *out_offset);
  96:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
  97:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** #if !LWIP_TCP || !TCP_QUEUE_OOSEQ || !PBUF_POOL_FREE_OOSEQ
  98:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** #define PBUF_POOL_IS_EMPTY()
  99:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** #else /* !LWIP_TCP || !TCP_QUEUE_OOSEQ || !PBUF_POOL_FREE_OOSEQ */
 100:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 101:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** #if !NO_SYS
 102:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** #ifndef PBUF_POOL_FREE_OOSEQ_QUEUE_CALL
 103:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** #include "lwip/tcpip.h"
 104:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** #define PBUF_POOL_FREE_OOSEQ_QUEUE_CALL()  do { \
 105:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   if (tcpip_try_callback(pbuf_free_ooseq_callback, NULL) != ERR_OK) { \
 106:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       SYS_ARCH_PROTECT(old_level); \
 107:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       pbuf_free_ooseq_pending = 0; \
 108:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       SYS_ARCH_UNPROTECT(old_level); \
 109:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   } } while(0)
 110:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** #endif /* PBUF_POOL_FREE_OOSEQ_QUEUE_CALL */
 111:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** #endif /* !NO_SYS */
 112:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 113:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** volatile u8_t pbuf_free_ooseq_pending;
 114:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** #define PBUF_POOL_IS_EMPTY() pbuf_pool_is_empty()
 115:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 116:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** /**
 117:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * Attempt to reclaim some memory from queued out-of-sequence TCP segments
 118:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * if we run out of pool pbufs. It's better to give priority to new packets
 119:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * if we're running out.
 120:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
 121:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * This must be done in the correct thread context therefore this function
 122:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * can only be used with NO_SYS=0 and through tcpip_callback.
 123:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  */
 124:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** #if !NO_SYS
 125:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** static
 126:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** #endif /* !NO_SYS */
 127:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** void
 128:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** pbuf_free_ooseq(void)
 129:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** {
 130:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   struct tcp_pcb *pcb;
 131:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   SYS_ARCH_SET(pbuf_free_ooseq_pending, 0);
 132:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 133:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   for (pcb = tcp_active_pcbs; NULL != pcb; pcb = pcb->next) {
 134:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     if (pcb->ooseq != NULL) {
 135:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       /** Free the ooseq pbufs of one PCB only */
 136:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       LWIP_DEBUGF(PBUF_DEBUG | LWIP_DBG_TRACE, ("pbuf_free_ooseq: freeing out-of-sequence pbufs\n")
 137:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       tcp_free_ooseq(pcb);
 138:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       return;
 139:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     }
 140:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
 141:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** }
 142:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 143:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** #if !NO_SYS
 144:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** /**
 145:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * Just a callback function for tcpip_callback() that calls pbuf_free_ooseq().
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 4


 146:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  */
 147:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** static void
 148:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** pbuf_free_ooseq_callback(void *arg)
 149:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** {
 150:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_UNUSED_ARG(arg);
 151:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   pbuf_free_ooseq();
 152:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** }
 153:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** #endif /* !NO_SYS */
 154:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 155:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** /** Queue a call to pbuf_free_ooseq if not already queued. */
 156:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** static void
 157:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** pbuf_pool_is_empty(void)
 158:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** {
 159:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** #ifndef PBUF_POOL_FREE_OOSEQ_QUEUE_CALL
 160:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   SYS_ARCH_SET(pbuf_free_ooseq_pending, 1);
 161:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** #else /* PBUF_POOL_FREE_OOSEQ_QUEUE_CALL */
 162:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   u8_t queued;
 163:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   SYS_ARCH_DECL_PROTECT(old_level);
 164:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   SYS_ARCH_PROTECT(old_level);
 165:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   queued = pbuf_free_ooseq_pending;
 166:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   pbuf_free_ooseq_pending = 1;
 167:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   SYS_ARCH_UNPROTECT(old_level);
 168:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 169:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   if (!queued) {
 170:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* queue a call to pbuf_free_ooseq if not already queued */
 171:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     PBUF_POOL_FREE_OOSEQ_QUEUE_CALL();
 172:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
 173:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** #endif /* PBUF_POOL_FREE_OOSEQ_QUEUE_CALL */
 174:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** }
 175:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** #endif /* !LWIP_TCP || !TCP_QUEUE_OOSEQ || !PBUF_POOL_FREE_OOSEQ */
 176:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 177:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** /* Initialize members of struct pbuf after allocation */
 178:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** static void
 179:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** pbuf_init_alloced_pbuf(struct pbuf *p, void *payload, u16_t tot_len, u16_t len, pbuf_type type, u8_
 180:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** {
  28              		.loc 1 180 1 view -0
  29              		.cfi_startproc
  30              		@ args = 8, pretend = 0, frame = 0
  31              		@ frame_needed = 0, uses_anonymous_args = 0
  32              		@ link register save eliminated.
  33              		.loc 1 180 1 is_stmt 0 view .LVU1
  34 0000 10B4     		push	{r4}
  35              	.LCFI0:
  36              		.cfi_def_cfa_offset 4
  37              		.cfi_offset 4, -4
 181:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   p->next = NULL;
  38              		.loc 1 181 3 is_stmt 1 view .LVU2
  39              		.loc 1 181 11 is_stmt 0 view .LVU3
  40 0002 0024     		movs	r4, #0
  41 0004 0460     		str	r4, [r0]
 182:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   p->payload = payload;
  42              		.loc 1 182 3 is_stmt 1 view .LVU4
  43              		.loc 1 182 14 is_stmt 0 view .LVU5
  44 0006 4160     		str	r1, [r0, #4]
 183:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   p->tot_len = tot_len;
  45              		.loc 1 183 3 is_stmt 1 view .LVU6
  46              		.loc 1 183 14 is_stmt 0 view .LVU7
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 5


  47 0008 0281     		strh	r2, [r0, #8]	@ movhi
 184:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   p->len = len;
  48              		.loc 1 184 3 is_stmt 1 view .LVU8
  49              		.loc 1 184 10 is_stmt 0 view .LVU9
  50 000a 4381     		strh	r3, [r0, #10]	@ movhi
 185:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   p->type_internal = (u8_t)type;
  51              		.loc 1 185 3 is_stmt 1 view .LVU10
  52              		.loc 1 185 22 is_stmt 0 view .LVU11
  53 000c 9DF80430 		ldrb	r3, [sp, #4]	@ zero_extendqisi2
  54              	.LVL1:
  55              		.loc 1 185 20 view .LVU12
  56 0010 0373     		strb	r3, [r0, #12]
 186:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   p->flags = flags;
  57              		.loc 1 186 3 is_stmt 1 view .LVU13
  58              		.loc 1 186 12 is_stmt 0 view .LVU14
  59 0012 9DF80830 		ldrb	r3, [sp, #8]	@ zero_extendqisi2
  60 0016 4373     		strb	r3, [r0, #13]
 187:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   p->ref = 1;
  61              		.loc 1 187 3 is_stmt 1 view .LVU15
  62              		.loc 1 187 10 is_stmt 0 view .LVU16
  63 0018 0123     		movs	r3, #1
  64 001a 8373     		strb	r3, [r0, #14]
 188:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   p->if_idx = NETIF_NO_INDEX;
  65              		.loc 1 188 3 is_stmt 1 view .LVU17
  66              		.loc 1 188 13 is_stmt 0 view .LVU18
  67 001c C473     		strb	r4, [r0, #15]
 189:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** }
  68              		.loc 1 189 1 view .LVU19
  69 001e 5DF8044B 		ldr	r4, [sp], #4
  70              	.LCFI1:
  71              		.cfi_restore 4
  72              		.cfi_def_cfa_offset 0
  73              	.LVL2:
  74              		.loc 1 189 1 view .LVU20
  75 0022 7047     		bx	lr
  76              		.cfi_endproc
  77              	.LFE177:
  79              		.section	.text.pbuf_skip_const,"ax",%progbits
  80              		.align	1
  81              		.syntax unified
  82              		.thumb
  83              		.thumb_func
  85              	pbuf_skip_const:
  86              	.LVL3:
  87              	.LFB199:
 190:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 191:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** /**
 192:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @ingroup pbuf
 193:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * Allocates a pbuf of the given type (possibly a chain for PBUF_POOL type).
 194:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
 195:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * The actual memory allocated for the pbuf is determined by the
 196:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * layer at which the pbuf is allocated and the requested size
 197:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * (from the size parameter).
 198:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
 199:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param layer header size
 200:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param length size of the pbuf's payload
 201:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param type this parameter decides how and where the pbuf
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 6


 202:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * should be allocated as follows:
 203:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
 204:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * - PBUF_RAM: buffer memory for pbuf is allocated as one large
 205:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *             chunk. This includes protocol headers as well.
 206:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * - PBUF_ROM: no buffer memory is allocated for the pbuf, even for
 207:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *             protocol headers. Additional headers must be prepended
 208:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *             by allocating another pbuf and chain in to the front of
 209:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *             the ROM pbuf. It is assumed that the memory used is really
 210:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *             similar to ROM in that it is immutable and will not be
 211:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *             changed. Memory which is dynamic should generally not
 212:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *             be attached to PBUF_ROM pbufs. Use PBUF_REF instead.
 213:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * - PBUF_REF: no buffer memory is allocated for the pbuf, even for
 214:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *             protocol headers. It is assumed that the pbuf is only
 215:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *             being used in a single thread. If the pbuf gets queued,
 216:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *             then pbuf_take should be called to copy the buffer.
 217:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * - PBUF_POOL: the pbuf is allocated as a pbuf chain, with pbufs from
 218:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *              the pbuf pool that is allocated during pbuf_init().
 219:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
 220:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @return the allocated pbuf. If multiple pbufs where allocated, this
 221:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * is the first pbuf of a pbuf chain.
 222:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  */
 223:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** struct pbuf *
 224:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** pbuf_alloc(pbuf_layer layer, u16_t length, pbuf_type type)
 225:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** {
 226:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   struct pbuf *p;
 227:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   u16_t offset = (u16_t)layer;
 228:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_DEBUGF(PBUF_DEBUG | LWIP_DBG_TRACE, ("pbuf_alloc(length=%"U16_F")\n", length));
 229:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 230:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   switch (type) {
 231:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     case PBUF_REF: /* fall through */
 232:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     case PBUF_ROM:
 233:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       p = pbuf_alloc_reference(NULL, length, type);
 234:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       break;
 235:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     case PBUF_POOL: {
 236:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       struct pbuf *q, *last;
 237:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       u16_t rem_len; /* remaining length */
 238:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       p = NULL;
 239:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       last = NULL;
 240:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       rem_len = length;
 241:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       do {
 242:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         u16_t qlen;
 243:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         q = (struct pbuf *)memp_malloc(MEMP_PBUF_POOL);
 244:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         if (q == NULL) {
 245:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****           PBUF_POOL_IS_EMPTY();
 246:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****           /* free chain so far allocated */
 247:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****           if (p) {
 248:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****             pbuf_free(p);
 249:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****           }
 250:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****           /* bail out unsuccessfully */
 251:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****           return NULL;
 252:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         }
 253:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         qlen = LWIP_MIN(rem_len, (u16_t)(PBUF_POOL_BUFSIZE_ALIGNED - LWIP_MEM_ALIGN_SIZE(offset)));
 254:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         pbuf_init_alloced_pbuf(q, LWIP_MEM_ALIGN((void *)((u8_t *)q + SIZEOF_STRUCT_PBUF + offset))
 255:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****                                rem_len, qlen, type, 0);
 256:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         LWIP_ASSERT("pbuf_alloc: pbuf q->payload properly aligned",
 257:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****                     ((mem_ptr_t)q->payload % MEM_ALIGNMENT) == 0);
 258:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         LWIP_ASSERT("PBUF_POOL_BUFSIZE must be bigger than MEM_ALIGNMENT",
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 7


 259:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****                     (PBUF_POOL_BUFSIZE_ALIGNED - LWIP_MEM_ALIGN_SIZE(offset)) > 0 );
 260:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         if (p == NULL) {
 261:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****           /* allocated head of pbuf chain (into p) */
 262:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****           p = q;
 263:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         } else {
 264:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****           /* make previous pbuf point to this pbuf */
 265:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****           last->next = q;
 266:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         }
 267:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         last = q;
 268:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         rem_len = (u16_t)(rem_len - qlen);
 269:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         offset = 0;
 270:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       } while (rem_len > 0);
 271:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       break;
 272:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     }
 273:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     case PBUF_RAM: {
 274:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       u16_t payload_len = (u16_t)(LWIP_MEM_ALIGN_SIZE(offset) + LWIP_MEM_ALIGN_SIZE(length));
 275:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       mem_size_t alloc_len = (mem_size_t)(LWIP_MEM_ALIGN_SIZE(SIZEOF_STRUCT_PBUF) + payload_len);
 276:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 277:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       /* bug #50040: Check for integer overflow when calculating alloc_len */
 278:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       if ((payload_len < LWIP_MEM_ALIGN_SIZE(length)) ||
 279:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****           (alloc_len < LWIP_MEM_ALIGN_SIZE(length))) {
 280:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         return NULL;
 281:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       }
 282:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 283:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       /* If pbuf is to be allocated in RAM, allocate memory for it. */
 284:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       p = (struct pbuf *)mem_malloc(alloc_len);
 285:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       if (p == NULL) {
 286:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         return NULL;
 287:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       }
 288:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       pbuf_init_alloced_pbuf(p, LWIP_MEM_ALIGN((void *)((u8_t *)p + SIZEOF_STRUCT_PBUF + offset)),
 289:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****                              length, length, type, 0);
 290:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       LWIP_ASSERT("pbuf_alloc: pbuf->payload properly aligned",
 291:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****                   ((mem_ptr_t)p->payload % MEM_ALIGNMENT) == 0);
 292:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       break;
 293:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     }
 294:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     default:
 295:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       LWIP_ASSERT("pbuf_alloc: erroneous type", 0);
 296:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       return NULL;
 297:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
 298:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_DEBUGF(PBUF_DEBUG | LWIP_DBG_TRACE, ("pbuf_alloc(length=%"U16_F") == %p\n", length, (void *)
 299:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   return p;
 300:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** }
 301:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 302:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** /**
 303:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @ingroup pbuf
 304:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * Allocates a pbuf for referenced data.
 305:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * Referenced data can be volatile (PBUF_REF) or long-lived (PBUF_ROM).
 306:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
 307:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * The actual memory allocated for the pbuf is determined by the
 308:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * layer at which the pbuf is allocated and the requested size
 309:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * (from the size parameter).
 310:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
 311:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param payload referenced payload
 312:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param length size of the pbuf's payload
 313:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param type this parameter decides how and where the pbuf
 314:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * should be allocated as follows:
 315:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 8


 316:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * - PBUF_ROM: It is assumed that the memory used is really
 317:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *             similar to ROM in that it is immutable and will not be
 318:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *             changed. Memory which is dynamic should generally not
 319:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *             be attached to PBUF_ROM pbufs. Use PBUF_REF instead.
 320:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * - PBUF_REF: It is assumed that the pbuf is only
 321:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *             being used in a single thread. If the pbuf gets queued,
 322:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *             then pbuf_take should be called to copy the buffer.
 323:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
 324:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @return the allocated pbuf.
 325:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  */
 326:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** struct pbuf *
 327:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** pbuf_alloc_reference(void *payload, u16_t length, pbuf_type type)
 328:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** {
 329:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   struct pbuf *p;
 330:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ASSERT("invalid pbuf_type", (type == PBUF_REF) || (type == PBUF_ROM));
 331:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* only allocate memory for the pbuf structure */
 332:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   p = (struct pbuf *)memp_malloc(MEMP_PBUF);
 333:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   if (p == NULL) {
 334:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     LWIP_DEBUGF(PBUF_DEBUG | LWIP_DBG_LEVEL_SERIOUS,
 335:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****                 ("pbuf_alloc_reference: Could not allocate MEMP_PBUF for PBUF_%s.\n",
 336:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****                  (type == PBUF_ROM) ? "ROM" : "REF"));
 337:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     return NULL;
 338:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
 339:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   pbuf_init_alloced_pbuf(p, payload, length, length, type, 0);
 340:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   return p;
 341:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** }
 342:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 343:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 344:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** #if LWIP_SUPPORT_CUSTOM_PBUF
 345:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** /**
 346:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @ingroup pbuf
 347:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * Initialize a custom pbuf (already allocated).
 348:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * Example of custom pbuf usage: @ref zerocopyrx
 349:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
 350:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param l header size
 351:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param length size of the pbuf's payload
 352:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param type type of the pbuf (only used to treat the pbuf accordingly, as
 353:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *        this function allocates no memory)
 354:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param p pointer to the custom pbuf to initialize (already allocated)
 355:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param payload_mem pointer to the buffer that is used for payload and headers,
 356:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *        must be at least big enough to hold 'length' plus the header size,
 357:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *        may be NULL if set later.
 358:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *        ATTENTION: The caller is responsible for correct alignment of this buffer!!
 359:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param payload_mem_len the size of the 'payload_mem' buffer, must be at least
 360:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *        big enough to hold 'length' plus the header size
 361:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  */
 362:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** struct pbuf *
 363:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** pbuf_alloced_custom(pbuf_layer l, u16_t length, pbuf_type type, struct pbuf_custom *p,
 364:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****                     void *payload_mem, u16_t payload_mem_len)
 365:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** {
 366:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   u16_t offset = (u16_t)l;
 367:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   void *payload;
 368:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_DEBUGF(PBUF_DEBUG | LWIP_DBG_TRACE, ("pbuf_alloced_custom(length=%"U16_F")\n", length));
 369:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 370:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   if (LWIP_MEM_ALIGN_SIZE(offset) + length > payload_mem_len) {
 371:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     LWIP_DEBUGF(PBUF_DEBUG | LWIP_DBG_LEVEL_WARNING, ("pbuf_alloced_custom(length=%"U16_F") buffer 
 372:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     return NULL;
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 9


 373:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
 374:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 375:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   if (payload_mem != NULL) {
 376:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     payload = (u8_t *)payload_mem + LWIP_MEM_ALIGN_SIZE(offset);
 377:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   } else {
 378:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     payload = NULL;
 379:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
 380:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   pbuf_init_alloced_pbuf(&p->pbuf, payload, length, length, type, PBUF_FLAG_IS_CUSTOM);
 381:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   return &p->pbuf;
 382:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** }
 383:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** #endif /* LWIP_SUPPORT_CUSTOM_PBUF */
 384:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 385:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** /**
 386:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @ingroup pbuf
 387:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * Shrink a pbuf chain to a desired length.
 388:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
 389:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param p pbuf to shrink.
 390:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param new_len desired new length of pbuf chain
 391:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
 392:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * Depending on the desired length, the first few pbufs in a chain might
 393:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * be skipped and left unchanged. The new last pbuf in the chain will be
 394:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * resized, and any remaining pbufs will be freed.
 395:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
 396:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @note If the pbuf is ROM/REF, only the ->tot_len and ->len fields are adjusted.
 397:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @note May not be called on a packet queue.
 398:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
 399:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @note Despite its name, pbuf_realloc cannot grow the size of a pbuf (chain).
 400:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  */
 401:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** void
 402:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** pbuf_realloc(struct pbuf *p, u16_t new_len)
 403:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** {
 404:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   struct pbuf *q;
 405:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   u16_t rem_len; /* remaining length */
 406:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   u16_t shrink;
 407:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 408:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ASSERT("pbuf_realloc: p != NULL", p != NULL);
 409:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 410:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* desired length larger than current length? */
 411:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   if (new_len >= p->tot_len) {
 412:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* enlarging not yet supported */
 413:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     return;
 414:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
 415:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 416:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* the pbuf chain grows by (new_len - p->tot_len) bytes
 417:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****    * (which may be negative in case of shrinking) */
 418:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   shrink = (u16_t)(p->tot_len - new_len);
 419:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 420:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* first, step over any pbufs that should remain in the chain */
 421:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   rem_len = new_len;
 422:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   q = p;
 423:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* should this pbuf be kept? */
 424:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   while (rem_len > q->len) {
 425:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* decrease remaining length by pbuf length */
 426:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     rem_len = (u16_t)(rem_len - q->len);
 427:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* decrease total length indicator */
 428:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     q->tot_len = (u16_t)(q->tot_len - shrink);
 429:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* proceed to next pbuf in chain */
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 10


 430:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     q = q->next;
 431:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     LWIP_ASSERT("pbuf_realloc: q != NULL", q != NULL);
 432:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
 433:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* we have now reached the new last pbuf (in q) */
 434:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* rem_len == desired length for pbuf q */
 435:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 436:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* shrink allocated memory for PBUF_RAM */
 437:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* (other types merely adjust their length fields */
 438:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   if (pbuf_match_allocsrc(q, PBUF_TYPE_ALLOC_SRC_MASK_STD_HEAP) && (rem_len != q->len)
 439:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** #if LWIP_SUPPORT_CUSTOM_PBUF
 440:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       && ((q->flags & PBUF_FLAG_IS_CUSTOM) == 0)
 441:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** #endif /* LWIP_SUPPORT_CUSTOM_PBUF */
 442:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****      ) {
 443:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* reallocate and adjust the length of the pbuf that will be split */
 444:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     q = (struct pbuf *)mem_trim(q, (mem_size_t)(((u8_t *)q->payload - (u8_t *)q) + rem_len));
 445:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     LWIP_ASSERT("mem_trim returned q == NULL", q != NULL);
 446:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
 447:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* adjust length fields for new last pbuf */
 448:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   q->len = rem_len;
 449:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   q->tot_len = q->len;
 450:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 451:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* any remaining pbufs in chain? */
 452:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   if (q->next != NULL) {
 453:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* free remaining pbufs in chain */
 454:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     pbuf_free(q->next);
 455:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
 456:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* q is last packet in chain */
 457:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   q->next = NULL;
 458:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 459:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** }
 460:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 461:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** /**
 462:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * Adjusts the payload pointer to reveal headers in the payload.
 463:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @see pbuf_add_header.
 464:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
 465:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param p pbuf to change the header size.
 466:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param header_size_increment Number of bytes to increment header size.
 467:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param force Allow 'header_size_increment > 0' for PBUF_REF/PBUF_ROM types
 468:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
 469:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @return non-zero on failure, zero on success.
 470:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
 471:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  */
 472:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** static u8_t
 473:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** pbuf_add_header_impl(struct pbuf *p, size_t header_size_increment, u8_t force)
 474:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** {
 475:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   u16_t type_internal;
 476:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   void *payload;
 477:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   u16_t increment_magnitude;
 478:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 479:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ASSERT("p != NULL", p != NULL);
 480:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   if ((p == NULL) || (header_size_increment > 0xFFFF)) {
 481:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     return 1;
 482:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
 483:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   if (header_size_increment == 0) {
 484:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     return 0;
 485:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
 486:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 11


 487:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   increment_magnitude = (u16_t)header_size_increment;
 488:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* Do not allow tot_len to wrap as a result. */
 489:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   if ((u16_t)(increment_magnitude + p->tot_len) < increment_magnitude) {
 490:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     return 1;
 491:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
 492:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 493:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   type_internal = p->type_internal;
 494:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 495:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* pbuf types containing payloads? */
 496:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   if (type_internal & PBUF_TYPE_FLAG_STRUCT_DATA_CONTIGUOUS) {
 497:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* set new payload pointer */
 498:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     payload = (u8_t *)p->payload - header_size_increment;
 499:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* boundary check fails? */
 500:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     if ((u8_t *)payload < (u8_t *)p + SIZEOF_STRUCT_PBUF) {
 501:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       LWIP_DEBUGF( PBUF_DEBUG | LWIP_DBG_TRACE,
 502:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****                    ("pbuf_add_header: failed as %p < %p (not enough space for new header size)\n",
 503:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****                     (void *)payload, (void *)((u8_t *)p + SIZEOF_STRUCT_PBUF)));
 504:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       /* bail out unsuccessfully */
 505:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       return 1;
 506:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     }
 507:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* pbuf types referring to external payloads? */
 508:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   } else {
 509:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* hide a header in the payload? */
 510:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     if (force) {
 511:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       payload = (u8_t *)p->payload - header_size_increment;
 512:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     } else {
 513:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       /* cannot expand payload to front (yet!)
 514:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****        * bail out unsuccessfully */
 515:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       return 1;
 516:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     }
 517:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
 518:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_DEBUGF(PBUF_DEBUG | LWIP_DBG_TRACE, ("pbuf_add_header: old %p new %p (%"U16_F")\n",
 519:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****               (void *)p->payload, (void *)payload, increment_magnitude));
 520:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 521:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* modify pbuf fields */
 522:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   p->payload = payload;
 523:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   p->len = (u16_t)(p->len + increment_magnitude);
 524:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   p->tot_len = (u16_t)(p->tot_len + increment_magnitude);
 525:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 526:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 527:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   return 0;
 528:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** }
 529:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 530:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** /**
 531:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * Adjusts the payload pointer to reveal headers in the payload.
 532:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
 533:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * Adjusts the ->payload pointer so that space for a header
 534:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * appears in the pbuf payload.
 535:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
 536:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * The ->payload, ->tot_len and ->len fields are adjusted.
 537:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
 538:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param p pbuf to change the header size.
 539:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param header_size_increment Number of bytes to increment header size which
 540:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *          increases the size of the pbuf. New space is on the front.
 541:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *          If header_size_increment is 0, this function does nothing and returns successful.
 542:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
 543:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * PBUF_ROM and PBUF_REF type buffers cannot have their sizes increased, so
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 12


 544:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * the call will fail. A check is made that the increase in header size does
 545:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * not move the payload pointer in front of the start of the buffer.
 546:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
 547:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @return non-zero on failure, zero on success.
 548:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
 549:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  */
 550:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** u8_t
 551:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** pbuf_add_header(struct pbuf *p, size_t header_size_increment)
 552:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** {
 553:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   return pbuf_add_header_impl(p, header_size_increment, 0);
 554:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** }
 555:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 556:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** /**
 557:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * Same as @ref pbuf_add_header but does not check if 'header_size > 0' is allowed.
 558:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * This is used internally only, to allow PBUF_REF for RX.
 559:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  */
 560:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** u8_t
 561:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** pbuf_add_header_force(struct pbuf *p, size_t header_size_increment)
 562:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** {
 563:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   return pbuf_add_header_impl(p, header_size_increment, 1);
 564:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** }
 565:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 566:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** /**
 567:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * Adjusts the payload pointer to hide headers in the payload.
 568:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
 569:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * Adjusts the ->payload pointer so that space for a header
 570:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * disappears in the pbuf payload.
 571:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
 572:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * The ->payload, ->tot_len and ->len fields are adjusted.
 573:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
 574:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param p pbuf to change the header size.
 575:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param header_size_decrement Number of bytes to decrement header size which
 576:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *          decreases the size of the pbuf.
 577:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *          If header_size_decrement is 0, this function does nothing and returns successful.
 578:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @return non-zero on failure, zero on success.
 579:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
 580:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  */
 581:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** u8_t
 582:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** pbuf_remove_header(struct pbuf *p, size_t header_size_decrement)
 583:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** {
 584:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   void *payload;
 585:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   u16_t increment_magnitude;
 586:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 587:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ASSERT("p != NULL", p != NULL);
 588:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   if ((p == NULL) || (header_size_decrement > 0xFFFF)) {
 589:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     return 1;
 590:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
 591:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   if (header_size_decrement == 0) {
 592:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     return 0;
 593:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
 594:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 595:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   increment_magnitude = (u16_t)header_size_decrement;
 596:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* Check that we aren't going to move off the end of the pbuf */
 597:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ERROR("increment_magnitude <= p->len", (increment_magnitude <= p->len), return 1;);
 598:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 599:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* remember current payload pointer */
 600:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   payload = p->payload;
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 13


 601:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_UNUSED_ARG(payload); /* only used in LWIP_DEBUGF below */
 602:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 603:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* increase payload pointer (guarded by length check above) */
 604:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   p->payload = (u8_t *)p->payload + header_size_decrement;
 605:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* modify pbuf length fields */
 606:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   p->len = (u16_t)(p->len - increment_magnitude);
 607:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   p->tot_len = (u16_t)(p->tot_len - increment_magnitude);
 608:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 609:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_DEBUGF(PBUF_DEBUG | LWIP_DBG_TRACE, ("pbuf_remove_header: old %p new %p (%"U16_F")\n",
 610:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****               (void *)payload, (void *)p->payload, increment_magnitude));
 611:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 612:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   return 0;
 613:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** }
 614:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 615:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** static u8_t
 616:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** pbuf_header_impl(struct pbuf *p, s16_t header_size_increment, u8_t force)
 617:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** {
 618:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   if (header_size_increment < 0) {
 619:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     return pbuf_remove_header(p, (size_t) - header_size_increment);
 620:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   } else {
 621:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     return pbuf_add_header_impl(p, (size_t)header_size_increment, force);
 622:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
 623:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** }
 624:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 625:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** /**
 626:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * Adjusts the payload pointer to hide or reveal headers in the payload.
 627:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
 628:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * Adjusts the ->payload pointer so that space for a header
 629:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * (dis)appears in the pbuf payload.
 630:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
 631:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * The ->payload, ->tot_len and ->len fields are adjusted.
 632:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
 633:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param p pbuf to change the header size.
 634:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param header_size_increment Number of bytes to increment header size which
 635:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * increases the size of the pbuf. New space is on the front.
 636:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * (Using a negative value decreases the header size.)
 637:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * If header_size_increment is 0, this function does nothing and returns successful.
 638:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
 639:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * PBUF_ROM and PBUF_REF type buffers cannot have their sizes increased, so
 640:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * the call will fail. A check is made that the increase in header size does
 641:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * not move the payload pointer in front of the start of the buffer.
 642:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @return non-zero on failure, zero on success.
 643:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
 644:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  */
 645:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** u8_t
 646:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** pbuf_header(struct pbuf *p, s16_t header_size_increment)
 647:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** {
 648:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   return pbuf_header_impl(p, header_size_increment, 0);
 649:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** }
 650:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 651:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** /**
 652:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * Same as pbuf_header but does not check if 'header_size > 0' is allowed.
 653:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * This is used internally only, to allow PBUF_REF for RX.
 654:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  */
 655:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** u8_t
 656:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** pbuf_header_force(struct pbuf *p, s16_t header_size_increment)
 657:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** {
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 14


 658:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   return pbuf_header_impl(p, header_size_increment, 1);
 659:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** }
 660:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 661:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** /** Similar to pbuf_header(-size) but de-refs header pbufs for (size >= p->len)
 662:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
 663:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param q pbufs to operate on
 664:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param size The number of bytes to remove from the beginning of the pbuf list.
 665:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *             While size >= p->len, pbufs are freed.
 666:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *        ATTENTION: this is the opposite direction as @ref pbuf_header, but
 667:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *                   takes an u16_t not s16_t!
 668:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @return the new head pbuf
 669:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  */
 670:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** struct pbuf *
 671:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** pbuf_free_header(struct pbuf *q, u16_t size)
 672:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** {
 673:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   struct pbuf *p = q;
 674:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   u16_t free_left = size;
 675:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   while (free_left && p) {
 676:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     if (free_left >= p->len) {
 677:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       struct pbuf *f = p;
 678:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       free_left = (u16_t)(free_left - p->len);
 679:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       p = p->next;
 680:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       f->next = 0;
 681:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       pbuf_free(f);
 682:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     } else {
 683:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       pbuf_remove_header(p, free_left);
 684:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       free_left = 0;
 685:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     }
 686:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
 687:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   return p;
 688:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** }
 689:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 690:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** /**
 691:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @ingroup pbuf
 692:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * Dereference a pbuf chain or queue and deallocate any no-longer-used
 693:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * pbufs at the head of this chain or queue.
 694:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
 695:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * Decrements the pbuf reference count. If it reaches zero, the pbuf is
 696:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * deallocated.
 697:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
 698:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * For a pbuf chain, this is repeated for each pbuf in the chain,
 699:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * up to the first pbuf which has a non-zero reference count after
 700:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * decrementing. So, when all reference counts are one, the whole
 701:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * chain is free'd.
 702:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
 703:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param p The pbuf (chain) to be dereferenced.
 704:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
 705:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @return the number of pbufs that were de-allocated
 706:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * from the head of the chain.
 707:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
 708:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @note MUST NOT be called on a packet queue (Not verified to work yet).
 709:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @note the reference counter of a pbuf equals the number of pointers
 710:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * that refer to the pbuf (or into the pbuf).
 711:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
 712:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @internal examples:
 713:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
 714:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * Assuming existing chains a->b->c with the following reference
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 15


 715:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * counts, calling pbuf_free(a) results in:
 716:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
 717:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * 1->2->3 becomes ...1->3
 718:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * 3->3->3 becomes 2->3->3
 719:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * 1->1->2 becomes ......1
 720:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * 2->1->1 becomes 1->1->1
 721:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * 1->1->1 becomes .......
 722:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
 723:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  */
 724:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** u8_t
 725:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** pbuf_free(struct pbuf *p)
 726:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** {
 727:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   u8_t alloc_src;
 728:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   struct pbuf *q;
 729:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   u8_t count;
 730:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 731:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   if (p == NULL) {
 732:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     LWIP_ASSERT("p != NULL", p != NULL);
 733:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* if assertions are disabled, proceed with debug output */
 734:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     LWIP_DEBUGF(PBUF_DEBUG | LWIP_DBG_LEVEL_SERIOUS,
 735:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****                 ("pbuf_free(p == NULL) was called.\n"));
 736:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     return 0;
 737:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
 738:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_DEBUGF(PBUF_DEBUG | LWIP_DBG_TRACE, ("pbuf_free(%p)\n", (void *)p));
 739:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 740:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   PERF_START;
 741:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 742:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   count = 0;
 743:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* de-allocate all consecutive pbufs from the head of the chain that
 744:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****    * obtain a zero reference count after decrementing*/
 745:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   while (p != NULL) {
 746:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     LWIP_PBUF_REF_T ref;
 747:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     SYS_ARCH_DECL_PROTECT(old_level);
 748:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* Since decrementing ref cannot be guaranteed to be a single machine operation
 749:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****      * we must protect it. We put the new ref into a local variable to prevent
 750:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****      * further protection. */
 751:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     SYS_ARCH_PROTECT(old_level);
 752:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* all pbufs in a chain are referenced at least once */
 753:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     LWIP_ASSERT("pbuf_free: p->ref > 0", p->ref > 0);
 754:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* decrease reference count (number of pointers to pbuf) */
 755:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     ref = --(p->ref);
 756:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     SYS_ARCH_UNPROTECT(old_level);
 757:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* this pbuf is no longer referenced to? */
 758:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     if (ref == 0) {
 759:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       /* remember next pbuf in chain for next iteration */
 760:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       q = p->next;
 761:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       LWIP_DEBUGF( PBUF_DEBUG | LWIP_DBG_TRACE, ("pbuf_free: deallocating %p\n", (void *)p));
 762:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       alloc_src = pbuf_get_allocsrc(p);
 763:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** #if LWIP_SUPPORT_CUSTOM_PBUF
 764:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       /* is this a custom pbuf? */
 765:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       if ((p->flags & PBUF_FLAG_IS_CUSTOM) != 0) {
 766:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         struct pbuf_custom *pc = (struct pbuf_custom *)p;
 767:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         LWIP_ASSERT("pc->custom_free_function != NULL", pc->custom_free_function != NULL);
 768:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         pc->custom_free_function(p);
 769:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       } else
 770:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** #endif /* LWIP_SUPPORT_CUSTOM_PBUF */
 771:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       {
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 16


 772:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         /* is this a pbuf from the pool? */
 773:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         if (alloc_src == PBUF_TYPE_ALLOC_SRC_MASK_STD_MEMP_PBUF_POOL) {
 774:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****           memp_free(MEMP_PBUF_POOL, p);
 775:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****           /* is this a ROM or RAM referencing pbuf? */
 776:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         } else if (alloc_src == PBUF_TYPE_ALLOC_SRC_MASK_STD_MEMP_PBUF) {
 777:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****           memp_free(MEMP_PBUF, p);
 778:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****           /* type == PBUF_RAM */
 779:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         } else if (alloc_src == PBUF_TYPE_ALLOC_SRC_MASK_STD_HEAP) {
 780:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****           mem_free(p);
 781:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         } else {
 782:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****           /* @todo: support freeing other types */
 783:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****           LWIP_ASSERT("invalid pbuf type", 0);
 784:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         }
 785:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       }
 786:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       count++;
 787:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       /* proceed to next pbuf */
 788:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       p = q;
 789:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       /* p->ref > 0, this pbuf is still referenced to */
 790:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       /* (and so the remaining pbufs in chain as well) */
 791:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     } else {
 792:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       LWIP_DEBUGF( PBUF_DEBUG | LWIP_DBG_TRACE, ("pbuf_free: %p has ref %"U16_F", ending here.\n", 
 793:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       /* stop walking through the chain */
 794:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       p = NULL;
 795:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     }
 796:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
 797:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   PERF_STOP("pbuf_free");
 798:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* return number of de-allocated pbufs */
 799:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   return count;
 800:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** }
 801:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 802:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** /**
 803:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * Count number of pbufs in a chain
 804:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
 805:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param p first pbuf of chain
 806:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @return the number of pbufs in a chain
 807:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  */
 808:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** u16_t
 809:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** pbuf_clen(const struct pbuf *p)
 810:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** {
 811:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   u16_t len;
 812:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 813:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   len = 0;
 814:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   while (p != NULL) {
 815:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     ++len;
 816:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     p = p->next;
 817:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
 818:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   return len;
 819:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** }
 820:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 821:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** /**
 822:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @ingroup pbuf
 823:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * Increment the reference count of the pbuf.
 824:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
 825:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param p pbuf to increase reference counter of
 826:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
 827:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  */
 828:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** void
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 17


 829:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** pbuf_ref(struct pbuf *p)
 830:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** {
 831:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* pbuf given? */
 832:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   if (p != NULL) {
 833:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     SYS_ARCH_SET(p->ref, (LWIP_PBUF_REF_T)(p->ref + 1));
 834:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     LWIP_ASSERT("pbuf ref overflow", p->ref > 0);
 835:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
 836:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** }
 837:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 838:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** /**
 839:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @ingroup pbuf
 840:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * Concatenate two pbufs (each may be a pbuf chain) and take over
 841:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * the caller's reference of the tail pbuf.
 842:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
 843:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @note The caller MAY NOT reference the tail pbuf afterwards.
 844:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * Use pbuf_chain() for that purpose.
 845:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
 846:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * This function explicitly does not check for tot_len overflow to prevent
 847:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * failing to queue too long pbufs. This can produce invalid pbufs, so
 848:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * handle with care!
 849:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
 850:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @see pbuf_chain()
 851:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  */
 852:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** void
 853:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** pbuf_cat(struct pbuf *h, struct pbuf *t)
 854:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** {
 855:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   struct pbuf *p;
 856:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 857:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ERROR("(h != NULL) && (t != NULL) (programmer violates API)",
 858:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****              ((h != NULL) && (t != NULL)), return;);
 859:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 860:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* proceed to last pbuf of chain */
 861:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   for (p = h; p->next != NULL; p = p->next) {
 862:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* add total length of second chain to all totals of first chain */
 863:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     p->tot_len = (u16_t)(p->tot_len + t->tot_len);
 864:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
 865:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* { p is last pbuf of first h chain, p->next == NULL } */
 866:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ASSERT("p->tot_len == p->len (of last pbuf in chain)", p->tot_len == p->len);
 867:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ASSERT("p->next == NULL", p->next == NULL);
 868:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* add total length of second chain to last pbuf total of first chain */
 869:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   p->tot_len = (u16_t)(p->tot_len + t->tot_len);
 870:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* chain last pbuf of head (p) with first of tail (t) */
 871:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   p->next = t;
 872:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* p->next now references t, but the caller will drop its reference to t,
 873:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****    * so netto there is no change to the reference count of t.
 874:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****    */
 875:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** }
 876:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 877:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** /**
 878:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @ingroup pbuf
 879:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * Chain two pbufs (or pbuf chains) together.
 880:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
 881:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * The caller MUST call pbuf_free(t) once it has stopped
 882:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * using it. Use pbuf_cat() instead if you no longer use t.
 883:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
 884:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param h head pbuf (chain)
 885:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param t tail pbuf (chain)
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 18


 886:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @note The pbufs MUST belong to the same packet.
 887:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @note MAY NOT be called on a packet queue.
 888:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
 889:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * The ->tot_len fields of all pbufs of the head chain are adjusted.
 890:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * The ->next field of the last pbuf of the head chain is adjusted.
 891:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * The ->ref field of the first pbuf of the tail chain is adjusted.
 892:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
 893:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  */
 894:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** void
 895:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** pbuf_chain(struct pbuf *h, struct pbuf *t)
 896:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** {
 897:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   pbuf_cat(h, t);
 898:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* t is now referenced by h */
 899:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   pbuf_ref(t);
 900:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_DEBUGF(PBUF_DEBUG | LWIP_DBG_TRACE, ("pbuf_chain: %p references %p\n", (void *)h, (void *)t)
 901:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** }
 902:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 903:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** /**
 904:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * Dechains the first pbuf from its succeeding pbufs in the chain.
 905:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
 906:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * Makes p->tot_len field equal to p->len.
 907:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param p pbuf to dechain
 908:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @return remainder of the pbuf chain, or NULL if it was de-allocated.
 909:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @note May not be called on a packet queue.
 910:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  */
 911:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** struct pbuf *
 912:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** pbuf_dechain(struct pbuf *p)
 913:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** {
 914:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   struct pbuf *q;
 915:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   u8_t tail_gone = 1;
 916:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* tail */
 917:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   q = p->next;
 918:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* pbuf has successor in chain? */
 919:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   if (q != NULL) {
 920:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* assert tot_len invariant: (p->tot_len == p->len + (p->next? p->next->tot_len: 0) */
 921:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     LWIP_ASSERT("p->tot_len == p->len + q->tot_len", q->tot_len == p->tot_len - p->len);
 922:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* enforce invariant if assertion is disabled */
 923:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     q->tot_len = (u16_t)(p->tot_len - p->len);
 924:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* decouple pbuf from remainder */
 925:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     p->next = NULL;
 926:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* total length of pbuf p is its own length only */
 927:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     p->tot_len = p->len;
 928:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* q is no longer referenced by p, free it */
 929:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     LWIP_DEBUGF(PBUF_DEBUG | LWIP_DBG_TRACE, ("pbuf_dechain: unreferencing %p\n", (void *)q));
 930:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     tail_gone = pbuf_free(q);
 931:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     if (tail_gone > 0) {
 932:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       LWIP_DEBUGF(PBUF_DEBUG | LWIP_DBG_TRACE,
 933:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****                   ("pbuf_dechain: deallocated %p (as it is no longer referenced)\n", (void *)q));
 934:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     }
 935:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* return remaining tail or NULL if deallocated */
 936:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
 937:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* assert tot_len invariant: (p->tot_len == p->len + (p->next? p->next->tot_len: 0) */
 938:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ASSERT("p->tot_len == p->len", p->tot_len == p->len);
 939:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   return ((tail_gone > 0) ? NULL : q);
 940:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** }
 941:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 942:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** /**
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 19


 943:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @ingroup pbuf
 944:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * Create PBUF_RAM copies of pbufs.
 945:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
 946:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * Used to queue packets on behalf of the lwIP stack, such as
 947:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * ARP based queueing.
 948:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
 949:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @note You MUST explicitly use p = pbuf_take(p);
 950:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
 951:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @note Only one packet is copied, no packet queue!
 952:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
 953:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param p_to pbuf destination of the copy
 954:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param p_from pbuf source of the copy
 955:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
 956:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @return ERR_OK if pbuf was copied
 957:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *         ERR_ARG if one of the pbufs is NULL or p_to is not big
 958:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *                 enough to hold p_from
 959:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  */
 960:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** err_t
 961:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** pbuf_copy(struct pbuf *p_to, const struct pbuf *p_from)
 962:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** {
 963:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   size_t offset_to = 0, offset_from = 0, len;
 964:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 965:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_DEBUGF(PBUF_DEBUG | LWIP_DBG_TRACE, ("pbuf_copy(%p, %p)\n",
 966:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****               (const void *)p_to, (const void *)p_from));
 967:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 968:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* is the target big enough to hold the source? */
 969:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ERROR("pbuf_copy: target not big enough to hold source", ((p_to != NULL) &&
 970:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****              (p_from != NULL) && (p_to->tot_len >= p_from->tot_len)), return ERR_ARG;);
 971:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 972:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* iterate through pbuf chain */
 973:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   do {
 974:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* copy one part of the original chain */
 975:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     if ((p_to->len - offset_to) >= (p_from->len - offset_from)) {
 976:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       /* complete current p_from fits into current p_to */
 977:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       len = p_from->len - offset_from;
 978:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     } else {
 979:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       /* current p_from does not fit into current p_to */
 980:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       len = p_to->len - offset_to;
 981:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     }
 982:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     MEMCPY((u8_t *)p_to->payload + offset_to, (u8_t *)p_from->payload + offset_from, len);
 983:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     offset_to += len;
 984:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     offset_from += len;
 985:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     LWIP_ASSERT("offset_to <= p_to->len", offset_to <= p_to->len);
 986:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     LWIP_ASSERT("offset_from <= p_from->len", offset_from <= p_from->len);
 987:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     if (offset_from >= p_from->len) {
 988:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       /* on to next p_from (if any) */
 989:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       offset_from = 0;
 990:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       p_from = p_from->next;
 991:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     }
 992:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     if (offset_to == p_to->len) {
 993:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       /* on to next p_to (if any) */
 994:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       offset_to = 0;
 995:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       p_to = p_to->next;
 996:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       LWIP_ERROR("p_to != NULL", (p_to != NULL) || (p_from == NULL), return ERR_ARG;);
 997:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     }
 998:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 999:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     if ((p_from != NULL) && (p_from->len == p_from->tot_len)) {
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 20


1000:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       /* don't copy more than one packet! */
1001:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       LWIP_ERROR("pbuf_copy() does not allow packet queues!",
1002:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****                  (p_from->next == NULL), return ERR_VAL;);
1003:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     }
1004:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     if ((p_to != NULL) && (p_to->len == p_to->tot_len)) {
1005:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       /* don't copy more than one packet! */
1006:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       LWIP_ERROR("pbuf_copy() does not allow packet queues!",
1007:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****                  (p_to->next == NULL), return ERR_VAL;);
1008:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     }
1009:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   } while (p_from);
1010:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_DEBUGF(PBUF_DEBUG | LWIP_DBG_TRACE, ("pbuf_copy: end of chain reached.\n"));
1011:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   return ERR_OK;
1012:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** }
1013:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
1014:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** /**
1015:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @ingroup pbuf
1016:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * Copy (part of) the contents of a packet buffer
1017:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * to an application supplied buffer.
1018:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
1019:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param buf the pbuf from which to copy data
1020:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param dataptr the application supplied buffer
1021:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param len length of data to copy (dataptr must be big enough). No more
1022:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * than buf->tot_len will be copied, irrespective of len
1023:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param offset offset into the packet buffer from where to begin copying len bytes
1024:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @return the number of bytes copied, or 0 on failure
1025:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  */
1026:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** u16_t
1027:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** pbuf_copy_partial(const struct pbuf *buf, void *dataptr, u16_t len, u16_t offset)
1028:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** {
1029:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   const struct pbuf *p;
1030:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   u16_t left = 0;
1031:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   u16_t buf_copy_len;
1032:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   u16_t copied_total = 0;
1033:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
1034:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ERROR("pbuf_copy_partial: invalid buf", (buf != NULL), return 0;);
1035:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ERROR("pbuf_copy_partial: invalid dataptr", (dataptr != NULL), return 0;);
1036:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
1037:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* Note some systems use byte copy if dataptr or one of the pbuf payload pointers are unaligned. 
1038:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   for (p = buf; len != 0 && p != NULL; p = p->next) {
1039:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     if ((offset != 0) && (offset >= p->len)) {
1040:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       /* don't copy from this buffer -> on to the next */
1041:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       offset = (u16_t)(offset - p->len);
1042:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     } else {
1043:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       /* copy from this buffer. maybe only partially. */
1044:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       buf_copy_len = (u16_t)(p->len - offset);
1045:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       if (buf_copy_len > len) {
1046:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         buf_copy_len = len;
1047:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       }
1048:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       /* copy the necessary parts of the buffer */
1049:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       MEMCPY(&((char *)dataptr)[left], &((char *)p->payload)[offset], buf_copy_len);
1050:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       copied_total = (u16_t)(copied_total + buf_copy_len);
1051:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       left = (u16_t)(left + buf_copy_len);
1052:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       len = (u16_t)(len - buf_copy_len);
1053:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       offset = 0;
1054:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     }
1055:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
1056:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   return copied_total;
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 21


1057:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** }
1058:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
1059:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** /**
1060:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @ingroup pbuf
1061:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * Get part of a pbuf's payload as contiguous memory. The returned memory is
1062:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * either a pointer into the pbuf's payload or, if split over multiple pbufs,
1063:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * a copy into the user-supplied buffer.
1064:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
1065:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param p the pbuf from which to copy data
1066:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param buffer the application supplied buffer
1067:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param bufsize size of the application supplied buffer
1068:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param len length of data to copy (dataptr must be big enough). No more
1069:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * than buf->tot_len will be copied, irrespective of len
1070:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param offset offset into the packet buffer from where to begin copying len bytes
1071:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @return the number of bytes copied, or 0 on failure
1072:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  */
1073:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** void *
1074:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** pbuf_get_contiguous(const struct pbuf *p, void *buffer, size_t bufsize, u16_t len, u16_t offset)
1075:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** {
1076:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   const struct pbuf *q;
1077:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   u16_t out_offset;
1078:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
1079:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ERROR("pbuf_get_contiguous: invalid buf", (p != NULL), return NULL;);
1080:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ERROR("pbuf_get_contiguous: invalid dataptr", (buffer != NULL), return NULL;);
1081:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ERROR("pbuf_get_contiguous: invalid dataptr", (bufsize >= len), return NULL;);
1082:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
1083:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   q = pbuf_skip_const(p, offset, &out_offset);
1084:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   if (q != NULL) {
1085:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     if (q->len >= (out_offset + len)) {
1086:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       /* all data in this pbuf, return zero-copy */
1087:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       return (u8_t *)q->payload + out_offset;
1088:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     }
1089:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* need to copy */
1090:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     if (pbuf_copy_partial(q, buffer, len, out_offset) != len) {
1091:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       /* copying failed: pbuf is too short */
1092:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       return NULL;
1093:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     }
1094:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     return buffer;
1095:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
1096:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* pbuf is too short (offset does not fit in) */
1097:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   return NULL;
1098:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** }
1099:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
1100:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** #if LWIP_TCP && TCP_QUEUE_OOSEQ && LWIP_WND_SCALE
1101:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** /**
1102:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * This method modifies a 'pbuf chain', so that its total length is
1103:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * smaller than 64K. The remainder of the original pbuf chain is stored
1104:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * in *rest.
1105:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * This function never creates new pbufs, but splits an existing chain
1106:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * in two parts. The tot_len of the modified packet queue will likely be
1107:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * smaller than 64K.
1108:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * 'packet queues' are not supported by this function.
1109:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
1110:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param p the pbuf queue to be split
1111:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param rest pointer to store the remainder (after the first 64K)
1112:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  */
1113:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** void pbuf_split_64k(struct pbuf *p, struct pbuf **rest)
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 22


1114:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** {
1115:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   *rest = NULL;
1116:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   if ((p != NULL) && (p->next != NULL)) {
1117:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     u16_t tot_len_front = p->len;
1118:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     struct pbuf *i = p;
1119:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     struct pbuf *r = p->next;
1120:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
1121:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* continue until the total length (summed up as u16_t) overflows */
1122:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     while ((r != NULL) && ((u16_t)(tot_len_front + r->len) >= tot_len_front)) {
1123:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       tot_len_front = (u16_t)(tot_len_front + r->len);
1124:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       i = r;
1125:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       r = r->next;
1126:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     }
1127:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* i now points to last packet of the first segment. Set next
1128:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****        pointer to NULL */
1129:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     i->next = NULL;
1130:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
1131:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     if (r != NULL) {
1132:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       /* Update the tot_len field in the first part */
1133:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       for (i = p; i != NULL; i = i->next) {
1134:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         i->tot_len = (u16_t)(i->tot_len - r->tot_len);
1135:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         LWIP_ASSERT("tot_len/len mismatch in last pbuf",
1136:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****                     (i->next != NULL) || (i->tot_len == i->len));
1137:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       }
1138:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       if (p->flags & PBUF_FLAG_TCP_FIN) {
1139:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         r->flags |= PBUF_FLAG_TCP_FIN;
1140:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       }
1141:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
1142:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       /* tot_len field in rest does not need modifications */
1143:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       /* reference counters do not need modifications */
1144:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       *rest = r;
1145:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     }
1146:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
1147:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** }
1148:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** #endif /* LWIP_TCP && TCP_QUEUE_OOSEQ && LWIP_WND_SCALE */
1149:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
1150:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** /* Actual implementation of pbuf_skip() but returning const pointer... */
1151:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** static const struct pbuf *
1152:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** pbuf_skip_const(const struct pbuf *in, u16_t in_offset, u16_t *out_offset)
1153:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** {
  88              		.loc 1 1153 1 is_stmt 1 view -0
  89              		.cfi_startproc
  90              		@ args = 0, pretend = 0, frame = 0
  91              		@ frame_needed = 0, uses_anonymous_args = 0
  92              		@ link register save eliminated.
1154:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   u16_t offset_left = in_offset;
  93              		.loc 1 1154 3 view .LVU22
1155:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   const struct pbuf *q = in;
  94              		.loc 1 1155 3 view .LVU23
1156:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
1157:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* get the correct pbuf */
1158:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   while ((q != NULL) && (q->len <= offset_left)) {
  95              		.loc 1 1158 3 view .LVU24
  96              		.loc 1 1158 9 is_stmt 0 view .LVU25
  97 0000 02E0     		b	.L4
  98              	.LVL4:
  99              	.L6:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 23


1159:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     offset_left = (u16_t)(offset_left - q->len);
 100              		.loc 1 1159 5 is_stmt 1 view .LVU26
 101              		.loc 1 1159 17 is_stmt 0 view .LVU27
 102 0002 C91A     		subs	r1, r1, r3
 103              	.LVL5:
 104              		.loc 1 1159 17 view .LVU28
 105 0004 89B2     		uxth	r1, r1
 106              	.LVL6:
1160:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     q = q->next;
 107              		.loc 1 1160 5 is_stmt 1 view .LVU29
 108              		.loc 1 1160 7 is_stmt 0 view .LVU30
 109 0006 0068     		ldr	r0, [r0]
 110              	.LVL7:
 111              	.L4:
1158:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     offset_left = (u16_t)(offset_left - q->len);
 112              		.loc 1 1158 22 is_stmt 1 view .LVU31
 113 0008 10B1     		cbz	r0, .L5
1158:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     offset_left = (u16_t)(offset_left - q->len);
 114              		.loc 1 1158 27 is_stmt 0 discriminator 1 view .LVU32
 115 000a 4389     		ldrh	r3, [r0, #10]
1158:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     offset_left = (u16_t)(offset_left - q->len);
 116              		.loc 1 1158 22 discriminator 1 view .LVU33
 117 000c 8B42     		cmp	r3, r1
 118 000e F8D9     		bls	.L6
 119              	.L5:
1161:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
1162:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   if (out_offset != NULL) {
 120              		.loc 1 1162 3 is_stmt 1 view .LVU34
 121              		.loc 1 1162 6 is_stmt 0 view .LVU35
 122 0010 02B1     		cbz	r2, .L7
1163:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     *out_offset = offset_left;
 123              		.loc 1 1163 5 is_stmt 1 view .LVU36
 124              		.loc 1 1163 17 is_stmt 0 view .LVU37
 125 0012 1180     		strh	r1, [r2]	@ movhi
 126              	.L7:
1164:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
1165:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   return q;
 127              		.loc 1 1165 3 is_stmt 1 view .LVU38
1166:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** }
 128              		.loc 1 1166 1 is_stmt 0 view .LVU39
 129 0014 7047     		bx	lr
 130              		.cfi_endproc
 131              	.LFE199:
 133              		.section	.rodata.pbuf_add_header_impl.str1.4,"aMS",%progbits,1
 134              		.align	2
 135              	.LC0:
 136 0000 4D696464 		.ascii	"Middlewares/Third_Party/LwIP/src/core/pbuf.c\000"
 136      6C657761 
 136      7265732F 
 136      54686972 
 136      645F5061 
 137 002d 000000   		.align	2
 138              	.LC1:
 139 0030 7020213D 		.ascii	"p != NULL\000"
 139      204E554C 
 139      4C00
 140 003a 0000     		.align	2
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 24


 141              	.LC2:
 142 003c 41737365 		.ascii	"Assertion \"%s\" failed at line %d in %s\012\000"
 142      7274696F 
 142      6E202225 
 142      73222066 
 142      61696C65 
 143              		.section	.text.pbuf_add_header_impl,"ax",%progbits
 144              		.align	1
 145              		.syntax unified
 146              		.thumb
 147              		.thumb_func
 149              	pbuf_add_header_impl:
 150              	.LVL8:
 151              	.LFB182:
 474:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   u16_t type_internal;
 152              		.loc 1 474 1 is_stmt 1 view -0
 153              		.cfi_startproc
 154              		@ args = 0, pretend = 0, frame = 0
 155              		@ frame_needed = 0, uses_anonymous_args = 0
 474:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   u16_t type_internal;
 156              		.loc 1 474 1 is_stmt 0 view .LVU41
 157 0000 70B5     		push	{r4, r5, r6, lr}
 158              	.LCFI2:
 159              		.cfi_def_cfa_offset 16
 160              		.cfi_offset 4, -16
 161              		.cfi_offset 5, -12
 162              		.cfi_offset 6, -8
 163              		.cfi_offset 14, -4
 164 0002 0C46     		mov	r4, r1
 165 0004 1646     		mov	r6, r2
 475:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   void *payload;
 166              		.loc 1 475 3 is_stmt 1 view .LVU42
 476:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   u16_t increment_magnitude;
 167              		.loc 1 476 3 view .LVU43
 477:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 168              		.loc 1 477 3 view .LVU44
 479:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   if ((p == NULL) || (header_size_increment > 0xFFFF)) {
 169              		.loc 1 479 3 view .LVU45
 479:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   if ((p == NULL) || (header_size_increment > 0xFFFF)) {
 170              		.loc 1 479 3 view .LVU46
 171 0006 0546     		mov	r5, r0
 172 0008 C8B1     		cbz	r0, .L19
 173              	.LVL9:
 174              	.L9:
 479:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   if ((p == NULL) || (header_size_increment > 0xFFFF)) {
 175              		.loc 1 479 3 discriminator 3 view .LVU47
 479:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   if ((p == NULL) || (header_size_increment > 0xFFFF)) {
 176              		.loc 1 479 3 discriminator 3 view .LVU48
 480:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     return 1;
 177              		.loc 1 480 3 view .LVU49
 480:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     return 1;
 178              		.loc 1 480 6 is_stmt 0 view .LVU50
 179 000a 002D     		cmp	r5, #0
 180 000c 18BF     		it	ne
 181 000e B4F5803F 		cmpne	r4, #65536
 182 0012 24D2     		bcs	.L13
 483:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     return 0;
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 25


 183              		.loc 1 483 3 is_stmt 1 view .LVU51
 483:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     return 0;
 184              		.loc 1 483 6 is_stmt 0 view .LVU52
 185 0014 2CB3     		cbz	r4, .L14
 487:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* Do not allow tot_len to wrap as a result. */
 186              		.loc 1 487 3 is_stmt 1 view .LVU53
 487:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* Do not allow tot_len to wrap as a result. */
 187              		.loc 1 487 23 is_stmt 0 view .LVU54
 188 0016 A2B2     		uxth	r2, r4
 189              	.LVL10:
 489:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     return 1;
 190              		.loc 1 489 3 is_stmt 1 view .LVU55
 489:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     return 1;
 191              		.loc 1 489 38 is_stmt 0 view .LVU56
 192 0018 2B89     		ldrh	r3, [r5, #8]
 489:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     return 1;
 193              		.loc 1 489 7 view .LVU57
 194 001a 1344     		add	r3, r3, r2
 195 001c 9BB2     		uxth	r3, r3
 489:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     return 1;
 196              		.loc 1 489 6 view .LVU58
 197 001e 9342     		cmp	r3, r2
 198 0020 21D3     		bcc	.L15
 493:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 199              		.loc 1 493 3 is_stmt 1 view .LVU59
 200              	.LVL11:
 496:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* set new payload pointer */
 201              		.loc 1 496 3 view .LVU60
 496:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* set new payload pointer */
 202              		.loc 1 496 6 is_stmt 0 view .LVU61
 203 0022 95F90C10 		ldrsb	r1, [r5, #12]
 204 0026 0029     		cmp	r1, #0
 205 0028 11DB     		blt	.L20
 510:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       payload = (u8_t *)p->payload - header_size_increment;
 206              		.loc 1 510 5 is_stmt 1 view .LVU62
 510:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       payload = (u8_t *)p->payload - header_size_increment;
 207              		.loc 1 510 8 is_stmt 0 view .LVU63
 208 002a F6B1     		cbz	r6, .L17
 511:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     } else {
 209              		.loc 1 511 7 is_stmt 1 view .LVU64
 511:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     } else {
 210              		.loc 1 511 26 is_stmt 0 view .LVU65
 211 002c 6968     		ldr	r1, [r5, #4]
 511:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     } else {
 212              		.loc 1 511 15 view .LVU66
 213 002e 0C1B     		subs	r4, r1, r4
 214              	.LVL12:
 215              	.L12:
 519:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 216              		.loc 1 519 73 is_stmt 1 view .LVU67
 522:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   p->len = (u16_t)(p->len + increment_magnitude);
 217              		.loc 1 522 3 view .LVU68
 522:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   p->len = (u16_t)(p->len + increment_magnitude);
 218              		.loc 1 522 14 is_stmt 0 view .LVU69
 219 0030 6C60     		str	r4, [r5, #4]
 523:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   p->tot_len = (u16_t)(p->tot_len + increment_magnitude);
 220              		.loc 1 523 3 is_stmt 1 view .LVU70
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 26


 523:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   p->tot_len = (u16_t)(p->tot_len + increment_magnitude);
 221              		.loc 1 523 21 is_stmt 0 view .LVU71
 222 0032 6989     		ldrh	r1, [r5, #10]
 523:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   p->tot_len = (u16_t)(p->tot_len + increment_magnitude);
 223              		.loc 1 523 12 view .LVU72
 224 0034 0A44     		add	r2, r2, r1
 225              	.LVL13:
 523:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   p->tot_len = (u16_t)(p->tot_len + increment_magnitude);
 226              		.loc 1 523 10 view .LVU73
 227 0036 6A81     		strh	r2, [r5, #10]	@ movhi
 524:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 228              		.loc 1 524 3 is_stmt 1 view .LVU74
 524:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 229              		.loc 1 524 14 is_stmt 0 view .LVU75
 230 0038 2B81     		strh	r3, [r5, #8]	@ movhi
 527:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** }
 231              		.loc 1 527 3 is_stmt 1 view .LVU76
 527:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** }
 232              		.loc 1 527 10 is_stmt 0 view .LVU77
 233 003a 0020     		movs	r0, #0
 234 003c 10E0     		b	.L10
 235              	.LVL14:
 236              	.L19:
 479:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   if ((p == NULL) || (header_size_increment > 0xFFFF)) {
 237              		.loc 1 479 3 is_stmt 1 discriminator 1 view .LVU78
 479:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   if ((p == NULL) || (header_size_increment > 0xFFFF)) {
 238              		.loc 1 479 3 discriminator 1 view .LVU79
 239 003e 0C4B     		ldr	r3, .L21
 240 0040 40F2DF12 		movw	r2, #479
 241              	.LVL15:
 479:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   if ((p == NULL) || (header_size_increment > 0xFFFF)) {
 242              		.loc 1 479 3 is_stmt 0 discriminator 1 view .LVU80
 243 0044 0B49     		ldr	r1, .L21+4
 244              	.LVL16:
 479:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   if ((p == NULL) || (header_size_increment > 0xFFFF)) {
 245              		.loc 1 479 3 discriminator 1 view .LVU81
 246 0046 0C48     		ldr	r0, .L21+8
 247              	.LVL17:
 479:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   if ((p == NULL) || (header_size_increment > 0xFFFF)) {
 248              		.loc 1 479 3 discriminator 1 view .LVU82
 249 0048 FFF7FEFF 		bl	printf
 250              	.LVL18:
 251 004c DDE7     		b	.L9
 252              	.LVL19:
 253              	.L20:
 498:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* boundary check fails? */
 254              		.loc 1 498 5 is_stmt 1 view .LVU83
 498:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* boundary check fails? */
 255              		.loc 1 498 24 is_stmt 0 view .LVU84
 256 004e 6968     		ldr	r1, [r5, #4]
 498:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* boundary check fails? */
 257              		.loc 1 498 13 view .LVU85
 258 0050 0C1B     		subs	r4, r1, r4
 259              	.LVL20:
 500:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       LWIP_DEBUGF( PBUF_DEBUG | LWIP_DBG_TRACE,
 260              		.loc 1 500 5 is_stmt 1 view .LVU86
 500:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       LWIP_DEBUGF( PBUF_DEBUG | LWIP_DBG_TRACE,
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 27


 261              		.loc 1 500 37 is_stmt 0 view .LVU87
 262 0052 05F11001 		add	r1, r5, #16
 500:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       LWIP_DEBUGF( PBUF_DEBUG | LWIP_DBG_TRACE,
 263              		.loc 1 500 8 view .LVU88
 264 0056 A142     		cmp	r1, r4
 265 0058 EAD9     		bls	.L12
 505:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     }
 266              		.loc 1 505 14 view .LVU89
 267 005a 0120     		movs	r0, #1
 268 005c 00E0     		b	.L10
 269              	.LVL21:
 270              	.L13:
 481:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
 271              		.loc 1 481 12 view .LVU90
 272 005e 0120     		movs	r0, #1
 273              	.LVL22:
 274              	.L10:
 528:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 275              		.loc 1 528 1 view .LVU91
 276 0060 70BD     		pop	{r4, r5, r6, pc}
 277              	.LVL23:
 278              	.L14:
 484:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
 279              		.loc 1 484 12 view .LVU92
 280 0062 0020     		movs	r0, #0
 281 0064 FCE7     		b	.L10
 282              	.LVL24:
 283              	.L15:
 490:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
 284              		.loc 1 490 12 view .LVU93
 285 0066 0120     		movs	r0, #1
 286 0068 FAE7     		b	.L10
 287              	.LVL25:
 288              	.L17:
 515:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     }
 289              		.loc 1 515 14 view .LVU94
 290 006a 0120     		movs	r0, #1
 291 006c F8E7     		b	.L10
 292              	.L22:
 293 006e 00BF     		.align	2
 294              	.L21:
 295 0070 00000000 		.word	.LC0
 296 0074 30000000 		.word	.LC1
 297 0078 3C000000 		.word	.LC2
 298              		.cfi_endproc
 299              	.LFE182:
 301              		.section	.text.pbuf_pool_is_empty,"ax",%progbits
 302              		.align	1
 303              		.syntax unified
 304              		.thumb
 305              		.thumb_func
 307              	pbuf_pool_is_empty:
 308              	.LFB176:
 158:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** #ifndef PBUF_POOL_FREE_OOSEQ_QUEUE_CALL
 309              		.loc 1 158 1 is_stmt 1 view -0
 310              		.cfi_startproc
 311              		@ args = 0, pretend = 0, frame = 0
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 28


 312              		@ frame_needed = 0, uses_anonymous_args = 0
 313 0000 10B5     		push	{r4, lr}
 314              	.LCFI3:
 315              		.cfi_def_cfa_offset 8
 316              		.cfi_offset 4, -8
 317              		.cfi_offset 14, -4
 162:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   SYS_ARCH_DECL_PROTECT(old_level);
 318              		.loc 1 162 3 view .LVU96
 163:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   SYS_ARCH_PROTECT(old_level);
 319              		.loc 1 163 3 view .LVU97
 164:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   queued = pbuf_free_ooseq_pending;
 320              		.loc 1 164 3 view .LVU98
 321 0002 FFF7FEFF 		bl	sys_arch_protect
 322              	.LVL26:
 165:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   pbuf_free_ooseq_pending = 1;
 323              		.loc 1 165 3 view .LVU99
 165:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   pbuf_free_ooseq_pending = 1;
 324              		.loc 1 165 10 is_stmt 0 view .LVU100
 325 0006 0B4B     		ldr	r3, .L27
 326 0008 1C78     		ldrb	r4, [r3]	@ zero_extendqisi2
 327 000a E4B2     		uxtb	r4, r4
 328              	.LVL27:
 166:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   SYS_ARCH_UNPROTECT(old_level);
 329              		.loc 1 166 3 is_stmt 1 view .LVU101
 166:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   SYS_ARCH_UNPROTECT(old_level);
 330              		.loc 1 166 27 is_stmt 0 view .LVU102
 331 000c 0122     		movs	r2, #1
 332 000e 1A70     		strb	r2, [r3]
 167:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 333              		.loc 1 167 3 is_stmt 1 view .LVU103
 334 0010 FFF7FEFF 		bl	sys_arch_unprotect
 335              	.LVL28:
 169:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* queue a call to pbuf_free_ooseq if not already queued */
 336              		.loc 1 169 3 view .LVU104
 169:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* queue a call to pbuf_free_ooseq if not already queued */
 337              		.loc 1 169 6 is_stmt 0 view .LVU105
 338 0014 04B1     		cbz	r4, .L26
 339              	.LVL29:
 340              	.L23:
 174:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** #endif /* !LWIP_TCP || !TCP_QUEUE_OOSEQ || !PBUF_POOL_FREE_OOSEQ */
 341              		.loc 1 174 1 view .LVU106
 342 0016 10BD     		pop	{r4, pc}
 343              	.LVL30:
 344              	.L26:
 171:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
 345              		.loc 1 171 5 is_stmt 1 view .LVU107
 171:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
 346              		.loc 1 171 5 view .LVU108
 347 0018 0021     		movs	r1, #0
 348 001a 0748     		ldr	r0, .L27+4
 349 001c FFF7FEFF 		bl	tcpip_try_callback
 350              	.LVL31:
 171:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
 351              		.loc 1 171 5 is_stmt 0 discriminator 1 view .LVU109
 352 0020 0028     		cmp	r0, #0
 353 0022 F8D0     		beq	.L23
 171:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 29


 354              		.loc 1 171 5 is_stmt 1 discriminator 1 view .LVU110
 355 0024 FFF7FEFF 		bl	sys_arch_protect
 356              	.LVL32:
 171:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
 357              		.loc 1 171 5 discriminator 1 view .LVU111
 358 0028 024B     		ldr	r3, .L27
 359 002a 0022     		movs	r2, #0
 360 002c 1A70     		strb	r2, [r3]
 171:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
 361              		.loc 1 171 5 discriminator 1 view .LVU112
 362 002e FFF7FEFF 		bl	sys_arch_unprotect
 363              	.LVL33:
 171:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
 364              		.loc 1 171 5 discriminator 3 view .LVU113
 174:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** #endif /* !LWIP_TCP || !TCP_QUEUE_OOSEQ || !PBUF_POOL_FREE_OOSEQ */
 365              		.loc 1 174 1 is_stmt 0 view .LVU114
 366 0032 F0E7     		b	.L23
 367              	.L28:
 368              		.align	2
 369              	.L27:
 370 0034 00000000 		.word	pbuf_free_ooseq_pending
 371 0038 00000000 		.word	pbuf_free_ooseq_callback
 372              		.cfi_endproc
 373              	.LFE176:
 375              		.section	.text.pbuf_free_ooseq,"ax",%progbits
 376              		.align	1
 377              		.syntax unified
 378              		.thumb
 379              		.thumb_func
 381              	pbuf_free_ooseq:
 382              	.LFB174:
 129:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   struct tcp_pcb *pcb;
 383              		.loc 1 129 1 is_stmt 1 view -0
 384              		.cfi_startproc
 385              		@ args = 0, pretend = 0, frame = 0
 386              		@ frame_needed = 0, uses_anonymous_args = 0
 387 0000 08B5     		push	{r3, lr}
 388              	.LCFI4:
 389              		.cfi_def_cfa_offset 8
 390              		.cfi_offset 3, -8
 391              		.cfi_offset 14, -4
 130:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   SYS_ARCH_SET(pbuf_free_ooseq_pending, 0);
 392              		.loc 1 130 3 view .LVU116
 131:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 393              		.loc 1 131 3 view .LVU117
 394              	.LBB2:
 131:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 395              		.loc 1 131 3 view .LVU118
 131:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 396              		.loc 1 131 3 view .LVU119
 397 0002 FFF7FEFF 		bl	sys_arch_protect
 398              	.LVL34:
 131:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 399              		.loc 1 131 3 discriminator 1 view .LVU120
 400 0006 084B     		ldr	r3, .L35
 401 0008 0022     		movs	r2, #0
 402 000a 1A70     		strb	r2, [r3]
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 30


 131:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 403              		.loc 1 131 3 discriminator 1 view .LVU121
 404 000c FFF7FEFF 		bl	sys_arch_unprotect
 405              	.LVL35:
 131:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 406              		.loc 1 131 3 is_stmt 0 discriminator 1 view .LVU122
 407              	.LBE2:
 131:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 408              		.loc 1 131 3 is_stmt 1 discriminator 2 view .LVU123
 133:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     if (pcb->ooseq != NULL) {
 409              		.loc 1 133 3 view .LVU124
 133:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     if (pcb->ooseq != NULL) {
 410              		.loc 1 133 12 is_stmt 0 view .LVU125
 411 0010 064B     		ldr	r3, .L35+4
 412 0012 1868     		ldr	r0, [r3]
 413              	.LVL36:
 133:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     if (pcb->ooseq != NULL) {
 414              		.loc 1 133 3 view .LVU126
 415 0014 00E0     		b	.L30
 416              	.L31:
 133:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     if (pcb->ooseq != NULL) {
 417              		.loc 1 133 48 is_stmt 1 discriminator 2 view .LVU127
 418 0016 C068     		ldr	r0, [r0, #12]
 419              	.LVL37:
 420              	.L30:
 133:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     if (pcb->ooseq != NULL) {
 421              		.loc 1 133 36 discriminator 1 view .LVU128
 422 0018 20B1     		cbz	r0, .L29
 134:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       /** Free the ooseq pbufs of one PCB only */
 423              		.loc 1 134 5 view .LVU129
 134:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       /** Free the ooseq pbufs of one PCB only */
 424              		.loc 1 134 12 is_stmt 0 view .LVU130
 425 001a 436F     		ldr	r3, [r0, #116]
 134:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       /** Free the ooseq pbufs of one PCB only */
 426              		.loc 1 134 8 view .LVU131
 427 001c 002B     		cmp	r3, #0
 428 001e FAD0     		beq	.L31
 136:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       tcp_free_ooseq(pcb);
 429              		.loc 1 136 101 is_stmt 1 view .LVU132
 137:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       return;
 430              		.loc 1 137 7 view .LVU133
 431 0020 FFF7FEFF 		bl	tcp_free_ooseq
 432              	.LVL38:
 138:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     }
 433              		.loc 1 138 7 view .LVU134
 434              	.L29:
 141:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 435              		.loc 1 141 1 is_stmt 0 view .LVU135
 436 0024 08BD     		pop	{r3, pc}
 437              	.L36:
 438 0026 00BF     		.align	2
 439              	.L35:
 440 0028 00000000 		.word	pbuf_free_ooseq_pending
 441 002c 00000000 		.word	tcp_active_pcbs
 442              		.cfi_endproc
 443              	.LFE174:
 445              		.section	.text.pbuf_free_ooseq_callback,"ax",%progbits
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 31


 446              		.align	1
 447              		.syntax unified
 448              		.thumb
 449              		.thumb_func
 451              	pbuf_free_ooseq_callback:
 452              	.LVL39:
 453              	.LFB175:
 149:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_UNUSED_ARG(arg);
 454              		.loc 1 149 1 is_stmt 1 view -0
 455              		.cfi_startproc
 456              		@ args = 0, pretend = 0, frame = 0
 457              		@ frame_needed = 0, uses_anonymous_args = 0
 149:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_UNUSED_ARG(arg);
 458              		.loc 1 149 1 is_stmt 0 view .LVU137
 459 0000 08B5     		push	{r3, lr}
 460              	.LCFI5:
 461              		.cfi_def_cfa_offset 8
 462              		.cfi_offset 3, -8
 463              		.cfi_offset 14, -4
 150:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   pbuf_free_ooseq();
 464              		.loc 1 150 3 is_stmt 1 view .LVU138
 151:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** }
 465              		.loc 1 151 3 view .LVU139
 466 0002 FFF7FEFF 		bl	pbuf_free_ooseq
 467              	.LVL40:
 152:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** #endif /* !NO_SYS */
 468              		.loc 1 152 1 is_stmt 0 view .LVU140
 469 0006 08BD     		pop	{r3, pc}
 470              		.cfi_endproc
 471              	.LFE175:
 473              		.section	.rodata.pbuf_alloc_reference.str1.4,"aMS",%progbits,1
 474              		.align	2
 475              	.LC3:
 476 0000 696E7661 		.ascii	"invalid pbuf_type\000"
 476      6C696420 
 476      70627566 
 476      5F747970 
 476      6500
 477              		.section	.text.pbuf_alloc_reference,"ax",%progbits
 478              		.align	1
 479              		.global	pbuf_alloc_reference
 480              		.syntax unified
 481              		.thumb
 482              		.thumb_func
 484              	pbuf_alloc_reference:
 485              	.LVL41:
 486              	.LFB179:
 328:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   struct pbuf *p;
 487              		.loc 1 328 1 is_stmt 1 view -0
 488              		.cfi_startproc
 489              		@ args = 0, pretend = 0, frame = 0
 490              		@ frame_needed = 0, uses_anonymous_args = 0
 328:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   struct pbuf *p;
 491              		.loc 1 328 1 is_stmt 0 view .LVU142
 492 0000 F0B5     		push	{r4, r5, r6, r7, lr}
 493              	.LCFI6:
 494              		.cfi_def_cfa_offset 20
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 32


 495              		.cfi_offset 4, -20
 496              		.cfi_offset 5, -16
 497              		.cfi_offset 6, -12
 498              		.cfi_offset 7, -8
 499              		.cfi_offset 14, -4
 500 0002 83B0     		sub	sp, sp, #12
 501              	.LCFI7:
 502              		.cfi_def_cfa_offset 32
 503 0004 0646     		mov	r6, r0
 504 0006 0D46     		mov	r5, r1
 505 0008 1446     		mov	r4, r2
 329:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ASSERT("invalid pbuf_type", (type == PBUF_REF) || (type == PBUF_ROM));
 506              		.loc 1 329 3 is_stmt 1 view .LVU143
 330:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* only allocate memory for the pbuf structure */
 507              		.loc 1 330 3 view .LVU144
 330:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* only allocate memory for the pbuf structure */
 508              		.loc 1 330 3 view .LVU145
 509 000a 412A     		cmp	r2, #65
 510 000c 18BF     		it	ne
 511 000e 012A     		cmpne	r2, #1
 512 0010 0FD1     		bne	.L43
 513              	.LVL42:
 514              	.L40:
 330:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* only allocate memory for the pbuf structure */
 515              		.loc 1 330 3 discriminator 3 view .LVU146
 330:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* only allocate memory for the pbuf structure */
 516              		.loc 1 330 3 discriminator 3 view .LVU147
 332:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   if (p == NULL) {
 517              		.loc 1 332 3 view .LVU148
 332:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   if (p == NULL) {
 518              		.loc 1 332 22 is_stmt 0 view .LVU149
 519 0012 0B20     		movs	r0, #11
 520 0014 FFF7FEFF 		bl	memp_malloc
 521              	.LVL43:
 333:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     LWIP_DEBUGF(PBUF_DEBUG | LWIP_DBG_LEVEL_SERIOUS,
 522              		.loc 1 333 3 is_stmt 1 view .LVU150
 333:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     LWIP_DEBUGF(PBUF_DEBUG | LWIP_DBG_LEVEL_SERIOUS,
 523              		.loc 1 333 6 is_stmt 0 view .LVU151
 524 0018 0746     		mov	r7, r0
 525 001a 38B1     		cbz	r0, .L39
 339:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   return p;
 526              		.loc 1 339 3 is_stmt 1 view .LVU152
 527 001c 0023     		movs	r3, #0
 528 001e 0193     		str	r3, [sp, #4]
 529 0020 0094     		str	r4, [sp]
 530 0022 2B46     		mov	r3, r5
 531 0024 2A46     		mov	r2, r5
 532 0026 3146     		mov	r1, r6
 533 0028 FFF7FEFF 		bl	pbuf_init_alloced_pbuf
 534              	.LVL44:
 340:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** }
 535              		.loc 1 340 3 view .LVU153
 536              	.L39:
 341:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 537              		.loc 1 341 1 is_stmt 0 view .LVU154
 538 002c 3846     		mov	r0, r7
 539 002e 03B0     		add	sp, sp, #12
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 33


 540              	.LCFI8:
 541              		.cfi_remember_state
 542              		.cfi_def_cfa_offset 20
 543              		@ sp needed
 544 0030 F0BD     		pop	{r4, r5, r6, r7, pc}
 545              	.LVL45:
 546              	.L43:
 547              	.LCFI9:
 548              		.cfi_restore_state
 330:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* only allocate memory for the pbuf structure */
 549              		.loc 1 330 3 is_stmt 1 discriminator 1 view .LVU155
 330:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* only allocate memory for the pbuf structure */
 550              		.loc 1 330 3 discriminator 1 view .LVU156
 551 0032 044B     		ldr	r3, .L44
 552 0034 4FF4A572 		mov	r2, #330
 553              	.LVL46:
 330:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* only allocate memory for the pbuf structure */
 554              		.loc 1 330 3 is_stmt 0 discriminator 1 view .LVU157
 555 0038 0349     		ldr	r1, .L44+4
 556              	.LVL47:
 330:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* only allocate memory for the pbuf structure */
 557              		.loc 1 330 3 discriminator 1 view .LVU158
 558 003a 0448     		ldr	r0, .L44+8
 559              	.LVL48:
 330:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* only allocate memory for the pbuf structure */
 560              		.loc 1 330 3 discriminator 1 view .LVU159
 561 003c FFF7FEFF 		bl	printf
 562              	.LVL49:
 563 0040 E7E7     		b	.L40
 564              	.L45:
 565 0042 00BF     		.align	2
 566              	.L44:
 567 0044 00000000 		.word	.LC0
 568 0048 00000000 		.word	.LC3
 569 004c 3C000000 		.word	.LC2
 570              		.cfi_endproc
 571              	.LFE179:
 573              		.section	.text.pbuf_alloced_custom,"ax",%progbits
 574              		.align	1
 575              		.global	pbuf_alloced_custom
 576              		.syntax unified
 577              		.thumb
 578              		.thumb_func
 580              	pbuf_alloced_custom:
 581              	.LVL50:
 582              	.LFB180:
 365:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   u16_t offset = (u16_t)l;
 583              		.loc 1 365 1 is_stmt 1 view -0
 584              		.cfi_startproc
 585              		@ args = 8, pretend = 0, frame = 0
 586              		@ frame_needed = 0, uses_anonymous_args = 0
 365:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   u16_t offset = (u16_t)l;
 587              		.loc 1 365 1 is_stmt 0 view .LVU161
 588 0000 10B5     		push	{r4, lr}
 589              	.LCFI10:
 590              		.cfi_def_cfa_offset 8
 591              		.cfi_offset 4, -8
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 34


 592              		.cfi_offset 14, -4
 593 0002 82B0     		sub	sp, sp, #8
 594              	.LCFI11:
 595              		.cfi_def_cfa_offset 16
 596 0004 8446     		mov	ip, r0
 597 0006 8E46     		mov	lr, r1
 598 0008 1846     		mov	r0, r3
 599              	.LVL51:
 365:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   u16_t offset = (u16_t)l;
 600              		.loc 1 365 1 view .LVU162
 601 000a 0499     		ldr	r1, [sp, #16]
 602              	.LVL52:
 366:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   void *payload;
 603              		.loc 1 366 3 is_stmt 1 view .LVU163
 367:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_DEBUGF(PBUF_DEBUG | LWIP_DBG_TRACE, ("pbuf_alloced_custom(length=%"U16_F")\n", length));
 604              		.loc 1 367 3 view .LVU164
 368:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 605              		.loc 1 368 95 view .LVU165
 370:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     LWIP_DEBUGF(PBUF_DEBUG | LWIP_DBG_LEVEL_WARNING, ("pbuf_alloced_custom(length=%"U16_F") buffer 
 606              		.loc 1 370 3 view .LVU166
 370:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     LWIP_DEBUGF(PBUF_DEBUG | LWIP_DBG_LEVEL_WARNING, ("pbuf_alloced_custom(length=%"U16_F") buffer 
 607              		.loc 1 370 7 is_stmt 0 view .LVU167
 608 000c 0CF1030C 		add	ip, ip, #3
 609              	.LVL53:
 370:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     LWIP_DEBUGF(PBUF_DEBUG | LWIP_DBG_LEVEL_WARNING, ("pbuf_alloced_custom(length=%"U16_F") buffer 
 610              		.loc 1 370 7 view .LVU168
 611 0010 2CF0030C 		bic	ip, ip, #3
 612              	.LVL54:
 370:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     LWIP_DEBUGF(PBUF_DEBUG | LWIP_DBG_LEVEL_WARNING, ("pbuf_alloced_custom(length=%"U16_F") buffer 
 613              		.loc 1 370 35 view .LVU169
 614 0014 0EEB0C04 		add	r4, lr, ip
 370:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     LWIP_DEBUGF(PBUF_DEBUG | LWIP_DBG_LEVEL_WARNING, ("pbuf_alloced_custom(length=%"U16_F") buffer 
 615              		.loc 1 370 6 view .LVU170
 616 0018 BDF81430 		ldrh	r3, [sp, #20]
 617              	.LVL55:
 370:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     LWIP_DEBUGF(PBUF_DEBUG | LWIP_DBG_LEVEL_WARNING, ("pbuf_alloced_custom(length=%"U16_F") buffer 
 618              		.loc 1 370 6 view .LVU171
 619 001c 9C42     		cmp	r4, r3
 620 001e 0CD8     		bhi	.L49
 375:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     payload = (u8_t *)payload_mem + LWIP_MEM_ALIGN_SIZE(offset);
 621              		.loc 1 375 3 is_stmt 1 view .LVU172
 375:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     payload = (u8_t *)payload_mem + LWIP_MEM_ALIGN_SIZE(offset);
 622              		.loc 1 375 6 is_stmt 0 view .LVU173
 623 0020 01B1     		cbz	r1, .L48
 376:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   } else {
 624              		.loc 1 376 5 is_stmt 1 view .LVU174
 376:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   } else {
 625              		.loc 1 376 13 is_stmt 0 view .LVU175
 626 0022 6144     		add	r1, r1, ip
 627              	.LVL56:
 628              	.L48:
 380:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   return &p->pbuf;
 629              		.loc 1 380 3 is_stmt 1 view .LVU176
 630 0024 0446     		mov	r4, r0
 631 0026 0223     		movs	r3, #2
 632 0028 0193     		str	r3, [sp, #4]
 633 002a 0092     		str	r2, [sp]
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 35


 634 002c 7346     		mov	r3, lr
 635 002e 7246     		mov	r2, lr
 636              	.LVL57:
 380:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   return &p->pbuf;
 637              		.loc 1 380 3 is_stmt 0 view .LVU177
 638 0030 FFF7FEFF 		bl	pbuf_init_alloced_pbuf
 639              	.LVL58:
 381:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** }
 640              		.loc 1 381 3 is_stmt 1 view .LVU178
 641              	.L46:
 382:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** #endif /* LWIP_SUPPORT_CUSTOM_PBUF */
 642              		.loc 1 382 1 is_stmt 0 view .LVU179
 643 0034 2046     		mov	r0, r4
 644 0036 02B0     		add	sp, sp, #8
 645              	.LCFI12:
 646              		.cfi_remember_state
 647              		.cfi_def_cfa_offset 8
 648              		@ sp needed
 649 0038 10BD     		pop	{r4, pc}
 650              	.LVL59:
 651              	.L49:
 652              	.LCFI13:
 653              		.cfi_restore_state
 372:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
 654              		.loc 1 372 12 view .LVU180
 655 003a 0024     		movs	r4, #0
 656 003c FAE7     		b	.L46
 657              		.cfi_endproc
 658              	.LFE180:
 660              		.section	.text.pbuf_add_header,"ax",%progbits
 661              		.align	1
 662              		.global	pbuf_add_header
 663              		.syntax unified
 664              		.thumb
 665              		.thumb_func
 667              	pbuf_add_header:
 668              	.LVL60:
 669              	.LFB183:
 552:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   return pbuf_add_header_impl(p, header_size_increment, 0);
 670              		.loc 1 552 1 is_stmt 1 view -0
 671              		.cfi_startproc
 672              		@ args = 0, pretend = 0, frame = 0
 673              		@ frame_needed = 0, uses_anonymous_args = 0
 552:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   return pbuf_add_header_impl(p, header_size_increment, 0);
 674              		.loc 1 552 1 is_stmt 0 view .LVU182
 675 0000 08B5     		push	{r3, lr}
 676              	.LCFI14:
 677              		.cfi_def_cfa_offset 8
 678              		.cfi_offset 3, -8
 679              		.cfi_offset 14, -4
 553:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** }
 680              		.loc 1 553 3 is_stmt 1 view .LVU183
 553:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** }
 681              		.loc 1 553 10 is_stmt 0 view .LVU184
 682 0002 0022     		movs	r2, #0
 683 0004 FFF7FEFF 		bl	pbuf_add_header_impl
 684              	.LVL61:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 36


 554:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 685              		.loc 1 554 1 view .LVU185
 686 0008 08BD     		pop	{r3, pc}
 687              		.cfi_endproc
 688              	.LFE183:
 690              		.section	.text.pbuf_add_header_force,"ax",%progbits
 691              		.align	1
 692              		.global	pbuf_add_header_force
 693              		.syntax unified
 694              		.thumb
 695              		.thumb_func
 697              	pbuf_add_header_force:
 698              	.LVL62:
 699              	.LFB184:
 562:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   return pbuf_add_header_impl(p, header_size_increment, 1);
 700              		.loc 1 562 1 is_stmt 1 view -0
 701              		.cfi_startproc
 702              		@ args = 0, pretend = 0, frame = 0
 703              		@ frame_needed = 0, uses_anonymous_args = 0
 562:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   return pbuf_add_header_impl(p, header_size_increment, 1);
 704              		.loc 1 562 1 is_stmt 0 view .LVU187
 705 0000 08B5     		push	{r3, lr}
 706              	.LCFI15:
 707              		.cfi_def_cfa_offset 8
 708              		.cfi_offset 3, -8
 709              		.cfi_offset 14, -4
 563:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** }
 710              		.loc 1 563 3 is_stmt 1 view .LVU188
 563:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** }
 711              		.loc 1 563 10 is_stmt 0 view .LVU189
 712 0002 0122     		movs	r2, #1
 713 0004 FFF7FEFF 		bl	pbuf_add_header_impl
 714              	.LVL63:
 564:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 715              		.loc 1 564 1 view .LVU190
 716 0008 08BD     		pop	{r3, pc}
 717              		.cfi_endproc
 718              	.LFE184:
 720              		.section	.rodata.pbuf_remove_header.str1.4,"aMS",%progbits,1
 721              		.align	2
 722              	.LC4:
 723 0000 696E6372 		.ascii	"increment_magnitude <= p->len\000"
 723      656D656E 
 723      745F6D61 
 723      676E6974 
 723      75646520 
 724              		.section	.text.pbuf_remove_header,"ax",%progbits
 725              		.align	1
 726              		.global	pbuf_remove_header
 727              		.syntax unified
 728              		.thumb
 729              		.thumb_func
 731              	pbuf_remove_header:
 732              	.LVL64:
 733              	.LFB185:
 583:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   void *payload;
 734              		.loc 1 583 1 is_stmt 1 view -0
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 37


 735              		.cfi_startproc
 736              		@ args = 0, pretend = 0, frame = 0
 737              		@ frame_needed = 0, uses_anonymous_args = 0
 583:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   void *payload;
 738              		.loc 1 583 1 is_stmt 0 view .LVU192
 739 0000 38B5     		push	{r3, r4, r5, lr}
 740              	.LCFI16:
 741              		.cfi_def_cfa_offset 16
 742              		.cfi_offset 3, -16
 743              		.cfi_offset 4, -12
 744              		.cfi_offset 5, -8
 745              		.cfi_offset 14, -4
 746 0002 0C46     		mov	r4, r1
 584:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   u16_t increment_magnitude;
 747              		.loc 1 584 3 is_stmt 1 view .LVU193
 585:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 748              		.loc 1 585 3 view .LVU194
 587:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   if ((p == NULL) || (header_size_decrement > 0xFFFF)) {
 749              		.loc 1 587 3 view .LVU195
 587:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   if ((p == NULL) || (header_size_decrement > 0xFFFF)) {
 750              		.loc 1 587 3 view .LVU196
 751 0004 0546     		mov	r5, r0
 752 0006 98B1     		cbz	r0, .L62
 753              	.LVL65:
 754              	.L56:
 587:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   if ((p == NULL) || (header_size_decrement > 0xFFFF)) {
 755              		.loc 1 587 3 discriminator 3 view .LVU197
 587:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   if ((p == NULL) || (header_size_decrement > 0xFFFF)) {
 756              		.loc 1 587 3 discriminator 3 view .LVU198
 588:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     return 1;
 757              		.loc 1 588 3 view .LVU199
 588:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     return 1;
 758              		.loc 1 588 6 is_stmt 0 view .LVU200
 759 0008 002D     		cmp	r5, #0
 760 000a 18BF     		it	ne
 761 000c B4F5803F 		cmpne	r4, #65536
 762 0010 1FD2     		bcs	.L59
 591:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     return 0;
 763              		.loc 1 591 3 is_stmt 1 view .LVU201
 591:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     return 0;
 764              		.loc 1 591 6 is_stmt 0 view .LVU202
 765 0012 04B3     		cbz	r4, .L60
 595:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* Check that we aren't going to move off the end of the pbuf */
 766              		.loc 1 595 3 is_stmt 1 view .LVU203
 595:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* Check that we aren't going to move off the end of the pbuf */
 767              		.loc 1 595 23 is_stmt 0 view .LVU204
 768 0014 A2B2     		uxth	r2, r4
 769              	.LVL66:
 597:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 770              		.loc 1 597 3 is_stmt 1 view .LVU205
 597:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 771              		.loc 1 597 3 view .LVU206
 772 0016 6B89     		ldrh	r3, [r5, #10]
 773 0018 9342     		cmp	r3, r2
 774 001a 11D3     		bcc	.L63
 597:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 775              		.loc 1 597 3 discriminator 2 view .LVU207
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 38


 597:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 776              		.loc 1 597 3 discriminator 2 view .LVU208
 600:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_UNUSED_ARG(payload); /* only used in LWIP_DEBUGF below */
 777              		.loc 1 600 3 view .LVU209
 778              	.LVL67:
 601:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 779              		.loc 1 601 3 view .LVU210
 604:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* modify pbuf length fields */
 780              		.loc 1 604 3 view .LVU211
 604:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* modify pbuf length fields */
 781              		.loc 1 604 25 is_stmt 0 view .LVU212
 782 001c 6968     		ldr	r1, [r5, #4]
 604:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* modify pbuf length fields */
 783              		.loc 1 604 35 view .LVU213
 784 001e 2144     		add	r1, r1, r4
 604:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* modify pbuf length fields */
 785              		.loc 1 604 14 view .LVU214
 786 0020 6960     		str	r1, [r5, #4]
 787              	.LVL68:
 606:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   p->tot_len = (u16_t)(p->tot_len - increment_magnitude);
 788              		.loc 1 606 3 is_stmt 1 view .LVU215
 606:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   p->tot_len = (u16_t)(p->tot_len - increment_magnitude);
 789              		.loc 1 606 12 is_stmt 0 view .LVU216
 790 0022 9B1A     		subs	r3, r3, r2
 606:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   p->tot_len = (u16_t)(p->tot_len - increment_magnitude);
 791              		.loc 1 606 10 view .LVU217
 792 0024 6B81     		strh	r3, [r5, #10]	@ movhi
 607:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 793              		.loc 1 607 3 is_stmt 1 view .LVU218
 607:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 794              		.loc 1 607 25 is_stmt 0 view .LVU219
 795 0026 2B89     		ldrh	r3, [r5, #8]
 607:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 796              		.loc 1 607 16 view .LVU220
 797 0028 9B1A     		subs	r3, r3, r2
 607:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 798              		.loc 1 607 14 view .LVU221
 799 002a 2B81     		strh	r3, [r5, #8]	@ movhi
 610:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 800              		.loc 1 610 73 is_stmt 1 view .LVU222
 612:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** }
 801              		.loc 1 612 3 view .LVU223
 612:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** }
 802              		.loc 1 612 10 is_stmt 0 view .LVU224
 803 002c 0020     		movs	r0, #0
 804              	.LVL69:
 805              	.L57:
 613:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 806              		.loc 1 613 1 view .LVU225
 807 002e 38BD     		pop	{r3, r4, r5, pc}
 808              	.LVL70:
 809              	.L62:
 587:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   if ((p == NULL) || (header_size_decrement > 0xFFFF)) {
 810              		.loc 1 587 3 is_stmt 1 discriminator 1 view .LVU226
 587:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   if ((p == NULL) || (header_size_decrement > 0xFFFF)) {
 811              		.loc 1 587 3 discriminator 1 view .LVU227
 812 0030 0A4B     		ldr	r3, .L64
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 39


 813 0032 40F24B22 		movw	r2, #587
 814 0036 0A49     		ldr	r1, .L64+4
 815              	.LVL71:
 587:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   if ((p == NULL) || (header_size_decrement > 0xFFFF)) {
 816              		.loc 1 587 3 is_stmt 0 discriminator 1 view .LVU228
 817 0038 0A48     		ldr	r0, .L64+8
 818              	.LVL72:
 587:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   if ((p == NULL) || (header_size_decrement > 0xFFFF)) {
 819              		.loc 1 587 3 discriminator 1 view .LVU229
 820 003a FFF7FEFF 		bl	printf
 821              	.LVL73:
 822 003e E3E7     		b	.L56
 823              	.LVL74:
 824              	.L63:
 597:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 825              		.loc 1 597 3 is_stmt 1 discriminator 1 view .LVU230
 597:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 826              		.loc 1 597 3 discriminator 1 view .LVU231
 827 0040 064B     		ldr	r3, .L64
 828 0042 40F25522 		movw	r2, #597
 829              	.LVL75:
 597:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 830              		.loc 1 597 3 is_stmt 0 discriminator 1 view .LVU232
 831 0046 0849     		ldr	r1, .L64+12
 832 0048 0648     		ldr	r0, .L64+8
 833 004a FFF7FEFF 		bl	printf
 834              	.LVL76:
 597:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 835              		.loc 1 597 3 is_stmt 1 discriminator 1 view .LVU233
 597:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 836              		.loc 1 597 3 discriminator 1 view .LVU234
 837 004e 0120     		movs	r0, #1
 597:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 838              		.loc 1 597 3 is_stmt 0 view .LVU235
 839 0050 EDE7     		b	.L57
 840              	.LVL77:
 841              	.L59:
 589:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
 842              		.loc 1 589 12 view .LVU236
 843 0052 0120     		movs	r0, #1
 844 0054 EBE7     		b	.L57
 845              	.L60:
 592:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
 846              		.loc 1 592 12 view .LVU237
 847 0056 0020     		movs	r0, #0
 848 0058 E9E7     		b	.L57
 849              	.L65:
 850 005a 00BF     		.align	2
 851              	.L64:
 852 005c 00000000 		.word	.LC0
 853 0060 30000000 		.word	.LC1
 854 0064 3C000000 		.word	.LC2
 855 0068 00000000 		.word	.LC4
 856              		.cfi_endproc
 857              	.LFE185:
 859              		.section	.text.pbuf_header_impl,"ax",%progbits
 860              		.align	1
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 40


 861              		.syntax unified
 862              		.thumb
 863              		.thumb_func
 865              	pbuf_header_impl:
 866              	.LVL78:
 867              	.LFB186:
 617:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   if (header_size_increment < 0) {
 868              		.loc 1 617 1 is_stmt 1 view -0
 869              		.cfi_startproc
 870              		@ args = 0, pretend = 0, frame = 0
 871              		@ frame_needed = 0, uses_anonymous_args = 0
 617:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   if (header_size_increment < 0) {
 872              		.loc 1 617 1 is_stmt 0 view .LVU239
 873 0000 08B5     		push	{r3, lr}
 874              	.LCFI17:
 875              		.cfi_def_cfa_offset 8
 876              		.cfi_offset 3, -8
 877              		.cfi_offset 14, -4
 618:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     return pbuf_remove_header(p, (size_t) - header_size_increment);
 878              		.loc 1 618 3 is_stmt 1 view .LVU240
 618:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     return pbuf_remove_header(p, (size_t) - header_size_increment);
 879              		.loc 1 618 6 is_stmt 0 view .LVU241
 880 0002 0029     		cmp	r1, #0
 618:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     return pbuf_remove_header(p, (size_t) - header_size_increment);
 881              		.loc 1 618 6 view .LVU242
 882 0004 02DB     		blt	.L70
 621:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
 883              		.loc 1 621 5 is_stmt 1 view .LVU243
 621:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
 884              		.loc 1 621 12 is_stmt 0 view .LVU244
 885 0006 FFF7FEFF 		bl	pbuf_add_header_impl
 886              	.LVL79:
 887              	.L68:
 623:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 888              		.loc 1 623 1 view .LVU245
 889 000a 08BD     		pop	{r3, pc}
 890              	.LVL80:
 891              	.L70:
 619:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   } else {
 892              		.loc 1 619 5 is_stmt 1 view .LVU246
 619:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   } else {
 893              		.loc 1 619 12 is_stmt 0 view .LVU247
 894 000c 4942     		rsbs	r1, r1, #0
 895 000e FFF7FEFF 		bl	pbuf_remove_header
 896              	.LVL81:
 619:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   } else {
 897              		.loc 1 619 12 view .LVU248
 898 0012 FAE7     		b	.L68
 899              		.cfi_endproc
 900              	.LFE186:
 902              		.section	.text.pbuf_header,"ax",%progbits
 903              		.align	1
 904              		.global	pbuf_header
 905              		.syntax unified
 906              		.thumb
 907              		.thumb_func
 909              	pbuf_header:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 41


 910              	.LVL82:
 911              	.LFB187:
 647:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   return pbuf_header_impl(p, header_size_increment, 0);
 912              		.loc 1 647 1 is_stmt 1 view -0
 913              		.cfi_startproc
 914              		@ args = 0, pretend = 0, frame = 0
 915              		@ frame_needed = 0, uses_anonymous_args = 0
 647:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   return pbuf_header_impl(p, header_size_increment, 0);
 916              		.loc 1 647 1 is_stmt 0 view .LVU250
 917 0000 08B5     		push	{r3, lr}
 918              	.LCFI18:
 919              		.cfi_def_cfa_offset 8
 920              		.cfi_offset 3, -8
 921              		.cfi_offset 14, -4
 648:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** }
 922              		.loc 1 648 3 is_stmt 1 view .LVU251
 648:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** }
 923              		.loc 1 648 10 is_stmt 0 view .LVU252
 924 0002 0022     		movs	r2, #0
 925 0004 FFF7FEFF 		bl	pbuf_header_impl
 926              	.LVL83:
 649:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 927              		.loc 1 649 1 view .LVU253
 928 0008 08BD     		pop	{r3, pc}
 929              		.cfi_endproc
 930              	.LFE187:
 932              		.section	.text.pbuf_header_force,"ax",%progbits
 933              		.align	1
 934              		.global	pbuf_header_force
 935              		.syntax unified
 936              		.thumb
 937              		.thumb_func
 939              	pbuf_header_force:
 940              	.LVL84:
 941              	.LFB188:
 657:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   return pbuf_header_impl(p, header_size_increment, 1);
 942              		.loc 1 657 1 is_stmt 1 view -0
 943              		.cfi_startproc
 944              		@ args = 0, pretend = 0, frame = 0
 945              		@ frame_needed = 0, uses_anonymous_args = 0
 657:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   return pbuf_header_impl(p, header_size_increment, 1);
 946              		.loc 1 657 1 is_stmt 0 view .LVU255
 947 0000 08B5     		push	{r3, lr}
 948              	.LCFI19:
 949              		.cfi_def_cfa_offset 8
 950              		.cfi_offset 3, -8
 951              		.cfi_offset 14, -4
 658:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** }
 952              		.loc 1 658 3 is_stmt 1 view .LVU256
 658:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** }
 953              		.loc 1 658 10 is_stmt 0 view .LVU257
 954 0002 0122     		movs	r2, #1
 955 0004 FFF7FEFF 		bl	pbuf_header_impl
 956              	.LVL85:
 659:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 957              		.loc 1 659 1 view .LVU258
 958 0008 08BD     		pop	{r3, pc}
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 42


 959              		.cfi_endproc
 960              	.LFE188:
 962              		.section	.rodata.pbuf_free.str1.4,"aMS",%progbits,1
 963              		.align	2
 964              	.LC5:
 965 0000 70627566 		.ascii	"pbuf_free: p->ref > 0\000"
 965      5F667265 
 965      653A2070 
 965      2D3E7265 
 965      66203E20 
 966 0016 0000     		.align	2
 967              	.LC6:
 968 0018 70632D3E 		.ascii	"pc->custom_free_function != NULL\000"
 968      63757374 
 968      6F6D5F66 
 968      7265655F 
 968      66756E63 
 969 0039 000000   		.align	2
 970              	.LC7:
 971 003c 696E7661 		.ascii	"invalid pbuf type\000"
 971      6C696420 
 971      70627566 
 971      20747970 
 971      6500
 972              		.section	.text.pbuf_free,"ax",%progbits
 973              		.align	1
 974              		.global	pbuf_free
 975              		.syntax unified
 976              		.thumb
 977              		.thumb_func
 979              	pbuf_free:
 980              	.LVL86:
 981              	.LFB190:
 726:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   u8_t alloc_src;
 982              		.loc 1 726 1 is_stmt 1 view -0
 983              		.cfi_startproc
 984              		@ args = 0, pretend = 0, frame = 0
 985              		@ frame_needed = 0, uses_anonymous_args = 0
 726:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   u8_t alloc_src;
 986              		.loc 1 726 1 is_stmt 0 view .LVU260
 987 0000 F8B5     		push	{r3, r4, r5, r6, r7, lr}
 988              	.LCFI20:
 989              		.cfi_def_cfa_offset 24
 990              		.cfi_offset 3, -24
 991              		.cfi_offset 4, -20
 992              		.cfi_offset 5, -16
 993              		.cfi_offset 6, -12
 994              		.cfi_offset 7, -8
 995              		.cfi_offset 14, -4
 727:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   struct pbuf *q;
 996              		.loc 1 727 3 is_stmt 1 view .LVU261
 728:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   u8_t count;
 997              		.loc 1 728 3 view .LVU262
 729:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 998              		.loc 1 729 3 view .LVU263
 731:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     LWIP_ASSERT("p != NULL", p != NULL);
 999              		.loc 1 731 3 view .LVU264
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 43


 731:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     LWIP_ASSERT("p != NULL", p != NULL);
 1000              		.loc 1 731 6 is_stmt 0 view .LVU265
 1001 0002 10B1     		cbz	r0, .L88
 1002 0004 0446     		mov	r4, r0
 742:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* de-allocate all consecutive pbufs from the head of the chain that
 1003              		.loc 1 742 9 view .LVU266
 1004 0006 0026     		movs	r6, #0
 1005 0008 17E0     		b	.L76
 1006              	.L88:
 732:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* if assertions are disabled, proceed with debug output */
 1007              		.loc 1 732 5 is_stmt 1 view .LVU267
 732:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* if assertions are disabled, proceed with debug output */
 1008              		.loc 1 732 5 view .LVU268
 732:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* if assertions are disabled, proceed with debug output */
 1009              		.loc 1 732 5 discriminator 1 view .LVU269
 732:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* if assertions are disabled, proceed with debug output */
 1010              		.loc 1 732 5 discriminator 1 view .LVU270
 1011 000a 2B4B     		ldr	r3, .L92
 1012 000c 4FF43772 		mov	r2, #732
 1013 0010 2A49     		ldr	r1, .L92+4
 1014 0012 2B48     		ldr	r0, .L92+8
 1015              	.LVL87:
 732:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* if assertions are disabled, proceed with debug output */
 1016              		.loc 1 732 5 is_stmt 0 discriminator 1 view .LVU271
 1017 0014 FFF7FEFF 		bl	printf
 1018              	.LVL88:
 732:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* if assertions are disabled, proceed with debug output */
 1019              		.loc 1 732 5 is_stmt 1 discriminator 3 view .LVU272
 732:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* if assertions are disabled, proceed with debug output */
 1020              		.loc 1 732 5 discriminator 3 view .LVU273
 735:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     return 0;
 1021              		.loc 1 735 56 view .LVU274
 736:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
 1022              		.loc 1 736 5 view .LVU275
 736:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
 1023              		.loc 1 736 12 is_stmt 0 view .LVU276
 1024 0018 0026     		movs	r6, #0
 1025              	.LVL89:
 1026              	.L77:
 800:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 1027              		.loc 1 800 1 view .LVU277
 1028 001a 3046     		mov	r0, r6
 1029 001c F8BD     		pop	{r3, r4, r5, r6, r7, pc}
 1030              	.LVL90:
 1031              	.L89:
 1032              	.LBB3:
 753:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* decrease reference count (number of pointers to pbuf) */
 1033              		.loc 1 753 5 is_stmt 1 discriminator 1 view .LVU278
 753:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* decrease reference count (number of pointers to pbuf) */
 1034              		.loc 1 753 5 discriminator 1 view .LVU279
 1035 001e 264B     		ldr	r3, .L92
 1036 0020 40F2F122 		movw	r2, #753
 1037 0024 2749     		ldr	r1, .L92+12
 1038 0026 2648     		ldr	r0, .L92+8
 1039              	.LVL91:
 753:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* decrease reference count (number of pointers to pbuf) */
 1040              		.loc 1 753 5 is_stmt 0 discriminator 1 view .LVU280
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 44


 1041 0028 FFF7FEFF 		bl	printf
 1042              	.LVL92:
 1043 002c 0DE0     		b	.L78
 1044              	.LVL93:
 1045              	.L80:
 1046              	.LBB4:
 767:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         pc->custom_free_function(p);
 1047              		.loc 1 767 9 is_stmt 1 discriminator 3 view .LVU281
 767:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         pc->custom_free_function(p);
 1048              		.loc 1 767 9 discriminator 3 view .LVU282
 768:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       } else
 1049              		.loc 1 768 9 view .LVU283
 768:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       } else
 1050              		.loc 1 768 11 is_stmt 0 view .LVU284
 1051 002e 2369     		ldr	r3, [r4, #16]
 768:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       } else
 1052              		.loc 1 768 9 view .LVU285
 1053 0030 2046     		mov	r0, r4
 1054 0032 9847     		blx	r3
 1055              	.LVL94:
 1056              	.L81:
 768:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       } else
 1057              		.loc 1 768 9 view .LVU286
 1058              	.LBE4:
 783:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         }
 1059              		.loc 1 783 11 is_stmt 1 discriminator 3 view .LVU287
 783:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         }
 1060              		.loc 1 783 11 discriminator 3 view .LVU288
 786:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       /* proceed to next pbuf */
 1061              		.loc 1 786 7 view .LVU289
 786:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       /* proceed to next pbuf */
 1062              		.loc 1 786 12 is_stmt 0 view .LVU290
 1063 0034 0136     		adds	r6, r6, #1
 1064              	.LVL95:
 786:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       /* proceed to next pbuf */
 1065              		.loc 1 786 12 view .LVU291
 1066 0036 F6B2     		uxtb	r6, r6
 1067              	.LVL96:
 788:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       /* p->ref > 0, this pbuf is still referenced to */
 1068              		.loc 1 788 7 is_stmt 1 view .LVU292
 788:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       /* p->ref > 0, this pbuf is still referenced to */
 1069              		.loc 1 788 9 is_stmt 0 view .LVU293
 1070 0038 2C46     		mov	r4, r5
 1071              	.LVL97:
 1072              	.L76:
 788:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       /* p->ref > 0, this pbuf is still referenced to */
 1073              		.loc 1 788 9 view .LVU294
 1074              	.LBE3:
 745:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     LWIP_PBUF_REF_T ref;
 1075              		.loc 1 745 12 is_stmt 1 view .LVU295
 1076 003a 002C     		cmp	r4, #0
 1077 003c EDD0     		beq	.L77
 1078              	.LBB6:
 746:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     SYS_ARCH_DECL_PROTECT(old_level);
 1079              		.loc 1 746 5 view .LVU296
 747:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* Since decrementing ref cannot be guaranteed to be a single machine operation
 1080              		.loc 1 747 5 view .LVU297
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 45


 751:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* all pbufs in a chain are referenced at least once */
 1081              		.loc 1 751 5 view .LVU298
 1082 003e FFF7FEFF 		bl	sys_arch_protect
 1083              	.LVL98:
 1084 0042 0746     		mov	r7, r0
 1085              	.LVL99:
 753:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* decrease reference count (number of pointers to pbuf) */
 1086              		.loc 1 753 5 view .LVU299
 753:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* decrease reference count (number of pointers to pbuf) */
 1087              		.loc 1 753 5 view .LVU300
 1088 0044 A37B     		ldrb	r3, [r4, #14]	@ zero_extendqisi2
 1089 0046 002B     		cmp	r3, #0
 1090 0048 E9D0     		beq	.L89
 1091              	.LVL100:
 1092              	.L78:
 753:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* decrease reference count (number of pointers to pbuf) */
 1093              		.loc 1 753 5 discriminator 3 view .LVU301
 753:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* decrease reference count (number of pointers to pbuf) */
 1094              		.loc 1 753 5 discriminator 3 view .LVU302
 755:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     SYS_ARCH_UNPROTECT(old_level);
 1095              		.loc 1 755 5 view .LVU303
 755:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     SYS_ARCH_UNPROTECT(old_level);
 1096              		.loc 1 755 15 is_stmt 0 view .LVU304
 1097 004a A57B     		ldrb	r5, [r4, #14]	@ zero_extendqisi2
 755:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     SYS_ARCH_UNPROTECT(old_level);
 1098              		.loc 1 755 11 view .LVU305
 1099 004c 013D     		subs	r5, r5, #1
 1100 004e EDB2     		uxtb	r5, r5
 755:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     SYS_ARCH_UNPROTECT(old_level);
 1101              		.loc 1 755 9 view .LVU306
 1102 0050 A573     		strb	r5, [r4, #14]
 1103              	.LVL101:
 756:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* this pbuf is no longer referenced to? */
 1104              		.loc 1 756 5 is_stmt 1 view .LVU307
 1105 0052 3846     		mov	r0, r7
 1106 0054 FFF7FEFF 		bl	sys_arch_unprotect
 1107              	.LVL102:
 758:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       /* remember next pbuf in chain for next iteration */
 1108              		.loc 1 758 5 view .LVU308
 758:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       /* remember next pbuf in chain for next iteration */
 1109              		.loc 1 758 8 is_stmt 0 view .LVU309
 1110 0058 002D     		cmp	r5, #0
 1111 005a DED1     		bne	.L77
 760:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       LWIP_DEBUGF( PBUF_DEBUG | LWIP_DBG_TRACE, ("pbuf_free: deallocating %p\n", (void *)p));
 1112              		.loc 1 760 7 is_stmt 1 view .LVU310
 760:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       LWIP_DEBUGF( PBUF_DEBUG | LWIP_DBG_TRACE, ("pbuf_free: deallocating %p\n", (void *)p));
 1113              		.loc 1 760 9 is_stmt 0 view .LVU311
 1114 005c 2568     		ldr	r5, [r4]
 1115              	.LVL103:
 761:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       alloc_src = pbuf_get_allocsrc(p);
 1116              		.loc 1 761 93 is_stmt 1 view .LVU312
 762:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** #if LWIP_SUPPORT_CUSTOM_PBUF
 1117              		.loc 1 762 7 view .LVU313
 762:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** #if LWIP_SUPPORT_CUSTOM_PBUF
 1118              		.loc 1 762 19 is_stmt 0 view .LVU314
 1119 005e 237B     		ldrb	r3, [r4, #12]	@ zero_extendqisi2
 762:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** #if LWIP_SUPPORT_CUSTOM_PBUF
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 46


 1120              		.loc 1 762 17 view .LVU315
 1121 0060 03F00F03 		and	r3, r3, #15
 1122              	.LVL104:
 765:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         struct pbuf_custom *pc = (struct pbuf_custom *)p;
 1123              		.loc 1 765 7 is_stmt 1 view .LVU316
 765:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         struct pbuf_custom *pc = (struct pbuf_custom *)p;
 1124              		.loc 1 765 13 is_stmt 0 view .LVU317
 1125 0064 627B     		ldrb	r2, [r4, #13]	@ zero_extendqisi2
 765:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         struct pbuf_custom *pc = (struct pbuf_custom *)p;
 1126              		.loc 1 765 10 view .LVU318
 1127 0066 12F0020F 		tst	r2, #2
 1128 006a 0AD0     		beq	.L79
 1129              	.LBB5:
 766:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         LWIP_ASSERT("pc->custom_free_function != NULL", pc->custom_free_function != NULL);
 1130              		.loc 1 766 9 is_stmt 1 view .LVU319
 1131              	.LVL105:
 767:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         pc->custom_free_function(p);
 1132              		.loc 1 767 9 view .LVU320
 767:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         pc->custom_free_function(p);
 1133              		.loc 1 767 9 view .LVU321
 1134 006c 2369     		ldr	r3, [r4, #16]
 1135              	.LVL106:
 767:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         pc->custom_free_function(p);
 1136              		.loc 1 767 9 is_stmt 0 view .LVU322
 1137 006e 002B     		cmp	r3, #0
 1138 0070 DDD1     		bne	.L80
 767:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         pc->custom_free_function(p);
 1139              		.loc 1 767 9 is_stmt 1 discriminator 1 view .LVU323
 767:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         pc->custom_free_function(p);
 1140              		.loc 1 767 9 discriminator 1 view .LVU324
 1141 0072 114B     		ldr	r3, .L92
 1142 0074 40F2FF22 		movw	r2, #767
 1143 0078 1349     		ldr	r1, .L92+16
 1144 007a 1148     		ldr	r0, .L92+8
 1145 007c FFF7FEFF 		bl	printf
 1146              	.LVL107:
 767:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         pc->custom_free_function(p);
 1147              		.loc 1 767 9 is_stmt 0 discriminator 1 view .LVU325
 1148 0080 D5E7     		b	.L80
 1149              	.LVL108:
 1150              	.L79:
 767:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         pc->custom_free_function(p);
 1151              		.loc 1 767 9 discriminator 1 view .LVU326
 1152              	.LBE5:
 773:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****           memp_free(MEMP_PBUF_POOL, p);
 1153              		.loc 1 773 9 is_stmt 1 view .LVU327
 773:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****           memp_free(MEMP_PBUF_POOL, p);
 1154              		.loc 1 773 12 is_stmt 0 view .LVU328
 1155 0082 022B     		cmp	r3, #2
 1156 0084 06D0     		beq	.L90
 776:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****           memp_free(MEMP_PBUF, p);
 1157              		.loc 1 776 16 is_stmt 1 view .LVU329
 776:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****           memp_free(MEMP_PBUF, p);
 1158              		.loc 1 776 19 is_stmt 0 view .LVU330
 1159 0086 012B     		cmp	r3, #1
 1160 0088 09D0     		beq	.L91
 779:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****           mem_free(p);
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 47


 1161              		.loc 1 779 16 is_stmt 1 view .LVU331
 779:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****           mem_free(p);
 1162              		.loc 1 779 19 is_stmt 0 view .LVU332
 1163 008a 6BB9     		cbnz	r3, .L84
 780:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         } else {
 1164              		.loc 1 780 11 is_stmt 1 view .LVU333
 1165 008c 2046     		mov	r0, r4
 1166 008e FFF7FEFF 		bl	mem_free
 1167              	.LVL109:
 780:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         } else {
 1168              		.loc 1 780 11 is_stmt 0 view .LVU334
 1169 0092 CFE7     		b	.L81
 1170              	.LVL110:
 1171              	.L90:
 774:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****           /* is this a ROM or RAM referencing pbuf? */
 1172              		.loc 1 774 11 is_stmt 1 view .LVU335
 1173 0094 2146     		mov	r1, r4
 1174 0096 0C20     		movs	r0, #12
 1175 0098 FFF7FEFF 		bl	memp_free
 1176              	.LVL111:
 774:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****           /* is this a ROM or RAM referencing pbuf? */
 1177              		.loc 1 774 11 is_stmt 0 view .LVU336
 1178 009c CAE7     		b	.L81
 1179              	.LVL112:
 1180              	.L91:
 777:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****           /* type == PBUF_RAM */
 1181              		.loc 1 777 11 is_stmt 1 view .LVU337
 1182 009e 2146     		mov	r1, r4
 1183 00a0 0B20     		movs	r0, #11
 1184 00a2 FFF7FEFF 		bl	memp_free
 1185              	.LVL113:
 777:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****           /* type == PBUF_RAM */
 1186              		.loc 1 777 11 is_stmt 0 view .LVU338
 1187 00a6 C5E7     		b	.L81
 1188              	.LVL114:
 1189              	.L84:
 783:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         }
 1190              		.loc 1 783 11 is_stmt 1 view .LVU339
 783:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         }
 1191              		.loc 1 783 11 view .LVU340
 783:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         }
 1192              		.loc 1 783 11 discriminator 1 view .LVU341
 783:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         }
 1193              		.loc 1 783 11 discriminator 1 view .LVU342
 1194 00a8 034B     		ldr	r3, .L92
 1195              	.LVL115:
 783:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         }
 1196              		.loc 1 783 11 is_stmt 0 discriminator 1 view .LVU343
 1197 00aa 40F20F32 		movw	r2, #783
 1198 00ae 0749     		ldr	r1, .L92+20
 1199 00b0 0348     		ldr	r0, .L92+8
 1200 00b2 FFF7FEFF 		bl	printf
 1201              	.LVL116:
 783:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         }
 1202              		.loc 1 783 11 discriminator 1 view .LVU344
 1203 00b6 BDE7     		b	.L81
 1204              	.L93:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 48


 1205              		.align	2
 1206              	.L92:
 1207 00b8 00000000 		.word	.LC0
 1208 00bc 30000000 		.word	.LC1
 1209 00c0 3C000000 		.word	.LC2
 1210 00c4 00000000 		.word	.LC5
 1211 00c8 18000000 		.word	.LC6
 1212 00cc 3C000000 		.word	.LC7
 1213              	.LBE6:
 1214              		.cfi_endproc
 1215              	.LFE190:
 1217              		.section	.rodata.pbuf_alloc.str1.4,"aMS",%progbits,1
 1218              		.align	2
 1219              	.LC8:
 1220 0000 70627566 		.ascii	"pbuf_alloc: pbuf q->payload properly aligned\000"
 1220      5F616C6C 
 1220      6F633A20 
 1220      70627566 
 1220      20712D3E 
 1221 002d 000000   		.align	2
 1222              	.LC9:
 1223 0030 50425546 		.ascii	"PBUF_POOL_BUFSIZE must be bigger than MEM_ALIGNMENT"
 1223      5F504F4F 
 1223      4C5F4255 
 1223      4653495A 
 1223      45206D75 
 1224 0063 00       		.ascii	"\000"
 1225              		.align	2
 1226              	.LC10:
 1227 0064 70627566 		.ascii	"pbuf_alloc: pbuf->payload properly aligned\000"
 1227      5F616C6C 
 1227      6F633A20 
 1227      70627566 
 1227      2D3E7061 
 1228 008f 00       		.align	2
 1229              	.LC11:
 1230 0090 70627566 		.ascii	"pbuf_alloc: erroneous type\000"
 1230      5F616C6C 
 1230      6F633A20 
 1230      6572726F 
 1230      6E656F75 
 1231              		.section	.text.pbuf_alloc,"ax",%progbits
 1232              		.align	1
 1233              		.global	pbuf_alloc
 1234              		.syntax unified
 1235              		.thumb
 1236              		.thumb_func
 1238              	pbuf_alloc:
 1239              	.LVL117:
 1240              	.LFB178:
 225:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   struct pbuf *p;
 1241              		.loc 1 225 1 is_stmt 1 view -0
 1242              		.cfi_startproc
 1243              		@ args = 0, pretend = 0, frame = 0
 1244              		@ frame_needed = 0, uses_anonymous_args = 0
 225:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   struct pbuf *p;
 1245              		.loc 1 225 1 is_stmt 0 view .LVU346
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 49


 1246 0000 2DE9F047 		push	{r4, r5, r6, r7, r8, r9, r10, lr}
 1247              	.LCFI21:
 1248              		.cfi_def_cfa_offset 32
 1249              		.cfi_offset 4, -32
 1250              		.cfi_offset 5, -28
 1251              		.cfi_offset 6, -24
 1252              		.cfi_offset 7, -20
 1253              		.cfi_offset 8, -16
 1254              		.cfi_offset 9, -12
 1255              		.cfi_offset 10, -8
 1256              		.cfi_offset 14, -4
 1257 0004 82B0     		sub	sp, sp, #8
 1258              	.LCFI22:
 1259              		.cfi_def_cfa_offset 40
 1260 0006 0E46     		mov	r6, r1
 1261 0008 9046     		mov	r8, r2
 226:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   u16_t offset = (u16_t)layer;
 1262              		.loc 1 226 3 is_stmt 1 view .LVU347
 227:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_DEBUGF(PBUF_DEBUG | LWIP_DBG_TRACE, ("pbuf_alloc(length=%"U16_F")\n", length));
 1263              		.loc 1 227 3 view .LVU348
 1264              	.LVL118:
 228:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 1265              		.loc 1 228 86 view .LVU349
 230:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     case PBUF_REF: /* fall through */
 1266              		.loc 1 230 3 view .LVU350
 1267 000a B2F5C17F 		cmp	r2, #386
 1268 000e 4DD0     		beq	.L106
 1269 0010 0446     		mov	r4, r0
 1270 0012 0DD8     		bhi	.L96
 1271 0014 012A     		cmp	r2, #1
 1272 0016 3FD0     		beq	.L97
 1273 0018 412A     		cmp	r2, #65
 1274 001a 3DD0     		beq	.L97
 1275              	.L98:
 295:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       return NULL;
 1276              		.loc 1 295 7 view .LVU351
 295:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       return NULL;
 1277              		.loc 1 295 7 view .LVU352
 295:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       return NULL;
 1278              		.loc 1 295 7 discriminator 1 view .LVU353
 295:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       return NULL;
 1279              		.loc 1 295 7 discriminator 1 view .LVU354
 1280 001c 494B     		ldr	r3, .L112
 1281 001e 40F22712 		movw	r2, #295
 1282              	.LVL119:
 295:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       return NULL;
 1283              		.loc 1 295 7 is_stmt 0 discriminator 1 view .LVU355
 1284 0022 4949     		ldr	r1, .L112+4
 1285              	.LVL120:
 295:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       return NULL;
 1286              		.loc 1 295 7 discriminator 1 view .LVU356
 1287 0024 4948     		ldr	r0, .L112+8
 1288              	.LVL121:
 295:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       return NULL;
 1289              		.loc 1 295 7 discriminator 1 view .LVU357
 1290 0026 FFF7FEFF 		bl	printf
 1291              	.LVL122:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 50


 295:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       return NULL;
 1292              		.loc 1 295 7 is_stmt 1 discriminator 3 view .LVU358
 295:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       return NULL;
 1293              		.loc 1 295 7 discriminator 3 view .LVU359
 296:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
 1294              		.loc 1 296 7 view .LVU360
 296:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
 1295              		.loc 1 296 14 is_stmt 0 view .LVU361
 1296 002a 4FF00009 		mov	r9, #0
 1297 002e 39E0     		b	.L94
 1298              	.LVL123:
 1299              	.L96:
 230:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     case PBUF_REF: /* fall through */
 1300              		.loc 1 230 3 view .LVU362
 1301 0030 B2F5207F 		cmp	r2, #640
 1302 0034 F2D1     		bne	.L98
 1303              	.LBB7:
 274:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       mem_size_t alloc_len = (mem_size_t)(LWIP_MEM_ALIGN_SIZE(SIZEOF_STRUCT_PBUF) + payload_len);
 1304              		.loc 1 274 7 is_stmt 1 view .LVU363
 274:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       mem_size_t alloc_len = (mem_size_t)(LWIP_MEM_ALIGN_SIZE(SIZEOF_STRUCT_PBUF) + payload_len);
 1305              		.loc 1 274 35 is_stmt 0 view .LVU364
 1306 0036 C11C     		adds	r1, r0, #3
 1307              	.LVL124:
 274:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       mem_size_t alloc_len = (mem_size_t)(LWIP_MEM_ALIGN_SIZE(SIZEOF_STRUCT_PBUF) + payload_len);
 1308              		.loc 1 274 35 view .LVU365
 1309 0038 01F4FE71 		and	r1, r1, #508
 274:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       mem_size_t alloc_len = (mem_size_t)(LWIP_MEM_ALIGN_SIZE(SIZEOF_STRUCT_PBUF) + payload_len);
 1310              		.loc 1 274 65 view .LVU366
 1311 003c F21C     		adds	r2, r6, #3
 1312              	.LVL125:
 274:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       mem_size_t alloc_len = (mem_size_t)(LWIP_MEM_ALIGN_SIZE(SIZEOF_STRUCT_PBUF) + payload_len);
 1313              		.loc 1 274 65 view .LVU367
 1314 003e 93B2     		uxth	r3, r2
 1315 0040 23F00303 		bic	r3, r3, #3
 274:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       mem_size_t alloc_len = (mem_size_t)(LWIP_MEM_ALIGN_SIZE(SIZEOF_STRUCT_PBUF) + payload_len);
 1316              		.loc 1 274 13 view .LVU368
 1317 0044 0B44     		add	r3, r3, r1
 1318 0046 9BB2     		uxth	r3, r3
 1319              	.LVL126:
 275:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 1320              		.loc 1 275 7 is_stmt 1 view .LVU369
 275:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 1321              		.loc 1 275 18 is_stmt 0 view .LVU370
 1322 0048 03F11000 		add	r0, r3, #16
 1323              	.LVL127:
 275:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 1324              		.loc 1 275 18 view .LVU371
 1325 004c 80B2     		uxth	r0, r0
 1326              	.LVL128:
 278:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****           (alloc_len < LWIP_MEM_ALIGN_SIZE(length))) {
 1327              		.loc 1 278 7 is_stmt 1 view .LVU372
 278:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****           (alloc_len < LWIP_MEM_ALIGN_SIZE(length))) {
 1328              		.loc 1 278 26 is_stmt 0 view .LVU373
 1329 004e 22F00302 		bic	r2, r2, #3
 278:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****           (alloc_len < LWIP_MEM_ALIGN_SIZE(length))) {
 1330              		.loc 1 278 10 view .LVU374
 1331 0052 9342     		cmp	r3, r2
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 51


 1332 0054 6FD3     		bcc	.L108
 278:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****           (alloc_len < LWIP_MEM_ALIGN_SIZE(length))) {
 1333              		.loc 1 278 55 discriminator 1 view .LVU375
 1334 0056 8242     		cmp	r2, r0
 1335 0058 70D8     		bhi	.L109
 284:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       if (p == NULL) {
 1336              		.loc 1 284 7 is_stmt 1 view .LVU376
 284:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       if (p == NULL) {
 1337              		.loc 1 284 26 is_stmt 0 view .LVU377
 1338 005a FFF7FEFF 		bl	mem_malloc
 1339              	.LVL129:
 285:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         return NULL;
 1340              		.loc 1 285 7 is_stmt 1 view .LVU378
 285:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         return NULL;
 1341              		.loc 1 285 10 is_stmt 0 view .LVU379
 1342 005e 8146     		mov	r9, r0
 1343 0060 00B3     		cbz	r0, .L94
 288:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****                              length, length, type, 0);
 1344              		.loc 1 288 7 is_stmt 1 view .LVU380
 288:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****                              length, length, type, 0);
 1345              		.loc 1 288 33 is_stmt 0 view .LVU381
 1346 0062 04F11001 		add	r1, r4, #16
 1347 0066 0144     		add	r1, r1, r0
 1348 0068 0331     		adds	r1, r1, #3
 288:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****                              length, length, type, 0);
 1349              		.loc 1 288 7 view .LVU382
 1350 006a 0023     		movs	r3, #0
 1351 006c 0193     		str	r3, [sp, #4]
 1352 006e CDF80080 		str	r8, [sp]
 1353 0072 3346     		mov	r3, r6
 1354 0074 3246     		mov	r2, r6
 1355 0076 21F00301 		bic	r1, r1, #3
 1356 007a FFF7FEFF 		bl	pbuf_init_alloced_pbuf
 1357              	.LVL130:
 290:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****                   ((mem_ptr_t)p->payload % MEM_ALIGNMENT) == 0);
 1358              		.loc 1 290 7 is_stmt 1 view .LVU383
 290:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****                   ((mem_ptr_t)p->payload % MEM_ALIGNMENT) == 0);
 1359              		.loc 1 290 7 view .LVU384
 1360 007e D9F80430 		ldr	r3, [r9, #4]
 1361 0082 13F0030F 		tst	r3, #3
 1362 0086 0DD0     		beq	.L94
 290:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****                   ((mem_ptr_t)p->payload % MEM_ALIGNMENT) == 0);
 1363              		.loc 1 290 7 discriminator 1 view .LVU385
 290:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****                   ((mem_ptr_t)p->payload % MEM_ALIGNMENT) == 0);
 1364              		.loc 1 290 7 discriminator 1 view .LVU386
 1365 0088 2E4B     		ldr	r3, .L112
 1366 008a 4FF49172 		mov	r2, #290
 1367 008e 3049     		ldr	r1, .L112+12
 1368 0090 2E48     		ldr	r0, .L112+8
 1369 0092 FFF7FEFF 		bl	printf
 1370              	.LVL131:
 1371 0096 05E0     		b	.L94
 1372              	.LVL132:
 1373              	.L97:
 290:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****                   ((mem_ptr_t)p->payload % MEM_ALIGNMENT) == 0);
 1374              		.loc 1 290 7 is_stmt 0 discriminator 1 view .LVU387
 1375              	.LBE7:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 52


 233:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       break;
 1376              		.loc 1 233 7 is_stmt 1 view .LVU388
 233:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       break;
 1377              		.loc 1 233 11 is_stmt 0 view .LVU389
 1378 0098 4246     		mov	r2, r8
 1379              	.LVL133:
 233:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       break;
 1380              		.loc 1 233 11 view .LVU390
 1381 009a 3146     		mov	r1, r6
 1382              	.LVL134:
 233:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       break;
 1383              		.loc 1 233 11 view .LVU391
 1384 009c 0020     		movs	r0, #0
 1385              	.LVL135:
 233:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       break;
 1386              		.loc 1 233 11 view .LVU392
 1387 009e FFF7FEFF 		bl	pbuf_alloc_reference
 1388              	.LVL136:
 1389 00a2 8146     		mov	r9, r0
 1390              	.LVL137:
 234:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     case PBUF_POOL: {
 1391              		.loc 1 234 7 is_stmt 1 view .LVU393
 1392              	.L94:
 300:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 1393              		.loc 1 300 1 is_stmt 0 view .LVU394
 1394 00a4 4846     		mov	r0, r9
 1395 00a6 02B0     		add	sp, sp, #8
 1396              	.LCFI23:
 1397              		.cfi_remember_state
 1398              		.cfi_def_cfa_offset 32
 1399              		@ sp needed
 1400 00a8 BDE8F087 		pop	{r4, r5, r6, r7, r8, r9, r10, pc}
 1401              	.LVL138:
 1402              	.L106:
 1403              	.LCFI24:
 1404              		.cfi_restore_state
 300:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 1405              		.loc 1 300 1 view .LVU395
 1406 00ac 0546     		mov	r5, r0
 230:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     case PBUF_REF: /* fall through */
 1407              		.loc 1 230 3 view .LVU396
 1408 00ae 0027     		movs	r7, #0
 1409 00b0 B946     		mov	r9, r7
 1410 00b2 13E0     		b	.L95
 1411              	.LVL139:
 1412              	.L111:
 1413              	.LBB8:
 1414              	.LBB9:
 245:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****           /* free chain so far allocated */
 1415              		.loc 1 245 11 is_stmt 1 view .LVU397
 1416 00b4 FFF7FEFF 		bl	pbuf_pool_is_empty
 1417              	.LVL140:
 247:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****             pbuf_free(p);
 1418              		.loc 1 247 11 view .LVU398
 247:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****             pbuf_free(p);
 1419              		.loc 1 247 14 is_stmt 0 view .LVU399
 1420 00b8 B9F1000F 		cmp	r9, #0
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 53


 1421 00bc 02D0     		beq	.L102
 248:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****           }
 1422              		.loc 1 248 13 is_stmt 1 view .LVU400
 1423 00be 4846     		mov	r0, r9
 1424 00c0 FFF7FEFF 		bl	pbuf_free
 1425              	.LVL141:
 1426              	.L102:
 251:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         }
 1427              		.loc 1 251 11 view .LVU401
 251:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         }
 1428              		.loc 1 251 18 is_stmt 0 view .LVU402
 1429 00c4 B946     		mov	r9, r7
 1430              	.LVL142:
 251:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         }
 1431              		.loc 1 251 18 view .LVU403
 1432 00c6 EDE7     		b	.L94
 1433              	.LVL143:
 1434              	.L104:
 258:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****                     (PBUF_POOL_BUFSIZE_ALIGNED - LWIP_MEM_ALIGN_SIZE(offset)) > 0 );
 1435              		.loc 1 258 9 is_stmt 1 discriminator 3 view .LVU404
 258:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****                     (PBUF_POOL_BUFSIZE_ALIGNED - LWIP_MEM_ALIGN_SIZE(offset)) > 0 );
 1436              		.loc 1 258 9 discriminator 3 view .LVU405
 260:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****           /* allocated head of pbuf chain (into p) */
 1437              		.loc 1 260 9 view .LVU406
 260:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****           /* allocated head of pbuf chain (into p) */
 1438              		.loc 1 260 12 is_stmt 0 view .LVU407
 1439 00c8 B9F1000F 		cmp	r9, #0
 1440 00cc 31D0     		beq	.L107
 265:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         }
 1441              		.loc 1 265 11 is_stmt 1 view .LVU408
 265:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         }
 1442              		.loc 1 265 22 is_stmt 0 view .LVU409
 1443 00ce CAF80070 		str	r7, [r10]
 1444              	.LVL144:
 1445              	.L105:
 267:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         rem_len = (u16_t)(rem_len - qlen);
 1446              		.loc 1 267 9 is_stmt 1 view .LVU410
 268:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         offset = 0;
 1447              		.loc 1 268 9 view .LVU411
 268:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         offset = 0;
 1448              		.loc 1 268 17 is_stmt 0 view .LVU412
 1449 00d2 341B     		subs	r4, r6, r4
 1450              	.LVL145:
 268:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         offset = 0;
 1451              		.loc 1 268 17 view .LVU413
 1452 00d4 A6B2     		uxth	r6, r4
 1453              	.LVL146:
 269:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       } while (rem_len > 0);
 1454              		.loc 1 269 9 is_stmt 1 view .LVU414
 269:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       } while (rem_len > 0);
 1455              		.loc 1 269 9 is_stmt 0 view .LVU415
 1456              	.LBE9:
 270:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       break;
 1457              		.loc 1 270 24 is_stmt 1 view .LVU416
 1458              	.LBB10:
 269:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       } while (rem_len > 0);
 1459              		.loc 1 269 16 is_stmt 0 view .LVU417
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 54


 1460 00d6 0025     		movs	r5, #0
 269:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       } while (rem_len > 0);
 1461              		.loc 1 269 16 view .LVU418
 1462              	.LBE10:
 270:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       break;
 1463              		.loc 1 270 24 view .LVU419
 1464 00d8 002E     		cmp	r6, #0
 1465 00da E3D0     		beq	.L94
 1466              	.LVL147:
 1467              	.L95:
 241:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         u16_t qlen;
 1468              		.loc 1 241 7 is_stmt 1 view .LVU420
 1469              	.LBB11:
 242:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         q = (struct pbuf *)memp_malloc(MEMP_PBUF_POOL);
 1470              		.loc 1 242 9 view .LVU421
 243:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         if (q == NULL) {
 1471              		.loc 1 243 9 view .LVU422
 1472 00dc BA46     		mov	r10, r7
 243:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         if (q == NULL) {
 1473              		.loc 1 243 28 is_stmt 0 view .LVU423
 1474 00de 0C20     		movs	r0, #12
 1475 00e0 FFF7FEFF 		bl	memp_malloc
 1476              	.LVL148:
 244:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****           PBUF_POOL_IS_EMPTY();
 1477              		.loc 1 244 9 is_stmt 1 view .LVU424
 244:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****           PBUF_POOL_IS_EMPTY();
 1478              		.loc 1 244 12 is_stmt 0 view .LVU425
 1479 00e4 0746     		mov	r7, r0
 1480              	.LVL149:
 244:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****           PBUF_POOL_IS_EMPTY();
 1481              		.loc 1 244 12 view .LVU426
 1482 00e6 0028     		cmp	r0, #0
 1483 00e8 E4D0     		beq	.L111
 253:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         pbuf_init_alloced_pbuf(q, LWIP_MEM_ALIGN((void *)((u8_t *)q + SIZEOF_STRUCT_PBUF + offset))
 1484              		.loc 1 253 9 is_stmt 1 view .LVU427
 253:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         pbuf_init_alloced_pbuf(q, LWIP_MEM_ALIGN((void *)((u8_t *)q + SIZEOF_STRUCT_PBUF + offset))
 1485              		.loc 1 253 16 is_stmt 0 view .LVU428
 1486 00ea EC1C     		adds	r4, r5, #3
 1487 00ec 24F00304 		bic	r4, r4, #3
 1488 00f0 A4B2     		uxth	r4, r4
 1489 00f2 C4F51474 		rsb	r4, r4, #592
 1490 00f6 A4B2     		uxth	r4, r4
 253:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         pbuf_init_alloced_pbuf(q, LWIP_MEM_ALIGN((void *)((u8_t *)q + SIZEOF_STRUCT_PBUF + offset))
 1491              		.loc 1 253 14 view .LVU429
 1492 00f8 B442     		cmp	r4, r6
 1493 00fa 28BF     		it	cs
 1494 00fc 3446     		movcs	r4, r6
 1495              	.LVL150:
 254:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****                                rem_len, qlen, type, 0);
 1496              		.loc 1 254 9 is_stmt 1 view .LVU430
 254:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****                                rem_len, qlen, type, 0);
 1497              		.loc 1 254 35 is_stmt 0 view .LVU431
 1498 00fe 05F11001 		add	r1, r5, #16
 1499 0102 0144     		add	r1, r1, r0
 1500 0104 0331     		adds	r1, r1, #3
 254:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****                                rem_len, qlen, type, 0);
 1501              		.loc 1 254 9 view .LVU432
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 55


 1502 0106 0023     		movs	r3, #0
 1503 0108 0193     		str	r3, [sp, #4]
 1504 010a CDF80080 		str	r8, [sp]
 1505 010e 2346     		mov	r3, r4
 1506 0110 3246     		mov	r2, r6
 1507 0112 21F00301 		bic	r1, r1, #3
 1508 0116 FFF7FEFF 		bl	pbuf_init_alloced_pbuf
 1509              	.LVL151:
 256:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****                     ((mem_ptr_t)q->payload % MEM_ALIGNMENT) == 0);
 1510              		.loc 1 256 9 is_stmt 1 view .LVU433
 256:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****                     ((mem_ptr_t)q->payload % MEM_ALIGNMENT) == 0);
 1511              		.loc 1 256 9 view .LVU434
 1512 011a 7B68     		ldr	r3, [r7, #4]
 1513 011c 13F0030F 		tst	r3, #3
 1514 0120 D2D0     		beq	.L104
 256:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****                     ((mem_ptr_t)q->payload % MEM_ALIGNMENT) == 0);
 1515              		.loc 1 256 9 discriminator 1 view .LVU435
 256:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****                     ((mem_ptr_t)q->payload % MEM_ALIGNMENT) == 0);
 1516              		.loc 1 256 9 discriminator 1 view .LVU436
 1517 0122 084B     		ldr	r3, .L112
 1518 0124 4FF48072 		mov	r2, #256
 1519 0128 0A49     		ldr	r1, .L112+16
 1520 012a 0848     		ldr	r0, .L112+8
 1521 012c FFF7FEFF 		bl	printf
 1522              	.LVL152:
 256:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****                     ((mem_ptr_t)q->payload % MEM_ALIGNMENT) == 0);
 1523              		.loc 1 256 9 discriminator 3 view .LVU437
 256:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****                     ((mem_ptr_t)q->payload % MEM_ALIGNMENT) == 0);
 1524              		.loc 1 256 9 discriminator 3 view .LVU438
 258:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****                     (PBUF_POOL_BUFSIZE_ALIGNED - LWIP_MEM_ALIGN_SIZE(offset)) > 0 );
 1525              		.loc 1 258 9 view .LVU439
 258:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****                     (PBUF_POOL_BUFSIZE_ALIGNED - LWIP_MEM_ALIGN_SIZE(offset)) > 0 );
 1526              		.loc 1 258 9 view .LVU440
 1527 0130 CAE7     		b	.L104
 1528              	.L107:
 262:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         } else {
 1529              		.loc 1 262 13 is_stmt 0 view .LVU441
 1530 0132 B946     		mov	r9, r7
 1531              	.LVL153:
 262:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         } else {
 1532              		.loc 1 262 13 view .LVU442
 1533 0134 CDE7     		b	.L105
 1534              	.LVL154:
 1535              	.L108:
 262:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         } else {
 1536              		.loc 1 262 13 view .LVU443
 1537              	.LBE11:
 1538              	.LBE8:
 1539              	.LBB12:
 280:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       }
 1540              		.loc 1 280 16 view .LVU444
 1541 0136 4FF00009 		mov	r9, #0
 1542 013a B3E7     		b	.L94
 1543              	.L109:
 1544 013c 4FF00009 		mov	r9, #0
 1545 0140 B0E7     		b	.L94
 1546              	.L113:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 56


 1547 0142 00BF     		.align	2
 1548              	.L112:
 1549 0144 00000000 		.word	.LC0
 1550 0148 90000000 		.word	.LC11
 1551 014c 3C000000 		.word	.LC2
 1552 0150 64000000 		.word	.LC10
 1553 0154 00000000 		.word	.LC8
 1554              	.LBE12:
 1555              		.cfi_endproc
 1556              	.LFE178:
 1558              		.section	.rodata.pbuf_realloc.str1.4,"aMS",%progbits,1
 1559              		.align	2
 1560              	.LC12:
 1561 0000 70627566 		.ascii	"pbuf_realloc: p != NULL\000"
 1561      5F726561 
 1561      6C6C6F63 
 1561      3A207020 
 1561      213D204E 
 1562              		.align	2
 1563              	.LC13:
 1564 0018 70627566 		.ascii	"pbuf_realloc: q != NULL\000"
 1564      5F726561 
 1564      6C6C6F63 
 1564      3A207120 
 1564      213D204E 
 1565              		.align	2
 1566              	.LC14:
 1567 0030 6D656D5F 		.ascii	"mem_trim returned q == NULL\000"
 1567      7472696D 
 1567      20726574 
 1567      75726E65 
 1567      64207120 
 1568              		.section	.text.pbuf_realloc,"ax",%progbits
 1569              		.align	1
 1570              		.global	pbuf_realloc
 1571              		.syntax unified
 1572              		.thumb
 1573              		.thumb_func
 1575              	pbuf_realloc:
 1576              	.LVL155:
 1577              	.LFB181:
 403:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   struct pbuf *q;
 1578              		.loc 1 403 1 is_stmt 1 view -0
 1579              		.cfi_startproc
 1580              		@ args = 0, pretend = 0, frame = 0
 1581              		@ frame_needed = 0, uses_anonymous_args = 0
 403:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   struct pbuf *q;
 1582              		.loc 1 403 1 is_stmt 0 view .LVU446
 1583 0000 F8B5     		push	{r3, r4, r5, r6, r7, lr}
 1584              	.LCFI25:
 1585              		.cfi_def_cfa_offset 24
 1586              		.cfi_offset 3, -24
 1587              		.cfi_offset 4, -20
 1588              		.cfi_offset 5, -16
 1589              		.cfi_offset 6, -12
 1590              		.cfi_offset 7, -8
 1591              		.cfi_offset 14, -4
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 57


 1592 0002 0E46     		mov	r6, r1
 404:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   u16_t rem_len; /* remaining length */
 1593              		.loc 1 404 3 is_stmt 1 view .LVU447
 405:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   u16_t shrink;
 1594              		.loc 1 405 3 view .LVU448
 406:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 1595              		.loc 1 406 3 view .LVU449
 408:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 1596              		.loc 1 408 3 view .LVU450
 408:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 1597              		.loc 1 408 3 view .LVU451
 1598 0004 0446     		mov	r4, r0
 1599 0006 B8B1     		cbz	r0, .L123
 1600              	.LVL156:
 1601              	.L115:
 408:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 1602              		.loc 1 408 3 discriminator 3 view .LVU452
 408:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 1603              		.loc 1 408 3 discriminator 3 view .LVU453
 411:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* enlarging not yet supported */
 1604              		.loc 1 411 3 view .LVU454
 411:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* enlarging not yet supported */
 1605              		.loc 1 411 19 is_stmt 0 view .LVU455
 1606 0008 2789     		ldrh	r7, [r4, #8]
 411:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* enlarging not yet supported */
 1607              		.loc 1 411 6 view .LVU456
 1608 000a B742     		cmp	r7, r6
 1609 000c 2ED9     		bls	.L114
 421:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   q = p;
 1610              		.loc 1 421 11 view .LVU457
 1611 000e 3546     		mov	r5, r6
 1612              	.LVL157:
 1613              	.L117:
 431:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
 1614              		.loc 1 431 5 is_stmt 1 discriminator 3 view .LVU458
 431:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
 1615              		.loc 1 431 5 discriminator 3 view .LVU459
 424:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* decrease remaining length by pbuf length */
 1616              		.loc 1 424 18 view .LVU460
 424:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* decrease remaining length by pbuf length */
 1617              		.loc 1 424 21 is_stmt 0 view .LVU461
 1618 0010 6389     		ldrh	r3, [r4, #10]
 424:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* decrease remaining length by pbuf length */
 1619              		.loc 1 424 18 view .LVU462
 1620 0012 AB42     		cmp	r3, r5
 1621 0014 18D2     		bcs	.L124
 426:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* decrease total length indicator */
 1622              		.loc 1 426 5 is_stmt 1 view .LVU463
 426:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* decrease total length indicator */
 1623              		.loc 1 426 13 is_stmt 0 view .LVU464
 1624 0016 EB1A     		subs	r3, r5, r3
 1625 0018 9DB2     		uxth	r5, r3
 1626              	.LVL158:
 428:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* proceed to next pbuf in chain */
 1627              		.loc 1 428 5 is_stmt 1 view .LVU465
 428:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* proceed to next pbuf in chain */
 1628              		.loc 1 428 27 is_stmt 0 view .LVU466
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 58


 1629 001a 2389     		ldrh	r3, [r4, #8]
 428:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* proceed to next pbuf in chain */
 1630              		.loc 1 428 18 view .LVU467
 1631 001c F21B     		subs	r2, r6, r7
 1632 001e 1344     		add	r3, r3, r2
 428:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* proceed to next pbuf in chain */
 1633              		.loc 1 428 16 view .LVU468
 1634 0020 2381     		strh	r3, [r4, #8]	@ movhi
 430:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     LWIP_ASSERT("pbuf_realloc: q != NULL", q != NULL);
 1635              		.loc 1 430 5 is_stmt 1 view .LVU469
 430:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     LWIP_ASSERT("pbuf_realloc: q != NULL", q != NULL);
 1636              		.loc 1 430 7 is_stmt 0 view .LVU470
 1637 0022 2468     		ldr	r4, [r4]
 1638              	.LVL159:
 431:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
 1639              		.loc 1 431 5 is_stmt 1 view .LVU471
 431:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
 1640              		.loc 1 431 5 view .LVU472
 1641 0024 002C     		cmp	r4, #0
 1642 0026 F3D1     		bne	.L117
 431:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
 1643              		.loc 1 431 5 discriminator 1 view .LVU473
 431:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
 1644              		.loc 1 431 5 discriminator 1 view .LVU474
 1645 0028 1A4B     		ldr	r3, .L126
 1646 002a 40F2AF12 		movw	r2, #431
 1647 002e 1A49     		ldr	r1, .L126+4
 1648 0030 1A48     		ldr	r0, .L126+8
 1649 0032 FFF7FEFF 		bl	printf
 1650              	.LVL160:
 1651 0036 EBE7     		b	.L117
 1652              	.LVL161:
 1653              	.L123:
 408:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 1654              		.loc 1 408 3 discriminator 1 view .LVU475
 408:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 1655              		.loc 1 408 3 discriminator 1 view .LVU476
 1656 0038 164B     		ldr	r3, .L126
 1657 003a 4FF4CC72 		mov	r2, #408
 1658 003e 1849     		ldr	r1, .L126+12
 1659              	.LVL162:
 408:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 1660              		.loc 1 408 3 is_stmt 0 discriminator 1 view .LVU477
 1661 0040 1648     		ldr	r0, .L126+8
 1662              	.LVL163:
 408:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 1663              		.loc 1 408 3 discriminator 1 view .LVU478
 1664 0042 FFF7FEFF 		bl	printf
 1665              	.LVL164:
 1666 0046 DFE7     		b	.L115
 1667              	.LVL165:
 1668              	.L124:
 438:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** #if LWIP_SUPPORT_CUSTOM_PBUF
 1669              		.loc 1 438 3 is_stmt 1 view .LVU479
 438:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** #if LWIP_SUPPORT_CUSTOM_PBUF
 1670              		.loc 1 438 7 is_stmt 0 view .LVU480
 1671 0048 227B     		ldrb	r2, [r4, #12]	@ zero_extendqisi2
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 59


 438:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** #if LWIP_SUPPORT_CUSTOM_PBUF
 1672              		.loc 1 438 6 view .LVU481
 1673 004a 12F00F0F 		tst	r2, #15
 1674 004e 05D1     		bne	.L120
 438:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** #if LWIP_SUPPORT_CUSTOM_PBUF
 1675              		.loc 1 438 65 discriminator 1 view .LVU482
 1676 0050 AB42     		cmp	r3, r5
 1677 0052 03D0     		beq	.L120
 440:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** #endif /* LWIP_SUPPORT_CUSTOM_PBUF */
 1678              		.loc 1 440 13 view .LVU483
 1679 0054 637B     		ldrb	r3, [r4, #13]	@ zero_extendqisi2
 440:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** #endif /* LWIP_SUPPORT_CUSTOM_PBUF */
 1680              		.loc 1 440 7 view .LVU484
 1681 0056 13F0020F 		tst	r3, #2
 1682 005a 08D0     		beq	.L125
 1683              	.L120:
 445:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
 1684              		.loc 1 445 5 is_stmt 1 discriminator 3 view .LVU485
 445:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
 1685              		.loc 1 445 5 discriminator 3 view .LVU486
 448:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   q->tot_len = q->len;
 1686              		.loc 1 448 3 view .LVU487
 448:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   q->tot_len = q->len;
 1687              		.loc 1 448 10 is_stmt 0 view .LVU488
 1688 005c 6581     		strh	r5, [r4, #10]	@ movhi
 449:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 1689              		.loc 1 449 3 is_stmt 1 view .LVU489
 449:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 1690              		.loc 1 449 14 is_stmt 0 view .LVU490
 1691 005e 2581     		strh	r5, [r4, #8]	@ movhi
 452:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* free remaining pbufs in chain */
 1692              		.loc 1 452 3 is_stmt 1 view .LVU491
 452:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* free remaining pbufs in chain */
 1693              		.loc 1 452 8 is_stmt 0 view .LVU492
 1694 0060 2068     		ldr	r0, [r4]
 452:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* free remaining pbufs in chain */
 1695              		.loc 1 452 6 view .LVU493
 1696 0062 08B1     		cbz	r0, .L121
 454:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
 1697              		.loc 1 454 5 is_stmt 1 view .LVU494
 1698 0064 FFF7FEFF 		bl	pbuf_free
 1699              	.LVL166:
 1700              	.L121:
 457:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 1701              		.loc 1 457 3 view .LVU495
 457:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 1702              		.loc 1 457 11 is_stmt 0 view .LVU496
 1703 0068 0023     		movs	r3, #0
 1704 006a 2360     		str	r3, [r4]
 1705              	.LVL167:
 1706              	.L114:
 459:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 1707              		.loc 1 459 1 view .LVU497
 1708 006c F8BD     		pop	{r3, r4, r5, r6, r7, pc}
 1709              	.LVL168:
 1710              	.L125:
 444:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     LWIP_ASSERT("mem_trim returned q == NULL", q != NULL);
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 60


 1711              		.loc 1 444 5 is_stmt 1 view .LVU498
 444:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     LWIP_ASSERT("mem_trim returned q == NULL", q != NULL);
 1712              		.loc 1 444 59 is_stmt 0 view .LVU499
 1713 006e 6368     		ldr	r3, [r4, #4]
 444:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     LWIP_ASSERT("mem_trim returned q == NULL", q != NULL);
 1714              		.loc 1 444 69 view .LVU500
 1715 0070 1B1B     		subs	r3, r3, r4
 444:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     LWIP_ASSERT("mem_trim returned q == NULL", q != NULL);
 1716              		.loc 1 444 24 view .LVU501
 1717 0072 E918     		adds	r1, r5, r3
 1718 0074 89B2     		uxth	r1, r1
 1719 0076 2046     		mov	r0, r4
 1720 0078 FFF7FEFF 		bl	mem_trim
 1721              	.LVL169:
 445:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
 1722              		.loc 1 445 5 is_stmt 1 view .LVU502
 445:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
 1723              		.loc 1 445 5 view .LVU503
 1724 007c 0446     		mov	r4, r0
 1725 007e 0028     		cmp	r0, #0
 1726 0080 ECD1     		bne	.L120
 445:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
 1727              		.loc 1 445 5 discriminator 1 view .LVU504
 445:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
 1728              		.loc 1 445 5 discriminator 1 view .LVU505
 1729 0082 044B     		ldr	r3, .L126
 1730 0084 40F2BD12 		movw	r2, #445
 1731 0088 0649     		ldr	r1, .L126+16
 1732 008a 0448     		ldr	r0, .L126+8
 1733              	.LVL170:
 445:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
 1734              		.loc 1 445 5 is_stmt 0 discriminator 1 view .LVU506
 1735 008c FFF7FEFF 		bl	printf
 1736              	.LVL171:
 1737 0090 E4E7     		b	.L120
 1738              	.L127:
 1739 0092 00BF     		.align	2
 1740              	.L126:
 1741 0094 00000000 		.word	.LC0
 1742 0098 18000000 		.word	.LC13
 1743 009c 3C000000 		.word	.LC2
 1744 00a0 00000000 		.word	.LC12
 1745 00a4 30000000 		.word	.LC14
 1746              		.cfi_endproc
 1747              	.LFE181:
 1749              		.section	.text.pbuf_free_header,"ax",%progbits
 1750              		.align	1
 1751              		.global	pbuf_free_header
 1752              		.syntax unified
 1753              		.thumb
 1754              		.thumb_func
 1756              	pbuf_free_header:
 1757              	.LVL172:
 1758              	.LFB189:
 672:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   struct pbuf *p = q;
 1759              		.loc 1 672 1 is_stmt 1 view -0
 1760              		.cfi_startproc
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 61


 1761              		@ args = 0, pretend = 0, frame = 0
 1762              		@ frame_needed = 0, uses_anonymous_args = 0
 672:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   struct pbuf *p = q;
 1763              		.loc 1 672 1 is_stmt 0 view .LVU508
 1764 0000 70B5     		push	{r4, r5, r6, lr}
 1765              	.LCFI26:
 1766              		.cfi_def_cfa_offset 16
 1767              		.cfi_offset 4, -16
 1768              		.cfi_offset 5, -12
 1769              		.cfi_offset 6, -8
 1770              		.cfi_offset 14, -4
 1771 0002 0546     		mov	r5, r0
 1772 0004 0C46     		mov	r4, r1
 673:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   u16_t free_left = size;
 1773              		.loc 1 673 3 is_stmt 1 view .LVU509
 1774              	.LVL173:
 674:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   while (free_left && p) {
 1775              		.loc 1 674 3 view .LVU510
 675:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     if (free_left >= p->len) {
 1776              		.loc 1 675 3 view .LVU511
 675:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     if (free_left >= p->len) {
 1777              		.loc 1 675 9 is_stmt 0 view .LVU512
 1778 0006 04E0     		b	.L129
 1779              	.LVL174:
 1780              	.L130:
 683:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       free_left = 0;
 1781              		.loc 1 683 7 is_stmt 1 view .LVU513
 1782 0008 2146     		mov	r1, r4
 1783 000a 2846     		mov	r0, r5
 1784 000c FFF7FEFF 		bl	pbuf_remove_header
 1785              	.LVL175:
 684:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     }
 1786              		.loc 1 684 7 view .LVU514
 684:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     }
 1787              		.loc 1 684 17 is_stmt 0 view .LVU515
 1788 0010 0024     		movs	r4, #0
 1789              	.LVL176:
 1790              	.L129:
 675:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     if (free_left >= p->len) {
 1791              		.loc 1 675 20 is_stmt 1 view .LVU516
 675:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     if (free_left >= p->len) {
 1792              		.loc 1 675 10 is_stmt 0 view .LVU517
 1793 0012 231E     		subs	r3, r4, #0
 1794 0014 18BF     		it	ne
 1795 0016 0123     		movne	r3, #1
 675:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     if (free_left >= p->len) {
 1796              		.loc 1 675 20 view .LVU518
 1797 0018 6DB1     		cbz	r5, .L134
 675:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     if (free_left >= p->len) {
 1798              		.loc 1 675 20 view .LVU519
 1799 001a 63B1     		cbz	r3, .L134
 676:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       struct pbuf *f = p;
 1800              		.loc 1 676 5 is_stmt 1 view .LVU520
 676:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       struct pbuf *f = p;
 1801              		.loc 1 676 23 is_stmt 0 view .LVU521
 1802 001c 6B89     		ldrh	r3, [r5, #10]
 676:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       struct pbuf *f = p;
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 62


 1803              		.loc 1 676 8 view .LVU522
 1804 001e A342     		cmp	r3, r4
 1805 0020 F2D8     		bhi	.L130
 1806              	.LBB13:
 677:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       free_left = (u16_t)(free_left - p->len);
 1807              		.loc 1 677 7 is_stmt 1 view .LVU523
 1808              	.LVL177:
 678:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       p = p->next;
 1809              		.loc 1 678 7 view .LVU524
 678:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       p = p->next;
 1810              		.loc 1 678 17 is_stmt 0 view .LVU525
 1811 0022 E41A     		subs	r4, r4, r3
 1812              	.LVL178:
 678:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       p = p->next;
 1813              		.loc 1 678 17 view .LVU526
 1814 0024 A4B2     		uxth	r4, r4
 1815              	.LVL179:
 679:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       f->next = 0;
 1816              		.loc 1 679 7 is_stmt 1 view .LVU527
 679:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       f->next = 0;
 1817              		.loc 1 679 9 is_stmt 0 view .LVU528
 1818 0026 2E68     		ldr	r6, [r5]
 1819              	.LVL180:
 680:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       pbuf_free(f);
 1820              		.loc 1 680 7 is_stmt 1 view .LVU529
 680:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       pbuf_free(f);
 1821              		.loc 1 680 15 is_stmt 0 view .LVU530
 1822 0028 0023     		movs	r3, #0
 1823 002a 2B60     		str	r3, [r5]
 681:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     } else {
 1824              		.loc 1 681 7 is_stmt 1 view .LVU531
 1825 002c 2846     		mov	r0, r5
 1826 002e FFF7FEFF 		bl	pbuf_free
 1827              	.LVL181:
 679:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       f->next = 0;
 1828              		.loc 1 679 9 is_stmt 0 view .LVU532
 1829 0032 3546     		mov	r5, r6
 1830              	.LVL182:
 679:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       f->next = 0;
 1831              		.loc 1 679 9 view .LVU533
 1832              	.LBE13:
 1833 0034 EDE7     		b	.L129
 1834              	.LVL183:
 1835              	.L134:
 687:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** }
 1836              		.loc 1 687 3 is_stmt 1 view .LVU534
 688:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 1837              		.loc 1 688 1 is_stmt 0 view .LVU535
 1838 0036 2846     		mov	r0, r5
 1839 0038 70BD     		pop	{r4, r5, r6, pc}
 688:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 1840              		.loc 1 688 1 view .LVU536
 1841              		.cfi_endproc
 1842              	.LFE189:
 1844              		.section	.text.pbuf_clen,"ax",%progbits
 1845              		.align	1
 1846              		.global	pbuf_clen
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 63


 1847              		.syntax unified
 1848              		.thumb
 1849              		.thumb_func
 1851              	pbuf_clen:
 1852              	.LVL184:
 1853              	.LFB191:
 810:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   u16_t len;
 1854              		.loc 1 810 1 is_stmt 1 view -0
 1855              		.cfi_startproc
 1856              		@ args = 0, pretend = 0, frame = 0
 1857              		@ frame_needed = 0, uses_anonymous_args = 0
 1858              		@ link register save eliminated.
 810:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   u16_t len;
 1859              		.loc 1 810 1 is_stmt 0 view .LVU538
 1860 0000 0346     		mov	r3, r0
 811:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 1861              		.loc 1 811 3 is_stmt 1 view .LVU539
 813:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   while (p != NULL) {
 1862              		.loc 1 813 3 view .LVU540
 1863              	.LVL185:
 814:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     ++len;
 1864              		.loc 1 814 3 view .LVU541
 813:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   while (p != NULL) {
 1865              		.loc 1 813 7 is_stmt 0 view .LVU542
 1866 0002 0020     		movs	r0, #0
 1867              	.LVL186:
 814:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     ++len;
 1868              		.loc 1 814 9 view .LVU543
 1869 0004 02E0     		b	.L137
 1870              	.LVL187:
 1871              	.L138:
 815:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     p = p->next;
 1872              		.loc 1 815 5 is_stmt 1 view .LVU544
 1873 0006 0130     		adds	r0, r0, #1
 1874              	.LVL188:
 815:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     p = p->next;
 1875              		.loc 1 815 5 is_stmt 0 view .LVU545
 1876 0008 80B2     		uxth	r0, r0
 1877              	.LVL189:
 816:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
 1878              		.loc 1 816 5 is_stmt 1 view .LVU546
 816:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
 1879              		.loc 1 816 7 is_stmt 0 view .LVU547
 1880 000a 1B68     		ldr	r3, [r3]
 1881              	.LVL190:
 1882              	.L137:
 814:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     ++len;
 1883              		.loc 1 814 12 is_stmt 1 view .LVU548
 1884 000c 002B     		cmp	r3, #0
 1885 000e FAD1     		bne	.L138
 818:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** }
 1886              		.loc 1 818 3 view .LVU549
 819:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 1887              		.loc 1 819 1 is_stmt 0 view .LVU550
 1888 0010 7047     		bx	lr
 1889              		.cfi_endproc
 1890              	.LFE191:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 64


 1892              		.section	.rodata.pbuf_ref.str1.4,"aMS",%progbits,1
 1893              		.align	2
 1894              	.LC15:
 1895 0000 70627566 		.ascii	"pbuf ref overflow\000"
 1895      20726566 
 1895      206F7665 
 1895      72666C6F 
 1895      7700
 1896              		.section	.text.pbuf_ref,"ax",%progbits
 1897              		.align	1
 1898              		.global	pbuf_ref
 1899              		.syntax unified
 1900              		.thumb
 1901              		.thumb_func
 1903              	pbuf_ref:
 1904              	.LVL191:
 1905              	.LFB192:
 830:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* pbuf given? */
 1906              		.loc 1 830 1 is_stmt 1 view -0
 1907              		.cfi_startproc
 1908              		@ args = 0, pretend = 0, frame = 0
 1909              		@ frame_needed = 0, uses_anonymous_args = 0
 832:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     SYS_ARCH_SET(p->ref, (LWIP_PBUF_REF_T)(p->ref + 1));
 1910              		.loc 1 832 3 view .LVU552
 832:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     SYS_ARCH_SET(p->ref, (LWIP_PBUF_REF_T)(p->ref + 1));
 1911              		.loc 1 832 6 is_stmt 0 view .LVU553
 1912 0000 98B1     		cbz	r0, .L142
 830:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* pbuf given? */
 1913              		.loc 1 830 1 view .LVU554
 1914 0002 10B5     		push	{r4, lr}
 1915              	.LCFI27:
 1916              		.cfi_def_cfa_offset 8
 1917              		.cfi_offset 4, -8
 1918              		.cfi_offset 14, -4
 1919 0004 0446     		mov	r4, r0
 833:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     LWIP_ASSERT("pbuf ref overflow", p->ref > 0);
 1920              		.loc 1 833 5 is_stmt 1 view .LVU555
 1921              	.LBB14:
 833:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     LWIP_ASSERT("pbuf ref overflow", p->ref > 0);
 1922              		.loc 1 833 5 view .LVU556
 833:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     LWIP_ASSERT("pbuf ref overflow", p->ref > 0);
 1923              		.loc 1 833 5 view .LVU557
 1924 0006 FFF7FEFF 		bl	sys_arch_protect
 1925              	.LVL192:
 833:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     LWIP_ASSERT("pbuf ref overflow", p->ref > 0);
 1926              		.loc 1 833 5 discriminator 1 view .LVU558
 1927 000a A37B     		ldrb	r3, [r4, #14]	@ zero_extendqisi2
 1928 000c 0133     		adds	r3, r3, #1
 1929 000e A373     		strb	r3, [r4, #14]
 833:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     LWIP_ASSERT("pbuf ref overflow", p->ref > 0);
 1930              		.loc 1 833 5 discriminator 1 view .LVU559
 1931 0010 FFF7FEFF 		bl	sys_arch_unprotect
 1932              	.LVL193:
 833:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     LWIP_ASSERT("pbuf ref overflow", p->ref > 0);
 1933              		.loc 1 833 5 is_stmt 0 discriminator 1 view .LVU560
 1934              	.LBE14:
 833:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     LWIP_ASSERT("pbuf ref overflow", p->ref > 0);
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 65


 1935              		.loc 1 833 5 is_stmt 1 discriminator 2 view .LVU561
 834:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
 1936              		.loc 1 834 5 view .LVU562
 834:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
 1937              		.loc 1 834 5 view .LVU563
 1938 0014 A37B     		ldrb	r3, [r4, #14]	@ zero_extendqisi2
 1939 0016 03B1     		cbz	r3, .L145
 1940              	.L139:
 836:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 1941              		.loc 1 836 1 is_stmt 0 view .LVU564
 1942 0018 10BD     		pop	{r4, pc}
 1943              	.LVL194:
 1944              	.L145:
 834:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
 1945              		.loc 1 834 5 is_stmt 1 discriminator 1 view .LVU565
 834:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
 1946              		.loc 1 834 5 discriminator 1 view .LVU566
 1947 001a 044B     		ldr	r3, .L146
 1948 001c 40F24232 		movw	r2, #834
 1949 0020 0349     		ldr	r1, .L146+4
 1950 0022 0448     		ldr	r0, .L146+8
 1951 0024 FFF7FEFF 		bl	printf
 1952              	.LVL195:
 834:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
 1953              		.loc 1 834 5 discriminator 3 view .LVU567
 834:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
 1954              		.loc 1 834 5 discriminator 3 view .LVU568
 836:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 1955              		.loc 1 836 1 is_stmt 0 view .LVU569
 1956 0028 F6E7     		b	.L139
 1957              	.LVL196:
 1958              	.L142:
 1959              	.LCFI28:
 1960              		.cfi_def_cfa_offset 0
 1961              		.cfi_restore 4
 1962              		.cfi_restore 14
 836:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 1963              		.loc 1 836 1 view .LVU570
 1964 002a 7047     		bx	lr
 1965              	.L147:
 1966              		.align	2
 1967              	.L146:
 1968 002c 00000000 		.word	.LC0
 1969 0030 00000000 		.word	.LC15
 1970 0034 3C000000 		.word	.LC2
 1971              		.cfi_endproc
 1972              	.LFE192:
 1974              		.section	.rodata.pbuf_cat.str1.4,"aMS",%progbits,1
 1975              		.align	2
 1976              	.LC16:
 1977 0000 28682021 		.ascii	"(h != NULL) && (t != NULL) (programmer violates API"
 1977      3D204E55 
 1977      4C4C2920 
 1977      26262028 
 1977      7420213D 
 1978 0033 2900     		.ascii	")\000"
 1979 0035 000000   		.align	2
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 66


 1980              	.LC17:
 1981 0038 702D3E74 		.ascii	"p->tot_len == p->len (of last pbuf in chain)\000"
 1981      6F745F6C 
 1981      656E203D 
 1981      3D20702D 
 1981      3E6C656E 
 1982 0065 000000   		.align	2
 1983              	.LC18:
 1984 0068 702D3E6E 		.ascii	"p->next == NULL\000"
 1984      65787420 
 1984      3D3D204E 
 1984      554C4C00 
 1985              		.section	.text.pbuf_cat,"ax",%progbits
 1986              		.align	1
 1987              		.global	pbuf_cat
 1988              		.syntax unified
 1989              		.thumb
 1990              		.thumb_func
 1992              	pbuf_cat:
 1993              	.LVL197:
 1994              	.LFB193:
 854:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   struct pbuf *p;
 1995              		.loc 1 854 1 is_stmt 1 view -0
 1996              		.cfi_startproc
 1997              		@ args = 0, pretend = 0, frame = 0
 1998              		@ frame_needed = 0, uses_anonymous_args = 0
 854:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   struct pbuf *p;
 1999              		.loc 1 854 1 is_stmt 0 view .LVU572
 2000 0000 38B5     		push	{r3, r4, r5, lr}
 2001              	.LCFI29:
 2002              		.cfi_def_cfa_offset 16
 2003              		.cfi_offset 3, -16
 2004              		.cfi_offset 4, -12
 2005              		.cfi_offset 5, -8
 2006              		.cfi_offset 14, -4
 855:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 2007              		.loc 1 855 3 is_stmt 1 view .LVU573
 857:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****              ((h != NULL) && (t != NULL)), return;);
 2008              		.loc 1 857 3 view .LVU574
 857:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****              ((h != NULL) && (t != NULL)), return;);
 2009              		.loc 1 857 3 view .LVU575
 2010 0002 0446     		mov	r4, r0
 2011 0004 0D46     		mov	r5, r1
 2012 0006 0029     		cmp	r1, #0
 2013 0008 18BF     		it	ne
 2014 000a 0028     		cmpne	r0, #0
 2015 000c 0CD1     		bne	.L149
 857:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****              ((h != NULL) && (t != NULL)), return;);
 2016              		.loc 1 857 3 discriminator 1 view .LVU576
 857:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****              ((h != NULL) && (t != NULL)), return;);
 2017              		.loc 1 857 3 discriminator 1 view .LVU577
 2018 000e 154B     		ldr	r3, .L156
 2019 0010 40F25932 		movw	r2, #857
 2020 0014 1449     		ldr	r1, .L156+4
 2021              	.LVL198:
 857:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****              ((h != NULL) && (t != NULL)), return;);
 2022              		.loc 1 857 3 is_stmt 0 discriminator 1 view .LVU578
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 67


 2023 0016 1548     		ldr	r0, .L156+8
 2024              	.LVL199:
 857:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****              ((h != NULL) && (t != NULL)), return;);
 2025              		.loc 1 857 3 discriminator 1 view .LVU579
 2026 0018 FFF7FEFF 		bl	printf
 2027              	.LVL200:
 857:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****              ((h != NULL) && (t != NULL)), return;);
 2028              		.loc 1 857 3 is_stmt 1 discriminator 1 view .LVU580
 857:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****              ((h != NULL) && (t != NULL)), return;);
 2029              		.loc 1 857 3 discriminator 1 view .LVU581
 857:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****              ((h != NULL) && (t != NULL)), return;);
 2030              		.loc 1 857 3 is_stmt 0 view .LVU582
 2031 001c 19E0     		b	.L148
 2032              	.LVL201:
 2033              	.L151:
 863:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
 2034              		.loc 1 863 5 is_stmt 1 view .LVU583
 863:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
 2035              		.loc 1 863 27 is_stmt 0 view .LVU584
 2036 001e 2389     		ldrh	r3, [r4, #8]
 863:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
 2037              		.loc 1 863 40 view .LVU585
 2038 0020 2989     		ldrh	r1, [r5, #8]
 863:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
 2039              		.loc 1 863 18 view .LVU586
 2040 0022 0B44     		add	r3, r3, r1
 863:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
 2041              		.loc 1 863 16 view .LVU587
 2042 0024 2381     		strh	r3, [r4, #8]	@ movhi
 861:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* add total length of second chain to all totals of first chain */
 2043              		.loc 1 861 34 is_stmt 1 discriminator 3 view .LVU588
 2044              	.LVL202:
 861:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* add total length of second chain to all totals of first chain */
 2045              		.loc 1 861 34 is_stmt 0 discriminator 3 view .LVU589
 2046 0026 1446     		mov	r4, r2
 2047              	.LVL203:
 2048              	.L149:
 861:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* add total length of second chain to all totals of first chain */
 2049              		.loc 1 861 23 is_stmt 1 discriminator 1 view .LVU590
 861:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* add total length of second chain to all totals of first chain */
 2050              		.loc 1 861 16 is_stmt 0 discriminator 1 view .LVU591
 2051 0028 2268     		ldr	r2, [r4]
 861:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* add total length of second chain to all totals of first chain */
 2052              		.loc 1 861 23 discriminator 1 view .LVU592
 2053 002a 002A     		cmp	r2, #0
 2054 002c F7D1     		bne	.L151
 866:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ASSERT("p->next == NULL", p->next == NULL);
 2055              		.loc 1 866 3 is_stmt 1 view .LVU593
 866:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ASSERT("p->next == NULL", p->next == NULL);
 2056              		.loc 1 866 3 view .LVU594
 2057 002e 2289     		ldrh	r2, [r4, #8]
 2058 0030 6389     		ldrh	r3, [r4, #10]
 2059 0032 9A42     		cmp	r2, r3
 2060 0034 0ED1     		bne	.L155
 2061              	.LVL204:
 2062              	.L152:
 866:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ASSERT("p->next == NULL", p->next == NULL);
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 68


 2063              		.loc 1 866 3 discriminator 3 view .LVU595
 866:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ASSERT("p->next == NULL", p->next == NULL);
 2064              		.loc 1 866 3 discriminator 3 view .LVU596
 867:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* add total length of second chain to last pbuf total of first chain */
 2065              		.loc 1 867 3 view .LVU597
 867:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* add total length of second chain to last pbuf total of first chain */
 2066              		.loc 1 867 3 view .LVU598
 2067 0036 2368     		ldr	r3, [r4]
 2068 0038 33B1     		cbz	r3, .L153
 867:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* add total length of second chain to last pbuf total of first chain */
 2069              		.loc 1 867 3 discriminator 1 view .LVU599
 867:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* add total length of second chain to last pbuf total of first chain */
 2070              		.loc 1 867 3 discriminator 1 view .LVU600
 2071 003a 0A4B     		ldr	r3, .L156
 2072 003c 40F26332 		movw	r2, #867
 2073 0040 0B49     		ldr	r1, .L156+12
 2074 0042 0A48     		ldr	r0, .L156+8
 2075 0044 FFF7FEFF 		bl	printf
 2076              	.LVL205:
 2077              	.L153:
 867:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* add total length of second chain to last pbuf total of first chain */
 2078              		.loc 1 867 3 discriminator 3 view .LVU601
 867:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* add total length of second chain to last pbuf total of first chain */
 2079              		.loc 1 867 3 discriminator 3 view .LVU602
 869:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* chain last pbuf of head (p) with first of tail (t) */
 2080              		.loc 1 869 3 view .LVU603
 869:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* chain last pbuf of head (p) with first of tail (t) */
 2081              		.loc 1 869 25 is_stmt 0 view .LVU604
 2082 0048 2389     		ldrh	r3, [r4, #8]
 869:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* chain last pbuf of head (p) with first of tail (t) */
 2083              		.loc 1 869 38 view .LVU605
 2084 004a 2A89     		ldrh	r2, [r5, #8]
 869:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* chain last pbuf of head (p) with first of tail (t) */
 2085              		.loc 1 869 16 view .LVU606
 2086 004c 1344     		add	r3, r3, r2
 869:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* chain last pbuf of head (p) with first of tail (t) */
 2087              		.loc 1 869 14 view .LVU607
 2088 004e 2381     		strh	r3, [r4, #8]	@ movhi
 871:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* p->next now references t, but the caller will drop its reference to t,
 2089              		.loc 1 871 3 is_stmt 1 view .LVU608
 871:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* p->next now references t, but the caller will drop its reference to t,
 2090              		.loc 1 871 11 is_stmt 0 view .LVU609
 2091 0050 2560     		str	r5, [r4]
 2092              	.LVL206:
 2093              	.L148:
 875:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 2094              		.loc 1 875 1 view .LVU610
 2095 0052 38BD     		pop	{r3, r4, r5, pc}
 2096              	.LVL207:
 2097              	.L155:
 866:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ASSERT("p->next == NULL", p->next == NULL);
 2098              		.loc 1 866 3 is_stmt 1 discriminator 1 view .LVU611
 866:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ASSERT("p->next == NULL", p->next == NULL);
 2099              		.loc 1 866 3 discriminator 1 view .LVU612
 2100 0054 034B     		ldr	r3, .L156
 2101 0056 40F26232 		movw	r2, #866
 2102 005a 0649     		ldr	r1, .L156+16
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 69


 2103 005c 0348     		ldr	r0, .L156+8
 2104              	.LVL208:
 866:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ASSERT("p->next == NULL", p->next == NULL);
 2105              		.loc 1 866 3 is_stmt 0 discriminator 1 view .LVU613
 2106 005e FFF7FEFF 		bl	printf
 2107              	.LVL209:
 2108 0062 E8E7     		b	.L152
 2109              	.L157:
 2110              		.align	2
 2111              	.L156:
 2112 0064 00000000 		.word	.LC0
 2113 0068 00000000 		.word	.LC16
 2114 006c 3C000000 		.word	.LC2
 2115 0070 68000000 		.word	.LC18
 2116 0074 38000000 		.word	.LC17
 2117              		.cfi_endproc
 2118              	.LFE193:
 2120              		.section	.text.pbuf_chain,"ax",%progbits
 2121              		.align	1
 2122              		.global	pbuf_chain
 2123              		.syntax unified
 2124              		.thumb
 2125              		.thumb_func
 2127              	pbuf_chain:
 2128              	.LVL210:
 2129              	.LFB194:
 896:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   pbuf_cat(h, t);
 2130              		.loc 1 896 1 is_stmt 1 view -0
 2131              		.cfi_startproc
 2132              		@ args = 0, pretend = 0, frame = 0
 2133              		@ frame_needed = 0, uses_anonymous_args = 0
 896:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   pbuf_cat(h, t);
 2134              		.loc 1 896 1 is_stmt 0 view .LVU615
 2135 0000 10B5     		push	{r4, lr}
 2136              	.LCFI30:
 2137              		.cfi_def_cfa_offset 8
 2138              		.cfi_offset 4, -8
 2139              		.cfi_offset 14, -4
 2140 0002 0C46     		mov	r4, r1
 897:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* t is now referenced by h */
 2141              		.loc 1 897 3 is_stmt 1 view .LVU616
 2142 0004 FFF7FEFF 		bl	pbuf_cat
 2143              	.LVL211:
 899:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_DEBUGF(PBUF_DEBUG | LWIP_DBG_TRACE, ("pbuf_chain: %p references %p\n", (void *)h, (void *)t)
 2144              		.loc 1 899 3 view .LVU617
 2145 0008 2046     		mov	r0, r4
 2146 000a FFF7FEFF 		bl	pbuf_ref
 2147              	.LVL212:
 900:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** }
 2148              		.loc 1 900 101 view .LVU618
 901:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 2149              		.loc 1 901 1 is_stmt 0 view .LVU619
 2150 000e 10BD     		pop	{r4, pc}
 901:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 2151              		.loc 1 901 1 view .LVU620
 2152              		.cfi_endproc
 2153              	.LFE194:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 70


 2155              		.section	.rodata.pbuf_dechain.str1.4,"aMS",%progbits,1
 2156              		.align	2
 2157              	.LC19:
 2158 0000 702D3E74 		.ascii	"p->tot_len == p->len + q->tot_len\000"
 2158      6F745F6C 
 2158      656E203D 
 2158      3D20702D 
 2158      3E6C656E 
 2159 0022 0000     		.align	2
 2160              	.LC20:
 2161 0024 702D3E74 		.ascii	"p->tot_len == p->len\000"
 2161      6F745F6C 
 2161      656E203D 
 2161      3D20702D 
 2161      3E6C656E 
 2162              		.section	.text.pbuf_dechain,"ax",%progbits
 2163              		.align	1
 2164              		.global	pbuf_dechain
 2165              		.syntax unified
 2166              		.thumb
 2167              		.thumb_func
 2169              	pbuf_dechain:
 2170              	.LVL213:
 2171              	.LFB195:
 913:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   struct pbuf *q;
 2172              		.loc 1 913 1 is_stmt 1 view -0
 2173              		.cfi_startproc
 2174              		@ args = 0, pretend = 0, frame = 0
 2175              		@ frame_needed = 0, uses_anonymous_args = 0
 913:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   struct pbuf *q;
 2176              		.loc 1 913 1 is_stmt 0 view .LVU622
 2177 0000 70B5     		push	{r4, r5, r6, lr}
 2178              	.LCFI31:
 2179              		.cfi_def_cfa_offset 16
 2180              		.cfi_offset 4, -16
 2181              		.cfi_offset 5, -12
 2182              		.cfi_offset 6, -8
 2183              		.cfi_offset 14, -4
 2184 0002 0446     		mov	r4, r0
 914:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   u8_t tail_gone = 1;
 2185              		.loc 1 914 3 is_stmt 1 view .LVU623
 915:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* tail */
 2186              		.loc 1 915 3 view .LVU624
 2187              	.LVL214:
 917:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* pbuf has successor in chain? */
 2188              		.loc 1 917 3 view .LVU625
 917:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* pbuf has successor in chain? */
 2189              		.loc 1 917 5 is_stmt 0 view .LVU626
 2190 0004 0568     		ldr	r5, [r0]
 2191              	.LVL215:
 919:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* assert tot_len invariant: (p->tot_len == p->len + (p->next? p->next->tot_len: 0) */
 2192              		.loc 1 919 3 is_stmt 1 view .LVU627
 919:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* assert tot_len invariant: (p->tot_len == p->len + (p->next? p->next->tot_len: 0) */
 2193              		.loc 1 919 6 is_stmt 0 view .LVU628
 2194 0006 05B3     		cbz	r5, .L165
 921:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* enforce invariant if assertion is disabled */
 2195              		.loc 1 921 5 is_stmt 1 view .LVU629
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 71


 921:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* enforce invariant if assertion is disabled */
 2196              		.loc 1 921 5 view .LVU630
 2197 0008 2A89     		ldrh	r2, [r5, #8]
 2198 000a 0389     		ldrh	r3, [r0, #8]
 2199 000c 4189     		ldrh	r1, [r0, #10]
 2200 000e 5B1A     		subs	r3, r3, r1
 2201 0010 9A42     		cmp	r2, r3
 2202 0012 12D1     		bne	.L168
 2203              	.LVL216:
 2204              	.L162:
 921:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* enforce invariant if assertion is disabled */
 2205              		.loc 1 921 5 discriminator 3 view .LVU631
 921:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* enforce invariant if assertion is disabled */
 2206              		.loc 1 921 5 discriminator 3 view .LVU632
 923:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* decouple pbuf from remainder */
 2207              		.loc 1 923 5 view .LVU633
 923:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* decouple pbuf from remainder */
 2208              		.loc 1 923 27 is_stmt 0 view .LVU634
 2209 0014 2389     		ldrh	r3, [r4, #8]
 923:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* decouple pbuf from remainder */
 2210              		.loc 1 923 40 view .LVU635
 2211 0016 6289     		ldrh	r2, [r4, #10]
 923:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* decouple pbuf from remainder */
 2212              		.loc 1 923 18 view .LVU636
 2213 0018 9B1A     		subs	r3, r3, r2
 923:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* decouple pbuf from remainder */
 2214              		.loc 1 923 16 view .LVU637
 2215 001a 2B81     		strh	r3, [r5, #8]	@ movhi
 925:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* total length of pbuf p is its own length only */
 2216              		.loc 1 925 5 is_stmt 1 view .LVU638
 925:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* total length of pbuf p is its own length only */
 2217              		.loc 1 925 13 is_stmt 0 view .LVU639
 2218 001c 0023     		movs	r3, #0
 2219 001e 2360     		str	r3, [r4]
 927:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* q is no longer referenced by p, free it */
 2220              		.loc 1 927 5 is_stmt 1 view .LVU640
 927:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* q is no longer referenced by p, free it */
 2221              		.loc 1 927 19 is_stmt 0 view .LVU641
 2222 0020 6389     		ldrh	r3, [r4, #10]
 927:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* q is no longer referenced by p, free it */
 2223              		.loc 1 927 16 view .LVU642
 2224 0022 2381     		strh	r3, [r4, #8]	@ movhi
 929:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     tail_gone = pbuf_free(q);
 2225              		.loc 1 929 94 is_stmt 1 view .LVU643
 930:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     if (tail_gone > 0) {
 2226              		.loc 1 930 5 view .LVU644
 930:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     if (tail_gone > 0) {
 2227              		.loc 1 930 17 is_stmt 0 view .LVU645
 2228 0024 2846     		mov	r0, r5
 2229 0026 FFF7FEFF 		bl	pbuf_free
 2230              	.LVL217:
 2231 002a 0646     		mov	r6, r0
 2232              	.LVL218:
 931:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       LWIP_DEBUGF(PBUF_DEBUG | LWIP_DBG_TRACE,
 2233              		.loc 1 931 5 is_stmt 1 view .LVU646
 2234              	.L161:
 933:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     }
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 72


 2235              		.loc 1 933 97 view .LVU647
 938:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   return ((tail_gone > 0) ? NULL : q);
 2236              		.loc 1 938 3 view .LVU648
 938:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   return ((tail_gone > 0) ? NULL : q);
 2237              		.loc 1 938 3 view .LVU649
 2238 002c 2289     		ldrh	r2, [r4, #8]
 2239 002e 6389     		ldrh	r3, [r4, #10]
 2240 0030 9A42     		cmp	r2, r3
 2241 0032 0CD1     		bne	.L169
 2242              	.L163:
 938:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   return ((tail_gone > 0) ? NULL : q);
 2243              		.loc 1 938 3 discriminator 3 view .LVU650
 938:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   return ((tail_gone > 0) ? NULL : q);
 2244              		.loc 1 938 3 discriminator 3 view .LVU651
 939:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** }
 2245              		.loc 1 939 3 view .LVU652
 939:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** }
 2246              		.loc 1 939 34 is_stmt 0 view .LVU653
 2247 0034 9EB9     		cbnz	r6, .L170
 2248              	.LVL219:
 2249              	.L160:
 940:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 2250              		.loc 1 940 1 view .LVU654
 2251 0036 2846     		mov	r0, r5
 2252 0038 70BD     		pop	{r4, r5, r6, pc}
 2253              	.LVL220:
 2254              	.L168:
 921:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* enforce invariant if assertion is disabled */
 2255              		.loc 1 921 5 is_stmt 1 discriminator 1 view .LVU655
 921:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* enforce invariant if assertion is disabled */
 2256              		.loc 1 921 5 discriminator 1 view .LVU656
 2257 003a 0A4B     		ldr	r3, .L171
 2258 003c 40F29932 		movw	r2, #921
 2259 0040 0949     		ldr	r1, .L171+4
 2260 0042 0A48     		ldr	r0, .L171+8
 2261              	.LVL221:
 921:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* enforce invariant if assertion is disabled */
 2262              		.loc 1 921 5 is_stmt 0 discriminator 1 view .LVU657
 2263 0044 FFF7FEFF 		bl	printf
 2264              	.LVL222:
 2265 0048 E4E7     		b	.L162
 2266              	.LVL223:
 2267              	.L165:
 915:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* tail */
 2268              		.loc 1 915 8 view .LVU658
 2269 004a 0126     		movs	r6, #1
 2270 004c EEE7     		b	.L161
 2271              	.LVL224:
 2272              	.L169:
 938:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   return ((tail_gone > 0) ? NULL : q);
 2273              		.loc 1 938 3 is_stmt 1 discriminator 1 view .LVU659
 938:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   return ((tail_gone > 0) ? NULL : q);
 2274              		.loc 1 938 3 discriminator 1 view .LVU660
 2275 004e 054B     		ldr	r3, .L171
 2276 0050 40F2AA32 		movw	r2, #938
 2277 0054 0649     		ldr	r1, .L171+12
 2278 0056 0548     		ldr	r0, .L171+8
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 73


 2279 0058 FFF7FEFF 		bl	printf
 2280              	.LVL225:
 2281 005c EAE7     		b	.L163
 2282              	.L170:
 939:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** }
 2283              		.loc 1 939 34 is_stmt 0 discriminator 2 view .LVU661
 2284 005e 0025     		movs	r5, #0
 2285              	.LVL226:
 939:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** }
 2286              		.loc 1 939 34 view .LVU662
 2287 0060 E9E7     		b	.L160
 2288              	.L172:
 2289 0062 00BF     		.align	2
 2290              	.L171:
 2291 0064 00000000 		.word	.LC0
 2292 0068 00000000 		.word	.LC19
 2293 006c 3C000000 		.word	.LC2
 2294 0070 24000000 		.word	.LC20
 2295              		.cfi_endproc
 2296              	.LFE195:
 2298              		.section	.rodata.pbuf_copy.str1.4,"aMS",%progbits,1
 2299              		.align	2
 2300              	.LC21:
 2301 0000 70627566 		.ascii	"pbuf_copy: target not big enough to hold source\000"
 2301      5F636F70 
 2301      793A2074 
 2301      61726765 
 2301      74206E6F 
 2302              		.align	2
 2303              	.LC22:
 2304 0030 6F666673 		.ascii	"offset_to <= p_to->len\000"
 2304      65745F74 
 2304      6F203C3D 
 2304      20705F74 
 2304      6F2D3E6C 
 2305 0047 00       		.align	2
 2306              	.LC23:
 2307 0048 6F666673 		.ascii	"offset_from <= p_from->len\000"
 2307      65745F66 
 2307      726F6D20 
 2307      3C3D2070 
 2307      5F66726F 
 2308 0063 00       		.align	2
 2309              	.LC24:
 2310 0064 705F746F 		.ascii	"p_to != NULL\000"
 2310      20213D20 
 2310      4E554C4C 
 2310      00
 2311 0071 000000   		.align	2
 2312              	.LC25:
 2313 0074 70627566 		.ascii	"pbuf_copy() does not allow packet queues!\000"
 2313      5F636F70 
 2313      79282920 
 2313      646F6573 
 2313      206E6F74 
 2314              		.section	.text.pbuf_copy,"ax",%progbits
 2315              		.align	1
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 74


 2316              		.global	pbuf_copy
 2317              		.syntax unified
 2318              		.thumb
 2319              		.thumb_func
 2321              	pbuf_copy:
 2322              	.LVL227:
 2323              	.LFB196:
 962:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   size_t offset_to = 0, offset_from = 0, len;
 2324              		.loc 1 962 1 is_stmt 1 view -0
 2325              		.cfi_startproc
 2326              		@ args = 0, pretend = 0, frame = 0
 2327              		@ frame_needed = 0, uses_anonymous_args = 0
 962:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   size_t offset_to = 0, offset_from = 0, len;
 2328              		.loc 1 962 1 is_stmt 0 view .LVU664
 2329 0000 2DE9F041 		push	{r4, r5, r6, r7, r8, lr}
 2330              	.LCFI32:
 2331              		.cfi_def_cfa_offset 24
 2332              		.cfi_offset 4, -24
 2333              		.cfi_offset 5, -20
 2334              		.cfi_offset 6, -16
 2335              		.cfi_offset 7, -12
 2336              		.cfi_offset 8, -8
 2337              		.cfi_offset 14, -4
 963:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 2338              		.loc 1 963 3 is_stmt 1 view .LVU665
 2339              	.LVL228:
 966:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 2340              		.loc 1 966 57 view .LVU666
 969:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****              (p_from != NULL) && (p_to->tot_len >= p_from->tot_len)), return ERR_ARG;);
 2341              		.loc 1 969 3 view .LVU667
 969:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****              (p_from != NULL) && (p_to->tot_len >= p_from->tot_len)), return ERR_ARG;);
 2342              		.loc 1 969 3 view .LVU668
 2343 0004 0029     		cmp	r1, #0
 2344 0006 18BF     		it	ne
 2345 0008 0028     		cmpne	r0, #0
 2346 000a 08D0     		beq	.L174
 2347 000c 0546     		mov	r5, r0
 2348 000e 0C46     		mov	r4, r1
 969:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****              (p_from != NULL) && (p_to->tot_len >= p_from->tot_len)), return ERR_ARG;);
 2349              		.loc 1 969 3 is_stmt 0 discriminator 2 view .LVU669
 2350 0010 0289     		ldrh	r2, [r0, #8]
 2351 0012 0B89     		ldrh	r3, [r1, #8]
 2352 0014 9A42     		cmp	r2, r3
 2353 0016 02D3     		bcc	.L174
 963:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 2354              		.loc 1 963 25 view .LVU670
 2355 0018 0027     		movs	r7, #0
 963:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 2356              		.loc 1 963 10 view .LVU671
 2357 001a B846     		mov	r8, r7
 2358 001c 3CE0     		b	.L175
 2359              	.L174:
 969:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****              (p_from != NULL) && (p_to->tot_len >= p_from->tot_len)), return ERR_ARG;);
 2360              		.loc 1 969 3 is_stmt 1 discriminator 3 view .LVU672
 969:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****              (p_from != NULL) && (p_to->tot_len >= p_from->tot_len)), return ERR_ARG;);
 2361              		.loc 1 969 3 discriminator 3 view .LVU673
 2362 001e 3B4B     		ldr	r3, .L193
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 75


 2363 0020 40F2C932 		movw	r2, #969
 2364 0024 3A49     		ldr	r1, .L193+4
 2365              	.LVL229:
 969:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****              (p_from != NULL) && (p_to->tot_len >= p_from->tot_len)), return ERR_ARG;);
 2366              		.loc 1 969 3 is_stmt 0 discriminator 3 view .LVU674
 2367 0026 3B48     		ldr	r0, .L193+8
 2368              	.LVL230:
 969:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****              (p_from != NULL) && (p_to->tot_len >= p_from->tot_len)), return ERR_ARG;);
 2369              		.loc 1 969 3 discriminator 3 view .LVU675
 2370 0028 FFF7FEFF 		bl	printf
 2371              	.LVL231:
 969:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****              (p_from != NULL) && (p_to->tot_len >= p_from->tot_len)), return ERR_ARG;);
 2372              		.loc 1 969 3 is_stmt 1 discriminator 1 view .LVU676
 969:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****              (p_from != NULL) && (p_to->tot_len >= p_from->tot_len)), return ERR_ARG;);
 2373              		.loc 1 969 3 discriminator 1 view .LVU677
 2374 002c 6FF00F00 		mvn	r0, #15
 969:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****              (p_from != NULL) && (p_to->tot_len >= p_from->tot_len)), return ERR_ARG;);
 2375              		.loc 1 969 3 is_stmt 0 view .LVU678
 2376 0030 6AE0     		b	.L176
 2377              	.LVL232:
 2378              	.L189:
 985:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     LWIP_ASSERT("offset_from <= p_from->len", offset_from <= p_from->len);
 2379              		.loc 1 985 5 is_stmt 1 discriminator 1 view .LVU679
 985:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     LWIP_ASSERT("offset_from <= p_from->len", offset_from <= p_from->len);
 2380              		.loc 1 985 5 discriminator 1 view .LVU680
 2381 0032 364B     		ldr	r3, .L193
 2382 0034 40F2D932 		movw	r2, #985
 2383 0038 3749     		ldr	r1, .L193+12
 2384 003a 3648     		ldr	r0, .L193+8
 2385 003c FFF7FEFF 		bl	printf
 2386              	.LVL233:
 2387 0040 3EE0     		b	.L178
 2388              	.L190:
 986:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     if (offset_from >= p_from->len) {
 2389              		.loc 1 986 5 discriminator 1 view .LVU681
 986:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     if (offset_from >= p_from->len) {
 2390              		.loc 1 986 5 discriminator 1 view .LVU682
 2391 0042 324B     		ldr	r3, .L193
 2392 0044 40F2DA32 		movw	r2, #986
 2393 0048 3449     		ldr	r1, .L193+16
 2394 004a 3248     		ldr	r0, .L193+8
 2395 004c FFF7FEFF 		bl	printf
 2396              	.LVL234:
 2397 0050 39E0     		b	.L179
 2398              	.L191:
 994:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       p_to = p_to->next;
 2399              		.loc 1 994 7 view .LVU683
 2400              	.LVL235:
 995:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       LWIP_ERROR("p_to != NULL", (p_to != NULL) || (p_from == NULL), return ERR_ARG;);
 2401              		.loc 1 995 7 view .LVU684
 995:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       LWIP_ERROR("p_to != NULL", (p_to != NULL) || (p_from == NULL), return ERR_ARG;);
 2402              		.loc 1 995 12 is_stmt 0 view .LVU685
 2403 0052 2D68     		ldr	r5, [r5]
 2404              	.LVL236:
 996:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     }
 2405              		.loc 1 996 7 is_stmt 1 view .LVU686
 996:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     }
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 76


 2406              		.loc 1 996 7 view .LVU687
 2407 0054 B5FA85F3 		clz	r3, r5
 2408 0058 5B09     		lsrs	r3, r3, #5
 2409 005a 002C     		cmp	r4, #0
 2410 005c 08BF     		it	eq
 2411 005e 0023     		moveq	r3, #0
 2412 0060 13B9     		cbnz	r3, .L187
 994:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       p_to = p_to->next;
 2413              		.loc 1 994 17 is_stmt 0 view .LVU688
 2414 0062 4FF00008 		mov	r8, #0
 2415 0066 36E0     		b	.L181
 2416              	.L187:
 996:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     }
 2417              		.loc 1 996 7 is_stmt 1 discriminator 1 view .LVU689
 996:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     }
 2418              		.loc 1 996 7 discriminator 1 view .LVU690
 2419 0068 284B     		ldr	r3, .L193
 2420 006a 4FF47972 		mov	r2, #996
 2421 006e 2C49     		ldr	r1, .L193+20
 2422 0070 2848     		ldr	r0, .L193+8
 2423 0072 FFF7FEFF 		bl	printf
 2424              	.LVL237:
 996:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     }
 2425              		.loc 1 996 7 discriminator 1 view .LVU691
 996:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     }
 2426              		.loc 1 996 7 discriminator 1 view .LVU692
 2427 0076 6FF00F00 		mvn	r0, #15
 996:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     }
 2428              		.loc 1 996 7 is_stmt 0 view .LVU693
 2429 007a 45E0     		b	.L176
 2430              	.LVL238:
 2431              	.L192:
1001:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****                  (p_from->next == NULL), return ERR_VAL;);
 2432              		.loc 1 1001 7 is_stmt 1 view .LVU694
1001:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****                  (p_from->next == NULL), return ERR_VAL;);
 2433              		.loc 1 1001 7 view .LVU695
 2434 007c 2368     		ldr	r3, [r4]
 2435 007e 7BB3     		cbz	r3, .L182
1001:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****                  (p_from->next == NULL), return ERR_VAL;);
 2436              		.loc 1 1001 7 discriminator 1 view .LVU696
1001:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****                  (p_from->next == NULL), return ERR_VAL;);
 2437              		.loc 1 1001 7 discriminator 1 view .LVU697
 2438 0080 224B     		ldr	r3, .L193
 2439 0082 40F2E932 		movw	r2, #1001
 2440 0086 2749     		ldr	r1, .L193+24
 2441 0088 2248     		ldr	r0, .L193+8
 2442 008a FFF7FEFF 		bl	printf
 2443              	.LVL239:
1001:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****                  (p_from->next == NULL), return ERR_VAL;);
 2444              		.loc 1 1001 7 discriminator 1 view .LVU698
1001:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****                  (p_from->next == NULL), return ERR_VAL;);
 2445              		.loc 1 1001 7 discriminator 1 view .LVU699
 2446 008e 6FF00500 		mvn	r0, #5
1001:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****                  (p_from->next == NULL), return ERR_VAL;);
 2447              		.loc 1 1001 7 is_stmt 0 view .LVU700
 2448 0092 39E0     		b	.L176
 2449              	.L183:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 77


1006:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****                  (p_to->next == NULL), return ERR_VAL;);
 2450              		.loc 1 1006 7 is_stmt 1 discriminator 2 view .LVU701
1006:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****                  (p_to->next == NULL), return ERR_VAL;);
 2451              		.loc 1 1006 7 discriminator 2 view .LVU702
1009:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_DEBUGF(PBUF_DEBUG | LWIP_DBG_TRACE, ("pbuf_copy: end of chain reached.\n"));
 2452              		.loc 1 1009 12 view .LVU703
 2453 0094 002C     		cmp	r4, #0
 2454 0096 36D0     		beq	.L188
 2455              	.LVL240:
 2456              	.L175:
 969:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****              (p_from != NULL) && (p_to->tot_len >= p_from->tot_len)), return ERR_ARG;);
 2457              		.loc 1 969 3 discriminator 4 view .LVU704
 969:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****              (p_from != NULL) && (p_to->tot_len >= p_from->tot_len)), return ERR_ARG;);
 2458              		.loc 1 969 3 discriminator 4 view .LVU705
 973:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* copy one part of the original chain */
 2459              		.loc 1 973 3 view .LVU706
 975:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       /* complete current p_from fits into current p_to */
 2460              		.loc 1 975 5 view .LVU707
 975:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       /* complete current p_from fits into current p_to */
 2461              		.loc 1 975 14 is_stmt 0 view .LVU708
 2462 0098 6E89     		ldrh	r6, [r5, #10]
 975:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       /* complete current p_from fits into current p_to */
 2463              		.loc 1 975 20 view .LVU709
 2464 009a A6EB0806 		sub	r6, r6, r8
 975:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       /* complete current p_from fits into current p_to */
 2465              		.loc 1 975 43 view .LVU710
 2466 009e 6389     		ldrh	r3, [r4, #10]
 975:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       /* complete current p_from fits into current p_to */
 2467              		.loc 1 975 49 view .LVU711
 2468 00a0 DB1B     		subs	r3, r3, r7
 975:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       /* complete current p_from fits into current p_to */
 2469              		.loc 1 975 8 view .LVU712
 2470 00a2 9E42     		cmp	r6, r3
 2471 00a4 00D3     		bcc	.L177
 977:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     } else {
 2472              		.loc 1 977 11 view .LVU713
 2473 00a6 1E46     		mov	r6, r3
 2474              	.L177:
 2475              	.LVL241:
 982:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     offset_to += len;
 2476              		.loc 1 982 5 is_stmt 1 view .LVU714
 2477 00a8 6868     		ldr	r0, [r5, #4]
 2478 00aa 6168     		ldr	r1, [r4, #4]
 2479 00ac 3246     		mov	r2, r6
 2480 00ae 3944     		add	r1, r1, r7
 2481 00b0 4044     		add	r0, r0, r8
 2482 00b2 FFF7FEFF 		bl	memcpy
 2483              	.LVL242:
 983:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     offset_from += len;
 2484              		.loc 1 983 5 view .LVU715
 983:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     offset_from += len;
 2485              		.loc 1 983 15 is_stmt 0 view .LVU716
 2486 00b6 B044     		add	r8, r8, r6
 2487              	.LVL243:
 984:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     LWIP_ASSERT("offset_to <= p_to->len", offset_to <= p_to->len);
 2488              		.loc 1 984 5 is_stmt 1 view .LVU717
 984:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     LWIP_ASSERT("offset_to <= p_to->len", offset_to <= p_to->len);
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 78


 2489              		.loc 1 984 17 is_stmt 0 view .LVU718
 2490 00b8 3744     		add	r7, r7, r6
 2491              	.LVL244:
 985:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     LWIP_ASSERT("offset_from <= p_from->len", offset_from <= p_from->len);
 2492              		.loc 1 985 5 is_stmt 1 view .LVU719
 985:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     LWIP_ASSERT("offset_from <= p_from->len", offset_from <= p_from->len);
 2493              		.loc 1 985 5 view .LVU720
 2494 00ba 6B89     		ldrh	r3, [r5, #10]
 2495 00bc 4345     		cmp	r3, r8
 2496 00be B8D3     		bcc	.L189
 2497              	.L178:
 985:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     LWIP_ASSERT("offset_from <= p_from->len", offset_from <= p_from->len);
 2498              		.loc 1 985 5 discriminator 3 view .LVU721
 985:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     LWIP_ASSERT("offset_from <= p_from->len", offset_from <= p_from->len);
 2499              		.loc 1 985 5 discriminator 3 view .LVU722
 986:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     if (offset_from >= p_from->len) {
 2500              		.loc 1 986 5 view .LVU723
 986:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     if (offset_from >= p_from->len) {
 2501              		.loc 1 986 5 view .LVU724
 2502 00c0 6389     		ldrh	r3, [r4, #10]
 2503 00c2 BB42     		cmp	r3, r7
 2504 00c4 BDD3     		bcc	.L190
 2505              	.L179:
 986:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     if (offset_from >= p_from->len) {
 2506              		.loc 1 986 5 discriminator 3 view .LVU725
 986:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     if (offset_from >= p_from->len) {
 2507              		.loc 1 986 5 discriminator 3 view .LVU726
 987:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       /* on to next p_from (if any) */
 2508              		.loc 1 987 5 view .LVU727
 987:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       /* on to next p_from (if any) */
 2509              		.loc 1 987 30 is_stmt 0 view .LVU728
 2510 00c6 6389     		ldrh	r3, [r4, #10]
 987:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       /* on to next p_from (if any) */
 2511              		.loc 1 987 8 view .LVU729
 2512 00c8 BB42     		cmp	r3, r7
 2513 00ca 01D8     		bhi	.L180
 989:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       p_from = p_from->next;
 2514              		.loc 1 989 7 is_stmt 1 view .LVU730
 2515              	.LVL245:
 990:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     }
 2516              		.loc 1 990 7 view .LVU731
 990:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     }
 2517              		.loc 1 990 14 is_stmt 0 view .LVU732
 2518 00cc 2468     		ldr	r4, [r4]
 2519              	.LVL246:
 989:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       p_from = p_from->next;
 2520              		.loc 1 989 19 view .LVU733
 2521 00ce 0027     		movs	r7, #0
 2522              	.LVL247:
 2523              	.L180:
 992:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       /* on to next p_to (if any) */
 2524              		.loc 1 992 5 is_stmt 1 view .LVU734
 992:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       /* on to next p_to (if any) */
 2525              		.loc 1 992 26 is_stmt 0 view .LVU735
 2526 00d0 6B89     		ldrh	r3, [r5, #10]
 992:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       /* on to next p_to (if any) */
 2527              		.loc 1 992 8 view .LVU736
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 79


 2528 00d2 4345     		cmp	r3, r8
 2529 00d4 BDD0     		beq	.L191
 2530              	.LVL248:
 2531              	.L181:
 996:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     }
 2532              		.loc 1 996 7 is_stmt 1 discriminator 2 view .LVU737
 996:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     }
 2533              		.loc 1 996 7 discriminator 2 view .LVU738
 999:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       /* don't copy more than one packet! */
 2534              		.loc 1 999 5 view .LVU739
 999:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       /* don't copy more than one packet! */
 2535              		.loc 1 999 8 is_stmt 0 view .LVU740
 2536 00d6 1CB1     		cbz	r4, .L182
 999:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       /* don't copy more than one packet! */
 2537              		.loc 1 999 36 discriminator 1 view .LVU741
 2538 00d8 6289     		ldrh	r2, [r4, #10]
 999:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       /* don't copy more than one packet! */
 2539              		.loc 1 999 51 discriminator 1 view .LVU742
 2540 00da 2389     		ldrh	r3, [r4, #8]
 999:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       /* don't copy more than one packet! */
 2541              		.loc 1 999 26 discriminator 1 view .LVU743
 2542 00dc 9A42     		cmp	r2, r3
 2543 00de CDD0     		beq	.L192
 2544              	.L182:
1001:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****                  (p_from->next == NULL), return ERR_VAL;);
 2545              		.loc 1 1001 7 is_stmt 1 discriminator 2 view .LVU744
1001:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****                  (p_from->next == NULL), return ERR_VAL;);
 2546              		.loc 1 1001 7 discriminator 2 view .LVU745
1004:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       /* don't copy more than one packet! */
 2547              		.loc 1 1004 5 view .LVU746
1004:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       /* don't copy more than one packet! */
 2548              		.loc 1 1004 8 is_stmt 0 view .LVU747
 2549 00e0 002D     		cmp	r5, #0
 2550 00e2 D7D0     		beq	.L183
1004:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       /* don't copy more than one packet! */
 2551              		.loc 1 1004 32 discriminator 1 view .LVU748
 2552 00e4 6A89     		ldrh	r2, [r5, #10]
1004:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       /* don't copy more than one packet! */
 2553              		.loc 1 1004 45 discriminator 1 view .LVU749
 2554 00e6 2B89     		ldrh	r3, [r5, #8]
1004:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       /* don't copy more than one packet! */
 2555              		.loc 1 1004 24 discriminator 1 view .LVU750
 2556 00e8 9A42     		cmp	r2, r3
 2557 00ea D3D1     		bne	.L183
1006:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****                  (p_to->next == NULL), return ERR_VAL;);
 2558              		.loc 1 1006 7 is_stmt 1 view .LVU751
1006:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****                  (p_to->next == NULL), return ERR_VAL;);
 2559              		.loc 1 1006 7 view .LVU752
 2560 00ec 2B68     		ldr	r3, [r5]
 2561 00ee 002B     		cmp	r3, #0
 2562 00f0 D0D0     		beq	.L183
1006:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****                  (p_to->next == NULL), return ERR_VAL;);
 2563              		.loc 1 1006 7 discriminator 1 view .LVU753
1006:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****                  (p_to->next == NULL), return ERR_VAL;);
 2564              		.loc 1 1006 7 discriminator 1 view .LVU754
 2565 00f2 064B     		ldr	r3, .L193
 2566 00f4 40F2EE32 		movw	r2, #1006
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 80


 2567 00f8 0A49     		ldr	r1, .L193+24
 2568 00fa 0648     		ldr	r0, .L193+8
 2569 00fc FFF7FEFF 		bl	printf
 2570              	.LVL249:
1006:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****                  (p_to->next == NULL), return ERR_VAL;);
 2571              		.loc 1 1006 7 discriminator 1 view .LVU755
1006:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****                  (p_to->next == NULL), return ERR_VAL;);
 2572              		.loc 1 1006 7 discriminator 1 view .LVU756
 2573 0100 6FF00500 		mvn	r0, #5
1006:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****                  (p_to->next == NULL), return ERR_VAL;);
 2574              		.loc 1 1006 7 is_stmt 0 view .LVU757
 2575 0104 00E0     		b	.L176
 2576              	.L188:
1011:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** }
 2577              		.loc 1 1011 10 view .LVU758
 2578 0106 0020     		movs	r0, #0
 2579              	.LVL250:
 2580              	.L176:
1012:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 2581              		.loc 1 1012 1 view .LVU759
 2582 0108 BDE8F081 		pop	{r4, r5, r6, r7, r8, pc}
 2583              	.L194:
 2584              		.align	2
 2585              	.L193:
 2586 010c 00000000 		.word	.LC0
 2587 0110 00000000 		.word	.LC21
 2588 0114 3C000000 		.word	.LC2
 2589 0118 30000000 		.word	.LC22
 2590 011c 48000000 		.word	.LC23
 2591 0120 64000000 		.word	.LC24
 2592 0124 74000000 		.word	.LC25
 2593              		.cfi_endproc
 2594              	.LFE196:
 2596              		.section	.rodata.pbuf_copy_partial.str1.4,"aMS",%progbits,1
 2597              		.align	2
 2598              	.LC26:
 2599 0000 70627566 		.ascii	"pbuf_copy_partial: invalid buf\000"
 2599      5F636F70 
 2599      795F7061 
 2599      72746961 
 2599      6C3A2069 
 2600 001f 00       		.align	2
 2601              	.LC27:
 2602 0020 70627566 		.ascii	"pbuf_copy_partial: invalid dataptr\000"
 2602      5F636F70 
 2602      795F7061 
 2602      72746961 
 2602      6C3A2069 
 2603              		.section	.text.pbuf_copy_partial,"ax",%progbits
 2604              		.align	1
 2605              		.global	pbuf_copy_partial
 2606              		.syntax unified
 2607              		.thumb
 2608              		.thumb_func
 2610              	pbuf_copy_partial:
 2611              	.LVL251:
 2612              	.LFB197:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 81


1028:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   const struct pbuf *p;
 2613              		.loc 1 1028 1 is_stmt 1 view -0
 2614              		.cfi_startproc
 2615              		@ args = 0, pretend = 0, frame = 0
 2616              		@ frame_needed = 0, uses_anonymous_args = 0
1028:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   const struct pbuf *p;
 2617              		.loc 1 1028 1 is_stmt 0 view .LVU761
 2618 0000 2DE9F843 		push	{r3, r4, r5, r6, r7, r8, r9, lr}
 2619              	.LCFI33:
 2620              		.cfi_def_cfa_offset 32
 2621              		.cfi_offset 3, -32
 2622              		.cfi_offset 4, -28
 2623              		.cfi_offset 5, -24
 2624              		.cfi_offset 6, -20
 2625              		.cfi_offset 7, -16
 2626              		.cfi_offset 8, -12
 2627              		.cfi_offset 9, -8
 2628              		.cfi_offset 14, -4
1029:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   u16_t left = 0;
 2629              		.loc 1 1029 3 is_stmt 1 view .LVU762
1030:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   u16_t buf_copy_len;
 2630              		.loc 1 1030 3 view .LVU763
 2631              	.LVL252:
1031:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   u16_t copied_total = 0;
 2632              		.loc 1 1031 3 view .LVU764
1032:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 2633              		.loc 1 1032 3 view .LVU765
1034:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ERROR("pbuf_copy_partial: invalid dataptr", (dataptr != NULL), return 0;);
 2634              		.loc 1 1034 3 view .LVU766
1034:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ERROR("pbuf_copy_partial: invalid dataptr", (dataptr != NULL), return 0;);
 2635              		.loc 1 1034 3 view .LVU767
 2636 0004 30B1     		cbz	r0, .L207
 2637 0006 8846     		mov	r8, r1
 2638 0008 9146     		mov	r9, r2
 2639 000a 0546     		mov	r5, r0
1034:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ERROR("pbuf_copy_partial: invalid dataptr", (dataptr != NULL), return 0;);
 2640              		.loc 1 1034 3 discriminator 2 view .LVU768
1034:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ERROR("pbuf_copy_partial: invalid dataptr", (dataptr != NULL), return 0;);
 2641              		.loc 1 1034 3 discriminator 2 view .LVU769
1035:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 2642              		.loc 1 1035 3 view .LVU770
1035:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 2643              		.loc 1 1035 3 view .LVU771
 2644 000c 69B1     		cbz	r1, .L208
1032:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 2645              		.loc 1 1032 9 is_stmt 0 view .LVU772
 2646 000e 0027     		movs	r7, #0
1030:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   u16_t buf_copy_len;
 2647              		.loc 1 1030 9 view .LVU773
 2648 0010 3E46     		mov	r6, r7
 2649 0012 2AE0     		b	.L198
 2650              	.L207:
1034:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ERROR("pbuf_copy_partial: invalid dataptr", (dataptr != NULL), return 0;);
 2651              		.loc 1 1034 3 is_stmt 1 discriminator 1 view .LVU774
1034:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ERROR("pbuf_copy_partial: invalid dataptr", (dataptr != NULL), return 0;);
 2652              		.loc 1 1034 3 discriminator 1 view .LVU775
 2653 0014 1D4B     		ldr	r3, .L209
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 82


 2654              	.LVL253:
1034:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ERROR("pbuf_copy_partial: invalid dataptr", (dataptr != NULL), return 0;);
 2655              		.loc 1 1034 3 is_stmt 0 discriminator 1 view .LVU776
 2656 0016 40F20A42 		movw	r2, #1034
 2657              	.LVL254:
1034:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ERROR("pbuf_copy_partial: invalid dataptr", (dataptr != NULL), return 0;);
 2658              		.loc 1 1034 3 discriminator 1 view .LVU777
 2659 001a 1D49     		ldr	r1, .L209+4
 2660              	.LVL255:
1034:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ERROR("pbuf_copy_partial: invalid dataptr", (dataptr != NULL), return 0;);
 2661              		.loc 1 1034 3 discriminator 1 view .LVU778
 2662 001c 1D48     		ldr	r0, .L209+8
 2663              	.LVL256:
1034:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ERROR("pbuf_copy_partial: invalid dataptr", (dataptr != NULL), return 0;);
 2664              		.loc 1 1034 3 discriminator 1 view .LVU779
 2665 001e FFF7FEFF 		bl	printf
 2666              	.LVL257:
1034:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ERROR("pbuf_copy_partial: invalid dataptr", (dataptr != NULL), return 0;);
 2667              		.loc 1 1034 3 is_stmt 1 discriminator 1 view .LVU780
1034:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ERROR("pbuf_copy_partial: invalid dataptr", (dataptr != NULL), return 0;);
 2668              		.loc 1 1034 3 discriminator 1 view .LVU781
 2669 0022 0027     		movs	r7, #0
 2670              	.LVL258:
 2671              	.L197:
1057:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 2672              		.loc 1 1057 1 is_stmt 0 view .LVU782
 2673 0024 3846     		mov	r0, r7
 2674 0026 BDE8F883 		pop	{r3, r4, r5, r6, r7, r8, r9, pc}
 2675              	.LVL259:
 2676              	.L208:
1035:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 2677              		.loc 1 1035 3 is_stmt 1 discriminator 1 view .LVU783
1035:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 2678              		.loc 1 1035 3 discriminator 1 view .LVU784
 2679 002a 184B     		ldr	r3, .L209
 2680              	.LVL260:
1035:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 2681              		.loc 1 1035 3 is_stmt 0 discriminator 1 view .LVU785
 2682 002c 40F20B42 		movw	r2, #1035
 2683              	.LVL261:
1035:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 2684              		.loc 1 1035 3 discriminator 1 view .LVU786
 2685 0030 1949     		ldr	r1, .L209+12
 2686              	.LVL262:
1035:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 2687              		.loc 1 1035 3 discriminator 1 view .LVU787
 2688 0032 1848     		ldr	r0, .L209+8
 2689              	.LVL263:
1035:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 2690              		.loc 1 1035 3 discriminator 1 view .LVU788
 2691 0034 FFF7FEFF 		bl	printf
 2692              	.LVL264:
1035:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 2693              		.loc 1 1035 3 is_stmt 1 discriminator 1 view .LVU789
1035:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 2694              		.loc 1 1035 3 discriminator 1 view .LVU790
 2695 0038 0027     		movs	r7, #0
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 83


1035:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 2696              		.loc 1 1035 3 is_stmt 0 view .LVU791
 2697 003a F3E7     		b	.L197
 2698              	.LVL265:
 2699              	.L199:
1044:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       if (buf_copy_len > len) {
 2700              		.loc 1 1044 7 is_stmt 1 view .LVU792
1044:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       if (buf_copy_len > len) {
 2701              		.loc 1 1044 31 is_stmt 0 view .LVU793
 2702 003c 6C89     		ldrh	r4, [r5, #10]
1044:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       if (buf_copy_len > len) {
 2703              		.loc 1 1044 20 view .LVU794
 2704 003e E41A     		subs	r4, r4, r3
 2705 0040 A4B2     		uxth	r4, r4
 2706              	.LVL266:
1045:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         buf_copy_len = len;
 2707              		.loc 1 1045 7 is_stmt 1 view .LVU795
1045:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         buf_copy_len = len;
 2708              		.loc 1 1045 10 is_stmt 0 view .LVU796
 2709 0042 A145     		cmp	r9, r4
 2710 0044 00D2     		bcs	.L201
1046:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       }
 2711              		.loc 1 1046 22 view .LVU797
 2712 0046 4C46     		mov	r4, r9
 2713              	.LVL267:
 2714              	.L201:
1049:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       copied_total = (u16_t)(copied_total + buf_copy_len);
 2715              		.loc 1 1049 7 is_stmt 1 view .LVU798
 2716 0048 6968     		ldr	r1, [r5, #4]
 2717 004a 2246     		mov	r2, r4
 2718 004c 1944     		add	r1, r1, r3
 2719 004e 08EB0600 		add	r0, r8, r6
 2720 0052 FFF7FEFF 		bl	memcpy
 2721              	.LVL268:
1050:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       left = (u16_t)(left + buf_copy_len);
 2722              		.loc 1 1050 7 view .LVU799
1050:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       left = (u16_t)(left + buf_copy_len);
 2723              		.loc 1 1050 20 is_stmt 0 view .LVU800
 2724 0056 2744     		add	r7, r7, r4
 2725              	.LVL269:
1050:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       left = (u16_t)(left + buf_copy_len);
 2726              		.loc 1 1050 20 view .LVU801
 2727 0058 BFB2     		uxth	r7, r7
 2728              	.LVL270:
1051:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       len = (u16_t)(len - buf_copy_len);
 2729              		.loc 1 1051 7 is_stmt 1 view .LVU802
1051:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       len = (u16_t)(len - buf_copy_len);
 2730              		.loc 1 1051 12 is_stmt 0 view .LVU803
 2731 005a 2644     		add	r6, r6, r4
 2732              	.LVL271:
1051:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       len = (u16_t)(len - buf_copy_len);
 2733              		.loc 1 1051 12 view .LVU804
 2734 005c B6B2     		uxth	r6, r6
 2735              	.LVL272:
1052:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       offset = 0;
 2736              		.loc 1 1052 7 is_stmt 1 view .LVU805
1052:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       offset = 0;
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 84


 2737              		.loc 1 1052 11 is_stmt 0 view .LVU806
 2738 005e A9EB0404 		sub	r4, r9, r4
 2739              	.LVL273:
1052:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       offset = 0;
 2740              		.loc 1 1052 11 view .LVU807
 2741 0062 1FFA84F9 		uxth	r9, r4
 2742              	.LVL274:
1053:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     }
 2743              		.loc 1 1053 7 is_stmt 1 view .LVU808
1053:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     }
 2744              		.loc 1 1053 14 is_stmt 0 view .LVU809
 2745 0066 0023     		movs	r3, #0
 2746              	.LVL275:
 2747              	.L200:
1038:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     if ((offset != 0) && (offset >= p->len)) {
 2748              		.loc 1 1038 42 is_stmt 1 discriminator 2 view .LVU810
 2749 0068 2D68     		ldr	r5, [r5]
 2750              	.LVL276:
 2751              	.L198:
1038:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     if ((offset != 0) && (offset >= p->len)) {
 2752              		.loc 1 1038 26 discriminator 1 view .LVU811
1038:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     if ((offset != 0) && (offset >= p->len)) {
 2753              		.loc 1 1038 31 is_stmt 0 discriminator 1 view .LVU812
 2754 006a 2A1E     		subs	r2, r5, #0
 2755 006c 18BF     		it	ne
 2756 006e 0122     		movne	r2, #1
1038:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     if ((offset != 0) && (offset >= p->len)) {
 2757              		.loc 1 1038 26 discriminator 1 view .LVU813
 2758 0070 B9F1000F 		cmp	r9, #0
 2759 0074 D6D0     		beq	.L197
1038:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     if ((offset != 0) && (offset >= p->len)) {
 2760              		.loc 1 1038 26 discriminator 1 view .LVU814
 2761 0076 002A     		cmp	r2, #0
 2762 0078 D4D0     		beq	.L197
1039:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       /* don't copy from this buffer -> on to the next */
 2763              		.loc 1 1039 5 is_stmt 1 view .LVU815
1039:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       /* don't copy from this buffer -> on to the next */
 2764              		.loc 1 1039 8 is_stmt 0 view .LVU816
 2765 007a 002B     		cmp	r3, #0
 2766 007c DED0     		beq	.L199
1039:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       /* don't copy from this buffer -> on to the next */
 2767              		.loc 1 1039 38 discriminator 1 view .LVU817
 2768 007e 6A89     		ldrh	r2, [r5, #10]
1039:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       /* don't copy from this buffer -> on to the next */
 2769              		.loc 1 1039 23 discriminator 1 view .LVU818
 2770 0080 9A42     		cmp	r2, r3
 2771 0082 DBD8     		bhi	.L199
1041:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     } else {
 2772              		.loc 1 1041 7 is_stmt 1 view .LVU819
1041:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     } else {
 2773              		.loc 1 1041 14 is_stmt 0 view .LVU820
 2774 0084 9B1A     		subs	r3, r3, r2
 2775              	.LVL277:
1041:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     } else {
 2776              		.loc 1 1041 14 view .LVU821
 2777 0086 9BB2     		uxth	r3, r3
 2778              	.LVL278:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 85


1041:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     } else {
 2779              		.loc 1 1041 14 view .LVU822
 2780 0088 EEE7     		b	.L200
 2781              	.L210:
 2782 008a 00BF     		.align	2
 2783              	.L209:
 2784 008c 00000000 		.word	.LC0
 2785 0090 00000000 		.word	.LC26
 2786 0094 3C000000 		.word	.LC2
 2787 0098 20000000 		.word	.LC27
 2788              		.cfi_endproc
 2789              	.LFE197:
 2791              		.section	.rodata.pbuf_get_contiguous.str1.4,"aMS",%progbits,1
 2792              		.align	2
 2793              	.LC28:
 2794 0000 70627566 		.ascii	"pbuf_get_contiguous: invalid buf\000"
 2794      5F676574 
 2794      5F636F6E 
 2794      74696775 
 2794      6F75733A 
 2795 0021 000000   		.align	2
 2796              	.LC29:
 2797 0024 70627566 		.ascii	"pbuf_get_contiguous: invalid dataptr\000"
 2797      5F676574 
 2797      5F636F6E 
 2797      74696775 
 2797      6F75733A 
 2798              		.section	.text.pbuf_get_contiguous,"ax",%progbits
 2799              		.align	1
 2800              		.global	pbuf_get_contiguous
 2801              		.syntax unified
 2802              		.thumb
 2803              		.thumb_func
 2805              	pbuf_get_contiguous:
 2806              	.LVL279:
 2807              	.LFB198:
1075:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   const struct pbuf *q;
 2808              		.loc 1 1075 1 is_stmt 1 view -0
 2809              		.cfi_startproc
 2810              		@ args = 4, pretend = 0, frame = 8
 2811              		@ frame_needed = 0, uses_anonymous_args = 0
1075:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   const struct pbuf *q;
 2812              		.loc 1 1075 1 is_stmt 0 view .LVU824
 2813 0000 70B5     		push	{r4, r5, r6, lr}
 2814              	.LCFI34:
 2815              		.cfi_def_cfa_offset 16
 2816              		.cfi_offset 4, -16
 2817              		.cfi_offset 5, -12
 2818              		.cfi_offset 6, -8
 2819              		.cfi_offset 14, -4
 2820 0002 82B0     		sub	sp, sp, #8
 2821              	.LCFI35:
 2822              		.cfi_def_cfa_offset 24
1076:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   u16_t out_offset;
 2823              		.loc 1 1076 3 is_stmt 1 view .LVU825
1077:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 2824              		.loc 1 1077 3 view .LVU826
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 86


1079:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ERROR("pbuf_get_contiguous: invalid dataptr", (buffer != NULL), return NULL;);
 2825              		.loc 1 1079 3 view .LVU827
1079:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ERROR("pbuf_get_contiguous: invalid dataptr", (buffer != NULL), return NULL;);
 2826              		.loc 1 1079 3 view .LVU828
 2827 0004 0446     		mov	r4, r0
 2828 0006 A8B1     		cbz	r0, .L219
 2829 0008 0E46     		mov	r6, r1
 2830 000a 1D46     		mov	r5, r3
1079:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ERROR("pbuf_get_contiguous: invalid dataptr", (buffer != NULL), return NULL;);
 2831              		.loc 1 1079 3 discriminator 2 view .LVU829
1079:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ERROR("pbuf_get_contiguous: invalid dataptr", (buffer != NULL), return NULL;);
 2832              		.loc 1 1079 3 discriminator 2 view .LVU830
1080:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ERROR("pbuf_get_contiguous: invalid dataptr", (bufsize >= len), return NULL;);
 2833              		.loc 1 1080 3 view .LVU831
1080:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ERROR("pbuf_get_contiguous: invalid dataptr", (bufsize >= len), return NULL;);
 2834              		.loc 1 1080 3 view .LVU832
 2835 000c D9B1     		cbz	r1, .L220
1080:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ERROR("pbuf_get_contiguous: invalid dataptr", (bufsize >= len), return NULL;);
 2836              		.loc 1 1080 3 discriminator 2 view .LVU833
1080:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ERROR("pbuf_get_contiguous: invalid dataptr", (bufsize >= len), return NULL;);
 2837              		.loc 1 1080 3 discriminator 2 view .LVU834
1081:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 2838              		.loc 1 1081 3 view .LVU835
1081:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 2839              		.loc 1 1081 3 view .LVU836
 2840 000e 9342     		cmp	r3, r2
 2841 0010 22D8     		bhi	.L221
1081:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 2842              		.loc 1 1081 3 discriminator 2 view .LVU837
1081:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 2843              		.loc 1 1081 3 discriminator 2 view .LVU838
1083:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   if (q != NULL) {
 2844              		.loc 1 1083 3 view .LVU839
1083:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   if (q != NULL) {
 2845              		.loc 1 1083 7 is_stmt 0 view .LVU840
 2846 0012 0DF10602 		add	r2, sp, #6
 2847              	.LVL280:
1083:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   if (q != NULL) {
 2848              		.loc 1 1083 7 view .LVU841
 2849 0016 BDF81810 		ldrh	r1, [sp, #24]
 2850              	.LVL281:
1083:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   if (q != NULL) {
 2851              		.loc 1 1083 7 view .LVU842
 2852 001a FFF7FEFF 		bl	pbuf_skip_const
 2853              	.LVL282:
1084:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     if (q->len >= (out_offset + len)) {
 2854              		.loc 1 1084 3 is_stmt 1 view .LVU843
1084:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     if (q->len >= (out_offset + len)) {
 2855              		.loc 1 1084 6 is_stmt 0 view .LVU844
 2856 001e 38B1     		cbz	r0, .L211
1085:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       /* all data in this pbuf, return zero-copy */
 2857              		.loc 1 1085 5 is_stmt 1 view .LVU845
1085:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       /* all data in this pbuf, return zero-copy */
 2858              		.loc 1 1085 10 is_stmt 0 view .LVU846
 2859 0020 4189     		ldrh	r1, [r0, #10]
1085:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       /* all data in this pbuf, return zero-copy */
 2860              		.loc 1 1085 31 view .LVU847
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 87


 2861 0022 BDF80630 		ldrh	r3, [sp, #6]
 2862 0026 5A19     		adds	r2, r3, r5
1085:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       /* all data in this pbuf, return zero-copy */
 2863              		.loc 1 1085 8 view .LVU848
 2864 0028 9142     		cmp	r1, r2
 2865 002a 1EDB     		blt	.L216
1087:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     }
 2866              		.loc 1 1087 7 is_stmt 1 view .LVU849
1087:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     }
 2867              		.loc 1 1087 23 is_stmt 0 view .LVU850
 2868 002c 4068     		ldr	r0, [r0, #4]
 2869              	.LVL283:
1087:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     }
 2870              		.loc 1 1087 33 view .LVU851
 2871 002e 1844     		add	r0, r0, r3
 2872              	.LVL284:
 2873              	.L211:
1098:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 2874              		.loc 1 1098 1 view .LVU852
 2875 0030 02B0     		add	sp, sp, #8
 2876              	.LCFI36:
 2877              		.cfi_remember_state
 2878              		.cfi_def_cfa_offset 16
 2879              		@ sp needed
 2880 0032 70BD     		pop	{r4, r5, r6, pc}
 2881              	.LVL285:
 2882              	.L219:
 2883              	.LCFI37:
 2884              		.cfi_restore_state
1079:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ERROR("pbuf_get_contiguous: invalid dataptr", (buffer != NULL), return NULL;);
 2885              		.loc 1 1079 3 is_stmt 1 discriminator 1 view .LVU853
1079:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ERROR("pbuf_get_contiguous: invalid dataptr", (buffer != NULL), return NULL;);
 2886              		.loc 1 1079 3 discriminator 1 view .LVU854
 2887 0034 124B     		ldr	r3, .L223
 2888              	.LVL286:
1079:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ERROR("pbuf_get_contiguous: invalid dataptr", (buffer != NULL), return NULL;);
 2889              		.loc 1 1079 3 is_stmt 0 discriminator 1 view .LVU855
 2890 0036 40F23742 		movw	r2, #1079
 2891              	.LVL287:
1079:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ERROR("pbuf_get_contiguous: invalid dataptr", (buffer != NULL), return NULL;);
 2892              		.loc 1 1079 3 discriminator 1 view .LVU856
 2893 003a 1249     		ldr	r1, .L223+4
 2894              	.LVL288:
1079:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ERROR("pbuf_get_contiguous: invalid dataptr", (buffer != NULL), return NULL;);
 2895              		.loc 1 1079 3 discriminator 1 view .LVU857
 2896 003c 1248     		ldr	r0, .L223+8
 2897              	.LVL289:
1079:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ERROR("pbuf_get_contiguous: invalid dataptr", (buffer != NULL), return NULL;);
 2898              		.loc 1 1079 3 discriminator 1 view .LVU858
 2899 003e FFF7FEFF 		bl	printf
 2900              	.LVL290:
1079:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ERROR("pbuf_get_contiguous: invalid dataptr", (buffer != NULL), return NULL;);
 2901              		.loc 1 1079 3 is_stmt 1 discriminator 1 view .LVU859
1079:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ERROR("pbuf_get_contiguous: invalid dataptr", (buffer != NULL), return NULL;);
 2902              		.loc 1 1079 3 discriminator 1 view .LVU860
 2903 0042 2046     		mov	r0, r4
1079:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ERROR("pbuf_get_contiguous: invalid dataptr", (buffer != NULL), return NULL;);
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 88


 2904              		.loc 1 1079 3 is_stmt 0 view .LVU861
 2905 0044 F4E7     		b	.L211
 2906              	.LVL291:
 2907              	.L220:
1080:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ERROR("pbuf_get_contiguous: invalid dataptr", (bufsize >= len), return NULL;);
 2908              		.loc 1 1080 3 is_stmt 1 discriminator 1 view .LVU862
1080:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ERROR("pbuf_get_contiguous: invalid dataptr", (bufsize >= len), return NULL;);
 2909              		.loc 1 1080 3 discriminator 1 view .LVU863
 2910 0046 0E4B     		ldr	r3, .L223
 2911              	.LVL292:
1080:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ERROR("pbuf_get_contiguous: invalid dataptr", (bufsize >= len), return NULL;);
 2912              		.loc 1 1080 3 is_stmt 0 discriminator 1 view .LVU864
 2913 0048 4FF48762 		mov	r2, #1080
 2914              	.LVL293:
1080:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ERROR("pbuf_get_contiguous: invalid dataptr", (bufsize >= len), return NULL;);
 2915              		.loc 1 1080 3 discriminator 1 view .LVU865
 2916 004c 0F49     		ldr	r1, .L223+12
 2917              	.LVL294:
1080:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ERROR("pbuf_get_contiguous: invalid dataptr", (bufsize >= len), return NULL;);
 2918              		.loc 1 1080 3 discriminator 1 view .LVU866
 2919 004e 0E48     		ldr	r0, .L223+8
 2920              	.LVL295:
1080:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ERROR("pbuf_get_contiguous: invalid dataptr", (bufsize >= len), return NULL;);
 2921              		.loc 1 1080 3 discriminator 1 view .LVU867
 2922 0050 FFF7FEFF 		bl	printf
 2923              	.LVL296:
1080:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ERROR("pbuf_get_contiguous: invalid dataptr", (bufsize >= len), return NULL;);
 2924              		.loc 1 1080 3 is_stmt 1 discriminator 1 view .LVU868
1080:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ERROR("pbuf_get_contiguous: invalid dataptr", (bufsize >= len), return NULL;);
 2925              		.loc 1 1080 3 discriminator 1 view .LVU869
 2926 0054 3046     		mov	r0, r6
1080:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ERROR("pbuf_get_contiguous: invalid dataptr", (bufsize >= len), return NULL;);
 2927              		.loc 1 1080 3 is_stmt 0 view .LVU870
 2928 0056 EBE7     		b	.L211
 2929              	.LVL297:
 2930              	.L221:
1081:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 2931              		.loc 1 1081 3 is_stmt 1 discriminator 1 view .LVU871
1081:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 2932              		.loc 1 1081 3 discriminator 1 view .LVU872
 2933 0058 094B     		ldr	r3, .L223
 2934              	.LVL298:
1081:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 2935              		.loc 1 1081 3 is_stmt 0 discriminator 1 view .LVU873
 2936 005a 40F23942 		movw	r2, #1081
 2937              	.LVL299:
1081:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 2938              		.loc 1 1081 3 discriminator 1 view .LVU874
 2939 005e 0B49     		ldr	r1, .L223+12
 2940              	.LVL300:
1081:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 2941              		.loc 1 1081 3 discriminator 1 view .LVU875
 2942 0060 0948     		ldr	r0, .L223+8
 2943              	.LVL301:
1081:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 2944              		.loc 1 1081 3 discriminator 1 view .LVU876
 2945 0062 FFF7FEFF 		bl	printf
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 89


 2946              	.LVL302:
1081:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 2947              		.loc 1 1081 3 is_stmt 1 discriminator 1 view .LVU877
1081:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 2948              		.loc 1 1081 3 discriminator 1 view .LVU878
 2949 0066 0020     		movs	r0, #0
1081:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 2950              		.loc 1 1081 3 is_stmt 0 view .LVU879
 2951 0068 E2E7     		b	.L211
 2952              	.LVL303:
 2953              	.L216:
1090:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       /* copying failed: pbuf is too short */
 2954              		.loc 1 1090 5 is_stmt 1 view .LVU880
1090:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       /* copying failed: pbuf is too short */
 2955              		.loc 1 1090 9 is_stmt 0 view .LVU881
 2956 006a 2A46     		mov	r2, r5
 2957 006c 3146     		mov	r1, r6
 2958 006e FFF7FEFF 		bl	pbuf_copy_partial
 2959              	.LVL304:
1090:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       /* copying failed: pbuf is too short */
 2960              		.loc 1 1090 8 discriminator 1 view .LVU882
 2961 0072 A842     		cmp	r0, r5
 2962 0074 01D1     		bne	.L222
1094:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
 2963              		.loc 1 1094 12 view .LVU883
 2964 0076 3046     		mov	r0, r6
 2965 0078 DAE7     		b	.L211
 2966              	.L222:
1092:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     }
 2967              		.loc 1 1092 14 view .LVU884
 2968 007a 0020     		movs	r0, #0
 2969 007c D8E7     		b	.L211
 2970              	.L224:
 2971 007e 00BF     		.align	2
 2972              	.L223:
 2973 0080 00000000 		.word	.LC0
 2974 0084 00000000 		.word	.LC28
 2975 0088 3C000000 		.word	.LC2
 2976 008c 24000000 		.word	.LC29
 2977              		.cfi_endproc
 2978              	.LFE198:
 2980              		.section	.text.pbuf_skip,"ax",%progbits
 2981              		.align	1
 2982              		.global	pbuf_skip
 2983              		.syntax unified
 2984              		.thumb
 2985              		.thumb_func
 2987              	pbuf_skip:
 2988              	.LVL305:
 2989              	.LFB200:
1167:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
1168:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** /**
1169:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @ingroup pbuf
1170:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * Skip a number of bytes at the start of a pbuf
1171:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
1172:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param in input pbuf
1173:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param in_offset offset to skip
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 90


1174:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param out_offset resulting offset in the returned pbuf
1175:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @return the pbuf in the queue where the offset is
1176:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  */
1177:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** struct pbuf *
1178:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** pbuf_skip(struct pbuf *in, u16_t in_offset, u16_t *out_offset)
1179:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** {
 2990              		.loc 1 1179 1 is_stmt 1 view -0
 2991              		.cfi_startproc
 2992              		@ args = 0, pretend = 0, frame = 0
 2993              		@ frame_needed = 0, uses_anonymous_args = 0
 2994              		.loc 1 1179 1 is_stmt 0 view .LVU886
 2995 0000 08B5     		push	{r3, lr}
 2996              	.LCFI38:
 2997              		.cfi_def_cfa_offset 8
 2998              		.cfi_offset 3, -8
 2999              		.cfi_offset 14, -4
1180:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   const struct pbuf *out = pbuf_skip_const(in, in_offset, out_offset);
 3000              		.loc 1 1180 3 is_stmt 1 view .LVU887
 3001              		.loc 1 1180 28 is_stmt 0 view .LVU888
 3002 0002 FFF7FEFF 		bl	pbuf_skip_const
 3003              	.LVL306:
1181:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   return LWIP_CONST_CAST(struct pbuf *, out);
 3004              		.loc 1 1181 3 is_stmt 1 view .LVU889
1182:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** }
 3005              		.loc 1 1182 1 is_stmt 0 view .LVU890
 3006 0006 08BD     		pop	{r3, pc}
 3007              		.cfi_endproc
 3008              	.LFE200:
 3010              		.section	.rodata.pbuf_take.str1.4,"aMS",%progbits,1
 3011              		.align	2
 3012              	.LC30:
 3013 0000 70627566 		.ascii	"pbuf_take: invalid buf\000"
 3013      5F74616B 
 3013      653A2069 
 3013      6E76616C 
 3013      69642062 
 3014 0017 00       		.align	2
 3015              	.LC31:
 3016 0018 70627566 		.ascii	"pbuf_take: invalid dataptr\000"
 3016      5F74616B 
 3016      653A2069 
 3016      6E76616C 
 3016      69642064 
 3017 0033 00       		.align	2
 3018              	.LC32:
 3019 0034 70627566 		.ascii	"pbuf_take: buf not large enough\000"
 3019      5F74616B 
 3019      653A2062 
 3019      7566206E 
 3019      6F74206C 
 3020              		.align	2
 3021              	.LC33:
 3022 0054 70627566 		.ascii	"pbuf_take: invalid pbuf\000"
 3022      5F74616B 
 3022      653A2069 
 3022      6E76616C 
 3022      69642070 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 91


 3023              		.align	2
 3024              	.LC34:
 3025 006c 64696420 		.ascii	"did not copy all data\000"
 3025      6E6F7420 
 3025      636F7079 
 3025      20616C6C 
 3025      20646174 
 3026              		.section	.text.pbuf_take,"ax",%progbits
 3027              		.align	1
 3028              		.global	pbuf_take
 3029              		.syntax unified
 3030              		.thumb
 3031              		.thumb_func
 3033              	pbuf_take:
 3034              	.LVL307:
 3035              	.LFB201:
1183:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
1184:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** /**
1185:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @ingroup pbuf
1186:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * Copy application supplied data into a pbuf.
1187:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * This function can only be used to copy the equivalent of buf->tot_len data.
1188:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
1189:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param buf pbuf to fill with data
1190:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param dataptr application supplied data buffer
1191:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param len length of the application supplied data buffer
1192:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
1193:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @return ERR_OK if successful, ERR_MEM if the pbuf is not big enough
1194:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  */
1195:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** err_t
1196:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** pbuf_take(struct pbuf *buf, const void *dataptr, u16_t len)
1197:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** {
 3036              		.loc 1 1197 1 is_stmt 1 view -0
 3037              		.cfi_startproc
 3038              		@ args = 0, pretend = 0, frame = 0
 3039              		@ frame_needed = 0, uses_anonymous_args = 0
 3040              		.loc 1 1197 1 is_stmt 0 view .LVU892
 3041 0000 2DE9F843 		push	{r3, r4, r5, r6, r7, r8, r9, lr}
 3042              	.LCFI39:
 3043              		.cfi_def_cfa_offset 32
 3044              		.cfi_offset 3, -32
 3045              		.cfi_offset 4, -28
 3046              		.cfi_offset 5, -24
 3047              		.cfi_offset 6, -20
 3048              		.cfi_offset 7, -16
 3049              		.cfi_offset 8, -12
 3050              		.cfi_offset 9, -8
 3051              		.cfi_offset 14, -4
1198:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   struct pbuf *p;
 3052              		.loc 1 1198 3 is_stmt 1 view .LVU893
1199:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   size_t buf_copy_len;
 3053              		.loc 1 1199 3 view .LVU894
1200:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   size_t total_copy_len = len;
 3054              		.loc 1 1200 3 view .LVU895
 3055              	.LVL308:
1201:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   size_t copied_total = 0;
 3056              		.loc 1 1201 3 view .LVU896
1202:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 92


1203:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ERROR("pbuf_take: invalid buf", (buf != NULL), return ERR_ARG;);
 3057              		.loc 1 1203 3 view .LVU897
 3058              		.loc 1 1203 3 view .LVU898
 3059 0004 48B1     		cbz	r0, .L238
 3060 0006 8846     		mov	r8, r1
 3061 0008 9146     		mov	r9, r2
 3062              	.LVL309:
 3063              		.loc 1 1203 3 is_stmt 0 view .LVU899
 3064 000a 0546     		mov	r5, r0
 3065              		.loc 1 1203 3 is_stmt 1 discriminator 2 view .LVU900
 3066              		.loc 1 1203 3 discriminator 2 view .LVU901
1204:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ERROR("pbuf_take: invalid dataptr", (dataptr != NULL), return ERR_ARG;);
 3067              		.loc 1 1204 3 view .LVU902
 3068              		.loc 1 1204 3 view .LVU903
 3069 000c 79B1     		cbz	r1, .L239
 3070              		.loc 1 1204 3 discriminator 2 view .LVU904
 3071              		.loc 1 1204 3 discriminator 2 view .LVU905
1205:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ERROR("pbuf_take: buf not large enough", (buf->tot_len >= len), return ERR_MEM;);
 3072              		.loc 1 1205 3 view .LVU906
 3073              		.loc 1 1205 3 view .LVU907
 3074 000e 0389     		ldrh	r3, [r0, #8]
 3075 0010 9342     		cmp	r3, r2
 3076 0012 16D3     		bcc	.L240
1200:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   size_t copied_total = 0;
 3077              		.loc 1 1200 10 is_stmt 0 view .LVU908
 3078 0014 1646     		mov	r6, r2
1201:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 3079              		.loc 1 1201 10 view .LVU909
 3080 0016 0027     		movs	r7, #0
 3081 0018 2EE0     		b	.L231
 3082              	.LVL310:
 3083              	.L238:
1203:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ERROR("pbuf_take: invalid dataptr", (dataptr != NULL), return ERR_ARG;);
 3084              		.loc 1 1203 3 is_stmt 1 discriminator 1 view .LVU910
1203:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ERROR("pbuf_take: invalid dataptr", (dataptr != NULL), return ERR_ARG;);
 3085              		.loc 1 1203 3 discriminator 1 view .LVU911
 3086 001a 224B     		ldr	r3, .L244
 3087 001c 40F2B342 		movw	r2, #1203
 3088              	.LVL311:
1203:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ERROR("pbuf_take: invalid dataptr", (dataptr != NULL), return ERR_ARG;);
 3089              		.loc 1 1203 3 is_stmt 0 discriminator 1 view .LVU912
 3090 0020 2149     		ldr	r1, .L244+4
 3091              	.LVL312:
1203:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ERROR("pbuf_take: invalid dataptr", (dataptr != NULL), return ERR_ARG;);
 3092              		.loc 1 1203 3 discriminator 1 view .LVU913
 3093 0022 2248     		ldr	r0, .L244+8
 3094              	.LVL313:
1203:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ERROR("pbuf_take: invalid dataptr", (dataptr != NULL), return ERR_ARG;);
 3095              		.loc 1 1203 3 discriminator 1 view .LVU914
 3096 0024 FFF7FEFF 		bl	printf
 3097              	.LVL314:
1203:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ERROR("pbuf_take: invalid dataptr", (dataptr != NULL), return ERR_ARG;);
 3098              		.loc 1 1203 3 is_stmt 1 discriminator 1 view .LVU915
1203:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ERROR("pbuf_take: invalid dataptr", (dataptr != NULL), return ERR_ARG;);
 3099              		.loc 1 1203 3 discriminator 1 view .LVU916
 3100 0028 6FF00F00 		mvn	r0, #15
1203:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ERROR("pbuf_take: invalid dataptr", (dataptr != NULL), return ERR_ARG;);
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 93


 3101              		.loc 1 1203 3 is_stmt 0 view .LVU917
 3102 002c 2FE0     		b	.L229
 3103              	.LVL315:
 3104              	.L239:
1204:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ERROR("pbuf_take: buf not large enough", (buf->tot_len >= len), return ERR_MEM;);
 3105              		.loc 1 1204 3 is_stmt 1 discriminator 1 view .LVU918
1204:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ERROR("pbuf_take: buf not large enough", (buf->tot_len >= len), return ERR_MEM;);
 3106              		.loc 1 1204 3 discriminator 1 view .LVU919
 3107 002e 1D4B     		ldr	r3, .L244
 3108 0030 40F2B442 		movw	r2, #1204
 3109              	.LVL316:
1204:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ERROR("pbuf_take: buf not large enough", (buf->tot_len >= len), return ERR_MEM;);
 3110              		.loc 1 1204 3 is_stmt 0 discriminator 1 view .LVU920
 3111 0034 1E49     		ldr	r1, .L244+12
 3112              	.LVL317:
1204:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ERROR("pbuf_take: buf not large enough", (buf->tot_len >= len), return ERR_MEM;);
 3113              		.loc 1 1204 3 discriminator 1 view .LVU921
 3114 0036 1D48     		ldr	r0, .L244+8
 3115              	.LVL318:
1204:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ERROR("pbuf_take: buf not large enough", (buf->tot_len >= len), return ERR_MEM;);
 3116              		.loc 1 1204 3 discriminator 1 view .LVU922
 3117 0038 FFF7FEFF 		bl	printf
 3118              	.LVL319:
1204:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ERROR("pbuf_take: buf not large enough", (buf->tot_len >= len), return ERR_MEM;);
 3119              		.loc 1 1204 3 is_stmt 1 discriminator 1 view .LVU923
1204:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ERROR("pbuf_take: buf not large enough", (buf->tot_len >= len), return ERR_MEM;);
 3120              		.loc 1 1204 3 discriminator 1 view .LVU924
 3121 003c 6FF00F00 		mvn	r0, #15
1204:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ERROR("pbuf_take: buf not large enough", (buf->tot_len >= len), return ERR_MEM;);
 3122              		.loc 1 1204 3 is_stmt 0 view .LVU925
 3123 0040 25E0     		b	.L229
 3124              	.LVL320:
 3125              	.L240:
 3126              		.loc 1 1205 3 is_stmt 1 discriminator 1 view .LVU926
 3127              		.loc 1 1205 3 discriminator 1 view .LVU927
 3128 0042 184B     		ldr	r3, .L244
 3129 0044 40F2B542 		movw	r2, #1205
 3130              	.LVL321:
 3131              		.loc 1 1205 3 is_stmt 0 discriminator 1 view .LVU928
 3132 0048 1A49     		ldr	r1, .L244+16
 3133              	.LVL322:
 3134              		.loc 1 1205 3 discriminator 1 view .LVU929
 3135 004a 1848     		ldr	r0, .L244+8
 3136              	.LVL323:
 3137              		.loc 1 1205 3 discriminator 1 view .LVU930
 3138 004c FFF7FEFF 		bl	printf
 3139              	.LVL324:
 3140              		.loc 1 1205 3 is_stmt 1 discriminator 1 view .LVU931
 3141              		.loc 1 1205 3 discriminator 1 view .LVU932
 3142 0050 4FF0FF30 		mov	r0, #-1
 3143              		.loc 1 1205 3 is_stmt 0 view .LVU933
 3144 0054 1BE0     		b	.L229
 3145              	.LVL325:
 3146              	.L242:
1206:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
1207:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   if ((buf == NULL) || (dataptr == NULL) || (buf->tot_len < len)) {
1208:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     return ERR_ARG;
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 94


1209:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
1210:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
1211:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* Note some systems use byte copy if dataptr or one of the pbuf payload pointers are unaligned. 
1212:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   for (p = buf; total_copy_len != 0; p = p->next) {
1213:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     LWIP_ASSERT("pbuf_take: invalid pbuf", p != NULL);
 3147              		.loc 1 1213 5 is_stmt 1 discriminator 1 view .LVU934
 3148              		.loc 1 1213 5 discriminator 1 view .LVU935
 3149 0056 134B     		ldr	r3, .L244
 3150 0058 40F2BD42 		movw	r2, #1213
 3151 005c 1649     		ldr	r1, .L244+20
 3152 005e 1348     		ldr	r0, .L244+8
 3153 0060 FFF7FEFF 		bl	printf
 3154              	.LVL326:
 3155 0064 0BE0     		b	.L232
 3156              	.LVL327:
 3157              	.L233:
1214:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     buf_copy_len = total_copy_len;
1215:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     if (buf_copy_len > p->len) {
1216:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       /* this pbuf cannot hold all remaining data */
1217:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       buf_copy_len = p->len;
1218:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     }
1219:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* copy the necessary parts of the buffer */
1220:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     MEMCPY(p->payload, &((const char *)dataptr)[copied_total], buf_copy_len);
 3158              		.loc 1 1220 5 view .LVU936
 3159 0066 2246     		mov	r2, r4
 3160 0068 08EB0701 		add	r1, r8, r7
 3161 006c 6868     		ldr	r0, [r5, #4]
 3162 006e FFF7FEFF 		bl	memcpy
 3163              	.LVL328:
1221:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     total_copy_len -= buf_copy_len;
 3164              		.loc 1 1221 5 view .LVU937
 3165              		.loc 1 1221 20 is_stmt 0 view .LVU938
 3166 0072 361B     		subs	r6, r6, r4
 3167              	.LVL329:
1222:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     copied_total += buf_copy_len;
 3168              		.loc 1 1222 5 is_stmt 1 view .LVU939
 3169              		.loc 1 1222 18 is_stmt 0 view .LVU940
 3170 0074 2744     		add	r7, r7, r4
 3171              	.LVL330:
1212:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     LWIP_ASSERT("pbuf_take: invalid pbuf", p != NULL);
 3172              		.loc 1 1212 40 is_stmt 1 discriminator 2 view .LVU941
 3173 0076 2D68     		ldr	r5, [r5]
 3174              	.LVL331:
 3175              	.L231:
1212:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     LWIP_ASSERT("pbuf_take: invalid pbuf", p != NULL);
 3176              		.loc 1 1212 32 discriminator 1 view .LVU942
 3177 0078 36B1     		cbz	r6, .L241
1213:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     buf_copy_len = total_copy_len;
 3178              		.loc 1 1213 5 view .LVU943
1213:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     buf_copy_len = total_copy_len;
 3179              		.loc 1 1213 5 view .LVU944
 3180 007a 002D     		cmp	r5, #0
 3181 007c EBD0     		beq	.L242
 3182              	.L232:
1213:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     buf_copy_len = total_copy_len;
 3183              		.loc 1 1213 5 discriminator 3 view .LVU945
1213:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     buf_copy_len = total_copy_len;
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 95


 3184              		.loc 1 1213 5 discriminator 3 view .LVU946
1214:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     if (buf_copy_len > p->len) {
 3185              		.loc 1 1214 5 view .LVU947
 3186              	.LVL332:
1215:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       /* this pbuf cannot hold all remaining data */
 3187              		.loc 1 1215 5 view .LVU948
1215:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       /* this pbuf cannot hold all remaining data */
 3188              		.loc 1 1215 25 is_stmt 0 view .LVU949
 3189 007e 6C89     		ldrh	r4, [r5, #10]
1215:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       /* this pbuf cannot hold all remaining data */
 3190              		.loc 1 1215 8 view .LVU950
 3191 0080 B442     		cmp	r4, r6
 3192 0082 F0D3     		bcc	.L233
1214:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     if (buf_copy_len > p->len) {
 3193              		.loc 1 1214 18 view .LVU951
 3194 0084 3446     		mov	r4, r6
 3195 0086 EEE7     		b	.L233
 3196              	.LVL333:
 3197              	.L241:
1223:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
1224:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ASSERT("did not copy all data", total_copy_len == 0 && copied_total == len);
 3198              		.loc 1 1224 3 is_stmt 1 view .LVU952
 3199              		.loc 1 1224 3 view .LVU953
 3200              		.loc 1 1224 3 is_stmt 0 discriminator 2 view .LVU954
 3201 0088 4F45     		cmp	r7, r9
 3202 008a 02D1     		bne	.L243
1225:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   return ERR_OK;
 3203              		.loc 1 1225 10 view .LVU955
 3204 008c 0020     		movs	r0, #0
 3205              	.LVL334:
 3206              	.L229:
1226:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** }
 3207              		.loc 1 1226 1 view .LVU956
 3208 008e BDE8F883 		pop	{r3, r4, r5, r6, r7, r8, r9, pc}
 3209              	.LVL335:
 3210              	.L243:
1224:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   return ERR_OK;
 3211              		.loc 1 1224 3 is_stmt 1 discriminator 3 view .LVU957
1224:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   return ERR_OK;
 3212              		.loc 1 1224 3 discriminator 3 view .LVU958
 3213 0092 044B     		ldr	r3, .L244
 3214 0094 4FF49962 		mov	r2, #1224
 3215 0098 0849     		ldr	r1, .L244+24
 3216 009a 0448     		ldr	r0, .L244+8
 3217 009c FFF7FEFF 		bl	printf
 3218              	.LVL336:
1225:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   return ERR_OK;
 3219              		.loc 1 1225 10 is_stmt 0 view .LVU959
 3220 00a0 0020     		movs	r0, #0
 3221 00a2 F4E7     		b	.L229
 3222              	.L245:
 3223              		.align	2
 3224              	.L244:
 3225 00a4 00000000 		.word	.LC0
 3226 00a8 00000000 		.word	.LC30
 3227 00ac 3C000000 		.word	.LC2
 3228 00b0 18000000 		.word	.LC31
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 96


 3229 00b4 34000000 		.word	.LC32
 3230 00b8 54000000 		.word	.LC33
 3231 00bc 6C000000 		.word	.LC34
 3232              		.cfi_endproc
 3233              	.LFE201:
 3235              		.section	.rodata.pbuf_take_at.str1.4,"aMS",%progbits,1
 3236              		.align	2
 3237              	.LC35:
 3238 0000 63686563 		.ascii	"check pbuf_skip result\000"
 3238      6B207062 
 3238      75665F73 
 3238      6B697020 
 3238      72657375 
 3239              		.section	.text.pbuf_take_at,"ax",%progbits
 3240              		.align	1
 3241              		.global	pbuf_take_at
 3242              		.syntax unified
 3243              		.thumb
 3244              		.thumb_func
 3246              	pbuf_take_at:
 3247              	.LVL337:
 3248              	.LFB202:
1227:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
1228:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** /**
1229:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @ingroup pbuf
1230:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * Same as pbuf_take() but puts data at an offset
1231:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
1232:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param buf pbuf to fill with data
1233:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param dataptr application supplied data buffer
1234:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param len length of the application supplied data buffer
1235:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param offset offset in pbuf where to copy dataptr to
1236:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
1237:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @return ERR_OK if successful, ERR_MEM if the pbuf is not big enough
1238:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  */
1239:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** err_t
1240:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** pbuf_take_at(struct pbuf *buf, const void *dataptr, u16_t len, u16_t offset)
1241:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** {
 3249              		.loc 1 1241 1 is_stmt 1 view -0
 3250              		.cfi_startproc
 3251              		@ args = 0, pretend = 0, frame = 8
 3252              		@ frame_needed = 0, uses_anonymous_args = 0
 3253              		.loc 1 1241 1 is_stmt 0 view .LVU961
 3254 0000 F0B5     		push	{r4, r5, r6, r7, lr}
 3255              	.LCFI40:
 3256              		.cfi_def_cfa_offset 20
 3257              		.cfi_offset 4, -20
 3258              		.cfi_offset 5, -16
 3259              		.cfi_offset 6, -12
 3260              		.cfi_offset 7, -8
 3261              		.cfi_offset 14, -4
 3262 0002 83B0     		sub	sp, sp, #12
 3263              	.LCFI41:
 3264              		.cfi_def_cfa_offset 32
 3265 0004 0E46     		mov	r6, r1
 3266 0006 1446     		mov	r4, r2
1242:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   u16_t target_offset;
 3267              		.loc 1 1242 3 is_stmt 1 view .LVU962
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 97


1243:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   struct pbuf *q = pbuf_skip(buf, offset, &target_offset);
 3268              		.loc 1 1243 3 view .LVU963
 3269              		.loc 1 1243 20 is_stmt 0 view .LVU964
 3270 0008 0DF10602 		add	r2, sp, #6
 3271              	.LVL338:
 3272              		.loc 1 1243 20 view .LVU965
 3273 000c 1946     		mov	r1, r3
 3274              	.LVL339:
 3275              		.loc 1 1243 20 view .LVU966
 3276 000e FFF7FEFF 		bl	pbuf_skip
 3277              	.LVL340:
1244:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
1245:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* return requested data if pbuf is OK */
1246:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   if ((q != NULL) && (q->tot_len >= target_offset + len)) {
 3278              		.loc 1 1246 3 is_stmt 1 view .LVU967
 3279              		.loc 1 1246 6 is_stmt 0 view .LVU968
 3280 0012 58B3     		cbz	r0, .L250
 3281 0014 0746     		mov	r7, r0
 3282              		.loc 1 1246 24 discriminator 1 view .LVU969
 3283 0016 0189     		ldrh	r1, [r0, #8]
 3284              		.loc 1 1246 51 discriminator 1 view .LVU970
 3285 0018 BDF80630 		ldrh	r3, [sp, #6]
 3286 001c 1A19     		adds	r2, r3, r4
 3287              		.loc 1 1246 19 discriminator 1 view .LVU971
 3288 001e 9142     		cmp	r1, r2
 3289 0020 27DB     		blt	.L251
 3290              	.LBB15:
1247:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     u16_t remaining_len = len;
 3291              		.loc 1 1247 5 is_stmt 1 view .LVU972
 3292              	.LVL341:
1248:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     const u8_t *src_ptr = (const u8_t *)dataptr;
 3293              		.loc 1 1248 5 view .LVU973
1249:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* copy the part that goes into the first pbuf */
1250:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     u16_t first_copy_len;
 3294              		.loc 1 1250 5 view .LVU974
1251:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     LWIP_ASSERT("check pbuf_skip result", target_offset < q->len);
 3295              		.loc 1 1251 5 view .LVU975
 3296              		.loc 1 1251 5 view .LVU976
 3297 0022 4289     		ldrh	r2, [r0, #10]
 3298 0024 9342     		cmp	r3, r2
 3299 0026 13D2     		bcs	.L255
 3300              	.LVL342:
 3301              	.L248:
 3302              		.loc 1 1251 5 discriminator 3 view .LVU977
 3303              		.loc 1 1251 5 discriminator 3 view .LVU978
1252:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     first_copy_len = (u16_t)LWIP_MIN(q->len - target_offset, len);
 3304              		.loc 1 1252 5 view .LVU979
 3305              		.loc 1 1252 29 is_stmt 0 view .LVU980
 3306 0028 7D89     		ldrh	r5, [r7, #10]
 3307 002a BDF80630 		ldrh	r3, [sp, #6]
 3308 002e EA1A     		subs	r2, r5, r3
 3309              		.loc 1 1252 22 view .LVU981
 3310 0030 9442     		cmp	r4, r2
 3311 0032 15DD     		ble	.L252
 3312              		.loc 1 1252 22 discriminator 1 view .LVU982
 3313 0034 95B2     		uxth	r5, r2
 3314              	.L249:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 98


 3315              	.LVL343:
1253:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     MEMCPY(((u8_t *)q->payload) + target_offset, dataptr, first_copy_len);
 3316              		.loc 1 1253 5 is_stmt 1 view .LVU983
 3317 0036 7868     		ldr	r0, [r7, #4]
 3318 0038 2A46     		mov	r2, r5
 3319 003a 3146     		mov	r1, r6
 3320 003c 1844     		add	r0, r0, r3
 3321 003e FFF7FEFF 		bl	memcpy
 3322              	.LVL344:
1254:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     remaining_len = (u16_t)(remaining_len - first_copy_len);
 3323              		.loc 1 1254 5 view .LVU984
 3324              		.loc 1 1254 19 is_stmt 0 view .LVU985
 3325 0042 621B     		subs	r2, r4, r5
 3326 0044 92B2     		uxth	r2, r2
 3327              	.LVL345:
1255:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     src_ptr += first_copy_len;
 3328              		.loc 1 1255 5 is_stmt 1 view .LVU986
 3329              		.loc 1 1255 13 is_stmt 0 view .LVU987
 3330 0046 7119     		adds	r1, r6, r5
 3331              	.LVL346:
1256:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     if (remaining_len > 0) {
 3332              		.loc 1 1256 5 is_stmt 1 view .LVU988
 3333              		.loc 1 1256 8 is_stmt 0 view .LVU989
 3334 0048 62B9     		cbnz	r2, .L256
1257:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       return pbuf_take(q->next, src_ptr, remaining_len);
1258:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     }
1259:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     return ERR_OK;
 3335              		.loc 1 1259 12 view .LVU990
 3336 004a 0020     		movs	r0, #0
 3337              	.LVL347:
 3338              	.L247:
 3339              		.loc 1 1259 12 view .LVU991
 3340              	.LBE15:
1260:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
1261:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   return ERR_MEM;
1262:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** }
 3341              		.loc 1 1262 1 view .LVU992
 3342 004c 03B0     		add	sp, sp, #12
 3343              	.LCFI42:
 3344              		.cfi_remember_state
 3345              		.cfi_def_cfa_offset 20
 3346              		@ sp needed
 3347 004e F0BD     		pop	{r4, r5, r6, r7, pc}
 3348              	.LVL348:
 3349              	.L255:
 3350              	.LCFI43:
 3351              		.cfi_restore_state
 3352              	.LBB16:
1251:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     first_copy_len = (u16_t)LWIP_MIN(q->len - target_offset, len);
 3353              		.loc 1 1251 5 is_stmt 1 discriminator 1 view .LVU993
1251:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     first_copy_len = (u16_t)LWIP_MIN(q->len - target_offset, len);
 3354              		.loc 1 1251 5 discriminator 1 view .LVU994
 3355 0050 094B     		ldr	r3, .L257
 3356 0052 40F2E342 		movw	r2, #1251
 3357 0056 0949     		ldr	r1, .L257+4
 3358 0058 0948     		ldr	r0, .L257+8
 3359              	.LVL349:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 99


1251:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     first_copy_len = (u16_t)LWIP_MIN(q->len - target_offset, len);
 3360              		.loc 1 1251 5 is_stmt 0 discriminator 1 view .LVU995
 3361 005a FFF7FEFF 		bl	printf
 3362              	.LVL350:
 3363 005e E3E7     		b	.L248
 3364              	.L252:
1252:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     MEMCPY(((u8_t *)q->payload) + target_offset, dataptr, first_copy_len);
 3365              		.loc 1 1252 22 discriminator 2 view .LVU996
 3366 0060 2546     		mov	r5, r4
 3367 0062 E8E7     		b	.L249
 3368              	.LVL351:
 3369              	.L256:
1257:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     }
 3370              		.loc 1 1257 7 is_stmt 1 view .LVU997
1257:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     }
 3371              		.loc 1 1257 14 is_stmt 0 view .LVU998
 3372 0064 3868     		ldr	r0, [r7]
 3373 0066 FFF7FEFF 		bl	pbuf_take
 3374              	.LVL352:
1257:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     }
 3375              		.loc 1 1257 14 view .LVU999
 3376 006a EFE7     		b	.L247
 3377              	.LVL353:
 3378              	.L250:
1257:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     }
 3379              		.loc 1 1257 14 view .LVU1000
 3380              	.LBE16:
1261:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** }
 3381              		.loc 1 1261 10 view .LVU1001
 3382 006c 4FF0FF30 		mov	r0, #-1
 3383              	.LVL354:
1261:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** }
 3384              		.loc 1 1261 10 view .LVU1002
 3385 0070 ECE7     		b	.L247
 3386              	.LVL355:
 3387              	.L251:
1261:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** }
 3388              		.loc 1 1261 10 view .LVU1003
 3389 0072 4FF0FF30 		mov	r0, #-1
 3390              	.LVL356:
1261:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** }
 3391              		.loc 1 1261 10 view .LVU1004
 3392 0076 E9E7     		b	.L247
 3393              	.L258:
 3394              		.align	2
 3395              	.L257:
 3396 0078 00000000 		.word	.LC0
 3397 007c 00000000 		.word	.LC35
 3398 0080 3C000000 		.word	.LC2
 3399              		.cfi_endproc
 3400              	.LFE202:
 3402              		.section	.rodata.pbuf_clone.str1.4,"aMS",%progbits,1
 3403              		.align	2
 3404              	.LC36:
 3405 0000 70627566 		.ascii	"pbuf_copy failed\000"
 3405      5F636F70 
 3405      79206661 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 100


 3405      696C6564 
 3405      00
 3406              		.section	.text.pbuf_clone,"ax",%progbits
 3407              		.align	1
 3408              		.global	pbuf_clone
 3409              		.syntax unified
 3410              		.thumb
 3411              		.thumb_func
 3413              	pbuf_clone:
 3414              	.LVL357:
 3415              	.LFB204:
1263:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
1264:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** /**
1265:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @ingroup pbuf
1266:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * Creates a single pbuf out of a queue of pbufs.
1267:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
1268:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @remark: Either the source pbuf 'p' is freed by this function or the original
1269:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *          pbuf 'p' is returned, therefore the caller has to check the result!
1270:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
1271:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param p the source pbuf
1272:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param layer pbuf_layer of the new pbuf
1273:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
1274:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @return a new, single pbuf (p->next is NULL)
1275:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *         or the old pbuf if allocation fails
1276:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  */
1277:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** struct pbuf *
1278:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** pbuf_coalesce(struct pbuf *p, pbuf_layer layer)
1279:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** {
1280:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   struct pbuf *q;
1281:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   if (p->next == NULL) {
1282:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     return p;
1283:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
1284:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   q = pbuf_clone(layer, PBUF_RAM, p);
1285:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   if (q == NULL) {
1286:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* @todo: what do we do now? */
1287:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     return p;
1288:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
1289:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   pbuf_free(p);
1290:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   return q;
1291:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** }
1292:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
1293:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** /**
1294:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @ingroup pbuf
1295:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * Allocates a new pbuf of same length (via pbuf_alloc()) and copies the source
1296:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * pbuf into this new pbuf (using pbuf_copy()).
1297:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
1298:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param layer pbuf_layer of the new pbuf
1299:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param type this parameter decides how and where the pbuf should be allocated
1300:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *             (@see pbuf_alloc())
1301:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param p the source pbuf
1302:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
1303:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @return a new pbuf or NULL if allocation fails
1304:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  */
1305:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** struct pbuf *
1306:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** pbuf_clone(pbuf_layer layer, pbuf_type type, struct pbuf *p)
1307:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** {
 3416              		.loc 1 1307 1 is_stmt 1 view -0
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 101


 3417              		.cfi_startproc
 3418              		@ args = 0, pretend = 0, frame = 0
 3419              		@ frame_needed = 0, uses_anonymous_args = 0
 3420              		.loc 1 1307 1 is_stmt 0 view .LVU1006
 3421 0000 38B5     		push	{r3, r4, r5, lr}
 3422              	.LCFI44:
 3423              		.cfi_def_cfa_offset 16
 3424              		.cfi_offset 3, -16
 3425              		.cfi_offset 4, -12
 3426              		.cfi_offset 5, -8
 3427              		.cfi_offset 14, -4
 3428 0002 1446     		mov	r4, r2
1308:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   struct pbuf *q;
 3429              		.loc 1 1308 3 is_stmt 1 view .LVU1007
1309:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   err_t err;
 3430              		.loc 1 1309 3 view .LVU1008
1310:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   q = pbuf_alloc(layer, p->tot_len, type);
 3431              		.loc 1 1310 3 view .LVU1009
 3432              		.loc 1 1310 7 is_stmt 0 view .LVU1010
 3433 0004 0A46     		mov	r2, r1
 3434              	.LVL358:
 3435              		.loc 1 1310 7 view .LVU1011
 3436 0006 2189     		ldrh	r1, [r4, #8]
 3437              	.LVL359:
 3438              		.loc 1 1310 7 view .LVU1012
 3439 0008 FFF7FEFF 		bl	pbuf_alloc
 3440              	.LVL360:
1311:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   if (q == NULL) {
 3441              		.loc 1 1311 3 is_stmt 1 view .LVU1013
 3442              		.loc 1 1311 6 is_stmt 0 view .LVU1014
 3443 000c 0546     		mov	r5, r0
 3444 000e 18B1     		cbz	r0, .L259
1312:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     return NULL;
1313:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
1314:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   err = pbuf_copy(q, p);
 3445              		.loc 1 1314 3 is_stmt 1 view .LVU1015
 3446              		.loc 1 1314 9 is_stmt 0 view .LVU1016
 3447 0010 2146     		mov	r1, r4
 3448 0012 FFF7FEFF 		bl	pbuf_copy
 3449              	.LVL361:
1315:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_UNUSED_ARG(err); /* in case of LWIP_NOASSERT */
 3450              		.loc 1 1315 3 is_stmt 1 view .LVU1017
1316:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ASSERT("pbuf_copy failed", err == ERR_OK);
 3451              		.loc 1 1316 3 view .LVU1018
 3452              		.loc 1 1316 3 view .LVU1019
 3453 0016 08B9     		cbnz	r0, .L262
 3454              	.LVL362:
 3455              	.L259:
1317:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   return q;
1318:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** }
 3456              		.loc 1 1318 1 is_stmt 0 view .LVU1020
 3457 0018 2846     		mov	r0, r5
 3458 001a 38BD     		pop	{r3, r4, r5, pc}
 3459              	.LVL363:
 3460              	.L262:
1316:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ASSERT("pbuf_copy failed", err == ERR_OK);
 3461              		.loc 1 1316 3 is_stmt 1 discriminator 1 view .LVU1021
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 102


1316:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ASSERT("pbuf_copy failed", err == ERR_OK);
 3462              		.loc 1 1316 3 discriminator 1 view .LVU1022
 3463 001c 034B     		ldr	r3, .L263
 3464 001e 40F22452 		movw	r2, #1316
 3465 0022 0349     		ldr	r1, .L263+4
 3466 0024 0348     		ldr	r0, .L263+8
 3467              	.LVL364:
1316:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ASSERT("pbuf_copy failed", err == ERR_OK);
 3468              		.loc 1 1316 3 is_stmt 0 discriminator 1 view .LVU1023
 3469 0026 FFF7FEFF 		bl	printf
 3470              	.LVL365:
 3471 002a F5E7     		b	.L259
 3472              	.L264:
 3473              		.align	2
 3474              	.L263:
 3475 002c 00000000 		.word	.LC0
 3476 0030 00000000 		.word	.LC36
 3477 0034 3C000000 		.word	.LC2
 3478              		.cfi_endproc
 3479              	.LFE204:
 3481              		.section	.text.pbuf_coalesce,"ax",%progbits
 3482              		.align	1
 3483              		.global	pbuf_coalesce
 3484              		.syntax unified
 3485              		.thumb
 3486              		.thumb_func
 3488              	pbuf_coalesce:
 3489              	.LVL366:
 3490              	.LFB203:
1279:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   struct pbuf *q;
 3491              		.loc 1 1279 1 is_stmt 1 view -0
 3492              		.cfi_startproc
 3493              		@ args = 0, pretend = 0, frame = 0
 3494              		@ frame_needed = 0, uses_anonymous_args = 0
1279:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   struct pbuf *q;
 3495              		.loc 1 1279 1 is_stmt 0 view .LVU1025
 3496 0000 38B5     		push	{r3, r4, r5, lr}
 3497              	.LCFI45:
 3498              		.cfi_def_cfa_offset 16
 3499              		.cfi_offset 3, -16
 3500              		.cfi_offset 4, -12
 3501              		.cfi_offset 5, -8
 3502              		.cfi_offset 14, -4
 3503 0002 0446     		mov	r4, r0
1280:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   if (p->next == NULL) {
 3504              		.loc 1 1280 3 is_stmt 1 view .LVU1026
1281:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     return p;
 3505              		.loc 1 1281 3 view .LVU1027
1281:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     return p;
 3506              		.loc 1 1281 8 is_stmt 0 view .LVU1028
 3507 0004 0368     		ldr	r3, [r0]
1281:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     return p;
 3508              		.loc 1 1281 6 view .LVU1029
 3509 0006 5BB1     		cbz	r3, .L266
 3510 0008 0846     		mov	r0, r1
 3511              	.LVL367:
1284:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   if (q == NULL) {
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 103


 3512              		.loc 1 1284 3 is_stmt 1 view .LVU1030
1284:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   if (q == NULL) {
 3513              		.loc 1 1284 7 is_stmt 0 view .LVU1031
 3514 000a 2246     		mov	r2, r4
 3515 000c 4FF42071 		mov	r1, #640
 3516              	.LVL368:
1284:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   if (q == NULL) {
 3517              		.loc 1 1284 7 view .LVU1032
 3518 0010 FFF7FEFF 		bl	pbuf_clone
 3519              	.LVL369:
1285:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* @todo: what do we do now? */
 3520              		.loc 1 1285 3 is_stmt 1 view .LVU1033
1285:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* @todo: what do we do now? */
 3521              		.loc 1 1285 6 is_stmt 0 view .LVU1034
 3522 0014 0546     		mov	r5, r0
 3523 0016 18B1     		cbz	r0, .L266
1289:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   return q;
 3524              		.loc 1 1289 3 is_stmt 1 view .LVU1035
 3525 0018 2046     		mov	r0, r4
 3526              	.LVL370:
1289:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   return q;
 3527              		.loc 1 1289 3 is_stmt 0 view .LVU1036
 3528 001a FFF7FEFF 		bl	pbuf_free
 3529              	.LVL371:
1290:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** }
 3530              		.loc 1 1290 3 is_stmt 1 view .LVU1037
1290:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** }
 3531              		.loc 1 1290 10 is_stmt 0 view .LVU1038
 3532 001e 2C46     		mov	r4, r5
 3533              	.LVL372:
 3534              	.L266:
1291:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 3535              		.loc 1 1291 1 view .LVU1039
 3536 0020 2046     		mov	r0, r4
 3537 0022 38BD     		pop	{r3, r4, r5, pc}
 3538              		.cfi_endproc
 3539              	.LFE203:
 3541              		.section	.text.pbuf_try_get_at,"ax",%progbits
 3542              		.align	1
 3543              		.global	pbuf_try_get_at
 3544              		.syntax unified
 3545              		.thumb
 3546              		.thumb_func
 3548              	pbuf_try_get_at:
 3549              	.LVL373:
 3550              	.LFB206:
1319:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
1320:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** #if LWIP_CHECKSUM_ON_COPY
1321:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** /**
1322:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * Copies data into a single pbuf (*not* into a pbuf queue!) and updates
1323:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * the checksum while copying
1324:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
1325:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param p the pbuf to copy data into
1326:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param start_offset offset of p->payload where to copy the data to
1327:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param dataptr data to copy into the pbuf
1328:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param len length of data to copy into the pbuf
1329:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param chksum pointer to the checksum which is updated
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 104


1330:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @return ERR_OK if successful, another error if the data does not fit
1331:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *         within the (first) pbuf (no pbuf queues!)
1332:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  */
1333:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** err_t
1334:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** pbuf_fill_chksum(struct pbuf *p, u16_t start_offset, const void *dataptr,
1335:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****                  u16_t len, u16_t *chksum)
1336:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** {
1337:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   u32_t acc;
1338:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   u16_t copy_chksum;
1339:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   char *dst_ptr;
1340:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ASSERT("p != NULL", p != NULL);
1341:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ASSERT("dataptr != NULL", dataptr != NULL);
1342:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ASSERT("chksum != NULL", chksum != NULL);
1343:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   LWIP_ASSERT("len != 0", len != 0);
1344:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
1345:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   if ((start_offset >= p->len) || (start_offset + len > p->len)) {
1346:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     return ERR_ARG;
1347:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
1348:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
1349:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   dst_ptr = ((char *)p->payload) + start_offset;
1350:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   copy_chksum = LWIP_CHKSUM_COPY(dst_ptr, dataptr, len);
1351:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   if ((start_offset & 1) != 0) {
1352:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     copy_chksum = SWAP_BYTES_IN_WORD(copy_chksum);
1353:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
1354:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   acc = *chksum;
1355:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   acc += copy_chksum;
1356:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   *chksum = FOLD_U32T(acc);
1357:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   return ERR_OK;
1358:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** }
1359:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** #endif /* LWIP_CHECKSUM_ON_COPY */
1360:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
1361:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** /**
1362:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @ingroup pbuf
1363:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * Get one byte from the specified position in a pbuf
1364:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * WARNING: returns zero for offset >= p->tot_len
1365:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
1366:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param p pbuf to parse
1367:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param offset offset into p of the byte to return
1368:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @return byte at an offset into p OR ZERO IF 'offset' >= p->tot_len
1369:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  */
1370:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** u8_t
1371:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** pbuf_get_at(const struct pbuf *p, u16_t offset)
1372:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** {
1373:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   int ret = pbuf_try_get_at(p, offset);
1374:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   if (ret >= 0) {
1375:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     return (u8_t)ret;
1376:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
1377:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   return 0;
1378:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** }
1379:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
1380:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** /**
1381:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @ingroup pbuf
1382:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * Get one byte from the specified position in a pbuf
1383:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
1384:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param p pbuf to parse
1385:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param offset offset into p of the byte to return
1386:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @return byte at an offset into p [0..0xFF] OR negative if 'offset' >= p->tot_len
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 105


1387:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  */
1388:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** int
1389:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** pbuf_try_get_at(const struct pbuf *p, u16_t offset)
1390:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** {
 3551              		.loc 1 1390 1 is_stmt 1 view -0
 3552              		.cfi_startproc
 3553              		@ args = 0, pretend = 0, frame = 8
 3554              		@ frame_needed = 0, uses_anonymous_args = 0
 3555              		.loc 1 1390 1 is_stmt 0 view .LVU1041
 3556 0000 00B5     		push	{lr}
 3557              	.LCFI46:
 3558              		.cfi_def_cfa_offset 4
 3559              		.cfi_offset 14, -4
 3560 0002 83B0     		sub	sp, sp, #12
 3561              	.LCFI47:
 3562              		.cfi_def_cfa_offset 16
1391:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   u16_t q_idx;
 3563              		.loc 1 1391 3 is_stmt 1 view .LVU1042
1392:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   const struct pbuf *q = pbuf_skip_const(p, offset, &q_idx);
 3564              		.loc 1 1392 3 view .LVU1043
 3565              		.loc 1 1392 26 is_stmt 0 view .LVU1044
 3566 0004 0DF10602 		add	r2, sp, #6
 3567 0008 FFF7FEFF 		bl	pbuf_skip_const
 3568              	.LVL374:
1393:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
1394:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* return requested data if pbuf is OK */
1395:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   if ((q != NULL) && (q->len > q_idx)) {
 3569              		.loc 1 1395 3 is_stmt 1 view .LVU1045
 3570              		.loc 1 1395 6 is_stmt 0 view .LVU1046
 3571 000c 48B1     		cbz	r0, .L270
 3572              		.loc 1 1395 24 discriminator 1 view .LVU1047
 3573 000e 4189     		ldrh	r1, [r0, #10]
 3574              		.loc 1 1395 30 discriminator 1 view .LVU1048
 3575 0010 BDF80620 		ldrh	r2, [sp, #6]
 3576              		.loc 1 1395 19 discriminator 1 view .LVU1049
 3577 0014 9142     		cmp	r1, r2
 3578 0016 07D9     		bls	.L271
1396:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     return ((u8_t *)q->payload)[q_idx];
 3579              		.loc 1 1396 5 is_stmt 1 view .LVU1050
 3580              		.loc 1 1396 22 is_stmt 0 view .LVU1051
 3581 0018 4368     		ldr	r3, [r0, #4]
 3582              		.loc 1 1396 32 view .LVU1052
 3583 001a 985C     		ldrb	r0, [r3, r2]	@ zero_extendqisi2
 3584              	.LVL375:
 3585              	.L268:
1397:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
1398:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   return -1;
1399:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** }
 3586              		.loc 1 1399 1 view .LVU1053
 3587 001c 03B0     		add	sp, sp, #12
 3588              	.LCFI48:
 3589              		.cfi_remember_state
 3590              		.cfi_def_cfa_offset 4
 3591              		@ sp needed
 3592 001e 5DF804FB 		ldr	pc, [sp], #4
 3593              	.LVL376:
 3594              	.L270:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 106


 3595              	.LCFI49:
 3596              		.cfi_restore_state
1398:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** }
 3597              		.loc 1 1398 10 view .LVU1054
 3598 0022 4FF0FF30 		mov	r0, #-1
 3599              	.LVL377:
1398:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** }
 3600              		.loc 1 1398 10 view .LVU1055
 3601 0026 F9E7     		b	.L268
 3602              	.LVL378:
 3603              	.L271:
1398:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** }
 3604              		.loc 1 1398 10 view .LVU1056
 3605 0028 4FF0FF30 		mov	r0, #-1
 3606              	.LVL379:
1398:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** }
 3607              		.loc 1 1398 10 view .LVU1057
 3608 002c F6E7     		b	.L268
 3609              		.cfi_endproc
 3610              	.LFE206:
 3612              		.section	.text.pbuf_get_at,"ax",%progbits
 3613              		.align	1
 3614              		.global	pbuf_get_at
 3615              		.syntax unified
 3616              		.thumb
 3617              		.thumb_func
 3619              	pbuf_get_at:
 3620              	.LVL380:
 3621              	.LFB205:
1372:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   int ret = pbuf_try_get_at(p, offset);
 3622              		.loc 1 1372 1 is_stmt 1 view -0
 3623              		.cfi_startproc
 3624              		@ args = 0, pretend = 0, frame = 0
 3625              		@ frame_needed = 0, uses_anonymous_args = 0
1372:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   int ret = pbuf_try_get_at(p, offset);
 3626              		.loc 1 1372 1 is_stmt 0 view .LVU1059
 3627 0000 08B5     		push	{r3, lr}
 3628              	.LCFI50:
 3629              		.cfi_def_cfa_offset 8
 3630              		.cfi_offset 3, -8
 3631              		.cfi_offset 14, -4
1373:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   if (ret >= 0) {
 3632              		.loc 1 1373 3 is_stmt 1 view .LVU1060
1373:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   if (ret >= 0) {
 3633              		.loc 1 1373 13 is_stmt 0 view .LVU1061
 3634 0002 FFF7FEFF 		bl	pbuf_try_get_at
 3635              	.LVL381:
1374:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     return (u8_t)ret;
 3636              		.loc 1 1374 3 is_stmt 1 view .LVU1062
1374:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     return (u8_t)ret;
 3637              		.loc 1 1374 6 is_stmt 0 view .LVU1063
 3638 0006 0028     		cmp	r0, #0
1374:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     return (u8_t)ret;
 3639              		.loc 1 1374 6 view .LVU1064
 3640 0008 01DA     		bge	.L277
1377:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** }
 3641              		.loc 1 1377 10 view .LVU1065
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 107


 3642 000a 0020     		movs	r0, #0
 3643              	.LVL382:
 3644              	.L274:
1378:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
 3645              		.loc 1 1378 1 view .LVU1066
 3646 000c 08BD     		pop	{r3, pc}
 3647              	.LVL383:
 3648              	.L277:
1375:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
 3649              		.loc 1 1375 5 is_stmt 1 view .LVU1067
1375:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
 3650              		.loc 1 1375 12 is_stmt 0 view .LVU1068
 3651 000e C0B2     		uxtb	r0, r0
 3652              	.LVL384:
1375:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
 3653              		.loc 1 1375 12 view .LVU1069
 3654 0010 FCE7     		b	.L274
 3655              		.cfi_endproc
 3656              	.LFE205:
 3658              		.section	.text.pbuf_put_at,"ax",%progbits
 3659              		.align	1
 3660              		.global	pbuf_put_at
 3661              		.syntax unified
 3662              		.thumb
 3663              		.thumb_func
 3665              	pbuf_put_at:
 3666              	.LVL385:
 3667              	.LFB207:
1400:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
1401:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** /**
1402:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @ingroup pbuf
1403:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * Put one byte to the specified position in a pbuf
1404:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * WARNING: silently ignores offset >= p->tot_len
1405:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
1406:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param p pbuf to fill
1407:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param offset offset into p of the byte to write
1408:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param data byte to write at an offset into p
1409:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  */
1410:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** void
1411:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** pbuf_put_at(struct pbuf *p, u16_t offset, u8_t data)
1412:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** {
 3668              		.loc 1 1412 1 is_stmt 1 view -0
 3669              		.cfi_startproc
 3670              		@ args = 0, pretend = 0, frame = 8
 3671              		@ frame_needed = 0, uses_anonymous_args = 0
 3672              		.loc 1 1412 1 is_stmt 0 view .LVU1071
 3673 0000 10B5     		push	{r4, lr}
 3674              	.LCFI51:
 3675              		.cfi_def_cfa_offset 8
 3676              		.cfi_offset 4, -8
 3677              		.cfi_offset 14, -4
 3678 0002 82B0     		sub	sp, sp, #8
 3679              	.LCFI52:
 3680              		.cfi_def_cfa_offset 16
 3681 0004 1446     		mov	r4, r2
1413:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   u16_t q_idx;
 3682              		.loc 1 1413 3 is_stmt 1 view .LVU1072
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 108


1414:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   struct pbuf *q = pbuf_skip(p, offset, &q_idx);
 3683              		.loc 1 1414 3 view .LVU1073
 3684              		.loc 1 1414 20 is_stmt 0 view .LVU1074
 3685 0006 0DF10602 		add	r2, sp, #6
 3686              	.LVL386:
 3687              		.loc 1 1414 20 view .LVU1075
 3688 000a FFF7FEFF 		bl	pbuf_skip
 3689              	.LVL387:
1415:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
1416:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* write requested data if pbuf is OK */
1417:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   if ((q != NULL) && (q->len > q_idx)) {
 3690              		.loc 1 1417 3 is_stmt 1 view .LVU1076
 3691              		.loc 1 1417 6 is_stmt 0 view .LVU1077
 3692 000e 30B1     		cbz	r0, .L278
 3693              		.loc 1 1417 24 discriminator 1 view .LVU1078
 3694 0010 4189     		ldrh	r1, [r0, #10]
 3695              		.loc 1 1417 30 discriminator 1 view .LVU1079
 3696 0012 BDF80620 		ldrh	r2, [sp, #6]
 3697              		.loc 1 1417 19 discriminator 1 view .LVU1080
 3698 0016 9142     		cmp	r1, r2
 3699 0018 01D9     		bls	.L278
1418:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     ((u8_t *)q->payload)[q_idx] = data;
 3700              		.loc 1 1418 5 is_stmt 1 view .LVU1081
 3701              		.loc 1 1418 15 is_stmt 0 view .LVU1082
 3702 001a 4368     		ldr	r3, [r0, #4]
 3703              		.loc 1 1418 33 view .LVU1083
 3704 001c 9C54     		strb	r4, [r3, r2]
 3705              	.L278:
1419:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
1420:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** }
 3706              		.loc 1 1420 1 view .LVU1084
 3707 001e 02B0     		add	sp, sp, #8
 3708              	.LCFI53:
 3709              		.cfi_def_cfa_offset 8
 3710              		@ sp needed
 3711 0020 10BD     		pop	{r4, pc}
 3712              		.loc 1 1420 1 view .LVU1085
 3713              		.cfi_endproc
 3714              	.LFE207:
 3716              		.section	.text.pbuf_memcmp,"ax",%progbits
 3717              		.align	1
 3718              		.global	pbuf_memcmp
 3719              		.syntax unified
 3720              		.thumb
 3721              		.thumb_func
 3723              	pbuf_memcmp:
 3724              	.LVL388:
 3725              	.LFB208:
1421:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
1422:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** /**
1423:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @ingroup pbuf
1424:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * Compare pbuf contents at specified offset with memory s2, both of length n
1425:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
1426:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param p pbuf to compare
1427:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param offset offset into p at which to start comparing
1428:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param s2 buffer to compare
1429:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param n length of buffer to compare
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 109


1430:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @return zero if equal, nonzero otherwise
1431:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *         (0xffff if p is too short, diffoffset+1 otherwise)
1432:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  */
1433:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** u16_t
1434:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** pbuf_memcmp(const struct pbuf *p, u16_t offset, const void *s2, u16_t n)
1435:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** {
 3726              		.loc 1 1435 1 is_stmt 1 view -0
 3727              		.cfi_startproc
 3728              		@ args = 0, pretend = 0, frame = 0
 3729              		@ frame_needed = 0, uses_anonymous_args = 0
 3730              		.loc 1 1435 1 is_stmt 0 view .LVU1087
 3731 0000 2DE9F041 		push	{r4, r5, r6, r7, r8, lr}
 3732              	.LCFI54:
 3733              		.cfi_def_cfa_offset 24
 3734              		.cfi_offset 4, -24
 3735              		.cfi_offset 5, -20
 3736              		.cfi_offset 6, -16
 3737              		.cfi_offset 7, -12
 3738              		.cfi_offset 8, -8
 3739              		.cfi_offset 14, -4
 3740 0004 0546     		mov	r5, r0
 3741 0006 0E46     		mov	r6, r1
 3742 0008 9046     		mov	r8, r2
 3743 000a 1F46     		mov	r7, r3
1436:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   u16_t start = offset;
 3744              		.loc 1 1436 3 is_stmt 1 view .LVU1088
 3745              	.LVL389:
1437:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   const struct pbuf *q = p;
 3746              		.loc 1 1437 3 view .LVU1089
1438:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   u16_t i;
 3747              		.loc 1 1438 3 view .LVU1090
1439:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
1440:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* pbuf long enough to perform check? */
1441:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   if (p->tot_len < (offset + n)) {
 3748              		.loc 1 1441 3 view .LVU1091
 3749              		.loc 1 1441 8 is_stmt 0 view .LVU1092
 3750 000c 0289     		ldrh	r2, [r0, #8]
 3751              	.LVL390:
 3752              		.loc 1 1441 28 view .LVU1093
 3753 000e CB18     		adds	r3, r1, r3
 3754              	.LVL391:
 3755              		.loc 1 1441 6 view .LVU1094
 3756 0010 9A42     		cmp	r2, r3
 3757 0012 05DA     		bge	.L283
1442:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     return 0xffff;
 3758              		.loc 1 1442 12 view .LVU1095
 3759 0014 4FF6FF70 		movw	r0, #65535
 3760              	.LVL392:
 3761              		.loc 1 1442 12 view .LVU1096
 3762 0018 20E0     		b	.L282
 3763              	.LVL393:
 3764              	.L285:
1443:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
1444:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
1445:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* get the correct pbuf from chain. We know it succeeds because of p->tot_len check above. */
1446:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   while ((q != NULL) && (q->len <= start)) {
1447:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     start = (u16_t)(start - q->len);
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 110


 3765              		.loc 1 1447 5 is_stmt 1 view .LVU1097
 3766              		.loc 1 1447 11 is_stmt 0 view .LVU1098
 3767 001a 301A     		subs	r0, r6, r0
 3768 001c 86B2     		uxth	r6, r0
 3769              	.LVL394:
1448:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     q = q->next;
 3770              		.loc 1 1448 5 is_stmt 1 view .LVU1099
 3771              		.loc 1 1448 7 is_stmt 0 view .LVU1100
 3772 001e 2D68     		ldr	r5, [r5]
 3773              	.LVL395:
 3774              	.L283:
1446:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     start = (u16_t)(start - q->len);
 3775              		.loc 1 1446 22 is_stmt 1 view .LVU1101
 3776 0020 65B1     		cbz	r5, .L290
1446:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     start = (u16_t)(start - q->len);
 3777              		.loc 1 1446 27 is_stmt 0 discriminator 1 view .LVU1102
 3778 0022 6889     		ldrh	r0, [r5, #10]
1446:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     start = (u16_t)(start - q->len);
 3779              		.loc 1 1446 22 discriminator 1 view .LVU1103
 3780 0024 B042     		cmp	r0, r6
 3781 0026 F8D9     		bls	.L285
 3782 0028 0024     		movs	r4, #0
 3783 002a 08E0     		b	.L287
 3784              	.LVL396:
 3785              	.L293:
 3786              	.LBB17:
1449:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
1450:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
1451:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   /* return requested data if pbuf is OK */
1452:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   for (i = 0; i < n; i++) {
1453:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* We know pbuf_get_at() succeeds because of p->tot_len check above. */
1454:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     u8_t a = pbuf_get_at(q, (u16_t)(start + i));
1455:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     u8_t b = ((const u8_t *)s2)[i];
1456:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     if (a != b) {
1457:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       return (u16_t)LWIP_MIN(i + 1, 0xFFFF);
 3787              		.loc 1 1457 7 is_stmt 1 view .LVU1104
 3788              		.loc 1 1457 14 is_stmt 0 view .LVU1105
 3789 002c 4FF6FE70 		movw	r0, #65534
 3790              	.LVL397:
 3791              		.loc 1 1457 14 view .LVU1106
 3792 0030 A042     		cmp	r0, r4
 3793 0032 28BF     		it	cs
 3794 0034 2046     		movcs	r0, r4
 3795 0036 0130     		adds	r0, r0, #1
 3796 0038 80B2     		uxth	r0, r0
 3797 003a 0FE0     		b	.L282
 3798              	.LVL398:
 3799              	.L290:
 3800              		.loc 1 1457 14 view .LVU1107
 3801              	.LBE17:
 3802 003c 0024     		movs	r4, #0
 3803              	.LVL399:
 3804              	.L287:
1452:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* We know pbuf_get_at() succeeds because of p->tot_len check above. */
 3805              		.loc 1 1452 17 is_stmt 1 discriminator 1 view .LVU1108
 3806 003e BC42     		cmp	r4, r7
 3807 0040 0BD2     		bcs	.L292
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 111


 3808              	.LBB18:
1454:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     u8_t b = ((const u8_t *)s2)[i];
 3809              		.loc 1 1454 5 view .LVU1109
1454:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     u8_t b = ((const u8_t *)s2)[i];
 3810              		.loc 1 1454 14 is_stmt 0 view .LVU1110
 3811 0042 3119     		adds	r1, r6, r4
 3812 0044 89B2     		uxth	r1, r1
 3813 0046 2846     		mov	r0, r5
 3814 0048 FFF7FEFF 		bl	pbuf_get_at
 3815              	.LVL400:
1455:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     if (a != b) {
 3816              		.loc 1 1455 5 is_stmt 1 view .LVU1111
1455:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     if (a != b) {
 3817              		.loc 1 1455 10 is_stmt 0 view .LVU1112
 3818 004c 18F80410 		ldrb	r1, [r8, r4]	@ zero_extendqisi2
 3819              	.LVL401:
1456:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       return (u16_t)LWIP_MIN(i + 1, 0xFFFF);
 3820              		.loc 1 1456 5 is_stmt 1 view .LVU1113
1456:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       return (u16_t)LWIP_MIN(i + 1, 0xFFFF);
 3821              		.loc 1 1456 8 is_stmt 0 view .LVU1114
 3822 0050 8842     		cmp	r0, r1
 3823 0052 EBD1     		bne	.L293
 3824              	.LBE18:
1452:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* We know pbuf_get_at() succeeds because of p->tot_len check above. */
 3825              		.loc 1 1452 23 is_stmt 1 discriminator 2 view .LVU1115
 3826 0054 0134     		adds	r4, r4, #1
 3827              	.LVL402:
1452:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* We know pbuf_get_at() succeeds because of p->tot_len check above. */
 3828              		.loc 1 1452 23 is_stmt 0 discriminator 2 view .LVU1116
 3829 0056 A4B2     		uxth	r4, r4
 3830              	.LVL403:
1452:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     /* We know pbuf_get_at() succeeds because of p->tot_len check above. */
 3831              		.loc 1 1452 23 discriminator 2 view .LVU1117
 3832 0058 F1E7     		b	.L287
 3833              	.LVL404:
 3834              	.L292:
1458:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     }
1459:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
1460:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   return 0;
 3835              		.loc 1 1460 10 view .LVU1118
 3836 005a 0020     		movs	r0, #0
 3837              	.LVL405:
 3838              	.L282:
1461:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** }
 3839              		.loc 1 1461 1 view .LVU1119
 3840 005c BDE8F081 		pop	{r4, r5, r6, r7, r8, pc}
 3841              		.loc 1 1461 1 view .LVU1120
 3842              		.cfi_endproc
 3843              	.LFE208:
 3845              		.section	.text.pbuf_memfind,"ax",%progbits
 3846              		.align	1
 3847              		.global	pbuf_memfind
 3848              		.syntax unified
 3849              		.thumb
 3850              		.thumb_func
 3852              	pbuf_memfind:
 3853              	.LVL406:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 112


 3854              	.LFB209:
1462:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
1463:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** /**
1464:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @ingroup pbuf
1465:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * Find occurrence of mem (with length mem_len) in pbuf p, starting at offset
1466:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * start_offset.
1467:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
1468:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param p pbuf to search, maximum length is 0xFFFE since 0xFFFF is used as
1469:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *        return value 'not found'
1470:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param mem search for the contents of this buffer
1471:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param mem_len length of 'mem'
1472:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param start_offset offset into p at which to start searching
1473:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @return 0xFFFF if substr was not found in p or the index where it was found
1474:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  */
1475:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** u16_t
1476:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** pbuf_memfind(const struct pbuf *p, const void *mem, u16_t mem_len, u16_t start_offset)
1477:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** {
 3855              		.loc 1 1477 1 is_stmt 1 view -0
 3856              		.cfi_startproc
 3857              		@ args = 0, pretend = 0, frame = 0
 3858              		@ frame_needed = 0, uses_anonymous_args = 0
 3859              		.loc 1 1477 1 is_stmt 0 view .LVU1122
 3860 0000 2DE9F041 		push	{r4, r5, r6, r7, r8, lr}
 3861              	.LCFI55:
 3862              		.cfi_def_cfa_offset 24
 3863              		.cfi_offset 4, -24
 3864              		.cfi_offset 5, -20
 3865              		.cfi_offset 6, -16
 3866              		.cfi_offset 7, -12
 3867              		.cfi_offset 8, -8
 3868              		.cfi_offset 14, -4
 3869 0004 0646     		mov	r6, r0
 3870 0006 8846     		mov	r8, r1
 3871 0008 1546     		mov	r5, r2
 3872 000a 1C46     		mov	r4, r3
1478:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   u16_t i;
 3873              		.loc 1 1478 3 is_stmt 1 view .LVU1123
1479:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   u16_t max_cmp_start = (u16_t)(p->tot_len - mem_len);
 3874              		.loc 1 1479 3 view .LVU1124
 3875              		.loc 1 1479 34 is_stmt 0 view .LVU1125
 3876 000c 0389     		ldrh	r3, [r0, #8]
 3877              	.LVL407:
 3878              		.loc 1 1479 9 view .LVU1126
 3879 000e 9F1A     		subs	r7, r3, r2
 3880 0010 BFB2     		uxth	r7, r7
 3881              	.LVL408:
1480:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   if (p->tot_len >= mem_len + start_offset) {
 3882              		.loc 1 1480 3 is_stmt 1 view .LVU1127
 3883              		.loc 1 1480 29 is_stmt 0 view .LVU1128
 3884 0012 2244     		add	r2, r2, r4
 3885              	.LVL409:
 3886              		.loc 1 1480 6 view .LVU1129
 3887 0014 9342     		cmp	r3, r2
 3888 0016 0BDB     		blt	.L300
 3889              	.LVL410:
 3890              	.L295:
1481:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     for (i = start_offset; i <= max_cmp_start; i++) {
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 113


 3891              		.loc 1 1481 30 is_stmt 1 discriminator 1 view .LVU1130
 3892 0018 BC42     		cmp	r4, r7
 3893 001a 0DD8     		bhi	.L301
 3894              	.LBB19:
1482:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       u16_t plus = pbuf_memcmp(p, i, mem, mem_len);
 3895              		.loc 1 1482 7 view .LVU1131
 3896              		.loc 1 1482 20 is_stmt 0 view .LVU1132
 3897 001c 2B46     		mov	r3, r5
 3898 001e 4246     		mov	r2, r8
 3899 0020 2146     		mov	r1, r4
 3900 0022 3046     		mov	r0, r6
 3901 0024 FFF7FEFF 		bl	pbuf_memcmp
 3902              	.LVL411:
1483:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       if (plus == 0) {
 3903              		.loc 1 1483 7 is_stmt 1 view .LVU1133
 3904              		.loc 1 1483 10 is_stmt 0 view .LVU1134
 3905 0028 48B1     		cbz	r0, .L298
 3906              	.LBE19:
1481:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     for (i = start_offset; i <= max_cmp_start; i++) {
 3907              		.loc 1 1481 49 is_stmt 1 discriminator 2 view .LVU1135
 3908 002a 0134     		adds	r4, r4, #1
 3909              	.LVL412:
1481:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     for (i = start_offset; i <= max_cmp_start; i++) {
 3910              		.loc 1 1481 49 is_stmt 0 discriminator 2 view .LVU1136
 3911 002c A4B2     		uxth	r4, r4
 3912              	.LVL413:
1481:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     for (i = start_offset; i <= max_cmp_start; i++) {
 3913              		.loc 1 1481 49 discriminator 2 view .LVU1137
 3914 002e F3E7     		b	.L295
 3915              	.LVL414:
 3916              	.L300:
1484:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         return i;
1485:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****       }
1486:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     }
1487:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
1488:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   return 0xFFFF;
 3917              		.loc 1 1488 10 view .LVU1138
 3918 0030 4FF6FF70 		movw	r0, #65535
 3919              	.LVL415:
 3920              	.L296:
1489:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** }
 3921              		.loc 1 1489 1 view .LVU1139
 3922 0034 BDE8F081 		pop	{r4, r5, r6, r7, r8, pc}
 3923              	.LVL416:
 3924              	.L301:
1488:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** }
 3925              		.loc 1 1488 10 view .LVU1140
 3926 0038 4FF6FF70 		movw	r0, #65535
 3927 003c FAE7     		b	.L296
 3928              	.LVL417:
 3929              	.L298:
 3930              	.LBB20:
1484:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         return i;
 3931              		.loc 1 1484 16 view .LVU1141
 3932 003e 2046     		mov	r0, r4
 3933              	.LVL418:
1484:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****         return i;
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 114


 3934              		.loc 1 1484 16 view .LVU1142
 3935 0040 F8E7     		b	.L296
 3936              	.LBE20:
 3937              		.cfi_endproc
 3938              	.LFE209:
 3940              		.section	.text.pbuf_strstr,"ax",%progbits
 3941              		.align	1
 3942              		.global	pbuf_strstr
 3943              		.syntax unified
 3944              		.thumb
 3945              		.thumb_func
 3947              	pbuf_strstr:
 3948              	.LVL419:
 3949              	.LFB210:
1490:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** 
1491:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** /**
1492:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * Find occurrence of substr with length substr_len in pbuf p, start at offset
1493:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * start_offset
1494:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * WARNING: in contrast to strstr(), this one does not stop at the first \0 in
1495:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * the pbuf/source string!
1496:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *
1497:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param p pbuf to search, maximum length is 0xFFFE since 0xFFFF is used as
1498:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  *        return value 'not found'
1499:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @param substr string to search for in p, maximum length is 0xFFFE
1500:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  * @return 0xFFFF if substr was not found in p or the index where it was found
1501:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****  */
1502:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** u16_t
1503:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** pbuf_strstr(const struct pbuf *p, const char *substr)
1504:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** {
 3950              		.loc 1 1504 1 is_stmt 1 view -0
 3951              		.cfi_startproc
 3952              		@ args = 0, pretend = 0, frame = 0
 3953              		@ frame_needed = 0, uses_anonymous_args = 0
1505:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   size_t substr_len;
 3954              		.loc 1 1505 3 view .LVU1144
1506:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   if ((substr == NULL) || (substr[0] == 0) || (p->tot_len == 0xFFFF)) {
 3955              		.loc 1 1506 3 view .LVU1145
 3956              		.loc 1 1506 6 is_stmt 0 view .LVU1146
 3957 0000 B9B1     		cbz	r1, .L304
1504:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   size_t substr_len;
 3958              		.loc 1 1504 1 view .LVU1147
 3959 0002 38B5     		push	{r3, r4, r5, lr}
 3960              	.LCFI56:
 3961              		.cfi_def_cfa_offset 16
 3962              		.cfi_offset 3, -16
 3963              		.cfi_offset 4, -12
 3964              		.cfi_offset 5, -8
 3965              		.cfi_offset 14, -4
 3966 0004 0446     		mov	r4, r0
 3967 0006 0D46     		mov	r5, r1
 3968              		.loc 1 1506 34 discriminator 1 view .LVU1148
 3969 0008 0B78     		ldrb	r3, [r1]	@ zero_extendqisi2
 3970              		.loc 1 1506 24 discriminator 1 view .LVU1149
 3971 000a ABB1     		cbz	r3, .L305
 3972              		.loc 1 1506 49 discriminator 2 view .LVU1150
 3973 000c 0089     		ldrh	r0, [r0, #8]
 3974              	.LVL420:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 115


 3975              		.loc 1 1506 44 discriminator 2 view .LVU1151
 3976 000e 4FF6FF73 		movw	r3, #65535
 3977 0012 9842     		cmp	r0, r3
 3978 0014 0CD0     		beq	.L303
1507:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     return 0xFFFF;
1508:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
1509:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   substr_len = strlen(substr);
 3979              		.loc 1 1509 3 is_stmt 1 view .LVU1152
 3980              		.loc 1 1509 16 is_stmt 0 view .LVU1153
 3981 0016 0846     		mov	r0, r1
 3982 0018 FFF7FEFF 		bl	strlen
 3983              	.LVL421:
1510:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   if (substr_len >= 0xFFFF) {
 3984              		.loc 1 1510 3 is_stmt 1 view .LVU1154
 3985              		.loc 1 1510 6 is_stmt 0 view .LVU1155
 3986 001c 4FF6FE73 		movw	r3, #65534
 3987 0020 9842     		cmp	r0, r3
 3988 0022 0CD8     		bhi	.L306
1511:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     return 0xFFFF;
1512:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
1513:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   return pbuf_memfind(p, substr, (u16_t)substr_len, 0);
 3989              		.loc 1 1513 3 is_stmt 1 view .LVU1156
 3990              		.loc 1 1513 10 is_stmt 0 view .LVU1157
 3991 0024 0023     		movs	r3, #0
 3992 0026 82B2     		uxth	r2, r0
 3993 0028 2946     		mov	r1, r5
 3994 002a 2046     		mov	r0, r4
 3995              	.LVL422:
 3996              		.loc 1 1513 10 view .LVU1158
 3997 002c FFF7FEFF 		bl	pbuf_memfind
 3998              	.LVL423:
 3999              	.L303:
1514:Middlewares/Third_Party/LwIP/src/core/pbuf.c **** }
 4000              		.loc 1 1514 1 view .LVU1159
 4001 0030 38BD     		pop	{r3, r4, r5, pc}
 4002              	.LVL424:
 4003              	.L304:
 4004              	.LCFI57:
 4005              		.cfi_def_cfa_offset 0
 4006              		.cfi_restore 3
 4007              		.cfi_restore 4
 4008              		.cfi_restore 5
 4009              		.cfi_restore 14
1507:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
 4010              		.loc 1 1507 12 view .LVU1160
 4011 0032 4FF6FF70 		movw	r0, #65535
 4012              	.LVL425:
 4013              		.loc 1 1514 1 view .LVU1161
 4014 0036 7047     		bx	lr
 4015              	.LVL426:
 4016              	.L305:
 4017              	.LCFI58:
 4018              		.cfi_def_cfa_offset 16
 4019              		.cfi_offset 3, -16
 4020              		.cfi_offset 4, -12
 4021              		.cfi_offset 5, -8
 4022              		.cfi_offset 14, -4
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 116


1507:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
 4023              		.loc 1 1507 12 view .LVU1162
 4024 0038 4FF6FF70 		movw	r0, #65535
 4025              	.LVL427:
1507:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****   }
 4026              		.loc 1 1507 12 view .LVU1163
 4027 003c F8E7     		b	.L303
 4028              	.LVL428:
 4029              	.L306:
1511:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     return 0xFFFF;
 4030              		.loc 1 1511 12 view .LVU1164
 4031 003e 4FF6FF70 		movw	r0, #65535
 4032              	.LVL429:
1511:Middlewares/Third_Party/LwIP/src/core/pbuf.c ****     return 0xFFFF;
 4033              		.loc 1 1511 12 view .LVU1165
 4034 0042 F5E7     		b	.L303
 4035              		.cfi_endproc
 4036              	.LFE210:
 4038              		.global	pbuf_free_ooseq_pending
 4039              		.section	.bss.pbuf_free_ooseq_pending,"aw",%nobits
 4042              	pbuf_free_ooseq_pending:
 4043 0000 00       		.space	1
 4044              		.text
 4045              	.Letext0:
 4046              		.file 2 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\../lib/gcc/arm-none-eabi/13.3.1/..
 4047              		.file 3 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\../lib/gcc/arm-none-eabi/13.3.1/..
 4048              		.file 4 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\../lib/gcc/arm-none-eabi/13.3.1/in
 4049              		.file 5 "Middlewares/Third_Party/LwIP/system/arch/cc.h"
 4050              		.file 6 "Middlewares/Third_Party/LwIP/src/include/lwip/arch.h"
 4051              		.file 7 "Middlewares/Third_Party/LwIP/src/include/lwip/err.h"
 4052              		.file 8 "Middlewares/Third_Party/LwIP/src/include/lwip/pbuf.h"
 4053              		.file 9 "Middlewares/Third_Party/LwIP/src/include/lwip/mem.h"
 4054              		.file 10 "Middlewares/Third_Party/LwIP/src/include/lwip/memp.h"
 4055              		.file 11 "Middlewares/Third_Party/LwIP/src/include/lwip/ip4_addr.h"
 4056              		.file 12 "Middlewares/Third_Party/LwIP/src/include/lwip/ip_addr.h"
 4057              		.file 13 "Middlewares/Third_Party/LwIP/src/include/lwip/tcpbase.h"
 4058              		.file 14 "Middlewares/Third_Party/LwIP/src/include/lwip/tcp.h"
 4059              		.file 15 "Middlewares/Third_Party/LwIP/src/include/lwip/priv/tcp_priv.h"
 4060              		.file 16 "Middlewares/Third_Party/LwIP/src/include/lwip/prot/tcp.h"
 4061              		.file 17 "Middlewares/Third_Party/LwIP/src/include/lwip/tcpip.h"
 4062              		.file 18 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\../lib/gcc/arm-none-eabi/13.3.1/.
 4063              		.file 19 "Middlewares/Third_Party/LwIP/src/include/lwip/sys.h"
 4064              		.file 20 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\../lib/gcc/arm-none-eabi/13.3.1/.
 4065              		.file 21 "<built-in>"
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 117


DEFINED SYMBOLS
                            *ABS*:00000000 pbuf.c
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:20     .text.pbuf_init_alloced_pbuf:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:25     .text.pbuf_init_alloced_pbuf:00000000 pbuf_init_alloced_pbuf
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:80     .text.pbuf_skip_const:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:85     .text.pbuf_skip_const:00000000 pbuf_skip_const
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:134    .rodata.pbuf_add_header_impl.str1.4:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:144    .text.pbuf_add_header_impl:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:149    .text.pbuf_add_header_impl:00000000 pbuf_add_header_impl
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:295    .text.pbuf_add_header_impl:00000070 $d
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:302    .text.pbuf_pool_is_empty:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:307    .text.pbuf_pool_is_empty:00000000 pbuf_pool_is_empty
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:370    .text.pbuf_pool_is_empty:00000034 $d
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:4042   .bss.pbuf_free_ooseq_pending:00000000 pbuf_free_ooseq_pending
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:451    .text.pbuf_free_ooseq_callback:00000000 pbuf_free_ooseq_callback
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:376    .text.pbuf_free_ooseq:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:381    .text.pbuf_free_ooseq:00000000 pbuf_free_ooseq
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:440    .text.pbuf_free_ooseq:00000028 $d
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:446    .text.pbuf_free_ooseq_callback:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:474    .rodata.pbuf_alloc_reference.str1.4:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:478    .text.pbuf_alloc_reference:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:484    .text.pbuf_alloc_reference:00000000 pbuf_alloc_reference
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:567    .text.pbuf_alloc_reference:00000044 $d
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:574    .text.pbuf_alloced_custom:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:580    .text.pbuf_alloced_custom:00000000 pbuf_alloced_custom
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:661    .text.pbuf_add_header:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:667    .text.pbuf_add_header:00000000 pbuf_add_header
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:691    .text.pbuf_add_header_force:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:697    .text.pbuf_add_header_force:00000000 pbuf_add_header_force
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:721    .rodata.pbuf_remove_header.str1.4:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:725    .text.pbuf_remove_header:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:731    .text.pbuf_remove_header:00000000 pbuf_remove_header
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:852    .text.pbuf_remove_header:0000005c $d
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:860    .text.pbuf_header_impl:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:865    .text.pbuf_header_impl:00000000 pbuf_header_impl
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:903    .text.pbuf_header:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:909    .text.pbuf_header:00000000 pbuf_header
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:933    .text.pbuf_header_force:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:939    .text.pbuf_header_force:00000000 pbuf_header_force
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:963    .rodata.pbuf_free.str1.4:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:973    .text.pbuf_free:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:979    .text.pbuf_free:00000000 pbuf_free
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:1207   .text.pbuf_free:000000b8 $d
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:1218   .rodata.pbuf_alloc.str1.4:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:1232   .text.pbuf_alloc:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:1238   .text.pbuf_alloc:00000000 pbuf_alloc
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:1549   .text.pbuf_alloc:00000144 $d
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:1559   .rodata.pbuf_realloc.str1.4:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:1569   .text.pbuf_realloc:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:1575   .text.pbuf_realloc:00000000 pbuf_realloc
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:1741   .text.pbuf_realloc:00000094 $d
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:1750   .text.pbuf_free_header:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:1756   .text.pbuf_free_header:00000000 pbuf_free_header
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:1845   .text.pbuf_clen:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:1851   .text.pbuf_clen:00000000 pbuf_clen
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:1893   .rodata.pbuf_ref.str1.4:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:1897   .text.pbuf_ref:00000000 $t
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 118


C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:1903   .text.pbuf_ref:00000000 pbuf_ref
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:1968   .text.pbuf_ref:0000002c $d
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:1975   .rodata.pbuf_cat.str1.4:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:1986   .text.pbuf_cat:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:1992   .text.pbuf_cat:00000000 pbuf_cat
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:2112   .text.pbuf_cat:00000064 $d
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:2121   .text.pbuf_chain:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:2127   .text.pbuf_chain:00000000 pbuf_chain
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:2156   .rodata.pbuf_dechain.str1.4:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:2163   .text.pbuf_dechain:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:2169   .text.pbuf_dechain:00000000 pbuf_dechain
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:2291   .text.pbuf_dechain:00000064 $d
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:2299   .rodata.pbuf_copy.str1.4:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:2315   .text.pbuf_copy:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:2321   .text.pbuf_copy:00000000 pbuf_copy
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:2586   .text.pbuf_copy:0000010c $d
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:2597   .rodata.pbuf_copy_partial.str1.4:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:2604   .text.pbuf_copy_partial:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:2610   .text.pbuf_copy_partial:00000000 pbuf_copy_partial
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:2784   .text.pbuf_copy_partial:0000008c $d
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:2792   .rodata.pbuf_get_contiguous.str1.4:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:2799   .text.pbuf_get_contiguous:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:2805   .text.pbuf_get_contiguous:00000000 pbuf_get_contiguous
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:2973   .text.pbuf_get_contiguous:00000080 $d
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:2981   .text.pbuf_skip:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:2987   .text.pbuf_skip:00000000 pbuf_skip
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:3011   .rodata.pbuf_take.str1.4:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:3027   .text.pbuf_take:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:3033   .text.pbuf_take:00000000 pbuf_take
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:3225   .text.pbuf_take:000000a4 $d
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:3236   .rodata.pbuf_take_at.str1.4:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:3240   .text.pbuf_take_at:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:3246   .text.pbuf_take_at:00000000 pbuf_take_at
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:3396   .text.pbuf_take_at:00000078 $d
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:3403   .rodata.pbuf_clone.str1.4:00000000 $d
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:3407   .text.pbuf_clone:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:3413   .text.pbuf_clone:00000000 pbuf_clone
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:3475   .text.pbuf_clone:0000002c $d
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:3482   .text.pbuf_coalesce:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:3488   .text.pbuf_coalesce:00000000 pbuf_coalesce
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:3542   .text.pbuf_try_get_at:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:3548   .text.pbuf_try_get_at:00000000 pbuf_try_get_at
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:3613   .text.pbuf_get_at:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:3619   .text.pbuf_get_at:00000000 pbuf_get_at
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:3659   .text.pbuf_put_at:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:3665   .text.pbuf_put_at:00000000 pbuf_put_at
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:3717   .text.pbuf_memcmp:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:3723   .text.pbuf_memcmp:00000000 pbuf_memcmp
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:3846   .text.pbuf_memfind:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:3852   .text.pbuf_memfind:00000000 pbuf_memfind
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:3941   .text.pbuf_strstr:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:3947   .text.pbuf_strstr:00000000 pbuf_strstr
C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s:4043   .bss.pbuf_free_ooseq_pending:00000000 $d

UNDEFINED SYMBOLS
printf
sys_arch_protect
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\cchYWNXO.s 			page 119


sys_arch_unprotect
tcpip_try_callback
tcp_free_ooseq
tcp_active_pcbs
memp_malloc
mem_free
memp_free
mem_malloc
mem_trim
memcpy
strlen
