// generated from rosidl_generator_c/resource/idl.h.em
// with input from example_interfaces:msg/Float32.idl
// generated code does not contain a copyright notice

#ifndef EXAMPLE_INTERFACES__MSG__FLOAT32_H_
#define EXAMPLE_INTERFACES__MSG__FLOAT32_H_

#include "example_interfaces/msg/detail/float32__struct.h"
#include "example_interfaces/msg/detail/float32__functions.h"
#include "example_interfaces/msg/detail/float32__type_support.h"

#endif  // EXAMPLE_INTERFACES__MSG__FLOAT32_H_
