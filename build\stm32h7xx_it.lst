ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccBtrFvC.s 			page 1


   1              		.cpu cortex-m7
   2              		.arch armv7e-m
   3              		.fpu fpv5-d16
   4              		.eabi_attribute 28, 1
   5              		.eabi_attribute 20, 1
   6              		.eabi_attribute 21, 1
   7              		.eabi_attribute 23, 3
   8              		.eabi_attribute 24, 1
   9              		.eabi_attribute 25, 1
  10              		.eabi_attribute 26, 1
  11              		.eabi_attribute 30, 1
  12              		.eabi_attribute 34, 1
  13              		.eabi_attribute 18, 4
  14              		.file	"stm32h7xx_it.c"
  15              		.text
  16              	.Ltext0:
  17              		.cfi_sections	.debug_frame
  18              		.file 1 "Core/Src/stm32h7xx_it.c"
  19              		.section	.text.NMI_Handler,"ax",%progbits
  20              		.align	1
  21              		.global	NMI_Handler
  22              		.syntax unified
  23              		.thumb
  24              		.thumb_func
  26              	NMI_Handler:
  27              	.LFB144:
   1:Core/Src/stm32h7xx_it.c **** /* USER CODE BEGIN Header */
   2:Core/Src/stm32h7xx_it.c **** /**
   3:Core/Src/stm32h7xx_it.c ****   ******************************************************************************
   4:Core/Src/stm32h7xx_it.c ****   * @file    stm32h7xx_it.c
   5:Core/Src/stm32h7xx_it.c ****   * @brief   Interrupt Service Routines.
   6:Core/Src/stm32h7xx_it.c ****   ******************************************************************************
   7:Core/Src/stm32h7xx_it.c ****   * @attention
   8:Core/Src/stm32h7xx_it.c ****   *
   9:Core/Src/stm32h7xx_it.c ****   * Copyright (c) 2025 STMicroelectronics.
  10:Core/Src/stm32h7xx_it.c ****   * All rights reserved.
  11:Core/Src/stm32h7xx_it.c ****   *
  12:Core/Src/stm32h7xx_it.c ****   * This software is licensed under terms that can be found in the LICENSE file
  13:Core/Src/stm32h7xx_it.c ****   * in the root directory of this software component.
  14:Core/Src/stm32h7xx_it.c ****   * If no LICENSE file comes with this software, it is provided AS-IS.
  15:Core/Src/stm32h7xx_it.c ****   *
  16:Core/Src/stm32h7xx_it.c ****   ******************************************************************************
  17:Core/Src/stm32h7xx_it.c ****   */
  18:Core/Src/stm32h7xx_it.c **** /* USER CODE END Header */
  19:Core/Src/stm32h7xx_it.c **** 
  20:Core/Src/stm32h7xx_it.c **** /* Includes ------------------------------------------------------------------*/
  21:Core/Src/stm32h7xx_it.c **** #include "main.h"
  22:Core/Src/stm32h7xx_it.c **** #include "stm32h7xx_it.h"
  23:Core/Src/stm32h7xx_it.c **** /* Private includes ----------------------------------------------------------*/
  24:Core/Src/stm32h7xx_it.c **** /* USER CODE BEGIN Includes */
  25:Core/Src/stm32h7xx_it.c **** /* USER CODE END Includes */
  26:Core/Src/stm32h7xx_it.c **** 
  27:Core/Src/stm32h7xx_it.c **** /* Private typedef -----------------------------------------------------------*/
  28:Core/Src/stm32h7xx_it.c **** /* USER CODE BEGIN TD */
  29:Core/Src/stm32h7xx_it.c **** 
  30:Core/Src/stm32h7xx_it.c **** /* USER CODE END TD */
  31:Core/Src/stm32h7xx_it.c **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccBtrFvC.s 			page 2


  32:Core/Src/stm32h7xx_it.c **** /* Private define ------------------------------------------------------------*/
  33:Core/Src/stm32h7xx_it.c **** /* USER CODE BEGIN PD */
  34:Core/Src/stm32h7xx_it.c **** 
  35:Core/Src/stm32h7xx_it.c **** /* USER CODE END PD */
  36:Core/Src/stm32h7xx_it.c **** 
  37:Core/Src/stm32h7xx_it.c **** /* Private macro -------------------------------------------------------------*/
  38:Core/Src/stm32h7xx_it.c **** /* USER CODE BEGIN PM */
  39:Core/Src/stm32h7xx_it.c **** 
  40:Core/Src/stm32h7xx_it.c **** /* USER CODE END PM */
  41:Core/Src/stm32h7xx_it.c **** 
  42:Core/Src/stm32h7xx_it.c **** /* Private variables ---------------------------------------------------------*/
  43:Core/Src/stm32h7xx_it.c **** /* USER CODE BEGIN PV */
  44:Core/Src/stm32h7xx_it.c **** 
  45:Core/Src/stm32h7xx_it.c **** /* USER CODE END PV */
  46:Core/Src/stm32h7xx_it.c **** 
  47:Core/Src/stm32h7xx_it.c **** /* Private function prototypes -----------------------------------------------*/
  48:Core/Src/stm32h7xx_it.c **** /* USER CODE BEGIN PFP */
  49:Core/Src/stm32h7xx_it.c **** 
  50:Core/Src/stm32h7xx_it.c **** /* USER CODE END PFP */
  51:Core/Src/stm32h7xx_it.c **** 
  52:Core/Src/stm32h7xx_it.c **** /* Private user code ---------------------------------------------------------*/
  53:Core/Src/stm32h7xx_it.c **** /* USER CODE BEGIN 0 */
  54:Core/Src/stm32h7xx_it.c **** 
  55:Core/Src/stm32h7xx_it.c **** /* USER CODE END 0 */
  56:Core/Src/stm32h7xx_it.c **** 
  57:Core/Src/stm32h7xx_it.c **** /* External variables --------------------------------------------------------*/
  58:Core/Src/stm32h7xx_it.c **** extern ETH_HandleTypeDef heth;
  59:Core/Src/stm32h7xx_it.c **** extern TIM_HandleTypeDef htim1;
  60:Core/Src/stm32h7xx_it.c **** 
  61:Core/Src/stm32h7xx_it.c **** /* USER CODE BEGIN EV */
  62:Core/Src/stm32h7xx_it.c **** 
  63:Core/Src/stm32h7xx_it.c **** /* USER CODE END EV */
  64:Core/Src/stm32h7xx_it.c **** 
  65:Core/Src/stm32h7xx_it.c **** /******************************************************************************/
  66:Core/Src/stm32h7xx_it.c **** /*           Cortex Processor Interruption and Exception Handlers          */
  67:Core/Src/stm32h7xx_it.c **** /******************************************************************************/
  68:Core/Src/stm32h7xx_it.c **** /**
  69:Core/Src/stm32h7xx_it.c ****   * @brief This function handles Non maskable interrupt.
  70:Core/Src/stm32h7xx_it.c ****   */
  71:Core/Src/stm32h7xx_it.c **** void NMI_Handler(void)
  72:Core/Src/stm32h7xx_it.c **** {
  28              		.loc 1 72 1 view -0
  29              		.cfi_startproc
  30              		@ Volatile: function does not return.
  31              		@ args = 0, pretend = 0, frame = 0
  32              		@ frame_needed = 0, uses_anonymous_args = 0
  33              		@ link register save eliminated.
  34              	.L2:
  73:Core/Src/stm32h7xx_it.c ****   /* USER CODE BEGIN NonMaskableInt_IRQn 0 */
  74:Core/Src/stm32h7xx_it.c **** 
  75:Core/Src/stm32h7xx_it.c ****   /* USER CODE END NonMaskableInt_IRQn 0 */
  76:Core/Src/stm32h7xx_it.c ****   /* USER CODE BEGIN NonMaskableInt_IRQn 1 */
  77:Core/Src/stm32h7xx_it.c ****    while (1)
  35              		.loc 1 77 4 view .LVU1
  78:Core/Src/stm32h7xx_it.c ****   {
  79:Core/Src/stm32h7xx_it.c ****   }
  36              		.loc 1 79 3 view .LVU2
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccBtrFvC.s 			page 3


  77:Core/Src/stm32h7xx_it.c ****   {
  37              		.loc 1 77 10 view .LVU3
  38 0000 FEE7     		b	.L2
  39              		.cfi_endproc
  40              	.LFE144:
  42              		.section	.text.HardFault_Handler,"ax",%progbits
  43              		.align	1
  44              		.global	HardFault_Handler
  45              		.syntax unified
  46              		.thumb
  47              		.thumb_func
  49              	HardFault_Handler:
  50              	.LFB145:
  80:Core/Src/stm32h7xx_it.c ****   /* USER CODE END NonMaskableInt_IRQn 1 */
  81:Core/Src/stm32h7xx_it.c **** }
  82:Core/Src/stm32h7xx_it.c **** 
  83:Core/Src/stm32h7xx_it.c **** /**
  84:Core/Src/stm32h7xx_it.c ****   * @brief This function handles Hard fault interrupt.
  85:Core/Src/stm32h7xx_it.c ****   */
  86:Core/Src/stm32h7xx_it.c **** void HardFault_Handler(void)
  87:Core/Src/stm32h7xx_it.c **** {
  51              		.loc 1 87 1 view -0
  52              		.cfi_startproc
  53              		@ Volatile: function does not return.
  54              		@ args = 0, pretend = 0, frame = 0
  55              		@ frame_needed = 0, uses_anonymous_args = 0
  56              		@ link register save eliminated.
  57              	.L4:
  88:Core/Src/stm32h7xx_it.c ****   /* USER CODE BEGIN HardFault_IRQn 0 */
  89:Core/Src/stm32h7xx_it.c **** 
  90:Core/Src/stm32h7xx_it.c ****   /* USER CODE END HardFault_IRQn 0 */
  91:Core/Src/stm32h7xx_it.c ****   while (1)
  58              		.loc 1 91 3 view .LVU5
  92:Core/Src/stm32h7xx_it.c ****   {
  93:Core/Src/stm32h7xx_it.c ****     /* USER CODE BEGIN W1_HardFault_IRQn 0 */
  94:Core/Src/stm32h7xx_it.c ****     /* USER CODE END W1_HardFault_IRQn 0 */
  95:Core/Src/stm32h7xx_it.c ****   }
  59              		.loc 1 95 3 view .LVU6
  91:Core/Src/stm32h7xx_it.c ****   {
  60              		.loc 1 91 9 view .LVU7
  61 0000 FEE7     		b	.L4
  62              		.cfi_endproc
  63              	.LFE145:
  65              		.section	.text.MemManage_Handler,"ax",%progbits
  66              		.align	1
  67              		.global	MemManage_Handler
  68              		.syntax unified
  69              		.thumb
  70              		.thumb_func
  72              	MemManage_Handler:
  73              	.LFB146:
  96:Core/Src/stm32h7xx_it.c **** }
  97:Core/Src/stm32h7xx_it.c **** 
  98:Core/Src/stm32h7xx_it.c **** /**
  99:Core/Src/stm32h7xx_it.c ****   * @brief This function handles Memory management fault.
 100:Core/Src/stm32h7xx_it.c ****   */
 101:Core/Src/stm32h7xx_it.c **** void MemManage_Handler(void)
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccBtrFvC.s 			page 4


 102:Core/Src/stm32h7xx_it.c **** {
  74              		.loc 1 102 1 view -0
  75              		.cfi_startproc
  76              		@ Volatile: function does not return.
  77              		@ args = 0, pretend = 0, frame = 0
  78              		@ frame_needed = 0, uses_anonymous_args = 0
  79              		@ link register save eliminated.
  80              	.L6:
 103:Core/Src/stm32h7xx_it.c ****   /* USER CODE BEGIN MemoryManagement_IRQn 0 */
 104:Core/Src/stm32h7xx_it.c **** 
 105:Core/Src/stm32h7xx_it.c ****   /* USER CODE END MemoryManagement_IRQn 0 */
 106:Core/Src/stm32h7xx_it.c ****   while (1)
  81              		.loc 1 106 3 view .LVU9
 107:Core/Src/stm32h7xx_it.c ****   {
 108:Core/Src/stm32h7xx_it.c ****     /* USER CODE BEGIN W1_MemoryManagement_IRQn 0 */
 109:Core/Src/stm32h7xx_it.c ****     /* USER CODE END W1_MemoryManagement_IRQn 0 */
 110:Core/Src/stm32h7xx_it.c ****   }
  82              		.loc 1 110 3 view .LVU10
 106:Core/Src/stm32h7xx_it.c ****   {
  83              		.loc 1 106 9 view .LVU11
  84 0000 FEE7     		b	.L6
  85              		.cfi_endproc
  86              	.LFE146:
  88              		.section	.text.BusFault_Handler,"ax",%progbits
  89              		.align	1
  90              		.global	BusFault_Handler
  91              		.syntax unified
  92              		.thumb
  93              		.thumb_func
  95              	BusFault_Handler:
  96              	.LFB147:
 111:Core/Src/stm32h7xx_it.c **** }
 112:Core/Src/stm32h7xx_it.c **** 
 113:Core/Src/stm32h7xx_it.c **** /**
 114:Core/Src/stm32h7xx_it.c ****   * @brief This function handles Pre-fetch fault, memory access fault.
 115:Core/Src/stm32h7xx_it.c ****   */
 116:Core/Src/stm32h7xx_it.c **** void BusFault_Handler(void)
 117:Core/Src/stm32h7xx_it.c **** {
  97              		.loc 1 117 1 view -0
  98              		.cfi_startproc
  99              		@ Volatile: function does not return.
 100              		@ args = 0, pretend = 0, frame = 0
 101              		@ frame_needed = 0, uses_anonymous_args = 0
 102              		@ link register save eliminated.
 103              	.L8:
 118:Core/Src/stm32h7xx_it.c ****   /* USER CODE BEGIN BusFault_IRQn 0 */
 119:Core/Src/stm32h7xx_it.c **** 
 120:Core/Src/stm32h7xx_it.c ****   /* USER CODE END BusFault_IRQn 0 */
 121:Core/Src/stm32h7xx_it.c ****   while (1)
 104              		.loc 1 121 3 view .LVU13
 122:Core/Src/stm32h7xx_it.c ****   {
 123:Core/Src/stm32h7xx_it.c ****     /* USER CODE BEGIN W1_BusFault_IRQn 0 */
 124:Core/Src/stm32h7xx_it.c ****     /* USER CODE END W1_BusFault_IRQn 0 */
 125:Core/Src/stm32h7xx_it.c ****   }
 105              		.loc 1 125 3 view .LVU14
 121:Core/Src/stm32h7xx_it.c ****   {
 106              		.loc 1 121 9 view .LVU15
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccBtrFvC.s 			page 5


 107 0000 FEE7     		b	.L8
 108              		.cfi_endproc
 109              	.LFE147:
 111              		.section	.text.UsageFault_Handler,"ax",%progbits
 112              		.align	1
 113              		.global	UsageFault_Handler
 114              		.syntax unified
 115              		.thumb
 116              		.thumb_func
 118              	UsageFault_Handler:
 119              	.LFB148:
 126:Core/Src/stm32h7xx_it.c **** }
 127:Core/Src/stm32h7xx_it.c **** 
 128:Core/Src/stm32h7xx_it.c **** /**
 129:Core/Src/stm32h7xx_it.c ****   * @brief This function handles Undefined instruction or illegal state.
 130:Core/Src/stm32h7xx_it.c ****   */
 131:Core/Src/stm32h7xx_it.c **** void UsageFault_Handler(void)
 132:Core/Src/stm32h7xx_it.c **** {
 120              		.loc 1 132 1 view -0
 121              		.cfi_startproc
 122              		@ Volatile: function does not return.
 123              		@ args = 0, pretend = 0, frame = 0
 124              		@ frame_needed = 0, uses_anonymous_args = 0
 125              		@ link register save eliminated.
 126              	.L10:
 133:Core/Src/stm32h7xx_it.c ****   /* USER CODE BEGIN UsageFault_IRQn 0 */
 134:Core/Src/stm32h7xx_it.c **** 
 135:Core/Src/stm32h7xx_it.c ****   /* USER CODE END UsageFault_IRQn 0 */
 136:Core/Src/stm32h7xx_it.c ****   while (1)
 127              		.loc 1 136 3 view .LVU17
 137:Core/Src/stm32h7xx_it.c ****   {
 138:Core/Src/stm32h7xx_it.c ****     /* USER CODE BEGIN W1_UsageFault_IRQn 0 */
 139:Core/Src/stm32h7xx_it.c ****     /* USER CODE END W1_UsageFault_IRQn 0 */
 140:Core/Src/stm32h7xx_it.c ****   }
 128              		.loc 1 140 3 view .LVU18
 136:Core/Src/stm32h7xx_it.c ****   {
 129              		.loc 1 136 9 view .LVU19
 130 0000 FEE7     		b	.L10
 131              		.cfi_endproc
 132              	.LFE148:
 134              		.section	.text.DebugMon_Handler,"ax",%progbits
 135              		.align	1
 136              		.global	DebugMon_Handler
 137              		.syntax unified
 138              		.thumb
 139              		.thumb_func
 141              	DebugMon_Handler:
 142              	.LFB149:
 141:Core/Src/stm32h7xx_it.c **** }
 142:Core/Src/stm32h7xx_it.c **** 
 143:Core/Src/stm32h7xx_it.c **** /**
 144:Core/Src/stm32h7xx_it.c ****   * @brief This function handles Debug monitor.
 145:Core/Src/stm32h7xx_it.c ****   */
 146:Core/Src/stm32h7xx_it.c **** void DebugMon_Handler(void)
 147:Core/Src/stm32h7xx_it.c **** {
 143              		.loc 1 147 1 view -0
 144              		.cfi_startproc
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccBtrFvC.s 			page 6


 145              		@ args = 0, pretend = 0, frame = 0
 146              		@ frame_needed = 0, uses_anonymous_args = 0
 147              		@ link register save eliminated.
 148:Core/Src/stm32h7xx_it.c ****   /* USER CODE BEGIN DebugMonitor_IRQn 0 */
 149:Core/Src/stm32h7xx_it.c **** 
 150:Core/Src/stm32h7xx_it.c ****   /* USER CODE END DebugMonitor_IRQn 0 */
 151:Core/Src/stm32h7xx_it.c ****   /* USER CODE BEGIN DebugMonitor_IRQn 1 */
 152:Core/Src/stm32h7xx_it.c **** 
 153:Core/Src/stm32h7xx_it.c ****   /* USER CODE END DebugMonitor_IRQn 1 */
 154:Core/Src/stm32h7xx_it.c **** }
 148              		.loc 1 154 1 view .LVU21
 149 0000 7047     		bx	lr
 150              		.cfi_endproc
 151              	.LFE149:
 153              		.section	.text.TIM1_UP_IRQHandler,"ax",%progbits
 154              		.align	1
 155              		.global	TIM1_UP_IRQHandler
 156              		.syntax unified
 157              		.thumb
 158              		.thumb_func
 160              	TIM1_UP_IRQHandler:
 161              	.LFB150:
 155:Core/Src/stm32h7xx_it.c **** 
 156:Core/Src/stm32h7xx_it.c **** /******************************************************************************/
 157:Core/Src/stm32h7xx_it.c **** /* STM32H7xx Peripheral Interrupt Handlers                                    */
 158:Core/Src/stm32h7xx_it.c **** /* Add here the Interrupt Handlers for the used peripherals.                  */
 159:Core/Src/stm32h7xx_it.c **** /* For the available peripheral interrupt handler names,                      */
 160:Core/Src/stm32h7xx_it.c **** /* please refer to the startup file (startup_stm32h7xx.s).                    */
 161:Core/Src/stm32h7xx_it.c **** /******************************************************************************/
 162:Core/Src/stm32h7xx_it.c **** 
 163:Core/Src/stm32h7xx_it.c **** /**
 164:Core/Src/stm32h7xx_it.c ****   * @brief This function handles TIM1 update interrupt.
 165:Core/Src/stm32h7xx_it.c ****   */
 166:Core/Src/stm32h7xx_it.c **** void TIM1_UP_IRQHandler(void)
 167:Core/Src/stm32h7xx_it.c **** {
 162              		.loc 1 167 1 view -0
 163              		.cfi_startproc
 164              		@ args = 0, pretend = 0, frame = 0
 165              		@ frame_needed = 0, uses_anonymous_args = 0
 166 0000 08B5     		push	{r3, lr}
 167              	.LCFI0:
 168              		.cfi_def_cfa_offset 8
 169              		.cfi_offset 3, -8
 170              		.cfi_offset 14, -4
 168:Core/Src/stm32h7xx_it.c ****   /* USER CODE BEGIN TIM1_UP_IRQn 0 */
 169:Core/Src/stm32h7xx_it.c **** 
 170:Core/Src/stm32h7xx_it.c ****   /* USER CODE END TIM1_UP_IRQn 0 */
 171:Core/Src/stm32h7xx_it.c ****   HAL_TIM_IRQHandler(&htim1);
 171              		.loc 1 171 3 view .LVU23
 172 0002 0248     		ldr	r0, .L14
 173 0004 FFF7FEFF 		bl	HAL_TIM_IRQHandler
 174              	.LVL0:
 172:Core/Src/stm32h7xx_it.c ****   /* USER CODE BEGIN TIM1_UP_IRQn 1 */
 173:Core/Src/stm32h7xx_it.c **** 
 174:Core/Src/stm32h7xx_it.c ****   /* USER CODE END TIM1_UP_IRQn 1 */
 175:Core/Src/stm32h7xx_it.c **** }
 175              		.loc 1 175 1 is_stmt 0 view .LVU24
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccBtrFvC.s 			page 7


 176 0008 08BD     		pop	{r3, pc}
 177              	.L15:
 178 000a 00BF     		.align	2
 179              	.L14:
 180 000c 00000000 		.word	htim1
 181              		.cfi_endproc
 182              	.LFE150:
 184              		.section	.text.ETH_IRQHandler,"ax",%progbits
 185              		.align	1
 186              		.global	ETH_IRQHandler
 187              		.syntax unified
 188              		.thumb
 189              		.thumb_func
 191              	ETH_IRQHandler:
 192              	.LFB151:
 176:Core/Src/stm32h7xx_it.c **** 
 177:Core/Src/stm32h7xx_it.c **** /**
 178:Core/Src/stm32h7xx_it.c ****   * @brief This function handles Ethernet global interrupt.
 179:Core/Src/stm32h7xx_it.c ****   */
 180:Core/Src/stm32h7xx_it.c **** void ETH_IRQHandler(void)
 181:Core/Src/stm32h7xx_it.c **** {
 193              		.loc 1 181 1 is_stmt 1 view -0
 194              		.cfi_startproc
 195              		@ args = 0, pretend = 0, frame = 0
 196              		@ frame_needed = 0, uses_anonymous_args = 0
 197 0000 08B5     		push	{r3, lr}
 198              	.LCFI1:
 199              		.cfi_def_cfa_offset 8
 200              		.cfi_offset 3, -8
 201              		.cfi_offset 14, -4
 182:Core/Src/stm32h7xx_it.c ****   /* USER CODE BEGIN ETH_IRQn 0 */
 183:Core/Src/stm32h7xx_it.c **** 
 184:Core/Src/stm32h7xx_it.c ****   /* USER CODE END ETH_IRQn 0 */
 185:Core/Src/stm32h7xx_it.c ****   HAL_ETH_IRQHandler(&heth);
 202              		.loc 1 185 3 view .LVU26
 203 0002 0248     		ldr	r0, .L18
 204 0004 FFF7FEFF 		bl	HAL_ETH_IRQHandler
 205              	.LVL1:
 186:Core/Src/stm32h7xx_it.c ****   /* USER CODE BEGIN ETH_IRQn 1 */
 187:Core/Src/stm32h7xx_it.c **** 
 188:Core/Src/stm32h7xx_it.c ****   /* USER CODE END ETH_IRQn 1 */
 189:Core/Src/stm32h7xx_it.c **** }
 206              		.loc 1 189 1 is_stmt 0 view .LVU27
 207 0008 08BD     		pop	{r3, pc}
 208              	.L19:
 209 000a 00BF     		.align	2
 210              	.L18:
 211 000c 00000000 		.word	heth
 212              		.cfi_endproc
 213              	.LFE151:
 215              		.section	.text.ETH_WKUP_IRQHandler,"ax",%progbits
 216              		.align	1
 217              		.global	ETH_WKUP_IRQHandler
 218              		.syntax unified
 219              		.thumb
 220              		.thumb_func
 222              	ETH_WKUP_IRQHandler:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccBtrFvC.s 			page 8


 223              	.LFB152:
 190:Core/Src/stm32h7xx_it.c **** 
 191:Core/Src/stm32h7xx_it.c **** /**
 192:Core/Src/stm32h7xx_it.c ****   * @brief This function handles Ethernet wake-up interrupt through EXTI line 86.
 193:Core/Src/stm32h7xx_it.c ****   */
 194:Core/Src/stm32h7xx_it.c **** void ETH_WKUP_IRQHandler(void)
 195:Core/Src/stm32h7xx_it.c **** {
 224              		.loc 1 195 1 is_stmt 1 view -0
 225              		.cfi_startproc
 226              		@ args = 0, pretend = 0, frame = 0
 227              		@ frame_needed = 0, uses_anonymous_args = 0
 228 0000 08B5     		push	{r3, lr}
 229              	.LCFI2:
 230              		.cfi_def_cfa_offset 8
 231              		.cfi_offset 3, -8
 232              		.cfi_offset 14, -4
 196:Core/Src/stm32h7xx_it.c ****   /* USER CODE BEGIN ETH_WKUP_IRQn 0 */
 197:Core/Src/stm32h7xx_it.c **** 
 198:Core/Src/stm32h7xx_it.c ****   /* USER CODE END ETH_WKUP_IRQn 0 */
 199:Core/Src/stm32h7xx_it.c ****   HAL_ETH_IRQHandler(&heth);
 233              		.loc 1 199 3 view .LVU29
 234 0002 0248     		ldr	r0, .L22
 235 0004 FFF7FEFF 		bl	HAL_ETH_IRQHandler
 236              	.LVL2:
 200:Core/Src/stm32h7xx_it.c ****   /* USER CODE BEGIN ETH_WKUP_IRQn 1 */
 201:Core/Src/stm32h7xx_it.c **** 
 202:Core/Src/stm32h7xx_it.c ****   /* USER CODE END ETH_WKUP_IRQn 1 */
 203:Core/Src/stm32h7xx_it.c **** }
 237              		.loc 1 203 1 is_stmt 0 view .LVU30
 238 0008 08BD     		pop	{r3, pc}
 239              	.L23:
 240 000a 00BF     		.align	2
 241              	.L22:
 242 000c 00000000 		.word	heth
 243              		.cfi_endproc
 244              	.LFE152:
 246              		.text
 247              	.Letext0:
 248              		.file 2 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\../lib/gcc/arm-none-eabi/13.3.1/..
 249              		.file 3 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin\\../lib/gcc/arm-none-eabi/13.3.1/..
 250              		.file 4 "Drivers/CMSIS/Device/ST/STM32H7xx/Include/stm32h723xx.h"
 251              		.file 5 "Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_def.h"
 252              		.file 6 "Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_dma.h"
 253              		.file 7 "Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_eth.h"
 254              		.file 8 "Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_tim.h"
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccBtrFvC.s 			page 9


DEFINED SYMBOLS
                            *ABS*:00000000 stm32h7xx_it.c
C:\Users\<USER>\AppData\Local\Temp\ccBtrFvC.s:20     .text.NMI_Handler:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccBtrFvC.s:26     .text.NMI_Handler:00000000 NMI_Handler
C:\Users\<USER>\AppData\Local\Temp\ccBtrFvC.s:43     .text.HardFault_Handler:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccBtrFvC.s:49     .text.HardFault_Handler:00000000 HardFault_Handler
C:\Users\<USER>\AppData\Local\Temp\ccBtrFvC.s:66     .text.MemManage_Handler:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccBtrFvC.s:72     .text.MemManage_Handler:00000000 MemManage_Handler
C:\Users\<USER>\AppData\Local\Temp\ccBtrFvC.s:89     .text.BusFault_Handler:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccBtrFvC.s:95     .text.BusFault_Handler:00000000 BusFault_Handler
C:\Users\<USER>\AppData\Local\Temp\ccBtrFvC.s:112    .text.UsageFault_Handler:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccBtrFvC.s:118    .text.UsageFault_Handler:00000000 UsageFault_Handler
C:\Users\<USER>\AppData\Local\Temp\ccBtrFvC.s:135    .text.DebugMon_Handler:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccBtrFvC.s:141    .text.DebugMon_Handler:00000000 DebugMon_Handler
C:\Users\<USER>\AppData\Local\Temp\ccBtrFvC.s:154    .text.TIM1_UP_IRQHandler:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccBtrFvC.s:160    .text.TIM1_UP_IRQHandler:00000000 TIM1_UP_IRQHandler
C:\Users\<USER>\AppData\Local\Temp\ccBtrFvC.s:180    .text.TIM1_UP_IRQHandler:0000000c $d
C:\Users\<USER>\AppData\Local\Temp\ccBtrFvC.s:185    .text.ETH_IRQHandler:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccBtrFvC.s:191    .text.ETH_IRQHandler:00000000 ETH_IRQHandler
C:\Users\<USER>\AppData\Local\Temp\ccBtrFvC.s:211    .text.ETH_IRQHandler:0000000c $d
C:\Users\<USER>\AppData\Local\Temp\ccBtrFvC.s:216    .text.ETH_WKUP_IRQHandler:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccBtrFvC.s:222    .text.ETH_WKUP_IRQHandler:00000000 ETH_WKUP_IRQHandler
C:\Users\<USER>\AppData\Local\Temp\ccBtrFvC.s:242    .text.ETH_WKUP_IRQHandler:0000000c $d

UNDEFINED SYMBOLS
HAL_TIM_IRQHandler
htim1
HAL_ETH_IRQHandler
heth
