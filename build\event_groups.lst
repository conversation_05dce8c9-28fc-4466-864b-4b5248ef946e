ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s 			page 1


   1              		.cpu cortex-m7
   2              		.arch armv7e-m
   3              		.fpu fpv5-d16
   4              		.eabi_attribute 28, 1
   5              		.eabi_attribute 20, 1
   6              		.eabi_attribute 21, 1
   7              		.eabi_attribute 23, 3
   8              		.eabi_attribute 24, 1
   9              		.eabi_attribute 25, 1
  10              		.eabi_attribute 26, 1
  11              		.eabi_attribute 30, 1
  12              		.eabi_attribute 34, 1
  13              		.eabi_attribute 18, 4
  14              		.file	"event_groups.c"
  15              		.text
  16              	.Ltext0:
  17              		.cfi_sections	.debug_frame
  18              		.file 1 "Middlewares/Third_Party/FreeRTOS/Source/event_groups.c"
  19              		.section	.text.prvTestWaitCondition,"ax",%progbits
  20              		.align	1
  21              		.syntax unified
  22              		.thumb
  23              		.thumb_func
  25              	prvTestWaitCondition:
  26              	.LVL0:
  27              	.LFB15:
   1:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** /*
   2:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c ****  * FreeRTOS Kernel V10.3.1
   3:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c ****  * Copyright (C) 2020 Amazon.com, Inc. or its affiliates.  All Rights Reserved.
   4:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c ****  *
   5:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c ****  * Permission is hereby granted, free of charge, to any person obtaining a copy of
   6:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c ****  * this software and associated documentation files (the "Software"), to deal in
   7:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c ****  * the Software without restriction, including without limitation the rights to
   8:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c ****  * use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of
   9:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c ****  * the Software, and to permit persons to whom the Software is furnished to do so,
  10:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c ****  * subject to the following conditions:
  11:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c ****  *
  12:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c ****  * The above copyright notice and this permission notice shall be included in all
  13:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c ****  * copies or substantial portions of the Software.
  14:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c ****  *
  15:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c ****  * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
  16:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c ****  * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS
  17:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c ****  * FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR
  18:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c ****  * COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER
  19:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c ****  * IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
  20:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c ****  * CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
  21:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c ****  *
  22:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c ****  * http://www.FreeRTOS.org
  23:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c ****  * http://aws.amazon.com/freertos
  24:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c ****  *
  25:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c ****  * 1 tab == 4 spaces!
  26:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c ****  */
  27:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
  28:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** /* Standard includes. */
  29:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** #include <stdlib.h>
  30:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
  31:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** /* Defining MPU_WRAPPERS_INCLUDED_FROM_API_FILE prevents task.h from redefining
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s 			page 2


  32:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** all the API functions to use the MPU wrappers.  That should only be done when
  33:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** task.h is included from an application file. */
  34:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** #define MPU_WRAPPERS_INCLUDED_FROM_API_FILE
  35:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
  36:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** /* FreeRTOS includes. */
  37:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** #include "FreeRTOS.h"
  38:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** #include "task.h"
  39:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** #include "timers.h"
  40:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** #include "event_groups.h"
  41:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
  42:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** /* Lint e961, e750 and e9021 are suppressed as a MISRA exception justified
  43:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** because the MPU ports require MPU_WRAPPERS_INCLUDED_FROM_API_FILE to be defined
  44:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** for the header files above, but not in this file, in order to generate the
  45:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** correct privileged Vs unprivileged linkage and placement. */
  46:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** #undef MPU_WRAPPERS_INCLUDED_FROM_API_FILE /*lint !e961 !e750 !e9021 See comment above. */
  47:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
  48:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** /* The following bit fields convey control information in a task's event list
  49:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** item value.  It is important they don't clash with the
  50:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** taskEVENT_LIST_ITEM_VALUE_IN_USE definition. */
  51:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** #if configUSE_16_BIT_TICKS == 1
  52:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	#define eventCLEAR_EVENTS_ON_EXIT_BIT	0x0100U
  53:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	#define eventUNBLOCKED_DUE_TO_BIT_SET	0x0200U
  54:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	#define eventWAIT_FOR_ALL_BITS			0x0400U
  55:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	#define eventEVENT_BITS_CONTROL_BYTES	0xff00U
  56:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** #else
  57:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	#define eventCLEAR_EVENTS_ON_EXIT_BIT	0x01000000UL
  58:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	#define eventUNBLOCKED_DUE_TO_BIT_SET	0x02000000UL
  59:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	#define eventWAIT_FOR_ALL_BITS			0x04000000UL
  60:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	#define eventEVENT_BITS_CONTROL_BYTES	0xff000000UL
  61:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** #endif
  62:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
  63:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** typedef struct EventGroupDef_t
  64:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** {
  65:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	EventBits_t uxEventBits;
  66:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	List_t xTasksWaitingForBits;		/*< List of tasks waiting for a bit to be set. */
  67:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
  68:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	#if( configUSE_TRACE_FACILITY == 1 )
  69:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		UBaseType_t uxEventGroupNumber;
  70:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	#endif
  71:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
  72:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	#if( ( configSUPPORT_STATIC_ALLOCATION == 1 ) && ( configSUPPORT_DYNAMIC_ALLOCATION == 1 ) )
  73:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		uint8_t ucStaticallyAllocated; /*< Set to pdTRUE if the event group is statically allocated to en
  74:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	#endif
  75:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** } EventGroup_t;
  76:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
  77:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** /*-----------------------------------------------------------*/
  78:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
  79:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** /*
  80:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c ****  * Test the bits set in uxCurrentEventBits to see if the wait condition is met.
  81:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c ****  * The wait condition is defined by xWaitForAllBits.  If xWaitForAllBits is
  82:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c ****  * pdTRUE then the wait condition is met if all the bits set in uxBitsToWaitFor
  83:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c ****  * are also set in uxCurrentEventBits.  If xWaitForAllBits is pdFALSE then the
  84:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c ****  * wait condition is met if any of the bits set in uxBitsToWait for are also set
  85:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c ****  * in uxCurrentEventBits.
  86:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c ****  */
  87:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** static BaseType_t prvTestWaitCondition( const EventBits_t uxCurrentEventBits, const EventBits_t uxB
  88:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s 			page 3


  89:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** /*-----------------------------------------------------------*/
  90:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
  91:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** #if( configSUPPORT_STATIC_ALLOCATION == 1 )
  92:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
  93:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	EventGroupHandle_t xEventGroupCreateStatic( StaticEventGroup_t *pxEventGroupBuffer )
  94:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	{
  95:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	EventGroup_t *pxEventBits;
  96:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
  97:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		/* A StaticEventGroup_t object must be provided. */
  98:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		configASSERT( pxEventGroupBuffer );
  99:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 100:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		#if( configASSERT_DEFINED == 1 )
 101:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		{
 102:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			/* Sanity check that the size of the structure used to declare a
 103:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			variable of type StaticEventGroup_t equals the size of the real
 104:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			event group structure. */
 105:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			volatile size_t xSize = sizeof( StaticEventGroup_t );
 106:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			configASSERT( xSize == sizeof( EventGroup_t ) );
 107:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		} /*lint !e529 xSize is referenced if configASSERT() is defined. */
 108:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		#endif /* configASSERT_DEFINED */
 109:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 110:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		/* The user has provided a statically allocated event group - use it. */
 111:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		pxEventBits = ( EventGroup_t * ) pxEventGroupBuffer; /*lint !e740 !e9087 EventGroup_t and StaticE
 112:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 113:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		if( pxEventBits != NULL )
 114:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		{
 115:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			pxEventBits->uxEventBits = 0;
 116:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			vListInitialise( &( pxEventBits->xTasksWaitingForBits ) );
 117:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 118:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			#if( configSUPPORT_DYNAMIC_ALLOCATION == 1 )
 119:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			{
 120:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				/* Both static and dynamic allocation can be used, so note that
 121:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				this event group was created statically in case the event group
 122:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				is later deleted. */
 123:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				pxEventBits->ucStaticallyAllocated = pdTRUE;
 124:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			}
 125:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			#endif /* configSUPPORT_DYNAMIC_ALLOCATION */
 126:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 127:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			traceEVENT_GROUP_CREATE( pxEventBits );
 128:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		}
 129:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		else
 130:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		{
 131:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			/* xEventGroupCreateStatic should only ever be called with
 132:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			pxEventGroupBuffer pointing to a pre-allocated (compile time
 133:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			allocated) StaticEventGroup_t variable. */
 134:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			traceEVENT_GROUP_CREATE_FAILED();
 135:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		}
 136:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 137:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		return pxEventBits;
 138:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	}
 139:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 140:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** #endif /* configSUPPORT_STATIC_ALLOCATION */
 141:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** /*-----------------------------------------------------------*/
 142:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 143:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** #if( configSUPPORT_DYNAMIC_ALLOCATION == 1 )
 144:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 145:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	EventGroupHandle_t xEventGroupCreate( void )
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s 			page 4


 146:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	{
 147:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	EventGroup_t *pxEventBits;
 148:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 149:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		/* Allocate the event group.  Justification for MISRA deviation as
 150:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		follows:  pvPortMalloc() always ensures returned memory blocks are
 151:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		aligned per the requirements of the MCU stack.  In this case
 152:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		pvPortMalloc() must return a pointer that is guaranteed to meet the
 153:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		alignment requirements of the EventGroup_t structure - which (if you
 154:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		follow it through) is the alignment requirements of the TickType_t type
 155:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		(EventBits_t being of TickType_t itself).  Therefore, whenever the
 156:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		stack alignment requirements are greater than or equal to the
 157:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		TickType_t alignment requirements the cast is safe.  In other cases,
 158:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		where the natural word size of the architecture is less than
 159:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		sizeof( TickType_t ), the TickType_t variables will be accessed in two
 160:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		or more reads operations, and the alignment requirements is only that
 161:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		of each individual read. */
 162:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		pxEventBits = ( EventGroup_t * ) pvPortMalloc( sizeof( EventGroup_t ) ); /*lint !e9087 !e9079 see
 163:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 164:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		if( pxEventBits != NULL )
 165:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		{
 166:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			pxEventBits->uxEventBits = 0;
 167:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			vListInitialise( &( pxEventBits->xTasksWaitingForBits ) );
 168:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 169:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			#if( configSUPPORT_STATIC_ALLOCATION == 1 )
 170:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			{
 171:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				/* Both static and dynamic allocation can be used, so note this
 172:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				event group was allocated statically in case the event group is
 173:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				later deleted. */
 174:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				pxEventBits->ucStaticallyAllocated = pdFALSE;
 175:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			}
 176:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			#endif /* configSUPPORT_STATIC_ALLOCATION */
 177:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 178:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			traceEVENT_GROUP_CREATE( pxEventBits );
 179:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		}
 180:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		else
 181:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		{
 182:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			traceEVENT_GROUP_CREATE_FAILED(); /*lint !e9063 Else branch only exists to allow tracing and doe
 183:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		}
 184:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 185:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		return pxEventBits;
 186:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	}
 187:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 188:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** #endif /* configSUPPORT_DYNAMIC_ALLOCATION */
 189:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** /*-----------------------------------------------------------*/
 190:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 191:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** EventBits_t xEventGroupSync( EventGroupHandle_t xEventGroup, const EventBits_t uxBitsToSet, const E
 192:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** {
 193:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** EventBits_t uxOriginalBitValue, uxReturn;
 194:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** EventGroup_t *pxEventBits = xEventGroup;
 195:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** BaseType_t xAlreadyYielded;
 196:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** BaseType_t xTimeoutOccurred = pdFALSE;
 197:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 198:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	configASSERT( ( uxBitsToWaitFor & eventEVENT_BITS_CONTROL_BYTES ) == 0 );
 199:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	configASSERT( uxBitsToWaitFor != 0 );
 200:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	#if ( ( INCLUDE_xTaskGetSchedulerState == 1 ) || ( configUSE_TIMERS == 1 ) )
 201:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	{
 202:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		configASSERT( !( ( xTaskGetSchedulerState() == taskSCHEDULER_SUSPENDED ) && ( xTicksToWait != 0 )
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s 			page 5


 203:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	}
 204:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	#endif
 205:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 206:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	vTaskSuspendAll();
 207:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	{
 208:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		uxOriginalBitValue = pxEventBits->uxEventBits;
 209:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 210:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		( void ) xEventGroupSetBits( xEventGroup, uxBitsToSet );
 211:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 212:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		if( ( ( uxOriginalBitValue | uxBitsToSet ) & uxBitsToWaitFor ) == uxBitsToWaitFor )
 213:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		{
 214:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			/* All the rendezvous bits are now set - no need to block. */
 215:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			uxReturn = ( uxOriginalBitValue | uxBitsToSet );
 216:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 217:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			/* Rendezvous always clear the bits.  They will have been cleared
 218:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			already unless this is the only task in the rendezvous. */
 219:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			pxEventBits->uxEventBits &= ~uxBitsToWaitFor;
 220:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 221:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			xTicksToWait = 0;
 222:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		}
 223:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		else
 224:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		{
 225:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			if( xTicksToWait != ( TickType_t ) 0 )
 226:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			{
 227:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				traceEVENT_GROUP_SYNC_BLOCK( xEventGroup, uxBitsToSet, uxBitsToWaitFor );
 228:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 229:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				/* Store the bits that the calling task is waiting for in the
 230:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				task's event list item so the kernel knows when a match is
 231:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				found.  Then enter the blocked state. */
 232:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				vTaskPlaceOnUnorderedEventList( &( pxEventBits->xTasksWaitingForBits ), ( uxBitsToWaitFor | eve
 233:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 234:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				/* This assignment is obsolete as uxReturn will get set after
 235:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				the task unblocks, but some compilers mistakenly generate a
 236:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				warning about uxReturn being returned without being set if the
 237:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				assignment is omitted. */
 238:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				uxReturn = 0;
 239:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			}
 240:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			else
 241:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			{
 242:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				/* The rendezvous bits were not set, but no block time was
 243:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				specified - just return the current event bit value. */
 244:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				uxReturn = pxEventBits->uxEventBits;
 245:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				xTimeoutOccurred = pdTRUE;
 246:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			}
 247:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		}
 248:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	}
 249:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	xAlreadyYielded = xTaskResumeAll();
 250:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 251:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	if( xTicksToWait != ( TickType_t ) 0 )
 252:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	{
 253:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		if( xAlreadyYielded == pdFALSE )
 254:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		{
 255:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			portYIELD_WITHIN_API();
 256:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		}
 257:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		else
 258:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		{
 259:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			mtCOVERAGE_TEST_MARKER();
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s 			page 6


 260:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		}
 261:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 262:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		/* The task blocked to wait for its required bits to be set - at this
 263:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		point either the required bits were set or the block time expired.  If
 264:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		the required bits were set they will have been stored in the task's
 265:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		event list item, and they should now be retrieved then cleared. */
 266:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		uxReturn = uxTaskResetEventItemValue();
 267:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 268:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		if( ( uxReturn & eventUNBLOCKED_DUE_TO_BIT_SET ) == ( EventBits_t ) 0 )
 269:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		{
 270:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			/* The task timed out, just return the current event bit value. */
 271:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			taskENTER_CRITICAL();
 272:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			{
 273:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				uxReturn = pxEventBits->uxEventBits;
 274:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 275:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				/* Although the task got here because it timed out before the
 276:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				bits it was waiting for were set, it is possible that since it
 277:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				unblocked another task has set the bits.  If this is the case
 278:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				then it needs to clear the bits before exiting. */
 279:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				if( ( uxReturn & uxBitsToWaitFor ) == uxBitsToWaitFor )
 280:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				{
 281:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 					pxEventBits->uxEventBits &= ~uxBitsToWaitFor;
 282:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				}
 283:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				else
 284:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				{
 285:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 					mtCOVERAGE_TEST_MARKER();
 286:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				}
 287:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			}
 288:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			taskEXIT_CRITICAL();
 289:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 290:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			xTimeoutOccurred = pdTRUE;
 291:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		}
 292:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		else
 293:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		{
 294:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			/* The task unblocked because the bits were set. */
 295:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		}
 296:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 297:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		/* Control bits might be set as the task had blocked should not be
 298:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		returned. */
 299:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		uxReturn &= ~eventEVENT_BITS_CONTROL_BYTES;
 300:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	}
 301:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 302:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	traceEVENT_GROUP_SYNC_END( xEventGroup, uxBitsToSet, uxBitsToWaitFor, xTimeoutOccurred );
 303:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 304:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	/* Prevent compiler warnings when trace macros are not used. */
 305:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	( void ) xTimeoutOccurred;
 306:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 307:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	return uxReturn;
 308:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** }
 309:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** /*-----------------------------------------------------------*/
 310:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 311:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** EventBits_t xEventGroupWaitBits( EventGroupHandle_t xEventGroup, const EventBits_t uxBitsToWaitFor,
 312:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** {
 313:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** EventGroup_t *pxEventBits = xEventGroup;
 314:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** EventBits_t uxReturn, uxControlBits = 0;
 315:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** BaseType_t xWaitConditionMet, xAlreadyYielded;
 316:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** BaseType_t xTimeoutOccurred = pdFALSE;
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s 			page 7


 317:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 318:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	/* Check the user is not attempting to wait on the bits used by the kernel
 319:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	itself, and that at least one bit is being requested. */
 320:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	configASSERT( xEventGroup );
 321:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	configASSERT( ( uxBitsToWaitFor & eventEVENT_BITS_CONTROL_BYTES ) == 0 );
 322:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	configASSERT( uxBitsToWaitFor != 0 );
 323:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	#if ( ( INCLUDE_xTaskGetSchedulerState == 1 ) || ( configUSE_TIMERS == 1 ) )
 324:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	{
 325:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		configASSERT( !( ( xTaskGetSchedulerState() == taskSCHEDULER_SUSPENDED ) && ( xTicksToWait != 0 )
 326:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	}
 327:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	#endif
 328:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 329:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	vTaskSuspendAll();
 330:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	{
 331:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		const EventBits_t uxCurrentEventBits = pxEventBits->uxEventBits;
 332:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 333:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		/* Check to see if the wait condition is already met or not. */
 334:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		xWaitConditionMet = prvTestWaitCondition( uxCurrentEventBits, uxBitsToWaitFor, xWaitForAllBits );
 335:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 336:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		if( xWaitConditionMet != pdFALSE )
 337:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		{
 338:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			/* The wait condition has already been met so there is no need to
 339:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			block. */
 340:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			uxReturn = uxCurrentEventBits;
 341:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			xTicksToWait = ( TickType_t ) 0;
 342:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 343:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			/* Clear the wait bits if requested to do so. */
 344:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			if( xClearOnExit != pdFALSE )
 345:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			{
 346:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				pxEventBits->uxEventBits &= ~uxBitsToWaitFor;
 347:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			}
 348:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			else
 349:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			{
 350:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				mtCOVERAGE_TEST_MARKER();
 351:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			}
 352:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		}
 353:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		else if( xTicksToWait == ( TickType_t ) 0 )
 354:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		{
 355:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			/* The wait condition has not been met, but no block time was
 356:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			specified, so just return the current value. */
 357:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			uxReturn = uxCurrentEventBits;
 358:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			xTimeoutOccurred = pdTRUE;
 359:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		}
 360:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		else
 361:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		{
 362:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			/* The task is going to block to wait for its required bits to be
 363:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			set.  uxControlBits are used to remember the specified behaviour of
 364:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			this call to xEventGroupWaitBits() - for use when the event bits
 365:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			unblock the task. */
 366:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			if( xClearOnExit != pdFALSE )
 367:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			{
 368:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				uxControlBits |= eventCLEAR_EVENTS_ON_EXIT_BIT;
 369:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			}
 370:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			else
 371:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			{
 372:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				mtCOVERAGE_TEST_MARKER();
 373:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			}
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s 			page 8


 374:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 375:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			if( xWaitForAllBits != pdFALSE )
 376:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			{
 377:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				uxControlBits |= eventWAIT_FOR_ALL_BITS;
 378:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			}
 379:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			else
 380:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			{
 381:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				mtCOVERAGE_TEST_MARKER();
 382:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			}
 383:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 384:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			/* Store the bits that the calling task is waiting for in the
 385:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			task's event list item so the kernel knows when a match is
 386:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			found.  Then enter the blocked state. */
 387:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			vTaskPlaceOnUnorderedEventList( &( pxEventBits->xTasksWaitingForBits ), ( uxBitsToWaitFor | uxCo
 388:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 389:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			/* This is obsolete as it will get set after the task unblocks, but
 390:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			some compilers mistakenly generate a warning about the variable
 391:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			being returned without being set if it is not done. */
 392:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			uxReturn = 0;
 393:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 394:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			traceEVENT_GROUP_WAIT_BITS_BLOCK( xEventGroup, uxBitsToWaitFor );
 395:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		}
 396:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	}
 397:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	xAlreadyYielded = xTaskResumeAll();
 398:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 399:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	if( xTicksToWait != ( TickType_t ) 0 )
 400:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	{
 401:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		if( xAlreadyYielded == pdFALSE )
 402:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		{
 403:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			portYIELD_WITHIN_API();
 404:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		}
 405:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		else
 406:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		{
 407:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			mtCOVERAGE_TEST_MARKER();
 408:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		}
 409:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 410:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		/* The task blocked to wait for its required bits to be set - at this
 411:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		point either the required bits were set or the block time expired.  If
 412:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		the required bits were set they will have been stored in the task's
 413:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		event list item, and they should now be retrieved then cleared. */
 414:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		uxReturn = uxTaskResetEventItemValue();
 415:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 416:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		if( ( uxReturn & eventUNBLOCKED_DUE_TO_BIT_SET ) == ( EventBits_t ) 0 )
 417:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		{
 418:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			taskENTER_CRITICAL();
 419:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			{
 420:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				/* The task timed out, just return the current event bit value. */
 421:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				uxReturn = pxEventBits->uxEventBits;
 422:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 423:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				/* It is possible that the event bits were updated between this
 424:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				task leaving the Blocked state and running again. */
 425:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				if( prvTestWaitCondition( uxReturn, uxBitsToWaitFor, xWaitForAllBits ) != pdFALSE )
 426:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				{
 427:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 					if( xClearOnExit != pdFALSE )
 428:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 					{
 429:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 						pxEventBits->uxEventBits &= ~uxBitsToWaitFor;
 430:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 					}
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s 			page 9


 431:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 					else
 432:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 					{
 433:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 						mtCOVERAGE_TEST_MARKER();
 434:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 					}
 435:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				}
 436:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				else
 437:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				{
 438:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 					mtCOVERAGE_TEST_MARKER();
 439:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				}
 440:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				xTimeoutOccurred = pdTRUE;
 441:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			}
 442:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			taskEXIT_CRITICAL();
 443:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		}
 444:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		else
 445:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		{
 446:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			/* The task unblocked because the bits were set. */
 447:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		}
 448:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 449:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		/* The task blocked so control bits may have been set. */
 450:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		uxReturn &= ~eventEVENT_BITS_CONTROL_BYTES;
 451:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	}
 452:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	traceEVENT_GROUP_WAIT_BITS_END( xEventGroup, uxBitsToWaitFor, xTimeoutOccurred );
 453:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 454:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	/* Prevent compiler warnings when trace macros are not used. */
 455:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	( void ) xTimeoutOccurred;
 456:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 457:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	return uxReturn;
 458:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** }
 459:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** /*-----------------------------------------------------------*/
 460:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 461:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** EventBits_t xEventGroupClearBits( EventGroupHandle_t xEventGroup, const EventBits_t uxBitsToClear )
 462:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** {
 463:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** EventGroup_t *pxEventBits = xEventGroup;
 464:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** EventBits_t uxReturn;
 465:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 466:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	/* Check the user is not attempting to clear the bits used by the kernel
 467:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	itself. */
 468:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	configASSERT( xEventGroup );
 469:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	configASSERT( ( uxBitsToClear & eventEVENT_BITS_CONTROL_BYTES ) == 0 );
 470:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 471:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	taskENTER_CRITICAL();
 472:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	{
 473:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		traceEVENT_GROUP_CLEAR_BITS( xEventGroup, uxBitsToClear );
 474:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 475:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		/* The value returned is the event group value prior to the bits being
 476:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		cleared. */
 477:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		uxReturn = pxEventBits->uxEventBits;
 478:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 479:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		/* Clear the bits. */
 480:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		pxEventBits->uxEventBits &= ~uxBitsToClear;
 481:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	}
 482:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	taskEXIT_CRITICAL();
 483:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 484:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	return uxReturn;
 485:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** }
 486:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** /*-----------------------------------------------------------*/
 487:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s 			page 10


 488:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** #if ( ( configUSE_TRACE_FACILITY == 1 ) && ( INCLUDE_xTimerPendFunctionCall == 1 ) && ( configUSE_T
 489:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 490:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	BaseType_t xEventGroupClearBitsFromISR( EventGroupHandle_t xEventGroup, const EventBits_t uxBitsTo
 491:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	{
 492:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		BaseType_t xReturn;
 493:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 494:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		traceEVENT_GROUP_CLEAR_BITS_FROM_ISR( xEventGroup, uxBitsToClear );
 495:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		xReturn = xTimerPendFunctionCallFromISR( vEventGroupClearBitsCallback, ( void * ) xEventGroup, ( 
 496:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 497:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		return xReturn;
 498:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	}
 499:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 500:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** #endif
 501:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** /*-----------------------------------------------------------*/
 502:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 503:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** EventBits_t xEventGroupGetBitsFromISR( EventGroupHandle_t xEventGroup )
 504:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** {
 505:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** UBaseType_t uxSavedInterruptStatus;
 506:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** EventGroup_t const * const pxEventBits = xEventGroup;
 507:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** EventBits_t uxReturn;
 508:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 509:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	uxSavedInterruptStatus = portSET_INTERRUPT_MASK_FROM_ISR();
 510:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	{
 511:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		uxReturn = pxEventBits->uxEventBits;
 512:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	}
 513:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	portCLEAR_INTERRUPT_MASK_FROM_ISR( uxSavedInterruptStatus );
 514:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 515:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	return uxReturn;
 516:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** } /*lint !e818 EventGroupHandle_t is a typedef used in other functions to so can't be pointer to co
 517:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** /*-----------------------------------------------------------*/
 518:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 519:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** EventBits_t xEventGroupSetBits( EventGroupHandle_t xEventGroup, const EventBits_t uxBitsToSet )
 520:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** {
 521:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** ListItem_t *pxListItem, *pxNext;
 522:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** ListItem_t const *pxListEnd;
 523:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** List_t const * pxList;
 524:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** EventBits_t uxBitsToClear = 0, uxBitsWaitedFor, uxControlBits;
 525:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** EventGroup_t *pxEventBits = xEventGroup;
 526:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** BaseType_t xMatchFound = pdFALSE;
 527:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 528:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	/* Check the user is not attempting to set the bits used by the kernel
 529:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	itself. */
 530:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	configASSERT( xEventGroup );
 531:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	configASSERT( ( uxBitsToSet & eventEVENT_BITS_CONTROL_BYTES ) == 0 );
 532:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 533:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	pxList = &( pxEventBits->xTasksWaitingForBits );
 534:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	pxListEnd = listGET_END_MARKER( pxList ); /*lint !e826 !e740 !e9087 The mini list structure is use
 535:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	vTaskSuspendAll();
 536:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	{
 537:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		traceEVENT_GROUP_SET_BITS( xEventGroup, uxBitsToSet );
 538:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 539:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		pxListItem = listGET_HEAD_ENTRY( pxList );
 540:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 541:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		/* Set the bits. */
 542:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		pxEventBits->uxEventBits |= uxBitsToSet;
 543:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 544:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		/* See if the new bit value should unblock any tasks. */
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s 			page 11


 545:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		while( pxListItem != pxListEnd )
 546:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		{
 547:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			pxNext = listGET_NEXT( pxListItem );
 548:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			uxBitsWaitedFor = listGET_LIST_ITEM_VALUE( pxListItem );
 549:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			xMatchFound = pdFALSE;
 550:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 551:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			/* Split the bits waited for from the control bits. */
 552:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			uxControlBits = uxBitsWaitedFor & eventEVENT_BITS_CONTROL_BYTES;
 553:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			uxBitsWaitedFor &= ~eventEVENT_BITS_CONTROL_BYTES;
 554:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 555:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			if( ( uxControlBits & eventWAIT_FOR_ALL_BITS ) == ( EventBits_t ) 0 )
 556:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			{
 557:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				/* Just looking for single bit being set. */
 558:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				if( ( uxBitsWaitedFor & pxEventBits->uxEventBits ) != ( EventBits_t ) 0 )
 559:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				{
 560:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 					xMatchFound = pdTRUE;
 561:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				}
 562:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				else
 563:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				{
 564:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 					mtCOVERAGE_TEST_MARKER();
 565:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				}
 566:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			}
 567:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			else if( ( uxBitsWaitedFor & pxEventBits->uxEventBits ) == uxBitsWaitedFor )
 568:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			{
 569:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				/* All bits are set. */
 570:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				xMatchFound = pdTRUE;
 571:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			}
 572:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			else
 573:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			{
 574:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				/* Need all bits to be set, but not all the bits were set. */
 575:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			}
 576:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 577:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			if( xMatchFound != pdFALSE )
 578:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			{
 579:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				/* The bits match.  Should the bits be cleared on exit? */
 580:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				if( ( uxControlBits & eventCLEAR_EVENTS_ON_EXIT_BIT ) != ( EventBits_t ) 0 )
 581:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				{
 582:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 					uxBitsToClear |= uxBitsWaitedFor;
 583:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				}
 584:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				else
 585:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				{
 586:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 					mtCOVERAGE_TEST_MARKER();
 587:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				}
 588:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 589:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				/* Store the actual event flag value in the task's event list
 590:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				item before removing the task from the event list.  The
 591:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				eventUNBLOCKED_DUE_TO_BIT_SET bit is set so the task knows
 592:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				that is was unblocked due to its required bits matching, rather
 593:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				than because it timed out. */
 594:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				vTaskRemoveFromUnorderedEventList( pxListItem, pxEventBits->uxEventBits | eventUNBLOCKED_DUE_TO
 595:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			}
 596:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 597:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			/* Move onto the next list item.  Note pxListItem->pxNext is not
 598:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			used here as the list item may have been removed from the event list
 599:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			and inserted into the ready/pending reading list. */
 600:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			pxListItem = pxNext;
 601:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		}
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s 			page 12


 602:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 603:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		/* Clear any bits that matched when the eventCLEAR_EVENTS_ON_EXIT_BIT
 604:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		bit was set in the control word. */
 605:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		pxEventBits->uxEventBits &= ~uxBitsToClear;
 606:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	}
 607:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	( void ) xTaskResumeAll();
 608:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 609:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	return pxEventBits->uxEventBits;
 610:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** }
 611:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** /*-----------------------------------------------------------*/
 612:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 613:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** void vEventGroupDelete( EventGroupHandle_t xEventGroup )
 614:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** {
 615:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** EventGroup_t *pxEventBits = xEventGroup;
 616:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** const List_t *pxTasksWaitingForBits = &( pxEventBits->xTasksWaitingForBits );
 617:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 618:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	vTaskSuspendAll();
 619:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	{
 620:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		traceEVENT_GROUP_DELETE( xEventGroup );
 621:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 622:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		while( listCURRENT_LIST_LENGTH( pxTasksWaitingForBits ) > ( UBaseType_t ) 0 )
 623:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		{
 624:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			/* Unblock the task, returning 0 as the event list is being deleted
 625:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			and cannot therefore have any bits set. */
 626:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			configASSERT( pxTasksWaitingForBits->xListEnd.pxNext != ( const ListItem_t * ) &( pxTasksWaiting
 627:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			vTaskRemoveFromUnorderedEventList( pxTasksWaitingForBits->xListEnd.pxNext, eventUNBLOCKED_DUE_TO
 628:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		}
 629:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 630:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		#if( ( configSUPPORT_DYNAMIC_ALLOCATION == 1 ) && ( configSUPPORT_STATIC_ALLOCATION == 0 ) )
 631:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		{
 632:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			/* The event group can only have been allocated dynamically - free
 633:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			it again. */
 634:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			vPortFree( pxEventBits );
 635:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		}
 636:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		#elif( ( configSUPPORT_DYNAMIC_ALLOCATION == 1 ) && ( configSUPPORT_STATIC_ALLOCATION == 1 ) )
 637:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		{
 638:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			/* The event group could have been allocated statically or
 639:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			dynamically, so check before attempting to free the memory. */
 640:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			if( pxEventBits->ucStaticallyAllocated == ( uint8_t ) pdFALSE )
 641:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			{
 642:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				vPortFree( pxEventBits );
 643:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			}
 644:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			else
 645:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			{
 646:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				mtCOVERAGE_TEST_MARKER();
 647:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			}
 648:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		}
 649:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		#endif /* configSUPPORT_DYNAMIC_ALLOCATION */
 650:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	}
 651:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	( void ) xTaskResumeAll();
 652:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** }
 653:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** /*-----------------------------------------------------------*/
 654:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 655:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** /* For internal use only - execute a 'set bits' command that was pended from
 656:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** an interrupt. */
 657:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** void vEventGroupSetBitsCallback( void *pvEventGroup, const uint32_t ulBitsToSet )
 658:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** {
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s 			page 13


 659:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	( void ) xEventGroupSetBits( pvEventGroup, ( EventBits_t ) ulBitsToSet ); /*lint !e9079 Can't avoi
 660:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** }
 661:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** /*-----------------------------------------------------------*/
 662:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 663:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** /* For internal use only - execute a 'clear bits' command that was pended from
 664:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** an interrupt. */
 665:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** void vEventGroupClearBitsCallback( void *pvEventGroup, const uint32_t ulBitsToClear )
 666:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** {
 667:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	( void ) xEventGroupClearBits( pvEventGroup, ( EventBits_t ) ulBitsToClear ); /*lint !e9079 Can't 
 668:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** }
 669:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** /*-----------------------------------------------------------*/
 670:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 671:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** static BaseType_t prvTestWaitCondition( const EventBits_t uxCurrentEventBits, const EventBits_t uxB
 672:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** {
  28              		.loc 1 672 1 view -0
  29              		.cfi_startproc
  30              		@ args = 0, pretend = 0, frame = 0
  31              		@ frame_needed = 0, uses_anonymous_args = 0
  32              		@ link register save eliminated.
 673:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** BaseType_t xWaitConditionMet = pdFALSE;
  33              		.loc 1 673 1 view .LVU1
 674:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 675:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	if( xWaitForAllBits == pdFALSE )
  34              		.loc 1 675 2 view .LVU2
  35              		.loc 1 675 4 is_stmt 0 view .LVU3
  36 0000 1AB9     		cbnz	r2, .L2
 676:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	{
 677:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		/* Task only has to wait for one bit within uxBitsToWaitFor to be
 678:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		set.  Is one already set? */
 679:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		if( ( uxCurrentEventBits & uxBitsToWaitFor ) != ( EventBits_t ) 0 )
  37              		.loc 1 679 3 is_stmt 1 view .LVU4
  38              		.loc 1 679 5 is_stmt 0 view .LVU5
  39 0002 0842     		tst	r0, r1
  40 0004 05D1     		bne	.L4
 673:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
  41              		.loc 1 673 12 view .LVU6
  42 0006 1046     		mov	r0, r2
  43              	.LVL1:
 673:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
  44              		.loc 1 673 12 view .LVU7
  45 0008 7047     		bx	lr
  46              	.LVL2:
  47              	.L2:
 680:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		{
 681:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			xWaitConditionMet = pdTRUE;
 682:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		}
 683:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		else
 684:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		{
 685:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			mtCOVERAGE_TEST_MARKER();
 686:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		}
 687:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	}
 688:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	else
 689:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	{
 690:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		/* Task has to wait for all the bits in uxBitsToWaitFor to be set.
 691:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		Are they set already? */
 692:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		if( ( uxCurrentEventBits & uxBitsToWaitFor ) == uxBitsToWaitFor )
  48              		.loc 1 692 3 is_stmt 1 view .LVU8
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s 			page 14


  49              		.loc 1 692 5 is_stmt 0 view .LVU9
  50 000a 8143     		bics	r1, r1, r0
  51              	.LVL3:
  52              		.loc 1 692 5 view .LVU10
  53 000c 03D0     		beq	.L5
 673:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
  54              		.loc 1 673 12 view .LVU11
  55 000e 0020     		movs	r0, #0
  56              	.LVL4:
 673:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
  57              		.loc 1 673 12 view .LVU12
  58 0010 7047     		bx	lr
  59              	.LVL5:
  60              	.L4:
 681:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		}
  61              		.loc 1 681 22 view .LVU13
  62 0012 0120     		movs	r0, #1
  63              	.LVL6:
 681:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		}
  64              		.loc 1 681 22 view .LVU14
  65 0014 7047     		bx	lr
  66              	.LVL7:
  67              	.L5:
 693:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		{
 694:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			xWaitConditionMet = pdTRUE;
  68              		.loc 1 694 22 view .LVU15
  69 0016 0120     		movs	r0, #1
  70              	.LVL8:
 695:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		}
 696:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		else
 697:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		{
 698:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			mtCOVERAGE_TEST_MARKER();
  71              		.loc 1 698 28 is_stmt 1 view .LVU16
 699:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		}
 700:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	}
 701:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 702:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	return xWaitConditionMet;
  72              		.loc 1 702 2 view .LVU17
 703:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** }
  73              		.loc 1 703 1 is_stmt 0 view .LVU18
  74 0018 7047     		bx	lr
  75              		.cfi_endproc
  76              	.LFE15:
  78              		.section	.text.xEventGroupCreateStatic,"ax",%progbits
  79              		.align	1
  80              		.global	xEventGroupCreateStatic
  81              		.syntax unified
  82              		.thumb
  83              		.thumb_func
  85              	xEventGroupCreateStatic:
  86              	.LVL9:
  87              	.LFB4:
  94:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	EventGroup_t *pxEventBits;
  88              		.loc 1 94 2 is_stmt 1 view -0
  89              		.cfi_startproc
  90              		@ args = 0, pretend = 0, frame = 8
  91              		@ frame_needed = 0, uses_anonymous_args = 0
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s 			page 15


  95:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
  92              		.loc 1 95 2 view .LVU20
  98:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
  93              		.loc 1 98 3 view .LVU21
  94 0000 80B1     		cbz	r0, .L13
  94:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	EventGroup_t *pxEventBits;
  95              		.loc 1 94 2 is_stmt 0 view .LVU22
  96 0002 10B5     		push	{r4, lr}
  97              	.LCFI0:
  98              		.cfi_def_cfa_offset 8
  99              		.cfi_offset 4, -8
 100              		.cfi_offset 14, -4
 101 0004 82B0     		sub	sp, sp, #8
 102              	.LCFI1:
 103              		.cfi_def_cfa_offset 16
 104 0006 0446     		mov	r4, r0
  98:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 105              		.loc 1 98 37 is_stmt 1 discriminator 2 view .LVU23
 106              	.LBB36:
 105:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			configASSERT( xSize == sizeof( EventGroup_t ) );
 107              		.loc 1 105 4 view .LVU24
 105:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			configASSERT( xSize == sizeof( EventGroup_t ) );
 108              		.loc 1 105 20 is_stmt 0 view .LVU25
 109 0008 2023     		movs	r3, #32
 110 000a 0193     		str	r3, [sp, #4]
 106:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		} /*lint !e529 xSize is referenced if configASSERT() is defined. */
 111              		.loc 1 106 4 is_stmt 1 view .LVU26
 112 000c 019B     		ldr	r3, [sp, #4]
 113 000e 202B     		cmp	r3, #32
 114 0010 11D0     		beq	.L9
 106:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		} /*lint !e529 xSize is referenced if configASSERT() is defined. */
 115              		.loc 1 106 4 discriminator 1 view .LVU27
 116              	.LBB37:
 117              	.LBI37:
 118              		.file 2 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h"
   1:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /*
   2:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * FreeRTOS Kernel V10.3.1
   3:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * Copyright (C) 2020 Amazon.com, Inc. or its affiliates.  All Rights Reserved.
   4:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  *
   5:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * Permission is hereby granted, free of charge, to any person obtaining a copy of
   6:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * this software and associated documentation files (the "Software"), to deal in
   7:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * the Software without restriction, including without limitation the rights to
   8:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of
   9:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * the Software, and to permit persons to whom the Software is furnished to do so,
  10:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * subject to the following conditions:
  11:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  *
  12:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * The above copyright notice and this permission notice shall be included in all
  13:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * copies or substantial portions of the Software.
  14:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  *
  15:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
  16:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS
  17:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR
  18:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER
  19:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
  20:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
  21:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  *
  22:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * http://www.FreeRTOS.org
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s 			page 16


  23:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * http://aws.amazon.com/freertos
  24:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  *
  25:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * 1 tab == 4 spaces!
  26:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  */
  27:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
  28:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
  29:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #ifndef PORTMACRO_H
  30:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define PORTMACRO_H
  31:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
  32:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #ifdef __cplusplus
  33:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** extern "C" {
  34:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #endif
  35:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
  36:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /*-----------------------------------------------------------
  37:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * Port specific definitions.
  38:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  *
  39:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * The settings in this file configure FreeRTOS correctly for the
  40:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * given hardware and compiler.
  41:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  *
  42:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * These settings should not be altered.
  43:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  *-----------------------------------------------------------
  44:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  */
  45:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
  46:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /* Type definitions. */
  47:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portCHAR		char
  48:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portFLOAT		float
  49:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portDOUBLE		double
  50:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portLONG		long
  51:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portSHORT		short
  52:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portSTACK_TYPE	uint32_t
  53:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portBASE_TYPE	long
  54:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
  55:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** typedef portSTACK_TYPE StackType_t;
  56:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** typedef long BaseType_t;
  57:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** typedef unsigned long UBaseType_t;
  58:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
  59:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #if( configUSE_16_BIT_TICKS == 1 )
  60:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	typedef uint16_t TickType_t;
  61:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	#define portMAX_DELAY ( TickType_t ) 0xffff
  62:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #else
  63:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	typedef uint32_t TickType_t;
  64:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	#define portMAX_DELAY ( TickType_t ) 0xffffffffUL
  65:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
  66:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	/* 32-bit tick type on a 32-bit architecture, so reads of the tick count do
  67:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	not need to be guarded with a critical section. */
  68:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	#define portTICK_TYPE_IS_ATOMIC 1
  69:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #endif
  70:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /*-----------------------------------------------------------*/
  71:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
  72:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /* Architecture specifics. */
  73:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portSTACK_GROWTH			( -1 )
  74:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portTICK_PERIOD_MS			( ( TickType_t ) 1000 / configTICK_RATE_HZ )
  75:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portBYTE_ALIGNMENT			8
  76:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /*-----------------------------------------------------------*/
  77:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
  78:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /* Scheduler utilities. */
  79:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portYIELD() 															\
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s 			page 17


  80:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {																				\
  81:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	/* Set a PendSV to request a context switch. */								\
  82:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	portNVIC_INT_CTRL_REG = portNVIC_PENDSVSET_BIT;								\
  83:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 																				\
  84:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	/* Barriers are normally not required but do ensure the code is completely	\
  85:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	within the specified behaviour for the architecture. */						\
  86:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	__asm volatile( "dsb" ::: "memory" );										\
  87:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	__asm volatile( "isb" );													\
  88:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** }
  89:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
  90:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portNVIC_INT_CTRL_REG		( * ( ( volatile uint32_t * ) 0xe000ed04 ) )
  91:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portNVIC_PENDSVSET_BIT		( 1UL << 28UL )
  92:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portEND_SWITCHING_ISR( xSwitchRequired ) if( xSwitchRequired != pdFALSE ) portYIELD()
  93:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portYIELD_FROM_ISR( x ) portEND_SWITCHING_ISR( x )
  94:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /*-----------------------------------------------------------*/
  95:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
  96:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /* Critical section management. */
  97:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** extern void vPortEnterCritical( void );
  98:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** extern void vPortExitCritical( void );
  99:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portSET_INTERRUPT_MASK_FROM_ISR()		ulPortRaiseBASEPRI()
 100:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portCLEAR_INTERRUPT_MASK_FROM_ISR(x)	vPortSetBASEPRI(x)
 101:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portDISABLE_INTERRUPTS()				vPortRaiseBASEPRI()
 102:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portENABLE_INTERRUPTS()					vPortSetBASEPRI(0)
 103:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portENTER_CRITICAL()					vPortEnterCritical()
 104:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portEXIT_CRITICAL()						vPortExitCritical()
 105:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 106:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /*-----------------------------------------------------------*/
 107:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 108:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /* Task function macros as described on the FreeRTOS.org WEB site.  These are
 109:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** not necessary for to use this port.  They are defined so the common demo files
 110:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** (which build with all the ports) will build. */
 111:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portTASK_FUNCTION_PROTO( vFunction, pvParameters ) void vFunction( void *pvParameters )
 112:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portTASK_FUNCTION( vFunction, pvParameters ) void vFunction( void *pvParameters )
 113:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /*-----------------------------------------------------------*/
 114:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 115:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /* Tickless idle/low power functionality. */
 116:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #ifndef portSUPPRESS_TICKS_AND_SLEEP
 117:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	extern void vPortSuppressTicksAndSleep( TickType_t xExpectedIdleTime );
 118:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	#define portSUPPRESS_TICKS_AND_SLEEP( xExpectedIdleTime ) vPortSuppressTicksAndSleep( xExpectedIdl
 119:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #endif
 120:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /*-----------------------------------------------------------*/
 121:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 122:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /* Architecture specific optimisations. */
 123:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #ifndef configUSE_PORT_OPTIMISED_TASK_SELECTION
 124:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	#define configUSE_PORT_OPTIMISED_TASK_SELECTION 1
 125:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #endif
 126:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 127:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #if configUSE_PORT_OPTIMISED_TASK_SELECTION == 1
 128:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 129:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	/* Generic helper function. */
 130:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	__attribute__( ( always_inline ) ) static inline uint8_t ucPortCountLeadingZeros( uint32_t ulBitma
 131:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	{
 132:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	uint8_t ucReturn;
 133:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 134:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 		__asm volatile ( "clz %0, %1" : "=r" ( ucReturn ) : "r" ( ulBitmap ) : "memory" );
 135:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 		return ucReturn;
 136:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	}
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s 			page 18


 137:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 138:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	/* Check the configuration. */
 139:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	#if( configMAX_PRIORITIES > 32 )
 140:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 		#error configUSE_PORT_OPTIMISED_TASK_SELECTION can only be set to 1 when configMAX_PRIORITIES is 
 141:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	#endif
 142:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 143:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	/* Store/clear the ready priorities in a bit map. */
 144:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	#define portRECORD_READY_PRIORITY( uxPriority, uxReadyPriorities ) ( uxReadyPriorities ) |= ( 1UL 
 145:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	#define portRESET_READY_PRIORITY( uxPriority, uxReadyPriorities ) ( uxReadyPriorities ) &= ~( 1UL 
 146:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 147:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	/*-----------------------------------------------------------*/
 148:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 149:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	#define portGET_HIGHEST_PRIORITY( uxTopPriority, uxReadyPriorities ) uxTopPriority = ( 31UL - ( ui
 150:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 151:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #endif /* configUSE_PORT_OPTIMISED_TASK_SELECTION */
 152:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 153:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /*-----------------------------------------------------------*/
 154:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 155:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #ifdef configASSERT
 156:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	void vPortValidateInterruptPriority( void );
 157:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	#define portASSERT_IF_INTERRUPT_PRIORITY_INVALID() 	vPortValidateInterruptPriority()
 158:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #endif
 159:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 160:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /* portNOP() is not required by this port. */
 161:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portNOP()
 162:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 163:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portINLINE	__inline
 164:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 165:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #ifndef portFORCE_INLINE
 166:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	#define portFORCE_INLINE inline __attribute__(( always_inline))
 167:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #endif
 168:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 169:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** portFORCE_INLINE static BaseType_t xPortIsInsideInterrupt( void )
 170:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 171:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** uint32_t ulCurrentInterrupt;
 172:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** BaseType_t xReturn;
 173:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 174:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	/* Obtain the number of the currently executing interrupt. */
 175:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	__asm volatile( "mrs %0, ipsr" : "=r"( ulCurrentInterrupt ) :: "memory" );
 176:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 177:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	if( ulCurrentInterrupt == 0 )
 178:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	{
 179:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 		xReturn = pdFALSE;
 180:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	}
 181:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	else
 182:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	{
 183:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 		xReturn = pdTRUE;
 184:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	}
 185:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 186:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	return xReturn;
 187:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** }
 188:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 189:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /*-----------------------------------------------------------*/
 190:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** portFORCE_INLINE static void vPortRaiseBASEPRI( void )
 119              		.loc 2 191 30 view .LVU28
 120              	.LBB38:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s 			page 19


 192:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** uint32_t ulNewBASEPRI;
 121              		.loc 2 193 1 view .LVU29
 194:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 195:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	__asm volatile
 122              		.loc 2 195 2 view .LVU30
 123              		.syntax unified
 124              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 125 0012 4FF05003 			mov r3, #80												
 126 0016 83F31188 		msr basepri, r3											
 127 001a BFF36F8F 		isb														
 128 001e BFF34F8F 		dsb														
 129              	
 130              	@ 0 "" 2
 131              		.thumb
 132              		.syntax unified
 133              	.L10:
 134              	.LBE38:
 135              	.LBE37:
 106:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		} /*lint !e529 xSize is referenced if configASSERT() is defined. */
 136              		.loc 1 106 4 discriminator 3 view .LVU31
 106:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		} /*lint !e529 xSize is referenced if configASSERT() is defined. */
 137              		.loc 1 106 4 discriminator 3 view .LVU32
 138 0022 FEE7     		b	.L10
 139              	.L13:
 140              	.LCFI2:
 141              		.cfi_def_cfa_offset 0
 142              		.cfi_restore 4
 143              		.cfi_restore 14
 106:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		} /*lint !e529 xSize is referenced if configASSERT() is defined. */
 144              		.loc 1 106 4 is_stmt 0 discriminator 3 view .LVU33
 145              	.LBE36:
  98:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 146              		.loc 1 98 3 is_stmt 1 discriminator 1 view .LVU34
 147              	.LBB39:
 148              	.LBI39:
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 149              		.loc 2 191 30 view .LVU35
 150              	.LBB40:
 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 151              		.loc 2 193 1 view .LVU36
 152              		.loc 2 195 2 view .LVU37
 153              		.syntax unified
 154              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 155 0024 4FF05003 			mov r3, #80												
 156 0028 83F31188 		msr basepri, r3											
 157 002c BFF36F8F 		isb														
 158 0030 BFF34F8F 		dsb														
 159              	
 160              	@ 0 "" 2
 161              		.thumb
 162              		.syntax unified
 163              	.L8:
 164              	.LBE40:
 165              	.LBE39:
  98:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 166              		.loc 1 98 3 discriminator 3 view .LVU38
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s 			page 20


  98:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 167              		.loc 1 98 3 discriminator 3 view .LVU39
 168 0034 FEE7     		b	.L8
 169              	.L9:
 170              	.LCFI3:
 171              		.cfi_def_cfa_offset 16
 172              		.cfi_offset 4, -8
 173              		.cfi_offset 14, -4
 174              	.LBB41:
 106:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		} /*lint !e529 xSize is referenced if configASSERT() is defined. */
 175              		.loc 1 106 51 discriminator 2 view .LVU40
 176              	.LBE41:
 111:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 177              		.loc 1 111 3 view .LVU41
 178              	.LVL10:
 113:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		{
 179              		.loc 1 113 3 view .LVU42
 115:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			vListInitialise( &( pxEventBits->xTasksWaitingForBits ) );
 180              		.loc 1 115 4 view .LVU43
 115:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			vListInitialise( &( pxEventBits->xTasksWaitingForBits ) );
 181              		.loc 1 115 29 is_stmt 0 view .LVU44
 182 0036 0023     		movs	r3, #0
 183 0038 40F8043B 		str	r3, [r0], #4
 184              	.LVL11:
 116:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 185              		.loc 1 116 4 is_stmt 1 view .LVU45
 186 003c FFF7FEFF 		bl	vListInitialise
 187              	.LVL12:
 123:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			}
 188              		.loc 1 123 5 view .LVU46
 123:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			}
 189              		.loc 1 123 40 is_stmt 0 view .LVU47
 190 0040 0123     		movs	r3, #1
 191 0042 2377     		strb	r3, [r4, #28]
 127:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		}
 192              		.loc 1 127 42 is_stmt 1 view .LVU48
 134:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		}
 193              		.loc 1 134 36 view .LVU49
 137:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	}
 194              		.loc 1 137 3 view .LVU50
 138:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 195              		.loc 1 138 2 is_stmt 0 view .LVU51
 196 0044 2046     		mov	r0, r4
 197 0046 02B0     		add	sp, sp, #8
 198              	.LCFI4:
 199              		.cfi_def_cfa_offset 8
 200              		@ sp needed
 201 0048 10BD     		pop	{r4, pc}
 138:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 202              		.loc 1 138 2 view .LVU52
 203              		.cfi_endproc
 204              	.LFE4:
 206              		.section	.text.xEventGroupCreate,"ax",%progbits
 207              		.align	1
 208              		.global	xEventGroupCreate
 209              		.syntax unified
 210              		.thumb
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s 			page 21


 211              		.thumb_func
 213              	xEventGroupCreate:
 214              	.LFB5:
 146:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	EventGroup_t *pxEventBits;
 215              		.loc 1 146 2 is_stmt 1 view -0
 216              		.cfi_startproc
 217              		@ args = 0, pretend = 0, frame = 0
 218              		@ frame_needed = 0, uses_anonymous_args = 0
 219 0000 38B5     		push	{r3, r4, r5, lr}
 220              	.LCFI5:
 221              		.cfi_def_cfa_offset 16
 222              		.cfi_offset 3, -16
 223              		.cfi_offset 4, -12
 224              		.cfi_offset 5, -8
 225              		.cfi_offset 14, -4
 147:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 226              		.loc 1 147 2 view .LVU54
 162:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 227              		.loc 1 162 3 view .LVU55
 162:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 228              		.loc 1 162 36 is_stmt 0 view .LVU56
 229 0002 2020     		movs	r0, #32
 230 0004 FFF7FEFF 		bl	pvPortMalloc
 231              	.LVL13:
 164:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		{
 232              		.loc 1 164 3 is_stmt 1 view .LVU57
 164:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		{
 233              		.loc 1 164 5 is_stmt 0 view .LVU58
 234 0008 0446     		mov	r4, r0
 235 000a 28B1     		cbz	r0, .L14
 166:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			vListInitialise( &( pxEventBits->xTasksWaitingForBits ) );
 236              		.loc 1 166 4 is_stmt 1 view .LVU59
 166:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			vListInitialise( &( pxEventBits->xTasksWaitingForBits ) );
 237              		.loc 1 166 29 is_stmt 0 view .LVU60
 238 000c 0025     		movs	r5, #0
 239 000e 40F8045B 		str	r5, [r0], #4
 240              	.LVL14:
 167:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 241              		.loc 1 167 4 is_stmt 1 view .LVU61
 242 0012 FFF7FEFF 		bl	vListInitialise
 243              	.LVL15:
 174:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			}
 244              		.loc 1 174 5 view .LVU62
 174:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			}
 245              		.loc 1 174 40 is_stmt 0 view .LVU63
 246 0016 2577     		strb	r5, [r4, #28]
 178:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		}
 247              		.loc 1 178 42 is_stmt 1 view .LVU64
 182:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		}
 248              		.loc 1 182 36 view .LVU65
 185:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	}
 249              		.loc 1 185 3 view .LVU66
 250              	.L14:
 186:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 251              		.loc 1 186 2 is_stmt 0 view .LVU67
 252 0018 2046     		mov	r0, r4
 253 001a 38BD     		pop	{r3, r4, r5, pc}
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s 			page 22


 186:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 254              		.loc 1 186 2 view .LVU68
 255              		.cfi_endproc
 256              	.LFE5:
 258              		.section	.text.xEventGroupWaitBits,"ax",%progbits
 259              		.align	1
 260              		.global	xEventGroupWaitBits
 261              		.syntax unified
 262              		.thumb
 263              		.thumb_func
 265              	xEventGroupWaitBits:
 266              	.LVL16:
 267              	.LFB7:
 312:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** EventGroup_t *pxEventBits = xEventGroup;
 268              		.loc 1 312 1 is_stmt 1 view -0
 269              		.cfi_startproc
 270              		@ args = 4, pretend = 0, frame = 0
 271              		@ frame_needed = 0, uses_anonymous_args = 0
 313:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** EventBits_t uxReturn, uxControlBits = 0;
 272              		.loc 1 313 1 view .LVU70
 314:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** BaseType_t xWaitConditionMet, xAlreadyYielded;
 273              		.loc 1 314 1 view .LVU71
 315:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** BaseType_t xTimeoutOccurred = pdFALSE;
 274              		.loc 1 315 1 view .LVU72
 316:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 275              		.loc 1 316 1 view .LVU73
 320:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	configASSERT( ( uxBitsToWaitFor & eventEVENT_BITS_CONTROL_BYTES ) == 0 );
 276              		.loc 1 320 2 view .LVU74
 277 0000 88B1     		cbz	r0, .L38
 312:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** EventGroup_t *pxEventBits = xEventGroup;
 278              		.loc 1 312 1 is_stmt 0 view .LVU75
 279 0002 2DE9F041 		push	{r4, r5, r6, r7, r8, lr}
 280              	.LCFI6:
 281              		.cfi_def_cfa_offset 24
 282              		.cfi_offset 4, -24
 283              		.cfi_offset 5, -20
 284              		.cfi_offset 6, -16
 285              		.cfi_offset 7, -12
 286              		.cfi_offset 8, -8
 287              		.cfi_offset 14, -4
 288 0006 0C46     		mov	r4, r1
 289 0008 1646     		mov	r6, r2
 290 000a 1D46     		mov	r5, r3
 291 000c 0746     		mov	r7, r0
 320:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	configASSERT( ( uxBitsToWaitFor & eventEVENT_BITS_CONTROL_BYTES ) == 0 );
 292              		.loc 1 320 29 is_stmt 1 discriminator 2 view .LVU76
 321:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	configASSERT( uxBitsToWaitFor != 0 );
 293              		.loc 1 321 2 view .LVU77
 294 000e B1F1807F 		cmp	r1, #16777216
 295 0012 11D3     		bcc	.L20
 321:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	configASSERT( uxBitsToWaitFor != 0 );
 296              		.loc 1 321 2 discriminator 1 view .LVU78
 297              	.LBB42:
 298              	.LBI42:
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 299              		.loc 2 191 30 view .LVU79
 300              	.LBB43:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s 			page 23


 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 301              		.loc 2 193 1 view .LVU80
 302              		.loc 2 195 2 view .LVU81
 303              		.syntax unified
 304              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 305 0014 4FF05003 			mov r3, #80												
 306 0018 83F31188 		msr basepri, r3											
 307 001c BFF36F8F 		isb														
 308 0020 BFF34F8F 		dsb														
 309              	
 310              	@ 0 "" 2
 311              	.LVL17:
 312              		.thumb
 313              		.syntax unified
 314              	.L21:
 315              		.loc 2 195 2 is_stmt 0 view .LVU82
 316              	.LBE43:
 317              	.LBE42:
 321:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	configASSERT( uxBitsToWaitFor != 0 );
 318              		.loc 1 321 2 is_stmt 1 discriminator 3 view .LVU83
 321:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	configASSERT( uxBitsToWaitFor != 0 );
 319              		.loc 1 321 2 discriminator 3 view .LVU84
 320 0024 FEE7     		b	.L21
 321              	.LVL18:
 322              	.L38:
 323              	.LCFI7:
 324              		.cfi_def_cfa_offset 0
 325              		.cfi_restore 4
 326              		.cfi_restore 5
 327              		.cfi_restore 6
 328              		.cfi_restore 7
 329              		.cfi_restore 8
 330              		.cfi_restore 14
 320:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	configASSERT( ( uxBitsToWaitFor & eventEVENT_BITS_CONTROL_BYTES ) == 0 );
 331              		.loc 1 320 2 discriminator 1 view .LVU85
 332              	.LBB44:
 333              	.LBI44:
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 334              		.loc 2 191 30 view .LVU86
 335              	.LBB45:
 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 336              		.loc 2 193 1 view .LVU87
 337              		.loc 2 195 2 view .LVU88
 338              		.syntax unified
 339              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 340 0026 4FF05003 			mov r3, #80												
 341 002a 83F31188 		msr basepri, r3											
 342 002e BFF36F8F 		isb														
 343 0032 BFF34F8F 		dsb														
 344              	
 345              	@ 0 "" 2
 346              	.LVL19:
 347              		.thumb
 348              		.syntax unified
 349              	.L19:
 350              		.loc 2 195 2 is_stmt 0 view .LVU89
 351              	.LBE45:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s 			page 24


 352              	.LBE44:
 320:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	configASSERT( ( uxBitsToWaitFor & eventEVENT_BITS_CONTROL_BYTES ) == 0 );
 353              		.loc 1 320 2 is_stmt 1 discriminator 3 view .LVU90
 320:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	configASSERT( ( uxBitsToWaitFor & eventEVENT_BITS_CONTROL_BYTES ) == 0 );
 354              		.loc 1 320 2 discriminator 3 view .LVU91
 355 0036 FEE7     		b	.L19
 356              	.LVL20:
 357              	.L20:
 358              	.LCFI8:
 359              		.cfi_def_cfa_offset 24
 360              		.cfi_offset 4, -24
 361              		.cfi_offset 5, -20
 362              		.cfi_offset 6, -16
 363              		.cfi_offset 7, -12
 364              		.cfi_offset 8, -8
 365              		.cfi_offset 14, -4
 321:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	configASSERT( uxBitsToWaitFor != 0 );
 366              		.loc 1 321 74 discriminator 2 view .LVU92
 322:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	#if ( ( INCLUDE_xTaskGetSchedulerState == 1 ) || ( configUSE_TIMERS == 1 ) )
 367              		.loc 1 322 2 view .LVU93
 368 0038 41B9     		cbnz	r1, .L22
 322:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	#if ( ( INCLUDE_xTaskGetSchedulerState == 1 ) || ( configUSE_TIMERS == 1 ) )
 369              		.loc 1 322 2 discriminator 1 view .LVU94
 370              	.LBB46:
 371              	.LBI46:
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 372              		.loc 2 191 30 view .LVU95
 373              	.LBB47:
 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 374              		.loc 2 193 1 view .LVU96
 375              		.loc 2 195 2 view .LVU97
 376              		.syntax unified
 377              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 378 003a 4FF05003 			mov r3, #80												
 379 003e 83F31188 		msr basepri, r3											
 380 0042 BFF36F8F 		isb														
 381 0046 BFF34F8F 		dsb														
 382              	
 383              	@ 0 "" 2
 384              	.LVL21:
 385              		.thumb
 386              		.syntax unified
 387              	.L23:
 388              		.loc 2 195 2 is_stmt 0 view .LVU98
 389              	.LBE47:
 390              	.LBE46:
 322:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	#if ( ( INCLUDE_xTaskGetSchedulerState == 1 ) || ( configUSE_TIMERS == 1 ) )
 391              		.loc 1 322 2 is_stmt 1 discriminator 3 view .LVU99
 322:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	#if ( ( INCLUDE_xTaskGetSchedulerState == 1 ) || ( configUSE_TIMERS == 1 ) )
 392              		.loc 1 322 2 discriminator 3 view .LVU100
 393 004a FEE7     		b	.L23
 394              	.LVL22:
 395              	.L22:
 322:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	#if ( ( INCLUDE_xTaskGetSchedulerState == 1 ) || ( configUSE_TIMERS == 1 ) )
 396              		.loc 1 322 38 discriminator 2 view .LVU101
 325:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	}
 397              		.loc 1 325 3 view .LVU102
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s 			page 25


 398 004c FFF7FEFF 		bl	xTaskGetSchedulerState
 399              	.LVL23:
 325:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	}
 400              		.loc 1 325 3 is_stmt 0 discriminator 1 view .LVU103
 401 0050 50B9     		cbnz	r0, .L24
 325:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	}
 402              		.loc 1 325 3 discriminator 2 view .LVU104
 403 0052 069B     		ldr	r3, [sp, #24]
 404 0054 43B1     		cbz	r3, .L24
 325:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	}
 405              		.loc 1 325 3 is_stmt 1 discriminator 7 view .LVU105
 406              	.LBB48:
 407              	.LBI48:
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 408              		.loc 2 191 30 view .LVU106
 409              	.LBB49:
 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 410              		.loc 2 193 1 view .LVU107
 411              		.loc 2 195 2 view .LVU108
 412              		.syntax unified
 413              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 414 0056 4FF05003 			mov r3, #80												
 415 005a 83F31188 		msr basepri, r3											
 416 005e BFF36F8F 		isb														
 417 0062 BFF34F8F 		dsb														
 418              	
 419              	@ 0 "" 2
 420              		.thumb
 421              		.syntax unified
 422              	.L25:
 423              	.LBE49:
 424              	.LBE48:
 325:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	}
 425              		.loc 1 325 3 discriminator 9 view .LVU109
 325:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	}
 426              		.loc 1 325 3 discriminator 9 view .LVU110
 427 0066 FEE7     		b	.L25
 428              	.L24:
 325:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	}
 429              		.loc 1 325 104 discriminator 8 view .LVU111
 329:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	{
 430              		.loc 1 329 2 view .LVU112
 431 0068 FFF7FEFF 		bl	vTaskSuspendAll
 432              	.LVL24:
 433              	.LBB50:
 331:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 434              		.loc 1 331 3 view .LVU113
 331:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 435              		.loc 1 331 21 is_stmt 0 view .LVU114
 436 006c D7F80080 		ldr	r8, [r7]
 437              	.LVL25:
 334:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 438              		.loc 1 334 3 is_stmt 1 view .LVU115
 334:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 439              		.loc 1 334 23 is_stmt 0 view .LVU116
 440 0070 2A46     		mov	r2, r5
 441 0072 2146     		mov	r1, r4
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s 			page 26


 442 0074 4046     		mov	r0, r8
 443 0076 FFF7FEFF 		bl	prvTestWaitCondition
 444              	.LVL26:
 336:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		{
 445              		.loc 1 336 3 is_stmt 1 view .LVU117
 336:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		{
 446              		.loc 1 336 5 is_stmt 0 view .LVU118
 447 007a F8B1     		cbz	r0, .L26
 340:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			xTicksToWait = ( TickType_t ) 0;
 448              		.loc 1 340 4 is_stmt 1 view .LVU119
 449              	.LVL27:
 341:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 450              		.loc 1 341 4 view .LVU120
 344:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			{
 451              		.loc 1 344 4 view .LVU121
 344:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			{
 452              		.loc 1 344 6 is_stmt 0 view .LVU122
 453 007c 8EB3     		cbz	r6, .L34
 346:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			}
 454              		.loc 1 346 5 is_stmt 1 view .LVU123
 346:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			}
 455              		.loc 1 346 30 is_stmt 0 view .LVU124
 456 007e 28EA0403 		bic	r3, r8, r4
 457 0082 3B60     		str	r3, [r7]
 341:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 458              		.loc 1 341 17 view .LVU125
 459 0084 0023     		movs	r3, #0
 460 0086 0693     		str	r3, [sp, #24]
 461              	.LVL28:
 462              	.L27:
 394:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		}
 463              		.loc 1 394 68 is_stmt 1 view .LVU126
 464              	.LBE50:
 397:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 465              		.loc 1 397 2 view .LVU127
 397:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 466              		.loc 1 397 20 is_stmt 0 view .LVU128
 467 0088 FFF7FEFF 		bl	xTaskResumeAll
 468              	.LVL29:
 399:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	{
 469              		.loc 1 399 2 is_stmt 1 view .LVU129
 399:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	{
 470              		.loc 1 399 4 is_stmt 0 view .LVU130
 471 008c 069B     		ldr	r3, [sp, #24]
 472 008e 93B1     		cbz	r3, .L17
 401:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		{
 473              		.loc 1 401 3 is_stmt 1 view .LVU131
 401:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		{
 474              		.loc 1 401 5 is_stmt 0 view .LVU132
 475 0090 48B9     		cbnz	r0, .L31
 403:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		}
 476              		.loc 1 403 4 is_stmt 1 view .LVU133
 477 0092 4FF0E023 		mov	r3, #-*********
 478 0096 4FF08052 		mov	r2, #*********
 479 009a C3F8042D 		str	r2, [r3, #3332]
 480              	.LVL30:
 403:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		}
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s 			page 27


 481              		.loc 1 403 4 view .LVU134
 482              		.syntax unified
 483              	@ 403 "Middlewares/Third_Party/FreeRTOS/Source/event_groups.c" 1
 484 009e BFF34F8F 		dsb
 485              	@ 0 "" 2
 403:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		}
 486              		.loc 1 403 4 view .LVU135
 487              	@ 403 "Middlewares/Third_Party/FreeRTOS/Source/event_groups.c" 1
 488 00a2 BFF36F8F 		isb
 489              	@ 0 "" 2
 403:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		}
 490              		.loc 1 403 26 view .LVU136
 491              		.thumb
 492              		.syntax unified
 493              	.L31:
 407:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		}
 494              		.loc 1 407 28 view .LVU137
 414:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 495              		.loc 1 414 3 view .LVU138
 414:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 496              		.loc 1 414 14 is_stmt 0 view .LVU139
 497 00a6 FFF7FEFF 		bl	uxTaskResetEventItemValue
 498              	.LVL31:
 414:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 499              		.loc 1 414 14 view .LVU140
 500 00aa 8046     		mov	r8, r0
 501              	.LVL32:
 416:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		{
 502              		.loc 1 416 3 is_stmt 1 view .LVU141
 416:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		{
 503              		.loc 1 416 5 is_stmt 0 view .LVU142
 504 00ac 10F0007F 		tst	r0, #33554432
 505 00b0 1AD0     		beq	.L39
 506              	.LVL33:
 507              	.L32:
 447:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 508              		.loc 1 447 3 is_stmt 1 view .LVU143
 450:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	}
 509              		.loc 1 450 3 view .LVU144
 450:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	}
 510              		.loc 1 450 12 is_stmt 0 view .LVU145
 511 00b2 28F07F48 		bic	r8, r8, #-16777216
 512              	.LVL34:
 452:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 513              		.loc 1 452 2 is_stmt 1 view .LVU146
 455:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 514              		.loc 1 455 2 view .LVU147
 457:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** }
 515              		.loc 1 457 2 view .LVU148
 516              	.L17:
 458:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** /*-----------------------------------------------------------*/
 517              		.loc 1 458 1 is_stmt 0 view .LVU149
 518 00b6 4046     		mov	r0, r8
 519 00b8 BDE8F081 		pop	{r4, r5, r6, r7, r8, pc}
 520              	.LVL35:
 521              	.L26:
 522              	.LBB51:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s 			page 28


 353:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		{
 523              		.loc 1 353 8 is_stmt 1 view .LVU150
 353:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		{
 524              		.loc 1 353 10 is_stmt 0 view .LVU151
 525 00bc 069B     		ldr	r3, [sp, #24]
 526 00be 002B     		cmp	r3, #0
 527 00c0 E2D0     		beq	.L27
 366:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			{
 528              		.loc 1 366 4 is_stmt 1 view .LVU152
 366:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			{
 529              		.loc 1 366 6 is_stmt 0 view .LVU153
 530 00c2 5EB9     		cbnz	r6, .L35
 531              	.LBE51:
 314:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** BaseType_t xWaitConditionMet, xAlreadyYielded;
 532              		.loc 1 314 23 view .LVU154
 533 00c4 0021     		movs	r1, #0
 534              	.L28:
 535              	.LVL36:
 536              	.LBB52:
 372:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			}
 537              		.loc 1 372 29 is_stmt 1 view .LVU155
 375:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			{
 538              		.loc 1 375 4 view .LVU156
 375:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			{
 539              		.loc 1 375 6 is_stmt 0 view .LVU157
 540 00c6 0DB1     		cbz	r5, .L29
 377:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			}
 541              		.loc 1 377 5 is_stmt 1 view .LVU158
 377:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			}
 542              		.loc 1 377 19 is_stmt 0 view .LVU159
 543 00c8 41F08061 		orr	r1, r1, #67108864
 544              	.LVL37:
 545              	.L29:
 381:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			}
 546              		.loc 1 381 29 is_stmt 1 view .LVU160
 387:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 547              		.loc 1 387 4 view .LVU161
 548 00cc 069A     		ldr	r2, [sp, #24]
 549 00ce 2143     		orrs	r1, r1, r4
 550              	.LVL38:
 387:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 551              		.loc 1 387 4 is_stmt 0 view .LVU162
 552 00d0 381D     		adds	r0, r7, #4
 553              	.LVL39:
 387:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 554              		.loc 1 387 4 view .LVU163
 555 00d2 FFF7FEFF 		bl	vTaskPlaceOnUnorderedEventList
 556              	.LVL40:
 392:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 557              		.loc 1 392 4 is_stmt 1 view .LVU164
 392:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 558              		.loc 1 392 13 is_stmt 0 view .LVU165
 559 00d6 4FF00008 		mov	r8, #0
 560              	.LVL41:
 392:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 561              		.loc 1 392 13 view .LVU166
 562 00da D5E7     		b	.L27
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s 			page 29


 563              	.LVL42:
 564              	.L35:
 368:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			}
 565              		.loc 1 368 19 view .LVU167
 566 00dc 4FF08071 		mov	r1, #16777216
 567 00e0 F1E7     		b	.L28
 568              	.LVL43:
 569              	.L34:
 341:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 570              		.loc 1 341 17 view .LVU168
 571 00e2 0023     		movs	r3, #0
 572 00e4 0693     		str	r3, [sp, #24]
 573 00e6 CFE7     		b	.L27
 574              	.LVL44:
 575              	.L39:
 341:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 576              		.loc 1 341 17 view .LVU169
 577              	.LBE52:
 418:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			{
 578              		.loc 1 418 4 is_stmt 1 view .LVU170
 579 00e8 FFF7FEFF 		bl	vPortEnterCritical
 580              	.LVL45:
 421:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 581              		.loc 1 421 5 view .LVU171
 421:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 582              		.loc 1 421 14 is_stmt 0 view .LVU172
 583 00ec D7F80080 		ldr	r8, [r7]
 584              	.LVL46:
 425:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				{
 585              		.loc 1 425 5 is_stmt 1 view .LVU173
 425:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				{
 586              		.loc 1 425 9 is_stmt 0 view .LVU174
 587 00f0 2A46     		mov	r2, r5
 588 00f2 2146     		mov	r1, r4
 589 00f4 4046     		mov	r0, r8
 590 00f6 FFF7FEFF 		bl	prvTestWaitCondition
 591              	.LVL47:
 425:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				{
 592              		.loc 1 425 7 discriminator 1 view .LVU175
 593 00fa 18B1     		cbz	r0, .L33
 427:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 					{
 594              		.loc 1 427 6 is_stmt 1 view .LVU176
 427:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 					{
 595              		.loc 1 427 8 is_stmt 0 view .LVU177
 596 00fc 16B1     		cbz	r6, .L33
 429:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 					}
 597              		.loc 1 429 7 is_stmt 1 view .LVU178
 429:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 					}
 598              		.loc 1 429 32 is_stmt 0 view .LVU179
 599 00fe 28EA0404 		bic	r4, r8, r4
 600              	.LVL48:
 429:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 					}
 601              		.loc 1 429 32 view .LVU180
 602 0102 3C60     		str	r4, [r7]
 603              	.L33:
 438:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				}
 604              		.loc 1 438 30 is_stmt 1 view .LVU181
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s 			page 30


 440:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			}
 605              		.loc 1 440 5 view .LVU182
 606              	.LVL49:
 442:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		}
 607              		.loc 1 442 4 view .LVU183
 608 0104 FFF7FEFF 		bl	vPortExitCritical
 609              	.LVL50:
 610 0108 D3E7     		b	.L32
 611              		.cfi_endproc
 612              	.LFE7:
 614              		.section	.text.xEventGroupClearBits,"ax",%progbits
 615              		.align	1
 616              		.global	xEventGroupClearBits
 617              		.syntax unified
 618              		.thumb
 619              		.thumb_func
 621              	xEventGroupClearBits:
 622              	.LVL51:
 623              	.LFB8:
 462:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** EventGroup_t *pxEventBits = xEventGroup;
 624              		.loc 1 462 1 view -0
 625              		.cfi_startproc
 626              		@ args = 0, pretend = 0, frame = 0
 627              		@ frame_needed = 0, uses_anonymous_args = 0
 463:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** EventBits_t uxReturn;
 628              		.loc 1 463 1 view .LVU185
 464:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 629              		.loc 1 464 1 view .LVU186
 468:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	configASSERT( ( uxBitsToClear & eventEVENT_BITS_CONTROL_BYTES ) == 0 );
 630              		.loc 1 468 2 view .LVU187
 631 0000 70B1     		cbz	r0, .L47
 462:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** EventGroup_t *pxEventBits = xEventGroup;
 632              		.loc 1 462 1 is_stmt 0 view .LVU188
 633 0002 70B5     		push	{r4, r5, r6, lr}
 634              	.LCFI9:
 635              		.cfi_def_cfa_offset 16
 636              		.cfi_offset 4, -16
 637              		.cfi_offset 5, -12
 638              		.cfi_offset 6, -8
 639              		.cfi_offset 14, -4
 640 0004 0C46     		mov	r4, r1
 641 0006 0546     		mov	r5, r0
 468:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	configASSERT( ( uxBitsToClear & eventEVENT_BITS_CONTROL_BYTES ) == 0 );
 642              		.loc 1 468 29 is_stmt 1 discriminator 2 view .LVU189
 469:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 643              		.loc 1 469 2 view .LVU190
 644 0008 B1F1807F 		cmp	r1, #16777216
 645 000c 11D3     		bcc	.L43
 469:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 646              		.loc 1 469 2 discriminator 1 view .LVU191
 647              	.LBB53:
 648              	.LBI53:
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 649              		.loc 2 191 30 view .LVU192
 650              	.LBB54:
 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 651              		.loc 2 193 1 view .LVU193
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s 			page 31


 652              		.loc 2 195 2 view .LVU194
 653              		.syntax unified
 654              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 655 000e 4FF05003 			mov r3, #80												
 656 0012 83F31188 		msr basepri, r3											
 657 0016 BFF36F8F 		isb														
 658 001a BFF34F8F 		dsb														
 659              	
 660              	@ 0 "" 2
 661              		.thumb
 662              		.syntax unified
 663              	.L44:
 664              	.LBE54:
 665              	.LBE53:
 469:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 666              		.loc 1 469 2 discriminator 3 view .LVU195
 469:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 667              		.loc 1 469 2 discriminator 3 view .LVU196
 668 001e FEE7     		b	.L44
 669              	.L47:
 670              	.LCFI10:
 671              		.cfi_def_cfa_offset 0
 672              		.cfi_restore 4
 673              		.cfi_restore 5
 674              		.cfi_restore 6
 675              		.cfi_restore 14
 468:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	configASSERT( ( uxBitsToClear & eventEVENT_BITS_CONTROL_BYTES ) == 0 );
 676              		.loc 1 468 2 discriminator 1 view .LVU197
 677              	.LBB55:
 678              	.LBI55:
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 679              		.loc 2 191 30 view .LVU198
 680              	.LBB56:
 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 681              		.loc 2 193 1 view .LVU199
 682              		.loc 2 195 2 view .LVU200
 683              		.syntax unified
 684              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 685 0020 4FF05003 			mov r3, #80												
 686 0024 83F31188 		msr basepri, r3											
 687 0028 BFF36F8F 		isb														
 688 002c BFF34F8F 		dsb														
 689              	
 690              	@ 0 "" 2
 691              		.thumb
 692              		.syntax unified
 693              	.L42:
 694              	.LBE56:
 695              	.LBE55:
 468:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	configASSERT( ( uxBitsToClear & eventEVENT_BITS_CONTROL_BYTES ) == 0 );
 696              		.loc 1 468 2 discriminator 3 view .LVU201
 468:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	configASSERT( ( uxBitsToClear & eventEVENT_BITS_CONTROL_BYTES ) == 0 );
 697              		.loc 1 468 2 discriminator 3 view .LVU202
 698 0030 FEE7     		b	.L42
 699              	.L43:
 700              	.LCFI11:
 701              		.cfi_def_cfa_offset 16
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s 			page 32


 702              		.cfi_offset 4, -16
 703              		.cfi_offset 5, -12
 704              		.cfi_offset 6, -8
 705              		.cfi_offset 14, -4
 469:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 706              		.loc 1 469 72 discriminator 2 view .LVU203
 471:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	{
 707              		.loc 1 471 2 view .LVU204
 708 0032 FFF7FEFF 		bl	vPortEnterCritical
 709              	.LVL52:
 473:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 710              		.loc 1 473 60 view .LVU205
 477:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 711              		.loc 1 477 3 view .LVU206
 477:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 712              		.loc 1 477 12 is_stmt 0 view .LVU207
 713 0036 2E68     		ldr	r6, [r5]
 714              	.LVL53:
 480:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	}
 715              		.loc 1 480 3 is_stmt 1 view .LVU208
 480:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	}
 716              		.loc 1 480 28 is_stmt 0 view .LVU209
 717 0038 26EA0404 		bic	r4, r6, r4
 718              	.LVL54:
 480:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	}
 719              		.loc 1 480 28 view .LVU210
 720 003c 2C60     		str	r4, [r5]
 482:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 721              		.loc 1 482 2 is_stmt 1 view .LVU211
 722 003e FFF7FEFF 		bl	vPortExitCritical
 723              	.LVL55:
 484:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** }
 724              		.loc 1 484 2 view .LVU212
 485:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** /*-----------------------------------------------------------*/
 725              		.loc 1 485 1 is_stmt 0 view .LVU213
 726 0042 3046     		mov	r0, r6
 727 0044 70BD     		pop	{r4, r5, r6, pc}
 485:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** /*-----------------------------------------------------------*/
 728              		.loc 1 485 1 view .LVU214
 729              		.cfi_endproc
 730              	.LFE8:
 732              		.section	.text.vEventGroupClearBitsCallback,"ax",%progbits
 733              		.align	1
 734              		.global	vEventGroupClearBitsCallback
 735              		.syntax unified
 736              		.thumb
 737              		.thumb_func
 739              	vEventGroupClearBitsCallback:
 740              	.LVL56:
 741              	.LFB14:
 666:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	( void ) xEventGroupClearBits( pvEventGroup, ( EventBits_t ) ulBitsToClear ); /*lint !e9079 Can't 
 742              		.loc 1 666 1 is_stmt 1 view -0
 743              		.cfi_startproc
 744              		@ args = 0, pretend = 0, frame = 0
 745              		@ frame_needed = 0, uses_anonymous_args = 0
 666:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	( void ) xEventGroupClearBits( pvEventGroup, ( EventBits_t ) ulBitsToClear ); /*lint !e9079 Can't 
 746              		.loc 1 666 1 is_stmt 0 view .LVU216
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s 			page 33


 747 0000 08B5     		push	{r3, lr}
 748              	.LCFI12:
 749              		.cfi_def_cfa_offset 8
 750              		.cfi_offset 3, -8
 751              		.cfi_offset 14, -4
 667:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** }
 752              		.loc 1 667 2 is_stmt 1 view .LVU217
 667:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** }
 753              		.loc 1 667 11 is_stmt 0 view .LVU218
 754 0002 FFF7FEFF 		bl	xEventGroupClearBits
 755              	.LVL57:
 668:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** /*-----------------------------------------------------------*/
 756              		.loc 1 668 1 view .LVU219
 757 0006 08BD     		pop	{r3, pc}
 758              		.cfi_endproc
 759              	.LFE14:
 761              		.section	.text.xEventGroupClearBitsFromISR,"ax",%progbits
 762              		.align	1
 763              		.global	xEventGroupClearBitsFromISR
 764              		.syntax unified
 765              		.thumb
 766              		.thumb_func
 768              	xEventGroupClearBitsFromISR:
 769              	.LVL58:
 770              	.LFB9:
 491:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		BaseType_t xReturn;
 771              		.loc 1 491 2 is_stmt 1 view -0
 772              		.cfi_startproc
 773              		@ args = 0, pretend = 0, frame = 0
 774              		@ frame_needed = 0, uses_anonymous_args = 0
 491:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		BaseType_t xReturn;
 775              		.loc 1 491 2 is_stmt 0 view .LVU221
 776 0000 08B5     		push	{r3, lr}
 777              	.LCFI13:
 778              		.cfi_def_cfa_offset 8
 779              		.cfi_offset 3, -8
 780              		.cfi_offset 14, -4
 781 0002 0A46     		mov	r2, r1
 492:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 782              		.loc 1 492 3 is_stmt 1 view .LVU222
 494:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		xReturn = xTimerPendFunctionCallFromISR( vEventGroupClearBitsCallback, ( void * ) xEventGroup, ( 
 783              		.loc 1 494 69 view .LVU223
 495:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 784              		.loc 1 495 3 view .LVU224
 495:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 785              		.loc 1 495 13 is_stmt 0 view .LVU225
 786 0004 0023     		movs	r3, #0
 787 0006 0146     		mov	r1, r0
 788              	.LVL59:
 495:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 789              		.loc 1 495 13 view .LVU226
 790 0008 0148     		ldr	r0, .L52
 791              	.LVL60:
 495:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 792              		.loc 1 495 13 view .LVU227
 793 000a FFF7FEFF 		bl	xTimerPendFunctionCallFromISR
 794              	.LVL61:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s 			page 34


 497:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	}
 795              		.loc 1 497 3 is_stmt 1 view .LVU228
 498:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 796              		.loc 1 498 2 is_stmt 0 view .LVU229
 797 000e 08BD     		pop	{r3, pc}
 798              	.L53:
 799              		.align	2
 800              	.L52:
 801 0010 00000000 		.word	vEventGroupClearBitsCallback
 802              		.cfi_endproc
 803              	.LFE9:
 805              		.section	.text.xEventGroupGetBitsFromISR,"ax",%progbits
 806              		.align	1
 807              		.global	xEventGroupGetBitsFromISR
 808              		.syntax unified
 809              		.thumb
 810              		.thumb_func
 812              	xEventGroupGetBitsFromISR:
 813              	.LVL62:
 814              	.LFB10:
 504:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** UBaseType_t uxSavedInterruptStatus;
 815              		.loc 1 504 1 is_stmt 1 view -0
 816              		.cfi_startproc
 817              		@ args = 0, pretend = 0, frame = 0
 818              		@ frame_needed = 0, uses_anonymous_args = 0
 819              		@ link register save eliminated.
 505:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** EventGroup_t const * const pxEventBits = xEventGroup;
 820              		.loc 1 505 1 view .LVU231
 506:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** EventBits_t uxReturn;
 821              		.loc 1 506 1 view .LVU232
 507:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 822              		.loc 1 507 1 view .LVU233
 509:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	{
 823              		.loc 1 509 2 view .LVU234
 824              	.LBB57:
 825              	.LBI57:
 196:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	(
 197:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 		"	mov %0, %1												\n"	\
 198:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 		"	msr basepri, %0											\n" \
 199:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 		"	isb														\n" \
 200:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 		"	dsb														\n" \
 201:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 		:"=r" (ulNewBASEPRI) : "i" ( configMAX_SYSCALL_INTERRUPT_PRIORITY ) : "memory"
 202:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	);
 203:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** }
 204:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 205:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /*-----------------------------------------------------------*/
 206:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 207:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** portFORCE_INLINE static uint32_t ulPortRaiseBASEPRI( void )
 826              		.loc 2 207 34 view .LVU235
 827              	.LBB58:
 208:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 209:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** uint32_t ulOriginalBASEPRI, ulNewBASEPRI;
 828              		.loc 2 209 1 view .LVU236
 210:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 211:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	__asm volatile
 829              		.loc 2 211 2 view .LVU237
 830              		.syntax unified
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s 			page 35


 831              	@ 211 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 832 0000 EFF31183 			mrs r3, basepri											
 833 0004 4FF05002 		mov r2, #80												
 834 0008 82F31188 		msr basepri, r2											
 835 000c BFF36F8F 		isb														
 836 0010 BFF34F8F 		dsb														
 837              	
 838              	@ 0 "" 2
 839              	.LVL63:
 212:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	(
 213:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 		"	mrs %0, basepri											\n" \
 214:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 		"	mov %1, %2												\n"	\
 215:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 		"	msr basepri, %1											\n" \
 216:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 		"	isb														\n" \
 217:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 		"	dsb														\n" \
 218:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 		:"=r" (ulOriginalBASEPRI), "=r" (ulNewBASEPRI) : "i" ( configMAX_SYSCALL_INTERRUPT_PRIORITY ) : "
 219:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	);
 220:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 221:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	/* This return will not be reached but is necessary to prevent compiler
 222:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	warnings. */
 223:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	return ulOriginalBASEPRI;
 840              		.loc 2 223 2 view .LVU238
 841              		.loc 2 223 2 is_stmt 0 view .LVU239
 842              		.thumb
 843              		.syntax unified
 844              	.LBE58:
 845              	.LBE57:
 511:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	}
 846              		.loc 1 511 3 is_stmt 1 view .LVU240
 511:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	}
 847              		.loc 1 511 12 is_stmt 0 view .LVU241
 848 0014 0068     		ldr	r0, [r0]
 849              	.LVL64:
 513:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 850              		.loc 1 513 2 is_stmt 1 view .LVU242
 851              	.LBB59:
 852              	.LBI59:
 224:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** }
 225:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /*-----------------------------------------------------------*/
 226:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 227:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** portFORCE_INLINE static void vPortSetBASEPRI( uint32_t ulNewMaskValue )
 853              		.loc 2 227 30 view .LVU243
 854              	.LBB60:
 228:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 229:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	__asm volatile
 855              		.loc 2 229 2 view .LVU244
 856              		.syntax unified
 857              	@ 229 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 858 0016 83F31188 			msr basepri, r3	
 859              	@ 0 "" 2
 860              	.LVL65:
 861              		.loc 2 229 2 is_stmt 0 view .LVU245
 862              		.thumb
 863              		.syntax unified
 864              	.LBE60:
 865              	.LBE59:
 515:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** } /*lint !e818 EventGroupHandle_t is a typedef used in other functions to so can't be pointer to co
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s 			page 36


 866              		.loc 1 515 2 is_stmt 1 view .LVU246
 516:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** /*-----------------------------------------------------------*/
 867              		.loc 1 516 1 is_stmt 0 view .LVU247
 868 001a 7047     		bx	lr
 869              		.cfi_endproc
 870              	.LFE10:
 872              		.section	.text.xEventGroupSetBits,"ax",%progbits
 873              		.align	1
 874              		.global	xEventGroupSetBits
 875              		.syntax unified
 876              		.thumb
 877              		.thumb_func
 879              	xEventGroupSetBits:
 880              	.LVL66:
 881              	.LFB11:
 520:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** ListItem_t *pxListItem, *pxNext;
 882              		.loc 1 520 1 is_stmt 1 view -0
 883              		.cfi_startproc
 884              		@ args = 0, pretend = 0, frame = 0
 885              		@ frame_needed = 0, uses_anonymous_args = 0
 520:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** ListItem_t *pxListItem, *pxNext;
 886              		.loc 1 520 1 is_stmt 0 view .LVU249
 887 0000 F8B5     		push	{r3, r4, r5, r6, r7, lr}
 888              	.LCFI14:
 889              		.cfi_def_cfa_offset 24
 890              		.cfi_offset 3, -24
 891              		.cfi_offset 4, -20
 892              		.cfi_offset 5, -16
 893              		.cfi_offset 6, -12
 894              		.cfi_offset 7, -8
 895              		.cfi_offset 14, -4
 521:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** ListItem_t const *pxListEnd;
 896              		.loc 1 521 1 is_stmt 1 view .LVU250
 522:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** List_t const * pxList;
 897              		.loc 1 522 1 view .LVU251
 523:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** EventBits_t uxBitsToClear = 0, uxBitsWaitedFor, uxControlBits;
 898              		.loc 1 523 1 view .LVU252
 524:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** EventGroup_t *pxEventBits = xEventGroup;
 899              		.loc 1 524 1 view .LVU253
 900              	.LVL67:
 525:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** BaseType_t xMatchFound = pdFALSE;
 901              		.loc 1 525 1 view .LVU254
 526:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 902              		.loc 1 526 1 view .LVU255
 530:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	configASSERT( ( uxBitsToSet & eventEVENT_BITS_CONTROL_BYTES ) == 0 );
 903              		.loc 1 530 2 view .LVU256
 904 0002 68B1     		cbz	r0, .L67
 905 0004 0D46     		mov	r5, r1
 906 0006 0446     		mov	r4, r0
 530:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	configASSERT( ( uxBitsToSet & eventEVENT_BITS_CONTROL_BYTES ) == 0 );
 907              		.loc 1 530 29 discriminator 2 view .LVU257
 531:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 908              		.loc 1 531 2 view .LVU258
 909 0008 B1F1807F 		cmp	r1, #16777216
 910 000c 11D3     		bcc	.L58
 531:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 911              		.loc 1 531 2 discriminator 1 view .LVU259
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s 			page 37


 912              	.LBB61:
 913              	.LBI61:
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 914              		.loc 2 191 30 view .LVU260
 915              	.LBB62:
 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 916              		.loc 2 193 1 view .LVU261
 195:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	(
 917              		.loc 2 195 2 view .LVU262
 918              		.syntax unified
 919              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 920 000e 4FF05003 			mov r3, #80												
 921 0012 83F31188 		msr basepri, r3											
 922 0016 BFF36F8F 		isb														
 923 001a BFF34F8F 		dsb														
 924              	
 925              	@ 0 "" 2
 926              		.thumb
 927              		.syntax unified
 928              	.L59:
 929              	.LBE62:
 930              	.LBE61:
 531:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 931              		.loc 1 531 2 discriminator 3 view .LVU263
 531:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 932              		.loc 1 531 2 discriminator 3 view .LVU264
 933 001e FEE7     		b	.L59
 934              	.L67:
 530:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	configASSERT( ( uxBitsToSet & eventEVENT_BITS_CONTROL_BYTES ) == 0 );
 935              		.loc 1 530 2 discriminator 1 view .LVU265
 936              	.LBB63:
 937              	.LBI63:
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 938              		.loc 2 191 30 view .LVU266
 939              	.LBB64:
 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 940              		.loc 2 193 1 view .LVU267
 195:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	(
 941              		.loc 2 195 2 view .LVU268
 942              		.syntax unified
 943              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 944 0020 4FF05003 			mov r3, #80												
 945 0024 83F31188 		msr basepri, r3											
 946 0028 BFF36F8F 		isb														
 947 002c BFF34F8F 		dsb														
 948              	
 949              	@ 0 "" 2
 950              		.thumb
 951              		.syntax unified
 952              	.L57:
 953              	.LBE64:
 954              	.LBE63:
 530:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	configASSERT( ( uxBitsToSet & eventEVENT_BITS_CONTROL_BYTES ) == 0 );
 955              		.loc 1 530 2 discriminator 3 view .LVU269
 530:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	configASSERT( ( uxBitsToSet & eventEVENT_BITS_CONTROL_BYTES ) == 0 );
 956              		.loc 1 530 2 discriminator 3 view .LVU270
 957 0030 FEE7     		b	.L57
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s 			page 38


 958              	.L58:
 531:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 959              		.loc 1 531 70 discriminator 2 view .LVU271
 533:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	pxListEnd = listGET_END_MARKER( pxList ); /*lint !e826 !e740 !e9087 The mini list structure is use
 960              		.loc 1 533 2 view .LVU272
 961              	.LVL68:
 534:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	vTaskSuspendAll();
 962              		.loc 1 534 2 view .LVU273
 534:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	vTaskSuspendAll();
 963              		.loc 1 534 12 is_stmt 0 view .LVU274
 964 0032 00F10C06 		add	r6, r0, #12
 965              	.LVL69:
 535:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	{
 966              		.loc 1 535 2 is_stmt 1 view .LVU275
 967 0036 FFF7FEFF 		bl	vTaskSuspendAll
 968              	.LVL70:
 537:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 969              		.loc 1 537 56 view .LVU276
 539:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 970              		.loc 1 539 3 view .LVU277
 539:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 971              		.loc 1 539 14 is_stmt 0 view .LVU278
 972 003a 2069     		ldr	r0, [r4, #16]
 973              	.LVL71:
 542:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 974              		.loc 1 542 3 is_stmt 1 view .LVU279
 542:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 975              		.loc 1 542 14 is_stmt 0 view .LVU280
 976 003c 2368     		ldr	r3, [r4]
 542:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 977              		.loc 1 542 28 view .LVU281
 978 003e 2B43     		orrs	r3, r3, r5
 979 0040 2360     		str	r3, [r4]
 545:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		{
 980              		.loc 1 545 3 is_stmt 1 view .LVU282
 524:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** EventGroup_t *pxEventBits = xEventGroup;
 981              		.loc 1 524 13 is_stmt 0 view .LVU283
 982 0042 0027     		movs	r7, #0
 545:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		{
 983              		.loc 1 545 8 view .LVU284
 984 0044 0AE0     		b	.L60
 985              	.LVL72:
 986              	.L61:
 567:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			{
 987              		.loc 1 567 9 is_stmt 1 view .LVU285
 567:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			{
 988              		.loc 1 567 44 is_stmt 0 view .LVU286
 989 0046 2168     		ldr	r1, [r4]
 567:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			{
 990              		.loc 1 567 11 view .LVU287
 991 0048 32EA0101 		bics	r1, r2, r1
 992 004c 05D1     		bne	.L63
 567:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			{
 993              		.loc 1 567 11 view .LVU288
 994 004e 11E0     		b	.L62
 995              	.LVL73:
 996              	.L64:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s 			page 39


 586:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				}
 997              		.loc 1 586 30 is_stmt 1 view .LVU289
 594:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			}
 998              		.loc 1 594 5 view .LVU290
 594:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			}
 999              		.loc 1 594 63 is_stmt 0 view .LVU291
 1000 0050 2168     		ldr	r1, [r4]
 594:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			}
 1001              		.loc 1 594 5 view .LVU292
 1002 0052 41F00071 		orr	r1, r1, #33554432
 1003 0056 FFF7FEFF 		bl	vTaskRemoveFromUnorderedEventList
 1004              	.LVL74:
 1005              	.L63:
 520:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** ListItem_t *pxListItem, *pxNext;
 1006              		.loc 1 520 1 view .LVU293
 1007 005a 2846     		mov	r0, r5
 1008              	.LVL75:
 1009              	.L60:
 545:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		{
 1010              		.loc 1 545 21 is_stmt 1 view .LVU294
 1011 005c B042     		cmp	r0, r6
 1012 005e 0ED0     		beq	.L68
 547:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			uxBitsWaitedFor = listGET_LIST_ITEM_VALUE( pxListItem );
 1013              		.loc 1 547 4 view .LVU295
 547:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			uxBitsWaitedFor = listGET_LIST_ITEM_VALUE( pxListItem );
 1014              		.loc 1 547 11 is_stmt 0 view .LVU296
 1015 0060 4568     		ldr	r5, [r0, #4]
 1016              	.LVL76:
 548:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			xMatchFound = pdFALSE;
 1017              		.loc 1 548 4 is_stmt 1 view .LVU297
 548:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			xMatchFound = pdFALSE;
 1018              		.loc 1 548 20 is_stmt 0 view .LVU298
 1019 0062 0368     		ldr	r3, [r0]
 1020              	.LVL77:
 549:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 1021              		.loc 1 549 4 is_stmt 1 view .LVU299
 552:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			uxBitsWaitedFor &= ~eventEVENT_BITS_CONTROL_BYTES;
 1022              		.loc 1 552 4 view .LVU300
 553:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 1023              		.loc 1 553 4 view .LVU301
 553:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 1024              		.loc 1 553 20 is_stmt 0 view .LVU302
 1025 0064 23F07F42 		bic	r2, r3, #-16777216
 1026              	.LVL78:
 555:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			{
 1027              		.loc 1 555 4 is_stmt 1 view .LVU303
 555:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			{
 1028              		.loc 1 555 6 is_stmt 0 view .LVU304
 1029 0068 13F0806F 		tst	r3, #67108864
 1030 006c EBD1     		bne	.L61
 558:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				{
 1031              		.loc 1 558 5 is_stmt 1 view .LVU305
 558:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				{
 1032              		.loc 1 558 40 is_stmt 0 view .LVU306
 1033 006e 2168     		ldr	r1, [r4]
 558:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				{
 1034              		.loc 1 558 7 view .LVU307
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s 			page 40


 1035 0070 1142     		tst	r1, r2
 1036 0072 F2D0     		beq	.L63
 1037              	.L62:
 1038              	.LVL79:
 580:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				{
 1039              		.loc 1 580 5 is_stmt 1 view .LVU308
 580:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				{
 1040              		.loc 1 580 7 is_stmt 0 view .LVU309
 1041 0074 13F0807F 		tst	r3, #16777216
 1042 0078 EAD0     		beq	.L64
 582:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				}
 1043              		.loc 1 582 6 is_stmt 1 view .LVU310
 582:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				}
 1044              		.loc 1 582 20 is_stmt 0 view .LVU311
 1045 007a 1743     		orrs	r7, r7, r2
 1046              	.LVL80:
 582:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				}
 1047              		.loc 1 582 20 view .LVU312
 1048 007c E8E7     		b	.L64
 1049              	.LVL81:
 1050              	.L68:
 605:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	}
 1051              		.loc 1 605 3 is_stmt 1 view .LVU313
 605:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	}
 1052              		.loc 1 605 14 is_stmt 0 view .LVU314
 1053 007e 2368     		ldr	r3, [r4]
 605:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	}
 1054              		.loc 1 605 28 view .LVU315
 1055 0080 23EA0703 		bic	r3, r3, r7
 1056 0084 2360     		str	r3, [r4]
 607:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 1057              		.loc 1 607 2 is_stmt 1 view .LVU316
 607:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 1058              		.loc 1 607 11 is_stmt 0 view .LVU317
 1059 0086 FFF7FEFF 		bl	xTaskResumeAll
 1060              	.LVL82:
 609:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** }
 1061              		.loc 1 609 2 is_stmt 1 view .LVU318
 610:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** /*-----------------------------------------------------------*/
 1062              		.loc 1 610 1 is_stmt 0 view .LVU319
 1063 008a 2068     		ldr	r0, [r4]
 1064 008c F8BD     		pop	{r3, r4, r5, r6, r7, pc}
 610:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** /*-----------------------------------------------------------*/
 1065              		.loc 1 610 1 view .LVU320
 1066              		.cfi_endproc
 1067              	.LFE11:
 1069              		.section	.text.xEventGroupSync,"ax",%progbits
 1070              		.align	1
 1071              		.global	xEventGroupSync
 1072              		.syntax unified
 1073              		.thumb
 1074              		.thumb_func
 1076              	xEventGroupSync:
 1077              	.LVL83:
 1078              	.LFB6:
 192:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** EventBits_t uxOriginalBitValue, uxReturn;
 1079              		.loc 1 192 1 is_stmt 1 view -0
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s 			page 41


 1080              		.cfi_startproc
 1081              		@ args = 0, pretend = 0, frame = 0
 1082              		@ frame_needed = 0, uses_anonymous_args = 0
 193:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** EventGroup_t *pxEventBits = xEventGroup;
 1083              		.loc 1 193 1 view .LVU322
 194:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** BaseType_t xAlreadyYielded;
 1084              		.loc 1 194 1 view .LVU323
 195:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** BaseType_t xTimeoutOccurred = pdFALSE;
 1085              		.loc 1 195 1 view .LVU324
 196:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 1086              		.loc 1 196 1 view .LVU325
 198:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	configASSERT( uxBitsToWaitFor != 0 );
 1087              		.loc 1 198 2 view .LVU326
 1088 0000 B2F1807F 		cmp	r2, #16777216
 1089 0004 08D3     		bcc	.L70
 198:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	configASSERT( uxBitsToWaitFor != 0 );
 1090              		.loc 1 198 2 discriminator 1 view .LVU327
 1091              	.LBB65:
 1092              	.LBI65:
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 1093              		.loc 2 191 30 view .LVU328
 1094              	.LBB66:
 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 1095              		.loc 2 193 1 view .LVU329
 195:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	(
 1096              		.loc 2 195 2 view .LVU330
 1097              		.syntax unified
 1098              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 1099 0006 4FF05003 			mov r3, #80												
 1100 000a 83F31188 		msr basepri, r3											
 1101 000e BFF36F8F 		isb														
 1102 0012 BFF34F8F 		dsb														
 1103              	
 1104              	@ 0 "" 2
 1105              	.LVL84:
 1106              		.thumb
 1107              		.syntax unified
 1108              	.L71:
 195:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	(
 1109              		.loc 2 195 2 is_stmt 0 view .LVU331
 1110              	.LBE66:
 1111              	.LBE65:
 198:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	configASSERT( uxBitsToWaitFor != 0 );
 1112              		.loc 1 198 2 is_stmt 1 discriminator 3 view .LVU332
 198:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	configASSERT( uxBitsToWaitFor != 0 );
 1113              		.loc 1 198 2 discriminator 3 view .LVU333
 1114 0016 FEE7     		b	.L71
 1115              	.LVL85:
 1116              	.L70:
 192:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** EventBits_t uxOriginalBitValue, uxReturn;
 1117              		.loc 1 192 1 is_stmt 0 view .LVU334
 1118 0018 2DE9F041 		push	{r4, r5, r6, r7, r8, lr}
 1119              	.LCFI15:
 1120              		.cfi_def_cfa_offset 24
 1121              		.cfi_offset 4, -24
 1122              		.cfi_offset 5, -20
 1123              		.cfi_offset 6, -16
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s 			page 42


 1124              		.cfi_offset 7, -12
 1125              		.cfi_offset 8, -8
 1126              		.cfi_offset 14, -4
 1127 001c 0746     		mov	r7, r0
 1128 001e 0D46     		mov	r5, r1
 1129 0020 1446     		mov	r4, r2
 1130 0022 1E46     		mov	r6, r3
 198:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	configASSERT( uxBitsToWaitFor != 0 );
 1131              		.loc 1 198 74 is_stmt 1 discriminator 2 view .LVU335
 199:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	#if ( ( INCLUDE_xTaskGetSchedulerState == 1 ) || ( configUSE_TIMERS == 1 ) )
 1132              		.loc 1 199 2 view .LVU336
 1133 0024 42B9     		cbnz	r2, .L72
 199:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	#if ( ( INCLUDE_xTaskGetSchedulerState == 1 ) || ( configUSE_TIMERS == 1 ) )
 1134              		.loc 1 199 2 discriminator 1 view .LVU337
 1135              	.LBB67:
 1136              	.LBI67:
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 1137              		.loc 2 191 30 view .LVU338
 1138              	.LBB68:
 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 1139              		.loc 2 193 1 view .LVU339
 195:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	(
 1140              		.loc 2 195 2 view .LVU340
 1141              		.syntax unified
 1142              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 1143 0026 4FF05003 			mov r3, #80												
 1144 002a 83F31188 		msr basepri, r3											
 1145 002e BFF36F8F 		isb														
 1146 0032 BFF34F8F 		dsb														
 1147              	
 1148              	@ 0 "" 2
 1149              	.LVL86:
 1150              		.thumb
 1151              		.syntax unified
 1152              	.L73:
 195:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	(
 1153              		.loc 2 195 2 is_stmt 0 view .LVU341
 1154              	.LBE68:
 1155              	.LBE67:
 199:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	#if ( ( INCLUDE_xTaskGetSchedulerState == 1 ) || ( configUSE_TIMERS == 1 ) )
 1156              		.loc 1 199 2 is_stmt 1 discriminator 3 view .LVU342
 199:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	#if ( ( INCLUDE_xTaskGetSchedulerState == 1 ) || ( configUSE_TIMERS == 1 ) )
 1157              		.loc 1 199 2 discriminator 3 view .LVU343
 1158 0036 FEE7     		b	.L73
 1159              	.LVL87:
 1160              	.L72:
 199:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	#if ( ( INCLUDE_xTaskGetSchedulerState == 1 ) || ( configUSE_TIMERS == 1 ) )
 1161              		.loc 1 199 38 discriminator 2 view .LVU344
 202:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	}
 1162              		.loc 1 202 3 view .LVU345
 1163 0038 FFF7FEFF 		bl	xTaskGetSchedulerState
 1164              	.LVL88:
 202:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	}
 1165              		.loc 1 202 3 is_stmt 0 discriminator 1 view .LVU346
 1166 003c 48B9     		cbnz	r0, .L74
 202:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	}
 1167              		.loc 1 202 3 discriminator 2 view .LVU347
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s 			page 43


 1168 003e 46B1     		cbz	r6, .L74
 202:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	}
 1169              		.loc 1 202 3 is_stmt 1 discriminator 7 view .LVU348
 1170              	.LBB69:
 1171              	.LBI69:
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 1172              		.loc 2 191 30 view .LVU349
 1173              	.LBB70:
 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 1174              		.loc 2 193 1 view .LVU350
 195:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	(
 1175              		.loc 2 195 2 view .LVU351
 1176              		.syntax unified
 1177              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 1178 0040 4FF05003 			mov r3, #80												
 1179 0044 83F31188 		msr basepri, r3											
 1180 0048 BFF36F8F 		isb														
 1181 004c BFF34F8F 		dsb														
 1182              	
 1183              	@ 0 "" 2
 1184              		.thumb
 1185              		.syntax unified
 1186              	.L75:
 1187              	.LBE70:
 1188              	.LBE69:
 202:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	}
 1189              		.loc 1 202 3 discriminator 9 view .LVU352
 202:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	}
 1190              		.loc 1 202 3 discriminator 9 view .LVU353
 1191 0050 FEE7     		b	.L75
 1192              	.L74:
 202:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	}
 1193              		.loc 1 202 104 discriminator 8 view .LVU354
 206:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	{
 1194              		.loc 1 206 2 view .LVU355
 1195 0052 FFF7FEFF 		bl	vTaskSuspendAll
 1196              	.LVL89:
 208:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 1197              		.loc 1 208 3 view .LVU356
 208:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 1198              		.loc 1 208 22 is_stmt 0 view .LVU357
 1199 0056 D7F80080 		ldr	r8, [r7]
 1200              	.LVL90:
 210:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 1201              		.loc 1 210 3 is_stmt 1 view .LVU358
 210:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 1202              		.loc 1 210 12 is_stmt 0 view .LVU359
 1203 005a 2946     		mov	r1, r5
 1204 005c 3846     		mov	r0, r7
 1205 005e FFF7FEFF 		bl	xEventGroupSetBits
 1206              	.LVL91:
 212:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		{
 1207              		.loc 1 212 3 is_stmt 1 view .LVU360
 212:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		{
 1208              		.loc 1 212 30 is_stmt 0 view .LVU361
 1209 0062 48EA0505 		orr	r5, r8, r5
 1210              	.LVL92:
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s 			page 44


 212:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		{
 1211              		.loc 1 212 5 view .LVU362
 1212 0066 34EA0503 		bics	r3, r4, r5
 1213 006a 1AD0     		beq	.L85
 225:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			{
 1214              		.loc 1 225 4 is_stmt 1 view .LVU363
 225:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			{
 1215              		.loc 1 225 6 is_stmt 0 view .LVU364
 1216 006c FEB9     		cbnz	r6, .L86
 244:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				xTimeoutOccurred = pdTRUE;
 1217              		.loc 1 244 5 is_stmt 1 view .LVU365
 244:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				xTimeoutOccurred = pdTRUE;
 1218              		.loc 1 244 14 is_stmt 0 view .LVU366
 1219 006e 3D68     		ldr	r5, [r7]
 1220              	.LVL93:
 245:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			}
 1221              		.loc 1 245 5 is_stmt 1 view .LVU367
 1222              	.L77:
 249:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 1223              		.loc 1 249 2 view .LVU368
 249:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 1224              		.loc 1 249 20 is_stmt 0 view .LVU369
 1225 0070 FFF7FEFF 		bl	xTaskResumeAll
 1226              	.LVL94:
 251:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	{
 1227              		.loc 1 251 2 is_stmt 1 view .LVU370
 251:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	{
 1228              		.loc 1 251 4 is_stmt 0 view .LVU371
 1229 0074 96B1     		cbz	r6, .L69
 253:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		{
 1230              		.loc 1 253 3 is_stmt 1 view .LVU372
 253:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		{
 1231              		.loc 1 253 5 is_stmt 0 view .LVU373
 1232 0076 48B9     		cbnz	r0, .L80
 255:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		}
 1233              		.loc 1 255 4 is_stmt 1 view .LVU374
 1234 0078 4FF0E023 		mov	r3, #-*********
 1235 007c 4FF08052 		mov	r2, #*********
 1236 0080 C3F8042D 		str	r2, [r3, #3332]
 255:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		}
 1237              		.loc 1 255 4 view .LVU375
 1238              		.syntax unified
 1239              	@ 255 "Middlewares/Third_Party/FreeRTOS/Source/event_groups.c" 1
 1240 0084 BFF34F8F 		dsb
 1241              	@ 0 "" 2
 255:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		}
 1242              		.loc 1 255 4 view .LVU376
 1243              	@ 255 "Middlewares/Third_Party/FreeRTOS/Source/event_groups.c" 1
 1244 0088 BFF36F8F 		isb
 1245              	@ 0 "" 2
 255:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		}
 1246              		.loc 1 255 26 view .LVU377
 1247              		.thumb
 1248              		.syntax unified
 1249              	.L80:
 259:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		}
 1250              		.loc 1 259 28 view .LVU378
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s 			page 45


 266:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 1251              		.loc 1 266 3 view .LVU379
 266:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 1252              		.loc 1 266 14 is_stmt 0 view .LVU380
 1253 008c FFF7FEFF 		bl	uxTaskResetEventItemValue
 1254              	.LVL95:
 266:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 1255              		.loc 1 266 14 view .LVU381
 1256 0090 0546     		mov	r5, r0
 1257              	.LVL96:
 268:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		{
 1258              		.loc 1 268 3 is_stmt 1 view .LVU382
 268:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		{
 1259              		.loc 1 268 5 is_stmt 0 view .LVU383
 1260 0092 10F0007F 		tst	r0, #33554432
 1261 0096 12D0     		beq	.L87
 1262              	.LVL97:
 1263              	.L81:
 295:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 1264              		.loc 1 295 3 is_stmt 1 view .LVU384
 299:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	}
 1265              		.loc 1 299 3 view .LVU385
 299:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	}
 1266              		.loc 1 299 12 is_stmt 0 view .LVU386
 1267 0098 25F07F45 		bic	r5, r5, #-16777216
 1268              	.LVL98:
 302:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 1269              		.loc 1 302 2 is_stmt 1 view .LVU387
 305:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 1270              		.loc 1 305 2 view .LVU388
 307:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** }
 1271              		.loc 1 307 2 view .LVU389
 1272              	.L69:
 308:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** /*-----------------------------------------------------------*/
 1273              		.loc 1 308 1 is_stmt 0 view .LVU390
 1274 009c 2846     		mov	r0, r5
 1275 009e BDE8F081 		pop	{r4, r5, r6, r7, r8, pc}
 1276              	.LVL99:
 1277              	.L85:
 215:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 1278              		.loc 1 215 4 is_stmt 1 view .LVU391
 219:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 1279              		.loc 1 219 4 view .LVU392
 219:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 1280              		.loc 1 219 15 is_stmt 0 view .LVU393
 1281 00a2 3B68     		ldr	r3, [r7]
 219:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 1282              		.loc 1 219 29 view .LVU394
 1283 00a4 23EA0403 		bic	r3, r3, r4
 1284 00a8 3B60     		str	r3, [r7]
 221:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		}
 1285              		.loc 1 221 4 is_stmt 1 view .LVU395
 1286              	.LVL100:
 221:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		}
 1287              		.loc 1 221 17 is_stmt 0 view .LVU396
 1288 00aa 0026     		movs	r6, #0
 1289 00ac E0E7     		b	.L77
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s 			page 46


 1290              	.LVL101:
 1291              	.L86:
 227:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 1292              		.loc 1 227 77 is_stmt 1 view .LVU397
 232:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 1293              		.loc 1 232 5 view .LVU398
 1294 00ae 3246     		mov	r2, r6
 1295 00b0 44F0A061 		orr	r1, r4, #83886080
 1296 00b4 381D     		adds	r0, r7, #4
 1297 00b6 FFF7FEFF 		bl	vTaskPlaceOnUnorderedEventList
 1298              	.LVL102:
 238:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			}
 1299              		.loc 1 238 5 view .LVU399
 238:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			}
 1300              		.loc 1 238 14 is_stmt 0 view .LVU400
 1301 00ba 0025     		movs	r5, #0
 1302 00bc D8E7     		b	.L77
 1303              	.LVL103:
 1304              	.L87:
 271:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			{
 1305              		.loc 1 271 4 is_stmt 1 view .LVU401
 1306 00be FFF7FEFF 		bl	vPortEnterCritical
 1307              	.LVL104:
 273:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 1308              		.loc 1 273 5 view .LVU402
 273:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 1309              		.loc 1 273 14 is_stmt 0 view .LVU403
 1310 00c2 3D68     		ldr	r5, [r7]
 1311              	.LVL105:
 279:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				{
 1312              		.loc 1 279 5 is_stmt 1 view .LVU404
 279:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				{
 1313              		.loc 1 279 7 is_stmt 0 view .LVU405
 1314 00c4 34EA0503 		bics	r3, r4, r5
 1315 00c8 02D0     		beq	.L88
 1316              	.LVL106:
 1317              	.L82:
 285:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				}
 1318              		.loc 1 285 30 is_stmt 1 view .LVU406
 288:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 1319              		.loc 1 288 4 view .LVU407
 1320 00ca FFF7FEFF 		bl	vPortExitCritical
 1321              	.LVL107:
 290:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		}
 1322              		.loc 1 290 4 view .LVU408
 290:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		}
 1323              		.loc 1 290 4 is_stmt 0 view .LVU409
 1324 00ce E3E7     		b	.L81
 1325              	.LVL108:
 1326              	.L88:
 281:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				}
 1327              		.loc 1 281 6 is_stmt 1 view .LVU410
 281:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				}
 1328              		.loc 1 281 31 is_stmt 0 view .LVU411
 1329 00d0 25EA0404 		bic	r4, r5, r4
 1330              	.LVL109:
 281:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 				}
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s 			page 47


 1331              		.loc 1 281 31 view .LVU412
 1332 00d4 3C60     		str	r4, [r7]
 1333 00d6 F8E7     		b	.L82
 1334              		.cfi_endproc
 1335              	.LFE6:
 1337              		.section	.text.vEventGroupSetBitsCallback,"ax",%progbits
 1338              		.align	1
 1339              		.global	vEventGroupSetBitsCallback
 1340              		.syntax unified
 1341              		.thumb
 1342              		.thumb_func
 1344              	vEventGroupSetBitsCallback:
 1345              	.LVL110:
 1346              	.LFB13:
 658:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	( void ) xEventGroupSetBits( pvEventGroup, ( EventBits_t ) ulBitsToSet ); /*lint !e9079 Can't avoi
 1347              		.loc 1 658 1 is_stmt 1 view -0
 1348              		.cfi_startproc
 1349              		@ args = 0, pretend = 0, frame = 0
 1350              		@ frame_needed = 0, uses_anonymous_args = 0
 658:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	( void ) xEventGroupSetBits( pvEventGroup, ( EventBits_t ) ulBitsToSet ); /*lint !e9079 Can't avoi
 1351              		.loc 1 658 1 is_stmt 0 view .LVU414
 1352 0000 08B5     		push	{r3, lr}
 1353              	.LCFI16:
 1354              		.cfi_def_cfa_offset 8
 1355              		.cfi_offset 3, -8
 1356              		.cfi_offset 14, -4
 659:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** }
 1357              		.loc 1 659 2 is_stmt 1 view .LVU415
 659:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** }
 1358              		.loc 1 659 11 is_stmt 0 view .LVU416
 1359 0002 FFF7FEFF 		bl	xEventGroupSetBits
 1360              	.LVL111:
 660:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** /*-----------------------------------------------------------*/
 1361              		.loc 1 660 1 view .LVU417
 1362 0006 08BD     		pop	{r3, pc}
 1363              		.cfi_endproc
 1364              	.LFE13:
 1366              		.section	.text.vEventGroupDelete,"ax",%progbits
 1367              		.align	1
 1368              		.global	vEventGroupDelete
 1369              		.syntax unified
 1370              		.thumb
 1371              		.thumb_func
 1373              	vEventGroupDelete:
 1374              	.LVL112:
 1375              	.LFB12:
 614:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** EventGroup_t *pxEventBits = xEventGroup;
 1376              		.loc 1 614 1 is_stmt 1 view -0
 1377              		.cfi_startproc
 1378              		@ args = 0, pretend = 0, frame = 0
 1379              		@ frame_needed = 0, uses_anonymous_args = 0
 614:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** EventGroup_t *pxEventBits = xEventGroup;
 1380              		.loc 1 614 1 is_stmt 0 view .LVU419
 1381 0000 10B5     		push	{r4, lr}
 1382              	.LCFI17:
 1383              		.cfi_def_cfa_offset 8
 1384              		.cfi_offset 4, -8
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s 			page 48


 1385              		.cfi_offset 14, -4
 1386 0002 0446     		mov	r4, r0
 615:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** const List_t *pxTasksWaitingForBits = &( pxEventBits->xTasksWaitingForBits );
 1387              		.loc 1 615 1 is_stmt 1 view .LVU420
 1388              	.LVL113:
 616:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 1389              		.loc 1 616 1 view .LVU421
 618:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	{
 1390              		.loc 1 618 2 view .LVU422
 1391 0004 FFF7FEFF 		bl	vTaskSuspendAll
 1392              	.LVL114:
 620:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 1393              		.loc 1 620 41 view .LVU423
 622:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		{
 1394              		.loc 1 622 3 view .LVU424
 1395              	.L92:
 622:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		{
 1396              		.loc 1 622 59 view .LVU425
 622:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		{
 1397              		.loc 1 622 10 is_stmt 0 view .LVU426
 1398 0008 6368     		ldr	r3, [r4, #4]
 622:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		{
 1399              		.loc 1 622 59 view .LVU427
 1400 000a 93B1     		cbz	r3, .L98
 626:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			vTaskRemoveFromUnorderedEventList( pxTasksWaitingForBits->xListEnd.pxNext, eventUNBLOCKED_DUE_TO
 1401              		.loc 1 626 4 is_stmt 1 view .LVU428
 1402 000c 2069     		ldr	r0, [r4, #16]
 1403 000e 04F10C03 		add	r3, r4, #12
 1404 0012 9842     		cmp	r0, r3
 1405 0014 04D0     		beq	.L99
 626:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			vTaskRemoveFromUnorderedEventList( pxTasksWaitingForBits->xListEnd.pxNext, eventUNBLOCKED_DUE_TO
 1406              		.loc 1 626 121 discriminator 2 view .LVU429
 627:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		}
 1407              		.loc 1 627 4 view .LVU430
 1408 0016 4FF00071 		mov	r1, #33554432
 1409 001a FFF7FEFF 		bl	vTaskRemoveFromUnorderedEventList
 1410              	.LVL115:
 1411 001e F3E7     		b	.L92
 1412              	.L99:
 626:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			vTaskRemoveFromUnorderedEventList( pxTasksWaitingForBits->xListEnd.pxNext, eventUNBLOCKED_DUE_TO
 1413              		.loc 1 626 4 discriminator 1 view .LVU431
 1414              	.LBB71:
 1415              	.LBI71:
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 1416              		.loc 2 191 30 view .LVU432
 1417              	.LBB72:
 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 1418              		.loc 2 193 1 view .LVU433
 195:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	(
 1419              		.loc 2 195 2 view .LVU434
 1420              		.syntax unified
 1421              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 1422 0020 4FF05003 			mov r3, #80												
 1423 0024 83F31188 		msr basepri, r3											
 1424 0028 BFF36F8F 		isb														
 1425 002c BFF34F8F 		dsb														
 1426              	
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s 			page 49


 1427              	@ 0 "" 2
 1428              		.thumb
 1429              		.syntax unified
 1430              	.L94:
 1431              	.LBE72:
 1432              	.LBE71:
 626:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			vTaskRemoveFromUnorderedEventList( pxTasksWaitingForBits->xListEnd.pxNext, eventUNBLOCKED_DUE_TO
 1433              		.loc 1 626 4 discriminator 3 view .LVU435
 626:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			vTaskRemoveFromUnorderedEventList( pxTasksWaitingForBits->xListEnd.pxNext, eventUNBLOCKED_DUE_TO
 1434              		.loc 1 626 4 discriminator 3 view .LVU436
 1435 0030 FEE7     		b	.L94
 1436              	.L98:
 640:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			{
 1437              		.loc 1 640 4 view .LVU437
 640:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			{
 1438              		.loc 1 640 19 is_stmt 0 view .LVU438
 1439 0032 237F     		ldrb	r3, [r4, #28]	@ zero_extendqisi2
 640:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			{
 1440              		.loc 1 640 6 view .LVU439
 1441 0034 13B1     		cbz	r3, .L100
 1442              	.L96:
 646:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			}
 1443              		.loc 1 646 29 is_stmt 1 view .LVU440
 651:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** }
 1444              		.loc 1 651 2 view .LVU441
 651:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** }
 1445              		.loc 1 651 11 is_stmt 0 view .LVU442
 1446 0036 FFF7FEFF 		bl	xTaskResumeAll
 1447              	.LVL116:
 652:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** /*-----------------------------------------------------------*/
 1448              		.loc 1 652 1 view .LVU443
 1449 003a 10BD     		pop	{r4, pc}
 1450              	.LVL117:
 1451              	.L100:
 642:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			}
 1452              		.loc 1 642 5 is_stmt 1 view .LVU444
 1453 003c 2046     		mov	r0, r4
 1454 003e FFF7FEFF 		bl	vPortFree
 1455              	.LVL118:
 1456 0042 F8E7     		b	.L96
 1457              		.cfi_endproc
 1458              	.LFE12:
 1460              		.section	.text.xEventGroupSetBitsFromISR,"ax",%progbits
 1461              		.align	1
 1462              		.global	xEventGroupSetBitsFromISR
 1463              		.syntax unified
 1464              		.thumb
 1465              		.thumb_func
 1467              	xEventGroupSetBitsFromISR:
 1468              	.LVL119:
 1469              	.LFB16:
 704:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** /*-----------------------------------------------------------*/
 705:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 706:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** #if ( ( configUSE_TRACE_FACILITY == 1 ) && ( INCLUDE_xTimerPendFunctionCall == 1 ) && ( configUSE_T
 707:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 708:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	BaseType_t xEventGroupSetBitsFromISR( EventGroupHandle_t xEventGroup, const EventBits_t uxBitsToSe
 709:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	{
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s 			page 50


 1470              		.loc 1 709 2 view -0
 1471              		.cfi_startproc
 1472              		@ args = 0, pretend = 0, frame = 0
 1473              		@ frame_needed = 0, uses_anonymous_args = 0
 1474              		.loc 1 709 2 is_stmt 0 view .LVU446
 1475 0000 08B5     		push	{r3, lr}
 1476              	.LCFI18:
 1477              		.cfi_def_cfa_offset 8
 1478              		.cfi_offset 3, -8
 1479              		.cfi_offset 14, -4
 1480 0002 1346     		mov	r3, r2
 710:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	BaseType_t xReturn;
 1481              		.loc 1 710 2 is_stmt 1 view .LVU447
 711:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 712:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		traceEVENT_GROUP_SET_BITS_FROM_ISR( xEventGroup, uxBitsToSet );
 1482              		.loc 1 712 65 view .LVU448
 713:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		xReturn = xTimerPendFunctionCallFromISR( vEventGroupSetBitsCallback, ( void * ) xEventGroup, ( ui
 1483              		.loc 1 713 3 view .LVU449
 1484              		.loc 1 713 13 is_stmt 0 view .LVU450
 1485 0004 0A46     		mov	r2, r1
 1486              	.LVL120:
 1487              		.loc 1 713 13 view .LVU451
 1488 0006 0146     		mov	r1, r0
 1489              	.LVL121:
 1490              		.loc 1 713 13 view .LVU452
 1491 0008 0148     		ldr	r0, .L103
 1492              	.LVL122:
 1493              		.loc 1 713 13 view .LVU453
 1494 000a FFF7FEFF 		bl	xTimerPendFunctionCallFromISR
 1495              	.LVL123:
 714:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 715:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		return xReturn;
 1496              		.loc 1 715 3 is_stmt 1 view .LVU454
 716:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	}
 1497              		.loc 1 716 2 is_stmt 0 view .LVU455
 1498 000e 08BD     		pop	{r3, pc}
 1499              	.L104:
 1500              		.align	2
 1501              	.L103:
 1502 0010 00000000 		.word	vEventGroupSetBitsCallback
 1503              		.cfi_endproc
 1504              	.LFE16:
 1506              		.section	.text.uxEventGroupGetNumber,"ax",%progbits
 1507              		.align	1
 1508              		.global	uxEventGroupGetNumber
 1509              		.syntax unified
 1510              		.thumb
 1511              		.thumb_func
 1513              	uxEventGroupGetNumber:
 1514              	.LVL124:
 1515              	.LFB17:
 717:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 718:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** #endif
 719:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** /*-----------------------------------------------------------*/
 720:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 721:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** #if (configUSE_TRACE_FACILITY == 1)
 722:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s 			page 51


 723:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	UBaseType_t uxEventGroupGetNumber( void* xEventGroup )
 724:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	{
 1516              		.loc 1 724 2 is_stmt 1 view -0
 1517              		.cfi_startproc
 1518              		@ args = 0, pretend = 0, frame = 0
 1519              		@ frame_needed = 0, uses_anonymous_args = 0
 1520              		@ link register save eliminated.
 725:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	UBaseType_t xReturn;
 1521              		.loc 1 725 2 view .LVU457
 726:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	EventGroup_t const *pxEventBits = ( EventGroup_t * ) xEventGroup; /*lint !e9087 !e9079 EventGroupH
 1522              		.loc 1 726 2 view .LVU458
 727:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 728:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		if( xEventGroup == NULL )
 1523              		.loc 1 728 3 view .LVU459
 1524              		.loc 1 728 5 is_stmt 0 view .LVU460
 1525 0000 08B1     		cbz	r0, .L107
 729:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		{
 730:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			xReturn = 0;
 731:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		}
 732:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		else
 733:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		{
 734:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 			xReturn = pxEventBits->uxEventGroupNumber;
 1526              		.loc 1 734 4 is_stmt 1 view .LVU461
 1527              		.loc 1 734 12 is_stmt 0 view .LVU462
 1528 0002 8069     		ldr	r0, [r0, #24]
 1529              	.LVL125:
 1530              		.loc 1 734 12 view .LVU463
 1531 0004 7047     		bx	lr
 1532              	.LVL126:
 1533              	.L107:
 730:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		}
 1534              		.loc 1 730 12 view .LVU464
 1535 0006 0020     		movs	r0, #0
 1536              	.LVL127:
 735:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		}
 736:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 737:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		return xReturn;
 1537              		.loc 1 737 3 is_stmt 1 view .LVU465
 738:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	}
 1538              		.loc 1 738 2 is_stmt 0 view .LVU466
 1539 0008 7047     		bx	lr
 1540              		.cfi_endproc
 1541              	.LFE17:
 1543              		.section	.text.vEventGroupSetNumber,"ax",%progbits
 1544              		.align	1
 1545              		.global	vEventGroupSetNumber
 1546              		.syntax unified
 1547              		.thumb
 1548              		.thumb_func
 1550              	vEventGroupSetNumber:
 1551              	.LVL128:
 1552              	.LFB18:
 739:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 740:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** #endif /* configUSE_TRACE_FACILITY */
 741:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** /*-----------------------------------------------------------*/
 742:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 743:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** #if ( configUSE_TRACE_FACILITY == 1 )
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s 			page 52


 744:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 
 745:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	void vEventGroupSetNumber( void * xEventGroup, UBaseType_t uxEventGroupNumber )
 746:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	{
 1553              		.loc 1 746 2 is_stmt 1 view -0
 1554              		.cfi_startproc
 1555              		@ args = 0, pretend = 0, frame = 0
 1556              		@ frame_needed = 0, uses_anonymous_args = 0
 1557              		@ link register save eliminated.
 747:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 		( ( EventGroup_t * ) xEventGroup )->uxEventGroupNumber = uxEventGroupNumber; /*lint !e9087 !e9079
 1558              		.loc 1 747 3 view .LVU468
 1559              		.loc 1 747 58 is_stmt 0 view .LVU469
 1560 0000 8161     		str	r1, [r0, #24]
 748:Middlewares/Third_Party/FreeRTOS/Source/event_groups.c **** 	}
 1561              		.loc 1 748 2 view .LVU470
 1562 0002 7047     		bx	lr
 1563              		.cfi_endproc
 1564              	.LFE18:
 1566              		.text
 1567              	.Letext0:
 1568              		.file 3 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin/../lib/gcc/arm-none-eabi/13.3.1/inc
 1569              		.file 4 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin/../lib/gcc/arm-none-eabi/13.3.1/../
 1570              		.file 5 "C:\\ST\\STM32CubeCLT_1.18.0\\GNU-tools-for-STM32\\bin/../lib/gcc/arm-none-eabi/13.3.1/../
 1571              		.file 6 "Middlewares/Third_Party/FreeRTOS/Source/include/FreeRTOS.h"
 1572              		.file 7 "Middlewares/Third_Party/FreeRTOS/Source/include/list.h"
 1573              		.file 8 "Middlewares/Third_Party/FreeRTOS/Source/include/timers.h"
 1574              		.file 9 "Middlewares/Third_Party/FreeRTOS/Source/include/event_groups.h"
 1575              		.file 10 "Middlewares/Third_Party/FreeRTOS/Source/include/portable.h"
 1576              		.file 11 "Middlewares/Third_Party/FreeRTOS/Source/include/task.h"
ARM GAS  C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s 			page 53


DEFINED SYMBOLS
                            *ABS*:00000000 event_groups.c
C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s:20     .text.prvTestWaitCondition:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s:25     .text.prvTestWaitCondition:00000000 prvTestWaitCondition
C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s:79     .text.xEventGroupCreateStatic:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s:85     .text.xEventGroupCreateStatic:00000000 xEventGroupCreateStatic
C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s:207    .text.xEventGroupCreate:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s:213    .text.xEventGroupCreate:00000000 xEventGroupCreate
C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s:259    .text.xEventGroupWaitBits:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s:265    .text.xEventGroupWaitBits:00000000 xEventGroupWaitBits
C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s:615    .text.xEventGroupClearBits:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s:621    .text.xEventGroupClearBits:00000000 xEventGroupClearBits
C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s:733    .text.vEventGroupClearBitsCallback:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s:739    .text.vEventGroupClearBitsCallback:00000000 vEventGroupClearBitsCallback
C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s:762    .text.xEventGroupClearBitsFromISR:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s:768    .text.xEventGroupClearBitsFromISR:00000000 xEventGroupClearBitsFromISR
C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s:801    .text.xEventGroupClearBitsFromISR:00000010 $d
C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s:806    .text.xEventGroupGetBitsFromISR:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s:812    .text.xEventGroupGetBitsFromISR:00000000 xEventGroupGetBitsFromISR
C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s:873    .text.xEventGroupSetBits:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s:879    .text.xEventGroupSetBits:00000000 xEventGroupSetBits
C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s:1070   .text.xEventGroupSync:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s:1076   .text.xEventGroupSync:00000000 xEventGroupSync
C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s:1338   .text.vEventGroupSetBitsCallback:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s:1344   .text.vEventGroupSetBitsCallback:00000000 vEventGroupSetBitsCallback
C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s:1367   .text.vEventGroupDelete:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s:1373   .text.vEventGroupDelete:00000000 vEventGroupDelete
C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s:1461   .text.xEventGroupSetBitsFromISR:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s:1467   .text.xEventGroupSetBitsFromISR:00000000 xEventGroupSetBitsFromISR
C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s:1502   .text.xEventGroupSetBitsFromISR:00000010 $d
C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s:1507   .text.uxEventGroupGetNumber:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s:1513   .text.uxEventGroupGetNumber:00000000 uxEventGroupGetNumber
C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s:1544   .text.vEventGroupSetNumber:00000000 $t
C:\Users\<USER>\AppData\Local\Temp\ccohaLzG.s:1550   .text.vEventGroupSetNumber:00000000 vEventGroupSetNumber

UNDEFINED SYMBOLS
vListInitialise
pvPortMalloc
xTaskGetSchedulerState
vTaskSuspendAll
xTaskResumeAll
uxTaskResetEventItemValue
vTaskPlaceOnUnorderedEventList
vPortEnterCritical
vPortExitCritical
xTimerPendFunctionCallFromISR
vTaskRemoveFromUnorderedEventList
vPortFree
